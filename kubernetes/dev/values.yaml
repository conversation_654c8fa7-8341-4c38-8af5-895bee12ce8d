containers:
  ## Config For rdash-backend
  backend:
    enabled: true
    image: ghcr.io/rdash-tech/rdash-api
    resources:
      limits:
        cpu: "1000m"
        memory: "2Gi"
      requests:
        cpu: "100m"
        memory: "1Gi"

  ## Config For rdash-worker
  worker:
    enabled: true
    resources:
      limits:
        cpu: "500m"
        memory: "1Gi"
      requests:
        cpu: "100m"
        memory: "256Mi"
        ephemeral-storage: "1Gi"
    queues:
      # - example_queue  <---- Comment like this to remove queue
      - callback_queue
      - callback_processing_queue
      - request_action_queue
      - whatsapp_notification_queue
      - whatsapp_processing_queue
      - failed_whatsapp_callback_queue
      - notification_queue
      - misconfig_resolver_queue
      - misconfig_schedular_queue
      - email_queue
      - schedular_worker_queue
      - excel_sync_queue
      - script_runner_queue
      - custom_tasks_queue
      - template_api_queue

  schedular:
    enabled: true
    resources:
      limits:
        cpu: "500m"
        memory: "1Gi"
      requests:
        cpu: "100m"
        memory: "256Mi"
        ephemeral-storage: "1Gi"

  ## Config For rdash-frontend
  frontend:
    enabled: true
    image: 548568290184.dkr.ecr.ap-south-1.amazonaws.com/rdash-frontend:latest
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
      requests:
        cpu: 50m
        memory: 64Mi
        ephemeral-storage: "1Gi"

  ## Config For redis
  redis:
    enabled: true
    image: redis:7.0-alpine
    resources:
      requests:
        cpu: "10m"
        memory: "64Mi"

ingress:
  enabled: true
  host: api.BRANCH.rdash.dev

## Config For rdash-backend (.env)
env:
  IS_DEMO_ENVIRONMENT: "False"
  DEBUG: "True"
  ALLOWED_HOSTS: "*"
  CORS_ALLOWED_ORIGINS: "*"
  IS_CLOUD_STORAGE_ENABLED: "True"
  GS_PROJECT_ID: "rollingbanners"
  GS_BUCKET_NAME: "rollingbanners-dev"
  GS_CUSTOM_DOMAIN: "ik.imagekit.io/91sqft/dev"
  FIREBASE_API_URL: "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key={}"
  DOMAIN_URI_PREFIX: "https://rdash.page.link"
  ANDROID_PACKAGE_NAME: "com.ninetyonesquarefeet.sqft91"
  IOS_BUNDLE_ID: "com.ninetyonesquarefeet.sqft91"
  METABASE_URL: "https://metabase.rdash.io/"
  IS_ANALYTICS_WORKER_ENABLED: "False"
  ANALYTICS_API_URL: ""
  IS_EVENT_ANALYTICS_ENABLED: "False"
  PASSWORD_RESET_DASHBOARD_URL: "https://app.stage.rdash.dev/password-reset"
  PASSWORD_RESET_FROM_EMAIL: "<EMAIL>"
  PASSWORD_RESET_TIMEOUT: "86400"
  EMAIL_BACKEND: "rollingbanners.email_backends.SESEmailBackend"
  EMAIL_HOST: "email-smtp.ap-south-1.amazonaws.com"
  EMAIL_HOST_USER: "<EMAIL>"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "True"
  EMAIL_USE_SSL: "False"
  WATI_URL: "https://live-mt-server.wati.io/321456"
  ALLOW_WATI_STAGE_MOVEMENT_ALERT: "False"
  #Recce Link URL
  LINK_URL: "https://link.rdash.io/"
  # recce rating update by google sheets api key
  # Custom users to whom whatsapp notification needs to be sent
  CUSTOM_USER_CONTACTS: '[{"user": "Abhishek", "contact": "918285628920"}]'
  SENTRY_DSN: ""
  SUPPORT_EMAIL: "<EMAIL>"
  FIREBASE_ASSET_LINKS: '[{"relation":["delegate_permission/common.handle_all_urls"],"target":{"namespace":"android_app","package_name":"com.ninetyonesquarefeet.sqft91","sha256_cert_fingerprints":["82:05:0B:76:57:F8:A5:D5:E8:F0:7C:7F:78:0B:8F:C0:33:DB:A8:39:F9:EC:1E:28:8E:8B:A1:15:98:F3:8F:88"]}}]'
  NEW_RELIC_MONITOR_MODE: "false"
  METABASE_USERNAME: "<EMAIL>"
  METABASE_HOST: "https://metabase.91sqft.in"
  METABASE_SECRET_KEY: "901a22c29ff6bb1b6d03ef486a6c8140e677c3c9e43852b0af68f66d6d6ba4a9"
  CONSOLIDATED_PROGRESS_REPORT_WEBHOOK: "https://n8n.rdash.io/webhook/1bd765a5-dc90-4613-bab2-f36a39bcef29"
  N8N_TRIGGER_API_KEY: "dRWmyLINelocKwoNBVYY0Ccf9XmUw3dWEoqHLg=="
  MASTER_TOKEN_HASH: "fcee983ff66378f3b0e0d802e278e9b39e8fd61595d485766614131b78c51304"
  TEMPLATE_ENDPOINT: "https://template-api.stage.rdash.dev/api"
  INTEGRATIONS_FACEBOOK_LEADGEN_WEBHOOK_TOKEN: "ab2c67d51ef6ecee00ed383dc92bcd8c9f042e52d9a0d8f1f5369d85d4e1ff53"
  INTEGRATIONS_FACEBOOK_APP_ID: "1091016715675231"
  INTEGRATIONS_FACEBOOK_APP_SECRET: ""
  INTEGRATIONS_FACEBOOK_API_VERSION: "v21.0"
  INTEGRATIONS_FACEBOOK_GRAPH_API_ENDPOINT: "https://graph.facebook.com"
  INTREGRATIONS_POC_USER_EMAILS: ""
