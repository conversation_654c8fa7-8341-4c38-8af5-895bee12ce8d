apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    application: rdash-backend
  name: rdash-backend
spec:
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: rdash-backend
      application: rdash-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: rdash-backend
        application: rdash-backend
    spec:
      containers:
        {{- if .Values.containers.backend.enabled }}
        - name: rdash-backend
          image: {{ .Values.containers.backend.image }}
          resources: {{ .Values.containers.backend.resources | toYaml | nindent 12}}
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          ports:
            - containerPort: 8000
              name: backend
          imagePullPolicy: IfNotPresent
        {{- end }}
        {{- if and .Values.containers.worker.enabled (not (empty .Values.containers.worker.queues)) }}
        - name: rdash-worker
          image: {{ .Values.containers.backend.image }}
          resources: {{ .Values.containers.worker.resources | toYaml | nindent 12}}
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command:
          - /bin/sh
          - -c
          - celery -A rollingbanners worker --without-gossip --without-mingle -l INFO -c 1 -Q {{ join "," .Values.containers.worker.queues }}
          imagePullPolicy: IfNotPresent
        {{- end }}
        {{- if and .Values.containers.schedular.enabled }}
        - name: rdash-scheduler
          image: {{ .Values.containers.backend.image }}
          resources: {{ .Values.containers.schedular.resources | toYaml | nindent 12}}
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command:
          - /bin/sh
          - -c
          - celery -A rollingbanners beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
          imagePullPolicy: IfNotPresent
        {{- end }}
        {{- if .Values.containers.frontend.enabled }}
        - name: rdash-frontend
          image: {{ .Values.containers.frontend.image }}
          resources: {{ .Values.containers.frontend.resources | toYaml | nindent 12}}
          ports:
          - containerPort: 80
            name: frontend
          imagePullPolicy: Always
        {{- end }}
        {{- if .Values.containers.redis.enabled }}
        - name: redis
          image: {{ .Values.containers.redis.image }}
          resources:
            requests:
              cpu: "10m"
              memory: "64Mi"
          ports:
            - containerPort: 6379
              name: redis
          imagePullPolicy: IfNotPresent
        {{- end }}
      restartPolicy: Always
      terminationGracePeriodSeconds: 10
      nodeSelector:
        type: SPOT
      tolerations:
      - effect: NoSchedule
        key: type
        operator: Equal
        value: spot
      - effect: NoSchedule
        key: kubernetes.azure.com/scalesetpriority
        operator: Equal
        value: spot