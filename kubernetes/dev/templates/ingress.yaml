apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/default-backend: rdash-backend-proxy
  name: api-branch-rdash-in-ingress
spec:
  ingressClassName: nginx
  rules:
  - host: {{ .Values.ingress.host }}
    http:
      paths:
      - backend:
          service:
            name: no-service
            port:
              number: 80
        path: /api/v2/callback(/|$)(.*)
        pathType: ImplementationSpecific
      {{- if .Values.containers.frontend.enabled }}
      - backend:
          service:
            name: rdash-backend-proxy
            port:
              number: 8080
        path: /healthz|/admin|/api|/swagger|/v1|/v2|/v3
        pathType: ImplementationSpecific
      - backend:
          service:
            name: rdash-frontend
            port:
              number: 80
        path: /
        pathType: Prefix
      {{- else }}
      - backend:
          service:
            name: rdash-backend-proxy
            port:
              number: 8080
        path: /
        pathType: Prefix
      {{- end }}
  tls:
  - hosts:
    - {{ .Values.ingress.host }}
    secretName: api-branch-rdash-in-letsencrypt
