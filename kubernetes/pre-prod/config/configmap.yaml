apiVersion: v1
data:
    DEBUG: "False"
    ALLOWED_HOSTS: "api.rdash.dev,api.private.rdash.dev"
    CORS_ALLOWED_ORIGINS: "https://rdash-requests.flutterflow.app,https://rdash-approvals.flutterflow.app,https://app.rdash.dev"
    IS_CLOUD_STORAGE_ENABLED: "True"
    GS_PROJECT_ID: "rollingbanners"
    GS_BUCKET_NAME: "rollingbanners-prod"
    GS_CUSTOM_DOMAIN: "ik.imagekit.io/91sqft"
    BITRIX_WEBHOOK_URI: "https://91sqft.bitrix24.in/rest/1/ms57j9ijpocel78b/"

    FIREBASE_API_URL: "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key={}"
    DOMAIN_URI_PREFIX: "https://rdash.page.link"
    ANDROID_PACKAGE_NAME: "com.ninetyonesquarefeet.sqft91"
    IOS_BUNDLE_ID: "com.ninetyonesquarefeet.sqft91"

    DASHBOARD_URL: "https://app.rdash.dev/"
    METABASE_URL: "https://metabase.rdash.io/"

    IS_ANALYTICS_WORKER_ENABLED: "False"
    ANALYTICS_API_URL: "http://*************:8000/bitrix/"
    IS_EVENT_ANALYTICS_ENABLED: "False"

    # Password reset configs
    PASSWORD_RESET_DASHBOARD_URL: "https://app.rdash.dev/reset-password"
    PASSWORD_RESET_FROM_EMAIL: "<EMAIL>"
    PASSWORD_RESET_TIMEOUT: "86400"
    EMAIL_BACKEND: "rollingbanners.email_backends.SESEmailBackend"
    EMAIL_HOST: "email-smtp.ap-south-1.amazonaws.com"
    EMAIL_HOST_USER: "<EMAIL>"
    EMAIL_PORT: "587"
    EMAIL_USE_TLS: "True"
    EMAIL_USE_SSL: "False"

    # WhatsApp
    WATI_URL: "https://live-mt-server.wati.io/321456"
    ALLOW_WATI_STAGE_MOVEMENT_ALERT: "True"

    # recce rating update by google sheets api key
    # Custom users to whom whatsapp notification needs to be sent
    CUSTOM_USER_CONTACTS: '[{"name": "Aditya", "contact": "917292027717"}]'
    SUPPORT_EMAIL: "<EMAIL>"
    FIREBASE_ASSET_LINKS: '[{"relation":["delegate_permission/common.handle_all_urls"],"target":{"namespace":"android_app","package_name":"com.ninetyonesquarefeet.sqft91","sha256_cert_fingerprints":["08:3C:C2:85:14:06:BC:6A:FA:AB:77:B6:D5:20:47:E0:BF:70:0D:EC:4C:1E:A7:EA:3C:44:A9:B8:C2:5E:F1:38"]}}]'
    NEW_RELIC_MONITOR_MODE: "true"
    METABASE_USERNAME: "<EMAIL>"
    METABASE_HOST: "https://metabase.rdash.io"
    CONSOLIDATED_PROGRESS_REPORT_WEBHOOK: "https://n8n.rdash.io/webhook/1bd765a5-dc90-4613-bab2-f36a39bcef29"
    TEMPLATE_ENDPOINT: "https://template-api.stage.rdash.dev/api"
    IS_NON_PROD_ENV: "True"

kind: ConfigMap
metadata:
    name: rdash-backend-env
