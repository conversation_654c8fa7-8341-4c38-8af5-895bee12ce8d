apiVersion: batch/v1
kind: Job
metadata:
  name: stage-db-export
spec:
  backoffLimit: 0
  ttlSecondsAfterFinished: 30
  template:
    metadata:
      labels:
        azure.workload.identity/use: "true"
    spec:
      containers:
        - name: db-export
          image: public.ecr.aws/x5v9a0b5/psql-worker:latest
          envFrom:
            - secretRef:
                name: secret-stage-db-export
          command:
          - "/bin/bash"
          - "-c"
          - |
            set -e
            export PGPASSWORD=${PGPASSWORD}
            pg_dump --no-acl --no-owner -h ${HOST} -p ${PORT} -U ${USER} -F c -b -v -f ${DUMP_FILE} ${DATABASE}
            aws s3 mv ${DUMP_FILE} ${S3_URI}/${DUMP_FILE} --only-show-errors --no-progress
      restartPolicy: Never
      serviceAccountName: aws-federation
      nodeSelector:
        kubernetes.io/os: linux
        kubernetes.io/arch: amd64
