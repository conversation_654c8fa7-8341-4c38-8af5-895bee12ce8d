apiVersion: batch/v1
kind: Job
metadata:
  name: rdash-backend-db-restore
spec:
  backoffLimit: 0
  ttlSecondsAfterFinished: 30
  template:
    metadata:
      labels:
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: aws-federation
      containers:
        - name: db-restore
          image: public.ecr.aws/x5v9a0b5/psql-worker:latest
          envFrom:
            - secretRef:
                name: db-credentials
          command:
          - "/bin/bash"
          - "-c"
          - |
            export PGPASSWORD=${PGPASSWORD}
            aws s3 cp ${S3_URI}/${DUMP_FILE} ${DUMP_FILE} --only-show-errors --no-progress

            pg_restore --verbose --no-acl --no-owner -h ${HOST} -p ${PORT} -U ${USER} -d ${DATABASE} ${DUMP_FILE}
            psql -h ${HOST} -p ${PORT} -U ${USER} -d ${DATABASE} << EOF
                  GRANT ALL PRIVILEGES ON DATABASE ${DATABASE} TO ${ROLE};
                  GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${ROLE};
                  GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${ROLE};
                  GRANT ALL PRIVILEGES ON SCHEMA public TO ${ROLE};
                  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${ROLE};
                  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${ROLE};
                  ALTER SCHEMA public OWNER TO ${ROLE};
                  ALTER DATABASE ${DATABASE} OWNER TO ${ROLE};
            EOF

            TABLES=$(psql -h ${HOST} -p ${PORT} -U ${USER} -d ${DATABASE} -tc "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
            for TABLE in ${TABLES}; do psql -h ${HOST} -p ${PORT} -U ${USER} -d ${DATABASE} -tc "ALTER TABLE ${TABLE} OWNER TO ${ROLE}"; done
      restartPolicy: Never
