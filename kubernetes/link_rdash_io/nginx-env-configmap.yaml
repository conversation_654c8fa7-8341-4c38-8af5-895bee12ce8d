apiVersion: v1
data:
    nginx.conf: |
        user              www-data  www-data;
        worker_processes  1;
        pid        /var/run/nginx.pid;
        worker_rlimit_nofile 4096;
        include /etc/nginx/modules-enabled/*.conf;

        events {
                worker_connections 4096;
                use epoll;
                multi_accept on;
        }

        http {
                include /etc/nginx/mime.types;
                default_type application/octet-stream;
                server_tokens off;
                server_names_hash_bucket_size 128;


                map $http_user_agent $redirect_location {
                    default         /web;
                    ~*iPhone        /ios;
                    ~*Android       /android;
                }

                log_format json_log escape=json '{'
                        '"connection": "$connection", ' # connection serial number
                        '"connection_requests": "$connection_requests", ' # number of requests made in connection
                        '"pid": "$pid", ' # process pid
                        '"request_id": "$request_id", ' # the unique request id
                        '"request_length": "$request_length", ' # request length (including headers and body)
                        '"remote_addr": "$remote_addr", ' # client IP
                        '"remote_user": "$remote_user", ' # client HTTP username
                        '"remote_port": "$remote_port", ' # client port
                        '"time": "$time_iso8601", ' # local time in the ISO 8601 standard format
                        '"request": "$request", ' # full path no arguments if the request
                        '"full_path": "$request_uri", ' # full path and arguments if the request
                        '"args": "$args", ' # args
                        '"downstream_service": "$http_service", ' # args
                        '"status": "$status", ' # response status code
                        '"body_bytes_sent": "$body_bytes_sent", ' # the number of body bytes exclude headers sent to a client
                        '"bytes_sent": "$bytes_sent", ' # the number of bytes sent to a client
                        '"http_referer": "$http_referer", ' # HTTP referer
                        '"http_user_agent": "$http_user_agent", ' # user agent
                        '"http_x_forwarded_for": "$http_x_forwarded_for", ' # http_x_forwarded_for
                        '"http_host": "$http_host", ' # the request Host: header
                        '"server_name": "$server_name", ' # the name of the vhost serving the request
                        '"request_time": "$request_time", ' # request processing time in seconds with msec resolution
                        '"upstream": "$upstream_addr", ' # upstream backend server for proxied requests
                        '"upstream_connect_time": "$upstream_connect_time", ' # upstream handshake time incl. TLS
                        '"upstream_header_time": "$upstream_header_time", ' # time spent receiving upstream headers
                        '"upstream_response_time": "$upstream_response_time", ' # time spend receiving upstream body
                        '"upstream_response_length": "$upstream_response_length", ' # upstream response length
                        '"upstream_cache_status": "$upstream_cache_status", ' # cache HIT/MISS where applicable
                        '"method": "$request_method", ' # request method
                        '"http_cf_ray": "$http_cf_ray" '
                        '}';
                access_log /var/log/nginx/access.log json_log;

                server {
                    listen 80;
                    underscores_in_headers on;
                    client_max_body_size 2M;
                    gzip on;
                    gzip_min_length 200;
                    gzip_types text/plain application/json;
                    error_log /var/log/nginx/error.log;

                    location /nginx-health {
                        return 200 "healthy\n";
                        access_log off;
                    }
                    location /.well-known/assetlinks.json {
                        add_header  Content-Type    application/json;
                        return 200 '[{"relation":["delegate_permission/common.handle_all_urls"],"target":{"namespace":"android_app","package_name":"com.ninetyonesquarefeet.sqft91","sha256_cert_fingerprints":["EA:F6:A3:14:EF:2B:EF:23:5F:FE:0C:4C:66:E3:AB:39:0C:62:D7:4D:C1:7D:9D:97:A4:1C:BA:F7:C6:51:7D:EE"]}}]';
                        access_log off;
                    }
                    location /.well-known/apple-app-site-association {
                        add_header  Content-Type    application/json;
                        return 200 '{"applinks": {"apps": [],"details": [{"appID": "39GM954D6N.io.rdash.app","paths": ["/project/*/recce/*", "/task/*", "/project/*/work-report/materials/received-stocks/*", "/project/*/work-report/materials/transferred-stocks/*", "/project/*/snag/*", "/direct-login/*",
                                    "/leads-management/my-leads/board/*/lead/*/details/*", "/task-manager/*",
                                    "/project/*/work-report/activity-schedule/*", "/project/*/detail/*",
                                    "/project/*/work-report/progress-report", "my-reports"]}]},"webcredentials": {"apps": ["39GM954D6N.io.rdash.app"]}}';
                        access_log off;
                    }
                    location ~* \.(woff|woff2|ttf|otf|eot)$ {
                        root /etc/nginx/fonts;
                        default_type text/css;
                        add_header Access-Control-Allow-Origin *;
                    }
                    location / {
                        error_page 418 = @redirect;
                        if ($redirect_location) {
                            return 418;
                        }
                    }

                    location @redirect {
                        if ($redirect_location = /web) {
                            rewrite ^ https://app.rdash.io$uri permanent;
                        }
                        if ($redirect_location = /ios) {
                            return 301 https://apps.apple.com/in/app/rdash/id6458734577;
                        }
                        if ($redirect_location = /android) {
                            return 301 https://play.google.com/store/apps/details?id=com.ninetyonesquarefeet.sqft91;
                        }
                    }
                }
        }
kind: ConfigMap
metadata:
    name: link-rdash-nginx-env
