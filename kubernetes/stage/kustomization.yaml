apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  # Ingress
  - ingress/ingress.yaml
  # Service
  - service/rdash-backend.yaml
  - service/redis.yaml
  # Deployment
  - deployment/rdash-backend.yaml
  - deployment/redis.yaml
  - deployment/celery-beat.yaml
  # ConfigMap
  - config/configmap.yaml
  # Worker
  - deployment/worker.yaml
  # Job
  - job/migrations.yaml
  # Role
  - role/role.yaml
  # SA
  - service-accounts/token.yaml

images:
  - name: ghcr.io/rdash-tech/rdash-api
    newName: ghcr.io/rdash-tech/rdash-api
    newTag: latest
