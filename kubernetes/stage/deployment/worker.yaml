apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: rdash-worker
  name: rdash-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: rdash-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: rdash-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: rdash-worker
          resources:
            requests:
              cpu: 700m
              memory: 1Gi
            limits:
              cpu: 1000m
              memory: 2Gi
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command:
          - /bin/sh
          - -c
          - celery -A rollingbanners worker -l info -c 1 -Q callback_queue,callback_processing_queue,request_action_queue,whatsapp_notification_queue,whatsapp_processing_queue,failed_whatsapp_callback_queue,notification_queue,misconfig_resolver_queue,misconfig_schedular_queue,email_queue,schedular_worker_queue,excel_sync_queue,custom_tasks_queue,template_api_queue
      restartPolicy: Always
      nodeSelector:
        type: SPOT
      tolerations:
      - effect: NoSchedule
        key: type
        operator: Equal
        value: spot
      - effect: NoSchedule
        key: kubernetes.azure.com/scalesetpriority
        operator: Equal
        value: spot
