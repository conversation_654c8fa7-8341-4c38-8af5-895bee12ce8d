apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  labels:
    app: redis
    deployment: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
      deployment: redis
  template:
    metadata:
      labels:
        app: redis
        deployment: redis
    spec:
      containers:
        - name: redis
          image: redis:latest
          ports:
            - containerPort: 6379
              name: redis
              protocol: TCP
          resources:
            requests:
              ephemeral-storage: "1Gi"
          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - redis-cli ping || exit 1
            failureThreshold: 3
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
      nodeSelector:
        type: ONDEMAND
