apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    application: rdash-backend
  name: rdash-backend
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: rdash-backend
      application: rdash-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: rdash-backend
        application: rdash-backend
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: rdash-backend
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: "750m"
              memory: "1500Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "1500m"
              memory: "3Gi"
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          ports:
            - containerPort: 8000
              protocol: TCP
              name: http
          livenessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: api.stage.rdash.dev
              path: /healthz
              port: 8000
            initialDelaySeconds: 60
            timeoutSeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: api.stage.rdash.dev
              path: /healthz
              port: 8000
            initialDelaySeconds: 60
            timeoutSeconds: 30
            periodSeconds: 30
            failureThreshold: 3
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      nodeSelector:
        type: SPOT
      tolerations:
      - effect: NoSchedule
        key: type
        operator: Equal
        value: spot
      - effect: NoSchedule
        key: kubernetes.azure.com/scalesetpriority
        operator: Equal
        value: spot