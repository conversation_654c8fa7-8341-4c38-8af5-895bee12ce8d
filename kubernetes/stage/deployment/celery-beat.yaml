apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: celery-beat
  name: celery-beat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: celery-beat
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: celery-beat
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: celery-beat
          resources:
            requests:
              cpu: "100m"
              memory: "500Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "100m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command:
          - /bin/sh
          - -c
          - celery -A rollingbanners beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
      restartPolicy: Always
      nodeSelector:
        type: SPOT
      tolerations:
      - effect: NoSchedule
        key: type
        operator: Equal
        value: spot
      - effect: NoSchedule
        key: kubernetes.azure.com/scalesetpriority
        operator: Equal
        value: spot
