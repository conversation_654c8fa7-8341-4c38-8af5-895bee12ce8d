apiVersion: batch/v1
kind: Job
metadata:
  name: migrate-stage-db-restore
  namespace: db-utils
spec:
  ttlSecondsAfterFinished: 30
  template:
    metadata:
      labels:
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: aws-federation
      containers:
        - name: db-restore
          image: public.ecr.aws/x5v9a0b5/psql-worker:latest
          envFrom:
            - secretRef:
                name: migrate-stage-db-restore
          command:
          - "/bin/bash"
          - "-c"
          - |
            set -e

            ## Assumes rdash_db and rdash_user already exist in the target database
            ## This job will restore the database from a dump file stored in S3

            export PGPASSWORD=${PGPASSWORD}

            aws s3 cp ${S3_URI}/${DUMP_FILE} ${DUMP_FILE} --only-show-errors --no-progress

            psql -h ${HOST} -U ${USER} -p 5432 -d postgres << EOF
                  ALTER DATABASE rdash_db OWNER TO ${USER};
                  SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='rdash_db';
            EOF

            pg_restore --no-acl --no-owner --verbose --create --clean --if-exists --exit-on-error -h ${HOST} -p 5432 -U ${USER} -d postgres ${DUMP_FILE}

            psql -h ${HOST} -p 5432 -U ${USER} -d rdash_db << EOF
                  -- Grant privileges on existing objects
                  GRANT ALL ON SCHEMA public TO rdash_user;
                  GRANT ALL ON ALL TABLES IN SCHEMA public TO rdash_user;
                  GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO rdash_user;

                  -- Set default privileges for future objects
                  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO rdash_user;
                  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO rdash_user;

                  -- Reassign ownership from the old user to the new one
                  REASSIGN OWNED BY stage_admin TO rdash_user;

                  -- Finally, make rdash_user the owner of the database itself
                  ALTER DATABASE rdash_db OWNER TO rdash_user;
            EOF
      restartPolicy: Never
      nodeSelector:
        kubernetes.io/os: linux
        kubernetes.io/arch: amd64
  backoffLimit: 0
