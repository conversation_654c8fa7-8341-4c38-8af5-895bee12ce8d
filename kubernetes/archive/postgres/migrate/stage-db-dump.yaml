apiVersion: batch/v1
kind: Job
metadata:
  name: migrate-stage-db-dump
  namespace: db-utils
spec:
  ttlSecondsAfterFinished: 30
  template:
    spec:
      containers:
        - name: db-dump
          image: public.ecr.aws/x5v9a0b5/psql-worker:latest
          envFrom:
            - secretRef:
                name: migrate-stage-db-dump
          command:
          - "/bin/bash"
          - "-c"
          - |
            set -e
            export PGPASSWORD=${PGPASSWORD}
            pg_dump --no-acl --no-owner -h ${HOST} -p ${PORT} -U ${USER} -F c -b -v -f ${DUMP_FILE} ${DATABASE}
            aws s3 mv ${DUMP_FILE} ${S3_URI}/${DUMP_FILE} --only-show-errors --no-progress
      restartPolicy: Never
      serviceAccount: common-bucket-sa
      serviceAccountName: common-bucket-sa
  backoffLimit: 0
