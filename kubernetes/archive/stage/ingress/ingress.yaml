apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-stage-rdash-io-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt"
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~* "^/api/v2/callback(/.*)?$" {
        return 403;
      }

spec:
  tls:
    - hosts:
        - api.stage.rdash.dev
        - flower.stage.rdash.dev
      secretName: api-rdash-dev-letsencrypt
  rules:
    - host: api.stage.rdash.dev
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: rdash-backend
                port:
                  number: 80

    - host: flower.stage.rdash.dev
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: celery-dashboard
                port:
                  number: 80
