apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-stage-rdash-uploads-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt"
    nginx.ingress.kubernetes.io/proxy-body-size: 2g
    nginx.ingress.kubernetes.io/proxy-max-temp-file-size: 2048m
    nginx.ingress.kubernetes.io/proxy-buffer-size: 8k
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  tls:
    - hosts:
        - api.stage.rdash.dev
      secretName: api-rdash-dev-letsencrypt
  rules:
    - host: api.stage.rdash.dev
      http:
        paths:
          - pathType: ImplementationSpecific
            path: "/api/v1/project/.*/design/file/"
            backend:
              service:
                name: rdash-backend-uploads
                port:
                  number: 80
