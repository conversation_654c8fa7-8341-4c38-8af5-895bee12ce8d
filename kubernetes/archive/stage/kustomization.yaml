apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  # Ingress
  - ingress/ingress.yaml
  - ingress/rdash-backend-uploads.yaml
  # Service
  - service/rdash-backend.yaml
  - service/rdash-backend-uploads.yaml
  - service/redis-service.yaml
  - service/flower-service.yaml
  # Deployment
  - deployment/rdash-backend.yaml
  - deployment/rdash-backend-uploads.yaml
  - deployment/redis-deployment.yaml
  - deployment/flower-deployment.yaml
  - deployment/celery-beat-deployment.yaml
  # ConfigMap
  - config/configmap.yaml
  # Worker
  - deployment/worker-notification.yaml
  - deployment/worker-email-notification.yaml
  - deployment/worker-request-action.yaml
  - deployment/worker-whatsapp-notification.yaml
  - deployment/worker-callback.yaml
  - deployment/worker.yaml
  - deployment/worker-misconfig-resolver.yaml
  - deployment/worker-excel-sync.yaml
  - deployment/worker-schedular.yaml
  - deployment/worker-custom-task.yaml
  - deployment/template-api-worker.yaml
  # Job
  - job/migrations.yaml
  # Role
  - role/role.yaml
  # SA
  - service-accounts/token.yaml

images:
  - name: ghcr.io/rdash-tech/rdash-api
    newName: ghcr.io/rdash-tech/rdash-api
    newTag: latest
