apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: token-generator-role
rules:
- apiGroups: [""]
  resources: ["serviceaccounts/token"]
  resourceNames: ["dashboard-admin-sa"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: ["dashboard-admin-token-secret"]
  verbs: ["get", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: token-generator-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: token-generator-role
subjects:
- kind: ServiceAccount
  name: token-generator-sa
  namespace: stage
