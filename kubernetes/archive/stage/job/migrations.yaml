apiVersion: batch/v1
kind: Job
metadata:
  labels:
    app: rdash-backend
  name: rdash-backend-migrations
spec:
  ttlSecondsAfterFinished: 5
  template:
    spec:
      containers:
        - name: rdash-backend-migrations
          image: ghcr.io/rdash-tech/rdash-api
          command: ["python", "manage.py", "migrate"]
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
      restartPolicy: Never
  backoffLimit: 5
