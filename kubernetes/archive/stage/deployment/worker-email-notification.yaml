apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: email-worker
  name: email-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: email-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: email-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: email-worker
          resources:
            requests:
              cpu: "150m"
              memory: "300Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "250m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners worker --without-gossip --without-mingle -l INFO -c 1 -Q email_queue",
            ]
      restartPolicy: Always
