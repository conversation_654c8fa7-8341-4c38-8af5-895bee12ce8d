apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: whatsapp-notification-worker
  name: whatsapp-notification-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: whatsapp-notification-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: whatsapp-notification-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: whatsapp-notification-worker
          resources:
            requests:
              cpu: "50m"
              memory: "400Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "65m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners worker --without-gossip --without-mingle -l INFO -c 1 -Q whatsapp_notification_queue,whatsapp_processing_queue",
            ]
      restartPolicy: Always
