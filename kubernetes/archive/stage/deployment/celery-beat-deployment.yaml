apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: celery-beat
  name: celery-beat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: celery-beat
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: celery-beat
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: celery-beat
          resources:
            requests:
              cpu: "50m"
              memory: "400Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "65m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler",
            ]
      restartPolicy: Always
