apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: celery-dashboard
  name: celery-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-dashboard
  template:
    metadata:
      labels:
        app: celery-dashboard
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: celery-dashboard
          resources:
            requests:
              cpu: "250m"
              memory: "500Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "300m"
              memory: "750Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners flower --basic_auth=91admin:admin@9191 -l --port=8888",
            ]
      restartPolicy: Always
