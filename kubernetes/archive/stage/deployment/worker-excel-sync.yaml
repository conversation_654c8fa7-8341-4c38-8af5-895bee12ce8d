apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: excel-sync
  name: rdash-worker-for-sheet-sync
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: excel-sync
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: excel-sync
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: excel-sync-worker
          resources:
            requests:
              cpu: "250m"
              memory: "500Mi"
            limits:
              cpu: "350m"
              memory: "600Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners worker -l info -c 1 -Q excel_sync_queue",
            ]
      restartPolicy: Always
