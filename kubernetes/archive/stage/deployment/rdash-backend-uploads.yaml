apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    application: rdash-backend-uploads
  name: rdash-backend-uploads
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: rdash-backend
      application: rdash-backend-uploads
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: rdash-backend
        application: rdash-backend-uploads
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: rdash-backend-uploads
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: "700m"
              memory: "1Gi"
              ephemeral-storage: "2Gi"
            limits:
              cpu: "1000m"
              memory: "2Gi"
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          ports:
            - containerPort: 8000
              protocol: TCP
          livenessProbe:
            httpGet:
              httpHeaders:
              - name: Host
                value: api.stage.rdash.dev
              path: /healthz
              port: 8000
            initialDelaySeconds: 90
            timeoutSeconds: 15
            periodSeconds: 5
            failureThreshold: 2
          readinessProbe:
            httpGet:
              httpHeaders:
              - name: Host
                value: api.stage.rdash.dev
              path: /healthz
              port: 8000
            initialDelaySeconds: 90
            timeoutSeconds: 15
            periodSeconds: 5
            failureThreshold: 2
      restartPolicy: Always
      terminationGracePeriodSeconds: 5
