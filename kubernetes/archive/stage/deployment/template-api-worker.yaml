apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: template-api-worker
  name: template-api-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: template-api-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: template-api-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: template-api-worker
          resources:
            requests:
              cpu: "150m"
              memory: "300Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "250m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command:
            - /bin/sh
            - -c
            - |
              celery -A rollingbanners worker -l INFO -c 1 -Q template_api_queue
      restartPolicy: Always