apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    application: rdash-backend
  name: rdash-backend
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: rdash-backend
      application: rdash-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: rdash-backend
        application: rdash-backend
    spec:
      containers:
        - image: 548568290184.dkr.ecr.ap-south-1.amazonaws.com/nginx:rdash-api.dev.v2
          name: nginx
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: "20m"
              memory: "100Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "50m"
              memory: "200Mi"
          ports:
            - containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 10
            timeoutSeconds: 1
            periodSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 20
            timeoutSeconds: 1
            periodSeconds: 5
            failureThreshold: 3
        - image: ghcr.io/rdash-tech/rdash-api
          name: rdash-backend
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: "700m"
              memory: "1500Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "1000m"
              memory: "3Gi"
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          ports:
            - containerPort: 8000
              protocol: TCP
      restartPolicy: Always
      terminationGracePeriodSeconds: 5
