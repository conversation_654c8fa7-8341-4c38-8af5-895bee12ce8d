apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: callback-worker
  name: callback-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: callback-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: callback-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: callback-worker
          resources:
            requests:
              cpu: "150m"
              memory: "300Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "250m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners worker -l info -c 1 -Q callback_queue,callback_processing_queue",
            ]
      restartPolicy: Always
