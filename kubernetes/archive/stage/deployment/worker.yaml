apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: rdash-worker
  name: rdash-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: rdash-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: rdash-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: rdash-worker
          resources:
            requests:
              cpu: "50m"
              memory: "400Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "65m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args: ["-c", "celery -A rollingbanners worker -l info -c 1"]
      restartPolicy: Always
