apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rdash-backend
    worker: misconfig-resolver-worker
  name: misconfig-resolver-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rdash-backend
      worker: misconfig-resolver-worker
  template:
    metadata:
      labels:
        app: rdash-backend
        worker: misconfig-resolver-worker
    spec:
      containers:
        - image: ghcr.io/rdash-tech/rdash-api
          name: misconfig-resolver-worker
          resources:
            requests:
              cpu: "50m"
              memory: "400Mi"
              ephemeral-storage: "1Gi"
            limits:
              cpu: "65m"
              memory: "500Mi"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: rdash-backend-env
            - secretRef:
                name: rdash-backend-env-secret
          command: ["/bin/sh"]
          args:
            [
              "-c",
              "celery -A rollingbanners worker --without-gossip --without-mingle -l INFO -c 1 -Q misconfig_resolver_queue,misconfig_schedular_queue",
            ]
