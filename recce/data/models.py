from urllib.parse import quote

from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.db import IntegrityError
from django.db.models import Q, UniqueConstraint
from django.utils.translation import gettext_lazy as _
from phonenumber_field import modelfields

from common.constants import FILE_FIELD_MAX_LENGTH
from common.exceptions import BaseValidationError
from common.helpers import get_upload_path
from common.mixins import HashidsModelMixin, TableNameMixin
from common.models import (
    BaseHistory,
    BaseModel,
    CreateDeleteModel,
    CreateModel,
    CreateModelWithDateOverride,
    CreateUpdateDeleteModel,
    DeleteModel,
    UpdateDeleteModel,
)
from core.models import Organization, Role
from project.data.models import Project, User
from reccev2.data.querysets import (
    RecceDropDownOptionQuerySet,
    RecceFieldQuerySet,
    RecceSectionQuerySetManager,
    RecceTemplateQuerySet,
)
from reccev2.domain.exceptions import DuplicateTemplateNameException, RecceFieldException
from rollingbanners.hash_id_converter import HashIdConverter as Hc


class RecceHistory(BaseHistory):
    pass


class ConfigManager(models.Manager):
    def get_queryset(self):
        return super(ConfigManager, self).get_queryset().filter(is_active=True)


class Config(models.Model):
    # TODO: Update fixtures.
    # TODO: rename to RecceTask and migrate cloud files as well
    title = models.CharField(max_length=250, unique=True)
    parent = models.ForeignKey("self", on_delete=models.PROTECT, null=True, blank=True, related_name="children")
    key = models.CharField(max_length=50, default="", unique=True)
    is_checked = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    demo_video = models.FileField(
        upload_to=get_upload_path, default=None, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH
    )
    position = models.PositiveSmallIntegerField(default=0)

    objects = ConfigManager()

    class Meta:
        ordering = ["position"]

    @staticmethod
    def get_children(recce_config, config_id_list):
        tree = recce_config.get_dict(config_id_list)
        if recce_config.children.filter(is_active=True).exists():
            children = list()
            for child in recce_config.children.filter(is_active=True):
                children.append(Config.get_children(child, config_id_list))
            tree["children"] = children
        return tree

    @staticmethod
    def get_all(config_id_list):
        # TODO: remove number of queries
        configs = list()
        for recce_config in Config.objects.filter(parent__isnull=True, is_active=True):
            configs.append(Config.get_children(recce_config, config_id_list))
        return configs

    @property
    def is_parent(self):
        return True if self.children.count() else False

    def get_dict(self, config_id_list):
        return {
            "title": self.title,
            "id": self.key,
            "is_checked": (
                self.is_checked if config_id_list is None else (True if str(self.key) in config_id_list else False)
            ),
            "is_parent": self.is_parent,
        }

    def __str__(self):
        return self.title


class RecceTemplate(CreateUpdateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="recce_templates")
    instructions = models.JSONField(default=list)
    sections = models.JSONField(default=list)
    name = models.CharField(max_length=256)
    last_used_at = models.DateTimeField(null=True, blank=True, default=None)
    position = models.IntegerField(null=True, default=None)

    objects = RecceTemplateQuerySet.as_manager()

    class Meta:
        constraints = [
            UniqueConstraint(
                name="recce_template_name_organization_unique",
                fields=["organization", "name"],
                condition=Q(deleted_at__isnull=True),
            )
        ]
        db_table = "recce_templates"
        verbose_name = _("Recce Template")
        verbose_name_plural = _("Recce Templates")

    def save(self, *args, **kwargs):
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:
            if "recce_template_name_organization_unique" in str(e):
                raise DuplicateTemplateNameException("Template name already exists for this organization")
            raise e


class Recce(UpdateDeleteModel, HashidsModelMixin):
    class RecceVersionDuplicateException(BaseValidationError):
        pass

    project_id: int

    PENDING = "pending"
    SUBMITTED = "completed"
    NOT_STARTED = "not_started"
    APPROVED = "approved"
    SUBMISSION_STARTED = "submission_started"
    UNAPPROVE = "unapprove"

    class StatusChoices(models.TextChoices):
        PENDING = "pending", _("Pending")
        SUBMITTED = "completed", _("Submitted")
        NOT_STARTED = "not_started", _("Not Started")
        APPROVED = "approved", _("Approved")
        MODIFIED = "modified", _("Modified")
        SUBMISSION_STARTED = "submission_started", _("Submission Started")
        UNAPPROVE = "unapprove", _("Unapproved")

    project = models.ForeignKey(Project, verbose_name=_("project"), on_delete=models.RESTRICT, related_name="recces")
    name = models.CharField(max_length=100, null=True, default=None, blank=True)
    version = models.SmallIntegerField(default=1)
    created_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_links_created", blank=True, null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=StatusChoices.choices, null=True, blank=True)
    location = models.PointField(verbose_name=_("location"), null=True)
    remark = models.TextField(null=True)  # deferred
    instructions = models.JSONField(null=True, blank=True, default=list)  # migrated from kam_remark
    template = models.ForeignKey(
        RecceTemplate, on_delete=models.RESTRICT, related_name="+", null=True, blank=True
    )  # null for old recces

    # Old recce data which need to be migrated
    check_list = models.JSONField(default=list)
    started_at = models.DateTimeField(null=True)  # will be used for recce history
    submitted_at = models.DateTimeField(null=True)  # will be used for recce history
    ended_at = models.DateTimeField(null=True)  # will be used for recce history
    due_at = models.DateTimeField(null=True, blank=True)  # migrated from project.recce_due_at
    link = models.URLField(null=True)  # remove in cleanup
    key_account_manager = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recces_managed", blank=True, null=True
    )  # remove in cleanup
    recce_project_engineer = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recces_engineered", blank=True, null=True
    )  # remove in cleanup
    kam_remark = models.TextField(null=True)  # remove in cleanup
    average_rating = models.DecimalField(null=True, blank=True, max_digits=4, decimal_places=2)  # remove in cleanup

    users = models.ManyToManyField(User, through="RecceUser", related_name="recces_assigned")

    submitted_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recces_submitted", blank=True, null=True
    )  # remove in cleanup
    template_sections = models.JSONField(
        null=True, blank=True, default=list
    )  # migrated from default template on the basis of recce check_list

    section_json = models.JSONField(default=dict)

    @property
    def recce_link(self):
        return f"project/{Hc.encode(self.project_id)}/recce/{Hc.encode(self.pk)}?recce_id={Hc.encode(self.pk)}"

    class Meta:
        ordering = ("-version",)
        unique_together = ["project", "version"]

    def save(self, *args, **kwargs):
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "recce_recce_project_id_version" in str(e):
                raise self.RecceVersionDuplicateException("Recce version already exists for this project")
            raise e

    # @property
    # def status(self):
    #     if self.ended_at:
    #         return self.SUBMITTED
    #     elif self.started_at:
    #         return self.PENDING
    #     return self.NOT_STARTED

    def __str__(self):
        return f"{self.project.name} : {self.pk}"

    def get_recce_link(self):
        return f"{self.recce_link}&store={quote(self.project.name)}&version={self.version}"


class RecceSection(CreateDeleteModel):
    recce = models.ForeignKey(Recce, on_delete=models.RESTRICT, related_name="sections")
    name = models.TextField()
    updated_at = models.DateTimeField(null=True, blank=True)
    is_custom = models.BooleanField(default=False)

    objects = RecceSectionQuerySetManager()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["recce", "name"], name="unique_section_name", condition=Q(deleted_at__isnull=True)
            ),
        ]
        db_table = "recce_sections"
        verbose_name = _("Recce Section")
        verbose_name_plural = _("Recce Sections")


class RecceTemplateRole(CreateModel):
    class RecceTemplateRoleChoices(models.TextChoices):
        STAKEHOLDER = "stakeholder"
        ASSIGNEE = "assignee"

    template = models.ForeignKey(RecceTemplate, on_delete=models.RESTRICT, related_name="+")
    role = models.ForeignKey(Role, on_delete=models.RESTRICT, related_name="+")
    type = models.CharField(max_length=50, choices=RecceTemplateRoleChoices.choices)

    class Meta:
        unique_together = ["template", "role", "type"]
        db_table = "recce_template_roles"
        verbose_name = _("Recce Template Role")
        verbose_name_plural = _("Recce Template Roles")


class RecceUser(BaseModel):
    class RecceUserChoices(models.TextChoices):
        STAKEHOLDER = "stakeholder"
        DYNAMIC = "dynamic"
        ASSIGNED = "assigned"

    recce = models.ForeignKey(Recce, on_delete=models.RESTRICT, related_name="recce_users")
    user = models.ForeignKey(User, on_delete=models.RESTRICT)
    assigned_at = models.DateTimeField(auto_now_add=True)
    role = models.ForeignKey(Role, on_delete=models.RESTRICT, related_name="+", null=True, blank=True)
    # TODO: data migration for role and type will be needed
    # TODO: and nullable constraints will have to be removed once the data is migrated
    type = models.CharField(max_length=50, choices=RecceUserChoices.choices, null=True, blank=True)


class RecceFileManager(models.Manager):
    # TODO: revisit this queryset
    def get_queryset(self):
        return super(RecceFileManager, self).get_queryset().filter(deleted_at__isnull=True)


class RecceField(CreateUpdateDeleteModel):
    INDEX_NAME = "recce_section_field_name_unique"

    class FieldChoices(models.TextChoices):
        RICH_TEXT = "rich_text"
        TEXT = "text"
        INTEGER = "integer"
        DECIMAL = "decimal"
        BOOLEAN = "boolean"
        DROPDOWN = "dropdown"
        PHONE_NUMBER = "phone_number"
        SPACES = "spaces"
        FILES = "files"
        DATE = "date"
        ATTACHMENT = "attachment"
        MULTI_DROPDOWN = "multi_dropdown"

    uuid = models.UUIDField()
    name = models.CharField(max_length=256)
    type = models.CharField(max_length=50, choices=FieldChoices.choices)
    # TODO : CustomDateField  Date  in fieldchoices
    data = models.JSONField(default=None, null=True, blank=True)
    section = models.ForeignKey(RecceSection, on_delete=models.RESTRICT, related_name="fields")
    is_required = models.BooleanField(default=False)
    is_custom = models.BooleanField(default=False)
    objects = RecceFieldQuerySet.as_manager()

    def save(self, *args, **kwargs):
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:
            if "recce_section_field_name_unique" in str(e):
                raise RecceFieldException(_("Field Name Duplicated"))

    class Meta:
        constraints = [
            UniqueConstraint(
                name="recce_section_field_name_unique",
                fields=["section", "name"],
                condition=Q(deleted_at__isnull=True),
            )
        ]
        db_table = "recce_fields"
        verbose_name = _("Recce Field")
        verbose_name_plural = _("Recce Fields")


class RecceDropDownOption(DeleteModel):
    data = models.JSONField(null=True, blank=True)
    field = models.OneToOneField(RecceField, on_delete=models.RESTRICT, related_name="options", null=True, blank=True)
    objects = RecceDropDownOptionQuerySet.as_manager()

    class Meta:
        db_table = "recce_dropdown_options"
        verbose_name = _("Recce DropDown Options")
        verbose_name_plural = _("Recce DropDown Options")


class RecceFile(models.Model, HashidsModelMixin, TableNameMixin):
    APP = "app"  # migration from app to android and ios
    WEB = "web"
    IOS = "ios"
    ANDROID = "android"

    SOURCE_CHOICES = [(WEB, "web"), (ANDROID, "android"), (IOS, "ios"), (APP, "app")]

    recce = models.ForeignKey(Recce, verbose_name=_("recce"), on_delete=models.RESTRICT, related_name="files")
    name = models.CharField(max_length=256, null=True)
    field = models.ForeignKey(RecceField, on_delete=models.RESTRICT, related_name="files", null=True, blank=True)
    type = models.CharField(max_length=10)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_files_uploaded", blank=True, null=True
    )
    detail = models.TextField(null=True)
    meta = models.JSONField(null=True)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)

    # TODO: old data need to be migrated
    task = models.CharField(max_length=50)
    source = models.CharField(_("source"), choices=SOURCE_CHOICES, default=ANDROID, max_length=10)
    version = models.SmallIntegerField(default=1)
    deleted_at = models.DateTimeField(null=True)
    deleted_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_files_deleted", blank=True, null=True
    )
    updated_at = models.DateTimeField(null=True)
    updated_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_files_updated", blank=True, null=True
    )

    history = GenericRelation(RecceHistory)

    objects = RecceFileManager()

    @property
    def name_property(self):
        if self.name:
            return self.name
        if self.file:
            return self.file.url.split("/")[-1]  # noqa


# class RecceFileElement(CreateUpdateDeleteModel):
#     class ElementShapeChoices(models.TextChoices):
#         LINE = "line"
#         TEXT = "text"
#         QUAD = "quad"

#     file = models.ForeignKey(RecceFile, on_delete=models.RESTRICT, related_name="file_elements")
#     points = models.JSONField(default=list)
#     uuid = models.UUIDField(default=None)
#     version = models.SmallIntegerField(default=1)
#     recce = models.ForeignKey(Recce, on_delete=models.RESTRICT, related_name="file_elements")
#     type = models.CharField(max_length=50, choices=ElementShapeChoices.choices)
#     el_element = models.ForeignKey(Element, on_delete=models.RESTRICT, related_name="+", null=True, blank=True)
#     meta_data = models.JSONField()

#     class Meta:
#         db_table = "recce_file_elements"
#         verbose_name = _("Recce File Element")
#         verbose_name_plural = _("Recce File Elements")


class RecceCheckpoint(models.Model):
    class RecceCheckpointActionChoices(models.TextChoices):
        # TODO: checkpoint need to be checked
        SUBMITTED = "submitted"
        ENDED = "ended"

    recce = models.ForeignKey(Recce, verbose_name=_("recce"), on_delete=models.RESTRICT, related_name="checkpoints")
    action = models.CharField(max_length=50, choices=RecceCheckpointActionChoices.choices, null=True, blank=True)
    task = models.CharField(max_length=50)
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="+", blank=True, null=True)
    reached_at = models.DateTimeField(auto_now_add=True)


class RecceSectionGuide(CreateDeleteModel):
    section = models.OneToOneField(RecceSection, on_delete=models.RESTRICT, related_name="guide")
    name = models.CharField(max_length=256)
    url = models.URLField()

    class Meta:
        db_table = "recce_section_guides"
        verbose_name = _("Recce Section Guide")
        verbose_name_plural = _("Recce Section Guides")


class RecceStatusHistory(CreateModelWithDateOverride):
    recce = models.ForeignKey(Recce, verbose_name=_("recce"), on_delete=models.RESTRICT, related_name="status_history")
    status = models.CharField(max_length=50, choices=Recce.StatusChoices.choices)

    class Meta:
        db_table = "recce_status_history"
        verbose_name = _("Recce Status History")
        verbose_name_plural = _("Recce Status Histories")


class RecceData(models.Model):
    recce = models.ForeignKey(Recce, verbose_name=_("recce"), on_delete=models.RESTRICT, related_name="data")
    task = models.CharField(max_length=50)
    data = models.JSONField(default=dict)
    submitted_at = models.DateTimeField(auto_now_add=True)
    submitted_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_data_submitted", blank=True, null=True
    )
    updated_at = models.DateTimeField(null=True)
    updated_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="recce_data_updated", blank=True, null=True
    )
    history = GenericRelation(RecceHistory)

    def save(self, *args, **kwargs):
        data = super().save(*args, **kwargs)

        return data


class RecceDevice(models.Model):
    recce = models.OneToOneField(Recce, primary_key=True, on_delete=models.CASCADE, related_name="device")
    phone_number = modelfields.PhoneNumberField(null=True)
    device_id = models.CharField(max_length=25, null=True)
    created_at = models.DateTimeField(auto_now_add=True)


class QualityCheckQuestion(models.Model):
    question = models.TextField()
    is_active = models.BooleanField(default=True)
    added_at = models.DateTimeField(auto_now_add=True)


class RecceFileThumbnail(models.Model):
    recce_file = models.OneToOneField(RecceFile, primary_key=True, on_delete=models.RESTRICT, related_name="thumbnail")
    thumbnail = models.ImageField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    created_at = models.DateTimeField(auto_now_add=True)


class RecceGuestUser(models.Model):
    recce = models.OneToOneField(Recce, primary_key=True, on_delete=models.RESTRICT, related_name="recce_guest")
    name = models.CharField(max_length=100)
    organization = models.CharField(max_length=100)
    phone_number = modelfields.PhoneNumberField()


class RecceClientPoc(BaseModel):
    recce = models.OneToOneField(Recce, primary_key=True, on_delete=models.RESTRICT, related_name="client_poc")
    phone_number = modelfields.PhoneNumberField()
    name = models.CharField(max_length=100)

    class Meta:
        db_table = "recce_client_poc"
