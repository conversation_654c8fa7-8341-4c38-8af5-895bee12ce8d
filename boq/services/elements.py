import collections
import decimal
from abc import ABC
from dataclasses import asdict
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional

import structlog
from django.db.models import Case, F, <PERSON>teger<PERSON>ield, Max, OuterRef, Q, Value, When
from django.db.models.functions import J<PERSON><PERSON>O<PERSON>, Lower, Trim
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError
from rest_framework.settings import api_settings

from authorization.domain.constants import Permissions
from boq.data.choices import BoqElementAction, BoqElementStatus, CustomElementType
from boq.data.entities import (
    BoqDataForExcelSheet,
    BoqElementBaseData,
    BoqElementCreateData,
    BoqElementEntityData,
    BoqElementGuidelineAttachmentData,
    BoqElementGuidelineData,
    BoqElementPreviewFileData,
    BoqElementProductionDrawingData,
)
from boq.data.models import (
    <PERSON>q<PERSON><PERSON>,
    BoqElementGuideline,
    BoqElementGuidelineAttachment,
    BoqElementPreviewFile,
    BoqElementProductionDrawing,
    BoqElementProductionDrawingTag,
    BoqSection,
)
from boq.data.selectors import (
    boq_element_current_version_list,
    fetch_boq_elements_prefetch_all,
    fetch_quotation_element_to_be_imported,
)
from boq.services.boq import update_boq_status
from boq.services.sections import boq_section_create_many
from boq.services.status_history import StatusService
from client.data.models import Client
from common import decorators
from common.element_base.entities import (
    ElementImportEntity,
    GuidelineAttachmentData,
    GuidelineData,
    PreviewFileData,
    ProductionDrawingData,
)
from common.element_base.mixins import ElementVersionService
from common.element_base.services import (
    ElementBaseService,
    ElementRelatedCommonServiceNew,
    ElementRelatedServiceNew,
)
from common.entities import ObjectStatus
from common.exceptions import (
    BoqElementCreateException,
    QuantityDimensionsMismatchError,
    QuantityValidationError,
    UOMConversionError,
    UOMValidationError,
)
from common.services import model_update, nested_object_segregation
from common.utils import validate_quantity_dimensions
from controlroom.data.querysets import QuerySet
from core.helpers import OrgPermissionHelper
from core.models import User
from crm.data.models import QuotationElement
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementCategory, OrganizationElement, SectionBase
from element.data.selectors import element_library_mapping_fetch_many, elements_fetch_by_ids
from element.domain.services import (
    element_category_org_mapping_create_many,
    get_element_code,
    position_reorder,
)
from order.data.models import VendorOrderElement
from project.data.models import Project
from ratecontract.data.selectors import rc_elements_fetch_by_ids
from work_progress_v2.interface.factory_helper import WorkProgressTimelineSync

logger = structlog.get_logger()


class BoqElementRelatedService(ElementRelatedServiceNew):
    preview_file = BoqElementPreviewFile
    guideline = BoqElementGuideline
    guideline_attachment = BoqElementGuidelineAttachment
    production_drawing = BoqElementProductionDrawing
    production_drawing_tag = BoqElementProductionDrawingTag


class BoqElementPersistenceService(ElementBaseService, ABC):
    element_model = BoqElement

    @classmethod
    def get_serial_numbers(cls, limit: int, client_id: int):
        distinct_serial_numbers = (
            BoqElement.objects.filter(
                client_id=client_id, custom_type=CustomElementType.BOQ, serial_number__isnull=False
            )
            .distinct("serial_number")
            .values_list("serial_number")
        )
        suggested_serial_numbers = (
            BoqElement.objects.annotate(plus_serial_number=F("serial_number") + 1)
            .filter(client_id=client_id, plus_serial_number__lt=100000, custom_type=CustomElementType.BOQ)
            .exclude(plus_serial_number__in=distinct_serial_numbers)
            .values_list("plus_serial_number", flat=True)
            .distinct()[:limit]
        )
        if suggested_serial_numbers:
            suggested_serial_numbers = list(suggested_serial_numbers)
            if len(suggested_serial_numbers) == limit:
                return suggested_serial_numbers
            max_serial_number = max(suggested_serial_numbers)
            return suggested_serial_numbers + [
                sno
                for sno in range(max_serial_number + 1, max_serial_number + (limit - len(suggested_serial_numbers)) + 1)
            ]
        else:
            if BoqElement.objects.filter(client_id=client_id).count() >= 100000:
                return []
            return [sno for sno in range(1, limit + 1)]

    @classmethod
    def get_elements_code(cls, category_ids: List[int], client_id: int) -> Dict:
        client_code = Client.objects.filter(organization_id=client_id).values_list("code", flat=True).first()
        categories: List[ElementCategory] = ElementCategory.objects.filter(id__in=category_ids)
        category_with_code_mapping = {}
        for category in categories:
            category_with_code_mapping[category.id] = str(client_code) + category.code
        return category_with_code_mapping

    @classmethod
    def create(
        cls,
        element_entity: BoqElementCreateData,
        save: bool = True,
    ) -> BoqElement:
        element_obj: BoqElement = super().create(element_entity, save=False)

        element_obj.brand_name = element_entity.brand_name
        element_obj.tax_percent = element_entity.tax_percent
        element_obj.hsn_code = element_entity.hsn_code

        element_obj.reported_quantity = element_entity.quantity
        if hasattr(element_entity, "element_id"):
            element_obj.element_id = element_entity.element_id
        if hasattr(element_entity, "position_ts"):
            element_obj.position_ts = element_entity.position_ts
        else:
            element_obj.position_ts = timezone.now()

        element_obj.boq_id = element_entity.boq_id

        if hasattr(element_entity, "organization_id"):
            element_obj.organization_id = element_entity.organization_id
        if hasattr(element_entity, "position"):
            element_obj.position = element_entity.position

        if hasattr(element_entity, "section_id"):
            element_obj.section_id = element_entity.section_id
        elif hasattr(element_entity, "section") and hasattr(element_entity.section, "id"):
            element_obj.section_id = element_entity.section.id

        if hasattr(element_entity, "version"):
            element_obj.version = element_entity.version
        else:
            element_obj.version = 1
        if hasattr(element_entity, "parent_element_id") and element_entity.parent_element_id:
            element_obj.parent_element_id = element_entity.parent_element_id
        if hasattr(element_entity, "quantity_dimensions"):
            element_obj.quantity_dimensions = element_entity.quantity_dimensions
        if hasattr(element_entity, "service_charge_percent"):
            element_obj.service_charge_percent = (
                element_entity.service_charge_percent
                if element_entity.service_charge_percent
                and element_entity.is_service_charge_with_base_amount is not None
                else 0
            )
            element_obj.is_service_charge_with_base_amount = element_entity.is_service_charge_with_base_amount
        if hasattr(element_entity, "budget_rate"):
            element_obj.budget_rate = element_entity.budget_rate
        if hasattr(element_entity, "quotation_element_id"):
            element_obj.quotation_element_id = element_entity.quotation_element_id

        if hasattr(element_entity, "discount_percent") and element_entity.discount_percent is not None:
            element_obj.discount_percent = element_entity.discount_percent

        if save:
            element_obj.full_clean()
            element_obj.save()
        return element_obj


class BoqElementUpdateService(BoqElementPersistenceService):
    @classmethod
    def get_base_amount(cls, client_rate: decimal.Decimal, quantity: decimal.Decimal) -> decimal.Decimal:
        return client_rate * quantity

    @classmethod
    def update(cls, element_data, element, categories_dict, user, save: bool = True):
        fields = [
            "name",
            "category_id",
            "uom",
            "description",
            "item_type_id",
            "quantity",
            "client_rate",
            "budget_rate",
            "section_id",
            "reported_quantity",
            "discount_percent",
            "service_charge_percent",
            "is_service_charge_with_base_amount",
            "quantity_dimensions",
            "uom",
            "tax_percent",
            "brand_name",
            "hsn_code",
        ]
        ignore_fields = [
            "quantity",
            "item_type_id",
            "updated_at",
            "updated_by_id",
            "client_rate",
            "section_id",
            "reported_quantity",
            "discount_percent",
            "service_charge_percent",
            "is_service_charge_with_base_amount",
            "quantity_dimensions",
            "tax_percent",
            "brand_name",
            "budget_rate",
            "hsn_code",
        ]
        if hasattr(element_data, "quantity_dimensions"):
            try:
                validate_quantity_dimensions(
                    quantity_dimensions=element_data.quantity_dimensions,
                    quantity=element_data.quantity,
                    quantity_uom=element_data.uom,
                )
            except QuantityDimensionsMismatchError as e:
                raise QuantityValidationError({"quantity_dimensions": e.message})
            except UOMConversionError as e:
                raise UOMValidationError({"quantity_dimensions": e.message})

        if hasattr(element_data, "current_is_service_charge_with_base_amount"):
            if element_data.current_is_service_charge_with_base_amount is None:
                element_data.service_charge_percent = 0

        element.name = element.name.strip()
        element.description = element.description.strip()
        element_data.name = element_data.name.strip()
        element_data.description = element_data.description.strip() if element_data.description else ""

        update_data = asdict(element_data)
        if element_data.quantity != element.quantity and element.update_method == ItemTypeUpdateMethodChoices.QUANTITY:
            if element.progress_quantity_input >= element_data.quantity:
                update_data["progress_percentage"] = 100
                update_data["progress_quantity_input"] = element_data.quantity
                fields.append("progress_quantity_input")
            else:
                update_data["progress_percentage"] = (element.progress_quantity_input / element_data.quantity) * 100
            fields.append("progress_percentage")

        if "section" in update_data and isinstance(update_data["section"], dict):
            update_data["section_id"] = update_data["section"].get("id")

        updated_element, _, updated_field = model_update(
            instance=element, fields=fields, data=update_data, updated_by_id=user.pk, save=False
        )

        element_to_updated_fields_map: dict[BoqElement, list[str]] = {}

        if "service_charge_percent" in updated_field and not OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_EDIT_SERVICE_CHARGE
        ):
            raise ValidationError({"service_charge": "User not allowed to update service charge for this project."})

        if "discount_percent" in updated_field and not OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_EDIT_DISCOUNT
        ):
            raise ValidationError({"discount_charge": "User not allowed to update discount for this project."})

        updated_element.discount_percent = element_data.discount_percent
        if (
            (
                element.element_status
                in [
                    BoqElementStatus.APPROVED,
                    BoqElementStatus.REQUESTED,
                    BoqElementStatus.CHANGE_REQUESTED,
                    BoqElementStatus.CANCELLATION_REQUESTED,
                ]
            )
            and updated_field
            and not {"reported_quantity", "budget_rate"}.intersection(set(updated_field))
        ):
            #  Only reported quantity and budget rate can be updated for all elements irrespective of status
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Element Update Not Allowed"})

        if "reported_quantity" in updated_field:
            updated_element.reported_quantity = element_data.reported_quantity
            updated_field.append("reported_quantity")

        elif "quantity" in updated_field and "reported_quantity" not in updated_field:
            updated_element.reported_quantity = updated_element.quantity
            updated_field.append("reported_quantity")

        if len(updated_field) == 0:
            return element, updated_field, element_to_updated_fields_map

        updated_data = list(set(updated_field[:]))

        """
        Not creating BoqElement History, Create BoqElementActionHistory instead
        for quantity or item_type or uom update in boq_element with action = ITEM_UPDATED
        """

        # if "item_type_id" in updated_field or "uom" in updated_field or:
        #     progress_percentage = 100 if getattr(updated_element, "progress_percentage") == 100 else 0

        #     if progress_percentage == 0:
        #         updated_element.progress_quantity_input = 0
        #         updated_field.append("progress_quantity_input")

        #     boq_element_item_type_history_create(
        #         element_ids_with_quantities=[
        #             {
        #                 "id": updated_element.id,
        #                 "quantity": updated_element.quantity,
        #                 "update_method": updated_element.update_method,
        #                 "progress_quantity_input": updated_element.progress_quantity_input,
        #                 "progress_percentage_input": updated_element.progress_percentage_input,
        #                 "uom": updated_element.uom,
        #                 "client_rate": updated_element.client_rate,
        #             }
        #         ],
        #         item_type_id=updated_element.item_type_id,
        #         created_by_id=user.pk,
        #         progress_percentage=progress_percentage,
        #         save=True,
        #     )
        #     setattr(updated_element, "progress_percentage", progress_percentage)
        #     setattr(updated_element, "status_updated_at", timezone.now())
        #     updated_field.append("progress_percentage")
        #     updated_field.append("status_updated_at")

        element_to_updated_fields_map[updated_element] = updated_field

        for field in ignore_fields:
            if field in updated_data:
                updated_data.remove(field)
        if not element.custom_type and updated_data:
            if "category_id" in updated_field:
                updated_element.code = get_element_code(
                    category_id=element_data.category_id, client_id=element.client_id
                )
                updated_field.append("code")

            updated_element.serial_number = cls.get_serial_numbers(limit=1, client_id=element.client_id)[0]
            updated_element.version = 1
            updated_element.custom_type = CustomElementType.BOQ
            updated_field.extend(["serial_number", "version", "custom_type"])

        elif element.custom_type == CustomElementType.BOQ and updated_data:
            if "category_id" in updated_field:
                new_category_code = categories_dict[updated_element.category_id].code
                updated_element.code = str(updated_element.code[:4]) + new_category_code
                updated_field.append("code")
            # if element.version > 1:
            #     updated_element.serial_number = cls.get_serial_numbers(limit=1, client_id=element.client_id)[0]
            #     updated_element.version = 1
            #     updated_field.extend(["serial_number", "version"])

        if save:
            updated_element.save(update_fields=updated_field)

        if save and ("item_type_id" in updated_field or "uom" in updated_field or "quantity" in updated_field):
            logger.info(
                "Updating Wp element on boq element update.",
                element_id=element.pk,
            )
            history_data = StatusService.create_history_entity(
                elements=[updated_element], action=BoqElementAction.ITEM_UPDATED, source=None
            )
            action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user.pk)
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user.pk,
                project_id=updated_element.boq_id,
                org_id=updated_element.organization_id,
            )

        return updated_element, updated_field, element_to_updated_fields_map

    @classmethod
    def filter_sections_to_be_created(cls, elements_data: List[BoqElementEntityData]) -> List[BoqSection]:
        sections = {}

        for element in elements_data:
            if (
                element.object_status != ObjectStatus.DELETE
                and element.section.id is None
                and element.section.name.lower() != SectionBase.DEFAULT_SECTION.lower()
            ):
                if element.section.name not in sections:
                    sections[element.section.name] = ""
        return list(sections.keys())

    @classmethod
    def create_and_map_new_section(
        cls, elements_data: list[BoqElementEntityData], user: User, boq_id: int, organization_id: int
    ) -> List[BoqElementEntityData]:
        new_sections = cls.filter_sections_to_be_created(elements_data=elements_data)
        sections = boq_section_create_many(
            boq_id=boq_id,
            created_by_id=user.pk,
            name_list=new_sections,
            organization_id=organization_id,
        )
        section_name_to_id_mapping = {section.name: section.id for section in sections}
        for element in elements_data:
            if element.section.name in section_name_to_id_mapping:
                element.section.id = section_name_to_id_mapping.get(element.section.name)

        return elements_data

    @classmethod
    def process_update(
        cls,
        elements_data,
        user: User,
        boq_id: int,
        client_id: int,
        organization_id: int,
        with_client_rate: bool,
    ):
        # Elements which are to be updated or created might contain new section
        elements_data = cls.create_and_map_new_section(
            elements_data=elements_data, user=user, boq_id=boq_id, organization_id=organization_id
        )
        for_create, for_updates, for_delete = nested_object_segregation(docs_list=elements_data)
        position_counter = BoqSectionService.get_section_name_to_latest_position_mapping(
            section_ids=[element_data.section.id for element_data in elements_data],
            boq_id=boq_id,
            organization_id=organization_id,
        )

        if for_create:
            BoqElementProcessService.process_bulk_create(
                element_entities=for_create,
                created_by_id=user.pk,
                client_id=client_id,
                boq_id=boq_id,
                organization_id=organization_id,
                position_counter=position_counter,
            )
        if for_updates:
            BoqElementProcessService.process_bulk_update_v2(
                element_entities=for_updates,
                with_client_rate=with_client_rate,
                user=user,
                boq_id=boq_id,
                organization_id=organization_id,
                position_counter=position_counter,
            )
        if for_delete:
            BoqElementProcessService.process_delete(
                delete_id_list=[delete_element.id for delete_element in for_delete],
                deleted_by_id=user.pk,
            )

        return for_create, for_updates, for_delete

    @classmethod
    def boq_element_client_rate_and_quantity_bulk_update(
        cls, entities: list[BoqDataForExcelSheet], updated_by_id: int, is_client_rate_editable: bool = True
    ):
        elements: dict = BoqElement.objects.filter(id__in=[entity.boq_element_id for entity in entities]).in_bulk()
        entity_dict = {entity.boq_element_id: entity for entity in entities}
        if is_client_rate_editable:
            fields = ["quantity", "client_rate"]
        else:
            fields = ["quantity"]
        elements_to_update = []

        for element_id, instance in elements.items():
            updated_element, is_updated, updated_field = model_update(
                instance=instance, fields=fields, data=entity_dict[element_id], updated_by_id=updated_by_id, save=False
            )

            if is_updated:
                elements_to_update.append(updated_element)

        fields.extend(["updated_at", "updated_by_id"])
        BoqElement.objects.bulk_update(elements_to_update, fields)


class BoqElementProcessService(ElementVersionService, BoqElementPersistenceService):
    @classmethod
    def process_create(
        cls,
        element_entity,
        created_by_id: int,
        client_id: int,
        boq_id: int,
        organization_id: Optional[int],
        serial_number: Optional[int],
        save: bool = True,
    ):
        """
        1. call create of base class
        2. call related services create method
        """
        if hasattr(element_entity, "quantity_dimensions"):
            try:
                validate_quantity_dimensions(
                    quantity_dimensions=element_entity.quantity_dimensions,
                    quantity=element_entity.quantity,
                    quantity_uom=element_entity.uom,
                )
            except QuantityDimensionsMismatchError as e:
                raise QuantityValidationError({"quantity_dimensions": e.message})
            except UOMConversionError as e:
                raise UOMValidationError({"quantity_dimensions": e.message})
            # element_entity.quantity_dimensions = element_entity.quantity_dimensions

        setattr(element_entity, "client_id", client_id)
        setattr(element_entity, "organization_id", organization_id)
        setattr(element_entity, "boq_id", boq_id)
        if hasattr(element_entity, "service_charge_percent"):
            setattr(element_entity, "service_charge_percent", element_entity.service_charge_percent)
            setattr(
                element_entity, "is_service_charge_with_base_amount", element_entity.is_service_charge_with_base_amount
            )
        if not hasattr(element_entity, "custom_type"):
            setattr(element_entity, "custom_type", CustomElementType.BOQ)

        if not hasattr(element_entity, "code"):
            setattr(
                element_entity,
                "code",
                get_element_code(category_id=element_entity.category_id, client_id=element_entity.client_id),
            )
        if not serial_number:
            serial_number = cls.get_serial_numbers(limit=1, client_id=element_entity.client_id)[0]
            setattr(element_entity, "serial_number", serial_number)
        else:
            setattr(element_entity, "serial_number", serial_number)
        setattr(element_entity, "created_by_id", created_by_id)
        element = cls.create(element_entity, save=save)
        if save:
            ElementRelatedCommonServiceNew.process_create(
                service=BoqElementRelatedService,
                data=element_entity,
                element=element,
                created_by_id=created_by_id,
                save=True,
            )
            update_boq_status(
                boq_id=boq_id,
                organization_id=organization_id,
                user_id=created_by_id,
            )

        return element

    @classmethod
    def prepare_data_entities(
        cls,
        created_element_objects,
        element_list,
    ):
        entity_list = []
        for new_element, element in zip(created_element_objects, element_list):
            preview_file_list = [
                BoqElementPreviewFileData(
                    object_status=ObjectStatus.ADD,
                    type=preview_file.type,
                    file=preview_file.file,
                    name=preview_file.name,
                    is_main=preview_file.is_main,
                    uploaded_at=preview_file.uploaded_at,
                )
                for preview_file in element.preview_files.all()
            ]
            guideline_data_list = []
            for guideline in element.guidelines.all():
                attachment_list = [
                    BoqElementGuidelineAttachmentData(
                        object_status=ObjectStatus.ADD,
                        file=attachment.file,
                        name=attachment.name,
                        type=attachment.type,
                        uploaded_at=attachment.uploaded_at,
                    )
                    for attachment in guideline.attachments.all()
                ]
                guideline_data_list.append(
                    BoqElementGuidelineData(
                        object_status=ObjectStatus.ADD,
                        attachments=attachment_list,
                        description=guideline.description,
                        name=guideline.name,
                        created_at=guideline.created_at,
                    )
                )
            production_drawing_data = [
                BoqElementProductionDrawingData(
                    object_status=ObjectStatus.ADD,
                    file=drawings.file,
                    name=drawings.name,
                    tags=drawings.tags.values_list("id", flat=True),
                    uploaded_at=drawings.uploaded_at,
                )
                for drawings in element.production_drawings.all()
            ]
            entity_list.append(
                ElementImportEntity(
                    object_status=ObjectStatus.ADD,
                    id=new_element.id,
                    preview_files=preview_file_list,
                    guidelines=guideline_data_list,
                    production_drawings=production_drawing_data,
                )
            )
        return entity_list

    @classmethod
    def process_bulk_create(
        cls,
        element_entities,
        created_by_id: int,
        client_id: int,
        boq_id: int,
        organization_id: int,
        position_counter: dict,
    ):
        element_obj_list = []
        serial_number_list = cls.get_serial_numbers(limit=len(element_entities), client_id=client_id)
        for index, element_data in enumerate(element_entities):
            if hasattr(element_data, "section"):
                section_name = element_data.section.name.lower()
            else:
                # While creating elements from import-via-lead
                section_name = element_data.section_name.lower()
            position = position_counter.get(section_name, 0)
            setattr(element_data, "position", position + 1)
            element_obj_list.append(
                cls.process_create(
                    element_entity=element_data,
                    created_by_id=created_by_id,
                    client_id=client_id,
                    boq_id=boq_id,
                    organization_id=organization_id,
                    serial_number=serial_number_list[index],
                    save=False,
                )
            )
            position_counter[section_name] += 1

        try:
            element_list = BoqElement.objects.bulk_create(objs=element_obj_list)
        except BoqElementCreateException as e:
            logger.info("Error while creating elements", error=str(e), element_list=element_obj_list)
            raise BoqElementPersistenceService.ElementCreateException("Error while creating elements")
        ElementRelatedCommonServiceNew.process_bulk_create(
            service=BoqElementRelatedService,
            data=element_entities,
            elements=element_list,
            created_by_id=created_by_id,
        )
        update_boq_status(
            boq_id=boq_id,
            organization_id=organization_id,
            user_id=created_by_id,
        )
        return element_list

    @classmethod
    def process_update(cls, element_entity, element, category_dict: Optional[dict], user: object, save=True):
        if not category_dict:
            category_dict = ElementCategory.objects.in_bulk()

        element, updated_field, element_to_updated_fields_map = BoqElementUpdateService.update(
            element_data=element_entity,
            element=element,
            categories_dict=category_dict,
            user=user,
            save=save,
        )
        if save:
            ElementRelatedCommonServiceNew.process_update(
                service=BoqElementRelatedService, updated_by_id=user.pk, element=element, data=element_entity
            )

        return element, updated_field, element_to_updated_fields_map

    @classmethod
    @decorators.deprecated_function()
    def process_bulk_update(cls, element_entities, user: User):
        element_obj_list = fetch_boq_elements_prefetch_all(element_id_list=[element.id for element in element_entities])
        element_dict = element_obj_list.in_bulk()
        category_dict = ElementCategory.objects.in_bulk()
        update_element_list = []

        for element_data in element_entities:
            element, updated_field, element_to_updated_fields_map = cls.process_update(
                element_entity=element_data,
                element=element_dict[element_data.id],
                category_dict=category_dict,
                user=user,
                save=False,
            )
            update_element_list.append(element)

        fields = [
            "name",
            "category_id",
            "uom",
            "description",
            "item_type_id",
            "quantity",
            "client_rate",
            "quantity",
            "item_type_id",
            "updated_at",
            "updated_by_id",
            "client_rate",
            "version",
            "serial_number",
            "custom_type",
            "code",
            "reported_quantity",
        ]

        BoqElement.objects.bulk_update(objs=update_element_list, fields=fields)

    @classmethod
    def get_elements_with_section_update(
        cls, elements_data: list[BoqElementEntityData], element_obj_list: QuerySet
    ) -> list[BoqElementEntityData]:
        element_dict = {element.id: element for element in element_obj_list}
        elements_with_section_change = []

        for element_data in elements_data:
            element_obj = element_dict.get(element_data.id)
            if not element_obj:
                continue

            new_section_id = getattr(element_data.section, "id", None)
            if new_section_id is not None and new_section_id != element_obj.section_id:
                elements_with_section_change.append(element_data)

        return elements_with_section_change

    @classmethod
    def process_bulk_update_v2(
        cls,
        element_entities,
        with_client_rate: bool,
        user: User,
        boq_id: int,
        organization_id: int,
        position_counter: dict,
    ):
        element_obj_list = fetch_boq_elements_prefetch_all(element_id_list=[element.id for element in element_entities])
        element_dict = element_obj_list.in_bulk()
        category_dict = ElementCategory.objects.in_bulk()
        update_element_list = []
        bulk_element_to_updated_fields_map: dict[BoqElement, list[str]] = {}
        new_serial_number_to_generated = {element_instance.client_id: [] for element_instance in element_obj_list}
        for element_data in element_entities:
            element, updated_fields, element_to_updated_fields_map = cls.process_update(
                element_entity=element_data,
                element=element_dict[element_data.id],
                category_dict=category_dict,
                user=user,
                save=False,
            )
            bulk_element_to_updated_fields_map = bulk_element_to_updated_fields_map | element_to_updated_fields_map
            # updating custom column fields if present
            if element_data.extra_data:
                current_extra_data = element.extra_data if element.extra_data else {}
                for key, value in element_data.extra_data.items():
                    current_extra_data[key] = value
                element.extra_data = current_extra_data
            if updated_fields:
                if (
                    "name" in updated_fields
                    or "description" in updated_fields
                    or "uom" in updated_fields
                    or "category_id" in updated_fields
                ):
                    new_serial_number_to_generated[element.client_id].append(element.id)
            update_element_list.append(element)
        element_with_serial_number = {}
        for client_id, element_list in new_serial_number_to_generated.items():
            if element_list:
                serial_numbers = cls.get_serial_numbers(limit=len(element_list), client_id=client_id)
                for element, serial_number in zip(element_list, serial_numbers):
                    element_with_serial_number[element] = serial_number
        if element_with_serial_number:
            for element_obj in update_element_list:
                if element_obj.id in element_with_serial_number:
                    element_obj.serial_number = element_with_serial_number[element_obj.id]

        fields = [
            "name",
            "category_id",
            "uom",
            "description",
            "item_type_id",
            "quantity",
            "progress_percentage",
            "progress_quantity_input",
            "updated_at",
            "updated_by_id",
            "version",
            "serial_number",
            "custom_type",
            "code",
            "section_id",
            "extra_data",
            "reported_quantity",
            "budget_rate",
            "brand_name",
            "tax_percent",
            "hsn_code",
            "position",
        ]
        if with_client_rate:
            fields.append("client_rate")

        updated_element_list = cls.update_element_positions(
            element_entities=element_entities,
            update_element_list=update_element_list,
            element_obj_list=element_obj_list,
            position_counter=position_counter,
        )

        BoqElement.objects.bulk_update(objs=updated_element_list, fields=fields)

        """
        Not creating action history by trigger
        create them manually
        """
        history_data = StatusService.create_history_entity(
            elements=updated_element_list, action=BoqElementAction.ITEM_UPDATED, source=None
        )
        action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user.pk)
        if bulk_element_to_updated_fields_map:
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=bulk_element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user.pk,
                project_id=boq_id,
                org_id=organization_id,
            )

    @classmethod
    def update_element_positions(
        cls, element_entities: list, update_element_list: list, element_obj_list: QuerySet, position_counter: dict
    ) -> list:
        element_entities_with_section_update = cls.get_elements_with_section_update(
            elements_data=element_entities, element_obj_list=element_obj_list
        )
        element_ids_with_section_update = {element.id for element in element_entities_with_section_update}

        updated_elements = []

        for element_data in update_element_list:
            element_copy = element_data
            if element_data.id in element_ids_with_section_update:
                section_name = element_data.section.name.lower()
                position = position_counter.get(section_name, 0)
                setattr(element_copy, "position", position + 1)
                position_counter[section_name] += 1

            updated_elements.append(element_copy)

        return updated_elements

    @classmethod
    def process_related_bulk_update(cls, element_obj_list, element_entities, updated_by_id: int):
        ElementRelatedCommonServiceNew.process_bulk_update(
            service=BoqElementRelatedService,
            updated_by_id=updated_by_id,
            element_list=element_obj_list,
            data=element_entities,
        )
        return True

    @classmethod
    def process_delete(cls, delete_id_list: list, deleted_by_id: int):
        return (
            BoqElement.objects.filter(id__in=delete_id_list)
            .available()
            .update(deleted_by_id=deleted_by_id, deleted_at=timezone.now())
        )

    @classmethod
    def process_duplicate(
        cls,
        element,
        element_entity,
        created_by_id: int,
        client_id: int,
        boq_id: int,
        organization_id: Optional[int],
        serial_number: Optional[int],
        to_create_flag: bool,
    ):
        if not hasattr(element_entity, "custom_type"):
            setattr(element_entity, "custom_type", element.custom_type)

        if not to_create_flag:
            version_dict = cls.get_versions(serial_numbers=[element.serial_number], project_id=boq_id)
            setattr(element_entity, "version", version_dict[element.serial_number]["version"])

        if to_create_flag:
            serial_number = None

        element = cls.process_create(
            element_entity=element_entity,
            created_by_id=created_by_id,
            boq_id=boq_id,
            client_id=client_id,
            organization_id=organization_id,
            serial_number=serial_number,
        )
        return element


class ElElementService(BoqElementProcessService):
    custom_type = None
    boq_element_model = BoqElement
    order_element_model = VendorOrderElement


class CustomBoqElementService(BoqElementProcessService):
    custom_type = CustomElementType.BOQ
    boq_element_model = BoqElement
    order_element_model = VendorOrderElement


class CustomOrderElementService(BoqElementProcessService):
    custom_type = CustomElementType.ORDER
    boq_element_model = BoqElement
    order_element_model = VendorOrderElement


class ElementDuplicateService:
    @classmethod
    def process_duplicate(
        cls,
        element_id: int,
        element_entity,
        created_by_id: int,
        boq_id: int,
        organization_id: Optional[int],
    ) -> BoqElement:
        element = BoqElement.objects.filter(id=element_id).available().first()
        if not element:
            raise ValidationError("Element to be Duplicated not found")
        if hasattr(element_entity, "quantity_dimensions"):
            try:
                validate_quantity_dimensions(
                    quantity_dimensions=element_entity.quantity_dimensions,
                    quantity=element_entity.quantity,
                    quantity_uom=element_entity.uom,
                )
            except QuantityDimensionsMismatchError as e:
                raise QuantityValidationError({"quantity_dimensions": e.message})
            except UOMConversionError as e:
                raise UOMValidationError({"quantity_dimensions": e.message})
            element.quantity_dimensions = element_entity.quantity_dimensions
        native_fields = ["name", "category_id", "uom", "description"]
        fields = [
            "name",
            "category_id",
            "uom",
            "description",
            "item_type_id",
            "quantity",
            "client_rate",
            "reported_quantity",
            "quantity_dimensions",
        ]
        element.name = element.name.strip()
        element.description = element.description.strip()
        element_entity.name = element_entity.name.strip()
        element_entity.description = element_entity.description.strip() if element_entity.description else ""
        setattr(element_entity, "parent_element_id", element.pk)
        setattr(element_entity, "position_ts", element.position_ts + timedelta(microseconds=1000))
        _, _, updated_fields = model_update(
            data=element_entity, fields=fields, instance=element, updated_by_id=created_by_id, save=False
        )
        to_create_flag = False
        if updated_fields and not element.custom_type and set(updated_fields).intersection(native_fields):
            element.custom_type = CustomElementType.BOQ
            to_create_flag = True

        if element.element:
            setattr(element_entity, "element_id", element.element_id)
        if element.custom_type is None:
            element = ElElementService.process_duplicate(
                element=element,
                element_entity=element_entity,
                created_by_id=created_by_id,
                boq_id=boq_id,
                client_id=element.client_id,
                organization_id=organization_id,
                serial_number=element.serial_number,
                to_create_flag=to_create_flag,
            )
        elif element.custom_type == CustomElementType.BOQ:
            element = CustomBoqElementService.process_duplicate(
                element=element,
                element_entity=element_entity,
                created_by_id=created_by_id,
                boq_id=boq_id,
                client_id=element.client_id,
                organization_id=organization_id,
                serial_number=element.serial_number,
                to_create_flag=to_create_flag,
            )
        else:
            element = CustomOrderElementService.process_duplicate(
                element=element,
                element_entity=element_entity,
                created_by_id=created_by_id,
                boq_id=boq_id,
                client_id=element.client_id,
                organization_id=organization_id,
                serial_number=element.serial_number,
                to_create_flag=to_create_flag,
            )
        return element


# TODO: Refactor


class BoqSectionService:
    @classmethod
    def get_section_name_to_latest_position_mapping(cls, section_ids: list[int], boq_id: int, organization_id: int):
        boq_section_name_and_position = (
            BoqElement.objects.filter(
                Q(boq_id=boq_id, organization_id=organization_id)
                & (Q(section_id__in=section_ids) | Q(section_id__isnull=True))
            )
            .available()
            .select_related("section")
            .values("section__name")
            .annotate(latest_position=Max("position"))
            .values("section__name", "latest_position")
        )
        position_counter = collections.defaultdict(int)
        for section in boq_section_name_and_position:
            section_name = (
                section["section__name"].lower() if section["section__name"] else BoqSection.DEFAULT_SECTION.lower()
            )
            position_counter[section_name] = section["latest_position"]
        return position_counter

    @classmethod
    def process_with_section_check_and_create(
        cls, boq_id, section_name_list, section_name_dict, created_by_id, organization_id
    ):
        # section_name_dict = {a.lower(): a for a in section_name_set}
        boq_sections = BoqSection.objects.filter(boq_id=boq_id, organization_id=organization_id).available()
        boq_sections_set = set(section.lower() for section in boq_sections.values_list("name", flat=True))
        boq_sections_set.add(BoqSection.DEFAULT_SECTION.lower())
        boq_section_dict = {a["name"].lower(): a["id"] for a in list(boq_sections.values("name", "id"))}
        boq_section_dict[BoqSection.DEFAULT_SECTION.lower()] = None
        element_section_set = set(section_name_list)
        section_to_create = list(element_section_set - boq_sections_set)
        section_to_create = sorted(
            list(map(lambda x: (section_name_dict.get(x), section_name_list.index(x)), section_to_create)),
            key=lambda x: x[1],
        )
        section_to_create = [section[0] for section in section_to_create]
        created_sections = boq_section_create_many(
            boq_id=boq_id, name_list=section_to_create, created_by_id=created_by_id, organization_id=organization_id
        )
        new_section_name_id = {section.name.lower(): section.id for section in created_sections}
        all_sections = new_section_name_id | boq_section_dict
        position_counter = cls.get_section_name_to_latest_position_mapping(
            section_ids=list(all_sections.values()), boq_id=boq_id, organization_id=organization_id
        )
        position_counter.update({key: 0 for key in new_section_name_id})

        return position_counter, all_sections

    @classmethod
    def process_without_section_check_and_create(
        cls, section_name, boq_id, element_count, organization_id: Optional[int], prev_element_id: Optional[int]
    ):
        if prev_element_id:
            prev_element_position = (
                BoqElement.objects.filter(id=prev_element_id).values_list("position", flat=True).first()
            )
        else:
            section_id = (
                BoqSection.objects.filter(boq_id=boq_id, organization_id=organization_id)
                .available()
                .annotate(section_name=Lower("name"))
                .filter(section_name=section_name.lower())
                .values_list("id", flat=True)
                .first()
            )
            prev_element_position = (
                BoqElement.objects.filter(boq_id=boq_id, organization_id=organization_id, section_id=section_id)
                .available()
                .order_by("-position")
                .values_list("position", flat=True)
                .first()
            )
            if not prev_element_position:
                prev_element_position = 0
        position_counter = collections.defaultdict(int)
        position_counter[BoqSection.DEFAULT_SECTION.lower()] = prev_element_position
        if section_name and section_name.lower() != BoqSection.DEFAULT_SECTION.lower():
            position_counter = {section_name.lower(): prev_element_position}
            boq_section = (
                BoqSection.objects.filter(boq_id=boq_id, organization_id=organization_id)
                .available()
                .annotate(lower_name=Trim(Lower("name")))
                .filter(lower_name=section_name.lower())
                .first()
            )
            if boq_section:
                boq_section_id = boq_section.id

                if prev_element_id:
                    shift_boq_elements_position(
                        boq_id=boq_id,
                        section_id=boq_section_id,
                        organization_id=organization_id,
                        prev_element_position=prev_element_position,
                        element_count=element_count,
                    )

            else:
                raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Section not available")})
        else:
            boq_section_id = None
            BoqElement.objects.filter(
                boq_id=boq_id,
                section_id=boq_section_id,
                organization_id=organization_id,
                position__gt=prev_element_position,
            ).update(position=F("position") + element_count)
        return position_counter, None, boq_section_id

    @classmethod
    def get_section_data(cls, element_list):
        section_name_list = [
            a.get("section_name").lower() if a.get("section_name") else BoqSection.DEFAULT_SECTION.lower()
            for a in element_list
        ]
        section_name_set = set(
            [a.get("section_name") if a.get("section_name") else BoqSection.DEFAULT_SECTION for a in element_list]
        )
        section_name_dict = {a.lower(): a for a in section_name_set}

        return section_name_list, section_name_dict


class BoqElementImportFromLibraryService(ElElementService):
    custom_type = None
    boq_element_model = BoqElement
    order_element_model = VendorOrderElement

    @classmethod
    def process_sections(
        cls,
        element_list: list,
        boq_id,
        with_section: bool,
        created_by_id: int,
        section_name: str,
        organization_id: Optional[int],
        prev_element_id: Optional[int] = None,
    ):
        element_id_list = [int(a.get("element_id")) for a in element_list]
        section_name_list, section_name_dict = BoqSectionService.get_section_data(element_list=element_list)
        element_id_section_name_dict = {a: b for a, b in zip(element_id_list, section_name_list)}
        boq_section_id = None

        if with_section:
            position_counter, all_sections = BoqSectionService.process_with_section_check_and_create(
                boq_id=boq_id,
                section_name_list=section_name_list,
                section_name_dict=section_name_dict,
                created_by_id=created_by_id,
                organization_id=organization_id,
            )
        else:
            position_counter, all_sections, boq_section_id = BoqSectionService.process_without_section_check_and_create(
                section_name=section_name,
                boq_id=boq_id,
                element_count=len(element_id_section_name_dict),
                organization_id=organization_id,
                prev_element_id=prev_element_id,
            )

        return boq_section_id, position_counter, all_sections, element_id_list, element_id_section_name_dict

    @classmethod
    def prepare_data_entities(
        cls,
        created_element_objects,
        element_list,
    ):
        entity_list = []
        for new_element, element in zip(created_element_objects, element_list):
            preview_files = element.preview_files.all()
            preview_file_list = []
            for preview_file in preview_files:
                preview_file_list.append(
                    PreviewFileData(
                        object_status=ObjectStatus.ADD,
                        type=preview_file.type,
                        file=preview_file.file,
                        name=preview_file.name,
                        is_main=preview_file.is_main,
                    )
                )
            guideline_data_list = []
            for guideline in element.guidelines.all():
                attachments = guideline.attachments.all()
                attachment_list = []
                for attachment in attachments:
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )

                guideline_data_list.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        attachments=attachment_list,
                        description=guideline.description,
                        name=guideline.name,
                    )
                )
            production_drawing_data = []
            for drawings in element.production_drawings.all():
                production_drawing_data.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        file=drawings.file,
                        name=drawings.name,
                        tags=drawings.tags.values_list("id", flat=True),
                    )
                )

            entity_list.append(
                ElementImportEntity(
                    object_status=ObjectStatus.ADD,
                    id=new_element.id,
                    preview_files=preview_file_list,
                    guidelines=guideline_data_list,
                    production_drawings=production_drawing_data,
                )
            )
        return entity_list

    @classmethod
    def element_creation_phase(
        cls,
        element_id_list,
        position_counter,
        element_id_section_name_dict,
        with_section,
        boq_section_id,
        all_sections,
        created_by_id,
        boq_id,
        organization_id,
        element_list,
        with_quantity: bool,
        client_id: Optional[int],
        is_client_rate_visible: bool,
        library_id: Optional[int],
        with_service_charge: Optional[bool],
        with_quantity_breakdown: Optional[bool],
        with_budget_rate: Optional[bool],
        with_tax_percent: Optional[bool],
        section_name: str,
    ):
        position_dict = {}

        if not element_id_list:
            return []
        subquery = OrganizationElement.objects.filter(
            element_id=OuterRef("id"), organization_id=organization_id
        ).annotate(
            client_rate_and_service_charge_percent=JSONObject(
                client_rate="client_rate",
                service_charge_percent="service_charge_percent",
                is_service_charge_with_base_amount="is_service_charge_with_base_amount",
            )
        )
        if library_id:
            elements = (
                element_library_mapping_fetch_many(library_id=library_id)
                .filter(element_id__in=element_id_list)
                .values_list("element_id", flat=True)
            )
            position_dict = {element: timezone.now() for element in elements}

        element_dict = (
            elements_fetch_by_ids(element_ids=element_id_list)
            .annotate(
                client_rate_and_service_charge_percent=subquery.values("client_rate_and_service_charge_percent")[:1]
            )
            .annotate(
                org_client_rate=F("client_rate_and_service_charge_percent__client_rate"),
                service_charge_percent=F("client_rate_and_service_charge_percent__service_charge_percent"),
                is_service_charge_with_base_amount=F(
                    "client_rate_and_service_charge_percent__is_service_charge_with_base_amount"
                ),
            )
            .order_by(
                Case(*[When(id=pk, then=Value(i)) for i, pk in enumerate(element_id_list)], output_field=IntegerField())
            )  # To maintain ordering
            .in_bulk()
        )
        version_dict = cls.get_versions(
            serial_numbers=[value.serial_number for value in element_dict.values()],
            project_id=boq_id,
        )

        boq_element_list = []
        for element_id, element in element_dict.items():
            position = position_counter.get(
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower(), 0
            )
            element_data = BoqElementBaseData(
                name=element.name,
                uom=element.uom,
                description=element.description,
                brand_name=element.brand_name,
                tax_percent=element.tax_percent if with_tax_percent else 0,
                hsn_code=element.hsn_code,
                category_id=element.category_id,
                item_type_id=element.item_type_id,
                client_rate=element.org_client_rate if element.org_client_rate else 0,
                quantity=(element.standard_quantity if with_quantity else 0),
                section_id=(
                    boq_section_id
                    if not with_section
                    else all_sections.get(element_id_section_name_dict.get(element_id))
                ),
            )
            if library_id and position_dict and position_dict.get(element_id):
                setattr(element_data, "position_ts", position_dict.get(element_id))
            setattr(element_data, "element_id", element.id)
            setattr(element_data, "position", position + 1)
            setattr(element_data, "version", version_dict[element.serial_number]["version"])
            setattr(element_data, "custom_type", None)
            if with_service_charge:
                setattr(element_data, "service_charge_percent", element.service_charge_percent)
                setattr(element_data, "is_service_charge_with_base_amount", element.is_service_charge_with_base_amount)

            if with_quantity_breakdown:
                setattr(element_data, "quantity_dimensions", element.quantity_dimensions)

            if with_budget_rate:
                setattr(element_data, "budget_rate", element.budget_rate)

            boq_element_list.append(
                BoqElementProcessService.process_create(
                    element_entity=element_data,
                    created_by_id=created_by_id,
                    client_id=element.client_id,
                    boq_id=boq_id,
                    organization_id=organization_id,
                    serial_number=element.serial_number,
                    save=False,
                )
            )
            position_counter[
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower()
            ] += 1

        try:
            created_element_objects = BoqElement.objects.bulk_create(objs=boq_element_list)
        except BoqElementCreateException as e:
            logger.info("Error while creating elements", error=str(e), element_list=boq_element_list)
            raise BoqElementPersistenceService.ElementCreateException("Error while creating elements")

        entity_list = cls.prepare_data_entities(
            created_element_objects=created_element_objects,
            element_list=list(element_dict.values()),
        )

        ElementRelatedCommonServiceNew.process_bulk_create(
            service=BoqElementRelatedService,
            data=entity_list,
            elements=created_element_objects,
            created_by_id=created_by_id,
        )

        update_boq_status(boq_id=boq_id, organization_id=organization_id, user_id=created_by_id)
        # NOTE: Creating item-created action history by trigger, refer -> boq mig-0066
        # history_data = StatusService.create_history_entity(
        #     elements=created_element_objects, action=BoqElementAction.ITEM_CREATED, source=None
        # )
        # StatusService.create_bulk(history_data_list=history_data, user_id=created_by_id)
        return created_element_objects

    @classmethod
    def process(
        cls,
        element_list: list,
        boq_id,
        with_section: bool,
        created_by_id: int,
        section_name: str,
        organization_id: Optional[int],
        with_quantity: bool,
        is_client_rate_visible: Optional[bool],
        library_id: Optional[int],
        with_service_charge: Optional[bool],
        with_quantity_breakdown: Optional[bool],
        with_budget_rate: Optional[bool],
        with_tax_percent: Optional[bool],
        prev_element_id: Optional[int],
    ):
        (
            boq_section_id,
            position_counter,
            all_sections,
            element_id_list,
            element_id_section_name_dict,
        ) = cls.process_sections(
            element_list=element_list,
            boq_id=boq_id,
            with_section=with_section,
            created_by_id=created_by_id,
            section_name=section_name,
            organization_id=organization_id,
            prev_element_id=prev_element_id,
        )

        created_element_objects = cls.element_creation_phase(
            library_id=library_id,
            client_id=None,
            element_id_list=element_id_list,
            position_counter=position_counter,
            element_id_section_name_dict=element_id_section_name_dict,
            all_sections=all_sections,
            created_by_id=created_by_id,
            boq_id=boq_id,
            organization_id=organization_id,
            element_list=element_list,
            boq_section_id=boq_section_id,
            with_section=with_section,
            with_quantity=with_quantity,
            is_client_rate_visible=is_client_rate_visible,
            with_service_charge=with_service_charge,
            with_quantity_breakdown=with_quantity_breakdown,
            with_budget_rate=with_budget_rate,
            with_tax_percent=with_tax_percent,
            section_name=section_name,
        )
        element_category_org_mapping_create_many(elements=created_element_objects, organization_id=organization_id)
        main_preview_file_mapping = {
            element.id: element.main_preview_file for element in created_element_objects if element.main_preview_file
        }
        currently_created_elements = BoqElement.objects.filter(id__in=main_preview_file_mapping.keys()).only("id")
        for element in currently_created_elements:
            element.main_preview_file = main_preview_file_mapping[element.id]
        cls.element_model.objects.bulk_update(objs=currently_created_elements, fields=["main_preview_file"])
        return created_element_objects


class BoqElementImportFromOtherBoq(BoqElementImportFromLibraryService):
    custom_type = CustomElementType.BOQ
    boq_element_model = BoqElement
    order_element_model = VendorOrderElement

    @classmethod
    def element_creation_phase(
        cls,
        element_id_list,
        position_counter,
        element_id_section_name_dict,
        with_section,
        boq_section_id,
        all_sections,
        created_by_id,
        boq_id,
        organization_id,
        element_list,
        with_quantity: bool,
        client_id: int,
        with_service_charge: Optional[bool],
        with_quantity_breakdown: Optional[bool],
        with_budget_rate: Optional[bool],
        with_tax_percent: Optional[bool],
        section_name: str,
    ):
        if not element_id_list:
            return []

        element_dict = (
            fetch_boq_elements_prefetch_all(element_id_list=element_id_list)
            .order_by(F("section_id").asc(nulls_first=True), "position")
            .in_bulk()
        )

        boq_element_list = []
        element_serial_version = []
        element_type_counter = collections.Counter()
        for element in element_dict.values():
            if element.client_id != client_id:
                element.custom_type = CustomElementType.BOQ
            element_type_counter[element.custom_type] += 1
            if element.custom_type is None:
                element_serial_version.append(element.serial_number)

        version_dict = {}
        if element_serial_version:
            version_dict = ElElementService.get_versions(serial_numbers=element_serial_version, project_id=boq_id)

        serial_number_list = []

        if element_type_counter.get(CustomElementType.BOQ) or element_type_counter.get(CustomElementType.ORDER):
            serial_number_list = cls.get_serial_numbers(
                limit=(
                    element_type_counter.get(CustomElementType.BOQ, 0)
                    + element_type_counter.get(CustomElementType.ORDER, 0)
                ),
                client_id=client_id,
            )
        counter = 0

        for element_id, element in element_dict.items():
            position = position_counter.get(
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower(), 0
            )
            element_data = BoqElementBaseData(
                name=element.name,
                uom=element.uom,
                brand_name=element.brand_name,
                tax_percent=element.tax_percent if with_tax_percent else 0,
                hsn_code=element.hsn_code,
                description=element.description,
                category_id=element.category_id,
                item_type_id=element.item_type_id,
                client_rate=element.client_rate,
                quantity=(element.quantity if with_quantity else 0),
                section_id=(
                    boq_section_id
                    if not with_section
                    else all_sections.get(element_id_section_name_dict.get(element_id))
                ),
            )
            if element.custom_type == CustomElementType.BOQ or element.custom_type == CustomElementType.ORDER:
                element.custom_type = CustomElementType.BOQ
                serial_number = serial_number_list[counter]
                setattr(element_data, "version", 1)
                counter += 1

            elif element.custom_type is None:
                serial_number = element.serial_number
                # TODO: Refactor
                count = version_dict[element.serial_number]["count"] - 1
                setattr(
                    element_data,
                    "version",
                    version_dict[element.serial_number]["version"] + count,
                )
                version_dict[element.serial_number]["count"] -= 1

            setattr(element_data, "custom_type", element.custom_type)
            setattr(element_data, "element_id", element.element_id)
            setattr(element_data, "position", position + 1)
            if with_budget_rate:
                setattr(element_data, "budget_rate", element.budget_rate)
            if with_service_charge:
                setattr(element_data, "service_charge_percent", element.service_charge_percent)
                setattr(element_data, "is_service_charge_with_base_amount", element.is_service_charge_with_base_amount)
            if with_quantity_breakdown:
                setattr(element_data, "quantity_dimensions", element.quantity_dimensions)

            boq_element_list.append(
                BoqElementProcessService.process_create(
                    element_entity=element_data,
                    created_by_id=created_by_id,
                    client_id=client_id,
                    boq_id=boq_id,
                    organization_id=organization_id,
                    serial_number=serial_number,
                    save=False,
                )
            )

            position_counter[
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower()
            ] += 1

        try:
            created_element_objects = BoqElement.objects.bulk_create(objs=boq_element_list)
        except BoqElementCreateException as e:
            logger.info("Error while creating elements", error=str(e), element_list=boq_element_list)
            raise BoqElementPersistenceService.ElementCreateException("Error while creating elements")

        entity_list = cls.prepare_data_entities(
            created_element_objects=created_element_objects,
            element_list=list(element_dict.values()),
        )

        ElementRelatedCommonServiceNew.process_bulk_create(
            service=BoqElementRelatedService,
            data=entity_list,
            elements=created_element_objects,
            created_by_id=created_by_id,
        )
        # NOTE: Creating item-created action history by trigger, refer -> boq mig-0066
        # history_data = StatusService.create_history_entity(
        #     elements=created_element_objects, action=BoqElementAction.ITEM_CREATED, source=None
        # )
        # StatusService.create_bulk(history_data_list=history_data, user_id=created_by_id)
        update_boq_status(boq_id=boq_id, organization_id=organization_id, user_id=created_by_id)

        return created_element_objects

    @classmethod
    def process(
        cls,
        element_list: list,
        boq_id,
        with_section: bool,
        created_by_id: int,
        section_name: str,
        organization_id: Optional[int],
        with_quantity: bool,
        with_service_charge: Optional[bool],
        with_quantity_breakdown: Optional[bool],
        with_budget_rate: Optional[bool],
        with_tax_percent: Optional[bool],
        prev_element_id: Optional[int],
    ):
        client_id = Project.objects.get(id=boq_id).client_id
        (
            boq_section_id,
            position_counter,
            all_sections,
            element_id_list,
            element_id_section_name_dict,
        ) = cls.process_sections(
            element_list=element_list,
            boq_id=boq_id,
            with_section=with_section,
            created_by_id=created_by_id,
            section_name=section_name,
            organization_id=organization_id,
            prev_element_id=prev_element_id,
        )
        created_element_objects = cls.element_creation_phase(
            client_id=client_id,
            element_id_list=element_id_list,
            position_counter=position_counter,
            element_id_section_name_dict=element_id_section_name_dict,
            all_sections=all_sections,
            created_by_id=created_by_id,
            boq_id=boq_id,
            organization_id=organization_id,
            element_list=element_list,
            boq_section_id=boq_section_id,
            with_section=with_section,
            with_quantity=with_quantity,
            with_service_charge=with_service_charge,
            with_quantity_breakdown=with_quantity_breakdown,
            with_budget_rate=with_budget_rate,
            with_tax_percent=with_tax_percent,
            section_name=section_name,
        )
        element_category_org_mapping_create_many(elements=created_element_objects, organization_id=organization_id)
        return created_element_objects


class BoqElementImportFromRC(BoqElementImportFromLibraryService):
    @classmethod
    def element_creation_phase(
        cls,
        element_id_list,
        position_counter,
        element_id_section_name_dict,
        with_section,
        boq_section_id,
        all_sections,
        created_by_id,
        boq_id,
        organization_id,
        element_list,
        with_quantity: bool,
        client_id: Optional[int],
        is_client_rate_visible: Optional[bool],
        rate_contract_id: int,
        section_name: str,
    ):
        if not element_id_list:
            return []

        element_list = rc_elements_fetch_by_ids(rate_contract_id=rate_contract_id, element_ids=element_id_list)
        element_dict = {element.element_id: element for element in element_list}
        version_dict = cls.get_versions(
            serial_numbers=[value.serial_number for value in element_dict.values()],
            project_id=boq_id,
        )
        boq_element_list = []
        for element_id, element in element_dict.items():
            position = position_counter.get(
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower(), 0
            )
            element_data = BoqElementBaseData(
                name=element.name,
                uom=element.uom,
                description=element.description,
                category_id=element.category_id,
                item_type_id=element.item_type_id,
                client_rate=element.rate,
                quantity=decimal.Decimal(0),
                section_id=(
                    boq_section_id
                    if not with_section
                    else all_sections.get(element_id_section_name_dict.get(element_id))
                ),
                brand_name="",
                tax_percent=0,
                hsn_code="",
            )
            setattr(element_data, "element_id", element.element_id)
            setattr(element_data, "position", position + 1)
            setattr(element_data, "version", version_dict[element.serial_number]["version"])
            setattr(element_data, "custom_type", None)
            setattr(element_data, "code", element.code)
            boq_element_list.append(
                BoqElementProcessService.process_create(
                    element_entity=element_data,
                    created_by_id=created_by_id,
                    client_id=element.client_id,
                    boq_id=boq_id,
                    organization_id=organization_id,
                    serial_number=element.serial_number,
                    save=False,
                )
            )
            position_counter[
                element_id_section_name_dict.get(element_id) if with_section else section_name.lower()
            ] += 1

        try:
            created_element_objects = BoqElement.objects.bulk_create(objs=boq_element_list)
        except BoqElementCreateException as e:
            logger.info("Error while creating elements", error=str(e), element_list=boq_element_list)
            raise BoqElementPersistenceService.ElementCreateException("Error while creating elements")

        entity_list = cls.prepare_data_entities(
            created_element_objects=created_element_objects,
            element_list=list(element_dict.values()),
        )

        ElementRelatedCommonServiceNew.process_bulk_create(
            service=BoqElementRelatedService,
            data=entity_list,
            elements=created_element_objects,
            created_by_id=created_by_id,
        )
        # NOTE: Creating item-created action history by trigger, refer -> boq mig-0066
        # history_data = StatusService.create_history_entity(
        #     elements=created_element_objects, action=BoqElementAction.ITEM_CREATED, source=None
        # )
        # StatusService.create_bulk(history_data_list=history_data, user_id=created_by_id)
        update_boq_status(boq_id=boq_id, organization_id=organization_id, user_id=created_by_id)

        return created_element_objects

    @classmethod
    def process(
        cls,
        element_list: list,
        boq_id,
        with_section: bool,
        created_by_id: int,
        section_name: str,
        organization_id: Optional[int],
        with_quantity: bool,
        is_client_rate_visible: Optional[bool],
        rate_contract_id: int,
        prev_element_id: Optional[int],
    ):
        (
            boq_section_id,
            position_counter,
            all_sections,
            element_id_list,
            element_id_section_name_dict,
        ) = cls.process_sections(
            element_list=element_list,
            boq_id=boq_id,
            with_section=with_section,
            created_by_id=created_by_id,
            section_name=section_name,
            organization_id=organization_id,
            prev_element_id=prev_element_id,
        )

        created_element_objects = cls.element_creation_phase(
            client_id=None,
            element_id_list=element_id_list,
            position_counter=position_counter,
            element_id_section_name_dict=element_id_section_name_dict,
            all_sections=all_sections,
            created_by_id=created_by_id,
            boq_id=boq_id,
            organization_id=organization_id,
            element_list=element_list,
            boq_section_id=boq_section_id,
            with_section=with_section,
            with_quantity=with_quantity,
            is_client_rate_visible=is_client_rate_visible,
            rate_contract_id=rate_contract_id,
            section_name=section_name,
        )
        element_category_org_mapping_create_many(elements=created_element_objects, organization_id=organization_id)
        return created_element_objects


class BoqElementImportFromQuotation(BoqElementImportFromLibraryService):
    @classmethod
    def process(
        cls,
        element_list: list,
        section_name: str,
        boq_id: int,
        with_section: bool,
        with_quantity: bool,
        with_rate: bool,
        created_by_id: int,
        client_id: int,
        organization_id: int,
        with_tax_percent: bool,
        with_discount: bool,
    ):
        (
            boq_section_id,
            position_counter,
            all_sections,
            element_id_list,
            element_id_section_name_dict,
        ) = cls.process_sections(
            element_list=element_list,
            boq_id=boq_id,
            with_section=with_section,
            created_by_id=created_by_id,
            section_name=section_name,
            organization_id=organization_id,
        )

        element_ids = [element.get("element_id") for element in element_list]

        quotation_elements = fetch_quotation_element_to_be_imported(element_ids=element_ids)
        element_entities: List[BoqElementCreateData] = cls.get_boq_element_entities(
            elements=list(quotation_elements),
            with_section=with_section,
            with_quantity=with_quantity,
            with_rate=with_rate,
            element_id_section_name_dict=element_id_section_name_dict,
            all_sections=all_sections,
            with_tax_percent=with_tax_percent,
            with_discount=with_discount,
        )
        cls.create_boq_elements(
            element_entities=element_entities,
            boq_id=boq_id,
            created_by_id=created_by_id,
            client_id=client_id,
            org_id=organization_id,
            position_counter=position_counter,
        )

    @classmethod
    def create_boq_elements(
        cls,
        element_entities: list,
        boq_id: int,
        created_by_id: int,
        client_id: int,
        org_id: int,
        position_counter: dict,
    ):
        logger.info("Creating boq elements", element_entities=element_entities)
        try:
            BoqElementProcessService.process_bulk_create(
                element_entities=element_entities,
                created_by_id=created_by_id,
                client_id=client_id,
                boq_id=boq_id,
                organization_id=org_id,
                position_counter=position_counter,
            )
        except UOMValidationError as e:
            logger.info(f"UOMValidationError: {str(e)}", element_entities=element_entities)
            raise e
        except QuantityValidationError as e:
            logger.info(f"QuantityValidationError: {str(e)}", element_entities=element_entities)
            raise e

        # NOTE: Creating item-created action history by trigger, refer -> boq mig-0066
        # history_data = StatusService.create_history_entity(
        #     elements=created_element_objects, action=BoqElementAction.ITEM_CREATED, source=None
        # )
        # StatusService.create_bulk(history_data_list=history_data, user_id=created_by_id)
        logger.info("Boq Elements Created", element_entities=element_entities)

    @classmethod
    def get_preview_file_data(cls, element: QuotationElement):
        preview_files = element.preview_files.all()
        preview_file_list = []
        for preview_file in preview_files:
            preview_file_list.append(
                PreviewFileData(
                    object_status=ObjectStatus.ADD,
                    type=preview_file.type,
                    file=preview_file.file,
                    name=preview_file.name,
                    is_main=preview_file.is_main,
                )
            )
        return preview_file_list

    @classmethod
    def get_guidelines_data(cls, element: QuotationElement):
        guidelines = element.guidelines.all()
        guideline_data_list = []
        for guideline in guidelines:
            attachments = guideline.attachments.all()
            attachment_list = []
            for attachment in attachments:
                attachment_list.append(
                    GuidelineAttachmentData(
                        object_status=ObjectStatus.ADD,
                        file=attachment.file,
                        name=attachment.name,
                        type=attachment.type,
                    )
                )

            guideline_data_list.append(
                GuidelineData(
                    object_status=ObjectStatus.ADD,
                    attachments=attachment_list,
                    description=guideline.description,
                    name=guideline.name,
                )
            )
        return guideline_data_list

    @classmethod
    def get_production_drawings_data(cls, element: QuotationElement):
        production_drawing_data = []
        for drawings in element.production_drawings.all():
            production_drawing_data.append(
                ProductionDrawingData(
                    object_status=ObjectStatus.ADD,
                    file=drawings.file,
                    name=drawings.name,
                    tags=drawings.tags.values_list("id", flat=True),
                )
            )
        return production_drawing_data

    @classmethod
    def get_boq_element_entities(
        cls,
        elements: List[QuotationElement],
        with_section: bool,
        with_quantity: bool,
        with_rate: bool,
        element_id_section_name_dict: dict,
        all_sections: dict,
        with_tax_percent: bool,
        with_discount: bool,
    ):
        element_entities = []
        for element in elements:
            section_name = element_id_section_name_dict.get(element.id)
            section_id = all_sections.get(section_name) if with_section else None
            element_entities.append(
                BoqElementCreateData(
                    name=element.name,
                    uom=element.uom,
                    description=element.description,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    quotation_element_id=element.pk,
                    client_rate=element.rate if with_rate else decimal.Decimal(0),
                    budget_rate=decimal.Decimal(0),
                    quantity=element.quantity if with_quantity else decimal.Decimal(0),
                    section_id=section_id,
                    preview_files=cls.get_preview_file_data(element),
                    guidelines=cls.get_guidelines_data(element),
                    production_drawings=cls.get_production_drawings_data(element),
                    hsn_code="",
                    tax_percent=element.tax_percent if with_tax_percent else decimal.Decimal(0),
                    brand_name="",
                    section_name=section_name,
                    discount_percent=element.discount_percent if with_discount else decimal.Decimal(0),
                )
            )
        return element_entities


def boq_elements_delete_many(*, element_id_list: list, boq_id: int, user_id: int):
    BoqElement.objects.filter(boq_id=boq_id, id__in=element_id_list).available().update(
        deleted_at=timezone.now(), deleted_by_id=user_id
    )


def boq_element_reorder(
    *, boq_element_id: int, boq_id: int, section_id: int, new_position: int, old_position: int
) -> None:
    element_list = BoqElement.objects.filter(boq_id=boq_id, section_id=section_id).order_by("position")
    position_reorder(
        queryset=element_list, object_id=boq_element_id, new_position=new_position, old_position=old_position
    )


# TODO: Remove after Boq ELement position is releases
def update_element_position_ts_on_duplicate(element: BoqElement):
    boq_element_current_version_list(boq_id=element.boq_id).filter(
        section_id=element.section_id, position_ts__gt=element.position_ts
    ).update(position_ts=F("position_ts") + timedelta(seconds=1))


def set_elements_position_on_create(
    prev_element_id: Optional[int], boq_id: int, organization_id: int, section_id: Optional[int]
) -> int:
    if prev_element_id:
        prev_element_position = BoqElement.objects.filter(id=prev_element_id).values_list("position", flat=True).first()
    else:
        prev_element_position = (
            BoqElement.objects.filter(boq_id=boq_id, organization_id=organization_id, section_id=section_id)
            .available()
            .order_by("-position")
            .values_list("position", flat=True)
            .first()
        )
        if not prev_element_position:
            prev_element_position = 0
    """
    If previous element ID is not given, then the elements are added at the end of the section.
    So no need to shift the elements.
    """
    if prev_element_id is None:
        return prev_element_position

    shift_boq_elements_position(
        boq_id=boq_id,
        organization_id=organization_id,
        prev_element_position=prev_element_position,
        section_id=section_id,
        element_count=1,
    )
    return prev_element_position


def shift_boq_elements_position(
    boq_id: int, organization_id: int, prev_element_position: int, section_id: int, element_count: int
):
    BoqElement.objects.filter(
        boq_id=boq_id, section_id=section_id, organization_id=organization_id, position__gt=prev_element_position
    ).available().update(temp_position=F("position") + element_count, position=0)
    BoqElement.objects.filter(
        boq_id=boq_id, section_id=section_id, organization_id=organization_id, position=0
    ).available().update(position=F("temp_position"))


def element_position_swap(boq_id: int, element1_id: BoqElement, element2_id: BoqElement):
    elements = BoqElement.objects.filter(id__in=[element1_id, element2_id], boq_id=boq_id).available()
    if len(elements) != 2:
        raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Invalid Elements")})

    # TODO: Remove after Boq ELement position is released
    elements[0].position_ts, elements[1].position_ts = elements[1].position_ts, elements[0].position_ts
    elements[0].temp_position, elements[1].temp_position = elements[1].position, elements[0].position
    elements[0].position = elements[1].position = 0
    BoqElement.objects.bulk_update(objs=[elements[0], elements[1]], fields=["temp_position", "position", "position_ts"])
    BoqElement.objects.filter(id__in=[element1_id, element2_id]).update(position=F("temp_position"))
