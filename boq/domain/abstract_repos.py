import abc
import decimal
from typing import List

from boq.data.models import BoqElement
from boq.domain.entities import (
    BoqElementDiscountRepoData,
    BoqElementInputSyncData,
    BoqElementOutputSyncData,
    BoqElementServiceChargeData,
    BoqElementServiceChargeStatusData,
    BoqElementStatusData,
)
from boq.domain.exceptions import DiscountPercentException, ServiceChargeException
from common.exceptions import BaseValidationError


class BoqElementAbstractRepo(abc.ABC):
    class DiscountUpdateException(DiscountPercentException):
        pass

    class InvalidDiscountValueException(DiscountPercentException):
        pass

    @abc.abstractmethod
    def get_element_data(self, element_ids: List[int], project_id: int, org_id: int) -> List[BoqElementStatusData]: ...

    @abc.abstractmethod
    def update_discounts(self, data: List[BoqElementDiscountRepoData], user_id: int): ...


class BoqElementServiceChargeAbstractRepo(abc.ABC):
    class ServiceChargeOperationException(ServiceChargeException):
        pass

    @abc.abstractmethod
    def get_element_data(
        self, element_ids: List[int], project_id: int, org_id: int
    ) -> List[BoqElementServiceChargeStatusData]: ...

    @abc.abstractmethod
    def update_service_charge(self, data: List[BoqElementServiceChargeData], user_id: int, with_base_amount: bool): ...


class BoqElementTaxPercentAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def check_if_status_is_not_draft(self, element_ids: List[int]):
        pass

    @abc.abstractmethod
    def update_tax_percent(self, element_ids, user_id: int, tax_percent: decimal.Decimal):
        pass


class BoqElementSyncAbstractRepo(abc.ABC):
    class SyncException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create_bulk(self, elements: List[BoqElementInputSyncData], user_id: int) -> List[BoqElementOutputSyncData]: ...

    @abc.abstractmethod
    def update_bulk(
        self, elements: List[BoqElementInputSyncData], user_id: int
    ) -> tuple[List[BoqElementOutputSyncData], dict[BoqElement, list[str]]]: ...

    @abc.abstractmethod
    def delete_bulk(self, elements: List[BoqElementInputSyncData], user_id: int) -> List[BoqElementOutputSyncData]: ...
