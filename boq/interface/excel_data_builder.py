import decimal
import textwrap
from typing import Optional, Union

import structlog
from bs4 import BeautifulSoup
from django.db.models import Count, F, OuterRef, Q, QuerySet, Subquery
from django.utils import timezone

from authorization.domain.constants import Permissions
from boq.data.choices import BoqElementStatus
from boq.data.entities import BoqElementExcelData
from boq.data.models import BoqElement, BoqElementGuideline, BoqGuideline
from boq.data.selectors import boq_element_current_version_list, boq_element_quantity_breakdown_exists
from boq.domain.data_parsers import BoqElementDataParser
from boq.services.excel import section_group_by_element
from common.element_base_serializer import ElementUomInitializer
from common.excel.constants import (
    BoqGridViewColumnsEnum,
    ElementExcelColumnsEnum,
    GuidelineExcelColumnsEnum,
    HorizontalAlignmentEnum,
    SheetTypeEnum,
    SummaryExcelColumnsEnum,
)
from common.excel.data_builder import ElementExcelDataBaseBuilder
from common.excel.entities import (
    <PERSON>ont<PERSON><PERSON>,
    <PERSON>er<PERSON>ell,
    <PERSON>etCell,
    SheetCellBorderEnum,
    SheetCellTypeEnum,
    SheetRowData,
)
from common.excel.generator import ExcelGenerator
from common.utils import is_valid_uuid
from controlroom.data.models import Project
from element.domain.services import BaseElementQuantityService
from microcontext.choices import MicroContextChoices
from project.data.models import ProjectOrgCustomFieldConfig
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData
from project.domain.services import get_project_org_custum_field_schema
from proposal.data.models import ProposalElementMapping
from rollingbanners.storage_backends import PublicMediaFileStorage

logger = structlog.get_logger(__name__)


class BoqElementExcelDataParser(BoqElementDataParser):
    def get_description(self):
        wrapper = textwrap.TextWrapper(width=100)
        word_list = wrapper.wrap(text=BeautifulSoup(self.element_obj.description, "html.parser").get_text())
        return "\n".join(word_list)

    def is_guideline_or_production_drawing_exists(self) -> bool:
        return (
            True
            if (hasattr(self.element_obj, "production_drawing_count") and self.element_obj.production_drawing_count > 0)
            or (hasattr(self.element_obj, "guidelines_count") and self.element_obj.guidelines_count > 0)
            else False
        )

    def get_reference_image(self) -> Optional[str]:
        preview_file = None
        if hasattr(self.element_obj, "preview_file") and self.element_obj.preview_file:
            preview_file = (
                PublicMediaFileStorage.url(self.element_obj.preview_file["file"])
                if self.element_obj.preview_file["file"].split(".")[1].lower() in ["jpg", "jpeg", "png", "jfif"]
                else None
            )
        return preview_file

    def get_element_reference(self):
        if hasattr(self.element_obj, "proposal_number") and self.element_obj.proposal_number:
            return f"{self.element_obj.job_id}/{self.element_obj.proposal_number}"
        if (
            hasattr(self.element_obj, "order_number")
            and hasattr(self.element_obj, "job_id")
            and self.element_obj.order_number
        ):
            return f"{self.element_obj.job_id}/{self.element_obj.order_number}"
        return ""

    def get_excel_entity(self):
        return BoqElementExcelData(
            description=self.get_description(),
            quantity=self.get_quantity(),
            is_guideline_or_production_drawing_exists=self.is_guideline_or_production_drawing_exists(),
            client_rate=self.get_client_rate(),
            reference_image=self.get_reference_image(),
            final_amount=self.get_final_amount(),
            discount_percent=self.get_discount_percent(),
            base_amount=self.get_base_amount(),
            service_charge_percent=self.get_service_charge_percent(),
            discount_value=self.get_discount_value(),
            amount_without_tax=self.get_amount_without_tax(),
            source=self.get_element_reference(),
        )


class BoqExcelDataBuilder(ElementExcelDataBaseBuilder):
    _element_col_display_text_mapping = {
        ElementExcelColumnsEnum.S_NO: "S No",
        ElementExcelColumnsEnum.ELEMENT_CODE: "Element Code",
        ElementExcelColumnsEnum.SECTION_NAME: "Section Name",
        ElementExcelColumnsEnum.ELEMENT_NAME: "Element Name",
        ElementExcelColumnsEnum.DESCRIPTION: "Description",
        ElementExcelColumnsEnum.BRAND_NAME: "Brand/Make",
        ElementExcelColumnsEnum.ELEMENT_CATEGORY: "Element Category",
        ElementExcelColumnsEnum.ITEM_TYPE: "Item Type",
        ElementExcelColumnsEnum.LENGTH: "Length",
        ElementExcelColumnsEnum.LENGTH_UOM: "Length UOM",
        ElementExcelColumnsEnum.BREADTH: "Breadth",
        ElementExcelColumnsEnum.BREADTH_UOM: "Breadth UOM",
        ElementExcelColumnsEnum.QUANTITY: "Quantity",
        ElementExcelColumnsEnum.UOM: "UOM",
        ElementExcelColumnsEnum.CLIENT_RATE: "Client Rate",
        ElementExcelColumnsEnum.BUDGET_RATE: "Budget Rate",
        ElementExcelColumnsEnum.BASE_AMOUNT: "Base Amount",
        ElementExcelColumnsEnum.DISCOUNT_PERCENT: "Discount Percent",
        ElementExcelColumnsEnum.GUIDELINE_OR_PRODUCT_DRAWING_EXISTS: "Guideline Or Production Drawing Exists",
        ElementExcelColumnsEnum.STATUS: "Status",
        ElementExcelColumnsEnum.HSN_CODE: "HSN",
        ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT: "Service Charge Percent",
        ElementExcelColumnsEnum.DISCOUNT_VALUE: "Discount Value",
        ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX: "Amount (w/o Tax)",
        ElementExcelColumnsEnum.TAX_PERCENT: "Tax %",
        ElementExcelColumnsEnum.FINAL_AMOUNT: "Final Amount",
        ElementExcelColumnsEnum.REFERENCE_IMAGE: "Reference Image",
    }
    _summary_cols_display_text_mapping = {
        SummaryExcelColumnsEnum.S_NO: "S No",
        SummaryExcelColumnsEnum.SECTIONS: "Sections",
        SummaryExcelColumnsEnum.AMOUNT: "Amount",
    }
    _guideline_cols_display_text_mapping = {
        GuidelineExcelColumnsEnum.S_NO: "S No.",
        GuidelineExcelColumnsEnum.GUIDELINE_NAME: "Guideline Name",
        GuidelineExcelColumnsEnum.GUIDELINE_DESCRIPTION: "Guideline Description",
    }

    _boq_id: int = None
    _org_filter: Q = None
    _org_id: int = None
    _uom_helper = None
    _element_quantity_service: BaseElementQuantityService = None
    _custom_column_config: ProjectOrgCustomFieldConfig = None
    _column_options_dict = {}
    _column_meta_data_dict = {}
    _column_dict = {}

    def __init__(
        self,
        boq_id: int,
        org_filter: Q,
        org_id: int,
    ):
        super().__init__()
        self._boq_id = boq_id
        self._org_filter = org_filter
        self._org_id = org_id
        self._element_quantity_service = BaseElementQuantityService()
        self._uom_helper = ElementUomInitializer()
        self._elements: QuerySet[BoqElement] = self.get_elements()
        self._summary = section_group_by_element(elements_queryset=self._elements)
        self._guidelines = (
            BoqGuideline.objects.filter(self._org_filter, boq_id=self._boq_id).available().order_by("-created_at")
        )

        self._custom_column_config: ProjectOrgCustomFieldConfig = get_project_org_custum_field_schema(
            project_id=self._boq_id, org_id=self._org_id, context=MicroContextChoices.BOQ_ELEMENT
        )
        if self._custom_column_config:
            for column in self._custom_column_config.custom_schema:
                self._column_meta_data_dict[column.get("id")] = column.get("type_meta_data")
                self._column_dict[column.get("id")] = column
                if column.get("type") == "dropdown":
                    self._column_options_dict[column.get("id")] = {}
                    for option in column.get("type_meta_data").get("options"):
                        self._column_options_dict[column.get("id")][option.get("id")] = option.get("name")

    def get_elements(self) -> QuerySet[BoqElement]:
        return (
            boq_element_current_version_list(boq_id=self._boq_id)
            .filter(self._org_filter)
            .annotate(
                production_drawing_count=Count(
                    "production_drawings", filter=Q(production_drawings__deleted_at__isnull=True)
                )
            )
            .annotate(guidelines_count=Count("guidelines", filter=Q(guidelines__deleted_at__isnull=True)))
        )

    def get_file_name(self) -> str:
        project_name = Project.objects.filter(id=self._boq_id).values_list("name", flat=True).first()
        if project_name is None:
            logger.info("Project does not exist", project_id=self._boq_id)
            raise self.ExcelDataBuilderException(f"Project with id {self._boq_id} does not exist")
        return f"{project_name}_BOQ.xlsx"

    def get_element_sheet_title(self) -> str:
        return "BOQ Elements"

    def get_guideline_sheet_title(self) -> str:
        return "BOQ Guidelines"

    def get_element_sheet_all_headers(self) -> dict[Union[ElementExcelColumnsEnum, str], HeaderCell]:
        headers = super().get_element_sheet_all_headers()
        if self._custom_column_config:
            for column in self._custom_column_config.custom_schema:
                headers.update(
                    {
                        column["id"]: HeaderCell(value=column["title"]),
                    }
                )
        logger.info("Boq Element sheet header data created", headers=headers)
        return headers

    def get_element_row_data(
        self, element: BoqElement, count: int
    ) -> dict[Union[ElementExcelColumnsEnum, str], SheetCell]:
        data = super().get_element_row_data(element=element, count=count)
        custom_field_data = self._add_custom_field_data(element=element)
        data.update(custom_field_data)
        return data

    def get_element_cell_data(self, element: BoqElement, col: ElementExcelColumnsEnum) -> SheetCell:
        element_excel_entity = BoqElementExcelDataParser(element_obj=element).get_excel_entity()
        if col == ElementExcelColumnsEnum.ELEMENT_CODE:
            return SheetCell(value=element.get_element_code())
        elif col == ElementExcelColumnsEnum.SECTION_NAME:
            return SheetCell(value=element.get_section_name())
        elif col == ElementExcelColumnsEnum.ELEMENT_NAME:
            return SheetCell(value=element.name, hyperlink=element.public_url)
        elif col == ElementExcelColumnsEnum.DESCRIPTION:
            return SheetCell(value=element_excel_entity.description)
        elif col == ElementExcelColumnsEnum.ELEMENT_CATEGORY:
            return SheetCell(value=element.get_element_category())
        elif col == ElementExcelColumnsEnum.ITEM_TYPE:
            return SheetCell(value=element.get_item_type_name() if element.get_item_type_name() else "")
        elif col == ElementExcelColumnsEnum.QUANTITY:
            return SheetCell(value=element_excel_entity.quantity)
        elif col == ElementExcelColumnsEnum.UOM:
            return SheetCell(value=self._uom_helper.uom_name_get(element.uom) if element.uom else "")
        elif col == ElementExcelColumnsEnum.CLIENT_RATE:
            return SheetCell(value=element_excel_entity.client_rate)
        elif col == ElementExcelColumnsEnum.GUIDELINE_OR_PRODUCT_DRAWING_EXISTS:
            return SheetCell(value="Yes" if element_excel_entity.is_guideline_or_production_drawing_exists else "")
        elif col == ElementExcelColumnsEnum.STATUS:
            return SheetCell(value=element.get_status_name())
        elif col == ElementExcelColumnsEnum.SOURCE:
            return SheetCell(value=element_excel_entity.source)
        elif col == ElementExcelColumnsEnum.ORDER_STATUS:
            return SheetCell(value=element.order_status)
        elif col == ElementExcelColumnsEnum.CREATED_AT:
            return SheetCell(
                value=element.created_at.astimezone(timezone.get_current_timezone()).strftime("%d-%m-%Y | %I:%M %p")
            )
        elif col == ElementExcelColumnsEnum.DRAFT_QUANTITY:
            return SheetCell(value=element.reported_quantity)
        elif col == ElementExcelColumnsEnum.FINAL_AMOUNT:
            return SheetCell(value=round(element_excel_entity.final_amount, 2))
        elif col == ElementExcelColumnsEnum.REFERENCE_IMAGE:
            return SheetCell(value=element_excel_entity.reference_image if element_excel_entity.reference_image else "")
        elif col == ElementExcelColumnsEnum.LENGTH:
            return SheetCell(value=element.get_length())
        elif col == ElementExcelColumnsEnum.LENGTH_UOM:
            return SheetCell(value=element.get_length_uom())
        elif col == ElementExcelColumnsEnum.BREADTH:
            return SheetCell(value=element.get_breadth())
        elif col == ElementExcelColumnsEnum.BREADTH_UOM:
            return SheetCell(value=element.get_breadth_uom())
        elif col == ElementExcelColumnsEnum.BUDGET_RATE:
            return SheetCell(value=round(element.budget_rate, 2))
        elif col == ElementExcelColumnsEnum.BASE_AMOUNT:
            return SheetCell(value=round(element_excel_entity.base_amount, 2))
        elif col == ElementExcelColumnsEnum.DISCOUNT_PERCENT:
            return SheetCell(value=round(element_excel_entity.discount_percent, 2))
        elif col == ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT:
            return SheetCell(value=round(element_excel_entity.service_charge_percent, 2))
        elif col in [ElementExcelColumnsEnum.DISCOUNT_VALUE, ElementExcelColumnsEnum.DISCOUNTED_VALUE]:
            return SheetCell(value=round(element_excel_entity.discount_value, 2))
        elif col == ElementExcelColumnsEnum.HSN_CODE:
            return SheetCell(value=element.hsn_code)
        elif col == ElementExcelColumnsEnum.TAX_PERCENT:
            return SheetCell(value=element.tax_percent)
        elif col == ElementExcelColumnsEnum.BRAND_NAME:
            return SheetCell(value=element.brand_name)
        elif col == ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX:
            return SheetCell(value=round(element_excel_entity.amount_without_tax, 2))
        else:
            raise ValueError(f"Unknown column {col}")

    def get_guideline_cell_data(self, guideline: BoqElementGuideline, col: GuidelineExcelColumnsEnum) -> SheetCell:
        if col == GuidelineExcelColumnsEnum.GUIDELINE_NAME:
            return SheetCell(value=guideline.name)
        elif col == GuidelineExcelColumnsEnum.GUIDELINE_DESCRIPTION:
            return SheetCell(value=guideline.get_description())
        else:
            raise ValueError(f"Unknown column {col} in guideline sheet")

    def get_summary_sheet_rows(self) -> list[SheetRowData]:
        rows = [SheetRowData(data={key: SheetCell(value="") for key in self._summary_cols})]
        count = 1
        base_amount = 0
        service_charge_amount = 0
        gross_amount = 0
        discounted_value = 0
        amount_without_gst = 0
        gst_value = 0
        final_amount = 0
        for section_name, data in self._summary.items():
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value=str(count))
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(value=section_name)
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(value=data.base_amount, type=SheetCellTypeEnum.DECIMAL)
                    base_amount += data.base_amount
                    service_charge_amount += data.service_charge_amount
                    gross_amount += data.gross_amount
                    discounted_value += data.discounted_value
                    amount_without_gst += data.amount_without_gst
                    gst_value += data.gst_value
                    final_amount += data.final_amount

            rows.append(SheetRowData(data=row_data))
            count += 1
        # rows.extend([SheetRowData(data={key: SheetCell(value="") for key in self._summary_cols})])
        row_data = {}
        if service_charge_amount:
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        value="Base Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=base_amount,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
            row_data = {}

            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        value="Service Charge Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=service_charge_amount,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        if discounted_value:
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        value="Gross Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=gross_amount,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
            row_data = {}

            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        value="Discount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=discounted_value,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        if gst_value:
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        value="Amount (w/o Tax)",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=amount_without_gst,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.SECTIONS:
                    row_data[key] = SheetCell(
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        value="Tax Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=gst_value,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        for key in self._summary_cols:
            if key == SummaryExcelColumnsEnum.S_NO:
                row_data[key] = SheetCell(value="")
            elif key == SummaryExcelColumnsEnum.SECTIONS:
                final_amount_text = "Final Amount" if not gst_value else "Final Amount (With Tax)"

                row_data[key] = SheetCell(
                    value=final_amount_text,
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
            elif key == SummaryExcelColumnsEnum.AMOUNT:
                row_data[key] = SheetCell(
                    value=final_amount,
                    type=SheetCellTypeEnum.DECIMAL,
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
        rows.append(SheetRowData(data=row_data))
        logger.info("BOQ summary sheet data created")
        return rows

    def _add_custom_field_data(
        self,
        element: BoqElement,
    ) -> dict[str, SheetCell]:
        formula_keys = []
        data = {}
        for key, col in self._column_dict.items():
            if element.extra_data is None or key not in element.extra_data:
                default_value = self._column_meta_data_dict.get(key).get("default_value")
                if default_value == "0":
                    data[key] = SheetCell(
                        type=SheetCellTypeEnum.DECIMAL,
                        value=round(decimal.Decimal(0), 2),
                    )
                else:
                    data[key] = SheetCell(
                        type=SheetCellTypeEnum.TEXT,
                        value=self._column_meta_data_dict.get(key).get("default_value"),
                    )
                continue
            if col.get("type") == "formula":
                formula_keys.append(key)
            elif col.get("type") == "dropdown":
                value_key = element.extra_data.get(key)
                value = self._column_options_dict.get(key).get(value_key)
                data[key] = SheetCell(
                    type=SheetCellTypeEnum.TEXT,
                    value=value,
                )
            else:
                data[key] = SheetCell(
                    type=SheetCellTypeEnum.TEXT,
                    value=element.extra_data.get(key),
                )
        for key in formula_keys:
            formula = self._column_meta_data_dict.get(key).get("formula")
            dependencies = self._column_meta_data_dict.get(key).get("dependency")
            flag = True
            is_attribute_or_custom_column_of_element = True
            for dependency in dependencies:
                if dependency not in data.keys():
                    flag = False
                elif not hasattr(element, dependency) and dependency not in data.keys():
                    # Checking dependencies if they are attribute of element or not
                    is_attribute_or_custom_column_of_element = False
            if flag:
                for field in data.keys():
                    if field in dependencies:
                        formula = formula.replace(field, str(data[field].value))
                data[key] = SheetCell(
                    type=SheetCellTypeEnum.DECIMAL,
                    value=round(eval(formula), 2),
                )
            elif is_attribute_or_custom_column_of_element:
                for dependency in sorted(dependencies, key=lambda x: len(x), reverse=True):
                    if dependency in data.keys():
                        formula = formula.replace(dependency, str(data[dependency].value))
                    else:
                        formula = formula.replace(dependency, str(getattr(element, dependency)))
                data[key] = SheetCell(
                    type=SheetCellTypeEnum.DECIMAL,
                    value=round(eval(formula), 2),
                )
            else:
                data[key] = SheetCell(
                    type=SheetCellTypeEnum.TEXT,
                    value="",
                )
        return data


class BoqGridViewExcelDataBuilder(BoqExcelDataBuilder):
    # Ordering of columns in the grid view
    _element_col_display_text_mapping = {
        ElementExcelColumnsEnum.S_NO: "S No",
        ElementExcelColumnsEnum.ELEMENT_CODE: "Code",
        ElementExcelColumnsEnum.ELEMENT_NAME: "Name",
        ElementExcelColumnsEnum.DESCRIPTION: "Description",
        ElementExcelColumnsEnum.BRAND_NAME: "Brand/Make",
        ElementExcelColumnsEnum.SECTION_NAME: "Section",
        ElementExcelColumnsEnum.ELEMENT_CATEGORY: "Category",
        ElementExcelColumnsEnum.LENGTH: "Length",
        ElementExcelColumnsEnum.LENGTH_UOM: "Length UOM",
        ElementExcelColumnsEnum.BREADTH: "Breadth",
        ElementExcelColumnsEnum.BREADTH_UOM: "Breadth UOM",
        ElementExcelColumnsEnum.UOM: "UOM",
        ElementExcelColumnsEnum.ITEM_TYPE: "Item Type",
        ElementExcelColumnsEnum.QUANTITY: "Quantity",
        ElementExcelColumnsEnum.DRAFT_QUANTITY: "Draft Quantity",
        ElementExcelColumnsEnum.CLIENT_RATE: "Client Rate",
        ElementExcelColumnsEnum.BUDGET_RATE: "Budget Rate",
        ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT: "Service Charge %",
        ElementExcelColumnsEnum.BASE_AMOUNT: "Base Amount",
        ElementExcelColumnsEnum.DISCOUNT_PERCENT: "Discount %",
        ElementExcelColumnsEnum.DISCOUNT_VALUE: "Discount Value",
        ElementExcelColumnsEnum.DISCOUNTED_VALUE: "Discounted Value",
        ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX: "Amount (w/o Tax)",
        ElementExcelColumnsEnum.HSN_CODE: "HSN",
        ElementExcelColumnsEnum.TAX_PERCENT: "Tax %",
        ElementExcelColumnsEnum.FINAL_AMOUNT: "Final Amount",
        ElementExcelColumnsEnum.SOURCE: "Source",
        ElementExcelColumnsEnum.STATUS: "Status",
        ElementExcelColumnsEnum.ORDER_STATUS: "Order Status",
        ElementExcelColumnsEnum.CREATED_AT: "Created At",
    }

    GridViewToGridExcelColumnMapping = {
        BoqGridViewColumnsEnum.CODE.value: ElementExcelColumnsEnum.ELEMENT_CODE,
        BoqGridViewColumnsEnum.NAME.value: ElementExcelColumnsEnum.ELEMENT_NAME,
        BoqGridViewColumnsEnum.DESCRIPTION.value: ElementExcelColumnsEnum.DESCRIPTION,
        BoqGridViewColumnsEnum.BRAND_NAME.value: ElementExcelColumnsEnum.BRAND_NAME,
        BoqGridViewColumnsEnum.SECTION.value: ElementExcelColumnsEnum.SECTION_NAME,
        BoqGridViewColumnsEnum.CATEGORY.value: ElementExcelColumnsEnum.ELEMENT_CATEGORY,
        BoqGridViewColumnsEnum.LENGTH.value: ElementExcelColumnsEnum.LENGTH,
        BoqGridViewColumnsEnum.LENGTH_UOM.value: ElementExcelColumnsEnum.LENGTH_UOM,
        BoqGridViewColumnsEnum.BREADTH.value: ElementExcelColumnsEnum.BREADTH,
        BoqGridViewColumnsEnum.BREADTH_UOM.value: ElementExcelColumnsEnum.BREADTH_UOM,
        BoqGridViewColumnsEnum.UOM.value: ElementExcelColumnsEnum.UOM,
        BoqGridViewColumnsEnum.ITEM_TYPE.value: ElementExcelColumnsEnum.ITEM_TYPE,
        BoqGridViewColumnsEnum.QUANTITY.value: ElementExcelColumnsEnum.QUANTITY,
        BoqGridViewColumnsEnum.REPORTED_QUANTITY.value: ElementExcelColumnsEnum.DRAFT_QUANTITY,
        BoqGridViewColumnsEnum.CLIENT_RATE.value: ElementExcelColumnsEnum.CLIENT_RATE,
        BoqGridViewColumnsEnum.BUDGET_RATE.value: ElementExcelColumnsEnum.BUDGET_RATE,
        BoqGridViewColumnsEnum.SERVICE_CHARGE_PERCENT.value: ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT,
        BoqGridViewColumnsEnum.BASE_AMOUNT.value: ElementExcelColumnsEnum.BASE_AMOUNT,
        BoqGridViewColumnsEnum.DISCOUNT_PERCENT.value: ElementExcelColumnsEnum.DISCOUNT_PERCENT,
        BoqGridViewColumnsEnum.DISCOUNT_VALUE.value: ElementExcelColumnsEnum.DISCOUNT_VALUE,
        BoqGridViewColumnsEnum.DISCOUNTED_VALUE.value: ElementExcelColumnsEnum.DISCOUNTED_VALUE,
        BoqGridViewColumnsEnum.AMOUNT_WITHOUT_TAX.value: ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX,
        BoqGridViewColumnsEnum.HSN_CODE.value: ElementExcelColumnsEnum.HSN_CODE,
        BoqGridViewColumnsEnum.TAX_PERCENT.value: ElementExcelColumnsEnum.TAX_PERCENT,
        BoqGridViewColumnsEnum.FINAL_AMOUNT.value: ElementExcelColumnsEnum.FINAL_AMOUNT,
        BoqGridViewColumnsEnum.ELEMENT_SOURCE.value: ElementExcelColumnsEnum.SOURCE,
        BoqGridViewColumnsEnum.ELEMENT_STATUS.value: ElementExcelColumnsEnum.STATUS,
        BoqGridViewColumnsEnum.ORDER_STATUS.value: ElementExcelColumnsEnum.ORDER_STATUS,
        BoqGridViewColumnsEnum.CREATED_AT.value: ElementExcelColumnsEnum.CREATED_AT,
    }

    def __init__(self, boq_id: int, org_filter: Q, org_id: int, column_ordering: list[str]):
        self._boq_id = boq_id
        self._org_filter = org_filter
        self._org_id = org_id
        self._element_quantity_service = BaseElementQuantityService()
        self._uom_helper = ElementUomInitializer()
        self._elements: QuerySet[BoqElement] = self.get_elements()
        self._custom_column_config: ProjectOrgCustomFieldConfig = get_project_org_custum_field_schema(
            project_id=self._boq_id, org_id=self._org_id, context=MicroContextChoices.BOQ_ELEMENT
        )
        self.column_ordering = column_ordering
        self.formula_dependencies = {ElementExcelColumnsEnum.REPORTED_QUANTITY.value}

        if self._custom_column_config:
            for column in self._custom_column_config.custom_schema:
                self._column_meta_data_dict[column.get("id")] = column.get("type_meta_data")
                self._column_dict[column.get("id")] = column
                if column.get("type") == "dropdown":
                    self._column_options_dict[column.get("id")] = {}
                    for option in column.get("type_meta_data").get("options"):
                        print()
                        self._column_options_dict[column.get("id")][option.get("id")] = option.get("name")

        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=boq_id)
        self._element_col_display_text_mapping[ElementExcelColumnsEnum.TAX_PERCENT] = (
            f"{project_config.tax_type.name} %"
        )
        self._element_col_display_text_mapping[ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX] = (
            f"Amount (w/o {project_config.tax_type.name})"
        )

    def remove_column_dict_keys(self, column: ElementExcelColumnsEnum):
        del self._column_dict[column]

    def filter_custom_columns_on_formula_dependency(self):
        custom_column_ids = []
        element_cols = set([col.value for col in self._element_cols])
        for custom_column_id, custom_column in self._column_dict.items():
            dependencies = set(
                [
                    dependency
                    for dependency in custom_column["type_meta_data"].get("dependency")
                    if not is_valid_uuid(dependency)
                ]
            )
            for dependency in dependencies:
                if dependency not in element_cols and dependency not in self.formula_dependencies:
                    custom_column_ids.append(custom_column_id)
                    break
        for column_id in custom_column_ids:
            self.remove_column_dict_keys(column_id)

    def remove_element_col(self, col: ElementExcelColumnsEnum):
        self._element_cols.remove(col)

    def get_elements(self) -> QuerySet[BoqElement]:
        return (
            boq_element_current_version_list(boq_id=self._boq_id)
            .filter(self._org_filter)
            .select_related("boq__project", "order_element__vendor_order")
            .annotate(
                production_drawing_count=Count(
                    "production_drawings", filter=Q(production_drawings__deleted_at__isnull=True)
                )
            )
            .annotate(guidelines_count=Count("guidelines", filter=Q(guidelines__deleted_at__isnull=True)))
            .annotate(order_number=F("order_element__vendor_order__order_number"))
            .annotate(job_id=F("boq__project__job_id"))
            .annotate(
                proposal_number=Subquery(
                    ProposalElementMapping.objects.select_related("proposal")
                    .filter(element_id=OuterRef("pk"))
                    .exclude(
                        Q(proposal__deleted_at__isnull=False)
                        | Q(element__element_status__in=[BoqElementStatus.DRAFT, BoqElementStatus.REJECTED])
                    )
                    .available()
                    .order_by("-proposal__created_at")
                    .values("proposal__ref_number")[:1]
                ),
            )
        )

    def get_element_sheet_all_headers(self) -> dict[Union[ElementExcelColumnsEnum, str], HeaderCell]:
        headers = super().get_element_sheet_all_headers()
        if self._custom_column_config:
            for column in self._custom_column_config.custom_schema:
                if column.get("id") not in self._column_dict:
                    # For removing the column from custom headers (dependency column case)
                    del headers[column["id"]]
        logger.info("Boq Element sheet header data created", headers=headers)
        return headers

    def get_element_sheet_headers(self) -> dict[Union[ElementExcelColumnsEnum, str], HeaderCell]:
        # Ordering columns as per Boq Grid View
        all_headers = self.get_element_sheet_all_headers()
        headers = {}
        seen_columns = set()
        # Adding headers as per column ordering
        if self.column_ordering:
            headers[ElementExcelColumnsEnum.S_NO] = all_headers[ElementExcelColumnsEnum.S_NO]
            for key in self.column_ordering:
                if key in self.GridViewToGridExcelColumnMapping:
                    headers[self.GridViewToGridExcelColumnMapping[key]] = all_headers[
                        self.GridViewToGridExcelColumnMapping[key]
                    ]
                elif (
                    isinstance(key, str) and is_valid_uuid(key) and key in all_headers
                ):  # Patch (To be corrected from Front End)
                    headers[key] = all_headers[key]
                seen_columns.add(key)
            # Adding remaining headers (if not given from FrontEnd)
            for key, value in all_headers.items():
                if key not in seen_columns and (
                    key in self._element_cols or (isinstance(key, str) and is_valid_uuid(key))
                ):
                    headers[key] = value
        else:
            for key, value in all_headers.items():
                if key in self._element_cols:
                    headers[key] = value
                elif isinstance(key, str) and is_valid_uuid(key):
                    headers[key] = value

        return headers


class BoqExcelDirectorService:
    visible_columns_name_mapping = {
        "brand": [ElementExcelColumnsEnum.BRAND_NAME],
        "discount": [
            ElementExcelColumnsEnum.DISCOUNT_VALUE,
            ElementExcelColumnsEnum.DISCOUNT_PERCENT,
            ElementExcelColumnsEnum.BASE_AMOUNT,
        ],
        "service_charge": [ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT],
        "budget_rate": [ElementExcelColumnsEnum.BUDGET_RATE],
        "base_amount": [ElementExcelColumnsEnum.BASE_AMOUNT],
        "hsn": [ElementExcelColumnsEnum.HSN_CODE],
        "tax_percent": [ElementExcelColumnsEnum.TAX_PERCENT],
    }

    def __init__(
        self,
        boq_id: int,
        org_filter: Q,
        org_id: int,
        visible_columns: list[str],
        with_elements: bool = True,
        with_guidelines: bool = True,
        with_client_rate: bool = False,
    ):
        self.boq_id = boq_id
        self.org_filter = org_filter
        self.with_elements = with_elements
        self.with_guidelines = with_guidelines
        self.with_client_rate = with_client_rate
        self.org_id = org_id
        self.data_builder = None
        self.visible_columns = visible_columns

    def build_excel(self):
        self.data_builder = BoqExcelDataBuilder(
            boq_id=self.boq_id,
            org_filter=self.org_filter,
            org_id=self.org_id,
        )

        if self.with_elements:
            self.data_builder = self.data_builder.add_sheet(sheet=SheetTypeEnum.ELEMENT).add_sheet(
                sheet=SheetTypeEnum.SUMMARY
            )
        if self.with_guidelines:
            self.data_builder = self.data_builder.add_sheet(sheet=SheetTypeEnum.GUIDELINE)
        data_builder = self.data_builder.add_element_cols(
            ElementExcelColumnsEnum.S_NO,
            ElementExcelColumnsEnum.ELEMENT_CODE,
            ElementExcelColumnsEnum.SECTION_NAME,
            ElementExcelColumnsEnum.ELEMENT_NAME,
            ElementExcelColumnsEnum.DESCRIPTION,
            ElementExcelColumnsEnum.ELEMENT_CATEGORY,
            ElementExcelColumnsEnum.ITEM_TYPE,
            ElementExcelColumnsEnum.QUANTITY,
            ElementExcelColumnsEnum.UOM,
            ElementExcelColumnsEnum.GUIDELINE_OR_PRODUCT_DRAWING_EXISTS,
            ElementExcelColumnsEnum.STATUS,
            ElementExcelColumnsEnum.REFERENCE_IMAGE,
        )
        if boq_element_quantity_breakdown_exists(boq_id=self.boq_id, org_id=self.org_id):
            data_builder = data_builder.add_element_cols(
                ElementExcelColumnsEnum.LENGTH,
                ElementExcelColumnsEnum.LENGTH_UOM,
                ElementExcelColumnsEnum.BREADTH,
                ElementExcelColumnsEnum.BREADTH_UOM,
            )
        if self.with_client_rate:
            data_builder = data_builder.add_element_cols(
                ElementExcelColumnsEnum.CLIENT_RATE,
                ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX,
                ElementExcelColumnsEnum.FINAL_AMOUNT,
            )
        if self.visible_columns:
            for column in self.visible_columns:
                if column in self.visible_columns_name_mapping:
                    data_builder = data_builder.add_element_cols(*self.visible_columns_name_mapping[column])

        data_builder = data_builder.add_summary_cols(
            SummaryExcelColumnsEnum.S_NO,
            SummaryExcelColumnsEnum.SECTIONS,
            SummaryExcelColumnsEnum.AMOUNT,
        )
        data_builder = data_builder.add_guideline_cols(
            GuidelineExcelColumnsEnum.S_NO,
            GuidelineExcelColumnsEnum.GUIDELINE_NAME,
            GuidelineExcelColumnsEnum.GUIDELINE_DESCRIPTION,
        )
        excel_file, filename = ExcelGenerator(data_builder=data_builder).generate()
        return excel_file, filename


class BoqGridViewExcelDirectorService:
    visible_columns_name_mapping = {
        "brand": [ElementExcelColumnsEnum.BRAND_NAME],
        "discount": [
            ElementExcelColumnsEnum.DISCOUNTED_VALUE,
            ElementExcelColumnsEnum.DISCOUNT_PERCENT,
            ElementExcelColumnsEnum.BASE_AMOUNT,
        ],
        "service_charge": [ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT],
        "budget_rate": [ElementExcelColumnsEnum.BUDGET_RATE],
        "base_amount": [ElementExcelColumnsEnum.BASE_AMOUNT],
        "hsn": [ElementExcelColumnsEnum.HSN_CODE],
        "tax_percent": [ElementExcelColumnsEnum.TAX_PERCENT],
        "amount": [ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX],
    }

    def __init__(
        self,
        boq_id: int,
        org_filter: Q,
        org_id: int,
        project_permissions: list,
        visible_columns: list[str],
        column_ordering: list[str],
    ):
        self.boq_id = boq_id
        self.org_filter = org_filter
        self.org_id = org_id
        self.project_permissions = project_permissions
        self.with_client_rate = True if Permissions.CAN_VIEW_CLIENT_RATE in self.project_permissions else False
        self.with_budget_rate = True if Permissions.CAN_VIEW_BUDGET_RATE in self.project_permissions else False
        self.visible_columns = visible_columns
        self.column_ordering = column_ordering

    def build_excel(self):
        self.data_builder = BoqGridViewExcelDataBuilder(
            boq_id=self.boq_id, org_filter=self.org_filter, org_id=self.org_id, column_ordering=self.column_ordering
        )
        self.data_builder.add_sheet(sheet=SheetTypeEnum.ELEMENT)

        self.data_builder.add_element_cols(
            ElementExcelColumnsEnum.S_NO,
            ElementExcelColumnsEnum.ELEMENT_CODE,
            ElementExcelColumnsEnum.ELEMENT_NAME,
            ElementExcelColumnsEnum.DESCRIPTION,
            ElementExcelColumnsEnum.SECTION_NAME,
            ElementExcelColumnsEnum.ELEMENT_CATEGORY,
        )

        if boq_element_quantity_breakdown_exists(boq_id=self.boq_id, org_id=self.org_id):
            self.data_builder.add_element_cols(
                ElementExcelColumnsEnum.LENGTH,
                ElementExcelColumnsEnum.LENGTH_UOM,
                ElementExcelColumnsEnum.BREADTH,
                ElementExcelColumnsEnum.BREADTH_UOM,
            )

        self.data_builder.add_element_cols(
            ElementExcelColumnsEnum.UOM,
            ElementExcelColumnsEnum.ITEM_TYPE,
            ElementExcelColumnsEnum.QUANTITY,
            ElementExcelColumnsEnum.DRAFT_QUANTITY,
        )

        if self.with_client_rate:
            self.data_builder.add_element_cols(
                ElementExcelColumnsEnum.CLIENT_RATE,
            )

        self.data_builder.add_element_cols(
            ElementExcelColumnsEnum.FINAL_AMOUNT,
            ElementExcelColumnsEnum.SOURCE,
            ElementExcelColumnsEnum.STATUS,
            ElementExcelColumnsEnum.ORDER_STATUS,
            ElementExcelColumnsEnum.CREATED_AT,
        )

        if self.visible_columns:
            for column in self.visible_columns:
                if column in self.visible_columns_name_mapping:
                    self.data_builder.add_element_cols(*self.visible_columns_name_mapping[column])

        if not self.with_budget_rate:
            self.data_builder.remove_element_col(ElementExcelColumnsEnum.BUDGET_RATE)

        # Filter out custom columns which have dependency on those columns that are not visible
        self.data_builder.filter_custom_columns_on_formula_dependency()

        excel_file, filename = ExcelGenerator(data_builder=self.data_builder).generate()
        return excel_file, filename
