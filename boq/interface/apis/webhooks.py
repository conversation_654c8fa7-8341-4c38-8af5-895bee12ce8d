import structlog
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from core.constants import ExternalWebhookType
from core.organization.domain.mixins import ExternalWebhookFileMixin
from project.data.models import Project

logger = structlog.get_logger(__name__)


class BoqExportPdfApi(ExternalWebhookFileMixin):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_EXPORT_BOQ_PDF]

    def get_webhook_data(self):
        return {
            "project_id": self.kwargs["boq_id"],
            "user_id": self.request.user.pk,
            "org_id": self.get_organization_id(),
        }

    def get_filename(self):
        return f"{Project.objects.get(pk=self.kwargs['boq_id']).job_id}_BOQ_PDF.pdf"

    @swagger_auto_schema(
        responses={HTTP_200_OK: "boq-export-pdf"},
        operation_id="boq_export_pdf",
        operation_summary="Export BOQ My Scope PDF",
    )
    def post(self, request, boq_id, *args, **kwargs):
        response = super().post(webhook_type=ExternalWebhookType.BOQ_ACTION_EXPORT_PDF)
        return Response(data=response, status=HTTP_200_OK)


class BoqExportAndSharePdfApi(ExternalWebhookFileMixin):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_EXPORT_AND_SHARE_BOQ_PDF]

    def get_webhook_data(self):
        return {
            "project_id": self.kwargs["boq_id"],
            "user_id": self.request.user.pk,
            "org_id": self.get_organization_id(),
        }

    def get_filename(self):
        return f"{Project.objects.get(pk=self.kwargs['boq_id']).job_id}_BOQ_PDF.pdf"

    @swagger_auto_schema(
        responses={HTTP_200_OK: "boq-export-pdf"},
        operation_id="boq_export_and_share_pdf",
        operation_summary="Export and Share BOQ My Scope PDF",
    )
    def post(self, request, boq_id, **kwargs):
        response = super().post(webhook_type=ExternalWebhookType.BOQ_ACTION_EXPORT_AND_SHARE_PDF)
        return Response(data=response, status=HTTP_200_OK)
