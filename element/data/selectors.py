from typing import List

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import <PERSON>, Count, F, Func, OuterRef, Prefetch, Q, QuerySet, Subquery, TextField, Value, When
from django.db.models.functions import Coalesce, Concat, Lower, Trim, TruncDate

from common.utils import padding_for_serial_number
from core.models import Organization, PmcClientMapping, User
from core.selectors import client_fetch_all as _client_fetch_all
from element.data.choices import ElementLibraryType, ItemTypeUpdateMethodChoices
from element.data.entities import ItemTypeConfigData, ItemTypeUpdateOptionsData
from element.data.models import (
    Element,
    ElementCategory,
    ElementCategoryOrgMapping,
    ElementGuideline,
    ElementGuidelineAttachment,
    ElementItemType,
    ElementLibrary,
    ElementLibraryGuideline,
    ElementLibraryGuidelineAttachments,
    ElementLibraryHistory,
    ElementLibraryMapping,
    ElementLibrarySection,
    ElementLibrarySectionSuggestion,
    ElementPreviewFile,
    ElementProductionDrawing,
    OrgItemTypeConfig,
)


def element_guideline_fetch(*, guideline_id: str):
    prefetch = Prefetch(
        "attachments",
        ElementGuidelineAttachment.objects.filter(deleted_at__isnull=True),
    )
    return ElementGuideline.objects.prefetch_related(prefetch).filter(id=guideline_id)


def element_section_suggestions_fetch(*, library_id: int):
    sections = (
        ElementLibrarySection.objects.filter(library_id=library_id).values_list(Lower("name"), flat=True).available()
    )
    suggestions = (
        ElementLibrarySectionSuggestion.objects.annotate(name_lower=Lower("name"))
        .annotate(is_checked=Case(When(name_lower__in=sections, then=Value(True)), default=Value(False)))
        .active()
    )
    return suggestions


def element_library_mapping_fetch_many(*, library_id: int) -> QuerySet[ElementLibraryMapping]:
    prefetch = Prefetch(
        "element__preview_files",
        ElementPreviewFile.objects.filter(is_main=True, deleted_at__isnull=True),
    )
    return (
        ElementLibraryMapping.objects.select_related("element", "section", "element__category", "element__item_type")
        .prefetch_related(prefetch)
        .filter(library_id=library_id)
        .order_by(F("section_id").asc(nulls_first=True), "position")
    )


def element_fetch_all_using_client(client_id: int) -> list:
    prefetch = Prefetch(
        "preview_files",
        ElementPreviewFile.objects.filter(is_main=True, deleted_at__isnull=True),
    )
    elements = Element.objects.available().filter(client_id=client_id).order_by("-id").prefetch_related(prefetch)
    section = {"id": "all-elements", "name": "All Elements"}
    element_sections = [{"element": element, "section": section, "position": 0} for element in elements]
    return element_sections


def element_fetch_all_and_client_rates_using_client(client_id: int, org_id: int) -> list:
    prefetch = Prefetch(
        "preview_files",
        ElementPreviewFile.objects.filter(is_main=True, deleted_at__isnull=True),
    )
    elements = (
        Element.objects.select_related("category", "item_type")
        .annotate_client_rate_and_service_charge_percent(org_id=org_id)
        .available()
        .filter(client_id=client_id)
        .order_by("-id")
        .prefetch_related(prefetch)
    )
    section = {"id": "all-elements", "name": "All Elements"}
    element_sections = [{"element": element, "section": section, "position": 0} for element in elements]
    return element_sections


def get_library_elements_with_one_preview_file(*, library_id):
    return (
        ElementLibraryMapping.objects.select_related("element", "section", "element__item_type")
        .prefetch_related(
            Prefetch(
                "element__preview_files",
                queryset=ElementPreviewFile.objects.filter(
                    is_main=True, deleted_at__isnull=True, type="IMAGE"
                ).order_by("-is_main"),
            )
        )
        .filter(library_id=library_id)
        .order_by(F("section_id").asc(nulls_first=True), "position")
    )


def library_fetch_all() -> QuerySet:
    subquery_section_count = ElementLibrary.objects.filter(id=OuterRef("id")).annotate(
        count=Count("sections", distinct=True, filter=Q(sections__deleted_at__isnull=True))
    )
    return (
        ElementLibrary.objects.annotate(
            # section_count=Count("sections", distinct=True, filter=Q(sections__deleted_at__isnull=True))
        )
        .annotate(section_count=Coalesce(Subquery(subquery_section_count.values("count")[:1]), Value(0)))
        .select_related("created_by__org", "updated_by__org", "store_type", "client")
        .annotate(element_count=Count("mappings", distinct=True))
        .order_by("-created_at")
        .available()
    )


def client_fetch_all(*, user: User):
    return (
        _client_fetch_all(user=user)
        .exclude(id=user.org_id)
        .annotate(
            library_count=Count(
                "client_library_mappings", distinct=True, filter=Q(client_library_mappings__vendor_id=user.org_id)
            ),
            rate_contract_count=Count("client_rate_contract", distinct=True),
        )
        .order_by("name")
        .all()
    )


def library_guideline_fetch(*, guideline_id: int):
    prefetch = Prefetch(
        "attachments",
        ElementLibraryGuidelineAttachments.objects.available(),
    )
    return ElementLibraryGuideline.objects.prefetch_related(prefetch).filter(id=guideline_id).available()


def library_guidelines_fetch(*, guideline_ids_list: List):
    prefetch = Prefetch(
        "attachments",
        ElementLibraryGuidelineAttachments.objects.available(),
    )
    return ElementLibraryGuideline.objects.prefetch_related(prefetch).filter(id__in=guideline_ids_list).available()


def element_duplicate_name_description_brand_list_fetch(*, library_id: int, hashIds: list):
    element_list = list(
        Element.objects.filter(mappings__library_id=library_id)
        .annotate(
            name_lower=Trim(Lower("name")),
            description_lower=Trim(Lower("description")),
            brand_name_lower=Trim(Lower("brand_name")),
        )
        .annotate(
            # name, description and brand_name are converted to md5 hash and then concatenated because
            # for example if name is 'abc' and desc is 'def' and other element has name 'ab' and
            # desc 'cdef', then md5 of concatenated string will be same which should not be the case.
            hash=Concat(
                Func("name_lower", function="MD5", output_field=TextField()),
                Func("description_lower", function="MD5", output_field=TextField()),
                Func("brand_name_lower", function="MD5", output_field=TextField()),
            )
        )
        .filter(hash__in=hashIds)
        .available()
        .prefetch_related("mappings")
        .values(
            "name_lower", "code", "serial_number", "mappings__library__name", "description_lower", "brand_name_lower"
        )
        .distinct("id")
    )
    element_name_library_code_dict = {
        (element["name_lower"], element["description_lower"], element["brand_name_lower"]): {
            "library_name": element["mappings__library__name"],
            "code": f"{element['code']}{padding_for_serial_number(serial_number=element['serial_number'], padding= 4)}",
            "description": element["description_lower"],
            "brand": element["brand_name_lower"],
        }
        for element in element_list
    }
    return element_name_library_code_dict


def element_categories_fetch_all():
    return ElementCategory.objects.filter(is_active=True)


def element_item_type_fetch_available():
    return ElementItemType.objects.filter(deleted_at__isnull=True)


def element_item_type_fetch_all():
    return ElementItemType.objects.all()


def element_library_using_element_list(*, element: Element) -> [int]:
    return ElementLibraryMapping.objects.filter(element=element).values_list("library_id", flat=True)


def element_count_fetch_using_client(client_id: int) -> int:
    return Element.objects.filter(client_id=client_id).available().count()


def element_library_fetch_using_client(client_id: int):
    return ElementLibrary.objects.filter(client_id=client_id).available().order_by("name")


def elements_fetch_all() -> QuerySet:
    guidelines_prefetch = Prefetch("guidelines", queryset=ElementGuideline.objects.available())
    guidelines_attachment = Prefetch("guidelines__attachments", queryset=ElementGuidelineAttachment.objects.available())
    production_drawing_prefetch = Prefetch("production_drawings", queryset=ElementProductionDrawing.objects.available())
    preview_file_prefetch = Prefetch("preview_files", queryset=ElementPreviewFile.objects.available())
    queryset = Element.objects.available().prefetch_related(
        guidelines_prefetch, guidelines_attachment, preview_file_prefetch, production_drawing_prefetch
    )
    return queryset


def elements_fetch_by_ids(element_ids: List[int]) -> QuerySet:
    queryset = elements_fetch_all().filter(id__in=element_ids)
    return queryset


def get_element_many(element_ids: List[int], org_id: int) -> List[Element]:
    return elements_fetch_by_ids(element_ids=element_ids).annotate_client_rate_and_service_charge_percent(org_id=org_id)


def element_get(element_id: int, org_id: int) -> Element:
    return (
        elements_fetch_by_ids(element_ids=[element_id])
        .annotate_client_rate_and_service_charge_percent(org_id=org_id)
        .first()
    )


def element_get_with_one_preview_file(element_id) -> QuerySet:
    prefetch = Prefetch(
        "preview_files",
        ElementPreviewFile.objects.filter(is_main=True, deleted_at__isnull=True),
    )
    return Element.objects.filter(id=element_id).prefetch_related(prefetch)


def get_element_get_with_one_preview_file_and_client_rate(element_id: int, org_id: int) -> Element:
    return (
        element_get_with_one_preview_file(element_id=element_id)
        .annotate_client_rate_and_service_charge_percent(org_id=org_id)
        .first()
    )


def self_client_get(client_id: int) -> Organization:
    self_client = (
        Organization.objects.filter(id=client_id, client__code__isnull=False)
        .select_related("client")
        .annotate(
            library_count=Count("libraries", distinct=True),
            rate_contract_count=Count("client_rate_contract", distinct=True),
        )
        .first()
    )
    return self_client


def get_element_mappings(organization_id: int, library_id: int) -> QuerySet:
    return element_library_mapping_fetch_many(library_id=library_id).annotate_client_rate_and_service_charge_percent(
        org_id=organization_id
    )


def filter_element_libraries_for_organization(org_id: int, client_id: int, show_all: bool, libraries: ElementLibrary):
    if org_id != client_id:
        show_all = False
    if org_id == client_id or PmcClientMapping.objects.filter(client_id=client_id, pmc_id=org_id).exists():
        if not show_all:
            libraries = libraries.filter(type=ElementLibraryType.STORE_TYPE)
    else:
        libraries = []

    return libraries


def library_history_list(*, library_id: int, client_id: int) -> QuerySet:
    return (
        ElementLibraryHistory.objects.filter(library_id=library_id, library__client_id=client_id)
        .annotate(
            date=TruncDate("created_at"),
            user_name=Func(F("created_by__first_name"), Value(" "), F("created_by__last_name"), function="CONCAT"),
            org_name=F("created_by__org__name"),
            user_photo=F("created_by__photo"),
        )
        .values("date", "created_by_id")
        .annotate(count=Count("id"), user_name=F("user_name"), org_name=F("org_name"), user_photo=F("user_photo"))
        .order_by("-date")
        .values("date", "user_name", "org_name", "user_photo")
    )


def element_category_list(*, organization_id: int) -> QuerySet:
    el_categories_org_mapping = ElementCategoryOrgMapping.objects.aggregate(
        global_inactive=ArrayAgg(
            "category_id",
            filter=Q(
                category__type=ElementCategory.CategoryScopeChoices.GLOBAL_SCOPE,
                is_active=False,
                organization_id=organization_id,
            ),
        ),
        local_active=ArrayAgg(
            "category_id",
            filter=Q(
                category__type=ElementCategory.CategoryScopeChoices.LOCAL_SCOPE,
                is_active=True,
                organization_id=organization_id,
            ),
        ),
    )

    global_el_category_ids = (
        ElementCategory.objects.filter(
            ~Q(id__in=el_categories_org_mapping["global_inactive"]),
            type=ElementCategory.CategoryScopeChoices.GLOBAL_SCOPE,
            is_active=True,
        )
        .values_list("id", flat=True)
        .all()
    )
    el_categories_ids = list(global_el_category_ids) + list(el_categories_org_mapping["local_active"])

    return ElementCategory.objects.filter(id__in=el_categories_ids)


def el_library_qty_dimensions_exists(element_library_id: int) -> bool:
    element_ids = ElementLibraryMapping.objects.filter(library_id=element_library_id).values_list(
        "element_id", flat=True
    )
    return Element.objects.filter(id__in=element_ids, quantity_dimensions__isnull=False).exists()


def org_item_type_config_fetch(org_id: int) -> list[ItemTypeConfigData]:
    item_type_configs: list[ItemTypeConfigData] = []
    item_type_config_obj_list = OrgItemTypeConfig.objects.filter(organization_id=org_id)
    item_type_update_method_dict = {
        item_type_config.item_type_id: ItemTypeUpdateMethodChoices(item_type_config.update_method)
        for item_type_config in item_type_config_obj_list
    }
    item_type_objs = ElementItemType.objects.all()
    for item_type in item_type_objs:
        update_method_options: list[ItemTypeUpdateOptionsData] = []
        default_update_method = (
            ItemTypeUpdateMethodChoices(item_type.default_update_method)
            if item_type.default_update_method
            else ItemTypeUpdateMethodChoices.PERCENTAGE
        )

        update_method = item_type_update_method_dict.get(item_type.pk, default_update_method)

        if default_update_method == ItemTypeUpdateMethodChoices.MILESTONE.value:
            update_method_options = [
                ItemTypeUpdateOptionsData(id=ItemTypeUpdateMethodChoices.MILESTONE, name="Milestone"),
                ItemTypeUpdateOptionsData(id=ItemTypeUpdateMethodChoices.QUANTITY, name="Quantity"),
            ]
        else:
            update_method_options = [
                ItemTypeUpdateOptionsData(id=ItemTypeUpdateMethodChoices.PERCENTAGE, name="Percentage"),
                ItemTypeUpdateOptionsData(id=ItemTypeUpdateMethodChoices.QUANTITY, name="Quantity"),
            ]
        item_type_configs.append(
            ItemTypeConfigData(
                id=item_type.pk,
                name=item_type.name,
                update_method=update_method,
                update_method_options=update_method_options,
            )
        )
    return item_type_configs
