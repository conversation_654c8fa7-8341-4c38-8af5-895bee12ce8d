import abc
from typing import List

from boq.domain.entities import BoqElementDiscountRepoData
from common.exceptions import BaseValidationError


class ElementServiceChargeAbstractRepo(abc.ABC):
    class ServiceChargeOperationException(BaseValidationError):
        pass

    @abc.abstractmethod
    def update_service_charge_percent(self, data: List[BoqElementDiscountRepoData], user_id: int):
        ...
