{"include": ["work_progress_v2/domain"], "exclude": ["**/tests", "**/test/**", "**/migrations", "**/__init__.py", "**/repo.py", "**/repos.py", "**/repository.py", "**/repositories.py", "**/testing/**", "**/.venv/**"], "useLibraryCodeForTypes": true, "typeCheckingMode": "basic", "reportUnknownMemberType": "information", "reportAttributeAccessIssue": "information", "reportMissingTypeStubs": "information"}