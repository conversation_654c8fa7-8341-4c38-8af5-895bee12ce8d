import structlog
from rest_framework import serializers

from common.element_base.services import ElementCodeService
from common.serializers import HashId<PERSON>ield
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from microcontext.serializers import BaseContextMetaDataSerializer

logger = structlog.getLogger(__name__)


class BaseProposalMetaDataSerializer(BaseContextMetaDataSerializer):
    proposal_code = serializers.CharField()
    proposal_id = HashIdField()
    proposal_type = serializers.CharField()

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


class BaseProposalItemMetaDataSerializer(BaseProposalMetaDataSerializer):
    item_code = serializers.SerializerMethodField()

    def get_item_code(self, obj):
        return ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.version
        )

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


class BaseProposalSentMetaDataSerializer(BaseContextMetaDataSerializer):
    project_id = HashIdField()
    project_name = serializers.CharField()
    project_job_id = serializers.CharField()
    template = serializers.CharField()
    proposal_id = HashIdField(source="meta_data.proposal_id")

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.PROPOSAL)
class ProposalMetaDataSerializer(BaseProposalMetaDataSerializer):
    ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.PROPOSAL_ITEM)
class ProposalItemMetaDataSerializer(BaseProposalItemMetaDataSerializer):
    ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.PROPOSAL_SENT)
class ProposalSentDataSerializer(BaseProposalSentMetaDataSerializer):
    proposal_ref_num = serializers.CharField(source="meta_data.proposal_ref_num")
    created_at = serializers.DateTimeField()
    org_name = serializers.CharField()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.PROPOSAL_REJECTED)
class ProposalRejectedDataSerializer(BaseProposalSentMetaDataSerializer):
    proposal_ref_num = serializers.CharField(source="meta_data.proposal_ref_num")
    created_at = serializers.DateTimeField()
    org_name = serializers.CharField()
