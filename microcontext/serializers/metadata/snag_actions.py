from datetime import datetime

from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from microcontext.choices import TemplateContextChoices
from microcontext.serializers.base import BaseContextMetaDataSerializer
from rollingbanners.hash_id_converter import HashIdConverter as Hc


class BaseSnagNotificationMetaSerializer(BaseContextMetaDataSerializer):
    project_id = serializers.SerializerMethodField()
    template = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_template(self, obj):
        return obj.meta_data.get("template")

    def get_project_id(self, obj):
        return Hc.encode(obj.meta_data.get("project_id"))


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.SNAG_ASSIGNED)
class SnagAssignmentNotificationMetaDataSerializer(BaseSnagNotificationMetaSerializer):
    project_job_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()
    template = serializers.SerializerMethodField()
    snag_title = serializers.SerializerMethodField()
    snag_due_date = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DateField())
    def get_snag_due_date(self, obj):
        return obj.snag_data.get("snag_due_date")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_snag_title(self, obj):
        return f"{obj.snag_data.get('snag_title')} ({obj.snag_data.get('snag_code')})"

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        breadcrumb = f"{obj.snag_data.get('project_name')} > Snags"
        return breadcrumb

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_project_job_id(self, obj):
        return obj.snag_data.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_title(self, obj):
        if obj.user.org_id == obj.snag_data.get("assigned_to_org_id"):
            title = "New snag has been assigned to your organization"
        else:
            title = f"New snag has been assigned to {obj.snag_data.get('assigned_to_org_name')}:"
        return title

    class Meta:
        ref_name = "SnagAssignmentMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.SNAG_ALLOTTED_AND_TIMELINE_COMMITTED
)
class SnagAllotAndTimelineCommittedNotificationMetaDataSerializer(BaseSnagNotificationMetaSerializer):
    project_job_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()
    snag_title = serializers.SerializerMethodField()
    committed_date = serializers.SerializerMethodField()
    allotee_user_name = serializers.SerializerMethodField()

    def get_allotee_user_name(self, obj):
        if obj.user_id == obj.snag_data.get("allotee_user_id"):
            return None
        return obj.snag_data.get("allotee_user_name")

    @swagger_serializer_method(serializer_or_field=serializers.DateField())
    def get_committed_date(self, obj):
        return obj.snag_data.get("committed_date") if obj.snag_data.get("committed_date") else None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_snag_title(self, obj):
        return f"{obj.snag_data.get('snag_title')} ({obj.snag_data.get('snag_code')})"

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        breadcrumb = f"{obj.snag_data.get('project_name')} > Snags"
        return breadcrumb

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_project_job_id(self, obj):
        return obj.snag_data.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_title(self, obj):
        if obj.user_id == obj.snag_data.get("allotee_user_id"):
            title = f"Snag has been allotted to you by {obj.snag_data.get('updated_by_name')}"
        else:
            title = f"Snag allotment updated by {obj.snag_data.get('updated_by_name')}"

        return title

    class Meta:
        ref_name = "SnagAllotAndCommitTimelineMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.SNAG_BULK_ASSIGNED)
class SnagBulkAssignmentNotificationMetaDataSerializer(BaseSnagNotificationMetaSerializer):
    project_job_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        breadcrumb = f"{obj.project_info.get('project_name')} > Snags"
        return breadcrumb

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_project_job_id(self, obj):
        return obj.project_info.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_title(self, obj):
        if obj.user.org_id == obj.meta_data.get("assigned_to_org_id"):
            title = f"{len(obj.meta_data.get('snag_ids'))} new snags have been assigned to your organization"
        else:
            title = (
                f"{len(obj.meta_data.get('snag_ids'))} new snags have been assigned to "
                f"{obj.assigned_organization_name}:"
            )
        return title

    class Meta:
        ref_name = "SnagBulkAssignmentMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.SNAG_MARKED_UNRESOLVED)
class SnagMarkedUnresolvedNotificationMetaDataSerializer(BaseSnagNotificationMetaSerializer):
    project_job_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()
    snag_title = serializers.SerializerMethodField()
    snag_due_date = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DateField())
    def get_snag_due_date(self, obj):
        return obj.snag_data.get("snag_due_date") if obj.snag_data.get("snag_due_date") else None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_snag_title(self, obj):
        return f"{obj.snag_data.get('snag_title')} ({obj.snag_data.get('snag_code')})"

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        breadcrumb = f"{obj.snag_data.get('project_name')} > Snags"
        return breadcrumb

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_project_job_id(self, obj):
        return obj.snag_data.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_title(self, obj):
        if obj.user.org_id == obj.snag_data.get("unresolved_by_org_id"):
            title = f"Snag has been marked as unresolved by {obj.snag_data.get('unresolved_by_name')}"
        else:
            title = (
                f"Snag has been marked as unresolved by "
                f"{obj.snag_data.get('unresolved_by_name')} from {obj.snag_data.get('unresolved_by_org_name')}"
            )

        return title

    class Meta:
        ref_name = "SnagMarkedUnresolvedMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.SNAG_BULK_ALLOTTED_AND_TIMELINE_COMMITTED
)
class SnagBulkAllotAndTimelineCommittedNotificationMetaDataSerializer(BaseSnagNotificationMetaSerializer):
    project_job_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()
    template = serializers.SerializerMethodField()
    committed_date = serializers.SerializerMethodField()
    allotee_user_name = serializers.SerializerMethodField()

    def get_allotee_user_name(self, obj):
        if obj.user_id != obj.allotee_id:
            return obj.allotee_name

    @swagger_serializer_method(serializer_or_field=serializers.DateField())
    def get_committed_date(self, obj):
        return (
            datetime.strptime(obj.meta_data.get("committed_at"), "%d-%m-%Y")
            if obj.meta_data.get("committed_at")
            else None
        )

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        breadcrumb = f"{obj.project_info.get('project_name')} > Snags"
        return breadcrumb

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_project_job_id(self, obj):
        return obj.project_info.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_title(self, obj):
        if obj.user_id == obj.allotee_id:
            title = (
                f"{len(obj.meta_data.get('snag_ids'))} {'snags' if len(obj.meta_data.get('snag_ids')) > 1 else 'snag'} "
                f"has been allotted to you by {obj.alloted_by_name}"
            )
        else:
            title = f"Snag allotment updated by {obj.alloted_by_name}"

        return title

    class Meta:
        ref_name = "SnagBulkAllotAndCommitTimelineMetaDataSerializer"
