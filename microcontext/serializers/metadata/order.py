import structlog
from rest_framework import serializers

from common.serializers import HashId<PERSON>ield
from microcontext.choices import MicroContextChoices
from microcontext.serializers import BaseContextMetaDataSerializer
from project.share.domain.constants import OrganizationTypeEnum

logger = structlog.getLogger(__name__)


class BaseOrderMetaDataSerializer(BaseContextMetaDataSerializer):
    order_number = serializers.CharField()
    order_type = serializers.CharField()
    order_id = HashIdField()

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


class BaseOrderItemMetaDataSerializer(BaseOrderMetaDataSerializer):
    name = serializers.CharField()

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.ORDER)
class OrderMetaDataSerializer(BaseOrderMetaDataSerializer):
    version = serializers.SerializerMethod<PERSON>ield()

    def get_version(self, obj):
        if hasattr(obj, "version"):
            return obj.version + 1


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.ORDER_ITEM)
class OrderItemMetaDataSerializer(BaseOrderItemMetaDataSerializer): ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.INVOICE)
class InvoiceeMetaDataSerializer(BaseContextMetaDataSerializer):
    invoice_number = serializers.CharField()
    invoice_type = serializers.SerializerMethodField()

    def get_invoice_type(self, obj):
        org_id = self.context.get("org_id")
        return OrganizationTypeEnum.VENDOR.value if obj.client_id == org_id else OrganizationTypeEnum.CLIENT.value

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()
