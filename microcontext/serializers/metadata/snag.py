from rest_framework import serializers

from common.utils import padding_for_serial_number
from microcontext.choices import MicroContextChoices
from microcontext.serializers.base import BaseContextMetaDataSerializer


class BaseSnagMetaSerializer(BaseContextMetaDataSerializer):
    code = serializers.SerializerMethodField()

    @staticmethod
    def get_code(instance):
        return f"SNG{padding_for_serial_number(serial_number=instance.serial_number,padding=3)}"

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.SNAG)
class SnagMetaSerializer(BaseSnagMetaSerializer):
    ...
