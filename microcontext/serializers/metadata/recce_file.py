import structlog
from rest_framework import serializers

from common.serializers import HashId<PERSON>ield
from microcontext.choices import MicroContextChoices
from microcontext.serializers.base import BaseContextMetaDataSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage

logger = structlog.getLogger(__name__)


class BaseRecceFileMetaDataSerializer(BaseContextMetaDataSerializer):
    file_name = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    recce_id = HashIdField()
    detail = serializers.CharField()
    section = serializers.CharField()
    section_id = HashIdField()
    field_id = HashIdField()

    @staticmethod
    def get_file_name(instance):
        if instance.name:
            return instance.name
        elif instance.file:
            return instance.file.split("/")[-1]
        return ""

    @staticmethod
    def get_file_url(instance):
        return PublicMediaFileStorage.url(instance.file) if instance.file else None

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.RECCE_FILE)
class RecceFileMetaDataSerializer(BaseRecceFileMetaDataSerializer): ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.RECCE_INTERNAL_FILE)
class RecceInternalFileMetaDataSerializer(BaseRecceFileMetaDataSerializer): ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.RECCE_EXTERNAL_FILE)
class RecceExternalFileMetaDataSerializer(BaseRecceFileMetaDataSerializer): ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.RECCE_LAYOUT_FILE)
class RecceLayoutFileMetaDataSerializer(BaseRecceFileMetaDataSerializer): ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.RECCE_SPACE_FILE)
class RecceSpaceFileMetaDataSerializer(BaseRecceFileMetaDataSerializer): ...
