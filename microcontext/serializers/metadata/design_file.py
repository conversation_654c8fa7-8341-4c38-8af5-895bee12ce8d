import structlog
from rest_framework import serializers

from common.serializers import HashId<PERSON>ield
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from microcontext.serializers.base import BaseContextMetaDataSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage

logger = structlog.getLogger(__name__)


class BaseDesignFileMetaDataSerializer(BaseContextMetaDataSerializer):
    file_name = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    version = serializers.SerializerMethodField()
    section_name = serializers.SerializerMethodField()

    @staticmethod
    def get_version(instance):
        return instance.version

    @staticmethod
    def get_file_name(instance):
        return instance.name

    @staticmethod
    def get_section_name(instance):
        return instance.section_name

    @staticmethod
    def get_file_url(instance):
        return PublicMediaFileStorage.url(instance.file) if instance.file else None

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()

    ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=MicroContextChoices.DESIGN_FILE)
class DesignFileMetaDataSerializer(BaseDesignFileMetaDataSerializer):
    ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.DESIGN_FILE_NEW_VERSION_UPLOADED
)
@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.DESIGN_FILE_NEW_VERSION_POST_FREEZE_UPLOADED
)
class DesignFileNewVersionInternalMetaDataSerializer(BaseContextMetaDataSerializer):
    project_id = HashIdField(source="meta_data.project_id")
    user_name = serializers.CharField()
    project_name = serializers.CharField()
    project_job_id = serializers.CharField()
    template = serializers.CharField(source="meta_data.template")
    file = serializers.CharField(source="meta_data.file_name")
    version = serializers.CharField(source="meta_data.file_version")

    class Meta:
        output_hash_id_fields = ()
        input_hash_id_fields = ()
