from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from commentv2.data.choices import CommentApprovalStatus
from microcontext.choices import TemplateContextChoices
from microcontext.serializers.base import BaseContextMetaDataSerializer
from microcontext.serializers.metadata.project_actions import (
    BaseProjectMetaDataSerializer,
)
from project.domain.status import Module
from rollingbanners.hash_id_converter import HashIdConverter


class ProjectCommentNotification(BaseProjectMetaDataSerializer):
    user_name = serializers.CharField()
    comment_preview_text = serializers.CharField()
    breadcrumb = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        if obj.module != Module.ORDER:
            return obj.meta_data.get("breadcrumb")
        breadcrumb_parts = obj.meta_data.get("breadcrumb").split(">")
        if len(breadcrumb_parts) >= 2 and breadcrumb_parts[1] == "Incoming Order":
            breadcrumb_parts[0] = str(Module.BOQ.label)
        return ">".join(breadcrumb_parts)

    class Meta:
        pass


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.COMMENT_MENTIONED)
class CommentMentionedNotificationSerializer(ProjectCommentNotification):
    pass


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.COMMENT_APPROVAL_ACCEPTED)
class CommentApprovalAcceptedNotificationSerializer(ProjectCommentNotification):
    pass


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.COMMENT_APPROVAL_REQUESTED)
class CommentApprovalRequestedNotificationSerializer(ProjectCommentNotification):
    pass


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.COMMENT_APPROVAL_REJECTED)
class CommentApprovalRejectedNotificationSerializer(ProjectCommentNotification):
    pass


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.PROJECT_COMMENT)
class ProjectCommentNotificationSerializer(ProjectCommentNotification):
    ...


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.PROJECT_COMMENT_APPROVAL_ACCEPTED
)
class ProjectCommentApprovalAcceptedNotificationSerializer(ProjectCommentNotification):
    sender_name = serializers.CharField()


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.PROJECT_COMMENT_APPROVAL_REJECTED
)
class ProjectCommentApprovalRejectedNotificationSerializer(ProjectCommentNotification):
    sender_name = serializers.CharField()


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.PROJECT_COMMENT_APPROVAL_REQUESTED
)
class ProjectCommentApprovalRequestedNotificationSerializer(ProjectCommentNotification):
    sender_name = serializers.CharField()


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.PROJECT_COMMENT_MENTIONED)
class ProjectCommentMentionedNotificationSerializer(ProjectCommentNotification):
    ...


class CommentApprovalNotifyBaseMetaDataSerializer(BaseContextMetaDataSerializer):
    title = serializers.SerializerMethodField()
    breadcrumb = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    project_id = serializers.SerializerMethodField()
    template = serializers.SerializerMethodField()
    project_job_id = serializers.SerializerMethodField()
    task_id = serializers.SerializerMethodField()

    def get_breadcrumb(self, obj):
        return obj.meta_data.get("breadcrumb")

    def get_description(self, obj):
        return obj.meta_data.get("preview_text")

    def get_project_id(self, obj):
        return HashIdConverter.encode(obj.comment_data.get("project_id"))

    def get_project_job_id(self, obj):
        if hasattr(obj, "comment_data"):
            return obj.comment_data.get("project_job_id")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_template(self, obj):
        return obj.meta_data.get("template")

    def get_task_id(self, obj):
        task_id = obj.comment_data.get("task_id")
        if task_id:
            return HashIdConverter.encode(task_id)

    class Meta:
        ref_name = "CommentApprovalNotifyBaseMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.COMMENT_APPROVAL_REQUESTED_V2
)
class CommentApprovalRequestedMetaDataSerializer(CommentApprovalNotifyBaseMetaDataSerializer):
    def get_title(self, obj):
        comment_data = obj.comment_data
        return f"{comment_data.get('requester_name')} has requested an approval from you on a comment"

    class Meta:
        ref_name = "CommentApprovalRequestedMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(context=TemplateContextChoices.COMMENT_APPROVAL_MENTIONED)
class CommentApprovalMentionedMetaDataSerializer(CommentApprovalNotifyBaseMetaDataSerializer):
    def get_title(self, obj):
        comment_data = obj.comment_data
        return f"You are mentioned in an approval request by {comment_data.get('requester_name')}"

    class Meta:
        ref_name = "CommentApprovalMentionedMetaDataSerializer"


@BaseContextMetaDataSerializer.register_meta_data_serializer(
    context=TemplateContextChoices.COMMENT_APPROVAL_ACCEPTED_REJECTED
)
class CommentApprovalRequestedRejectedMetaDataSerializer(CommentApprovalNotifyBaseMetaDataSerializer):
    def get_title(self, obj):
        comment_data = obj.comment_data
        if comment_data.get("status") == CommentApprovalStatus.APPROVED.value:
            status = "successfully approved"
        elif comment_data.get("status") == CommentApprovalStatus.REJECTED.value:
            status = "rejected"
        return f"Your request on comment is {status} by {comment_data.get('approver_name')}"

    class Meta:
        ref_name = "CommentApprovalRequestedRejectedMetaDataSerializer"
