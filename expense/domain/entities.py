import decimal
from dataclasses import dataclass, field
from datetime import date
from typing import List, Optional

from common.context_values import BaseContextValues, ProjectContextValueMixin
from common.entities import BaseObjectData, ObjectStatus
from payment_entry.domain.entities import BasePaymentEntryCreateData


@dataclass
class ExpenseItemProofData(BaseObjectData):
    id: Optional[int]
    file: str
    name: str


@dataclass
class ExpenseItemData(BaseObjectData):
    id: Optional[int]
    boq_element_id: Optional[int]
    name: str
    expense_type_id: int
    quantity: decimal.Decimal
    uom: int
    rate: decimal.Decimal
    gst_percent: decimal.Decimal
    item_date: Optional[date]
    attached_proofs: list[ExpenseItemProofData] = field(default_factory=list)
    library_element_id: Optional[int] = None


@dataclass
class ExpenseProofData(BaseObjectData):
    id: Optional[int]
    file: str
    name: str


@dataclass
class ExpensePaymentEntryCreateData(BasePaymentEntryCreateData):
    is_cancelled: bool = False
    object_status: Optional[ObjectStatus] = None
    expense_id: Optional[int] = None
    id: Optional[int] = None


@dataclass
class ExpenseCreateInputData:
    id: Optional[int]
    project_id: int
    start_date: Optional[date]
    end_date: Optional[date]
    remark: Optional[str]
    is_draft: bool
    items: List[ExpenseItemData]
    proofs: List[ExpenseProofData]
    incurred_by_id: int
    payment_entries: list[ExpensePaymentEntryCreateData]


@dataclass(frozen=True)
class ExpenseAmountValueProviderData(BaseContextValues, ProjectContextValueMixin):
    ...
