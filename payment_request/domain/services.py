import decimal
from typing import Dict, List, Optional

import structlog
from django.db.models import Q, QuerySet
from django.utils import timezone

from approval_request.domain.callbacks import RequestToResourceBaseCallback
from approval_request.domain.constants import RequestHierarchyCasesActionsEnum, RequestStatusEnum
from approval_request.domain.entities import (
    RequestActionData,
    RequestCreateServiceData,
    ResourceToRequestResetServiceData,
)
from approval_request.domain.services.request import RequestActionService, ResourceToRequestService
from approval_request.interface.factories import ResourceToRequestServiceFactory
from authorization.domain.constants import Permissions
from common.entities import ObjectStatus
from common.events import Events
from common.events.requests import ResourceRequestCancelEventData
from common.events.services import trigger_event
from common.exceptions import BaseValidationError
from common.json_parser.constants import TextEditorJsonConstants
from common.services import AllowedActions, model_update

# from payment_entry.domain.factories import PaymentEntryAbstractService
from core.helpers import OrgPermissionHelper
from core.models import User
from core.services import get_runtime_user_with_token_data
from microcontext.domain.constants import MicroContext
from order.data.models import VendorOrder, VendorPurchaseOrder
from order.data.selectors.selector_v1 import purchase_order_max_version
from order.invoice.data.models import Invoice
from payment_entry.domain.services import PaymentEntryAbstractService
from payment_request.data.choices import PaymentRequestEventChoices, PaymentRequestStatus, PaymentRequestType
from payment_request.data.models import (
    PaymentEntry,
    PaymentRequest,
    PaymentRequestAndRequestMapping,
    PaymentRequestAttachment,
    PaymentRequestHistory,
)
from payment_request.data.selectors import (
    get_invoice_with_amount_details,
    get_purchase_order_with_amount_details,
    invoice_fetch,
    payment_entry_get,
    payment_request_get,
    payment_request_with_paid_amount_get,
    purchase_order_fetch,
)
from payment_request.domain.constants import PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE, PaymentRequestActionEnum
from payment_request.domain.entities import (
    PaymentEntryCreateData,
    PaymentRequestAttachmentData,
    PaymentRequestCreateData,
    PaymentRequestEntryCreateData,
    PaymentRequestUpdateData,
)
from payment_request.domain.exceptions import PaymentEntryException, PaymentRequestException
from payment_request.domain.triggers import payment_entry_created_trigger
from payment_request.domain.validators import PaymentRequestAmountValidator
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData
from project.domain.services import check_or_assign_project_permission

logger = structlog.get_logger(__name__)


class PaymentEntryService(PaymentEntryAbstractService):
    class PaymentEntryServiceException(PaymentEntryException):
        pass

    class InvalidPaymentRequestModel(PaymentEntryServiceException):
        pass

    class InvalidAmountException(PaymentEntryServiceException):
        pass

    def __init__(self, user):
        self.user = user

    def validate_payment_entries_amount(
        self, payment_request_entries: list[PaymentRequestEntryCreateData], payment_request: PaymentRequest
    ):
        total_amount = sum([entry.amount for entry in payment_request_entries])
        if total_amount > payment_request.request_amount:
            logger.info(
                "invalid payment_entries_amount",
                total_amount=total_amount,
                request_amount=payment_request.request_amount,
            )
            raise self.PaymentEntryServiceException("Invalid payment entries amount")

    def validate_payment_entry_amount(self, amount: decimal.Decimal, payment_request: PaymentRequest):
        if amount <= 0 or amount > (payment_request.request_amount - payment_request.paid_amount):
            logger.info("invalid payment_entry_amount", amount=amount)
            raise self.InvalidAmountException("Invalid payment entry amount")

    def create_entry(self, data: PaymentEntryCreateData):
        payment_request = payment_request_get(payment_request_id=data.payment_request_id)
        if payment_request.vendor_id != self.user.org_id and payment_request.client_id != self.user.org_id:
            logger.info(
                "Invalid vendor or client id", vendor_id=payment_request.vendor_id, client_id=payment_request.client_id
            )
            raise self.PaymentEntryServiceException("Invalid vendor or client id")

        # Check for TDS validation
        if payment_request.invoice:
            max_amount_allowed = payment_request.request_amount - payment_request.paid_amount
            max_payment_entry_amount_allowed = max(
                min(
                    max_amount_allowed,
                    payment_request.invoice.amount
                    + payment_request.invoice.gst_amount
                    - (payment_request.invoice.tds_amount or 0)
                    - payment_request.invoice_without_order_unapproved_paid_till_date,
                ),
                0,
            )
            if data.amount > max_payment_entry_amount_allowed:
                logger.info(
                    "Invalid payment entry amount",
                    amount=data.amount,
                    max_payment_entry_amount_allowed=max_payment_entry_amount_allowed,
                )
                raise self.InvalidAmountException(
                    "Payment Entry can not be more than {max_payment_entry_amount_allowed}"
                )

        self.validate_payment_entry_amount(amount=data.amount, payment_request=payment_request)
        created_obj = self.create(data=data, payment_request=payment_request, save=True)
        payment_entry_created_trigger(
            project_id=payment_request.project_id,
            payment_request_id=payment_request.id,
            org_id=payment_request.client_id,
            user_id=self.user.id,
            entry_id=created_obj.id,
        )
        return created_obj

    def create(self, data: PaymentRequestEntryCreateData, payment_request: PaymentRequest, save=True):
        payment_entry = PaymentEntry()
        payment_entry.payment_request_id = payment_request.id
        payment_entry.amount = data.amount
        payment_entry.payment_date = data.payment_date
        payment_entry.remark = data.remark
        payment_entry.file = data.file
        payment_entry.file_name = data.file_name
        payment_entry.payment_mode = data.payment_mode
        payment_entry.transaction_id = data.transaction_id
        payment_entry.created_by_id = self.user.id
        if save:
            payment_entry.save()
        return payment_entry

    def cancel(self, payment_entry_id: int, save: bool = True):
        payment_entry = payment_entry_get(id=payment_entry_id)
        if payment_entry.cancelled_at:
            raise self.PaymentEntryServiceException("Payment entry already cancelled")
        if payment_entry.payment_request.cancelled_at:
            raise self.PaymentEntryServiceException("Payment request is already cancelled")
        if payment_entry.created_by.org_id != self.user.org_id:
            raise self.PaymentEntryServiceException("You are not allowed to cancel this payment entry")

        payment_entry.cancelled_at = timezone.now()
        payment_entry.cancelled_by_id = self.user.id
        if save:
            payment_entry.save()
        return payment_entry

    def cancel_bulk_entries(self, payment_queryset: QuerySet[PaymentEntry]):
        payment_queryset.filter(cancelled_at=None).update(cancelled_at=timezone.now(), cancelled_by_id=self.user.id)
        return True

    def bulk_create(self, data: list[PaymentRequestEntryCreateData], payment_request: PaymentRequest):
        logger.info("Payment entries bulk create started")
        self.validate_payment_entries_amount(data, payment_request=payment_request)
        objs = []
        for payment_entry_data in data:
            objs.append(self.create(data=payment_entry_data, payment_request=payment_request, save=False))

        created_objs = PaymentEntry.objects.bulk_create(objs)
        for entry in created_objs:
            payment_entry_created_trigger(
                project_id=payment_request.project_id,
                payment_request_id=payment_request.id,
                org_id=payment_request.client_id,
                user_id=self.user.id,
                entry_id=entry.id,
            )
        return created_objs

    def bulk_update(self, data: list[PaymentRequestEntryCreateData], payment_request: PaymentRequest):
        logger.info("Payment entries bulk update started")
        new_payment_entries_objs = []
        cancelled_payment_entries_objs = []
        for payment_entry_data in data:
            if payment_entry_data.object_status == ObjectStatus.ADD:
                new_payment_entries_objs.append(payment_entry_data)
            elif payment_entry_data.object_status == ObjectStatus.UPDATE:
                if payment_entry_data.is_cancelled:
                    cancelled_payment_entries_objs.append(
                        PaymentEntry(
                            id=payment_entry_data.id, cancelled_at=timezone.now(), cancelled_by_id=self.user.id
                        )
                    )
            else:
                # No delete object status implemented
                pass
        if new_payment_entries_objs:
            self.bulk_create(data=new_payment_entries_objs, payment_request=payment_request)
        if cancelled_payment_entries_objs:
            PaymentEntry.objects.bulk_update(cancelled_payment_entries_objs, fields=["cancelled_at", "cancelled_by_id"])


class PaymentRequestValidatorService:
    class PaymentRequestPurchaseOrderValidationException(PaymentRequestException):
        pass

    class PaymentRequestTypeException(PaymentRequestException):
        pass

    class PaymentRequestAmountException(PaymentRequestException):
        pass

    @classmethod
    def validate_request_type(cls, request_type, purchase_order_id, invoice_id):
        if request_type == PaymentRequestType.PO_ADVANCE.value:
            if not purchase_order_id:
                raise cls.PaymentRequestTypeException("Purchase order is required for this request type")
        elif request_type == PaymentRequestType.INVOICE.value:
            if not invoice_id:
                raise cls.PaymentRequestTypeException("Invoice is required for this request type")
        elif request_type == PaymentRequestType.NON_ORDER.value:
            if purchase_order_id or invoice_id:
                raise cls.PaymentRequestTypeException(
                    "Purchase order and invoice are not allowed for this request type"
                )

    @classmethod
    def validate_request_amount_and_gst_amount(cls, request_amount, gst_amount):
        if cls._validate_request_amount(request_amount):
            raise cls.PaymentRequestAmountException("Request amount should be greater than 0")
        if cls._validate_gst_amount(gst_amount):
            raise cls.PaymentRequestAmountException("Tax amount should be greater than or equal to 0")
        if cls._validate_request_and_gst_amount(request_amount, gst_amount):
            raise cls.PaymentRequestAmountException("Tax amount should be less than or equal to request amount")

    @classmethod
    def validate_percentage_and_amount(
        cls, amount: decimal.Decimal, amount_percent: decimal.Decimal, final_amount: decimal.Decimal
    ) -> bool:
        return round((amount * amount_percent) / 100, 2) == final_amount

    @classmethod
    def validate_request_amount_data(
        cls,
        data: PaymentRequestUpdateData,
        base_amount: decimal.Decimal,
        tax_amount: decimal.Decimal,
    ):
        # Data's Request amount consists of base amount and gst amount
        cls.validate_request_amount_and_gst_amount(request_amount=data.request_amount, gst_amount=data.gst_amount)
        if data.base_amount_percent_toggle:
            if not cls.validate_percentage_and_amount(
                base_amount,
                data.base_amount_percent,
                data.request_amount - data.gst_amount,
            ):
                raise cls.PaymentRequestAmountException("Base amount percent is not valid")

        if data.gst_amount_percent_toggle:
            if not cls.validate_percentage_and_amount(tax_amount, data.gst_amount_percent, data.gst_amount):
                raise cls.PaymentRequestAmountException("Tax amount percent is not valid")

    @classmethod
    def validate_payment_request_data_against_purchase_order(
        cls,
        request_base_amount: decimal.Decimal,
        request_gst_amount: decimal.Decimal,
        payment_entries_amount: decimal.Decimal,
        purchase_order: VendorPurchaseOrder,
        exclude_payment_request_id: int = None,
    ):
        requestable_base_amount = PaymentRequestService.get_requestable_base_amount_for_po(
            purchase_order=purchase_order, include_request_base_amount=0
        )
        requestable_gst_amount = PaymentRequestService.get_requestable_gst_amount_for_po(
            purchase_order=purchase_order, include_request_gst_amount=0
        )

        if exclude_payment_request_id:
            payment_request = PaymentRequest.objects.filter(id=exclude_payment_request_id).first()
            exclude_payment_request_base_amount = payment_request.request_amount - payment_request.gst_amount
            exclude_payment_request_gst_amount = payment_request.gst_amount
        else:
            exclude_payment_request_base_amount = 0
            exclude_payment_request_gst_amount = 0

        data = {
            "request_base_amount": request_base_amount,
            "request_gst_amount": request_gst_amount,
            "requestable_base_amount": requestable_base_amount,
            "requestable_gst_amount": requestable_gst_amount,
            "exclude_payment_request_base_amount": exclude_payment_request_base_amount,
            "exclude_payment_request_gst_amount": exclude_payment_request_gst_amount,
            "payment_entries_amount": payment_entries_amount,
            "max_payment_entry_amount_allowed": request_base_amount + request_gst_amount,
        }

        validator = PaymentRequestAmountValidator(data=data)
        validator.is_valid(raise_exception=True)

    @classmethod
    def validate_payment_request_data_against_invoice(
        cls,
        request_base_amount: decimal.Decimal,
        request_gst_amount: decimal.Decimal,
        payment_entries_amount: decimal.Decimal,
        invoice: Invoice,
        exclude_payment_request_id: int = None,
    ):
        requestable_base_amount = PaymentRequestService.get_requestable_base_amount_for_invoice(
            invoice=invoice, include_request_base_amount=0
        )
        requestable_gst_amount = PaymentRequestService.get_requestable_gst_amount_for_invoice(
            invoice=invoice, include_request_gst_amount=0
        )

        if exclude_payment_request_id:
            payment_request = PaymentRequest.objects.filter(id=exclude_payment_request_id).first()
            exclude_payment_request_base_amount = payment_request.request_amount - payment_request.gst_amount
            exclude_payment_request_gst_amount = payment_request.gst_amount
        else:
            exclude_payment_request_base_amount = 0
            exclude_payment_request_gst_amount = 0

        data = {
            "request_base_amount": request_base_amount,
            "request_gst_amount": request_gst_amount,
            "requestable_base_amount": requestable_base_amount,
            "requestable_gst_amount": requestable_gst_amount,
            "exclude_payment_request_base_amount": exclude_payment_request_base_amount,
            "exclude_payment_request_gst_amount": exclude_payment_request_gst_amount,
            "payment_entries_amount": payment_entries_amount,
            "max_payment_entry_amount_allowed": min(
                request_base_amount + request_gst_amount,
                invoice.amount + invoice.gst_amount - (invoice.tds_amount or 0),
            ),
        }

        validator = PaymentRequestAmountValidator(data=data)
        validator.is_valid(raise_exception=True)

    @classmethod
    def validate_payment_entries_amount_against_payment_request(
        cls,
        request_base_amount: decimal.Decimal,
        request_gst_amount: decimal.Decimal,
        payment_entries_amount: decimal.Decimal,
    ):
        data = {
            "request_base_amount": request_base_amount,
            "request_gst_amount": request_gst_amount,
            "requestable_base_amount": request_base_amount,
            "requestable_gst_amount": request_gst_amount,
            "exclude_payment_request_base_amount": 0,
            "exclude_payment_request_gst_amount": 0,
            "payment_entries_amount": payment_entries_amount,
            "max_payment_entry_amount_allowed": request_base_amount + request_gst_amount,
        }
        validator = PaymentRequestAmountValidator(data=data)
        validator.is_valid(raise_exception=True)

    @classmethod
    def validate_purchase_order_with_amount(
        cls,
        data: PaymentRequestUpdateData,
        purchase_order: VendorPurchaseOrder,
        exclude_payment_request_id: int = None,
    ):
        cls.validate_request_amount_data(
            data=data,
            base_amount=purchase_order.amount,
            tax_amount=purchase_order.tax_amount,
        )
        cls.validate_payment_request_data_against_purchase_order(
            request_base_amount=data.request_amount - data.gst_amount,
            request_gst_amount=data.gst_amount,
            payment_entries_amount=sum(
                [
                    payment_entry.amount
                    for payment_entry in data.payment_request_entries
                    if not payment_entry.is_cancelled
                ]
            ),
            purchase_order=purchase_order,
            exclude_payment_request_id=exclude_payment_request_id,
        )

    @classmethod
    def validate_invoice_with_amount(
        cls,
        data: PaymentRequestUpdateData,
        invoice: Invoice,
        exclude_payment_request_id: int = None,
    ):
        cls.validate_request_amount_data(
            data=data,
            base_amount=invoice.amount - invoice.credit_notes_amount,
            tax_amount=invoice.gst_amount - invoice.credit_notes_gst_amount,
        )
        cls.validate_payment_request_data_against_invoice(
            request_base_amount=data.request_amount - data.gst_amount,
            request_gst_amount=data.gst_amount,
            payment_entries_amount=sum(
                [
                    payment_entry.amount
                    for payment_entry in data.payment_request_entries
                    if not payment_entry.is_cancelled
                ]
            ),
            invoice=invoice,
            exclude_payment_request_id=exclude_payment_request_id,
        )

    @classmethod
    def validate_non_order_with_amount(cls, data: PaymentRequestUpdateData):
        cls.validate_request_amount_data(
            data=data,
            base_amount=data.request_amount,
            tax_amount=data.gst_amount,
        )
        cls.validate_payment_entries_amount_against_payment_request(
            request_base_amount=data.request_amount - data.gst_amount,
            request_gst_amount=data.gst_amount,
            payment_entries_amount=sum(
                [
                    payment_entry.amount
                    for payment_entry in data.payment_request_entries
                    if not payment_entry.is_cancelled
                ]
            ),
        )

    @classmethod
    def validate_request_data(cls, data: PaymentRequestUpdateData):
        errors = []
        if data.purchase_order_id and data.invoice_id:
            raise PaymentRequestException("Either PO or Invoice is required")

        if cls._validate_request_amount(data.request_amount):
            errors.append({"request_amount": "Request amount should be greater than 0"})
        if cls._validate_gst_amount(data.gst_amount):
            errors.append({"gst_amount": "Tax amount should be greater than or equal to 0"})
        if cls._validate_request_and_gst_amount(data.request_amount, data.gst_amount):
            errors.append({"gst_amount": "Tax amount should be less than or equal to request amount"})

        if data.base_amount_percent_toggle and not (0 <= data.base_amount_percent <= 100):
            errors.append({"base_amount_percent": "Base amount percentage should be greater than or equal to 0"})
        if data.gst_amount_percent_toggle and not (0 <= data.gst_amount_percent <= 100):
            errors.append({"gst_amount_percent": "Tax amount percentage should be greater than or equal to 0"})

        return errors

    @staticmethod
    def _validate_request_amount(request_amount: decimal.Decimal):
        return request_amount <= 0

    @staticmethod
    def _validate_gst_amount(gst_amount: decimal.Decimal):
        return gst_amount < 0

    @staticmethod
    def _validate_request_and_gst_amount(request_amount: decimal.Decimal, gst_amount: decimal.Decimal):
        return request_amount < gst_amount


class PaymentRequestService:
    validator_service = PaymentRequestValidatorService

    class PaymentRequestServiceException(PaymentRequestException):
        pass

    class PaymentRequestInvoiceValidationException(PaymentRequestServiceException):
        pass

    class PaymentRequestNotFoundException(PaymentRequestServiceException):
        pass

    class PaymentRequestTypeException(PaymentRequestServiceException):
        pass

    class PaymentRequestAmountException(PaymentRequestServiceException):
        pass

    class PaymentRequestPaymentRequestValidationException(PaymentRequestServiceException):
        pass

    class PaymentRequestCancelException(PaymentRequestException):
        pass

    class PaymentRequestUpdateException(PaymentRequestException):
        pass

    class PaymentRequestPurchaseOrderValidationException(PaymentRequestServiceException):
        pass

    def __init__(self, user):
        self.user = user

    def get_payment_request_amount_details(self, data: dict):
        include_request_base_amount, include_request_gst_amount = self._get_included_request_amounts(data)

        if data.get("type") == PaymentRequestType.PO_ADVANCE.value:
            return self._get_po_advance_details(data, include_request_base_amount, include_request_gst_amount)
        elif data.get("type") == PaymentRequestType.INVOICE.value:
            return self._get_invoice_details(data, include_request_base_amount, include_request_gst_amount)
        else:
            raise self.PaymentRequestTypeException("Invalid request type")

    def _get_included_request_amounts(self, data: dict):
        include_request_base_amount = 0
        include_request_gst_amount = 0
        if data.get("payment_request_id"):
            payment_request = (
                PaymentRequest.objects.filter(id=data.get("payment_request_id"))
                .values("request_amount", "gst_amount")
                .first()
            )
            if payment_request:
                include_request_base_amount = payment_request.get("request_amount") - payment_request.get("gst_amount")
                include_request_gst_amount = payment_request.get("gst_amount")
        return include_request_base_amount, include_request_gst_amount

    def _get_po_advance_details(
        self, data: dict, include_request_base_amount: decimal.Decimal, include_request_gst_amount: decimal.Decimal
    ):
        po_data = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}
        already_requested_amount_data = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}
        requestable_amount = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}

        purchase_order = get_purchase_order_with_amount_details(purchase_order_id=data.get("purchase_order_id"))
        po_data["base_amount"] = purchase_order.amount
        po_data["gst_amount"] = purchase_order.tax_amount
        po_data["total_amount"] = po_data["base_amount"] + po_data["gst_amount"]

        already_requested_amount_data["base_amount"] = (
            purchase_order.already_requested_amount_on_po_and_invoices
            - purchase_order.already_requested_gst_amount_on_po_and_invoices
        )
        already_requested_amount_data["gst_amount"] = purchase_order.already_requested_gst_amount_on_po_and_invoices
        already_requested_amount_data["total_amount"] = (
            already_requested_amount_data["base_amount"] + already_requested_amount_data["gst_amount"]
        )

        requestable_amount["base_amount"] = self.get_requestable_base_amount_for_po(
            purchase_order=purchase_order, include_request_base_amount=include_request_base_amount
        )
        requestable_amount["gst_amount"] = self.get_requestable_gst_amount_for_po(
            purchase_order=purchase_order, include_request_gst_amount=include_request_gst_amount
        )
        requestable_amount["total_amount"] = requestable_amount["base_amount"] + requestable_amount["gst_amount"]

        paid_amount = purchase_order.already_paid_amount

        return {
            "po_data": po_data,
            "invoice_data": None,
            "already_requested_amount_data": already_requested_amount_data,
            "paid_amount": paid_amount,
            "requestable_amount": requestable_amount,
        }

    def _get_invoice_details(
        self, data: dict, include_request_base_amount: decimal.Decimal, include_request_gst_amount: decimal.Decimal
    ):
        po_data = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}
        invoice_total_data = {
            "base_amount": 0,
            "gst_amount": 0,
            "total_amount": 0,
            "has_credit_note": False,
            "credit_note_base_amount": 0,
            "credit_note_gst_amount": 0,
            "credit_note_total_amount": 0,
        }
        invoice_data = {
            "base_amount": 0,
            "gst_amount": 0,
            "total_amount": 0,
        }
        already_requested_amount_data = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}
        requestable_amount = {"base_amount": 0, "gst_amount": 0, "total_amount": 0}

        invoice = get_invoice_with_amount_details(invoice_id=data.get("invoice_id"))
        invoice_data["base_amount"] = invoice.amount - invoice.credit_notes_amount
        invoice_data["gst_amount"] = invoice.gst_amount - invoice.credit_notes_gst_amount
        invoice_data["total_amount"] = invoice_data["base_amount"] + invoice_data["gst_amount"]

        invoice_total_data["base_amount"] = invoice.amount
        invoice_total_data["gst_amount"] = invoice.gst_amount
        invoice_total_data["total_amount"] = invoice_total_data["base_amount"] + invoice_total_data["gst_amount"]
        invoice_total_data["has_credit_note"] = invoice.credit_notes_count > 0
        invoice_total_data["credit_note_base_amount"] = invoice.credit_notes_amount
        invoice_total_data["credit_note_gst_amount"] = invoice.credit_notes_gst_amount
        invoice_total_data["credit_note_total_amount"] = (
            invoice_total_data["credit_note_base_amount"] + invoice_total_data["credit_note_gst_amount"]
        )

        already_requested_amount_data["base_amount"] = (
            invoice.already_requested_amount - invoice.already_requested_gst_amount
        )
        already_requested_amount_data["gst_amount"] = invoice.already_requested_gst_amount
        already_requested_amount_data["total_amount"] = (
            already_requested_amount_data["base_amount"] + already_requested_amount_data["gst_amount"]
        )

        requestable_amount["base_amount"] = self.get_requestable_base_amount_for_invoice(
            invoice=invoice, include_request_base_amount=include_request_base_amount
        )
        requestable_amount["gst_amount"] = self.get_requestable_gst_amount_for_invoice(
            invoice=invoice, include_request_gst_amount=include_request_gst_amount
        )
        requestable_amount["total_amount"] = requestable_amount["base_amount"] + requestable_amount["gst_amount"]

        paid_amount = invoice.already_paid_amount
        po_data["base_amount"] = invoice.po_base_amount
        po_data["gst_amount"] = invoice.po_gst_amount
        po_data["total_amount"] = po_data["base_amount"] + po_data["gst_amount"]

        return {
            "po_data": po_data,
            "invoice_data": invoice_data,
            "invoice_total_data": invoice_total_data,
            "already_requested_amount_data": already_requested_amount_data,
            "paid_amount": paid_amount,
            "requestable_amount": requestable_amount,
        }

    @classmethod
    def get_requestable_base_amount_for_po(
        cls, purchase_order: VendorPurchaseOrder, include_request_base_amount: decimal.Decimal
    ) -> decimal.Decimal:
        requestable_base_amount = (
            max(
                min(
                    max(purchase_order.amount, purchase_order.invoices_total_base_amount)
                    - (
                        purchase_order.already_requested_amount_on_po_and_invoices
                        - purchase_order.already_requested_gst_amount_on_po_and_invoices
                    ),
                    purchase_order.amount
                    - (
                        purchase_order.already_requested_amount_on_po_and_invoices
                        - purchase_order.already_requested_gst_amount_on_po_and_invoices
                    ),
                ),
                0,
            )
            + include_request_base_amount
        )
        return requestable_base_amount

    @classmethod
    def get_requestable_gst_amount_for_po(
        cls, purchase_order: VendorPurchaseOrder, include_request_gst_amount: decimal.Decimal
    ) -> decimal.Decimal:
        requestable_gst_amount = (
            max(
                min(
                    max(purchase_order.tax_amount, purchase_order.invoices_total_gst_amount)
                    - purchase_order.already_requested_gst_amount_on_po_and_invoices,
                    purchase_order.tax_amount - purchase_order.already_requested_gst_amount_on_po_and_invoices,
                ),
                0,
            )
            + include_request_gst_amount
        )
        return requestable_gst_amount

    @classmethod
    def get_requestable_base_amount_for_invoice(
        cls, invoice: Invoice, include_request_base_amount: decimal.Decimal
    ) -> decimal.Decimal:
        requestable_base_amount = (
            max(
                min(
                    max(invoice.po_base_amount, invoice.invoices_total_base_amount)
                    - (
                        invoice.already_requested_amount_on_po_and_invoices
                        - invoice.already_requested_gst_amount_on_po_and_invoices
                    ),
                    invoice.amount
                    - invoice.credit_notes_amount
                    - (invoice.already_requested_amount - invoice.already_requested_gst_amount),
                ),
                0,
            )
            + include_request_base_amount
        )
        return requestable_base_amount

    @classmethod
    def get_requestable_gst_amount_for_invoice(
        cls, invoice: Invoice, include_request_gst_amount: decimal.Decimal
    ) -> decimal.Decimal:
        requestable_gst_amount = (
            max(
                min(
                    max(invoice.po_gst_amount, invoice.invoices_total_gst_amount)
                    - invoice.already_requested_gst_amount_on_po_and_invoices,
                    invoice.gst_amount - invoice.credit_notes_gst_amount - invoice.already_requested_gst_amount,
                ),
                0,
            )
            + include_request_gst_amount
        )
        return requestable_gst_amount

    def create_history(self, payment_request: PaymentRequest):
        PaymentRequestHistory.objects.create(
            payment_request_id=payment_request.id,
            created_by_id=self.user.id,
            invoice_id=payment_request.invoice_id,
            purchase_order_id=payment_request.purchase_order_id,
            request_amount=payment_request.request_amount,
            gst_amount=payment_request.gst_amount,
            remark=payment_request.remark,
            cancelled_at=payment_request.cancelled_at,
            cancelled_by_id=payment_request.cancelled_by_id,
        )

    def _create_and_update_attachments(
        self, payment_request: PaymentRequest, attachments: List[PaymentRequestAttachmentData]
    ):
        logger.info("Creating and updating payment attachments.")
        current_attachments = payment_request.attachments.all()
        current_attachment_ids: list[int] = [attachment.id for attachment in current_attachments]
        incoming_attachment_ids: list[int] = []
        new_attachment_objs: list[PaymentRequestAttachment] = []

        for attachment in attachments:
            if not attachment.id:
                new_attachment_objs.append(
                    PaymentRequestAttachment(
                        file=attachment.file,
                        name=attachment.name,
                        payment_request_id=payment_request.pk,
                        uploaded_by_id=self.user.pk,
                    )
                )
            else:
                incoming_attachment_ids.append(attachment.id)

        deleted_attachment_ids: list[int] = list(set(current_attachment_ids) - set(incoming_attachment_ids))

        if new_attachment_objs:
            PaymentRequestAttachment.objects.bulk_create(new_attachment_objs)
        if deleted_attachment_ids:
            PaymentRequestAttachment.objects.filter(id__in=deleted_attachment_ids).soft_delete(user_id=self.user.pk)
        logger.info("Payment-request attachments created and updated successfully.")

    def create(self, data: PaymentRequestCreateData):
        logger.info("Payment-request creation started.")
        self.validator_service.validate_request_type(
            request_type=data.request_type,
            purchase_order_id=data.purchase_order_id,
            invoice_id=data.invoice_id,
        )
        payment_request = PaymentRequest()
        order_id = None
        payment_request.project_id = data.project_id
        payment_request.client_id = data.client_id
        if data.request_type == PaymentRequestType.PO_ADVANCE.value:
            if data.attachment:
                raise self.PaymentRequestTypeException("Attachment is not allowed for this request type.")

            payment_request.purchase_order_id = data.purchase_order_id

            purchase_order = get_purchase_order_with_amount_details(purchase_order_id=data.purchase_order_id)

            if not purchase_order:
                raise self.PaymentRequestPurchaseOrderValidationException("Not a valid purchase order")

            self.validator_service.validate_purchase_order_with_amount(data=data, purchase_order=purchase_order)

            order_id = purchase_order.vendor_order_id

        if data.request_type == PaymentRequestType.INVOICE.value:
            if data.attachment:
                raise self.PaymentRequestTypeException("Attachment is not allowed for this request type.")

            payment_request.invoice_id = data.invoice_id
            invoice = get_invoice_with_amount_details(invoice_id=data.invoice_id)
            if not invoice:
                raise self.PaymentRequestInvoiceValidationException("Not a valid invoice")
            self.validator_service.validate_invoice_with_amount(data=data, invoice=invoice)

            order_id = invoice.order_id

        if data.request_type == PaymentRequestType.NON_ORDER.value:
            self.validator_service.validate_non_order_with_amount(
                data=data,
            )
            if data.purchase_order_id or data.invoice_id:
                raise self.PaymentRequestTypeException(
                    "Purchase order and invoice are not allowed for this request type"
                )

        payment_request.vendor_id = data.vendor_id
        payment_request.creator_id = self.user.org_id
        payment_request.request_type = data.request_type
        payment_request.request_amount = data.request_amount
        payment_request.gst_amount = data.gst_amount
        payment_request.created_by_id = self.user.pk
        payment_request.remark = data.remark
        payment_request.base_amount_percent_toggle = data.base_amount_percent_toggle
        payment_request.gst_amount_percent_toggle = data.gst_amount_percent_toggle

        if data.base_amount_percent_toggle:
            payment_request.base_amount_percent = data.base_amount_percent

        if data.gst_amount_percent_toggle:
            payment_request.gst_amount_percent = data.gst_amount_percent

        payment_request.save()
        self.create_history(payment_request=payment_request)
        logger.info("Payment-request created successfully.")

        self._create_and_update_attachments(payment_request=payment_request, attachments=data.attachment)

        # TODO: PRD Check for payment entries amount
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=data.project_id)
        PaymentEntryService(user=self.user).bulk_create(
            data=data.payment_request_entries, payment_request=payment_request
        )
        logger.info("Payment entries created successfully")

        logger.info("Payment-request request creation started")
        request_data = RequestCreateServiceData(
            created_by_id=self.user.id,
            event=PaymentRequestEventChoices.SUBMIT,
            context=MicroContext.PAYMENT_REQUEST.value,
            context_id=payment_request.pk,
            org_id=data.client_id,
            project_id=data.project_id,
            order_id=order_id,
            description=[
                {
                    "text": f"{project_config.currency.symbol} {payment_request.request_amount}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ],
        )
        resource_request_service = ResourceToRequestServiceFactory.get_service(
            self,
            data=request_data,
            resource_callback=RequestToPaymentRequestCallback(resource_id=payment_request.pk, user_id=self.user.id),
        )
        try:
            logger.info("PaymentRequest request creation input", RequestCreateServiceData=request_data.__dict__)
            resource_request_service.create_request(
                data=request_data,
                on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
            )
        except ResourceToRequestService.CreateException as e:
            raise self.PaymentRequestServiceException("Payment-request request not created") from e
        logger.info("Payment-request request created", request_data=request_data)
        if payment_request.request_type == PaymentRequestType.NON_ORDER.value:
            if payment_request.client_id == self.user.org_id:
                logger.info("Payment-request assigning to vendor org", request_data=request_data)
                check_or_assign_project_permission(
                    project_id=data.project_id,
                    to_assign_org=payment_request.vendor_id,
                    assigned_by_org=self.user.org_id,
                    created_by_id=self.user.pk,
                )

        return payment_request

    def update(self, payment_request_id, data: PaymentRequestUpdateData):
        # todo: need to add condition for approved payment request

        payment_request = payment_request_with_paid_amount_get(payment_request_id=payment_request_id)
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=payment_request.project_id)
        if payment_request.created_by_id != self.user.id:
            raise self.PaymentRequestUpdateException("You are not allowed to update this payment request")
        if payment_request.cancelled_at:
            raise self.PaymentRequestCancelException("Cannot update cancelled payment request")
        self.validator_service.validate_request_type(
            request_type=payment_request.request_type,
            purchase_order_id=data.purchase_order_id,
            invoice_id=data.invoice_id,
        )
        if (
            payment_request.request_type in [PaymentRequestType.PO_ADVANCE.value, PaymentRequestType.INVOICE.value]
            and data.attachment
        ):
            raise self.PaymentRequestTypeException("Attachment is not allowed for this request type.")

        allowed_fields = [
            "purchase_order_id",
            "invoice_id",
            "request_amount",
            "gst_amount",
            "remark",
            "base_amount_percent",
            "gst_amount_percent",
            "base_amount_percent_toggle",
            "gst_amount_percent_toggle",
        ]
        payment_request, is_updated, updated_fields = model_update(
            instance=payment_request,
            fields=allowed_fields,
            data=data,
            clean=False,
            save=False,
            updated_by_id=self.user.id,
        )
        self._create_and_update_attachments(payment_request=payment_request, attachments=data.attachment)

        if "request_amount" in updated_fields or "gst_amount" in updated_fields:
            # The amount percentage should be reset to zero in case the toggle is turned off.
            if not data.base_amount_percent_toggle:
                payment_request.base_amount_percent = 0
            if not data.gst_amount_percent_toggle:
                payment_request.gst_amount_percent = 0

        order_id = None

        # if any of these fields are updated, then request needs to be reset
        request_updated_fields = [
            "request_amount",
            "gst_amount",
            "base_amount_percent",
            "gst_amount_percent",
            "base_amount_percent_toggle",
            "gst_amount_percent_toggle",
        ]
        if payment_request.request_type == PaymentRequestType.PO_ADVANCE.value and (
            "purchase_order_id" in updated_fields or set(request_updated_fields).intersection(set(updated_fields))
        ):
            purchase_order = get_purchase_order_with_amount_details(
                purchase_order_id=data.purchase_order_id,
            )
            if not purchase_order:
                raise self.PaymentRequestPurchaseOrderValidationException("Not a valid purchase order")
            self.validator_service.validate_purchase_order_with_amount(
                data=data,
                purchase_order=purchase_order,
                exclude_payment_request_id=payment_request.id,
            )

            order_id = purchase_order.vendor_order_id

        elif payment_request.request_type == PaymentRequestType.INVOICE.value and (
            "invoice_id" in updated_fields or set(request_updated_fields).intersection(set(updated_fields))
        ):
            invoice = get_invoice_with_amount_details(
                invoice_id=data.invoice_id,
            )
            if not invoice:
                raise self.PaymentRequestInvoiceValidationException("Not a valid invoice")
            self.validator_service.validate_invoice_with_amount(
                data=data,
                invoice=invoice,
                exclude_payment_request_id=payment_request.id,
            )

            order_id = invoice.order_id

        elif payment_request.request_type == PaymentRequestType.NON_ORDER.value:
            self.validator_service.validate_non_order_with_amount(
                data=data,
            )
            if data.purchase_order_id or data.invoice_id:
                raise self.PaymentRequestTypeException(
                    "Purchase order and invoice are not allowed for this request type"
                )

        if is_updated:
            payment_request.save()
            logger.info("Payment-request updated successfully")

        self.validator_service.validate_payment_entries_amount_against_payment_request(
            request_base_amount=data.request_amount - data.gst_amount,
            request_gst_amount=data.gst_amount,
            payment_entries_amount=sum(
                [
                    payment_entry.amount
                    for payment_entry in data.payment_request_entries
                    if not payment_entry.is_cancelled
                ]
            ),
        )

        PaymentEntryService(user=self.user).bulk_update(
            data=data.payment_request_entries, payment_request=payment_request
        )

        if not is_updated:
            return payment_request

        if payment_request.status == PaymentRequestStatus.SUBMITTED.value and payment_request.request_data:
            return
        if set(request_updated_fields).intersection(set(updated_fields)):
            if payment_request.status == PaymentRequestStatus.SUBMITTED.value:
                logger.info("Payment-request request creation started")
                request_data = RequestCreateServiceData(
                    created_by_id=self.user.id,  # request creator is user who updates payment-request after hierarchy activation  # noqa
                    event=PaymentRequestEventChoices.UPDATE,
                    context=MicroContext.PAYMENT_REQUEST.value,
                    context_id=payment_request.pk,
                    org_id=payment_request.client_id,
                    project_id=payment_request.project_id,
                    order_id=order_id,
                    description=[
                        {
                            "text": f"{project_config.currency.symbol} {payment_request.request_amount}",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                    ],
                )
                resource_request_service = ResourceToRequestServiceFactory.get_service(
                    self,
                    data=request_data,
                    resource_callback=RequestToPaymentRequestCallback(
                        resource_id=payment_request.pk, user_id=self.user.id
                    ),
                )
                try:
                    logger.info(
                        "Payment-request request creation input", RequestCreateServiceData=request_data.__dict__
                    )
                    resource_request_service.create_request(
                        data=request_data,
                        on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                        on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                    )
                except ResourceToRequestService.CreateException as e:
                    raise self.PaymentRequestServiceException("payment-request request not created") from e
                logger.info("Payment-request request created", request_data=request_data)
            else:
                logger.info("Payment-request request reset started")
                request_data = ResourceToRequestResetServiceData(
                    created_by_id=payment_request.request_data.get("created_by_id"),
                    event=PaymentRequestEventChoices.UPDATE,
                    context=MicroContext.PAYMENT_REQUEST.value,
                    context_id=payment_request.pk,
                    org_id=payment_request.client_id,
                    request_id=payment_request.request_data.get("request_id"),
                    project_id=payment_request.project_id,
                    order_id=order_id,
                    description=[
                        {
                            "text": f"{project_config.currency.symbol} {payment_request.request_amount}",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                    ],
                    user_id=self.user.id,
                )
                resource_request_service = ResourceToRequestServiceFactory.get_service(
                    self,
                    data=request_data,
                    resource_callback=RequestToPaymentRequestCallback(
                        resource_id=payment_request.pk, user_id=self.user.id
                    ),
                )
                try:
                    logger.info(
                        "Payment-request request reset input", ResourceToRequestResetServiceData=request_data.__dict__
                    )
                    resource_request_service.reset_request(
                        data=request_data,
                        on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                        on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                    )
                except ResourceToRequestService.UpdateException as e:
                    raise self.PaymentRequestServiceException("Payment-request request not resetted") from e
                logger.info(
                    "Payment-request request resetted",
                    request_id=payment_request.request_data.get("request_id"),
                    request_data=request_data,
                )

        self.create_history(payment_request=payment_request)
        return payment_request

    def cancel(self, payment_request_id, is_admin=False):
        payment_request = payment_request_get(payment_request_id=payment_request_id)

        if payment_request.created_by_id != self.user.id and not is_admin:
            raise self.PaymentRequestCancelException("You are not allowed to cancel this payment request")
        if payment_request.cancelled_at:
            raise self.PaymentRequestCancelException("Payment request already cancelled")
        payment_request.cancelled_at = timezone.now()
        payment_request.cancelled_by_id = self.user.id
        payment_request.save()
        self.update_payment_request_status(
            payment_request_id=payment_request_id, status=PaymentRequestStatus.CANCELLED.value
        )
        PaymentEntryService(user=self.user).cancel_bulk_entries(payment_queryset=payment_request.payment_entries.all())
        return payment_request

    def cancel_on_parent_resource_cancellation(
        self, invoice_ids: Optional[List[int]] = None, purchase_order_ids: Optional[List[int]] = None
    ):
        if not invoice_ids and not purchase_order_ids:
            return
        request_filter = Q()
        if invoice_ids:
            request_filter = request_filter | Q(invoice_id__in=invoice_ids)
        if purchase_order_ids:
            request_filter = request_filter | Q(purchase_order_id__in=purchase_order_ids)

        payment_requests = PaymentRequest.objects.filter(
            ~Q(status=PaymentRequestStatus.CANCELLED.value), request_filter
        )
        payment_request_ids = payment_requests.values_list("id", flat=True)
        for payment_request_id in payment_request_ids:
            trigger_event(
                event=Events.RESOURCE_CANCEL,
                event_data=ResourceRequestCancelEventData(
                    context=MicroContext.PAYMENT_REQUEST.value, context_id=payment_request_id, user_id=self.user.id
                ),
            )
        PaymentEntryService(user=self.user).cancel_bulk_entries(
            payment_queryset=PaymentEntry.objects.filter(payment_request_id__in=payment_request_ids)
        )
        payment_requests.update(
            cancelled_at=timezone.now(), cancelled_by_id=self.user.id, status=PaymentRequestStatus.CANCELLED.value
        )

        return True

    @staticmethod
    def update_payment_request_status(payment_request_id: int, status: PaymentRequestStatus):
        # TODO: This should be generic for all resources and use Action Service

        logger.info("Payment-request status updation started")
        payment_request = payment_request_get(payment_request_id=payment_request_id)
        if not payment_request:
            logger.info("Payment-request not found", payment_request_id=payment_request_id)
            raise PaymentRequestService.PaymentRequestNotFoundException("Payment-request not found.")
        if (
            payment_request.status == PaymentRequestStatus.CANCELLED
            or payment_request.status == PaymentRequestStatus.REJECTED
        ):
            raise PaymentRequestService.PaymentRequestServiceException(
                "Payment-request request is already in final state."
            )
        if status == PaymentRequestStatus.APPROVED and payment_request.status not in [
            PaymentRequestStatus.HOLD,
            PaymentRequestStatus.REQUEST_MISCONFIGURED,
            PaymentRequestStatus.PENDING,
        ]:
            raise PaymentRequestService.PaymentRequestServiceException("Payment-request request cannot be approved.")
        if status == PaymentRequestStatus.HOLD and payment_request.status != PaymentRequestStatus.PENDING:
            raise PaymentRequestService.PaymentRequestServiceException("Payment-request request cannot be put on hold.")
        if status == PaymentRequestStatus.REJECTED and (
            payment_request.status
            not in [
                PaymentRequestStatus.HOLD,
                PaymentRequestStatus.REQUEST_MISCONFIGURED,
                PaymentRequestStatus.PENDING,
            ]
        ):
            raise PaymentRequestService.PaymentRequestServiceException("Payment-request request cannot be rejected.")

        payment_request.status = status
        payment_request.save(update_fields=["status"])
        logger.info("Payment-request status updated")


class RequestToPaymentRequestCallback(RequestToResourceBaseCallback):
    class RequestToPaymentRequestCallbackException(BaseValidationError):
        pass

    def on_create(self, request_id: int, task_id: int, event: str, comment_id: int):
        try:
            PaymentRequestAndRequestMapping.objects.create(
                request_id=request_id,
                resource_id=self.resource_id,
                event=event,
                task_id=task_id,
                created_by_id=self.user_id,
                comment_id=comment_id,
            )
        except PaymentRequestAndRequestMapping.ModelDataIntegrityException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e
        PaymentRequestService.update_payment_request_status(
            payment_request_id=self.resource_id, status=PaymentRequestStatus.PENDING.value
        )

    def on_request_not_required(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.SUBMITTED.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_cancel(self):
        try:
            user = User.objects.filter(id=self.user_id).first()
            PaymentRequestService(user=user).cancel(payment_request_id=self.resource_id, is_admin=True)
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_reset(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.PENDING.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_approve(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.APPROVED.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_pending(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.PENDING.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_hold(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.HOLD.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e

    def on_reject(self):
        try:
            PaymentRequestService.update_payment_request_status(
                payment_request_id=self.resource_id, status=PaymentRequestStatus.REJECTED.value
            )
        except PaymentRequestService.PaymentRequestServiceException as e:
            raise self.RequestToPaymentRequestCallbackException(e) from e


class PaymentRequestAndRequestActionService(RequestActionService):
    def __init__(self, data: RequestActionData, is_admin: bool, user_id: int, org_id: int):
        super().__init__(data=data, is_admin=is_admin, user_id=user_id)
        self.org_id = org_id

    def can_cancel(self):
        if self.data.status in [
            RequestStatusEnum.APPROVED.value,
            RequestStatusEnum.REJECTED.value,
            RequestStatusEnum.CANCELLED.value,
        ]:
            return False
        return self.user_id == self.data.created_by_id

    def can_cancel_as_admin(self):
        if self.data.status in [RequestStatusEnum.CANCELLED.value, RequestStatusEnum.REJECTED.value]:
            return False
        if self.data.org_id == self.org_id and self.is_admin:
            user = get_runtime_user_with_token_data(user_id=self.user_id, org_id=self.data.org_id, is_admin=True)
            return OrgPermissionHelper.has_permission(
                user=user, permission=Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS.value
            )
        return False

    def get_user_actions(self):
        actions = []
        if self.org_id == self.data.org_id:
            actions = super().get_user_actions()
        if self.can_cancel_as_admin():
            actions.append(self.action_choices.CANCEL_AS_ADMIN.value)
        return actions


class PaymentRequestActionService(AllowedActions):
    payment_request: Optional[PaymentRequest] = None
    is_admin: bool = False
    action_choices = PaymentRequestActionEnum
    request_action_service = PaymentRequestAndRequestActionService

    def __init__(
        self,
        user_id: int,
        payment_request: PaymentRequest,
        request_action_service: PaymentRequestAndRequestActionService,
        is_admin: bool = None,
    ) -> None:
        self.user_id = user_id
        self.payment_request = payment_request
        self.is_admin = is_admin
        self.request_action_service = request_action_service

    def can_edit(self):
        if self.payment_request.status in [
            PaymentRequestStatus.APPROVED.value,
            PaymentRequestStatus.REJECTED.value,
            PaymentRequestStatus.CANCELLED.value,
        ]:
            return False
        return self.payment_request.created_by_id == self.user_id

    def can_cancel(self):
        if not self.is_request_created():
            return False
        if self.payment_request.status in [PaymentRequestStatus.SUBMITTED.value, PaymentRequestStatus.CANCELLED.value]:
            return False
        else:
            return self.request_action_service.can_cancel()

    def can_resource_cancel(self):
        if not self.is_request_created():
            if self.payment_request.status == PaymentRequestStatus.CANCELLED.value:
                return False
            return self.payment_request.created_by_id == self.user_id
        return False

    def is_request_created(self):
        return True if self.payment_request.request_data else False

    def get_user_actions(self, can_create_vendor_payment_request: bool = False):
        logger.info("Getting user actions", user_id=self.user_id, payment_request_id=self.payment_request.pk)
        action_list = []
        if not can_create_vendor_payment_request:
            return []
        if self.can_edit():
            action_list.append(self.action_choices.EDIT.value)
        if self.can_cancel():
            action_list.append(self.action_choices.CANCEL.value)
        if self.can_resource_cancel():
            action_list.append(self.action_choices.RESOURCE_CANCEL.value)
        if self.payment_request.status == PaymentRequestStatus.SUBMITTED.value and self.is_request_created():
            return action_list
        if self.request_action_service:
            action_list.extend(self.request_action_service.get_user_actions())
        return action_list


def payment_request_detail(payment_request_id: int):
    payment_request = payment_request_get(payment_request_id=payment_request_id)
    if payment_request.request_type == PaymentRequestType.PO_ADVANCE.value:
        data = purchase_order_fetch(purchase_order_id=payment_request.purchase_order_id)
        setattr(payment_request, "purchase_order", data)
    elif payment_request.request_type == PaymentRequestType.INVOICE.value:
        data = invoice_fetch(invoice_id=payment_request.invoice_id)
        setattr(payment_request, "purchase_order_dict", data.purchase_order_obj)
        delattr(data, "purchase_order_obj")
        setattr(payment_request, "invoice", data)
    elif payment_request.request_type == PaymentRequestType.NON_ORDER.value:
        setattr(payment_request, "purchase_order", None)
        setattr(payment_request, "purchase_order_dict", None)
        setattr(payment_request, "invoice", None)

    return payment_request


def payment_request_secondary_comment_update(*, payment_request_id: int, request_id: int, comment_id: int):
    PaymentRequestAndRequestMapping.objects.filter(resource_id=payment_request_id, request_id=request_id).update(
        secondary_comment_id=comment_id
    )


class PaymentRequestPaidTillDateService:
    def __init__(self, payment_request_id: int) -> None:
        self.payment_request_id = payment_request_id
        self.payment_request = payment_request_get(payment_request_id=self.payment_request_id)

    def _fetch_po_paid_till_date(self, order_id: int) -> List[Dict[str, decimal.Decimal]]:
        data = []

        purchase_orders = purchase_order_max_version(vendor_order_id=order_id).prefetch_related(
            "payment_requests__payment_entries"
        )
        for po in purchase_orders:
            total_payment_entry_amount = 0
            payment_requests: List[PaymentRequest] = po.payment_requests.all()
            for payment_request in payment_requests:
                payment_entries: List[PaymentEntry] = payment_request.payment_entries.all()
                for payment_entry in payment_entries:
                    # if (
                    #     self.payment_request.status == PaymentRequestStatus.APPROVED.value
                    #     and payment_entry.created_at < self.payment_request.approved_at
                    #     and payment_entry.cancelled_at is None
                    # ):
                    #     total_payment_entry_amount += payment_entry.amount
                    if (
                        (
                            self.payment_request.status
                            in [PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]
                        )
                        and payment_entry.created_at < self.payment_request.finished_at
                        and payment_entry.cancelled_at is None
                    ):
                        total_payment_entry_amount += payment_entry.amount
                    elif (
                        payment_entry.cancelled_at is None
                        and self.payment_request.status in PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE
                    ):
                        total_payment_entry_amount += payment_entry.amount
            data.append({"po_number": f"{po.po_number}", "amount": total_payment_entry_amount})
        return data

    def _fetch_invoice_when_order_exists_paid_till_date(self, order_id: int) -> List[Dict[str, decimal.Decimal]]:
        data = []
        invoices = Invoice.objects.filter(order_id=order_id).prefetch_related("payment_requests__payment_entries")
        for invoice in invoices:
            total_payment_entry_amount = 0
            payment_requests: List[PaymentRequest] = invoice.payment_requests.all()
            for payment_request in payment_requests:
                payment_entries = payment_request.payment_entries.all()
                for payment_entry in payment_entries:
                    # if (
                    #     self.payment_request.status == PaymentRequestStatus.APPROVED.value
                    #     and payment_entry.created_at < self.payment_request.approved_at
                    #     and payment_entry.cancelled_at is None
                    # ):
                    #     total_payment_entry_amount += payment_entry.amount
                    if (
                        (
                            self.payment_request.status
                            in [PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]
                        )
                        and payment_entry.created_at < self.payment_request.finished_at
                        and payment_entry.cancelled_at is None
                    ):
                        total_payment_entry_amount += payment_entry.amount
                    elif (
                        payment_entry.cancelled_at is None
                        and self.payment_request.status in PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE
                    ):
                        total_payment_entry_amount += payment_entry.amount
            data.append({"invoice_number": f"{invoice.invoice_number}", "amount": total_payment_entry_amount})
        return data

    def _fetch_when_order_request_type(self) -> List[Dict[str, decimal.Decimal]]:
        """
        When payment request type is PO_ADVANCE or INVOICE and order exists
        Then fetch po and invoices latched to the order and their payment entries amount sum
        """
        order_id = None
        data = []
        if self.payment_request.purchase_order_id:
            order_id = self.payment_request.purchase_order.vendor_order_id
        elif self.payment_request.invoice_id and self.payment_request.invoice.order_id:
            order_id = self.payment_request.invoice.order_id
        order = (
            VendorOrder.objects.filter(id=order_id)
            .prefetch_related(
                "purchase_orders__payment_requests__payment_entries",
                "invoices__payment_requests__payment_entries",
            )
            .first()
        )
        purchase_orders: List[VendorPurchaseOrder] = order.purchase_orders.all()
        invoices: List[Invoice] = order.invoices.all()
        if purchase_orders:
            # TODO: Need to Improve the logic to reduce time complexity
            for po in purchase_orders:
                total_payment_entry_amount = 0
                payment_requests: List[PaymentRequest] = po.payment_requests.all()
                for payment_request in payment_requests:
                    payment_entries: List[PaymentEntry] = payment_request.payment_entries.all()
                    for payment_entry in payment_entries:
                        if (
                            (
                                self.payment_request.status
                                in [PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]
                            )
                            and payment_entry.created_at < self.payment_request.finished_at
                            and payment_entry.cancelled_at is None
                        ):
                            total_payment_entry_amount += payment_entry.amount
                        elif (
                            payment_entry.cancelled_at is None
                            and self.payment_request.status in PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE
                        ):
                            total_payment_entry_amount += payment_entry.amount
                data.append({"po_number": f"{po.po_number}", "amount": total_payment_entry_amount})
        if invoices:
            # TODO: Need to Improve the logic to reduce time complexity
            for invoice in invoices:
                total_payment_entry_amount = 0
                payment_requests: List[PaymentRequest] = invoice.payment_requests.all()
                for payment_request in payment_requests:
                    payment_entries = payment_request.payment_entries.all()
                    for payment_entry in payment_entries:
                        if (
                            (
                                self.payment_request.status
                                in [PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]
                            )
                            and payment_entry.created_at < self.payment_request.finished_at
                            and payment_entry.cancelled_at is None
                        ):
                            total_payment_entry_amount += payment_entry.amount
                        elif (
                            payment_entry.cancelled_at is None
                            and self.payment_request.status in PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE
                        ):
                            total_payment_entry_amount += payment_entry.amount
                data.append({"invoice_number": f"{invoice.invoice_number}", "amount": total_payment_entry_amount})
        return data

    def _fetch_when_invoice_without_order_request_type(self) -> List[Dict[str, decimal.Decimal]]:
        """
        When payment request type is INVOICE and order is not exists
        Then fetch invoice and their payment entries amount sum
        """
        data = []
        invoice: Invoice = (
            Invoice.objects.filter(id=self.payment_request.invoice_id)
            .prefetch_related("payment_requests__payment_entries")
            .first()
        )
        total_payment_entry_amount = 0
        payment_requests = invoice.payment_requests.all()
        for payment_request in payment_requests:
            payment_entries: List[PaymentEntry] = payment_request.payment_entries.all()
            for payment_entry in payment_entries:
                if (
                    (
                        self.payment_request.status
                        in [PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]
                    )
                    and payment_entry.created_at < self.payment_request.finished_at
                    and payment_entry.cancelled_at is None
                ):
                    total_payment_entry_amount += payment_entry.amount
                elif (
                    payment_entry.cancelled_at is None
                    and self.payment_request.status in PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE
                ):
                    total_payment_entry_amount += payment_entry.amount
        data.append({"invoice_number": f"{invoice.invoice_number}", "amount": total_payment_entry_amount})
        return data

    def _fetch_when_non_order_request_type(self) -> List[Dict[str, decimal.Decimal]]:
        """
        When payment request type is NON_ORDER
        No summary data to fetch
        """
        return []

    def fetch(self) -> Dict:
        if (
            self.payment_request.request_type == PaymentRequestType.INVOICE.value
            and self.payment_request.invoice.order_id is None
        ):
            return {
                "summary_text": "It is the total of all the payment entries against this invoice until this request is approved or rejected",  # noqa
                "invoice_paid_till_date": self._fetch_when_invoice_without_order_request_type(),
                "po_paid_till_date": [],
            }
        elif self.payment_request.request_type in [
            PaymentRequestType.PO_ADVANCE.value,
            PaymentRequestType.INVOICE.value,
        ]:
            if self.payment_request.purchase_order_id:
                order_id = self.payment_request.purchase_order.vendor_order_id
            elif self.payment_request.invoice_id and self.payment_request.invoice.order_id:
                order_id = self.payment_request.invoice.order_id
            return {
                "summary_text": "It is the total of all the payment entries against POs and invoices linked to this order until this request is approved or rejected",  # noqa
                "invoice_paid_till_date": self._fetch_invoice_when_order_exists_paid_till_date(order_id=order_id),
                "po_paid_till_date": self._fetch_po_paid_till_date(order_id=order_id),
            }
        elif self.payment_request.request_type == PaymentRequestType.NON_ORDER.value:
            return {
                "summary_text": "It is the total of all the payment entries against Non order request to this vendor until this request is approved or rejected",  # noqa
                "invoice_paid_till_date": [],
                "po_paid_till_date": [],
            }
