from rest_framework import serializers

from common.serializers import BaseSerializer


class PaymentRequestAmountValidator(BaseSerializer):
    request_base_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    request_gst_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    requestable_base_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    requestable_gst_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    exclude_payment_request_base_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    exclude_payment_request_gst_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    payment_entries_amount = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    max_payment_entry_amount_allowed = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)

    class Meta:
        input_hash_id_fields = ()
        output_hash_id_fields = ()

    def validate(self, data):
        errors = {}

        request_base_amount = data.get("request_base_amount", 0)
        request_gst_amount = data.get("request_gst_amount", 0)
        requestable_base_amount = data.get("requestable_base_amount", 0)
        requestable_gst_amount = data.get("requestable_gst_amount", 0)
        exclude_payment_request_base_amount = data.get("exclude_payment_request_base_amount", 0)
        exclude_payment_request_gst_amount = data.get("exclude_payment_request_gst_amount", 0)
        payment_entries_amount = data.get("payment_entries_amount", 0)
        max_payment_entry_amount_allowed = data.get("max_payment_entry_amount_allowed", 0)

        # Check if request_base_amount is less than requestable_base_amount
        if request_base_amount - exclude_payment_request_base_amount > requestable_base_amount:
            errors["base_amount"] = "Request base amount must be less than or equal to requestable base amount"

        # Check if request_gst_amount is less than requestable_gst_amount
        if request_gst_amount - exclude_payment_request_gst_amount > requestable_gst_amount:
            errors["gst_amount"] = "Request GST amount must be less than or equal to requestable GST amount"

        if max_payment_entry_amount_allowed < payment_entries_amount:
            errors["paid_amount"] = (
                "Payment done can not be greater than "
                + str(round(max_payment_entry_amount_allowed, 0))
                + " for this request"
            )
        elif request_base_amount + request_gst_amount < payment_entries_amount:
            errors["paid_amount"] = "Payment entries amount must be less than or equal to request amount"

        # Raise ValidationError if there are any errors
        if errors:
            raise serializers.ValidationError(errors)

        return data
