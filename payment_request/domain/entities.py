import datetime
import decimal
from dataclasses import dataclass
from typing import Optional

from common.context_values import BaseContextValues, ProjectContextValueMixin
from common.entities import ObjectStatus
from payment_entry.domain.entities import BasePaymentEntryCreateData
from payment_request.data.choices import PaymentMode, PaymentRequestType


@dataclass
class PaymentRequestEntryCreateData(BasePaymentEntryCreateData):
    is_cancelled: bool = False
    object_status: Optional[ObjectStatus] = None
    payment_request_id: Optional[int] = None
    id: Optional[int] = None


@dataclass
class PaymentRequestAttachmentData:
    id: Optional[int]
    file: str
    name: str


@dataclass
class PaymentEntryCreateData(PaymentRequestEntryCreateData):
    pass


@dataclass
class PaymentRequestUpdateData:
    purchase_order_id: Optional[int]
    invoice_id: Optional[int]
    request_amount: decimal.Decimal
    gst_amount: decimal.Decimal
    base_amount_percent: decimal.Decimal
    gst_amount_percent: decimal.Decimal
    base_amount_percent_toggle: bool
    gst_amount_percent_toggle: bool
    remark: Optional[str]
    attachment: list[PaymentRequestAttachmentData]
    payment_request_entries: list[PaymentEntryCreateData]


@dataclass
class PaymentRequestCreateData(PaymentRequestUpdateData):
    project_id: int
    vendor_id: int
    client_id: int
    request_type: PaymentRequestType


@dataclass(frozen=True)
class PaymentRequestAmountValueProviderData(BaseContextValues, ProjectContextValueMixin):
    ...


@dataclass
class PaymentRequestEntryCreateDataV2:
    transaction_id: Optional[str]
    payment_mode: PaymentMode
    remark: Optional[str]
    transaction_proof_file_url: Optional[str]
    transaction_proof_file_name: Optional[str]
    amount: decimal.Decimal
    payment_date: datetime.date
