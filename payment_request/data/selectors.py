from typing import Optional

from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ExpressionWrapper,
    F,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import <PERSON><PERSON>ce, Concat, JSONObject

from core.models import User
from order.data.models import VendorOrder, VendorPurchaseOrder
from order.domain.constants import OrderStatusEnum
from order.domain.status_choices import POStatus
from order.invoice.data.choices import InvoiceStatus
from order.invoice.data.models import Invoice, InvoiceCreditNote
from payment_request.data.choices import PaymentRequestStatus, PaymentRequestType
from payment_request.data.models import (
    PaymentEntry,
    PaymentRequest,
    PaymentRequestAndRequestMapping,
    PaymentRequestAttachment,
)
from payment_request.domain.exceptions import PaymentEntryNotFound, PaymentRequestNotFound


def payment_request_get(payment_request_id: int) -> PaymentRequest:
    payment_request = (
        payment_request_selector()
        .annotate_request_approved_at()
        .annotate_request_finished_at()
        .filter(id=payment_request_id)
        .annotate_po_amount_for_pr_approved()
        .annotate_po_amount_for_pr_unapproved()
        .annotate_invoice_with_order_amount_for_pr_approved()
        .annotate_invoice_with_order_amount_for_pr_unapproved()
        .annotate_invoice_without_order_amount_for_pr_approved()
        .annotate_invoice_without_order_amount_for_pr_unapproved()
        .annotate_non_order_amount_incase_of_pr_approved()
        .annotate_non_order_amount_incase_of_pr_unapproved()
        .annotate_paid_till_date()
        .annotate_invoice_total_paid_amount()
        .prefetch_related("payment_entries")
        .prefetch_related(Prefetch("attachments", queryset=PaymentRequestAttachment.objects.available()))
        .first()
    )
    if not payment_request:
        raise PaymentRequestNotFound("Payment request not found")
    return payment_request


def valid_purchase_order_queryset():
    return VendorPurchaseOrder.objects.filter(
        ~Q(status=POStatus.PO_CANCELLED),
        vendor_order__issued_at__isnull=False,
        vendor_order__cancelled_at__isnull=True,
        deleted_at__isnull=True,
    ).exclude(
        Q(
            vendor_order__outgoing_status__in=[
                VendorOrder.OutgoingStatus.NOT_SENT.value,
                VendorOrder.OutgoingStatus.CANCELLED.value,
            ],
        )
        | Q(vendor_order__snapshots__isnull=True, vendor_order__outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value)
    )


def valid_invoice_queryset():
    return Invoice.objects.filter(
        status__in=[InvoiceStatus.ATTACHED.value, InvoiceStatus.APPROVED.value],
        cancelled_at=None,
        deleted_at__isnull=True,
    ).annotate_invoice_total_amount()


def purchase_orders_for_client_vendor_fetch_all(client_id: int, vendor_id: int):
    return valid_purchase_order_queryset().filter(
        vendor_order__org_from_id=client_id,
        vendor_order__org_to_id=vendor_id,
    )


def purchase_order_get(purchase_order_id: int, client_id: int, vendor_id: int):
    purchase_order = (
        purchase_orders_for_client_vendor_fetch_all(client_id=client_id, vendor_id=vendor_id)
        .filter(
            id=purchase_order_id,
        )
        .annotate(
            already_requested_amount=Coalesce(
                purchase_order_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                purchase_order_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .first()
    )
    return purchase_order


def get_purchase_order_with_amount_details(purchase_order_id: int):
    purchase_order = (
        valid_purchase_order_queryset()
        .filter(id=purchase_order_id)
        .annotate(
            requested_amount_from_payment_request_against_po=Coalesce(
                requested_amount_from_payment_request_against_po_subquery("id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_gst_amount_from_payment_request_against_po=Coalesce(
                requested_gst_amount_from_payment_request_against_po_subquery("id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_amount_from_payment_request_against_latched_invoices=Coalesce(
                requested_amount_from_payment_request_against_invoice_with_po_subquery("id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_gst_amount_from_payment_request_against_latched_invoices=Coalesce(
                requested_gst_amount_from_payment_request_against_invoice_with_po_subquery("id"),
                Value(0),
                output_field=DecimalField(),
            ),
            already_paid_amount_from_requests_on_po=Coalesce(
                purchase_order_already_paid_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_paid_amount_from_requests_on_invoices=Coalesce(
                invoices_already_paid_amount(), Value(0), output_field=DecimalField()
            ),
            already_paid_amount=ExpressionWrapper(
                F("already_paid_amount_from_requests_on_po") + F("already_paid_amount_from_requests_on_invoices"),
                output_field=DecimalField(),
            ),
            total_invoices_base_amount=Coalesce(
                Sum(
                    "invoices__amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=False,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            total_invoices_gst_amount=Coalesce(
                Sum(
                    "invoices__gst_amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=False,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            already_requested_amount_on_po_and_invoices=ExpressionWrapper(
                F("requested_amount_from_payment_request_against_po")
                + F("requested_amount_from_payment_request_against_latched_invoices"),
                output_field=DecimalField(),
            ),
            already_requested_gst_amount_on_po_and_invoices=ExpressionWrapper(
                F("requested_gst_amount_from_payment_request_against_po")
                + F("requested_gst_amount_from_payment_request_against_latched_invoices"),
                output_field=DecimalField(),
            ),
            already_requested_amount=Coalesce(
                F("requested_amount_from_payment_request_against_po"), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                F("requested_gst_amount_from_payment_request_against_po"), Value(0), output_field=DecimalField()
            ),
            credit_notes_amount=Coalesce(
                Sum(
                    "invoices__credit_notes__amount",
                    filter=Q(
                        invoices__credit_notes__status=InvoiceCreditNote.Status.ATTACHED,
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            credit_notes_gst_amount=Coalesce(
                Sum(
                    "invoices__credit_notes__gst_amount",
                    filter=Q(
                        invoices__credit_notes__status=InvoiceCreditNote.Status.ATTACHED,
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            invoices_total_base_amount=ExpressionWrapper(
                F("total_invoices_base_amount") - F("credit_notes_amount"),
                output_field=DecimalField(),
            ),
            invoices_total_gst_amount=ExpressionWrapper(
                F("total_invoices_gst_amount") - F("credit_notes_gst_amount"),
                output_field=DecimalField(),
            ),
        )
        .select_related("vendor_order")
        .first()
    )

    return purchase_order


def get_invoice_with_amount_details(invoice_id: int):
    invoice = (
        valid_invoice_queryset()
        .filter(id=invoice_id)
        .annotate(
            po_base_amount=Coalesce(F("vendor_po__amount"), Value(0), output_field=DecimalField()),
            po_gst_amount=Coalesce(F("vendor_po__tax_amount"), Value(0), output_field=DecimalField()),
        )
        .annotate(
            requested_amount_from_payment_request_against_latched_po=Coalesce(
                requested_amount_from_payment_request_against_po_subquery("vendor_po_id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_gst_amount_from_payment_request_against_latched_po=Coalesce(
                requested_gst_amount_from_payment_request_against_po_subquery("vendor_po_id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_amount_from_payment_request_against_latched_invoices=Coalesce(
                requested_amount_from_payment_request_against_invoice_with_po_subquery("vendor_po_id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_gst_amount_from_payment_request_against_latched_invoices=Coalesce(
                requested_gst_amount_from_payment_request_against_invoice_with_po_subquery("vendor_po_id"),
                Value(0),
                output_field=DecimalField(),
            ),
            requested_amount_from_payment_request_against_invoice=Coalesce(
                invoice_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            requested_gst_amount_from_payment_request_against_invoice=Coalesce(
                invoice_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            total_invoices_base_amount=Coalesce(
                Case(
                    When(vendor_po_id__isnull=False, then=get_latched_invoices_total_base_amount()),
                    default=F("amount"),
                    output_field=DecimalField(),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            total_invoices_gst_amount=Coalesce(
                Case(
                    When(vendor_po_id__isnull=False, then=get_latched_invoices_total_gst_amount()),
                    default=F("gst_amount"),
                    output_field=DecimalField(),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            already_paid_amount=Coalesce(invoice_already_paid_amount_subquery(), Value(0), output_field=DecimalField()),
            already_requested_amount_on_po_and_invoices=ExpressionWrapper(
                F("requested_amount_from_payment_request_against_latched_po")
                + F("requested_amount_from_payment_request_against_latched_invoices"),
                output_field=DecimalField(),
            ),
            already_requested_gst_amount_on_po_and_invoices=ExpressionWrapper(
                F("requested_gst_amount_from_payment_request_against_latched_po")
                + F("requested_gst_amount_from_payment_request_against_latched_invoices"),
                output_field=DecimalField(),
            ),
            already_requested_amount=Coalesce(
                F("requested_amount_from_payment_request_against_invoice"), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                F("requested_gst_amount_from_payment_request_against_invoice"), Value(0), output_field=DecimalField()
            ),
            credit_notes_count=Coalesce(
                Count("credit_notes", filter=Q(credit_notes__status=InvoiceCreditNote.Status.ATTACHED)),
                Value(0),
                output_field=DecimalField(),
            ),
            credit_notes_amount=Coalesce(
                Sum(
                    "credit_notes__amount",
                    filter=Q(credit_notes__status=InvoiceCreditNote.Status.ATTACHED),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            credit_notes_gst_amount=Coalesce(
                Sum(
                    "credit_notes__gst_amount",
                    filter=Q(credit_notes__status=InvoiceCreditNote.Status.ATTACHED),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
            total_credit_notes_amount_from_invoices=Coalesce(
                get_latched_invoices_total_credit_note_base_amount(),
                Value(0),
                output_field=DecimalField(),
            ),
            total_credit_notes_gst_amount_from_invoices=Coalesce(
                get_latched_invoices_total_credit_note_gst_amount(),
                Value(0),
                output_field=DecimalField(),
            ),
            invoices_total_base_amount=ExpressionWrapper(
                F("total_invoices_base_amount") - F("total_credit_notes_amount_from_invoices"),
                output_field=DecimalField(),
            ),
            invoices_total_gst_amount=ExpressionWrapper(
                F("total_invoices_gst_amount") - F("total_credit_notes_gst_amount_from_invoices"),
                output_field=DecimalField(),
            ),
        )
        .first()
    )

    return invoice


def invoices_for_client_vendor_fetch_all(client_id: int, vendor_id: int):
    return valid_invoice_queryset().filter(
        client_id=client_id,
        vendor_id=vendor_id,
    )


def invoice_get(invoice_id: int, client_id: int, vendor_id: int):
    invoice = (
        invoices_for_client_vendor_fetch_all(client_id=client_id, vendor_id=vendor_id)
        .filter(id=invoice_id)
        .annotate(
            already_requested_amount=Coalesce(
                invoice_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                invoice_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .first()
    )
    return invoice


def purchase_order_already_requested_amount_subquery():
    subquery_for_already_requested_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_requested_amount


def purchase_order_already_requested_gst_amount_subquery():
    subquery_for_already_requested_gst_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_gst_amount")[:1]
    )
    return subquery_for_already_requested_gst_amount


def requested_amount_from_payment_request_against_invoice_with_po_subquery(field_name: str):
    subquery_for_already_requested_amount_from_invoice = Subquery(
        Invoice.objects.filter(
            vendor_po_id=OuterRef(field_name), status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED]
        )
        .available()
        .values("vendor_po_id")
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_requested_amount_from_invoice


def requested_gst_amount_from_payment_request_against_invoice_with_po_subquery(field_name: str):
    subquery_for_already_requested_gst_amount_from_invoice = Subquery(
        Invoice.objects.filter(
            vendor_po_id=OuterRef(field_name), status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED]
        )
        .available()
        .values("vendor_po_id")
        .annotate(
            payment_request_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_gst_amount")[:1]
    )
    return subquery_for_already_requested_gst_amount_from_invoice


def requested_amount_from_payment_request_against_po_subquery(field_name: str):
    subquery_for_already_requested_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef(field_name),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__request_type=PaymentRequestType.PO_ADVANCE.value,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_requested_amount


def requested_gst_amount_from_payment_request_against_po_subquery(field_name: str):
    subquery_for_already_requested_gst_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef(field_name),
        )
        .annotate(
            payment_request_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__request_type=PaymentRequestType.PO_ADVANCE.value,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_gst_amount")[:1]
    )
    return subquery_for_already_requested_gst_amount


def purchase_order_already_paid_amount_subquery():
    subquery_for_already_paid_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__payment_entries__amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__request_type=PaymentRequestType.PO_ADVANCE.value,
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_paid_amount


def purchase_order_fetch(purchase_order_id: int):
    # TODO : needs refactoring
    return (
        VendorPurchaseOrder.objects.filter(pk=purchase_order_id)
        .annotate(
            already_requested_amount=Coalesce(
                purchase_order_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                purchase_order_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .annotate(
            order_number=Concat(
                F("vendor_order__project__job_id"),
                Value("/"),
                F("vendor_order__order_number"),
                output_field=CharField(),
            )
        )
        .annotate(
            already_paid_amount=Coalesce(
                purchase_order_already_paid_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .first()
    )


def vendor_po_dropdown_list(client_id: int, vendor_id: int, project_id: int):
    return (
        purchase_orders_for_client_vendor_fetch_all(client_id=client_id, vendor_id=vendor_id)
        .annotate(
            already_requested_amount=Coalesce(
                purchase_order_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                purchase_order_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .annotate(
            order_number=Concat(
                F("vendor_order__project__job_id"),
                Value("/"),
                F("vendor_order__order_number"),
                output_field=CharField(),
            )
        )
        .annotate(
            already_paid_amount=Coalesce(
                purchase_order_already_paid_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .filter(vendor_order__project_id=project_id)
    )


def invoice_purchase_order_already_requested_amount_subquery():
    subquery_for_already_requested_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef("vendor_po_id"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_requested_amount


def invoice_purchase_order_already_paid_amount_subquery():
    subquery_for_already_paid_amount = Subquery(
        VendorPurchaseOrder.objects.filter(
            id=OuterRef("vendor_po_id"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__payment_entries__amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_paid_amount


def invoice_already_requested_amount_subquery():
    subquery_for_already_requested_amount = Subquery(
        Invoice.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_requested_amount


def invoice_already_requested_gst_amount_subquery():
    subquery_for_already_requested_gst_amount = Subquery(
        Invoice.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    Q(payment_requests__cancelled_at__isnull=True),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_gst_amount")[:1]
    )
    return subquery_for_already_requested_gst_amount


def get_latched_invoices_total_credit_note_base_amount():
    subquery_for_total_invoice_amount = Subquery(
        VendorPurchaseOrder.objects.filter(id=OuterRef("vendor_po_id"), cancelled_at__isnull=True)
        .annotate(
            invoices_total_base_amount=Coalesce(
                Sum(
                    "invoices__credit_notes__amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        .values("invoices_total_base_amount")[:1]
    )
    return subquery_for_total_invoice_amount


def get_latched_invoices_total_credit_note_gst_amount():
    subquery_for_total_invoice_amount = Subquery(
        VendorPurchaseOrder.objects.filter(id=OuterRef("vendor_po_id"), cancelled_at__isnull=True)
        .annotate(
            invoices_total_gst_amount=Coalesce(
                Sum(
                    "invoices__credit_notes__gst_amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        .values("invoices_total_gst_amount")[:1]
    )
    return subquery_for_total_invoice_amount


def get_latched_invoices_total_base_amount():
    subquery_for_total_invoice_amount = Subquery(
        VendorPurchaseOrder.objects.filter(id=OuterRef("vendor_po_id"), cancelled_at__isnull=True)
        .annotate(
            invoices_total_base_amount=Coalesce(
                Sum(
                    "invoices__amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        .values("invoices_total_base_amount")[:1]
    )
    return subquery_for_total_invoice_amount


def get_latched_invoices_total_gst_amount():
    subquery_for_total_invoice_amount = Subquery(
        VendorPurchaseOrder.objects.filter(id=OuterRef("vendor_po_id"), cancelled_at__isnull=True)
        .annotate(
            invoices_total_gst_amount=Coalesce(
                Sum(
                    "invoices__gst_amount",
                    filter=Q(
                        invoices__status__in=[InvoiceStatus.APPROVED, InvoiceStatus.ATTACHED],
                        invoices__deleted_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        .values("invoices_total_gst_amount")[:1]
    )
    return subquery_for_total_invoice_amount


def invoices_already_paid_amount():
    subquery_for_already_paid_amount = Subquery(
        Invoice.objects.filter(
            vendor_po_id=OuterRef("pk"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__payment_entries__amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_paid_amount


def invoice_already_paid_amount_subquery():
    subquery_for_already_paid_amount = Subquery(
        Invoice.objects.filter(
            id=OuterRef("pk"),
        )
        .annotate(
            payment_request_amount=Sum(
                "payment_requests__payment_entries__amount",
                filter=Q(
                    Q(
                        payment_requests__cancelled_at__isnull=True,
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                    ~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    ),
                ),
            )
        )
        .values("payment_request_amount")[:1]
    )
    return subquery_for_already_paid_amount


def invoice_fetch(invoice_id: int):
    # TODO : needs refactoring
    return (
        Invoice.objects.filter(pk=invoice_id)
        .select_related("uploaded_by__org__invoice_config")
        .annotate_invoice_total_amount()
        .annotate(
            already_requested_amount=Coalesce(
                invoice_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                invoice_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .annotate(
            already_paid_amount=Coalesce(invoice_already_paid_amount_subquery(), Value(0), output_field=DecimalField())
        )
        .annotate(
            purchase_order_obj=Subquery(
                VendorPurchaseOrder.objects.filter(
                    po_number=OuterRef("vendor_po__po_number"),
                    vendor_order_id=OuterRef("vendor_po__vendor_order_id"),
                )
                .order_by("-version")
                .annotate(
                    payment_request_amount=Sum(
                        "payment_requests__payment_entries__amount",
                        filter=Q(
                            Q(
                                payment_requests__cancelled_at__isnull=True,
                                payment_requests__payment_entries__cancelled_at__isnull=True,
                            ),
                            ~Q(
                                payment_requests__status__in=[
                                    PaymentRequestStatus.CANCELLED.value,
                                    PaymentRequestStatus.REJECTED.value,
                                ]
                            ),
                        ),
                    )
                )
                .annotate(
                    payment_request_amount=Sum(
                        "payment_requests__request_amount",
                        filter=Q(
                            Q(payment_requests__cancelled_at__isnull=True),
                            ~Q(
                                payment_requests__status__in=[
                                    PaymentRequestStatus.CANCELLED.value,
                                    PaymentRequestStatus.REJECTED.value,
                                ]
                            ),
                        ),
                    )
                )
                .annotate(
                    order_number=Concat(
                        F("vendor_order__project__job_id"),
                        Value("/"),
                        F("vendor_order__order_number"),
                        output_field=CharField(),
                    )
                )
                .annotate(
                    po_object=JSONObject(
                        pk="pk",
                        po_number="po_number",
                        payment_request_amount="payment_request_amount",
                        payment_request_paid_amount="payment_request_amount",
                        amount="amount",
                        tax_amount="tax_amount",
                        order_number="order_number",
                        file="file",
                        name="name",
                    ),
                )
                .values("po_object")[:1]
            )
        )
        .first()
    )


def invoice_dropdown_list(client_id: int, vendor_id: int, project_id: int):
    # todo add status filter
    # todo client vendor filter to be added once invoice feature is updated
    invoices = invoices_for_client_vendor_fetch_all(client_id=client_id, vendor_id=vendor_id).select_related("order")
    invoices = (
        invoices.annotate(
            already_requested_amount=Coalesce(
                invoice_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
            ),
            already_requested_gst_amount=Coalesce(
                invoice_already_requested_gst_amount_subquery(), Value(0), output_field=DecimalField()
            ),
        )
        .annotate(
            already_paid_amount=Coalesce(invoice_already_paid_amount_subquery(), Value(0), output_field=DecimalField()),
        )
        .annotate(
            purchase_order_obj=Subquery(
                VendorPurchaseOrder.objects.filter(
                    id=OuterRef("vendor_po_id"),
                )
                .annotate(
                    already_paid_amount=Coalesce(
                        purchase_order_already_paid_amount_subquery(), Value(0), output_field=DecimalField()
                    ),
                )
                .annotate(
                    payment_request_amount=Coalesce(
                        purchase_order_already_requested_amount_subquery(), Value(0), output_field=DecimalField()
                    )
                )
                .annotate(
                    order_number=Concat(
                        F("vendor_order__project__job_id"),
                        Value("/"),
                        F("vendor_order__order_number"),
                        output_field=CharField(),
                    )
                )
                .annotate(
                    po_object=JSONObject(
                        pk="pk",
                        po_number="po_number",
                        already_requested_amount="payment_request_amount",
                        already_paid_amount="already_paid_amount",
                        amount="amount",
                        order_number="order_number",
                        name="name",
                        file="file",
                        tax_amount="tax_amount",
                    ),
                )
                .values("po_object")[:1]
            )
        )
    )

    return invoices.filter(project_id=project_id)


def payment_request_selector() -> QuerySet:
    payment_request = PaymentRequest.objects.select_related(
        "project", "vendor", "created_by", "client", "created_by__org", "purchase_order", "invoice"
    ).annotate_payment_request_number()
    subquery_for_paid_amount = Subquery(
        PaymentRequest.objects.filter(id=OuterRef("pk"))
        .annotate(
            paid_amount=Coalesce(
                Sum(
                    "payment_entries__amount",
                    filter=Q(
                        payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                0,
                output_field=DecimalField(),
            )
        )
        .values("paid_amount")[:1]
    )
    payment_request = (
        payment_request.select_related("project", "vendor", "client")
        .annotate(paid_amount=Coalesce(subquery_for_paid_amount, 0, output_field=DecimalField()))
        .annotate(
            reference_number=Case(
                When(request_type=PaymentRequestType.PO_ADVANCE, then=F("purchase_order__po_number")),
                When(request_type=PaymentRequestType.INVOICE, then=F("invoice__invoice_number")),
            ),
            o_number=Case(
                When(request_type=PaymentRequestType.PO_ADVANCE, then=F("purchase_order__vendor_order__order_number")),
                When(request_type=PaymentRequestType.INVOICE, then=F("invoice__order__order_number")),
                default=None,
            ),
            order_number=Case(
                When(
                    o_number__isnull=False,
                    then=Concat(F("project__job_id"), Value("/"), F("o_number"), output_field=CharField()),
                ),
                default=None,
            ),
            order_type=Case(
                When(request_type=PaymentRequestType.PO_ADVANCE, then=F("purchase_order__vendor_order__order_type")),
                When(request_type=PaymentRequestType.INVOICE, then=F("invoice__order__order_type")),
            ),
            order_id=Case(
                When(request_type=PaymentRequestType.PO_ADVANCE, then=F("purchase_order__vendor_order_id")),
                When(request_type=PaymentRequestType.INVOICE, then=F("invoice__order_id")),
            ),
            order_amount=Case(
                When(o_number__isnull=False, then=F("purchase_order__vendor_order__saved_total_amount")),
                default=None,
            ),
        )
        .annotate_request_data()
        .annotate(
            payment_due=F("request_amount") - F("paid_amount"),
        )
    )

    return payment_request


def payment_request_with_paid_amount_get(payment_request_id: int):
    return (
        payment_request_selector()
        .annotate_invoice_total_paid_amount()
        .annotate_payment_due_amount()
        .filter(id=payment_request_id)
        .prefetch_related(Prefetch("attachments", queryset=PaymentRequestAttachment.objects.available()))
        .first()
    )


def vendor_payment_request_list(client_id: int):
    return (
        payment_request_selector()
        .select_related("project__config", "invoice")
        .annotate_invoice_total_paid_amount()
        .annotate_payment_due_amount()
        .filter(Q(client_id=client_id))
        .annotate(
            payment_due=Case(
                When(
                    status__in=[PaymentRequestStatus.REJECTED.value, PaymentRequestStatus.CANCELLED.value],
                    then=Value(0.0),
                ),
                default=F("payment_due_amount"),
                output_field=DecimalField(),
            ),
        )
        .order_by("-created_at")
    )


def client_payment_request_list(vendor_id: int) -> QuerySet:
    # to exclude the payment request created by the client if not approved
    return (
        payment_request_selector()
        .annotate_invoice_total_paid_amount()
        .annotate_payment_due_amount()
        .select_related("project__config")
        .filter(Q(vendor_id=vendor_id))
        .exclude(
            Q(
                ~Q(
                    status__in=[
                        PaymentRequestStatus.APPROVED.value,
                        PaymentRequestStatus.SUBMITTED.value,
                    ]
                ),
                creator_id=F("client_id"),
            )
        )
        .order_by("-created_at")
    )


def vendor_payment_entry_list(client_id: int):
    payment_request_ids = vendor_payment_request_list(client_id=client_id).values_list("id", flat=True)
    return payment_entries_fetch().filter(payment_request_id__in=payment_request_ids).order_by("-created_at")


def client_payment_entry_list(vendor_id: int):
    payment_request_ids = client_payment_request_list(vendor_id=vendor_id).values_list("id", flat=True)
    return payment_entries_fetch().filter(payment_request_id__in=payment_request_ids).order_by("-created_at")


def header_aggregate(queryset: QuerySet):
    payment_request_aggregate = queryset.annotate_payment_entry_count().aggregate(
        payment_requested=Coalesce(
            Sum(
                "request_amount",
                filter=Q(
                    ~Q(
                        status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    )
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        payment_requested_count=Coalesce(
            Count(
                "id",
                filter=Q(
                    ~Q(
                        status__in=[
                            PaymentRequestStatus.CANCELLED.value,
                            PaymentRequestStatus.REJECTED.value,
                        ]
                    )
                ),
            ),
            Value(0),
        ),
        payment_approved_count=Count("id", filter=Q(status=PaymentRequestStatus.APPROVED.value)),
        payment_approved=Coalesce(
            Sum("request_amount", filter=Q(status=PaymentRequestStatus.APPROVED.value)),
            Value(0),
            output_field=DecimalField(),
        ),
        pending_approval_count=Coalesce(
            Count(
                "id",
                filter=Q(
                    status__in=[
                        PaymentRequestStatus.PENDING.value,
                        PaymentRequestStatus.HOLD.value,
                    ]
                ),
            ),
            Value(0),
        ),
        pending_approval=Coalesce(
            Sum(
                "request_amount",
                filter=Q(
                    status__in=[
                        PaymentRequestStatus.PENDING.value,
                        PaymentRequestStatus.HOLD.value,
                    ]
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        payment_done=Coalesce(
            Sum(
                "paid_amount",
                filter=Q(
                    ~Q(
                        status__in=[
                            PaymentRequestStatus.REJECTED.value,
                            PaymentRequestStatus.CANCELLED.value,
                        ],
                    )
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        payment_done_count=Coalesce(
            Sum(
                "payment_entry_count",
                filter=Q(
                    ~Q(
                        status__in=[
                            PaymentRequestStatus.REJECTED.value,
                            PaymentRequestStatus.CANCELLED.value,
                        ],
                    )
                ),
            ),
            Value(0),
        ),
        total_payment_due=Coalesce(
            Sum(
                "payment_due",
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        total_payment_due_count=Count("id", filter=Q(payment_due__gt=0)),
    )

    return payment_request_aggregate


def vendor_payment_request_header(
    client_id: int, vendor_id: Optional[int], project_id: Optional[int], currency_id: Optional[int]
):
    if vendor_id and project_id:
        raise ValueError("Either vendor_id or project_id should be provided")
    filter_query = Q()
    if vendor_id:
        filter_query = Q(vendor_id=vendor_id)
    elif project_id:
        filter_query = Q(project_id=project_id)
    if currency_id:
        filter_query &= Q(project__config__currency_id=currency_id)
    queryset = vendor_payment_request_list(client_id).filter(filter_query)

    return header_aggregate(queryset)


def client_payment_request_header(
    client_id: int, vendor_id: Optional[int], project_id: Optional[int], currency_id: Optional[int]
):
    if client_id and project_id:
        raise ValueError("Either vendor_id or project_id should be provided")
    filter_query = Q()
    if client_id:
        filter_query = Q(client_id=client_id)
    elif project_id:
        filter_query = Q(project_id=project_id)
    if currency_id:
        filter_query &= Q(project__config__currency_id=currency_id)
    queryset = client_payment_request_list(vendor_id=vendor_id).filter(filter_query)

    return header_aggregate(queryset)


def payment_entries_fetch():
    return (
        PaymentEntry.objects.select_related(
            "payment_request__project",
            "payment_request__vendor",
            "payment_request",
            "payment_request__invoice",
            "payment_request__purchase_order",
            "created_by",
            "created_by__org",
        )
        .annotate_payment_request_number()
        .all()
    )


def payment_entry_get(id: int):
    payment_entry = payment_entries_fetch().filter(pk=id).first()
    if not payment_entry:
        raise PaymentEntryNotFound("Payment entry not found")
    return payment_entry


def payment_request_comment_id_get(*, payment_request_id: int, request_id: int, org_id: int) -> Optional[int]:
    payment_request_mapping: PaymentRequestAndRequestMapping = (
        PaymentRequestAndRequestMapping.objects.filter(resource_id=payment_request_id, request_id=request_id)
        .select_related("comment__organization")
        .first()
    )
    if org_id == payment_request_mapping.comment.organization_id:
        return payment_request_mapping.comment_id
    if not payment_request_mapping.secondary_comment:
        return None
    return payment_request_mapping.secondary_comment_id


def payment_request_immediate_approver_user_fetch(payment_requests: QuerySet[PaymentRequest]):
    immediate_pending_approver_user_ids = [
        user_id
        for sublist in payment_requests.annotate_immediate_approver_user_ids().values_list(
            "immediate_approver_user_ids", flat=True
        )
        for user_id in sublist
    ]

    return User.objects.filter(id__in=immediate_pending_approver_user_ids)


def payment_request_approver_fetch(payment_requests: QuerySet[PaymentRequest]):
    payment_request_approver_user_ids = [
        user_id
        for sublist in payment_requests.annotate_pending_approver_user_ids().values_list(
            "pending_approver_user_ids", flat=True
        )
        for user_id in sublist
    ]

    return User.objects.filter(id__in=payment_request_approver_user_ids)


def client_payment_request_amount_fetch_for_non_order(*, client_id: int, vendor_id: int, project_id: int):
    requested_amount = (
        client_payment_request_list(vendor_id=vendor_id)
        .filter(client_id=client_id, project_id=project_id, request_type=PaymentRequestType.NON_ORDER.value)
        .exclude(status__in=[PaymentRequestStatus.CANCELLED.value, PaymentRequestStatus.REJECTED.value])
        .aggregate(total_requested_amount=Coalesce(Sum("request_amount"), Value(0), output_field=DecimalField()))[
            "total_requested_amount"
        ]
    )

    """
    Exclude payment requests where:
    1. The payment request status is not "Approved".
    2. The payment request is created by the client on behalf of the vendor (creator_id = client_id).
    Rationale:
    When a client raises a payment request on behalf of a vendor, the vendor should not see 
    the request until it is approved. This ensures that unapproved requests remain hidden 
    from the vendor to maintain confidentiality and workflow integrity.
    """

    already_paid_amount = (
        PaymentEntry.objects.filter(
            payment_request__project_id=project_id,
            payment_request__client_id=client_id,
            payment_request__vendor_id=vendor_id,
            payment_request__request_type=PaymentRequestType.NON_ORDER.value,
            cancelled_at__isnull=True,
        )
        .exclude(
            Q(
                ~Q(payment_request__status=PaymentRequestStatus.APPROVED.value),
                payment_request__creator_id=F("payment_request__client_id"),
            )
        )
        .aggregate(total_paid_amount=Coalesce(Sum("amount"), Value(0), output_field=DecimalField()))[
            "total_paid_amount"
        ]
    )
    return requested_amount, already_paid_amount


def vendor_payment_request_amount_fetch_for_non_order(*, client_id: int, vendor_id: int, project_id: int):
    data = (
        vendor_payment_request_list(client_id=client_id)
        .filter(
            vendor_id=vendor_id,
            project_id=project_id,
            request_type=PaymentRequestType.NON_ORDER.value,
        )
        .exclude(status__in=[PaymentRequestStatus.CANCELLED.value, PaymentRequestStatus.REJECTED.value])
        .aggregate(
            total_requested_amount=Coalesce(Sum("request_amount"), Value(0.0), output_field=DecimalField()),
            paid_amount_sum=Sum("paid_amount"),
        )
    )

    requested_amount = data.get("total_requested_amount")
    already_paid_amount = data.get("paid_amount_sum")

    return requested_amount, already_paid_amount


def get_vendor_payment_request_list_v1(client_id: int, project_id: int):
    return (
        PaymentRequest.objects.annotate_payment_request_number()
        .filter(client_id=client_id, project_id=project_id)
        .values("id", "request_number", "status")
    )


def get_client_payment_request_list_v1(vendor_id: int, project_id: int):
    return (
        PaymentRequest.objects.annotate_payment_request_number()
        .filter(vendor_id=vendor_id, project_id=project_id)
        .values("id", "request_number", "status")
    )


def get_vendor_payment_request_detail_v1(payment_request_id: int, client_id: int) -> PaymentRequest:
    payment_request = payment_request_selector_v2().filter(id=payment_request_id, client_id=client_id).first()
    if not payment_request:
        raise PaymentRequestNotFound("Payment request not found")
    return payment_request


def get_client_payment_request_detail_v1(payment_request_id: int, vendor_id: int) -> PaymentRequest:
    payment_request = payment_request_selector_v2().filter(id=payment_request_id, vendor_id=vendor_id).first()
    if not payment_request:
        raise PaymentRequestNotFound("Payment request not found")
    return payment_request


def get_vendor_payment_entry_v2(id: int, client_id: int):
    payment_entry = (
        PaymentEntry.objects.select_related("payment_request")
        .filter(pk=id, payment_request__client_id=client_id)
        .first()
    )
    if not payment_entry:
        raise PaymentEntryNotFound("Payment entry not found")
    return payment_entry


def get_client_payment_entry_v2(id: int, vendor_id: int):
    payment_entry = (
        PaymentEntry.objects.select_related("payment_request")
        .filter(pk=id, payment_request__vendor_id=vendor_id)
        .first()
    )
    if not payment_entry:
        raise PaymentEntryNotFound("Payment entry not found")
    return payment_entry


def payment_request_selector_v2() -> QuerySet:
    payment_request = PaymentRequest.objects.select_related(
        "project", "vendor", "created_by", "client", "created_by__org", "purchase_order", "invoice"
    ).annotate_payment_request_number()
    subquery_for_paid_amount = Subquery(
        PaymentRequest.objects.filter(id=OuterRef("pk"))
        .annotate(
            paid_amount=Coalesce(
                Sum(
                    "payment_entries__amount",
                    filter=Q(
                        payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                0,
                output_field=DecimalField(),
            )
        )
        .values("paid_amount")[:1]
    )
    payment_request = (
        payment_request.annotate(paid_amount=Coalesce(subquery_for_paid_amount, 0, output_field=DecimalField()))
        .annotate_request_data()
        .annotate(
            payment_due=F("request_amount") - F("paid_amount"),
        )
    )

    return payment_request
