from django.db.models import <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, OuterR<PERSON>, Q, QuerySet, Subquery, Sum, Value, When
from django.db.models.functions import <PERSON>, Coalesce, Concat, Greatest, Least, LPad

from common.querysets import AvailableQuerySetMixin
from payment_request.data.choices import PaymentRequestStatus, PaymentRequestType
from payment_request.domain.constants import PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE


class PaymentRequestQuerySet(QuerySet):
    def annotate_request_id(self):
        from payment_request.data.models import PaymentRequestAndRequestMapping

        request_subquery = Subquery(
            PaymentRequestAndRequestMapping.objects.filter(resource_id=OuterRef("pk"))
            .order_by("-id")
            .values("request_id")[:1]
        )
        return self.annotate(request_id=request_subquery)

    def annotate_request_approved_at(self):
        from approval_request.data.choices import ApprovalRequestStatusChoices
        from approval_request.data.models import ApprovalRequestActionHistory

        return self.annotate_request_id().annotate(
            approved_at=Subquery(
                ApprovalRequestActionHistory.objects.filter(
                    request_id=OuterRef("request_id"), request__status=ApprovalRequestStatusChoices.APPROVED.value
                )
                .order_by("-created_at")
                .values("created_at")[:1]
            )
        )

    def annotate_request_finished_at(self):
        from approval_request.data.choices import ApprovalRequestStatusChoices
        from approval_request.data.models import ApprovalRequestActionHistory

        return self.annotate_request_id().annotate(
            finished_at=Case(
                When(
                    Q(status=PaymentRequestStatus.APPROVED.value),
                    then=Subquery(
                        ApprovalRequestActionHistory.objects.filter(
                            request_id=OuterRef("request_id"),
                            request__status=ApprovalRequestStatusChoices.APPROVED.value,
                        )
                        .order_by("-created_at")
                        .values("created_at")[:1]
                    ),
                ),
                When(
                    Q(status=PaymentRequestStatus.REJECTED.value),
                    then=Subquery(
                        ApprovalRequestActionHistory.objects.filter(
                            request_id=OuterRef("request_id"),
                            request__status=ApprovalRequestStatusChoices.REJECTED.value,
                        )
                        .order_by("-created_at")
                        .values("created_at")[:1]
                    ),
                ),
            ),
        )

    def annotate_request_data(self):
        from payment_request.data.models import PaymentRequestAndRequestMapping

        request_mapping = PaymentRequestAndRequestMapping.objects.filter(
            resource_id=OuterRef("pk")
        ).annotate_request_data()
        return self.annotate(request_data=request_mapping.values("request_data")[:1])

    def annotate_payment_request_number(self):
        from payment_request.data.models import PaymentRequestAndRequestMapping

        request_mapping = PaymentRequestAndRequestMapping.objects.filter(resource_id=OuterRef("pk")).annotate(
            payment_request_number=Concat(
                F("request__year"),
                Value("PAY"),
                LPad(Cast("request__serial_number", CharField()), 8, Value("0")),
                output_field=CharField(),
            )
        )
        return self.annotate(request_number=request_mapping.values("payment_request_number")[:1])

    def annotate_pending_approver_user_ids(self):
        from approval_request.data.models import ApprovalRequest

        return self.annotate_request_id().annotate(
            pending_approver_user_ids=Coalesce(
                ApprovalRequest.objects.filter(id=OuterRef("request_id"))
                .annotate_pending_approver_user_ids()
                .values_list("pending_approver_user_ids", flat=True),
                [],
            )
        )

    def annotate_immediate_approver_user_ids(self):
        from approval_request.data.models import ApprovalRequest

        return self.annotate_request_id().annotate(
            immediate_approver_user_ids=Coalesce(
                ApprovalRequest.objects.filter(id=OuterRef("request_id"))
                .annotate_immediate_approver_user_ids()
                .values_list("immediate_approver_user_ids", flat=True),
                [],
            )
        )

    def annotate_payment_entry_count(self):
        from payment_request.data.models import PaymentRequest

        return self.annotate(
            payment_entry_count=Subquery(
                PaymentRequest.objects.filter(id=OuterRef("pk"))
                .annotate(
                    payment_entry_count=Count("payment_entries", filter=Q(payment_entries__cancelled_at__isnull=True))
                )
                .values("payment_entry_count")[:1]
            )
        )

    def annotate_po_amount_for_pr_approved(self):
        from order.data.models import VendorOrder

        po_subquery_in_case_of_pr_approved = VendorOrder.objects.filter(id=OuterRef("order_id")).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "purchase_orders__payment_requests__payment_entries__amount",
                    filter=Q(
                        purchase_orders__payment_requests__payment_entries__created_at__lte=OuterRef("finished_at"),
                        purchase_orders__payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            po_approved_paid_till_date=Subquery(po_subquery_in_case_of_pr_approved.values("paid_till_date_amount")[:1])
        )

    def annotate_po_amount_for_pr_unapproved(self):
        from order.data.models import VendorOrder

        po_subquery_in_case_of_pr_unapproved = VendorOrder.objects.filter(id=OuterRef("order_id")).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "purchase_orders__payment_requests__payment_entries__amount",
                    filter=Q(
                        purchase_orders__payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            po_unapproved_paid_till_date=Subquery(
                po_subquery_in_case_of_pr_unapproved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_invoice_with_order_amount_for_pr_approved(self):
        from order.data.models import VendorOrder

        invoice_with_order_subquery_incase_of_pr_approved = VendorOrder.objects.filter(
            id=OuterRef("order_id")
        ).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "invoices__payment_requests__payment_entries__amount",
                    filter=Q(
                        invoices__payment_requests__payment_entries__cancelled_at__isnull=True,
                        invoices__payment_requests__payment_entries__created_at__lte=OuterRef("finished_at"),
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            invoice_approved_paid_till_date=Subquery(
                invoice_with_order_subquery_incase_of_pr_approved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_invoice_with_order_amount_for_pr_unapproved(self):
        from order.data.models import VendorOrder

        invoice_with_order_subquery_incase_of_pr_unapproved = VendorOrder.objects.filter(
            id=OuterRef("order_id")
        ).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "invoices__payment_requests__payment_entries__amount",
                    filter=Q(
                        invoices__payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            invoice_unapproved_paid_till_date=Subquery(
                invoice_with_order_subquery_incase_of_pr_unapproved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_invoice_without_order_amount_for_pr_approved(self):
        from order.invoice.data.models import Invoice

        invoice_without_order_subquery_incase_of_pr_approved = Invoice.objects.filter(
            id=OuterRef("invoice_id")
        ).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        payment_requests__request_type=PaymentRequestType.INVOICE.value,
                        payment_requests__payment_entries__created_at__lte=OuterRef("finished_at"),
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            invoice_without_order_approved_paid_till_date=Subquery(
                invoice_without_order_subquery_incase_of_pr_approved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_invoice_without_order_amount_for_pr_unapproved(self):
        from order.invoice.data.models import Invoice

        invoice_without_subquery_incase_of_pr_unapproved = Invoice.objects.filter(id=OuterRef("invoice_id")).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        payment_requests__request_type=PaymentRequestType.INVOICE.value,
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            invoice_without_order_unapproved_paid_till_date=Subquery(
                invoice_without_subquery_incase_of_pr_unapproved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_non_order_amount_incase_of_pr_approved(self):
        from project.data.models import Project

        non_order_incase_of_pr_approved = Project.objects.filter(
            id=OuterRef("project_id"),
        ).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                        payment_requests__request_type=PaymentRequestType.NON_ORDER.value,
                        payment_requests__payment_entries__created_at__lte=OuterRef("finished_at"),
                        payment_requests__client_id=OuterRef("client_id"),
                        payment_requests__vendor_id=OuterRef("vendor_id"),
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            non_order_incase_of_pr_approved_paid_till_date=Subquery(
                non_order_incase_of_pr_approved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_non_order_amount_incase_of_pr_unapproved(self):
        from project.data.models import Project

        non_order_incase_of_pr_unapproved = Project.objects.filter(
            id=OuterRef("project_id"),
        ).annotate(
            paid_till_date_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        payment_requests__payment_entries__cancelled_at__isnull=True,
                        payment_requests__request_type=PaymentRequestType.NON_ORDER.value,
                        payment_requests__client_id=OuterRef("client_id"),
                        payment_requests__vendor_id=OuterRef("vendor_id"),
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            non_order_incase_of_pr_unapproved_paid_till_date=Subquery(
                non_order_incase_of_pr_unapproved.values("paid_till_date_amount")[:1]
            )
        )

    def annotate_paid_till_date(self):
        return self.annotate(
            paid_till_date=Case(
                When(
                    (
                        Q(request_type=PaymentRequestType.PO_ADVANCE.value)
                        | Q(
                            request_type=PaymentRequestType.INVOICE.value,
                            invoice__order_id__isnull=False,
                        )
                    )
                    & Q(status__in=[PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value]),
                    then=F("po_approved_paid_till_date") + F("invoice_approved_paid_till_date"),
                ),
                When(
                    (
                        Q(request_type=PaymentRequestType.PO_ADVANCE.value)
                        | Q(
                            request_type=PaymentRequestType.INVOICE.value,
                            invoice__order_id__isnull=False,
                        )
                    )
                    & Q(status__in=PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE),
                    then=F("po_unapproved_paid_till_date") + F("invoice_unapproved_paid_till_date"),
                ),
                When(
                    Q(
                        request_type=PaymentRequestType.INVOICE.value,
                        invoice__order_id__isnull=True,
                        status__in=PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE,
                    ),
                    then=F("invoice_without_order_unapproved_paid_till_date"),
                ),
                When(
                    Q(
                        request_type=PaymentRequestType.INVOICE.value,
                        invoice__order_id__isnull=True,
                        status__in=[PaymentRequestStatus.APPROVED.value, PaymentRequestStatus.REJECTED.value],
                    ),
                    then=F("invoice_without_order_approved_paid_till_date"),
                ),
                When(
                    Q(request_type=PaymentRequestType.NON_ORDER.value, status=PaymentRequestStatus.APPROVED.value),
                    then=F("non_order_incase_of_pr_approved_paid_till_date"),
                ),
                When(
                    Q(
                        request_type=PaymentRequestType.NON_ORDER.value,
                        status__in=PAYMENT_REQUEST_STATUSES_FOR_PAID_TILL_DATE,
                    ),
                    then=F("non_order_incase_of_pr_unapproved_paid_till_date"),
                ),
                default=Value(0),
                output_field=DecimalField(),
            ),
        )

    def annotate_invoice_total_paid_amount(self):
        from order.invoice.data.models import Invoice

        invoice_total_paid_amount = Invoice.objects.filter(id=OuterRef("invoice_id")).annotate(
            total_paid_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        Q(
                            payment_requests__cancelled_at__isnull=True,
                            payment_requests__payment_entries__cancelled_at__isnull=True,
                        ),
                        ~Q(
                            payment_requests__status__in=[
                                PaymentRequestStatus.CANCELLED.value,
                                PaymentRequestStatus.REJECTED.value,
                            ]
                        ),
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )
        return self.annotate(
            invoice_total_paid_amount=Subquery(invoice_total_paid_amount.values("total_paid_amount")[:1])
        )

    def annotate_payment_due_amount(self):
        return self.annotate_invoice_total_paid_amount().annotate(
            amount_remaining=F("request_amount") - F("paid_amount"),
            payment_due_amount=Case(
                When(
                    Q(invoice__isnull=False),
                    then=Greatest(
                        Least(
                            F("amount_remaining"),
                            F("invoice__amount")
                            + F("invoice__gst_amount")
                            - F("invoice__tds_amount")
                            - F("invoice_total_paid_amount"),
                        ),
                        Value(0),
                    ),
                ),
                default=F("amount_remaining"),
            ),
        )


class PaymentEntryQuerySet(QuerySet):
    def annotate_payment_request_number(self):
        from payment_request.data.models import PaymentRequest

        subquery = PaymentRequest.objects.filter(id=OuterRef("payment_request_id")).annotate_payment_request_number()
        return self.annotate(request_number=Subquery(subquery.values("request_number")[:1]))


class PaymentRequestAttachmentQuerySet(QuerySet, AvailableQuerySetMixin):
    pass
