from django.core.exceptions import ValidationError
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomFileField,
    HashIdField,
    SplitCharHashIdListField,
    SplitCharListField,
)
from core.serializers import OrganizationModelSerializer, UserModelSerializer
from order.data.choices import OrderTypeChoices
from order.interface.vendor_serializers import VendorPurchaseOrderModelSerializer
from order.invoice.domain.services.invoice import get_invoice_hidden_fields
from order.invoice.interface.serializers import InvoiceModelSerializer, InvoiceNumberSerializer

# from payment_entry.interface.serializers import BasePaymentEntryDetailSerializer
from payment_entry.interface.serializers import BasePaymentEntryModelSerializer
from payment_request.data.choices import PaymentMode, PaymentRequestStatus, PaymentRequestType
from payment_request.data.models import PaymentEntry, PaymentRequest, PaymentRequestAttachment
from payment_request.domain.entities import (
    PaymentEntryCreateData,
    PaymentRequestAttachmentData,
    PaymentRequestCreateData,
    PaymentRequestEntryCreateData,
    PaymentRequestEntryCreateDataV2,
    PaymentRequestUpdateData,
)
from payment_request.domain.factories import PaymentRequestActionServiceFactory
from payment_request.domain.services import PaymentRequestValidatorService
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData
from project.serializers import ProjectModelSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage


class OrganizationSerializer(OrganizationModelSerializer):
    class Meta(OrganizationModelSerializer.Meta):
        ref_name = "OrganizationSerializer"
        fields = ("id", "name")
        output_hash_id_fields = ("id",)


class UserSerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "UserOutput"
        fields = (
            "id",
            "name",
        )


class ProjectSerializer(ProjectModelSerializer):
    class Meta(ProjectModelSerializer.Meta):
        fields = ("id", "name", "job_id")


class PaymentRequestPurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
    order_number = serializers.CharField()
    already_requested_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    already_paid_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0, source="tax_amount")
    already_requested_gst_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    file = serializers.URLField()

    class Meta(VendorPurchaseOrderModelSerializer.Meta):
        fields = (
            "id",
            "order_number",
            "po_number",
            "amount",
            "gst_amount",
            "already_requested_amount",
            "already_requested_gst_amount",
            "already_paid_amount",
            "name",
            "file",
        )

        ref_name = "PurchaseOrderDropdownSerializer"


class PurchaseOrderDropdownSerializer(PaymentRequestPurchaseOrderSerializer):
    amount = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=15, decimal_places=2))
    def get_amount(self, obj):
        return obj.amount + obj.tax_amount

    class Meta(PaymentRequestPurchaseOrderSerializer.Meta):
        fields = (
            "id",
            "order_number",
            "po_number",
            "amount",
            "gst_amount",
            "already_requested_amount",
            "already_requested_gst_amount",
            "already_paid_amount",
        )

        ref_name = "PurchaseOrderDropdownSerializer"


class InvoiceDropdownSerializer(InvoiceModelSerializer):
    class InvoicePurchaseOrderDropdownSerializer(PurchaseOrderDropdownSerializer):
        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=15, decimal_places=2))
        def get_amount(self, obj):
            return obj.get("amount") + obj.get("tax_amount")

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=15, decimal_places=2))
        def get_gst_amount(self, obj):
            return obj.get("tax_amount")

    amount = serializers.DecimalField(max_digits=15, decimal_places=2, source="invoice_total_amount")
    already_requested_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    already_requested_gst_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    already_paid_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    purchase_order = serializers.SerializerMethodField()

    def get_purchase_order(self, obj):
        if hasattr(obj, "purchase_order_obj") and obj.purchase_order_obj:
            return self.InvoicePurchaseOrderDropdownSerializer(obj.purchase_order_obj).data

    class Meta(InvoiceModelSerializer.Meta):
        fields = (
            "id",
            "invoice_number",
            "amount",
            "gst_amount",
            "already_requested_amount",
            "already_requested_gst_amount",
            "already_paid_amount",
            "purchase_order",
            "file",
            "file_name",
        )
        ref_name = "InvoiceDropdownSerializer"


class PaymentEntryModelSerializer(BasePaymentEntryModelSerializer):
    # is_cancelled = serializers.SerializerMethodField()
    # can_cancel = serializers.SerializerMethodField()

    # @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    # def get_is_cancelled(self, obj):
    #     return obj.is_cancelled

    # def get_can_cancel(self, obj):
    #     if obj.is_cancelled:
    #         return False
    #     if obj.created_by.org_id != self.context.get("org_id"):
    #         return False
    #     return True

    vendor = OrganizationSerializer(source="payment_request.vendor")
    project = ProjectSerializer(source="payment_request.project")
    request_number = serializers.SerializerMethodField()
    request_type = serializers.SerializerMethodField()
    po_number = serializers.SerializerMethodField()
    invoice_number = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_number(self, obj):
        return obj.request_number

    def get_created_by(self, obj):
        if obj.created_by.org_id != self.context.get("org_id"):
            return OrganizationSerializer(
                {
                    "pk": obj.created_by.org_id,
                    "name": obj.created_by.org.name,
                }
            ).data
        else:
            return UserSerializer(obj.created_by).data

    @swagger_serializer_method(serializer_or_field=serializers.ChoiceField(choices=PaymentRequestType.choices))
    def get_request_type(self, obj):
        return obj.payment_request.request_type

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_po_number(self, obj):
        return obj.payment_request.purchase_order.po_number if obj.payment_request.purchase_order else None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_invoice_number(self, obj):
        return obj.payment_request.invoice.invoice_number if obj.payment_request.invoice else None

    class Meta:
        model = PaymentEntry
        fields = "__all__"


class PaymentEntrySerializer(PaymentEntryModelSerializer):
    class Meta(PaymentEntryModelSerializer.Meta):
        fields = (
            "id",
            "project",
            "transaction_id",
            "payment_mode",
            "amount",
            "payment_date",
            "remark",
            "file",
            "file_name",
            "is_cancelled",
            "can_cancel",
        )


class PaymentRequestAttachmentModelSerializer(BaseModelSerializer):
    class Meta:
        model = PaymentRequestAttachment
        fields = "__all__"


class PaymentRequestModelSerializer(BaseModelSerializer):
    class PurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = ("id", "po_number")
            ref_name = "PaymentRequestPurchaseOrderSerializer"

    class AttachmentSerializer(PaymentRequestAttachmentModelSerializer):
        class Meta(PaymentRequestAttachmentModelSerializer.Meta):
            fields = ("id", "file", "name", "uploaded_at")
            ref_name = "PaymentRequestAttachmentSerializer"

    project = ProjectSerializer()
    vendor = OrganizationSerializer()
    client = OrganizationSerializer()
    purchase_order = PurchaseOrderSerializer()
    invoice = InvoiceNumberSerializer()
    payment_entries = PaymentEntrySerializer(many=True)
    request_number = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    paid_amount = serializers.SerializerMethodField()
    is_cancelled = serializers.SerializerMethodField()
    reference_number = serializers.CharField(allow_null=True)
    actions = serializers.SerializerMethodField()
    request_id = serializers.SerializerMethodField()
    task_id = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    order_number = serializers.SerializerMethodField()
    order_type = serializers.SerializerMethodField()
    attachment = AttachmentSerializer(many=True, source="attachments", default=[])

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_order_type(self, obj):
        if hasattr(obj, "order_type") and obj.order_type:
            return dict(OrderTypeChoices.choices)[obj.order_type]
        return None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_order_number(self, obj):
        if hasattr(obj, "order_number"):
            return obj.order_number
        return None

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_is_cancelled(self, obj):
        return obj.is_cancelled

    def get_paid_amount(self, obj):
        if hasattr(obj, "paid_amount"):
            return obj.paid_amount
        return 0

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_number(self, obj):
        if hasattr(obj, "request_number"):
            return obj.request_number

    @swagger_serializer_method(serializer_or_field=serializers.DictField())
    def get_created_by(self, obj):
        if obj.created_by.org_id != self.context.get("org_id"):
            return OrganizationSerializer(
                {
                    "pk": obj.created_by.org_id,
                    "name": obj.created_by.org.name,
                }
            ).data
        else:
            return UserSerializer(obj.created_by).data

    @swagger_serializer_method(serializer_or_field=serializers.ListField())
    def get_actions(self, obj):
        can_create_vendor_payment_request = self.context.get("can_create_vendor_payment_request", False)
        action_service = PaymentRequestActionServiceFactory().get_service(
            user_id=self.context.get("user_id"),
            is_admin=self.context.get("is_admin"),
            org_id=self.context.get("org_id"),
            payment_request=obj,
        )
        user_actions = action_service.get_user_actions(
            can_create_vendor_payment_request=can_create_vendor_payment_request
        )
        return user_actions

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_id(self, obj):
        if not getattr(obj, "request_data"):
            return None
        if obj.status == PaymentRequestStatus.SUBMITTED.value:
            return None
        return obj.request_data["request_id"]

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_task_id(self, obj):
        if not getattr(obj, "request_data"):
            return None
        if obj.status == PaymentRequestStatus.SUBMITTED.value:
            return None
        return obj.request_data["task_id"]

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        if obj.vendor_id == self.context.get("org_id") and obj.status not in [
            PaymentRequestStatus.SUBMITTED,
            PaymentRequestStatus.APPROVED,
            PaymentRequestStatus.REJECTED,
            PaymentRequestStatus.CANCELLED,
        ]:
            return PaymentRequestStatus.PENDING.value
        else:
            return obj.status

    class Meta:
        model = PaymentRequest
        fields = "__all__"
        output_hash_id_fields = (
            "id",
            "request_id",
            "task_id",
        )


class PaymentRequestDetailSerializer(PaymentRequestModelSerializer):
    class OrderDataSerializer(BaseSerializer):
        id = HashIdField()
        number = serializers.CharField()
        type = serializers.CharField()
        amount = serializers.DecimalField(max_digits=15, decimal_places=2)

        class Meta:
            input_hash_id_fields = ()
            output_hash_id_fields = ()

    class InvoiceSerializer(InvoiceDropdownSerializer):
        amount = serializers.SerializerMethodField()
        hidden_fields = serializers.SerializerMethodField()

        def get_amount(self, obj):
            return obj.amount + obj.gst_amount

        def get_hidden_fields(self, obj):
            return get_invoice_hidden_fields(
                is_tds_enabled_on_org_level=obj.uploaded_by.org.invoice_config.is_tds_enabled
                if hasattr(obj.uploaded_by.org, "invoice_config")
                else False,
                tds_amount=obj.tds_amount,
            )

        class Meta(InvoiceDropdownSerializer.Meta):
            fields = (
                "id",
                "invoice_number",
                "amount",
                "gst_amount",
                "already_requested_amount",
                "already_requested_gst_amount",
                "already_paid_amount",
                "file",
                "file_name",
                "hidden_fields",
                "tds_amount",
            )
            ref_name = "PaymentRequestInvoiceSerializer"

    purchase_order = serializers.SerializerMethodField()

    def get_purchase_order(self, obj):
        if hasattr(obj, "purchase_order_dict") and obj.purchase_order_dict:
            file = obj.purchase_order_dict["file"]
            if file:
                obj.purchase_order_dict["file"] = PublicMediaFileStorage.url(file)
            obj.purchase_order_dict["amount"] = (
                obj.purchase_order_dict["amount"] + obj.purchase_order_dict["tax_amount"]
            )
            return PaymentRequestPurchaseOrderSerializer(obj.purchase_order_dict).data
        elif hasattr(obj, "purchase_order"):
            if obj.purchase_order and obj.purchase_order.file:
                obj.purchase_order.file = PublicMediaFileStorage.url(obj.purchase_order.file.url)
                obj.purchase_order.amount = obj.purchase_order.amount + obj.purchase_order.tax_amount
                return PaymentRequestPurchaseOrderSerializer(obj.purchase_order).data

    invoice = InvoiceSerializer()
    paid_till_date = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    payment_due = serializers.SerializerMethodField()
    order = serializers.SerializerMethodField()
    base_amount = serializers.SerializerMethodField()
    max_payment_entry_amount_allowed = serializers.SerializerMethodField()

    def get_max_payment_entry_amount_allowed(self, obj):
        max_amount_allowed = obj.request_amount - obj.paid_amount
        if obj.invoice:
            max_amount_allowed = min(
                max_amount_allowed,
                obj.invoice.invoice_total_amount - (obj.invoice.tds_amount or 0) - obj.invoice.already_paid_amount,
            )
        return max(max_amount_allowed, 0)

    def get_payment_due(self, obj):
        return serializers.DecimalField(max_digits=15, decimal_places=2).to_representation(
            self.get_max_payment_entry_amount_allowed(obj)
        )

    def get_base_amount(self, obj):
        return obj.request_amount - obj.gst_amount

    def get_order(self, obj):
        if obj.order_id:
            return self.OrderDataSerializer(
                {
                    "id": obj.order_id,
                    "number": obj.order_number,
                    "type": obj.order_type,
                    "amount": obj.order_amount,
                }
            ).data

    class Meta(PaymentRequestModelSerializer.Meta):
        fields = (
            "id",
            "vendor",
            "project",
            "request_number",
            "request_type",
            "purchase_order",
            "invoice",
            "request_amount",
            "base_amount",
            "remark",
            "gst_amount",
            "base_amount_percent",
            "gst_amount_percent",
            "base_amount_percent_toggle",
            "gst_amount_percent_toggle",
            "payment_entries",
            "created_by",
            "is_cancelled",
            "status",
            "actions",
            "request_id",
            "task_id",
            "order_number",
            "order_type",
            "paid_till_date",
            "attachment",
            "payment_due",
            "order",
            "max_payment_entry_amount_allowed",
        )


class PaymentRequestEntryCreateDataSerializer(BaseDataclassSerializer):
    transaction_id = serializers.CharField(allow_null=True, max_length=20)
    payment_mode = serializers.ChoiceField(choices=PaymentMode.choices)
    remark = serializers.CharField(allow_null=True, allow_blank=True, max_length=1000)
    file = CustomFileField(allow_null=True)
    amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
    )
    payment_date = serializers.DateField()
    id = HashIdField(allow_null=True, required=False)
    payment_request_id = HashIdField(allow_null=True, required=False)

    def validate(self, attrs):
        if attrs.amount <= 0:
            raise ValidationError({"amount": "Amount should be greater than 0"})
        return attrs

    class Meta:
        dataclass = PaymentRequestEntryCreateData
        ref_name = "PaymentRequestEntryCreateDataSerializer"


class PaymentRequestAttachmentDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    file = CustomFileField()
    name = serializers.CharField()

    class Meta:
        dataclass = PaymentRequestAttachmentData
        ref_name = "PaymentRequestAttachmentDataSerializer"


class PaymentRequestCreateInputSerializer(BaseDataclassSerializer):
    project_id = HashIdField(required=True)
    vendor_id = HashIdField(required=True)
    client_id = HashIdField(required=True)
    request_type = serializers.ChoiceField(choices=PaymentRequestType.choices)
    request_amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
    )
    gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    purchase_order_id = HashIdField(allow_null=True)
    invoice_id = HashIdField(allow_null=True)
    payment_request_entries = PaymentRequestEntryCreateDataSerializer(many=True)
    remark = serializers.CharField(allow_null=True, allow_blank=True)
    attachment = PaymentRequestAttachmentDataSerializer(many=True)

    def validate(self, attrs):
        errors = PaymentRequestValidatorService.validate_request_data(data=attrs)

        if attrs.request_type == PaymentRequestType.PO_ADVANCE and not attrs.purchase_order_id:
            errors.append({"purchase_order_id": "PO is required for PO Advance"})

        if attrs.request_type == PaymentRequestType.INVOICE and not attrs.invoice_id:
            errors.append({"purchase_order_id": "Invoice is required for Invoice"})

        if errors:
            raise ValidationError(errors)

        return attrs

    class Meta:
        dataclass = PaymentRequestCreateData
        ref_name = "PaymentRequestCreateInputSerializer"


class PaymentEntryCreateDataSerializer(PaymentRequestEntryCreateDataSerializer):
    payment_request_id = HashIdField(required=True)
    transaction_id = serializers.CharField(allow_null=True, allow_blank=True)
    remark = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta(PaymentRequestEntryCreateDataSerializer.Meta):
        dataclass = PaymentEntryCreateData
        ref_name = "PaymentEntryCreateDataSerializer"


class PaymentEntryDetailSerializer(PaymentEntrySerializer):
    class PaymentRequestDetailSerializer(PaymentRequestModelSerializer):
        class Meta(PaymentRequestModelSerializer.Meta):
            fields = (
                "id",
                "vendor",
                "project",
                "request_type",
                "gst_amount",
                "request_amount",
                "purchase_order",
                "invoice",
                "is_cancelled",
            )
            ref_name = "PaymentEntryRequestDetailSerializer"

    payment_request = PaymentRequestDetailSerializer()

    class Meta(PaymentEntrySerializer.Meta):
        fields = (
            "id",
            "transaction_id",
            "payment_mode",
            "amount",
            "payment_date",
            "remark",
            "file",
            "file_name",
            "is_cancelled",
            "payment_request",
            "request_number",
            "can_cancel",
            "created_at",
        )


class PaymentRequestListSerializer(PaymentRequestModelSerializer):
    paid_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    project_config = serializers.SerializerMethodField()
    payment_due = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=15, decimal_places=2))
    def get_payment_due(self, obj):
        max_amount_allowed = obj.request_amount - obj.paid_amount
        if obj.invoice:
            max_amount_allowed = min(
                max_amount_allowed,
                obj.invoice.amount
                + obj.invoice.gst_amount
                - (obj.invoice.tds_amount or 0)
                - obj.invoice_total_paid_amount,
            )
        return max(max_amount_allowed, 0)

    @swagger_serializer_method(serializer_or_field=ProjectCountryConfigData.drf_serializer)
    def get_project_config(self, obj):
        # TODO: Check if cache is hit every-time on passing
        return self.get_local_serializer_cache(
            "project_config",
            obj.project_id,
            ProjectCountryConfigData.drf_serializer(ProjectCountryConfigCache.get(instance_id=obj.project_id)).data,
        )

    class Meta(PaymentRequestModelSerializer.Meta):
        fields = (
            "id",
            "vendor",
            "client",
            "project",
            "created_at",
            "created_by",
            "project",
            "request_number",
            "request_type",
            "status",
            "actions",
            "request_id",
            "task_id",
            "request_amount",
            "paid_amount",
            "reference_number",
            "project_config",
            "payment_due",
        )


class PaymentEntryListSerializer(PaymentEntryModelSerializer):
    project_config = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=ProjectCountryConfigData.drf_serializer)
    def get_project_config(self, obj):
        return ProjectCountryConfigData.drf_serializer(
            ProjectCountryConfigCache.get(instance_id=obj.payment_request.project_id)
        ).data

    class Meta(PaymentEntryModelSerializer.Meta):
        fields = (
            "id",
            "payment_date",
            "project",
            "request_number",
            "transaction_id",
            "payment_mode",
            "vendor",
            "request_type",
            "amount",
            "is_cancelled",
            "created_by",
            "created_at",
            "can_cancel",
            "project_config",
        )


class PaymentRequestHeaderOutputSerializer(BaseSerializer):
    payment_requested = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    payment_requested_count = serializers.IntegerField(default=0)
    payment_approved = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    payment_approved_count = serializers.IntegerField()
    pending_approval = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    pending_approval_count = serializers.IntegerField(default=0)
    payment_done = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    payment_done_count = serializers.IntegerField()
    payment_due_count = serializers.IntegerField(source="total_payment_due_count")
    payment_due = serializers.DecimalField(source="total_payment_due", max_digits=20, decimal_places=2, default=0)

    class Meta:
        ref_name = "VendorPaymentRequestHeaderOutputSerializer"


class PaymentRequestUpdateInputSerializer(BaseDataclassSerializer):
    request_amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
    )
    gst_amount = serializers.DecimalField(max_digits=10, decimal_places=2, default=0)
    purchase_order_id = HashIdField(allow_null=True)
    invoice_id = HashIdField(allow_null=True)
    remark = serializers.CharField(allow_null=True, allow_blank=True)
    attachment = PaymentRequestAttachmentDataSerializer(many=True)
    payment_request_entries = PaymentRequestEntryCreateDataSerializer(many=True)

    def validate(self, attrs):
        errors = PaymentRequestValidatorService.validate_request_data(data=attrs)
        if errors:
            raise ValidationError(errors)

        return attrs

    class Meta:
        dataclass = PaymentRequestUpdateData
        ref_name = "PaymentRequestUpdateInputSerializer"


class PaidTillDateInfoFetchOutputSerializer(BaseSerializer):
    class POPaidTillDateSerializer(BaseSerializer):
        po_number = serializers.CharField()
        amount = serializers.DecimalField(max_digits=10, decimal_places=2, default=0)

        class Meta:
            ref_name = "POPaidTillDateSerializer"

    class InvoicePaidTillDateSerializer(BaseSerializer):
        invoice_number = serializers.CharField()
        amount = serializers.DecimalField(max_digits=10, decimal_places=2, default=0)

        class Meta:
            ref_name = "InvoicePaidTillDateSerializer"

    po_paid_till_date = POPaidTillDateSerializer(many=True)
    invoice_paid_till_date = InvoicePaidTillDateSerializer(many=True)
    summary_text = serializers.CharField()

    class Meta:
        ref_name = "PaidTillDateInfoFetchOutputSerializer"


class BasePaymentRequestListFilterSerializer(BaseSerializer):
    project_ids = SplitCharHashIdListField(required=False)
    request_date = serializers.DateField(required=False)
    request_type = serializers.ChoiceField(choices=PaymentRequestType.choices, required=False)
    request_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    paid_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    created_by_ids = SplitCharHashIdListField(required=False)
    statuses = SplitCharListField(required=False)
    search_text = serializers.CharField(required=False)
    request_number = serializers.CharField(required=False)
    ordering = serializers.CharField(required=False)
    currency_id = HashIdField(required=False)
    payment_due = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    is_payment_due = serializers.BooleanField(required=False, allow_null=True, default=None)


class PaymentRequestListOutputV2Serializer(BaseSerializer):
    request_id = serializers.CharField(source="request_number")
    status = serializers.ChoiceField(choices=PaymentRequestStatus.choices)
    id = HashIdField()

    class Meta:
        ref_name = "PaymentRequestListOutputV2Serializer"
        fields = ("id", "request_id", "status")
        output_hash_id_fields = ()


class PaymentRequestEntryListOutputV2Serializer(BaseSerializer):
    transaction_id = serializers.CharField()
    payment_mode = serializers.ChoiceField(PaymentMode.choices)
    amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    payment_date = serializers.DateField()
    id = HashIdField()

    class Meta:
        ref_name = "PaymentRequestEntryListOutputV2Serializer"
        fields = ("id", "transaction_id", "payment_mode", "amount", "payment_date")
        output_hash_id_fields = ()


class PaymentRequestEntryCreateOutputV2Serializer(BaseSerializer):
    id = HashIdField()

    class Meta:
        ref_name = "PaymentRequestEntryCreateOutputV2Serializer"
        fields = "id"
        output_hash_id_fields = ()


class PaymentRequestDetailsOutputV2Serializer(BaseModelSerializer):
    class PurchaseOrderSerializer(BaseSerializer):
        id = HashIdField()
        number = serializers.CharField(source="po_number")

        class Meta:
            ref_name = "PurchaseOrderSerializer"
            fields = ("id", "number")
            output_hash_id_fields = ()

    class InvoiceSerializer(BaseSerializer):
        id = HashIdField()
        number = serializers.CharField(source="invoice_number")

        class Meta:
            ref_name = "InvoiceSerializer"
            fields = ("id", "number")
            output_hash_id_fields = ()

    class VendorClientSerializer(BaseSerializer):
        id = HashIdField()
        name = serializers.CharField()

        class Meta:
            ref_name = "VendorClientSerializer"
            fields = ("id", "name")
            output_hash_id_fields = ()

    request_id = serializers.SerializerMethodField()
    request_number = serializers.CharField()
    vendor = VendorClientSerializer()
    client = VendorClientSerializer()
    requested_by = UserSerializer(source="created_by")
    requested_at = serializers.DateTimeField(source="created_at")
    invoice = InvoiceSerializer()
    purchase_order = PurchaseOrderSerializer()
    request_amount_without_tax = serializers.SerializerMethodField()
    tax_amount = serializers.CharField(source="gst_amount")
    total_request_amount = serializers.CharField(source="request_amount")
    paid_till_date = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    payment_due = serializers.DecimalField(max_digits=15, decimal_places=2)
    request_status = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_id(self, obj):
        if not getattr(obj, "request_data"):
            return None
        if obj.status == PaymentRequestStatus.SUBMITTED.value:
            return None
        return obj.request_data["request_id"]

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_amount_without_tax(self, obj):
        if hasattr(obj, "request_amount") and obj.request_amount:
            if hasattr(obj, "gst_amount") and obj.gst_amount:
                return str(obj.request_amount - obj.gst_amount)
            else:
                return str(obj.request_amount)

    @swagger_serializer_method(serializer_or_field=serializers.ChoiceField(choices=PaymentRequestStatus.choices))
    def get_request_status(self, obj):
        if hasattr(obj, "status"):
            return obj.status

    class Meta(PaymentRequest.Meta):
        fields = (
            "id",
            "request_id",
            "request_number",
            "vendor",
            "client",
            "request_type",
            "requested_by",
            "requested_at",
            "invoice",
            "purchase_order",
            "request_amount_without_tax",
            "tax_amount",
            "total_request_amount",
            "paid_till_date",
            "payment_due",
            "remark",
            "request_status",
        )
        ref_name = "PaymentRequestDetailsOutputV2Serializer"
        model = PaymentRequest
        output_hash_id_fields = (
            "id",
            "request_id",
        )


class PaymentRequestEntryCreateInputV2Serializer(BaseDataclassSerializer):
    transaction_id = serializers.CharField(allow_null=True, max_length=20)
    payment_mode = serializers.ChoiceField(choices=PaymentMode.choices)
    remark = serializers.CharField(allow_null=True, allow_blank=True, max_length=1000)
    transaction_proof_file_url = serializers.URLField(allow_null=True, required=True)
    transaction_proof_file_name = serializers.CharField(
        allow_null=True, allow_blank=True, max_length=200, required=True
    )
    amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
    )
    payment_date = serializers.DateField()

    def validate(self, attrs):
        if attrs.amount <= 0:
            raise ValidationError({"amount": "Amount should be greater than 0"})
        return attrs

    class Meta:
        dataclass = PaymentRequestEntryCreateDataV2
        ref_name = "PaymentRequestEntryCreateInputV2Serializer"


class PaymentEntryDetailsOutputV2Serializer(PaymentEntryModelSerializer):
    class TransactionProofSerializer(BaseSerializer):
        url = serializers.FileField(source="file")
        name = serializers.CharField(source="file_name")

        class Meta:
            ref_name = "TransactionProofSerializer"

    status = serializers.SerializerMethodField()
    transaction_proof = TransactionProofSerializer(source="*")

    def get_status(self, obj):
        if hasattr(obj, "is_cancelled"):
            if obj.is_cancelled:
                return "cancelled"
        return "submitted"

    class Meta(PaymentEntryModelSerializer.Meta):
        fields = (
            "id",
            "transaction_id",
            "payment_mode",
            "amount",
            "payment_date",
            "remark",
            "transaction_proof",
            "status",
            "created_at",
            "created_by",
        )
