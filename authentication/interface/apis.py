from urllib.parse import urljoin

import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from phonenumber_field import serializerfields
from rest_framework import serializers, status
from rest_framework.authtoken.models import Token
from rest_framework.response import Response

from authentication.domain.exceptions import ExpiredUser
from authentication.domain.otp_authentication import OTPAuthenticationService, process_user_login
from authentication.domain.services import admin_login_bypass_token_generator, magic_link_email_service
from authentication.domain.trigger import trigger_user_logged_in
from authentication.interface.serializers import (
    AdminBypassLoginSerialzer,
    AuthLoginSerializer,
    DirectLoginSerializer,
    EmailMagicLinkDirectLoginSerializer,
    PhoneNumberSerializer,
)
from common.apis import <PERSON><PERSON>pen<PERSON><PERSON>
from common.choices import SourceChoices
from common.constants import RequestHeaders
from common.serializers import BaseSerializer
from core.caches import UserAuthCache
from core.exceptions import (
    InactiveUserException,
    MagicLinkEmailServiceException,
    OrganizationException,
    OrganizationSubscriptionExpiredException,
    PhoneNumberLoginNotAllowed,
    UserNotRegistered,
    UserNotVerified,
)
from core.models import OrganizationUser, Timezone, User
from core.selectors import get_timezone_instance_by_name
from core.services import (
    create_login_history,
    is_phone_number_login_allowed,
    user_get_using_phone_number,
    validate_subscription_using_org_id,
)
from external_services.messengers.whatsapp.entities import (
    EventTemplates,
    ReceiverData,
    WhatsappMessengerData,
    WhatsAppTemplateData,
)
from external_services.messengers.whatsapp.services import WhatsappExternalMessenger
from rollingbanners.firebase import FirebaseService
from rollingbanners.otp_service import BaseOTPService

from .views import _process_request

logger = structlog.get_logger(__name__)
OTP_SERVICE: BaseOTPService = import_string(settings.OTP_SERVICE)


class FirebaseTokenVerifyApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        token = serializers.CharField()  # firebase Token
        fcm_token = serializers.CharField(required=False, allow_null=True, allow_blank=True)
        # TODO: Remove below fields after all apps are updated
        source = serializers.ChoiceField(choices=SourceChoices.choices, default=SourceChoices.WEB)
        latitude = serializers.FloatField(min_value=-90, max_value=90, required=False)
        longitude = serializers.FloatField(min_value=-180, max_value=180, required=False)

        class Meta:
            ref_name = "ValidateInputToken"
            input_hash_id_fields = ()

    input_serializer_class = InputSerializer
    serializer_class = InputSerializer

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        decoded_token = FirebaseService.validate_firebase_token(data.get("token"))
        self.validate_data(PhoneNumberSerializer, decoded_token)
        timezone: Timezone = get_timezone_instance_by_name(
            self.get_request_header(RequestHeaders.TIMEZONE) or self.get_request_header(RequestHeaders.OLD_TIMEZONE)
        )
        fcm_token = data.get("fcm_token")
        source = self.get_platform() or data.get("source")
        latitude = self.get_latitude() or data.get("latitude")
        longitude = self.get_longitude() or data.get("longitude")
        ip_address = self.get_ip_address()

        try:
            # in case of app only creating user if not exists
            response = process_user_login(
                fcm_token=fcm_token,
                source=source,
                phone_number=decoded_token.get("phone_number"),
                ip_address=ip_address,
                latitude=latitude,
                longitude=longitude,
                timezone=timezone,
            )
            return response
        except ExpiredUser as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info(
                "subscription_ended",
                user_id=e.user_id,
                org_id=e.org_id,
                expiry_date=e.expiry_date.isoformat(),
            )
            return Response(status=e.code)


class UserCheckApi(BaseOpenApi):
    serializer_class = PhoneNumberSerializer
    input_serializer_class = PhoneNumberSerializer

    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            user = user_get_using_phone_number(phone_number=data.get("phone_number"), create=False)
            if not OrganizationUser.objects.filter(user_id=user.pk).exists():
                raise UserNotRegistered(_("User not registered."))
            if hasattr(user, "org") and user.org:
                if not user.org.is_active:
                    raise OrganizationException(_("Organization is not active."))
                is_subscription_valid, subscription_end_date = validate_subscription_using_org_id(user.org.id)
                if is_subscription_valid is False:
                    raise OrganizationSubscriptionExpiredException(
                        expiry_date=subscription_end_date, user_id=user.pk, org_id=user.org.id
                    )
            if user.is_expired:
                raise ExpiredUser(_("Your login has been expired. Please contact to RDash admin."))
            if user.is_verified is None:
                raise UserNotVerified(_("User not Verified. Please contact organisation admin."))
            if is_phone_number_login_allowed(user) is False:
                raise PhoneNumberLoginNotAllowed(_("Mobile login disabled. Kindly login via Email."))

        except UserNotRegistered as e:
            self.set_response_message(str(e))
            logger.info("User not registered.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except InactiveUserException as e:
            self.set_response_message(str(e))
            logger.info("Inactive User.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except ExpiredUser as e:
            self.set_response_message(str(e))
            logger.info("Expired User.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except UserNotVerified as e:
            self.set_response_message(str(e))
            logger.info("User is not verified.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except OrganizationException as e:
            self.set_response_message(str(e))
            logger.info("Organization is not active.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except PhoneNumberLoginNotAllowed as e:
            self.set_response_message(str(e))
            logger.info("Mobile login disabled. Kindly login via Email.", error=str(e))
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info(
                "subscription_ended",
                user_id=e.user_id,
                org_id=e.org_id,
                expiry_date=e.expiry_date.isoformat(),
            )
            return Response(status=e.code)

        return Response(True, status=status.HTTP_200_OK)


class AuthDirectLoginView(BaseOpenApi):
    pagination_class = None

    @swagger_auto_schema(
        query_serializer=AdminBypassLoginSerialzer(), responses={status.HTTP_200_OK: AuthLoginSerializer()}
    )
    def get(self, request, *args, **kwargs):
        serializer = AdminBypassLoginSerialzer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        token, _ = Token.objects.get_or_create(user=user)
        UserAuthCache.delete(instance_id=str(user.pk))

        try:
            process_request = _process_request(token=token, user=user)
            ip_address = self.get_ip_address()
            # user_logged_in.send(sender=user.__class__, request=request, user=user)
            create_login_history(ip_address=ip_address, user_id=user.pk, source=self.get_platform())
            return process_request
        except ExpiredUser as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info("subscription_ended", user_id=e.user_id, org_id=e.org_id, expiry_date=e.expiry_date.isoformat())
            return Response(status=e.code)


class AuthDirectLoginViaEmailTemplateView(AuthDirectLoginView):
    pagination_class = None

    @swagger_auto_schema(
        query_serializer=DirectLoginSerializer(),
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="direct_login_via_email",
    )
    def get(self, request, *args, **kwargs):
        serializer = DirectLoginSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        token, _ = Token.objects.get_or_create(user=user)
        UserAuthCache.delete(instance_id=str(user.pk))
        try:
            process_request = _process_request(token=token, user=user)
            # user_logged_in.send(sender=user.__class__, request=request, user=user)
            create_login_history(
                ip_address=self.get_ip_address(),
                user_id=user.id,
                source=self.get_platform(),
            )
            return process_request
        except ExpiredUser as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info("subscription_ended", user_id=e.user_id, org_id=e.org_id, expiry_date=e.expiry_date.isoformat())
            return Response(status=e.code)


class MagicLinkAuthDirectLogin(AuthDirectLoginView):
    pagination_class = None
    input_serializer_class = EmailMagicLinkDirectLoginSerializer

    @swagger_auto_schema(
        query_serializer=EmailMagicLinkDirectLoginSerializer(),
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="email_magic_direct_login",
    )
    def get(self, request, *args, **kwargs):
        serializer = EmailMagicLinkDirectLoginSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        token, _ = Token.objects.get_or_create(user=user)
        UserAuthCache.delete(instance_id=str(user.pk))
        try:
            process_request = _process_request(token=token, user=user)
            create_login_history(
                ip_address=self.get_ip_address(),
                user_id=user.id,
                source=self.get_platform(),
            )
            trigger_user_logged_in(user_id=user.id, source=self.get_platform())
            return process_request
        except ExpiredUser as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info("subscription_ended", user_id=e.user_id, org_id=e.org_id, expiry_date=e.expiry_date.isoformat())
            return Response(status=e.code)


class EmailMagicLinkApi(BaseOpenApi):
    pagination_class = None

    class InputSerializer(BaseSerializer):
        email = serializers.EmailField()

        class Meta:
            ref_name = "EmailMagicLinkInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="email_magic_direct_login",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            magic_link_email_service(email=data.get("email"))
        except MagicLinkEmailServiceException:
            self.set_response_message("Unable to send magic link. Please try again.")
            raise ValidationError("Unable to send magic link. Please try again.")

        except UserNotRegistered:
            self.set_response_message("User not registered.")
            raise ValidationError("User not registered")

        except InactiveUserException:
            self.set_response_message("User is not active. Please contact the organization admin.")
            raise ValidationError("User is not active. Please contact the organization admin.")

        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info("subscription_ended", user_id=e.user_id, org_id=e.org_id, expiry_date=e.expiry_date.isoformat())
            return Response(status=e.code)

        return Response(True, status=status.HTTP_200_OK)


class WhatsappMagicLinkApi(BaseOpenApi):
    pagination_class = None

    class InputSerializer(BaseSerializer):
        phone_number = serializers.CharField()

        class Meta:
            ref_name = "WhatsappMagicLinkInputSerializer"

    filter_serializer_class = InputSerializer

    @swagger_auto_schema(
        # request_body=InputSerializer, # TODO: Swagger Fix
        responses={status.HTTP_200_OK: ""},
        operation_id="whatsapp_magic_direct_login",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        try:
            user = User.objects.get(phone_number=f'+{data.get("phone_number")}', deleted_at__isnull=True)
        except User.DoesNotExist:
            return Response(True, status=status.HTTP_200_OK)
        uid = urlsafe_base64_encode(force_bytes(user.hashid))
        token = admin_login_bypass_token_generator.make_token(user)
        url = urljoin(settings.LINK_URL, f"direct-login?uid={uid}&token={token}&auid={user.hash_id}")
        messenger = WhatsappExternalMessenger(
            data=WhatsappMessengerData(
                template_data=WhatsAppTemplateData(
                    name=EventTemplates.LOGIN_VIA_WHATSAPP.value, context={"login_url": url}
                ),
                receivers=[ReceiverData(name=user.name, phone_number=user.phone_number.as_e164)],
            )
        )
        try:
            messenger.process()
        except WhatsappExternalMessenger.WhatsappMessengerException as e:
            logger.info("Cannot send whatsapp login url", error=e)
        return Response(True, status=status.HTTP_200_OK)


class LoginOtpSendApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        token = serializers.CharField()  # firebase Token
        phone_number = serializerfields.PhoneNumberField()
        app_hash = serializers.CharField(required=False, default=None, allow_null=True)

        class Meta:
            ref_name = "SentLoginOtpInputSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={status.HTTP_200_OK: ""},
        operation_id="login_otp_send_api",
        operation_summary="Sent OTP to the user",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            user = user_get_using_phone_number(phone_number=data.get("phone_number"), create=False)
            if is_phone_number_login_allowed(user) is False:
                raise OTPAuthenticationService.PhoneNumberLoginNotAllowedException(
                    message="Mobile login disabled. Kindly login via Email.",
                    code=OTPAuthenticationService.ExceptionCodeEnum.OTP_LOGIN_NOT_ALLOWED.value,
                )
            if hasattr(user, "org") and user.org:
                is_subscription_valid, subscription_end_date = validate_subscription_using_org_id(user.org.id)
                if is_subscription_valid is False:
                    raise OrganizationSubscriptionExpiredException(
                        expiry_date=subscription_end_date, user_id=user.pk, org_id=user.org.id
                    )
            OTPAuthenticationService().send_otp(
                token=data.get("token"), phone_number=str(data.get("phone_number")), app_hash=data.get("app_hash")
            )
        except OTPAuthenticationService.SendOTPException as e:
            return Response(data={"message": e.message, "error_code": e.code}, status=status.HTTP_400_BAD_REQUEST)
        except OTPAuthenticationService.OTPAuthenticationServiceException as e:
            return Response(data={"message": e.message, "error_code": e.code}, status=status.HTTP_400_BAD_REQUEST)
        except OTPAuthenticationService.PhoneNumberLoginNotAllowedException as e:
            return Response(data={"message": e.message, "error_code": e.code}, status=status.HTTP_400_BAD_REQUEST)
        except OrganizationSubscriptionExpiredException as e:
            logger.info(
                "subscription_ended",
                user_id=e.user_id,
                org_id=e.org_id,
                expiry_date=e.expiry_date.isoformat(),
            )
            return Response(data={"message": e.detail, "error_code": e.code}, status=status.HTTP_401_UNAUTHORIZED)
        self.set_response_message("OTP sent successfully.")
        return Response(status=status.HTTP_200_OK)


class LoginOtpVerifyApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        token = serializers.CharField()  # firebase Token
        otp = serializers.CharField()
        fcm_token = serializers.CharField(required=False, allow_null=True, allow_blank=True)
        # TODO: Remove below fields after all apps are updated
        source = serializers.ChoiceField(choices=SourceChoices.choices, default=SourceChoices.WEB)
        latitude = serializers.FloatField(min_value=-90, max_value=90, required=False)
        longitude = serializers.FloatField(min_value=-180, max_value=180, required=False)

        class Meta:
            ref_name = "VerifyLoginOtpInputSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="login_otp_verify_api",
        operation_summary="Login otp verify api",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        timezone: Timezone = get_timezone_instance_by_name(
            self.get_request_header(RequestHeaders.TIMEZONE) or self.get_request_header(RequestHeaders.OLD_TIMEZONE)
        )
        try:
            phone_number = OTPAuthenticationService().verify_otp(
                token=data.get("token"),
                otp=data.get("otp"),
            )
        except OTPAuthenticationService.VerifyOTPException as e:
            return Response(data={"message": e.message, "error_code": e.code}, status=status.HTTP_400_BAD_REQUEST)
        except OTPAuthenticationService.OTPAuthenticationServiceException as e:
            return Response(data={"message": e.message, "error_code": e.code}, status=status.HTTP_400_BAD_REQUEST)
        latitude = self.get_latitude() or data.get("latitude")
        longitude = self.get_longitude() or data.get("longitude")
        return process_user_login(
            fcm_token=data.get("fcm_token"),
            source=self.get_platform() or data.get("source"),
            phone_number=phone_number,
            ip_address=self.get_ip_address(),
            latitude=latitude,
            longitude=longitude,
            timezone=timezone,
        )
