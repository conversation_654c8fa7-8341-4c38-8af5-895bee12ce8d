from typing import Optional

import structlog
from dateutil.relativedelta import relativedelta
from dj_rest_auth import views
from dj_rest_auth.views import PasswordResetConfirmView as AuthPasswordResetConfirmView
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers, status
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import PermissionDenied
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from authentication.domain.exceptions import ExpiredUser, InactiveUser, NotRegisteredUser
from authentication.domain.services import fcm_token_delete, fcm_token_update
from authentication.interface.serializers import (
    AuthLoginSerializer,
    AuthPermissionSerializer,
    PasswordResetConfirmSerializer,
    PasswordResetSerializer,
)
from authorization.domain.constants import NOTIFICATION_PERMISSIONS, Permissions
from authorization.domain.services import permission_categorize
from common.apis import BaseApi, RequestHeaderMixin
from common.choices import PermissionScope, SourceChoices
from common.serializers import BaseSerializer
from core.caches import OrganizationCountryConfigCache
from core.entities import TimezoneData
from core.exceptions import OrganizationSubscriptionExpiredException
from core.helpers import OrgPermissionHelper
from core.models import Timezone, User
from core.selectors import (
    fetch_default_org_timezone_obj,
    get_default_currency_tax_timezone,
    organization_user_fetch_active_using_user,
)
from core.services import (
    _validate_subscription,
    create_login_history,
    organization_user_update,
    set_user_profile_to_analytics_platform,
    user_location_history_create,
)
from core.user.services import user_approval_data_fetch
from rollingbanners.authentication import TokenData
from rollingbanners.jwt import get_default_jwt_expiry_utc

logger = structlog.get_logger(__name__)


def _process_request(*, token: Token, org_id: Optional[int] = None, user: User):
    if user.deleted_by or user.deleted_at or not user.is_active:
        raise ValidationError(_("User is not Found."))
    if hasattr(user, "org") and user.org and user.org.system_user_id != user.pk and not user.org.is_active:
        raise ValidationError(_("Organization is not active."))
    if user.is_expired:
        raise ExpiredUser(_("Your login has been expired. Please contact to RDash admin."))

    org_users = organization_user_fetch_active_using_user(user_id=token.user_id)

    subscription_end_date = timezone.now().date() + relativedelta(
        years=1
    )  # default value for no org config or no subscription end date cases()

    jwt_expiry_utc = get_default_jwt_expiry_utc()
    if org_id:
        default_timezone_data = fetch_default_org_timezone_obj()
        org_user = org_users.filter(organization_id=org_id).first()
        org_config = org_user.organization.config if hasattr(org_user.organization, "config") else None
        if org_config is not None and org_config.subscription_end_date is not None:
            subscription_end_date = org_config.subscription_end_date
            if org_config.timezone is None:
                setattr(
                    org_config,
                    "timezone",
                    Timezone(
                        tz=default_timezone_data.name,
                        locale=default_timezone_data.locale,
                    ),
                )
            is_valid, _jwt_expiry_utc = _validate_subscription(org_config)
            if is_valid is False:
                raise OrganizationSubscriptionExpiredException(
                    expiry_date=subscription_end_date, user_id=user.id, org_id=org_id
                )
            else:
                jwt_expiry_utc = min(jwt_expiry_utc, _jwt_expiry_utc)
    else:
        org_user = org_users.order_by("-accessed_at").first()

    setattr(token, "subscription_end_date", subscription_end_date)
    setattr(token, "show_banner_after_remaining_days", settings.SUBSCRIPTION_BANNER_THRESHOLD_DAYS)

    # updating last accessed_at in organization user
    if org_user is not None:
        data = dict(accessed_at=timezone.now())
        organization_user_update(instance=org_user, data=data)

    if not hasattr(user, "token_data"):
        token_data = TokenData(
            key=user.auth_token.key,
            user_id=user.pk,
            org_id=org_user.organization_id if org_user else None,
            org_type=org_user.organization.type if org_user else None,
            is_admin=org_user.is_admin if org_user else False,
        )
        setattr(user, "token_data", token_data)
    else:
        token_data = user.token_data
    try:
        permissions = list(set(OrgPermissionHelper.get_permissions(user=user)) - NOTIFICATION_PERMISSIONS.keys())
    except PermissionDenied:
        if token_data.org_id is None:
            permissions = []
        else:
            PermissionDenied()
    if Permissions.CAN_VIEW_ALL_PROJECTS in permissions:
        setattr(
            user,
            "token_data",
            TokenData(
                key=user.token_data.key,
                user_id=user.pk,
                org_id=user.token_data.org_id,
                org_type=user.token_data.org_type,
                is_admin=user.token_data.is_admin,
                org_observer=True,
            ),
        )
    setattr(token, "permission_list", permissions)
    permissions: dict = permission_categorize(permissions, scope=PermissionScope.CORE)
    setattr(token, "permissions", permissions)
    user_approval_data = user_approval_data_fetch(user)
    setattr(token, "user_approval_data", user_approval_data)
    if user.org_id:
        config = OrganizationCountryConfigCache.get(instance_id=user.org_id)
        setattr(token, "currency", config.currency)
        setattr(token, "tax_type", config.tax_type)
        setattr(token, "tds_type", config.tds_type)
        if not user.timezone:
            setattr(token, "timezone", config.timezone)
        else:
            setattr(
                token,
                "timezone",
                TimezoneData(
                    id=user.timezone.id,
                    name=user.timezone.name,
                    locale=user.timezone.locale,
                ),
            )
    else:
        default_currency_data, default_tax_data, default_timezone_data = get_default_currency_tax_timezone()
        setattr(token, "currency", default_currency_data)
        setattr(token, "tax_type", default_tax_data)
        setattr(token, "timezone", default_timezone_data)

    setattr(token, "uid", user.pk)

    serializer = AuthLoginSerializer(
        instance=token,
        context={
            "org_user": org_user,
            "org_observer": user.token_data.org_observer,
            "org_id": user.token_data.org_id,
            "user": user,
            "jwt_expiry_utc": jwt_expiry_utc,
        },
    )
    set_user_profile_to_analytics_platform(user=user)
    response = Response(serializer.data, status=status.HTTP_200_OK)
    return response


class AuthLoginView(views.LoginView, RequestHeaderMixin):
    def get_response(self):
        self.request.user.refresh_from_db()
        process_request = _process_request(token=self.token, user=self.request.user)
        create_login_history(
            ip_address=self.get_ip_address(),
            user_id=self.request.user.pk,
            source=self.get_platform(),
        )

        return process_request

    @swagger_auto_schema(responses={status.HTTP_200_OK: AuthLoginSerializer()})
    def post(self, request, *args, **kwargs):
        return super(AuthLoginView, self).post(request, args, kwargs)


class AuthVerifyView(BaseApi):
    class FilterSerializer(BaseSerializer):
        fcm_token = serializers.CharField(required=False, allow_null=True, allow_blank=True)
        # TODO: Remove below fields after all apps are updated
        source = serializers.ChoiceField(choices=SourceChoices, required=False)
        latitude = serializers.FloatField(min_value=-90, max_value=90, required=False)
        longitude = serializers.FloatField(min_value=-180, max_value=180, required=False)

        class Meta:
            ref_name = "AuthVerifyFilter"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="verify_token",
        operation_summary="verifying the login token",
    )
    def get(self, request):
        data = self.validate_filter_data()

        if hasattr(request.user, "app_token") and request.user.app_token:
            raise ValidationError(_("App token can not be used for this request."))

        request.user.refresh_from_db()

        try:
            response = _process_request(
                token=request.user.auth_token,
                org_id=request.user.token_data.org_id if request.user.token_data.org_id else None,
                user=request.user,
            )
        except ExpiredUser as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            self.set_response_message(str(e))
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        except OrganizationSubscriptionExpiredException as e:
            self.set_response_message(e.detail)
            logger.info(
                "subscription_ended",
                user_id=e.user_id,
                org_id=e.org_id,
                expiry_date=e.expiry_date.isoformat(),
            )
            return Response(status=e.code)

        # user_logged_in.send(sender=User.__class__, request=self.request, user=
        # User.objects.get(id=self.request.user.pk))

        fcm_token = data.get("fcm_token")
        source = self.get_source() or data.get("source")
        latitude = self.get_latitude() or data.get("latitude")
        longitude = self.get_longitude() or data.get("longitude")

        create_login_history(
            ip_address=self.get_ip_address(),
            user_id=self.request.user.pk,
            source=source,
        )

        if fcm_token:
            fcm_token_update(fcm_token=fcm_token, user_id=request.user.pk, source=source)

        user_location_history_create(user_id=request.user.pk, latitude=latitude, longitude=longitude)

        return response


class AuthTokenVerifyView(BaseApi):
    @swagger_auto_schema(
        responses={status.HTTP_200_OK: AuthLoginSerializer()},
        operation_id="token_verify",
        operation_summary="verifying the app token",
    )
    def get(self, request):
        return Response({"detail": _("Token is valid.")}, status=status.HTTP_200_OK)


class AuthPermissionView(APIView):
    @swagger_auto_schema(responses={status.HTTP_200_OK: AuthPermissionSerializer()})
    def get(self, request):
        return Response({"permissions": request.user.get_all_permissions()}, status=status.HTTP_200_OK)


class PasswordResetView(GenericAPIView):
    """
    Accepts the following POST parameters: email
    Returns the success/fail message.
    """

    serializer_class = PasswordResetSerializer
    permission_classes = (AllowAny,)

    def post(self, request, *args, **kwargs):
        # Create a serializer with request.data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            opts = {
                "site_name": settings.PASSWORD_RESET_DASHBOARD_URL,
                "from_email": settings.PASSWORD_RESET_FROM_EMAIL,
                "request": self.request,
            }
            serializer.save(**opts)
        except NotRegisteredUser:
            return Response({"message": _("User is not registered.")}, status=status.HTTP_404_NOT_FOUND)
        except InactiveUser:
            return Response({"message": _("User is not active.")}, status=status.HTTP_403_FORBIDDEN)
        except Exception:
            return Response(
                {"message": _("Unable to process at this time.")}, status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        else:
            return Response(
                {
                    "message": _("Password reset e-mail has been sent."),
                },
                status=status.HTTP_200_OK,
            )


class PasswordResetConfirmView(AuthPasswordResetConfirmView):
    """
    Password reset e-mail link is confirmed, therefore
    this resets the user's password.

    Accepts the following POST parameters: token, uid,
        new_password1, new_password2
    Returns the success/fail message.
    """

    serializer_class = PasswordResetConfirmSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            serializer.save()
        except Exception:
            return Response(
                {"message": _("Unable to process at this time.")}, status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        else:
            return Response({"message": _("Password reset done successfully.")})


class LogoutView(views.LogoutView):
    def post(self, request, *args, **kwargs):
        data = request.data
        fcm_token = data.get("fcm_token") if "fcm_token" in data.keys() else None
        if fcm_token:
            fcm_token_delete(fcm_token=fcm_token)
        return Response({"detail": _("Successfully logged out.")})
