from dj_rest_auth.serializers import TokenSerializer
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import SetPasswordForm
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.mail import EmailMultiAlternatives
from django.template import loader
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_decode as uid_decoder
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext as _
from drf_yasg.utils import swagger_serializer_method
from phonenumber_field import serializerfields
from rest_framework import serializers
from rest_framework.settings import api_settings

from authentication.domain.exceptions import InactiveUser, NotRegisteredUser
from authentication.domain.services import (
    admin_login_bypass_token_generator,
    email_login_bypass_token_generator,
    email_magic_link_login_check,
)
from common.helpers import generate_dynamic_recce_link
from common.serializers import BaseSerializer
from core.entities import CurrencyData, TaxTypeData, TDSTypeData, TimezoneData
from core.serializers import ApprovalCountSerializer, CountrySerializer, ProfileSerializer
from rollingbanners import jwt
from rollingbanners.hash_id_converter import HashIdConverter, HashIdException

UserModel = get_user_model()


class DirectLoginSerializer(BaseSerializer):
    uid = serializers.CharField(required=True)
    token = serializers.CharField(required=True)

    user = None

    def validate(self, attrs):
        # derived from password reset confirm
        try:
            attrs["uid"] = str(HashIdConverter.decode(force_str(uid_decoder(attrs["uid"]))))
            self.user = UserModel._default_manager.select_related("org").get(pk=attrs["uid"])
        except (TypeError, ValueError, OverflowError, HashIdException, UserModel.DoesNotExist):
            raise serializers.ValidationError(_("Direct login link is expired."))

        if not email_login_bypass_token_generator.check_token(self.user, attrs["token"]):
            raise serializers.ValidationError(_("Direct login link is expired."))
        return attrs

    def save(self, **kwargs) -> UserModel:
        if self.user is None:
            raise Exception("Call is_valid before save.")
        return self.user

    class Meta:
        pass


class EmailMagicLinkDirectLoginSerializer(BaseSerializer):
    uid = serializers.CharField(required=True)
    token = serializers.CharField(required=True)

    user = None

    def validate(self, attrs):
        # derived from password reset confirm
        try:
            attrs["uid"] = str(HashIdConverter.decode(force_str(uid_decoder(attrs["uid"]))))
            self.user = UserModel._default_manager.select_related("org").get(pk=attrs["uid"])
        except (TypeError, ValueError, OverflowError, HashIdException, UserModel.DoesNotExist):
            raise serializers.ValidationError(_("Direct login link is expired."))

        if not email_magic_link_login_check.check_token(self.user, attrs["token"]):
            raise serializers.ValidationError(_("Direct login link is expired."))
        return attrs

    def save(self, **kwargs) -> UserModel:
        if self.user is None:
            raise Exception("Call is_valid before save.")
        return self.user

    class Meta:
        pass


class AdminBypassLoginSerialzer(DirectLoginSerializer):
    def validate(self, attrs):
        # derived from password reset confirm
        try:
            attrs["uid"] = str(HashIdConverter.decode(force_str(uid_decoder(attrs["uid"]))))
            self.user = UserModel._default_manager.select_related("org").get(pk=attrs["uid"])
        except (TypeError, ValueError, OverflowError, HashIdException, UserModel.DoesNotExist):
            raise serializers.ValidationError(_("Direct login link is expired."))

        if not admin_login_bypass_token_generator.check_token(self.user, attrs["token"]):
            raise serializers.ValidationError(_("Direct login link is expired."))
        return attrs


class AuthLoginSerializer(BaseSerializer):
    token = serializers.SerializerMethodField()
    hash = serializers.SerializerMethodField()
    profile = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    permission_list = serializers.ListField(child=serializers.CharField())
    data = serializers.SerializerMethodField()
    org_id = serializers.SerializerMethodField()
    timezone = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    tax_type = serializers.SerializerMethodField()
    tds_type = serializers.SerializerMethodField()
    country = serializers.SerializerMethodField()
    uid = serializers.SerializerMethodField()
    subscription_end_date = serializers.SerializerMethodField()
    show_banner_after_remaining_days = serializers.SerializerMethodField()

    def get_country(self, obj):
        user = self.context.get("user")
        if user is None:
            return None
        return CountrySerializer(user.org.country).data if user.org and user.org.country else None

    @swagger_serializer_method(serializer_or_field=ProfileSerializer())
    def get_profile(self, obj):
        return ProfileSerializer(obj.user, context=self.context).data

    @staticmethod
    def get_hash(obj):
        # TODO: Finalize hash logic with FE team and then update
        #  Use cache to save hash data
        return "d6MwBqLny0vV8JkK"

    def get_token(self, obj):
        # TODO: After uncommenting this line please add Token post signal to delete
        #  this entry from redis. Use user json against token key for saving and
        #  then retrieve user object from cache in BearerAuthentication.
        # CACHE.set_with_ttl(USER_TOKEN_KEY.format(obj.key), obj.user, USER_TOKEN_TTL_SECONDS)
        serializer = TokenDataSerializer(instance=obj, context=self.context).data
        return jwt.encode(serializer, infinite_valid=False, jwt_expiry_utc=self.context["jwt_expiry_utc"])

    def get_permissions(self, obj):
        if hasattr(obj, "permissions"):
            return obj.permissions
        return {}

    def get_data(self, obj):
        return {"approval_count": ApprovalCountSerializer(obj.user_approval_data).data}

    def get_org_id(self, obj):
        org_id = self.context.get("org_id")
        if org_id is None:
            return None
        return HashIdConverter.encode(org_id)

    def get_timezone(self, obj):
        return TimezoneData.drf_serializer(obj.timezone).data

    def get_currency(self, obj):
        if hasattr(obj, "currency"):
            return CurrencyData.drf_serializer(obj.currency).data
        return None

    def get_tax_type(self, obj):
        if hasattr(obj, "tax_type"):
            return TaxTypeData.drf_serializer(obj.tax_type).data
        return None

    def get_tds_type(self, obj):
        if hasattr(obj, "tds_type") and obj.tds_type:
            return TDSTypeData.drf_serializer(obj.tds_type).data
        return None

    def get_uid(self, obj):
        if hasattr(obj, "uid"):
            return obj.uid
        return None

    def get_subscription_end_date(self, obj):
        if hasattr(obj, "subscription_end_date"):
            return obj.subscription_end_date
        return None

    def get_show_banner_after_remaining_days(self, obj):
        if hasattr(obj, "show_banner_after_remaining_days"):
            return obj.show_banner_after_remaining_days
        return None

    class Meta:
        fields = (
            "token",
            "profile",
            "hash",
            "permissions",
            "data",
            "timezone",
            "currency",
            "tax_type",
            "country",
            "uid",
            "subscription_end_date",
            "show_banner_after_remaining_days",
        )


class AuthPermissionSerializer(serializers.Serializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    permissions = serializers.ListField()


class PasswordResetSerializer(serializers.Serializer):
    WEB = "web"
    APP = "app"
    OPTION_CHOICES = ((WEB, "web"), (APP, "app"))

    email = serializers.EmailField(required=True)
    source = serializers.ChoiceField(choices=OPTION_CHOICES, allow_blank=True, required=False)

    def send_mail(
        self, subject_template_name, email_template_name, context, from_email, to_email, html_email_template_name=None
    ):
        """
        Send a django.core.mail.EmailMultiAlternatives to `to_email`.
        """
        subject = loader.render_to_string(subject_template_name, context)
        # Email subject *must not* contain newlines
        subject = "".join(subject.splitlines())
        body = loader.render_to_string(email_template_name, context)
        email_message = EmailMultiAlternatives(subject, body, from_email, [to_email])
        if html_email_template_name is not None:
            html_email = loader.render_to_string(html_email_template_name, context)
            email_message.attach_alternative(html_email, "text/html")

        email_message.send()

    def get_user(self, email):
        """Given an email, return matching user who should receive a reset."""
        email_field_name = UserModel.get_email_field_name()
        try:
            user = UserModel._default_manager.get(**{"%s__iexact" % email_field_name: email})
        except ObjectDoesNotExist:
            raise NotRegisteredUser("Email does not exist.")
        if not user.is_active:
            raise InactiveUser("User is Inactive.")
        return user

    def save(
        self,
        site_name=None,
        subject_template_name="authentication/password_reset_subject.txt",
        email_template_name="authentication/password_reset_email.html",
        use_https=False,
        token_generator=default_token_generator,
        from_email=None,
        request=None,
        html_email_template_name=None,
        extra_email_context=None,
    ):
        """
        Generate a one-use only link for resetting password and send it to the
        user.
        """
        email = self.validated_data["email"]
        source = self.validated_data.get("source", "web")
        email_field_name = UserModel.get_email_field_name()
        user = self.get_user(email)
        user_email = getattr(user, email_field_name)
        uid = urlsafe_base64_encode(force_bytes(user.hashid))
        token = token_generator.make_token(user)
        link = f"{site_name}?uid={uid}&token={token}&name={user.first_name}"
        if source == "app":
            link = generate_dynamic_recce_link(recce_absolute_uri=link)
            if not link:
                raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Unable to create link")})
        context = {
            "email": user_email,
            "site_name": site_name,
            "user": user,
            "protocol": "https" if use_https else "http",
            "link": link,
            **(extra_email_context or {}),
        }
        self.send_mail(
            subject_template_name,
            email_template_name,
            context,
            from_email,
            user_email,
            html_email_template_name=html_email_template_name,
        )


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for confirming a password reset attempt.
    """

    new_password1 = serializers.CharField(max_length=128, required=True)
    new_password2 = serializers.CharField(max_length=128, required=True)
    uid = serializers.CharField(required=True)
    token = serializers.CharField(required=True)

    set_password_form_class = SetPasswordForm

    _errors = {}
    user = None
    set_password_form = None

    def custom_validation(self, attrs):
        pass

    def validate(self, attrs):
        # Decode the uidb64 (allauth use base36) to uid to get User object
        try:
            uid = force_str(uid_decoder(attrs["uid"]))
            self.user = UserModel._default_manager.get(pk=HashIdConverter.decode(uid))
        except (TypeError, ValueError, OverflowError, UserModel.DoesNotExist, HashIdException):
            raise serializers.ValidationError({"uid": [_("Password reset link is expired.")]})

        if not default_token_generator.check_token(self.user, attrs["token"]):
            raise serializers.ValidationError({"token": [_("Password reset link is expired.")]})

        self.custom_validation(attrs)
        # Construct SetPasswordForm instance
        self.set_password_form = self.set_password_form_class(
            user=self.user,
            data=attrs,
        )
        if not self.set_password_form.is_valid():
            raise serializers.ValidationError(self.set_password_form.errors)

        return attrs

    def save(self):
        return self.set_password_form.save()


class PhoneNumberSerializer(BaseSerializer):
    phone_number = serializerfields.PhoneNumberField()

    class Meta:
        ref_name = "PhoneNumberInput"


class TokenDataSerializer(BaseSerializer, TokenSerializer):
    org_id = serializers.SerializerMethodField()
    is_admin = serializers.SerializerMethodField()
    org_type = serializers.SerializerMethodField()
    org_observer = serializers.SerializerMethodField()
    is_app_token = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()

    def get_org_type(self, obj):
        org_user = self.context.get("org_user")
        if org_user:
            return org_user.organization.type
        return None

    def get_org_id(self, obj):
        org_user = self.context.get("org_user")
        if org_user:
            return org_user.organization_id
        return None

    def get_user_id(self, obj):
        user = self.context.get("user")
        return user.id

    def get_is_admin(self, obj):
        org_user = self.context.get("org_user")
        if org_user:
            return org_user.is_admin
        return False

    def get_org_observer(self, obj):
        org_observer = self.context.get("org_observer")
        if org_observer:
            return True
        return False

    def get_is_app_token(self, obj):
        return True if self.context.get("is_app_token") else False

    class Meta(TokenSerializer.Meta):
        fields = ("key", "org_id", "is_admin", "org_type", "org_observer", "is_app_token", "user_id")
        output_hash_id_fields = ("org_id", "user_id")
