from typing import List

from django.db.models.functions import Lower

from design.data.models import (
    DesignFileVersionTag,
)
from design.data.repositories import (
    DesignFileVersionTagMappingRepository,
    OrganizationDesignFileTagRepository,
    ProjectDesignFileTagRepository,
)
from design.data.selectors import (
    organization_design_file_tags_fetch_all,
    project_design_files_tags_for_dropdown_fetch_all,
)
from design.domain.entities import (
    DesignFileTagCreateUpdateEntity,
    DesignFileTagEntity,
    ProjectDesignFileTagEntity,
)
from design.domain.exceptions import (
    DesignFileTagExceptionConstants,
    DesignValidationException,
)


class OrganizationDesignFileTagService:
    """
    This service is responsible for creating, deleting and renaming the organization design file tags.
    1. Create: This service is called by organization design file tag create tag api,
        - this service will check if the tag already exists for organization
        - if tag already exists then it will raise an exception
        - this function will call the repository function to create the organization design file tag

    2. Delete: This service will delete the organization design file tag
        - this service will check if the tag exists for organization
        - if tag does not exist then it will raise an exception
        - this function will call the repository function to delete the organization design file tag
    3. Rename: This service will rename the organization design file tag
        - this service will check if the tag with new name exists for organization
        - if tag does exist then it will raise an exception
        - this function will delete the old organization design file tag and create the new one

    """

    class DesignFileTagAlreadyExists(DesignValidationException):
        pass

    class DesignFileTagDoesNotExist(DesignValidationException):
        pass

    def check_if_tag_exists(self, tag_name):
        return (
            organization_design_file_tags_fetch_all(organization_id=self.organization_id)
            .annotate(lower_name=Lower("name"))
            .filter(lower_name=tag_name.lower())
            .exists()
        )

    def __init__(self, organization_id: int, user_id: int):
        self.organization_id = organization_id
        self.user_id = user_id
        self.repository = OrganizationDesignFileTagRepository()

    def create(
        self,
        data: DesignFileTagCreateUpdateEntity,
    ):
        if self.check_if_tag_exists(tag_name=data.name):
            raise self.DesignFileTagAlreadyExists(DesignFileTagExceptionConstants.TAG_ALREADY_EXISTS.value)
        new_design_file_tag = self.repository.create_tag(
            tag_name=data.name, user_id=self.user_id, organization_id=self.organization_id
        )
        return new_design_file_tag

    def delete(self, tag_id: int):
        design_file_tag = (
            organization_design_file_tags_fetch_all(organization_id=self.organization_id).filter(id=tag_id).first()
        )
        if not design_file_tag:
            raise self.DesignFileTagDoesNotExist(DesignFileTagExceptionConstants.TAG_DOES_NOT_EXIST.value)

        if design_file_tag.is_active:
            self.repository.delete_active_tag(
                tag_id=design_file_tag.id,
                user_id=self.user_id,
                organization_id=self.organization_id,
            )
        else:
            self.repository.delete_tag(
                tag_id=design_file_tag.id, organization_id=self.organization_id, user_id=self.user_id
            )

        return True

    def rename(self, data: DesignFileTagCreateUpdateEntity, tag_id: int):
        org_tags_exists = (
            organization_design_file_tags_fetch_all(organization_id=self.organization_id)
            .annotate(lower_name=Lower("name"))
            .filter(lower_name=data.name.lower())
            .exclude(id=tag_id)
        ).exists()

        if org_tags_exists:
            raise self.DesignFileTagAlreadyExists(DesignFileTagExceptionConstants.TAG_ALREADY_EXISTS.value)

        self.delete(tag_id=tag_id)
        return self.create(data=data)


class ProjectDesignFileTagService:
    """
    This service will create the entities and call the repository layer to create the design file tags
    and mappings and return the list of tag id's created.

    """

    def __init__(self, project_id: int, user_id: int, organization_id: int):
        self.project_id = project_id
        self.user_id = user_id
        self.organization_id = organization_id
        self.repository = ProjectDesignFileTagRepository()

    def create(self, data: List[DesignFileTagCreateUpdateEntity]):
        return self.repository.create_tags(
            data=ProjectDesignFileTagEntity(
                project_id=self.project_id,
                organization_id=self.organization_id,
                created_by_id=self.user_id,
                design_file_tag_list=[DesignFileTagCreateUpdateEntity(name=tag.name) for tag in data],
            )
        )


class DesignFileVersionTagMappingService:
    """
    - This service is responsible for creating and deleting the design file version tag mapping for design file version.
    - This service will also call the project design file tag service to create the design file tags if not exists.
    - It will calculate the list of tags to be created and deleted and
    call the repository function to create and delete the mappings.

    1.  Create -
        this function segregate the incoming tags which are to be created and which are already created.
         - if the tags which are to be created are already exists then it will raise an exception.
         - this function will call the project design file tag service to create the design file tags.
        - this function will call the repository function to create the design file version tag mapping.

    2. Update -
        -this function will get the existing design_file_tags associated with design file version.
        -will check for the tags to be added and removed,and also to be created.
        -this function will call the repository function to delete the design file version tag mapping.
        -this function will call the repository function to create the design file version tag mapping.
    """

    class DesignFileTagAlreadyExists(DesignValidationException):
        pass

    def __init__(
        self,
        project_id: int,
        organization_id: int,
        user_id: int,
        design_file_version_id: int,
    ):
        self.organization_id = organization_id
        self.user_id = user_id

        self.project_id = project_id
        self.design_file_version_id = design_file_version_id
        self.repository = DesignFileVersionTagMappingRepository(
            design_file_version_id=design_file_version_id, user_id=user_id
        )

    def _segregate(
        self,
        tags: list[DesignFileTagEntity],
    ) -> (List[int], List[str]):
        tag_id_list, tags_to_create = (
            [tag.id for tag in tags if tag.id is not None],
            [tag.name for tag in tags if tag.id is None],
        )
        return tag_id_list, tags_to_create

    def _get_project_design_file_tags(self):
        return project_design_files_tags_for_dropdown_fetch_all(
            project_id=self.project_id, organization_id=self.organization_id
        )

    def _create_design_file_tag_assignment(self, tag_id_list: list[int]):
        self.repository.create(tag_id_list=tag_id_list)

    def create(
        self,
        tags: list[DesignFileTagEntity],
    ):
        if not tags:
            return
        tag_id_list, tags_to_create = self._segregate(tags=tags)
        valid_tags_id_list = []
        created_tags_id_list = []
        design_file_tags = self._get_project_design_file_tags().values("id", "name")
        if tag_id_list:
            tag_id_set = set(tag_id_list)
            valid_tags_id_list = [tag["id"] for tag in design_file_tags if tag["id"] in tag_id_set]
        if tags_to_create:
            intersect = set(tag.lower() for tag in tags_to_create) & set(
                tag["name"].lower() for tag in design_file_tags
            )

            tag_name_to_id = {tag["name"].lower(): tag["id"] for tag in design_file_tags}

            # Remove intersecting tags from `tags_to_create` and add their IDs to valid_tags_id_list
            tags_to_create = [tag for tag in tags_to_create if tag.lower() not in intersect]
            valid_tags_id_list.extend(tag_name_to_id[tag_name] for tag_name in intersect)

            service = ProjectDesignFileTagService(
                project_id=self.project_id, user_id=self.user_id, organization_id=self.organization_id
            )
            entity_list = [DesignFileTagCreateUpdateEntity(name=tag) for tag in tags_to_create]
            created_tags_id_list = service.create(data=entity_list)

        self._create_design_file_tag_assignment(tag_id_list=valid_tags_id_list + created_tags_id_list)

    def _delete(self, tag_id_list: List[int]):
        self.repository.delete(tag_id_list=tag_id_list)

    def update(self, tags: list[DesignFileTagEntity]):
        design_file_version_tags = DesignFileVersionTag.objects.filter(
            design_file_version_id=self.design_file_version_id,
        ).available()
        existing_tags = set(design_file_version_tags.values_list("design_file_tag_id", flat=True))
        input_tags_set = set([tag.id for tag in tags if tag.id is not None])
        tags_to_add = input_tags_set.difference(existing_tags)
        tags_to_remove = list(existing_tags.difference(input_tags_set))
        self._delete(tag_id_list=tags_to_remove)
        tags = [tag for tag in tags if (tag.id in tags_to_add) or (tag.id is None)]
        self.create(tags=tags)
