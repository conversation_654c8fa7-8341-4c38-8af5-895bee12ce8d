from functools import partial
from logging import getLogger

from django.conf import settings
from django.db import transaction
from django.db.models import Count, OuterRef, Prefetch, Subquery
from django.db.transaction import on_commit
from django.shortcuts import get_object_or_404
from django.utils.module_loading import import_string
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from authorization.domain.constants import Permissions
from common.apis import BaseApi
from common.serializers import BaseSerializer, HashIdListField
from design.data.models import (
    DesignFile,
    DesignFileVersion,
    DesignFileVersionTag,
    DesignRepository,
)
from design.data.models.selectors import (
    design_approved_file_fetch_all,
    design_file_version_fetch,
    design_files_list,
    get_design_file_uploaded_by_user_list,
)
from design.domain.entities import DesignFileTagEntity
from design.domain.services import (
    DesignFileVersionTagMappingService,
    design_file_status_history_create,
    design_file_uploaded_trigger_event,
    design_new_version_uploaded_trigger_event,
    design_status_get,
    get_design_repository,
    save_design_file,
    save_design_version_file,
)
from design.interface.serializers import (
    DesignFileUpdateSerializer,
    DesignFileUploadSerializer,
    DesignFileVersionOutputSerializer,
    DesignFileVersionOutputSerializerNoUploadedBy,
    DesignFileVersionSerializer,
    DesignFileVersionUploadSerializer,
    DesignLinkUploadSerializer,
    DesignLinkVersionUploadSerializer,
    DesignRepositoryModelSerializer,
)
from microcontext.domain.constants import MicroContext
from project.domain.helpers import ProjectPermissionHelper
from project.domain.services import ProjectStatusUpdateService
from project.domain.status import DesignStatus, Module
from project.interface.apis.internal.apis import ProjectBaseApi
from rollingbanners.comment_base_service import CommentBaseService

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


logger = getLogger(__name__)


class DesignFileUploadedBydUserListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class DesignFileUserListSerializer(DesignFileVersionSerializer):
        class Meta(DesignFileVersionSerializer.Meta):
            fields = ("uploaded_by",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: DesignFileUserListSerializer()},
        operation_id="design_file_uploaded_by_user_list",
        operation_summary="Fetch All User List who uploaded by",
    )
    def get(self, request, project_id):
        users = get_design_file_uploaded_by_user_list(project_id=project_id)
        return Response(
            [data.get("uploaded_by") for data in self.DesignFileUserListSerializer(users, many=True).data],
            status=HTTP_200_OK,
        )


class DesignFileListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: DesignFileVersionOutputSerializer()},
        operation_summary="Fetch design file list using project_id.",
    )
    def get(self, request, project_id):
        logger.info("DesignFileListApi GET called")
        logger.debug(f"project_id : {project_id}")
        get_design_repository(project_id=project_id)

        permissions = ProjectPermissionHelper.get_permissions(project_id=project_id, user=request.user)
        files = design_files_list(
            project_id=project_id, organization_id=self.get_organization_id(), permissions=permissions
        )

        return Response(
            DesignFileVersionOutputSerializer(files, many=True, context=self.get_serializer_context()).data,
            status=HTTP_200_OK,
        )


class DesignApprovedFileListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignFileVersionOutputSerializer):
        class Meta(DesignFileVersionOutputSerializer.Meta):
            ref_name = "DesignApprovedFileListOutput"
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "file_size",
                "version",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_summary="Fetch approved design file list using project_id.",
    )
    def get(self, request, project_id, *args, **kwargs):
        get_design_repository(project_id=project_id)
        files = design_approved_file_fetch_all(project_id=project_id, organization_id=self.get_organization_id())
        return Response(self.OutputSerializer(files, many=True).data, status=HTTP_200_OK)


class DesignFileGetApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignFileVersionOutputSerializer):
        class Meta(DesignFileVersionOutputSerializer.Meta):
            ref_name = "DesignFileOutput"
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "file_size",
                "version",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_design_file_get",
        operation_summary="Get Design File Using File ID",
    )
    def get(self, request, project_id, file_id):
        file = (
            DesignFileVersion.objects.select_related(
                "design_file",
                "design_file__design_section",
            )
            .prefetch_related(
                Prefetch(
                    "version_tags", queryset=DesignFileVersionTag.objects.select_related("design_file_tag").available()
                )
            )
            .annotate_comment_count(org_id=self.get_organization_id())
            .filter(pk=file_id)
            .order_by("-uploaded_at")
        ).first()

        return Response(self.OutputSerializer(file).data, status=HTTP_200_OK)


class DesignApprovedVersionListApi(BaseApi):
    class OutputSerializer(DesignFileVersionOutputSerializer):
        class Meta(DesignFileVersionOutputSerializer.Meta):
            ref_name = "DesignApprovedVersionListOutput"
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "file_size",
                "version",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_summary="Fetch approved design version list using project_id.",
    )
    def get(self, request, project_id, file_id):
        design_repository: DesignRepository = get_design_repository(project_id=project_id)
        file_version: DesignFileVersion = (
            DesignFileVersion.objects.filter(pk=file_id, design_file__design_repository=design_repository)
            .available()
            .first()
        )
        file_versions = []
        if file_version:
            file_versions = (
                DesignFileVersion.objects.select_related("design_file", "design_file__design_section")
                .filter(design_file_id=file_version.design_file_id)
                .prefetch_related(
                    Prefetch(
                        "version_tags",
                        queryset=DesignFileVersionTag.objects.select_related("design_file_tag").available(),
                    )
                )
                .available()
                .filter(status=DesignFileVersion.StatusChoices.APPROVED.value)
                .exclude(pk=file_version.pk)
                .annotate_comment_count(org_id=self.get_organization_id())
                .order_by("-version")
            )
        return Response(self.OutputSerializer(file_versions, many=True).data, status=HTTP_200_OK)


class DesignFileUploadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    parser_classes = (MultiPartParser,)

    tags = openapi.Parameter(
        "tags",
        openapi.IN_FORM,
        description="Tag list",
        type=openapi.TYPE_ARRAY,
        items=openapi.Items(type=openapi.TYPE_OBJECT),
        default=[{"id": "J6vVDpMpDMWPoYEe", "name": "Tag Name"}],
    )

    @swagger_auto_schema(
        request_body=DesignFileUploadSerializer,
        manual_parameters=[tags],
        responses={HTTP_201_CREATED: DesignFileVersionOutputSerializerNoUploadedBy()},
        operation_summary="Upload design file in project's design repository.",
    )
    @transaction.atomic
    def post(self, request, project_id):
        logger.info("DesignFileUploadApi POST called")
        logger.debug(f"project_id : {project_id}")
        design_repository = get_design_repository(project_id=project_id)
        design_repository.unfreeze(request.user.pk)
        data = request.data
        logger.debug(f"raw data : {data}")
        serializer = DesignFileUploadSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        logger.debug(f"validated data : {serializer.validated_data}")
        design_file = save_design_file(
            design_repository=design_repository,
            data=serializer.design_file_data,
        )
        design_file_version = save_design_version_file(
            design_file=design_file, user=request.user, data=serializer.design_file_version_data
        )
        design_file_tags = [DesignFileTagEntity(id=tag["id"], name=tag["name"]) for tag in serializer.data["tags"]]
        if design_file_tags:
            DesignFileVersionTagMappingService(
                project_id=project_id,
                user_id=request.user.pk,
                organization_id=self.get_organization_id(),
                design_file_version_id=design_file_version.id,
            ).create(tags=design_file_tags)
        design_file_status_history_create(
            file_id=design_file_version.id,
            status=DesignFileVersion.StatusChoices.SUBMITTED.value,
            user_id=request.user.pk,
        )

        design_file_uploaded_trigger_event(
            file_id=design_file.pk,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            file_version_id=design_file_version.pk,
            project_id=project_id,
        )

        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=DesignStatus.DESIGN_REVIEW.value,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )

        design_file_version = design_file_version_fetch(file_version_id=design_file_version.id)

        return Response(
            DesignFileVersionOutputSerializerNoUploadedBy(design_file_version).data, status=HTTP_201_CREATED
        )


class DesignLinkUploadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        request_body=DesignLinkUploadSerializer,
        responses={HTTP_201_CREATED: DesignFileVersionOutputSerializerNoUploadedBy()},
        operation_summary="Upload design link in project's design repository.",
    )
    @transaction.atomic
    def post(self, request, project_id):
        logger.info("DesignLinkUploadApi POST called")
        logger.debug(f"project_id : {project_id}")
        design_repository = get_design_repository(project_id=project_id)
        design_repository.unfreeze(request.user.pk)
        data = request.data.copy()
        logger.debug(f"raw data : {data}")
        serializer = DesignLinkUploadSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        logger.debug(f"validated data : {serializer.validated_data}")
        design_file = save_design_file(design_repository=design_repository, data=serializer.design_file_data)
        design_file_version = save_design_version_file(
            design_file=design_file, user=request.user, data=serializer.design_file_version_data
        )
        design_file_tags = [DesignFileTagEntity(id=tag["id"], name=tag["name"]) for tag in serializer.data["tags"]]
        if design_file_tags:
            DesignFileVersionTagMappingService(
                project_id=project_id,
                user_id=request.user.pk,
                organization_id=self.get_organization_id(),
                design_file_version_id=design_file_version.id,
            ).create(tags=design_file_tags)

        design_file_status_history_create(
            file_id=design_file_version.id,
            status=DesignFileVersion.StatusChoices.SUBMITTED.value,
            user_id=request.user.pk,
        )

        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=DesignStatus.DESIGN_REVIEW.value,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )
        design_file_version = design_file_version_fetch(file_version_id=design_file_version.id)

        return Response(
            DesignFileVersionOutputSerializerNoUploadedBy(design_file_version).data, status=HTTP_201_CREATED
        )


class DesignFileVersionUploadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = DesignFileVersionUploadSerializer
    parser_classes = (MultiPartParser,)

    tags = openapi.Parameter(
        "tags",
        openapi.IN_FORM,
        description="Tag list",
        type=openapi.TYPE_ARRAY,
        items=openapi.Items(type=openapi.TYPE_OBJECT),
        default=[{"id": "J6vVDpMpDMWPoYEe", "name": "Tag Name"}],
    )

    @swagger_auto_schema(
        request_body=DesignFileVersionUploadSerializer,
        manual_parameters=[tags],
        responses={HTTP_201_CREATED: DesignFileVersionOutputSerializerNoUploadedBy()},
        operation_summary="Upload design file version in project's design repository.",
    )
    @transaction.atomic
    def post(self, request, project_id, file_id):
        logger.info("DesignFileVersionUploadApi POST called")
        logger.debug(f"project_id : {project_id}")
        design_repository = get_design_repository(project_id=project_id)
        design_repository.unfreeze(request.user.pk)
        file_version: DesignFileVersion = get_object_or_404(
            DesignFileVersion.objects.select_related("design_file").filter(pk=file_id).available(),
            design_file__design_repository=design_repository,
        )
        logger.debug(f"file_version : {file_version}")
        DesignFileVersion.objects.filter(design_file=file_version.design_file, is_latest=True).update(is_latest=False)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.design_file_version_data
        data.update({"version": DesignFileVersion.objects.filter(design_file=file_version.design_file).count() + 1})
        design_file_version = save_design_version_file(
            design_file=file_version.design_file, user=request.user, data=data
        )

        design_file_tags = [DesignFileTagEntity(id=tag["id"], name=tag["name"]) for tag in serializer.data["tags"]]
        if design_file_tags:
            DesignFileVersionTagMappingService(
                project_id=project_id,
                user_id=request.user.pk,
                organization_id=self.get_organization_id(),
                design_file_version_id=design_file_version.id,
            ).create(tags=design_file_tags)

        design_file_status_history_create(
            file_id=design_file_version.id,
            status=DesignFileVersion.StatusChoices.SUBMITTED.value,
            user_id=request.user.pk,
        )
        design_file_uploaded_trigger_event(
            file_id=file_version.design_file.pk,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            file_version_id=design_file_version.pk,
            project_id=project_id,
        )
        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=DesignStatus.DESIGN_REVIEW.value,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )

        on_commit(
            partial(
                design_new_version_uploaded_trigger_event,
                project_id=project_id,
                user=request.user,
                design_file_name=design_file_version.design_file.name,
                file_id=design_file_version.pk,
                file_version=file_version.version + 1,
                file_name=file_version.design_file.name,
            )
        )
        design_file_version = design_file_version_fetch(file_version_id=design_file_version.id)

        return Response(
            DesignFileVersionOutputSerializerNoUploadedBy(design_file_version).data, status=HTTP_201_CREATED
        )


class DesignLinkVersionUploadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = DesignLinkVersionUploadSerializer

    @swagger_auto_schema(
        request_body=DesignLinkVersionUploadSerializer,
        responses={HTTP_201_CREATED: DesignFileVersionOutputSerializerNoUploadedBy()},
        operation_summary="Upload design link in project's design repository.",
    )
    @transaction.atomic
    def post(self, request, project_id, file_id):
        logger.info("DesignLinkUploadApi POST called")
        logger.debug(f"project_id : {project_id}")
        design_repository = get_design_repository(project_id=project_id)
        design_repository.unfreeze(request.user.pk)
        file_version: DesignFileVersion = get_object_or_404(
            DesignFileVersion.objects.select_related("design_file").filter(pk=file_id).available(),
            design_file__design_repository=design_repository,
        )
        logger.debug(f"file_version : {file_version}")
        DesignFileVersion.objects.filter(design_file=file_version.design_file, is_latest=True).update(is_latest=False)
        serializer = self.get_serializer(data=request.data.copy())
        serializer.is_valid(raise_exception=True)
        data = serializer.design_file_version_data
        data.update({"version": DesignFileVersion.objects.filter(design_file=file_version.design_file).count() + 1})
        design_file_version = save_design_version_file(
            design_file=file_version.design_file, user=request.user, data=data
        )
        design_file_tags = [DesignFileTagEntity(id=tag["id"], name=tag["name"]) for tag in serializer.data["tags"]]
        if design_file_tags:
            DesignFileVersionTagMappingService(
                project_id=project_id,
                user_id=request.user.pk,
                organization_id=self.get_organization_id(),
                design_file_version_id=design_file_version.id,
            ).create(tags=design_file_tags)
        design_file_status_history_create(
            file_id=design_file_version.id,
            status=DesignFileVersion.StatusChoices.SUBMITTED.value,
            user_id=request.user.pk,
        )
        design_file_uploaded_trigger_event(
            file_id=file_version.design_file.pk,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            file_version_id=design_file_version.pk,
            project_id=project_id,
        )
        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=DesignStatus.DESIGN_REVIEW.value,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )

        on_commit(
            partial(
                design_new_version_uploaded_trigger_event,
                project_id=project_id,
                user=request.user,
                design_file_name=design_file_version.design_file.name,
                file_id=file_id,
                file_version=file_version.version,
                file_name=file_version.design_file.name,
            )
        )

        design_file_version = design_file_version_fetch(file_version_id=design_file_version.id)
        return Response(
            DesignFileVersionOutputSerializerNoUploadedBy(design_file_version).data, status=HTTP_201_CREATED
        )


class DesignFileVersionGetApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignFileVersionSerializer):
        class Meta(DesignFileVersionSerializer.Meta):
            ref_name = "DesignFileVersionGetOutput"
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "version",
                "status",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    serializer_class = DesignFileUpdateSerializer

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_summary="View design file.")
    @transaction.atomic
    def get(self, request, project_id, file_id):
        logger.info("DesignFileVersionApi GET called")
        logger.debug(f"project_id : {project_id}")
        design_repository = get_design_repository(project_id=project_id)
        file_version: DesignFileVersion = get_object_or_404(
            DesignFileVersion.objects.select_related("design_file")
            .annotate_comment_count(org_id=self.get_organization_id())
            .filter(pk=file_id)
            .available(),
            design_file__design_repository=design_repository,
        )
        logger.debug(f"file_version : {file_version}")
        return Response(self.OutputSerializer(file_version).data, status=HTTP_200_OK)


class DesignFileVersionDeleteApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignFileVersionSerializer):
        class Meta(DesignFileVersionSerializer.Meta):
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "version",
                "status",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    serializer_class = DesignFileUpdateSerializer

    @swagger_auto_schema(responses={HTTP_200_OK: "OK"}, operation_summary="Delete design file.")
    @transaction.atomic
    def delete(self, request, project_id, file_id):
        if not ProjectPermissionHelper.has_permission(
            user=request.user, project_id=project_id, permission=Permissions.CAN_EDIT_DESIGN_FILES.value
        ):
            raise PermissionDenied()
        logger.debug(f"pk : {project_id}, file_pk : {file_id}")
        design_repository = get_design_repository(project_id=project_id)
        file_version: DesignFileVersion = get_object_or_404(
            DesignFileVersion.objects.select_related("design_file").filter(pk=file_id).available(),
            design_file__design_repository=design_repository,
        )

        if file_version.status == DesignFileVersion.StatusChoices.APPROVED.value:
            self.set_response_message("Approved file can not be deleted.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Approved file can not be deleted."})

        logger.debug(f"file_version : {file_version}")
        file_version.soft_delete(request.user.id)

        CommentHelperService.archive(
            context=MicroContext.DESIGN_FILE.value,
            context_id=file_id,
            user_id=request.user.pk,
        )

        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=design_status_get(project_id=project_id),
            organization_id=request.user.token_data.org_id,
            user_id=request.user.pk,
        )

        if file_version.is_latest:
            latest_file_version: DesignFileVersion = (
                DesignFileVersion.objects.select_related("design_file")
                .prefetch_related("tags")
                .annotate_comment_count(org_id=self.get_organization_id())
                .filter(design_file__id=file_version.design_file_id)
                .exclude(pk=file_version.pk)
                .available()
                .order_by("-uploaded_at")
                .first()
            )
            logger.debug(f"latest_file_version : {latest_file_version}")
            if latest_file_version:
                file_version.is_latest = False
                file_version.save(update_fields=["is_latest"])
                latest_file_version.is_latest = True
                latest_file_version.save(update_fields=["is_latest"])
                if latest_file_version.status != DesignFileVersion.StatusChoices.APPROVED:
                    design_repository.unfreeze(request.user.pk)
                return Response(
                    DesignFileVersionOutputSerializer(latest_file_version, context=self.get_serializer_context()).data,
                    status=HTTP_200_OK,
                )
        return Response(status=HTTP_200_OK)


class DesignFileDeleteManyApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        file_id_list = HashIdListField()

        class Meta:
            ref_name = "ProjectDesignDeleteManyInputSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="project_design_file_delete_many_api",
        operation_summary="Project design file delete many API",
    )
    @transaction.atomic
    def delete(self, request, project_id, *args, **kwargs):
        if not ProjectPermissionHelper.has_permission(
            user=request.user, project_id=project_id, permission=Permissions.CAN_EDIT_DESIGN_FILES.value
        ):
            raise PermissionDenied()
        logger.debug(f"pk : {project_id}")
        data = self.validate_input_data()
        file_id_list = data.get("file_id_list")

        design_file_versions = DesignFileVersion.objects.filter(
            id__in=file_id_list, design_file__design_repository_id=project_id, is_latest=True
        ).available()

        if design_file_versions.filter(status=DesignFileVersion.StatusChoices.APPROVED.value).exists():
            self.set_response_message("Approved file can not be deleted.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Approved file can not be deleted."})

        design_file_id_list = list(design_file_versions.values_list("id", flat=True))

        available_version_ids = list(
            (
                DesignFile.objects.filter(id__in=design_file_versions.values_list("design_file_id", flat=True))
                .available()
                .annotate(
                    available_version_id=Subquery(
                        DesignFileVersion.objects.filter(design_file_id=OuterRef("id"))
                        .available()
                        .exclude(is_latest=True)
                        .order_by("-uploaded_at")
                        .values("id")[:1]
                    )
                )
            )
            .filter(available_version_id__isnull=False)
            .values_list("available_version_id", flat=True)
        )

        available_file_version = DesignFileVersion.objects.filter(id__in=available_version_ids)

        if available_file_version.exclude(status=DesignFileVersion.StatusChoices.APPROVED.value).exists():
            design_repository = get_design_repository(project_id=project_id)
            design_repository.unfreeze(request.user.pk)

        design_file_versions.soft_delete(user_id=request.user.pk)
        available_file_version.update(is_latest=True)

        CommentHelperService.archive_many(
            {MicroContext.DESIGN_FILE.value: design_file_id_list}, user_id=request.user.pk
        )

        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.DESIGN.value,
            status=design_status_get(project_id=project_id),
            organization_id=request.user.token_data.org_id,
            user_id=request.user.pk,
        )

        return Response(status=HTTP_200_OK)


class DesignFileVersionUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignFileVersionSerializer):
        class Meta(DesignFileVersionSerializer.Meta):
            fields = (
                "id",
                "name",
                "type",
                "section",
                "tags",
                "file",
                "version",
                "status",
                "comment_count",
                "uploaded_at",
            )
            output_hash_id_fields = ("id",)

    serializer_class = DesignFileUpdateSerializer

    @swagger_auto_schema(
        request_body=DesignFileUpdateSerializer, responses={HTTP_200_OK: "OK"}, operation_summary="Update design file."
    )
    @transaction.atomic
    def put(self, request, project_id, file_id):
        if not ProjectPermissionHelper.has_permission(
            user=request.user, project_id=project_id, permission=Permissions.CAN_EDIT_DESIGN_FILES.value
        ):
            raise PermissionDenied()
        serializer = self.get_serializer(data=request.data.copy())
        serializer.is_valid(raise_exception=True)
        logger.debug(f"data : {serializer.validated_data}")
        logger.debug(f"pk : {project_id}, file_pk : {file_id}")
        design_repository = get_design_repository(project_id=project_id)
        file_version: DesignFileVersion = get_object_or_404(
            DesignFileVersion.objects.select_related("design_file").filter(pk=file_id).available(),
            design_file__design_repository=design_repository,
        )
        logger.debug(f"file_version : {file_version}")
        design_file_tags = [DesignFileTagEntity(id=tag["id"], name=tag["name"]) for tag in serializer.data["tags"]]
        if design_file_tags:
            DesignFileVersionTagMappingService(
                project_id=project_id,
                user_id=request.user.pk,
                organization_id=self.get_organization_id(),
                design_file_version_id=file_version.id,
            ).update(tags=design_file_tags)

        file_version.design_file.name = serializer.data.get("name")
        file_version.design_file.save()
        design_file_version = design_file_version_fetch(file_version_id=file_version.id)

        return Response(DesignFileVersionOutputSerializerNoUploadedBy(design_file_version).data, status=HTTP_200_OK)


class DesignRepositoryStatusApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(DesignRepositoryModelSerializer):
        class Meta(DesignRepositoryModelSerializer.Meta):
            ref_name = "DesignRepositoryStatusOutputSerializer"
            fields = ("id", "status")
            output_hash_id_fields = ("id",)

    serializer_class = OutputSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "project_id"
    queryset = DesignRepository.objects.all()

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()}, operation_summary="View design status.", deprecated=True
    )
    def get(self, request, *args, **kwargs):
        instance: DesignRepository = self.get_object()
        if instance.is_frozen:
            return Response({"id": instance.hashid, "status": DesignRepository.StatusChoices.DESIGNS_FROZEN})
        status_query = (
            DesignFileVersion.objects.values("status")
            .annotate(total=Count("status"))
            .filter(design_file__design_repository=instance.pk, is_latest=True)
            .available()
        )
        statuses = {status.get("status"): status.get("total") for status in status_query}
        if not len(statuses):
            return Response({"id": instance.hashid, "status": DesignRepository.StatusChoices.DESIGNS_PENDING})
        if DesignFileVersion.StatusChoices.SUBMITTED in statuses and statuses.get(
            DesignFileVersion.StatusChoices.SUBMITTED
        ):
            return Response({"id": instance.hashid, "status": DesignRepository.StatusChoices.APPROVAL_PENDING})
        if DesignFileVersion.StatusChoices.DISAPPROVED in statuses and statuses.get(
            DesignFileVersion.StatusChoices.DISAPPROVED
        ):
            return Response({"id": instance.hashid, "status": DesignRepository.StatusChoices.APPROVAL_PENDING})

        ProjectStatusUpdateService.process(
            project_id=instance.pk,
            module=Module.DESIGN.value,
            status=DesignStatus.DESIGN_APPROVED.value,
            organization_id=request.user.token_data.org_id,
            user_id=request.user.pk,
        )

        return Response({"id": instance.hashid, "status": DesignRepository.StatusChoices.DESIGNS_APPROVED})
