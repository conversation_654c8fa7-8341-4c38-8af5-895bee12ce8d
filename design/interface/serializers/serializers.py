from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from common.choices import SourceChoices
from common.serializers import BaseModelSerializer, BaseSerializer, HashIdField, HashIdSerializerMixin
from common.utils import filter_dict_using_keys
from core.serializers import UserModelSerializer
from design.data.models import DesignFile, DesignFileTag, DesignFileVersion, DesignRepository, ProjectDesignSection
from design.interface.validators import design_file_tag_name_validator
from project.domain.entities import ProjectSharedDataEntity


class DesignRepositoryModelSerializer(BaseModelSerializer):
    status = serializers.ChoiceField(choices=DesignRepository.StatusChoices)

    class Meta:
        model = DesignRepository
        fields = "__all__"


class DesignFileTagSerializer(serializers.ModelSerializer, HashIdSerializerMixin):
    class Meta:
        model = DesignFileTag
        fields = ("id", "name")
        output_hash_id_fields = ["id"]
        input_hash_id_fields = ["id"]


class DesignFileInputTagSerializer(serializers.ModelSerializer, HashIdSerializerMixin):
    id = serializers.CharField()

    class Meta:
        model = DesignFileTag
        fields = ("name", "id")
        output_hash_id_fields = ["id"]
        input_hash_id_fields = ["id"]


class DesignFileTagInputSerializer(BaseSerializer):
    id = serializers.CharField(allow_null=True)
    name = serializers.CharField(required=True, max_length=50, validators=[design_file_tag_name_validator])

    class Meta:
        input_hash_id_fields = ["id"]
        output_hash_id_fields = ["id"]
        ref_name = "DesignFileTagInputSerializer"


class DesignFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = DesignFile
        fields = "__all__"


class DesignFileVersionSerializer(BaseModelSerializer):
    class SectionSerializer(BaseModelSerializer):
        class Meta:
            model = ProjectDesignSection
            fields = ("id", "name")

    class UploadedBySerializer(UserModelSerializer):
        def get_organization_name(self, obj):
            project_shared_data: ProjectSharedDataEntity | None = self.context.get("project_shared_data", None)

            if not project_shared_data:
                return super().get_organization_name(obj)

            if obj.org.id in project_shared_data.clients:
                return project_shared_data.clients[obj.org.id].name
            elif obj.org.id in project_shared_data.vendors:
                return project_shared_data.vendors[obj.org.id].name
            return super().get_organization_name(obj)

        class Meta(UserModelSerializer.Meta):
            ref_name = "DesignFileVersionSerializerUploadedBy"
            fields = ("id", "name", "photo", "organization_name")

    name = serializers.CharField(source="design_file.name")
    # section = serializers.CharField(source="design_file.section")
    section = serializers.SerializerMethodField()
    # type = serializers.CharField(source="design_file.type")
    section_name = serializers.CharField(source="design_section.name")
    tags = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    uploaded_by = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    def get_status(self, obj):
        if obj.status == DesignFileVersion.StatusChoices.SUBMITTED:
            return {"id": obj.status, "name": "Draft", "color_code": "#ea9245"}
        elif obj.status == DesignFileVersion.StatusChoices.APPROVED:
            return {"id": obj.status, "name": "Approved", "color_code": "#61c7cd"}
        elif obj.status == DesignFileVersion.StatusChoices.DISAPPROVED:
            return {"id": obj.status, "name": "Rejected", "color_code": "#f37a7d"}
        elif obj.status == DesignFileVersion.StatusChoices.REVIEWED:
            return {"id": obj.status, "name": "Reviewed", "color_code": "#5da0d2"}
        elif obj.status == DesignFileVersion.StatusChoices.CLIENT_REJECTED:
            return {"id": obj.status, "name": "Client Rejected", "color_code": "#f37a7d"}

    @swagger_serializer_method(serializer_or_field=serializers.DictField())
    def get_section(self, obj):
        return self.SectionSerializer(obj.design_file.design_section).data

    def get_tags(self, obj):
        return DesignFileTagSerializer(
            [version_tag.design_file_tag for version_tag in obj.version_tags.all()], many=True
        ).data

    @swagger_serializer_method(serializer_or_field=UploadedBySerializer())
    def get_uploaded_by(self, obj):
        return self.UploadedBySerializer(obj.uploaded_by).data

    @swagger_serializer_method(serializer_or_field=UploadedBySerializer())
    def get_updated_by(self, obj):
        return self.UploadedBySerializer(obj.updated_by).data if obj.updated_by else None

    def get_file_size(self, obj):
        if (
            obj.type == DesignFileVersion.TYPE_LINK
            or self.context.get("view") is None
            or self.context.get("view").get_source() == SourceChoices.WEB
        ):
            return 0
        return round(obj.file.size / 1000000, 2)

    @staticmethod
    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_comment_count(obj):
        return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

    class Meta:
        model = DesignFileVersion
        fields = "__all__"
        output_hash_id_fields = ["id"]
        input_hash_id_fields = ["id"]


class DesignFileTagValidateMixin:
    def validate_tags(self, value):
        serializer = DesignFileTagInputSerializer(data=value, many=True)
        if not serializer.is_valid(raise_exception=False):
            raise serializers.ValidationError("Invalid tags data.")
        return value


class DesignFileMixinSerializer(DesignFileTagValidateMixin):
    @property
    def design_file_data(self) -> dict:
        keys = ["name", "section_id"]
        data = filter_dict_using_keys(self.validated_data, keys)
        data["design_section_id"] = data.pop("section_id")
        return data

    @property
    def design_file_version_data(self) -> dict:
        keys = ["file", "type"]
        return filter_dict_using_keys(self.validated_data, keys)


class DesignFileUploadSerializer(serializers.Serializer, DesignFileMixinSerializer):
    name = serializers.CharField(max_length=100)
    type = serializers.ChoiceField(choices=DesignFileVersion.TYPE_CHOICES)
    section_id = HashIdField()
    file = serializers.FileField(max_length=2000)
    tags = serializers.JSONField(default=[])


class DesignLinkUploadSerializer(DesignFileUploadSerializer):
    file = serializers.URLField()


# TODO: remove DesignFileVersionSerializer inheritance
class DesignFileVersionUploadSerializer(DesignFileVersionSerializer, DesignFileMixinSerializer):
    tags = serializers.JSONField()
    type = serializers.ChoiceField(choices=DesignFileVersion.TYPE_CHOICES)

    class Meta(DesignFileVersionSerializer.Meta):
        fields = ("file", "tags", "type")


class DesignLinkVersionUploadSerializer(DesignFileVersionUploadSerializer):
    file = serializers.URLField()

    class Meta(DesignFileVersionUploadSerializer.Meta):
        fields = ("file", "tags", "type")


class DesignFileUpdateSerializer(serializers.Serializer, DesignFileTagValidateMixin):
    name = serializers.CharField(max_length=100)
    tags = serializers.JSONField()


class DesignFileVersionOutputSerializer(DesignFileVersionSerializer):
    class Meta(DesignFileVersionSerializer.Meta):
        fields = (
            "id",
            "name",
            "type",
            "section",
            "tags",
            "file",
            "version",
            "status",
            "comment_count",
            "uploaded_at",
            "uploaded_by",
            "file_size",
            "updated_by",
            "updated_at",
        )
        output_hash_id_fields = ("id",)


class DesignFileVersionOutputSerializerNoUploadedBy(DesignFileVersionOutputSerializer):
    class Meta(DesignFileVersionOutputSerializer.Meta):
        fields = (
            "id",
            "name",
            "type",
            "section",
            "tags",
            "file",
            "version",
            "status",
            "comment_count",
            "uploaded_at",
            "uploaded_by",
        )
        output_hash_id_fields = ("id",)
