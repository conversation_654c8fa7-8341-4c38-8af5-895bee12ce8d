# name: Migrate Stage DB To Azure
# on:
#   workflow_dispatch:

# permissions:
#       id-token: write
#       contents: read

# jobs:
#   deploy:
#     name: Migrate Stage DB to Azure
#     runs-on: ubuntu-latest
#     environment: migrate
#     timeout-minutes: 30
#     steps:
#       - name: Checkout
#         uses: actions/checkout@v3

#       - name: Configure aws credentials
#         uses: aws-actions/configure-aws-credentials@v4.0.1
#         with:
#           role-to-assume: ${{ vars.ORG_AWS_ASSUME_ROLE_ARN }}
#           aws-region: ${{ vars.ORG_AWS_REGION }}

#       - name: Azure login
#         uses: azure/login@v2
#         with:
#           client-id: ${{ secrets.ORG_AZURE_CLIENT_ID }}
#           tenant-id: ${{ secrets.ORG_AZURE_TENANT_ID }}
#           subscription-id: ${{ secrets.ORG_AZURE_SUBSCRIPTION_ID }}

#       - name: Update EKS kubeconfig
#         run: |-
#             aws eks update-kubeconfig --name ${{ vars.ORG_AWS_STAGE_EKS_CLUSTER }}
#             kubectl config set-context --current --namespace=${{ vars.MIGRATE_STAGE_NAMESPACE }}

#       - name: Export Database To S3
#         run: |-
#               kubectl create secret generic migrate-stage-db-dump \
#               --from-literal=PGPASSWORD=${{ secrets.STAGE_SOURCE_PGPASSWORD }} \
#               --from-literal=USER=${{ secrets.STAGE_SOURCE_PGUSER }} \
#               --from-literal=HOST=${{ vars.STAGE_SOURCE_PGHOST }} \
#               --from-literal=PORT=${{ vars.STAGE_PGPORT }} \
#               --from-literal=DATABASE=${{ vars.STAGE_PGDATABASE }} \
#               --from-literal=S3_URI=${{ vars.STAGE_S3_URI }} \
#               --from-literal=DUMP_FILE=${{ vars.STAGE_DUMP_FILE }} \
#               --dry-run=client -o yaml | kubectl apply -f -
#               kubectl wait --for=condition=complete job/$(kubectl apply -f kubernetes/postgres/migrate/stage-db-dump.yaml | cut -d'/' -f2 | cut -d' ' -f1 ) --timeout=300s

#       - name: Update AKS kubeconfig
#         run: |-
#             az aks get-credentials --resource-group ${{ vars.ORG_AZURE_STAGE_RESOURCE_GROUP }} --name ${{ vars.ORG_AZURE_AKS_CLUSTER }} --overwrite-existing
#             kubectl config set-context --current --namespace=${{ vars.MIGRATE_STAGE_NAMESPACE }}

#       - name: Restore Database From S3
#         run: |-
#               kubectl create secret generic migrate-stage-db-restore \
#               --from-literal=PGPASSWORD=${{ secrets.STAGE_DEST_PGPASSWORD }} \
#               --from-literal=USER=${{ secrets.STAGE_DEST_PGUSER }} \
#               --from-literal=HOST=${{ vars.STAGE_DEST_PGHOST }} \
#               --from-literal=PORT=${{ vars.STAGE_PGPORT }} \
#               --from-literal=DATABASE=${{ vars.STAGE_PGDATABASE }} \
#               --from-literal=S3_URI=${{ vars.STAGE_S3_URI }} \
#               --from-literal=DUMP_FILE=${{ vars.STAGE_DUMP_FILE }} \
#               --from-literal=AWS_ROLE_ARN=${{ vars.ORG_STAGE_AWS_AZURE_FEDERATION_ROLE_ARN }} \
#               --from-literal=AWS_WEB_IDENTITY_TOKEN_FILE=${{ vars.ORG_AWS_WEB_IDENTITY_TOKEN_FILE }} \
#               --from-literal=ROLE="rdash_user" \
#               --dry-run=client -o yaml | kubectl apply -f -

#               kubectl wait --for=condition=complete job/$(kubectl apply -f kubernetes/postgres/migrate/stage-db-restore.yaml | cut -d'/' -f2 | cut -d' ' -f1 ) --timeout=300s