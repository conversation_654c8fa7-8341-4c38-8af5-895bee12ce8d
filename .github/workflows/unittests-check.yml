name: Unit Tests

on:
  push:
    branches:
      - "*"

jobs:
  unittests-check:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/rdash-tech/rb-api:base-image
      credentials:
        username: rd-shubham
        password: ${{  secrets.DOCKER_CONTAINER_REGISTRY_TOKEN }}
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v2
      - name: Poetry 1.7.1
        run: |-
          pip install poetry==1.7.1

      - name: Install dependencies
        run: |-
          poetry config virtualenvs.create false --local
          make install

      - name: Compile Cython hashids
        run: |-
          sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list
          sed -i 's|security.debian.org|archive.debian.org/debian-security|g' /etc/apt/sources.list
          apt-get update -y && apt-get install -y build-essential
          cd rollingbanners/cython_hashids && python setup.py build_ext --inplace

      - name: Run tests
        run: pytest
        env:
          TWILIO_ACCOUNT_SID: ""
          TWILIO_AUTH_TOKEN: ""
          TWILIO_VERIFY_SERVICE_SID: ""
          N8N_TRIGGER_API_KEY: ""
          MASTER_TOKEN_HASH: ""
          INTREGRATIONS_POC_USER_EMAILS: ""
