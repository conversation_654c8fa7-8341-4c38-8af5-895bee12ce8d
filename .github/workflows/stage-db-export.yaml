name: Export Stage DB To S3
on:
  workflow_dispatch:
  schedule:
    - cron: "0 2 * * *"

permissions:
      id-token: write
      contents: read

jobs:
  deploy:
    name: Exporting stage db to s3
    runs-on: ubuntu-latest
    environment: stage
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Azure login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.ORG_AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.ORG_AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.ORG_AZURE_SUBSCRIPTION_ID }}

      - name: Update AKS kubeconfig
        run: |-
            az aks get-credentials --resource-group ${{ vars.ORG_AZURE_STAGE_RESOURCE_GROUP }} --name ${{ vars.ORG_AZURE_AKS_CLUSTER }} --overwrite-existing
            kubectl config set-context --current --namespace=${{ vars.STAGE_DB_NAMESPACE }}


      - name: Export Database
        run: |-
              kube<PERSON><PERSON> create secret generic secret-stage-db-export \
              --from-literal=PGPASSWORD=${{ secrets.STAGE_DB_ADMIN_PASSWORD }} \
              --from-literal=USER=${{ secrets.STAGE_DB_ADMIN_USER }} \
              --from-literal=HOST=${{ vars.STAGE_DB_HOST }} \
              --from-literal=PORT=${{ vars.STAGE_DB_PORT }} \
              --from-literal=DATABASE=${{ vars.STAGE_DATABASE }} \
              --from-literal=S3_URI=${{ vars.STAGE_S3_URI }} \
              --from-literal=DUMP_FILE=${{ vars.STAGE_DUMP_FILE }} \
              --from-literal=AWS_ROLE_ARN=${{ vars.ORG_STAGE_AWS_AZURE_FEDERATION_ROLE_ARN }} \
              --from-literal=AWS_WEB_IDENTITY_TOKEN_FILE=${{ vars.ORG_AWS_WEB_IDENTITY_TOKEN_FILE }} \
              --dry-run=client -o yaml | kubectl apply -f -
              kubectl wait --for=condition=complete job/$(kubectl apply -f kubernetes/postgres/stage/db-export.yaml | cut -d'/' -f2 | cut -d' ' -f1 ) --timeout=300s

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: deployments
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_TITLE: Rdash DB dump updated for branch databases
          SLACK_USERNAME: 91bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: "${{ github.event.head_commit.message }}\n Stage database dump completed Successfully"
