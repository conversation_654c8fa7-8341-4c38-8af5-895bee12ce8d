name: Deploy on Dev
on:
  workflow_dispatch:
    inputs:
      application:
        type: boolean
        default: true
        description: Redeploy Application
      database:
        type: boolean
        default: false
        description: Reinitialize Database
      frontend:
        type: boolean
        default: false
        description: Deploy Frontend
      notify:
        type: boolean
        default: true
        description: Send Slack Notification’s 🔔
      DASHBOARD_URL:
        type: string
        default: "https://app.stage.rdash.dev"
        description: dashboard url
      VMS_HOST:
        type: string
        default: "https://vms-api.stage.rdash.dev"
        description: vms host url

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest

    timeout-minutes: 20
    environment: dev
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Azure login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.ORG_AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.ORG_AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.ORG_AZURE_SUBSCRIPTION_ID }}

      - uses: docker/login-action@v3
        with:
          registry: ${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}
          username: ${{ secrets.ORG_STAGE_ACR_USERNAME }}
          password: ${{ secrets.ORG_STAGE_ACR_PASSWORD }}

      - name: Set up Docker Context for Buildx
        if: ${{ github.event.inputs.application == 'true' }}
        id: buildx-context
        run: docker context create builders

      - name: Set up Docker Buildx
        if: ${{ github.event.inputs.application == 'true' }}
        uses: docker/setup-buildx-action@v3
        with:
          endpoint: builders
          driver-opts: image=public.ecr.aws/x5v9a0b5/moby/buildkit:buildx-stable-1

      - name: Setup Kubernetes
        run: |
          az aks get-credentials --resource-group ${{ vars.ORG_AZURE_STAGE_RESOURCE_GROUP }} --name ${{ vars.ORG_AZURE_AKS_CLUSTER }} --overwrite-existing

      - name: Update kubeconfig & Set Up ENV
        id: image
        run: |-
          export repo_name=$(echo "${{ github.repository }}" | cut -d'/' -f2)
          export branch=`echo "${GITHUB_REF##*/}" | sed 's/[^a-zA-Z0-9]/-/g' | tr '[:upper:]' '[:lower:]'`
          export SANITIZED_NAME=$(echo "RD_${GITHUB_REF##*/}" | sed 's/[^a-zA-Z0-9]/_/g' | tr '[:upper:]' '[:lower:]')
          export git_hash=$(git rev-parse --short "$GITHUB_SHA")
          image_tag=${branch}.${git_hash}

          if kubectl get ns ${branch} &> /dev/null; then
            echo "PROVISION_ENV=false" >> $GITHUB_ENV
            kubectl config set-context --current --namespace=${branch}
          else
            echo "PROVISION_ENV=true" >> $GITHUB_ENV
            kubectl create namespace ${branch} --dry-run=client -o yaml | kubectl apply -f -
            kubectl label namespace ${branch} origin=dev
            kubectl label namespace ${branch} app=rdash-api
            kubectl config set-context --current --namespace=${branch}
          fi

          echo "name=${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}/${repo_name}:${image_tag}" >> $GITHUB_OUTPUT
          echo "cache=${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}/${repo_name}-cache" >> $GITHUB_OUTPUT

          echo "BRANCH=${branch}" >> $GITHUB_ENV
          echo "DATABASE=${SANITIZED_NAME}" >> $GITHUB_ENV
          echo "USER=${SANITIZED_NAME}" >> $GITHUB_ENV
          echo "PASSWORD=$(echo ${branch} | md5sum | cut -d ' ' -f1)" >> $GITHUB_ENV
          echo "BASE_URL=https://api.${branch}.rdash.dev" >> $GITHUB_ENV


      - name: Recreate Database or User
        if: ${{ github.event.inputs.database == 'true' || env.PROVISION_ENV == 'true' }}
        run: |-
          export PGPASSWORD=${{ secrets.DEV_RDS_PASSWORD }}
          export PGUSER=${{ secrets.DEV_RDS_USER }}

          psql -h "${{ vars.DEV_RDS_HOST }}" -d postgres << EOF
                ALTER DATABASE ${{ env.DATABASE }} OWNER TO ${{ secrets.DEV_RDS_USER }};
                SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='${{ env.DATABASE }}';
                DROP DATABASE ${{ env.DATABASE }};
                DROP USER ${{ env.USER }};
          EOF
          psql -h ${{ vars.DEV_RDS_HOST }} -d postgres << EOF
                CREATE DATABASE ${{ env.DATABASE }};
                CREATE USER ${{ env.USER }} WITH PASSWORD '${{ env.PASSWORD }}';
          EOF

          kubectl create secret generic db-credentials \
            --from-literal=PGPASSWORD=${{ secrets.DEV_RDS_PASSWORD }} \
            --from-literal=USER=${{ secrets.DEV_RDS_USER }} \
            --from-literal=HOST=${{ vars.DEV_RDS_HOST }} \
            --from-literal=PORT=5432 \
            --from-literal=ROLE=${{ env.USER }} \
            --from-literal=DATABASE=${{ env.DATABASE }} \
            --from-literal=S3_URI=${{ vars.DEV_S3_URI }} \
            --from-literal=DUMP_FILE=${{ vars.DEV_DUMP_FILE }} \
            --from-literal=AWS_ROLE_ARN=${{ vars.ORG_STAGE_AWS_AZURE_FEDERATION_ROLE_ARN }} \
            --from-literal=AWS_WEB_IDENTITY_TOKEN_FILE=${{ vars.ORG_AWS_WEB_IDENTITY_TOKEN_FILE }} \
            --dry-run=client -o yaml | kubectl apply -f -
            kubectl apply -f kubernetes/postgres/dev/sa.yaml
            kubectl wait --for=condition=complete $(kubectl apply -f kubernetes/postgres/dev/db-restore.yaml -o name) --timeout=300s

      - name: Create .env file
        if: ${{ github.event.inputs.application == 'true' }}
        run: |
          cat <<EOF > .env
          VMS_HOST=${{ github.event.inputs.VMS_HOST }}
          PUBLIC_HASH_ID_KEY=${{ secrets.STAGE_PUBLIC_HASH_ID_KEY }}
          DATABASE_URL=postgresql://${{ env.USER }}:${{ env.PASSWORD }}@${{ vars.DEV_RDS_HOST }}:5432/${{ env.DATABASE }}
          REDIS_URI=${{ vars.DEV_REDIS_URI }}
          REDIS_URI_CELERY=${{ vars.DEV_REDIS_URI_CELERY }}
          FIREBASE_API_KEY=${{ secrets.STAGE_FIREBASE_API_KEY }}
          EMAIL_HOST_PASSWORD=${{ secrets.STAGE_EMAIL_HOST_PASSWORD }}
          WATI_TOKEN=${{ secrets.STAGE_WATI_TOKEN }}
          RECCE_RATING_UPDATE_KEY=${{ secrets.STAGE_RECCE_RATING_UPDATE_KEY }}
          GOOGLE_SERVICE_ACCOUNT_KEY=${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          VMS_TOKEN=${{ secrets.STAGE_VMS_TOKEN }}
          METABASE_PASSWORD=${{ secrets.STAGE_METABASE_PASSWORD }}
          METABASE_SECRET_KEY=${{ secrets.STAGE_METABASE_SECRET_KEY }}
          MIXPANEL_PROJECT_KEY=${{ secrets.STAGE_MIXPANEL_PROJECT_KEY }}
          SES_EMAIL_BACKEND=${{ vars.DEV_SES_EMAIL_BACKEND }}
          SES_EMAIL_HOST=${{ vars.DEV_SES_EMAIL_HOST }}
          SES_EMAIL_HOST_USER=${{ vars.DEV_SES_EMAIL_HOST_USER }}
          SES_EMAIL_HOST_PASSWORD=${{ vars.DEV_SES_EMAIL_HOST_PASSWORD }}
          SES_EMAIL_PORT=${{ vars.DEV_SES_EMAIL_PORT }}
          SES_FROM_EMAIL=${{ vars.DEV_SES_FROM_EMAIL }}
          SES_EMAIL_USE_TLS=${{ vars.DEV_SES_EMAIL_USE_TLS }}
          SES_EMAIL_USE_SSL=${{ vars.DEV_SES_EMAIL_USE_SSL }}
          AWS_ACCESS_KEY_ID=${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY=${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}
          AWS_STORAGE_BUCKET_NAME=${{ vars.DEV_AWS_STORAGE_BUCKET_NAME }}
          AWS_S3_REGION_NAME=${{ vars.DEV_AWS_S3_REGION_NAME }}
          AWS_S3_CUSTOM_DOMAIN=${{ vars.DEV_AWS_S3_CUSTOM_DOMAIN }}
          AWS_CDN_DOMAIN=${{ vars.DEV_AWS_CDN_DOMAIN }}
          DASHBOARD_URL=${{ github.event.inputs.frontend == 'true' && env.BASE_URL || github.event.inputs.DASHBOARD_URL }}
          TWILIO_ACCOUNT_SID=${{ vars.DEV_TWILIO_ACCOUNT_SID }}
          TWILIO_AUTH_TOKEN=${{ vars.DEV_TWILIO_AUTH_TOKEN }}
          TWILIO_VERIFY_SERVICE_SID=${{ vars.DEV_TWILIO_VERIFY_SERVICE_SID }}
          OLD_IMAGE_KIT_DOMAIN=${{ vars.DEV_OLD_IMAGE_KIT_DOMAIN }}
          EOF
          echo 'SECRET_KEY=${{ secrets.STAGE_SECRET_KEY }}' >> .env


      - name: Build and push Rdash Backend
        if: ${{ github.event.inputs.application == 'true' }}
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          file: Dockerfile.dev
          tags: ${{ steps.image.outputs.name }}
          cache-from: type=registry,ref=${{ steps.image.outputs.cache }}
          cache-to: mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=${{ steps.image.outputs.cache }}

      - name: Update Image & Deploy
        if: ${{ github.event.inputs.application == 'true' }}
        run: |-
          kubectl create secret generic rdash-backend-env-secret --from-env-file .env --dry-run=client -o yaml | kubectl apply -f -
          helm template rdash-backend kubernetes/dev \
            --set containers.backend.image=${{ steps.image.outputs.name }} \
            --set ingress.host=api.${{ env.BRANCH }}.rdash.dev \
            --set containers.frontend.enabled=${{ github.event.inputs.frontend}} \
            | kubectl apply -f -
          kubectl rollout restart deployment rdash-backend
          echo "Deployed Url: https://api.${{ env.BRANCH }}.rdash.dev"

      - name: Slack Notification
        if: ${{ github.event.inputs.notify == 'true' && (github.event.inputs.database == 'true' || env.PROVISION_ENV == 'true') }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: deployments
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_TITLE: Database Re/Initialized
          SLACK_USERNAME: RdBot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: "${{ github.event.head_commit.message }}\n Database Url: ```postgresql://${{ env.USER }}:****@${{ vars.DEV_RDS_HOST }}:5432/${{ env.DATABASE }}```"

      - name: Slack Notification
        if: ${{ github.event.inputs.notify == 'true' && github.event.inputs.application == 'true' }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: deployments
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_TITLE: Application Re/Deployed on kubernetes
          SLACK_USERNAME: RdBot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: "${{ github.event.head_commit.message }}\n Deployed Url: https://api.${{ env.BRANCH }}.rdash.dev"