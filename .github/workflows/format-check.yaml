name: Formatter Check

on:
  push:
    branches:
      - "*"
      - "!master"

jobs:
  format-check:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout branch
        uses: actions/checkout@v2

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v40
        with:
          files: "**/*.py"
          files_ignore: "**/migrations/**"
      
      - name: Setup Python env
        uses: actions/setup-python@v4
        with:
          python-version: "3.10.9"

      - name: Install ruff
        if: steps.changed-files.outputs.all_changed_files_count !=0
        run: pip install ruff==0.11.13
        
      - name: Run ruff
        if: steps.changed-files.outputs.all_changed_files_count !=0
        run: ruff check ${{ steps.changed-files.outputs.all_changed_files }}
