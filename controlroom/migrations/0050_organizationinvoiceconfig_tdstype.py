# Generated by Django 3.2.15 on 2025-07-07 13:18

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0014_auto_20250707_1318'),
        ('core', '0174_auto_20250707_1318'),
        ('controlroom', '0049_approvalhierarchy'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationInvoiceConfig',
            fields=[
            ],
            options={
                'verbose_name': 'Organization Invoice Config',
                'verbose_name_plural': 'Organization Invoice Config',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.organizationinvoiceconfig',),
        ),
        migrations.CreateModel(
            name='TDSType',
            fields=[
            ],
            options={
                'verbose_name': 'TDS Type',
                'verbose_name_plural': 'TDS Types',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('core.tdstype',),
        ),
    ]
