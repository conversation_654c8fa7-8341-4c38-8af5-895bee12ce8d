from typing import Dict
from urllib.parse import urljoin

from django.conf import settings
from django.contrib.admin.models import ADDITION, LogEntry
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.encoding import force_bytes
from django.utils.html import format_html
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _

from approval_request.approval_hierarchy.data.models import ApprovalHierarchy as _ApprovalHierarchy
from authentication.domain.services import admin_login_bypass_token_generator
from client.data.models import Client as _Client
from client.data.models import StoreType as _StoreType
from common.choices import OrganizationType, PermissionScope
from common.utils import padding_for_serial_number
from controlroom.data.querysets import QuerySet
from core.constants import GST_NUMBER_TO_STATE_MAPPING
from core.models import City as _City
from core.models import CommonDomain as _CommonDomain
from core.models import Country as _Country
from core.models import CountryCurrencyMapping as _CountryCurrencyMapping
from core.models import CountryTaxMapping as _CountryTaxMapping
from core.models import CountryTimezoneMapping as _CountryTimezoneMapping
from core.models import Currency as _Currency
from core.models import FromToOrgMapping as _FromToOrgMapping
from core.models import GstSlab as _GstSlab
from core.models import ItemExpenseType as _ItemExpenseType
from core.models import ItemExpenseTypeCategory as _ItemExpenseTypeCategory
from core.models import Organization as _Organization
from core.models import OrganizationAddress as _OrganizationAddress
from core.models import OrganizationBlockedAction as _OrganizationBlockedAction
from core.models import OrganizationCallbackUrl as _OrganizationCallbackUrl
from core.models import OrganizationCallbackUrlEvent as _OrganizationCallbackUrlEvent
from core.models import OrganizationConfig as _OrganizationConfig
from core.models import OrganizationConfigCorePermission as _OrganizationConfigCorePermission
from core.models import OrganizationConfigRole as _OrganizationConfigRole
from core.models import OrganizationCustomFieldConfig as _OrganizationCustomFieldConfig
from core.models import OrganizationDefaultModuleLevelMapping as _OrganizationDefaultModuleLevelMapping
from core.models import OrganizationDefaultModuleSetting as _OrganizationDefaultModuleSetting
from core.models import OrganizationDocument as _OrganizationDocument
from core.models import OrganizationDomain as _OrganizationDomain
from core.models import OrganizationGSTNumber as _OrganizationGSTNumber
from core.models import OrganizationLegalEntity as _OrganizationLegalEntity
from core.models import OrganizationOrderPaymentTerm as _OrganizationOrderPaymentTerm
from core.models import OrganizationUser as _OrganizationUser
from core.models import PmcClientMapping as _PmcClientMapping
from core.models import PmcVendorMapping as _PmcVendorMapping
from core.models import ProductionDrawingTag as _ProductionDrawingTag
from core.models import Region as _Region
from core.models import RegionStateMapping as _RegionStateMapping
from core.models import Role as _Role
from core.models import RolePermission as _RolePermission
from core.models import State as _State
from core.models import SystemCallbackUrl as _SystemCallbackUrl
from core.models import SystemCallbackUrlEvent as _SystemCallbackUrlEvent
from core.models import TaxSlab as _TaxSlab
from core.models import TaxType as _TaxType
from core.models import TDSType as _TDSType
from core.models import Timezone as _Timezone
from core.models import UnitOfMeasurement as _UnitsMeasurement
from core.models import User as _User
from core.models import VendorOrgTag as _VendorOrgTag
from core.organization.models import OrganizationDocumentConfig as _OrganizationDocumentConfig
from core.organization.models import OrganizationDocumentFieldConfig as _OrganizationDocumentFieldConfig
from core.organization.models import OrganizationDocumentFieldContextConfig as _OrganizationDocumentFieldContextConfig
from core.organization.models import OrganizationSectionConfig as _OrganizationSectionConfig
from crm.data.models import BoardStage
from dashboard.data.choices import DashboardCollectionType
from dashboard.data.models import Dashboard as _Dashboard
from dashboard.data.models import DashboardCard as _DashboardCard
from dashboard.data.models import DashboardCollection as _DashboardCollection
from dashboard.data.models import DashboardFilter as _DashboardFilter
from dashboard.data.models import DashboardTag as _DashboardTag
from design.data.models import DesignFileTag as _DesignFileTag
from element.data.models import ElementCategory as _ElementCategory
from element.data.models import ElementCategoryOrgMapping as _ElementCategoryOrgMapping
from element.data.models import ElementLibrary as _ElementLibrary
from element.data.models import LibraryClientVendorMapping as _LibraryClientVendorMapping
from execution_tracker.data.models import DeadLetterQueueTask
from integrations.models import FacebookPageCrmBoardMapping
from inventory.data.models import ConsumptionReason as _ConsumptionReason
from order.data.models import DeductionRemark as _DeductionRemark
from order.data.models import DeductionType as _DeductionType
from order.invoice.data.models import InvoiceType as _InvoiceType
from order.invoice.data.models import OrganizationInvoiceConfig as _OrganizationInvoiceConfig

from progressreport.models import ManpowerCategory as _ManpowerCategory
from project.data.models import BusinessCategory as _BusinessCategory
from project.data.models import CityStateMapping as _CityStateMapping
from project.data.models import Project as _Project
from project.data.models import ProjectOrganization as _ProjectOrganization
from project.data.models import ProjectUser as _ProjectUser
from project.data.models import Scope as _Scope
from recce.data.models import Recce as _Recce
from vendor.data.models import Vendor as _Vendor


class OrganizationBlockedAction(_OrganizationBlockedAction):
    def __str__(self):
        return f"{self.action}"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Blocked Actions"
        verbose_name = "Organization Blocked Action"


class GstSlab(_GstSlab):
    def __str__(self):
        return f"{self.gst_percent}"

    class Meta:
        proxy = True
        verbose_name_plural = "GstSlabs"
        verbose_name = "GstSlab"


class UnitOfMeasurement(_UnitsMeasurement):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Units of Measurements"
        verbose_name = "Unit of Measurement"


class Region(_Region):
    class Meta:
        proxy = True
        verbose_name_plural = "Regions"
        verbose_name = "Region"


class RegionStateMapping(_RegionStateMapping):
    def __str__(self):
        return f"{self.region.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "RegionStateMappings"
        verbose_name = "RegionStateMapping"


class ItemExpenseType(_ItemExpenseType):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "ItemExpenseTypes"
        verbose_name = "ItemExpenseType"


class ItemExpenseTypeCategory(_ItemExpenseTypeCategory):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True


class ElementCategory(_ElementCategory):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Element Categories"
        verbose_name = "Element Category"


class ElementCategoryOrgMapping(_ElementCategoryOrgMapping):
    def __str__(self):
        return f"{self.category.name} ({self.organization.name if self.organization else '-'})"

    class Meta:
        proxy = True
        verbose_name_plural = "Element Category Org Mappings"
        verbose_name = "Element Category Org Mapping"


class CommonDomain(_CommonDomain):
    def __str__(self) -> str:
        return f"{self.domain_name}"

    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Common Domains"


class OrganizationDomain(_OrganizationDomain):
    def __str__(self) -> str:
        return f"{self.domain_name}"

    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Domain"


class VendorOrgTag(_VendorOrgTag):
    def __str__(self) -> str:
        return f"{self.name}"

    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Vendor Org Tags"


class OrganizationConfigCorePermission(_OrganizationConfigCorePermission):
    def __str__(self):
        return f"{self.permission}"

    class Meta:
        proxy = True


class DeductionType(_DeductionType):
    def __str__(self) -> str:
        return f"{self.name}"

    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Deduction Types"


class DeductionRemark(_DeductionRemark):
    def __str__(self) -> str:
        return f"{self.deduction_type.name} - {self.remark}"

    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Deduction Remarks"


class StoreType(_StoreType):
    objects = QuerySet.as_manager()

    class Meta:
        proxy = True
        verbose_name_plural = "Store Types"


class ElementLibrary(_ElementLibrary):
    objects = QuerySet.as_manager()

    def __str__(self):
        return f"{self.name} ({self.client.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Element Libraries"


class ClientData(_Client):
    def __str__(self):
        return f"{self.name}"

    @property
    def name(self):
        return self.organization.name

    def clean(self):
        self.code = self.code.upper()
        super().clean()

    class Meta:
        proxy = True


class User(_User):
    def __str__(self):
        return self.name

    @property
    def is_deleted(self):
        if self.deleted_at is not None or self.deleted_by is not None:
            return True
        return False

    @property
    def verified(self):
        return self.is_verified

    @property
    def organization(self):
        return self.org.name if self.org else "-"

    @property
    def orgs(self):
        return (", ").join([org.name for org in self.organizations.all()])

    @property
    def login_url(self):
        uid = urlsafe_base64_encode(force_bytes(self.hashid))
        token = admin_login_bypass_token_generator.make_token(self)
        return urljoin(
            settings.LINK_URL if not settings.DEBUG else settings.DASHBOARD_URL, f"direct-login?uid={uid}&token={token}"
        )

    @property
    def login_link(self):
        return format_html("<a href='%s' target='_blank'>%s</a>" % (self.login_url, "Login"))

    def clean(self):
        super().clean()
        errors = dict()
        user_with_email = User.objects.filter(email=self.email, deleted_at__isnull=True)
        user_with_phone = User.objects.filter(phone_number=self.phone_number, deleted_at__isnull=True)

        if self.pk:
            user_with_email = user_with_email.exclude(pk=self.pk)
            user_with_phone = user_with_phone.exclude(pk=self.pk)

        if user_with_email.exists():
            errors["email"] = _("User already exist with this email.")
        if user_with_phone.exists():
            errors["phone_number"] = _("User already exist with this phone number.")

        if len(errors):
            raise ValidationError(errors)

    class Meta:
        proxy = True
        permissions = [
            ("can_login_directly", "Can login directly"),
            ("can_switch_organization", "Can switch organization"),
        ]


class OrganizationUser(_OrganizationUser):
    def __str__(self):
        return f"{self.user.name} ({self.organization.name})"

    def clean(self):
        try:
            super().clean()
        except ValidationError as e:
            errors: Dict = e.message_dict
            if "role_id" in errors:
                errors["role"] = errors["role_id"]
                del errors["role_id"]
            if "organization_id" in errors:
                errors["organization"] = errors["organization_id"]
                del errors["organization_id"]
            raise ValidationError(errors)

    class Meta:
        proxy = True


class Project(_Project):
    def __str__(self):
        return f"{self.name} (deleted)" if self.deleted_at is not None else f"{self.name}"

    class Meta:
        proxy = True


class ProjectUser(_ProjectUser):
    class Meta:
        proxy = True


class Recce(_Recce):
    def __str__(self):
        return f"{self.project.client.name} : {self.project.name} : {self.version}"

    class Meta:
        proxy = True
        ordering = ["-id"]


class PmcClientMapping(_PmcClientMapping):
    def __str__(self):
        return f"PMC-{self.pmc.name}, CLIENT-{self.client.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "PMC Client Mappings"
        verbose_name = "PMC Client Mapping"


class PmcVendorMapping(_PmcVendorMapping):
    def __str__(self):
        return f"PMC-{self.pmc.name}, VENDOR-{self.vendor.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "PMC Vendor Mappings"
        verbose_name = "PMC Vendor Mapping"


class ProjectUserRole(_Role):
    class ProjectUserRoleManager(models.Manager):
        def get_queryset(self):
            return super().get_queryset().filter(scope=PermissionScope.PROJECT)

    objects = ProjectUserRoleManager()

    def clean(self):
        errors = {}
        if ProjectUserRole.objects.exclude(pk=self.pk).filter(organization=self.organization, name=self.name).exists():
            errors["name"] = "A role with this name already exists for the organization."

        if (
            _Role.objects.exclude(pk=self.pk)
            .filter(organization=self.organization, name=self.name, role_type=self.role_type)
            .exists()
        ):
            errors[
                "name"
            ] = "A role with this name already exists for the organization either at organization or project level."

        if len(errors):
            raise ValidationError(errors)

    def save(self, *args, **kwargs):
        self.scope = PermissionScope.PROJECT
        self.type = self.organization.type
        super().save(*args, **kwargs)

    class Meta:
        proxy = True
        verbose_name_plural = "Project User Roles"
        verbose_name = "Project User Role"


class BusinessCategory(_BusinessCategory):
    def __str__(self):
        return f"{self.name} ({self.organization.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Business Categories"
        verbose_name = "Business Category"


class OrganizationLegalEntity(_OrganizationLegalEntity):
    def __str__(self):
        return f"{self.name} ({self.organization.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Legal Entities"
        verbose_name = "Organization Legal Entity"


class OrganizationOrderPaymentTerm(_OrganizationOrderPaymentTerm):
    def __str__(self):
        return f"{self.title} ({self.organization.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Payment Terms"
        verbose_name = "Organization Payment Term"


class OrganizationAddress(_OrganizationAddress):
    def __str__(self):
        if self.header:
            return f"{self.header} ({self.organization.name})"
        return f"{self.organization.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Addresses"
        verbose_name = "Organization Address"


class OrganizationDocument(_OrganizationDocument):
    def __str__(self):
        return f"({self.name})"

    class Meta:
        proxy = True


class OrganizationConfigRole(_OrganizationConfigRole):
    def __str__(self):
        return f"{self.role.name}"

    class Meta:
        proxy = True


class OrganizationConfig(_OrganizationConfig):
    def __str__(self):
        return f"{self.organization.name}"

    @property
    def is_new(self):
        if self.pk is None:
            return False
        content_type = ContentType.objects.get(app_label="controlroom", model="organizationconfig")
        history = LogEntry.objects.filter(content_type_id=content_type.id, object_id=self.pk)
        count = history.count()
        if count == 0:
            return True
        if count == 1 and history.filter(action_flag=ADDITION).exists():
            return True
        return False

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Configs"
        verbose_name = "Organization Config"


class PMC(_Organization):
    class PMCManager(models.Manager):
        def get_queryset(self):
            return super().get_queryset()

    objects = PMCManager()

    def save(self, *args, **kwargs):
        self.type = OrganizationType.PMC
        super(PMC, self).save(*args, **kwargs)

    class Meta:
        proxy = True
        verbose_name_plural = "PMCs"


class Client(_Organization):
    def code(self):
        return self.client.code if hasattr(self, "client") else "NA"

    def save(self, *args, **kwargs):
        self.type = OrganizationType.CLIENT
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.client.code if hasattr(self, 'client') else 'NA'})"

    class Meta:
        proxy = True


class OrganizationSystemUser(_Organization):
    def __str__(self):
        return self.name

    @property
    def login_url(self):
        uid = urlsafe_base64_encode(force_bytes(self.system_user.hashid))
        token = admin_login_bypass_token_generator.make_token(self.system_user)
        return urljoin(settings.DASHBOARD_URL, f"direct-login?uid={uid}&token={token}")

    class Meta:
        proxy = True


class Vendor(_Organization):
    def code(self):
        return self.vendor.code if hasattr(self, "vendor") else "NA"

    def save(self, *args, **kwargs):
        self.type = OrganizationType.VENDOR
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.vendor.code if hasattr(self, 'vendor') else 'NA'})"

    class Meta:
        proxy = True


class VendorData(_Vendor):
    def __str__(self):
        return f"{self.organization.name}"

    @property
    def name(self):
        return self.organization.name

    def clean(self):
        if not self.code:
            self.code = self.get_vendor_code()
        self.code = self.code.upper()
        super().clean()

    def get_vendor_code(self):
        from vendorv2.domain.services import VendorServiceV2

        code_prefix = VendorServiceV2.code_prefix
        serial_number = VendorServiceV2.fetch_next_serial_number()
        return f"{code_prefix}{padding_for_serial_number(serial_number, 4)}"

    class Meta:
        proxy = True


class OrganizationGSTNumberData(_OrganizationGSTNumber):
    def __str__(self):
        return f"{self.gst_number}"

    def save(self, *args, **kwargs):
        if self.file:
            self.name = self.file.name
        if self.gst_number and len(self.gst_number) == 15 and self.gst_number[:2].isdigit():
            self.gst_state = GST_NUMBER_TO_STATE_MAPPING.get(int(self.gst_number[:2]), None)

        super().save(*args, **kwargs)

    class Meta:
        proxy = True


class ProjectOrganizationRole(_Role):
    class ProjectOrganizationRoleManager(models.Manager):
        def get_queryset(self):
            return super().get_queryset().filter(scope=PermissionScope.ORGANIZATION)

    objects = ProjectOrganizationRoleManager()

    def save(self, *args, **kwargs):
        self.scope = PermissionScope.ORGANIZATION
        super().save(*args, **kwargs)

    class Meta:
        proxy = True
        verbose_name_plural = "Project Organization Roles"
        verbose_name = "Project Organization Role"


class OrganizationUserRole(_Role):
    class OrganizationUserRoleManager(models.Manager):
        def get_queryset(self):
            return super().get_queryset().filter(scope=PermissionScope.CORE)

    objects = OrganizationUserRoleManager()

    def __str__(self):
        return f"{self.name} ({self.organization.name})"

    def clean(self):
        errors = {}
        if (
            OrganizationUserRole.objects.exclude(pk=self.pk)
            .filter(organization=self.organization, name=self.name)
            .exists()
        ):
            errors["name"] = "A role with this name already exists for the organization."

        if (
            _Role.objects.exclude(pk=self.pk)
            .filter(organization=self.organization, name=self.name, role_type=self.role_type)
            .exists()
        ):
            errors[
                "name"
            ] = "A role with this name already exists for the organization either at organization or project level."

        if len(errors):
            raise ValidationError(errors)

    def save(self, *args, **kwargs):
        self.scope = PermissionScope.CORE
        self.type = OrganizationType.PMC
        super().save(*args, **kwargs)

    class Meta:
        proxy = True
        verbose_name_plural = "Organization User Roles"
        verbose_name = "Organization User Role"


class FromToOrgMapping(_FromToOrgMapping):
    def __str__(self) -> str:
        return f"{self.org_from.name} to {self.org_to.name}"

    def clean(self):
        if self.org_from == self.org_to:
            raise ValidationError("Client and Vendor cannot be same.")
        super().clean()

    class Meta:
        proxy = True
        verbose_name = "From To Org Mapping"


class ProjectOrganization(_ProjectOrganization):
    def __str__(self):
        return f"{self.project.name} ({self.organization.name})"

    class Meta:
        proxy = True


class CityStateMapping(_CityStateMapping):
    def __str__(self):
        return f"{self.city} ({self.state})"

    class Meta:
        proxy = True


class ManpowerCategory(_ManpowerCategory):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True


class LibrarySharedMappings(_LibraryClientVendorMapping):
    def __str__(self):
        return f"{self.library.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Library Shared Mappings"
        verbose_name = "Library Shared Mapping"


class OrganizationCustomFieldConfig(_OrganizationCustomFieldConfig):
    def __str__(self):
        return f"{self.context} {self.org.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Custom Field Config"
        verbose_name = "Organization Custom Field Configs"


class OrgnanizationCallbackUrl(_OrganizationCallbackUrl):
    def __str__(self):
        return f"{self.organization.name} {self.url}"

    class Meta:
        proxy = True


class OrganizationCallbackUrlEvent(_OrganizationCallbackUrlEvent):
    def __str__(self):
        return f"{self.event}"

    class Meta:
        proxy = True


class SystemCallbackUrl(_SystemCallbackUrl):
    def __str__(self):
        return f"{self.url}"

    class Meta:
        proxy = True


class SystemCallbackUrlEvent(_SystemCallbackUrlEvent):
    def __str__(self):
        return f"{self.event}"

    class Meta:
        proxy = True


class Country(_Country):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True


class ProductionDrawingTag(_ProductionDrawingTag):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name_plural = "Production Drawing Tag"
        verbose_name = "Production Drawing Tags"
        proxy = True


class DesignFileTag(_DesignFileTag):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name_plural = "Global Design File Tags"
        verbose_name = "Global Design File Tags"
        proxy = True


class InvoiceType(_InvoiceType):
    class Meta:
        verbose_name_plural = "Invoice Types"
        verbose_name = "Invoice Type"
        proxy = True


class ConsumptionReason(_ConsumptionReason):
    class Meta:
        verbose_name_plural = "Inventory Consumption Reasons"
        verbose_name = "Inventory Consumption Reasons"
        proxy = True


class Organization(_Organization):
    def __str__(self):
        return f"{self.name} ({self.id})"

    class Meta:
        proxy = True


class Scope(_Scope):
    def __str__(self):
        if self.organization is not None:
            return f"{self.name} ({self.organization.name})"
        else:
            return self.name

    class Meta:
        proxy = True
        verbose_name_plural = "Scope"
        verbose_name = "Scope"


class Dashboard(_Dashboard):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Dashboards"
        verbose_name = "Dashboard"


class DashboardCard(_DashboardCard):
    def __str__(self):
        return f"{self.chart_question_id} ({self.dashboard.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Dashboard Cards"
        verbose_name = "Dashboard Card"


class DashboardFilter(_DashboardFilter):
    def __str__(self):
        return f"{self.dashboard.name} - {self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Dashboard Filters"
        verbose_name = "Dashboard Filter"


class DashboardTag(_DashboardTag):
    class Meta:
        proxy = True
        verbose_name_plural = "Dashboard Tags"
        verbose_name = "Dashboard Tag"


class CustomDashboardCollection(_DashboardCollection):
    class CustomDashboardCollectionManager(models.Manager):
        def get_queryset(self):
            return (
                super()
                .get_queryset()
                .filter(custom_question_id__isnull=False, type=DashboardCollectionType.CUSTOM.value)
            )

    objects = CustomDashboardCollectionManager()

    class Meta:
        proxy = True
        verbose_name_plural = "Custom Dashboard Collections"
        verbose_name = "Custom Dashboard Collection"


class Timezone(_Timezone):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Timezones"
        verbose_name = "Timezone"


class Currency(_Currency):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Currencies"
        verbose_name = "Currency"


class TaxType(_TaxType):
    def __str__(self):
        return f"{self.name} ({self.remark})" if self.remark else f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Tax Types"
        verbose_name = "Tax Type"


class TDSType(_TDSType):
    def __str__(self):
        return f"{self.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "TDS Types"
        verbose_name = "TDS Type"


class OrganizationInvoiceConfig(_OrganizationInvoiceConfig):
    def __str__(self):
        return f"{self.organization.name}"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Invoice Config"
        verbose_name = "Organization Invoice Config"


class CountryTimezoneMapping(_CountryTimezoneMapping):
    def __str__(self):
        return self.country.name + " - " + self.timezone.name

    class Meta:
        proxy = True
        verbose_name_plural = "Country Timezone Mappings"
        verbose_name = "Country Timezone Mapping"


class CountryTaxMapping(_CountryTaxMapping):
    def __str__(self):
        return self.country.name + " - " + self.tax_type.name

    class Meta:
        proxy = True
        verbose_name_plural = "Country Tax Mappings"
        verbose_name = "Country Tax Mapping"


class CountryCurrencyMapping(_CountryCurrencyMapping):
    def __str__(self):
        return self.country.name + " - " + self.currency.name

    class Meta:
        proxy = True
        verbose_name_plural = "Country Currency Mappings"
        verbose_name = "Country Currency Mapping"


class TaxSlab(_TaxSlab):
    def __str__(self):
        return f"{str(self.tax_type)} - {self.tax_percent}%"

    class Meta:
        proxy = True
        verbose_name_plural = "Tax Slabs"
        verbose_name = "Tax Slab"


class OrganizationSectionConfig(_OrganizationSectionConfig):
    def __str__(self):
        return f"{self.name} ({self.country.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Section Configs"
        verbose_name = "Organization Section Config"


class OrganizationDocumentConfig(_OrganizationDocumentConfig):
    def __str__(self):
        return f"{self.name} ({self.section.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Document Configs"
        verbose_name = "Organization Document Config"


class OrganizationDocumentFieldConfig(_OrganizationDocumentFieldConfig):
    def __str__(self):
        return f"{self.name} ({self.document_config.name}) ({self.document_config.section.name})"

    class Meta:
        proxy = True
        verbose_name_plural = "Organization Document Field Configs"
        verbose_name = "Organization Document Field Config"


class OrganizationDocumentFieldContextConfig(_OrganizationDocumentFieldContextConfig):
    class Meta:
        proxy = True
        verbose_name_plural = "Organization Document Field Context Configs"
        verbose_name = "Organization Document Field Context Config"


class OrganizationDefaultModuleSetting(_OrganizationDefaultModuleSetting):
    class Meta:
        proxy = True
        verbose_name_plural = "Default Org Module Settings"
        verbose_name = "Default Org Module Setting"


class OrganizationDefaultModuleLevelMapping(_OrganizationDefaultModuleLevelMapping):
    class Meta:
        proxy = True
        verbose_name_plural = "Default Org Module Level Mappings"
        verbose_name = "Default Org Module Level Mapping"


class State(_State):
    def __str__(self):
        text = self.name
        if self.country:
            text += f", {self.country.name}"
        return text

    class Meta:
        proxy = True


class RolePermission(_RolePermission):
    def __str__(self):
        return f"{self.permission}"

    class Meta:
        proxy = True


class City(_City):
    def __str__(self):
        text = self.name
        if self.state:
            text += f", {self.state.name}"
        if self.country:
            text += f", {self.country.name}"
        return text

    class Meta:
        proxy = True


class Role(_Role):
    def __str__(self):
        return f"{self.name} ({self.organization.name})"

    class Meta:
        proxy = True


class IntegrationFacebookCrm(FacebookPageCrmBoardMapping):
    class Meta:
        proxy = True
        verbose_name = "Integration Facebook CRM"
        verbose_name_plural = "Integration Facebook CRM"


class BoardStageProxy(BoardStage):
    class Meta:
        proxy = True
        verbose_name = "Board Stage"
        verbose_name_plural = "Board Stages"

    def __str__(self):
        return f"{self.board.name} - {self.name}"


class ApprovalHierarchy(_ApprovalHierarchy):
    class Meta:
        proxy = True
        verbose_name = "Approval Hierarchy"
        verbose_name_plural = "Approval Hierarchies"

    def __str__(self):
        return f"{self.organization.name} - {self.context}"


class DeadLetterQueueTasks(DeadLetterQueueTask):
    class Meta:
        proxy = True
        verbose_name = "Execution Tracker DLQ Task"
        verbose_name_plural = "Execution Tracker DLQ Task"
