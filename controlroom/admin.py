import abc
import json
from datetime import datetime
from functools import partial
from typing import Any, Optional

from django.conf.urls import url
from django.contrib import admin, messages
from django.contrib.admin.widgets import AutocompleteSelect
from django.contrib.auth.admin import UserAdmin as _UserAdmin
from django.db import transaction
from django.db.models import F
from django.db.models.query import QuerySet
from django.db.transaction import on_commit
from django.forms import BaseInlineFormSet
from django.http import HttpResponseRedirect
from django.http.request import HttpRequest
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from authentication.data.models import AppToken
from authentication.interface.serializers import TokenDataSerializer
from authorization.domain.constants import (
    CORE_PERMISSION_SECTION_MAPPINGS,
    DIRECT_CORE_PERMISSIONS,
    DIRECT_PROJECT_PERMISSIONS,
    PROJECT_PERMISSION_SECTION_MAPPINGS,
    Permissions,
)
from client.onboard.services import ClientOnboardService
from common.admin import DuplicateAdminMixin, SoftDeleteAdminMixin
from common.choices import OrganizationType, PermissionScope
from common.constants import CustomFieldTypeChoices
from common.events.constants import Events
from common.utils import padding_for_serial_number
from controlroom.data.models import (
    PMC,
    ApprovalHierarchy,
    BusinessCategory,
    City,
    Client,
    ClientData,
    CommonDomain,
    ConsumptionReason,
    Country,
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    Currency,
    CustomDashboardCollection,
    Dashboard,
    DashboardCard,
    DashboardFilter,
    DashboardTag,
    DeadLetterQueueTasks,
    DeductionRemark,
    DeductionType,
    DesignFileTag,
    ElementCategory,
    ElementCategoryOrgMapping,
    ElementLibrary,
    FromToOrgMapping,
    GstSlab,
    IntegrationFacebookCrm,
    InvoiceType,
    ItemExpenseType,
    ItemExpenseTypeCategory,
    LibrarySharedMappings,
    ManpowerCategory,
    OrganizationAddress,
    OrganizationBlockedAction,
    OrganizationCallbackUrlEvent,
    OrganizationConfig,
    OrganizationConfigRole,
    OrganizationCustomFieldConfig,
    OrganizationDefaultModuleLevelMapping,
    OrganizationDefaultModuleSetting,
    OrganizationDocument,
    OrganizationDocumentConfig,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationDomain,
    OrganizationInvoiceConfig,
    OrganizationLegalEntity,
    OrganizationOrderPaymentTerm,
    OrganizationSectionConfig,
    OrganizationSystemUser,
    OrganizationUser,
    OrganizationUserRole,
    OrgnanizationCallbackUrl,
    PmcClientMapping,
    PmcVendorMapping,
    ProductionDrawingTag,
    Project,
    ProjectOrganization,
    ProjectOrganizationRole,
    ProjectUser,
    ProjectUserRole,
    Region,
    Role,
    Scope,
    State,
    StoreType,
    SystemCallbackUrl,
    SystemCallbackUrlEvent,
    TaxSlab,
    TaxType,
    TDSType,
    Timezone,
    UnitOfMeasurement,
    User,
    Vendor,
    VendorData,
    VendorOrgTag,
)
from controlroom.data.models import Organization as _Organization
from controlroom.interface.admin_filters import (
    OrganizationCustomFieldContextFilter,
    PmcVendorListFilter,
    ProjectOrganizationRoleListFilter,
    UserLoginExpiryFilter,
)
from controlroom.interface.forms import (
    AppTokenAddForm,
    AppTokenChangeForm,
    ClientDataForm,
    ClientStoreTypeForm,
    CountryAdminForm,
    CustomDashboardCollectionForm,
    DashboardTagsForm,
    ElementCategoryForm,
    ElementCategoryMappingForm,
    FacebookPageCrmBoardMappingForm,
    FromToOrgMappingEditModelForm,
    FromToOrgMappingModelForm,
    GlobalDesignFileTagTagForm,
    GstSlabForm,
    LibrarySharedMappingsModelForm,
    OrganizationAddressForm,
    OrganizationCallbackForm,
    OrganizationConfigEditModelForm,
    OrganizationConfigPermissionsInlineForm,
    OrganizationConfigRoleModelForm,
    OrganizationCustomFieldConfigForm,
    OrganizationDocumentForm,
    OrganizationForeignKeyModelForm,
    OrgForm,
    PmcClientMappingModelForm,
    PmcVendorMappingModelForm,
    ProductionDrawingTagForm,
    ProjectOrganizationModelForm,
    RegionForm,
    RegionInlineForm,
    RolePermissionsInlineForm,
    TimezoneAdminForm,
    UnitOfMeasurementForm,
    UserCreationForm,
    VendorDataForm,
    VendorDataInlineFormSet,
)
from core.admin import RoleActionInline, RolePermissionInline
from core.caches import OrgAssignmentCache, UserAuthCache, UserTokenCache
from core.choices import OrganizationDocumentChoices
from core.models import (
    Organization,
    OrganizationConfigCorePermission,
    RegionStateMapping,
    RolePermission,
)
from core.role.services import duplicate_role
from core.services import add_organization_legal_name, system_user_history_create, token_delete_using_user
from crm.data.models import LeadEvent
from crm.lead.data.choices import LeadEventTypeChoices
from dashboard.data.choices import DashboardCollectionType
from element.data.models import ElementLibraryMapping
from element.domain.services import element_library_mapping_delete_many
from execution_tracker.data.choices import DLQStatusType
from execution_tracker.domain.constants import ExecutionTrackerActions
from execution_tracker.domain.exceptions import DLQException
from execution_tracker.domain.services import DLQService
from project.data.models import ProjectStatus
from project.domain.services import (
    organization_user_cache_clear,
    project_organization_status_create,
)
from recce.data.models import RecceData, RecceFile, RecceUser
from rollingbanners import jwt
from vendorv2.domain.services import VendorServiceV2


class ModelAdminMixin:
    def has_module_permission(self, request):
        return request.user.is_staff

    def has_view_permission(self, request, obj=None):
        return self.has_module_permission(request)

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser


class OrganizationDropdownOrdering:
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        formfield = super().formfield_for_foreignkey(db_field, request, **kwargs)
        if db_field.name == "organization":
            formfield.queryset = Organization.objects.order_by("name")
        return formfield


class OrganizationUserAdmin(OrganizationDropdownOrdering, admin.TabularInline):
    model = OrganizationUser
    extra = 0
    fields = ("organization", "is_admin", "role", "joined_at")
    readonly_fields = ("joined_at",)
    fk_name = "user"
    form = OrganizationForeignKeyModelForm

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        if obj and not request.user.has_perm("controlroom.can_switch_organization"):
            formset.form.base_fields["organization"].queryset = Organization.objects.filter(id=obj.org_id)
            formset.form.base_fields["role"].queryset = Role.objects.filter(
                scope=PermissionScope.CORE, organization_id=obj.org_id
            )
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        formfield = super().formfield_for_foreignkey(db_field, request, **kwargs)
        if db_field.name == "role":
            formfield.queryset = Role.objects.filter(scope=PermissionScope.CORE).select_related("organization")
        return formfield

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user", "organization")


class ClientDataInline(admin.StackedInline):
    model = ClientData

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj):
        if obj and hasattr(obj, "client") and obj.client.code and obj.client.code != ClientOnboardService.DEFAULT_CODE:
            return ("code",)
        return tuple()


class ProjectUserAdmin(admin.TabularInline):
    model = ProjectUser
    extra = 0
    fields = ("user", "role", "created_by", "created_at")
    readonly_fields = ("user", "role", "created_by", "created_at")
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(StoreType)
class StoreTypeAdmin(admin.ModelAdmin, SoftDeleteAdminMixin):
    actions = [*admin.ModelAdmin.actions, *SoftDeleteAdminMixin.actions]
    search_fields = ("client__name", "name")
    list_display = (
        "name",
        "client",
        "created_by",
        "created_at",
    )
    exclude = ("updated_by", "updated_at", "deleted_by", "deleted_at", "check_list")
    readonly_fields = (
        "created_at",
        "created_by",
    )
    autocomplete_fields = ("client",)

    def get_queryset(self, request):
        queryset: QuerySet = super().get_queryset(request=request)
        return queryset.available().select_related("created_by")

    def has_soft_delete_permission(self, request):
        return self.has_module_permission(request)

    def get_form(self, request, obj=None, **kwargs):
        form = super(StoreTypeAdmin, self).get_form(request, obj, **kwargs)
        if "client" in form.base_fields.keys():
            form.base_fields["client"].queryset = Client.objects.all()
        return form

    def save_model(self, request, obj, form, change):
        if change:
            obj.updated_by = request.user
        else:
            obj.created_by = request.user
        return super().save_model(request, obj, form, change)


@admin.register(ElementLibrary)
class ElementLibraryAdmin(admin.ModelAdmin, SoftDeleteAdminMixin):
    actions = [*admin.ModelAdmin.actions, *SoftDeleteAdminMixin.actions]
    search_fields = ("client__name", "name")
    list_display = ("name", "client", "created_by", "created_at")
    exclude = ("created_by", "updated_by", "updated_at", "deleted_by", "deleted_at")
    form = ClientStoreTypeForm

    def get_queryset(self, request):
        queryset: QuerySet = super().get_queryset(request=request)
        return queryset.available().select_related("created_by", "client")

    def soft_delete_extra_task(self, request, queryset):
        mapping_id_list = list(
            ElementLibraryMapping.objects.filter(library_id__in=queryset.values_list("id", flat=True)).values_list(
                "id", flat=True
            )
        )
        element_library_mapping_delete_many(mapping_id_list=mapping_id_list, user_id=request.user.pk)

    def has_soft_delete_permission(self, request):
        return self.has_module_permission(request)

    def get_form(self, request, obj=None, **kwargs):
        form = super(ElementLibraryAdmin, self).get_form(request, obj, **kwargs)
        if "client" in form.base_fields.keys():
            form.base_fields["client"].queryset = Organization.objects.filter(type=OrganizationType.CLIENT)
        return form

    def save_model(self, request, obj, form, change):
        if change:
            obj.updated_by = request.user
        else:
            obj.created_by = request.user
        return super().save_model(request, obj, form, change)


def get_or_create_client(organization_id):
    ClientData.objects.get_or_create(organization_id=organization_id)


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    search_fields = ("name", "client__code")
    list_display = ("name", "code")
    fields = ("name",)
    inlines = (ClientDataInline,)

    def get_queryset(self, request):
        return super(ClientAdmin, self).get_queryset(request).prefetch_related("client")

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        on_commit(partial(get_or_create_client, organization_id=obj.id))

    def has_delete_permission(self, request, obj=None) -> bool:
        return False

    def has_change_permission(self, request, obj=None):
        return False


class TaxTypeInline(admin.TabularInline):
    model = CountryTaxMapping
    extra = 0
    fields = ("tax_type", "max_slab_percent", "is_default")


class CurrencyInline(admin.TabularInline):
    model = CountryCurrencyMapping
    extra = 0
    fields = ("currency", "is_default")


class TimezoneInline(admin.TabularInline):
    model = CountryTimezoneMapping
    extra = 0
    fields = ("timezone", "is_default")


class StateInline(admin.TabularInline):
    model = State
    extra = 0


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    search_fields = ("name", "code")
    list_display = ("name", "code")
    inlines = (TaxTypeInline, CurrencyInline, TimezoneInline, StateInline)
    exclude = ("uid_document",)
    readonly_fields = ("created_at", "updated_at", "created_by", "updated_by")
    form = CountryAdminForm

    def save_model(self, request, obj, form, change):
        """
        Given a model instance save it to the database.
        """
        if change:
            obj.updated_by_id = request.user.pk
            obj.updated_at = timezone.now()
        else:
            obj.created_by_id = request.user.pk
        if obj.uid_field:
            obj.uid_document_id = obj.uid_field.document_config_id
        obj.save()

    def get_form(self, request, obj=None, **kwargs):
        form = super(CountryAdmin, self).get_form(request, obj, **kwargs)
        if obj is None and "uid_field" in form.base_fields:
            form.base_fields.pop("uid_field")
            return form

        if "uid_field" in form.base_fields.keys():
            form.base_fields["uid_field"].queryset = (
                OrganizationDocumentFieldConfig.objects.select_related("document_config", "document_config__section")
                .filter(
                    type=CustomFieldTypeChoices.TEXT,
                    document_config__country_id=obj.id,
                    document_config__multiple_allowed=False,
                )
                .all()
            )
        return form

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("uid_field")


class CityInline(admin.TabularInline):
    model = City


@admin.register(State)
class StateAdmin(admin.ModelAdmin):
    list_display = ("name", "country", "is_active")
    search_fields = ("name", "country__name")
    inlines = (CityInline,)
    extra = 0


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin, SoftDeleteAdminMixin):
    actions = [*admin.ModelAdmin.actions, *SoftDeleteAdminMixin.actions]
    search_fields = ("client__name", "job_id", "name")
    list_display = ("job_id", "name", "is_deleted")
    fields = ("name", "job_id")
    readonly_fields = ("name", "job_id")
    inlines = (ProjectUserAdmin,)

    def has_soft_delete_permission(self, request):
        return self.has_module_permission(request)

    def get_queryset(self, request):
        return super(ProjectAdmin, self).get_queryset(request).select_related("client")

    @admin.display(
        description="is deleted",
        boolean=True,
    )
    def is_deleted(self, obj):
        return True if obj.deleted_by else False

    @transaction.atomic
    def soft_delete(self, request, queryset):
        project_ids = list(queryset.values_list("id", flat=True))
        lead_event_ids = list(
            LeadEvent.objects.select_related("lead")
            .filter(lead__project_id__in=project_ids, type=LeadEventTypeChoices.CONVERTED_TO_PROJECT.value)
            .values_list("id", flat=True)
        )

        LeadEvent.objects.filter(id__in=lead_event_ids).soft_delete(user_id=request.user.pk)

        super().soft_delete(request, queryset)


def transfer_user_data(*, app_user: User, bitrix_user: User):
    RecceUser.objects.filter(user_id=app_user.pk).update(user_id=bitrix_user.pk)
    RecceFile.objects.filter(uploaded_by_id=app_user.pk).update(uploaded_by_id=bitrix_user.pk)
    RecceFile.objects.filter(deleted_by_id=app_user.pk).update(deleted_by_id=bitrix_user.pk)
    RecceFile.objects.filter(updated_by_id=app_user.pk).update(updated_by_id=bitrix_user.pk)
    RecceData.objects.filter(submitted_by_id=app_user.pk).update(submitted_by_id=bitrix_user.pk)
    RecceData.objects.filter(updated_by_id=app_user.pk).update(updated_by_id=bitrix_user.pk)


@admin.register(User)
class UserAdmin(_UserAdmin, SoftDeleteAdminMixin):
    list_filter = (UserLoginExpiryFilter,)
    list_display = (
        "id",
        "name",
        "organization",
        "email",
        "phone_number",
        "is_active",
        "is_deleted",
        "verified",
        "is_expired",
        "login_link",
    )
    readonly_fields = ("org", "deleted_at", "deleted_by")
    search_fields = ("first_name", "last_name", "email", "phone_number", "organizations__name")
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "first_name",
                    "last_name",
                    "email",
                    "phone_number",
                    "photo",
                    "is_active",
                    "is_verified",
                    "login_expired_at",
                    "timezone",
                ),
            },
        ),
    )
    fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "first_name",
                    "last_name",
                    "email",
                    "phone_number",
                    "photo",
                    "is_active",
                    "is_verified",
                    "org",
                    "deleted_at",
                    "deleted_by",
                    "login_expired_at",
                    "timezone",
                ),
            },
        ),
    )
    actions = [*admin.ModelAdmin.actions, *SoftDeleteAdminMixin.actions, "log_out_from_all_devices"]
    add_form = UserCreationForm
    request = None

    def changelist_view(self, request, *args, **kwargs):
        self.request = request
        return super().changelist_view(request, *args, **kwargs)

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id:
            self.inlines = [
                OrganizationUserAdmin,
            ]
        else:
            self.inlines = []
        return super().changeform_view(request, object_id, form_url, extra_context)

    @admin.display(description="Login Link")
    def login_link(self, user):
        return (
            format_html(
                "<a href='%s%s' target='_blank'>%s</a>"
                % (user.login_url, f"&auid={self.request.user.hash_id}", "Login")
            )
            if self.request.user.pk == user.pk or self.request.user.has_perm("controlroom.can_login_directly")
            else "-"
        )

    @admin.display(description="Deleted", boolean=True)
    def is_deleted(self, user):
        return user.is_deleted

    @admin.display(description="Verified", boolean=True)
    def verified(self, user):
        return user.verified

    @admin.display(description="Expired", boolean=True)
    def is_expired(self, user):
        return user.is_expired

    @transaction.atomic
    def merge_user_contact(self, request, queryset):
        if len(queryset) != 2:
            messages.error(request, "Two users needs to be selected")
            return HttpResponseRedirect(request.get_full_path())

        user1 = queryset.first()
        user2 = queryset.last()

        if ((user1.phone_number and not user2.phone_number) and (not user1.email and user2.email)) or (
            user2.phone_number and not user1.phone_number and (not user2.email and user1.email)
        ):
            app_user = user1 if user1.phone_number else user2
            bitrix_user = user1 if user1.email else user2

            if OrganizationUser.objects.select_related("organization").filter(
                user_id=bitrix_user.id, organization__name="91Squarefeet"
            ).exists() and not (OrganizationUser.objects.filter(user_id=app_user.id).exists()):
                transfer_user_data(app_user=app_user, bitrix_user=bitrix_user)

                bitrix_user.phone_number = app_user.phone_number
                app_user.delete()
                bitrix_user.save(update_fields=["phone_number"])

                messages.success(request, "Successfully merged.")
            else:
                messages.error(request, "Cannot Merge these users.")

        else:
            messages.error(request, "Cannot Merge these users.")

    merge_user_contact.short_description = "Merge Users"

    @transaction.atomic
    def log_out_from_all_devices(self, request, queryset):
        for user in queryset:
            if user.pk and hasattr(user, "auth_token") and user.auth_token is not None:
                UserTokenCache.delete(instance_id=user.auth_token.key)
                UserAuthCache.delete(instance_id=user.pk)
            OrgAssignmentCache.delete(instance_id=user.org_id)
            organization_user_cache_clear(user_id=user.id, org_id=user.org_id)
            token_delete_using_user(user_id=user.id)
        messages.success(request, "Successfully Logged Out From All Devices.")

    log_out_from_all_devices.short_description = "Log Out From All Devices"

    def save_model(self, request, obj, form, change):
        res = super().save_model(request, obj, form, change)
        return res

    def get_queryset(self, request):
        return (
            super(UserAdmin, self)
            .get_queryset(request)
            .select_related("org")
            .filter(app_token__isnull=True)
            .prefetch_related("organizations")
            .exclude(id=F("org__system_user_id"))
        )


@admin.register(PmcClientMapping)
class PmcClientMappingAdmin(admin.ModelAdmin):
    list_display = ("id", "pmc", "client")
    form = PmcClientMappingModelForm
    search_fields = ("client__name", "pmc__name")

    def has_change_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("pmc", "client")


@admin.register(OrganizationSystemUser)
class OrganizationSystemAdmin(admin.ModelAdmin):
    actions_on_top = False
    actions_selection_counter = False
    list_per_page = 20
    search_fields = ("name",)
    list_display = ("name", "bypass_login_action")
    list_display_links = None

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            url(
                r"^(?P<org_id>.+)/login/$",
                self.admin_site.admin_view(self.process_login_link),
                name="login-link",
            ),
        ]
        return custom_urls + urls

    def bypass_login_action(self, obj):
        return format_html(
            '<a class="button" href="{}" target="_blank">Login</a>&nbsp;',
            reverse("admin:login-link", kwargs={"org_id": obj.pk}),
        )

    def process_login_link(self, request, *args, **kwargs):
        organization_id = kwargs["org_id"]
        organization = self.get_object(request, organization_id)
        if not organization.system_user:
            key = AppToken.generate_key()
            user = User(
                first_name="System",
                last_name="User",
                username=key,
                password=key,
                org_id=organization_id,
                email=None,
                phone_number=None,
                is_verified=True,
            )
            user.save()
            org_user = OrganizationUser(
                organization_id=organization_id,
                user_id=user.pk,
                is_admin=True,
                joined_by_id=request.user.id,
            )
            org_user.save()
            organization.system_user_id = user.pk
            organization.save()
            system_user_history_create(
                accessed_by_id=request.user.id,
                system_user_id=user.pk,
                organization_id=organization_id,
            )
            return redirect(organization.login_url)
        token_delete_using_user(user_id=organization.system_user_id)
        system_user_history_create(
            accessed_by_id=request.user.id,
            system_user_id=organization.system_user_id,
            organization_id=organization_id,
        )
        return redirect(organization.login_url)

    bypass_login_action.short_description = "Action"
    bypass_login_action.allow_tags = True


@admin.register(LibrarySharedMappings)
class LibrarySharedMappingsAdmin(admin.ModelAdmin):
    list_display = ("id", "shared_to", "shared_by", "library")
    form = LibrarySharedMappingsModelForm
    search_fields = ("client__name", "vendor__name")

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("vendor", "client")

    def shared_to(self, obj):
        return obj.vendor

    def shared_by(self, obj):
        return obj.client


@admin.register(PmcVendorMapping)
class PmcVendorMappingAdmin(admin.ModelAdmin):
    list_display = ("id", "pmc", "vendor")
    form = PmcVendorMappingModelForm
    list_filter = (PmcVendorListFilter,)
    search_fields = ("vendor__name",)

    def has_change_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("pmc", "vendor")


class BaseRolePermissionsInlineMixin:
    @abc.abstractmethod
    def get_permissions(self):
        pass

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        formfield = super().formfield_for_choice_field(db_field, request, **kwargs)
        if db_field.name == "permission":
            formfield.choices = [
                (
                    permission,
                    (
                        "Can Access Project All Details"
                        if permission == "can_access_project_attachment"
                        else permission.replace("_", " ").title()
                    ),
                )
                for permission in self.get_permissions()
            ]
        return formfield


class ProjectScopeUserPermissionsInline(BaseRolePermissionsInlineMixin, RolePermissionInline):
    def get_permissions(self):
        return PROJECT_PERMISSION_SECTION_MAPPINGS.keys()


@admin.register(ProjectUserRole)
class ProjectUserRoleAdmin(admin.ModelAdmin, DuplicateAdminMixin):
    actions = [*admin.ModelAdmin.actions, *DuplicateAdminMixin.actions]
    list_display = ("name", "organization")
    search_fields = ("organization__name", "name")
    form = OrganizationForeignKeyModelForm
    fields = ("created_by", "organization", "name", "is_active")
    inlines = (ProjectScopeUserPermissionsInline, RoleActionInline)

    def copy(self, instance: ProjectUserRole, created_by_id: int):
        duplicate_role(role=instance, created_by_id=created_by_id)

    def has_duplicate_permission(self, request):
        return self.has_add_permission(request)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return (
                ("organization", "created_by")
                if ProjectUser.objects.filter(role_id=obj.id).count()
                else ("created_by",)
            )
        else:
            return tuple()

    def get_form(self, request, obj=None, **kwargs):
        form = super(ProjectUserRoleAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def has_delete_permission(self, request, obj=None) -> bool:
        return False

    def save_model(self, request, obj, form, change):
        obj.level = None
        super().save_model(request, obj, form, change)


@admin.register(BusinessCategory)
class BusinessCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "organization")
    search_fields = ("name", "organization__name")
    autocomplete_fields = ["organization"]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(Scope)
class ScopeAdmin(OrganizationDropdownOrdering, admin.ModelAdmin):
    list_display = ("name", "organization")
    search_fields = ("name", "organization__name")

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj is None and "organization" in form.base_fields:
            form.base_fields["organization"].widget = AutocompleteSelect(
                form.base_fields["organization"].queryset.model._meta.get_field("organization").remote_field,
                admin_site=admin.site,
            )
        return form


@admin.register(OrganizationAddress)
class OrganizationAddressAdmin(OrganizationDropdownOrdering, admin.ModelAdmin):
    list_display = ("organization", "header")
    search_fields = ("organization__name", "header")
    form = OrganizationForeignKeyModelForm

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(OrganizationLegalEntity)
class OrganizationLegalEntityAdmin(OrganizationDropdownOrdering, admin.ModelAdmin):
    list_display = ("organization", "name")
    search_fields = ("organization__name", "name")
    form = OrganizationForeignKeyModelForm

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(OrganizationOrderPaymentTerm)
class OrganizationOrderPaymentTermAdmin(OrganizationDropdownOrdering, admin.ModelAdmin):
    list_display = ("organization", "title", "description", "created_by")
    search_fields = ("organization__name",)
    form = OrganizationForeignKeyModelForm

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")

    def get_form(self, request, obj=None, **kwargs):
        form = super(OrganizationOrderPaymentTermAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form


class OrganizationConfigRoleAdmin(admin.TabularInline):
    model = OrganizationConfigRole
    extra = 0
    form = OrganizationConfigRoleModelForm

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset.form = OrganizationConfigRoleModelForm
        formset.form.base_fields["role"].queryset = (
            Role.objects.filter(scope=PermissionScope.PROJECT)
            .select_related("organization")
            .filter(organization_id=obj.organization_id)
        )
        return formset

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("role", "role__organization")


class RoleConfigPermissionFormSet(BaseInlineFormSet):
    DEFAULT_PERMISSION_LIST = [
        Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS,
        Permissions.CAN_CREATE_ELEMENT_LIBRARY,
        Permissions.CAN_APPROVE_ALL_APPROVAL_REQUESTS,
        Permissions.CAN_CREATE_CLIENT,
        Permissions.CAN_ACCESS_SHARED_ELEMENT_LIBRARY,
        Permissions.CAN_ACCESS_MANAGE_CLIENT,
        Permissions.CAN_EDIT_MANAGE_VENDOR,
        Permissions.CAN_EDIT_CLIENT,
        Permissions.CAN_ACCESS_ELEMENT_LIBRARY,
        Permissions.CAN_ACCESS_MY_ELEMENT_LIBRARY,
        Permissions.CAN_VIEW_RATE_CONTRACT,
        Permissions.CAN_ACCESS_CLIENT_PAYMENT_REQUEST,
        Permissions.CAN_ACCESS_MANAGE_VENDOR,
        Permissions.CAN_ACCESS_GOOGLE_SHEET,
        Permissions.CAN_CREATE_PROJECT,
        Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST,
        Permissions.CAN_EDIT_APPROVAL_HIERARCHY,
        Permissions.CAN_SHARE_PROJECT,
        Permissions.CAN_LINK_SNAG_ITEMS,
        Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX,
    ]

    def __init__(self, *args, **kwargs):
        super(RoleConfigPermissionFormSet, self).__init__(*args, **kwargs)
        if self.instance and not self.instance.is_new:
            self.initial = []
        else:
            self.initial = [{"permission": p.value} for p in self.DEFAULT_PERMISSION_LIST]


class ProjectRoleConfigPermissionFormSet(BaseInlineFormSet):
    DEFAULT_PERMISSION_LIST = [
        Permissions.CAN_CREATE_INCOMING_ORDER,
        Permissions.CAN_ACCESS_OTHER_EXPENSE,
        Permissions.CAN_CLOSE_ORDER,
        Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT,
        Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY,
        Permissions.CAN_CREATE_PROPOSAL,
        Permissions.CAN_CANCEL_INCOMING_ORDER,
        Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL,
        Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and not self.instance.is_new:
            self.initial = []
        else:
            self.initial = [{"permission": p.value} for p in self.DEFAULT_PERMISSION_LIST]


class OrganizationConfigCorePermissionInline(BaseRolePermissionsInlineMixin, admin.TabularInline):
    model = OrganizationConfigCorePermission
    extra = 0
    form = OrganizationConfigPermissionsInlineForm
    formset = RoleConfigPermissionFormSet

    PERMISSION_LIST = list(
        set(CORE_PERMISSION_SECTION_MAPPINGS.keys())
        - set(DIRECT_CORE_PERMISSIONS)
        - {Permissions.CAN_ACCESS_METABASE_DASHBOARD}
        - set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys())
    ) + [Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX]

    def get_extra(self, request, obj, *args, **kwargs) -> int:
        if obj is None or obj.is_new:
            return len(RoleConfigPermissionFormSet.DEFAULT_PERMISSION_LIST)
        return super().get_extra(request, obj, *args, **kwargs)

    def get_permissions(self):
        return self.PERMISSION_LIST

    verbose_name = "Organization Config Core Permission"
    verbose_name_plural = "Organization Config Core Permissions"

    def get_queryset(self, request):
        queryset = super().get_queryset(request).filter(permission__in=self.get_permissions())
        return queryset


class OrganizationBlockedActionInline(admin.TabularInline):
    model = OrganizationBlockedAction
    extra = 0


class ProjectConfigCorePermissionInline(BaseRolePermissionsInlineMixin, admin.TabularInline):
    model = OrganizationConfigCorePermission
    extra = 0
    verbose_name = "Project Config Core Permission"
    verbose_name_plural = "Project Config Core Permissions"
    form = OrganizationConfigPermissionsInlineForm
    formset = ProjectRoleConfigPermissionFormSet
    PERMISSION_LIST = list(
        (
            set(CORE_PERMISSION_SECTION_MAPPINGS.keys())
            - set(DIRECT_CORE_PERMISSIONS)
            - {Permissions.CAN_ACCESS_METABASE_DASHBOARD}
        )
        & set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys())
    )

    def get_extra(self, request, obj, *args, **kwargs) -> int:
        if obj is None or obj.is_new:
            return len(ProjectRoleConfigPermissionFormSet.DEFAULT_PERMISSION_LIST)
        return super().get_extra(request, obj, *args, **kwargs)

    def get_permissions(self):
        return self.PERMISSION_LIST

    def get_queryset(self, request):
        queryset = super().get_queryset(request).filter(permission__in=self.get_permissions())
        return queryset


@admin.register(OrganizationConfig)
class OrganizationConfigAdmin(OrganizationDropdownOrdering, admin.ModelAdmin):
    list_display = ("organization",)
    search_fields = ("organization__name",)
    readonly_fields = ("order_cc_emails", "order_receiver_emails")

    def get_form(self, request, obj=None, **kwargs):
        if obj:
            self.form = OrganizationConfigEditModelForm
        else:
            self.form = OrganizationForeignKeyModelForm
            self.form.base_fields["organization"].widget = AutocompleteSelect(
                self.form.base_fields["organization"].queryset.model._meta.get_field("organization").remote_field,
                admin_site=admin.site,
            )

        form = super(OrganizationConfigAdmin, self).get_form(request, obj, **kwargs)

        if obj is None and "organization" in form.base_fields:
            form.base_fields["organization"].widget = AutocompleteSelect(
                form.base_fields["organization"].queryset.model._meta.get_field("organization").remote_field,
                admin_site=admin.site,
            )

        return form

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id:
            self.inlines = [
                OrganizationConfigRoleAdmin,
                OrganizationConfigCorePermissionInline,
                ProjectConfigCorePermissionInline,
                OrganizationBlockedActionInline,
            ]
        else:
            self.inlines = []
        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization", "order_cc_emails", "order_receiver_emails")
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(PMC)
class PMCAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)
    fields = ("name",)
    readonly_fields = ("name",)


class VendorDataInline(admin.StackedInline):
    model = VendorData
    fk_name = "organization"
    autocomplete_fields = ["referal_organization"]
    formset = VendorDataInlineFormSet

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj):
        if obj and hasattr(obj, "vendor") and obj.vendor.code:
            return (
                "code",
                "serial_number",
                "vendor_pk_id",
            )
        if obj:
            return (
                "serial_number",
                "vendor_pk_id",
            )
        return ("vendor_pk_id",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    search_fields = ("name", "vendor__code")
    list_display = ("name", "code")
    fields = ("name",)
    inlines = (VendorDataInline,)

    def get_queryset(self, request):
        return super(VendorAdmin, self).get_queryset(request).prefetch_related("vendor")

    def has_delete_permission(self, request, obj=None) -> bool:
        return False

    def has_change_permission(self, request, obj=None):
        return False


class ProjectLevelCoreScopePermissionsInline(BaseRolePermissionsInlineMixin, admin.StackedInline):
    model = RolePermission
    extra = 0
    verbose_name = "Project Level Permission"
    verbose_name_plural = "Project Level Permissions"
    form = RolePermissionsInlineForm

    def get_permissions(self):
        return list(
            set(CORE_PERMISSION_SECTION_MAPPINGS.keys()).intersection(
                set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys())
                - (set(CORE_PERMISSION_SECTION_MAPPINGS.keys()) - set(DIRECT_CORE_PERMISSIONS)).intersection(
                    PROJECT_PERMISSION_SECTION_MAPPINGS.keys()
                )
            )
        )

    def get_queryset(self, request):
        queryset = super().get_queryset(request).filter(permission__in=self.get_permissions())
        return queryset


class RolePermissionFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(RolePermissionFormSet, self).__init__(*args, **kwargs)
        if kwargs.get("instance") and kwargs.get("instance").pk is not None:
            self.intial = []
        else:
            self.initial = [
                {"permission": Permissions.CAN_ACCESS_ELEMENT_LIBRARY.value},
                {"permission": Permissions.CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS.value},
                {"permission": Permissions.CAN_ACCESS_MANAGE_VENDOR.value},
                {"permission": Permissions.CAN_CREATE_PROJECT.value},
                {"permission": Permissions.CAN_ACCESS_WORK_PROGRESS_MATERIALS.value},
                {"permission": Permissions.CAN_ACCESS_MANAGE_CLIENT.value},
                {"permission": Permissions.CAN_ACCESS_MY_ELEMENT_LIBRARY.value},
                {"permission": Permissions.CAN_ACCESS_ORG_SETTINGS.value},
                {"permission": Permissions.CAN_CREATE_CLIENT.value},
                {"permission": Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS.value},
                {"permission": Permissions.CAN_ACCESS_SHARED_ELEMENT_LIBRARY.value},
                {"permission": Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX.value},
            ]


class CoreLevelCoreScopePermissionsInline(BaseRolePermissionsInlineMixin, admin.StackedInline):
    model = RolePermission
    extra = 0
    verbose_name = "Core Level Permission"
    verbose_name_plural = "Core Level Permissions"
    form = RolePermissionsInlineForm
    formset = RolePermissionFormSet

    def get_extra(self, request, obj, *args, **kwargs) -> int:
        if obj is None or obj.pk is None:
            return 13
        return super().get_extra(request, obj, *args, **kwargs)

    def get_permissions(self):
        return list(set(CORE_PERMISSION_SECTION_MAPPINGS.keys()) - set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys())) + [
            Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX
        ]

    def get_queryset(self, request):
        queryset = super().get_queryset(request).filter(permission__in=self.get_permissions())
        return queryset


class FeatureLevelProjectScopePermissionsInline(BaseRolePermissionsInlineMixin, admin.StackedInline):
    model = RolePermission
    extra = 0
    verbose_name = "Feature Level Permission"
    verbose_name_plural = "Feature Level Permissions"
    form = RolePermissionsInlineForm
    formset = RolePermissionFormSet

    def get_permissions(self):
        return list(
            (set(CORE_PERMISSION_SECTION_MAPPINGS.keys()) - set(DIRECT_CORE_PERMISSIONS)).intersection(
                PROJECT_PERMISSION_SECTION_MAPPINGS.keys()
            )
        )

    def get_queryset(self, request):
        queryset = super().get_queryset(request).filter(permission__in=self.get_permissions())
        return queryset


@admin.register(OrganizationUserRole)
class OrganizationUserRoleAdmin(admin.ModelAdmin, DuplicateAdminMixin):
    actions = [*admin.ModelAdmin.actions, *DuplicateAdminMixin.actions]
    list_display = ("name", "organization")
    search_fields = ("organization__name", "name")
    form = OrganizationForeignKeyModelForm
    fields = ("created_by", "organization", "name")
    inlines = (
        CoreLevelCoreScopePermissionsInline,
        ProjectLevelCoreScopePermissionsInline,
        FeatureLevelProjectScopePermissionsInline,
    )

    def copy(self, instance: OrganizationUserRole, created_by_id: int):
        duplicate_role(role=instance, created_by_id=created_by_id)

    def has_duplicate_permission(self, request):
        return self.has_add_permission(request)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return (
                ("organization", "created_by")
                if OrganizationUser.objects.filter(role_id=obj.id).count()
                else ("created_by",)
            )
        else:
            return tuple()

    def get_form(self, request, obj=None, **kwargs):
        form = super(OrganizationUserRoleAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def has_delete_permission(self, request, obj=None) -> bool:
        return False


class ProjectScopeOrgPermissionsInline(RolePermissionInline):
    def formfield_for_choice_field(self, db_field, request, **kwargs):
        formfield = super().formfield_for_choice_field(db_field, request, **kwargs)
        if db_field.name == "permission":
            formfield.choices = [
                (permission, permission.replace("_", " ").title())
                for permission in set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys()) - set(DIRECT_PROJECT_PERMISSIONS)
            ]
        return formfield


@admin.register(ProjectOrganizationRole)
class ProjectOrganizationRoleAdmin(admin.ModelAdmin):
    list_display = ("name", "organization")
    search_fields = ("organization__name", "name")
    form = OrganizationForeignKeyModelForm
    fields = ("created_by", "organization", "name", "type")
    list_filter = (ProjectOrganizationRoleListFilter,)
    inlines = (ProjectScopeOrgPermissionsInline, RoleActionInline)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return "organization", "created_by"
        else:
            return tuple()

    def get_form(self, request, obj=None, **kwargs):
        form = super(ProjectOrganizationRoleAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def has_delete_permission(self, request, obj=None) -> bool:
        return False


@admin.register(FromToOrgMapping)
class FromToOrgMappingAdmin(admin.ModelAdmin, SoftDeleteAdminMixin):
    actions = [*SoftDeleteAdminMixin.actions]
    list_display = (
        "org_from",
        "org_to",
        "is_deleted",
    )
    search_fields = ("org_from__name", "org_to__name")

    def has_delete_permission(self, request, obj=None):
        return False

    def has_soft_delete_permission(self, request):
        return self.has_module_permission(request)

    def get_form(self, request, obj=None, **kwargs):
        if obj:
            self.form = FromToOrgMappingEditModelForm
        else:
            self.form = FromToOrgMappingModelForm
        form = super(FromToOrgMappingAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("created_by",)
        else:
            return tuple()

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("org_from", "org_to")

    @admin.display(
        description="is deleted",
        boolean=True,
    )
    def is_deleted(self, obj):
        return True if obj.deleted_by else False


@admin.register(ProjectOrganization)
class ProjectOrganizationAdmin(admin.ModelAdmin):
    list_display = ("project", "organization", "role", "assigned_by")
    search_fields = ("project__name", "project__job_id", "organization__name", "assigned_by__name")
    form = ProjectOrganizationModelForm

    def get_form(self, request, obj=None, **kwargs):
        form = super(ProjectOrganizationAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("project", "organization")

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if not ProjectStatus.objects.filter(
            project_id=form.cleaned_data["project"].id, organization_id=form.cleaned_data["organization"].id
        ).exists():
            project_organization_status_create(project_org_obj=obj)


@admin.register(ManpowerCategory)
class ManpowerCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "is_active")

    def get_form(self, request, obj=None, **kwargs):
        form = super(ManpowerCategoryAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form


@admin.register(DeductionType)
class DeductionTypeAdmin(admin.ModelAdmin):
    list_display = ("id", "name")
    search_fields = ("name",)


@admin.register(DeductionRemark)
class DeductionRemarkAdmin(admin.ModelAdmin):
    list_display = ("deduction_type", "remark")
    search_fields = ("remark", "deduction_type__name")

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("deduction_type")


@admin.register(OrganizationCustomFieldConfig)
class OrganizationCustomFieldConfigAdmin(admin.ModelAdmin):
    list_display = ("org", "context")
    search_fields = ("org__name",)
    list_filter = (OrganizationCustomFieldContextFilter,)
    form = OrganizationCustomFieldConfigForm


@admin.register(AppToken)
class AppTokenAdmin(admin.ModelAdmin):
    actions = [*admin.ModelAdmin.actions, "regenerate"]
    list_display = ("key", "organization", "created_by", "created_at")
    search_fields = ("key", "user__org__name")
    ordering = ("-created_at",)

    @transaction.atomic
    def regenerate(self, request, queryset):
        for app_token in queryset:
            UserTokenCache.delete(instance_id=app_token.key)
            UserAuthCache.delete(instance_id=app_token.user_id)
            app_token.regenerate_key()
        messages.success(request, "Successfully regenerated tokens.")

    regenerate.short_description = "Regenerate Tokens"
    regenerate.allowed_permissions = ("regenerate",)

    @staticmethod
    def has_regenerate_permission(request):
        return True

    def has_delete_permission(self, request, obj=None) -> bool:
        return False

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return "key", "created_by", "created_at", "token"
        return ()

    def get_form(self, request, obj=None, **kwargs):
        if obj:
            self.form = AppTokenChangeForm
            org_user = OrganizationUser.objects.filter(user_id=obj.user_id, organization_id=obj.user.org_id).first()
            if org_user:
                self.form.base_fields["role"].initial = org_user.role
            self.form.base_fields["role"].queryset = OrganizationUserRole.objects.filter(
                organization_id=obj.user.org_id
            ).select_related("organization")
        else:
            self.form = AppTokenAddForm
        return super().get_form(request, obj, **kwargs)

    def get_queryset(self, request):
        queryset = super().get_queryset(request).select_related("user", "user__org")
        return queryset.exclude(created_by_id=1)

    def save_form(self, request, form, change):
        if not change:
            token = AppToken()
            key = AppToken.generate_key()
            user = User(
                first_name="System",
                last_name="User",
                username=key,
                password=key,
                org=form.cleaned_data.get("organization"),
                email=None,
                phone_number=None,
                is_verified=True,
            )
            user.save()
            org_user = OrganizationUser(
                organization=form.cleaned_data.get("organization"),
                user_id=user.pk,
            )
            org_user.save()
            token.key = key
            token.user = user
            token.created_by = request.user
            token.save()
            return token
        token = super().save_form(request, form, change)
        org_user = OrganizationUser.objects.filter(user_id=token.user_id, organization_id=token.user.org_id).first()
        if org_user:
            org_user.role = form.cleaned_data.get("role")
            org_user.save()
        return token

    @admin.display(description="Organization")
    def organization(self, obj):
        return obj.user.org.name

    @admin.display(description="Token")
    def token(self, app_token):
        user = app_token.user
        org_id = user.org_id
        org_user = OrganizationUser(
            organization_id=org_id,
            user_id=user.id,
            role_id=None,
            is_admin=False,
        )
        context = {
            "is_app_token": True,
            "org_user": org_user,
            "org_observer": True,
            "user": user,
        }
        serializer = TokenDataSerializer(instance=app_token, context=context).data
        token = jwt.encode(serializer, jwt_expiry_utc=None, infinite_valid=True)
        return mark_safe(
            f"""
                            <a
                                href="#"
                                onclick="
                                    var input = document.createElement('input');
                                    input.setAttribute('value', '{token}');
                                    document.body.appendChild(input);
                                    input.select();
                                    var result = document.execCommand('copy');
                                    document.body.removeChild(input);
                                    return result;
                                "
                            >
                                Copy Token
                            </a>
                        """
        )


class OrganizationCallbackUrlEventAdmin(admin.TabularInline):
    model = OrganizationCallbackUrlEvent
    extra = 0

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        formfield = super().formfield_for_choice_field(db_field, request, **kwargs)
        if db_field.name == "event":
            formfield.choices = [
                (event, event.replace("_", " ").title())
                for event in [
                    Events.TASK_CREATED.value,
                    Events.PROJECT_CREATED.value,
                    Events.SINGLE_RECCE_APPROVED.value,
                    Events.RECCE_SUBMITTED.value,
                    Events.PROJECT_POC_ASSIGNED.value,
                ]
            ]
        return formfield


@admin.register(OrgnanizationCallbackUrl)
class OrganizationCallbackUrlAdmin(admin.ModelAdmin):
    list_display = ("organization", "url")
    search_fields = ("url", "organization__name")
    inlines = (OrganizationCallbackUrlEventAdmin,)
    autocomplete_fields = ("organization",)
    form = OrganizationCallbackForm

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


class SystemCallbackUrlEventAdmin(admin.TabularInline):
    model = SystemCallbackUrlEvent
    extra = 0

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        formfield = super().formfield_for_choice_field(db_field, request, **kwargs)
        if db_field.name == "event":
            formfield.choices = [
                (event, event.replace("_", " ").title())
                for event in [
                    Events.USER_CREATED.value,
                    Events.ORDER_CREATED.value,
                    Events.ORDER_SENT.value,
                    Events.TASK_DONE.value,
                    Events.ORDER_MODIFIED.value,
                    Events.DESIGN_FILE_UPLOADED.value,
                    Events.ORDER_CANCELLED.value,
                    Events.SNAG_POC_ALLOTED.value,
                    Events.SNAG_ASSIGNED.value,
                    Events.DESIGN_FILE_APPROVED.value,
                    Events.DESIGN_NEW_VERSION_UPLOADED.value,
                    Events.APPROVAL_REQUEST_CREATED.value,
                    Events.APPROVAL_REQUEST_APPROVED.value,
                    Events.BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE.value,
                    Events.ORDER_SENT_WITHOUT_NOTFICATION.value,
                    Events.ORGANIZATION_CREATED.value,
                    Events.PO_REVISED.value,
                    Events.PO_UPLOADED.value,
                    Events.APPROVAL_REQUEST_EDITED.value,
                    Events.PROPOSAL_APPROVED.value,
                    Events.PROPOSAL_REJECTED.value,
                    Events.PROPOSAL_CREATED.value,
                    Events.PAYMENT_ENTRY_CREATED.value,
                    Events.LEAD_QUOTATION_SUBMITTED.value,
                    Events.DRAFT_ORDER_MODIFIED.value,
                ]
            ]
        return formfield


@admin.register(SystemCallbackUrl)
class SystemCallbackUrlAdmin(admin.ModelAdmin):
    list_display = ("url",)
    search_fields = ("url",)
    inlines = (SystemCallbackUrlEventAdmin,)

    def get_queryset(self, request):
        return super().get_queryset(request)


@admin.register(VendorOrgTag)
class VendorOrgTagAdmin(admin.ModelAdmin):
    list_display = ("name", "organization")
    search_fields = ("name", "organization__name")
    autocomplete_fields = ("organization",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(ElementCategoryOrgMapping)
class ElementCategoryOrgMappingAdmin(admin.ModelAdmin):
    list_display = ("category", "organization")
    search_fields = ("category__name", "organization__name")
    autocomplete_fields = ("organization",)
    form = ElementCategoryMappingForm

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization", "category")


@admin.register(ElementCategory)
class ElementCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "code")
    search_fields = ("name", "code")
    form = ElementCategoryForm

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(CommonDomain)
class CommonDomainAdmin(admin.ModelAdmin):
    list_display = ("domain_name",)
    search_fields = ("domain_name",)

    def get_queryset(self, request):
        return super().get_queryset(request).order_by("domain_name")


@admin.register(OrganizationDomain)
class OrganizationDomainAdmin(admin.ModelAdmin):
    list_display = ("domain_name", "organization")
    search_fields = ("domain_name", "organization__name")
    autocomplete_fields = ("organization",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(GstSlab)
class GstSlabAdmin(admin.ModelAdmin):
    list_display = ("id", "gst_percent", "is_active")
    search_fields = ("gst_percent", "is_active")
    form = GstSlabForm


@admin.register(ItemExpenseType)
class ItemExpenseTypeAdmin(admin.ModelAdmin):
    list_display = (
        "organization",
        "name",
        "is_active",
        "created_at",
        "category_name",
    )
    search_fields = ("organization__name", "name", "category__name")

    def category_name(self, obj):
        return obj.category.name if obj.category else "N/A"

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(ItemExpenseTypeCategory)
class ItemExpenseTypeCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "is_default_type")
    search_fields = ("name",)


class RegionInlineAdmin(admin.StackedInline):
    model = RegionStateMapping
    extra = 0
    verbose_name = "State"
    verbose_name_plural = "States"
    form = RegionInlineForm


@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "organization",
        "is_active",
    )
    search_fields = ("organization", "name", "is_active")
    form = RegionForm
    inlines = (RegionInlineAdmin,)


@admin.register(ProductionDrawingTag)
class ProductionDrawingTagAdmin(admin.ModelAdmin):
    list_display = ("name", "type", "is_active", "created_at")
    search_fields = ("name", "is_active")
    form = ProductionDrawingTagForm

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(DesignFileTag)
class GlobalDesignFileTag(admin.ModelAdmin):
    list_display = ("name", "created_at", "created_by")
    search_fields = ("name", "is_active")
    form = GlobalDesignFileTagTagForm
    readonly_fields = ("created_at", "created_by")

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_active=True)

    def save_model(self, request, obj, form, change):
        obj.created_by = request.user
        obj.is_active = True
        return super().save_model(request, obj, form, change)

    actions = ["mark_inactive"]

    @transaction.atomic
    def mark_inactive(self, request, queryset):
        queryset.update(is_active=False)
        messages.success(request, "Successfully marked inactive.")

    mark_inactive.short_description = "Mark inactive"


@admin.register(UnitOfMeasurement)
class UnitOfMeasurementAdmin(admin.ModelAdmin):
    list_display = ("name", "created_at", "created_by")
    search_fields = ("name",)
    form = UnitOfMeasurementForm

    def has_change_permission(self, request: HttpRequest, obj: Any = None) -> bool:
        return False

    def save_model(self, request, obj, form, change):
        obj.is_active = True
        if not change:
            obj.created_by = request.user
        return super().save_model(request, obj, form, change)

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(InvoiceType)
class InvoiceTypeAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "is_active")
    search_fields = ("name",)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("name",)
        else:
            return tuple()


@admin.register(ConsumptionReason)
class ConsumptionReasonsAdmin(admin.ModelAdmin):
    list_display = ("id", "reason")
    search_fields = ("reason",)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("reason",)
        else:
            return tuple()


class OrganizationDocumentInline(admin.StackedInline):
    model = OrganizationDocument
    form = OrganizationDocumentForm
    fk_name = "organization"
    extra = 0

    def has_delete_permission(self, request, obj=None):
        return False

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "uploaded_by":
            kwargs["queryset"] = User.objects.filter(id=request.user.pk)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        formfield = super().formfield_for_choice_field(db_field, request, **kwargs)
        if db_field.name == "type":
            formfield.choices = [
                (OrganizationDocumentChoices.BUSINESS_CARD.value, OrganizationDocumentChoices.BUSINESS_CARD.name),
                (OrganizationDocumentChoices.BROCHURE.value, OrganizationDocumentChoices.BROCHURE.name),
                (
                    OrganizationDocumentChoices.SUPPORTING_DOCUMENT.value,
                    OrganizationDocumentChoices.SUPPORTING_DOCUMENT.name,
                ),
            ]
        return formfield


class OrganizationAddressInline(admin.StackedInline):
    model = OrganizationAddress
    form = OrganizationAddressForm
    fk_name = "organization"
    extra = 0

    def has_delete_permission(self, request, obj=None):
        return False


class VendorDataInlineV2(admin.StackedInline):
    model = VendorData
    form = VendorDataForm
    fk_name = "organization"
    extra = 0

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        readonly_fields: tuple = super().get_readonly_fields(request, obj)
        return readonly_fields + ("code",)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "created_by":
            kwargs["queryset"] = User.objects.filter(id=request.user.pk)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class ClientDataInlineV2(admin.StackedInline):
    model = ClientData
    form = ClientDataForm
    fk_name = "organization"
    extra = 0

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        readonly_fields: tuple = super().get_readonly_fields(request, obj)
        if (hasattr(obj, "client") and obj.client.code == ClientOnboardService.DEFAULT_CODE) or (obj is None):
            return readonly_fields
        return readonly_fields + ("code",)


@admin.register(_Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        # "pan_number",
        "vendor_code",
        "client_code",
    )
    search_fields = ("id", "name", "client__code", "vendor__code")
    inlines = (
        OrganizationAddressInline,
        OrganizationDocumentInline,
    )
    autocomplete_fields = ["referral_org", "referral_by"]

    form = OrgForm

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("client", "vendor")

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj) + ("vendor_code",)
        if obj is None:
            return readonly_fields
        return readonly_fields + ("referral_org", "country", "referral_by")

    def vendor_code(self, obj):
        return obj.vendor.code if obj.vendor else None

    def client_code(self, obj):
        return obj.client.code if obj.client else None

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "country":
            kwargs["queryset"] = Country.objects.all()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_delete_permission(self, request, obj=None):
        return False

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)

        if obj:
            form.base_fields["creation_time"].initial = obj.created_at
            if hasattr(obj, "vendor"):
                # form.base_fields["msme_id"].initial = obj.vendor.msme_id
                # form.base_fields["state"].initial = obj.vendor.state
                # form.base_fields["city"].initial = obj.vendor.city
                # form.base_fields["aadhar_number"].initial = obj.vendor.aadhar_number
                # form.base_fields["pan_number"].initial = obj.vendor.pan_number

                form.base_fields["created_by"].initial = obj.vendor.created_by.name
                if obj.vendor.updated_by is not None:
                    form.base_fields["updated_by"].initial = obj.vendor.updated_by.name

                form.base_fields["updation_time"].initial = obj.vendor.updated_at
            if hasattr(obj, "client"):
                form.base_fields["client_code"].initial = obj.client.code

                if obj.client.updated_at:
                    if form.base_fields["updation_time"].initial is None:
                        form.base_fields["updation_time"].initial = obj.client.updated_at
                    else:
                        form.base_fields["updation_time"].initial = max(
                            obj.client.updated_at, form.base_fields["updation_time"].initial
                        )

                if obj.client.code != "RDASH":
                    form.base_fields["client_code"].disabled = True
        else:
            # form.base_fields["msme_id"].initial = ""
            # form.base_fields["state"].initial = ""
            # form.base_fields["city"].initial = ""
            # form.base_fields["aadhar_number"].initial = ""
            # form.base_fields["pan_number"].initial = ""
            form.base_fields["client_code"].initial = "RDASH"
            form.base_fields["client_code"].disabled = False
            form.base_fields["created_by"].initial = request.user.name
            form.base_fields["updated_by"].initial = request.user.name

            form.base_fields["creation_time"].initial = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
            form.base_fields["updation_time"].initial = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        return form

    def get_vendor_code(self):
        code_prefix = VendorServiceV2.code_prefix
        serial_number = VendorServiceV2.fetch_next_serial_number()
        return f"{code_prefix}{padding_for_serial_number(serial_number, 4)}"

    def vendor_code_add(self, formset):
        if formset.cleaned_data:
            formset.cleaned_data[0]["code"] = self.get_vendor_code()
        return formset

    def cliend_code_check_or_update(self, formset):
        if formset.cleaned_data:
            formset.cleaned_data[0]["code"] = ClientOnboardService.DEFAULT_CODE
        return formset

    def add_file_name(self, formset):
        if formset.cleaned_data:
            for idx, form_set in enumerate(formset.cleaned_data):
                if formset.cleaned_data[idx].get("file") and formset.cleaned_data[idx]["file"]:
                    formset.cleaned_data[idx]["name"] = formset.cleaned_data[idx]["file"].name
        return formset

    def log_out_users_from_all_devices(self, request, org_id):
        OrgAssignmentCache.delete(instance_id=org_id)
        for user in User.objects.filter(org_id=org_id):
            if user.pk and hasattr(user, "auth_token") and user.auth_token is not None:
                UserTokenCache.delete(instance_id=user.auth_token.key)
                UserAuthCache.delete(instance_id=str(user.pk))
            organization_user_cache_clear(user_id=user.id, org_id=user.org_id)
            token_delete_using_user(user_id=user.id)
        messages.success(request, "Successfully Logged Out Users From All Devices.")

    def save_model(self, request, obj, form, change):
        obj.type = OrganizationType.PMC.value
        if change:
            is_active = Organization.objects.filter(id=form.instance.id).first().is_active

            if is_active != form.cleaned_data["is_active"] and not form.cleaned_data["is_active"]:
                self.log_out_users_from_all_devices(request, form.instance.id)

        super().save_model(request, obj, form, change)

        # Access the saved instance
        instance: _Organization = form.instance
        vendor_data: Optional[VendorData] = VendorData.objects.filter(organization=instance).first()
        if vendor_data is None:
            vendor_data = VendorData(organization=instance, code=self.get_vendor_code(), created_by_id=request.user.pk)

        # vendor_data.city = form.cleaned_data.get("city").name if form.cleaned_data.get("city") else None
        # vendor_data.state = form.cleaned_data.get("state").pk if form.cleaned_data.get("state") else None
        vendor_data.msme_id = form.cleaned_data.get("msme_id")
        vendor_data.aadhar_number = form.cleaned_data.get("aadhar_number")
        vendor_data.pan_number = form.cleaned_data.get("pan_number")
        vendor_data.referal_organization = form.cleaned_data.get("referral_org")

        if not obj.id:
            vendor_data.created_by = request.user
        vendor_data.updated_by = request.user
        vendor_data.updated_at = datetime.now()
        vendor_data.save()

        client_data: Optional[ClientData] = ClientData.objects.filter(organization=instance).first()
        if client_data is None:
            client_data = ClientData(code=ClientOnboardService.DEFAULT_CODE, organization_id=form.instance.pk)

        client_data.code = form.cleaned_data.get("client_code")
        client_data.clean()
        client_data.save()

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)
        if not change:
            add_organization_legal_name(company_name=form.instance.name, organization_id=form.instance.pk)


@admin.register(DashboardTag)
class DashboardTagAdmin(admin.ModelAdmin):
    list_display = ("id", "name")
    search_fields = ("id", "name")


class DashboardTagsInline(admin.TabularInline):
    model = Dashboard.tags.through
    extra = 0
    form = DashboardTagsForm

    def has_module_permission(self, request):
        return request.user.has_perm("controlroom.view_dashboardtag")

    def has_view_permission(self, request, obj=None):
        return request.user.has_perm("controlroom.view_dashboardtag")

    def has_add_permission(self, request, obj=None):
        return request.user.has_perm("controlroom.add_dashboardtag")

    def has_change_permission(self, request, obj=None):
        return request.user.has_perm("controlroom.change_dashboardtag")

    def has_delete_permission(self, request, obj=None):
        return request.user.has_perm("controlroom.delete_dashboardtag")


class DashboardCardInline(admin.TabularInline):
    model = DashboardCard
    extra = 0


class DashboardFilterInline(admin.TabularInline):
    model = DashboardFilter
    extra = 0


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "position")
    search_fields = ("id", "name")
    inlines = (DashboardTagsInline, DashboardCardInline, DashboardFilterInline)
    ordering = ("position",)


@admin.register(CustomDashboardCollection)
class CustomDashboardCollectionAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "organization")
    search_fields = ("id", "name", "organization__name")
    form = CustomDashboardCollectionForm

    def save_model(self, request, obj, form, change):
        obj.type = DashboardCollectionType.CUSTOM.value
        if obj.pk is None:
            obj.created_by_id = request.user.pk
        else:
            obj.updated_by_id = request.user.pk
            obj.updated_at = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "symbol", "code")
    search_fields = ("id", "name", "symbol", "code")


@admin.register(Timezone)
class TimezoneAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "locale")
    search_fields = ("id", "name", "locale")
    form = TimezoneAdminForm
    extra = 0


class TaxSlabAdmin(admin.TabularInline):
    model = TaxSlab
    extra = 0


@admin.register(TaxType)
class TaxTypeAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "remark")
    search_fields = ("id", "name", "remark")
    inlines = (TaxSlabAdmin,)


@admin.register(TDSType)
class TDSTypeAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )
    search_fields = (
        "id",
        "name",
    )


@admin.register(OrganizationInvoiceConfig)
class OrganizationInvoiceConfigAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization",
    )
    search_fields = (
        "id",
        "organization__name",
    )


class OrganizationDocumentFieldConfigInline(admin.TabularInline):
    model = OrganizationDocumentFieldConfig
    extra = 1

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("document_config", "document_config__section")


@admin.register(OrganizationDocumentConfig)
class OrganizationDocumentConfigAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "country")
    search_fields = ("id", "name", "country__name")
    inlines = [OrganizationDocumentFieldConfigInline]

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("section")


class OrganizationDocumentConfigInline(admin.TabularInline):
    model = OrganizationDocumentConfig
    extra = 1

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("section")


@admin.register(OrganizationSectionConfig)
class OrganizationSectionConfigAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "country")
    search_fields = ("id", "name", "country__name")
    inlines = [OrganizationDocumentConfigInline]


@admin.register(OrganizationDocumentFieldContextConfig)
class OrganizationDocumentFieldContextConfigAdmin(admin.ModelAdmin):
    list_display = ("id", "context", "name", "field_config", "country")
    search_fields = ("id", "context", "name", "field_config__name", "country__name")


@admin.register(OrganizationDefaultModuleSetting)
class OrganizationDefaultModuleSettingAdmin(admin.ModelAdmin):
    list_display = ("id", "org", "module", "is_client")
    search_fields = ("org__name",)


@admin.register(OrganizationDefaultModuleLevelMapping)
class OrganizationDefaultModuleLevelMappingAdmin(admin.ModelAdmin):
    list_display = ("id", "module_setting", "level")
    search_fields = ("module_setting__org__name",)


@admin.register(IntegrationFacebookCrm)
class FacebookPageCrmBoardMappingAdmin(admin.ModelAdmin):
    form = FacebookPageCrmBoardMappingForm
    list_display = ("organization", "page", "board", "stage")
    search_fields = ("page__facebook_connection__organization__name", "page__name", "board__name")
    list_display_links = None

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("page__facebook_connection__organization", "page", "board", "stage")

    def organization(self, obj):
        return obj.page.facebook_connection.organization if obj.page and obj.page.facebook_connection else None

    organization.short_description = "Organization"

    def get_form(self, request, obj=None, **kwargs):
        self.form = FacebookPageCrmBoardMappingForm
        form = super(FacebookPageCrmBoardMappingAdmin, self).get_form(request, obj, **kwargs)
        if "created_by" in form.base_fields.keys():
            form.base_fields["created_by"].queryset = User.objects.filter(id=request.user.pk)
        return form

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ApprovalHierarchy)
class ApprovalHierarchyAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization",
        "context",
        "is_active",
        "is_skipped",
        "is_auto_approved_on_final_level_misconfiguration",
        "version",
    )
    search_fields = ("organization__name", "context")
    readonly_fields = (
        "organization",
        "context",
        "config",
        "is_active",
        "is_skipped",
        "version",
        "created_by",
        "created_at",
        "updated_by",
        "updated_at",
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("organization")


@admin.register(DeadLetterQueueTasks)
class DeadLetterQueueTaskAdmin(admin.ModelAdmin):
    list_display = ("task_id", "queue", "status", "retry_count", "created_at", "updated_at")
    list_filter = ("status", "queue", "retry_count")
    search_fields = ("task_id", "queue")
    date_hierarchy = "created_at"

    actions = [ExecutionTrackerActions.requeue, ExecutionTrackerActions.drop]

    fieldsets = (
        ("Task Details", {"fields": ("task_id", "child_task_id", "queue", "status", "retry_count")}),
        ("Task Data", {"fields": ("formatted_data", "error")}),
        ("Timestamps", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )

    def formatted_data(self, obj):
        """Display the JSON data in a more readable format"""
        if obj.data:
            formatted = json.dumps(obj.data, indent=2)
            return format_html("<pre>{}</pre>", formatted)
        return "-"

    formatted_data.short_description = "Data"

    def get_readonly_fields(self, request, obj=None):
        """Make all fields except status read-only"""
        if obj:
            all_fields = []
            for fieldset in self.fieldsets:
                all_fields.extend(fieldset[1]["fields"])
            readonly = list(all_fields)
            if "status" in readonly:
                readonly.remove("status")
            return readonly
        return self.readonly_fields

    def has_add_permission(self, request):
        return False

    def has_view_permission(self, request, obj=None):
        return self.has_module_permission(request)

    def requeue(self, request, queryset):
        """Requeue selected tasks from the Dead Letter Queue"""
        service = DLQService()
        task_ids = list(queryset.values_list("task_id", flat=True))

        try:
            results, errors = service.requeue_tasks_bulk(task_ids)
            if not errors:
                success_count = len(results)
                self.message_user(request, f"Successfully requeued {success_count} tasks.", level=messages.SUCCESS)
            else:
                success_count = len(results)
                error_count = len(errors)
                self.message_user(
                    request,
                    f"Partially succeeded: {success_count} tasks requeued, {error_count} tasks failed.",
                    level=messages.WARNING,
                )
        except DLQException as e:
            self.message_user(request, f"Failed to requeue tasks: {str(e)}", level=messages.ERROR)

    requeue.short_description = "Requeue selected tasks"

    def drop(self, request, queryset):
        """Drop selected tasks from the Dead Letter Queue"""
        service = DLQService()
        task_ids = list(queryset.values_list("task_id", flat=True))

        try:
            results = service.update_task_status_bulk(task_ids, status=DLQStatusType.DROPPED)
            success_count = len(results)
            self.message_user(
                request,
                f"Succeeded: {success_count} tasks dropped",
                level=messages.WARNING,
            )
        except DLQException as e:
            self.message_user(request, f"Failed to drop tasks: {str(e)}", level=messages.ERROR)

    drop.short_description = "Drop selected tasks"
