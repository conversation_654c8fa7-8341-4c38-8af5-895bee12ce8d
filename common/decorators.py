import functools
import warnings
from typing import Any, Callable, Concatenate, Optional, ParamSpec, TypeVar, cast

from django.db import models
from django.http.response import HttpResponseBase
from django.utils.functional import SimpleLazyObject, lazy
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.status import HTTP_410_GONE
from typing_extensions import deprecated

from common.services import model_choice_get_many

model_choice_get_many_dynamic = lazy(model_choice_get_many, list)


P = ParamSpec("P")
R = TypeVar("R")

GONE_MESSAGE = {"message": "This API no longer exists."}
DEPRECATION_HEADER = "RD-Deprecated-API"
SUGGESTED_API_HEADER = "RD-Replacement-Endpoint"


def register_dynamic_choice_fields(*, choice_fields, choice_model, through_model=None):
    def wrapper(model, *args, **kwargs):
        for choice_field in choice_fields:
            field = model._meta.get_field(choice_field)
            if through_model:
                through_model_field = through_model._meta.get_field(choice_field)
                if type(field) != type(through_model_field):
                    raise Exception(f"Field {choice_field} type does not match with field in {through_model.__name__}.")
            if not isinstance(field, models.fields.PositiveSmallIntegerField) and not isinstance(
                field, models.fields.CharField
            ):
                raise Exception("Dynamic choices for this field type is not supported yet.")
            field.choices = model_choice_get_many_dynamic(
                choice_model=choice_model,
                data_model=through_model if through_model else model,
                choice_field=choice_field,
            )
        return model

    return wrapper


def _add_deprecation_headers(response: HttpResponseBase, suggested_api: str | None = None) -> None:
    response[DEPRECATION_HEADER] = "true"
    if suggested_api is not None:
        response[SUGGESTED_API_HEADER] = suggested_api


SelfT = TypeVar("SelfT")
P = ParamSpec("P")
EndpointT = Callable[Concatenate[SelfT, Request, P], HttpResponseBase]


def deprecated_api(
    message: Optional[str] = None, use_instead: Optional[int] = None, removed: Optional[bool] = False
) -> Callable[[EndpointT[SelfT, P]], EndpointT[SelfT, P]]:
    """
    Decorator to mark an API as deprecated.
    Example:
    @decorators.deprecated_api(message="This API is deprecated", use_instead=2)
    """

    def decorator(func: EndpointT[SelfT, P]) -> EndpointT[SelfT, P]:
        warning_message = message or f"This '{func.__name__}' API is deprecated"

        if use_instead:
            warning_message += f". Use 'v{use_instead}' instead"

        # Add type hint for IDE warning
        func = deprecated(warning_message, category=DeprecationWarning, stacklevel=2)(func)  # type: ignore

        @functools.wraps(func)
        def endpoint_method(self: SelfT, request: Request, *args: P.args, **kwargs: P.kwargs) -> HttpResponseBase:
            if removed:
                response: HttpResponseBase = Response(GONE_MESSAGE, status=HTTP_410_GONE)
            else:
                warnings.warn(f"{warning_message}.", DeprecationWarning, stacklevel=2)
                response = func(self, request, *args, **kwargs)

            _add_deprecation_headers(response, suggested_api=f"v{use_instead}" if use_instead else None)

            return response

        return endpoint_method

    return decorator


def deprecated_function(message: str = "This function is deprecated."):
    """
    Decorator to mark a function as deprecated.
    Example:
    @decorators.deprecated_function(message="This function is deprecated.")
    """
    return deprecated(message)  # type: ignore


T = TypeVar("T")
P = ParamSpec("P")


def service_lazy() -> Callable[[Callable[P, T]], Callable[P, T]]:
    """
    Decorator for lazy loading service instances using Django's SimpleLazyObject.
    The wrapped function is not called until an attribute on its result is accessed.

    Example:
        class ServiceFactory:
            @service_lazy()
            def get_service(self, user_id: int) -> UserService:
                print("Creating service")
                return UserService(user_id)

        factory = ServiceFactory()
        service = factory.get_service(123)  # Service not yet created.
        service.get_elements()  # Now the service is instantiated.
    """

    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(self: Any, *args: P.args, **kwargs: P.kwargs) -> T:
            # SimpleLazyObject will call the lambda the first time an attribute is accessed.
            return SimpleLazyObject(lambda: func(self, *args, **kwargs))

        return cast(Callable[P, T], wrapper)

    return decorator
