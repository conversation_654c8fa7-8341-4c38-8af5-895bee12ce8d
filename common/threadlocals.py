from threading import local
from typing import Any, Callable, ParamSpec, TypeVar

_threadlocals = local()
_thread_variables = set()


class ThreadLocalCache:
    """
    A class to manage thread-local caching.
    This can be used to cache values that are specific to the current thread.
    """

    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        return getattr(_threadlocals, key, default)

    @classmethod
    def set(cls, key: str, val: Any):
        _thread_variables.add(key)
        setattr(_threadlocals, key, val)

    @classmethod
    def delete(cls, key: str):
        if hasattr(_threadlocals, key):
            delattr(_threadlocals, key)

    @classmethod
    def delete_all(cls):
        for key in _thread_variables:
            cls.delete(key)


T = TypeVar("T")
P = ParamSpec("P")


def thread_local_cache() -> Callable[[Callable[P, T]], Callable[P, T]]:
    """
    Decorator to cache the result of a function in thread-local storage.
    This is useful for caching results that are expensive to compute and
    should be reused within the same thread.
    """

    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            cls = args[0] if args else None

            method: Callable | None = None

            if cls is not None and hasattr(cls, "get_cache_key"):
                method = getattr(cls, "get_cache_key", None)

            if method is None:
                assert cls.CACHE_KEY, "CACHE_KEY must be defined in the class"
                key = cls.CACHE_KEY

            else:
                if len(kwargs) < 1 or not callable(method):
                    raise ValueError("First argument must be a class instance with a 'get_cache_key' method")

                instance_id = kwargs.get("instance_id", None)
                instance_key = kwargs.get("key", None)

                key: str = method(key=str(instance_id) if instance_id else instance_key)

            cached_value = ThreadLocalCache.get(key, None)
            if cached_value is not None:
                return cached_value

            result = func(*args, **kwargs)
            ThreadLocalCache.set(key, result)
            return result

        return wrapper

    return decorator
