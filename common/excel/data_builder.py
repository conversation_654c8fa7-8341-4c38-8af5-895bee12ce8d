import abc
from typing import Optional

import structlog
from django.core.exceptions import ValidationError
from django.db.models import QuerySet

from common.excel.constants import (
    ElementExcelColumnsEnum,
    FontEnum,
    GuidelineExcelColumnsEnum,
    HorizontalAlignmentEnum,
    SheetCellTypeEnum,
    SheetTypeEnum,
    SummaryExcelColumnsEnum,
    TermsAndConditionsExcelColumnsEnum,
    VerticalAlignmentEnum,
)
from common.excel.entities import (
    ElementExcelSheetBaseData,
    ExcelBaseData,
    HeaderCell,
    SheetCell,
    SheetRowData,
)
from common.types import DjangoModelType
from element.data.models import ElementBase, GuidelineBase

logger = structlog.get_logger(__name__)


class ExcelDataBuilder:
    class ExcelDataBuilderException(ValidationError):
        pass

    _sheets: list[SheetTypeEnum] = []

    def __init__(self):
        self._sheets = []

    def add_sheet(self, sheet: SheetTypeEnum):
        self._sheets.append(sheet)
        return self

    def build(self) -> ExcelBaseData:
        self.validate()
        excel_data = ExcelBaseData(
            file_name=self.get_file_name(),
            sheets=self.get_sheets_data(),
        )
        logger.info("Excel data built successfully", file_name=excel_data.file_name)
        return excel_data

    def validate(self):
        if len(self._sheets) == 0:
            raise self.ExcelDataBuilderException("At least one sheet must be selected")

    @abc.abstractmethod
    def get_file_name(self) -> str:
        pass

    @abc.abstractmethod
    def get_sheets_data(self) -> list[ElementExcelSheetBaseData]:
        pass


class ElementExcelDataBaseBuilder(ExcelDataBuilder):
    _element_col_display_text_mapping = {}
    _summary_cols_display_text_mapping = {}
    _guideline_cols_display_text_mapping = {}
    _term_and_condition_cols_display_text_mapping = {}

    _element_cols: list[ElementExcelColumnsEnum] = []
    _summary_cols: list[SummaryExcelColumnsEnum] = []
    _guideline_cols: list[GuidelineExcelColumnsEnum] = []
    _term_and_condition_cols: list[TermsAndConditionsExcelColumnsEnum] = []
    _elements: QuerySet[DjangoModelType] = None
    _guidelines: QuerySet[GuidelineBase] = None
    _summary: Optional[list[dict]] = None

    def __init__(self):
        super().__init__()
        self._element_cols = []
        self._summary_cols = []
        self._guideline_cols = []
        self._term_and_condition_cols = []
        self._elements = None
        self._guidelines = None
        self._summary = None

    def add_element_cols(self, *cols: ElementExcelColumnsEnum):
        self._element_cols.extend(cols)
        return self

    def add_summary_cols(self, *cols: SummaryExcelColumnsEnum):
        self._summary_cols.extend(cols)
        return self

    def add_guideline_cols(self, *cols: GuidelineExcelColumnsEnum):
        self._guideline_cols.extend(cols)
        return self

    def add_term_and_condition_cols(self, *cols: TermsAndConditionsExcelColumnsEnum):
        self._term_and_condition_cols.extend(cols)
        return self

    def validate(self):
        super().validate()
        for sheet in self._sheets:
            if sheet == SheetTypeEnum.ELEMENT:
                if len(self._element_cols) == 0:
                    raise self.ExcelDataBuilderException("At least one element column must be selected")
                if len(self._element_col_display_text_mapping) == 0:
                    raise self.ExcelDataBuilderException("Element column display text mapping is missing")
                if self._elements is None:
                    raise self.ExcelDataBuilderException("Elements data is missing")
            elif sheet == SheetTypeEnum.GUIDELINE:
                if len(self._guideline_cols) == 0:
                    raise self.ExcelDataBuilderException("At least one guideline column must be selected")
                if len(self._guideline_cols_display_text_mapping) == 0:
                    raise self.ExcelDataBuilderException("Guideline column display text mapping is missing")
            elif sheet == SheetTypeEnum.SUMMARY:
                if len(self._summary_cols) == 0:
                    raise self.ExcelDataBuilderException("At least one summary column must be selected")
                if len(self._summary_cols_display_text_mapping) == 0:
                    raise self.ExcelDataBuilderException("Summary column display text mapping is missing")
                if self._summary is None:
                    raise self.ExcelDataBuilderException("Summary data is missing")
            elif sheet == SheetTypeEnum.TERMS_AND_CONDITIONS:
                if len(self._term_and_condition_cols) == 0:
                    raise self.ExcelDataBuilderException("At least one terms and conditions column must be selected")
                if len(self._term_and_condition_cols_display_text_mapping) == 0:
                    raise self.ExcelDataBuilderException("Terms and conditions column display text mapping is missing")

    def get_sheets_data(self) -> list[ElementExcelSheetBaseData]:
        sheets: list[ElementExcelSheetBaseData] = []
        for sheet in self._sheets:
            if sheet == SheetTypeEnum.ELEMENT:
                sheets.append(self.get_element_sheet())
            elif sheet == SheetTypeEnum.SUMMARY:
                sheets.append(self.get_summary_sheet())
            elif sheet == SheetTypeEnum.GUIDELINE:
                sheets.append(self.get_guideline_sheet())
            elif sheet == SheetTypeEnum.TERMS_AND_CONDITIONS:
                sheets.append(self.get_terms_and_conditions_sheet())
        return sheets

    def get_element_sheet(self) -> ElementExcelSheetBaseData:
        headers = self.get_element_sheet_headers()
        sheet = ElementExcelSheetBaseData(
            title=self.get_element_sheet_title(),
            headers=headers,
            rows=self.get_element_sheet_rows(),
            org_logo_url=self.get_org_logo_url(),
            org_name=self.get_org_name(),
        )
        return sheet

    def get_summary_sheet(self) -> ElementExcelSheetBaseData:
        headers = self.get_summary_sheet_headers()
        sheet = ElementExcelSheetBaseData(
            title=self.get_summary_sheet_title(),
            headers=headers,
            rows=self.get_summary_sheet_rows(),
            org_logo_url=None,
            org_name=None,
        )
        return sheet

    def get_guideline_sheet(self) -> ElementExcelSheetBaseData:
        headers = self.get_guideline_sheet_headers()
        sheet = ElementExcelSheetBaseData(
            title=self.get_guideline_sheet_title(),
            headers=headers,
            rows=self.get_guideline_sheet_rows(),
        )
        return sheet

    def get_terms_and_conditions_sheet(self) -> ElementExcelSheetBaseData:
        headers = self.get_terms_and_conditions_sheet_headers()
        sheet = ElementExcelSheetBaseData(
            title=self.get_terms_and_conditions_sheet_title(),
            headers=headers,
            rows=self.get_terms_and_conditions_sheet_rows(),
        )
        return sheet

    def get_element_sheet_headers(self) -> dict[ElementExcelColumnsEnum, HeaderCell]:
        all_headers = self.get_element_sheet_all_headers()
        headers = {}
        for key, value in all_headers.items():
            if key in self._element_cols:
                headers[key] = value
        return headers

    def get_summary_sheet_headers(self) -> dict[ElementExcelColumnsEnum, HeaderCell]:
        all_headers = self.get_summary_sheet_all_headers()
        headers = {}
        for key, value in all_headers.items():
            if key in self._summary_cols:
                headers[key] = value
        return headers

    def get_guideline_sheet_headers(self) -> dict[GuidelineExcelColumnsEnum, HeaderCell]:
        all_headers = self.get_guideline_sheet_all_headers()
        headers = {}
        for key, value in all_headers.items():
            if key in self._guideline_cols:
                headers[key] = value
        return headers

    def get_terms_and_conditions_sheet_headers(self) -> dict[TermsAndConditionsExcelColumnsEnum, HeaderCell]:
        all_headers = self.get_terms_and_conditions_sheet_all_headers()
        headers = {}
        for key, value in all_headers.items():
            if key in self._term_and_condition_cols:
                headers[key] = value
        return headers

    def get_element_sheet_all_headers(self) -> dict[ElementExcelColumnsEnum, HeaderCell]:
        all_headers = {}
        decimal_field_cols = [
            ElementExcelColumnsEnum.LENGTH,
            ElementExcelColumnsEnum.BREADTH,
            ElementExcelColumnsEnum.STANDARD_QUANTITY,
            ElementExcelColumnsEnum.CLIENT_RATE,
            ElementExcelColumnsEnum.FINAL_AMOUNT,
            ElementExcelColumnsEnum.QUANTITY,
            ElementExcelColumnsEnum.BASE_AMOUNT,
            ElementExcelColumnsEnum.BUDGET_RATE,
            ElementExcelColumnsEnum.DISCOUNT_PERCENT,
            ElementExcelColumnsEnum.DISCOUNT_VALUE,
            ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT,
            ElementExcelColumnsEnum.ORDER_RATE,
            ElementExcelColumnsEnum.TAX_PERCENT,
        ]
        for key, value in self._element_col_display_text_mapping.items():
            type = SheetCellTypeEnum.TEXT
            if key in decimal_field_cols:
                type = SheetCellTypeEnum.DECIMAL
            elif key == ElementExcelColumnsEnum.ELEMENT_NAME:
                type = SheetCellTypeEnum.HYPERLINK
            elif key == ElementExcelColumnsEnum.REFERENCE_IMAGE:
                type = SheetCellTypeEnum.IMAGE
            cell = HeaderCell(
                value=value,
                col_type=type,
                background_color=self.get_element_header_background_color(),
                font=self.get_element_sheet_header_font(),
                horizontal_alignment=self.get_element_sheet_header_horizontal_alignment(),
                vertical_alignment=self.get_element_sheet_header_vertical_alignment(),
            )
            all_headers[key] = cell
        return all_headers

    def get_summary_sheet_all_headers(self) -> dict[SummaryExcelColumnsEnum, HeaderCell]:
        all_headers = {}
        for key, value in self._summary_cols_display_text_mapping.items():
            cell = HeaderCell(value=value, font=FontEnum.BOLD)
            all_headers[key] = cell
        return all_headers

    def get_guideline_sheet_all_headers(self) -> dict[GuidelineExcelColumnsEnum, HeaderCell]:
        all_headers = {}
        for key, value in self._guideline_cols_display_text_mapping.items():
            cell = HeaderCell(value=value)
            all_headers[key] = cell
        return all_headers

    def get_terms_and_conditions_sheet_all_headers(self) -> dict[TermsAndConditionsExcelColumnsEnum, HeaderCell]:
        all_headers = {}
        for key, value in self._term_and_condition_cols_display_text_mapping.items():
            cell = HeaderCell(value=value)
            all_headers[key] = cell
        return all_headers

    def get_element_sheet_rows(self) -> list[SheetRowData]:
        rows: list[SheetRowData] = []
        count = 1
        for element in self._elements:
            row_data = self.get_element_row_data(element=element, count=count)
            rows.append(SheetRowData(data=row_data))
            count += 1
        return rows

    def get_element_row_data(self, element: ElementBase, count: int) -> dict[ElementExcelColumnsEnum, SheetCell]:
        row_data = {}
        for key in self._element_cols:
            if key == ElementExcelColumnsEnum.S_NO:
                row_data[key] = SheetCell(value=count)
                continue
            row_data[key] = self.get_element_cell_data(element=element, col=key)
        return row_data

    def get_summary_sheet_rows(self) -> list[SheetRowData]:
        # Implement this funtion in the subclass, because summary sheet contains some extra formattings
        raise NotImplementedError("get_summary_sheet_rows method is not implemented")

    def get_terms_and_conditions_sheet_rows(self) -> list[SheetRowData]:
        # Implement this funtion in the subclass, because terms and conditions sheet contains some extra formattings
        raise NotImplementedError("get_terms_and_conditions_sheet_rows method is not implemented")

    def get_guideline_sheet_rows(self) -> list[SheetRowData]:
        rows: list[SheetRowData] = []
        count = 1
        for guideline in self._guidelines:
            row_data = {}
            for key in self._guideline_cols:
                if key == GuidelineExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value=count)
                    continue
                row_data[key] = self.get_guideline_cell_data(guideline=guideline, col=key)
            rows.append(SheetRowData(data=row_data))
        return rows

    def get_element_sheet_title(self) -> str:
        return "Elements"

    def get_summary_sheet_title(self) -> str:
        return "Summary"

    def get_guideline_sheet_title(self) -> str:
        return "Guidelines"

    def get_terms_and_conditions_sheet_title(self) -> str:
        return "Terms and Conditions"

    def get_element_cell_data(self, element: ElementBase, col: ElementExcelColumnsEnum) -> SheetCell:
        raise NotImplementedError("get_element_cell_data method is not implemented")

    def get_summary_cell_data(self, summary_data: dict, col: SummaryExcelColumnsEnum) -> SheetCell:
        raise NotImplementedError("get_summary_cell_data method is not implemented")

    def get_guideline_cell_data(self, guideline: GuidelineBase, col: GuidelineExcelColumnsEnum) -> SheetCell:
        raise NotImplementedError("get_guideline_cell_data method is not implemented")

    def get_element_header_background_color(self) -> str:
        return "FFFFFF"

    def get_element_sheet_header_font(self) -> FontEnum:
        return FontEnum.DEFAULT

    def get_element_sheet_header_horizontal_alignment(self) -> HorizontalAlignmentEnum:
        return HorizontalAlignmentEnum.GENERAL

    def get_element_sheet_header_vertical_alignment(self) -> VerticalAlignmentEnum:
        return VerticalAlignmentEnum.TOP

    def get_org_logo_url(self) -> Optional[str]:
        return None

    def get_org_name(self) -> Optional[str]:
        return None
