from io import Bytes<PERSON>
from typing import Optional

import requests
import structlog
from django.core.exceptions import ValidationError
from openpyxl import Workbook
from openpyxl.cell import Cell
from openpyxl.drawing.image import Image as XLImage
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.writer.excel import save_virtual_workbook
from PIL import Image

from common.excel.constants import ExcelColumnsEnum, HorizontalAlignmentEnum, VerticalAlignmentEnum
from common.excel.data_builder import ExcelDataBuilder
from common.excel.entities import (
    ElementExcelSheetBaseData,
    ExcelBaseData,
    FontEnum,
    HeaderCell,
    SheetCell,
    SheetCellBorderEnum,
    SheetCellTypeEnum,
    SheetRowData,
)

logger = structlog.get_logger(__name__)


class ExcelGenerator:
    class ExcelGeneratorException(ValidationError):
        pass

    _workbook: Optional[Workbook] = None
    _data: Optional[ExcelBaseData] = None

    _default_alignment: Alignment = Alignment(
        horizontal=HorizontalAlignmentEnum.GENERAL.value,
        vertical=VerticalAlignmentEnum.TOP.value,
        text_rotation=0,
        wrap_text=True,
        shrink_to_fit=True,
        indent=0,
    )
    _image_alignment: Alignment = Alignment(
        horizontal=HorizontalAlignmentEnum.CENTER.value,
        vertical=VerticalAlignmentEnum.CENTER.value,
        text_rotation=0,
        wrap_text=True,
        shrink_to_fit=True,
        indent=0,
    )

    def __init__(self, data_builder: ExcelDataBuilder):
        self._workbook = Workbook()
        del self._workbook["Sheet"]
        self._data = data_builder.build()

    def generate(self):
        logger.info("Excel file generation started.")
        if self._data is None:
            raise self.ExcelGeneratorException("Excel data not found")
        if self._workbook is None:
            raise self.ExcelGeneratorException("Workbook not found")
        for sheet_data in self._data.sheets:
            self.generate_sheet(sheet_data)
        excel_file = save_virtual_workbook(self._workbook)
        logger.info("Excel file generated successfully.")
        return excel_file, self._data.file_name

    def generate_sheet(self, sheet_data: ElementExcelSheetBaseData) -> Worksheet:
        sheet = self._create_sheet(sheet_data.title)
        if len(sheet_data.rows) == 0:
            self._add_empty_sheet_data(sheet=sheet, sheet_name=sheet_data.title)
            return sheet
        if sheet_data.org_logo_url:
            self._add_org_logo(sheet=sheet, logo_url=sheet_data.org_logo_url)
        elif sheet_data.org_name:
            self._add_org_name(sheet=sheet, org_name=sheet_data.org_name)
        sheet = self._add_headers(sheet=sheet, headers=sheet_data.headers)
        sheet = self._add_rows(sheet=sheet, rows_data=sheet_data.rows, headers=sheet_data.headers)
        image_col_numbers = []
        if sheet_data.org_logo_url or sheet_data.org_name:
            image_col_numbers.append(1)
        col_number = 1
        for header_cell in sheet_data.headers.values():
            if header_cell.col_type == SheetCellTypeEnum.IMAGE:
                image_col_numbers.append(col_number)
            col_number += 1
        sheet = self._auto_adjust_column_width(sheet=sheet, image_col_numbers=image_col_numbers)
        return sheet

    def _create_sheet(self, title: str, view_grid_lines: bool = True) -> Worksheet:
        sheet = self._workbook.create_sheet(title=title)
        sheet.sheet_view.showGridLines = view_grid_lines
        return sheet

    def _add_headers(
        self,
        sheet: Worksheet,
        headers: dict[ExcelColumnsEnum, HeaderCell],
    ) -> Worksheet:
        if self._is_sheet_empty(sheet):
            header_row = 1
        else:
            header_row = sheet.max_row + 1
        col_number = 1
        for cell_data in headers.values():
            cell: Cell = sheet.cell(row=header_row, column=col_number)
            self._fill_cell(sheet=sheet, cell=cell, cell_data=cell_data)
            col_number += 1
        return sheet

    def _add_rows(
        self, sheet: Worksheet, rows_data: list[SheetRowData], headers: dict[ExcelColumnsEnum, HeaderCell]
    ) -> Worksheet:
        row_number = sheet.max_row + 1
        for row_data in rows_data:
            data: dict[ExcelColumnsEnum, SheetCell] = row_data.data
            col_number = 1
            for key, header_cell in headers.items():
                cell_data: SheetCell = data.get(key)
                col_type: SheetCellTypeEnum = header_cell.col_type
                cell: Cell = sheet.cell(row=row_number, column=col_number)
                if cell_data:
                    self._fill_cell(sheet=sheet, cell=cell, cell_data=cell_data, col_type=col_type)
                col_number += 1
            row_number += 1

        return sheet

    def _get_image_from_url(self, url: str) -> XLImage:
        file_url = url + "?tr=w-200,h-200"
        data = requests.get(file_url)
        image_file = Image.open(BytesIO(data.content))
        return XLImage(image_file)

    def _add_empty_sheet_data(self, sheet: Worksheet, sheet_name: str) -> Worksheet:
        row_data = [f"{sheet_name} has no data"]
        sheet.append(row_data)
        return sheet

    def _fill_cell(
        self, sheet: Worksheet, cell: Cell, cell_data: SheetCell, col_type: SheetCellTypeEnum = SheetCellTypeEnum.TEXT
    ):
        cell.alignment = self._get_cell_alignment(cell_data)
        cell_type: SheetCellTypeEnum = col_type
        # if cell type is default then col type is applied else cell type is applied
        if cell_data.type != SheetCellTypeEnum.TEXT:
            cell_type = cell_data.type
        # Add value in cell
        if cell_type == SheetCellTypeEnum.TEXT:
            cell.value = cell_data.value
        elif cell_type == SheetCellTypeEnum.DECIMAL:
            cell.value = cell_data.value
            cell.number_format = "0.00"
        elif cell_type == SheetCellTypeEnum.IMAGE:
            if cell_data.value:
                image = self._get_image_from_url(cell_data.value)
                sheet.add_image(image, cell.coordinate)
                sheet.row_dimensions[cell.row].height = 170
                cell.alignment = self._image_alignment
        elif cell_type == SheetCellTypeEnum.HYPERLINK:
            cell.value = cell_data.value
            cell.hyperlink = cell_data.hyperlink
            cell.style = "Hyperlink"
            cell.alignment = self._default_alignment

        self._set_font(cell=cell, cell_data=cell_data)
        if cell_data.background_color:
            self._set_background_color(cell=cell, cell_data=cell_data)
        if cell_data.color:
            self._set_font_color(cell=cell, cell_data=cell_data)
        self._set_border(cell=cell, cell_data=cell_data)

    def _set_font(self, cell: Cell, cell_data: SheetCell):
        if cell_data.font == FontEnum.BOLD:
            cell.font = Font(bold=True)
        elif cell_data.font == FontEnum.ITALIC:
            cell.font = Font(italic=True)

    def _set_font_color(self, cell: Cell, cell_data: SheetCell):
        cell.font = Font(color=cell_data.color)

    def _set_background_color(self, cell: Cell, cell_data: SheetCell):
        cell.fill = PatternFill(
            start_color=cell_data.background_color, end_color=cell_data.background_color, fill_type="solid"
        )

    def _set_border(self, cell: Cell, cell_data: SheetCell):
        thin = Side(border_style="thin", color="000000")
        if cell_data.border == SheetCellBorderEnum.DEFAULT:
            thin = Side(border_style="thin")
            border = Border(left=thin, right=thin, top=thin, bottom=thin)
        elif cell_data.border == SheetCellBorderEnum.AROUND:
            border = Border(left=thin, right=thin, top=thin, bottom=thin)
        elif cell_data.border == SheetCellBorderEnum.TOP:
            border = Border(top=thin)
        elif cell_data.border == SheetCellBorderEnum.BOTTOM:
            border = Border(bottom=thin)
        cell.border = border

    def _auto_adjust_column_width(self, sheet: Worksheet, image_col_numbers: list[int] = []) -> Worksheet:
        image_col_letters = [get_column_letter(col_number) for col_number in image_col_numbers]
        for col in sheet.columns:
            col_letter = col[0].column_letter
            if col_letter in image_col_letters:
                sheet.column_dimensions[col_letter].width = 34
            else:
                max_length = 0
                for cell in col:
                    try:  # Necessary to avoid error on empty cells
                        if len(max(str(cell.value).split("\n"))) > max_length:
                            max_length = len(max(str(cell.value).split("\n")))
                    except:  # noqa
                        pass
                adjusted_width = max_length + 1
                sheet.column_dimensions[col_letter].width = adjusted_width
        return sheet

    def _add_org_logo(self, sheet: Worksheet, logo_url: str) -> Worksheet:
        image = self._get_image_from_url(url=logo_url)
        sheet.add_image(image, "A1")
        sheet.row_dimensions[1].height = 170
        sheet.cell(row=1, column=1).alignment = self._image_alignment
        return sheet

    def _add_org_name(self, sheet: Worksheet, org_name: str) -> Worksheet:
        sheet.cell(row=1, column=1, value=org_name)
        return sheet

    def _get_cell_alignment(self, cell_data: SheetCell) -> Alignment:
        return Alignment(
            horizontal=cell_data.horizontal_alignment.value,
            vertical=cell_data.vertical_alignment.value,
            text_rotation=0,
            wrap_text=True,
            shrink_to_fit=True,
        )

    def _is_sheet_empty(self, sheet: Worksheet) -> bool:
        is_empty = all(not any(cell.value for cell in row) for row in sheet.iter_rows())
        return is_empty
