from typing import Annotated, Any, ClassVar, Optional

import pydantic
from pydantic._internal._generics import PydanticGenericMetadata
from pydantic._internal._model_construction import (
    ModelMetaclass as PydanticModelMetaclass,
)
from rest_framework import serializers
from typing_extensions import dataclass_transform

from common.entities import ObjectStatus
from common.pydantic.custom_fields import HashIdInt
from common.pydantic.parse import create_serializer_from_model
from common.serializers import HashId<PERSON>ield


@dataclass_transform(kw_only_default=True, field_specifiers=(pydantic.Field,))
class ModelMetaclass(PydanticModelMetaclass, type):
    def __new__(
        mcs,  # noqa: N804
        cls_name: str,
        bases: tuple[type[Any], ...],
        namespace: dict[str, Any],
        __pydantic_generic_metadata__: Optional[PydanticGenericMetadata] = None,
        __pydantic_reset_parent_namespace__: bool = True,
        _create_model_module: Optional[str] = None,
        **kwargs: Any,
    ):
        cls = super().__new__(
            mcs,
            cls_name,
            bases,
            namespace,
            __pydantic_generic_metadata__,
            __pydantic_reset_parent_namespace__,
            _create_model_module,
            **kwargs,
        )
        # Create serializer only if it's not already set by the user
        # Serializer should never be inherited from the parent classes
        if not hasattr(cls, "drf_serializer") or getattr(cls, "drf_serializer") in (
            getattr(base, "drf_serializer", None) for base in cls.__mro__[1:]
        ):
            setattr(
                cls,
                "drf_serializer",
                create_serializer_from_model(cls),
            )
        return cls


class BaseModel(pydantic.BaseModel, metaclass=ModelMetaclass):
    # Populated by the metaclass or manually set by the user
    drf_serializer: ClassVar[type[serializers.Serializer]]

    def validate_input_data(self, attrs):
        return attrs

    class Config:
        arbitrary_types_allowed = True


class BaseModelV2(pydantic.BaseModel):
    pass


class BaseObjectPydanticData(BaseModel):
    object_status: ObjectStatus


class BaseNestedObjectModel(BaseObjectPydanticData):
    id: Annotated[Optional[int], HashIdField(allow_null=True)]


class PydanticInputBaseModel(BaseModelV2):
    pass


class PydanticBlockBaseModel(PydanticInputBaseModel):
    type: str
    text_type: str | None = None
    text: str | None = None
    name: str | None = None
    id: HashIdInt | None = None
    url: str | None = None
    color: str | None = None
    meta: dict[str, Any] | None = None


class PydanticPaginatedBaseModel(BaseModelV2):
    count: int
    data: list


class PydanticFilterBaseModel(PydanticInputBaseModel):
    limit: str | None = None
    offset: str | None = None


class WorkProgressBlockBaseModel(PydanticBlockBaseModel):
    pass


class PhoneEntity(BaseModelV2):
    country_code: str
    number: str
