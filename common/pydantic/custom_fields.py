from typing import TYPE_CHECKING, Annotated, Any, Union

from phonenumber_field.phonenumber import PhoneNumber, to_python
from pydantic import GetCoreSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema

from common.exceptions import HashIdException
from rollingbanners.hash_id_converter import HashIdConverter

if TYPE_CHECKING:
    PhoneNumberStr = Annotated[str, ...]
    CustomFileUrlStr = Annotated[str, ...]
    HashIdInt = Annotated[int, ...]
else:

    class PhoneNumberStr:
        """
        Example usage:
            class User(BaseModel):
                phone_number: PhoneNumberStr
        """

        @classmethod
        def __get_pydantic_core_schema__(cls, _source: type[Any], _handler) -> core_schema.CoreSchema:
            return core_schema.no_info_after_validator_function(
                function=cls._validate,
                schema=core_schema.str_schema(),
                serialization=core_schema.plain_serializer_function_ser_schema(
                    function=cls.serialize,
                    return_schema=core_schema.dict_schema(
                        keys_schema=core_schema.str_schema(),
                        values_schema=core_schema.str_schema(),
                    ),
                    info_arg=True,
                ),
            )

        @classmethod
        def __get_pydantic_json_schema__(cls, core_schema, handler) -> JsonSchemaValue:
            field_schema = handler(core_schema)
            field_schema.update(type="string", format="phone-number")
            return field_schema

        @classmethod
        def _validate(cls, input_value: str, /) -> str:
            phone_number = to_python(input_value)
            if phone_number and not phone_number.is_valid():
                raise ValueError(f"Enter a valid phone number. phone_number: {phone_number}")
            return phone_number.as_e164

        @classmethod
        def serialize(cls, value: str) -> dict[str, str]:
            phone_number = PhoneNumber.from_string(value)
            if phone_number and not phone_number.is_valid():
                raise ValueError(f"Enter a valid phone number. phone_number: {phone_number}")

            return {
                "country_code": f"{phone_number.get('country_code')}",
                "number": f"{phone_number.get('number')}",
            }

    class CustomFileUrlStr:
        @classmethod
        def __get_pydantic_core_schema__(cls, _source: type[Any], _handler) -> core_schema.CoreSchema:
            return core_schema.no_info_after_validator_function(cls._validate, core_schema.str_schema())

        @classmethod
        def __get_pydantic_json_schema__(cls, core_schema, handler) -> JsonSchemaValue:
            field_schema = handler(core_schema)
            field_schema.update(type="string", format="file-url")
            return field_schema

        @classmethod
        def _validate(cls, input_value: str, /) -> str:
            if input_value is None:
                raise ValueError("Value cannot be null.")
            if not isinstance(input_value, str):
                raise TypeError("Value must be a string.")
            if input_value.strip() == "":
                raise ValueError("Value cannot be an empty string.")
            return input_value

    class HashIdInt:
        @classmethod
        def __get_pydantic_json_schema__(cls, core_schema, handler) -> JsonSchemaValue:
            field_schema = handler(core_schema)
            field_schema.update(type="string", format="hash-id")
            return field_schema

        @classmethod
        def __get_pydantic_core_schema__(cls, source_type, handler: GetCoreSchemaHandler) -> core_schema.CoreSchema:
            """
            Defines validation (input conversion) and serialization (output conversion).
            """

            return core_schema.no_info_after_validator_function(
                function=cls._validate,
                schema=core_schema.union_schema([core_schema.str_schema(), core_schema.int_schema()]),
                serialization=core_schema.plain_serializer_function_ser_schema(
                    function=cls.encode,
                    return_schema=core_schema.union_schema([core_schema.str_schema(), core_schema.int_schema()]),
                    info_arg=True,
                ),
            )

        @classmethod
        def _validate(cls, value: Union[str, int]) -> int:
            if isinstance(value, str):
                return cls.decode(value)
            return value

        @classmethod
        def encode(cls, value: int, info: core_schema.SerializationInfo) -> Union[int, str]:
            context = info.context
            is_encode = True
            if context:
                is_encode = context.get("is_encode", True)
            if not is_encode:
                return value
            hash_id = HashIdConverter.encode(value)
            if hash_id is None:
                raise ValueError(f"Input must be int: {value}")
            return hash_id

        @classmethod
        def decode(cls, value: str) -> int:
            try:
                decoded_value = HashIdConverter.decode(value)
                return decoded_value
            except HashIdException:
                raise ValueError(f"Enter a valid hash id. hash_id: {value}")
