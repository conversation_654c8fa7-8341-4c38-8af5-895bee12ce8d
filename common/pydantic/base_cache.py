import abc
from typing import Generic, TypeVar

from django.conf import settings
from django.utils.module_loading import import_string

from common import threadlocals
from common.pydantic.base_model import BaseModelV2
from rollingbanners.custom_caches import BaseCache

CACHE: BaseCache = import_string(settings.CUSTOM_CACHE)

T = TypeVar("T", bound=BaseModelV2 | int)
K = TypeVar("K", bound=BaseModelV2)
A = TypeVar("A", bound=BaseModelV2)


class PydanticBaseCache(abc.ABC, Generic[K, T]):
    CACHE_KEY = None
    CACHE_TTL = 60 * 60  # 1 hour
    RETURN_CLASS = None  # Type of the return class, if not set, it will be BaseModelV2

    @classmethod
    def get_cache_key(cls, key: K) -> str:
        if not cls.CACHE_KEY or not isinstance(cls.CACHE_KEY, str) or cls.CACHE_KEY.count("_{}") != 1:  # "something_{}"
            raise Exception("Invalid cache key.")

        if isinstance(key, str) or isinstance(key, int):
            return cls.CACHE_KEY.format(str(key))

        raise Exception("Convert key to string before passing it to get_cache_key.")

    @classmethod
    @abc.abstractmethod
    def get_cache_key_regex(cls) -> str:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    @threadlocals.thread_local_cache()
    def get(cls, key: K) -> T:
        cache_key = cls.get_cache_key(key=key)
        cache_data = CACHE.get(cache_key, decode_json=True)

        if cache_data is not None:
            if isinstance(cache_data, int):
                return cache_data
            if cls.RETURN_CLASS:
                return cls.RETURN_CLASS.model_validate(cache_data)
            else:
                raise Exception(
                    "RETURN_CLASS is not set for PydanticBaseCache, please set it to the class you want to return."
                )

        data = cls.get_data(key=key)
        if isinstance(data, int):
            model_dump_json = data
        else:
            model_dump_json = data.model_dump_json()

        CACHE.set_with_ttl(cache_key, model_dump_json, ttl_seconds=cls.CACHE_TTL, encode_json=False)
        return data

    @classmethod
    def set(cls, key: K, data: T) -> None:
        cache_key = cls.get_cache_key(key=key)
        if isinstance(data, int):
            model_dump_json = data
        else:
            model_dump_json = data.model_dump_json()
        CACHE.set_with_ttl(cache_key, model_dump_json, ttl_seconds=cls.CACHE_TTL, encode_json=False)

    @classmethod
    def delete(cls, key: K) -> None:
        CACHE.delete(cls.get_cache_key(key=key))

    @classmethod
    def delete_many(cls) -> None:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    @abc.abstractmethod
    def get_data(cls, key: K) -> T:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    def mset(cls, data: dict[str, A]) -> None:
        CACHE.mset({k: v.model_dump_json() for k, v in data.items()})


class PydanticIncrAndDecrCounterCache(PydanticBaseCache[K, int]):
    @classmethod
    def incr(cls, key: K) -> int:
        cache_key = cls.get_cache_key(key=key)
        cls.get(key=key)
        return CACHE.incr(cache_key)

    @classmethod
    def decr(cls, key: K) -> int:
        cache_key = cls.get_cache_key(key=key)
        cls.get(key=key)
        return CACHE.decr(cache_key)

    @classmethod
    def get(cls, key: K) -> int:
        data = super().get(key=key)
        assert isinstance(data, int)
        return data
