import abc

from django.contrib import messages
from django.db import transaction
from django.utils import timezone


class SoftDeleteAdminMixin:
    actions = ["soft_delete", "revert_soft_delete"]

    def soft_delete_extra_task(self, request, queryset):
        ...

    @transaction.atomic
    def soft_delete(self, request, queryset):
        self.soft_delete_extra_task(request, queryset)
        queryset.filter(deleted_at__isnull=True).update(deleted_at=timezone.now(), deleted_by_id=request.user.pk)
        messages.success(request, "Successfully soft deleted.")

    soft_delete.short_description = "Soft Delete"
    soft_delete.allowed_permissions = ("soft_delete",)

    @transaction.atomic
    def revert_soft_delete(self, request, queryset):
        queryset.filter(deleted_at__isnull=False).update(deleted_at=None, deleted_by_id=None)
        messages.success(request, "Successfully reverted soft delete.")

    revert_soft_delete.short_description = "Revert Soft Delete"
    revert_soft_delete.allowed_permissions = ("revert_soft_delete",)

    def has_soft_delete_permission(self, request):
        return True

    def has_revert_soft_delete_permission(self, request):
        return True


class DuplicateAdminMixin:
    actions = ["duplicate"]

    @abc.abstractmethod
    def copy(self, instance, created_by_id):
        pass

    @transaction.atomic
    def duplicate(self, request, queryset):
        if len(queryset) != 1:
            messages.error(request, "Please select only one item.")
            return
        self.copy(instance=queryset.first(), created_by_id=request.user.pk)
        messages.success(request, "Successfully Copied.")

    duplicate.short_description = "Duplicate"
    duplicate.allowed_permissions = ("duplicate",)

    def has_duplicate_permission(self, request):
        return True
