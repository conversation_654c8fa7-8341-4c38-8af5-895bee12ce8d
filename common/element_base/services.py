import abc
import logging
from typing import Dict, <PERSON>, Tuple

from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework.settings import api_settings

from common.choices import CustomElementType
from common.element_base.entities import (
    ElementImportEntity,
    GuidelineAttachmentBaseData,
    GuidelineAttachmentData,
    GuidelineAttachmentUpdateData,
    GuidelineBaseData,
    GuidelineData,
    GuidelineUpdateData,
    PreviewFileBaseData,
    PreviewFileData,
    PreviewFileUpdateData,
    ProductionDrawingBaseData,
    ProductionDrawingData,
    ProductionDrawingUpdateData,
)
from common.element_base.related_base_services.guideline_base import (
    ElementGuidelineBaseService,
)
from common.element_base.related_base_services.preview_file_base import (
    ElementPreviewFileBaseService,
)
from common.element_base.related_base_services.production_drawing_base import (
    ElementProductionDrawingBaseService,
)
from common.entities import ObjectStatus
from common.exceptions import BaseValidationError, Multiple<PERSON>rueValueError, NoTrueValueError
from common.services import model_update, nested_object_segregation
from common.utils import (
    check_if_only_one_truth_value_in_list,
    padding_for_serial_number,
)
from element.data.entities import ElementBaseData
from element.data.models import (
    ElementBase,
    GuidelineAttachmentBase,
    GuidelineBase,
    PreviewFileBase,
    ProductionDrawingBase,
    ProductionDrawingTagBase,
)

logger = logging.getLogger(__name__)


class ElementRelatedBasePersistenceServices:
    production_drawing: ProductionDrawingBase = None
    guideline: GuidelineBase = None
    guideline_attachment: GuidelineAttachmentBase = None
    preview_file: PreviewFileBase = None

    @classmethod
    def production_drawing_create(
        cls,
        production_drawing_data: ProductionDrawingBaseData,
        element_id: int,
        created_by_id: int,
        save: bool = True,
        clean: bool = True,
    ):
        production_drawing = cls.production_drawing()
        production_drawing.name = production_drawing_data.name
        production_drawing.file = production_drawing_data.file
        production_drawing.element_id = element_id
        production_drawing.uploaded_by_id = created_by_id
        if hasattr(production_drawing_data, "uploaded_at"):
            production_drawing.uploaded_at = production_drawing_data.uploaded_at

        if clean:
            production_drawing.full_clean()

        if save:
            production_drawing.save()

        return production_drawing

    @classmethod
    def guideline_create(
        cls,
        guideline_data: GuidelineBaseData,
        created_by_id: int,
        element_id: int,
        clean: bool = True,
        save: bool = True,
    ):
        guideline = cls.guideline()
        guideline.name = guideline_data.name
        guideline.element_id = element_id
        guideline.description = guideline_data.description
        guideline.created_by_id = created_by_id
        guideline.created_at = timezone.now()

        if clean:
            guideline.full_clean()

        if save:
            guideline.save()

        return guideline

    @classmethod
    def guideline_attachment_create(
        cls,
        created_by_id: int,
        attachment_data: GuidelineAttachmentBaseData,
        guideline_id: int,
        clean: bool = True,
        save: bool = True,
    ):
        attachment = cls.guideline_attachment()
        attachment.element_guideline_id = guideline_id
        attachment.file = attachment_data.file
        attachment.name = attachment_data.name
        attachment.type = attachment_data.type
        attachment.uploaded_by_id = created_by_id

        if clean:
            attachment.full_clean()

        if save:
            attachment.save()
        return attachment

    @classmethod
    def guideline_attachment_bulk_create(
        cls,
        guideline_id: int,
        attachments: list[GuidelineAttachmentBaseData],
        updated_by_id: int,
    ):
        if attachments:
            guidelines_attachment_list = []
            for attachment in attachments:
                attachment_obj = cls.guideline_attachment_create(
                    attachment_data=attachment,
                    guideline_id=guideline_id,
                    created_by_id=updated_by_id,
                    save=False,
                    clean=False,
                )
                guidelines_attachment_list.append(attachment_obj)

            cls.guideline_attachment.objects.bulk_create(objs=guidelines_attachment_list)

    @classmethod
    def guideline_bulk_create(
        cls,
        guidelines: list[GuidelineData],
        element_id: int,
        created_by_id: int,
    ):
        guidelines_list = []
        for guideline_data in guidelines:
            guideline = cls.guideline_create(
                guideline_data=guideline_data,
                created_by_id=created_by_id,
                element_id=element_id,
                clean=True,
                save=False,
            )
            guidelines_list.append([guideline, guideline_data.attachments])

        created_guidelines = cls.guideline.objects.bulk_create(objs=[guidelines[0] for guidelines in guidelines_list])
        guidelines_attachment_list = []
        for created_guideline, guideline_data in zip(created_guidelines, guidelines_list):
            if guideline_data[1]:
                for attachment_data in guideline_data[1]:
                    attachment = cls.guideline_attachment_create(
                        attachment_data=attachment_data,
                        guideline_id=guideline_data[0].id,
                        created_by_id=created_by_id,
                        save=False,
                        clean=False,
                    )
                    guidelines_attachment_list.append(attachment)

        cls.guideline_attachment.objects.bulk_create(objs=guidelines_attachment_list)

    @classmethod
    def preview_file_create(
        cls,
        preview_file_data: PreviewFileBaseData,
        element_id: int,
        created_by_id: int,
        save: bool = True,
        clean: bool = True,
    ):
        preview_file = cls.preview_file()
        preview_file.file = preview_file_data.file
        preview_file.name = preview_file_data.name
        preview_file.is_main = preview_file_data.is_main
        preview_file.type = preview_file_data.type
        preview_file.element_id = element_id
        preview_file.uploaded_by_id = created_by_id
        preview_file.uploaded_at = timezone.now()

        if clean:
            preview_file.full_clean()

        if save:
            preview_file.save()

        return preview_file

    @classmethod
    def preview_file_bulk_create(
        cls,
        preview_files: list[PreviewFileBaseData],
        element_id: int,
        created_by_id: int,
        create: bool = True,
    ):
        flag = False
        try:
            flag = check_if_only_one_truth_value_in_list(flag_list=[file.is_main for file in preview_files])
        except MultipleTrueValueError:
            raise MultipleTrueValueError("is_main assigned to more than one preview file")
        except NoTrueValueError:
            if not cls.preview_file.objects.available().filter(element_id=element_id).count():
                raise NoTrueValueError("no is_main assigned")

        if flag:
            cls.preview_file.objects.available().filter(element_id=element_id).update(is_main=False)
        preview_file_list = []
        for preview_file in preview_files:
            preview_file_list.append(
                cls.preview_file_create(
                    preview_file_data=preview_file,
                    element_id=element_id,
                    created_by_id=created_by_id,
                    save=False,
                    clean=True,
                )
            )
        if create:
            return cls.preview_file.objects.bulk_create(objs=preview_file_list)

        return preview_file_list

    @classmethod
    def production_drawing_update(
        cls,
        instance,
        data: ProductionDrawingBaseData,
        updated_by_id: int,
        clean: bool = True,
        save: bool = True,
    ):
        fields = ["name"]
        production_drawing, _, _ = model_update(
            instance=instance,
            fields=fields,
            data=data,
            updated_by_id=updated_by_id,
            clean=clean,
            save=save,
        )
        if not save:
            return production_drawing

        if production_drawing.tags:
            production_drawing.tags.set(production_drawing.tags)

        return production_drawing

    @classmethod
    def production_drawing_bulk_update(
        cls,
        production_drawings: list[ProductionDrawingUpdateData],
        updated_by_id: int,
    ):
        production_drawings_dict = {
            production_drawing.id: production_drawing for production_drawing in production_drawings
        }
        id_set = set(production_drawings_dict.keys())
        production_drawing_obj_dict = cls.production_drawing.objects.available().in_bulk(id_list=id_set)
        obj_id_set = set(production_drawing_obj_dict.keys())

        if obj_id_set != id_set:
            raise ValidationError({"production_drawings": f"Unable to get data for  {len(id_set-obj_id_set)} drawings"})

        to_update_obj_list = []
        for key, value in production_drawing_obj_dict.items():
            production_drawing = cls.production_drawing_update(
                instance=value,
                data=production_drawings_dict.get(key),
                updated_by_id=updated_by_id,
                clean=True,
                save=False,
            )

            value.tags.set(production_drawings_dict.get(key).tags)
            to_update_obj_list.append(production_drawing)

        cls.production_drawing.objects.bulk_update(objs=to_update_obj_list, fields=["name"])

    @classmethod
    def production_drawing_bulk_create(
        cls,
        element_id: int,
        production_drawings_data: list[ProductionDrawingData],
        created_by_id,
    ):
        production_drawings_created = None
        if production_drawings_data:
            production_drawing_obj_list = []
            for production_drawing_data in production_drawings_data:
                production_drawing = cls.production_drawing_create(
                    production_drawing_data=production_drawing_data,
                    element_id=element_id,
                    created_by_id=created_by_id,
                    clean=True,
                    save=False,
                )
                production_drawing_obj_list.append([production_drawing, production_drawing_data.tags])

            production_drawings_created = cls.production_drawing.objects.bulk_create(
                objs=[obj[0] for obj in production_drawing_obj_list]
            )
            for new_drawings, drawings in zip(production_drawings_created, production_drawing_obj_list):
                if drawings[1]:
                    new_drawings.tags.set(drawings[1])

        return production_drawings_created

    @classmethod
    def preview_file_assign_main(
        cls,
        element_id: int,
        file_id: int,
    ):
        preview_file = cls.preview_file.objects.filter(element_id=element_id, id=file_id).available().first()
        if preview_file:
            if preview_file.is_main:
                return
            else:
                cls.preview_file.objects.filter(element_id=element_id).available().update(is_main=False)
                preview_file.is_main = True
                preview_file.save(update_fields=["is_main"])
        else:
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("File not available.")})

    @classmethod
    def guideline_update(cls, element_guideline_id: int, data, updated_by_id: int, save: bool = True):
        fields = ["name", "description"]
        element_guideline = cls.guideline.objects.filter(pk=element_guideline_id).first()
        if not element_guideline:
            raise ValidationError({"element_guideline_id": ("Element guideline not found.")})
        element_guideline, _, _ = model_update(
            instance=element_guideline, fields=fields, data=data, updated_by_id=updated_by_id, save=save
        )
        return element_guideline

    @classmethod
    def guidelines_bulk_update(cls, guidelines: list[GuidelineUpdateData], updated_by_id: int):
        guidelines_list = []
        for guideline in guidelines:
            guideline_obj = cls.guideline_update(
                element_guideline_id=guideline.id, data=guideline, updated_by_id=updated_by_id, save=False
            )
            guidelines_list.append(guideline_obj)

        cls.guideline.objects.bulk_update(
            objs=guidelines_list, fields=["name", "description", "updated_at", "updated_by_id"]
        )

    @classmethod
    def preview_file_delete(
        cls,
        element_id: int,
        preview_files: list[PreviewFileUpdateData],
        deleted_by_id: int,
    ):
        delete_id_list = [obj.id for obj in preview_files]
        cls.preview_file.objects.filter(id__in=delete_id_list).update(
            deleted_at=timezone.now(), deleted_by_id=deleted_by_id
        )
        is_main_available = cls.preview_file.objects.filter(element_id=element_id, is_main=True).available()
        if is_main_available:
            return
        else:
            element_preview_file = (
                cls.preview_file.objects.filter(element_id=element_id).available().order_by("type").first()
            )
            if element_preview_file:
                element_preview_file.is_main = True
                element_preview_file.save(update_fields=["is_main"])

    @classmethod
    def guideline_delete(
        cls,
        guideline_list: list[GuidelineUpdateData],
        deleted_by_id: int,
    ):
        cls.guideline.objects.filter(id__in=[obj.id for obj in guideline_list]).update(
            deleted_at=timezone.now(), deleted_by_id=deleted_by_id
        )

    @classmethod
    def guideline_attachment_delete(
        cls,
        attachment_list: list[GuidelineAttachmentUpdateData],
        deleted_by_id: int,
    ):
        cls.guideline_attachment.objects.filter(id__in=attachment_list).update(
            deleted_at=timezone.now(), deleted_by_id=deleted_by_id
        )

    @classmethod
    def production_drawing_delete(
        cls,
        production_drawings: list[ProductionDrawingUpdateData],
        deleted_by_id: int,
    ):
        cls.production_drawing.objects.filter(id__in=[obj.id for obj in production_drawings]).update(
            deleted_at=timezone.now(), deleted_by_id=deleted_by_id
        )


class ElementRelatedServiceNew(
    ElementPreviewFileBaseService,
    ElementGuidelineBaseService,
    ElementProductionDrawingBaseService,
):
    production_drawing = ProductionDrawingBase
    guideline = GuidelineBase
    guideline_attachment = GuidelineAttachmentBase
    preview_file = PreviewFileBase
    production_drawing_tag = ProductionDrawingTagBase


class GuidelinesProcessor:
    @classmethod
    def process_guideline_update(cls, service, data, element, updated_by_id, save: bool = True):
        attachment_id_for_deletion = []
        guidelines_id_for_deletion = []
        guidelines_model_objs_for_update = []
        added_attachments_model_objs_for_create = []
        guideline_data_for_create = []  # [ (guidelines_model_objs_for_create,attachments_model_objs_for_create,)]

        guidelines = element.guidelines.all()

        guidelines_dict = {guideline.id: guideline for guideline in guidelines}
        if data.guidelines:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.guidelines)
            if for_create:
                guideline_data_for_create.extend(
                    cls.process_guideline_create(
                        element=element, data=data, created_by_id=updated_by_id, service=service, save=False
                    )
                )

            if for_updates:
                for guideline in data.guidelines:
                    setattr(guideline, "updated_by_id", updated_by_id)

                guidelines_model_objs = service.guidelines_bulk_update(
                    guidelines_obj_dict=guidelines_dict, guidelines=for_updates, save=False
                )
                guidelines_model_objs_for_update.extend(guidelines_model_objs)

                # For attachments
                for update in for_updates:
                    for_create, for_updates, for_delete = nested_object_segregation(docs_list=update.attachments)

                    if for_create:
                        guideline, attachment = service.guideline_attachment_bulk_create(
                            guideline=guidelines_dict[update.id],
                            attachments=update.attachments,
                            created_by_id=updated_by_id,
                            save=False,
                        )
                        added_attachments_model_objs_for_create.extend(attachment)

                    if for_delete:
                        attachment_id_for_deletion.extend([obj.id for obj in for_delete])

            if for_delete:
                guidelines_id_for_deletion.extend([obj.id for obj in for_delete])

        if save:
            newly_created_guidelines = service.guideline.objects.bulk_create(
                objs=[guidelines[0] for guidelines in guideline_data_for_create]
            )
            service.guideline.objects.bulk_update(objs=guidelines_model_objs_for_update, fields=["name"])
            attachment_to_create = []
            for guideline, attachments in zip(newly_created_guidelines, guideline_data_for_create):
                if attachments[1]:
                    for attachment in attachments[1]:
                        setattr(attachment, service.guideline_attachment().get_guideline_foriegn_key, guideline.id)
                        attachment_to_create.append(attachment)

            attachment_to_create.extend(added_attachments_model_objs_for_create)
            service.guideline_attachment.objects.bulk_create(attachment_to_create)
            service.guideline_delete(id_list=guidelines_id_for_deletion, deleted_by_id=updated_by_id)
            service.guideline_attachment_delete(
                attachment_id_list=attachment_id_for_deletion, deleted_by_id=updated_by_id
            )

        return (
            guideline_data_for_create,
            guidelines_model_objs_for_update,
            added_attachments_model_objs_for_create,
            attachment_id_for_deletion,
            guidelines_id_for_deletion,
        )

    @classmethod
    def process_guideline_create(cls, element, data, service, created_by_id, save):
        guideline_and_attachments_for_create = []
        for guideline in data.guidelines:
            setattr(guideline, "element_id", element.id)
            setattr(guideline, "created_by_id", created_by_id)

        guideline_and_attachment = []
        for guideline in data.guidelines:
            guideline_and_attachment.append(tuple([guideline, guideline.attachments]))

        created_guidelines_objs = service.guideline_bulk_create(
            guidelines=[guideline[0] for guideline in guideline_and_attachment], save=save
        )
        for created_guidelines_obj, attachments in zip(created_guidelines_objs, guideline_and_attachment):
            tuple_of_guideline_and_attachment = (created_guidelines_obj, [])
            if attachments[1]:
                tuple_of_guideline_and_attachment = service.guideline_attachment_bulk_create(
                    guideline=created_guidelines_obj,
                    created_by_id=created_by_id,
                    attachments=attachments[1],
                    save=False,
                )
            guideline_and_attachments_for_create.append(tuple_of_guideline_and_attachment)

        if save:
            to_create_attachments_list = []
            for tup in guideline_and_attachments_for_create:
                if tup[1]:
                    for attachment in tup[1]:
                        setattr(attachment, "guideline_id", tup[0].id)
                    to_create_attachments_list.extend(tup[1])

            service.guideline_attachment.objects.bulk_create(objs=to_create_attachments_list)
            return created_guidelines_objs
        return guideline_and_attachments_for_create


class ProductionDrawingProcessor:
    @classmethod
    def process_production_drawing_create(cls, created_by_id: int, element_id: int, data, service, save: bool):
        production_drawings_model_objs_for_create = []
        for drawings in data.production_drawings:
            setattr(drawings, "element_id", element_id)
            setattr(drawings, "created_by_id", created_by_id)
        production_drawings_model_objs_for_create.extend(
            service.production_drawing_bulk_create(production_drawings_data=data.production_drawings, save=save)
        )
        if save:
            tags_objs = []
            for drawing in production_drawings_model_objs_for_create:
                if drawing[1]:
                    objs = service.production_drawing_tags_bulk_create(
                        data_list=[{"id": drawing[0].id, "tags": drawing[1]}], save=False
                    )
                    tags_objs.extend(objs)
            service.production_drawing_tag.objects.bulk_create(objs=tags_objs)
            return production_drawings_model_objs_for_create

        return production_drawings_model_objs_for_create

    @classmethod
    def process_production_drawing_update(cls, service, data, element, updated_by_id, save: bool = True):
        production_drawings_model_objs_for_create = []
        production_drawings_model_objs_for_update = []
        production_drawing_delete_id_list = []
        tags_to_create_list = []
        tags_to_delete_list = []

        production_drawings = element.production_drawings.all()
        production_drawings_dict = {drawing.id: drawing for drawing in production_drawings}

        if data.production_drawings:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.production_drawings)
            if for_create:
                production_drawings_model_objs_for_create.extend(
                    cls.process_production_drawing_create(
                        created_by_id=updated_by_id, element_id=element.id, service=service, data=data, save=False
                    )
                )

            if for_updates:
                objs_list, tags_to_create_list, tags_to_delete_list = service.production_drawing_bulk_update(
                    production_drawing_dict=production_drawings_dict,
                    production_drawings=for_updates,
                    updated_by_id=updated_by_id,
                )

                production_drawings_model_objs_for_update.extend(objs_list)

            if for_delete:
                production_drawing_delete_id_list.extend([obj.id for obj in for_delete])

        if save:
            created_objects = service.production_drawing.objects.bulk_create(
                [drawing[0] for drawing in production_drawings_model_objs_for_create]
            )

            for created_obj, drawing in zip(created_objects, production_drawings_model_objs_for_create):
                tags_to_create_list.append({"id": created_obj.id, "tags": drawing[1]})

            if tags_to_create_list:
                service.production_drawing_tags_bulk_create(data_list=tags_to_create_list)

            if tags_to_delete_list:
                service.production_drawing_tags_delete(data_list=tags_to_delete_list)

            service.production_drawing.objects.bulk_update(
                objs=production_drawings_model_objs_for_update, fields=["name"]
            )

            service.production_drawing_delete(
                production_drawings_id_list=production_drawing_delete_id_list, deleted_by_id=updated_by_id
            )

            return created_objects

        return (
            production_drawings_model_objs_for_create,
            production_drawings_model_objs_for_update,
            production_drawing_delete_id_list,
            tags_to_create_list,
            tags_to_delete_list,
        )


class PreviewFileProcessor:
    @classmethod
    def process_preview_file_create(cls, data, element, created_by_id, service, save):
        preview_files_model_objs_for_create = []
        if data.preview_files:
            for file in data.preview_files:
                setattr(file, "element_id", element.id)
                setattr(file, "created_by_id", created_by_id)
            preview_files_model_objs_for_create.extend(
                service.preview_file_bulk_create(preview_files=data.preview_files, save=save)
            )
        return preview_files_model_objs_for_create

    @classmethod
    def process_preview_files_update(cls, element, service, data, updated_by_id, save: bool = True):
        preview_files_model_objs_for_create = []
        preview_files_model_objs_for_update = []
        preview_files_model_objs_for_delete = []

        preview_files_obj = element.preview_files.all().filter(deleted_at__isnull=True)

        if data.preview_files:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.preview_files)
            is_main_flag = False  # Flag to store if is_main is assigned to any new file

            main_file_id = None
            delete_id_list = [obj.id for obj in for_delete]

            for file in for_create:
                # Check is_main for new files
                if file.is_main:
                    if not is_main_flag:
                        is_main_flag = True
                    else:
                        raise MultipleTrueValueError("is_main assigned to more than one preview file")

            if preview_files_obj:
                # store main file id from previous preview files
                for preview_file in preview_files_obj:
                    if preview_file.is_main:
                        main_file_id = preview_file.id

                if not main_file_id:
                    raise NoTrueValueError("No main file is assigned previously.")
            else:
                # No preview files available, check for main file in new files
                if not is_main_flag:
                    raise NoTrueValueError("No main file is assigned.")

            if is_main_flag and for_updates:
                raise MultipleTrueValueError("You have assigned to more than one main preview file.")

            preview_files_dict = {obj.id: obj for obj in preview_files_obj}

            if for_create:
                preview_files_model_objs_for_create.extend(
                    cls.process_preview_file_create(
                        data=data, element=element, created_by_id=updated_by_id, service=service, save=False
                    )
                )
                if is_main_flag:
                    # make previous main file is_main=False
                    service.preview_file_remove_as_main(preview_file_id=main_file_id)

            if for_updates:
                # make previous main file is_main=False
                service.preview_file_remove_as_main(preview_file_id=main_file_id)

                objs_list = service.preview_file_update(preview_files_dict[for_updates[0].id], save=False)
                preview_files_model_objs_for_update.extend([objs_list])

            if for_delete:
                if main_file_id in delete_id_list and not is_main_flag and not for_updates:
                    raise NoTrueValueError("Make another file as 'main file' before deleting the main file.")
                preview_files_model_objs_for_delete.extend([obj.id for obj in for_delete])

        if save:
            service.preview_file.objects.bulk_create(objs=preview_files_model_objs_for_create)
            service.preview_file.objects.bulk_update(objs=preview_files_model_objs_for_update, fields=["is_main"])
            service.preview_file_delete(delete_id_list=preview_files_model_objs_for_delete, deleted_by_id=updated_by_id)
            return (
                preview_files_model_objs_for_create,
                preview_files_model_objs_for_update,
                preview_files_model_objs_for_delete,
            )

        return (
            preview_files_model_objs_for_create,
            preview_files_model_objs_for_update,
            preview_files_model_objs_for_delete,
        )


class ElementRelatedCommonService:
    @classmethod
    def process_update(cls, service, updated_by_id: int, element_id: int, data):
        if data.guidelines:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.guidelines)
            if for_create:
                service.guideline_bulk_create(guidelines=for_create, element_id=element_id, created_by_id=updated_by_id)

            if for_updates:
                service.guidelines_bulk_update(guidelines=for_updates, updated_by_id=updated_by_id)

                for update in for_updates:
                    for_create, for_updates, for_delete = nested_object_segregation(docs_list=update.attachments)

                    if for_create:
                        service.guideline_attachment_bulk_create(
                            guideline_id=update.id, attachments=update.attachments, updated_by_id=updated_by_id
                        )
                    if for_delete:
                        service.guideline_attachment_delete(
                            attachment_list=[obj.id for obj in for_delete], deleted_by_id=updated_by_id
                        )

            if for_delete:
                service.guideline_delete(guideline_list=for_delete, deleted_by_id=updated_by_id)

        if data.production_drawings:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.production_drawings)
            if for_create:
                service.production_drawing_bulk_create(
                    element_id=element_id,
                    production_drawings_data=data.production_drawings,
                    created_by_id=updated_by_id,
                )

            if for_updates:
                service.production_drawing_bulk_update(production_drawings=for_updates, updated_by_id=updated_by_id)

            if for_delete:
                service.production_drawing_delete(production_drawings=for_delete, deleted_by_id=updated_by_id)

        if data.preview_files:
            for_create, for_updates, for_delete = nested_object_segregation(docs_list=data.preview_files)
            if for_create:
                service.preview_file_bulk_create(
                    preview_files=data.preview_files,
                    element_id=element_id,
                    created_by_id=updated_by_id,
                )

            if for_updates:
                if len(for_updates) > 1:
                    raise NoTrueValueError("Preview File update support assign main to only one file")

                elif len(for_updates) == 1:
                    service.preview_file_assign_main(element_id=element_id, file_id=for_updates[0].id)

            if for_delete:
                service.preview_file_delete(
                    element_id=element_id, preview_files=for_delete, deleted_by_id=updated_by_id
                )

    @classmethod
    def process_create(cls, service, data, element_id: int, created_by_id: int):
        if data.preview_files:
            service.preview_file_bulk_create(
                preview_files=data.preview_files,
                element_id=element_id,
                created_by_id=created_by_id,
            )
            logger.info("Preview file created")

        if data.guidelines:
            service.guideline_bulk_create(
                guidelines=data.guidelines, element_id=element_id, created_by_id=created_by_id
            )
            logger.info("Element Service- mapping created")

        if data.production_drawings:
            service.production_drawing_bulk_create(
                element_id=element_id,
                production_drawings_data=data.production_drawings,
                created_by_id=created_by_id,
            )
            logger.info("Element Production Drawing Created")


class ElementRelatedCommonServiceNew:
    @classmethod
    def process_update(cls, service, updated_by_id: int, element, data, save: bool = True):
        guidelines_data = GuidelinesProcessor.process_guideline_update(
            data=data, element=element, service=service, updated_by_id=updated_by_id, save=save
        )
        preview_files_data = PreviewFileProcessor.process_preview_files_update(
            element=element, service=service, data=data, updated_by_id=updated_by_id, save=save
        )

        if hasattr(element, "main_preview_file"):
            # check main file in newly created preview files
            main_file = None
            for preview_file in preview_files_data[0]:
                if preview_file.is_main:
                    main_file = preview_file
                    break

            # check main file in updated preview files
            if not main_file:
                for preview_file in preview_files_data[1]:
                    if preview_file.is_main:
                        main_file = preview_file
                        break

            if main_file and main_file != element.main_preview_file:
                element.main_preview_file = main_file
                if save:
                    element.save(update_fields=["main_preview_file"])

        production_drawing_data = ProductionDrawingProcessor.process_production_drawing_update(
            service=service, data=data, element=element, updated_by_id=updated_by_id, save=save
        )
        return (
            guidelines_data,
            preview_files_data,
            production_drawing_data,
        )

    @classmethod
    def process_bulk_update(cls, service, updated_by_id: int, element_list: list, data):
        preview_file_bulk_data = []
        production_drawing_bulk_data = []
        guideline_bulk_data = []
        preview_files_to_create = []
        preview_files_to_update = []
        preview_files_to_delete = []
        production_drawings_for_create = []
        production_drawings_for_update = []
        production_drawings_for_delete = []
        guideline_data_for_create = []
        guidelines_model_objs_for_update = []
        added_attachments_model_objs_for_create = []
        attachment_id_for_deletion = []
        guidelines_id_for_deletion = []

        element_dict = {element.id: element for element in element_list}
        for element_data in data:
            guidelines_data, preview_files_data, production_drawing_data = cls.process_update(
                data=element_data,
                element=element_dict.get(element_data.id),
                service=service,
                updated_by_id=updated_by_id,
                save=False,
            )

            guideline_bulk_data.append(guidelines_data)
            preview_file_bulk_data.append(preview_files_data)
            production_drawing_bulk_data.append(production_drawing_data)

        if preview_file_bulk_data:
            for files in preview_file_bulk_data:
                preview_files_to_create.extend(files[0])
                preview_files_to_update.extend(files[1])
                preview_files_to_delete.extend(files[2])

        service.preview_file.objects.bulk_create(objs=preview_files_to_create)
        service.preview_file.objects.bulk_update(objs=preview_files_to_update, fields=["is_main"])
        if preview_files_to_delete:
            service.preview_file.objects.filter(id__in=preview_files_to_delete).update(
                deleted_at=timezone.now(),
                deleted_by_id=updated_by_id,
            )

        if production_drawing_bulk_data:
            tags_to_create = []
            tags_to_delete = []
            for files in production_drawing_bulk_data:
                production_drawings_for_create.extend(files[0])
                production_drawings_for_update.extend(files[1])
                production_drawings_for_delete.extend(files[2])
                tags_to_create.extend(files[3])
                tags_to_delete.extend(files[4])
            created_objs = service.production_drawing.objects.bulk_create(
                objs=[drawing[0] for drawing in production_drawings_for_create]
            )
            tags_objs = []
            for created_obj, drawings in zip(created_objs, production_drawings_for_create):
                if drawings[1]:
                    objs = service.production_drawing_tags_bulk_create(
                        data_list=[{"id": created_obj.id, "tags": drawings[1]}], save=False
                    )
                    tags_objs.extend(objs)
            # tags_to_create.extend(tags_objs)
            tags_objs.extend(service.production_drawing_tags_bulk_create(data_list=tags_to_create, save=False))
            service.production_drawing_tag.objects.bulk_create(objs=tags_objs)
            if tags_to_delete:
                service.production_drawing_tags_delete(data_list=tags_to_delete)
            service.production_drawing.objects.bulk_update(objs=production_drawings_for_update, fields=["name"])
            if production_drawings_for_delete:
                service.production_drawing.objects.filter(id__in=production_drawings_for_delete).update(
                    deleted_at=timezone.now(),
                    deleted_by_id=updated_by_id,
                )

        if guideline_bulk_data:
            for files in guideline_bulk_data:
                guideline_data_for_create.extend(files[0])
                guidelines_model_objs_for_update.extend(files[1])
                added_attachments_model_objs_for_create.extend(files[2])
                attachment_id_for_deletion.extend(files[3])
                guidelines_id_for_deletion.extend(files[4])

        attachment_to_create = []
        if guideline_data_for_create:
            newly_created_guidelines = service.guideline.objects.bulk_create(
                objs=[guidelines[0] for guidelines in guideline_data_for_create]
            )
            if newly_created_guidelines:
                for guideline, attachments in zip(newly_created_guidelines, guideline_data_for_create):
                    if attachments[1]:
                        for attachment in attachments[1]:
                            setattr(attachment, service.guideline_attachment().get_guideline_foriegn_key, guideline.id)
                            attachment_to_create.append(attachment)

                attachment_to_create.extend(added_attachments_model_objs_for_create)
        if guidelines_model_objs_for_update:
            service.guideline.objects.bulk_update(objs=guidelines_model_objs_for_update, fields=["name", "description"])
        if attachment_to_create:
            service.guideline_attachment.objects.bulk_create(attachment_to_create)
        if guidelines_id_for_deletion:
            service.guideline_delete(id_list=guidelines_id_for_deletion, deleted_by_id=updated_by_id)
        if attachment_id_for_deletion:
            service.guideline_attachment_delete(
                attachment_id_list=attachment_id_for_deletion, deleted_by_id=updated_by_id
            )

        return True

    @classmethod
    def process_create(cls, service, data, element, created_by_id: int, save: bool = True):
        preview_files_model_objs_for_create = []
        guideline_and_attachments_for_create = []
        production_drawings_model_objs_for_create = []

        if data.preview_files:
            if save:
                try:
                    check_if_only_one_truth_value_in_list(flag_list=[file.is_main for file in data.preview_files])
                except MultipleTrueValueError:
                    raise MultipleTrueValueError("is_main assigned to more than one preview file")
                except NoTrueValueError:
                    raise NoTrueValueError("no is_main assigned")

            preview_files_model_objs_for_create = PreviewFileProcessor.process_preview_file_create(
                data=data, element=element, service=service, created_by_id=created_by_id, save=save
            )

            if hasattr(element, "main_preview_file"):
                main_file = None
                for file in preview_files_model_objs_for_create:
                    if file.is_main:
                        main_file = file
                        break

                if main_file and main_file != element.main_preview_file:
                    element.main_preview_file = main_file
                    if save:
                        element.save(update_fields=["main_preview_file"])

        if data.guidelines:
            guideline_and_attachments_for_create.extend(
                GuidelinesProcessor.process_guideline_create(
                    element=element, data=data, service=service, created_by_id=created_by_id, save=save
                )
            )

        if data.production_drawings:
            production_drawings_model_objs_for_create.extend(
                ProductionDrawingProcessor.process_production_drawing_create(
                    created_by_id=created_by_id, element_id=element.id, data=data, service=service, save=save
                )
            )
        if save:
            return

        return (
            preview_files_model_objs_for_create,
            guideline_and_attachments_for_create,
            production_drawings_model_objs_for_create,
        )

    @classmethod
    def process_bulk_create(
        cls,
        service,
        elements: List[ElementBase],
        data: List[ElementBaseData],
        created_by_id: int,
    ):
        preview_files_model_objs = []
        guidelines_model_objs = []
        production_drawings_model_objs = []

        for index, element_data in enumerate(data):
            (
                preview_files,
                guidelines,
                production_drawings,
            ) = cls.process_create(
                service=service,
                element=elements[index],
                save=False,
                data=element_data,
                created_by_id=created_by_id,
            )
            preview_files_model_objs.extend(preview_files)
            guidelines_model_objs.extend(guidelines)
            production_drawings_model_objs.extend(production_drawings)

        if preview_files_model_objs:
            service.preview_file.objects.bulk_create(objs=preview_files_model_objs)
        if guidelines_model_objs:
            guidelines = service.guideline.objects.bulk_create(
                objs=[guideline[0] for guideline in guidelines_model_objs]
            )
            attachments_to_create = []
            for created_guidelines, tup in zip(guidelines, guidelines_model_objs):
                if tup[1]:
                    for attachment in tup[1]:
                        setattr(attachment, "guideline_id", created_guidelines.id)
                    attachments_to_create.extend(tup[1])

            if attachments_to_create:
                service.guideline_attachment.objects.bulk_create(objs=attachments_to_create)
        if production_drawings_model_objs:
            created_objs = service.production_drawing.objects.bulk_create(
                objs=[drawing[0] for drawing in production_drawings_model_objs]
            )
            tags_objs = []
            for created_obj, drawings in zip(created_objs, production_drawings_model_objs):
                if drawings[1]:
                    objs = service.production_drawing_tags_bulk_create(
                        data_list=[{"id": created_obj.id, "tags": drawings[1]}], save=False
                    )
                    tags_objs.extend(objs)
            service.production_drawing_tag.objects.bulk_create(objs=tags_objs)

        return True

    @classmethod
    def prepare_data_entities(
        cls,
        created_element_objects,
        element_list,
    ):
        entity_list = []
        for new_element, element in zip(created_element_objects, element_list):
            preview_files = element.preview_files.all()
            preview_file_list = []
            for preview_file in preview_files:
                preview_file_list.append(
                    PreviewFileData(
                        object_status=ObjectStatus.ADD,
                        type=preview_file.type,
                        file=preview_file.file,
                        name=preview_file.name,
                        is_main=preview_file.is_main,
                    )
                )
            guideline_data_list = []
            for guideline in element.guidelines.all():
                attachments = guideline.attachments.all()
                attachment_list = []
                for attachment in attachments:
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )

                guideline_data_list.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        attachments=attachment_list,
                        description=guideline.description,
                        name=guideline.name,
                    )
                )
            production_drawing_data = []
            for drawings in element.production_drawings.all():
                production_drawing_data.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        file=drawings.file,
                        name=drawings.name,
                        tags=drawings.tags.values_list("id", flat=True),
                    )
                )

            entity_list.append(
                ElementImportEntity(
                    object_status=ObjectStatus.ADD,
                    id=new_element.id,
                    preview_files=preview_file_list,
                    guidelines=guideline_data_list,
                    production_drawings=production_drawing_data,
                )
            )
        return entity_list


class ElementBaseService:
    class ElementCreateException(BaseValidationError):
        pass

    element_model: ElementBase = None
    version_field = None

    @abc.abstractclassmethod
    def get_project_id(cls, element_entity: ElementBase):
        ...

    @abc.abstractclassmethod
    def get_elements_code(cls, category_ids: List[int], client_id: int) -> List:
        ...

    @abc.abstractclassmethod
    def get_serial_numbers(cls, limit: int, client_id: int) -> Dict:
        ...

    @classmethod
    def create(cls, element_entity: ElementBaseData, save: bool = True) -> ElementBase:
        element_obj = cls.element_model()
        element_obj.quantity = element_entity.quantity
        element_obj.client_id = element_entity.client_id
        element_obj.client_rate = element_entity.client_rate
        element_obj.item_type_id = element_entity.item_type_id
        element_obj.custom_type = element_entity.custom_type
        if not element_entity.serial_number:
            element_obj.serial_number = cls.get_serial_numbers(limit=1, client_id=element_entity.client_id)[0]
        else:
            element_obj.serial_number = element_entity.serial_number
        element_obj.code = element_entity.code
        element_obj.name = element_entity.name
        element_obj.uom = element_entity.uom
        element_obj.created_by_id = element_entity.created_by_id
        element_obj.description = element_entity.description
        element_obj.category_id = element_entity.category_id
        if hasattr(element_entity, "extra_data") and element_entity.extra_data:
            element_obj.extra_data = element_entity.extra_data

        if save:
            element_obj.full_clean()
            element_obj.save()
        return element_obj

    @classmethod
    def create_bulk(cls, element_entities: List[ElementBaseData], save: bool = True) -> List[ElementBase]:
        serial_numbers_with_versions: Dict = cls.get_versions(
            serial_numbers=[element.serial_number for element in element_entities],
            project_id=cls.get_project_id(element_entity=element_entities[0]),
        )
        element_objs = []
        for index, element_entity in enumerate(element_entities):
            if hasattr(element_entity, cls.version_field) and getattr(element_entity, cls.version_field) is None:
                count = serial_numbers_with_versions[element_entity.serial_number]["count"] - 1
                setattr(
                    element_entity,
                    cls.version_field,
                    serial_numbers_with_versions[element_entity.serial_number]["version"] + count,
                )
                serial_numbers_with_versions[element_entity.serial_number]["count"] -= 1
            setattr(element_entity, "code", element_entity.code)
            element_obj: ElementBase = cls.create(element_entity=element_entity, save=False)
            if hasattr(element_entity, "discount_percent"):
                element_obj.discount_percent = element_entity.discount_percent
            if hasattr(element_entity, "quantity_dimensions"):
                element_obj.quantity_dimensions = element_entity.quantity_dimensions
            if hasattr(element_entity, "service_charge_percent"):
                element_obj.service_charge_percent = element_entity.service_charge_percent
            if hasattr(element_entity, "is_service_charge_with_base_amount"):
                element_obj.is_service_charge_with_base_amount = element_entity.is_service_charge_with_base_amount
            if hasattr(element_entity, "tax_percent"):
                element_obj.tax_percent = element_entity.tax_percent
            if hasattr(element_entity, "discounted_client_rate"):
                element_obj.discounted_client_rate = element_entity.discounted_client_rate
            element_objs.append(element_obj)
        if save:
            return cls.element_model.objects.bulk_create(objs=element_objs)
        return element_objs

    @classmethod
    def update(
        cls,
        element_entity: ElementBaseData,
        element_instance: ElementBase,
        fields: List,
        updated_by_id=int,
        save: bool = True,
    ) -> ElementBase:
        element, _, updated_fields = model_update(
            instance=element_instance,
            fields=fields,
            updated_by_id=updated_by_id,
            data=element_entity,
            save=save,
            clean=save,
        )
        return element, updated_fields

    @classmethod
    def update_bulk(
        cls,
        element_entities: List[ElementBaseData],
        elements_instances: List[ElementBase],
        updated_by_id: int,
        categories_dict: Dict,
        fields: List,
        save: bool = True,
    ) -> Tuple[List[ElementBase], Dict]:
        element_objs: List[ElementBase] = []
        updated_fields_element_wise = {}
        for element_entity, element_instance in zip(element_entities, elements_instances):
            element_obj, updated_fields = cls.update(
                element_instance=element_instance,
                element_entity=element_entity,
                fields=fields,
                updated_by_id=updated_by_id,
                save=False,
            )
            updated_fields_element_wise[element_obj.id] = updated_fields
            if updated_fields:
                if "category_id" in updated_fields:
                    new_category_code = categories_dict[element_entity.category_id].code
                    element_obj.code = str(element_obj.code[:4]) + new_category_code
            if hasattr(element_entity, "discount_percent"):
                element_obj.discount_percent = element_entity.discount_percent
            if hasattr(element_entity, "service_charge_percent"):
                element_obj.is_service_charge_with_base_amount = element_entity.is_service_charge_with_base_amount
                if element_obj.is_service_charge_with_base_amount is None:
                    element_obj.service_charge_percent = 0
                else:
                    element_obj.service_charge_percent = element_entity.service_charge_percent
            if hasattr(element_entity, "tax_percent"):
                element_obj.tax_percent = element_entity.tax_percent
            element_objs.append(element_obj)
        if save:
            cls.element_model.objects.bulk_update(objs=element_objs, fields=fields)
            return element_objs
        return element_objs


class ElementCodeService:
    @classmethod
    def get_code(cls, serial_number, custom_type, code, version):
        if custom_type is None:
            # If its el element then there is no CX or CO
            serial_number = padding_for_serial_number(serial_number=serial_number, padding=4)
            if version == 0:
                return f"{code}{serial_number}"
            return f"{code}{serial_number}/{version}"
        elif custom_type == CustomElementType.BOQ:
            # If its custom_boq_element but not el element then there is CX
            if version == 0:
                serial_number = padding_for_serial_number(serial_number=serial_number, padding=4)
                return f"CX{code}{serial_number}"
            serial_number = padding_for_serial_number(serial_number=serial_number, padding=5)
            return f"CX{code}{serial_number}/{version}"
        elif custom_type == CustomElementType.ORDER:
            # If its custom order element then there is CO
            serial_number = padding_for_serial_number(serial_number=serial_number, padding=5)
            if version is None or version == 0:
                return f"CO{code}{serial_number}"
            return f"CO{code}{serial_number}/{version}"
        else:
            return ""
