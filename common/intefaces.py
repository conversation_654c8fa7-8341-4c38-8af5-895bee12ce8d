import abc
from typing import Dict, List

from common.context_values import BaseContextValues
from common.entities import ProjectRoleAssignee
from common.exceptions import BaseValidationError


class ContextValueProviderAbstractInterface(abc.ABC):
    @abc.abstractmethod
    def get_value(self, data: BaseContextValues): ...


class ContextProviderHelperAbstractInterface(abc.ABC):
    @abc.abstractstaticmethod
    def get_providers(self) -> Dict[str, ContextValueProviderAbstractInterface]: ...


class ProjectRoleAssigneeHelperAbstractInterface(abc.ABC):
    class ProjectAssigneeNotFound(BaseValidationError):
        pass

    class ProjectRoleDeleted(BaseValidationError):
        pass

    class ProjectUserDeleted(BaseValidationError):
        pass

    class ProjectUserInactive(BaseValidationError):
        pass

    class ProjectRoleInactive(BaseValidationError):
        pass

    @abc.abstractmethod
    def get_role_assignees(self, role_id_level_mapping: dict, project_id: int) -> List[ProjectRoleAssignee]: ...

    @abc.abstractmethod
    def check_last_level_assignees_for_misconfiguration(self, role_ids: list[int], project_id: int): ...
