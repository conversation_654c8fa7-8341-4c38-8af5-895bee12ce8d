from dataclasses import dataclass

from common.events.actions.base import CommentActionData


@dataclass(frozen=True)
class CommentMentionNotificationActionData(CommentActionData):
    user_id_list: list[int]
    mentioned_by_id: int


@dataclass(frozen=True)
class CommentApprovalBaseActionData(CommentActionData):
    mentioned_by_id: int
    approver_id: int


@dataclass(frozen=True)
class CommentApprovalRequestNotificationActionData(CommentApprovalBaseActionData):
    pass


@dataclass(frozen=True)
class CommentApprovalAcceptedRejectedActionData(CommentApprovalBaseActionData):
    pass


@dataclass(frozen=True)
class ProjectCommentBaseActionData:
    project_id: int
    org_id: int


@dataclass(frozen=True)
class ProjectCommentApprovalAcceptedRejectedActionData(
    CommentApprovalAcceptedRejectedActionData, ProjectCommentBaseActionData
): ...


@dataclass(frozen=True)
class ProjectCommentApprovalRequestActionData(
    CommentApprovalRequestNotificationActionData, ProjectCommentBaseActionData
): ...


@dataclass(frozen=True)
class ProjectCommentMentionNotificationActionData(
    CommentMentionNotificationActionData, ProjectCommentBaseActionData
): ...


@dataclass(frozen=True)
class CommentApprovalRequestedActionData(CommentActionData):
    pass


@dataclass(frozen=True)
class CommentApprovalMentionedActionData(CommentActionData):
    pass


@dataclass(frozen=True)
class CommentApprovalAcceptedRejectedActionData(CommentActionData):
    pass
