from dataclasses import dataclass
from typing import List, Optional

from common.events.base import BaseEventData
from microcontext.domain.constants import MicroContext


@dataclass(frozen=True)
class BaseRequestSchedularActionData: ...


@dataclass(frozen=True)
class RequestSchedularActionData(BaseRequestSchedularActionData):
    org_id: int
    context: MicroContext


@dataclass(frozen=True)
class RequestMisconfiguredSkipActionData(BaseRequestSchedularActionData):
    role_id: int
    user_id: int


@dataclass(frozen=True)
class MisconfigRequestSchedularActionData(BaseRequestSchedularActionData):
    user_id: Optional[int]
    role_id: Optional[int]


@dataclass(frozen=True)
class ResourceRequestBaseActionData(BaseEventData):
    context_id: int
    context: MicroContext
    user_id: int


@dataclass(frozen=True)
class ResourceRequestCancelActionData(ResourceRequestBaseActionData):
    pass


@dataclass(frozen=True)
class BaseRequestActionData(BaseEventData):
    request_id: int


@dataclass(frozen=True)
class RequestCreateActionData(BaseRequestActionData):
    immediate_approver_ids: List[int]


@dataclass(frozen=True)
class RequestApprovedActionData(RequestCreateActionData):
    pass


@dataclass(frozen=True)
class RequestApprovalFinallyApprovedActionData(BaseRequestActionData):
    pass


@dataclass(frozen=True)
class RequestRejectedActionData(BaseRequestActionData):
    rejector_id: int


@dataclass(frozen=True)
class RequestHoldActionData(BaseRequestActionData):
    hold_by_id: int


@dataclass(frozen=True)
class RequestEditedActionData(RequestCreateActionData):
    editor_id: int
