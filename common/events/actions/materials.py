from dataclasses import dataclass

from common.events.actions.base import ProjectActionData
from inventory.domain.enums import TransferBatchActions


@dataclass(frozen=True)
class InventoryTransferBatchCreateActionData(ProjectActionData):
    batch_id: int


@dataclass(frozen=True)
class InventoryTransferBatchApproveActionData(InventoryTransferBatchCreateActionData):
    action: TransferBatchActions
    transfer_id: int
