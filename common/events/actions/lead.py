from dataclasses import dataclass
from typing import List
from uuid import UUID

from common.events.base import BaseEventData


@dataclass(frozen=True)
class BaseLeadActionData(BaseEventData):
    lead_id: int


@dataclass(frozen=True)
class LeadAssignmentActionData(BaseLeadActionData):
    assignee_ids: List[int]
    assigned_by_id: int


@dataclass(frozen=True)
class LeadCreateActionData(BaseLeadActionData):
    board_id: int
    created_by_id: int


@dataclass(frozen=True)
class LeadStageChangeActionData(BaseLeadActionData):
    user_id: int
    board_id: int
    current_stage_id: int
    target_stage_id: int


@dataclass(frozen=True)
class BoardAssignmentActionData:
    board_id: int
    assignee_ids: List[int]
    assigned_by_id: int


@dataclass(frozen=True)
class LeadQuotationSubmitActionData(BaseEventData):
    quotation_id: int


@dataclass(frozen=True)
class LeadExportExcelActionData:
    board_id: int
    download_id: int
    uuid: UUID
    org_id: int
    user_id: int
    board_name: str
