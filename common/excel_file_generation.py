from io import BytesIO

# from openpyxl.worksheet.properties
import requests
from openpyxl import Workbook
from openpyxl.drawing.image import Image as XLImage
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils.units import pixels_to_points
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.writer.excel import save_virtual_workbook
from PIL import Image

"""
    data = [{"sheet_title":'title', sheet_data:"serializer.data"},
            {"sheet_title":'title', sheet_data:"serializer.data"}
            ]
"""


def auto_adjust_column_width(worksheet, image_column_letter):
    for col in worksheet.columns:
        max_length = 0
        column = col[0].column_letter  # Get the column name
        for cell in col:
            try:  # Necessary to avoid error on empty cells
                if len(max(str(cell.value).split("\n"))) > max_length:
                    max_length = len(max(str(cell.value).split("\n")))
            except:  # noqa
                pass
        adjusted_width = max_length + 1
        worksheet.column_dimensions[column].width = adjusted_width
        if column in image_column_letter.values():
            worksheet.column_dimensions[column].width = 34


def align_content_in_column(col, alignment):
    for cell in col:
        try:  # Necessary to avoid error on empty cells
            cell.alignment = alignment
        except:  # noqa
            pass


def format_text(worksheet, image_column_letter):
    default_alignment = Alignment(
        horizontal="general",
        vertical="top",
        text_rotation=0,
        wrap_text=True,
        shrink_to_fit=True,
        indent=0,
    )
    image_alignment = Alignment(
        horizontal="center",
        vertical="center",
        text_rotation=0,
        wrap_text=True,
        shrink_to_fit=True,
        indent=0,
    )

    for col in worksheet.columns:
        if image_column_letter and col[0].column_letter in image_column_letter.values():
            align_content_in_column(col=col, alignment=image_alignment)
        else:
            align_content_in_column(col=col, alignment=default_alignment)


def get_image_from_url(*, file):
    file_url = file + "?tr=w-200,h-200"
    data = requests.get(file_url)
    image_file = Image.open(BytesIO(data.content))
    return XLImage(image_file)


def get_logo_image_from_url(*, file, height=50):
    file_url = file + f"?tr=h-{height}"
    data = requests.get(file_url)
    image_file = Image.open(BytesIO(data.content))
    return XLImage(image_file)


def insert_image_in_excel_sheet(*, img, worksheet, column_letter, row_number):
    cell_number = f"{column_letter}{row_number}"
    img.anchor = cell_number
    worksheet.add_image(img)
    worksheet.row_dimensions[row_number].height = 170


def insert_org_logo_in_sheet(worksheet, org_logo_img):
    cell_number = "A1"
    org_logo_img.anchor = cell_number
    worksheet.add_image(org_logo_img)
    worksheet.row_dimensions[1].height = pixels_to_points(org_logo_img.height)


def insert_hyperlink(*, link, worksheet, column_letter, row_number):
    cell_number = f"{column_letter}{row_number}"
    worksheet[cell_number].hyperlink = link
    worksheet[cell_number].style = "Hyperlink"


def data_list_for_extend(
    image_fields,
    color_fields,
    data,
    hyperlink_data,
):
    li = []
    for key, value in data.items():
        if key not in image_fields and key not in hyperlink_data.values():
            if key in color_fields and value:
                li.append(value.get("value"))
            else:
                if value and isinstance(value, dict):
                    li.append(value.get("value"))
                else:
                    li.append(value)
    return li


def add_border_to_sheet(worksheet):
    thin = Side(border_style="thin", color="000000")
    border = Border(top=thin, left=thin, right=thin, bottom=thin)
    for col in worksheet.columns:
        for cell in col:
            try:
                cell.border = border
            except:  # noqa
                pass


def font_and_color_to_row_add(worksheet, color, row_no):
    for cell in worksheet[row_no]:
        try:
            cell.font = Font(bold=True, color=color)
        except:  # noqa
            pass


def top_border_to_row_add(worksheet, summary_total_row):
    thin = Side(border_style="thin", color="000000")
    border = Border(top=thin)
    for cell in worksheet[summary_total_row]:
        try:
            if not cell.col_idx == 1:
                cell.border = border
        except:  # noqa
            pass


def bottom_border_to_row_add(worksheet, summary_total_row):
    thin = Side(border_style="thin", color="000000")
    border = Border(bottom=thin)
    for cell in worksheet[summary_total_row]:
        try:
            if not cell.col_idx == 1:
                cell.border = border
        except:  # noqa
            pass


def align_text_in_row(worksheet, row_no, alignment):
    for cell in worksheet[row_no]:
        try:
            cell.alignment = Alignment(horizontal=alignment)
        except:  # noqa
            pass


def bold_text_to_row(worksheet, summary_total_row):
    for cell in worksheet[summary_total_row]:
        try:
            cell.font = cell.font.copy(bold=True)
        except:  # noqa
            pass


def add_font_color_to_field(sheet, color_field_column_letter, data, sno):
    for field_name, letter in color_field_column_letter.items():
        cell_number = f"{letter}{sno+2}"
        if isinstance(data.get(field_name), dict) and data.get(field_name).get("color"):
            sheet[cell_number].font = Font(color=data.get(field_name).get("color").replace("#", ""))


def format_sheet_header(column_letters, sheet, with_serial_number, is_summary_sheet, header_row_number):
    alignment = Alignment(
        horizontal="center",
        vertical="center",
        text_rotation=0,
        wrap_text=True,
        shrink_to_fit=True,
        indent=0,
    )
    if with_serial_number:
        sheet[f"A{header_row_number}"].font = Font(size=12, bold=True)
        if not is_summary_sheet:
            sheet[f"A{header_row_number}"].fill = PatternFill(
                start_color="FFC7CE", end_color="FFC7CE", fill_type="solid"
            )
        try:
            sheet[f"A{header_row_number}"].alignment = alignment
        except:  # noqa
            pass

    for letter in column_letters.values():
        sheet[f"{letter}{header_row_number}"].font = Font(size=12, bold=True)
        if not is_summary_sheet:
            sheet[f"{letter}{header_row_number}"].fill = PatternFill(
                start_color="FFC7CE", end_color="FFC7CE", fill_type="solid"
            )
        try:
            sheet[f"{letter}{header_row_number}"].alignment = alignment
        except:  # noqa
            pass


def summary_total_deductions_changes(
    worksheet: Workbook,
    no_of_rows_inserted: int,
    summary_dedcutions_key: str,
    summary_dedcutions_value: str,
    summary_total_key: str,
    total_value: int,
):
    if summary_total_key and summary_dedcutions_key:
        bottom_border_to_row_add(worksheet=worksheet, summary_total_row=no_of_rows_inserted - 1)
    if summary_dedcutions_key:
        sub_total = str(format(total_value, ".2f"))
        total_value -= summary_dedcutions_value
        value = "-" + str(format(summary_dedcutions_value, ".2f"))
        worksheet.append(["", "Sub Total", sub_total])
        align_text_in_row(worksheet=worksheet, row_no=no_of_rows_inserted, alignment="right")
        no_of_rows_inserted += 1
        worksheet.append(["", summary_dedcutions_key, value])
    align_text_in_row(worksheet=worksheet, row_no=no_of_rows_inserted, alignment="right")
    if summary_total_key:
        no_of_rows_inserted += 1
        worksheet.append(["", summary_total_key, str(format(total_value, ".2f"))])
        top_border_to_row_add(worksheet=worksheet, summary_total_row=no_of_rows_inserted)
        bold_text_to_row(worksheet=worksheet, summary_total_row=no_of_rows_inserted)
        align_text_in_row(worksheet=worksheet, row_no=no_of_rows_inserted, alignment="right")


def create_excel_file(data: list, org_logo_url_or_name: str = None):
    wb = Workbook()
    del wb["Sheet"]
    total_value = 0
    for sheet in data:
        sheet_title = sheet.get("sheet_title")
        sheet_data_list = sheet.get("sheet_data")
        column_names = []
        base_char = 65
        if sheet.get("with_serial_number"):
            column_names.append("S No.")
            base_char = 66

        new_sheet = wb.create_sheet(sheet_title)
        new_sheet.title = sheet.get("sheet_title")
        image_fields = sheet.get("image-fields", [])
        hyperlink_data = sheet.get("hyperlink-data", {})
        color_fields = sheet.get("color_fields", [])
        add_border = sheet.get("add-border", False)
        format_header = sheet.get("format_header", False)
        summary_sheet = sheet.get("summary_sheet", False)
        view_grid_lines = sheet.get("view_grid_lines", True)
        new_sheet.sheet_view.showGridLines = view_grid_lines

        header_row_number = 1
        org_logo_img = None
        if org_logo_url_or_name:
            new_sheet.append([])
            try:
                org_logo_img = get_logo_image_from_url(file=org_logo_url_or_name, height=50)
            except Exception:
                new_sheet.cell(row=1, column=1, value=org_logo_url_or_name)
            header_row_number = 2

        # TODO need to refactor Excel generation for summary and normal.
        if summary_sheet:
            summary_value_key = summary_sheet.get("summary_value_key", None)
            summary_total_key = summary_sheet.get("summary_total_key", None)
            summary_dedcutions_value = summary_sheet.get("summary_dedcutions_value", 0)
            summary_dedcutions_key = summary_sheet.get("summary_dedcutions_key", None)
            total_value = 0
        else:
            summary_value_key = None
            summary_total_key = None
            summary_dedcutions_value = 0
            summary_dedcutions_key = None

        column_names.extend(
            [
                (" ".join(key.split("_")).title() if len(key.split("_")) >= 1 else key)
                for key in sheet_data_list[0].keys()
                if key not in hyperlink_data.values() and key not in image_fields
            ]
        )
        column_names.extend(
            [(" ".join(key.split("_")).title() if len(key.split("_")) > 1 else key) for key in image_fields]
        )
        new_sheet.append(column_names)
        # no_of_rows_inserted means header rows , blank rows, mostly in which there isn't data  but other things
        no_of_rows_inserted = 2
        if summary_sheet:
            no_of_rows_inserted += 1
            new_sheet.append([])

        column_letters = {}
        index = 0

        for name in sheet_data_list[0].keys():
            if name in hyperlink_data.values() or name in image_fields:
                continue
            column_letters[name] = chr(base_char + index)
            index += 1

        for name in image_fields:
            column_letters[name] = chr(base_char + index)
            index += 1

        image_column_letter = {key: value for key, value in column_letters.items() if key in image_fields}
        color_field_column_letter = {key: value for key, value in column_letters.items() if key in color_fields}

        for sno, data in enumerate(sheet_data_list):
            record = []
            if sheet.get("with_serial_number"):
                record.append(sno + 1)

            record.extend(
                data_list_for_extend(
                    image_fields,
                    color_fields,
                    data,
                    hyperlink_data,
                )
            )
            if summary_sheet:
                total_value += float(data.get(summary_value_key))

            new_sheet.append(record)
            no_of_rows_inserted += 1
            if image_fields:
                image_url_data = {key: value for key, value in data.items() if key in image_fields}
                for field in image_fields:
                    if image_url_data.get(field):
                        insert_image_in_excel_sheet(
                            img=get_image_from_url(file=image_url_data.get(field)),
                            worksheet=new_sheet,
                            column_letter=column_letters.get(field),
                            row_number=sno + 2,
                        )
            if hyperlink_data:
                for field in hyperlink_data.keys():
                    if data.get(hyperlink_data.get(field)):
                        insert_hyperlink(
                            link=data.get(hyperlink_data.get(field)),
                            worksheet=new_sheet,
                            column_letter=column_letters.get(field),
                            row_number=sno + 2,
                        )
            if color_fields:
                add_font_color_to_field(
                    sheet=new_sheet, color_field_column_letter=color_field_column_letter, data=data, sno=sno
                )

            if sheet.get("decimal_number_fields"):
                for field_name in sheet.get("decimal_number_fields"):
                    if field_name in column_letters:
                        if summary_sheet:
                            cell_number = f"{column_letters.get(field_name)}{sno + 3}"
                        else:
                            cell_number = f"{column_letters.get(field_name)}{sno + 2}"
                        new_sheet[cell_number].number_format = "0.00"

        auto_adjust_column_width(worksheet=new_sheet, image_column_letter=image_column_letter)
        format_text(worksheet=new_sheet, image_column_letter=image_column_letter)
        if summary_sheet:
            summary_total_deductions_changes(
                worksheet=new_sheet,
                no_of_rows_inserted=no_of_rows_inserted,
                summary_dedcutions_key=summary_dedcutions_key,
                summary_dedcutions_value=summary_dedcutions_value,
                summary_total_key=summary_total_key,
                total_value=total_value,
            )
        if add_border:
            add_border_to_sheet(worksheet=new_sheet)
        if format_header:
            format_sheet_header(
                column_letters=column_letters,
                sheet=new_sheet,
                with_serial_number=sheet.get("with_serial_number"),
                is_summary_sheet=summary_sheet,
                header_row_number=header_row_number,
            )
        if org_logo_img:
            insert_org_logo_in_sheet(worksheet=new_sheet, org_logo_img=org_logo_img)

    return save_virtual_workbook(wb)


def excel_data_file_with_no_record(sheet_name: str):
    return [{f"{sheet_name} has no data": ""}]


def get_excel_data_validation(
    *, formula: str, error_title: str, error_message: str, prompt_title: str, prompt_message: str
):
    data_validation = DataValidation(type="list", formula1=formula, allow_blank=False, showDropDown=False)
    data_validation.error = error_message
    data_validation.errorTitle = error_title
    data_validation.prompt = prompt_message
    data_validation.promptTitle = prompt_title
    return data_validation
