from enum import Enum
from typing import TypedDict

from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _
from pydantic import GetCoreSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema

from common.helpers import get_parsed_url_list
from rollingbanners import settings


class BaseEnum(Enum):
    @classmethod
    @property
    def reverse(cls):
        return {member.value: member for member in cls.__members__.values()}

    @classmethod
    def to_enum(cls, value):
        return cls.reverse[value]

    @classmethod
    @property
    def choices(cls):
        return [member.value for member in cls.__members__.values()]

    @classmethod
    def __get_pydantic_json_schema__(cls, core_schema, handler) -> JsonSchemaValue:
        field_schema = handler(core_schema)
        field_schema.update(type="string", format="hash-id")
        return field_schema

    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler: GetCoreSchemaHandler) -> core_schema.CoreSchema:
        """
        Defines validation (input conversion) and serialization (output conversion).
        """

        return core_schema.no_info_after_validator_function(
            function=cls._validate,
            schema=core_schema.str_schema(),
            serialization=core_schema.plain_serializer_function_ser_schema(
                function=cls.to_value,
                return_schema=core_schema.str_schema(),
                info_arg=True,
            ),
        )

    @classmethod
    def _validate(cls, value: str):
        return cls.to_enum(value)

    @classmethod
    def to_value(cls, value, info: core_schema.SerializationInfo) -> str:
        return value.value


SYSTEM_USER_ID = 1
SQFT_91_ORG_id = 421

USER_NOT_PERMITTED_ERROR = 4001
FILE_FIELD_MAX_LENGTH = 250


class StandardDecimalConfig(TypedDict):
    max_digits: int
    decimal_places: int


STANDARD_DECIMAL_CONFIG: StandardDecimalConfig = {"max_digits": 10, "decimal_places": 4}


class CustomFieldTypeEnum(BaseEnum):
    TEXT = "text"
    LONG_TEXT = "long_text"
    RICH_TEXT = "rich_text"
    DECIMAL = "decimal"
    DROPDOWN = "dropdown"
    PHONE_NUMBER = "phone_number"
    DATE = "date"
    DATETIME = "datetime"
    FILE = "file"
    MULTIPLE_FILES = "multiple_files"
    EMAIL = "email"
    BOOLEAN = "boolean"
    MULTI_DROPDOWN = "multi_dropdown"
    LEAD_ASSIGNEE = "lead_assignee"
    LEAD_COMPANY = "lead_company"
    LEAD_NAME = "lead_name"
    LEAD_VALUE = "lead_amount"
    LEAD_CONTACT = "lead_contact"
    LEAD_COUNTRY = "lead_country"
    LEAD_STATE = "lead_state"
    LEAD_CITY = "lead_city"
    LEAD_ADDRESS = "lead_address"
    STATE = "state"
    LEAD_ADDRESS_LINE_2 = "lead_address_line_2"
    LEAD_ZIPCODE = "lead_zipcode"
    LEAD_NUMBER = "lead_number"


class CustomFieldTypeChoices(TextChoices):
    TEXT = CustomFieldTypeEnum.TEXT.value, _("Text")
    LONG_TEXT = CustomFieldTypeEnum.LONG_TEXT.value, _("Long Text")
    RICH_TEXT = CustomFieldTypeEnum.RICH_TEXT.value, _("Rich Text")
    DECIMAL = CustomFieldTypeEnum.DECIMAL.value, _("Decimal")
    DROPDOWN = CustomFieldTypeEnum.DROPDOWN.value, _("Dropdown")
    PHONE_NUMBER = CustomFieldTypeEnum.PHONE_NUMBER.value, _("Phone Number")
    DATE = CustomFieldTypeEnum.DATE.value, _("Date")
    DATE_TIME = CustomFieldTypeEnum.DATETIME.value, _("Date Time")
    FILE = CustomFieldTypeEnum.FILE.value, _("File")
    MULTIPLE_FILES = CustomFieldTypeEnum.MULTIPLE_FILES.value, _("Multiple Files")
    EMAIL = CustomFieldTypeEnum.EMAIL.value, _("Email")
    BOOLEAN = CustomFieldTypeEnum.BOOLEAN.value, _("Boolean")
    MULTI_DROPDOWN = CustomFieldTypeEnum.MULTI_DROPDOWN.value, _("Multi Dropdown")
    LEAD_ASSIGNEE = CustomFieldTypeEnum.LEAD_ASSIGNEE.value, _("Lead Assignee")
    LEAD_COMPANY = CustomFieldTypeEnum.LEAD_COMPANY.value, _(" Lead Company")
    LEAD_NAME = CustomFieldTypeEnum.LEAD_NAME.value, _("Lead Name")
    LEAD_VALUE = CustomFieldTypeEnum.LEAD_VALUE.value, _("Lead Value")
    LEAD_CONTACT = CustomFieldTypeEnum.LEAD_CONTACT.value, _("Lead Contact")
    LEAD_COUNTRY = CustomFieldTypeEnum.LEAD_COUNTRY.value, _("Lead Country")
    LEAD_STATE = CustomFieldTypeEnum.LEAD_STATE.value, _("Lead State")
    LEAD_CITY = CustomFieldTypeEnum.LEAD_CITY.value, _("Lead City")
    LEAD_ADDRESS = CustomFieldTypeEnum.LEAD_ADDRESS.value, _("Lead Address")
    STATE = CustomFieldTypeEnum.STATE.value, _("State")
    LEAD_ADDRESS_LINE_2 = CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value, _("Lead Address Line 2")
    LEAD_ZIPCODE = CustomFieldTypeEnum.LEAD_ZIPCODE.value, _("Lead Zipcode")


class BudgetRateDecimalConfig:
    MAX_DIGITS = 15
    DECIMAL_PLACES = 2


class RateDecimalConfig:
    MAX_DIGITS = 15
    DECIMAL_PLACES = 2


class QuantityDecimalConfig:
    MAX_DIGITS = 15
    DECIMAL_PLACES = 4


class FileNameFieldConfig:
    MAX_LENGTH = 250


class TaxPercentageFieldConfig:
    MAX_DIGITS = 5
    DECIMAL_PLACES = 2


class HsnCodeFieldConfig:
    MAX_LENGTH = 8


class ElementNameConfig:
    MAX_LENGTH = 300


class DiscountPercentageFieldConfig:
    MAX_DIGITS = 15
    DECIMAL_PLACES = 12


class StockValueDecimalConfig:
    MAX_DIGITS = 30
    DECIMAL_PLACES = 4


TWO_DECIMAL_FACTOR = 100


"""
    NOTE: When adding new headers, please add them to rollingbanners.settings CORS_ALLOW_HEADERS also
"""


class RequestHeaders(BaseEnum):
    # remove old headers after all apps are updated
    OLD_PLATFORM = "platform"
    OLD_CLIENT_VERSION = "client-version"
    OLD_DEVICE_OS = "device-os"
    OLD_DEVICE_VERSION = "device-version"
    OLD_BYPASS_USER_ID = "bypass-user-id"
    OLD_TIMEZONE = "tz"

    PLATFORM = "rd-platform"
    LATITUDE = "rd-lat"
    LONGITUDE = "rd-long"
    APP_VERSION = "rd-app-version"
    BYPASS_USER_ID = "rd-bypass-user-id"
    TIMEZONE = "rd-tz"
    SDK_VERSION = "rd-sdk-version"
    SESSION_ID = "rd-session-id"
    CLIENT_REQUEST_ID = "rd-client-request-id"
    DEVICE_MODEL = "rd-device-model"
    DEVICE_VERSION = "rd-device-version"
    WORK_PROGRESS_VERSION = "rd-work-progress-version"
    REMOTE_ADDR = "REMOTE_ADDR"
    USER_AGENT = "user-agent"


AWS_PARSED_URL_LIST = get_parsed_url_list(
    url_list=[settings.AWS_S3_CUSTOM_DOMAIN, settings.AWS_CDN_DOMAIN, settings.OLD_IMAGE_KIT_DOMAIN]
)
