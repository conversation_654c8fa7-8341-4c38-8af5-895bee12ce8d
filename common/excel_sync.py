from abc import ABC, abstractmethod
from collections import defaultdict

import gspread
import pandas as pd
import structlog
from django.conf import settings
from django_thread import ThreadPoolExecutor
from gspread import spreadsheet
from gspread_dataframe import get_as_dataframe, set_with_dataframe
from gspread_formatting import (
    BooleanCondition,
    CellFormat,
    Color,
    DataValidationRule,
    TextFormat,
    format_cell_ranges,
    set_column_widths,
    set_data_validation_for_cell_range,
    set_row_height,
    set_row_heights,
)
from openpyxl.utils.cell import get_column_letter
from pandas import DataFrame

from core.selectors import gst_slab_active_fetch
from core.services import check_if_current_org_is_91_sqft

logger = structlog.get_logger(__name__)

gc = gspread.service_account_from_dict(settings.GOOGLE_SERVICE_ACCOUNT_KEY)


class SyncExcelBase(ABC):
    @classmethod
    def excel_access(cls, obj: spreadsheet, is_91_org: bool, is_open_in_sheet: bool = True):
        if is_open_in_sheet:
            if is_91_org:
                obj.share("91sqft.com", perm_type="domain", role="writer")
            else:
                obj.share(None, perm_type="anyone", role="writer")
        else:
            obj.share(None, perm_type="anyone", role="writer")

    @classmethod
    def get_rows_to_lock_in_sheet(cls, generated_df_with_editable: DataFrame, lock_field: str):
        rows_to_lock = []
        for index in generated_df_with_editable.index:
            is_editable = bool(generated_df_with_editable[lock_field][index])
            if is_editable is False:
                rows_to_lock.append(index + 1)
        return rows_to_lock

    @classmethod
    def create_blank_excel(cls, file_name: str, sheets: list[str], org_id: int, is_open_in_sheet: bool = False):
        sh = gc.create(file_name)
        if sheets:
            for sheet in sheets:
                sh.add_worksheet(title=sheet, rows="10000", cols="50")

            sh.del_worksheet(sh.sheet1)
            is_91_org = check_if_current_org_is_91_sqft(org_id=org_id)
            cls.excel_access(obj=sh, is_91_org=is_91_org, is_open_in_sheet=is_open_in_sheet)

        return sh

    @classmethod
    def format_sheet_link_using_id(cls, sheet_id: str):
        return f"https://docs.google.com/spreadsheets/d/{sheet_id}"

    @classmethod
    @abstractmethod
    def sync_excel(cls, *args, **kwargs):
        pass

    @classmethod
    @abstractmethod
    def export_file_name(cls, *args, **kwargs):
        pass

    @classmethod
    def sheet_cell_width(
        cls,
        generated_df: DataFrame,
        to_write_df: DataFrame,
        to_write_df_columns: list,
        idx: int,
        row_height_dict: dict,
        image_fields: list,
    ):
        max_length = 0
        adjusted_width = 0
        for row_idx in range(0, len(generated_df)):
            try:
                cell_value = to_write_df[to_write_df_columns[idx]].iloc[row_idx]

                try:  # Necessary to avoid error on empty cells
                    max_length = max(max_length, len(max(str(cell_value).split("\n"))))
                    max_str = max(cell_value.split("\n"), key=len)
                    max_height_str = len(max_str)
                    if row_height_dict[row_idx] is None:
                        row_height_dict[row_idx] = 0
                    if image_fields and to_write_df_columns[idx] in image_fields and cell_value:
                        row_height_dict[row_idx] = max(row_height_dict[row_idx], 200)
                    else:
                        row_height_dict[row_idx] = max(row_height_dict[row_idx], max_height_str)
                except:  # noqa
                    pass
            except:  # noqa
                pass

        adjusted_width = max_length + 1
        return adjusted_width

    @classmethod
    def get_lock_json(
        cls,
        sheet_instance_id: int,
        start_row_index: int,
        end_row_index: int,
        start_column_index: int,
        end_column_index: int,
    ) -> dict:
        return {
            "addProtectedRange": {
                "protectedRange": {
                    "range": {
                        "sheetId": sheet_instance_id,
                        "startRowIndex": start_row_index,
                        "endRowIndex": end_row_index,
                        "startColumnIndex": start_column_index,
                        "endColumnIndex": end_column_index,
                    },
                    "description": "Protecting total row",
                    "warningOnly": False,
                    "editors": {
                        "domainUsersCanEdit": False,
                    },
                },
            },
        }

    @classmethod
    def merge_excel_data(
        cls,
        sheet_instance: spreadsheet,
        google_sheet_df: DataFrame,
        generated_df: DataFrame,
        pk_column: str,
        sheet_obj,
        image_fields: list[str],
        highlight_column: list[str] = [],
        editable_columns: dict[str] = {},
        to_lock_extra_rows: bool = True,
        rows_to_lock: list[int] = [],
    ):
        to_write_df = generated_df
        lock_column_start_point, lock_row_start_point = (0, 0)
        lock_column_end_point = len(generated_df.columns)
        lock_row_end_point = len(generated_df) + 7 if to_lock_extra_rows else len(generated_df) + 1

        if (
            pk_column and pk_column in google_sheet_df.columns and pk_column in generated_df.columns
        ):  # when pk column is preset
            column_list_to_remove = list(generated_df.columns)  # removing duplicate column from sheet df
            column_list_to_remove.remove(pk_column)

            for col in list(
                generated_df.columns
            ):  # if any new column is created if generated df but not present in sheet
                if col not in google_sheet_df.columns:
                    column_list_to_remove.remove(col)

            google_sheet_df = google_sheet_df.drop(columns=column_list_to_remove).astype(str)

            generated_df = generated_df.astype(str)  # converting all fields to string

            _merged_df = generated_df.merge(google_sheet_df, how="left", on=[pk_column]).astype(str)

            reassign_column_values = [column for column in google_sheet_df.columns]

            _merged_df = generated_df.merge(google_sheet_df[reassign_column_values], how="left", on=[pk_column]).astype(
                str
            )

            _merged_df = _merged_df.replace("nan", " ")  # converting all nan and None value to blank
            _merged_df = _merged_df.replace("None", " ")

            to_write_df = _merged_df

        initial_json = cls.initial_request_data(
            sheet_obj=sheet_obj,
            sheet_instance=sheet_instance,
            lock_row_start_point=lock_row_start_point,
            lock_row_end_point=lock_row_end_point,
            lock_column_start_point=lock_column_start_point,
            lock_column_end_point=lock_column_end_point,
        )

        if sheet_instance.row_count < len(to_write_df.values):
            increase_row = {
                "insertDimension": {
                    "range": {
                        "sheetId": sheet_instance.id,
                        "dimension": "ROWS",
                        "startIndex": sheet_instance.row_count,
                        "endIndex": sheet_instance.row_count + 10000,  # Add 10000 new rows
                    }
                }
            }
            initial_json.insert(0, increase_row)

        body = {"requests": initial_json}

        to_write_df.rename(
            columns={column: None for column in to_write_df.columns if isinstance(column, str) and "Unnamed" in column},
            inplace=True,
        )

        lock_fmt = CellFormat(backgroundColor=Color(20, 20, 20))

        unlock_fmt = CellFormat(backgroundColor=Color().fromHex("#ffffff"))

        cell_fmt = CellFormat(horizontalAlignment="LEFT", verticalAlignment="TOP", wrapStrategy="WRAP")

        column_bold_format = CellFormat(textFormat=TextFormat(bold=True))
        special_col_format = CellFormat(backgroundColor=Color.fromHex("#C27BA0"))

        cell_width = []
        row_height_dict = defaultdict(int)
        format_cell_range_list = []
        to_write_df_columns = [col for col in to_write_df.columns]

        int_validation = DataValidationRule(
            BooleanCondition("NUMBER_GREATER_THAN_EQ", ["0"]),
            strict=True,
            inputMessage="Please enter a number greater than 0",
        )
        tax_percent_validation = DataValidationRule(
            BooleanCondition(
                "ONE_OF_LIST", [str(int(decimal_percent)) for decimal_percent in list(gst_slab_active_fetch())]
            ),
            strict=True,
            inputMessage="Please enter valid tax percentage",
            # showCustomUi=True,
        )
        low = high = 0
        for idx in range(1, lock_column_end_point + 1):  # column wise
            high += 1
            col_letter = get_column_letter(idx)

            if to_write_df_columns[idx - 1] in editable_columns:
                json = cls.get_lock_json(
                    sheet_instance_id=sheet_instance.id,
                    start_row_index=lock_row_start_point + 1,
                    end_row_index=lock_row_end_point,
                    start_column_index=low,
                    end_column_index=high - 1,
                )

                low = high
                body["requests"].append(json)
                format_cell_range_list.append((f"{col_letter}1:{col_letter}{lock_row_end_point}", unlock_fmt))
                if editable_columns[to_write_df_columns[idx - 1]].get("type"):
                    if editable_columns[to_write_df_columns[idx - 1]].get("type") == "int":
                        set_data_validation_for_cell_range(
                            sheet_instance, f"{col_letter}2:{col_letter}{lock_row_end_point}", int_validation
                        )
                    if editable_columns[to_write_df_columns[idx - 1]].get("type") == "tax_percent":
                        set_data_validation_for_cell_range(
                            sheet_instance, f"{col_letter}2:{col_letter}{lock_row_end_point}", tax_percent_validation
                        )
            else:
                format_cell_range_list.append((f"{col_letter}1:{col_letter}{lock_row_end_point}", lock_fmt))

            format_cell_range_list.append((f"{col_letter}1:{col_letter}{lock_row_end_point}", cell_fmt))

            format_cell_range_list.append((f"{col_letter}1:{col_letter}{1}", column_bold_format))

            if idx > len(to_write_df_columns):
                continue
            if to_write_df_columns[idx - 1] in image_fields:
                cell_width.append((col_letter, 200))
                continue
            adjusted_width = cls.sheet_cell_width(
                generated_df=generated_df,
                to_write_df=to_write_df,
                to_write_df_columns=to_write_df_columns,
                idx=idx - 1,
                row_height_dict=row_height_dict,
                image_fields=image_fields,
            )

            cell_width.append((col_letter, 3 * adjusted_width + 55))

            if to_write_df_columns[idx - 1] in highlight_column:
                format_cell_range_list.append((f"{col_letter}:{col_letter}{lock_row_end_point}", special_col_format))
        if low < high:
            json = cls.get_lock_json(
                sheet_instance_id=sheet_instance.id,
                start_row_index=lock_row_start_point + 1,
                end_row_index=lock_row_end_point,
                start_column_index=low,
                end_column_index=high,
            )
            body["requests"].append(json)

        for row in rows_to_lock:
            json = cls.get_lock_json(
                sheet_instance_id=sheet_instance.id,
                start_row_index=row,
                end_row_index=row + 1,
                start_column_index=lock_column_start_point,
                end_column_index=lock_column_end_point,
            )
            col_letter = get_column_letter(lock_column_end_point)
            format_cell_range_list.append((f"A{row+1}:{col_letter}{row+1}", lock_fmt))
            body["requests"].append(json)

        sheet_obj.batch_update(body)
        set_with_dataframe(sheet_instance, to_write_df)

        row_height = []
        for idx, value in row_height_dict.items():
            if value >= 200:
                row_height.append((f"{idx + 1 + 1}", 200))
            elif value >= 75:
                row_height.append((f"{idx + 1 + 1}", 70))

        if row_height:
            set_row_heights(sheet_instance, row_height)

        cell_width.append(("A", 25))  # Setting 1st column width
        format_cell_ranges(sheet_instance, format_cell_range_list)
        set_column_widths(sheet_instance, cell_width)
        set_row_height(sheet_instance, f"{1}", 40)  # for column height

    @classmethod
    def merge_excel_without_editable_columns(
        cls,
        sheet_instance: spreadsheet,
        google_sheet_df: DataFrame,
        generated_df: DataFrame,
        pk_column: str,
        sheet_obj,
        image_fields: list[str],
        to_lock_extra_rows: bool = True,
    ):
        executor = ThreadPoolExecutor(max_workers=5)
        to_write_df = generated_df
        lock_row_start_point = 0
        lock_column_end_point = len(generated_df.columns)
        lock_row_end_point = len(generated_df) + 1 if to_lock_extra_rows else len(generated_df) + 1

        protected_range_list = [
            {"unmergeCells": {"range": {"sheetId": sheet_instance.id}}},
            {"updateCells": {"range": {"sheetId": sheet_instance._properties["sheetId"]}, "fields": "*"}},
        ]

        logger.info(
            "protected range list initialized",
        )

        for protection_range in sheet_obj.list_protected_ranges(sheetid=sheet_instance.id):
            obj = {
                "deleteProtectedRange": {
                    "protectedRangeId": protection_range.get("protectedRangeId"),
                }
            }
            protected_range_list.append(obj)

        if (
            pk_column and pk_column in google_sheet_df.columns and pk_column in generated_df.columns
        ):  # when pk column is preset
            column_list_to_remove = list(generated_df.columns)  # removing duplicate column from sheet df
            column_list_to_remove.remove(pk_column)
            for col in column_list_to_remove.copy():
                if col not in google_sheet_df.columns:
                    column_list_to_remove.remove(col)
            google_sheet_df = google_sheet_df.drop(columns=column_list_to_remove).astype(str)

            generated_df = generated_df.astype(str)  # converting all fields to string

            _merged_df = generated_df.merge(google_sheet_df, how="left", on=[pk_column]).astype(str)

            reassign_column_values = [column for column in google_sheet_df.columns]

            _merged_df = generated_df.merge(google_sheet_df[reassign_column_values], how="left", on=[pk_column]).astype(
                str
            )

            _merged_df = _merged_df.replace("nan", " ")  # converting all nan and None value to blank
            _merged_df = _merged_df.replace("None", " ")

            to_write_df = _merged_df

        if sheet_instance.row_count < len(to_write_df.values):
            increase_row = {
                "insertDimension": {
                    "range": {
                        "sheetId": sheet_instance.id,
                        "dimension": "ROWS",
                        "startIndex": sheet_instance.row_count,
                        "endIndex": sheet_instance.row_count + 10000,  # Add 10000 new rows
                    },
                    "inheritFromBefore": True,  # Inherit properties from the row before
                }
            }
            protected_range_list.insert(0, increase_row)

        to_write_df.rename(
            columns={column: None for column in to_write_df.columns if isinstance(column, str) and "Unnamed" in column},
            inplace=True,
        )

        lock_fmt = CellFormat(backgroundColor=Color(20, 20, 20))

        cell_fmt = CellFormat(horizontalAlignment="LEFT", verticalAlignment="TOP", wrapStrategy="WRAP")

        cell_width = []
        row_height_dict = defaultdict(int)
        format_cell_range_list = []
        to_write_df_columns = [col for col in to_write_df.columns]

        for idx in range(1, lock_column_end_point + 1):  # column wise
            col_letter = get_column_letter(idx)

            if idx > len(to_write_df_columns):
                continue
            if to_write_df_columns[idx - 1] in image_fields:
                cell_width.append((col_letter, 200))
                continue
            adjusted_width = cls.sheet_cell_width(
                generated_df=generated_df,
                to_write_df=to_write_df,
                to_write_df_columns=to_write_df_columns,
                idx=idx - 1,
                row_height_dict=row_height_dict,
                image_fields=image_fields,
            )

            cell_width.append((col_letter, 3 * adjusted_width + 60))

        protected_range_list.append(
            cls.get_lock_json(
                sheet_instance_id=sheet_instance.id,
                start_row_index=lock_row_start_point,
                end_row_index=lock_row_end_point,
                start_column_index=0,
                end_column_index=2,
            )
        )

        format_cell_range_list.append((f"A{1}:{get_column_letter(lock_column_end_point)}{1}", lock_fmt))
        row_height = []
        for idx, value in row_height_dict.items():
            if value >= 200:
                row_height.append((f"{idx + 1 + 1}", 200))
            elif value >= 75:
                row_height.append((f"{idx + 1 + 1}", 70))

        logger.info(
            "batch_update_thread stated",
        )
        batch_update_thread = executor.submit(sheet_obj.batch_update, {"requests": protected_range_list})
        batch_update_thread.result()
        logger.info(
            "batch_update_thread completed",
        )
        if row_height:
            set_row_heights(sheet_instance, row_height)

        format_cell_range_list.append((f"A1:{get_column_letter(lock_column_end_point)}{lock_row_end_point}", lock_fmt))
        cell_width.append(("A", 25))  # Setting 1st column width
        format_cell_range_list.append(
            (f"A{1}:{get_column_letter(lock_column_end_point)}{lock_row_end_point}", cell_fmt)
        )
        logger.info(
            "threads execution started",
        )

        format_cell_thread = executor.submit(format_cell_ranges, sheet_instance, format_cell_range_list)
        column_width_thread = executor.submit(set_column_widths, sheet_instance, cell_width)
        row_height_thread = executor.submit(set_row_height, sheet_instance, f"{1}", 40)  # for column height
        set_with_dataframe_thread = executor.submit(set_with_dataframe, sheet_instance, to_write_df)
        column_width_thread.result()
        format_cell_thread.result()
        row_height_thread.result()

        set_with_dataframe_thread.result()

        logger.info(
            "all threads execution completed",
        )

    @classmethod
    def create_generated_df(cls, generated_excel_data: list, sheet_name: str):
        sheet_data = None
        column_names = []
        image_fields = []
        with_serial_number = True

        for _sheet in generated_excel_data:
            if _sheet.get("sheet_title") == sheet_name:
                sheet_data = _sheet.get("sheet_data")
                image_fields = _sheet.get("image-fields", [])
                with_serial_number = _sheet.get("with_serial_number", True)

                break

        if with_serial_number:
            column_names.append("Sl No.")
        column_names.extend(
            [
                (" ".join(key.split("_")).title() if len(key.split("_")) > 1 else key.title())
                for key in sheet_data[0].keys()
            ]
        )

        df_data_list = []
        for sno, data in enumerate(sheet_data):
            record = []
            if with_serial_number:
                record.append(sno + 1)
            for key, value in data.items():
                if key in image_fields:
                    if value:
                        record.append(f'=IMAGE("{value}",4,200,200)')
                    else:
                        record.append("")
                else:
                    record.append(value)
            df_data_list.append(record)

        generated_df = pd.DataFrame(df_data_list, columns=column_names).iloc[:, : cls.df_col_length]

        return generated_df

    @classmethod
    def load_dataframes_to_update(cls, excel_sheet_url: str, excel_meta: dict) -> list[DataFrame]:
        sheet_object = gc.open_by_url(excel_sheet_url)
        dataframes_to_update = {}

        for sheet_name, meta_details in cls.sheets.items():
            pk_column = meta_details.get("pk_column")
            required_columns = [col for col in meta_details.get("editable_columns", [])] + [pk_column]
            sheet_instance = sheet_object.get_worksheet_by_id(excel_meta.get(sheet_name))
            google_sheet_df = get_as_dataframe(sheet_instance).iloc[:, : cls.df_col_length]

            if (pk_column is None) or bool(required_columns) is False or pk_column not in google_sheet_df.columns:
                continue

            google_sheet_df = google_sheet_df.dropna(subset=[pk_column])

            dataframes_to_update[sheet_name] = google_sheet_df[required_columns]

        return dataframes_to_update

    @classmethod
    def create_sheets(cls, sheet_name: str, org_from_id: int):
        filename = cls.export_file_name(name=sheet_name)
        sheet = cls.create_blank_excel(
            file_name=filename,
            sheets=[sheet for sheet in cls.sheets],
            org_id=org_from_id,
            is_open_in_sheet=False,
        )
        sheet_dict = {}
        for _sheet in sheet.worksheets():
            sheet_dict[_sheet.title] = _sheet.id

        _excel_sheet = cls.format_sheet_link_using_id(sheet_id=sheet.id)
        return _excel_sheet, sheet_dict

    @classmethod
    def initial_request_data(
        cls,
        sheet_obj,
        sheet_instance,
        lock_row_start_point,
        lock_row_end_point,
        lock_column_start_point,
        lock_column_end_point,
    ):
        json = [{"unmergeCells": {"range": {"sheetId": sheet_instance.id}}}]

        for protection_range in sheet_obj.list_protected_ranges(sheetid=sheet_instance.id):
            obj = {
                "deleteProtectedRange": {
                    "protectedRangeId": protection_range.get("protectedRangeId"),
                }
            }
            json.append(obj)
        lock_json = cls.get_lock_json(  # locking header row
            sheet_instance_id=sheet_instance.id,
            start_row_index=lock_row_start_point,
            end_row_index=lock_row_start_point + 1,
            start_column_index=lock_column_start_point,
            end_column_index=lock_column_end_point,
        )
        json.append(lock_json)

        json.append(
            {"updateCells": {"range": {"sheetId": sheet_instance.id}, "fields": "userEnteredValue"}}
        )  # clearing the  sheet

        return json
