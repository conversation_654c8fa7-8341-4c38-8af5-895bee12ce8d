from typing import Optional, Union

import structlog
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import models
from django.db.models.fields.related import ForeignKey
from django.utils import timezone
from django.utils.functional import classproperty
from packaging import version

from common.choices import SourceChoices
from common.exceptions import FieldHistoryException, SoftDeleteException
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


class ResponseMessageMixin:
    _response_message = None

    def __init__(self, *args, **kwargs):
        self._response_message = None

    def set_response_message(self, message):
        self._response_message = message

    def get_response_message(self):
        return self._response_message


class FieldHistoryMixin:
    DATETIME = "datetime"
    DECIMAL = "decimal"
    FOREIGN_KEY = "foreign_key"

    @classmethod
    def get_field_type(cls, field):
        field_mapping = {
            models.DateTimeField: cls.DATETIME,
            models.DecimalField: cls.DECIMAL,
            models.ForeignKey: cls.FOREIGN_KEY,
        }
        if not hasattr(cls, field):
            raise FieldHistoryException(f"Field {field} not found.")
        field_class = type(cls._meta.get_field(field))
        field_type = field_mapping.get(field_class, None)
        if field_type is None:
            raise FieldHistoryException(f"Field history for {field_class} not implemented.")
        return field_type

    @staticmethod
    def to_field_history_datetime(datetime_obj):
        return datetime_obj.isoformat() if datetime_obj is not None else None

    def to_field_history(self, field):
        field_type = self.get_field_type(field)
        if field_type == self.DATETIME:
            return self.to_field_history_datetime(getattr(self, field))
        if field_type == self.DECIMAL:
            return str(getattr(self, field))
        if field_type == self.FOREIGN_KEY:
            return str(getattr(self, field))

    @classmethod
    def from_field_history(cls, field, value):
        field_type = cls.get_field_type(field)
        if field_type == cls.DATETIME:
            return timezone.datetime.fromisoformat(value) if value is not None else None
        if field_type == cls.DECIMAL:
            return value if value is not None else None
        if field_type == cls.FOREIGN_KEY:
            return value if value is not None else None


# TODO: use BaseModelMixin instead of HashidsModelMixin
class HashidsModelMixin:
    @property
    def hashid(self):
        return HashIdConverter.encode(self.pk)

    @property
    def hash_id(self):
        return self.hashid


class TableNameMixin:
    @classproperty
    def table_name(self):
        return self._meta.db_table


class SoftDeleteMixin:
    def soft_delete(self, user_id: int, save: bool = True) -> Union[None, bool]:
        if not hasattr(self, "deleted_at") or not hasattr(self, "deleted_by"):
            raise SoftDeleteException("deleted_at or deleted_by field missing.")
        if self.deleted_at is not None:
            # no need to delete again
            return False
        self.deleted_at = timezone.now()
        self.deleted_by_id = user_id
        if save:
            self.save(update_fields=["deleted_at", "deleted_by_id"])
        return None


class BaseModelMixin:
    @property
    def hash_id(self):
        return HashIdConverter.encode(self.pk)

    @classmethod
    def get_object_using_hash_id(cls, hash_id: str):
        return cls.objects.get(pk=HashIdConverter.decode(hash_id))

    def get_fields(self) -> list[str]:
        return [
            field.name if type(field) is not ForeignKey else f"{field.name}_id" for field in self._meta.get_fields()
        ]


class NameModelMixin:
    def __str__(self):
        return self.name


class AppVersionMixin:
    minimum_required_app_version: Optional[str] = None

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        if self.get_source() in [
            SourceChoices.APP.value,
            SourceChoices.ANDROID.value,
            SourceChoices.IOS.value,
        ]:
            self.check_app_version()

    def get_minimum_required_app_version(self) -> Optional[str]:
        return self.minimum_required_app_version

    def check_app_version(self):
        app_version = self.get_app_version()
        minimum_required_app_version = self.get_minimum_required_app_version()
        if (
            minimum_required_app_version
            and app_version
            and version.parse(app_version) < version.parse(minimum_required_app_version)
        ):
            self.set_response_message("Please update the app to the latest version to use this feature")
            raise DjangoValidationError("Please update the app to the latest version to use this feature")


class ExternalApiMixin:
    REQUIRED_METHOD_PERMISSIONS = None

    def get_permission_list(self) -> list:
        if self.request and self.request.method in self.REQUIRED_METHOD_PERMISSIONS:
            return self.REQUIRED_METHOD_PERMISSIONS[self.request.method]

        logger.warning(
            "Permissions not provided for request method in REQUIRED_METHOD_PERMISSIONS.",
            extra={
                "required_method_permissions": self.REQUIRED_METHOD_PERMISSIONS,
                "request_method": self.request.method,
            },
        )
        return []
