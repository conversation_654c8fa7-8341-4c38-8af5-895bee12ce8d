from typing import Any, Optional

import pytz
import structlog
from django.conf import settings
from django.utils import timezone
from django.utils.module_loading import import_string
from django.views.decorators.csrf import csrf_exempt
from rest_framework.exceptions import ValidationError
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from wkhtmltopdf.views import PDFTemplateView

from authorization.domain.constants import Permissions
from common.choices import SourceChoices
from common.constants import RequestHeaders
from common.events import BaseEventData, Events
from common.events.base import BaseEventHandler
from common.exceptions import SerializerMissingError
from common.mixins import ResponseMessageMixin
from common.pydantic.mixins import BaseApiPydanticMixin
from common.pydantic.parse import BasePydanticModelSerializer
from common.serializers import (
    BaseKeyProtectedDataClassSerializer,
    BaseKeyProtectedSerializer,
    BaseSerializer,
)
from core.caches import OrganizationCountryConfigCache
from core.entities import OrgUserEntity
from core.helpers import OrgPermissionHelper
from core.models import Organization, User
from core.selectors import organization_get
from project.data.models import Project
from rollingbanners.filter_backends import CustomFilterBackend
from rollingbanners.hash_id_converter import HashIdConverter

event_handler: BaseEventHandler = import_string(settings.EVENT_HANDLER)()
logger = structlog.get_logger(__name__)


class UserRequest(Request):
    user: User


class BaseGenericApiView(GenericAPIView):
    request: UserRequest

    def convert_args(self, request, *args, **kwargs):
        return (args, kwargs)

    def add_response_headers(self, response):
        return response

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        """
        Identical to rest framework's dispatch except we add the ability
        to convert arguments (for common URL params).
        """

        self.args = args
        self.kwargs = kwargs
        request = self.initialize_request(request, *args, **kwargs)
        self.request = request
        self.headers = self.default_response_headers  # deprecate?

        try:
            self.initial(request, *args, **kwargs)

            # Get the appropriate handler method
            method = request.method.lower()
            if method in self.http_method_names and hasattr(self, method):
                handler = getattr(self, method)

                # Only convert args when using defined handlers
                (args, kwargs) = self.convert_args(request, *args, **kwargs)
                self.args = args
                self.kwargs = kwargs
            else:
                handler = self.http_method_not_allowed

            response = handler(request, *args, **kwargs)

        except Exception as exc:
            response = self.handle_exception(exc)

        self.response = self.finalize_response(request, response, *args, **kwargs)
        self.response = self.add_response_headers(self.response)

        return self.response


class RequestHeaderMixin:
    def get_request_headers(self):
        return {
            key.replace("HTTP_", "").replace("-", "_").lower(): value
            for key, value in self.request.META.items()
            if key.startswith("HTTP_")
        }

    def get_request_header(self, name: RequestHeaders, default=None):
        header_name = f"HTTP_{name.value.replace('-', '_').upper()}"
        return self.request.META.get(header_name, default)

    def get_platform(self):
        platform = self.get_request_header(RequestHeaders.PLATFORM) or self.get_request_header(
            RequestHeaders.OLD_PLATFORM, SourceChoices.CUSTOM.value
        )
        if platform == SourceChoices.APP.value:
            logger.error("App platform is not supported.")
        return platform

    def get_latitude(self):
        return (
            float(self.get_request_header(RequestHeaders.LATITUDE))
            if self.get_request_header(RequestHeaders.LATITUDE)
            else None
        )

    def get_longitude(self):
        return (
            float(self.get_request_header(RequestHeaders.LONGITUDE))
            if self.get_request_header(RequestHeaders.LONGITUDE)
            else None
        )

    def get_app_version(self):
        return self.get_request_header(RequestHeaders.APP_VERSION)

    def get_ip_address(self):
        return self.request.META.get(RequestHeaders.REMOTE_ADDR.value)

    def get_bypass_user_id(self) -> Optional[int]:
        bypass_user_id = self.get_request_header(RequestHeaders.BYPASS_USER_ID) or self.get_request_header(
            RequestHeaders.OLD_BYPASS_USER_ID
        )
        if bypass_user_id:
            return HashIdConverter.decode(bypass_user_id)
        return None

    def get_work_progress_version(self):
        version = self.get_request_header(RequestHeaders.WORK_PROGRESS_VERSION)

        try:
            if version:
                return int(version)
            return None
        except ValueError:
            return None

    def is_source_app(self) -> bool:
        return self.get_platform() in [
            SourceChoices.APP.value,
            SourceChoices.ANDROID.value,
            SourceChoices.IOS.value,
        ]


class BaseApi(BaseGenericApiView, ResponseMessageMixin, RequestHeaderMixin, BaseApiPydanticMixin):
    filter_data = "query_params"
    filter_serializer_class = None
    filter_backends = [CustomFilterBackend]
    input_serializer_class = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.validated_data = None
        self.is_system_user = None

    def check_if_system_user(self):
        if hasattr(self.request, "user") and hasattr(self.request.user, "token_data"):
            return self.request.user.token_data.is_admin

    def validate_data(self, serializer_class, data, context={}):
        serializer = serializer_class(data=data, context=context)
        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as e:
            if hasattr(serializer, "get_response_message") and serializer.get_response_message():
                self.set_response_message(serializer.get_response_message())
            raise e
        if (hasattr(serializer_class, "Meta") and hasattr(serializer_class.Meta, "dataclass")) or issubclass(
            type(serializer), BasePydanticModelSerializer
        ):
            return serializer.save()
        return serializer.validated_data

    def get_filter_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_organization_timezone()
        return context

    def get_validated_data(self):
        if not self.validated_data:
            self.validate_input_data()
        return self.validated_data

    def validate_filter_data(self, copy=True) -> Any:
        if not self.filter_serializer_class:
            if hasattr(self, "FilterSerializer") and (issubclass(self.FilterSerializer, BaseSerializer)):
                self.filter_serializer_class = self.FilterSerializer
            else:
                raise SerializerMissingError("Filter serializer missing.")
        if copy:
            data = self.request.query_params.copy() if self.filter_data == "query_params" else self.request.data.copy()
        else:
            data = self.request.query_params if self.filter_data == "query_params" else self.request.data

        return self.validate_data(self.filter_serializer_class, data, context=self.get_filter_context())

    def validate_input_data(self, copy=True) -> Any:
        if not self.input_serializer_class:
            if hasattr(self, "InputSerializer") and issubclass(self.InputSerializer, BaseSerializer):
                self.input_serializer_class = self.InputSerializer
            else:
                raise SerializerMissingError("Input serializer missing.")
        if copy:
            data = self.request.data.copy()
        else:
            data = self.request.data

        self.validated_data = self.validate_data(self.input_serializer_class, data, context=self.get_input_context())
        return self.validated_data

    def get_paginated_data(self):
        is_paginated = False
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            is_paginated = True
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data), is_paginated
        serializer = self.get_serializer(queryset, many=True)
        return serializer.data, is_paginated

    def get_organization_id(self) -> int:
        if not hasattr(self.request.user, "token_data"):
            raise Exception("User instance is not annotated with token_data.")
        return self.request.user.token_data.org_id

    def get_organization(self) -> Optional[Organization]:
        org_id = self.get_organization_id()
        if org_id is None:
            return None
        return organization_get(org_id=org_id)

    def get_org_and_project_for_org_separation(self, org_id: Optional[int] = None, project_id: Optional[int] = None):
        if project_id:
            try:
                project = Project.objects.select_related("organization").get(id=project_id)
                return project.organization, project
            except Project.DoesNotExist:
                raise ValidationError("Invalid project id")

        if org_id:
            return self.get_organization(), None

        return None, None

    def get_organization_country_id(self) -> Optional[int]:
        org = self.get_organization()
        if org is None:
            return None
        return org.country_id

    def is_testing_user(self) -> bool:
        return Permissions.FOR_TESTING.value in OrgPermissionHelper.get_permissions(user=self.request.user)

    def get_user_id(self) -> int:
        return self.request.user.id

    def get_user(self):
        self.request.user.refresh_from_db()
        return self.request.user

    def get_token_data(self):
        return self.request.user.token_data if hasattr(self.request.user, "token_data") else None

    @staticmethod
    def trigger_event(event: Events, event_data: BaseEventData):
        event_handler.process_event(event=event, event_data=event_data)

    def get_source(self):
        return self.get_platform()

    def get_input_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_organization_timezone()
        return context

    def get_output_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_organization_timezone()
        return context

    def user_is_admin(self):
        return self.request.user.token_data.is_admin

    def set_org_timezone(self):
        self.set_timezone(self.get_organization_timezone())

    def get_user_timezone(self):
        return self.get_organization_timezone()

    def get_organization_timezone(self) -> pytz.BaseTzInfo:
        if not hasattr(self.request, "_cached_org_timezone"):
            if self.get_organization_id():
                self.request._cached_org_timezone = pytz.timezone(
                    OrganizationCountryConfigCache.get(instance_id=self.get_organization_id()).timezone.name
                )
            else:
                self.request._cached_org_timezone = pytz.timezone("Asia/kolkata")

        return self.request._cached_org_timezone

    def set_timezone(self, pytz_timezone: pytz.BaseTzInfo):
        timezone.activate(pytz_timezone)

    def get_org_user_entity(self) -> OrgUserEntity:
        return OrgUserEntity(
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )


class BaseOpenApi(BaseApi):
    permission_classes = (AllowAny,)

    def get_input_context(self):
        return self.get_serializer_context()


class BaseKeyProtectedApi(BaseOpenApi):
    def validate_input_data(self, copy=True):
        if hasattr(self, "InputSerializer") and (
            issubclass(self.InputSerializer, BaseKeyProtectedSerializer)
            or issubclass(self.InputSerializer, BaseKeyProtectedDataClassSerializer)
        ):
            self.input_serializer_class = self.InputSerializer
        else:
            raise SerializerMissingError("Wrong serializer...!")

        return super().validate_input_data(copy=copy)


class PdfGenerateApi(BaseApi, PDFTemplateView):
    cmd_options = {
        "page-width": 210,
        # "page-size": "A4",
        "margin-top": 16,
        # "margin-top": 0,
        "margin-left": 0,
        "margin-right": 0,
        # "disable-smart-shrinking": True,
        "margin-bottom": 15,
        # "margin-left": 100,
        "quiet": None,
        "enable-local-file-access": "",
        "orientation": "portrait",
        # "viewport-size": "595x840",
        # --footer - center[page] / [topage]
    }
    context = None
    show_content_in_browser = False

    def get_context_data(self, **kwargs):
        # context = super(PdfGenerateApi, self).get_context_data(**kwargs)

        # context = self.context
        # return context
        return self.context

    # def get(self, request, *args, **kwargs):
    # return super(PdfGenerateApi, self).get(request, *args, **kwargs)
