import decimal
import re
import time
import uuid
from collections import Counter, OrderedDict
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decima<PERSON>
from functools import lru_cache
from typing import Dict, List

import pytz
import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import F, QuerySet
from django.urls import ResolverMatch
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from packaging import version
from rest_framework.settings import api_settings

from common.choices import SourceChoices
from common.constants import RequestHeaders
from common.exceptions import (
    HashIdException,
    MultipleTrueValueError,
    NoTrueValueError,
    QuantityDimensionsMismatchError,
    UOMConversionError,
    UOMValidationError,
)
from element.domain.utils import UOM_2D_TO_1D_MAPPING, uom_conversion_service
from rollingbanners.authentication import RequestMetaData, TokenData
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


def sort_by_key(*, data: List[Dict], key: str):
    data.sort(key=lambda x: x.get(key))
    return data


def filter_dict_using_keys(data: dict, keys: list) -> dict:
    return {key: value for key, value in data.items() if key in keys}


def get_id_list_from_obj_list(data: list):
    return [obj.get("id") for obj in data]


# TODO: Make Static padding for order elements and others
def padding_for_serial_number(serial_number: int, padding: int = 4) -> str:
    return str(serial_number).zfill(padding)


def ordering_queryset(
    ordering_fields: str,
    allowed_fields_mapping: dict,
    queryset: QuerySet,
):
    if not ordering_fields:
        return queryset
    order_list = []
    for field in ordering_fields.split(","):
        if field.lstrip("-") not in allowed_fields_mapping:
            continue
        order_list.append(field.replace(field.lstrip("-"), allowed_fields_mapping[field.lstrip("-")]))
    if order_list:
        return queryset.order_by(*order_list)
    return queryset


def order_queryset_by_null_last(queryset, ordering, ordering_fields):
    ordering = ordering.strip() if ordering else None
    if ordering and ordering.strip("-") in ordering_fields:
        if len(ordering.split(",")) > 1:
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Only One Ordering Field allowed")})

        if "-" in ordering:
            return queryset.order_by(F(ordering.strip("-")).desc(nulls_last=True))
        else:
            return queryset.order_by(F(ordering).asc(nulls_last=True))

    return queryset


def formatINR(number):
    s, *d = str(number).partition(".")
    r = ",".join([s[x - 2 : x] for x in range(-3, -len(s), -2)][::-1] + [s[-3:]])
    return "".join([r] + d)


def check_if_only_one_truth_value_in_list(flag_list):
    counter_list = Counter(flag_list)
    if counter_list.get(True):
        if counter_list.get(True) > 1:
            raise MultipleTrueValueError("Many True Values")
        return True
    else:
        raise NoTrueValueError("No True Value")


def get_id_list_from_string(value: str):
    if value:
        result = []
        errors = OrderedDict()
        for idx, item in enumerate(value.strip().split(",")):
            try:
                try:
                    result.append(HashIdConverter.decode(item))

                except HashIdException:
                    raise ValidationError(f"{item} is not a valid hashid.")
            except ValidationError as e:
                errors[idx] = e.detail

        if errors:
            raise ValidationError(errors)

        if result:
            return result


def get_uuid_list_from_string(value: str):
    if value:
        result = []
        for idx, item in enumerate(value.strip().split(",")):
            result.append(item)

        if result:
            return result


def get_today_starting_datetime():
    today_date_time = timezone.now()
    return today_date_time.replace(hour=0, minute=0, second=0, microsecond=0)


def format_date_from_string(date: str) -> str:
    date = datetime.strptime(date, "%Y-%m-%d")
    month_abbr = date.strftime("%b")
    day_abbr = date.strftime("%d")
    year_abbr = date.strftime("%Y")
    date_form = f"{day_abbr} {month_abbr} {year_abbr}"

    return date_form


def date_range(start_date, end_date):
    for n in range(int((end_date - start_date).days) + 1):
        yield start_date + timedelta(n)


def validate_quantity_dimensions(quantity_dimensions: dict, quantity: decimal.Decimal, quantity_uom: int) -> bool:
    if not quantity_dimensions:
        return True
    length = quantity_dimensions.get("length")
    breadth = quantity_dimensions.get("breadth")
    length_uom = quantity_dimensions.get("length_uom")
    breadth_uom = quantity_dimensions.get("breadth_uom")

    # Ensure all required fields are present
    if (
        length is None
        or breadth is None
        or length_uom is None
        or breadth_uom is None
        or quantity is None
        or quantity_uom is None
    ):
        raise QuantityDimensionsMismatchError(
            message="All dimensions and units are required",
        )

    try:
        to_uom = UOM_2D_TO_1D_MAPPING[quantity_uom]
    except KeyError:
        raise UOMValidationError("Wrong UOM provided for Quantity")

    try:
        length_to_uom = round(uom_conversion_service(from_uom=length_uom, to_uom=to_uom, value=length), 4)
        breadth_to_uom = round(uom_conversion_service(from_uom=breadth_uom, to_uom=to_uom, value=breadth), 4)
    except KeyError:
        raise UOMConversionError("Error converting units")

    # Calculate quantity
    calculated_quantity = round(decimal.Decimal(length_to_uom * breadth_to_uom), 4)

    # Convert provided quantity to the specified unit
    provided_quantity_in_uom = round(decimal.Decimal(quantity), 4)

    # Compare calculated area with provided area
    if calculated_quantity != provided_quantity_in_uom:
        raise QuantityDimensionsMismatchError("Calculated quantity does not match provided quantity.")

    return True


def is_valid_uuid(uuid_string):
    """
    Function to check if a UUID string is valid.
    """
    try:
        uuid_obj = uuid.UUID(uuid_string)
        return str(uuid_obj) == uuid_string
    except ValueError:
        return False


def float_to_decimal_field_value_get(value, n):
    return str(value.quantize(Decimal(10) ** -n))


def convert_utc_to_timezone(datetime_obj: datetime, tz: str = settings.TIME_ZONE):
    return datetime_obj.astimezone(pytz.timezone(tz))


def datetime_to_cron(dt: datetime):
    """
    Generate a cron expression from a datetime object.
    """
    cron_fields = [
        dt.minute,  # Minute (0 - 59)
        dt.hour,  # Hour (0 - 23)
        dt.day,  # Day of the month (1 - 31)
        dt.month,  # Month (1 - 12)
        dt.isoweekday() % 7,  # Day of the week (0 - 6, Sunday = 0)
    ]
    return generate_cron_expression(
        minute=cron_fields[0], hour=cron_fields[1], day=cron_fields[2], month=cron_fields[3], day_of_week=cron_fields[4]
    )


def generate_cron_expression(minute="*", hour="*", day="*", month="*", day_of_week="*"):
    """
    Generate a cron expression from given parameters.
    """
    return f"{minute} {hour} {day} {month} {day_of_week}"


def get_current_local_time() -> timezone.datetime:
    return timezone.localtime(timezone.now())


def get_current_utc_time(time: timezone.datetime) -> timezone.datetime:
    return time.astimezone(timezone.utc)


def get_local_time(time: timezone.datetime) -> timezone.datetime:
    return time.astimezone(timezone.get_current_timezone())


def get_utc_time(time: timezone.datetime) -> timezone.datetime:
    return time.astimezone(timezone.utc)


@lru_cache(maxsize=1)
def get_request_headers_mapping():
    return {
        value.value.removeprefix("rd-").replace("-", "_"): f"HTTP_{value.value.replace('-', '_').upper()}"
        for name, value in RequestHeaders.__members__.items()
        if not name.startswith("OLD_")
    }.items()


@lru_cache(maxsize=1024)
def get_latency_bucket(latency_ms):
    for (low, high), bucket in settings.LOGGING_REQUEST_LATENCY_BUCKETS:
        if low <= latency_ms < high:
            return bucket
    return settings.LOGGING_REQUEST_LATENCY_BUCKETS[-1][1]


def format_trailing_zeros(value):
    """Format number to remove trailing zeros if it's a whole number"""
    if value is None:
        return None

    if isinstance(value, str):
        try:
            value = float(value)
        except ValueError:
            return value

    if isinstance(value, Decimal) or isinstance(value, float):
        if value == int(value):
            return str(int(value))  # Return as integer if it's a whole number
        return str(value).rstrip("0").rstrip(".")  # Remove trailing zeros
    return str(value)


def should_handle_backward_compatibility(source: str, app_version: str):
    return is_source_mobile(source, app_version)


def is_source_mobile(source: str, app_version: str):
    if source in [
        SourceChoices.APP.value,
        SourceChoices.ANDROID.value,
        SourceChoices.IOS.value,
    ]:
        is_outdated = is_outdated_app_version(app_version)
        return is_outdated
    return False


def is_outdated_app_version(app_version: str):
    minimum_required_app_version = "1010591"
    logger.info(
        "app version",
        app_version=app_version,
        minimum_required_app_version=minimum_required_app_version,
    )
    return bool(app_version) and version.parse(app_version) < version.parse(minimum_required_app_version)


def get_sanitized_filename(filename: str) -> str:
    # Sanitize filename: only alphanumeric, hyphens, underscores allowed
    filename = filename.replace(" ", "_")
    sanitized_filename = re.sub(r"[^a-zA-Z0-9_-]", "", filename)
    return sanitized_filename


def transform_url_with_filename(url: str, filename: str) -> str:
    sanitized_filename = get_sanitized_filename(filename)
    old_domain = settings.AWS_CDN_DOMAIN
    new_domain = settings.AWS_ASSET_DOMAIN
    url = url.replace(old_domain, new_domain)
    path, extension = url.rsplit(".", 1)
    url = f"{path}/custom-name/{sanitized_filename}.{extension}"
    return url


class RequestDataExtractor:
    """Extracts request data for logging purposes."""

    @staticmethod
    def get_request_header(request, header_key, meta_key):
        if hasattr(request, "headers"):
            return request.headers.get(header_key)
        return request.META.get(meta_key)

    @staticmethod
    def extract_request_token_data(request):
        """Extract token-related data from the request object."""
        token_data = getattr(request, "token_data", None)
        if isinstance(token_data, TokenData) and token_data.user_id:
            return {
                "user_id": token_data.user_id,
                "org_id": token_data.org_id,
                "is_admin": getattr(token_data, "is_admin", None),
                "is_app_token": getattr(token_data, "is_app_token", None),
            }
        return {}

    @staticmethod
    def extract_request_headers_data(request):
        """Extract specific request headers based on a mapping."""
        meta = getattr(request, "META", None)
        if meta:
            return {name: value for name, key in get_request_headers_mapping() if (value := meta.get(key)) is not None}
        return {}

    @staticmethod
    def extract_request_metadata(request):
        """Extract user-specific metadata from the request object."""
        user = getattr(request, "user", None)
        if user and hasattr(user, "request_metadata") and isinstance(user.request_metadata, RequestMetaData):
            return {
                "email": getattr(user.request_metadata, "email", None),
                "country": getattr(user.request_metadata, "country_name", None),
                "user_name": getattr(user.request_metadata, "user_name", None),
                "org_name": getattr(user.request_metadata, "org_name", None),
            }
        return {}

    @staticmethod
    def extract_resolver_match(request):
        """
        Extract API endpoint and route information from resolver match.
        Adds Following keys to the log:
        - api: API endpoint name
        - route: API route
        """
        resolver_match = getattr(request, "resolver_match", None)
        if isinstance(resolver_match, ResolverMatch) and hasattr(resolver_match, "func"):
            try:
                api_name = f"{resolver_match.func.__module__}.{resolver_match.func.__name__}"
            except AttributeError:
                api_name = str(resolver_match.func)
            return {
                "api": api_name,
                "route": getattr(resolver_match, "route", None),
            }
        return {}

    @staticmethod
    def calculate_latency_data(request):
        """
        Calculate request latency in milliseconds and determine bucket.
        Adds Following keys to the log:
        - latency_bucket: Bucket for latency
        - latency_ms: Latency in milliseconds
        """
        if hasattr(request, "start_time"):
            latency_ms = (time.time() - request.start_time) * 1000
            return {
                "latency_bucket": get_latency_bucket(int(latency_ms)),
                "latency_ms": round(latency_ms, 2),
            }
        return {}
