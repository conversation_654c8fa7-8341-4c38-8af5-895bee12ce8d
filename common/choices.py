from enum import Enum

from django.contrib.gis.db import models
from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _


class CustomElementType(models.TextChoices):
    BOQ = "boq", _("BOQ")
    ORDER = "order", _("Order")


class PermissionScope(TextChoices):
    CORE = "CORE", _("Organization User")
    PROJECT = "PROJECT", _("Project User")
    ORGANIZATION = "ORGANIZATION", _("Project Organization")


class OrganizationType(TextChoices):
    CLIENT = "CLIENT", _("Client")
    PMC = "PMC", _("PMC")
    VENDOR = "VENDOR", _("Vendor")


class OrganizationVerificationStatus(TextChoices):
    NOT_VERIFIED = "not_verified", _("Not Verified")
    VERIFIED = "verified", _("Verified")
    REJECTED = "rejected", _("Rejected")
    PENDING = "pending", _("Pending")
    IN_PROGRESS = "in_progress", _("In Progress")
    DOCUMENTS_REQUIRED = "documents_required", _("Documents Required")


class RoleType(TextChoices):
    GENERIC = "GENERIC", _("Generic")
    DYNAMIC = "DYNAMIC", _("Dynamic")


class StateChoices(models.IntegerChoices):
    NOT_SET = 0, "Not Set"
    ANDHRA_PRADESH = 1, "Andhra Pradesh"
    ARUNACHAL_PRADESH = 2, "Arunachal Pradesh"
    ASSAM = 3, "Assam"
    BIHAR = 4, "Bihar"
    CHHATTISGARH = 5, "Chhattisgarh"
    GOA = 6, "Goa"
    GUJARAT = 7, "Gujarat"
    HARYANA = 8, "Haryana"
    HIMACHAL_PRADESH = 9, "Himachal Pradesh"
    JHARKHAND = 10, "Jharkhand"
    KARNATAKA = 11, "Karnataka"
    KERALA = 12, "Kerala"
    MADHYA_PRADESH = 13, "Madhya Pradesh"
    MAHARASHTRA = 14, "Maharashtra"
    MANIPUR = 15, "Manipur"
    MEGHALAYA = 16, "Meghalaya"
    MIZORAM = 17, "Mizoram"
    NAGALAND = 18, "Nagaland"
    ODISHA = 19, "Odisha"
    PUNJAB = 20, "Punjab"
    RAJASTHAN = 21, "Rajasthan"
    SIKKIM = 22, "Sikkim"
    TAMIL_NADU = 23, "Tamil Nadu"
    TELANGANA = 24, "Telangana"
    TRIPURA = 25, "Tripura"
    UTTAR_PRADESH = 26, "Uttar Pradesh"
    UTTARAKHAND = 27, "Uttarakhand"
    WEST_BENGAL = 28, "West Bengal"
    JAMMU_KASHMIR = 29, "Jammu & Kashmir"
    DELHI = 30, "Delhi"
    ANDAMAN_AND_NICOBAR_ISLANDS = 31, "Andaman and Nicobar Islands"
    LAKSHADWEEP = 32, "Lakshadweep"
    PUDUCHERRY = 33, "Puducherry"
    CHANDIGARH = 34, "Chandigarh"
    LADAKH = 35, "Ladakh"
    DADRA_AND_NAGAR_HAVELI_AND_DAMAN_AND_DIU = 36, "Dadra and Nagar Haveli and Daman & Diu"


class CountryChoices(TextChoices):
    INDIA = "India"


class CityChoices(TextChoices):
    AGRA = "Agra"
    AHMEDABAD = "Ahmedabad"
    AJMER = "Ajmer"
    ALIGARH = "Aligarh"
    AMRITSAR = "Amritsar"
    AURANGABAD = "Aurangabad"
    BANGALORE = "Bangalore"
    BARIELEY = "Barieley"
    BARMER = "Barmer"
    BEED = "Beed"
    BHUBANESWAR = "Bhubaneswar"
    BHOPAL = "Bhopal"
    BHIWADI = "Bhiwandi"
    BIKANER = "Bikaner"
    BILASPUR = "Bilaspur"
    CHENNAI = "Chennai"
    CUTTACK = "Cuttack"
    DHANBAD = "Dhanbad"
    DURGAPUR = "Durgapur"
    DEHRADUN = "Dehradun"
    ERNAKULAM = "Ernakulam"
    FARIDABAD = "Faridabad"
    GHAZIABAD = "Ghaziabad"
    GWALIOR = "Gwalior"
    GURGAON = "Gurgaon"
    GORAKHPUR = "Gorakhpur"
    GUWAHATI = "Guwahati"
    GUNTUR = "Guntur"
    HAMIRPUR = "Hamirpur"
    HUBBALLI = "Hubballi"
    HYDERABAD = "Hyderabad"
    INDORE = "Indore"
    JALANDHAR = "Jalandhar"
    JALGAON = "Jalgaon"
    JAMMU = "Jammu"
    JAMNAGAR = "Jamnagar"
    JAMSHEDPUR = "Jamshedpur"
    JAIPUR = "Jaipur"
    JHANSI = "Jhansi"
    JODHPUR = "Jodhpur"
    KANPUR = "Kanpur"
    KOLHAPUR = "Kolhapur"
    KOLKATA = "Kolkata"
    KANNUR = "Kannur"
    KOCHI = "Kochi"
    KURNOOL = "Kurnool"
    LUDHIANA = "Ludhiana"
    LUCKNOW = "Lucknow"
    MADURAI = "Madurai"
    MANGLORE = "Mangalore"
    MORADABAD = "Moradabad"
    MYSORE = "Mysore"
    MUMBAI = "Mumbai"
    MEERUT = "Meerut"
    NAGPUR = "Nagpur"
    NASHIK = "Nashik"
    ONGOLE = "Ongole"
    PUNE = "Pune"
    RAIPUR = "Raipur"
    PANCHKULA = "Panchkula"
    PATNA = "Patna"
    RAJKOT = "Rajkot"
    RANCHI = "Ranchi"
    ROURKELA = "Rourkela"
    SHIMLA = "Shimla"
    SILIGURI = "Siliguri"
    SONIPAT = "Sonipat"
    SRINAGAR = "Srinagar"
    SURAT = "Surat"
    THANE = "Thane"
    THIRUVANANTHAPURAM = "Thiruvananthapuram"
    THRISSUR = "Thrissur"
    VADODARA = "Vadodara"
    VARANASI = "Varanasi"
    VIJAYWADA = "Vijaywada"
    VISAKHAPATNAM = "Visakhapatnam"
    DELHI = "Delhi"
    GREATER_NOIDA = "Greater Noida"
    NOIDA = "Noida"
    ROORKEE = "Roorkee"
    AMBALA = "Ambala"
    SAHARANPUR = "Saharanpur"
    GAUTAM_BUDH_NAGAR = "Gautam Budh Nagar"
    HISAR = "Hisar"
    BHIWANI = "Bhiwani"


class SourceChoices(TextChoices):
    WEB = "web", _("WEB")
    APP = "app", _("APP")
    IOS = "ios", _("IOS")
    ANDROID = "android", _("Android")
    N8N = "n8n", _("N8N")
    CUSTOM = "custom", _("Custom")


class NestedObjectStatus(TextChoices):
    ADDED = "added", _("Added")
    ADDED_UPDATED = "added_updated", _("Added & Updated")
    UPDATED = "updated", _("Updated")
    DELETED = "deleted", _("Deleted")


class SectionKey:
    OTHERS = "others"
    ELEMENT_LIBRARY = "element_library"
    PROJECT = "project"
    RECCE = "recce"
    RECCE_V2 = "recce_v2"
    DESIGN = "design"
    BOQ = "boq"
    ORDERS_EXPENSES = "orders_expenses"
    WORK_REPORTS = "work_reports"
    SNAG = "snag"
    VMS = "vms"
    FINANCE = "finance"
    MY_SCOPE = "my_scope"
    INCOMING_ORDER = "incoming_order"
    PROPOSAL_FOR_CLIENT = "proposal_for_client"
    OUTGOING_ORDER = "outgoing_order"
    PROPOSAL_FROM_VENDOR = "proposal_from_vendor"
    VENDOR_WISE_SCOPE = "vendor_wise_scope"
    VENDOR_WISE_PROGRESS = "vendor_wise_progress"
    PROGRESS_UPDATE = "progress_update"
    ORG_SETTINGS = "org_settings"
    OTHER_PERMISSIONS = "other_permissions"
    BOARD = "board"
    LEAD = "lead"
    INVENTORY = "inventory"
    DASHBOARDS = "dashboards"
    PROJECT_ACTIVITY_SCHEDULE = "project_activity_schedule"
    VENDOR_INVOICES = "vendor_invoices"
    VENDOR_PAYMENTS = "vendor_payments"


class SectionKeyChoices(TextChoices):
    OTHERS = SectionKey.OTHERS, _("Others")
    ELEMENT_LIBRARY = SectionKey.ELEMENT_LIBRARY, _("Element Library")
    PROJECT = SectionKey.PROJECT, _("Project")
    RECCE = SectionKey.RECCE, _("Recce")
    RECCE_V2 = SectionKey.RECCE_V2, _("Recce V2")
    DESIGN = SectionKey.DESIGN, _("Design")
    BOQ = SectionKey.BOQ, _("BOQ")
    ORDERS_EXPENSES = SectionKey.ORDERS_EXPENSES, _("Orders & Expenses")
    WORK_REPORTS = SectionKey.WORK_REPORTS, _("Work Reports")
    SNAG = SectionKey.SNAG, _("Snag")
    VMS = SectionKey.VMS, _("VMS")
    FINANCE = SectionKey.FINANCE, _("Finance")
    MY_SCOPE = SectionKey.MY_SCOPE, _("My Scope")
    INCOMING_ORDER = SectionKey.INCOMING_ORDER, _("Incoming Order")
    PROPOSAL_FOR_CLIENT = SectionKey.PROPOSAL_FOR_CLIENT, _("Proposal for Client")
    OUTGOING_ORDER = SectionKey.OUTGOING_ORDER, _("Outgoing Order")
    PROPOSAL_FROM_VENDOR = SectionKey.PROPOSAL_FROM_VENDOR, _("Proposal from Vendor")
    VENDOR_WISE_SCOPE = SectionKey.VENDOR_WISE_SCOPE, _("Vendor Wise Scope")
    VENDOR_WISE_PROGRESS = SectionKey.VENDOR_WISE_PROGRESS, _("Vendor Wise Progress")
    PROGRESS_UPDATE = SectionKey.PROGRESS_UPDATE, _("Progress Update")
    ORG_SETTINGS = SectionKey.ORG_SETTINGS, _("Organization Settings")
    OTHER_PERMISSIONS = SectionKey.OTHER_PERMISSIONS, _("Other Permissions")
    BOARD = SectionKey.BOARD, _("Board")
    LEAD = SectionKey.LEAD, _("Lead")
    INVENTORY = SectionKey.INVENTORY, _("Inventory")
    DASHBOARDS = SectionKey.DASHBOARDS, _("Dashboards")


class FilePathChoices(TextChoices):
    """
    file Path folder name is derived from the model name
    for model -"ElementPreviewFile"
    the folder name is -  "element-preview-file"
    in similar way the path/folder name should be mentioned
    and should be conveyed to the frontend as well
    """

    # Core Files
    ELEMENT_LIBRARY_GUIDELINE_ATTACHMENTS = "element-library-guideline-attachments"
    ELEMENT_LIBRARY_PREVIEW_FILE = "element-preview-file"
    ELEMENT_LIBRARY_PRODUCTION_DRAWING = "element-production-drawing"
    USER = "user"
    ORGANIZATION_DOCUMENT = "organization-document"
    VENDOR_DOCUMENT = "vendor-organization-document"
    CLIENT_DOCUMENT = "client-organization-document"
    TASK = "task"
    QUOTATION_ATTACHMENT = "quotation-attachment"
    QUOTATION_ELMENT_PREVIEW_FILE = "quotation-element-preview-file"
    QUOTATION_ELEMENT_PRODUCTION_DRAWING = "quotation-element-production-drawing"
    QUOTATION_ELEMENT_GUIDELINE_ATTACHMENT = "quotation-element-guideline-attachment"
    QUOTATION_PDF_PREVIEW_FILE = "quotation-pdf-preview-file"
    RATE_CONTRACT_DOCUMENT = "rate-contract-document"
    RATE_CONTRACT_TERMS_AND_CONDITION_ATTACHMENT = "rate-contract-terms-and-condition-attachment"
    LEAD_FILE = "lead-file"

    # Project Files
    BOQ_ELEMENT_PREVIEW_FILE = "boq-element-preview-file"
    BOQ_ELEMENT_PRODUCTION_DRAWING = "boq-element-production-drawing"
    BOQ_ELEMENT_GUIDELINE_ATTACHMENT = "boq-element-guideline-attachment"
    ORDER_ELEMENT_PREVIEW_FILE = "custom-vendor-order-element-preview-file"
    ORDER_ELEMENT_PRODUCTION_DRAWING = "custom-vendor-element-production-drawing"
    ORDER_ELEMENT_GUIDELINE_ATTACHMENT = "custom-vendor-element-guideline-attachment"
    SEND_ORDER_IN_MAIL_ATTACHMENT = "order_excel_attachments"
    SEND_PROPOSAL_IN_MAIL_ATTACHMENT = "proposal_excel_attachments"
    PROGRESS_REPORT_ATTACHMENT = "progress-report-attachment"
    PROPOSAL_DOCUMENT = "proposal-document"
    ASSIGNMENT_TIMELINE_PREVIEW_FILE = "assignment-timeline-preview-file"
    DEDUCTION_ATTACHMENT = "deduction-attachment"
    INVOICE_FILE = "invoice-file"
    INVOICE_SUPPORTING_DOC = "invoice-supporting-doc"
    INVOICE_CREDIT_NOTE_CREDIT_DOC = "invoice-credit-note-credit-doc"
    INVOICE_CREDIT_NOTE_DEBIT_DOC = "invoice-credit-note-debit-doc"
    SNAG_REMARK = "snag-remark"
    PROPOSAL_REQUEST_ATTACHMENT = "proposal-request-attachment"
    RECCE_TEMPLATE_INSTRUCTION_FILES = "recce-template-instruction-files"
    RECCE_INSTRUCTION_FILES = "recce-instructions-files"
    EXPENSE_PROOF = "expense-proof"
    EXPENSE_ITEM_PROOF = "expense-item-proof"
    PAYMENT_ENTRY = "payment-entry"
    PAYMENT_REQUEST = "payment-request"
    PROJECT_ATTACHMENTS = "project-attachments"
    PROJECT_FIELD_ATTACHMENTS = "project-field-attachments"
    ORDER_PO_RECEIVED = "order-po-received"
    ORDER_PO_PREVIEW = "order-po-preview"
    INVENTORY_CONSUMPTION_BATCH_ATTACHMENT = "consumption-attachment"
    INVENTORY_TRANSFER_BATCH_ATTACHMENT = "inventory-transfer-attachment"
    INVENTORY_BATCH_ATTACHMENT = "inventory-batch-attachment"
    RECCE_FILE = "recce-file"
    VIEW_POINT_ATTACHMENT = "view-point-file"
    PROJECT_SCHEDULE_ACTIVITY_ATTACHMENT = "project-schedule-activity-attachment"
    SNAG_ASSIGNMENT_TIMELINE_PREVIEW_FILE = "snag"

    # Core and Project Files
    TERMS_AND_CONDITION_ATTACHMENT = "terms-and-condition-attachment"
    COMMENT = "comment-v2"
    ELEMENT_GUIDELINE_ATTACHMENT = "element-guideline-attachment"  # Confirm its position whether in project or core
    COMMENT_REPLY = "commentReply"  # TODO: Have to fixed on app side when next app build was released


class ReservedRoleNames(Enum):
    PROPOSAL_APPROVER = "Proposal Approver"
    ORDER_VIEWER = "Order Viewer"
    ORDER_RECEIVER = "Order Receiver"
    PROJECT_POC = "Project POC"
    RECCIE_ASSIGNEE = "Recce Assignee"


class ProjectUserRoleLevelChoices(models.IntegerChoices):
    LEVEL_1 = 1, _("Level 1")
    LEVEL_2 = 2, _("Level 2")
    LEVEL_3 = 3, _("Level 3")
    LEVEL_4 = 4, _("Level 4")
    LEVEL_5 = 5, _("Level 5")
    LEVEL_6 = 6, _("Level 6")

    LEVEL_96 = 96, _("Project POC")  # JSW One Homes
    # ORDER RECEIVERS LEVELS
    LEVEL_97 = 97, _("Proposal Approver")
    LEVEL_98 = 98, _("Order Viewer")
    LEVEL_99 = 99, _("Order Receiver")
    # Recce Assignee
    LEVEL_100 = 100, _("Recce Assignee")


class OrderType(TextChoices):
    INCOMING = "incoming"
    OUTGOING = "outgoing"


class VendorStatusChoices(TextChoices):
    ONBOARDED = "onboarded", _("Onboarded")
    ACTIVE = "active", _("Active")
    INACTIVE = "inactive", _("Inactive")
    BLACKLISTED = "blacklisted", _("Blacklisted")


class VendorStatusColorCode(Enum):
    BLACKLISTED = "#FF0000"
    ACTIVE = "#000000"
    INACTIVE = "#FF0000"
    ONBOARDED = "#000000"


class ElementTypeContext(Enum):
    BOQ_ELEMENT = "boq_element"
    LIBRARY_ELEMENT = "library_element"
    ORDER_ELEMENT = "order_element"
    EXPENSE_ITEM = "expense_item"
