import json
from enum import Enum

import requests
import structlog
from django.conf import settings

logger = structlog.get_logger(__name__)


class WhatsAppEventHandler:
    class EventTemplates(Enum):
        RECCE_STARTED = "recce_begin"
        RECCE_SUBMITTED_SELF = "recce_submit_2_1"
        RECCE_SUBMITTED = "recce_submit_1_1_v1"
        RECCE_FEEDBACK_FORM = "recce_feedback_v2"
        RECCE_LINK_CREATED = "recce_creation_final_1"
        FIRST_STAGE_MOVEMENT = "stage_movement_1"
        SECOND_STAGE_MOVEMENT = "stage_movement_2"
        THIRD_STAGE_MOVEMENT = "stage_movement_3"
        FOURTH_STAGE_MOVEMENT = "stage_movement_4"
        DESIGN_FREEZE = "design_freeze"
        DESIGN_FILE_COMMENT = "comment_design_file"
        DUE_DATE_MODIFICATION = "2_dd"
        PROGRESS_REPORT_CREATED = "dpr_submit"
        PROJECT_DATE_CHANGED = "dates_change"
        COMMENT_APPROVAL_REQUESTED = "user_approval"
        MENTIONED_IN_COMMENT = "user_tag_v2"
        COMMENT_APPROVAL_APPROVED = "user_request_approved"
        COMMENT_APPROVAL_REJECTED = "user_request_rejected"
        MARK_EXECUTION_COMPLETED = "mark_execution_completed_v1"
        VENDOR_PROGRESS_REPORT_CREATED = "dpr_vendor_wise"
        PROPOSAL_SENT = "proposal_sent"
        PROPOSAL_REJECTED = "proposal_rejected_v2"
        DESIGN_FILE_NEW_VERSION = "design_revision_internal_v2"
        DESIGN_FILE_NEW_VERSION_POST_FREEZE = "design_revision_delivery"

    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {settings.WATI_TOKEN}"}

    @staticmethod
    def prepare_wati_params(data: dict) -> list:
        return [
            {
                "name": key,
                "value": (1 if value == 0 else value) if isinstance(value, int) else (value if value else "NA"),
            }
            for key, value in data.items()
        ]

    @staticmethod
    def _get_wati_parameters(parameters: list, user: dict) -> list:
        logger.debug("wati parameters", parameters=parameters, user=user)
        parameters.append({"name": "user", "value": user.get("name")})
        return parameters

    @staticmethod
    def _get_wati_receivers(data: list, users: list) -> list:
        receivers = []
        lookup = set()
        for user in users:
            temp = data.copy()
            temp.append({"name": "user", "value": user.get("name")})
            contact = user.get("contact").replace("+", "")
            if contact in lookup:
                # Avoid sending same event multiple times to same user
                continue
            receivers.append({"whatsappNumber": contact, "customParams": temp})
            lookup.add(contact)
        logger.debug(f"[#] Receivers: {receivers}")
        return receivers

    def _prepare_single_wati_message(self, template_name, data, user):
        return {
            "template_name": template_name,
            "broadcast_name": "recce_alert",
            "parameters": self._get_wati_parameters(data, user),
        }

    def _prepare_multiple_wati_message(self, template_name, data, users):
        return {
            "template_name": template_name,
            "broadcast_name": "recce_alert",
            "receivers": self._get_wati_receivers(data, users),
        }

    @classmethod
    def _send_wati_notifications(cls, data: dict) -> None:
        endpoint = "/api/v1/sendTemplateMessages"
        content = json.dumps(data)
        logger.info(f"send_wati_notifications: data - {content}")
        response = requests.request("POST", settings.WATI_URL + endpoint, headers=cls.headers, data=content)
        logger.info(f"send_wati_notifications: status code - {response.status_code}", wati_response=response.json())
        return content, response

    @classmethod
    def _send_wati_notification(cls, data: dict, phone_number: str) -> None:
        endpoint = "/api/v1/sendTemplateMessage"
        content = json.dumps(data)
        logger.debug(f"send_wati_notification: data - {content}")
        response = requests.request(
            "POST",
            settings.WATI_URL + endpoint + "/?whatsappNumber=" + phone_number.replace("+", ""),
            headers=cls.headers,
            data=content,
        )
        logger.info(f"send_wati_notification: status code - {response.status_code}", wati_response=response.json())
        return content, response

    def send_wati_event(self, template_name, data, users):
        if not users:
            return None
        data = self.prepare_wati_params(data)
        if len(users) == 1:
            data = self._prepare_single_wati_message(template_name, data, users[0])
            logger.info(
                f"[#] Message prepared for single user having template: {template_name} & data: {data} & User: {users}"
            )
            return self._send_wati_notification(data, users[0].get("contact"))
        else:
            data = self._prepare_multiple_wati_message(template_name, data, users)
            logger.info(
                f"[#] Message prepared for multiple user having template: {template_name}"
                f" & data: {data} & User: {users}"
            )
            return self._send_wati_notifications(data)
