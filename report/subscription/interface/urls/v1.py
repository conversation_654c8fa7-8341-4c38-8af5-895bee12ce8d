from django.urls import path

from report.subscription.interface.apis import (
    ReportSubscriptionUserChannelEnableDisableApi,
    ReportSubscriptionUserCreateApi,
    ReportSubscriptionUserDeleteApi,
    ReportSubscriptionUserHistoryListApi,
    ReportSubscriptionUserListApi,
    ReportSubscriptionUserUpdateApi,
)

urlpatterns = [
    path("subscription-user/list/", ReportSubscriptionUserListApi.as_view(), name="report-subscription-user-list"),
    path(
        "subscription-user/create/", ReportSubscriptionUserCreateApi.as_view(), name="report-subscription-user-create"
    ),
    path(
        "subscription-user/<hash_id:subscription_user_id>/update/",
        ReportSubscriptionUserUpdateApi.as_view(),
        name="report-subscription-user-update",
    ),
    path(
        "subscription-user/<hash_id:subscription_user_id>/<str:channel>/<str:action>/",
        ReportSubscriptionUserChannelEnableDisableApi.as_view(),
        name="report-subscription-user-channel-enable-disable",
    ),
    path(
        "subscription-user/<hash_id:subscription_user_id>/delete/",
        ReportSubscriptionUserDeleteApi.as_view(),
        name="report-subscription-user-delete",
    ),
    path(
        "subscription-user/<hash_id:subscription_user_id>/histories/",
        ReportSubscriptionUserHistoryListApi.as_view(),
        name="report-subscription-user-history-list",
    ),
]
