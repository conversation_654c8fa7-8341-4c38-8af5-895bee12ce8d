# Generated by Django 3.2.15 on 2025-05-19 09:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscription', '0011_auto_20250516_0522'),
    ]

    operations = [
        migrations.AlterField(
            model_name='reportsubscription',
            name='action',
            field=models.CharField(choices=[('webhook_task_done', 'Webhook Task Done'), ('webhook_task_created', 'Webhook Task Created'), ('webhook_approval_request_created', 'Webhook Approval Request Created'), ('webhook_approval_request_approved', 'Webhook Approval Request Approved'), ('webhook_approval_request_edited', 'Webhook Approval Request Edited'), ('webhook_user_created', 'Webhook User Created'), ('webhook_user_logged_in', 'Webhook User Logged In'), ('webhook_order_created', 'Webhook Order Created'), ('webhook_order_sent', 'Webhook Order Sent'), ('webhook_order_sent_without_notification', 'Webhook Order Sent Without Notification'), ('webhook_order_modified', 'Webhook Order Modified'), ('webhook_draft_order_modified', 'Webhook Draft Order Modified'), ('webhook_order_cancelled', 'Webhook Order Cancelled'), ('webhook_po_revised', 'Webhook PO Revised'), ('webhook_po_uploaded', 'Webhook PO Uploaded'), ('webhook_organization_created', 'Webhook Organization Created'), ('webhook_dpr_created', 'Webhook DPR Created'), ('webhook_boq_element_progress_percentage_update', 'Webhook BOQ Element Progress Percentage Update'), ('webhook_design_file_upload', 'Webhook Design File Upload'), ('webhook_design_new_version_uploaded', 'Webhook Design New Version Uploaded'), ('webhook_design_file_approved', 'Webhook Design File Approved'), ('webhook_design_freeze', 'Webhook Design Freeze'), ('webhook_design_file_status_updated', 'Webhook Design File Status Updated'), ('webhook_snag_poc_alloted', 'Webhook Snag POC Alloted'), ('webhook_snag_assigned', 'Webhook Snag Assigned'), ('webhook_proposal_approved', 'Webhook Proposal Approved'), ('webhook_proposal_rejected', 'Webhook Proposal Rejected'), ('webhook_proposal_created', 'Webhook Proposal Created'), ('webhook_proposal_updated_when_order_exist', 'Webhook Proposal Updated When Order Exist'), ('webhook_proposal_updated_when_order_not_exist', 'Webhook Proposal Updated When Order Not Exist'), ('webhook_proposal_entry_created', 'Webhook Payment Entry Created'), ('webhook_lead_quotation_submitted', 'Webhook Lead Quotation Submitted'), ('webhook_lead_assignment', 'Webhook Lead Assignment'), ('webhook_lead_created', 'Webhook Lead Created'), ('webhook_lead_stage_changed', 'Webhook Lead Stage Changed'), ('webhook_project_created', 'Webhook Project Created'), ('webhook_mark_execution_completed', 'Webhook Mark Execution Completed'), ('webhook_project_assignment', 'Webhook Project Assignment'), ('webhook_single_recce_approved', 'Webhook Single Recce Approved'), ('webhook_rece_submitted', 'Webhook Recce Submitted'), ('webhook_project_schedule_activities_created', 'Webhook Project Schedule Activities Created'), ('webhook_project_schedule_activities_updated', 'Webhook Project Schedule Activities Updated'), ('webhook_project_schedule_activities_deleted', 'Webhook Project Schedule Activities Deleted'), ('webhook_project_schedule_activities_edited', 'Webhook Project Schedule Activities Edited'), ('webhook_facebook_lead_created', 'Webhook Facebook Lead Created'), ('webhook_new_invoice_uploaded', 'Webhook New Invoice Uploaded'), ('notify_project_comment', 'Notify Project Comment'), ('notify_comment_mentioned', 'Notify Comment Mentioned'), ('notify_comment_approval_requested', 'Notify Comment Approval Requested'), ('notify_comment_approval_accepted', 'Notify Comment Approval Accepted'), ('notify_comment_approval_rejected', 'Notify Comment Approval Rejected'), ('notify_project_comment_mentioned', 'Notify Project Comment Mentioned'), ('notify_project_comment_approval_accepted', 'Notify Project Comment Approval Accepted'), ('notify_project_comment_approval_rejected', 'Notify Project Comment Approval Rejected'), ('notify_project_comment_approval_requested', 'Notify Project Comment Approval Requested'), ('notify_comment_approval_mentioned', 'Notify Comment Approval Mentioned'), ('notify_comment_approval_accepted_rejected', 'Notify Comment Approval Accepted Rejected'), ('notify_recce_link_creation', 'Notify Recce Link creation'), ('notify_recce_started', 'Notify Recce Started'), ('notify_recce_submission', 'Notify Recce Submission'), ('notify_recce_submission_self', 'Notify Self Recce Submission'), ('notify_recce_assigned', 'Notify Recce Assigned'), ('notify_recce_feedback_form', 'Notify Recce Feedback Form'), ('notify_recce_updated', 'Notify Recce Updated'), ('notify_recce_approved', 'Notify Recce Approved'), ('notify_design_freeze', 'Notify Design Freeze'), ('notify_design_approved', 'Notify Design Approved'), ('notify_design_new_version_uploaded', 'Notify Design New Version Uploaded'), ('notify_design_new_version_post_freeze_uploaded', 'Notify Design New Version Post Freeze Uploaded'), ('notify_project_creation', 'Notify Project Creation'), ('notify_project_assignment', 'Notify Project Assignment'), ('notify_project_shared', 'Notify Project Shared'), ('notify_project_user_removed', 'Notify Project User Removed'), ('notify_project_date_changed', 'Notify Project Date Changed'), ('notify_project_date_assign', 'Notify Project Date Assign'), ('notify_new_invoice_uploaded', 'Notify New Invoice Uploaded'), ('notify_all_invoices_marked_uploaded', 'Notify All Invoices Marked Uploaded'), ('notify_work_progress_report_pdf_generated', 'Notify Work Progress Report PDF Generated'), ('notify_work_progress_export_report_pdf_generated', 'Notify Work Progress Export Report PDF Generated'), ('notify_mark_execution_completed', 'Notify Mark Execution Completed'), ('notify_client_for_work_progress_report_pdf_generated', 'Notify Client For Work Progress Report PDF Generated'), ('notify_approval_request_created', 'Notify Approval Request Created'), ('notify_approval_request_approved', 'Notify Approval Request Approved'), ('notify_approval_request_finally_approved', 'Notify Approval Request Finally Approved'), ('notify_approval_request_rejected', 'Notify Approval Request Rejected'), ('notify_approval_request_edited', 'Notify Approval Request Edited'), ('notify_approval_request_hold', 'Notify Approval Request Hold'), ('resource_request_cancel', 'Resource Request Cancel'), ('callback_approval_request_created', 'Callback Approval Request Created'), ('callback_approval_request_approved', 'Callback Approval Request Approved'), ('callback_approval_request_finalized', 'Callback Approval Request Finalized'), ('notify_snag_assignment_to_creator', 'Notify Snag Assignment To Creator'), ('notify_snag_assignment_to_assignee', 'Notify Snag Assignment To Assignee'), ('notify_snag_bulk_assignment_to_creators', 'Notify Snag Bulk Assignment To Creators'), ('notify_snag_bulk_assignment_to_assignee', 'Notify Snag Bulk Assignment To Assignee'), ('notify_snag_allot_and_commit_timeline_to_allotee', 'Notify Snag Allot And Commit Timeline To Allotee'), ('notify_snag_allot_and_commit_timeline', 'Notify Snag Allot And Commit Timeline To Observers'), ('notify_snag_unresolved', 'Notify Snag Unresolved'), ('notify_snag_bulk_allot_and_commit_timeline_to_allotee', 'Notify Snag Bulk Allot And Timeline Commit To Allotee'), ('notify_snag_bulk_allot_and_commit_timeline_to_observers', 'Notify Snag Bulk Allot And Commit Timeline To Observers'), ('notify_user_onboarded', 'Notify User Onboarded'), ('callback_task_created', 'Callback Task Created'), ('callback_user_created', 'Callback User Created'), ('callback_dpr_created', 'Callback DPR Created'), ('callback_project_created', 'Callback Project Created'), ('callback_single_recce_approved', 'Callback Single Recce Approved'), ('callback_rece_submitted', 'Callback Recce Submitted'), ('callback_task_done', 'Callback Task Done'), ('callback_design_file_upload', 'Callback Design File Upload'), ('callback_snag_poc_alloted', 'Callback Snag POC Alloted'), ('schedule_misconfig_request', 'Schedule Misconfig Request'), ('skip_requests_misconfigured_levels', 'Skip Requests Misconfigured Levels'), ('callback_snag_assigned', 'Callback Snag Assigned'), ('design_file_approved', 'Design File Approved'), ('callback_design_new_version_uploaded', 'Callback Design New Version Uploaded'), ('callback_boq_element_progress_percentage_update', 'Callback BOQ Element Progress Percentage Update'), ('callback_organization_created', 'Callback Organization Created'), ('callback_approval_request_edited', 'Callback Approval Request Edited'), ('callback_stock_transfer_batch_approved', 'Callback Stock Transfer Batch Approved'), ('notify_stock_transfer_batch_created', 'Notify Stock Transfer Batch Created'), ('notify_stock_transfer_batch_action', 'Notify Stock Transfer Batch Action '), ('callback_proposal_approved', 'Callback Proposal Approved'), ('callback_proposal_rejected', 'Callback Proposal Rejected'), ('callback_proposal_created', 'Callback Proposal Created'), ('notify_proposal_request_for_new_order', 'Notify Proposal Request For New Order'), ('notify_proposal_request_for_order_change', 'Notify Proposal Request For Order Change'), ('notify_proposal_approve_for_new_order', 'Notify Proposal Approve For New Order'), ('notify_proposal_approve_for_order_change', 'Notify Proposal Approve For Order Change'), ('notify_proposal_reject_for_new_order', 'Notify Proposal Reject For New Order'), ('notify_proposal_reject_for_order_change', 'Notify Proposal Reject For Order Change'), ('notify_proposal_sent', 'Notify Proposal Sent'), ('notify_proposal_rejected', 'Notify Proposal Rejected'), ('callback_po_uploaded', 'Callback PO Uploaded'), ('callback_po_revised', 'Callback PO Revised'), ('notify_po_sent', 'Notify PO Sent'), ('notify_po_received', 'Notify PO Received'), ('notify_po_sent_cancelled', 'Notify PO Sent Cancelled'), ('notify_po_uploaded_email', 'Notify PO Uploaded Email'), ('notify_po_cancelled_email', 'Notify PO Cancelled Email'), ('notify_po_revised_email', 'Notify PO Revised Email'), ('notify_po_received_cancelled', 'Notify PO Received Cancelled'), ('callback_order_modified', 'Callback Order Modified'), ('callback_order_created', 'Callback Order Created'), ('callback_order_sent', 'Callback Order Sent'), ('callback_order_cancelled', 'Callback Order Cancelled'), ('notify_order_received', 'Notify Order Received'), ('notify_order_sent_completed', 'Notify Order Sent Completed'), ('notify_order_received_completed', 'Notify Order Received Completed'), ('notify_order_sent', 'Notify Order Sent'), ('notify_order_sent_modified', 'Notify Order Sent Modified'), ('notify_order_received_modified', 'Notify Order Received Modified'), ('notify_order_sent_cancelled', 'Notify Order Sent Cancelled'), ('notify_order_received_cancelled', 'Notify Order Received Cancelled'), ('notify_order_sent_without_proposal', 'Notify Order Sent Without Proposal'), ('notify_order_modify_without_proposal', 'Notify Order Modify Without Proposal'), ('notify_order_cancel_without_proposal', 'Notify Order Cancel Without Proposal'), ('callback_proposal_entry_created', 'Callback Payment Entry Created'), ('notify_task_assigned', 'Notify Task Assigned'), ('notify_task_mentioned', 'Notify Task Mentioned'), ('notify_task_reply_mentioned', 'Notify Task Reply Mentioned'), ('notify_approval_request_reply_mentioned', 'Notify Approval Request Reply Mentioned'), ('notify_task_updated', 'Notify Task Updated'), ('notify_task_done', 'Notify Task Done'), ('notify_task_archived_for_all', 'Notify Task Archived For All'), ('notify_task_reminder', 'Notify Task Reminder'), ('notify_todays_due_task_count', 'Notify Todays Due Task Count'), ('notify_lead_assignment', 'Notify Lead Assignment'), ('notify_board_assignment', 'Notify Board Assignment'), ('callback_lead_quotation_submitted', 'Callback Lead Quotation Submitted'), ('callback_update_poc_role', 'Callback Update POC Role'), ('notify_project_schedule_completed', 'Notify Project Schedule Completed'), ('notify_project_schedule_activities_assigned', 'Notify Project Schedule Activities Assigned'), ('notify_project_schedule_activities_deleted', 'Notify Project Schedule Activities Deleted'), ('notify_project_schedule_delay', 'Notify Project Schedule Delay'), ('notify_project_schedule_overdue', 'Notify Project Schedule Overdue'), ('template_work_progress_generate_report', 'Template Work Progress Generate Report'), ('template_work_progress_export_report', 'Template Work Progress Export Report')], max_length=100),
        ),
    ]
