import structlog
from django.db import models
from django.db.utils import IntegrityError
from django.utils import timezone
from phonenumber_field import modelfields

from authorization.domain.constants import Actions
from common.events.constants import Events
from common.models import BaseModel, CreateModel, UpdateDeleteModel
from core.models import Organization
from microcontext.choices import MicroContextChoices
from project.data.models import Project
from report.subscription.data.choices import EmailErrorCodes, WhatsappErrorCodes
from report.subscription.data.querysets import ReportSubscriptionUserQueryset

logger = structlog.get_logger(__name__)


class ReportSubscription(BaseModel):
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="report_subscriptions")
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="report_subscriptions")
    context = models.CharField(choices=MicroContextChoices.choices, max_length=100)
    action = models.CharField(choices=Actions.choices, max_length=100)
    event = models.Char<PERSON>ield(choices=Events.choices, max_length=100)

    class Meta:
        db_table = "report_subscriptions"
        unique_together = ("org", "project", "context", "action", "event")


class ReportSubscriptionBase(CreateModel):
    name = models.CharField(max_length=255)
    phone_number = modelfields.PhoneNumberField(null=True, blank=True, default=None)
    email = models.EmailField(null=True, blank=True, default=None)
    email_permitted = models.BooleanField(default=False)
    whatsapp_permitted = models.BooleanField(default=False)

    class Meta:
        abstract = True


class ReportSubscriptionUser(ReportSubscriptionBase, UpdateDeleteModel):
    subscription = models.ForeignKey(
        ReportSubscription, on_delete=models.RESTRICT, related_name="report_subscription_users"
    )
    last_email_delivered = models.BooleanField(default=True)
    last_whatsapp_delivered = models.BooleanField(default=True)
    objects = ReportSubscriptionUserQueryset.as_manager()

    def clean(self):
        errors = {}
        if (
            self.phone_number
            and ReportSubscriptionUser.objects.filter(
                phone_number=self.phone_number, subscription_id=self.subscription_id
            )
            .available()
            .exclude(pk=self.pk)
            .exists()
        ):
            errors["phone_number"] = ["Phone number already exists"]

        if (
            self.email
            and ReportSubscriptionUser.objects.filter(email=self.email, subscription_id=self.subscription_id)
            .available()
            .exclude(pk=self.pk)
            .exists()
        ):
            errors["email"] = ["Email already exists"]
        if errors:
            raise self.ModelDataIntegrityException(errors)

    def save(self, *args, **kwargs):
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:
            if "report_subscription_phone_number_unique" in str(e):
                raise self.ModelDataIntegrityException({"phone_number": ["Phone number already exists"]})
            elif "report_subscription_email_unique" in str(e):
                raise self.ModelDataIntegrityException({"email": ["Email already exists"]})
            raise e

    def get_last_error(self, *args, **kwargs) -> str:
        if not self.last_email_delivered and not self.last_whatsapp_delivered:
            email_history = self.undelivered_email_histories.all().first()
            if email_history is None:
                logger.info("No undelivered email history found")
            time = timezone.localtime(email_history.created_at).strftime("%d %b,%y")
            return f"DPR whatsapp and email message on {time} are not delivered"
        elif not self.last_email_delivered:
            email_history = self.undelivered_email_histories.all().first()
            if email_history is None:
                logger.info("No undelivered email history found")
            time = timezone.localtime(email_history.created_at).strftime("%d %b,%y")
            return f"DPR email message on {time} is not delivered"
        elif not self.last_whatsapp_delivered:
            whatsapp_history = self.undelivered_whatsapp_histories.all().first()
            if whatsapp_history is None:
                logger.info("No undelivered whatsapp history found")
            time = timezone.localtime(whatsapp_history.created_at).strftime("%d %b,%y")
            return f"DPR whatsapp message on {time} is not delivered"

    class Meta:
        db_table = "report_subscription_users"
        constraints = [
            models.UniqueConstraint(
                name="report_subscription_email_unique",
                fields=["subscription", "email"],
                condition=models.Q(deleted_at__isnull=True),
            ),
            models.UniqueConstraint(
                name="report_subscription_phone_number_unique",
                fields=["subscription", "phone_number"],
                condition=models.Q(deleted_at__isnull=True),
            ),
        ]


class ReportSubscriptionUserUpdateHistory(ReportSubscriptionBase):
    subscription_user = models.ForeignKey(
        ReportSubscriptionUser, on_delete=models.RESTRICT, related_name="update_histories"
    )

    class Meta:
        db_table = "report_subscription_users_update_histories"


class ReportSubscriptionUndeliveredEmailHistory(BaseModel):
    subscription_user = models.ForeignKey(
        ReportSubscriptionUser, on_delete=models.RESTRICT, related_name="undelivered_email_histories"
    )
    email = models.EmailField()
    context_id = models.IntegerField()
    error_code = models.IntegerField(choices=EmailErrorCodes.choices, null=True, blank=True, default=None)
    error_message = models.TextField(null=True, blank=True, default=None)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "report_subscription_undelivered_email_histories"


class ReportSubscriptionUndeliveredWhatsappHistory(BaseModel):
    subscription_user = models.ForeignKey(
        ReportSubscriptionUser, on_delete=models.RESTRICT, related_name="undelivered_whatsapp_histories"
    )
    phone_number = modelfields.PhoneNumberField()
    context_id = models.IntegerField()
    error_code = models.IntegerField(choices=WhatsappErrorCodes.choices, null=True, blank=True, default=None)
    error_message = models.TextField(null=True, blank=True, default=None)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "report_subscription_undelivered_whatsapp_histories"
