from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from report.download.domain.entities import DeleteDownloadInputEntity
from report.download.domain.factories import DownloadServiceFactory
from report.download.interface.base_api import DownloadBaseApi


class DeleteDownloadApi(DownloadBaseApi):
    """
    Deletes a download.
    """

    input_pydantic_class = DeleteDownloadInputEntity

    def delete(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = DownloadServiceFactory(user_entity=user_entity)

        service = factory.get_service()
        files_deleted_cnt = service.delete_download(data=data)

        if files_deleted_cnt == 1:
            self.set_response_message("File deleted successfully")
        elif files_deleted_cnt > 1:
            self.set_response_message("Files deleted successfully")
        return Response(status=HTTP_200_OK)
