import abc
import inspect
from io import BytesIO
from uuid import UUID

from common.exceptions import BaseValidationError
from microcontext.domain.constants import MicroContext
from report.download.domain.entities import (
    DeleteDownloadInputEntity,
    DownloadDetailEntity,
    DownloadEntity,
    DownloadFilterEntity,
)
from report.download.domain.enums import DownloadProgressStatusEnum


class DownloadAbstractRepo:
    class Exception(BaseValidationError):
        pass

    class DownloadNotFoundException(Exception):
        pass

    class InvalidContextException(BaseValidationError):
        pass

    @abc.abstractmethod
    def __init__(self, user_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {
            "user_id",
            "org_id",
        }
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def get_download_list(
        self,
        filter_data: DownloadFilterEntity,
    ) -> list[DownloadEntity]:
        pass

    @abc.abstractmethod
    def get_download(self, download_id: int) -> DownloadDetailEntity:
        pass

    @abc.abstractmethod
    def create_download(
        self,
        name: str,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
        context: MicroContext | None = None,
        context_id: int | None = None,
        uuid: UUID | None = None,
    ) -> int:
        pass

    @abc.abstractmethod
    def update_download_status(
        self,
        download_id: int,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
    ):
        pass

    @abc.abstractmethod
    def delete_download(self, data: DeleteDownloadInputEntity) -> int:
        pass
