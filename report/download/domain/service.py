from io import Bytes<PERSON>
from uuid import UUID

from common.exceptions import BaseValidationError
from microcontext.domain.constants import MicroContext
from report.download.domain.abstract_repos import DownloadAbstractRepo
from report.download.domain.entities import DeleteDownloadInputEntity, DownloadFilterEntity
from report.download.domain.enums import DownloadProgressStatusEnum


class DownloadService:
    class Exception(BaseValidationError):
        pass

    class DownloadNotFoundException(Exception):
        pass

    def __init__(self, repo: DownloadAbstractRepo):
        self.repo = repo

    def get_download(self, download_id: int):
        try:
            return self.repo.get_download(download_id=download_id)
        except DownloadAbstractRepo.DownloadNotFoundException:
            raise self.DownloadNotFoundException("Download not found")

    def get_download_list(self, filter_data: DownloadFilterEntity):
        return self.repo.get_download_list(filter_data=filter_data)

    def delete_download(self, data: DeleteDownloadInputEntity) -> int:
        return self.repo.delete_download(data=data)

    def create_download(
        self,
        name: str,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
        context: MicroContext | None = None,
        context_id: int | None = None,
        uuid: UUID | None = None,
    ) -> int:
        return self.repo.create_download(
            file=file,
            name=name,
            progress_status=progress_status,
            context=context,
            context_id=context_id,
            uuid=uuid,
        )

    def update_download_status(
        self,
        download_id: int,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
    ):
        return self.repo.update_download_status(
            download_id=download_id,
            progress_status=progress_status,
            file=file,
        )
