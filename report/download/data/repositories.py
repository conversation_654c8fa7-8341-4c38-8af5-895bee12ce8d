from datetime import timedelta
from io import BytesIO
from uuid import UUID

import structlog
from django.core.files.base import ContentFile
from django.utils import timezone

from microcontext.domain.constants import MicroContext
from report.download.data.models import ReportDownload
from report.download.domain.abstract_repos import DownloadAbstractRepo
from report.download.domain.entities import (
    DeleteDownloadInputEntity,
    DownloadDetailEntity,
    DownloadDetailProjectEntity,
    DownloadEntity,
    DownloadFilterEntity,
)
from report.download.domain.enums import DownloadProgressStatusEnum

logger = structlog.get_logger(__name__)


class DownloadRepo(DownloadAbstractRepo):
    FAIL_SAFE_TIME = timedelta(hours=5)

    def __init__(self, user_id: int, org_id: int, project_id: int | None = None):
        self.user_id = user_id
        self.org_id = org_id
        self.project_id = project_id
        self.current_time = timezone.now()

    def get_download_list(self, filter_data: DownloadFilterEntity) -> list[DownloadEntity]:
        downloads = (
            ReportDownload.objects.get_queryset()
            .filter(
                created_by_id=self.user_id,
                organization_id=self.org_id,
            )
            .available()
            .order_by("-created_at")
        )

        if filter_data.context:
            downloads = downloads.filter(context=filter_data.context.value)

        if filter_data.search_text:
            downloads = downloads.filter(name__icontains=filter_data.search_text)

        entities: list[DownloadEntity] = []

        for download in downloads:
            download: ReportDownload
            progress_status = DownloadProgressStatusEnum(download.progress_status)

            if (
                progress_status == DownloadProgressStatusEnum.IN_PROGRESS
                and (self.current_time - download.created_at) > self.FAIL_SAFE_TIME
            ):
                progress_status = DownloadProgressStatusEnum.FAILED

            entity = DownloadEntity(
                id=download.pk,
                file=download.file.url if download.file else None,
                name=download.name,
                progress_status=progress_status,
                created_at=download.created_at,
            )
            entities.append(entity)

        return entities

    def create_download(
        self,
        name: str,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
        context: MicroContext | None = None,
        context_id: int | None = None,
        uuid: UUID | None = None,
    ) -> int:
        instance = ReportDownload()

        instance.name = name
        instance.progress_status = progress_status.value
        instance.organization_id = self.org_id
        instance.context = context.value if context else None
        instance.context_id = context_id
        instance.created_by_id = self.user_id
        instance.project_id = self.project_id
        instance.uuid = uuid

        if file:
            content_file = ContentFile(file.getvalue())
            instance.file.save(name, content_file, save=False)

        instance.clean()

        instance.save()

        return instance.pk

    def update_download_status(
        self,
        download_id: int,
        progress_status: DownloadProgressStatusEnum,
        file: BytesIO | None = None,
    ):
        instance = ReportDownload.objects.filter(id=download_id).first()

        if not instance:
            raise self.DownloadNotFoundException("Download not found")

        if file:
            content_file = ContentFile(file.getvalue())
            instance.file.save(instance.name, content_file, save=True)

        instance.progress_status = progress_status.value
        instance.save()

        return instance

    def delete_download(self, data: DeleteDownloadInputEntity) -> int:
        downloads = ReportDownload.objects.available()

        if data.download_ids:
            downloads = downloads.filter(id__in=data.download_ids)

        for download in downloads:
            download: ReportDownload

            download.soft_delete(user_id=self.user_id, save=False)

        downloads.bulk_update(
            downloads,
            ["deleted_at", "deleted_by_id"],
        )
        return len(downloads)

    def get_download(self, download_id: int) -> DownloadDetailEntity:
        download = ReportDownload.objects.select_related("project", "organization").get(id=download_id)

        if not download:
            raise self.DownloadNotFoundException("Download not found")

        return DownloadDetailEntity(
            id=download.pk,
            url=download.file.url if download.file else None,
            name=download.name,
            project=(
                DownloadDetailProjectEntity(
                    id=download.project.pk,
                    name=download.project.name,
                    job_id=download.project.job_id,
                    org_name=download.organization.name,
                )
                if download.project
                else None
            ),
            deleted_at=download.deleted_at,
            uuid=download.uuid,
        )
