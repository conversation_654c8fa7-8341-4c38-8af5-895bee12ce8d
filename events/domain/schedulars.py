import abc

import structlog

from events.domain.task import misconfig_request_resolver_worker, skip_misconfigured_levels_for_request_worker
from microcontext.domain.constants import MicroContext

logger = structlog.get_logger(__name__)


class BaseSchdeular:
    _queue = None
    _worker = None

    def __init__(self) -> None:
        if self._queue is None:
            raise NotImplementedError(f"[!] Queue is not defined for schedular: {type(self).__name__}")
        if self._worker is None:
            raise NotImplementedError(f"[!] Worker is not defined for schedular: {type(self).__name__}")

    @abc.abstractmethod
    def schedule(self):
        ...


class MisConfigSchedular(BaseSchdeular):
    _queue = "misconfig_resolver_queue"
    _worker = misconfig_request_resolver_worker

    def __init__(self):
        super().__init__()

    def schedule(self, request_id: int, context: MicroContext, resource_id: int):
        logger.info("Scheduling started")
        scheduled_task = self._worker.apply_async(
            queue=self._queue, kwargs={"request_id": request_id, "context": context, "resource_id": resource_id}
        )
        logger.info("Scheduling finished", child_task_id=scheduled_task.id)


class MisconfigLevelsSkipSchedular(BaseSchdeular):
    _queue = "misconfig_resolver_queue"
    _worker = skip_misconfigured_levels_for_request_worker

    def __init__(self):
        super().__init__()

    def schedule(
        self,
        approval_request_id: int,
    ):
        logger.info("Scheduling started")
        scheduled_task = self._worker.apply_async(
            queue=self._queue,
            kwargs={
                "approval_request_id": approval_request_id,
            },
        )
        # self._worker(
        #     approval_request_id=approval_request_id,
        # )
        logger.info("Scheduling finished", child_task_id=scheduled_task.id)
