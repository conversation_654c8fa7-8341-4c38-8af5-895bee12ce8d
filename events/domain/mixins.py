import abc
from typing import Dict, List

import structlog
from celery import shared_task
from django.db.models import QuerySet

from authentication.data.selectors import get_user_token, get_user_tokens
from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.base import BaseEventData
from common.services import flatten
from core.helpers import OrgPermissionHelper
from core.models import (
    OrganizationCallbackUrl,
    SystemCallbackUrl,
    SystemWebhookCallbackUrl,
    User,
    WebhookCallbackUrl,
)
from core.selectors import organization_user_fetch_all
from core.user.repository import UserRepository
from events.actions.base_action_processor import BaseActionProcessor
from events.data.entities import CallbackUrlData, WebhookUrlData
from events.domain.services import PushNotificationService
from external_services.messengers.whatsapp.entities import ReceiverData, WhatsappMessengerData, WhatsAppTemplateData
from external_services.messengers.whatsapp.services import WhatsappExternalMessenger
from notifications.data.models import Notification
from notifications.interface.serializers.base import NotificationSerializer
from project.data.models import <PERSON>User
from project.domain.helpers import <PERSON><PERSON>er<PERSON><PERSON>elper
from project.selectors import project_user_fetch_all
from report.subscription.data.models import ReportSubscriptionUser
from report.subscription.data.selectors import get_report_subscription_users

logger = structlog.getLogger(__name__)


class MultiNotificationMixin:
    def dispatch_jobs(self, job_data: List[Notification]):
        if job_data:
            user_tokens = list(get_user_tokens(user_ids=[data.user_id for data in job_data]))
            logger.info(f"No. of user token fetched for send Notifications: {len(user_tokens)}")
            if user_tokens:
                serialized_data = dict(NotificationSerializer(job_data[-1]).data)
                to_send_data = flatten(serialized_data)
                logger.info(f"To send data for send Notifications: {to_send_data}")
                PushNotificationService.send_multicast(user_tokens=user_tokens, data=to_send_data)
                logger.info(f"Push Notification sent successfully for send data: {to_send_data}")
            return
        logger.info("No Data Found to dispatch job")


class SingleNotificationMixin:
    def dispatch_jobs(self, job_data: List[Notification]):
        for data in job_data:
            logger.info(f"Fetching token for user_id: {data.user_id}")
            user_tokens = get_user_token(user_id=data.user_id)
            logger.info(f"Token for user {data.user_id}: {user_tokens}")
            if user_tokens:
                serialized_data = dict(NotificationSerializer(data).data)
                to_send_data = flatten(serialized_data)
                logger.info(f"To send data: {to_send_data}")
                for user_token in user_tokens:
                    PushNotificationService.send(user_token=user_token, data=to_send_data)
                logger.info(f"Push Notification sent successfully for user: {data.user_id}")


class ProjectRecipientMixin:
    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions):
        recipients = []
        project_users: List[ProjectUser] = (
            project_user_fetch_all(project_id=meta_data.project_id)
            .select_related("role", "project", "user")
            .filter(
                user__is_active=True,
                user__deleted_at__isnull=True,
            )
        )
        logger.info(f"No. of Project users: {len(project_users)}")

        for project_user in project_users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=project_user.project_id,
                org_id=project_user.role.organization_id,
                user_id=project_user.user_id,
                action=action_type,
            ):
                contact = project_user.user.phone_number.as_e164 if project_user.user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=project_user.user_id,
                        name=project_user.user.name,
                        contact=contact,
                        email=project_user.user.email,
                        org_id=project_user.user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))


class OrgRecipientMixin:
    @abc.abstractmethod
    def get_recipient_org_id(self, action_data: BaseEventData) -> int: ...

    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions):
        recipients = []
        organziation_users = organization_user_fetch_all(org_id=self.get_recipient_org_id(meta_data)).filter(
            user__is_active=True,
            user__deleted_at__isnull=True,
        )
        logger.info(f"No. of Org users: {len(organziation_users)}")

        for organziation_user in organziation_users:
            if OrgPermissionHelper.is_action_permitted(
                org_id=organziation_user.organization_id, user_id=organziation_user.user_id, action=action_type
            ):
                contact = organziation_user.user.phone_number.as_e164 if organziation_user.user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=organziation_user.user_id,
                        name=organziation_user.user.name,
                        contact=contact,
                        email=organziation_user.user.email,
                        org_id=organziation_user.user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))


class ProjectOrgRecipientsMixin:
    @abc.abstractmethod
    def get_recipient_org_id(self, action_data: BaseEventData) -> int: ...

    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions):
        recipients = []
        users = UserRepository().fetch_active_users(org_id=self.get_recipient_org_id(meta_data))
        project_users: QuerySet = (
            project_user_fetch_all(
                project_id=meta_data.project_id,
            )
            .filter(user_id__in=users.values_list("id", flat=True), user__is_active=True)
            .select_related("role", "project", "user")
        )
        logger.info(f"No. of Project users: {len(project_users)}")

        logger.info(f"No. of Org users: {len(users)}")

        for project_user in project_users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=project_user.project_id,
                org_id=project_user.role.organization_id,
                user_id=project_user.user_id,
                action=action_type,
            ):
                recipients.append(
                    ActionRecipient(
                        user_id=project_user.user_id,
                        name=project_user.user.name,
                        contact=project_user.user.contact,
                        email=project_user.user.email,
                        org_id=project_user.user.org_id,
                    )
                )

        for user in users:
            if OrgPermissionHelper.is_action_permitted(org_id=user.org_id, user_id=user.pk, action=action_type):
                recipients.append(
                    ActionRecipient(
                        user_id=user.pk,
                        name=user.name,
                        contact=user.contact,
                        email=user.email,
                        org_id=user.org_id,
                    )
                )

        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))


class ProjectUserWithinOrgMixin:
    @abc.abstractmethod
    def get_recipient_org_id(self, action_data: BaseEventData) -> int: ...

    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions):
        recipients = []
        org_id = self.get_recipient_org_id(meta_data)
        if org_id is None:
            return recipients

        project_users: QuerySet = (
            project_user_fetch_all(project_id=meta_data.project_id)
            .filter(
                user__org_id=org_id,
                user__is_active=True,
                user__deleted_at__isnull=True,
            )
            .select_related("role", "project", "user")
        )
        logger.info(f"No. of Project users: {len(project_users)}")

        for project_user in project_users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=project_user.project_id,
                org_id=project_user.role.organization_id,
                user_id=project_user.user_id,
                action=action_type,
            ):
                contact = project_user.user.phone_number.as_e164 if project_user.user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=project_user.user_id,
                        name=project_user.user.name,
                        contact=contact,
                        email=project_user.user.email,
                        org_id=project_user.user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))


class ProjectUserWithinOrgAndClientOrgMixin:
    @abc.abstractmethod
    def get_recipient_org_id(self, action_data: BaseEventData) -> int: ...

    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions):
        from project.domain.services import get_client_ids

        organization_ids = []
        recipient_org_id = self.get_recipient_org_id(meta_data)

        organization_ids.append(recipient_org_id)
        client_ids: list[int] = get_client_ids(org_id=recipient_org_id, project_id=meta_data.project_id)

        if len(client_ids) > 1:
            pass
        else:
            organization_ids.extend(client_ids)

        recipients = []
        project_users: QuerySet = (
            project_user_fetch_all(project_id=meta_data.project_id)
            .filter(
                user__org_id__in=organization_ids,
                user__is_active=True,
                user__deleted_at__isnull=True,
            )
            .select_related("role", "project", "user")
        )

        logger.info(f"No. of Project users: {len(project_users)}")
        for project_user in project_users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=project_user.project_id,
                org_id=project_user.role.organization_id,
                user_id=project_user.user_id,
                action=action_type,
            ):
                contact = project_user.user.phone_number.as_e164 if project_user.user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=project_user.user_id,
                        name=project_user.user.name,
                        contact=contact,
                        email=project_user.user.email,
                        org_id=project_user.user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))


class WhatsappNotificationMixin(abc.ABC):
    whatsapp_template = None

    @staticmethod
    @shared_task
    def send_whatsapp_message(
        whatsapp_template: str,
        notification_ids: list[int],
        recipients: List[dict],
        context: Dict,
    ):
        if whatsapp_template is None:
            raise Exception("Whatsapp template not found")

        # request_body, response = WhatsAppEventHandler().send_wati_event(whatsapp_template, context, recipients)
        messenger = WhatsappExternalMessenger(
            data=WhatsappMessengerData(
                template_data=WhatsAppTemplateData(name=whatsapp_template, context=context),
                receivers=[
                    ReceiverData(name=recipient.get("name"), phone_number=recipient.get("contact"))
                    for recipient in recipients
                ],
            )
        )
        try:
            messenger.process()
        except WhatsappExternalMessenger.WhatsappMessengerException as e:
            logger.error("Cannot send whatsapp message", error=e)


class BaseNotificationDispatcher(abc.ABC):
    queue = None

    @abc.abstractmethod
    def process(self, *args, **kwargs):
        pass


class WhatsappNotificationDispatcher(BaseNotificationDispatcher, WhatsappNotificationMixin):
    queue = "whatsapp_notification_queue"

    def process(
        self,
        processor_obj: BaseActionProcessor,
        action_data: BaseEventData,
        job_data: list[Notification],
        attachments,
        recipients: List[ActionRecipient],
    ):
        context = processor_obj.get_whatsapp_context(
            action_data=action_data, job_data=job_data, attachments=attachments
        )
        self.send_whatsapp_message.apply_async(
            queue=self.queue,
            kwargs={
                "whatsapp_template": processor_obj.whatsapp_template,
                "notification_ids": [notification.id for notification in job_data],
                "recipients": [recipient.__dict__ for recipient in recipients if recipient.contact is not None],
                "context": context,
            },
        )


class OrganizationCallbackMixin:
    @abc.abstractmethod
    def _get_recipient_org_ids(self) -> List[int]:
        pass

    def _get_organization_callback_urls(self) -> List[CallbackUrlData]:
        recipient_orgs = self._get_recipient_org_ids()
        logger.info("All org ids", recipient_orgs=recipient_orgs)
        # recipient_orgs = filter_blocked_orgs(org_ids=recipient_orgs, action=self.get_action())
        # logger.info("Allowed org ids", recipient_orgs=recipient_orgs)
        organization_callback_urls = OrganizationCallbackUrl.objects.filter(
            events__event=self.get_event(), organization__in=recipient_orgs
        )
        logger.info("organization callback urls fetched", url_count=len(organization_callback_urls))
        callback_url_data_list: List[CallbackUrlData] = []
        for organization_callback_url in organization_callback_urls:
            callback_url_data_list.append(
                CallbackUrlData(
                    url=organization_callback_url.url,
                    fallback_email=organization_callback_url.fallback_email,
                )
            )
        return callback_url_data_list


class WebhookCallbackMixin:
    @abc.abstractmethod
    def _get_recipient_org_ids(self) -> List[int]:
        pass

    def _get_organization_callback_urls(self) -> List[WebhookUrlData]:
        recipient_orgs = list(set([org_id for org_id in self._get_recipient_org_ids() if org_id is not None]))
        logger.info("All org ids", recipient_orgs=recipient_orgs)
        # recipient_orgs = filter_blocked_orgs(org_ids=recipient_orgs, action=self.get_action())
        # logger.info("Allowed org ids", recipient_orgs=recipient_orgs)
        organization_callback_urls = WebhookCallbackUrl.objects.filter(
            webhook_events__event=self.get_event(), organization__in=recipient_orgs
        )
        logger.info("organization callback urls fetched", url_count=len(organization_callback_urls))
        callback_url_data_list: List[WebhookUrlData] = []
        for organization_callback_url in organization_callback_urls:
            callback_url_data_list.append(
                WebhookUrlData(
                    url=organization_callback_url.url,
                    fallback_email=organization_callback_url.fallback_email,
                    org_id=organization_callback_url.organization_id,
                )
            )
        return callback_url_data_list


class SystemCallbackMixin:
    def _get_system_callback_urls(self) -> List[CallbackUrlData]:
        system_callback_urls = SystemCallbackUrl.objects.filter(events__event=self.get_event())
        logger.info("system callback urls fetched", url_count=len(system_callback_urls))
        callback_url_data_list: List[CallbackUrlData] = []
        for system_callback_url in system_callback_urls:
            callback_url_data_list.append(
                CallbackUrlData(
                    url=system_callback_url.url,
                    fallback_email=system_callback_url.fallback_email,
                )
            )
        return callback_url_data_list


class WebhookSystemCallbackMixin:
    def _get_system_callback_urls(self) -> List[CallbackUrlData]:
        system_webhook_callback_urls = SystemWebhookCallbackUrl.objects.filter(
            system_webhook_events__event=self.get_event()
        )
        logger.info("system webhook callback urls fetched", url_count=len(system_webhook_callback_urls))
        callback_url_data_list: List[CallbackUrlData] = []
        for system_callback_url in system_webhook_callback_urls:
            callback_url_data_list.append(
                CallbackUrlData(
                    url=system_callback_url.url,
                    fallback_email=system_callback_url.fallback_email,
                )
            )
        return callback_url_data_list


class BaseRecipientMixin:
    @abc.abstractmethod
    def _get_event_recipients(self) -> list[ActionRecipient]:
        pass

    def _get_event_recipients_from_project_user(self) -> list[ActionRecipient]:
        recipients = []
        project_users = self._get_project_user_queryset()
        logger.info(f"No. of Project users: {len(project_users)}")
        for project_user in project_users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=project_user.project_id,
                org_id=project_user.role.organization_id,
                user_id=project_user.user_id,
                action=self.get_action(),
            ):
                contact = project_user.user.phone_number.as_e164 if project_user.user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=project_user.user_id,
                        name=project_user.user.name,
                        contact=contact,
                        email=project_user.user.email,
                        org_id=project_user.user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))

    def _get_event_recipients_from_user(self) -> list[ActionRecipient]:
        recipients = []
        users = self._get_user_queryset()
        logger.info(f"No. of users: {len(users)}")
        for user in users:
            if OrgPermissionHelper.is_action_permitted(org_id=user.org_id, user_id=user.pk, action=self.get_action()):
                contact = user.phone_number.as_e164 if user.phone_number else None
                recipients.append(
                    ActionRecipient(
                        user_id=user.pk,
                        name=user.name,
                        contact=contact,
                        email=user.email,
                        org_id=user.org_id,
                    )
                )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))

    def _get_event_recipients_from_subscription_user(self) -> list[ActionRecipient]:
        recipients = []
        subscription_users: QuerySet[ReportSubscriptionUser] = self._get_subscription_user_queryset()
        logger.info(f"No. of Subscription users: {len(subscription_users)}")
        for subscription_user in subscription_users:
            contact = None
            email = None
            if subscription_user.phone_number and subscription_user.whatsapp_permitted:
                contact = subscription_user.phone_number.as_e164
            if subscription_user.email and subscription_user.email_permitted:
                email = subscription_user.email
            recipients.append(
                ActionRecipient(
                    name=subscription_user.name,
                    contact=contact,
                    email=email,
                )
            )
        logger.info(f"No. of Recipients: {list(set(recipients))}")
        return list(set(recipients))

    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        project_users = (
            project_user_fetch_all(project_id=self.get_action_data().project_id)
            .select_related("role", "project", "user")
            .filter(
                user__is_active=True,
                user__deleted_at__isnull=True,
            )
        )
        return project_users

    def get_recipient_org_id(self) -> int:
        raise NotImplementedError("Please implement get_recipient_org_id")

    def _get_user_queryset(self) -> QuerySet[User]:
        users = UserRepository().fetch_active_users(org_id=self.get_recipient_org_id())
        return users

    def _get_subscription_user_queryset(self):
        return get_report_subscription_users(
            context=self.get_context(),
            project_id=self.get_action_data().project_id,
            event=self.get_event(),
            action=self.get_action(),
            org_id=self.get_recipient_org_id(),
        )


class ProjectRecipientMixinV2(BaseRecipientMixin):
    def _get_event_recipients(self) -> list[ActionRecipient]:
        return self._get_event_recipients_from_project_user()


class OrgRecipientMixinV2(BaseRecipientMixin):
    def _get_event_recipients(self, meta_data: BaseEventData, action_type: Actions) -> list[ActionRecipient]:
        return self._get_event_recipients_from_user()


class ProjectAndOrgRecipientMixinV2(BaseRecipientMixin):
    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        queryset = super()._get_project_user_queryset().filter(user__org_id=self.get_recipient_org_id())
        return queryset

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        recipients.extend(self._get_event_recipients_from_project_user())
        recipients.extend(self._get_event_recipients_from_user())
        return list(set(recipients))


class ProjectOrgRecipientMixinV2(BaseRecipientMixin):
    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        users = self._get_user_queryset()
        project_users = super()._get_project_user_queryset().filter(user_id__in=users.values_list("id", flat=True))
        return project_users

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        recipients.extend(self._get_event_recipients_from_project_user())
        recipients.extend(self._get_event_recipients_from_user())
        return list(set(recipients))


class ProjectUserWithinOrgMixinV2(BaseRecipientMixin):
    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        project_users = super()._get_project_user_queryset().filter(user__org_id=self.get_recipient_org_id())
        return project_users

    def _get_event_recipients(self) -> list[ActionRecipient]:
        return self._get_event_recipients_from_project_user()


class ProjectUserWithinOrgAndClientOrgMixinV2(BaseRecipientMixin):
    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        from project.domain.services import get_client_ids

        organization_ids = []
        recipient_org_id = self.get_recipient_org_id()
        organization_ids.append(recipient_org_id)
        client_ids: list[int] = get_client_ids(org_id=recipient_org_id, project_id=self.get_action_data().project_id)
        if len(client_ids) <= 1:
            organization_ids.extend(client_ids)
        project_users = super()._get_project_user_queryset().filter(user__org_id__in=organization_ids)
        return project_users

    def _get_event_recipients(self) -> list[ActionRecipient]:
        return self._get_event_recipients_from_project_user()


class ProjectUserWithinOrgAndSubscriptionUserMixin(ProjectUserWithinOrgMixinV2):
    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        # removing email for project users
        # at present this mixin is used for dpr subscription only
        for recipient in super()._get_event_recipients():
            recipients.append(
                ActionRecipient(
                    user_id=recipient.user_id,
                    name=recipient.name,
                    contact=recipient.contact,
                    org_id=recipient.org_id,
                )
            )
        recipients.extend(self._get_event_recipients_from_subscription_user())
        return recipients
