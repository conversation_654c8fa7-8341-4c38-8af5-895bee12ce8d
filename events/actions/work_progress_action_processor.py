import abc

import structlog
from django.utils import timezone

from common.entities import AttachmentBase
from common.events.actions.work_progress import WorkProgressGenerateReportPDFGeneratedActionData
from common.utils import get_sanitized_filename, transform_url_with_filename
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.domain.constants import EmailNotificationMode
from events.domain.mixins import ProjectUserWithinOrgAndSubscriptionUserMixin
from events.interface.dispatchers import (
    EmailNotificationDispatcher,
    PushNotificationDispatcher,
    WhatsappNotificationDispatcherV2,
)
from external_services.messengers.email.entities import EmailTemplate, TemplateModule
from microcontext.choices import MicroContextChoices
from progressreport.models import ProgressReport
from project.domain.status import Module

logger = structlog.getLogger(__name__)


class WorkProgressNotifyActionProcessorV2(NotifyActionProcessorV2, ProjectUserWithinOrgAndSubscriptionUserMixin):
    _context = MicroContextChoices.PROGRESS_REPORT
    _module = Module.WORK_REPORT
    _email_template = EmailTemplate.DPR_CREATED.value
    _email_notification_mode = EmailNotificationMode.INDIVIDUAL
    _email_module = TemplateModule.PROGRESS_REPORT.value
    _dispatcher_classes = [
        PushNotificationDispatcher,
        WhatsappNotificationDispatcherV2,
        EmailNotificationDispatcher,
    ]
    _action_data_class = WorkProgressGenerateReportPDFGeneratedActionData

    def prepare_data(self):
        self.set_project_timezone()
        action_data: WorkProgressGenerateReportPDFGeneratedActionData = self.get_action_data()
        dpr = (
            ProgressReport.objects.filter(id=action_data.dpr_id)
            .select_related("created_by", "created_by__org", "ppr__project", "ppr__project__store")
            .first()
        )

        assert dpr, "DPR not found"

        url = dpr.pdf.url
        filename = f"DPR {dpr.ppr.project.name}_{timezone.now().strftime('%d %B %Y')}"
        sanitized_filename = get_sanitized_filename(filename)
        url = transform_url_with_filename(url=url, filename=filename)

        attachments = [AttachmentBase(url=url, name=sanitized_filename)]
        logger.info("Assets attachments", attachments=attachments)

        self._prepared_data = {
            "project": dpr.ppr.project,
            "date": dpr.created_at,
            "creator": dpr.created_by,
            "project_progress_percentage": dpr.derived_project_progress_percent,
            "attachments": attachments,
        }

    def get_context_id(self):
        return self.get_action_data().dpr_id

    def get_whatsapp_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        return {
            "project": f"{prepared_data.get('project').name} ",
            "project_user": prepared_data.get("creator").name,
            "org": prepared_data.get("creator").org.name,
            "job_id": prepared_data.get("project").job_id,
            "link": self.get_redirect_link(),
        }

    def get_whatsapp_attachments(self) -> list[AttachmentBase]:
        return self.get_prepared_data().get("attachments")

    def get_recipient_org_id(self) -> int:
        return self.get_action_data().org_id

    def get_email_data(self):
        prepared_data = self.get_prepared_data()
        org = prepared_data.get("creator").org
        return {
            "subject": f"DPR Generated | {prepared_data.get('project').name} ({prepared_data.get('project').job_id})",
            "body": "",
            "html_context": {
                "date": prepared_data.get("date").strftime("%d %B %Y"),
                "creator_name": prepared_data.get("creator").name,
                "org_name": org.name,
                "org_logo": org.logo.url if org.logo.name else None,
                "project_name": f"{prepared_data.get('project').name} ({prepared_data.get('project').job_id})",
                "project_progress_percentage": prepared_data.get("project_progress_percentage"),
                "project_address": prepared_data.get("project").store.address_line_1,
                "link": self.get_redirect_link(),
            },
            "from_name": org.name,
        }

    def get_email_attachments(self):
        return self.get_prepared_data().get("attachments")

    @abc.abstractmethod
    def get_redirect_link(self) -> str:
        pass
