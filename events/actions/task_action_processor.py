from typing import Optional

import structlog
from django.utils import timezone

from common.events.actions.task import TaskBaseActionData
from core.models import User
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.interface.dispatchers import PushNotificationDispatcher, WhatsappNotificationDispatcherV2
from microcontext.choices import MicroContextChoices
from project.domain.status import Module
from rollingbanners.hash_id_converter import HashIdConverter as Hc
from task.data.choices import TaskTypeChoices
from task.data.models import Task

logger = structlog.get_logger(__name__)


class TaskNotifyActionProcessor(NotifyActionProcessorV2):
    _context = MicroContextChoices.TASK
    _module = Module.TASK.value
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _action_data_class = TaskBaseActionData

    def get_context_id(self) -> int:
        return self.get_action_data().task_id

    def prepare_data(self):
        task_data = self._get_task_data()
        self._check_task_data(task_data)
        self._prepared_data = self._prepare_data_from_task_data(task_data)

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        prepared_data = self.get_prepared_data()
        meta_data["project_id"] = prepared_data.get("project_id")
        meta_data["comment_id"] = prepared_data.get("comment_id")
        meta_data["preview_text"] = prepared_data.get("preview_text")
        meta_data["breadcrumb"] = (
            f"{prepared_data.get('project_name')} > {prepared_data.get('breadcrumb')}"
            if prepared_data.get("breadcrumb")
            else ""
        )
        meta_data["assignee_id"] = prepared_data.get("assignee_id")
        meta_data["task_id"] = prepared_data.get("task_id")
        return meta_data

    def get_whatsapp_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        data = {
            "due_at": self._get_due_at(),
            "project": prepared_data.get("project_name"),
            "job_id": prepared_data.get("project_job_id"),
            "lead_name": "",
            "creator_name": prepared_data.get("creator_name"),
            "link": prepared_data.get("link"),
            "assignee_name": self._get_assignee_name(),
        }
        return data

    def _get_task_data(self):
        task_data = (
            Task.objects.filter(id=self.get_context_id())
            .annotate_assignee()
            .select_related("project", "created_by")
            .values(
                "id",
                "due_at",
                "project_id",
                "project__name",
                "project__job_id",
                "created_by__first_name",
                "created_by__last_name",
                "created_by__email",
                "created_by__phone_number",
                "created_by_id",
                "updated_by__first_name",
                "updated_by__last_name",
                "updated_by_id",
                "comment_id",
                "task_assignee_id",
                "assignee_data",
                "description",
                "type",
                "request_id",
                "context",
                "context_id",
            )
            .first()
        )
        return task_data

    def _prepare_data_from_task_data(self, task_data: dict):
        from commentv2.domain.services.services import build_preview_text, prepare_notification_data

        breadcrumb = None
        preview_text = None
        if task_data.get("comment_id"):
            preview_text, breadcrumb, _ = prepare_notification_data(comment_id=task_data.get("comment_id"))
            if "Project" in breadcrumb:
                breadcrumb = breadcrumb.replace("Project", "")
        return {
            "task_id": task_data.get("id"),
            "due_at": task_data.get("due_at"),
            "project_id": task_data.get("project_id"),
            "project_name": task_data.get("project__name"),
            "project_job_id": task_data.get("project__job_id"),
            "creator_id": task_data.get("created_by_id"),
            "creator_name": task_data.get("created_by__first_name") + " " + task_data.get("created_by__last_name"),
            "creator_email": task_data.get("created_by__email"),
            "creator_contact": task_data.get("created_by__phone_number"),
            "creator_org_id": task_data.get("created_by__org_id"),
            "updater_name": (
                task_data.get("updated_by__first_name") + " " + task_data.get("updated_by__last_name")
                if task_data.get("updated_by_id")
                else None
            ),
            "updater_id": task_data.get("updated_by_id"),
            "breadcrumb": breadcrumb,
            "link": f"task/{Hc.encode(task_data.get('id'))}",
            "assignee_id": task_data.get("task_assignee_id"),
            "assignee_data": task_data.get("assignee_data"),
            "preview_text": preview_text if preview_text else build_preview_text(task_data.get("description")),
            "description": task_data.get("description"),
            "context": task_data.get("context"),
            "context_id": task_data.get("context_id"),
        }

    def _check_task_data(self, task_data: dict):
        if task_data.get("type") != TaskTypeChoices.PURE_TASK.value:
            logger.info("Task type is not pure task", action_data=self.get_action_data())
            raise self.JobAbortException("Task type is not pure task")

    def _get_due_at(self):
        prepared_data = self.get_prepared_data()
        action_data: TaskBaseActionData = self.get_action_data()
        if action_data.old_due_at is None or timezone.datetime.fromisoformat(action_data.old_due_at).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        ) == prepared_data.get("due_at").strftime("%Y-%m-%dT%H:%M:%SZ"):
            return self._get_formatted_date(prepared_data.get("due_at"))
        else:
            return (
                self._get_formatted_date(timezone.datetime.fromisoformat(action_data.old_due_at))
                + " -> "
                + prepared_data.get("due_at").strftime("%d %b %Y")
            )

    def _get_assignee_name(self):
        action_data: TaskBaseActionData = self.get_action_data()
        prepared_data = self.get_prepared_data()
        if action_data.old_assignee_id is None or action_data.old_assignee_id == prepared_data.get(  # In task create
            "assignee_id"
        ):  # In task update
            return self._get_current_assignee_name()
        else:
            # In task update
            old_assignee_name = User.objects.get(id=action_data.old_assignee_id).name
            if prepared_data.get("assignee_id") != prepared_data.get("created_by_id"):
                return old_assignee_name + " -> " + prepared_data.get("assignee_data").get("name")
            else:
                return "Not set yet"

    def _get_formatted_date(self, date: Optional[timezone.datetime]):
        return timezone.localtime(date).strftime("%d %b %Y") if date is not None else "Not set yet"

    def _get_current_assignee_name(self):
        prepared_data = self.get_prepared_data()
        if prepared_data.get("assignee_id") != prepared_data.get("created_by_id"):
            return prepared_data.get("assignee_data").get("name")
        else:
            return "Not set yet"
