import copy

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRec<PERSON>ient
from common.events.actions.snags import SnagAssignedActionData, SnagBulkAssignedActionData
from common.events.constants import Events
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.snags_notification_processor import SnagNotifyActionProcessor
from events.actions.types.snag_recipient_selector import (
    ActorData,
    AssigneeData,
    AssignorData,
    CreatorData,
    SnagRecipientSelector,
)
from events.domain.mixins import ProjectAndOrgRecipientMixinV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextActions, TemplateContextChoices
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.models import SnagAssignment


class BaseSnagBulkAssignmentNotificationProcessor(SnagNotifyActionProcessor, ProjectAndOrgRecipientMixinV2):
    _action_data_class = SnagBulkAssignedActionData
    _event = Events.SNAG_BULK_ASSIGNED.value
    _push_notification_template = TemplateContextChoices.SNAG_BULK_ASSIGNED
    _whatsapp_template = EventTemplates.SNAG_BULK_ASSIGNED.value
    _context = MicroContext.PROJECT_SNAG.value

    def get_context_id(self) -> int:
        return self.get_action_data().project_id

    def get_recipient_org_id(self):
        return self.get_action_data().assigned_to_org_id

    def prepare_data(self):
        action_data: SnagAssignedActionData = self.get_action_data()
        snag_assignments = (
            SnagAssignment.objects.filter(
                snag_id__in=action_data.snag_ids,
                org_from_id=action_data.assigned_by_org_id,
                org_to_id=action_data.assigned_to_org_id,
            )
            .annotate_snag_code()
            .select_related(
                "snag",
                "snag__created_by",
                "created_by",
                "org_from",
                "org_to",
                "snag__project",
            )
        )

        prepared_data = copy.deepcopy(action_data.__dict__)

        prepared_data["snag_ids"] = [assignment.snag_id for assignment in snag_assignments]
        prepared_data["snag_count"] = len(prepared_data["snag_ids"])
        prepared_data["project_id"] = snag_assignments[0].snag.project_id
        prepared_data["project_name"] = snag_assignments[0].snag.project.name
        prepared_data["project_job_id"] = snag_assignments[0].snag.project.job_id
        prepared_data["assigned_by_org_name"] = snag_assignments[0].org_from.name
        prepared_data["assigned_to_org_name"] = snag_assignments[0].org_to.name
        prepared_data["assigned_by_name"] = snag_assignments[0].created_by.name
        prepared_data["assigned_by_id"] = snag_assignments[0].created_by_id
        prepared_data["snag_creators_id"] = [assignment.snag.created_by_id for assignment in snag_assignments]
        prepared_data["link"] = f"/project/{HashIdConverter.encode(prepared_data['project_id'])}/snags/"

        self._prepared_data = prepared_data

    def get_push_notification_meta_data(self):
        prepared_data = self.get_prepared_data()
        meta_data = super().get_push_notification_meta_data()
        meta_data["snag_assignment_id"] = prepared_data.get("snag_assignment_id")
        meta_data["project_id"] = prepared_data.get("project_id")
        meta_data["assigned_by_org_id"] = prepared_data.get("assigned_by_org_id")

        return meta_data

    def get_snag_users_data(self):
        prepared_data = self.get_prepared_data()
        assignee_user_recipients = super()._get_event_recipients()
        assignee_user_ids = [data.user_id for data in assignee_user_recipients]

        users = active_users_get_using_ids(
            user_ids=[*prepared_data.get("snag_creators_id"), prepared_data.get("assigned_by_id"), *assignee_user_ids]
        ).in_bulk()
        actor_data_id = prepared_data.get("assigned_by_id")
        creators_id = prepared_data.get("snag_creators_id")
        assignees_data = [
            AssigneeData(user_id=user.id, org_id=user.org_id)
            for user_id, user in users.items()
            if user_id in assignee_user_ids
        ]

        actor_data = ActorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        assignors_data = [AssignorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)]
        creators_data = [
            CreatorData(user_id=users.get(creator_id).id, org_id=users.get(creator_id).org_id)
            for creator_id in creators_id
        ]
        return assignors_data, actor_data, creators_data, assignees_data, users


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_CREATORS)
class SnagAssignmentNotifyActionProcessor(BaseSnagBulkAssignmentNotificationProcessor):
    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        action_data: SnagAssignedActionData = self.get_action_data()
        message_text = "*{assignee_org_name}* by *{Assigner_Name}*".format(
            assignee_org_name=(
                prepared_data.get("assigned_to_org_name")
                if action_data.assigned_by_org_id != action_data.assigned_to_org_id
                else "your organization"
            ),
            Assigner_Name=(
                prepared_data.get("assigned_by_org_name")
                if action_data.assigned_by_org_id != action_data.assigned_to_org_id
                else prepared_data.get("assigned_by_name")
            ),
        )
        whatsapp_data = {
            "snag_count": "Snags",
            "message_text": message_text,
            "project_name": prepared_data.get("project_name"),
            "job_id": prepared_data.get("project_job_id"),
            "link": prepared_data.get("link"),
        }
        return whatsapp_data

    def _get_event_recipients(self):
        assignors_data, actor_data, creators_data, assignees_data, users = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=creators_data,
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=None,
            allotee_data=None,
        )

        observers = recipient_selector.get_observers(action=MicroContextActions.BULK_ASSIGN)

        recipients = []
        for user_data in observers:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_ASSIGNEE)
class AssigneeSnagAssignmentNotifyActionProcessor(BaseSnagBulkAssignmentNotificationProcessor):
    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        action_data: SnagAssignedActionData = self.get_action_data()
        message_text = "*{assignee_org_name}* by *{Assigner_Name}*".format(
            assignee_org_name=(
                prepared_data.get("assigned_to_org_name")
                if action_data.assigned_by_org_id != action_data.assigned_to_org_id
                else "your organization"
            ),
            Assigner_Name=(
                prepared_data.get("assigned_by_org_name")
                if action_data.assigned_by_org_id != action_data.assigned_to_org_id
                else prepared_data.get("assigned_by_name")
            ),
        )
        whatsapp_data = {
            "snag_count": f"{prepared_data['snag_count']} Snags",
            "message_text": message_text,
            "project_name": prepared_data.get("project_name"),
            "job_id": prepared_data.get("project_job_id"),
            "link": prepared_data.get("link"),
        }
        return whatsapp_data

    def _get_event_recipients(self):
        assignors_data, actor_data, creators_data, assignees_data, users = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=creators_data,
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=None,
            allotee_data=None,
        )
        target_recipients = recipient_selector.get_target_recipients(action=MicroContextActions.BULK_ASSIGN)
        recipients = []
        for user_data in target_recipients:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients
