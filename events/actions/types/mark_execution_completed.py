import structlog

from authorization.domain.constants import Actions
from common.events.actions.work_progress import MarkExecutionCompletedActionData
from common.events.constants import Events
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from events.domain.mixins import ProjectUserWithinOrgMixinV2
from events.interface.dispatchers import PushNotificationDispatcher, WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.data.models import Project
from project.domain.status import Module
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_MARK_EXECUTION_COMPLETED)
class MarkExecutionCompletedActionProcessorV2(NotifyActionProcessorV2, ProjectUserWithinOrgMixinV2):
    _context = MicroContextChoices.PROGRESS_REPORT
    _module = Module.WORK_REPORT
    _action_data_class = MarkExecutionCompletedActionData
    _push_notification_template = TemplateContextChoices.MARK_EXECUTION_COMPLETED
    _whatsapp_template = EventTemplates.MARK_EXECUTION_COMPLETED.value
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _event = Events.WORK_PROGRESS_MARK_EXECUTION_COMPLETED
    _push_notification_mode = PushNotificationMode.GROUP

    def get_context_id(self) -> int:
        return self.get_action_data().project_id

    def get_whatsapp_data(self) -> dict:
        action_data: MarkExecutionCompletedActionData = self.get_action_data()
        project = Project.objects.filter(id=action_data.project_id).first()
        user = User.objects.select_related("org").filter(id=action_data.user_id).first()
        return {
            "project_name": project.name,
            "job_id": project.job_id,
            "user": user.name,
            "org_name": user.org.name,
            "link": self.get_redirect_link(),
            "recipient_key_name": "project_user",
        }

    def get_recipient_org_id(self) -> int:
        return self.get_action_data().org_id

    def get_whatsapp_attachments(self) -> list:
        return []

    def get_redirect_link(self) -> str:
        return f"project/{HashIdConverter.encode(self.get_action_data().project_id)}/work-report/scope-update?mark-execution-completed"
