import structlog

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.materials import (
    InventoryTransferBatchApproveActionData,
)
from common.events.constants import Events
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.materials_action_processor import MaterialsNotifyActionProcessor
from external_services.messengers.whatsapp.entities import EventTemplates
from inventory.data.models import InventoryTransferBatch
from inventory.domain.enums import TransferBatchActions
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from rollingbanners.hash_id_converter import HashIdConverter as Hc

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_STOCK_TRANSFER_BATCH_ACTION)
class TransferBatchCreateActionActionProcessor(MaterialsNotifyActionProcessor):
    _context = MicroContextChoices.INVENTORY_TRANSFER_BATCH
    _action_data_class = InventoryTransferBatchApproveActionData
    _push_notification_template = TemplateContextChoices.INVENTORY_TRANSFER_BATCH_ACTION
    _whatsapp_template = EventTemplates.INVENTORY_TRANSFER_BATCH_ACTION.value
    _event = Events.STOCK_TRANSFER_BATCH_ACTION

    def prepare_data(self):
        action_data: InventoryTransferBatchApproveActionData = self.get_action_data()
        super().prepare_data()
        prepared_data = self._prepared_data
        prepared_data[
            "link"
        ] = f"project/{Hc.encode(action_data.project_id)}/work-report/materials/transferred-stocks/{Hc.encode(action_data.transfer_id)}?grn=true"  # noqa
        self._prepared_data = prepared_data

    def get_whatsapp_data(self):
        action_data: InventoryTransferBatchApproveActionData = self.get_action_data()
        base_data = super().get_whatsapp_data()
        if action_data.action == TransferBatchActions.CANCEL.value:
            base_data.update({"action": "Cancelled"})
        elif action_data.action == TransferBatchActions.APPROVE.value:
            base_data.update({"action": "Approved"})
        elif action_data.action == TransferBatchActions.REJECT.value:
            base_data.update({"action": "Rejected"})
        return base_data

    def _get_event_recipients(self) -> list[ActionRecipient]:
        action_data: InventoryTransferBatchApproveActionData = self.get_action_data()
        user_ids = InventoryTransferBatch.objects.filter(id=action_data.transfer_id).values_list(
            "created_by_id", flat=True
        )
        user = active_users_get_using_ids(user_ids=user_ids).first()
        return (
            [
                ActionRecipient(
                    name=user.name,
                    contact=user.contact,
                    email=user.email,
                    user_id=user.pk,
                    org_id=user.org_id,
                )
            ]
            if user
            else []
        )
