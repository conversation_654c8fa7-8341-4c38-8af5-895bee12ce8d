from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskReplyActionData
from common.events.constants import Events
from core.models import User
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from notifications.data.choices import NotificationType


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_REPLY_MENTIONED)
class TaskReplyMentionedNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_type = NotificationType.MENTIONED
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.TASK_REPLY_MENTIONED.value
    _whatsapp_template = EventTemplates.TASK_REPLY_MENTIONED.value
    _action_data_class = TaskReplyActionData
    _event = Events.TASK_REPLIED

    def _get_due_at(self):
        prepared_data = self.get_prepared_data()
        return self._get_formatted_date(prepared_data.get("due_at"))

    def _get_assignee_name(self):
        return self._get_current_assignee_name()

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        action_data: TaskReplyActionData = self.get_action_data()
        users = active_users_get_using_ids(user_ids=action_data.mentioned_user_ids)
        for user in users:
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        action_data: TaskReplyActionData = self.get_action_data()
        data["replier_name"] = (
            User.objects.filter(id=action_data.replier_id, is_active=True, deleted_at__isnull=True).first().name
        )
        return data

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        action_data: TaskReplyActionData = self.get_action_data()
        meta_data["replier_id"] = action_data.replier_id
        meta_data["mentioned_user_ids"] = action_data.mentioned_user_ids
        return meta_data
