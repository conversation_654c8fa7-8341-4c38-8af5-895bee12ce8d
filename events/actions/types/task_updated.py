from django.utils import timezone

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskUpdatedActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_UPDATED)
class TaskUpdatedNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.TASK_UPDATED.value
    _whatsapp_template = EventTemplates.TASK_UPDATED.value
    _action_data_class = TaskUpdatedActionData
    _event = Events.TASK_UPDATED

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        data["updator_name"] = prepared_data.get("updater_name")
        data["creator_name"] = prepared_data.get("creator_name")
        data["assignee_name"] = self._get_assignee_name()
        return data

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        prepared_data = self.get_prepared_data()
        action_data = self.get_action_data()
        meta_data["updater_id"] = prepared_data.get("updater_id")
        meta_data["old_due_at"] = (
            self._get_formatted_date((timezone.datetime.fromisoformat(action_data.old_due_at)))
            if action_data.old_due_at
            else None
        )
        return meta_data

    def _get_event_recipients(self) -> list[ActionRecipient]:
        prepared_data = self.get_prepared_data()
        recipients = []
        if prepared_data.get("assignee_data") is not None:
            recipients.append(
                ActionRecipient(
                    user_id=prepared_data.get("assignee_id"),
                    name=prepared_data.get("assignee_data").get("name"),
                    contact=prepared_data.get("assignee_data").get("phone_number"),
                    email=prepared_data.get("assignee_data").get("email"),
                    org_id=prepared_data.get("assignee_data").get("org_id"),
                )
            )
        return recipients
