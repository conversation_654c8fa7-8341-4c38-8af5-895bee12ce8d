from authorization.domain.constants import Actions
from common.events.actions.project_schedule import ProjectScheduleCompletedActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.project_schedule_action_processor import ProjectScheduleActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from project_schedule.data.models import ProjectSchedule


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROJECT_SCHEDULE_COMPLETED)
class ProjectScheduleCompletedActionProcessor(ProjectScheduleActionProcessor):
    _action_data_class = ProjectScheduleCompletedActionData
    _event = Events.PROJECT_SCHEDULE_COMPLETED
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.PROJECT_SCHEDULE_COMPLETED.value
    _whatsapp_template = EventTemplates.PROJECT_SCHEDULE_COMPLETED.value

    def prepare_data_from_schedule(self, schedule: ProjectSchedule) -> dict:
        data = super().prepare_data_from_schedule(schedule)
        data["actual_start_date"] = schedule.actual_start_date
        data["actual_end_date"] = schedule.actual_end_date
        return data

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        data = super().get_whatsapp_data()
        data["actual_start_date"] = prepared_data["actual_start_date"].strftime("%d %b %Y")
        data["actual_end_date"] = prepared_data["actual_end_date"].strftime("%d %b %Y")
        data["recipient_key_name"] = "project_user"
        return data
