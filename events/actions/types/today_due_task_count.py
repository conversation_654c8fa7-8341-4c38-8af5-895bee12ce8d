from django.utils import timezone

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TodaysDueTaskCountActionData
from common.events.constants import Events
from core.selectors import active_user_get
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from events.interface.dispatchers import PushNotificationDispatcher, WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.domain.status import Module
from task.data.choices import TaskAssignedAs
from task.data.models import TaskAssignment


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TODAYS_DUE_TASK_COUNT)
class TodaysDueTaskCountNotifyActionProcessor(NotifyActionProcessorV2):
    _context = MicroContextChoices.TASK
    _module = Module.TASK.value
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _action_data_class = TodaysDueTaskCountActionData
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _push_notification_template = TemplateContextChoices.TODAYS_DUE_TASK_COUNT.value
    _whatsapp_template = EventTemplates.TODAYS_DUE_TASK_COUNT.value
    _event = Events.TODAYS_DUE_TASK_COUNT

    def prepare_data(self):
        action_data: TodaysDueTaskCountActionData = self.get_action_data()
        task_count = (
            TaskAssignment.objects.available()
            .filter(
                assignee_id=action_data.user_id,
                assigned_as=TaskAssignedAs.ASSIGNEE,
                task__due_at__date=timezone.now().date(),
            )
            .count()
        )
        if task_count == 0:
            raise self.JobAbortException("No task found for today")
        user = active_user_get(user_id=action_data.user_id)
        self._prepared_data = {
            "task_count": task_count,
            "user": user,
        }

    def get_push_notification_meta_data(self):
        data = super().get_push_notification_meta_data()
        data["task_count"] = self.get_prepared_data()["task_count"]
        return data

    def get_whatsapp_data(self):
        data = {}
        task_count = self.get_prepared_data()["task_count"]
        if task_count == 1:
            data["count_text"] = "1 task is"
        else:
            data["count_text"] = f"{task_count} tasks are"
        return data

    def _get_event_recipients(self) -> list[ActionRecipient]:
        user = self.get_prepared_data()["user"]
        return [
            ActionRecipient(
                user_id=user.pk,
                name=user.name,
                email=user.email,
                contact=user.contact,
                org_id=user.org_id,
            )
        ]
