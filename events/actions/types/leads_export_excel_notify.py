from io import BytesIO

import pytz
import structlog

from authorization.domain.constants import Actions
from common.events import Events
from common.events.actions import ActionRecipient
from common.events.actions.lead import LeadExportExcelActionData
from common.excel.constants import SheetTypeEnum
from common.excel.generator import ExcelGenerator
from core.caches import OrganizationCountryConfigCache
from core.entities import OrgUserEntity
from core.exceptions import ResourceDoesNotExistException, ResourceDoesNotExistsApiException
from core.services import get_user_with_token_data
from crm.lead.excel.excel_data_builder import LeadsExcelDataBuilder
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from events.interface.dispatchers import PushNotificationDispatcher
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.domain.status import Module
from report.download.domain.enums import DownloadProgressStatusEnum
from report.download.domain.factories import DownloadServiceFactory
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.LEAD_EXCEL_EXPORT)
class LeadsExportExcelNotifyActionProcessor(NotifyActionProcessorV2):
    _context = MicroContextChoices.EXPORTER_FILE
    _module = Module.BOARD
    _push_notification_template = TemplateContextChoices.LEADS_EXPORT_EXCEL
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _event = Events.LEAD_EXPORT_EXCEL_GENERATED
    _dispatcher_classes = [
        PushNotificationDispatcher,
    ]
    _action_data_class = LeadExportExcelActionData

    def prepare_data(self):
        action_data: LeadExportExcelActionData = self.get_action_data()

        logger.info(
            "Leads Excel Export Notify Action Processor",
            action_data=action_data,
        )

        user = get_user_with_token_data(org_id=action_data.org_id, user_id=action_data.user_id)
        organization_timezone = pytz.timezone(
            OrganizationCountryConfigCache.get(instance_id=action_data.org_id).timezone.name
        )
        user_entity = OrgUserEntity(
            user_id=action_data.user_id,
            org_id=action_data.org_id,
        )
        try:
            logger.info("Lead excel data preparation started.")

            logger.info(
                "Preparing leads excel data",
                board_id=action_data.board_id,
                user_id=action_data.user_id,
                org_id=action_data.org_id,
            )

            data_builder = LeadsExcelDataBuilder(
                board_id=action_data.board_id,
                user=user,
                organization_timezone=organization_timezone,
                board_name=action_data.board_name,
            )
            logger.info("Leads excel data builder initiated.")
            data_builder.add_sheet(sheet=SheetTypeEnum.LEADS)

            for section in data_builder.sections_config:
                for field in section.fields:
                    data_builder.add_leads_cols(field.name)

            logger.info("Leads excel data builder populated with sections and fields.")
            excel_file, filename = ExcelGenerator(
                data_builder=data_builder,
            ).generate()

            logger.info("Leads excel file generated successfully.", filename=filename)

            download_service = DownloadServiceFactory(user_entity=user_entity).get_service()

            data = download_service.update_download_status(
                download_id=action_data.download_id,
                progress_status=DownloadProgressStatusEnum.UPLOADED,
                file=BytesIO(excel_file),
            )

            if data.deleted_at:
                raise self.JobAbortException("Download is deleted")

            assert data.file, "Download URL is not available"

            self._prepared_data = {
                "org": user.org.name,
                "user": user,
                "url": data.file.url,
                "uuid": str(data.uuid),
                "name": data.name,
                "board_name": action_data.board_name,
            }

        except ResourceDoesNotExistException:
            raise ResourceDoesNotExistsApiException()
        except Exception as e:
            logger.error(f"Error while exporting leads excel: {e}")
            self._prepared_data = {"org": user.org.name, "user": user}

            action_data: LeadExportExcelActionData = self.get_action_data()
            user_entity = OrgUserEntity(
                user_id=action_data.user_id,
                org_id=action_data.org_id,
            )

            download_service = DownloadServiceFactory(user_entity=user_entity).get_service()

            download_service.update_download_status(
                download_id=action_data.download_id,
                progress_status=DownloadProgressStatusEnum.FAILED,
            )

    def get_context_id(self):
        return self.get_action_data().download_id

    def get_push_notification_meta_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        meta_data = super().get_push_notification_meta_data()
        meta_data["url"] = prepared_data.get("url", "")
        meta_data["uuid"] = prepared_data.get("uuid", "")
        meta_data["board_name"] = prepared_data.get("board_name", "")
        meta_data["name"] = prepared_data.get("name", "")
        return meta_data

    def get_redirect_link(self) -> str:
        download_hash_id = HashIdConverter.encode(self.get_context_id())
        return f"my-downloads/?download_id={download_hash_id}"

    def get_recipient_org_id(self) -> int:
        return self.get_action_data().org_id

    def _get_event_recipients(self) -> list[ActionRecipient]:
        user = self.get_prepared_data()["user"]
        return [
            ActionRecipient(
                user_id=user.pk,
                name=user.name,
                email=user.email,
                contact=user.contact,
                org_id=user.org_id,
            )
        ]
