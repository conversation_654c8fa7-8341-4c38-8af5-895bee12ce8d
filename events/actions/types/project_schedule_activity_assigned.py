from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.project_schedule import ProjectScheduleActivitiesAssignedActionData
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.types.project_schedule_completed import ProjectScheduleActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from schedular.models import Events


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED)
class ProjectScheduleActivitiesAssignedActionProcessor(ProjectScheduleActionProcessor):
    _action_data_class = ProjectScheduleActivitiesAssignedActionData
    _event = Events.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED.value
    _whatsapp_template = EventTemplates.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED.value

    def _get_event_recipients(self) -> list[ActionRecipient]:
        action_data: ProjectScheduleActivitiesAssignedActionData = self.get_action_data()
        users = User.objects.filter(id__in=set(action_data.assigned_user_ids))
        recipients = [
            ActionRecipient(
                user_id=user.pk,
                org_id=user.org_id,
                name=user.name,
                contact=user.phone_number.as_e164 if user.phone_number else None,
                email=user.email,
            )
            for user in users
        ]
        return recipients

    def get_whatsapp_data(self):
        data = super().get_whatsapp_data()
        data["recipient_key_name"] = "assignee"
        return data
