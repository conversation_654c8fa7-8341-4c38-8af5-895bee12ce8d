import copy
from datetime import datetime

from authorization.domain.constants import Actions
from common.events import Events
from common.events.actions import ActionRecipient
from common.events.actions.snags import SnagAllotAndCommitTimelineActionData
from common.utils import convert_utc_to_timezone
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.snags_notification_processor import SnagNotifyActionProcessor
from events.actions.types.snag_recipient_selector import (
    ActorData,
    AlloteeData,
    AllotterData,
    AssigneeData,
    AssignorData,
    CreatorData,
    SnagRecipientSelector,
)
from events.domain.mixins import ProjectAndOrgRecipientMixinV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextActions, TemplateContextChoices
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.models import AssignmentTimeline


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_ALLOTEE)
class AlloteeSnagAllotAndTimelineCommitNotifyActionProcessor(SnagNotifyActionProcessor, ProjectAndOrgRecipientMixinV2):
    _action_data_class = SnagAllotAndCommitTimelineActionData
    _event = Events.SNAG_ALLOTTED_POC_AND_COMMITTED_TIMELINE.value
    _push_notification_template = TemplateContextChoices.SNAG_ALLOTTED_AND_TIMELINE_COMMITTED
    _whatsapp_template = EventTemplates.SNAG_ALLOTTED_AND_TIMELINE_COMMITTED.value
    _context = MicroContext.SNAG.value

    def get_context_id(self) -> int:
        return self.get_action_data().snag_id

    def prepare_data(self):
        action_data: SnagAllotAndCommitTimelineActionData = self.get_action_data()
        prepared_data = copy.deepcopy(action_data.__dict__)
        assignment_timeline = (
            AssignmentTimeline.objects.select_related(
                "snag_assignment",
                "snag_assignment__allotted_poc",
                "snag_assignment__snag",
                "snag_assignment__snag__project",
                "snag_assignment__snag__project__client",
                "allotted_poc",
                "created_by",
            )
            .annotate_snag_code()
            .previous_committed_timeline()
            .filter(id=action_data.timeline_id, snag_assignment_id=action_data.snag_assignment_id)
            .first()
        )
        prepared_data["allotee_name"] = None
        if action_data.allotted_to_id or assignment_timeline.snag_assignment.allotted_poc_id:
            if assignment_timeline.allotted_poc:
                prepared_data["allotee_name"] = assignment_timeline.allotted_poc.name
            elif assignment_timeline.snag_assignment.allotted_poc:
                prepared_data["allotee_name"] = assignment_timeline.snag_assignment.allotted_poc.name

            prepared_data["allotted_to_id"] = (
                action_data.allotted_to_id
                if action_data.allotted_to_id
                else assignment_timeline.snag_assignment.allotted_poc_id
            )

        prepared_data["project_name"] = assignment_timeline.snag_assignment.snag.project.name
        prepared_data["project_job_id"] = assignment_timeline.snag_assignment.snag.project.job_id
        prepared_data["snag_code"] = assignment_timeline.code
        prepared_data["snag_title"] = assignment_timeline.snag_assignment.snag.title
        prepared_data["snag_description"] = assignment_timeline.snag_assignment.snag.description
        prepared_data["assigned_by_id"] = assignment_timeline.snag_assignment.created_by_id
        prepared_data["allotter_id"] = assignment_timeline.created_by_id
        prepared_data["allotter_org_id"] = assignment_timeline.created_by.org_id
        prepared_data["allotter_name"] = assignment_timeline.created_by.name
        prepared_data["committed_date"] = (
            assignment_timeline.committed_at
            if assignment_timeline.committed_at
            else assignment_timeline.snag_assignment.committed_at
        )
        prepared_data["previous_committed_timeline"] = assignment_timeline.previous_committed_timeline
        prepared_data["snag_due_date"] = assignment_timeline.snag_assignment.date_of_closure
        prepared_data["snag_created_by_id"] = assignment_timeline.snag_assignment.snag.created_by_id
        prepared_data["link"] = (
            f"project/{HashIdConverter.encode(prepared_data['project_id'])}/snag/"
            f"{HashIdConverter.encode(assignment_timeline.snag_assignment.snag.id)}"
        )
        prepared_data["client_name"] = assignment_timeline.snag_assignment.snag.project.client.name

        self._prepared_data = prepared_data

    def get_recipient_org_id(self):
        prepared_data = self.get_prepared_data()
        return prepared_data.get("allotter_org_id")

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        commit_timeline_text = None
        if prepared_data["committed_date"]:
            committed_date = convert_utc_to_timezone(prepared_data["committed_date"]).strftime("%d %b %Y")
            commit_timeline_text = f"Updated Committed Timeline : *{committed_date}*"
            if prepared_data.get("previous_committed_timeline"):
                previous_committed_timeline = convert_utc_to_timezone(
                    prepared_data["previous_committed_timeline"]
                ).strftime("%d %b %Y")
                commit_timeline_text += f" Previous Committed Timeline : " f" *{previous_committed_timeline})*"

        snag_due_date = (
            datetime.strftime(
                prepared_data["snag_due_date"],
                "%d %b %Y",
            )
            if prepared_data["snag_due_date"]
            else "Not set yet."
        )
        whatsapp_data = {
            "committed_timeline_text": commit_timeline_text if commit_timeline_text else " ",
            "allotted_to_text": "Allotted to - *You*",
            "snag_code": prepared_data.get("snag_code"),
            "snag_title": prepared_data.get("snag_title"),
            "allotter_name": prepared_data.get("allotter_name"),
            "project_name": prepared_data.get("project_name"),
            "project_job_id": prepared_data.get("project_job_id"),
            "snag_due_date": "{date}".format(date=snag_due_date),
            "link": prepared_data["link"],
            "client_name": prepared_data["client_name"],
        }
        return whatsapp_data

    def get_push_notification_meta_data(self):
        prepared_data = self.get_prepared_data()
        meta_data = super().get_push_notification_meta_data()
        meta_data["snag_assignment_id"] = prepared_data.get("snag_assignment_id")
        meta_data["timeline_id"] = self.get_action_data().timeline_id
        meta_data["project_id"] = self.get_action_data().project_id
        return meta_data

    def get_snag_users_data(self):
        prepared_data = self.get_prepared_data()
        assignee_user_recipients = super()._get_event_recipients()
        assignee_user_ids = [data.user_id for data in assignee_user_recipients]
        actor_data_id = prepared_data.get("allotter_id")
        user_id_list = [
            prepared_data.get("snag_created_by_id"),
            prepared_data.get("assigned_by_id"),
            *assignee_user_ids,
            actor_data_id,
        ]
        if prepared_data.get("allotted_to_id"):
            user_id_list.append(prepared_data.get("allotted_to_id"))

        users = active_users_get_using_ids(user_ids=user_id_list).in_bulk()
        creator_id = prepared_data.get("snag_created_by_id")
        assignees_data = [
            AssigneeData(user_id=user.id, org_id=user.org_id)
            for user_id, user in users.items()
            if user_id in assignee_user_ids
        ]
        allotee_data = None
        actor_data = ActorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        allotter_data = AllotterData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        assignors_data = [
            AssignorData(
                user_id=users.get(prepared_data.get("assigned_by_id")).id,
                org_id=users.get(prepared_data.get("assigned_by_id")).org_id,
            )
        ]
        creator_data = CreatorData(user_id=users.get(creator_id).id, org_id=users.get(creator_id).org_id)
        if prepared_data.get("allotted_to_id"):
            allotee_data = AlloteeData(
                user_id=users.get(prepared_data.get("allotted_to_id")).id,
                org_id=users.get(prepared_data.get("allotted_to_id")).org_id,
            )

        return assignors_data, actor_data, creator_data, assignees_data, allotee_data, allotter_data, users

    def _get_event_recipients(self):
        (
            assignors_data,
            actor_data,
            creator_data,
            assignees_data,
            allotee_data,
            allotter_data,
            users,
        ) = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=[creator_data],
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=allotter_data,
            allotee_data=allotee_data,
        )
        target_recipients = recipient_selector.get_target_recipients(
            action=MicroContextActions.ALLOT_USER_AND_COMMIT_TIMELINE
        )

        recipients = []
        for user_data in target_recipients:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS)
class SnagAllotAndTimelineCommitObserverNotifyActionProcessor(AlloteeSnagAllotAndTimelineCommitNotifyActionProcessor):
    def get_whatsapp_data(self):
        whatsapp_data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        whatsapp_data["allotted_to_text"] = (
            f"Allotted to - *{prepared_data.get('allotee_name')}*" if prepared_data.get("allotee_name") else " "
        )

        return whatsapp_data

    def _get_event_recipients(self):
        (
            assignors_data,
            actor_data,
            creator_data,
            assignees_data,
            allotee_data,
            allotter_data,
            users,
        ) = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=[creator_data],
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=allotter_data,
            allotee_data=allotee_data,
        )

        observers = recipient_selector.get_observers(action=MicroContextActions.ALLOT_USER_AND_COMMIT_TIMELINE)

        recipients = []
        for user_data in observers:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients
