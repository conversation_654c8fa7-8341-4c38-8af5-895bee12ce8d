from typing import Dict

from authorization.domain.constants import Actions
from common.events import BaseEventData
from common.events.actions import ActionRecipient
from common.events.actions.comment import CommentMentionNotificationActionData
from common.events.comment import ProjectCommentMentionNotificationEventData
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.comment_action_processor import CommentNotifyActionProcessor
from events.domain.mixins import SingleNotificationMixin, WhatsappNotificationDispatcher
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from notifications.data.models import Notification
from rollingbanners.hash_id_converter import HashIdConverter


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_COMMENT_MENTIONED)
class CommentMentionedActionProcessor(CommentNotifyActionProcessor, SingleNotificationMixin):
    DATA_CLASS = ProjectCommentMentionNotificationEventData
    template = TemplateContextChoices.COMMENT_MENTIONED
    mediums = [WhatsappNotificationDispatcher]
    whatsapp_template = EventTemplates.MENTIONED_IN_COMMENT.value

    def get_meta_data(self, meta_data: ProjectCommentMentionNotificationEventData) -> Dict:
        from commentv2.domain.services.services import prepare_notification_data

        meta_dict = dict()
        meta_dict["user_id"] = meta_data.mentioned_by_id
        (
            meta_dict["comment_preview_text"],
            meta_dict["breadcrumb"],
            meta_dict["project_id"],
            meta_dict["comment_full_text"],
        ) = prepare_notification_data(comment_id=meta_data.comment_id, is_full_text_required=True)
        meta_dict.update({"template": self.template})
        return meta_dict

    def _get_event_recipients(self, meta_data: CommentMentionNotificationActionData, action_type: Actions):
        users = active_users_get_using_ids(user_ids=meta_data.user_id_list).exclude(id=meta_data.mentioned_by_id)
        recipients = []

        for user in users:
            recipients.append(
                ActionRecipient(
                    user_id=user.id,
                    name=user.name,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return list(set(recipients))

    def get_attachments(self, *args, **kwargs):
        return None

    def get_whatsapp_context(self, job_data: list[Notification], action_data: BaseEventData, attachments) -> dict:
        return {
            "project_name": job_data[0].project_name,
            "job_id": job_data[0].project_job_id,
            "comment": job_data[0].meta_data.get("comment_full_text"),
            "user": job_data[0].user_name,
            "link": f"project/{HashIdConverter.encode(job_data[0].project_id)}/detail?comment_id={HashIdConverter.encode(action_data.comment_id)}",
        }
