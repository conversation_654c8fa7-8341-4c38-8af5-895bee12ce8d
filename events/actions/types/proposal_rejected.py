import structlog

from authorization.domain.constants import Actions
from common.events import BaseEventData
from common.events.actions.proposal import ProposalRejectedActionData
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.proposal_action_processor import ProposalSentNotifyActionProcessor
from events.domain.mixins import MultiNotificationMixin, WhatsappNotificationDispatcher
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from notifications.data.models import Notification

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROPOSAL_REJECTED)
class ProposalRejectedActionProcessor(ProposalSentNotifyActionProcessor, MultiNotificationMixin):
    DATA_CLASS = ProposalRejectedActionData
    template = TemplateContextChoices.PROPOSAL_REJECTED
    mediums = [WhatsappNotificationDispatcher]
    whatsapp_template = EventTemplates.PROPOSAL_REJECTED.value

    def get_context_id(self, meta_data: ProposalRejectedActionData) -> int:
        return meta_data.proposal_id

    def get_attachments(self, meta_data: ProposalRejectedActionData):
        return None

    def get_whatsapp_context(self, job_data: list[Notification], action_data: BaseEventData, attachments) -> dict:
        return {
            "project_name": job_data[0].project_name,
            "proposal_for": job_data[0].org_name,
            "ref_no": action_data.proposal_ref_num,
            "user": job_data[0].user_name,
        }

    def get_recipient_org_id(self, action_data: ProposalRejectedActionData) -> int:
        return action_data.vendor_org_id
