from authorization.domain.constants import Actions
from common.events.actions.project_schedule import ProjectScheduleDelayActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.types.project_schedule_overdue import ProjectScheduleOverdueActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROJECT_SCHEDULE_DELAY)
class ProjectScheduleDelayActionProcessor(ProjectScheduleOverdueActionProcessor):
    _action_data_class = ProjectScheduleDelayActionData
    _event = Events.PROJECT_SCHEDULE_DELAY
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.PROJECT_SCHEDULE_DELAY.value
    _whatsapp_template = EventTemplates.PROJECT_SCHEDULE_DELAY.value
