from authorization.domain.constants import Actions
from common.events.actions.project_schedule import ProjectScheduleDelayActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.types.project_schedule_overdue import ProjectScheduleOverdueActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from project_schedule.data.models import ProjectSchedule


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROJECT_SCHEDULE_DELAY)
class ProjectScheduleDelayActionProcessor(ProjectScheduleOverdueActionProcessor):
    _action_data_class = ProjectScheduleDelayActionData
    _event = Events.PROJECT_SCHEDULE_DELAY
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.PROJECT_SCHEDULE_DELAY.value
    _whatsapp_template = EventTemplates.PROJECT_SCHEDULE_DELAY.value

    def prepare_data_from_schedule(self, schedule: ProjectSchedule) -> dict:
        data = super().prepare_data_from_schedule(schedule)
        data["projected_end_date"] = schedule.projected_end_date
        data["planned_end_date"] = schedule.planned_end_date
        data["delay_days"] = schedule.delay_days
        data["progress_percent"] = schedule.completion_percentage
        return data

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        data = super().get_whatsapp_data()
        data["link"] = self.get_redirect_link()
        data["progress_percent"] = prepared_data["progress_percent"]
        return data

    def get_redirect_link(self):
        return self.get_prepared_data().get("link") + "?activity-schedule-delayed"
