from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.recce import RecceSubmittedActionData
from common.events.constants import Events
from core.models import User
from core.selectors import active_user_get
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.recce_action_processor import (
    RecceCallbackBaseActionProcessor,
    RecceNotifyActionProcessorV2,
)
from events.interface.dispatchers import (
    PushNotificationDispatcher,
    WhatsappNotificationDispatcherV2,
)
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from reccev2.data.selectors import get_recce_using_id


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_RECCE_SUBMISSION)
class RecceSubmittedActionProcessor(RecceNotifyActionProcessorV2):
    _action_data_class = RecceSubmittedActionData
    _push_notification_template = TemplateContextChoices.RECCE_SUBMITTED
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.RECCE_SUBMITTED.value
    _event = Events.RECCE_SUBMITTED

    def _get_project_user_queryset(self):
        return super()._get_project_user_queryset().exclude(user_id=self.get_action_data().user_id)

    def get_whatsapp_data(self):
        action_data = self.get_action_data()
        recce = get_recce_using_id(recce_id=action_data.recce_id)
        user = User.objects.filter(id=action_data.user_id).first()
        hours = "NA"
        if recce.submitted_at and recce.started_at:
            difference = recce.submitted_at - recce.started_at
            hours = str((difference.days * 24) + (difference.seconds // 3600))
        return {
            "job_id": recce.project.job_id,
            "store_name": recce.project.name,
            "city": recce.project.store.city.name if recce.project.store.city else "",
            "state": recce.project.store.state.name if recce.project.store.state else "",
            "client_name": recce.project.client.name,
            "link": recce.recce_link,
            "hours": hours,
            "phone_number": user.phone_number.as_e164,
        }


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_RECCE_SUBMISSION_SELF)
class RecceSubmittedSelfActionProcessor(RecceNotifyActionProcessorV2):
    _action_data_class = RecceSubmittedActionData
    _push_notification_template = TemplateContextChoices.RECCE_SUBMITTED
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.RECCE_SUBMITTED_SELF.value
    _event = Events.RECCE_SUBMITTED

    def _get_event_recipients(self) -> list[ActionRecipient]:
        user = active_user_get(user_id=self.get_action_data().user_id)
        return (
            [
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    contact=user.phone_number.as_e164,
                    email=user.email,
                    org_id=user.org_id,
                )
            ]
            if user is not None
            else []
        )

    def get_whatsapp_data(self):
        action_data = self.get_action_data()
        recce = get_recce_using_id(recce_id=action_data.recce_id)
        hours = "NA"
        if recce.submitted_at and recce.started_at:
            difference = recce.submitted_at - recce.started_at
            hours = str((difference.days * 24) + (difference.seconds // 3600))
        return {
            "job_id": recce.project.job_id,
            "store_name": recce.project.name,
            "city": recce.project.store.city.name if recce.project.store.city else "",
            "state": recce.project.store.state.name if recce.project.store.state else "",
            "client_name": recce.project.client.name,
            "link": recce.recce_link,
            "hours": hours,
        }


@BaseActionProcessor.register_action_processor(action=Actions.CALLBACK_RECCE_SUBMITTED)
class RecceSubmittedCallbackActionProcessor(RecceCallbackBaseActionProcessor):
    _action_data_class = RecceSubmittedActionData
    _event = Events.RECCE_SUBMITTED
