import copy
import json
from typing import List

from django.core.serializers.json import Django<PERSON><PERSON><PERSON>nco<PERSON>

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.lead import LeadAssignmentActionData
from common.events.constants import Events
from core.models import User
from crm.data.models import LeadAssignee
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.notify_action_processor import NotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from events.interface.dispatchers import PushNotificationDispatcher, WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.domain.status import Module
from rollingbanners.hash_id_converter import HashIdConverter


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_LEAD_ASSIGNMENT)
class LeadAssignmentNotifyActionProcessor(NotifyActionProcessorV2):
    _context = MicroContextChoices.LEAD
    _module = Module.LEAD
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _push_notification_mode = PushNotificationMode.GROUP
    _action_data_class = LeadAssignmentActionData
    _event = Events.LEAD_ASSIGNED
    _push_notification_template = TemplateContextChoices.LEAD_ASSIGNED
    _whatsapp_template = EventTemplates.LEAD_ASSIGNED.value

    def get_whatsapp_data(self) -> dict:
        data = self.get_prepared_data()
        whatsapp_data = {}
        whatsapp_data["assigner"] = data["assigned_by_name"]
        whatsapp_data["value"] = data["lead_value"]
        whatsapp_data["company_name"] = data["company_text"]
        whatsapp_data["contact_text"] = data["contact_text"]
        whatsapp_data["lead_name"] = data["lead_name"]
        whatsapp_data["board_name"] = data["board_name"]
        whatsapp_data["link"] = data["link"]
        return whatsapp_data

    def get_context_id(self) -> int:
        return self.get_prepared_data().get("lead_id")

    def get_push_notification_meta_data(self):
        prepared_data = self.get_prepared_data()
        meta_data = super().get_push_notification_meta_data()
        meta_data["assigned_by_id"] = prepared_data.get("assigned_by_id")
        meta_data["board_id"] = prepared_data.get("board_id")
        meta_data["lead_name"] = prepared_data.get("lead_name")
        meta_data["board_name"] = prepared_data.get("board_name")
        meta_data["lead_value"] = json.dumps(prepared_data.get("lead_value"), cls=DjangoJSONEncoder)
        return meta_data

    def prepare_data(self):
        action_data: LeadAssignmentActionData = self.get_action_data()
        prepared_data = copy.deepcopy(action_data.__dict__)
        assigness: List[LeadAssignee] = (
            LeadAssignee.objects.filter(lead_id=action_data.lead_id, user_id__in=action_data.assignee_ids)
            .select_related("user", "lead", "lead__board", "lead__company")
            .prefetch_related("lead__contacts__contact")
            .available()
            .all()
        )
        assigned_by = User.objects.filter(id=action_data.assigned_by_id).first()

        lead_name = assigness[0].lead.name
        board_name = assigness[0].lead.board.name
        board_id = assigness[0].lead.board_id
        lead_value = assigness[0].lead.amount if assigness[0].lead.amount else 0
        lead_contact = assigness[0].lead.contacts.first()
        contact = lead_contact.contact if lead_contact else None
        contact_phone = str(contact.phone) if contact else None
        company_name = assigness[0].lead.company.name if assigness[0].lead.company else None
        company_text = f"{company_name}" if company_name else " "
        contact_text = f"{contact_phone}" if contact_phone else " "

        assignees_data = []
        for assignee in assigness:
            assignees_data.append(
                {
                    "name": assignee.user.name,
                    "email": assignee.user.email,
                    "contact": assignee.user.contact,
                    "assignee_id": assignee.user.pk,
                    "org_id": assignee.user.org_id,
                }
            )
        prepared_data.update(
            {
                "lead_name": lead_name,
                "board_name": board_name,
                "lead_value": lead_value,
                "assignees": assignees_data,
                "board_id": board_id,
                "assigned_by_name": assigned_by.name,
                "link": (
                    f"leads-management/my-leads/board/{HashIdConverter.encode(board_id)}/lead"
                    f"/{HashIdConverter.encode(action_data.lead_id)}/details"
                ),
                "company_text": company_text,
                "contact_text": contact_text,
            }
        )
        self._prepared_data = prepared_data

    def _get_event_recipients(self):
        prepared_data = self.get_prepared_data()
        assignees_data = prepared_data.get("assignees")
        recipients = []
        for user in assignees_data:
            recipients.append(
                ActionRecipient(
                    user_id=user.get("assignee_id"),
                    name=user.get("name"),
                    email=user.get("email"),
                    contact=user.get("contact"),
                    org_id=user.get("org_id"),
                )
            )
        return recipients
