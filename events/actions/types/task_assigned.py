from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskAssignedActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_ASSIGNED)
class TaskAssignedNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_template = TemplateContextChoices.TASK_ASSIGNED.value
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _whatsapp_template = EventTemplates.TASK_ASSIGNED.value
    _action_data_class = TaskAssignedActionData
    _event = Events.TASK_CREATED

    def _check_task_data(self, task_data: dict):
        super()._check_task_data(task_data)
        action_data: TaskAssignedActionData = self.get_action_data()
        if action_data.old_assignee_id == task_data.get("task_assignee_id"):
            raise self.JobAbortException("Task assignee not changed, no need to send assinged notification")

    def _get_event_recipients(self) -> list[ActionRecipient]:
        prepared_data = self.get_prepared_data()
        recipients = []
        if prepared_data.get("assignee_id") != prepared_data.get("creator_id"):
            recipients.append(
                ActionRecipient(
                    user_id=prepared_data.get("assignee_id"),
                    name=prepared_data.get("assignee_data").get("name"),
                    contact=prepared_data.get("assignee_data").get("phone_number"),
                    email=prepared_data.get("assignee_data").get("email"),
                    org_id=prepared_data.get("assignee_data").get("org_id"),
                )
            )
        return recipients
