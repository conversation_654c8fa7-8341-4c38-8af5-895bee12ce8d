from urllib.parse import urlsplit

from django.db.models import QuerySet

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.recce import RecceLinkCreatedActionData
from common.events.constants import Events
from core.models import OrganizationConfigRole
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.recce_action_processor import RecceNotifyActionProcessorV2
from events.interface.dispatchers import (
    PushNotificationDispatcher,
    WhatsappNotificationDispatcherV2,
)
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from project.data.models import ProjectUser
from project.domain.helpers import ProjectPermissionHelper
from reccev2.data.models import Recce


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_RECCE_LINK_CREATION)
class RecceLinkCreatedActionProcessorV2(RecceNotifyActionProcessorV2):
    _action_data_class = RecceLinkCreatedActionData
    _push_notification_template = TemplateContextChoices.RECCE_LINK_CREATED
    _dispatcher_classes = [PushNotificationDispatcher]
    _whatsapp_template = EventTemplates.RECCE_LINK_CREATED.value
    _event = Events.RECCE_LINK_CREATED

    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        return (
            super()
            ._get_project_user_queryset()
            .exclude(
                role_id__in=OrganizationConfigRole.objects.filter(
                    organization_config_id=self.get_action_data().org_id, is_recce_role=True
                ).values_list("role_id", flat=True)
            )
        )

    def _get_recce_assignees_recipients(self) -> list[ActionRecipient]:
        action_data = self.get_action_data()
        recipients = []
        users = active_users_get_using_ids(user_ids=self.get_action_data().recce_user_ids)
        for user in users:
            if ProjectPermissionHelper.is_action_permitted(
                project_id=action_data.project_id,
                org_id=action_data.org_id,
                user_id=user.pk,
                action=self.get_action(),
            ):
                recipients.append(
                    ActionRecipient(
                        user_id=user.pk,
                        contact=user.phone_number.as_e164 if user.phone_number else None,
                        name=user.name,
                        email=user.email,
                        org_id=user.org_id,
                    )
                )
        return recipients

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = self._get_event_recipients_from_project_user()
        recipients.extend(self._get_recce_assignees_recipients())
        return list(set(recipients))


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_RECCE_ASSIGNED)
class RecceAssignedActionProcessor(RecceLinkCreatedActionProcessorV2):
    _dispatcher_classes = [WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.RECCE_LINK_CREATED.value

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        users = active_users_get_using_ids(user_ids=self.get_action_data().recce_user_ids)
        for user in users:
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    contact=user.phone_number.as_e164 if user.phone_number else None,
                    name=user.name,
                    email=user.email,
                    org_id=user.org_id,
                )
            )
        return recipients

    def get_whatsapp_data(self):
        recce = (
            Recce.objects.filter(id=self.get_action_data().recce_id)
            .select_related("project", "project__store__city", "project__store__state", "project__client")
            .available()
            .first()
        )
        url_parts = urlsplit(recce.recce_link)
        recce_link = url_parts.path + "?" + url_parts.query
        return {
            "recce_link": recce_link,
            "job_id": recce.project.job_id,
            "store_name": recce.project.name,
            "city": recce.project.store.city.name if recce.project.store.city else "",
            "state": recce.project.store.state.name if recce.project.store.state else "",
            "client_name": recce.project.client.name,
        }
