from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.comment import CommentApprovalMentionedActionData
from common.events.constants import Events
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.comment_action_processor import CommentApprovalNotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_COMMENT_APPROVAL_MENTIONED)
class CommentApprovalMentionedActionProcessor(CommentApprovalNotifyActionProcessorV2):
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.COMMENT_APPROVAL_MENTIONED
    _whatsapp_template = EventTemplates.COMMENT_APPROVAL_MENTIONED.value
    _action_data_class = CommentApprovalMentionedActionData
    _event = Events.PROJECT_COMMENT_APPROVAL_REQUESTED

    def _get_event_recipients(self):
        mentioned_user_ids = self.get_prepared_data().get("mentioned_user_ids")
        users = User.objects.filter(id__in=mentioned_user_ids, is_active=True, deleted_at__isnull=True)
        recipients = []
        for user in users:
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients

    def get_whatsapp_data(self):
        data = super().get_whatsapp_data()
        data["link"] = self.get_prepared_data().get("link")
        return data
