from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskMentionedActionData
from common.events.constants import Events
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from task.domain.utils import mentioned_user_ids_get_by_description


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_MENTIONED)
class TaskMentionedNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.TASK_MENTIONED.value
    _whatsapp_template = EventTemplates.TASK_MENTIONED.value
    _action_data_class = TaskMentionedActionData
    _event = Events.TASK_CREATED

    def prepare_data(self):
        task_data = self._get_task_data()
        self._check_task_data(task_data)
        prepared_data = self._prepare_data_from_task_data(task_data)
        mentioned_user_ids = mentioned_user_ids_get_by_description(description=task_data.get("description"))
        if prepared_data.get("assignee_id") in mentioned_user_ids:
            mentioned_user_ids.remove(prepared_data.get("assignee_id"))
        prepared_data["mentioned_user_ids"] = mentioned_user_ids
        self._prepared_data = prepared_data

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        meta_data["mentioned_user_ids"] = self.get_prepared_data().get("mentioned_user_ids")
        return meta_data

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        data["assignee_name"] = self._get_assignee_name()
        data["mentioner_name"] = (
            prepared_data.get("updator_name")
            if prepared_data.get("updator_name")
            else prepared_data.get("creator_name")
        )
        return data

    def _get_event_recipients(self) -> list[ActionRecipient]:
        recipients = []
        mentioned_user_ids = self.get_prepared_data().get("mentioned_user_ids")
        users = active_users_get_using_ids(user_ids=mentioned_user_ids)
        for user in users:
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients
