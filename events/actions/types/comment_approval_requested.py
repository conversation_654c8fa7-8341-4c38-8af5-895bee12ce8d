from typing import Dict

from django.conf import settings

from authorization.domain.constants import Actions
from common.events import BaseEventAttachmentData, BaseEventData
from common.events.actions import ActionRecipient
from common.events.actions.comment import (
    CommentApprovalBaseActionData,
    CommentApprovalRequestedActionData,
    CommentApprovalRequestNotificationActionData,
)
from common.events.comment import ProjectCommentApprovalRequestEventData
from common.events.constants import Events
from core.models import OneTimeUseToken, User
from core.services import OneTimeUseApiServices
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.comment_action_processor import CommentApprovalNotifyActionProcessorV2, CommentNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from events.domain.mixins import (
    SingleNotificationMixin,
    WhatsappNotificationDispatcher,
    WhatsappNotificationMixin,
)
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from notifications.data.models import Notification
from project.domain.status import Module
from rollingbanners.hash_id_converter import HashIdConverter


# @BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_COMMENT_APPROVAL_REQUESTED)
class CommentApprovalRequestActionProcessor(
    CommentNotifyActionProcessor, SingleNotificationMixin, WhatsappNotificationMixin
):
    DATA_CLASS = ProjectCommentApprovalRequestEventData
    template = TemplateContextChoices.COMMENT_APPROVAL_REQUESTED
    mediums = [WhatsappNotificationDispatcher]
    whatsapp_template = EventTemplates.COMMENT_APPROVAL_REQUESTED.value

    def _get_event_recipients(self, meta_data: CommentApprovalBaseActionData, action_type: Actions):
        obj = User.objects.get(id=meta_data.approver_id)
        return [
            ActionRecipient(
                user_id=meta_data.approver_id,
                name=obj.name,
                contact=obj.contact,
                org_id=obj.org_id,
            )
        ]

    def get_meta_data(self, meta_data: CommentApprovalBaseActionData) -> Dict:
        from commentv2.domain.services.services import prepare_notification_data

        meta_dict = dict()
        meta_dict["user_id"] = meta_data.mentioned_by_id
        (
            meta_dict["comment_preview_text"],
            meta_dict["breadcrumb"],
            meta_dict["project_id"],
            meta_dict["comment_full_text"],
        ) = prepare_notification_data(comment_id=meta_data.comment_id, is_full_text_required=True)
        meta_dict.update({"template": self.template})
        return meta_dict

    def get_attachments(self, meta_data: BaseEventData) -> list[BaseEventAttachmentData]:
        return None

    def get_whatsapp_context(
        self,
        job_data: list[Notification],
        action_data: CommentApprovalRequestNotificationActionData,
        attachments,
    ) -> Dict:
        token: OneTimeUseToken = OneTimeUseApiServices.generate_tokens(
            context=Module.COMMENT, context_id=action_data.comment_id, user_ids=[action_data.approver_id]
        )[0]
        hash_comment_id = HashIdConverter.encode(action_data.comment_id)

        return {
            "project_name": job_data[0].project_name,
            "user": job_data[0].user_name,
            "job_id": job_data[0].project_job_id,
            "comment": job_data[0].meta_data.get("comment_full_text"),
            "approval_link": f"{settings.DASHBOARD_URL}/comment/{hash_comment_id}/approved?token={token.hash_id}",
            "rejection_link": f"{settings.DASHBOARD_URL}/comment/{hash_comment_id}/rejected?token={token.hash_id}",
            "landing_context": self.get_landing_context(job_data=job_data[0]),
        }


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_COMMENT_APPROVAL_REQUESTED)
class CommentApprovalRequestedActionProcessorV2(CommentApprovalNotifyActionProcessorV2):
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _push_notification_template = TemplateContextChoices.COMMENT_APPROVAL_REQUESTED_V2
    _whatsapp_template = EventTemplates.COMMENT_APPROVAL_REQUESTED.value
    _action_data_class = CommentApprovalRequestedActionData
    _event = Events.PROJECT_COMMENT_APPROVAL_REQUESTED

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        data = super().get_whatsapp_data()
        data["job_id"] = prepared_data.get("job_id")
        data["link"] = self.get_redirect_link()
        return data

    def get_redirect_link(self):
        return self.get_prepared_data().get("link") + "?task-approval-requested"

    def _get_event_recipients(self):
        approver: User = self.get_prepared_data().get("approver")
        return [
            ActionRecipient(
                user_id=approver.pk,
                name=approver.name,
                contact=approver.contact,
                email=approver.email,
                org_id=approver.org_id,
            )
        ]
