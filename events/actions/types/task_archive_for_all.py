from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskArchiveForAllActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_ARCHIVED_FOR_ALL)
class TaskArchivedForAllNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.TASK_ARCHIVED_FOR_ALL.value
    _whatsapp_template = EventTemplates.TASK_ARCHIVED_FOR_ALL.value
    _action_data_class = TaskArchiveForAllActionData
    _event = Events.TASK_ARCHIVED_FOR_ALL

    def _get_due_at(self) -> str:
        prepared_data = self.get_prepared_data()
        return self._get_formatted_date(prepared_data.get("due_at"))

    def _get_event_recipients(self) -> list[ActionRecipient]:
        prepared_data = self.get_prepared_data()
        recipients = []
        if prepared_data.get("assignee_data") is not None:
            recipients.append(
                ActionRecipient(
                    user_id=prepared_data.get("assignee_id"),
                    name=prepared_data.get("assignee_data").get("name"),
                    contact=prepared_data.get("assignee_data").get("phone_number"),
                    email=prepared_data.get("assignee_data").get("email"),
                    org_id=prepared_data.get("assignee_data").get("org_id"),
                )
            )
        return recipients

    def _get_assignee_name(self):
        return self._get_current_assignee_name()

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        data["link"] = self.get_redirect_link()
        return data

    def get_redirect_link(self):
        return self.get_prepared_data().get("link") + "?task-archived"
