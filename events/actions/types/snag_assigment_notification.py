import copy
from datetime import datetime

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.snags import SnagAssignedActionData
from common.events.constants import Events
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.snags_notification_processor import SnagNotifyActionProcessor
from events.actions.types.snag_recipient_selector import (
    ActorData,
    AssigneeData,
    AssignorData,
    CreatorData,
    SnagRecipientSelector,
)
from events.domain.mixins import ProjectAndOrgRecipientMixinV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextActions, TemplateContextChoices
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.models import SnagAssignment


class BaseSnagAssignmentNotificationProcessor(SnagNotifyActionProcessor, ProjectAndOrgRecipientMixinV2):
    _action_data_class = SnagAssignedActionData
    _event = Events.SNAG_ASSIGNED.value
    _push_notification_template = TemplateContextChoices.SNAG_ASSIGNED
    _whatsapp_template = EventTemplates.SNAG_ASSIGNED.value
    _context = MicroContext.SNAG.value

    def get_context_id(self) -> int:
        return self.get_action_data().snag_ids[0]

    def get_recipient_org_id(self):
        return self.get_action_data().assigned_to_org_id

    def prepare_data(self):
        action_data: SnagAssignedActionData = self.get_action_data()
        snag_assignment = (
            SnagAssignment.objects.filter(
                snag_id__in=action_data.snag_ids,
                org_from_id=action_data.assigned_by_org_id,
                org_to_id=action_data.assigned_to_org_id,
            )
            .annotate_snag_code()
            .select_related(
                "snag",
                "snag__created_by",
                "created_by",
                "org_from",
                "org_to",
                "snag__project",
                "snag__project__client",
            )
            .first()
        )

        prepared_data = copy.deepcopy(action_data.__dict__)
        prepared_data["snag_assignment_id"] = snag_assignment.id
        prepared_data["project_id"] = snag_assignment.snag.project_id
        prepared_data["project_name"] = snag_assignment.snag.project.name
        prepared_data["project_job_id"] = snag_assignment.snag.project.job_id
        prepared_data["snag_code"] = snag_assignment.code
        prepared_data["snag_title"] = snag_assignment.snag.title
        prepared_data["snag_description"] = snag_assignment.snag.description
        prepared_data["assigned_by_org_name"] = snag_assignment.org_from.name
        prepared_data["assigned_to_org_name"] = snag_assignment.org_to.name
        prepared_data["assigned_by_name"] = snag_assignment.created_by.name
        prepared_data["assigned_by_id"] = snag_assignment.created_by_id
        prepared_data["snag_creator_id"] = snag_assignment.snag.created_by_id
        prepared_data["snag_creator_org_id"] = snag_assignment.snag.created_by.org_id
        prepared_data["snag_due_date"] = snag_assignment.date_of_closure
        prepared_data["link"] = (
            f"project/{HashIdConverter.encode(prepared_data['project_id'])}/snag"
            f"/{HashIdConverter.encode(snag_assignment.snag_id)}"
        )
        prepared_data["client_name"] = snag_assignment.snag.project.client.name

        self._prepared_data = prepared_data

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        snag_due_date = (
            datetime.strftime(
                prepared_data["snag_due_date"],
                "%d %b %Y",
            )
            if prepared_data.get("snag_due_date")
            else "Not set yet."
        )
        whatsapp_data = {
            "snag_code": prepared_data.get("snag_code"),
            "snag_title": prepared_data.get("snag_title"),
            "assigned_by_org_name": prepared_data.get("assigned_by_org_name"),
            "assigned_to_org_name": prepared_data.get("assigned_to_org_name"),
            "assigned_by_name": prepared_data.get("assigned_by_name"),
            "project_name": prepared_data.get("project_name"),
            "project_job_id": prepared_data.get("project_job_id"),
            "snag_due_date": "{date}".format(date=snag_due_date),
            "link": prepared_data["link"],
            "client_name": prepared_data["client_name"],
        }
        return whatsapp_data

    def get_push_notification_meta_data(self):
        prepared_data = self.get_prepared_data()
        meta_data = super().get_push_notification_meta_data()
        meta_data["snag_assignment_id"] = prepared_data.get("snag_assignment_id")
        meta_data["project_id"] = prepared_data.get("project_id")
        meta_data["assigned_by_org_id"] = prepared_data.get("assigned_by_org_id")

        return meta_data

    def get_snag_users_data(self):
        prepared_data = self.get_prepared_data()
        assignee_user_recipients = super()._get_event_recipients()
        assignee_user_ids = [data.user_id for data in assignee_user_recipients]

        users = active_users_get_using_ids(
            user_ids=[prepared_data.get("snag_creator_id"), prepared_data.get("assigned_by_id"), *assignee_user_ids]
        ).in_bulk()
        actor_data_id = prepared_data.get("assigned_by_id")
        creator_id = prepared_data.get("snag_creator_id")
        assignees_data = [
            AssigneeData(user_id=user.id, org_id=user.org_id)
            for user_id, user in users.items()
            if user_id in assignee_user_ids
        ]

        actor_data = ActorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        assignors_data = [AssignorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)]
        creator_data = CreatorData(user_id=users.get(creator_id).id, org_id=users.get(creator_id).org_id)
        return assignors_data, actor_data, creator_data, assignees_data, users


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_ASSIGNMENT_TO_CREATOR)
class SnagAssignmentNotifyActionProcessor(BaseSnagAssignmentNotificationProcessor):
    def get_whatsapp_data(self):
        whatsapp_data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        action_data = self.get_action_data()
        assignor_name = prepared_data.get("assigned_by_name")
        if action_data.assigned_by_org_id == action_data.assigned_to_org_id:
            assignee_name = "your organization"

        else:
            assignee_name = prepared_data.get("assigned_to_org_name")

        message_text = "{assignee_org_name} by {assigner_name}".format(
            assignee_org_name=assignee_name, assigner_name=assignor_name
        )
        whatsapp_data["message_text"] = message_text

        return whatsapp_data

    def _get_event_recipients(self):
        assignors_data, actor_data, creator_data, assignees_data, users = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=[creator_data],
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=None,
            allotee_data=None,
        )
        observers = recipient_selector.get_observers(action=MicroContextActions.ASSIGN)
        recipients = []
        for user_data in observers:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_ASSIGNMENT_TO_ASSIGNEE)
class AssigneeSnagAssignmentNotifyActionProcessor(BaseSnagAssignmentNotificationProcessor):
    def get_whatsapp_data(self):
        whatsapp_data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        action_data = self.get_action_data()

        assignee_name = "your organization"
        if action_data.assigned_by_org_id == action_data.assigned_to_org_id:
            assignor_name = prepared_data.get("assigned_by_name")
        else:
            assignor_name = prepared_data.get("assigned_by_org_name")

        message_text = "{assignee_org_name} by {assigner_name}".format(
            assignee_org_name=assignee_name, assigner_name=assignor_name
        )
        whatsapp_data["message_text"] = message_text

        return whatsapp_data

    def _get_event_recipients(self):
        assignors_data, actor_data, creator_data, assignees_data, users = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=[creator_data],
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=None,
            allotee_data=None,
        )

        observers = recipient_selector.get_target_recipients(action=MicroContextActions.ASSIGN)
        recipients = []
        for user_data in observers:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients
