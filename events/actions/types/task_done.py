from rest_framework import serializers

from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.task import TaskDoneActionData
from common.events.constants import Events
from common.serializers import BaseSerializer
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.callback_action_processor import CallbackActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from events.domain.mixins import SystemCallbackMixin
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from task.data.choices import TaskAssignedAs
from task.data.models import TaskAssignment


@BaseActionProcessor.register_action_processor(action=Actions.CALLBACK_TASK_DONE)
class TaskDoneCallbackActionProcessor(CallbackActionProcessor, SystemCallbackMixin):
    class TaskSerializer(BaseSerializer):
        task_id = serializers.IntegerField()
        user_id = serializers.IntegerField()
        org_id = serializers.IntegerField()

        class Meta:
            ref_name = "TaskSerializer"

    _action_data_class = TaskDoneActionData
    _event = Events.TASK_DONE
    _serializer_class = TaskSerializer

    def prepare_data(self):
        action_data: TaskDoneActionData = self.get_action_data()
        return {"task_id": action_data.task_id, "user_id": action_data.user_id, "org_id": action_data.org_id}


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_DONE)
class TaskDoneNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.TASK_DONE.value
    _whatsapp_template = EventTemplates.TASK_DONE.value
    _action_data_class = TaskDoneActionData
    _event = Events.TASK_CREATED

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        action_data = self.get_action_data()
        prepared_data = self.get_prepared_data()
        data["assignee_name"] = self._get_current_assignee_name()
        if action_data.user_id == prepared_data.get("creator_id"):
            data["done_by_name"] = prepared_data.get("creator_name")
        else:
            data["done_by_name"] = self._get_current_assignee_name()
        return data

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        meta_data["done_by_id"] = self.get_action_data().user_id
        return meta_data

    def _check_task_data(self, task_data: dict):
        super()._check_task_data(task_data)
        action_data: TaskDoneActionData = self.get_action_data()
        if action_data.user_id == task_data.get("created_by_id"):
            if task_data.get("created_by_id") == task_data.get("task_assignee_id"):
                raise self.JobAbortException("Task creator done the task, no need to send notification")
            assignment = TaskAssignment.objects.filter(
                task_id=task_data.get("task_id"),
                assignee_id=task_data.get("task_assignee_id"),
                assigned_as=TaskAssignedAs.ASSIGNEE,
            ).first()
            if assignment and assignment.done_at:
                raise self.JobAbortException(
                    "Task creator mark done and assignee already marked done task, no need to send notification"
                )
        if action_data.user_id not in [task_data.get("task_assignee_id"), task_data.get("created_by_id")]:
            raise self.JobAbortException("Task is not done by assignee, no need to send notification")

    def _get_event_recipients(self) -> list[ActionRecipient]:
        prepared_data = self.get_prepared_data()
        action_data: TaskDoneActionData = self.get_action_data()
        if action_data.user_id == prepared_data.get("creator_id"):
            user_ids = TaskAssignment.objects.filter(
                task_id=prepared_data.get("task_id"), assigned_as__in=[TaskAssignedAs.ASSIGNEE, TaskAssignedAs.MENTIONS]
            ).values_list("assignee_id", flat=True)
            users = active_users_get_using_ids(user_ids=user_ids)
            return [
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    contact=user.contact,
                    email=user.email,
                    org_id=user.org_id,
                )
                for user in users
            ]
        else:
            return [
                ActionRecipient(
                    user_id=prepared_data.get("creator_id"),
                    name=prepared_data.get("creator_name"),
                    contact=prepared_data.get("creator_contact"),
                    email=prepared_data.get("creator_email"),
                    org_id=prepared_data.get("creator_org_id"),
                )
            ]

    def _get_due_at(self) -> str:
        prepared_data = self.get_prepared_data()
        return self._get_formatted_date(prepared_data.get("due_at"))

    def _get_assignee_name(self):
        return self._get_current_assignee_name()
