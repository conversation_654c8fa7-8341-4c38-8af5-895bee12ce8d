from authorization.domain.constants import Actions
from common.events.actions.base import ActionRecipient
from common.events.actions.schedule import ScheduledJobActionData
from common.events.constants import Events
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.task_action_processor import TaskNotifyActionProcessor
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_TASK_REMINDER)
class TaskReminderNotifyActionProcessor(TaskNotifyActionProcessor):
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _push_notification_template = TemplateContextChoices.TASK_REMINDER.value
    _whatsapp_template = EventTemplates.TASK_REMINDER.value
    _action_data_class = ScheduledJobActionData
    _event = Events.TASK_REMINDER

    def get_context_id(self) -> int:
        return self.get_action_data().context_id

    def prepare_data(self):
        action_data: ScheduledJobActionData = self.get_action_data()
        super().prepare_data()
        user = User.objects.filter(id=action_data.user_id, is_active=True, deleted_at__isnull=True).first()
        self._prepared_data["user"] = user

    def get_push_notification_meta_data(self) -> dict:
        meta_data = super().get_push_notification_meta_data()
        meta_data["user_id"] = self.get_action_data().user_id
        return meta_data

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        data["assignee_name"] = self._get_current_assignee_name()
        data["link"] = self.get_redirect_link()
        return data

    def get_redirect_link(self) -> str:
        return self.get_prepared_data().get("link") + "?task-reminder"

    def _check_task_data(self, task_data: dict):
        pass

    def _get_event_recipients(self) -> list[ActionRecipient]:
        prepared_data = self.get_prepared_data()
        user = prepared_data["user"]
        return [
            ActionRecipient(
                user_id=user.pk,
                name=user.name,
                email=user.email,
                contact=user.contact,
                org_id=user.org_id,
            )
        ]

    def _get_due_at(self) -> str:
        prepared_data = self.get_prepared_data()
        return self._get_formatted_date(prepared_data.get("due_at"))

    def _get_assignee_name(self):
        return self._get_current_assignee_name()
