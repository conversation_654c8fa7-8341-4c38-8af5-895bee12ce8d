import structlog

from authorization.domain.constants import Actions
from common.events import BaseEventData
from common.events.actions.proposal import ProposalSentActionData
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.proposal_action_processor import ProposalSentNotifyActionProcessor
from events.domain.mixins import MultiNotificationMixin, WhatsappNotificationDispatcher
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from notifications.data.models import Notification
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROPOSAL_SENT)
class ProposalSentActionProcessor(ProposalSentNotifyActionProcessor, MultiNotificationMixin):
    DATA_CLASS = ProposalSentActionData
    template = TemplateContextChoices.PROPOSAL_SENT
    mediums = [WhatsappNotificationDispatcher]
    whatsapp_template = EventTemplates.PROPOSAL_SENT.value

    def get_context_id(self, meta_data: ProposalSentActionData) -> int:
        return meta_data.proposal_id

    def get_attachments(self, meta_data: ProposalSentActionData):
        return None

    def get_whatsapp_context(self, job_data: list[Notification], action_data: BaseEventData, attachments) -> dict:
        return {
            "project_name": job_data[0].project_name,
            "job_id": job_data[0].project_job_id,
            "created_at": action_data.created_at,
            "proposal_from": job_data[0].org_name,
            "user": job_data[0].user_name,
            "ref_no": action_data.proposal_ref_num,
            "total_items": action_data.total_items,
            "amount": action_data.amount,
            "recipient_key_name": "project_user",
            "link": f"project/{HashIdConverter.encode(action_data.project_id)}/boq/proposal-for-client/{action_data.proposal_id}",
        }

    def get_recipient_org_id(self, action_data: ProposalSentActionData) -> int:
        return action_data.client_org_id
