from authorization.domain.constants import Actions
from common.events.actions.recce import RecceStartedActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.recce_action_processor import RecceNotifyActionProcessorV2
from events.interface.dispatchers import WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from reccev2.data.selectors import get_recce_using_id


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_RECCE_STARTED)
class RecceStartedActionProcessor(RecceNotifyActionProcessorV2):
    _action_data_class = RecceStartedActionData
    _dispatcher_classes = [WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.RECCE_STARTED.value
    _event = Events.RECCE_STARTED

    def get_whatsapp_data(self):
        action_data = self.get_action_data()
        recce = get_recce_using_id(recce_id=action_data.recce_id)
        return {
            "job_id": recce.project.job_id,
            "store_name": recce.project.name,
            "city": recce.project.store.city.name if recce.project.store.city else "",
            "state": recce.project.store.state.name if recce.project.store.state else "",
            "client_name": recce.project.client.name,
        }
