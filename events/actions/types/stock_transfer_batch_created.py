import structlog
from django.db.models import QuerySet

from authorization.domain.constants import Actions
from common.events.actions.materials import InventoryTransferBatchCreateActionData
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.materials_action_processor import MaterialsNotifyActionProcessor
from events.domain.mixins import ProjectRecipientMixinV2
from external_services.messengers.whatsapp.entities import EventTemplates
from inventory.data.models import InventoryTransferBatch
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.data.models import ProjectUser
from rollingbanners.hash_id_converter import HashIdConverter as Hc

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_STOCK_TRANSFER_BATCH_CREATED)
class TransferBatchCreatedActionProcessor(MaterialsNotifyActionProcessor, ProjectRecipientMixinV2):
    _context = MicroContextChoices.INVENTORY_BATCH
    _action_data_class = InventoryTransferBatchCreateActionData
    _push_notification_template = TemplateContextChoices.INVENTORY_TRANSFER_BATCH_CREATED
    _whatsapp_template = EventTemplates.INVENTORY_TRANSFER_BATCH_CREATED.value
    _event = Events.STOCK_TRANSFER_BATCH_CREATED

    def prepare_data(self):
        action_data: InventoryTransferBatchCreateActionData = self.get_action_data()
        super().prepare_data()
        prepared_data = self._prepared_data
        prepared_data["link"] = (
            f"project/{Hc.encode(action_data.receiver_project_id)}/work-report/materials/received-stocks/{Hc.encode(action_data.batch_id)}"  # noqa
        )
        self._prepared_data = prepared_data

    def _get_project_user_queryset(self) -> QuerySet[ProjectUser]:
        action_data: InventoryTransferBatchCreateActionData = self.get_action_data()
        batch = InventoryTransferBatch.objects.filter(id=action_data.batch_id).first()
        project_users = (
            ProjectUser.objects.filter(project_id=batch.receiver_project_id, user__org_id=batch.organization_id)
            .select_related("role", "project", "user")
            .filter(user__is_active=True, user__deleted_at__isnull=True)
        )
        return project_users
