import structlog

from authorization.domain.constants import Actions
from common.events.actions.design import DesignNewVersionUploadedActionData
from common.events.constants import Events
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.design_action_processor import DesignNotifyActionProcessor
from events.interface.dispatchers import (
    PushNotificationDispatcher,
    WhatsappNotificationDispatcherV2,
)
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextChoices, TemplateContextChoices
from project.data.models import Project

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_DESIGN_NEW_VERSION_UPLOADED)
class DesignApprovedInternalActionProcessor(DesignNotifyActionProcessor):
    _action_data_class = DesignNewVersionUploadedActionData
    _push_notification_template = TemplateContextChoices.DESIGN_FILE_NEW_VERSION_UPLOADED
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.DESIGN_FILE_NEW_VERSION.value
    _event = Events.DESIGN_NEW_VERSION_UPLOADED
    _context = MicroContextChoices.DESIGN_FILE.value

    def get_context_id(self):
        return self.get_action_data().file_id

    def get_attachments(self, *args, **kwargs):
        return None

    def prepare_data(self):
        super().prepare_data()
        self._prepared_data["project"] = Project.objects.get(id=self._prepared_data.get("project_id"))
        self._prepared_data["user"] = User.objects.get(id=self._prepared_data.get("user_id"))

    def get_whatsapp_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        return {
            "project_name": prepared_data["project"].name,
            "job_id": prepared_data["project"].job_id,
            "user": prepared_data["user"].name,
            "design_file_name": prepared_data["design_file_name"],
            "version": prepared_data["file_version"],
        }


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED)
class DesignApprovedExternalActionProcessor(DesignApprovedInternalActionProcessor):
    _push_notification_template = TemplateContextChoices.DESIGN_FILE_NEW_VERSION_POST_FREEZE_UPLOADED
    _whatsapp_template = EventTemplates.DESIGN_FILE_NEW_VERSION_POST_FREEZE.value
    _event = Events.DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED
