from typing import Dict, List

import pytz
import structlog
from django.db.models import Prefetch

from authorization.domain.constants import Actions
from common.element_base.services import ElementCodeService
from common.events.actions.order import OrderEmailActionData, OrderModifyEmailActionData
from common.events.base import BaseEventAttachmentData
from common.helpers import get_login_url
from common.utils import formatINR
from core.models import Organization, User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.order_action_processor import OrderEmailActionProcessor
from order.data.models import OrderElement<PERSON>napshot, OrderSnapshot, VendorOrder, VendorOrderElement
from order.domain.constants import OrderStatusEnum
from order.domain.services.helper_service import is_order_flow_default
from project.domain.caches import ProjectCountryConfigCache

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_ORDER_MODIFY_WITHOUT_PROPOSAL)
class OrderModifyEmailActionProcessor(OrderEmailActionProcessor):
    DATA_CLASS = OrderModifyEmailActionData
    html_email_template_name = "order/purchase_intent_modified.html"

    def get_attachments(self, action_data: OrderEmailActionData) -> List[BaseEventAttachmentData]:
        vendor_order = VendorOrder.objects.filter(id=action_data.order_id).values("email_data").first()
        if vendor_order:
            return vendor_order["email_data"].get("attachments", [])
        return []

    def _can_allow_email_dispatch(self, action_data: OrderEmailActionData) -> bool:
        order = VendorOrder.objects.filter(id=action_data.order_id).first()
        if self.is_org_blacklisted(order=order):
            return False
        if is_order_flow_default(order=order):
            return True
        return False

    def _find_changes_in_elements(
        self, order_elements: List[VendorOrderElement], snapshot_elements: List[OrderElementSnapshot]
    ):
        snapshot_elements_mapping: Dict[str, OrderElementSnapshot] = {}
        for element in snapshot_elements:
            element_code = ElementCodeService.get_code(
                serial_number=element.serial_number,
                custom_type=element.custom_type,
                code=element.code,
                version=element.boq_element_version,
            )
            snapshot_elements_mapping[element_code] = element

        to_add = 0
        to_update = 0
        to_delete = 0
        for element in order_elements:
            element_code = ElementCodeService.get_code(
                serial_number=element.serial_number,
                custom_type=element.custom_type,
                code=element.code,
                version=element.boq_element_version,
            )
            if element_code not in snapshot_elements_mapping:
                to_add += 1
            elif element_code in snapshot_elements_mapping and element.status == OrderStatusEnum.CANCELLED:
                to_delete += 1
            elif element_code in snapshot_elements_mapping:
                if (
                    element.quantity != snapshot_elements_mapping[element_code].quantity
                    or element.vendor_rate != snapshot_elements_mapping[element_code].vendor_rate
                    or element.quantity_dimensions != snapshot_elements_mapping[element_code].quantity_dimensions
                    or element.item_type_id != snapshot_elements_mapping[element_code].item_type_id
                    or element.tax_percent != snapshot_elements_mapping[element_code].tax_percent
                ):
                    to_update += 1
        return to_add, to_update, to_delete

    def get_html_context(self, action_data: OrderModifyEmailActionData) -> dict:
        order_snapshot = (
            OrderSnapshot.objects.filter(id=action_data.snapshot_id)
            .prefetch_related(
                "order_elements",
                Prefetch(
                    "order",
                    VendorOrder.objects.select_related("project")
                    .annotate_elements_final_amount()
                    .annotate_deduction_amount()
                    .annotate_deduction_tax_amount(),
                ),
                Prefetch("order__order_elements", queryset=VendorOrderElement.available_objects.all()),
            )
            .first()
        )

        order = order_snapshot.order
        logo = Organization.objects.filter(pk=order.org_from.pk).first().logo
        user = User.objects.select_related("org").filter(pk=order.issued_by.pk).first()
        logo_url = logo.url if logo else None
        currency = ProjectCountryConfigCache.get(instance_id=order.project_id).currency

        if logo:
            logo_company = None
        else:
            logo_company = order.org_from.name if len(order.org_from.name) < 20 else order.org_from.name[:17] + "..."

        last_approved_value = order_snapshot.amount + order_snapshot.tax_amount
        new_order_value = (order.elements_final_amount + order.elements_tax_amount) - (
            order.deduction_amount + order.deduction_tax_amount
        )
        change_order_value = float(new_order_value) - float(last_approved_value)
        to_add, to_update, to_delete = self._find_changes_in_elements(
            order_elements=order.order_elements.all(), snapshot_elements=order_snapshot.order_elements.all()
        )
        project_timezone = pytz.timezone(ProjectCountryConfigCache.get(instance_id=order.project_id).timezone.name)
        return {
            "issued_org": order.org_from.name,
            "modified_org": order.updated_by.org.name,
            "modified_by": order.updated_by.name,
            "logo_company": logo_company,
            "client_name": order.org_from.name,
            "logo_url": logo_url,
            "order_number": f"{order.project.job_id}/{order.order_number}",
            "order_from": order.work_order_from,
            "company_name": order.org_to.name,
            "created_at": order.issued_at,
            "custom_message": action_data.body,
            "shipping_address": order.shipping_address,
            "issued_by": order.issued_by.name,
            "org_name": user.org.name,
            "start_date": self.stringify_date(date=order.started_at, project_timezone=project_timezone),
            "due_date": self.stringify_date(date=order.due_at, project_timezone=project_timezone),
            "total_added_items": to_add,
            "total_updated_items": to_update,
            "total_deleted_items": to_delete,
            "new_order_value": formatINR(round(new_order_value, 2)),
            "last_approved_value": formatINR(round(last_approved_value, 2)),
            "change_order_value": formatINR(round(abs(change_order_value), 2)),
            "login_url": get_login_url(),
            "negative": True if change_order_value < 0 else False,
            "positive": True if change_order_value >= 0 else False,
            "currency_symbol": currency.symbol,
            "project_name": order.project.name,
            "job_id": order.project.job_id,
        }
