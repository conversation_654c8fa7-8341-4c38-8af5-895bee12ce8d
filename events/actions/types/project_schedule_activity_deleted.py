from authorization.domain.constants import Actions
from common.events.constants import Events
from common.events.project_schedule import ProjectScheduleActivitiesDeletedActionData
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.types.project_schedule_activity_assigned import ProjectScheduleActivitiesAssignedActionProcessor
from events.domain.constants import PushNotificationMode
from events.interface.dispatchers import PushNotificationDispatcher, WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from project_schedule.data.models import ProjectScheduleActivity
from rollingbanners.hash_id_converter import HashIdConverter


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_DELETED)
class ProjectScheduleActivitiesDeletedActionProcessor(ProjectScheduleActivitiesAssignedActionProcessor):
    _action_data_class = ProjectScheduleActivitiesDeletedActionData
    _event = Events.PROJECT_SCHEDULE_ACTIVITIES_DELETED
    _push_notification_mode = PushNotificationMode.GROUP
    _push_notification_template = TemplateContextChoices.PROJECT_SCHEDULE_ACTIVITIES_DELETED.value
    _dispatcher_classes = [PushNotificationDispatcher, WhatsappNotificationDispatcherV2]
    _whatsapp_template = EventTemplates.PROJECT_SCHEDULE_ACTIVITIES_DELETED.value

    def get_push_notification_meta_data(self):
        data = super().get_push_notification_meta_data()
        action_data: ProjectScheduleActivitiesDeletedActionData = self.get_action_data()
        data["activity_id"] = action_data.activity_ids[0]
        return data

    def _get_whatsapp_data_for_single_activity(self, activity_id: int):
        prepared_data = self.get_prepared_data()
        data = {}
        activity = ProjectScheduleActivity.objects.filter(id=activity_id).first()
        data["wbs"] = activity.wbs
        data["activity_name"] = activity.name
        data["link"] = (
            prepared_data.get("link")
            + "?activity-schedule-deleted&scheduleId="
            + HashIdConverter.encode(prepared_data.get("schedule_id"))
        )
        return data

    def _get_whatsapp_data_for_multiple_activities(self, activity_ids: list[int]):
        prepared_data = self.get_prepared_data()
        data = {}
        data["count"] = len(activity_ids)
        data["link"] = (
            prepared_data.get("link")
            + "?activity-schedule-bulk-deleted&scheduleId="
            + HashIdConverter.encode(prepared_data.get("schedule_id"))
        )
        return data

    def get_whatsapp_data(self):
        action_data: ProjectScheduleActivitiesDeletedActionData = self.get_action_data()
        data = super().get_whatsapp_data()
        deleter_user_id = User.objects.filter(id=action_data.user_id).first()
        data["project_user"] = deleter_user_id.name
        if len(action_data.activity_ids) == 1:
            data.update(self._get_whatsapp_data_for_single_activity(action_data.activity_ids[0]))
        else:
            data.update(self._get_whatsapp_data_for_multiple_activities(action_data.activity_ids))
        return data

    def get_whatsapp_template(self) -> str:
        action_data: ProjectScheduleActivitiesDeletedActionData = self.get_action_data()
        if len(action_data.activity_ids) == 1:
            return EventTemplates.PROJECT_SCHEDULE_ACTIVITY_DELETED.value
        return EventTemplates.PROJECT_SCHEDULE_ACTIVITIES_DELETED.value
