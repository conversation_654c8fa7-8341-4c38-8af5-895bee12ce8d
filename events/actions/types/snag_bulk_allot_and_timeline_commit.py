import copy
from datetime import datetime

from authorization.domain.constants import Actions
from common.events import Events
from common.events.actions import ActionRecipient
from common.events.actions.snags import SnagBulkAllotAndCommitTimelineActionData
from core.models import User
from core.selectors import active_users_get_using_ids
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.snags_notification_processor import SnagNotifyActionProcessor
from events.actions.types.snag_recipient_selector import (
    ActorData,
    AlloteeData,
    AllotterData,
    AssigneeData,
    AssignorData,
    CreatorData,
    SnagRecipientSelector,
)
from events.domain.mixins import ProjectAndOrgRecipientMixinV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextActions, TemplateContextChoices
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.selectors import get_snag_assignments_to_org


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_BULK_ALLOT_AND_TIMELINE_COMMIT_TO_ALLOTEE)
class AlloteeSnagBulkAllotAndTimelineCommitNotifyActionProcessor(
    SnagNotifyActionProcessor, ProjectAndOrgRecipientMixinV2
):
    _action_data_class = SnagBulkAllotAndCommitTimelineActionData
    _event = Events.SNAG_BULK_ALLOTTED_AND_COMMITTED_TIMELINE.value
    _push_notification_template = TemplateContextChoices.SNAG_BULK_ALLOTTED_AND_TIMELINE_COMMITTED
    _whatsapp_template = EventTemplates.SNAG_BULK_ALLOT_AND_TIMELINE_COMMITTED.value
    _context = MicroContext.PROJECT_SNAG.value

    def get_context_id(self) -> int:
        return self.get_action_data().project_id

    def prepare_data(self):
        action_data: SnagBulkAllotAndCommitTimelineActionData = self.get_action_data()
        prepared_data = copy.deepcopy(action_data.__dict__)

        snag_assignment_list = get_snag_assignments_to_org(
            snag_id_list=action_data.snag_ids, organization_id=action_data.organization_id
        ).select_related("snag", "allotted_poc", "snag__project")

        snag_creator_ids = [snag_assignment.snag.created_by_id for snag_assignment in snag_assignment_list]
        snag_assignor_ids = [snag_assignment.created_by_id for snag_assignment in snag_assignment_list]

        allotee_user_id = action_data.allotted_to_id
        allotter_user_id = action_data.updated_by_id

        users = User.objects.filter(id__in=[allotee_user_id, allotter_user_id]).in_bulk()

        if action_data.allotted_to_id:
            prepared_data["allotee_name"] = snag_assignment_list[0].allotted_poc.name
            prepared_data["allotted_to_id"] = action_data.allotted_to_id

        prepared_data["snag_count"] = len(action_data.snag_ids)

        prepared_data["project_name"] = snag_assignment_list[0].snag.project.name
        prepared_data["project_job_id"] = snag_assignment_list[0].snag.project.job_id
        prepared_data["allotter_id"] = action_data.updated_by_id
        prepared_data["allotter_org_id"] = action_data.organization_id
        prepared_data["allotter_name"] = users.get(allotter_user_id).name
        prepared_data["allotee_name"] = users.get(allotee_user_id).name
        prepared_data["committed_date"] = action_data.committed_at
        prepared_data["snag_creator_ids"] = snag_creator_ids
        prepared_data["snag_assignor_ids"] = snag_assignor_ids
        prepared_data["link"] = f"project/{HashIdConverter.encode(prepared_data['project_id'])}/snags/"

        self._prepared_data = prepared_data

    def get_recipient_org_id(self):
        return self.get_action_data().organization_id

    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        if prepared_data.get("committed_date"):
            committed_date_obj = datetime.strptime(prepared_data.get("committed_date"), "%d-%m-%Y")
            committed_date = "Committed Date - *{date}*".format(date=datetime.strftime(committed_date_obj, "%d %b %Y"))
        else:
            committed_date = " "
        whatsapp_data = {
            "allotted_user": "You",
            "allotted_by": prepared_data.get("allotter_name"),
            "project_name": prepared_data.get("project_name"),
            "job_id": prepared_data.get("project_job_id"),
            "committed_date": committed_date,
            "snag_count": f"{prepared_data.get('snag_count')} "
            f"{'snags' if prepared_data.get('snag_count') > 1 else 'snag'}",
            "link": prepared_data.get("link"),
        }
        return whatsapp_data

    def get_push_notification_meta_data(self):
        meta_data = super().get_push_notification_meta_data()
        meta_data["project_id"] = self.get_action_data().project_id

        return meta_data

    def get_snag_users_data(self):
        prepared_data = self.get_prepared_data()
        assignee_user_recipients = super()._get_event_recipients()
        assignee_user_ids = [data.user_id for data in assignee_user_recipients]
        user_id_list = [
            *prepared_data["snag_creator_ids"],
            *prepared_data["snag_assignor_ids"],
            prepared_data["allotter_id"],
            *assignee_user_ids,
        ]
        if prepared_data.get("allotted_to_id"):
            user_id_list.append(prepared_data.get("allotted_to_id"))

        users = active_users_get_using_ids(user_ids=user_id_list).in_bulk()
        actor_data_id = prepared_data.get("allotter_id")

        assignees_data = [
            AssigneeData(user_id=user.id, org_id=user.org_id)
            for user_id, user in users.items()
            if user_id in assignee_user_ids
        ]
        allotee_data = None
        actor_data = ActorData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        allotter_data = AllotterData(user_id=users.get(actor_data_id).id, org_id=users.get(actor_data_id).org_id)
        assignors_data = [
            AssignorData(user_id=users.get(assignor_id).id, org_id=users.get(assignor_id).org_id)
            for assignor_id in prepared_data["snag_assignor_ids"]
        ]
        creators_data = [
            CreatorData(user_id=users.get(creator_id).id, org_id=users.get(creator_id).org_id)
            for creator_id in prepared_data["snag_creator_ids"]
        ]

        if prepared_data.get("allotted_to_id"):
            allotee_data = AlloteeData(
                user_id=users.get(prepared_data.get("allotted_to_id")).id,
                org_id=users.get(prepared_data.get("allotted_to_id")).org_id,
            )

        return assignors_data, actor_data, creators_data, assignees_data, allotee_data, allotter_data, users

    def _get_event_recipients(self):
        (
            assignors_data,
            actor_data,
            creators_data,
            assignees_data,
            allotee_data,
            allotter_data,
            users,
        ) = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=creators_data,
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=allotter_data,
            allotee_data=allotee_data,
        )
        target_recipients = recipient_selector.get_target_recipients(
            action=MicroContextActions.BULK_ALLOT_USER_AND_COMMIT_TIMELINE
        )

        recipients = []
        for user_data in target_recipients:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_SNAG_BULK_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS)
class SnagBulkAllotAndTimelineCommitObserverNotifyActionProcessor(
    AlloteeSnagBulkAllotAndTimelineCommitNotifyActionProcessor
):
    def get_whatsapp_data(self):
        prepared_data = self.get_prepared_data()
        if prepared_data.get("committed_date"):
            committed_date_obj = datetime.strptime(prepared_data.get("committed_date"), "%d-%m-%Y")
            committed_date = "Committed Date- {date}".format(date=datetime.strftime(committed_date_obj, "%d %b %Y"))
        else:
            committed_date = " "

        whatsapp_data = {
            "allotted_user": prepared_data.get("allotee_name"),
            "allotted_by": prepared_data.get("allotter_name"),
            "project_name": prepared_data.get("project_name"),
            "job_id": prepared_data.get("project_job_id"),
            "snag_count": "Snags" if prepared_data.get("snag_count") > 1 else "Snag",
            "committed_date": committed_date,
            "link": prepared_data.get("link"),
        }
        return whatsapp_data

    def _get_event_recipients(self):
        (
            assignors_data,
            actor_data,
            creators_data,
            assignees_data,
            allotee_data,
            allotter_data,
            users,
        ) = self.get_snag_users_data()
        recipient_selector = SnagRecipientSelector(
            creators_data=creators_data,
            actor_data=actor_data,
            assignees_data=assignees_data,
            assignors_data=assignors_data,
            allotter_data=allotter_data,
            allotee_data=allotee_data,
        )

        observers = recipient_selector.get_observers(action=MicroContextActions.BULK_ALLOT_USER_AND_COMMIT_TIMELINE)

        recipients = []
        for user_data in observers:
            user = users.get(user_data.user_id)
            recipients.append(
                ActionRecipient(
                    user_id=user.pk,
                    name=user.name,
                    email=user.email,
                    contact=user.contact,
                    org_id=user.org_id,
                )
            )
        return recipients
