from authorization.domain.constants import Actions
from commentv2.data.choices import CommentApprovalStatus
from common.events.actions.base import ActionRecipient
from common.events.actions.comment import CommentApprovalAcceptedRejectedActionData
from common.events.constants import Events
from core.models import User
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.comment_action_processor import CommentApprovalNotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_COMMENT_APPROVAL_ACCEPTED_REJECTED)
class CommentApprovalAcceptedRejectedNotifyActionProcessor(CommentApprovalNotifyActionProcessorV2):
    _push_notification_mode = PushNotificationMode.INDIVIDUAL
    _push_notification_template = TemplateContextChoices.COMMENT_APPROVAL_ACCEPTED_REJECTED
    _whatsapp_template = EventTemplates.COMMENT_APPROVAL_ACCEPTED_REJECTED.value
    _action_data_class = CommentApprovalAcceptedRejectedActionData
    _event = Events.COMMENT_APPROVAL_ACCEPTED_REJECTED

    def get_whatsapp_data(self):
        data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        if prepared_data.get("status") == CommentApprovalStatus.APPROVED.value:
            data["status"] = "successfully approved"
        elif prepared_data.get("status") == CommentApprovalStatus.REJECTED.value:
            data["status"] = "rejected"
        data["approver_name"] = prepared_data.get("approver").name
        data["link"] = prepared_data.get("link")
        return data

    def _get_event_recipients(self):
        requester: User = self.get_prepared_data().get("requester")
        return [
            ActionRecipient(
                user_id=requester.pk,
                name=requester.name,
                contact=requester.contact,
                email=requester.email,
                org_id=requester.org_id,
            )
        ]
