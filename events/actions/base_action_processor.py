import abc
from abc import ABC
from typing import Any, Dict

import structlog
from django.core.exceptions import ValidationError

from authorization.domain.constants import Actions
from common.events import BaseEventData
from common.events.constants import Events
from events.interface.exceptions import (
    ActionMetaDataMismatch,
    BaseActionProcessorException,
    ConflictRegisteredActionProcessor,
    QueueWorkerException,
)

logger = structlog.getLogger(__name__)


class BaseActionProcessor(ABC):
    class JobAbortException(ValidationError):
        """
        This exception is used to abort the current job
        Must be handle on parent function
        """

        pass

    _registered_action_processor = dict()
    queue = "default_queue"
    worker = None

    @classmethod
    def load_action_processor(cls, action: Actions):
        # TODO: Patch need to fix this
        # from events.actions.types.dpr_pdf_template import (
        #     WorkProgressExportReportActionProcessor,
        #     WorkProgressGenerateReportActionProcessor,
        # )

        from .types.dpr_export_notify import WorkProgressExportReportNotifyActionProcessor
        from .types.dpr_export_template import WorkProgressExportReportTemplateActionProcessor
        from .types.dpr_generated_template import WorkProgressGenerateReportTemplateActionProcessor
        from .types.leads_export_excel_notify import LeadsExportExcelNotifyActionProcessor  # noqa

        cls._registered_action_processor[
            Actions.NOTIFY_WORK_PROGRESS_EXPORT_REPORT_PDF_GENERATED
        ] = WorkProgressExportReportNotifyActionProcessor
        cls._registered_action_processor[
            Actions.TEMPLATE_WORK_PROGRESS_EXPORT_REPORT
        ] = WorkProgressExportReportTemplateActionProcessor
        cls._registered_action_processor[
            Actions.TEMPLATE_WORK_PROGRESS_GENERATE_REPORT
        ] = WorkProgressGenerateReportTemplateActionProcessor
        cls._registered_action_processor[Actions.LEAD_EXCEL_EXPORT] = LeadsExportExcelNotifyActionProcessor
        return cls._registered_action_processor[action]

    @classmethod
    def register_action_processor(cls, **kwargs):
        """
        This decorator performs following thing:
            i) Ensures that no two actions have same processor.
            ii) It assigns and maps an `action` to a specific processor responsible
                for processing the action data.
        """

        def wrapper(klass):
            if not kwargs.get("action"):
                raise BaseActionProcessorException(
                    "While registering action processor to any action type. "
                    "You must pass `action` as an argument in the decorator."
                )
            assert (hasattr(klass, "DATA_CLASS") and klass.DATA_CLASS) or (
                hasattr(klass, "_action_data_class") and klass._action_data_class
            ), f"Serializer {klass.__name__} must declare `DATA_CLASS` or `_action_data_class` attribute"
            klass._prop = kwargs["action"]
            if klass._prop in cls._registered_action_processor:  # noqa
                raise ConflictRegisteredActionProcessor(
                    f"Action type `{klass._prop}` is already registered. It can "
                    f"not be registered with more than one action processor "
                )
            cls._registered_action_processor[klass._prop] = klass
            return klass

        return wrapper

    @abc.abstractmethod
    def process(cls, action: Actions, action_data: BaseEventData, task_id: int):
        pass

    @abc.abstractmethod
    def prepare_data(cls, action_data: BaseEventData):
        pass

    @classmethod
    def get_queue(cls):
        return cls.queue

    @classmethod
    def get_worker(cls):
        return cls.worker

    @classmethod
    def get_action_data_class(cls):
        return cls.DATA_CLASS

    @classmethod
    def enqueue_task(cls, action_data: Dict, action: Actions):
        if not cls.get_worker():
            raise QueueWorkerException("Worker must be defined for enqueing task")
        task = cls.get_worker().apply_async(
            queue=cls.get_queue(), kwargs={"action_data": action_data, "action": action}
        )
        # task = cls.get_worker()(action_data=action_data, action=action)
        logger.info("Task enqueued successfully", task_id=task.task_id)


class BaseActionProcessorV2(BaseActionProcessor):
    _queue = "default_queue"
    _worker = None
    _action_data = None
    _event: Events = None
    _action: Actions = None
    _action_data_class = None

    def __init__(self, action_data: BaseEventData, action: Actions) -> None:
        if not isinstance(action_data, self._action_data_class):  # noqa
            logger.error(f"[!] Found Action meta data mismatch for action processor: {type(self).__name__} ")
            raise ActionMetaDataMismatch(
                f"[!] Found Action meta data mismatch for action processor: {type(self).__name__}"
            )
        if self._event is None:
            raise NotImplementedError(f"[!] Event is not defined for action processor: {type(self).__name__}")
        self._action_data = action_data
        self._action = action

    @abc.abstractmethod
    def process(self):
        pass

    @classmethod
    def get_action_data_class(cls):
        return cls._action_data_class

    def get_action_data(self) -> Any:
        return self._action_data

    def get_action(self):
        return self._action

    def get_event(self):
        return self._event.value

    @classmethod
    def get_queue(cls):
        return cls._queue

    @classmethod
    def get_worker(cls):
        return cls._worker
