from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from client.data.selectors import fetch_all_client_mappings_exclude_self_mapping
from client.onboard.serializers import ClientOrganizationSerializer, VendorClientMappingSerializer
from client.onboard.services import client_org_details_fetch
from common.serializers import Hash<PERSON>d<PERSON><PERSON>
from core.apis import ExternalOrgBaseApi


class ExternalClientBaseApi(ExternalOrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_MANAGE_CLIENT]
    pagination_class = None


class ExternalClientListApi(ExternalClientBaseApi):
    class OutputSerializer(VendorClientMappingSerializer):
        client_id = HashIdField()
        name = serializers.CharField(source="billing_entity_name")

        class Meta(VendorClientMappingSerializer.Meta):
            ref_name = "ExternalClientListApiOutput"
            fields = ("client_id", "name", "created_at")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="ext_client_list",
        operation_summary="Client List",
    )
    def get(self, request, *args, **kwargs):
        clients = fetch_all_client_mappings_exclude_self_mapping(organization_id=self.get_organization_id())
        return Response(self.OutputSerializer(clients, many=True).data, status=HTTP_200_OK)


class ExternalClientDetailApi(ExternalClientBaseApi):
    class OutputSerializer(ClientOrganizationSerializer):
        class Meta(ClientOrganizationSerializer.Meta):
            ref_name = "ExternalClientDetailApiOutput"
            fields = (
                "id",
                "name",
                "code",
                "gst",
                "company_addresses",
                "pan",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="ext_client_organization_details_api",
        operation_summary="External Client Organization Details API",
    )
    def get(self, request, client_id, *args, **kwargs):
        organization, is_editable = client_org_details_fetch(client_id=client_id, user=request.user)
        return Response(self.OutputSerializer(organization).data, status=HTTP_200_OK)
