from authorization.domain.constants import Permissions
from client.data.selectors import fetch_client_mapping
from client.onboard.exceptions import ClientException
from core.apis import OrgBaseApi


class ManageClientBaseApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_MANAGE_CLIENT]
    mapping = None

    def get_mapping(self):
        if self.kwargs.get("client_id") and self.mapping is None:
            self.mapping = fetch_client_mapping(
                organization_id=self.get_organization_id(), client_id=self.kwargs.get("client_id")
            )
            if not self.mapping:
                self.set_response_message("Client not found")
                raise ClientException("Client not found")
            return self.mapping
