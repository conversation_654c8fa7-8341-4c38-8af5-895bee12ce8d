from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from client.onboard.domain.entities import ClientDocumentDetail
from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema


class ClientDocumentDetailsApi(ManageClientBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ClientDocumentDetail)},
        operation_id="client_document_details_api",
        operation_summary="Get Client Document Details",
    )
    def get(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(client_id=client_id)

        data = service.get_document_details()

        self.set_response_message("Client Document details fetched successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
