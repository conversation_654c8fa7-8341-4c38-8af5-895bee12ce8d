from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from common.serializers import BaseSerializer
from core.organization.domain.entities import OrganizationBasicDetailsData


class CheckClientPocApiV2(ManageClientBaseApi):
    class InputSerializer(BaseSerializer):
        poc_email_id = serializers.EmailField()

        class Meta:
            fields = "poc_email_id"
            ref_name = "CheckClientPocApiV2Input"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: pydantic_schema(OrganizationBasicDetailsData)},
        operation_id="check_client_poc_api_v2",
        operation_summary="Check Client POC Api V2",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        user_entity = self.get_org_user_entity()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_onboard_service()
        org_service = factory.get_org_service()
        try:
            client_id = service.get_client_details_via_email(poc_email=data["poc_email_id"])
            client_data = org_service.get_prefill_basic_details(org_id=client_id)
        except service.ClientNotFoundException:
            self.set_response_message("Create New Organization")
            return Response(status=HTTP_200_OK)
        except service.ClientOnboardServiceV2Exception as e:
            if isinstance(e.messages, list):
                self.set_response_message(" ".join(e.messages))
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(pydantic_dump(client_data), status=HTTP_200_OK)
