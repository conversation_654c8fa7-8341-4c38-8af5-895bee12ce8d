from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClient<PERSON>ase<PERSON><PERSON>
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBillingEntityData


class ClientBillingEntityListApi(ManageClientBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityData)},
        operation_id="client_billing_entity_list",
        operation_summary="Get Client Billing Entities",
    )
    def get(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(client_id=client_id)

        data = service.get_billing_entities()

        self.set_response_message("Billing entities fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
