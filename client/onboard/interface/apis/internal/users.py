from django.db import transaction
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_202_ACCEPTED

from client.onboard.data.selectors import client_users_get
from client.onboard.domain.services.users import ClientUserService
from client.onboard.interface.base_api import ManageClientBase<PERSON>pi
from client.onboard.interface.serializers import ClientUserSerializer
from common.serializers import BaseSerializer, HashIdField, PhoneNumberSerializer
from core.organization.enums import UserStatus
from core.organization.exceptions import PocUserException
from core.organization.serializers import UserAddInputSerializer


class ClientUserListApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = ClientUserSerializer

    class FilterSerializer(BaseSerializer):
        email_available = serializers.BooleanField(default=False, allow_null=True, required=False)

        class Meta:
            ref_name = "ClientUserListApiFilterSerializer"

    def get_queryset(self):
        data = self.validate_filter_data()
        condition = Q()
        if data.get("email_available") and data.get("email_available") is not None:
            condition = Q(
                email__isnull=not data.get("email_available"),
                has_left_org=False,
            )
        users = client_users_get(mapping_id=self.get_mapping().id).filter(condition).order_by("created_at")
        return users

    @swagger_auto_schema(
        responses={HTTP_200_OK: ClientUserSerializer(many=True)},
        operation_id="client_onboard_user_list",
        operation_summary="List all user POC's of a client.",
    )
    @transaction.atomic
    def get(self, request, client_id, *args, **kwargs):
        return Response(self.serializer_class(self.get_queryset(), many=True).data, status=HTTP_200_OK)


class ClientUserAddAndInviteApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = ClientUserSerializer
    input_serializer_class = UserAddInputSerializer

    @swagger_auto_schema(
        request_body=UserAddInputSerializer(),
        responses={HTTP_200_OK: ClientUserSerializer()},
        operation_id="Client_onboard_add_and_invite_api",
        operation_summary="Add and invite client user api",
    )
    @transaction.atomic
    def post(self, request, client_id, *args, **kwargs):
        data = self.validate_input_data()
        self.get_mapping()
        try:
            user = ClientUserService().add_and_invite(
                created_by_id=self.get_user_id(), mapping=self.mapping, user=data, organization_id=client_id
            )
            self.set_response_message("User added successfully")
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User added successfully")
        user = client_users_get(mapping_id=self.mapping.id).filter(id=user.id).first()
        return Response(self.serializer_class(user).data, status=HTTP_201_CREATED)


class ClientUserUpdateAndInviteApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = ClientUserSerializer
    input_serializer_class = UserAddInputSerializer

    @swagger_auto_schema(
        request_body=UserAddInputSerializer(),
        responses={HTTP_200_OK: ClientUserSerializer()},
        operation_id="client_onboard_update_and_invite_api",
        operation_summary="Client user update and invite ",
    )
    @transaction.atomic
    def put(self, request, client_id, poc_id, *args, **kwargs):
        data = self.validate_input_data()
        self.get_mapping()
        try:
            user = ClientUserService().update_and_invite(
                poc_user_id=poc_id,
                updated_by_id=self.get_user_id(),
                mapping=self.mapping,
                poc_user=data,
                organization_id=client_id,
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User updated successfully")
        user = client_users_get(mapping_id=self.mapping.id).filter(id=user.id).first()
        return Response(self.serializer_class(user).data, status=HTTP_202_ACCEPTED)


class ClientUserInviteAgainApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        operation_id="client_onboard_invite_again_api",
        operation_summary="User onboard invite again api",
    )
    @transaction.atomic
    def put(self, request, client_id, poc_id, *args, **kwargs):
        self.get_mapping()
        poc_user = client_users_get(mapping_id=self.mapping).filter(id=poc_id).first()
        if poc_user.active_status == UserStatus.LEFT.value:
            self.set_response_message("Cannot Invite")
            raise PocUserException("Now Allowed")
        try:
            ClientUserService().invite_again(
                poc_user_id=poc_id,
                invited_by_id=self.get_user_id(),
                mapping=self.mapping,
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User updated successfully")
        return Response(status=HTTP_200_OK)


class ClientUserRemoveApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = ClientUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ClientUserSerializer(many=True)},
        operation_id="client_onboard_Client_user_remove_api",
        operation_summary="Client user remove api",
    )
    @transaction.atomic
    def delete(self, request, client_id, poc_id, *args, **kwargs):
        self.get_mapping()
        try:
            ClientUserService().remove(
                poc_user_id=poc_id,
                deleted_by_id=self.get_user_id(),
                mapping_id=self.mapping.id,
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message(message="User removed successfully.")
        return Response(status=HTTP_200_OK)


class ClientUserSetPrimaryApi(ManageClientBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: ClientUserSerializer(many=True)},
        operation_id="Client_onboard_Client_user_set_primary_api",
        operation_summary="Client user make primary api",
    )
    @transaction.atomic
    def put(self, request, client_id, poc_id, *args, **kwargs):
        self.get_mapping()
        poc_user = client_users_get(mapping_id=self.mapping).filter(id=poc_id).first()
        if poc_user.active_status == UserStatus.LEFT.value:
            self.set_response_message("Cannot set primary")
            raise PocUserException("Now Allowed")

        ClientUserService().set_primary(
            poc_user_id=poc_id,
            mapping_id=self.mapping.id,
        )
        self.set_response_message("User marked as primary.")
        return Response(status=HTTP_200_OK)


class ClientPOCVerifyApi(ManageClientBaseApi):
    class OutputSerializer(BaseSerializer):
        name = serializers.CharField(required=True, allow_blank=True)
        phone_number = PhoneNumberSerializer()

        class Meta:
            ref_name = "ClientPOCVerifyOutputSerializer"

    class InputSerializer(BaseSerializer):
        email = serializers.EmailField(required=True)
        poc_id = HashIdField(required=False)

        class Meta:
            ref_name = "ClientPOCVerifyEmailSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=InputSerializer(),
        operation_id="Client_poc_verify",
        operation_summary="Client POC Verify",
    )
    def post(self, request, client_id, *args, **kwargs):
        try:
            data = self.validate_input_data()
        except ValidationError as e:
            if "email" in e.detail:
                self.set_response_message(message=" ".join(e.detail["email"]))
            raise e
        try:
            data = ClientUserService().verify_email(
                email=data.get("email"),
                organization_id=client_id,
                mapping_id=self.get_mapping().id,
                poc_id=data.get("poc_id"),
            )
        except ClientUserService.PocUserServiceException as e:
            self.set_response_message(e.message)
            raise e

        if data:
            response_data = self.OutputSerializer(data).data
        else:
            response_data = {"name": None, "phone_number": None}
        return Response(response_data, status=HTTP_201_CREATED)
