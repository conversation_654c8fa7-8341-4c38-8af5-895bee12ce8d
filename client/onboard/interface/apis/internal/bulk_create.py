from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED, HTTP_400_BAD_REQUEST

from client.excel.parse import ClientBulkCreateExcelParser
from client.onboard.domain.entities import ClientOnboardInBulkData
from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBase<PERSON><PERSON>
from common.serializers import BaseSerializer


class ClientBulkCreateApi(ManageClientBaseApi):
    class InputSerializer(BaseSerializer):
        file = serializers.FileField()
        country_id = serializers.IntegerField()

        class Meta:
            ref_name = "ClientBulkCreateInputSerializer"

    class OutputSerializer(BaseSerializer):
        organization_name = serializers.CharField()
        status = serializers.CharField()

        class Meta:
            ref_name = "ClientBulkCreateApiOutputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: OutputSerializer()},
        request_body=InputSerializer(),
        operation_id="vendor_bulk_create",
        operation_summary="Vendor bulk create",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        file: InMemoryUploadedFile = data.get("file")
        country_id = data.get("country_id")
        user_entity = self.get_org_user_entity()

        excel_parser = ClientBulkCreateExcelParser(file=file, country_id=country_id)

        try:
            excel_data = excel_parser.parse()
        except ClientBulkCreateExcelParser.InvalidFileFormatException:
            return Response(
                {
                    "error_code": 4001,
                    "message": "Invalid file format supplied, please download correct file format",
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except ClientBulkCreateExcelParser.InvalidDataException:
            error_file, total_errors, total_success = excel_parser.get_error_file()
            return Response(
                {
                    "error_code": 4002,
                    "message": f"Errors found in {total_errors} rows.",
                    "description": f"{total_success} Client successfully passed for upload.",
                    "error_file": error_file,
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except ClientBulkCreateExcelParser.ExcelParserException as e:
            return Response(
                {"error_code": 4001, "message": e.message},
                status=HTTP_400_BAD_REQUEST,
            )

        clients_data: list[ClientOnboardInBulkData] = [
            ClientOnboardInBulkData(
                uid_number=data.get("uid_number"),
                client_name=data.get("client_name"),  # type: ignore
                tax_number=data.get("tax_number"),
                user_name=data.get("user_name"),
                phone_number=data.get("phone_number"),
                email=data.get("email"),
                aadhar_number=data.get("aadhar_number"),
                msme_id=data.get("msme_id"),
                base_address_line_1=data.get("base_address_line_1"),
                base_address_line_2=data.get("base_address_line_2"),
                base_country_name=data.get("base_country_name"),
                base_state_name=data.get("base_state_name"),
                base_city_name=data.get("base_city_name"),
                base_zip_code=data.get("base_zip_code"),
                additional_address_line_1=data.get("additional_address_line_1"),
                additional_address_line_2=data.get("additional_address_line_2"),
                additional_country_name=data.get("additional_country_name"),
                additional_state_name=data.get("additional_state_name"),
                additional_city_name=data.get("additional_city_name"),
                additional_zip_code=data.get("additional_zip_code"),
            )
            for data in excel_data
        ]

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_onboard_service()

        status_list = service.onboard_clients_in_bulk(clients_data=clients_data, country_id=country_id)

        return Response(
            self.OutputSerializer(
                [
                    {
                        "organization_name": status[0],
                        "status": status[1],
                    }
                    for status in status_list
                ],
                many=True,
            ).data,
            status=HTTP_201_CREATED,
        )
