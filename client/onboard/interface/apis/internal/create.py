from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from client.data.selectors import fetch_client_using_mapping_id
from client.onboard.domain.entities import ClientOnboardInputData
from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from client.onboard.serializers import VendorClientSerializer


class ClientCreateApiV2(ManageClientBaseApi):
    class OutputSerializer(VendorClientSerializer):
        class Meta(VendorClientSerializer.Meta):
            fields = ("id", "organization", "poc", "is_invited", "is_client_active")
            ref_name = "ClientCreateApiV2Output"

    input_serializer_class = ClientOnboardInputData.drf_serializer

    @swagger_auto_schema(
        request_body=ClientOnboardInputData.drf_serializer,
        responses={HTTP_200_OK: OutputSerializer},
        operation_id="client_create_api_v2",
        operation_summary="Create Client Api V2",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_onboard_service()

        client_id, from_to_org_mapping_id = service.onboard_client(data=data)

        return Response(
            self.OutputSerializer(fetch_client_using_mapping_id(mapping_id=from_to_org_mapping_id)).data,
            status=HTTP_200_OK,
        )
