from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageC<PERSON><PERSON><PERSON><PERSON><PERSON>
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBillingEntityCreateData, OrganizationBillingEntityCreateEntity


class ClientBillingEntityCreateApi(ManageClientBaseApi):
    input_pydantic_class = OrganizationBillingEntityCreateData

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityCreateEntity)},
        operation_id="client_billing_entity_create",
        operation_summary="Create Client Billing Entity",
    )
    def post(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(client_id=client_id)

        try:
            billing_entity = service.create_billing_entity(data=data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Billing entity created successfully")
        return Response(pydantic_dump(billing_entity), status=HTTP_200_OK)
