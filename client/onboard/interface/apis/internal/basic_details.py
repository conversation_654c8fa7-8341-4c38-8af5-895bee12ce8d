from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.entities import ClientBasicDetailData, ClientBillingEntityFilter
from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBasicDetailsUpdateData


class ClientBasicDetailsApi(ManageClientBaseApi):
    filter_pydantic_class = ClientBillingEntityFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ClientBasicDetailData)},
        operation_id="client_basic_details",
        operation_summary="Get Client Basic Details",
    )
    def get(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data: ClientBillingEntityFilter = self.validate_pydantic_filter_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=filter_data.billing_entity_id,
        )

        try:
            data = service.get_basic_details()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Client details fetched successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ClientBasicDetailsUpdateApi(ManageClientBaseApi):
    input_pydantic_class = OrganizationBasicDetailsUpdateData

    @swagger_auto_schema(
        request_body=pydantic_schema(OrganizationBasicDetailsUpdateData),
        responses={HTTP_200_OK: {}},
        operation_id="client_basic_details_update",
        operation_summary="Update Client Basic Details",
    )
    @transaction.atomic
    def put(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=data.billing_entity_id,
        )

        try:
            service.update_basic_details(data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Client details updated successfully.")
        return Response(status=HTTP_200_OK)
