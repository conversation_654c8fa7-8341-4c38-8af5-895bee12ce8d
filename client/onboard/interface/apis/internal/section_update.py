from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from core.organization.domain.entities import OrganizationSectionUpdateData


class ClientOrganizationSectionsUpdateApi(ManageClientBaseApi):
    input_serializer_class = OrganizationSectionUpdateData.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationSectionUpdateData.drf_serializer},
        operation_id="client_section_update_api",
        operation_summary="Update Client Section Api",
    )
    @transaction.atomic
    def put(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data: OrganizationSectionUpdateData = self.validate_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=data.billing_entity_id,
        )

        try:
            service.update_sections_data(data=data)
        except service.SectionUpdateException as e:
            return Response(e.message_dict, status=HTTP_400_BAD_REQUEST)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Client sections updated successfully.")
        return Response(status=HTTP_200_OK)
