from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi


class ClientBillingEntityDeleteApi(ManageClientBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: {}},
        operation_id="client_billing_entity_delete",
        operation_summary="Delete Client Billing Entity",
    )
    def delete(self, request, client_id, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=billing_entity_id,
        )

        try:
            service.delete_billing_entity()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Billing entity deleted successfully.")
        return Response(status=HTTP_200_OK)
