from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClientBaseApi
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt


class ClientUploadLogoApi(ManageClientBaseApi):
    class InputModel(PydanticInputBaseModel):
        logo_url: CustomFileUrlStr | None = None
        billing_entity_id: HashIdInt | None = None

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: {}},
        operation_id="client_logo_upload",
        operation_summary="Upload Client Logo",
    )
    @transaction.atomic
    def post(self, request, client_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        input_data = self.validate_pydantic_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=input_data.billing_entity_id,
        )

        try:
            service.update_billing_entity_logo(logo=input_data.logo_url)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Client logo uploaded successfully.")
        return Response({"logo_url": input_data.logo_url}, status=HTTP_200_OK)
