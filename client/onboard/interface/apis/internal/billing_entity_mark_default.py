from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from client.onboard.domain.factory import ClientFactory
from client.onboard.interface.base_api import ManageClient<PERSON><PERSON><PERSON><PERSON>
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import LinkedOrganizationBillingEntityData


class ClientBillingEntityMarkDefaultApi(ManageClientBaseApi):
    class InputModel(PydanticInputBaseModel):
        is_default: bool

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(LinkedOrganizationBillingEntityData)},
        operation_id="client_billing_entity_mark_default",
        operation_summary="Mark Client Billing Entity Default",
    )
    def post(self, request, client_id, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = ClientFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            client_id=client_id,
            billing_entity_id=billing_entity_id,
        )

        try:
            service.mark_billing_entity_default(is_default=data.is_default)
            billing_entity = service.get_billing_entities(billing_entity_id=billing_entity_id)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message(
            f"Billing entity {'marked' if data.is_default else 'unmarked'} as default successfully"
        )
        return Response(pydantic_dump(billing_entity[0]), status=HTTP_200_OK)
