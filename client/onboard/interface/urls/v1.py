from django.urls import include, path

from client.onboard.apis import (
    CheckClientPocApi,
    ClientCodeExistsCheckApi,
    ClientOrgDetailsApi,
    ClientOrgKycUpdateApi,
    ClientOrgUpdateApi,
    ClientOtherDetailDocumentCreateApi,
    ClientOtherDetailDocumentDeleteApi,
    ClientOtherDetailDocumentUpdateApi,
    ClientOtherDetailsApi,
    OnboardClientActiveStatusChangeAPI,
    OnboardClientCreateApi,
    OnboardClientUpdateAPI,
    OnboardedClientListApi,
    OnboardOrganizationDetailsApi,
)
from client.onboard.interface.apis.internal.users import (
    <PERSON>lientPOCVerifyApi,
    ClientUserAddAndInviteApi,
    ClientUserInviteAgainApi,
    ClientUserListApi,
    ClientUserRemoveApi,
    ClientUserSetPrimaryApi,
    ClientUserUpdateAndInviteApi,
)

USERS = [
    path("list/", ClientUserListApi.as_view(), name="client-list-api"),
    path("add-invite/", ClientUserAddAndInviteApi.as_view(), name="client-user-add-invite-api"),
    path(
        "<hash_id:poc_id>/update-invite/",
        ClientUserUpdateAndInviteApi.as_view(),
        name="client-user-update-and-invite-api",
    ),
    path(
        "<hash_id:poc_id>/remove/",
        ClientUserRemoveApi.as_view(),
        name="client-user-remove-api",
    ),
    path(
        "<hash_id:poc_id>/set-primary/",
        ClientUserSetPrimaryApi.as_view(),
        name="client-user-set-primary-api",
    ),
    path("verify-email/", ClientPOCVerifyApi.as_view(), name="client-poc-verify-api"),
    path(
        "<hash_id:poc_id>/invite-again/",
        ClientUserInviteAgainApi.as_view(),
        name="client-poc-verify-api",
    ),
]

urlpatterns = [
    path("<hash_id:client_id>/user/", include(USERS)),
    path("list/", OnboardedClientListApi.as_view(), name="onboard-client-list-api"),
    path("create/", OnboardClientCreateApi.as_view(), name="onboard-client-create-api-v2"),
    path(
        "organization/update/", OnboardClientUpdateAPI.as_view(), name="onboard-client-update-api"
    ),  # Update when client login via email for first time and updates info
    path(
        "<hash_id:mapping_id>/status-change/",
        OnboardClientActiveStatusChangeAPI.as_view(),
        name="onboard-client-status-change-api",
    ),
    path("check-client-poc/", CheckClientPocApi.as_view(), name="check-client-poc-api"),
    path(
        "details/",
        OnboardOrganizationDetailsApi.as_view(),
        name="client-organization-details",
    ),
    path("check-code/", ClientCodeExistsCheckApi.as_view(), name="check-client-code-exists"),
    path("<hash_id:client_id>/details/", ClientOrgDetailsApi.as_view(), name="client-org-details-api"),
    path("<hash_id:client_id>/other-details/", ClientOtherDetailsApi.as_view(), name="client-other-details-api"),
    path(
        "<hash_id:client_id>/update/",
        ClientOrgUpdateApi.as_view(),
        name="client-other-details-poc-update-api",
    ),
    path(
        "<hash_id:client_id>/kyc/update/",
        ClientOrgKycUpdateApi.as_view(),
        name="client-other-details-poc-update-api",
    ),
    path(
        "<hash_id:client_id>/other-details/document/create/",
        ClientOtherDetailDocumentCreateApi.as_view(),
        name="client-other-details-document-create-api",
    ),
    path(
        "<hash_id:client_id>/other-details/document/<hash_id:doc_id>/update/",
        ClientOtherDetailDocumentUpdateApi.as_view(),
        name="client-other-details-document-update-api",
    ),
    path(
        "<hash_id:client_id>/other-details/document/<hash_id:doc_id>/delete/",
        ClientOtherDetailDocumentDeleteApi.as_view(),
        name="client-other-details-document-delete-api",
    ),
]
