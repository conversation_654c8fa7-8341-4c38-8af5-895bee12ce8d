from django.urls import include, path

from client.onboard.interface.apis.internal.basic_details import ClientBasicD<PERSON>ilsApi, ClientBasicDetailsUpdateApi
from client.onboard.interface.apis.internal.billing_entity_create import ClientBillingEntityCreateApi
from client.onboard.interface.apis.internal.billing_entity_delete import Client<PERSON>illingEntityDeleteApi
from client.onboard.interface.apis.internal.billing_entity_list import ClientBillingEntityListApi
from client.onboard.interface.apis.internal.billing_entity_mark_active import ClientBillingEntityMarkActiveApi
from client.onboard.interface.apis.internal.billing_entity_mark_default import ClientBillingEntityMarkDefaultApi
from client.onboard.interface.apis.internal.bulk_create import ClientBulk<PERSON>reateApi
from client.onboard.interface.apis.internal.check_poc import CheckClientPocApiV2
from client.onboard.interface.apis.internal.create import ClientCreateApiV2
from client.onboard.interface.apis.internal.document_details import ClientDocumentDetailsApi
from client.onboard.interface.apis.internal.kyc_details import C<PERSON><PERSON>yc<PERSON>eta<PERSON><PERSON>pi
from client.onboard.interface.apis.internal.logo_upload import ClientUploadLogoApi
from client.onboard.interface.apis.internal.onboard_config import ClientOnboardConfigApi
from client.onboard.interface.apis.internal.other_details import ClientOtherDetailsApi
from client.onboard.interface.apis.internal.section_update import ClientOrganizationSectionsUpdateApi

BILLING_ENTITY = [
    path(
        "list/",
        ClientBillingEntityListApi.as_view(),
        name="client-billing-entity-list",
    ),
    path(
        "create/",
        ClientBillingEntityCreateApi.as_view(),
        name="client-billing-entity-create",
    ),
    path(
        "<hash_id:billing_entity_id>/mark-default/",
        ClientBillingEntityMarkDefaultApi.as_view(),
        name="client-billing-entity-mark-default",
    ),
    path(
        "<hash_id:billing_entity_id>/mark-active/",
        ClientBillingEntityMarkActiveApi.as_view(),
        name="client-billing-entity-mark-active",
    ),
    path(
        "<hash_id:billing_entity_id>/delete/",
        ClientBillingEntityDeleteApi.as_view(),
        name="client-billing-entity-delete",
    ),
]

CLIENT = [
    path("basic-details/", ClientBasicDetailsApi.as_view(), name="client-basic-details"),
    path(
        "basic-details/update/",
        ClientBasicDetailsUpdateApi.as_view(),
        name="client-basic-details-update",
    ),
    path("kyc-details/", ClientKycDetailsApi.as_view(), name="client-kyc-details"),
    path(
        "sections/update/",
        ClientOrganizationSectionsUpdateApi.as_view(),
        name="client-sections-update",
    ),
    path("other-details/", ClientOtherDetailsApi.as_view(), name="client-other-details"),
    path("document-details/", ClientDocumentDetailsApi.as_view(), name="client-document-details"),
    path("billing-entity/", include(BILLING_ENTITY)),
    path("logo-upload/", ClientUploadLogoApi.as_view(), name="client-logo-upload"),
]

urlpatterns = [
    path("config/", ClientOnboardConfigApi.as_view(), name="client-onboard-config"),
    path("create/", ClientCreateApiV2.as_view(), name="client-create-api"),
    path("check-client-poc/", CheckClientPocApiV2.as_view(), name="check-client"),
    path("bulk-create-via-excel/", ClientBulkCreateApi.as_view(), name="client-bulk-create-api"),
    path("<hash_id:client_id>/", include(CLIENT)),
]
