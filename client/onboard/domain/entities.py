import datetime
import re
from typing import Optional

from pydantic import EmailStr

from common.pydantic.base_model import BaseModel, BaseModelV2, PydanticInputBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt, PhoneNumberStr
from common.regex import RegularExpression as Regex
from core.entities import CountryData
from core.organization.domain.entities import (
    OrganizationAddressData,
    OrganizationBusinessCardData,
    OrganizationOnboardInputData,
)


class ClientAddressData(OrganizationAddressData):
    pass


class ClientBasicDetailData(BaseModel):
    id: HashIdInt | None = None
    name: str
    logo: Optional[str]
    addresses: list[ClientAddressData]
    country: CountryData


class ClientBusinessCardData(OrganizationBusinessCardData):
    pass


class ClientBasicDetails(BaseModelV2):
    id: HashIdInt
    name: str
    logo: CustomFileUrlStr | None
    business_card: ClientBusinessCardData | None
    addresses: list[ClientAddressData]


class CreateClientInputData(BaseModel):
    name: str
    country_id: Optional[int]
    code: Optional[str] = None


class ClientOnboardInputData(OrganizationOnboardInputData):
    code: Optional[str]
    email: Optional[str]
    company_id: HashIdInt | None = None

    def validate_input_data(self, attrs):
        if attrs.get("id"):
            return attrs
        if attrs.get("code"):
            attrs["code"] = attrs.get("code").upper()
            if not bool(re.match(Regex.CLIENT_CODE, attrs.get("code"))):
                raise ValueError("Invalid client code")
        return attrs


class ClientOnboardInBulkData(BaseModel):
    client_name: str
    tax_number: Optional[str] = None
    uid_number: Optional[str] = None
    user_name: Optional[str] = None
    phone_number: Optional[PhoneNumberStr] = None
    email: Optional[EmailStr] = None
    base_address_line_1: Optional[str] = None
    base_address_line_2: Optional[str] = None
    base_country_name: Optional[str] = None
    base_state_name: Optional[str] = None
    base_city_name: Optional[str] = None
    base_zip_code: Optional[str] = None
    additional_address_line_1: Optional[str] = None
    additional_address_line_2: Optional[str] = None
    additional_country_name: Optional[str] = None
    additional_state_name: Optional[str] = None
    additional_city_name: Optional[str] = None
    additional_zip_code: Optional[str] = None
    aadhar_number: Optional[str] = None
    msme_id: Optional[str] = None


class ClientDocumentDetailUploadedBy(BaseModelV2):
    name: str
    photo: CustomFileUrlStr | None


class ClientDocumentDetail(BaseModelV2):
    id: HashIdInt
    file: CustomFileUrlStr
    name: str
    tags: list[str] | None = []
    uploaded_by: ClientDocumentDetailUploadedBy
    uploaded_at: datetime.datetime


class ClientBillingEntityFilter(PydanticInputBaseModel):
    billing_entity_id: HashIdInt | None = None
