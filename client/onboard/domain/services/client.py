import re
from collections import defaultdict
from typing import <PERSON><PERSON>, Union

import structlog
from django.conf import settings
from django.db import transaction
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as translate
from phonenumber_field.phonenumber import PhoneNumber

from client.data.models import (
    Client,
)
from client.data.selectors import fetch_client_mapping
from client.interface.external.abstract_factories import CompanyToClientServiceAbstractFactory
from client.onboard.data.selectors import (
    get_client_basic_details,
    get_client_mapping,
    get_client_onboard_section_config,
)
from client.onboard.domain.constants import get_client_onboard_data
from client.onboard.domain.entities import (
    ClientAddressData,
    ClientBasicDetailData,
    ClientOnboardInBulkData,
    ClientOnboardInputData,
    CreateClientInputData,
)
from client.onboard.domain.services.users import ClientUserService
from client.onboard.exceptions import ClientCompanyException, ClientException, ClientExceptionConstants
from common.choices import OrganizationType
from common.exceptions import BaseValidation<PERSON>rror
from common.regex import RegularExpression as Regex
from core.caches import CountryListCache
from core.entities import CountryData
from core.models import City, OrganizationConfig, State
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.selectors import (
    get_linked_organization_documents_by_section,
    get_mapping_for_client,
    get_organization_sections_config,
)
from core.organization.domain.entities import (
    CityData,
    DocumentFieldValueBaseData,
    DocumentFileFieldValueData,
    DocumentStateFieldValueData,
    DocumentTextFieldValueData,
    FileData,
    OrganizationAddressUpdateData,
    OrganizationBasicDetailsData,
    OrganizationBasicDetailsUpdateData,
    OrganizationDocumentData,
    OrganizationFieldData,
    OrganizationSectionConfigData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
    StateData,
)
from core.organization.domain.services import (
    OrganizationBaseService,
    OrganizationOnboardBaseService,
    get_org_basic_detail_data,
    get_section_config_data,
)
from core.organization.entities import OnboardUserEntity
from core.organization.manage_cv_services import (
    OrganizationDomainService,
)
from core.organization.models import (
    LinkedOrganizationDocument,
    LinkedOrganizations,
)
from core.selectors import get_country_with_timezone_tax_currency, get_user_by_email
from core.services import (
    organization_create_by_referral_org,
)

CompanyToClientServiceFactory: CompanyToClientServiceAbstractFactory = import_string(
    settings.COMPANY_TO_CLIENT_SERVICE_FACTORY
)


logger = structlog.get_logger(__name__)


def get_client_onboard_config(country_id: int) -> list[OrganizationSectionConfigData]:
    sections_config = get_client_onboard_section_config(country_id=country_id)
    config = []
    for section_config in sections_config:
        config.append(get_section_config_data(section_config))
    return config


class ClientOnboardServiceV2(OrganizationOnboardBaseService):
    DEFAULT_CODE = "RDASH"
    OLD_DEFAULT_CODE = "PROJ"

    class ClientOnboardServiceV2Exception(BaseValidationError):
        pass

    class ClientNotFoundException(ClientOnboardServiceV2Exception):
        pass

    def get_client_details_via_email(self, poc_email: str, org_id: int) -> OrganizationBasicDetailsData:
        poc_user = get_user_by_email(email=poc_email)
        client_domain = OrganizationDomainService.get_org_domain_using_email(email=poc_email)
        client_id = None
        if not poc_user and not client_domain:
            raise self.ClientNotFoundException("Client not found for given email")
        if poc_user and client_domain and poc_user.org_id != client_domain.organization_id:
            logger.info(
                "POC Domain Mismatch",
                poc_user=poc_user.id,
                poc_org_id=poc_user.org_id,
                domain_org_id=client_domain.organization_id,
            )
            raise self.ClientOnboardServiceV2Exception(
                translate("POC User's email and organization domain mismatch found. Please contact support.")
            )
        if poc_user:
            client_id = poc_user.org_id
        if client_domain:
            client_id = client_domain.organization_id
        if client_id == org_id:
            raise self.ClientOnboardServiceV2Exception(translate("Email id belongs to the user of your organization."))
        if fetch_client_mapping(client_id=client_id, organization_id=org_id):
            raise self.ClientOnboardServiceV2Exception(translate("This organization is already in your client list."))
        return get_org_basic_detail_data(org_id=client_id)

    def check_client_code_availability(self, code: str):
        code = code.upper()
        if not bool(re.match(Regex.CLIENT_CODE, code)):
            raise self.ClientOnboardServiceV2Exception(
                translate("Organisation code should be of 5 alphabets, eg. ABCDE")
            )
        if code == self.DEFAULT_CODE:
            raise self.ClientOnboardServiceV2Exception(translate("This code cannot be used."))
        if Client.objects.filter(code=code).exists():
            raise self.ClientOnboardServiceV2Exception(translate("This client Code is not available."))
        return code

    def create_client_org(self, data: CreateClientInputData, org_id: int, user_id: int):
        if data.code:
            self.check_client_code_availability(code=data.code)
        client_organization = organization_create_by_referral_org(
            name=data.name,
            org_type=OrganizationType.CLIENT,
            referral_org_id=org_id,
            referred_by_id=user_id,
            country_id=data.country_id,
        )
        Client.objects.update_or_create(
            organization_id=client_organization.pk,
            defaults={"code": data.code if data.code else self.DEFAULT_CODE},
        )
        return client_organization

    def create_linked_organizations(self, client_id: int, org_id: int, user_id: int) -> int:
        mapping = LinkedOrganizations(
            client_id=client_id,
            vendor_id=org_id,
            org_id=org_id,
            created_by_id=user_id,
        )
        mapping_r = LinkedOrganizations(
            client_id=client_id,
            vendor_id=org_id,
            org_id=client_id,
            created_by_id=user_id,
        )
        mapping, mapping_r = LinkedOrganizations.objects.bulk_create([mapping, mapping_r])
        return mapping.pk

    def onboard_client(self, data: ClientOnboardInputData, user_id: int, org_id: int) -> tuple[int, int]:
        poc_user = None
        is_new_client = False
        if data.id and data.email:
            if get_client_mapping(client_id=data.id, vendor_id=org_id).exists():
                raise ClientException(translate("Client already exists in your client list."))
            poc_user = get_user_by_email(email=data.email)
            if poc_user:
                if poc_user.org_id != data.id:
                    raise ClientException(translate(f"Data mismatch {ClientExceptionConstants.ONBOARDING_NOT_ALLOWED}"))
                client_id = poc_user.org_id
            else:
                organization_domain = OrganizationDomainService.get_org_domain_using_email(email=data.email)
                if organization_domain.organization_id != data.id:
                    raise ClientException(translate(f"Data mismatch {ClientExceptionConstants.ONBOARDING_NOT_ALLOWED}"))
                Client.objects.get_or_create(organization_id=data.id)
                client_id = data.id

        else:
            client = self.create_client_org(
                data=CreateClientInputData(name=data.name, country_id=data.country_id, code=data.code),
                org_id=org_id,
                user_id=user_id,
            )
            client_id = client.id
            is_new_client = True

        country = get_country_with_timezone_tax_currency(
            country_id=data.country_id,
        )
        currency = country.country_currency_mapping.all().first().currency
        tax_type_mapping = country.country_tax_mapping.all().first()
        tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
        timezone = country.country_timezone_mapping.all().first().timezone
        OrganizationConfig.objects.get_or_create(
            organization_id=client_id,
            defaults={
                "timezone": timezone,
                "tax_type": tax_type,
                "currency": currency,
            },
        )
        from_to_org_mapping_id = self.create_from_to_org_mapping(
            client_id=client_id,
            vendor_id=org_id,
            user_id=user_id,
        )
        user_entity = None
        if data.email:
            if poc_user:
                user_entity = OnboardUserEntity(
                    name=poc_user.name, email=poc_user.email, phone_number=poc_user.phone_number, is_invited=False
                )
            else:
                user_entity = OnboardUserEntity(name=None, email=data.email, phone_number=None, is_invited=False)
        if user_entity:
            ClientUserService().add(user=user_entity, mapping_id=from_to_org_mapping_id, created_by_id=user_id)

        linked_mapping_id = self.create_linked_organizations(client_id=client_id, org_id=org_id, user_id=user_id)
        if data.addresses:
            self.create_linked_addresses(addresses_data=data.addresses, mapping_id=linked_mapping_id, user_id=user_id)
        if is_new_client:
            self.create_addresses(addresses_data=data.addresses[:1], org_id=client_id, user_id=user_id)
        if data.company_id:
            service = CompanyToClientServiceFactory.get_service()
            try:
                service.update_company_from_client(company_id=data.company_id, client_id=client_id, user_id=user_id)
            except ClientCompanyException as e:
                logger.info("Failed to update company", company_id=data.company_id, error=e)
                raise self.ClientOnboardServiceV2Exception("Failed to update company")
        return client_id, from_to_org_mapping_id

    def onboard_clients_in_bulk(
        self, clients_data: list[ClientOnboardInBulkData], country_id: int, org_id: int, user_id: int
    ) -> list[tuple]:
        status_list = []
        for client_data in clients_data:
            sid = transaction.savepoint()
            try:
                client = self.create_client_org(
                    data=CreateClientInputData(country_id=country_id, name=client_data.client_name),
                    org_id=org_id,
                    user_id=user_id,
                )
                client_id = client.pk

                country = get_country_with_timezone_tax_currency(
                    country_id=country_id,
                )
                currency = country.country_currency_mapping.all().first().currency
                tax_type_mapping = country.country_tax_mapping.all().first()
                tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
                timezone = country.country_timezone_mapping.all().first().timezone
                OrganizationConfig.objects.get_or_create(
                    organization_id=client_id,
                    defaults={
                        "timezone": timezone,
                        "tax_type": tax_type,
                        "currency": currency,
                    },
                )

                from_to_org_mapping_id = self.create_from_to_org_mapping(
                    client_id=client_id,
                    vendor_id=org_id,
                    user_id=user_id,
                )
                country_mapping = {country.get("name"): country.get("id") for country in CountryListCache.get()}
                states_mapping = {
                    state.name: state.pk
                    for state in State.objects.filter(country_id=country_id, is_active=True).all().order_by("name")
                }
                city_mapping = {
                    city.name: city.pk
                    for city in City.objects.filter(is_active=True, is_verified=True).order_by("name")
                }
                addresses = []
                if any(
                    [
                        client_data.base_address_line_1,
                        client_data.base_address_line_2,
                        client_data.base_city_name,
                        client_data.base_state_name,
                        client_data.base_country_name,
                        client_data.base_zip_code,
                    ]
                ):
                    addresses.append(
                        OrganizationAddressUpdateData(
                            id=None,
                            address_line_1=client_data.base_address_line_1,
                            address_line_2=client_data.base_address_line_2,
                            city_id=city_mapping.get(client_data.base_city_name),
                            state_id=states_mapping.get(client_data.base_state_name),
                            country_id=country_mapping.get(client_data.base_country_name),
                            zip_code=client_data.base_zip_code,
                        ),
                    )
                if any(
                    [
                        client_data.additional_address_line_1,
                        client_data.additional_address_line_2,
                        client_data.additional_city_name,
                        client_data.additional_state_name,
                        client_data.additional_country_name,
                        client_data.additional_zip_code,
                    ]
                ):
                    addresses.append(
                        OrganizationAddressUpdateData(
                            id=None,
                            address_line_1=client_data.additional_address_line_1,
                            address_line_2=client_data.additional_address_line_2,
                            city_id=city_mapping.get(client_data.additional_city_name),
                            state_id=states_mapping.get(client_data.additional_state_name),
                            country_id=country_mapping.get(client_data.additional_country_name),
                            zip_code=client_data.additional_zip_code,
                        )
                    )

                linked_mapping_id = self.create_linked_organizations(
                    client_id=client_id, org_id=org_id, user_id=user_id
                )
                self.create_linked_addresses(addresses_data=addresses, mapping_id=linked_mapping_id, user_id=user_id)
                self.create_addresses(addresses_data=addresses[:1], org_id=client_id, user_id=user_id)
                if client_data.user_name or client_data.phone_number or client_data.email:
                    ClientUserService().add(
                        mapping_id=from_to_org_mapping_id,
                        user=OnboardUserEntity(
                            name=client_data.user_name,
                            email=client_data.email,
                            phone_number=(
                                PhoneNumber.from_string(client_data.phone_number) if client_data.phone_number else None
                            ),
                            is_invited=False,
                        ),
                        created_by_id=user_id,
                    )
            except self.ClientOnboardServiceV2Exception as e:
                status_list.append((client_data.client_name, e))
                transaction.savepoint_rollback(sid)
                continue

            data = get_client_onboard_data(
                country_id=country_id,
                client_data=client_data,
            )
            try:
                ClientOrganizationService(org_id=org_id, client_id=client_id).update_sections_data(
                    data=data, user_id=user_id
                )
            except ClientOrganizationService.OrganizationSectionUpdateException as e:
                status_list.append((client_data.client_name, e))
                sid = transaction.savepoint()
                continue
            transaction.savepoint_commit(sid)
            status_list.append((client_data.client_name, "Client Added"))

        return status_list


class ClientOrganizationService(OrganizationBaseService):
    def __init__(self, org_id: int, client_id: int):
        self.org_id = org_id
        self.client_id = client_id

    def get_basic_details(self) -> ClientBasicDetailData:
        client_org = get_client_basic_details(org_id=self.org_id, client_id=self.client_id)
        address_list = []
        for address in client_org.client_addresses.all():
            address_list.append(
                ClientAddressData(
                    id=address.pk,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city=CityData(id=address.city_id, name=address.city.name) if address.city_id else None,
                    state=StateData(id=address.state_id, name=address.state.name) if address.state_id else None,
                    country=(
                        CountryData(id=address.country_id, name=address.country.name) if address.country_id else None
                    ),
                    zip_code=address.zip_code,
                    value=address.full_address,
                )
            )

        return ClientBasicDetailData(
            id=client_org.pk,
            name=client_org.name,
            logo=client_org.logo.url if client_org.logo.name else None,
            addresses=address_list,
            country=CountryData(id=client_org.country_id, name=client_org.country.name),
        )

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, user_id: int):
        mapping = get_mapping_for_client(org_id=self.org_id, client_id=self.client_id)
        return self._update_basic_details(data=data, user_id=user_id, mapping=mapping)

    def get_kyc_details(self) -> OrganizationSectionData:
        section_config = (
            get_organization_sections_config(org_id=self.client_id)
            .filter(type=OrganizationSectionTypeChoices.KYC_DETAILS)
            .first()
        )
        mapping_id = get_mapping_for_client(org_id=self.org_id, client_id=self.client_id)
        org_docs = get_linked_organization_documents_by_section(
            mapping_id=mapping_id, section_type=OrganizationSectionTypeChoices.KYC_DETAILS
        )
        doc_config_org_doc_mapping: dict[int, list[LinkedOrganizationDocument]] = defaultdict(list)
        for org_doc in org_docs:
            doc_config_org_doc_mapping[org_doc.document_config_id].append(org_doc)

        org_doc_id_field_config_id_value_mapping: dict[Tuple[int, int], DocumentFieldValueBaseData] = {}
        for org_doc in org_docs:
            for field_data in org_doc.text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

            for field_data in org_doc.file_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentFileFieldValueData(
                        id=field_data.pk,
                        data=(
                            FileData(file_name=field_data.file_name, file=field_data.file.url)
                            if field_data.file_name and field_data.file.name
                            else None
                        ),
                    )
                )

            for field_data in org_doc.state_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentStateFieldValueData(
                        id=field_data.pk, data=StateData(id=field_data.state.pk, name=field_data.state.name)
                    )
                )

        documents: list[OrganizationDocumentData] = []
        for doc_config in section_config.documents_config.all():
            fields: list[OrganizationFieldData] = []
            fields_data_list: list[dict[int, DocumentFieldValueBaseData]] = []

            for field_config in doc_config.fields_config.all():
                field = OrganizationFieldData(
                    id=field_config.pk,
                    name=field_config.name,
                    type=field_config.type,
                    position=field_config.position,
                    is_required=field_config.is_required,
                    regex=field_config.regex,
                    is_visible_on_app=field_config.is_visible_on_app,
                    is_capitalized=field_config.is_capitalized,
                )
                fields.append(field)

            for org_doc in doc_config_org_doc_mapping[doc_config.pk]:
                field_data_dict: dict[Union[str, int], Union[int, DocumentFieldValueBaseData]] = {}
                for field_config in doc_config.fields_config.all():
                    if (org_doc.pk, field_config.pk) in org_doc_id_field_config_id_value_mapping:
                        field_data_dict[field_config.pk] = org_doc_id_field_config_id_value_mapping[
                            (org_doc.pk, field_config.pk)
                        ]
                field_data_dict["id"] = org_doc.pk
                fields_data_list.append(field_data_dict)

            document = OrganizationDocumentData(
                id=doc_config.pk,
                name=doc_config.name,
                multiple_allowed=doc_config.multiple_allowed,
                position=doc_config.position,
                is_required=doc_config.is_required,
                fields=fields,
                fields_data=fields_data_list,
                is_visible_on_app=doc_config.is_visible_on_app,
            )
            documents.append(document)

        section = OrganizationSectionData(
            id=section_config.pk,
            name=section_config.name,
            position=section_config.position,
            type=section_config.type,
            documents=documents,
        )
        return section

    def update_sections_data(self, data: OrganizationSectionUpdateData, user_id: int):
        logger.info("Updating client organization section data", org_id=self.org_id, data=data)
        mapping = get_mapping_for_client(org_id=self.org_id, client_id=self.client_id)
        self.update_linked_organization_section_data(data=data, mapping=mapping, user_id=user_id, org_id=self.org_id)
        logger.info("Client sections updated successfully.")
