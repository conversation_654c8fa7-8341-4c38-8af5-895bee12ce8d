import re

import structlog
from django.conf import settings
from django.db import transaction
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as translate
from phonenumber_field.phonenumber import PhoneNumber

from client.data.models import Client
from client.data.selectors import fetch_client_mapping
from client.interface.external.abstract_factories import CompanyToClientServiceAbstractFactory
from client.onboard.data.repositories import ClientLinkedOrganizationRepo
from client.onboard.data.selectors import get_client_mapping
from client.onboard.domain.abstract_repos import ClientLinkedOrganizationAbstractRepo
from client.onboard.domain.constants import get_client_onboard_data
from client.onboard.domain.entities import (
    ClientOnboardInBulkData,
    ClientOnboardInputData,
    CreateClientInputData,
)
from client.onboard.domain.services.users import ClientUserService
from client.onboard.exceptions import ClientCompanyException, ClientException, ClientExceptionConstants
from common.choices import OrganizationType
from common.exceptions import BaseValidation<PERSON>rror
from common.regex import RegularExpression as Regex
from core.caches import CountryListCache
from core.entities import OrgUserEntity
from core.models import City, Organization, OrganizationConfig, State
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.models import LinkedOrganizations
from core.organization.domain.entities import (
    OrganizationAddressUpdateData,
    OrganizationBusinessCardUpdateData,
    OrganizationSectionConfigData,
)
from core.organization.domain.services.linked_organization import LinkedOrganizationService
from core.organization.domain.services.onboard import OrganizationOnboardBaseService
from core.organization.domain.services.organization import get_section_config_data
from core.organization.entities import OnboardUserEntity
from core.organization.manage_cv_services import OrganizationDomainService
from core.selectors import get_country_with_timezone_tax_currency, get_user_by_email
from core.services import organization_create_by_referral_org

CompanyToClientServiceFactory: CompanyToClientServiceAbstractFactory = import_string(
    settings.COMPANY_TO_CLIENT_SERVICE_FACTORY
)


logger = structlog.get_logger(__name__)


class ClientOnboardServiceV2(OrganizationOnboardBaseService):
    DEFAULT_CODE = "RDASH"
    OLD_DEFAULT_CODE = "PROJ"

    class ClientOnboardServiceV2Exception(BaseValidationError):
        pass

    class ClientNotFoundException(ClientOnboardServiceV2Exception):
        pass

    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id

    def get_client_details_via_email(self, poc_email: str):
        poc_user = get_user_by_email(email=poc_email)
        client_domain = OrganizationDomainService.get_org_domain_using_email(email=poc_email)
        client_id = None
        if not poc_user and not client_domain:
            raise self.ClientNotFoundException("Client not found for given email")
        if poc_user and client_domain and poc_user.org_id != client_domain.organization_id:
            logger.info(
                "POC Domain Mismatch",
                poc_user=poc_user.id,
                poc_org_id=poc_user.org_id,
                domain_org_id=client_domain.organization_id,
            )
            raise self.ClientOnboardServiceV2Exception(
                translate("POC User's email and organization domain mismatch found. Please contact support.")
            )
        if poc_user:
            client_id = poc_user.org_id
        if client_domain:
            client_id = client_domain.organization_id
        if client_id == self.org_id:
            raise self.ClientOnboardServiceV2Exception(translate("Email id belongs to the user of your organization."))
        if fetch_client_mapping(client_id=client_id, organization_id=self.org_id):
            raise self.ClientOnboardServiceV2Exception(translate("This organization is already in your client list."))

        return client_id

    def check_client_code_availability(self, code: str):
        code = code.upper()
        if not bool(re.match(Regex.CLIENT_CODE, code)):
            raise self.ClientOnboardServiceV2Exception(
                translate("Organisation code should be of 5 alphabets, eg. ABCDE")
            )
        if code == self.DEFAULT_CODE:
            raise self.ClientOnboardServiceV2Exception(translate("This code cannot be used."))
        if Client.objects.filter(code=code).exists():
            raise self.ClientOnboardServiceV2Exception(translate("This client Code is not available."))
        return code

    def create_client_org(self, data: CreateClientInputData):
        if data.code:
            self.check_client_code_availability(code=data.code)

        client_organization = organization_create_by_referral_org(
            name=data.name,
            org_type=OrganizationType.CLIENT,
            referral_org_id=self.org_id,
            referred_by_id=self.user_id,
            country_id=data.country_id,
        )
        Client.objects.update_or_create(
            organization_id=client_organization.pk,
            defaults={"code": data.code if data.code else self.DEFAULT_CODE},
        )
        return client_organization

    def create_linked_organizations(self, client_id: int):
        mapping = LinkedOrganizations(
            client_id=client_id,
            vendor_id=self.org_id,
            org_id=self.org_id,
            created_by_id=self.user_id,
        )
        mapping_r = LinkedOrganizations(
            client_id=client_id,
            vendor_id=self.org_id,
            org_id=client_id,
            created_by_id=self.user_id,
        )
        mapping, mapping_r = LinkedOrganizations.objects.bulk_create([mapping, mapping_r])
        logger.info("Linked organization mappings created", mapping_id=mapping.pk, mapping_r_id=mapping_r.pk)
        return mapping, mapping_r

    def onboard_client(self, data: ClientOnboardInputData) -> tuple[int, int]:
        poc_user = None
        is_new_client = False
        if data.id and data.email:
            if get_client_mapping(client_id=data.id, vendor_id=self.org_id).exists():
                raise ClientException(translate("Client already exists in your client list."))
            poc_user = get_user_by_email(email=data.email)
            if poc_user:
                if poc_user.org_id != data.id:
                    raise ClientException(translate(f"Data mismatch {ClientExceptionConstants.ONBOARDING_NOT_ALLOWED}"))
                client_id = poc_user.org_id
            else:
                organization_domain = OrganizationDomainService.get_org_domain_using_email(email=data.email)
                if organization_domain.organization_id != data.id:
                    raise ClientException(translate(f"Data mismatch {ClientExceptionConstants.ONBOARDING_NOT_ALLOWED}"))
                Client.objects.get_or_create(organization_id=data.id)
                client_id = data.id

        else:
            client = self.create_client_org(
                data=CreateClientInputData(
                    name=data.name,
                    country_id=data.country_id,
                    code=data.code,
                )
            )
            client_id = client.id
            is_new_client = True

        country = get_country_with_timezone_tax_currency(
            country_id=data.country_id,
        )
        currency = country.country_currency_mapping.all().first().currency
        tax_type_mapping = country.country_tax_mapping.all().first()
        tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
        timezone = country.country_timezone_mapping.all().first().timezone
        OrganizationConfig.objects.get_or_create(
            organization_id=client_id,
            defaults={
                "timezone": timezone,
                "tax_type": tax_type,
                "currency": currency,
            },
        )
        from_to_org_mapping_id = self.create_from_to_org_mapping(
            client_id=client_id,
            vendor_id=self.org_id,
            user_id=self.user_id,
        )
        user_entity = None
        if data.email:
            if poc_user:
                user_entity = OnboardUserEntity(
                    name=poc_user.name, email=poc_user.email, phone_number=poc_user.phone_number, is_invited=False
                )
            else:
                user_entity = OnboardUserEntity(name=None, email=data.email, phone_number=None, is_invited=False)
        if user_entity:
            ClientUserService().add(user=user_entity, mapping_id=from_to_org_mapping_id, created_by_id=self.user_id)

        linked_mapping, reverse_linked_mapping = self.create_linked_organizations(client_id=client_id)
        linked_mapping_id = linked_mapping.pk

        # Create linked billing entity
        linked_billing_entity_id = self.create_linked_billing_entity(
            mapping_id=linked_mapping_id,
            name=data.name,
            user_id=self.user_id,
            country_id=country.pk,
        )
        linked_mapping.primary_billing_entity_id = linked_billing_entity_id
        linked_mapping.save()

        # Create reverse linked billing entity
        current_org = Organization.objects.get(id=self.org_id)
        reverse_linked_billing_entity_id = self.create_linked_billing_entity(
            mapping_id=reverse_linked_mapping.pk,
            name=current_org.name,
            logo=current_org.logo,
            user_id=self.user_id,
            country_id=country.pk,
        )
        reverse_linked_mapping.primary_billing_entity_id = reverse_linked_billing_entity_id
        reverse_linked_mapping.save()

        if data.addresses:
            self.create_linked_addresses(
                addresses_data=data.addresses,
                mapping_id=linked_mapping_id,
                billing_entity_id=linked_billing_entity_id,
                user_id=self.user_id,
            )

        if data.business_card:
            self.create_linked_business_card(
                data=OrganizationBusinessCardUpdateData(
                    id=data.business_card.id,
                    file=data.business_card.file,
                    name=data.business_card.file_name,
                ),
                mapping_id=linked_mapping_id,
                user_id=self.user_id,
                billing_entity_id=linked_billing_entity_id,
            )
        if is_new_client:
            billing_entity_id = self.create_billing_entity(
                org_id=client_id,
                name=data.name,
                country_id=country.pk,
                user_id=self.user_id,
            )

            self.create_addresses(
                addresses_data=data.addresses[:1],
                org_id=client_id,
                user_id=self.user_id,
                billing_entity_id=billing_entity_id,
            )
            if data.business_card:
                self.create_business_card(
                    data=OrganizationBusinessCardUpdateData(
                        id=data.business_card.id,
                        file=data.business_card.file,
                        name=data.business_card.file_name,
                    ),
                    org_id=client_id,
                    user_id=self.user_id,
                    billing_entity_id=billing_entity_id,
                )

        if data.company_id:
            service = CompanyToClientServiceFactory.get_service()
            try:
                service.update_company_from_client(
                    company_id=data.company_id, client_id=client_id, user_id=self.user_id
                )
            except ClientCompanyException as e:
                logger.info("Failed to update company", company_id=data.company_id, error=e)
                raise self.ClientOnboardServiceV2Exception("Failed to update company")
        return client_id, from_to_org_mapping_id

    def onboard_clients_in_bulk(
        self,
        clients_data: list[ClientOnboardInBulkData],
        country_id: int,
    ) -> list[tuple]:
        status_list = []
        user_entity = OrgUserEntity(org_id=self.org_id, user_id=self.user_id)
        for client_data in clients_data:
            sid = transaction.savepoint()
            try:
                client = self.create_client_org(
                    data=CreateClientInputData(
                        country_id=country_id,
                        name=client_data.client_name,
                    ),
                )
                client_id = client.pk

                country = get_country_with_timezone_tax_currency(
                    country_id=country_id,
                )
                currency = country.country_currency_mapping.all().first().currency
                tax_type_mapping = country.country_tax_mapping.all().first()
                tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
                timezone = country.country_timezone_mapping.all().first().timezone
                OrganizationConfig.objects.get_or_create(
                    organization_id=client_id,
                    defaults={
                        "timezone": timezone,
                        "tax_type": tax_type,
                        "currency": currency,
                    },
                )

                from_to_org_mapping_id = self.create_from_to_org_mapping(
                    client_id=client_id,
                    vendor_id=self.org_id,
                    user_id=self.user_id,
                )
                country_mapping = {country.get("name"): country.get("id") for country in CountryListCache.get()}
                states_mapping = {
                    state.name: state.pk
                    for state in State.objects.filter(country_id=country_id, is_active=True).all().order_by("name")
                }
                city_mapping = {
                    city.name: city.pk
                    for city in City.objects.filter(is_active=True, is_verified=True).order_by("name")
                }
                addresses = []
                if any(
                    [
                        client_data.base_address_line_1,
                        client_data.base_address_line_2,
                        client_data.base_city_name,
                        client_data.base_state_name,
                        client_data.base_country_name,
                        client_data.base_zip_code,
                    ]
                ):
                    addresses.append(
                        OrganizationAddressUpdateData(
                            id=None,
                            address_line_1=client_data.base_address_line_1,
                            address_line_2=client_data.base_address_line_2,
                            city_id=city_mapping.get(client_data.base_city_name),
                            state_id=states_mapping.get(client_data.base_state_name),
                            country_id=country_mapping.get(client_data.base_country_name),
                            zip_code=client_data.base_zip_code,
                        ),
                    )
                if any(
                    [
                        client_data.additional_address_line_1,
                        client_data.additional_address_line_2,
                        client_data.additional_city_name,
                        client_data.additional_state_name,
                        client_data.additional_country_name,
                        client_data.additional_zip_code,
                    ]
                ):
                    addresses.append(
                        OrganizationAddressUpdateData(
                            id=None,
                            address_line_1=client_data.additional_address_line_1,
                            address_line_2=client_data.additional_address_line_2,
                            city_id=city_mapping.get(client_data.additional_city_name),
                            state_id=states_mapping.get(client_data.additional_state_name),
                            country_id=country_mapping.get(client_data.additional_country_name),
                            zip_code=client_data.additional_zip_code,
                        )
                    )

                linked_mapping_id = self.create_linked_organizations(client_id=client_id)
                self.create_linked_addresses(
                    addresses_data=addresses,
                    mapping_id=linked_mapping_id,
                    user_id=self.user_id,
                )
                self.create_addresses(addresses_data=addresses[:1], org_id=client_id, user_id=self.user_id)
                if client_data.user_name or client_data.phone_number or client_data.email:
                    ClientUserService().add(
                        mapping_id=from_to_org_mapping_id,
                        user=OnboardUserEntity(
                            name=client_data.user_name,
                            email=client_data.email,
                            phone_number=(
                                PhoneNumber.from_string(client_data.phone_number) if client_data.phone_number else None
                            ),
                            is_invited=False,
                        ),
                        created_by_id=self.user_id,
                    )
            except self.ClientOnboardServiceV2Exception as e:
                status_list.append((client_data.client_name, e))
                transaction.savepoint_rollback(sid)
                continue

            data = get_client_onboard_data(
                country_id=country_id,
                client_data=client_data,
            )
            try:
                service = ClientLinkedOrganizationService(
                    user_entity=user_entity,
                    client_id=client_id,
                    repo=ClientLinkedOrganizationRepo(user_entity=user_entity, client_id=client_id),
                )

                service.update_sections_data(data=data)
            except ClientLinkedOrganizationService.SectionUpdateException as e:
                status_list.append((client_data.client_name, e))
                sid = transaction.savepoint()
                continue
            transaction.savepoint_commit(sid)
            status_list.append((client_data.client_name, "Client Added"))

        return status_list

    def get_onboard_config(self, country_id: int, is_app: bool) -> list[OrganizationSectionConfigData]:
        sections_config = self.get_org_sections_config(country_id=country_id)

        if not is_app:
            sections_config = sections_config.filter(
                type__in=[
                    OrganizationSectionTypeChoices.KYC_DETAILS,
                    OrganizationSectionTypeChoices.OTHER_DETAILS,
                ]
            )
        else:
            sections_config = sections_config.filter(type__in=[OrganizationSectionTypeChoices.KYC_DETAILS])

        config = []
        for section_config in sections_config:
            config.append(get_section_config_data(section_config))
        return config


class ClientLinkedOrganizationService(LinkedOrganizationService):
    def __init__(
        self,
        user_entity: OrgUserEntity,
        client_id: int,
        repo: ClientLinkedOrganizationAbstractRepo,
        billing_entity_id: int | None = None,
    ):
        super().__init__(user_entity=user_entity, repo=repo, billing_entity_id=billing_entity_id)
        self.client_id = client_id

    def get_document_details(self):
        return self.repo.get_document_details()
