from client.onboard.data.repositories import ClientLinkedOrganizationRepo
from client.onboard.domain.services.client import ClientLinkedOrganizationService, ClientOnboardServiceV2
from core.entities import OrgUserEntity
from core.organization.data.repositories import OrganizationRepo
from core.organization.domain.services.organization import OrganizationService


class ClientFactory:
    def __init__(self, user_entity: OrgUserEntity):
        self.user_entity = user_entity

    def get_linked_repo(self, client_id: int):
        return ClientLinkedOrganizationRepo(
            user_entity=self.user_entity,
            client_id=client_id,
        )

    def get_org_repo(self):
        return OrganizationRepo(
            user_entity=self.user_entity,
        )

    def get_linked_service(self, client_id: int, billing_entity_id: int | None = None):
        return ClientLinkedOrganizationService(
            user_entity=self.user_entity,
            client_id=client_id,
            billing_entity_id=billing_entity_id,
            repo=self.get_linked_repo(client_id=client_id),
        )

    def get_onboard_service(self):
        return ClientOnboardServiceV2(user_entity=self.user_entity)

    def get_org_service(self, billing_entity_id: int | None = None):
        return OrganizationService(
            user_entity=self.user_entity,
            repo=self.get_org_repo(),
            billing_entity_id=billing_entity_id,
        )
