from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from phonenumber_field import serializerfields
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from client.data.models import Client
from client.data.selectors import (
    fetch_all_client_mappings_exclude_self_mapping,
    fetch_client,
    fetch_client_using_mapping_id,
    other_detail_documents_and_attachment_get,
    vendor_client_mapping_get,
)
from client.onboard.entities import ClientKycInputEntity
from client.onboard.exceptions import ClientException
from client.onboard.filters import ClientOnboardListFilter
from client.onboard.serializers import (
    ClientInputSerializer,
    ClientOrganizationSerializer,
    ClientOrgDetailsOutputSerializer,
    ClientOrgUpdateInputSerializer,
    ClientOtherDetailSerializer,
    ClientPOCUserSerializer,
    VendorClientSerializer,
)
from client.onboard.services import (
    ClientOnboardService,
    ManageClientOtherDocumentService,
    client_org_details_fetch,
)
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
)
from core.apis import OrgBaseApi
from core.helpers import OrgPermissionHelper
from core.selectors import organization_with_address_gst_pan_get
from core.serializers import (
    OrganizationGSTDataSerializer,
    OrganizationPANUpdateDataSerializer,
    OtherDocumentDataSerializer,
)
from vendorv2.interface.serializers import VendorOtherDetailDocumentOutputSerialzer


class OnboardClientActiveStatusChangeAPI(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        status = serializers.BooleanField()

        class Meta:
            ref_name = "OnboardClientActiveStatusChangeSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="client_onboard_status_change_api",
        operation_summary="Client Status Change API",
    )
    @transaction.atomic
    def put(self, request, mapping_id, *args, **kwargs):
        data = self.validate_input_data()
        ClientOnboardService.set_active_status(
            mapping_id=mapping_id, user_id=request.user.pk, active_status=data.get("status")
        )
        return Response(status=HTTP_200_OK)


class OnboardClientUpdateAPI(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        class PocInputSerializer(BaseSerializer):
            email = serializers.EmailField()
            name = serializers.CharField()
            phone_number = serializerfields.PhoneNumberField()

            class Meta:
                ref_name = "ClientPOCInputSerializer"

        poc = PocInputSerializer()

        organization_name = serializers.CharField(allow_null=True, allow_blank=True)

        class Meta:
            ref_name = "ClientUpdateInputSerializer"

    class OutputSerializer(VendorClientSerializer):
        class Meta(VendorClientSerializer.Meta):
            fields = ("id", "organization", "poc", "is_invited", "is_client_active")

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_onboard_client_update_api",
        operation_summary="Client Update API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        ClientOnboardService.update_client(
            data=data,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )
        return Response(status=HTTP_200_OK)


class OnboardClientCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorClientSerializer):
        class Meta(VendorClientSerializer.Meta):
            fields = ("id", "organization", "poc", "is_invited", "is_client_active")

    input_serializer_class = ClientInputSerializer

    @swagger_auto_schema(
        request_body=ClientInputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_onboard_v2_create_client_api",
        operation_summary="Client Create Api for Onboarding Client v2",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            mapping = ClientOnboardService.create_client(
                data=data,
                organization_id=self.get_organization_id(),
                user_id=request.user.pk,
            )
        except ClientOnboardService.CompanyToClientException as e:
            self.set_response_message("Failed to create client")
            raise e
        client_mapping = fetch_client_using_mapping_id(mapping_id=mapping.id)
        return Response(data=self.OutputSerializer(client_mapping).data, status=HTTP_200_OK)


class CheckClientPocApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        poc_email_id = serializers.EmailField()

        class Meta:
            fields = "poc_email_id"
            ref_name = "CheckClientPocApiInput"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ClientOrganizationSerializer()},
        operation_id="client_onboard_check_client_poc",
        operation_summary="Check if with email, could be POC for a client",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            organization, poc_user = ClientOnboardService.check_poc_user_and_domain_org(
                poc_email_id=data.get("poc_email_id"), vendor_organization_id=self.get_organization_id()
            )

            if organization:
                data = ClientOrganizationSerializer(
                    organization_with_address_gst_pan_get(org_id=organization.id), context={"user": poc_user}
                ).data
            else:
                data = None
                self.set_response_message("Create New Organization")
            return Response(
                data=data,
                status=HTTP_200_OK,
            )
        except ClientException as e:
            if isinstance(e.messages, list):
                self.set_response_message(" ".join(e.messages))
            return Response(status=HTTP_400_BAD_REQUEST)


class OnboardOrganizationDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class OutputSerializer(BaseModelSerializer):
        organization_name = serializers.CharField(source="organization.name")
        poc = serializers.SerializerMethodField()

        def get_poc(self, obj):
            return ClientPOCUserSerializer(obj.organization.config.poc).data

        class Meta:
            ref_name = "OnboardOrganizationDetailsSerializer"
            model = Client
            fields = ("organization_name", "poc")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="client_onboard_organization_details",
        operation_summary="Get Organization Details.",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        client = fetch_client(organization_id=self.get_organization_id())
        return Response(data=self.OutputSerializer(client).data, status=HTTP_200_OK)


class OnboardedClientListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        organization_name = serializers.CharField(required=False)
        poc_name = serializers.CharField(required=False)
        is_invited = serializers.BooleanField(required=False, default=None, allow_null=True)
        is_client_active = serializers.BooleanField(required=False, default=None, allow_null=True)

        class Meta:
            ref_name = "OnboardedClientFilterSerializer"

    class OutputSerializer(VendorClientSerializer):
        class Meta(VendorClientSerializer.Meta):
            fields = ("id", "organization", "poc", "is_invited", "is_client_active")
            ref_name = "ClientOnboardOutputSerializer"

    def get_queryset(self):
        return fetch_all_client_mappings_exclude_self_mapping(organization_id=self.get_organization_id())

    serializer_class = OutputSerializer
    filter_serializer_class = FilterSerializer
    filterset_class = ClientOnboardListFilter

    @swagger_auto_schema(
        # query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="client_onboard_client_list",
        operation_summary="List all Clients of a vendor.",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data, is_paginated = self.get_paginated_data()
        return data


class ClientCodeExistsCheckApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        code = serializers.CharField(max_length=5, min_length=5)

        class Meta:
            ref_name = "ClientCodeExistsCheckApiFilterSerializer"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: ""},
        operation_id="client_onboard_code_exists_check",
        operation_summary="Check Client Code Availability",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        filter_data = self.validate_filter_data()
        code = filter_data.get("code")
        ClientOnboardService.check_client_code_availability(code=code)
        return Response(status=HTTP_200_OK)


class ClientOrgDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: ClientOrgDetailsOutputSerializer()},
        operation_id="client_organization_details_api",
        operation_summary="Client Organization Details API",
    )
    @transaction.atomic
    def get(self, request, client_id, *args, **kwargs):
        vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        organization, is_editable = client_org_details_fetch(client_id=client_id, user=request.user)
        return Response(
            data=ClientOrgDetailsOutputSerializer(organization, context={"is_editable": is_editable}).data,
            status=HTTP_200_OK,
        )


class ClientOtherDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class OutputSerializer(BaseSerializer):
        documents = ClientOtherDetailSerializer(many=True)
        is_editable = serializers.BooleanField()

        class Meta:
            ref_name = "ClientPOCAndOtherDetailsApiOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_organization_other_details_api",
        operation_summary="Client Organization Other Details API",
    )
    @transaction.atomic
    def get(self, request, client_id, *args, **kwargs):
        mapping = vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        is_editable = OrgPermissionHelper.has_permission(user=request.user, permission=Permissions.CAN_EDIT_CLIENT)

        data = {
            "documents": other_detail_documents_and_attachment_get(mapping=mapping),
            "is_editable": is_editable,
        }
        return Response(data=self.OutputSerializer(data).data, status=HTTP_200_OK)


class ClientOrgUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    input_serializer_class = ClientOrgUpdateInputSerializer

    @swagger_auto_schema(
        request_body=ClientOrgUpdateInputSerializer,
        responses={HTTP_200_OK: ClientOrgDetailsOutputSerializer()},
        operation_id="client_organization_data_and_address_update_api",
        operation_summary="Client organization data update Api",
    )
    @transaction.atomic
    def put(self, request, client_id, *args, **kwargs):
        mapping = vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        ClientOnboardService.basic_detail_update(
            data=self.validate_input_data(), organization=mapping.org_from, user_id=request.user.pk
        )

        organization, is_editable = client_org_details_fetch(client_id=client_id, user=request.user)
        return Response(
            data=ClientOrgDetailsOutputSerializer(organization, context={"is_editable": is_editable}).data,
            status=HTTP_200_OK,
        )


class ClientOrgKycUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class InputSerializer(BaseDataclassSerializer):
        pan = OrganizationPANUpdateDataSerializer()
        gst = OrganizationGSTDataSerializer(many=True)

        class Meta:
            ref_name = "ClientOrgKycUpdateApiInputSerializer"
            dataclass = ClientKycInputEntity

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ClientOrgDetailsOutputSerializer()},
        operation_id="client_organization_gst_and_pan_update",
        operation_summary="Client Organization GST and PAN Update API",
    )
    @transaction.atomic
    def put(self, request, client_id, *args, **kwargs):
        mapping = vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        data = self.validate_input_data()
        ClientOnboardService.kyc_detail_update(
            data=data,
            organization=mapping.org_from,
            user_id=request.user.pk,
        )

        organization, is_editable = client_org_details_fetch(client_id=client_id, user=request.user)
        return Response(
            data=ClientOrgDetailsOutputSerializer(organization, context={"is_editable": is_editable}).data,
            status=HTTP_200_OK,
        )


class ClientOtherDetailDocumentCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = OtherDocumentDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorOtherDetailDocumentOutputSerialzer()},
        request_body=OtherDocumentDataSerializer(),
        operation_id="client_other_detail_document_create_api",
        operation_summary="Client Other Document Create API",
    )
    @transaction.atomic
    def post(self, request, client_id, *args, **kwargs):
        mapping = vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        other_doc = ManageClientOtherDocumentService.other_doc_create(
            data=self.validate_input_data(), user_id=request.user.pk, cv_mapping_id=mapping.id
        )
        return Response(ClientOtherDetailSerializer(other_doc).data, status=HTTP_201_CREATED)


class ClientOtherDetailDocumentUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = OtherDocumentDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorOtherDetailDocumentOutputSerialzer()},
        request_body=OtherDocumentDataSerializer(),
        operation_id="client_other_detail_document_update_api",
        operation_summary="Client Other Document Update API",
    )
    @transaction.atomic
    def put(self, request, client_id, doc_id, *args, **kwargs):
        vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        other_doc = ManageClientOtherDocumentService.other_doc_update(
            data=self.validate_input_data(), user_id=request.user.pk, doc_id=doc_id
        )
        return Response(ClientOtherDetailSerializer(other_doc).data, status=HTTP_201_CREATED)


class ClientOtherDetailDocumentDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        operation_id="client_other_detail_document_delete_api",
        operation_summary="Client Other Document Delete API",
    )
    @transaction.atomic
    def delete(self, request, client_id, doc_id, *args, **kwargs):
        vendor_client_mapping_get(client_id=client_id, vendor_id=self.get_organization_id())
        ManageClientOtherDocumentService.other_doc_delete(doc_id=doc_id)
        return Response(status=HTTP_201_CREATED)
