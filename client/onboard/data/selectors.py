from datetime import timedelta

from django.db.models import Case, F, OuterRef, Q, Subquery, Value, When
from django.utils import timezone

from client.data.models import ClientPoc, VendorClientMapping
from core.models import UserLoginHistory
from core.organization.enums import UserStatus


def client_users_get(mapping_id: int):
    subquery = UserLoginHistory.objects.filter(user_id=OuterRef("user_id")).order_by("-id").values("login_at")[:1]
    return (
        ClientPoc.objects.available()
        .filter(mapping_id=mapping_id)
        .annotate(
            last_login_at=Subquery(subquery),
            active_status=Case(
                When(is_invited=False, then=None),
                When(
                    ~Q(user__org_id=F("mapping__org_from_id")),
                    is_invited=True,
                    user_id__isnull=False,
                    then=Value(UserStatus.LEFT.value),
                ),
                When(
                    user__org_id=F("mapping__org_from_id"),
                    is_invited=True,
                    user_id__isnull=False,
                    user__deleted_at__isnull=False,
                    then=Value(UserStatus.LEFT.value),
                ),
                When(
                    is_invited=True,
                    user_id__isnull=False,
                    last_login_at__isnull=False,
                    user__org_id=F("mapping__org_from_id"),
                    last_login_at__gt=timezone.now() - timedelta(days=90),
                    then=Value(UserStatus.ACTIVE.value),
                ),
                default=Value(UserStatus.INACTIVE.value),
            ),
        )
        .annotate(has_left_org=Case(When(active_status=UserStatus.LEFT.value, then=True), default=False))
    )


def get_client_mapping(client_id: int, vendor_id: int):
    return VendorClientMapping.objects.filter(org_from_id=client_id, org_to_id=vendor_id).available()
