from django.db.models import F

from client.data.models import ClientOtherDetailDocument, VendorClientMapping
from client.onboard.domain.abstract_repos import ClientLinkedOrganizationAbstractRepo
from client.onboard.domain.entities import (
    ClientDocumentDetail,
    ClientDocumentDetailUploadedBy,
)
from core.entities import OrgUserEntity
from core.organization.data.models import LinkedOrganizations
from core.organization.data.repositories import LinkedOrganizationRepo


class ClientLinkedOrganizationRepo(ClientLinkedOrganizationAbstractRepo, LinkedOrganizationRepo):
    def __init__(self, user_entity: OrgUserEntity, client_id: int):
        self.client_id = client_id
        super().__init__(user_entity)

    def get_mapping(self) -> LinkedOrganizations:
        mapping = (
            LinkedOrganizations.objects.filter(client_id=self.client_id, vendor_id=self.org_id, org_id=self.org_id)
            .select_related("client__country__uid_field")
            .annotate(
                uid_field_id=F("client__country__uid_field_id"),
                uid_field_name=F("client__country__uid_field__name"),
            )
            .first()
        )
        if not mapping:
            raise self.MappingNotFound("Linked organization mapping not found.")
        return mapping

    def get_document_details(self) -> list[ClientDocumentDetail]:
        fromToOrgMapping = (
            VendorClientMapping.objects.filter(
                org_from_id=self.client_id,
                org_to_id=self.org_id,
            )
            .available()
            .values("id")
            .first()
        )

        if not fromToOrgMapping:
            raise self.MappingNotFound("Vendor client mapping not found.")

        documents = ClientOtherDetailDocument.objects.filter(cv_mapping=fromToOrgMapping["id"], deleted_at__isnull=True)

        client_documents = []

        for doc in documents:
            client_documents.append(
                ClientDocumentDetail(
                    id=doc.pk,
                    name=doc.name,
                    file=doc.file.url,
                    tags=doc.tags,
                    uploaded_at=doc.created_at,
                    uploaded_by=ClientDocumentDetailUploadedBy(
                        name=doc.created_by.name,
                        photo=doc.created_by.photo.url if doc.created_by.photo else None,
                    ),
                )
            )
        return client_documents
