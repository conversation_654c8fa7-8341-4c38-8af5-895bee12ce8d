from enum import Enum


class ClientFields(Enum):
    QUANTITY = "quantity"
    UOM = "uom"
    RATE = "rate"
    BASE_AMOUNT = "base_amount"
    SERVICE_CHARGE = "service_charge"
    GROSS_AMOUNT = "gross_amount"
    DISCOUNT = "discount"
    AMOUNT_WITHOUT_TAX = "amount_without_tax"
    HSN = "hsn"
    TAX = "tax"
    FINAL_AMOUNT = "final_amount"
    SECTION_TOTAL = "section_total"
    TOTAL_BASE_AMOUNT = "total_base_amount"
    TOTAL_SERVICE_CHARGE = "total_service_charge"
    TOTAL_GROSS_AMOUNT = "total_gross_amount"
    TOTAL_DISCOUNT = "total_discount"
    TOTAL_AMOUNT_WITHOUT_TAX = "total_amount_without_tax"
    NET_AMOUNT = "net_amount"
    CLIENT_RATE = "client_rate"
    BUDGET_RATE = "budget_rate"


class ClientFieldTypes(Enum):
    COLUMN = "column"
    SUMMARY = "summary"
