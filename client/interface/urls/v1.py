from django.urls import path

from client.interface import apis
from client.interface.config_apis import (
    ClientFieldsSettingApi,
    ClientFieldsVisibilityUpdateApi,
    ClientFieldVisibilityUpdateApi,
)

urlpatterns = [
    path("<hash_id:client_id>/store-types/", apis.ClientStoreTypeListApi.as_view(), name="client-store-type-list"),
    path("create/", apis.ClientCreateApi.as_view(), name="client-create-api"),
    path("add-brand/", apis.BrandAddApi.as_view(), name="brand-add-api"),
    path("<hash_id:client_id>/update/", apis.ClientUpdateApi.as_view(), name="client-update-api"),
    path("list/", apis.ClientListApi.as_view(), name="client-list-api"),
    path("v2/list/", apis.ProjectClientListApi.as_view(), name="project-client-list-api"),
    path(
        "config/client-field-settings/",
        ClientFieldsSettingApi.as_view(),
        name="config-client-field-settings",
    ),
    path(
        "config/update-client-field-visibility/",
        ClientFieldVisibilityUpdateApi.as_view(),
        name="config-client-field-visibility-update",
    ),
    path(
        "config/update-client-fields-visibility/",
        ClientFieldsVisibilityUpdateApi.as_view(),
        name="config-client-field-visibility-update",
    ),
]
