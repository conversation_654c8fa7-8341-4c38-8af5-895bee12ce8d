from django.db.models import TextChoices

from client.domain.constants import ClientFields, ClientFieldTypes


class ClientFieldChoices(TextChoices):
    quantity = ClientFields.QUANTITY.value, "Quantity"
    uom = ClientFields.UOM.value, "UOM"
    rate = ClientFields.RATE.value, "Rate"
    base_amount = ClientFields.BASE_AMOUNT.value, "Base Amount"
    service_charge = ClientFields.SERVICE_CHARGE.value, "Service Charge"
    gross_amount = ClientFields.GROSS_AMOUNT.value, "Gross Amount"
    discount = ClientFields.DISCOUNT.value, "Discount"
    amount_without_tax = ClientFields.AMOUNT_WITHOUT_TAX.value, "Amount Without Tax"
    hsn = ClientFields.HSN.value, "HSN"
    tax = ClientFields.TAX.value, "Tax"
    final_amount = ClientFields.FINAL_AMOUNT.value, "Final Amount"
    section_total = ClientFields.SECTION_TOTAL.value, "Section Total"
    total_base_amount = ClientFields.TOTAL_BASE_AMOUNT.value, "Total Base Amount"
    total_service_charge = ClientFields.TOTAL_SERVICE_CHARGE.value, "Total Service Charge"
    total_gross_amount = ClientFields.TOTAL_GROSS_AMOUNT.value, "Total Gross Amount"
    total_discount = ClientFields.TOTAL_DISCOUNT.value, "Total Discount"
    total_amount_without_tax = ClientFields.TOTAL_AMOUNT_WITHOUT_TAX.value, "Total Amount w/o Tax"
    net_amount = ClientFields.NET_AMOUNT.value, "Net Amount"
    client_rate = ClientFields.CLIENT_RATE.value, "Client Rate"
    budget_rate = ClientFields.BUDGET_RATE.value, "Budget Rate"


class ClientFieldTypeChoices(TextChoices):
    column = ClientFieldTypes.COLUMN.value, "Column"
    summary = ClientFieldTypes.SUMMARY.value, "Summary"
