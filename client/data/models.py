import re

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Exists, OuterRef, Q, QuerySet, UniqueConstraint
from django.db.utils import IntegrityError
from django.utils.translation import gettext_lazy as _

from client.data.choices import ClientFieldChoices, ClientFieldTypeChoices
from client.data.querysets import ClientHiddenFieldQueryset
from client.onboard.data.querysets import ClientPocQueryset
from common.mixins import NameModelMixin
from common.models import (
    CreateDeleteModel,
    CreateModel,
    CreateUpdateDeleteModel,
    DocumentAndAttachmentsModel,
)
from common.querysets import AvailableQ<PERSON>ySetMixin
from common.regex import RegularExpression
from core.models import BasePOCModel, FromToOrgMapping, Organization
from smtp_email.data.models import EmailBase


class Client(models.Model):
    organization = models.OneToOneField(
        Organization, verbose_name=_("organization"), primary_key=True, on_delete=models.CASCADE, related_name="client"
    )

    code = models.CharField(_("code"), default="RDASH", max_length=5, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        errors = dict()
        if len(self.code) not in [4, 5]:
            errors["code"] = _("Client code must be of 5 characters.")
        if self.code:
            is_regex_matched = bool(re.match(RegularExpression.CLIENT_CODE_CONTROLROOM, self.code))
            if not is_regex_matched:
                errors["code"] = _("The Client should have alphabets only.")

        if len(errors):
            raise ValidationError(errors)
        self.code = self.code.upper() if self.code else None

    def save(self, *args, **kwargs):
        try:
            is_regex_matched = bool(re.match("^[A-Z]{4,5}$", self.code))
            if not is_regex_matched:
                raise ValidationError({"code": _("The Client should have alphabets only.")})
            super(Client, self).save(*args, **kwargs)
        except IntegrityError as e:
            if "core_client_code_unique" in str(e):
                raise ValidationError(
                    {
                        "code": _(
                            f"Client with code '{self.code.upper()}' already exists.\n "
                            f"Kindly try other 5 letter code."
                        )
                    }
                )

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=("code",),
                condition=~Q(code__in=["PROJ", "RDASH"]),
                name="core_client_code_unique",
            ),
        ]


class StoreType(NameModelMixin, CreateUpdateDeleteModel):
    CUSTOM_INDEX = "client_store_types_client_id_name_lowercase_unique"
    client = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="store_types")
    name = models.CharField(max_length=100)
    check_list = models.JSONField(default=list, blank=True)

    def save(self, *args, **kwargs):
        try:
            super(StoreType, self).save(*args, **kwargs)
        except IntegrityError as e:
            if self.CUSTOM_INDEX in str(e):
                raise ValidationError({"name": _("Store type with this name already exists for this client.")})
            raise

    class Meta:
        db_table = "client_store_types"


class VendorClientMapping(FromToOrgMapping):
    class VendorClientMappingQueryset(QuerySet, AvailableQuerySetMixin):
        def annotate_is_users_invited(self):
            return self.annotate(
                is_users_invited=Exists(
                    ClientPoc.objects.filter(mapping_id=OuterRef("id"), is_invited=True).available()
                ),
            )

    def save(self, *args, **kwargs):
        if self.org_from_id == self.org_to_id:
            raise ValidationError("Client and Vendor cannot be same")
        super().save(*args, **kwargs)

    objects = VendorClientMappingQueryset.as_manager()

    @property
    def client(self):
        return self.org_from

    @client.setter
    def client(self, client):
        self.org_from = client

    @property
    def vendor(self):
        return self.org_to

    @vendor.setter
    def vendor(self, vendor):
        self.org_to = vendor

    @property
    def client_id(self):
        return self.org_from_id

    @client_id.setter
    def client_id(self, client_id):
        self.org_from_id = client_id

    @property
    def vendor_id(self):
        return self.org_to_id

    @vendor_id.setter
    def vendor_id(self, vendor_id):
        self.org_to_id = vendor_id

    class Meta:
        proxy = True


class ClientEmail(EmailBase):
    sent_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    client = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    context = models.JSONField(default=dict)

    class Meta:
        db_table = "client_onboard_email"


class ClientOtherDetailDocument(DocumentAndAttachmentsModel):
    cv_mapping = models.ForeignKey(FromToOrgMapping, related_name="client_other_detail_docs", on_delete=models.RESTRICT)

    class Meta:
        db_table = "client_other_detail_documents"


class ClientInviteData(CreateModel):
    cv_mapping = models.ForeignKey(FromToOrgMapping, related_name="+", on_delete=models.RESTRICT)
    poc_data = models.JSONField(default=dict)

    class Meta:
        db_table = "client_invite_data"


class ClientPoc(BasePOCModel):
    objects = ClientPocQueryset.as_manager()

    class Meta:
        db_table = "client_poc"
        verbose_name = "Client POC"
        verbose_name_plural = "Client POC's"
        constraints = [
            models.UniqueConstraint(
                name="client_poc_mapping_user_unique_together",
                fields=["mapping_id", "user_id"],
                condition=Q(user__isnull=False, deleted_at__isnull=True),
            )
        ]


class ClientHiddenField(CreateDeleteModel):
    """
    This model will store fields which needs to be hidden from client organization.
    """

    type = models.CharField(choices=ClientFieldTypeChoices.choices, max_length=10)
    field = models.CharField(choices=ClientFieldChoices.choices, max_length=40)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="client_hidden_fields")

    objects = ClientHiddenFieldQueryset.as_manager()

    class Meta:
        db_table = "client_hidden_fields"
        verbose_name = "Client Hidden Field"
        verbose_name_plural = "Client Hidden Fields"
        constraints = [
            models.UniqueConstraint(
                name="client_hidden_field_org_unique_together",
                fields=["organization_id", "field"],
                condition=Q(deleted_at__isnull=True),
            )
        ]
