from django.db.models import Count, F, OuterRef, Q, Subquery, Sum, Value
from django.db.models.functions import JSONObject

from client.data.models import Client, ClientOtherDetailDocument, ClientPoc, VendorClientMapping
from core.models import OrganizationUser, UserLoginHistory
from core.organization.data.models import LinkedOrganizationBillingEntity, LinkedOrganizations


def primary_user_subquery():
    poc_subquery = (
        ClientPoc.objects.filter(mapping_id=OuterRef("id"), is_primary=True)
        .available()
        .order_by("created_at")
        .annotate(primary_user=JSONObject(name="name", phone_number="phone_number", email="email"))
        .values("primary_user")[:1]
    )
    return poc_subquery


def fetch_all_client_mapping(organization_id: int):
    linked_org_subquery = Subquery(
        LinkedOrganizations.objects.filter(
            client_id=OuterRef("org_from_id"),
            vendor_id=OuterRef("org_to_id"),
            org_id=OuterRef("org_to_id"),
        )
        .annotate(
            billing_entity_name=Subquery(
                LinkedOrganizationBillingEntity.objects.filter(mapping_id=OuterRef("id"), is_primary=True).values(
                    "name"
                )[:1]
            )
        )
        .values("billing_entity_name")[:1]
    )

    return (
        VendorClientMapping.objects.filter(org_to_id=organization_id)
        .annotate(primary_user=Subquery(primary_user_subquery()))
        .annotate(billing_entity_name=linked_org_subquery)
        .annotate_is_users_invited()
        .select_related("client_poc__org", "org_from")
        .prefetch_related("org_from__client")
        .available()
        .order_by("-is_client_active", "org_from__name", F("created_at").desc(nulls_last=True))
    )


def is_primary_client(poc_user_name: str) -> bool:
    return ClientPoc.objects.filter(name=poc_user_name, is_primary=True, deleted_at__isnull=True).exists()


def fetch_all_client_mappings_exclude_self_mapping(organization_id: int):
    return fetch_all_client_mapping(organization_id).exclude(org_to_id=F("org_from_id"))


def org_mappings_subquery():
    org_mappings = (
        VendorClientMapping.objects.filter(
            Q(org_from_id=OuterRef("org_from_id")) | Q(org_to_id=OuterRef("org_from_id"))
        )
        .available()
        .values(row=Value(1))
        .annotate(records=Sum("row"))
        .values("records")
    )
    return org_mappings


def org_user_count_subquery():
    org_user_count = (
        OrganizationUser.objects.select_related("user")
        .filter(organization_id=OuterRef("org_from_id"), user__deleted_at__isnull=True)
        .values("organization_id")
        .annotate(org_user_count=Count("organization_id"))
        .values("org_user_count")
    )
    return org_user_count


def user_is_admin_subquery():
    user_is_admin_and_not_login = (
        OrganizationUser.objects.select_related("user")
        .filter(
            organization_id=OuterRef("org_from_id"),
            is_admin=True,
            user__deleted_at__isnull=True,
            user_id=OuterRef("client_poc_id"),
        )
        .values(row=Value(1))
        .annotate(records=Sum("row"))
        .values("records")
    )
    return user_is_admin_and_not_login


def user_not_logged_in_subquery():
    return UserLoginHistory.objects.filter(user_id=OuterRef("client_poc_id")).values("user_id")


def fetch_client(organization_id: int):
    return (
        Client.objects.filter(organization_id=organization_id)
        .select_related("organization", "organization__config__poc")
        .first()
    )


def fetch_client_mapping(client_id: int, organization_id: int):
    return (
        VendorClientMapping.objects.filter(org_from_id=client_id, org_to_id=organization_id)
        .select_related("org_from")
        .available()
        .first()
    )


def fetch_client_using_mapping_id(mapping_id: int):
    return (
        VendorClientMapping.objects.filter(id=mapping_id)
        .select_related("org_from")
        .prefetch_related("org_from__client")
        .available()
        .first()
    )


def vendor_client_mapping_get(client_id: int, vendor_id: int):
    mapping = fetch_client_mapping(client_id=client_id, organization_id=vendor_id)
    return mapping


def other_detail_documents_and_attachment_get(mapping: VendorClientMapping):
    return ClientOtherDetailDocument.objects.filter(cv_mapping=mapping, deleted_at__isnull=True)
