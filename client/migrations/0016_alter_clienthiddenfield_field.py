# Generated by Django 3.2.15 on 2025-07-14 05:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('client', '0015_alter_clientotherdetaildocument_file'),
    ]

    operations = [
        migrations.AlterField(
            model_name='clienthiddenfield',
            name='field',
            field=models.CharField(choices=[('quantity', 'Quantity'), ('uom', 'UOM'), ('rate', 'Rate'), ('base_amount', 'Base Amount'), ('service_charge', 'Service Charge'), ('gross_amount', 'Gross Amount'), ('discount', 'Discount'), ('amount_without_tax', 'Amount Without Tax'), ('hsn', 'HSN'), ('tax', 'Tax'), ('final_amount', 'Final Amount'), ('section_total', 'Section Total'), ('total_base_amount', 'Total Base Amount'), ('total_service_charge', 'Total Service Charge'), ('total_gross_amount', 'Total Gross Amount'), ('total_discount', 'Total Discount'), ('total_amount_without_tax', 'Total Amount w/o Tax'), ('net_amount', 'Net Amount'), ('client_rate', 'Client Rate'), ('budget_rate', 'Budget Rate')], max_length=40),
        ),
    ]
