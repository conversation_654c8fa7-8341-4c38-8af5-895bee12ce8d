from abc import abstractmethod

from core.models import User


class OrgSyncExcelMixin:
    @classmethod
    @abstractmethod
    def load_data_and_sync(cls, module_pk: int, org_from_id: int, user_id: int):
        pass

    @classmethod
    @abstractmethod
    def update_into_db(
        cls, excel_sheet_url: str, excel_meta: dict, module_pk: int, user_id: int, org_id: int, user: User
    ):
        pass
