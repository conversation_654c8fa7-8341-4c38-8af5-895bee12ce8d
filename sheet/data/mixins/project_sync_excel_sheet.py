from abc import abstractmethod

from core.models import User


class ProjectSyncExcelMixin:
    @classmethod
    @abstractmethod
    def load_data_and_sync(cls, org_from_id: int, module_pk: int, project_id: int, org_filter: dict, user_id: int):
        pass

    @classmethod
    @abstractmethod
    def update_into_db(
        cls,
        excel_sheet_url: str,
        excel_meta: dict,
        module_pk: int,
        user_id: int,
        org_id: int,
        org_filter: dict,
        user: User,
        project_id: int,
    ):
        pass
