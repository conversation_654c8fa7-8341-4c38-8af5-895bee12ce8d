from django.db import models

from core.models import Organization
from microcontext.choices import MicroContextChoices
from project.data.models import Project


class SheetBase(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    module = models.CharField(max_length=100, choices=MicroContextChoices.choices)
    module_pk = models.IntegerField()
    excel_sheet = models.CharField(max_length=200, null=True, blank=True)
    excel_meta = models.JSONField(default=dict, null=True, blank=True)

    class Meta:
        abstract = True


class OrganizationSheets(SheetBase):
    class Meta:
        db_table = "organization_sheets"
        unique_together = ["organization", "module", "module_pk"]


class ProjectSheets(SheetBase):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT)

    class Meta:
        db_table = "project_sheets"
        unique_together = ["organization", "project", "module", "module_pk"]
