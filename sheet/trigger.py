from functools import partial
from typing import Optional

from django.db.transaction import on_commit

from common.events.constants import Events
from common.events.services import trigger_event
from common.events.sheet import ProjectSyncSheetCreatedEventData


def trigger_project_sheet_created(
    user_id: int, context_id: int, context: str, project_id: int, sheet_url: Optional[str]
):
    on_commit(
        partial(
            trigger_event,
            event=Events.PROJECT_SYNC_SHEET_CREATED,
            event_data=ProjectSyncSheetCreatedEventData(
                user_id=user_id,
                context_id=context_id,
                context=context,
                project_id=project_id,
                sheet_url=sheet_url,
            ),
        )
    )
