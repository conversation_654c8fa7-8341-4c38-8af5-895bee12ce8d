import gspread
from celery import shared_task
from django.conf import settings
from django.utils.translation import gettext as _
from gspread import spreadsheet
from gspread_dataframe import get_as_dataframe
from pandas import DataFrame

from boq.data.models import Boq
from common.entities import ObjectStatus
from common.excel_file_generation import excel_data_file_with_no_record
from common.excel_sync import SyncExcelBase
from core.models import User
from microcontext.choices import MicroContextChoices
from ratecontract.data.models import RateContractElement
from ratecontract.domain.entities import RateContractElementBaseData
from ratecontract.domain.services import RateContractElementUpdateService
from ratecontract.serializers.model_serializers import RateContractElementSerializer
from sheet.data.mixins.org_sync_excel_sheet import OrgSyncExcelMixin
from sheet.data.models import OrganizationSheets
from sheet.interface.exceptions import (
    CellContainsEmptyValueException,
    SyncSheetDoesNotExistsException,
)
from sheet.interface.serializers import RateContractElementOutputSerializer

gc = gspread.service_account_from_dict(settings.GOOGLE_SERVICE_ACCOUNT_KEY)


class RateContractSyncExcel(OrgSyncExcelMixin, SyncExcelBase):
    sheets = {
        "Rate Contract Details": {
            "pk_column": "Element Code",
            "editable_columns": {"Rate": {"type": "int"}},
        },
    }
    df_col_length = 30
    module = MicroContextChoices.RATE_CONTRACT.value

    @classmethod
    def export_file_name(cls, name):
        return f"Rc_{name}_excel"

    @classmethod
    def sync_excel(
        cls,
        obj: spreadsheet,
        generated_excel_data,
        instance: Boq,
        to_lock_extra_rows: bool = True,
    ):
        for sheet in cls.sheets:
            sheet_instance = obj.get_worksheet_by_id(instance.excel_meta.get(sheet))

            google_sheet_df = get_as_dataframe(sheet_instance).iloc[:, : cls.df_col_length]

            generated_df = cls.create_generated_df(generated_excel_data=generated_excel_data, sheet_name=sheet)

            cls.merge_excel_data(
                sheet_instance=sheet_instance,
                google_sheet_df=google_sheet_df,
                generated_df=generated_df,
                pk_column=cls.sheets[sheet].get("pk_column", None),
                sheet_obj=obj,
                image_fields=cls.sheets[sheet].get("image_fields", []),
                editable_columns=cls.sheets[sheet].get("editable_columns", []),
                to_lock_extra_rows=to_lock_extra_rows,
            )

    @shared_task
    def load_data_and_sync(module_pk: int, org_from_id: int, user_id: int):
        module = MicroContextChoices.RATE_CONTRACT.value
        sheet_instance, _ = OrganizationSheets.objects.get_or_create(
            organization_id=org_from_id, module=module, module_pk=module_pk
        )
        if not sheet_instance.excel_sheet:
            raise SyncSheetDoesNotExistsException(_("Sheet Does Not Exists to update"))

        elements = RateContractElement.objects.filter(rate_contract_id=module_pk).select_related(
            "element", "element__category", "element__item_type"
        )

        excel_data = [
            {
                "sheet_title": list(RateContractSyncExcel.sheets.keys())[0],
                "sheet_data": (
                    RateContractElementSerializer(elements, many=True).data
                    if elements
                    else excel_data_file_with_no_record(sheet_name=list(RateContractSyncExcel.sheets.keys())[0])
                ),
                "with_serial_number": bool(elements),
                "image-fields": [],
            }
        ]

        sheet = gc.open_by_url(sheet_instance.excel_sheet)

        RateContractSyncExcel.sync_excel(
            obj=sheet, generated_excel_data=excel_data, instance=sheet_instance, to_lock_extra_rows=False
        )

    @classmethod
    def update_into_db(
        cls,
        excel_sheet_url: str,
        excel_meta: dict,
        module_pk: int,
        user_id: int,
        org_id: int,
        user: User,
    ):
        data_to_update: dict = cls.load_dataframes_to_update(excel_sheet_url=excel_sheet_url, excel_meta=excel_meta)

        for sheet_name, df_from_sheet in data_to_update.items():
            pk_column = cls.sheets[sheet_name]["pk_column"]
            if df_from_sheet.empty:
                continue
            objs = RateContractElement.objects.filter(rate_contract_id=module_pk).select_related("element")

            df_from_db = DataFrame(list(RateContractElementOutputSerializer(objs, many=True).data))

            df_from_sheet = df_from_sheet.rename(
                columns=lambda col_name: (
                    col_name.replace(" ", "_").lower() if len(col_name.split(" ")) > 1 else col_name.lower()
                )
            )
            pk_column = pk_column.replace(" ", "_").lower() if len(pk_column.split(" ")) > 1 else pk_column.lower()

            df_from_sheet = df_from_sheet[df_from_sheet[pk_column].isin(df_from_db[pk_column])]

            df_from_sheet = df_from_sheet.merge(df_from_db, how="left", on=[pk_column]).astype(str)
            to_update_elements = []

            for index, row in df_from_sheet.iterrows():
                if row["rate"].lower().strip() == "nan":
                    raise CellContainsEmptyValueException(_("Rates can not be empty"))
                to_update_elements.append(
                    RateContractElementBaseData(
                        id=row["id"],
                        element_id=row["element_id"],
                        rate=row["rate"],
                        object_status=ObjectStatus.UPDATE.value,
                    )
                )

            RateContractElementUpdateService().update(data=to_update_elements, user_id=user_id)
