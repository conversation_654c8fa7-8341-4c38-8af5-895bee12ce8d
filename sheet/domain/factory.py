from boq.data.models import <PERSON><PERSON>
from boq.domain.sync_file import B<PERSON>QSyncExcel
from common.excel_sync import SyncExcelBase
from common.models import BaseModel
from element.data.models import ElementLibrary
from element.interface.sync_file import ElementLibrarySyncExcel
from microcontext.choices import MicroContextChoices
from order.data.models import Vendor<PERSON>rder
from order.interface.sync_file import Vendor<PERSON>rderSyncExcel
from ratecontract.data.models import RateContract
from sheet.domain.base_class.rate_contract import RateContractSyncExcel

model_choices = {
    MicroContextChoices.RATE_CONTRACT.value: RateContract,
    MicroContextChoices.ELEMENT_LIBRARY.value: ElementLibrary,
    MicroContextChoices.BOQ.value: Boq,
    MicroContextChoices.ORDER.value: VendorOrder,
}

base_class_choices = {
    MicroContextChoices.RATE_CONTRACT.value: RateContractSyncExcel,
    MicroContextChoices.ELEMENT_LIBRARY.value: ElementLibrarySyncExcel,
    MicroContextChoices.BOQ.value: BOQSyncExcel,
    MicroContextChoices.ORDER.value: VendorOrderSyncExcel,
}


def _model_factory(*, module) -> BaseModel:
    try:
        return model_choices[module]
    except KeyError:
        raise Exception(f"No model found for module name {module}")


def base_class_factory(*, module) -> SyncExcelBase:
    try:
        return base_class_choices[module]
    except KeyError:
        raise Exception(f"No base class found for module name {module}")
