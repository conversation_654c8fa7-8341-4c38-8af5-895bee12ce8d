from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.apis import Base<PERSON><PERSON>
from common.excel_sync import SyncExcelBase
from project.interface.apis.internal.apis import ProjectBaseApi
from sheet.data.models import OrganizationSheets, ProjectSheets
from sheet.domain.factory import base_class_factory
from sheet.interface.exceptions import InvalidCellDataException, SyncSheetDoesNotExistsException


class UpdateViaSyncSheetApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: "link"},
        operation_id="update_via_sheet_sync",
        operation_summary="update via sheet sync",
    )
    @transaction.atomic
    def post(self, request, module_name, module_pk, *args, **kwargs):
        excel_class: SyncExcelBase = base_class_factory(module=module_name)
        org_from_id = self.get_organization_id()

        sheet_instance = OrganizationSheets.objects.filter(
            organization_id=org_from_id, module=module_name, module_pk=module_pk
        ).first()

        if not sheet_instance.excel_sheet:
            raise SyncSheetDoesNotExistsException(_("Sheet Does Not Exists to update"))
        try:
            excel_class.update_into_db(
                excel_sheet_url=sheet_instance.excel_sheet,
                excel_meta=sheet_instance.excel_meta,
                module_pk=module_pk,
                user_id=request.user.id,
                org_id=self.get_organization_id(),
                user=request.user,
            )
        except InvalidCellDataException as e:
            self.set_response_message(_("Invalid Tax Cell Data"))
            raise ValidationError(e)

        return Response({"link": sheet_instance.excel_sheet}, HTTP_200_OK)


class UpdateViaSyncSheetProjectApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: "link"},
        operation_id="update_via_sheet_sync_project",
        operation_summary="update via sheet sync for project",
    )
    @transaction.atomic
    def post(self, request, project_id, module_name, module_pk, *args, **kwargs):
        excel_class: SyncExcelBase = base_class_factory(module=module_name)
        org_from_id = self.get_organization_id()
        org_filter = self.get_organization_filter()

        sheet_instance = ProjectSheets.objects.filter(
            organization_id=org_from_id, module=module_name, module_pk=module_pk
        ).first()

        if not sheet_instance.excel_sheet:
            raise SyncSheetDoesNotExistsException(_("Sheet Does Not Exists to update"))

        excel_class.update_into_db(
            excel_sheet_url=sheet_instance.excel_sheet,
            excel_meta=sheet_instance.excel_meta,
            module_pk=module_pk,
            user_id=request.user.id,
            org_id=self.get_organization_id(),
            org_filter=org_filter,
            user=request.user,
            project_id=project_id,
        )

        return Response({"link": sheet_instance.excel_sheet}, HTTP_200_OK)
