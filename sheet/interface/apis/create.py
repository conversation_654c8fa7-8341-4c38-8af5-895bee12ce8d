import structlog
from django.db import transaction
from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.apis import BaseApi
from common.excel_sync import SyncExcelBase
from common.models import BaseModel
from project.interface.apis.internal.apis import ProjectBaseApi
from sheet.data.models import OrganizationSheets, ProjectSheets
from sheet.domain.factory import _model_factory, base_class_factory
from sheet.trigger import trigger_project_sheet_created

logger = structlog.getLogger(__name__)


class CreateSyncSheetApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: "link"},
        operation_id="sheet_sync",
        operation_summary="sheet sync",
    )
    @transaction.atomic
    def post(self, request, module_name, module_pk, *args, **kwargs):
        excel_class: SyncExcelBase = base_class_factory(module=module_name)
        model: BaseModel = _model_factory(module=module_name)
        org_from_id = self.get_organization_id()

        sheet_instance, is_created = OrganizationSheets.objects.get_or_create(
            organization_id=org_from_id, module=module_name, module_pk=module_pk
        )
        model_obj: object = get_object_or_404(model, pk=module_pk)

        if not sheet_instance.excel_sheet:
            sheet_instance.excel_sheet, sheet_instance.excel_meta = excel_class.create_sheets(
                sheet_name=model_obj.name, org_from_id=org_from_id
            )
            sheet_instance.save()

        excel_class.load_data_and_sync.apply_async(
            queue="excel_sync_queue",
            kwargs={
                "module_pk": module_pk,
                "org_from_id": org_from_id,
                "user_id": request.user.pk,
            },
        )
        # excel_class.load_data_and_sync(
        #     module_pk=module_pk,
        #     org_from_id=org_from_id,
        #     user_id=request.user.pk,
        # )

        return Response({"link": sheet_instance.excel_sheet}, HTTP_200_OK)


class CreateSyncSheetProjectApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: "link"},
        operation_id="project_sheet_sync",
        operation_summary="project sheet sync",
    )
    @transaction.atomic
    def post(self, request, project_id, module_name, module_pk, *args, **kwargs):
        excel_class: SyncExcelBase = base_class_factory(module=module_name)
        model: BaseModel = _model_factory(module=module_name)
        org_from_id = self.get_organization_id()
        org_filter = self.get_organization_filter()

        sheet_instance, is_created = ProjectSheets.objects.get_or_create(
            organization_id=org_from_id, module=module_name, module_pk=module_pk, project_id=project_id
        )

        model_obj: object = get_object_or_404(model, pk=module_pk)

        if not sheet_instance.excel_sheet:
            sheet_instance.excel_sheet, sheet_instance.excel_meta = excel_class.create_sheets(
                sheet_name=model_obj.project.name, org_from_id=org_from_id
            )
            sheet_instance.save()
            trigger_project_sheet_created(
                user_id=request.user.pk,
                context_id=module_pk,
                context=module_name,
                project_id=project_id,
                sheet_url=sheet_instance.excel_sheet,
            )

        excel_class.load_data_and_sync.apply_async(
            queue="excel_sync_queue",
            kwargs={
                "module_pk": module_pk,
                "org_from_id": org_from_id,
                "project_id": project_id,
                "org_filter": org_filter.__dict__,
                "user_id": request.user.pk,
            },
        )

        return Response({"link": sheet_instance.excel_sheet}, HTTP_200_OK)
