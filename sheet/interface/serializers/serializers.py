from rest_framework import serializers

from ratecontract.serializers.model_serializers import RateContractElementSerializer


class RateContractElementOutputSerializer(RateContractElementSerializer):
    element_id = serializers.IntegerField(source="element.id")

    class Meta(RateContractElementSerializer.Meta):
        output_hash_id_fields = []
        fields = (
            "id",
            "element_id",
            "element_code",
        )
