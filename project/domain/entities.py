import datetime
import decimal
from dataclasses import dataclass
from typing import Dict, List, Optional, Union
from uuid import UUID

from authorization.domain.constants import Permissions
from common.constants import CustomFieldTypeChoices, CustomFieldTypeEnum
from common.context_values import BaseContextValues, ProjectContextValueMixin
from common.pydantic.base_model import BaseModel, BaseModelV2
from common.pydantic.custom_fields import HashIdInt
from core.entities import CurrencyData, TaxTypeData, TimezoneData
from core.models import Role, User
from element.data.choices import ItemTypeUpdateMethodChoices
from project.data.models import ProjectCustomField, ProjectCustomFieldDropDownOption, ProjectCustomSection
from project.domain.constants import ColumnSettingSectionTypeEnum, ProjectSectionTypeEnum


class RegionValueProviderData(BaseContextValues, ProjectContextValueMixin):
    # TODO: Use RequestCreateInputData inplace of BaseContextValues
    ...


@dataclass(frozen=True)
class BusinessCategoryValueProviderData(BaseContextValues, ProjectContextValueMixin): ...


@dataclass
class CustomFieldDropdownOptionInputData:
    name: str
    uuid: UUID
    is_archived: bool


@dataclass
class ProjectCustomFieldInputData:
    uuid: UUID
    name: str
    is_required: bool
    position: int
    type: CustomFieldTypeChoices
    is_archived: bool
    options: Optional[List[CustomFieldDropdownOptionInputData]] = None
    is_added_in_existing_projects: Optional[bool] = False


@dataclass
class ProjectCustomSectionInputData:
    name: str
    uuid: UUID
    position: int
    type: ProjectSectionTypeEnum
    fields: List[ProjectCustomFieldInputData]


@dataclass
class ProjectCustomFieldMappings:
    section_uuid_object_mapping: Dict[UUID, ProjectCustomSection]
    field_uuid_object_mapping: Dict[UUID, ProjectCustomField]
    dropdown_option_uuid_object_mapping: Dict[UUID, ProjectCustomFieldDropDownOption]
    field_uuid_dropdown_option_uuids_mapping: Dict[UUID, List[UUID]]
    field_uuid_dropdown_option_names_mapping: Dict[UUID, List[str]]


@dataclass(frozen=True)
class ProjectUserRoleData:
    user: Optional[User]
    role: Optional[Role]


@dataclass(frozen=True)
class ProjectCreateData:
    name: str
    client_id: int
    estimated_cost: float
    business_category_id: int
    project_scope_ids: List[int]
    address: str
    city: str
    state: int
    dealer_name: str
    dealer_phone_number: str
    area: float
    store_type_id: Union[int, None] = None
    recce_due_at: Optional[datetime.datetime] = None
    kam_completion_due_at: Optional[datetime.datetime] = None
    expected_started_at: Optional[datetime.datetime] = None


@dataclass
class DataCounter:
    dropdown_added_ids: List[int]
    field_added_ids: List[int]
    dropdown_removed_ids: List[int]
    field_removed_ids: List[int]


# class ColumnSettingData(BaseCacheData):
@dataclass
class ColumnSettingData:
    id: str
    name: str
    is_enabled: bool  # toggle enable/disable
    is_fixed: bool  # sticked on project list page
    type: CustomFieldTypeEnum  # field type
    section: ColumnSettingSectionTypeEnum  # section of config


class ProjectCountryConfigData(BaseModel):
    currency: CurrencyData
    tax_type: TaxTypeData
    timezone: TimezoneData


@dataclass
class ProjectPocData:
    organization_id: int
    poc_id: int


class ProjectFieldHistoryUpdatedByOrgDataEntity(BaseModelV2):
    id: HashIdInt
    name: str


class ProjectFieldHistoryUpdatedByDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: str | None = None
    org: ProjectFieldHistoryUpdatedByOrgDataEntity


class ProjectFieldHistoryDataEntity(BaseModelV2):
    updated_at: datetime.datetime
    reason: str | None
    value: datetime.datetime
    updated_by: ProjectFieldHistoryUpdatedByDataEntity
    prev_value: datetime.datetime | None = None


class ProjectFieldHistoryUsingDatesEntity(BaseModelV2):
    history_entities: List[ProjectFieldHistoryDataEntity]
    prev_date: datetime.datetime | None
    prev_reason: str | None
    curr_date: datetime.datetime | None
    curr_reason: str | None


class ItemTypeConfigMilestoneEntity(BaseModelV2):
    id: HashIdInt
    name: str
    percentage: decimal.Decimal
    is_visible: bool


class ItemTypeConfigDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    allowed_update_methods: List[ItemTypeUpdateMethodChoices]
    default_update_method: ItemTypeUpdateMethodChoices
    milestones: list[ItemTypeConfigMilestoneEntity]


class ItemTypeDefaultConfigDataEntity(BaseModelV2):
    allowed_update_methods: List[ItemTypeUpdateMethodChoices]
    default_update_method: ItemTypeUpdateMethodChoices


class ItemTypeConfigCacheEntity(BaseModelV2):
    default_config: ItemTypeDefaultConfigDataEntity
    item_types: dict[int, ItemTypeConfigDataEntity]


class ProjectUserPermissionEntity(BaseModelV2):
    permissions: list[Permissions]


class ProjectOrgCreatedData(BaseModelV2):
    created_date: datetime.date


class ProjectSharedClientDataEntity(BaseModelV2):
    name: str


class ProjectSharedVendorDataEntity(BaseModelV2):
    name: str


class ProjectSharedDataEntity(BaseModelV2):
    clients: dict[int, ProjectSharedClientDataEntity]
    vendors: dict[int, ProjectSharedVendorDataEntity]
