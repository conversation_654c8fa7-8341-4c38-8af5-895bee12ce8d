from abc import ABC
from dataclasses import asdict
from datetime import date, datetime, timedelta
from typing import Dict, List, Union

from django.db.models import Prefetch
from django.utils import timezone

from common.entities import (
    ProjectDataEntity,
    ProjectOrganizationEntity,
    ProjectTimelineData,
)
from common.pydantic.base_cache import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.caches import CACH<PERSON>, BaseDataCache, UserDataCache
from core.entities import CountryData, OrgIdProjectIdEntity, ProjectUserEntity, UserOrgData, UserRoleData
from core.models import User
from core.organization.domain.entities import StateData
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementItemType
from element.domain.constants import DEFAULT_UPDATE_METHOD_MAPPING
from project.data.models import Project, ProjectOrgItemTypeConfig
from project.domain.entities import (
    ColumnSettingData,
    ItemTypeConfigCacheEntity,
    ItemTypeConfigDataEntity,
    ItemTypeConfigMilestoneEntity,
    ItemTypeDefaultConfigDataEntity,
    ProjectCountryConfigData,
    ProjectSharedClientDataEntity,
    ProjectSharedDataEntity,
    ProjectSharedVendorDataEntity,
    ProjectUserPermissionEntity,
)
from project.selectors import (
    fetch_project_id,
    get_project_config,
    project_assignment_list_fetch,
    project_created_by_fetch_all,
    user_client_list_fetch,
    user_country_list_fetch,
    user_project_stage_list_fetch,
    user_state_list_fetch,
)
from project.serializers import ProjectDataEntityDataSerializer
from work_progress_v2.data.models.milestone import ItemTypeMileStone


class ProjectAssignmentCache(BaseDataCache):
    CACHE_KEY = "project_assignments_v2_{}"

    @classmethod
    def get_data(cls, instance_id: int) -> dict:
        from project.domain.services import project_assignment_mappings_get

        return project_assignment_mappings_get(project_id=instance_id)


class UserCountryListCache(UserDataCache):
    CACHE_KEY = "user_countries_{}"

    @classmethod
    def get_data(cls, user: User) -> list[dict]:
        countries: list[CountryData] = user_country_list_fetch(user=user)
        return CountryData.drf_serializer(countries, many=True).data

    @classmethod
    def get(cls, user: User) -> list[CountryData]:
        data = super().get(user)
        serializer = CountryData.drf_serializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data


class UserStateListCache(UserDataCache):
    CACHE_KEY = "user_states_v2_{}"

    @classmethod
    def get_data(cls, user: User) -> list[dict]:
        states: list[StateData] = user_state_list_fetch(user=user)
        return StateData.drf_serializer(states, many=True).data

    @classmethod
    def get(cls, user: User) -> list[StateData]:
        data = super().get(user)
        serializer = StateData.drf_serializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data


class UserClientListCache(UserDataCache):
    CACHE_KEY = "user_clients_{}"

    @classmethod
    def get_data(cls, user: User) -> list:
        return list(user_client_list_fetch(user=user))


class UserProjectStagesListCache(UserDataCache):
    CACHE_KEY = "user_project_stages_{}"

    @classmethod
    def get_data(cls, user: User) -> list:
        return list(user_project_stage_list_fetch(user=user))


class UserProjectSearchConfigCache(UserDataCache):
    CACHE_KEY = "user_search_config_v2_{}"

    @classmethod
    def get_data(cls, user: User) -> dict:
        from project.domain.services import project_search_config_data_prepare

        return project_search_config_data_prepare(user=user)


class UserClientStoreTypeListCache(UserDataCache):
    CACHE_KEY = "user_store_type_list_{}"

    @classmethod
    def get_data(cls, user: User) -> dict:
        from project.domain.services import project_client_store_type_list

        return project_client_store_type_list(user=user)


class ProjectJobIdCache(BaseDataCache):
    CACHE_KEY = "project_job_id_cache_{}"

    @classmethod
    def get_data(cls, instance_id: str) -> int:
        return fetch_project_id(job_id=instance_id)


class UserRoleAssignmentCache(UserDataCache, ABC):
    CACHE_KEY = "user_role_{}"

    @classmethod
    def get(cls, user_role_data: UserRoleData) -> Union[list, dict]:
        cache_key = cls.get_cache_key(
            key=f"{user_role_data.user.pk}_{user_role_data.user.token_data.org_id}_{user_role_data.role_id}"
        )
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(user_role_data=user_role_data)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    def get_data(cls, user_role_data: UserRoleData) -> list:
        project_user_data = project_assignment_list_fetch(user_role_data=user_role_data)
        return [{"id": project_user.user.pk, "name": project_user.user.name} for project_user in project_user_data]

    @classmethod
    def delete_many(cls, user_org_data: UserOrgData):
        CACHE.delete_many(cls.get_cache_key(key=f"{user_org_data.user_id}_{user_org_data.org_id}"))


class ProjectCreatedByCache(UserDataCache):
    CACHE_KEY = "project_created_by_{}"

    @classmethod
    def get_data(cls, user: User):
        return project_created_by_fetch_all(user=user)


class ProjectDataCache(BaseDataCache, ABC):
    CACHE_KEY = "project_data_{}"
    CACHE_TTL = 60 * 60 * 24

    @classmethod
    def is_vendor_logged_in(
        cls, logged_in_organization_id: int, client_organization_id: int, project_creator_organization_id: int
    ) -> bool:
        if logged_in_organization_id in (client_organization_id, project_creator_organization_id):
            return False
        return True

    @classmethod
    def is_actual_start_date_set(cls, project_details: ProjectOrganizationEntity) -> bool:
        project_details: ProjectDataEntity = cls.get(project_details=project_details)
        return project_details.dates.actual_start_date

    @classmethod
    def actual_start_date_set(cls, project_details: ProjectOrganizationEntity):
        cache_key = cls.get_cache_key(key=f"{project_details.project_id}_{project_details.organization_id}")
        cls.delete(project_details=project_details)
        data = cls.get_data(project_details=project_details)

        CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)

    @classmethod
    def get(cls, project_details: ProjectOrganizationEntity) -> ProjectDataEntity:
        cache_key = cls.get_cache_key(key=f"{project_details.project_id}_{project_details.organization_id}")
        data = CACHE.get(cache_key, decode_json=True)

        if data is None:
            data = cls.get_data(project_details=project_details)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)

        serializer_obj = ProjectDataEntityDataSerializer(data=data)
        serializer_obj.is_valid(raise_exception=True)
        return serializer_obj.validated_data

    @classmethod
    def get_data(cls, project_details: ProjectOrganizationEntity):
        from order.services.vendor_progress import vendor_timeline_fetch
        from project.domain.services import project_creator_timeline_fetch

        instance: Project = Project.objects.get(id=project_details.project_id)

        is_vendor = cls.is_vendor_logged_in(
            logged_in_organization_id=project_details.organization_id,
            client_organization_id=instance.client_id,
            project_creator_organization_id=instance.organization_id,
        )

        if is_vendor:
            project_timeline: ProjectTimelineData = vendor_timeline_fetch(
                organization_id=project_details.organization_id, project_id=project_details.project_id
            )

        else:
            project_timeline: ProjectTimelineData = project_creator_timeline_fetch(
                project=instance, organization_id=project_details.organization_id
            )

        details = ProjectDataEntity(
            dates=project_timeline,
            client_id=instance.client_id,
            organization_id=instance.organization_id,
            is_vendor=is_vendor,
        )

        return ProjectDataEntityDataSerializer(details).data

    @classmethod
    def delete(cls, project_details: ProjectOrganizationEntity) -> None:
        CACHE.delete(cls.get_cache_key(key=f"{project_details.project_id}_{project_details.organization_id}"))


class WorkProgressChartCache(BaseDataCache):
    CACHE_KEY = "wpr_dates_{}"
    CACHE_TTL = 60 * 15  # 15 min

    @classmethod
    def get(cls, project_details: ProjectOrganizationEntity) -> Union[list, dict]:
        curr_date = timezone.now().date().strftime("%Y_%m_%d")
        cache_key = super().get_cache_key(
            key=f"{project_details.project_id}_{project_details.organization_id}_{curr_date}"
        )
        data = CACHE.get(cache_key, decode_json=True)
        if data:
            data = cls.decode_json_dates_to_date(data)
        if data is None:
            data = cls.get_data(project_details=project_details)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
            data = cls.decode_json_dates_to_date(data)
        return data

    @classmethod
    def get_data(cls, project_details: ProjectOrganizationEntity):
        from progressreport.services.progress_report import (
            progress_report_execution_due_date_data_get,
            progress_report_projected_end_date_data_get,
            trim_due_dates_data,
        )

        end_date = date.today()
        start_date = date.today() + timedelta(-60)
        project_id = project_details.project_id
        org_id = project_details.organization_id

        execution_due_date_data = progress_report_execution_due_date_data_get(
            start_date=start_date,
            end_date=end_date,
            project_id=project_id,
            org_id=org_id,
            is_vendor=project_details.is_vendor,
        )

        projected_end_date_data = progress_report_projected_end_date_data_get(
            start_date=start_date, end_date=end_date, project_id=project_id, org_id=org_id
        )

        execution_due_date_data = trim_due_dates_data(given_dates_and_due_dates=execution_due_date_data)
        projected_end_date_data = trim_due_dates_data(given_dates_and_due_dates=projected_end_date_data)

        json_execution_due_date_data = cls.convert_dates_to_isoformat(execution_due_date_data)
        json_projected_end_date_data = cls.convert_dates_to_isoformat(projected_end_date_data)

        return {
            "execution_due_date_data": json_execution_due_date_data,
            "projected_end_date_data": json_projected_end_date_data,
        }

    @classmethod
    def delete(cls, project_details: ProjectOrganizationEntity) -> None:
        curr_date = timezone.now().date().strftime("%Y_%m_%d")
        CACHE.delete(
            super().get_cache_key(key=f"{project_details.project_id}_{project_details.organization_id}_{curr_date}")
        )

    @classmethod
    def decode_json_dates_to_date(cls, json_dates: Dict):
        json_execution_due_date_data = json_dates["execution_due_date_data"]
        json_projected_end_date_data = json_dates["projected_end_date_data"]

        execution_due_date_data = cls.convert_json_dates_to_date_object(json_execution_due_date_data)
        projected_end_date_data = cls.convert_json_dates_to_date_object(json_projected_end_date_data)

        return {"execution_due_date_data": execution_due_date_data, "projected_end_date_data": projected_end_date_data}

    @staticmethod
    def convert_dates_to_isoformat(given_dates_and_due_dates: List[Dict]):
        json_date_data = []
        for due_date_entity in given_dates_and_due_dates:
            given_date = due_date_entity["given_date"].isoformat()
            due_date = due_date_entity["due_date"]
            if due_date:
                due_date = due_date.isoformat()
            json_date_data.append({"given_date": given_date, "due_date": due_date, "reason": due_date_entity["reason"]})
        return json_date_data

    @staticmethod
    def convert_json_dates_to_date_object(given_dates_and_due_dates: List[Dict]):
        date_data = []
        for due_date_entity in given_dates_and_due_dates:
            given_date = due_date_entity["given_date"]
            due_date = due_date_entity["due_date"]
            if due_date:
                due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            else:
                due_date = None
            given_date = datetime.strptime(given_date, "%Y-%m-%d").date()
            date_data.append({"given_date": given_date, "due_date": due_date, "reason": due_date_entity["reason"]})
        return date_data


class ItemTypeProgressUpdateMethodCache(BaseDataCache):
    CACHE_KEY = "item_type_progress_update_method_{}"
    CACHE_TTL = 60 * 60 * 24

    @classmethod
    def get(cls, project_org_data: ProjectOrganizationEntity) -> dict:
        cache_key = cls.get_cache_key(key=f"{project_org_data.project_id}_{project_org_data.organization_id}")
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(project_org_data=project_org_data)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    def get_data(cls, project_org_data: ProjectOrganizationEntity) -> dict:
        project_configs = {
            config["item_type_id"]: config["update_method"]
            for config in ProjectOrgItemTypeConfig.objects.filter(
                project_id=project_org_data.project_id, organization_id=project_org_data.organization_id
            ).values("item_type_id", "update_method")
        }

        item_types = ElementItemType.objects.all()
        data = {}
        for item_type in item_types:
            if item_type.pk in list(project_configs.keys()):
                data[str(item_type.pk)] = project_configs[item_type.pk]
            else:
                data[str(item_type.pk)] = item_type.default_update_method
        return data

    @classmethod
    def delete(cls, project_org_data: ProjectOrganizationEntity) -> None:
        CACHE.delete(cls.get_cache_key(key=f"{project_org_data.project_id}_{project_org_data.organization_id}"))


class ProjectListConfigCache(BaseDataCache):
    CACHE_KEY = "project_list_config_{}"
    CACHE_TTL = 60 * 60 * 24  # 24 hours

    @classmethod
    def get(cls, instance_id: int) -> List[Dict]:
        cache_key = cls.get_cache_key(key=f"{instance_id}")
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(instance_id=instance_id)
            data = [asdict(column_setting) for column_setting in data]
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)

        data = [ColumnSettingData(**dict) for dict in data]
        return data

    @classmethod
    def get_data(cls, instance_id: int) -> List[ColumnSettingData]:
        from project.domain.services import get_project_list_column_settings

        return get_project_list_column_settings(org_id=instance_id)


class ProjectCountryConfigCache(BaseDataCache):
    CACHE_KEY = "project_config_{}"

    @classmethod
    def get_data(cls, instance_id: int) -> dict:
        config: ProjectCountryConfigData = get_project_config(project_id=instance_id)
        return ProjectCountryConfigData.drf_serializer(config).data

    @classmethod
    def get(cls, instance_id: int) -> ProjectCountryConfigData:
        data = super().get(instance_id)
        serializer = ProjectCountryConfigData.drf_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    @classmethod
    def get_cache_key_regex(cls) -> str:
        return cls.CACHE_KEY.split("_{}")[0]

    @classmethod
    def delete_many(cls) -> None:
        CACHE.delete_many(cls.get_cache_key_regex())


class ItemTypeConfigCache(PydanticBaseCache[OrgIdProjectIdEntity, ItemTypeConfigCacheEntity]):
    CACHE_KEY = "item_type_config_v1_{}"
    CACHE_TTL = 60 * 60 * 24

    @classmethod
    def get_cache_key(cls, key: OrgIdProjectIdEntity):
        return cls.CACHE_KEY.format(f"{key.org_id}_{key.project_id}")

    @classmethod
    def get(cls, key: OrgIdProjectIdEntity):
        cache_key = cls.get_cache_key(key=key)
        cache_data = CACHE.get(cache_key, decode_json=True)

        if cache_data is None:
            data = cls.get_data(key=key)
            data_dump = data.model_dump()
            CACHE.set_with_ttl(cache_key, data_dump, ttl_seconds=cls.CACHE_TTL, encode_json=True)
            return data

        return ItemTypeConfigCacheEntity.model_validate(cache_data)

    @classmethod
    def get_data(cls, key: OrgIdProjectIdEntity):
        project_configs: dict[int, ItemTypeUpdateMethodChoices] = {
            config["item_type_id"]: ItemTypeUpdateMethodChoices(config["update_method"])
            for config in ProjectOrgItemTypeConfig.objects.filter(
                project_id=key.project_id, organization_id=key.org_id
            ).values("item_type_id", "update_method")
        }

        item_types = ElementItemType.objects.select_related("milestone_config").prefetch_related(
            Prefetch(
                "milestone_config__milestones",
                queryset=ItemTypeMileStone.objects.available().order_by("percentage"),
                to_attr="ordered_milestones",
            )
        )

        data = ItemTypeConfigCacheEntity(
            default_config=ItemTypeDefaultConfigDataEntity(
                default_update_method=ItemTypeUpdateMethodChoices.PERCENTAGE,
                allowed_update_methods=[ItemTypeUpdateMethodChoices.PERCENTAGE]
                + DEFAULT_UPDATE_METHOD_MAPPING[ItemTypeUpdateMethodChoices.PERCENTAGE],
            ),
            item_types={},
        )

        for item_type in item_types:
            item_type_default_update_method = (
                ItemTypeUpdateMethodChoices(item_type.default_update_method)
                if item_type.default_update_method
                else data.default_config.default_update_method
            )

            milestones = [
                ItemTypeConfigMilestoneEntity(
                    id=milestone.pk,
                    name=milestone.name,
                    percentage=milestone.percentage,
                    is_visible=milestone.is_visible,
                )
                for milestone in (
                    item_type.milestone_config.ordered_milestones if hasattr(item_type, "milestone_config") else []
                )
            ]

            allowed_update_methods = [item_type_default_update_method] + DEFAULT_UPDATE_METHOD_MAPPING.get(
                item_type_default_update_method, []
            )

            if item_type.pk in project_configs:
                data.item_types[item_type.pk] = ItemTypeConfigDataEntity(
                    id=item_type.pk,
                    name=item_type.name,
                    default_update_method=project_configs[item_type.pk],
                    allowed_update_methods=allowed_update_methods,
                    milestones=milestones,
                )
            else:
                data.item_types[item_type.pk] = ItemTypeConfigDataEntity(
                    id=item_type.pk,
                    name=item_type.name,
                    default_update_method=item_type_default_update_method,
                    allowed_update_methods=allowed_update_methods,
                    milestones=milestones,
                )

        return data


class ProjectUserPermissionCache(PydanticBaseCache[ProjectUserEntity, ProjectUserPermissionEntity]):
    CACHE_KEY = "project_user_permission_{}"
    CACHE_TTL = 10
    RETURN_CLASS = ProjectUserPermissionEntity

    @classmethod
    def get_cache_key(cls, key: ProjectUserEntity):
        return cls.CACHE_KEY.format(f"{key.user_id}_{key.org_id}_{key.project_id}")

    @classmethod
    def get_data(cls, key: ProjectUserEntity):
        from core.services import get_user_with_token_data
        from project.domain.helpers import ProjectPermissionHelper

        user = get_user_with_token_data(org_id=key.org_id, user_id=key.user_id)

        permissions = ProjectPermissionHelper.get_permissions(project_id=key.project_id, user=user)

        return ProjectUserPermissionEntity(permissions=permissions)


class ProjectSharedDataCache(PydanticBaseCache[OrgIdProjectIdEntity, ProjectSharedDataEntity]):
    CACHE_KEY = "project_shared_data_project_{}_org_{}"
    CACHE_TTL = 10
    RETURN_CLASS = ProjectSharedDataEntity

    @classmethod
    def get_cache_key(cls, key: OrgIdProjectIdEntity):
        return cls.CACHE_KEY.format(key.project_id, key.org_id)

    @classmethod
    def get_data(cls, key: OrgIdProjectIdEntity) -> ProjectSharedDataEntity:
        from core.organization.data.selectors import get_mapping_for_clients, get_mapping_for_vendors
        from project.domain.services import get_child_organization_ids, get_parent_organization_ids

        client_organization_ids = get_parent_organization_ids(project_id=key.project_id, organization_id=key.org_id)
        vendor_organization_ids = get_child_organization_ids(project_id=key.project_id, organization_id=key.org_id)

        client_linked_organizations = get_mapping_for_clients(org_id=key.org_id, client_ids=client_organization_ids)
        vendor_linked_organizations = get_mapping_for_vendors(org_id=key.org_id, vendor_ids=vendor_organization_ids)

        clients = {
            client.client_id: ProjectSharedClientDataEntity(name=client.primary_billing_entity.name)
            for client in client_linked_organizations
        }

        vendors = {
            vendor.vendor_id: ProjectSharedVendorDataEntity(name=vendor.primary_billing_entity.name)
            for vendor in vendor_linked_organizations
        }

        return ProjectSharedDataEntity(
            clients=clients,
            vendors=vendors,
        )
