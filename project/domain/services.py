import abc
import datetime
import decimal
import re
from collections import defaultdict
from copy import deepcopy
from dataclasses import asdict, is_dataclass
from functools import partial
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import UUID, uuid4

import pytz
import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.db.models import Count, F, Min, Q, QuerySet
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from rest_framework.settings import api_settings

from approval_request.approval_hierarchy.domain.enums import BusinessCategoryTypeConditionEnum
from authorization.domain.constants import Permissions
from boq.data.selectors import boq_element_history_fetch_all_using_project_and_org
from client.data.models import Client
from client.interface.serializers import ClientStoreTypeSerializer
from common.choices import (
    OrderType,
    OrganizationType,
    PermissionScope,
    ReservedRoleNames,
    RoleType,
    SectionKey,
)

# from common.apis import Base<PERSON><PERSON>
from common.constants import SYSTEM_USER_ID, CustomFieldTypeEnum
from common.entities import (
    ObjectStatus,
    ProjectDataEntity,
    ProjectOrganizationEntity,
    ProjectTimelineData,
)
from common.events.constants import Events, ProjectDateChangedDateType
from common.events.design import DesignApprovedEventData, DesignFreezedEventData
from common.events.project import (
    ProjectAssignmentEventData,
    ProjectCreationEventData,
    ProjectDateAssignEventData,
    ProjectDateChangedEventData,
    ProjectUserRemovedEventData,
)
from common.events.services import trigger_event
from common.exceptions import BaseValidationError
from common.models import BaseModel
from common.serializers import CustomColumnConfigurationSerializer, PhoneNumberSerializer
from common.services import is_empty, many_to_many_field_set, model_update
from common.utils import get_id_list_from_string
from controlroom.data.models import OrganizationConfig, OrganizationConfigRole, OrganizationDefaultModuleSetting
from core.caches import OrganizationCountryConfigCache, RolePermissionCache
from core.entities import OrganizationCountryConfigCacheData, OrgUserEntity, UserOrgData, UserRoleData
from core.exceptions import OrgUserNotFoundException
from core.helpers import OrgPermissionHelper
from core.models import Organization, OrganizationUser, Role, RolePermission, User
from core.selectors import OrganizationConfigRoleService
from core.serializers import UserSerializer
from core.service.role import PmcRoleService
from core.services import region_state_mapping_fetch
from core.utils import get_relative_path
from element.data.entities import ItemTypeConfigData
from element.data.selectors import org_item_type_config_fetch
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import (
    CONTEXT_CORE_PERMISSION_MAPPING,
    CONTEXT_PROJECT_PERMISSION_MAPPING,
    MicroContext,
)
from project.data.choices import ProjectExpenseClosureChoices, SheetType
from project.data.models import (
    Project,
    ProjectAttachment,
    ProjectConfig,
    ProjectCustomDateFieldData,
    ProjectCustomDropDownFieldData,
    ProjectCustomField,
    ProjectCustomFieldDropDownOption,
    ProjectCustomFileFieldData,
    ProjectCustomMultipleFilesField,
    ProjectCustomMultipleFilesFieldData,
    ProjectCustomNumberFieldData,
    ProjectCustomPhoneNumberFieldData,
    ProjectCustomRichTextFieldData,
    ProjectCustomSection,
    ProjectCustomTextFieldData,
    ProjectFieldHistory,
    ProjectOrganization,
    ProjectOrgCustomFieldConfig,
    ProjectOrgData,
    ProjectOrgExtraData,
    ProjectOrgItemTypeConfig,
    ProjectProgressSchedule,
    ProjectStatus,
    ProjectStatusHistory,
    ProjectUser,
    Store,
    StoreFieldHistory,
)
from project.domain.abstract_services import LeadToProjectAbstractService
from project.domain.caches import (
    ProjectAssignmentCache,
    ProjectCreatedByCache,
    ProjectDataCache,
    ProjectListConfigCache,
    UserClientListCache,
    UserClientStoreTypeListCache,
    UserProjectSearchConfigCache,
    UserProjectStagesListCache,
    UserRoleAssignmentCache,
    UserStateListCache,
    WorkProgressChartCache,
)
from project.domain.constants import (
    ACTUAL_COMPLETION_DATE_UUID,
    ASSIGNED_PROJECT_USER_SECTION_UUID,
    BUSINESS_CATEGORY_UUID,
    CITY_UUID,
    CLIENT_UUID,
    COUNTRY_UUID,
    EXECUTION_DUE_DATE_UUID,
    FIXED_COLUMN_FIELD_UUIDS,
    IMPORTANT_DATES_SECTION_UUID,
    PROJECT_CREATED_BY_UUID,
    PROJECT_CREATION_DATE_UUID,
    PROJECT_CURRENCY_UUID,
    PROJECT_CUSTOM_FIELD_CONFIG,
    PROJECT_ESTIMATE_UUID,
    PROJECT_IS_ARCHIVED_UUID,
    PROJECT_JOB_ID_UUID,
    PROJECT_NAME_UUID,
    PROJECT_SCHEDULE_PROGRESS_UUID,
    PROJECT_SCOPE_AMOUNT_WITHOUT_GST_UUID,
    PROJECT_SCOPE_PROGRESS_UUID,
    PROJECT_SCOPE_SECTION_UUID,
    PROJECT_STAGE_UUID,
    PROJECT_TAX_TYPE_UUID,
    PROJECT_TIMEZONE_UUID,
    READONLY_FIELD_UUIDS,
    RECCE_COMPLETION_DATE_UUID,
    RECCE_DUE_DATE_UUID,
    STATE_UUID,
    SYSTEM_FIELD_NAMES_UUID_MAPPING,
    SYSTEM_FIELD_UUIDS,
    UNEDITABLE_SYSTEM_SECTION_UUIDS,
    ColumnOrderDict,
    ColumnSectionOrder,
    ColumnSettingFieldNameMapping,
    ColumnSettingSectionTypeEnum,
    ProjectSectionActionEnum,
    ProjectSectionTypeEnum,
    SystemFieldNameEnum,
)
from project.domain.entities import (
    ColumnSettingData,
    CustomFieldDropdownOptionInputData,
    DataCounter,
    ProjectCountryConfigData,
    ProjectCustomFieldInputData,
    ProjectCustomFieldMappings,
    ProjectCustomSectionInputData,
    ProjectPocData,
    ProjectUserRoleData,
)
from project.domain.exceptions import (
    CustomFieldNotFoundException,
    ProjectConfigExceptions,
    ProjectCreationPrefillDataException,
    ProjectHoldLostRectificationNotAllowed,
    ProjectNotFoundException,
    ProjectRegionStateNotFoundException,
    ProjectStatusValidationError,
    StatusNotAllowed,
    UnprocessedStatus,
)
from project.domain.helpers import ProjectOrganizationHelper, ProjectPermissionHelper
from project.domain.mappings import (
    BOQ_STATUS_WEIGHT_MAPPING,
    DESIGN_STATUS_WEIGHT_MAPPING,
    ORDER_STATUS_WEIGHT_MAPPING,
    PROJECT_STATUS_AGAINST_MODULE_STATUS_MAPPING,
    PROJECT_STATUS_WEIGHT_MAPPING,
    RECCE_STATUS_WEIGHT_MAPPING,
    WORK_REPORT_WEIGHT_MAPPING,
    ProjectHoldLostMappings,
)
from project.domain.status import OrderStatus
from project.domain.status_color_code import (
    BoqStatus,
    BoqStatusColorCode,
    DesignStatus,
    DesignStatusColorCode,
    Module,
    OrderStatusColorCode,
    RDStatus,
    RecceStatus,
    RecceStatusColorCode,
    StepperStatus,
    WorkReportStatus,
    WorkReportStatusColorCode,
)
from project.interface.external.abstract_factories import LeadToProjectAbstractFactory
from project.selectors import (
    custom_field_fetch_by_id,
    custom_field_fetch_by_uuids,
    custom_field_with_distinct_section_fetch,
    custom_fields_to_update_fetch,
    dropdown_option_fetch_by_id,
    dropdown_option_has_field_check,
    fetch_project_custom_field_config,
    field_dropdown_options_fetch,
    fields_have_data_check,
    get_custom_fields,
    get_module_status_history_of_project,
    get_project_queryset,
    get_status_history_without_hold_lost_rectification,
    project_custom_field_unarchived_config_fetch,
    project_custom_field_uuids_name_fetch_all,
    project_custom_section_fetch_all,
    project_custom_section_uuids_name_fetch_all,
    project_detail_custom_field_config_fetch,
    project_fetch_all,
    project_field_history_fetch_all,
    project_id_fetch_for_user_role,
    project_org_fetch_all,
    project_status_fetch,
    project_status_fetch_others,
    project_user_fetch_all,
    project_user_fetch_all_with_role,
    project_user_list_using_role,
)
from project.serializers import (
    ConfigPrefetchCustomSectionSerializer,
    ProjectCustomSectionSerializer,
    ProjecUserRoleSerializer,
    RoleSerializer,
    SearchConfigSerializer,
)
from project.share.data.entities import DEFAULT_CLIENT_PERMISSIONS_GROUP_MODULE_WISE
from project.share.data.repository import (
    OrganizationRepository,
    RolePermissionRepository,
    RoleRepository,
)
from project.share.domain.constants import PERMISSION_SET_MAPPING, PermissionGroup
from project.tasks import CustomProjectFieldCreationDispatcher, CustomProjectFieldCreationDispatcherData
from rollingbanners.authentication import TokenData
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.selectors import check_project_organization_snags_exists as snags_exists

LeadToProjectServiceFactory: LeadToProjectAbstractFactory = import_string(settings.LEAD_TO_PROJECT_SERVICE_FACTORY)


logger = structlog.getLogger(__name__)


def project_assignment_mappings_get(*, project_id: int) -> dict:
    mappings = dict()
    project_organizations = project_org_fetch_all(project_id=project_id).select_related("role")
    for project_organization in project_organizations:
        if str(project_organization.organization_id) not in mappings:
            mappings[str(project_organization.organization_id)] = {
                "role_ids": {str(project_organization.role.organization_id): [project_organization.role_id]},
                "user_ids": {},
            }
        elif (
            str(project_organization.role.organization_id)
            not in mappings[str(project_organization.organization_id)]["role_ids"]
        ):
            mappings[str(project_organization.organization_id)]["role_ids"][
                str(project_organization.role.organization_id)
            ] = [project_organization.role_id]
        else:
            mappings[str(project_organization.organization_id)]["role_ids"][
                str(project_organization.role.organization_id)
            ].append(project_organization.role_id)
    project_users = project_user_fetch_all_with_role(project_id=project_id)
    for project_user in project_users:
        if str(project_user.role.organization_id) in mappings:
            try:
                mappings[str(project_user.role.organization_id)]["user_ids"][str(project_user.user_id)].append(
                    project_user.role_id
                )
            except KeyError:
                mappings[str(project_user.role.organization_id)]["user_ids"][str(project_user.user_id)] = [
                    project_user.role_id
                ]
    return mappings


def get_project_role_mapping(role_mappings: list[dict]):
    validated_role_mappings = defaultdict(list)
    for mapping in role_mappings:
        validated_role_mappings[mapping.get("role_id")].append(mapping.get("user_id"))
    return dict(validated_role_mappings)


def project_role_mappings_validate(
    role_mappings: list[dict], project_id: int, org_id: int, user: User
) -> dict[int, list]:
    # TODO: validate as per required project roles
    #  at least one assignment from each required role should be present
    validated_role_mappings = defaultdict(list)
    permissions = ProjectPermissionHelper.get_permissions(project_id=project_id, user=user)
    if Permissions.CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE in permissions:
        for mapping in role_mappings:
            validated_role_mappings[mapping.get("role_id")].append(mapping.get("user_id"))
        return dict(validated_role_mappings)
    project_user_ids = set(
        ProjectUser.objects.filter(project_id=project_id, role__organization_id=org_id).values_list(
            "user_id", flat=True
        )
    )
    for mapping in role_mappings:
        if mapping.get("user_id") not in project_user_ids:
            validated_role_mappings[mapping.get("role_id")].append(mapping.get("user_id"))
        else:
            raise ValidationError(f"User with id {mapping.get('user_id')} is already assigned to the project.")
    return dict(validated_role_mappings)


def project_creation_trigger_event(project: Project, user: User):
    event_data = ProjectCreationEventData(
        project_id=project.id,
        project_job_id=project.job_id,
        user_id=user.id,
    )
    trigger_event(event=Events.PROJECT_CREATED, event_data=event_data)


def project_create(
    *,
    name: str,
    user: User,
    client_id: int,
    store_type_id: int,
    estimated_cost: float,
    business_category_id: int,
    recce_due_at: Optional[datetime.datetime] = None,
    kam_completion_due_at: Optional[datetime.datetime] = None,
    expected_started_at: Optional[datetime.datetime] = None,
    organization_id: int,
):
    project = Project(
        name=name,
        client_id=client_id,
        store_type_id=store_type_id,
        estimated_cost=estimated_cost,
        project_started_at=timezone.now(),
        business_category_id=business_category_id,
        recce_due_at=recce_due_at,
        kam_completion_due_at=kam_completion_due_at,
        expected_started_at=expected_started_at,
        organization_id=organization_id,
        version=3,
        created_by_id=user.id,
    )
    project.full_clean()
    project.save()

    project_status_history_create(
        project_id=project.pk, module=Module.PROJECT.value, status=RDStatus.PROJECT_CREATED, user_id=user.id
    )

    if kam_completion_due_at is not None:
        project_field_history_create(project=project, field="kam_completion_due_at", user_id=user.id, is_initial=True)
    if expected_started_at is not None:
        project_field_history_create(project=project, field="expected_started_at", user_id=user.id, is_initial=True)

    project_field_history_create(project=project, field="estimated_cost", user_id=user.id, is_initial=True)

    on_commit(partial(project_creation_trigger_event, user=user, project=project))
    return project


def job_id_generate(*, client_id: int, job_number: Optional[int] = None):
    if not job_number:
        last_job_id = Project.objects.filter(job_id__isnull=False).order_by("-id").first().job_id
        job_number = re.sub("^[A-Z0-9]{3}[A-Z]{1,2}", "", last_job_id)
        try:
            job_number = int(job_number) + 1
        except ValueError:
            logger.info("Regex Not Matched, fix required", last_job_id=last_job_id, job_number=job_number)
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Cannot Assign Job ID. Contact Support")})

    client = Client.objects.filter(pk=client_id).first()
    if not client:
        raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Invalid client.")})
    if not client.code:
        client_project = Project.objects.filter(client_id=client_id).first()
        if not client_project or not client_project.job_id:
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Client not configured.")})
        client.code = client_project.job_id[:4]
        client.save(update_fields=["code"])
    return f"{client.code}{job_number}"


def store_create(
    *,
    project_id: int,
    address_line_1: str,
    address_line_2: str,
    city_id: int,
    state_id: int,
    country_id: int,
    zip_code: str,
    dealer_name: str,
    dealer_phone_number: str,
    area: float,
    user_id: int,
):
    store = Store(
        project_id=project_id,
        address_line_1=address_line_1,
        address_line_2=address_line_2,
        city_id=city_id,
        state_id=state_id,
        country_id=country_id,
        zip_code=zip_code,
        dealer_name=dealer_name,
        dealer_phone_number=dealer_phone_number,
        area=area,
    )
    store.full_clean()
    store.save()
    store_field_history_create(store=store, field="area", user_id=user_id, is_initial=True)


def project_update(
    *,
    project: Project,
    data: dict[str, Any],
    updated_by_id: int,
    sync_delete_cache: bool = False,
) -> Tuple[Project, list[str]]:
    fields = [
        "name",
        "stage",
        "store_type_id",
        "business_category_id",
        "status",
        "remark",
        "estimated_cost",
        "final_cost",
        "recce_completed",
        "execution_completed",
        "project_started_at",
        "project_ended_at",
        "recce_due_at",
        "recce_completed_at",
        "kam_completion_due_at",
        "ops_completion_due_at",
        "execution_completed_at",
        "expected_started_at",
        "actual_started_at",
    ]
    fields_in_cache = [
        "actual_started_at",
        "expected_started_at",
        "client_id",
        "kam_completion_due_at",
        "execution_completed_at",
    ]

    old_kam_completion_due_at = project.kam_completion_due_at
    old_expected_started_at = project.expected_started_at
    old_estimated_cost = project.estimated_cost
    updated_project, is_updated, updated_fields = model_update(
        instance=project, fields=fields, data=data, updated_by_id=updated_by_id, save=False
    )

    if is_updated and "client_id" in updated_fields and updated_project.job_id:
        updated_project.job_id = job_id_generate(
            client_id=updated_project.client_id, job_number=int(updated_project.job_id[4:])
        )
        updated_fields.append("job_id")

    if not updated_project.job_id and data.get("job_id"):
        updated_project.job_id = data.get("job_id")
        updated_fields.append("job_id")

    if len(updated_fields):
        updated_project.save(update_fields=updated_fields)

    # to maintain backward compatibility
    if "project_scope_ids" in data:
        data["scope_ids"] = data.get("project_scope_ids")
        if many_to_many_field_set(obj=updated_project, key="scopes", value=data.get("scope_ids")):
            updated_fields.append("scope_ids")

    if "kam_completion_due_at" in updated_fields:
        if data.get("reason", None) is None:
            raise ValidationError({"reason": "Reason is required for updating Execution Due Date."})
        if old_kam_completion_due_at is None:
            project_field_history_create(
                project=project,
                field="kam_completion_due_at",
                user_id=updated_by_id,
                is_initial=True,
                reason=data.get("reason"),
            )
        else:
            if not ProjectFieldHistory.objects.filter(project=project, field="kam_completion_due_at").count():
                ProjectFieldHistory.objects.create(
                    project=project,
                    field="kam_completion_due_at",
                    value=project.to_field_history_datetime(old_kam_completion_due_at),
                    reason=data.get("reason"),
                    is_initial=True,
                    created_by_id=1,  # admin user
                )
            project_field_history_create(
                project=project,
                field="kam_completion_due_at",
                user_id=updated_by_id,
                is_initial=False,
                reason=data.get("reason"),
            )
        on_commit(
            partial(
                WorkProgressChartCache.delete,
                project_details=ProjectOrganizationEntity(
                    project_id=project.pk, organization_id=project.organization_id
                ),
            )
        )

    if "expected_started_at" in updated_fields:
        if old_expected_started_at is None:
            project_field_history_create(
                project=project, field="expected_started_at", user_id=updated_by_id, is_initial=True
            )
        else:
            if not ProjectFieldHistory.objects.filter(project=project, field="expected_started_at").count():
                ProjectFieldHistory.objects.create(
                    project=project,
                    field="expected_started_at",
                    value=project.to_field_history_datetime(old_expected_started_at),
                    is_initial=True,
                    created_by_id=1,  # admin user
                )
            project_field_history_create(
                project=project, field="expected_started_at", user_id=updated_by_id, is_initial=False
            )

    if "estimated_cost" in updated_fields:
        if not ProjectFieldHistory.objects.filter(project=project, field="estimated_cost").count():
            ProjectFieldHistory.objects.create(
                project=project,
                field="estimated_cost",
                value=str(old_estimated_cost),
                is_initial=True,
                created_by_id=1,  # admin user
            )
        project_field_history_create(
            project=project, field="estimated_cost", user_id=updated_by_id, is_initial=False, reason=data.get("reason")
        )

    if set(updated_fields) & set(fields_in_cache):
        if sync_delete_cache:
            # We are deleting the cache regardless of api fulfillment because
            # in work progress we are updating the cache and
            # we need to get the new data from the cache itself
            ProjectDataCache.delete(
                project_details=ProjectOrganizationEntity(
                    project_id=project.pk, organization_id=project.organization_id
                ),
            )
        else:
            on_commit(
                partial(
                    ProjectDataCache.delete,
                    project_details=ProjectOrganizationEntity(
                        project_id=project.pk, organization_id=project.organization_id
                    ),
                )
            )

    return updated_project, updated_fields


def project_field_history_create(
    *,
    project: Project,
    field: str,
    user_id: int,
    is_initial: bool = False,
    reason: Optional[str] = None,
):
    ProjectFieldHistory.objects.create(
        project=project,
        field=field,
        value=project.to_field_history(field),
        created_by_id=user_id,
        is_initial=is_initial,
        reason=reason,
    )


def store_field_history_create(
    *,
    store: Store,
    field: str,
    user_id: int,
    is_initial: bool = False,
):
    StoreFieldHistory.objects.create(
        store=store,
        field=field,
        value=store.to_field_history(field),
        created_by_id=user_id,
        is_initial=is_initial,
    )


def store_update(
    *,
    store: Store,
    data: dict[str, Any],
    updated_by_id: int,
) -> Tuple[Store, list[str]]:
    fields = [
        "address_line_1",
        "address_line_2",
        "city_id",
        "state_id",
        "country_id",
        "zip_code",
        "location",
        "dealer_name",
        "dealer_phone_number",
        "dealer_gstin",
        "local_name",
        "local_name_file",
        "area",
    ]
    old_area = store.area
    updated_store, _, updated_fields = model_update(
        instance=store, fields=fields, data=data, updated_by_id=updated_by_id
    )
    if "area" in updated_fields:
        if not StoreFieldHistory.objects.filter(store=store, field="area").count():
            StoreFieldHistory.objects.create(
                store=store,
                field="area",
                value=str(old_area),
                is_initial=True,
                created_by_id=1,  # admin user
            )
        store_field_history_create(store=store, field="area", user_id=updated_by_id, is_initial=False)
    return updated_store, updated_fields


def project_user_remove_many(user_ids: list[int], project_id: int, role_id: int):
    ProjectUser.objects.filter(user_id__in=user_ids, project_id=project_id, role_id=role_id).delete()
    ProjectAssignmentCache.delete(instance_id=project_id)


def project_assignment_trigger(created_by: User, recipient_list: List, project: Project, role_id: int):
    # role_name = Role.objects.filter(id=role_id).values_list("name", flat=True).first()
    event_data = ProjectAssignmentEventData(
        project_id=project.id,
        project_job_id=project.job_id,
        user_id=created_by.id,
        role_id=role_id,
        recipient_ids=recipient_list,
    )
    trigger_event(event=Events.PROJECT_ASSIGNED, event_data=event_data)


@transaction.atomic
def project_user_assign_many(
    *,
    role_id: int,
    user_ids: list[int],
    project: Project,
    user: User,
    organization_id: int,
) -> list[ProjectUser]:
    if not len(user_ids):
        return list()
    project_users = list()
    for user_id in user_ids:
        project_user = ProjectUser(user_id=user_id, role_id=role_id, project_id=project.id, created_by_id=user.pk)
        project_users.append(project_user)

    try:
        with transaction.atomic():
            project_users = ProjectUser.objects.bulk_create(objs=project_users)
    except IntegrityError:
        project_users = list()
        for user_id in user_ids:
            project_user, created = ProjectUser.objects.get_or_create(
                user_id=user_id, role_id=role_id, project_id=project.id, defaults={"created_by_id": user.pk}
            )
            if created:
                project_users.append(project_user)

    ProjectAssignmentCache.delete(instance_id=project.id)
    on_commit(
        partial(project_assignment_trigger, created_by=user, recipient_list=user_ids, project=project, role_id=role_id)
    )
    return project_users


def project_organization_assign(
    organization_id: int, project_id: int, role_id: int, assigned_by_organization_id: int, created_by_id: int
) -> ProjectOrganization:
    project_organization, created = ProjectOrganization.objects.get_or_create(
        organization_id=organization_id,
        project_id=project_id,
        role_id=role_id,
        defaults={"assigned_by_id": assigned_by_organization_id, "created_by_id": created_by_id},
    )
    return project_organization


def project_client_assign(
    organization_id: int, project_id: int, role_id: int, assigned_by_organization_id: int, created_by_id: int
):
    project_organization, created = ProjectOrganization.objects.update_or_create(
        project_id=project_id,
        role_id=role_id,
        defaults={
            "organization_id": organization_id,
            "assigned_by_id": assigned_by_organization_id,
            "created_by_id": created_by_id,
        },
    )
    return project_organization


def project_user_role_mapping_update_many(*, role_mappings: dict, project: Project, organization_id: int, user: User):
    for role_id, new_user_ids in role_mappings.items():
        current_user_ids = list(project_user_list_using_role(role_id=role_id, project_id=project.id))

        if current_user_ids == new_user_ids:
            continue

        remove_user_list = list(set(current_user_ids) - set(new_user_ids))
        add_user_list = list(set(new_user_ids) - set(current_user_ids))
        if add_user_list:
            project_user_assign_many(
                role_id=role_id,
                user_ids=add_user_list,
                project=project,
                organization_id=organization_id,
                user=user,
            )

        if remove_user_list:
            project_user_remove_many(user_ids=remove_user_list, project_id=project.id, role_id=role_id)


def project_user_update(*, instance: ProjectUser, data: Dict[str, Any]) -> ProjectUser:
    fields = ["role"]
    project_user, _, _ = model_update(instance=instance, fields=fields, data=data)
    return project_user


def project_organization_update(*, instance: ProjectOrganization, data: Dict[str, Any]) -> ProjectOrganization:
    fields = ["role"]
    project_org, _, _ = model_update(instance=instance, fields=fields, data=data)
    return project_org


def user_client_list(*, user: User) -> QuerySet:
    client_list = UserClientListCache.get(user=user)
    return Organization.objects.filter(id__in=client_list)


def project_stage_list(*, user: User) -> list:
    stage_list = UserProjectStagesListCache.get(user=user)
    choice_list = [{"id": key, "name": value} for key, value in RDStatus.choices if key in stage_list]
    choice_list = sorted(choice_list, key=lambda x: x.get("name"))
    return choice_list


def project_role_assignment_list(*, user: User, role_id: int) -> list:
    user_role_data: UserRoleData = UserRoleData(user=user, role_id=role_id)
    data = UserRoleAssignmentCache.get(user_role_data=user_role_data)

    return data


def project_header_data_fetch(*, project_id: int, organization_id: int, user: User):
    project = Project.objects.filter(id=project_id).first()
    status_obj = ProjectStatus.objects.get(project_id=project.id, organization_id=organization_id)

    header_list = list()
    header_list.append(
        {
            "section_key": SectionKey.RECCE,
            "name": "Recce",
            "status": {
                "name": dict(RecceStatus.choices)[status_obj.recce_status],
                "color_code": RecceStatusColorCode.get_color_code(status_obj.recce_status),
            },
            "stepper_status": RecceStatusColorCode.get_stepper_status(status_obj.recce_status),
        }
    )
    if OrgPermissionHelper.has_permission(user=user, permission=Permissions.FOR_TESTING):
        header_list.append(
            {
                "section_key": SectionKey.RECCE_V2,
                "name": "Recce V2",
                "status": {
                    "name": dict(RecceStatus.choices)[status_obj.recce_status],
                    "color_code": RecceStatusColorCode.get_color_code(status_obj.recce_status),
                },
                "stepper_status": RecceStatusColorCode.get_stepper_status(status_obj.recce_status),
            }
        )
    header_list.append(
        {
            "section_key": SectionKey.DESIGN,
            "name": "Design",
            "status": {
                "name": dict(DesignStatus.choices)[status_obj.design_status],
                "color_code": DesignStatusColorCode.get_color_code(status_obj.design_status),
            },
            "stepper_status": DesignStatusColorCode.get_stepper_status(status_obj.design_status),
        }
    )
    header_list.append(
        {
            "section_key": SectionKey.BOQ,
            "name": "BOQ",
            "status": {
                "name": dict(BoqStatus.choices)[status_obj.boq_status],
                "color_code": BoqStatusColorCode.get_color_code(status_obj.boq_status),
            },
            "stepper_status": BoqStatusColorCode.get_stepper_status(status_obj.boq_status),
        }
    )
    header_list.append(
        {
            "section_key": SectionKey.ORDERS_EXPENSES,
            "name": "Order",
            "status": {
                "name": dict(OrderStatus.choices)[status_obj.order_status],
                "color_code": OrderStatusColorCode.get_color_code(status_obj.order_status),
            },
            "stepper_status": (
                StepperStatus.IN_PROGRESS
                if Project.objects.get(id=project_id).orders.exists()
                else StepperStatus.NOT_STARTED
            ),
        }
    )
    header_list.append(
        {
            "section_key": SectionKey.WORK_REPORTS,
            "name": "Work Progress",
            "status": {
                "name": dict(WorkReportStatus.choices)[status_obj.work_report_status],
                "color_code": WorkReportStatusColorCode.get_color_code(status_obj.work_report_status),
            },
            "stepper_status": WorkReportStatusColorCode.get_stepper_status(status_obj.work_report_status),
        }
    )
    header_list.append(
        {
            "section_key": SectionKey.SNAG,
            "name": "Snag",
            "status": None,
            "stepper_status": (
                StepperStatus.IN_PROGRESS
                if snags_exists(project_id=project_id, organization_id=organization_id)
                else StepperStatus.NOT_STARTED
            ),
        }
    )
    if Permissions.CAN_ACCESS_FINANCE.value in OrgPermissionHelper.get_permissions(user=user):
        header_list.append(
            {
                "section_key": SectionKey.FINANCE,
                "name": "Finance",
                "status": None,
                "stepper_status": StepperStatus.NOT_STARTED,
            }
        )
    setattr(project, "header_list", header_list)
    setattr(project, "project_status", status_obj.project_status)
    return project


def assignment_role_data_prepare(project_queryset: QuerySet, org_id: int):
    project_list = []
    if isinstance(project_queryset, QuerySet):
        project_list = list(project_queryset.values_list("id", flat=True))
    elif isinstance(project_queryset, list):
        project_list = [project_id for project_id in project_queryset]

    project_user_queryset = ProjectUser.objects.select_related("user", "role").filter(
        project_id__in=project_list,
        role__id__in=OrganizationConfigRoleService.project_list_assignment_role_ids(org_id=org_id),
    )
    project_dict = defaultdict()

    for obj in project_user_queryset:
        user_data = UserSerializer(obj.user).data

        if obj.project_id not in project_dict:
            project_dict[obj.project_id] = defaultdict(list)

        if obj.role_id not in project_dict[obj.project_id]:
            project_dict[obj.project_id][HashIdConverter.encode(obj.role_id)].append(user_data)

    return project_dict


def project_archive_status_update(project_id: int, user_id: int, org_id: int, is_archived: bool):
    project_org_data_obj = ProjectOrgData.objects.get(project_id=project_id, organization_id=org_id)

    if project_org_data_obj.is_archived == is_archived:
        return

    status = RDStatus.PROJECT_ARCHIVED.value if is_archived else RDStatus.PROJECT_UNARCHIVED.value
    project_org_data_obj.is_archived = is_archived
    project_org_data_obj.save(update_fields=["is_archived"])
    project_status_history_create(
        project_id=project_id, status=status, user_id=user_id, organization_id=org_id, module=Module.PROJECT.value
    )
    on_commit(
        partial(
            UserProjectSearchConfigCache.delete,
            user_org_data=UserOrgData(user_id=user_id, org_id=org_id),
        )
    )


def project_status_history_create(
    *,
    project_id: int,
    module: int,
    status: int,
    user_id: int,
    organization_id: int = None,
    save: bool = True,
    clean: bool = True,
) -> ProjectStatusHistory:
    status_history = ProjectStatusHistory()
    status_history.project_id = project_id
    status_history.status = status
    status_history.module = module
    status_history.created_by_id = user_id
    status_history.organization_id = organization_id
    if clean:
        status_history.full_clean()
    if save:
        status_history.save()

    return status_history


def actual_start_date_assignment(project_id: int, user_id: int):
    project = Project.objects.get(id=project_id)
    if not project.actual_started_at:
        project_update(project=project, data={"actual_started_at": timezone.now()}, updated_by_id=user_id)


class ProjectStatusProcessor:
    _is_processed: bool = False
    _is_updated: bool = False
    _project_status: Optional[ProjectStatus] = None
    _module: Optional[Module] = None
    _status: int = None
    _rd_status: Optional[int] = None
    _module_status: Optional[int] = None

    def __init__(self, project_status: ProjectStatus, module: Module, status: int):
        self._project_status = project_status
        self._module = module
        self._status = status

    def process(self) -> None:
        self._is_processed = True
        if ProjectStatusMainService.is_status_change_allowed(
            module=self._module,
            old_status=self._project_status.get_module_status(module=self._module),
            new_status=self._status,
        ):
            self._is_updated = True
            self._project_status.set_module_status(module=self._module, status=self._status)
            if self._module == Module.PROJECT:
                self._rd_status = self._status
            else:
                self._module_status = self._status
                rd_status = ProjectStatusMainService.get_new_project_status(module=self._module, status=self._status)
                try:
                    if rd_status is not None and ProjectStatusMainService.is_status_change_allowed(
                        module=Module.PROJECT,
                        old_status=self._project_status.get_module_status(module=Module.PROJECT),
                        new_status=rd_status,
                    ):
                        self._rd_status = rd_status
                        self._project_status.set_module_status(module=Module.PROJECT, status=rd_status)
                except StatusNotAllowed:
                    pass

    @property
    def rd_status(self) -> int:
        if not self._is_processed:
            raise UnprocessedStatus("Please call process() method before getting project status.")
        return self._rd_status

    @property
    def module_status(self) -> int:
        if not self._is_processed:
            raise UnprocessedStatus("Please call process() method before getting module status.")
        return self._module_status

    @property
    def is_updated(self):
        return self._is_updated

    @property
    def project_status(self):
        if not self._is_processed:
            raise UnprocessedStatus("Please call process() method before getting project status object.")
        return self._project_status


class ProjectStatusBaseService(abc.ABC):
    _project_status = None
    _user_id = None

    module_and_model_field_mapping = {
        Module.PROJECT.value: "project_status",
        Module.RECCE.value: "recce_status",
        Module.DESIGN.value: "design_status",
        Module.BOQ.value: "boq_status",
        Module.WORK_REPORT.value: "work_report_status",
        Module.ORDER.value: "order_status",
    }

    def __init__(self, project_status: ProjectStatus, user_id: int):
        self._project_status = project_status
        self._user_id = user_id

    @classmethod
    def get_project_status_field(cls, module) -> str:
        return cls.module_and_model_field_mapping.get(module)

    @abc.abstractmethod
    def update(self, module: Module, status: int) -> bool:
        pass

    @staticmethod
    def create_status_histories(status_histories: list[ProjectStatusHistory]):
        """
        create history in bulk
        """
        return ProjectStatusHistory.objects.bulk_create(objs=status_histories)

    @staticmethod
    def update_project_statuses(project_statuses: list[ProjectStatus], fields: List[str]):
        """
        create history in bulk
        """
        return ProjectStatus.objects.bulk_update(project_statuses, fields=fields)


class ProjectStatusHoldLostRectificationService(ProjectStatusBaseService):
    def update(self, module: Module, status: int) -> bool:
        if (
            module != Module.PROJECT
            and status not in ProjectHoldLostMappings.get_all_hold_lost_rectification_statuses()
        ):
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")

        flag = False
        history = None

        if status == RDStatus.HOLD.value:
            flag, history = self.hold()
        elif status == RDStatus.LOST.value:
            flag, history = self.lost()
        elif status == RDStatus.RECTIFICATION.value:
            flag, history = self.rectification()
        elif status == RDStatus.RESUME.value:
            flag, history = self.resume()
        elif status == RDStatus.RESTORE.value:
            flag, history = self.restore()
        elif status == RDStatus.RECTIFICATION_COMPLETE.value:
            flag, history = self.rectification_complete()

        if flag and not history and status != RDStatus.RECTIFICATION_COMPLETE.value:
            history = project_status_history_create(
                project_id=self._project_status.project_id,
                module=Module.PROJECT,
                status=status,
                user_id=self._user_id,
                organization_id=self._project_status.organization_id,
                save=False,
                clean=False,
            )
        if history:
            self.create_status_histories(status_histories=[history])
        if flag:
            self._project_status.save(update_fields=["project_status"])
            # Update rd_status if organization is creator org
            if self._project_status.organization_id == self._project_status.project.organization_id:
                project = self._project_status.project
                project.rd_status = self._project_status.project_status
                project.save(update_fields=["rd_status"])
            return True
        return False

    def hold(self):
        if self._project_status.project_status not in ProjectHoldLostMappings.get_status_list_for_hold():
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        self._project_status.set_module_status(module=Module.PROJECT, status=RDStatus.HOLD.value)

        return True, None

    def rectification(self):
        if self._project_status.project_status not in ProjectHoldLostMappings.get_status_list_for_rectification():
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        self._project_status.set_module_status(module=Module.PROJECT, status=RDStatus.RECTIFICATION.value)
        return True, None

    def lost(self):
        if self._project_status.project_status not in ProjectHoldLostMappings.get_status_list_for_lost():
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        self._project_status.set_module_status(module=Module.PROJECT, status=RDStatus.LOST.value)

        return True, None

    def recover_module_corresponding_project_status(
        self,
    ):
        history, new_status = self.recover_project_status_using_module(
            organization_id=self._project_status.organization_id,
            project_status_histories=get_module_status_history_of_project(
                project_id=self._project_status.project_id, organization_id=self._project_status.organization_id
            ),
        )
        self._project_status.set_module_status(module=Module.PROJECT, status=new_status)
        if history is None and new_status == RDStatus.PROJECT_CREATED:
            return None

        history = project_status_history_create(
            project_id=self._project_status.project_id,
            module=Module.PROJECT,
            status=new_status,
            user_id=self._user_id,
            organization_id=self._project_status.organization_id,
            save=False,
            clean=False,
        )
        return history

    def recover_status(self, history_status: int):
        new_status = self.recover_project_status(
            organization_id=self._project_status.organization_id,
            project_status_histories=get_status_history_without_hold_lost_rectification(
                project_id=self._project_status.project_id, organization_id=self._project_status.organization_id
            ),
        )
        self._project_status.set_module_status(module=Module.PROJECT, status=new_status)

        history = project_status_history_create(
            project_id=self._project_status.project_id,
            module=Module.PROJECT,
            status=history_status,
            user_id=self._user_id,
            organization_id=self._project_status.organization_id,
            save=False,
            clean=False,
        )
        return history

    def resume(self):
        if self._project_status.project_status != RDStatus.HOLD.value:
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        history = self.recover_status(history_status=RDStatus.RESUME.value)
        return True, history

    def restore(self):
        if self._project_status.project_status != RDStatus.LOST.value:
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        history = self.recover_status(history_status=RDStatus.RESTORE.value)

        return True, history

    def rectification_complete(self):
        if self._project_status.project_status != RDStatus.RECTIFICATION.value:
            raise ProjectHoldLostRectificationNotAllowed("Status Change Not Allowed")
        history = self.recover_module_corresponding_project_status()
        return True, history

    @classmethod
    def recover_project_status(
        cls, organization_id: int, project_status_histories: List[ProjectStatusHistory]
    ) -> RDStatus:
        def sort_func(value):
            return ProjectStatusMainService.get_weight(module=Module.PROJECT, status_value=value)

        organization_ids = list()
        statuses = list()
        for project_status_history in project_status_histories:
            if project_status_history.organization_id in [organization_id, None]:
                if not ProjectStatusMainService.is_common_status(
                    module=Module.PROJECT, status=project_status_history.status
                ):
                    return project_status_history.status
                if len(statuses) == 0:
                    statuses.append(project_status_history.status)
            else:
                if not ProjectStatusMainService.is_common_status(
                    module=Module.PROJECT, status=project_status_history.status
                ):
                    continue
                if project_status_history.organization_id not in organization_ids:
                    statuses.append(project_status_history.status)
                    organization_ids.append(project_status_history.organization_id)
        if len(statuses):
            statuses.sort(reverse=True, key=sort_func)
            return statuses[0]
        return RDStatus.PROJECT_CREATED

    @classmethod
    def recover_project_status_using_module(
        cls, organization_id: int, project_status_histories: List[ProjectStatusHistory]
    ) -> (ProjectStatusHistory, RDStatus):
        for project_status_history in project_status_histories:
            if project_status_history.organization_id not in [
                organization_id,
                None,
            ] and not ProjectStatusMainService.is_common_status(
                module=project_status_history.module, status=project_status_history.status
            ):
                continue

            final_status = PROJECT_STATUS_AGAINST_MODULE_STATUS_MAPPING.get(
                (project_status_history.module, project_status_history.status)
            )
            if final_status:
                return project_status_history, final_status

        return None, RDStatus.PROJECT_CREATED


class ProjectStatusMainService(ProjectStatusBaseService):
    """This class will contain base service which will be used in
    ProjectStatusUpdate Service and ProjectModuleStatus Update Service
    This contains two functions-
    1- is_status_change_allowed
    2- is_common_status
    3- get_new_project_status
    """

    module_and_status_weight_mapping = {
        Module.PROJECT.value: PROJECT_STATUS_WEIGHT_MAPPING,
        Module.RECCE.value: RECCE_STATUS_WEIGHT_MAPPING,
        Module.DESIGN.value: DESIGN_STATUS_WEIGHT_MAPPING,
        Module.BOQ.value: BOQ_STATUS_WEIGHT_MAPPING,
        Module.WORK_REPORT.value: WORK_REPORT_WEIGHT_MAPPING,
        Module.ORDER.value: ORDER_STATUS_WEIGHT_MAPPING,
    }

    def update(self, module: Module, status: int, allow_cascade: bool = True) -> bool:
        project_statuses, histories = self.process_status_change(
            project_status=self._project_status,
            module=module,
            status=status,
            user_id=self._user_id,
            selector=project_status_fetch_others,
        )
        if not len(histories):
            return False

        fields = [self.get_project_status_field(module=module)]
        # Update rd_status if organization is creator org
        if (
            module == Module.PROJECT
            and self._project_status.organization_id == self._project_status.project.organization_id
        ):
            project = self._project_status.project
            project.rd_status = self._project_status.project_status
            project.save(update_fields=["rd_status"])
            # Till Here

        if not module == Module.PROJECT and self.get_new_project_status(module=module, status=status) and allow_cascade:
            fields.append(self.get_project_status_field(module=Module.PROJECT))
            # Update rd_status if organization is creator org
            if self._project_status.organization_id == self._project_status.project.organization_id:
                project = self._project_status.project
                project.rd_status = self._project_status.project_status
                project.save(update_fields=["rd_status"])
            # Till Here
        self.update_project_statuses(project_statuses=project_statuses, fields=fields)

        if module == Module.DESIGN:
            on_commit(
                partial(
                    project_design_status_events_trigger,
                    project_id=self._project_status.project_id,
                    design_status=status,
                    user_id=self._user_id,
                )
            )

        self.create_status_histories(histories)

        return True

    @classmethod
    def update_status_and_initialize_history(
        cls, project_status: ProjectStatus, module: Module, status: int, user_id: int, raise_exception=True
    ) -> Tuple[ProjectStatus, List[ProjectStatusHistory], bool]:
        created_project_history_objects = list()
        project_status_processor = ProjectStatusProcessor(
            project_status=project_status,
            module=module,
            status=status,
        )
        try:
            project_status_processor.process()
        except StatusNotAllowed:
            if raise_exception:
                raise ProjectStatusValidationError(
                    f"""
                        This action can not be performed when project is in
                        {dict(RDStatus.choices).get(project_status.get_module_status(module))} stage.
                        """
                )
            return project_status, created_project_history_objects, False
        if not project_status_processor.is_updated:
            return project_status, created_project_history_objects, False
        if project_status_processor.rd_status:
            created_project_history_objects.append(
                project_status_history_create(
                    project_id=project_status.project_id,
                    module=Module.PROJECT,
                    status=project_status_processor.rd_status,
                    user_id=user_id,
                    organization_id=project_status.organization_id,
                    save=False,
                    clean=False,
                )
            )
        if project_status_processor.module_status:
            created_project_history_objects.append(
                project_status_history_create(
                    project_id=project_status.project_id,
                    module=module,
                    status=project_status_processor.module_status,
                    user_id=user_id,
                    organization_id=project_status.organization_id,
                    save=False,
                )
            )
        return project_status, created_project_history_objects, True

    @classmethod
    def process_status_change(
        cls, project_status: ProjectStatus, module: Module, status: int, user_id: int, selector: callable
    ) -> Tuple[List[ProjectStatus], List[ProjectStatusHistory]]:
        updated_project_objects: List[ProjectStatus] = list()
        created_project_history_objects: List[ProjectStatusHistory] = list()
        status_object, history_objects, is_updated = cls.update_status_and_initialize_history(
            project_status=project_status, module=module, status=status, user_id=user_id, raise_exception=True
        )
        if not is_updated:
            return updated_project_objects, created_project_history_objects
        updated_project_objects.append(status_object)
        created_project_history_objects.extend(history_objects)
        if ProjectStatusMainService.is_common_status(module=module, status=status):
            project_statuses: List[ProjectStatus] = selector(
                project_id=project_status.project_id, organization_id=project_status.organization_id
            )
            for other_project_status in project_statuses:
                other_status_object, other_history_objects, is_other_updated = cls.update_status_and_initialize_history(
                    project_status=other_project_status,
                    module=module,
                    status=status,
                    user_id=user_id,
                    raise_exception=False,
                )
                if is_other_updated:
                    updated_project_objects.append(other_status_object)
                    created_project_history_objects.extend(other_history_objects)
        return updated_project_objects, created_project_history_objects

    @classmethod
    def get_weight(cls, module: int, status_value: int) -> int:
        weight_dict = cls.module_and_status_weight_mapping.get(module)
        return weight_dict.get(status_value)

    @classmethod
    def is_status_change_allowed(cls, module: int, old_status: int, new_status: int) -> bool:
        """This  Service will check if status change from current to the new is allowed for the following project.
        This function will return boolean value i.e. if status change is allowed.
        This will contain logic for the all the status including restriction.
        """
        if old_status == new_status:
            return False
        if module not in cls.module_and_status_weight_mapping.keys():
            raise Exception("Module has no weightage declared")

        if module == Module.PROJECT.value and old_status == RDStatus.RECTIFICATION.value:
            return False

        if module == Module.PROJECT.value and (
            new_status
            in [
                RDStatus.HOLD.value,
                RDStatus.LOST.value,
                RDStatus.RECTIFICATION.value,
            ]
            or old_status
            in [
                RDStatus.HOLD.value,
                RDStatus.LOST.value,
                RDStatus.RECTIFICATION.value,
            ]
        ):
            raise StatusNotAllowed(
                "Hold/Lost/Restore/Resume/Rectification/RectificationComplete status change is not allowed."
            )

        new_status_weight = cls.get_weight(module=module, status_value=new_status)
        old_status_weight = cls.get_weight(module=module, status_value=old_status)

        if old_status_weight > new_status_weight:
            return False
        else:
            return True

    @classmethod
    def is_common_status(cls, module, status: int):
        """
        This Service will return True if the status is common for all organization.
        """
        if module in [Module.RECCE.value, Module.DESIGN.value]:
            return True
        elif module == Module.PROJECT and status in [
            RDStatus.PROJECT_CREATED,
            RDStatus.RECCE_PENDING,
            RDStatus.DESIGN_PENDING,
            RDStatus.DESIGN_FREEZE,
        ]:
            return True
        return False

    @classmethod
    def get_new_project_status(cls, module, status) -> int:
        return PROJECT_STATUS_AGAINST_MODULE_STATUS_MAPPING.get((module, status), None)


class ProjectStatusUpdateService:
    @classmethod
    def process(cls, project_id: int, status: int, module: Module, organization_id: int, user_id: int):
        # passed organization_id
        project_status = project_status_fetch(project_id=project_id, organization_id=organization_id)
        if module == Module.PROJECT and status in ProjectHoldLostMappings.get_all_hold_lost_rectification_statuses():
            project_status_service = ProjectStatusHoldLostRectificationService
        else:
            project_status_service = ProjectStatusMainService

        project_status_service_obj = project_status_service(project_status=project_status, user_id=user_id)
        return project_status_service_obj.update(module=module, status=status)


def project_list_fetch_for_roles(queryset: QuerySet, role_list: Union[str, List[str]], query_param_data: dict):
    if not role_list:
        return queryset

    user_role_id_list = list()
    if isinstance(role_list, str):
        role_list = role_list.strip().split(",")
    for role in role_list:
        user_id_list = [
            HashIdConverter.decode(user_id) for user_id in list(query_param_data.get(role.strip()).strip().split(","))
        ]
        user_role_id_list.append(tuple([HashIdConverter.decode(role.strip()), user_id_list]))
    project_list = (
        project_id_fetch_for_user_role(user_role_id_list=user_role_id_list)
        .distinct("project_id")
        .values_list("project_id", flat=True)
    )
    queryset = queryset.filter(id__in=project_list)
    return queryset


def project_list_fetch_for_created_by(queryset: QuerySet, user_id_list: str):
    user_ids = get_id_list_from_string(value=user_id_list)
    project_ids = ProjectStatusHistory.objects.filter(
        created_by_id__in=user_ids,
        status=RDStatus.PROJECT_CREATED,
        module=Module.PROJECT,
    ).values_list("project_id", flat=True)
    return queryset.filter(id__in=project_ids)


def project_counter_list(user: User):
    # passed user org_id
    projects = (
        project_fetch_all(user=user)
        .annotate_project_status(organization_id=user.token_data.org_id)
        .annotate_project_is_archived(organization_id=user.token_data.org_id)
    )
    search_config_dict = projects.aggregate(
        all_projects=Count("id"),
        project_created=Count("id", filter=Q(project_status=RDStatus.PROJECT_CREATED.value, is_archived=False)),
        execution_in_progress=Count(
            "id", filter=Q(project_status=RDStatus.EXECUTION_IN_PROGRESS.value, is_archived=False)
        ),
        execution_completed=Count("id", filter=Q(project_status=RDStatus.EXECUTION_COMPLETED.value, is_archived=False)),
        scope_approval_awaited=Count(
            "id", filter=Q(project_status=RDStatus.SCOPE_APPROVAL_AWAITED.value, is_archived=False)
        ),
        partial_scope_approved=Count(
            "id", filter=Q(project_status=RDStatus.PARTIAL_SCOPE_APPROVED.value, is_archived=False)
        ),
        full_scope_approved=Count("id", filter=Q(project_status=RDStatus.FULL_SCOPE_APPROVED.value, is_archived=False)),
        recce_pending=Count("id", filter=Q(project_status=RDStatus.RECCE_PENDING.value, is_archived=False)),
        design_pending=Count("id", filter=Q(project_status=RDStatus.DESIGN_PENDING.value, is_archived=False)),
        design_freeze=Count("id", filter=Q(project_status=RDStatus.DESIGN_FREEZE.value, is_archived=False)),
        hold=Count("id", filter=Q(project_status=RDStatus.HOLD.value, is_archived=False)),
        lost=Count("id", filter=Q(project_status=RDStatus.LOST.value, is_archived=False)),
        rectification=Count("id", filter=Q(project_status=RDStatus.RECTIFICATION.value, is_archived=False)),
        archived=Count("id", filter=Q(is_archived=True)),
    )
    all_projects_count = search_config_dict["all_projects"] - search_config_dict["archived"]
    diction = [
        {"id": None, "name": "All Projects", "count": all_projects_count},
        {
            "id": RDStatus.PROJECT_CREATED.value,
            "name": dict(RDStatus.choices).get(RDStatus.PROJECT_CREATED.value),
            "count": search_config_dict["project_created"],
        },
        {
            "id": RDStatus.RECCE_PENDING.value,
            "name": dict(RDStatus.choices).get(RDStatus.RECCE_PENDING.value),
            "count": search_config_dict["recce_pending"],
        },
        {
            "id": RDStatus.DESIGN_PENDING.value,
            "name": dict(RDStatus.choices).get(RDStatus.DESIGN_PENDING.value),
            "count": search_config_dict["design_pending"],
        },
        {
            "id": RDStatus.DESIGN_FREEZE.value,
            "name": dict(RDStatus.choices).get(RDStatus.DESIGN_FREEZE.value),
            "count": search_config_dict["design_freeze"],
        },
        {
            "id": RDStatus.SCOPE_APPROVAL_AWAITED.value,
            "name": dict(RDStatus.choices).get(RDStatus.SCOPE_APPROVAL_AWAITED.value),
            "count": search_config_dict["scope_approval_awaited"],
        },
        {
            "id": RDStatus.PARTIAL_SCOPE_APPROVED.value,
            "name": dict(RDStatus.choices).get(RDStatus.PARTIAL_SCOPE_APPROVED.value),
            "count": search_config_dict["partial_scope_approved"],
        },
        {
            "id": RDStatus.FULL_SCOPE_APPROVED.value,
            "name": dict(RDStatus.choices).get(RDStatus.FULL_SCOPE_APPROVED.value),
            "count": search_config_dict["full_scope_approved"],
        },
        {
            "id": RDStatus.EXECUTION_IN_PROGRESS.value,
            "name": dict(RDStatus.choices).get(RDStatus.EXECUTION_IN_PROGRESS.value),
            "count": search_config_dict["execution_in_progress"],
        },
        {
            "id": RDStatus.EXECUTION_COMPLETED.value,
            "name": dict(RDStatus.choices).get(RDStatus.EXECUTION_COMPLETED.value),
            "count": search_config_dict["execution_completed"],
        },
        {
            "id": RDStatus.HOLD.value,
            "name": dict(RDStatus.choices).get(RDStatus.HOLD.value),
            "count": search_config_dict["hold"],
        },
        {
            "id": RDStatus.LOST.value,
            "name": dict(RDStatus.choices).get(RDStatus.LOST.value),
            "count": search_config_dict["lost"],
        },
        {
            "id": RDStatus.RECTIFICATION.value,
            "name": dict(RDStatus.choices).get(RDStatus.RECTIFICATION.value),
            "count": search_config_dict["rectification"],
        },
        {
            "id": RDStatus.PROJECT_ARCHIVED.value,
            "name": dict(RDStatus.choices).get(RDStatus.PROJECT_ARCHIVED.value),
            "count": search_config_dict["archived"],
        },
    ]

    return diction


def project_search_config_data_prepare(user: User):
    roles = OrganizationConfigRoleService.organization_search_config_role_list(org_id=user.token_data.org_id)
    data = {
        "counter_list": project_counter_list(user=user),
        "role_list": roles,
    }
    return SearchConfigSerializer(data).data


def project_client_store_type_list(user: User):
    projects = (
        project_fetch_all(user=user)
        .filter(store_type__isnull=False)
        .select_related("store_type", "store_type__client")
        .distinct("store_type")
    )
    store_type_list = [project.store_type for project in projects]

    return ClientStoreTypeSerializer(store_type_list, many=True).data


def fetch_project_search_config(user: User):
    return UserProjectSearchConfigCache.get(user=user)


def client_store_type_list_fetch(user: User):
    return UserClientStoreTypeListCache.get(user=user)


def organization_user_cache_clear(user_id: int, org_id: int):
    user_org_data = UserOrgData(user_id=user_id, org_id=org_id)
    UserStateListCache.delete(user_org_data=user_org_data)
    UserClientListCache.delete(user_org_data=user_org_data)
    UserProjectStagesListCache.delete(user_org_data=user_org_data)
    UserProjectSearchConfigCache.delete(user_org_data=user_org_data)
    UserClientStoreTypeListCache.delete(user_org_data=user_org_data)
    UserRoleAssignmentCache.delete_many(user_org_data=user_org_data)


def restrict_action_decorator(*args, **kwargs):
    status_list = kwargs.get("status_list")

    def decorator(func):
        def inner(*args, **kwargs):
            project_id = kwargs.get("project_id")
            kwargs.get("origin_org_id", kwargs.get("org_id"))
            # TODO: organization_id to be passed and selector needs to be updated
            if project_id:
                current_project_status = Project.objects.get(id=project_id).rd_status
                if current_project_status in status_list:
                    error = (
                        f"Cannot Perform this action as Project is in "
                        f"{dict(RDStatus.choices).get(current_project_status).upper()} Stage"
                    )
                    raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _(error)})

            return func(*args, **kwargs)

        return inner

    return decorator


def project_attachment_create(*, data: dict, project_id: int, user_id: int):
    attachment = ProjectAttachment()
    attachment.project_id = project_id
    attachment.file = get_relative_path(data.get("url"))
    attachment.name = data.get("name")
    attachment.uploaded_by_id = user_id
    attachment.full_clean()
    attachment.save()
    return attachment


def project_attachment_update(*, data: dict, attachment_id: int, project_id: int, user_id: int):
    obj = ProjectAttachment.objects.filter(project_id=project_id, id=attachment_id).first()
    if obj:
        obj.name = data.get("name")
        obj.updated_by_id = user_id
        obj.updated_at = timezone.now()
        obj.save(update_fields=["name", "updated_by_id", "updated_at"])
        return obj
    raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Attachment Not Found.")})


def project_attachment_delete(*, attachment_id: int, user_id: int):
    ProjectAttachment.objects.filter(id=attachment_id).update(deleted_at=timezone.now(), deleted_by_id=user_id)


def project_user_removed_trigger_event(project: Project, removed_by: User, recipient_ids: List[int]):
    event_data = ProjectUserRemovedEventData(
        project_id=project.pk,
        project_job_id=project.job_id,
        user_id=removed_by.pk,
        recipient_ids=recipient_ids,
    )
    trigger_event(event=Events.PROJECT_USER_REMOVED, event_data=event_data)


def project_date_changed_trigger_event(
    project: Project,
    changed_by: User,
    date_type: ProjectDateChangedDateType,
    changed_from: datetime.date,
    changed_to: datetime.date,
):
    event_data = ProjectDateChangedEventData(
        project_id=project.pk,
        project_job_id=project.job_id,
        user_id=changed_by.pk,
        date_type=date_type,
        changed_from=changed_from,
        changed_to=changed_to,
    )
    trigger_event(event=Events.PROJECT_DATE_CHANGED, event_data=event_data)


def project_date_assign_trigger_event(
    project: Project,
    changed_by: User,
    date_type: ProjectDateChangedDateType,
    date: datetime.date,
):
    event_data = ProjectDateAssignEventData(
        project_id=project.pk,
        project_job_id=project.job_id,
        user_id=changed_by.pk,
        date_type=date_type,
        date=date,
    )
    trigger_event(event=Events.PROJECT_DATE_ASSIGN, event_data=event_data)


def project_date_event_trigger(
    project: Project,
    changed_by: User,
    date_type: ProjectDateChangedDateType,
    changed_to: datetime.datetime,
    changed_from: datetime.datetime = None,
):
    if changed_from is None:
        on_commit(
            partial(
                project_date_assign_trigger_event,
                project=project,
                date_type=date_type,
                date=changed_to.strftime("%Y-%m-%d") if changed_to else None,
                changed_by=changed_by,
            )
        )
    else:
        on_commit(
            partial(
                project_date_changed_trigger_event,
                project=project,
                date_type=date_type,
                changed_from=changed_from.strftime("%Y-%m-%d") if changed_from else None,
                changed_to=changed_to.strftime("%Y-%m-%d") if changed_to else None,
                changed_by=changed_by,
            )
        )


def project_date_events_trigger(
    fields: list[str],
    project: Project,
    perv_recce_due_at: datetime.datetime,
    prev_kam_completion_due_at: datetime.datetime,
    prev_expected_started_at: datetime.datetime,
    changed_by: User,
):
    for field in fields:
        if field == "recce_due_at":
            project_date_event_trigger(
                project=project,
                date_type=ProjectDateChangedDateType.RECCE_DUE_AT,
                changed_from=perv_recce_due_at,
                changed_to=project.recce_due_at,
                changed_by=changed_by,
            )
        elif field == "kam_completion_due_at":
            project_date_event_trigger(
                project=project,
                date_type=ProjectDateChangedDateType.EXECUTION_DUE_AT,
                changed_from=prev_kam_completion_due_at,
                changed_to=project.kam_completion_due_at,
                changed_by=changed_by,
            )

        elif field == "expected_started_at":
            project_date_event_trigger(
                project=project,
                date_type=ProjectDateChangedDateType.EXPECTED_STARTED_AT,
                changed_from=prev_expected_started_at,
                changed_to=project.expected_started_at,
                changed_by=changed_by,
            )


def project_design_approved_event_trigger(project_id: int):
    event_data = DesignApprovedEventData(project_id=project_id)
    trigger_event(event=Events.DESIGN_APPROVED, event_data=event_data)


def project_design_freezed_event_trigger(project_id: int, user_id: int):
    event_data = DesignFreezedEventData(project_id=project_id, user_id=user_id)
    trigger_event(event=Events.DESIGN_FREEZED, event_data=event_data)


def project_design_status_events_trigger(project_id: int, design_status: int, user_id: int):
    if design_status == DesignStatus.DESIGN_APPROVED:
        project_design_approved_event_trigger(project_id=project_id)
    elif design_status == DesignStatus.DESIGN_FREEZE:
        project_design_freezed_event_trigger(project_id=project_id, user_id=user_id)


def project_created_by_list(user: User):
    return ProjectCreatedByCache.get(user=user)


def project_organization_status_create(
    project_org_obj,
):
    if ProjectStatus.objects.filter(
        organization_id=project_org_obj.organization_id, project_id=project_org_obj.project_id
    ).exists():
        return
    project = Project.objects.get(id=project_org_obj.project_id)
    if project_org_obj.organization_id == project.organization_id:
        logger.info("project_org_signal : project org is creator org exiting")
        return

    assigner_status_histories = ProjectStatusHistory.objects.filter(
        module__in=[Module.RECCE.value, Module.DESIGN.value],
        project_id=project_org_obj.project_id,
        organization_id=project_org_obj.assigned_by_id,
    )
    logger.info(f"project_org_signal : Status History Fetched, Count- {len(assigner_status_histories)}")
    status = ProjectStatus()
    status.organization_id = project_org_obj.organization_id
    status.project_id = project_org_obj.project_id

    proj_org_data = ProjectOrgData(
        organization_id=project_org_obj.organization_id,
        project_id=project_org_obj.project_id,
    )

    status_to_create_for_new_org = ProjectStatusHistory.objects.filter(
        project_id=project_org_obj.project_id, module__in=[Module.RECCE.value, Module.DESIGN.value]
    ).order_by("id")
    logger.info(
        f"project_org_signal : Status History Fetched for project status fetched,\
        Count- {len(status_to_create_for_new_org)}"
    )

    status_history_list = []

    for status_history_obj in status_to_create_for_new_org:
        status, history_objects, is_updated = ProjectStatusMainService.update_status_and_initialize_history(
            project_status=status,
            module=status_history_obj.module,
            status=status_history_obj.status,
            user_id=status_history_obj.created_by_id,
        )

        if history_objects:
            for obj in history_objects:
                if obj.module == Module.PROJECT.value:
                    obj.created_at = status_history_obj.created_at
                    status_history_list.append(obj)

    logger.info(f"project_org_signal : Project status History , Count- {len(status_history_list)}")

    status.save()
    proj_org_data.save()
    logger.info("project_org_signal : Project Status Saved")

    new_status_history_list = [
        ProjectStatusHistory(
            project_id=project_org_obj.project_id,
            organization_id=project_org_obj.organization_id,
            module=Module.PROJECT.value,
            status=RDStatus.PROJECT_ASSIGNED.value,
            created_by_id=project_org_obj.created_by_id,
        ),
    ]

    for history in assigner_status_histories:
        new_history_obj = ProjectStatusHistory()
        new_history_obj.status = history.status
        new_history_obj.module = history.module
        new_history_obj.organization_id = project_org_obj.organization_id
        new_history_obj.project_id = project_org_obj.project_id
        new_history_obj.created_at = history.created_at
        new_history_obj.created_by_id = history.created_by_id
        new_status_history_list.append(new_history_obj)

    new_status_history_list.extend(status_history_list)
    logger.info(f"project_org_signal : All status History , Count- {len(new_status_history_list)}")

    ProjectStatusHistory.objects.bulk_create(objs=new_status_history_list)
    logger.info("project_org_signal : History created")


def create_project_work_progress_configs(data):
    from project.domain.factories import ProjectServiceFactory

    logger.info("Creating project work progress configs", data=data)

    wp_service = ProjectServiceFactory(
        user=User(id=SYSTEM_USER_ID),
        user_entity=OrgUserEntity(org_id=data.organization_id, user_id=SYSTEM_USER_ID),
    ).get_work_progress_service()

    wp_service.create_project_configs(project_id=data.project_id)


def project_progress_sheet_fetch(project_id: int, org_id: int):
    return ProjectProgressSchedule.objects.filter(project_id=project_id, organization_id=org_id).first()


def project_progress_shedule_create(project_id: int, org_id: int, sheet_url: str):
    ProjectProgressSchedule.objects.update_or_create(
        project_id=project_id, organization_id=org_id, defaults={"sheet_url": sheet_url}
    )


def project_sheet_fetch(project_id: int, org_id: int, sheet_type: SheetType):
    sheet_url_type = ""
    if sheet_type == SheetType.COST_SHEET:
        sheet_url_type = "cost_sheet_url"
    elif sheet_type == SheetType.COST_COMPARE_SHEET:
        sheet_url_type = "cost_compare_sheet_url"
    elif sheet_type == SheetType.BOQ_VS_ORDER_SHEET:
        sheet_url_type = "boq_vs_order_sheet_url"
    return getattr(
        ProjectOrgExtraData.objects.filter(project_id=project_id, organization_id=org_id).first(), sheet_url_type, None
    )


def project_sheet_create(project_id: int, org_id: int, sheet_url: str, sheet_type: SheetType):
    sheet_url_type = ""
    if sheet_type == SheetType.COST_SHEET:
        sheet_url_type = "cost_sheet_url"
    elif sheet_type == SheetType.COST_COMPARE_SHEET:
        sheet_url_type = "cost_compare_sheet_url"
    elif sheet_type == SheetType.BOQ_VS_ORDER_SHEET:
        sheet_url_type = "boq_vs_order_sheet_url"
    ProjectOrgExtraData.objects.update_or_create(
        project_id=project_id, organization_id=org_id, defaults={sheet_url_type: sheet_url}
    )


def project_creator_timeline_fetch(project: Project, organization_id: int) -> ProjectTimelineData:
    actual_start_date = project.actual_started_at
    if actual_start_date is None:
        history = boq_element_history_fetch_all_using_project_and_org(
            project_id=project.id, organization_id=organization_id
        ).aggregate(
            actual_start_date=Min("created_at"),
        )
        actual_start_date = history.get("actual_start_date")

    return ProjectTimelineData(
        expected_start_date=project.expected_started_at,
        execution_due_date=project.kam_completion_due_at,
        actual_start_date=actual_start_date,
        actual_completion_date=project.execution_completed_at,
    )


def project_org_data_update(*, instance: ProjectOrgData, data: Dict[str, Any]) -> ProjectOrgData:
    fields = ["progress_percentage"]
    project_org_data, _, _ = model_update(instance=instance, fields=fields, data=data)
    return project_org_data


def project_other_organization_fetch(parent_org_ids: List[int], child_org_ids: List[int], project_id: int, org_id: int):
    from project.share.domain.service.services import BaseOrganizationService, sort_via_assignment_type

    organizations: QuerySet[Organization] = (
        OrganizationRepository()
        .get_list(project_id=project_id, org_from_id=org_id, parent_org_ids=parent_org_ids, child_org_ids=child_org_ids)
        .select_related("config__poc")
    )

    for org in organizations:
        setattr(org, "assignment_type", BaseOrganizationService.get_assignment_type(organization=org))

    return sort_via_assignment_type(organizations)


def get_client_role(organization_id: int, created_by_id: int) -> Role:
    role = RoleRepository().create_client_role(organization_id=organization_id, created_by_id=created_by_id)

    org_module_settings = (
        OrganizationDefaultModuleSetting.objects.filter(org_id=organization_id, is_client=True)
        .prefetch_related("level_mappings")
        .all()
    )
    permissions = []
    if org_module_settings:
        module_wise_setting_mappings = {
            setting.module: [level.level for level in setting.level_mappings.all()] for setting in org_module_settings
        }
    else:
        module_wise_setting_mappings = DEFAULT_CLIENT_PERMISSIONS_GROUP_MODULE_WISE

    for module, levels in module_wise_setting_mappings.items():
        for level in levels:
            permissions.extend(PERMISSION_SET_MAPPING.get(PermissionGroup.to_enum(level), []))

    RolePermissionRepository().bulk_create(
        instance_list=[RolePermission(role_id=role.pk, permission=permission) for permission in permissions]
    )
    return role


def get_client_ids(org_id: int, project_id: int) -> list[int]:
    data: ProjectDataEntity = ProjectDataCache.get(
        project_details=ProjectOrganizationEntity(project_id=project_id, organization_id=org_id)
    )

    if org_id == data.client_id:
        return []
    elif org_id == data.organization_id:
        return [data.client_id]
    else:
        ids = []
        project_assignments = ProjectAssignmentCache.get(instance_id=project_id)
        for organization_id, assignment in project_assignments.items():
            if str(org_id) == organization_id:
                ids.extend(assignment["role_ids"].keys())
        return [int(i) for i in set(ids) if int(i) != org_id]


def get_project_proposal_client(project_id: int, organization_id: int):
    client = (
        ProjectOrganization.objects.filter(organization=organization_id, project_id=project_id).first().assigned_by
    )  # TODO: use .all() for drop down, one vendor can have multiple clients
    if client.id == organization_id:
        client = Project.objects.filter(id=project_id).select_related("client").first().client
    return client


def get_project_org_custum_field_schema(project_id: int, org_id: int, context: MicroContextChoices.choices):
    return ProjectOrgCustomFieldConfig.objects.filter(project_id=project_id, org_id=org_id, context=context).first()


def create_project_org_custum_field_schema(
    project_id: int, org_id: int, context: MicroContextChoices.choices, column_config_list: list
):
    return (
        ProjectOrgCustomFieldConfig.objects.update_or_create(
            project_id=project_id,
            org_id=org_id,
            context=context,
            defaults={"custom_schema": CustomColumnConfigurationSerializer(column_config_list, many=True).data},
        ),
    )


def get_user_and_permission_check_with_token_data(user_id: int):
    org_user = (
        OrganizationUser.objects.filter(user_id=user_id, is_active=True, user__deleted_at__isnull=True)
        .select_related("role")
        .first()
    )
    if org_user is None:
        raise OrgUserNotFoundException("User not found")
    org_observer = False
    if org_user.role:
        permissions = RolePermissionCache.get(instance_id=org_user.role_id)
        if Permissions.CAN_VIEW_ALL_PROJECTS.value in permissions:
            org_observer = True

    token_data = TokenData(
        key="", is_admin=org_user.is_admin, org_id=org_user.organization_id, org_observer=org_observer, user_id=user_id
    )
    setattr(org_user.user, "token_data", token_data)
    return org_user.user


def fetch_project_region(*, project_id: int, org_id: int) -> int:
    state = Store.objects.filter(project_id=project_id).values_list("state", flat=True).first()
    region_id = region_state_mapping_fetch(state=state, org_id=org_id)
    if not region_id:
        raise ProjectRegionStateNotFoundException("Project Region Not Found")
    return region_id


def check_context_permission(context: str, user: User, org_id: int, project_id: int, type: OrderType):
    check_org_permissions = False
    if context in [
        MicroContext.EXPENSE.value,
        MicroContext.INVOICE.value,
        MicroContext.PAYMENT_REQUEST.value,
        MicroContext.INSTA_ORDER.value,
        MicroContext.ORDER.value,
    ]:
        check_org_permissions = True
    if context == MicroContext.INVOICE.value:
        if type == OrderType.INCOMING.value:
            context = MicroContext.CLIENT_INVOICE.value
        else:
            context = MicroContext.VENDOR_INVOICE.value
    elif context == MicroContext.PAYMENT_REQUEST.value:
        if type == OrderType.INCOMING.value:
            context = MicroContext.CLIENT_PAYMENT_REQUEST.value
        else:
            context = MicroContext.VENDOR_PAYMENT_REQUEST.value

    if check_org_permissions:
        if ProjectPermissionHelper.check_user_permission_with_tokendata(
            project_id=project_id,
            user=user,
            permissions_to_check=CONTEXT_PROJECT_PERMISSION_MAPPING.get(context),
            org_id=org_id,
        ) and OrgPermissionHelper.check_user_permission_with_tokendata(
            user=user, permissions_to_check=CONTEXT_CORE_PERMISSION_MAPPING.get(context)
        ):
            return True
    else:
        if ProjectPermissionHelper.check_user_permission_with_tokendata(
            project_id=project_id,
            user=user,
            permissions_to_check=CONTEXT_PROJECT_PERMISSION_MAPPING.get(context),
            org_id=org_id,
        ):
            return True
    return False


def check_or_assign_project_permission(
    project_id: int, to_assign_org: int, assigned_by_org: int, created_by_id: int
) -> bool:
    logger.info("Project permission sharing")
    if (
        ProjectOrganizationHelper.is_org_assignment_permitted(
            project_id=project_id, to_assign_org=to_assign_org, assigned_by_org=assigned_by_org
        )
        and not ProjectOrganization.objects.filter(
            organization_id=to_assign_org, project_id=project_id, assigned_by_id=assigned_by_org
        ).exists()
    ):
        role = RoleRepository().create_vendor_role(organization_id=assigned_by_org, created_by_id=created_by_id)
        permissions = (
            PERMISSION_SET_MAPPING.get(PermissionGroup.PROJECT_LEVEL_1, [])
            + PERMISSION_SET_MAPPING.get(PermissionGroup.RECCE_LEVEL_1, [])
            + PERMISSION_SET_MAPPING.get(PermissionGroup.DESIGN_LEVEL_3, [])
        )
        RolePermissionRepository().bulk_create(
            instance_list=[RolePermission(role_id=role.pk, permission=permission) for permission in permissions]
        )
        project_organization_assign(
            organization_id=to_assign_org,
            project_id=project_id,
            assigned_by_organization_id=assigned_by_org,
            created_by_id=created_by_id,
            role_id=role.pk,
        )
        logger.info("Project permission shared successfully")
        return True
    else:
        logger.info("Project permission not shared")
        return False


def project_org_item_type_config_assignment(
    assignee_org_id: int, assigner_org_id: int, project_id: int, creator_id: int
):
    org_configs: list[ItemTypeConfigData] = org_item_type_config_fetch(org_id=assigner_org_id)
    project_org_configs: list[ProjectOrgItemTypeConfig] = []
    for config in org_configs:
        project_org_configs.append(
            ProjectOrgItemTypeConfig(
                organization_id=assignee_org_id,
                item_type_id=config.id,
                project_id=project_id,
                created_by_id=creator_id,
                update_method=config.update_method,
            )
        )
    ProjectOrgItemTypeConfig.objects.bulk_create(project_org_configs)


def fetch_project_business_category(project_id: int, org_id: int):
    project = Project.objects.get(id=project_id)
    if org_id == project.organization_id:
        return (
            project.business_category_id
            if project.business_category
            else BusinessCategoryTypeConditionEnum.NO_BUSINESS_CATEGORY.value[0]
        )
    return BusinessCategoryTypeConditionEnum.SHARED_PROJECT.value[0]  # shared project


def get_project_custom_field_default_config():
    """returns default static config"""
    return deepcopy(PROJECT_CUSTOM_FIELD_CONFIG)


def segregate_system_and_custom_field_data(fields: dict):
    system_field_dict = {}
    custom_field_dict = {}
    for key, value in fields.items():
        if key in SYSTEM_FIELD_UUIDS:
            system_field_dict[key] = value
        else:
            custom_field_dict[key] = value
    return system_field_dict, custom_field_dict


def fetch_project_section_config(org_id: int) -> List[dict]:
    config = fetch_project_custom_field_config(org_id=org_id)
    if not config:
        return get_project_custom_field_default_config()
    return ProjectCustomSectionSerializer(config, many=True).data


def project_detail_config_fetch(org_id: int, project_id: int, basic_section_only=False) -> List[dict]:
    """
    Fetch config w.r.t project detail (unarchive + archive fields with data in given project).
    if config is not found -> fetch default config
    return config with basic and advance section accordingly
    """
    config = project_detail_custom_field_config_fetch(org_id=org_id, project_id=project_id)
    if basic_section_only:
        config = config.filter(type=ProjectSectionTypeEnum.BASIC.value)
    if not config:
        return (
            [x for x in get_project_custom_field_default_config() if x["type"] == ProjectSectionTypeEnum.BASIC.value]
            if basic_section_only
            else get_project_custom_field_default_config()
        )
    return ProjectCustomSectionSerializer(config, many=True).data


def project_creation_config_fetch(org_id: int) -> List[dict]:
    config = project_custom_field_unarchived_config_fetch(org_id=org_id)
    if not config:
        return get_project_custom_field_default_config()
    return ProjectCustomSectionSerializer(config, many=True).data


def project_instance_fetch(user: User, project_id: int) -> Project:
    """returns project instance"""
    project = get_project_queryset(user=user).filter(pk=project_id).annotate_boq_scope_amount_without_tax().first()
    if not project:
        raise ProjectNotFoundException("Project not found")
    return project


def prepare_system_field_data(project: Project, project_config: ProjectCountryConfigData, timezone: pytz.BaseTzInfo):
    return {
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_NAME.value]: project.name,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.CLIENT.value]: project.client.name,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_ESTIMATE.value]: project.estimated_cost,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.BUSINESS_CATEGORY.value]: (
            {
                "id": HashIdConverter.encode(project.business_category_id),
                "name": project.business_category.name,
            }
            if project.business_category
            else {
                "id": None,
                "name": None,
            }
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.RECCE_DUE_DATE.value]: (
            project.recce_due_at.astimezone(timezone).strftime("%Y-%m-%d") if project.recce_due_at else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.EXPECTED_START_DATE.value]: (
            project.expected_started_at.astimezone(timezone).strftime("%Y-%m-%d")
            if project.expected_started_at
            else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.EXECUTION_DUE_DATE.value]: (
            project.kam_completion_due_at.astimezone(timezone).strftime("%Y-%m-%d")
            if project.kam_completion_due_at
            else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_SCOPE.value]: [
            {"id": HashIdConverter.encode(scope.id), "name": scope.name} for scope in project.scopes.all()
        ],
        SYSTEM_FIELD_NAMES_UUID_MAPPING[
            SystemFieldNameEnum.PROJECT_SCOPE_AMOUNT_WITHOUT_GST.value
        ]: project.total_saved_final_amount_without_tax,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_ADDRESS_LINE_1.value]: project.store.address_line_1,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_ADDRESS_LINE_2.value]: project.store.address_line_2,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.CITY.value]: {
            "id": HashIdConverter.encode(project.store.city_id),
            "name": project.store.city.name,
        }
        if project.store.city
        else None,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.STATE.value]: (
            {
                "id": HashIdConverter.encode(project.store.state_id),
                "name": project.store.state.name if project.store.state else None,
            }
            if project.store.state
            else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.COUNTRY.value]: {
            "id": HashIdConverter.encode(project.store.country_id),
            "name": project.store.country.name if project.store.country else None,
        },
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.ZIPCODE.value]: (
            project.store.zip_code if project.store.zip_code else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.CLIENT_POC_NAME.value]: project.store.dealer_name,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.CLIENT_POC_NUMBER.value]: (
            PhoneNumberSerializer(project.store.dealer_phone_number).data if project.store.dealer_phone_number else None
        ),
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_AREA.value]: project.store.area,
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_CURRENCY.value]: {
            "id": HashIdConverter.encode(project_config.currency.id),
            "name": project_config.currency.code,
        },
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_TAX_TYPE.value]: {
            "id": HashIdConverter.encode(project_config.tax_type.id),
            "name": project_config.tax_type.name,
        },
        SYSTEM_FIELD_NAMES_UUID_MAPPING[SystemFieldNameEnum.PROJECT_TIMEZONE.value]: {
            "id": HashIdConverter.encode(project_config.timezone.id),
            "name": project_config.timezone.name,
        },
    }


def prepare_custom_field_data(project: Project, timezone: pytz.BaseTzInfo, is_list_view=False):
    custom_field_data_dict = {}
    for data_obj in project.custom_text_data.all():
        custom_field_data_dict[str(data_obj.field.uuid)] = data_obj.data
    for data_obj in project.custom_decimal_data.all():
        custom_field_data_dict[str(data_obj.field.uuid)] = data_obj.data
    for data_obj in project.custom_date_data.all():
        custom_field_data_dict[str(data_obj.field.uuid)] = (
            data_obj.data.astimezone(timezone).strftime("%Y-%m-%d") if data_obj.data else None
        )
    if not is_list_view:
        for data_obj in project.custom_richtext_data.all():
            custom_field_data_dict[str(data_obj.field.uuid)] = data_obj.data
        for data_obj in project.custom_file_data.all():
            custom_field_data_dict[str(data_obj.field.uuid)] = (
                {"name": data_obj.name, "url": data_obj.data.url} if data_obj.data else None
            )
        for data_obj in project.custom_multiple_files_fields.all():
            custom_field_data_dict[str(data_obj.field.uuid)] = []
            for attachment in data_obj.custom_multiple_files_data.all():
                custom_field_data_dict[str(data_obj.field.uuid)].append(
                    {
                        "id": HashIdConverter.encode(attachment.id),
                        "name": attachment.name,
                        "url": attachment.data.url,
                        "uploaded_at": attachment.created_at,
                        "uploaded_by": {
                            "id": HashIdConverter.encode(attachment.created_by.id),
                            "name": attachment.created_by.name,
                            "photo": attachment.created_by.photo.url if attachment.created_by.photo.name else None,
                        },
                    }
                )

    for data_obj in project.custom_phonenumber_data.all():
        custom_field_data_dict[str(data_obj.field.uuid)] = (
            PhoneNumberSerializer(data_obj.data).data if data_obj.data else None
        )
    for data_obj in project.custom_dropdown_data.all():
        custom_field_data_dict[str(data_obj.field.uuid)] = (
            {
                "id": HashIdConverter.encode(data_obj.data_id),
                "name": data_obj.data.name,
            }
            if data_obj.data
            else None
        )
    return custom_field_data_dict


def project_created_field_data(created_at: datetime) -> dict:
    return {
        "name": "Project Creation Date",
        "uuid": PROJECT_CREATION_DATE_UUID,
        "type": CustomFieldTypeEnum.DATE.value,
        "position": 0,
        "is_archived": False,
        "value": created_at,
    }


def project_scope_amount_data(currency_symbol: str, total_amount: Optional[decimal.Decimal] = None) -> dict:
    return {
        "name": "Project Scope Amount" + f" (in {currency_symbol})",
        "uuid": PROJECT_SCOPE_AMOUNT_WITHOUT_GST_UUID,
        "type": CustomFieldTypeEnum.DECIMAL.value,
        "position": 4,
        "is_archived": False,
        "value": total_amount if total_amount else "-",
    }


def project_scope_progress_data(scope_progress: Optional[decimal.Decimal] = None) -> dict:
    return {
        "name": "Scope Progress",
        "uuid": PROJECT_SCOPE_PROGRESS_UUID,
        "type": CustomFieldTypeEnum.DECIMAL.value,
        "position": 5,
        "is_archived": False,
        "value": scope_progress if scope_progress else "-",
    }


def project_schedule_progress_data(schedule_progress: Optional[decimal.Decimal] = None) -> dict:
    return {
        "name": "Schedule Progress",
        "uuid": PROJECT_SCHEDULE_PROGRESS_UUID,
        "type": CustomFieldTypeEnum.DECIMAL.value,
        "position": 6,
        "is_archived": False,
        "value": schedule_progress if schedule_progress else "-",
    }


def get_project_section_data(
    project: Project,
    sections: list,
    can_edit_section: bool,
    project_config: ProjectCountryConfigData,
    timezone: pytz.BaseTzInfo,
    user_permissions: list[Permissions],
):
    custom_field_data_dict = prepare_custom_field_data(project=project, timezone=timezone)
    system_field_data_dict = prepare_system_field_data(
        project=project, project_config=project_config, timezone=timezone
    )
    logger.info(
        "System field data and custom field data prepared.",
        custom_field_data_dict=custom_field_data_dict,
        system_field_data_dict=system_field_data_dict,
    )
    system_field_data_dict.update(custom_field_data_dict)
    section_to_return_index = []
    actions = [ProjectSectionActionEnum.EDIT.value] if can_edit_section else []
    for index, section in enumerate(sections):
        section["id"] = section.get("id") if section.get("id") else section.get("name").lower().replace(" ", "_")
        if section.get("uuid") == ASSIGNED_PROJECT_USER_SECTION_UUID:
            continue
        section["actions"] = [] if section.get("uuid") in UNEDITABLE_SYSTEM_SECTION_UUIDS else actions
        section_to_return_index.append(index)
        if section.get("uuid") == IMPORTANT_DATES_SECTION_UUID:
            section.get("fields").insert(0, project_created_field_data(created_at=project.created_at))
        if section.get("uuid") == PROJECT_SCOPE_SECTION_UUID:
            section.get("fields").append(
                project_scope_amount_data(
                    total_amount=(
                        project.total_saved_final_amount_without_tax
                        if project.total_saved_final_amount_without_tax
                        and Permissions.CAN_ACCESS_MY_SCOPE in user_permissions
                        else None
                    ),
                    currency_symbol=project_config.currency.symbol,
                )
            )
            section.get("fields").append(
                project_scope_progress_data(
                    scope_progress=(
                        decimal.Decimal(project.progress_percentage)
                        if Permissions.CAN_ACCESS_MY_SCOPE in user_permissions
                        else None
                    ),
                )
            )
            section.get("fields").append(
                project_schedule_progress_data(
                    schedule_progress=(
                        decimal.Decimal(project.schedule.completion_percentage)
                        if hasattr(project, "schedule")
                        and Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE in user_permissions
                        else None
                    ),
                )
            )
        for field in section.get("fields", []):
            if field.get("uuid") == PROJECT_ESTIMATE_UUID:
                field["name"] = field.get("name") + f" (in {project_config.currency.symbol})"
            field["value"] = field.get("value") if field.get("value") else system_field_data_dict.get(field.get("uuid"))
            field["readonly"] = field.get("uuid") in READONLY_FIELD_UUIDS

    sections_copy = []
    for index in section_to_return_index:
        sections_copy.append(sections[index])

    return sections_copy


def create_field_uuid_data_mapping(section_list: List[Dict]):
    field_uuid_data_mapping: Dict[UUID, dict] = {}
    for section in section_list:
        for field in section.get("fields"):
            field_uuid_data_mapping[field.get("uuid")] = {
                "name": field.get("name"),
                "uuid": field.get("uuid"),
                "type": field.get("type"),
                "position": field.get("position"),
                "is_required": field.get("is_required"),
                "data_counter": field.get("data_counter"),
            }
    return field_uuid_data_mapping


def get_project_data(
    project: Project,
    org_id: int,
    sections: list,
    parent_org_ids: List[int],
    child_org_ids: List[int],
    user_permissions: list,
    project_config: ProjectCountryConfigData,
    timezone: pytz.BaseTzInfo,
):
    can_edit_section = Permissions.CAN_EDIT_PROJECT in user_permissions
    section_data = get_project_section_data(
        project=project,
        sections=sections,
        can_edit_section=can_edit_section,
        project_config=project_config,
        timezone=timezone,
        user_permissions=user_permissions,
    )
    other_organizations = project_other_organization_fetch(
        parent_org_ids=parent_org_ids,
        child_org_ids=child_org_ids,
        project_id=project.pk,
        org_id=org_id,
    )
    project_users = list(
        project_user_fetch_all(project_id=project.pk)
        .select_related("user", "role")
        .prefetch_related("role__organization_config_role")
        .filter(role__organization_id=org_id)
    )
    setattr(project, "sections", section_data)
    setattr(project, "other_organizations", other_organizations)
    setattr(project, "role_mappings", project_users)
    logger.info("Project with data fetched.")
    return project


def get_can_give_project_estimate_reason(project_id: int):
    project_field_histories_values = list(
        project_field_history_fetch_all(project_id=project_id, field="estimated_cost").values_list("value", flat=True)
    )
    return not (len(project_field_histories_values) == 1 and float(project_field_histories_values[0]) == 0)


class ProjectCustomSectionService(ProjectConfigExceptions):
    """
    1. This service segregates input data into sections that need to be created, updated, deleted
    2. While creating sections, it prepares section and their subsequent field and dropdown option objects.
        And then bulk creates them
    3. While updating sections, it fetches sections that need to be updated. And then,
        prepares update section and their subsequent field and dropdown option objects.
        Then, bulk updates them
    4. While deleting sections, it fetches sections that need to be deleted. Fetches all corresponding fields
        that does not have any data in any project. The, soft deletes all dropdown options, fields
         and their corresponding sections.
    """

    def __init__(self, org_id: int, user_id: int) -> None:
        self.org_id = org_id
        self.user_id = user_id
        self.mappings = ProjectCustomFieldMappings(
            section_uuid_object_mapping={},
            field_uuid_object_mapping={},
            dropdown_option_uuid_object_mapping={},
            field_uuid_dropdown_option_uuids_mapping={},
            field_uuid_dropdown_option_names_mapping={},
        )

    def prepare_dropdown_objs(
        self, data: List[CustomFieldDropdownOptionInputData], field: ProjectCustomField, time: datetime
    ) -> List:
        if not data:
            return []
        logger.info("Preparing new created dropdown option objects")
        dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []
        option_names = set()
        option_uuids = set()
        for option_data in data:
            if option_data.uuid in option_uuids:
                logger.info("Duplicate dropdown option uuid found", uuid=option_data.uuid)
                raise self.DuplicateDropdownOptionUUIDException(
                    f"Duplicate dropdown option uuid '{option_data.uuid}' found."
                )
            option_uuids.add(option_data.uuid)

            if option_data.name.lower() in option_names:
                logger.info("Duplicate dropdown option name found", name=option_data.name)
                raise self.DuplicateDropdownOptionNameException(
                    f"Duplicate dropdown option name '{option_data.name}' found."
                )
            option_names.add(option_data.name.lower())

            option = ProjectCustomFieldDropDownOption()
            option.uuid = option_data.uuid
            option.name = option_data.name
            option.field = field
            option.created_by_id = self.user_id
            if option_data.is_archived is True:
                option.archived_by_id = self.user_id
                option.archived_at = time
                # TODO: Store archive history for dropdown option

            dropdown_option_objs.append(option)
        logger.info("New created dropdown option objects prepared", dropdown_option_objs=dropdown_option_objs)
        return dropdown_option_objs

    def prepare_field_and_dropdown_objs(
        self,
        data: List[ProjectCustomFieldInputData],
        section: ProjectCustomSection,
        incoming_data_field_uuids: List[UUID],
        incoming_data_field_names: List[str],
        time: datetime,
    ) -> Tuple[List[ProjectCustomField], List[ProjectCustomFieldDropDownOption]]:
        logger.info("Preparing new created field objects")
        fields_objs: List[ProjectCustomField] = []
        dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []
        for field_data in data:
            custom_field = ProjectCustomField()
            custom_field.organization_id = self.org_id
            custom_field.section = section
            custom_field.uuid = field_data.uuid
            custom_field.name = field_data.name
            custom_field.position = field_data.position
            custom_field.type = field_data.type
            custom_field.is_required = field_data.is_required
            custom_field.created_by_id = self.user_id
            custom_field.is_added_in_existing_projects = field_data.is_added_in_existing_projects
            if field_data.is_archived is True:
                custom_field.archived_by_id = self.user_id
                custom_field.archived_at = time
                # TODO: Store archive history for field

            fields_objs.append(custom_field)

            if field_data.type == CustomFieldTypeEnum.DROPDOWN.value and field_data.uuid not in SYSTEM_FIELD_UUIDS:
                created_dropdown_option_objs = self.prepare_dropdown_objs(
                    data=field_data.options, field=custom_field, time=time
                )
                dropdown_option_objs.extend(created_dropdown_option_objs)

        logger.info("New created field objects prepared", fields_objs=fields_objs)
        return fields_objs, dropdown_option_objs

    def section_create_bulk(self, objs: List[ProjectCustomSection]):
        try:
            ProjectCustomSection.objects.bulk_create(objs=objs)
        except IntegrityError as e:
            logger.info("Error while creating new section", error=e)
            raise self.CustomSectionCreateException(e.message)

    def field_create_bulk(self, objs: List[ProjectCustomField]) -> List[ProjectCustomField]:
        try:
            return ProjectCustomField.objects.bulk_create(objs=objs)
        except IntegrityError as e:
            logger.info("Error while creating new field", error=e)
            raise self.CustomFieldCreateException(e.message)

    def dropdown_option_create_bulk(self, objs: List[ProjectCustomFieldDropDownOption]):
        try:
            ProjectCustomFieldDropDownOption.objects.bulk_create(objs=objs)
        except IntegrityError as e:
            logger.info("Error while creating new dropdown options", error=e)
            raise self.CustomDropdownOptionCreateException(e)

    def check_duplicate_field(
        self,
        data: List[ProjectCustomFieldInputData],
        field_names: List[str],
        field_uuids: List[UUID],
        new_field_names: List[str],
        new_field_uuids: List[UUID],
    ):
        logger.info("Validating duplicate field names and uuids.")
        for field in data:
            field_name = field.name.lower()
            if field_name in field_names or field_name in new_field_names:
                raise self.DuplicateFieldNameException(f"Duplicate field name '{field.name}' found.")
            if field.uuid in field_uuids or field.uuid in new_field_uuids:
                raise self.DuplicateFieldUUIDException(f"Duplicate field uuid '{field.uuid}' found.")
            new_field_names.append(field_name)
            new_field_uuids.append(field.uuid)

    def check_duplicate_section_and_field(
        self,
        data: List[ProjectCustomSectionInputData],
        section_names: List[str],
        section_uuids: List[UUID],
        field_names: List[str],
        field_uuids: List[UUID],
    ):
        new_section_names: List[str] = []
        new_section_uuids: List[UUID] = []
        new_field_names: List[str] = []
        new_field_uuids: List[UUID] = []
        logger.info("Validating section data for duplicate section and field.")
        for section_data in data:
            section_name = section_data.name.lower()
            if section_name in section_names or section_name in new_section_names:
                raise self.DuplicateSectionNameException(f"Duplicate section name {section_data.name} found.")
            if section_data.uuid in section_uuids or section_data.uuid in new_section_uuids:
                raise self.DuplicateSectionUUIDException(f"Duplicate section uuid {section_data.uuid} found.")
            new_section_names.append(section_name)
            new_section_uuids.append(section_data.uuid)

            self.check_duplicate_field(
                data=section_data.fields,
                field_names=field_names,
                field_uuids=field_uuids,
                new_field_names=new_field_names,
                new_field_uuids=new_field_uuids,
            )

    def create_section(
        self,
        data: List[ProjectCustomSectionInputData],
        section_names: List[str],
        section_uuids: List[UUID],
        field_names: List[str],
        field_uuids: List[UUID],
        incoming_data_field_uuids: List[UUID],
        incoming_data_field_names: List[str],
        time: datetime,
    ):
        logger.info("New section creation started.")
        section_objs = []
        field_objs = []
        dropdown_option_objs = []
        try:
            self.check_duplicate_section_and_field(
                data=data,
                section_names=section_names,
                section_uuids=section_uuids,
                field_names=field_names,
                field_uuids=field_uuids,
            )
        except self.DuplicateSectionException as e:
            logger.info("Duplicate section found", error=e)
            raise e

        for section_data in data:
            custom_section = ProjectCustomSection()
            custom_section.organization_id = self.org_id
            custom_section.uuid = section_data.uuid
            custom_section.name = section_data.name
            custom_section.position = section_data.position
            custom_section.type = section_data.type
            custom_section.created_by_id = self.user_id
            section_objs.append(custom_section)

            created_field_objs, created_dropdown_option_objs = self.prepare_field_and_dropdown_objs(
                data=section_data.fields,
                section=custom_section,
                incoming_data_field_uuids=incoming_data_field_uuids,
                incoming_data_field_names=incoming_data_field_names,
                time=time,
            )
            field_objs.extend(created_field_objs)
            dropdown_option_objs.extend(created_dropdown_option_objs)

        logger.info(
            "Bulk creating new section, field and dropdown option objects.",
            section_objs=section_objs,
            field_objs=field_objs,
            dropdown_option_objs=dropdown_option_objs,
        )
        self.section_create_bulk(objs=section_objs)
        created_fields = self.field_create_bulk(objs=field_objs)
        field_ids_added_in_existing_projects = [
            field.pk for field in created_fields if field.is_added_in_existing_projects
        ]
        if field_ids_added_in_existing_projects:
            self.dispatch_fields_creation_in_all_projects(field_ids_added_in_existing_projects)
        self.dropdown_option_create_bulk(objs=dropdown_option_objs)
        logger.info("New section creation finished.")

    def dispatch_fields_creation_in_all_projects(self, field_ids: list[int]):
        transaction.on_commit(
            partial(
                CustomProjectFieldCreationDispatcher().dispatch,
                data=CustomProjectFieldCreationDispatcherData(
                    org_id=self.org_id,
                    field_ids=field_ids,
                    user_id=self.user_id,
                ),
            )
        )

    def prepare_updated_dropdown_objs(
        self,
        data: List[CustomFieldDropdownOptionInputData],
        field: ProjectCustomField,
        time: datetime,
    ):
        logger.info("Preparing updated dropdown option obejcts.")
        dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []
        new_dropdown_options: List[ProjectCustomFieldDropDownOption] = []

        incoming_dropdown_option_uuids = set()
        incoming_dropdown_option_names = set()

        for option_data in data:
            if option_data.uuid not in incoming_dropdown_option_uuids:
                incoming_dropdown_option_uuids.add(option_data.uuid)
            else:
                logger.info("Duplicate dropdown option uuid found", uuid=option_data.uuid)
                raise self.DuplicateDropdownOptionUUIDException(
                    f"Duplicate dropdown option uuid '{option_data.uuid}' found"
                )
            if option_data.name.lower() not in incoming_dropdown_option_names:
                incoming_dropdown_option_names.add(option_data.name.lower())
            else:
                logger.info("Duplicate dropdown option name found", name=option_data.name)
                raise self.DuplicateDropdownOptionNameException(
                    f"Duplicate dropdown option name '{option_data.name}' found."
                )
            option = self.mappings.dropdown_option_uuid_object_mapping.get(option_data.uuid)
            if not option:
                new_dropdown_options.append(option_data)
                continue

            if is_dataclass(option_data):
                option_data_dict = asdict(option_data)
            if option_data.is_archived is True and option.archived_at is None:
                option_data_dict["archived_by_id"] = self.user_id
                option_data_dict["archived_at"] = time
                # TODO: Store archive history for dropdown option

            elif option_data.is_archived is False and option.archived_at is not None:
                option_data_dict["archived_by_id"] = None
                option_data_dict["archived_at"] = None
                # TODO: Store archive history for dropdown option

            instance, is_updated, _ = model_update(
                instance=option,
                fields=["archived_by_id", "archived_at"],
                data=option_data_dict,
                updated_by_id=self.user_id,
                save=False,
            )
            if is_updated:
                dropdown_option_objs.append(instance)

        deleted_dropdown_option_uuids = set(
            self.mappings.field_uuid_dropdown_option_uuids_mapping.get(field.uuid)
        ) - set(incoming_dropdown_option_uuids)
        if len(deleted_dropdown_option_uuids):
            logger.info(
                "Deleting dropdown options for field.",
                field_uuid=field.uuid,
                deleted_dropdown_option_uuids=deleted_dropdown_option_uuids,
            )
            self.delete_dropdown_options(option_uuids=deleted_dropdown_option_uuids)

        new_dropdown_option_objs = self.prepare_dropdown_objs(data=new_dropdown_options, field=field, time=time)
        logger.info(dropdown_option_objs=dropdown_option_objs, new_dropdown_option_objs=new_dropdown_option_objs)
        return dropdown_option_objs, new_dropdown_option_objs

    def prepare_updated_field_and_dropdown_objs(
        self,
        data: List[ProjectCustomFieldInputData],
        section: ProjectCustomSection,
        incoming_data_field_uuids: List[UUID],
        incoming_data_field_names: List[str],
        time: datetime,
    ):
        logger.info("Preparing updated section and field objects.")
        fields_objs: List[ProjectCustomField] = []
        dropdown_option_objs = []
        new_fields: List[ProjectCustomFieldInputData] = []
        new_dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []

        for field_data in data:
            incoming_data_field_uuids.append(field_data.uuid)
            incoming_data_field_names.append(field_data.name.lower())
            field = self.mappings.field_uuid_object_mapping.get(field_data.uuid)
            if not field:
                new_fields.append(field_data)
                continue

            if is_dataclass(field_data):
                field_data_dict = asdict(field_data)
            if field_data.is_archived is True and field.archived_at is None:
                field_data_dict["archived_by_id"] = self.user_id
                field_data_dict["archived_at"] = time

                # TODO: Store archive history for field
            elif field_data.is_archived is False and field.archived_at is not None:
                field_data_dict["archived_by_id"] = None
                field_data_dict["archived_at"] = None
                # TODO: Store archive history for field

            instance, is_updated, updated_fields = model_update(
                instance=field,
                fields=["position", "is_required", "archived_by_id", "archived_at"],
                data=field_data_dict,
                updated_by_id=self.user_id,
                save=False,
            )
            if is_updated:
                if str(field_data.uuid) in SYSTEM_FIELD_UUIDS:
                    raise self.SystemFieldUpdateException("System field can not be updated")
                fields_objs.append(instance)

            if field_data.type == CustomFieldTypeEnum.DROPDOWN.value and str(field_data.uuid) not in SYSTEM_FIELD_UUIDS:
                updated_dropdown_option_objs, new_dropdown_objs = self.prepare_updated_dropdown_objs(
                    data=field_data.options,
                    field=field,
                    time=time,
                )
                dropdown_option_objs.extend(updated_dropdown_option_objs)
                new_dropdown_option_objs.extend(new_dropdown_objs)

        new_field_objs, new_field_dropdown_objs = self.prepare_field_and_dropdown_objs(
            data=new_fields,
            section=section,
            incoming_data_field_uuids=incoming_data_field_uuids,
            incoming_data_field_names=incoming_data_field_names,
            time=time,
        )
        new_dropdown_option_objs.extend(new_field_dropdown_objs)
        logger.info(
            fields_objs=fields_objs,
            dropdown_option_objs=dropdown_option_objs,
            new_field_objs=new_field_objs,
            new_dropdown_option_objs=new_dropdown_option_objs,
        )
        return fields_objs, dropdown_option_objs, new_field_objs, new_dropdown_option_objs

    def delete_dropdown_options(self, option_uuids: List[UUID]):
        logger.info("Dropdown option uuids to be deleted", option_uuids=option_uuids)
        if dropdown_option_has_field_check(option_uuids=option_uuids, org_id=self.org_id):
            raise self.DeletingDataDropdownOptionException("Dropdown option has a data field, can not be deleted.")
        ProjectCustomFieldDropDownOption.objects.available().select_related("field", "field__section").filter(
            field__organization_id=self.org_id, uuid__in=option_uuids
        ).soft_delete(user_id=self.user_id)
        ProjectCustomDropDownFieldData.objects.filter(data__uuid__in=option_uuids, data__isnull=True).delete()

    def delete_field_data(self, deleted_field_uuids: List[UUID]):
        logger.info("deleting field data")
        ProjectCustomTextFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

        ProjectCustomRichTextFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

        ProjectCustomNumberFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

        ProjectCustomDateFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

        ProjectCustomPhoneNumberFieldData.objects.filter(
            field__uuid__in=deleted_field_uuids, data__isnull=True
        ).delete()

        ProjectCustomFileFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

        ProjectCustomMultipleFilesField.objects.filter(field__uuid__in=deleted_field_uuids).delete()

        ProjectCustomDropDownFieldData.objects.filter(field__uuid__in=deleted_field_uuids, data__isnull=True).delete()

    def delete_fields_while_updating_section(self, deleted_field_uuids: List[UUID]):
        logger.info("Field uuids to be deleted", deleted_field_uuids=deleted_field_uuids)
        if fields_have_data_check(field_uuids=deleted_field_uuids, org_id=self.org_id):
            raise self.DeletingDataFieldException("Field has data, can not be deleted.")
        ProjectCustomField.objects.available().select_related("section").filter(
            section__organization_id=self.org_id, uuid__in=deleted_field_uuids
        ).soft_delete(user_id=self.user_id)
        ProjectCustomFieldDropDownOption.objects.available().select_related("field", "field__section").filter(
            field__section__organization_id=self.org_id, field__uuid__in=deleted_field_uuids
        ).soft_delete(user_id=self.user_id)
        if deleted_field_uuids:
            self.delete_field_data(deleted_field_uuids=deleted_field_uuids)

    def update_section(
        self,
        data: List[ProjectCustomSectionInputData],
        section_names: List[str],
        section_uuids: List[UUID],
        field_names: List[str],
        field_uuids: List[UUID],
        incoming_data_field_uuids: List[UUID],
        incoming_data_field_names: List[str],
        time: datetime,
    ) -> Tuple[List[UUID], List[str]]:
        logger.info("Update section started.")
        project_custom_sections = project_custom_section_fetch_all(org_id=self.org_id)

        self.set_mappings(sections=project_custom_sections)

        section_objs: List[ProjectCustomSection] = []
        field_objs: List[ProjectCustomField] = []
        dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []
        new_field_objs: List[ProjectCustomField] = []
        new_dropdown_option_objs: List[ProjectCustomFieldDropDownOption] = []

        for section_data in data:
            section: ProjectCustomSection = self.mappings.section_uuid_object_mapping.get(section_data.uuid)
            instance, is_updated, _ = model_update(
                instance=section, fields=["position", "type"], data=section_data, updated_by_id=self.user_id, save=False
            )
            if is_updated:
                section_objs.append(instance)

            (
                updated_field_objs,
                updated_dropdown_option_objs,
                new_field_objects,
                new_dropdown_objects,
            ) = self.prepare_updated_field_and_dropdown_objs(
                data=section_data.fields,
                section=section,
                incoming_data_field_uuids=incoming_data_field_uuids,
                incoming_data_field_names=incoming_data_field_names,
                time=time,
            )

            field_objs.extend(updated_field_objs)
            dropdown_option_objs.extend(updated_dropdown_option_objs)
            new_field_objs.extend(new_field_objects)
            new_dropdown_option_objs.extend(new_dropdown_objects)

        # delete fields
        deleted_field_uuids: List[UUID] = list(set(field_uuids) - set(incoming_data_field_uuids))
        deleted_field_names: List[str] = [field.name for field in new_field_objs]
        if len(deleted_field_uuids):
            logger.info("Checking fields data and deleting fields in updated section.")
            self.delete_fields_while_updating_section(deleted_field_uuids=deleted_field_uuids)
            field_uuids = list(set(field_uuids) - set(deleted_field_uuids))
            field_names = list(set(field_names) - set(deleted_field_names))

        # update fields
        try:
            ProjectCustomSection.objects.bulk_update(
                objs=section_objs,
                fields=[
                    "position",
                    "type",
                    "updated_by_id",
                    "updated_at",
                ],
            )
        except IntegrityError as e:
            logger.info("Error while updating project custom section", error=e)
            raise self.CustomSectionUpdateException(e.message)
        try:
            ProjectCustomField.objects.bulk_update(
                objs=field_objs,
                fields=["position", "is_required", "archived_by_id", "archived_at", "updated_by_id", "updated_at"],
            )
        except IntegrityError as e:
            logger.info("Error while updating project custom field", error=e)
            raise self.CustomFieldUpdateException(e.message)
        try:
            ProjectCustomFieldDropDownOption.objects.bulk_update(
                objs=dropdown_option_objs, fields=["archived_by_id", "archived_at", "updated_by_id", "updated_at"]
            )
        except IntegrityError as e:
            logger.info("Error while updating project custom dropdown option", error=e)
            raise self.CustomDropdownOptionUpdateException(e.message)

        # create fields
        try:
            logger.info("Checking duplicate field names and uuids iun updated section data.")
            self.check_duplicate_field(
                data=new_field_objs,
                field_names=field_names,
                field_uuids=field_uuids,
                new_field_names=[],
                new_field_uuids=[],
            )
        except self.DuplicateSectionException as e:
            logger.info("Duplicate field found", error=e)
            raise e
        created_fields = self.field_create_bulk(objs=new_field_objs)
        field_ids_added_in_existing_projects = [
            field.pk for field in created_fields if field.is_added_in_existing_projects
        ]
        if field_ids_added_in_existing_projects:
            self.dispatch_fields_creation_in_all_projects(field_ids_added_in_existing_projects)
        self.dropdown_option_create_bulk(objs=new_dropdown_option_objs)

        return field_uuids, field_names

    def set_mappings(self, sections: QuerySet) -> None:
        logger.info("Creating section uuid and object mapping")
        section_uuid_object_mapping: Dict[UUID, ProjectCustomSection] = {}
        field_uuid_object_mapping: Dict[UUID, ProjectCustomField] = {}
        dropdown_option_uuid_object_mapping: Dict[UUID, ProjectCustomFieldDropDownOption] = {}
        field_uuid_dropdown_option_uuids_mapping: Dict[UUID, List[UUID]] = {}
        field_uuid_dropdown_option_names_mapping: Dict[UUID, List[str]] = {}

        for section in sections:
            section_uuid_object_mapping[section.uuid] = section
            fields = section.fields.all()
            for field in fields:
                field_uuid_object_mapping[field.uuid] = field
                if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                    self.make_dropdown_option_mappings(
                        options=field.options.all(),
                        field=field,
                        dropdown_option_uuid_object_mapping=dropdown_option_uuid_object_mapping,
                        field_uuid_dropdown_option_uuids_mapping=field_uuid_dropdown_option_uuids_mapping,
                        field_uuid_dropdown_option_names_mapping=field_uuid_dropdown_option_names_mapping,
                    )

        logger.info(section_uuid_object_mapping=section_uuid_object_mapping)

        self.mappings.section_uuid_object_mapping = section_uuid_object_mapping
        self.mappings.field_uuid_object_mapping = field_uuid_object_mapping
        self.mappings.dropdown_option_uuid_object_mapping = dropdown_option_uuid_object_mapping
        self.mappings.field_uuid_dropdown_option_uuids_mapping = field_uuid_dropdown_option_uuids_mapping
        self.mappings.field_uuid_dropdown_option_names_mapping = field_uuid_dropdown_option_names_mapping

    def make_dropdown_option_mappings(
        self,
        options: List[ProjectCustomFieldDropDownOption],
        field: ProjectCustomField,
        dropdown_option_uuid_object_mapping: Dict[UUID, ProjectCustomFieldDropDownOption],
        field_uuid_dropdown_option_uuids_mapping: Dict[UUID, List[UUID]],
        field_uuid_dropdown_option_names_mapping: Dict[UUID, List[str]],
    ):
        for option in options:
            dropdown_option_uuid_object_mapping[option.uuid] = option
            if field_uuid_dropdown_option_uuids_mapping.get(field.uuid):
                field_uuid_dropdown_option_uuids_mapping[field.uuid].append(option.uuid)
                field_uuid_dropdown_option_names_mapping[field.uuid].append(option.name)
            else:
                field_uuid_dropdown_option_uuids_mapping[field.uuid] = [option.uuid]
                field_uuid_dropdown_option_names_mapping[field.uuid] = [option.name]

    def filter_section_uuids_to_be_deleted(self, section_uuids: List[UUID]) -> List[UUID]:
        logger.info("Section uuids before filter", section_uuids=section_uuids)
        fields = custom_field_with_distinct_section_fetch(section_uuids=section_uuids, org_id=self.org_id)

        for field in fields:
            section_uuids.remove(field.section_id)
        logger.info("Section uuids after filter", section_uuids=section_uuids)
        return section_uuids

    def delete_section(self, section_uuids: List[UUID]):
        logger.info("Project custom field config, delete section method started.")
        section_to_be_deleted_uuids: List[UUID] = self.filter_section_uuids_to_be_deleted(section_uuids=section_uuids)
        logger.info(section_to_be_deleted_uuids=section_to_be_deleted_uuids)

        if section_to_be_deleted_uuids:
            ProjectCustomSection.objects.available().filter(
                organization_id=self.org_id, uuid__in=section_to_be_deleted_uuids
            ).soft_delete(user_id=self.user_id)
            ProjectCustomField.objects.available().select_related("section").filter(
                organization_id=self.org_id, section__uuid__in=section_to_be_deleted_uuids
            ).soft_delete(user_id=self.user_id)
            ProjectCustomFieldDropDownOption.objects.available().select_related("field", "field__section").filter(
                field__organization_id=self.org_id, field__section__uuid__in=section_to_be_deleted_uuids
            ).soft_delete(user_id=self.user_id)

        logger.info("Project custom field config delete finished.")

    def update(self, sections: List[ProjectCustomSectionInputData]):
        logger.info("Project custom field config update started.")
        time = timezone.now()
        section_uuid_name_list: List[Tuple[UUID, str]] = project_custom_section_uuids_name_fetch_all(org_id=self.org_id)
        field_uuid_name_list: List[Tuple[UUID, str]] = project_custom_field_uuids_name_fetch_all(org_id=self.org_id)

        section_uuids: List[UUID] = [attr[0] for attr in section_uuid_name_list]
        section_names: List[str] = [attr[1].lower() for attr in section_uuid_name_list]

        field_uuids: List[UUID] = [attr[0] for attr in field_uuid_name_list]
        field_names: List[str] = [attr[1].lower() for attr in field_uuid_name_list]

        new_sections: List[ProjectCustomSectionInputData] = []
        updated_sections: List[ProjectCustomSectionInputData] = []
        updated_section_uuids: List[str] = []
        incoming_data_field_uuids: List[UUID] = []
        incoming_data_field_names: List[str] = []

        for section in sections:
            if section.uuid in section_uuids:
                updated_sections.append(section)
                updated_section_uuids.append(section.uuid)
            else:
                new_sections.append(section)

        deleted_section_uuids: List[UUID] = list(set(section_uuids) - set(updated_section_uuids))
        logger.info(
            "Updated and deleted sections",
            updated_sections_uuids=updated_section_uuids,
            deleted_section_uuids=deleted_section_uuids,
        )
        if len(deleted_section_uuids):
            try:
                self.delete_section(section_uuids=deleted_section_uuids)
            except self.ProjectCustomSectionDeleteException as e:
                logger.info("Error while deleting section", error=e)
                raise e

        try:
            field_uuids, field_names = self.update_section(
                data=updated_sections,
                section_names=section_names,
                section_uuids=section_uuids,
                field_names=field_names,
                field_uuids=field_uuids,
                incoming_data_field_uuids=incoming_data_field_uuids,
                incoming_data_field_names=incoming_data_field_names,
                time=time,
            )
        except self.ProjectCustomSectionUpdateException as e:
            logger.info("Error while updating section", error=e)
            raise e

        if len(new_sections):
            try:
                self.create_section(
                    data=new_sections,
                    section_names=section_names,
                    section_uuids=section_uuids,
                    field_names=field_names,
                    field_uuids=field_uuids,
                    incoming_data_field_uuids=incoming_data_field_uuids,
                    incoming_data_field_names=incoming_data_field_names,
                    time=time,
                )
            except self.ProjectCustomSectionCreateException as e:
                logger.info("Error while creating new section", error=e)
                raise e
        ProjectListConfigCache.delete(instance_id=self.org_id)
        logger.info("Project list config cache deleted.")
        logger.info("Project custom field config update finished.")

    def check_field_deletion(self, field_uuids: List[UUID]) -> bool:
        logger.info("Field deletion check started.", field_uuids=field_uuids)
        return not fields_have_data_check(field_uuids=field_uuids, org_id=self.org_id)

    def check_dropdown_option_deletion(self, dropdown_uuids: List[UUID]) -> bool:
        logger.info("Dropdown option deletion check started.", dropdown_uuids=dropdown_uuids)
        return not dropdown_option_has_field_check(option_uuids=dropdown_uuids, org_id=self.org_id)

    def fetch_dropdown_options(self, field_id: int) -> QuerySet[ProjectCustomFieldDropDownOption]:
        """Checks if given field is dropdown type and returns dropdown options for it."""
        field = custom_field_fetch_by_id(field_id=field_id, org_id=self.org_id)
        if field.type != CustomFieldTypeEnum.DROPDOWN.value:
            raise self.InvalidFieldException("Field is not dropdown type.")
        return field_dropdown_options_fetch(field_id=field_id, org_id=self.org_id)

    # For External API
    def prepare_dropdown_field_data(self, fields_data: list[dict]):
        uuids = []
        uuid_to_options_mapping = {}
        dropdown_objs = []

        for field_data in fields_data:
            uuids.append(field_data.get("uuid"))
            uuid_to_options_mapping[field_data.get("uuid")] = field_data.get("options")

        fields = custom_field_fetch_by_uuids(field_uuids=uuids, org_id=self.org_id)
        for field in fields:
            dropdown_data = []
            for option in uuid_to_options_mapping.get(str(field.uuid)):
                dropdown_data.append(CustomFieldDropdownOptionInputData(name=option, uuid=uuid4(), is_archived=False))
            dropdown_objs.extend(
                self.prepare_dropdown_objs(
                    data=dropdown_data,
                    field=field,
                    time=timezone.now(),
                )
            )
        return dropdown_objs

    def create_dropdown_options(self, fields_data: list[dict]):
        dropdown_objs = self.prepare_dropdown_field_data(fields_data=fields_data)
        self.dropdown_option_create_bulk(objs=dropdown_objs)
        logger.info("Dropdown options created successfully.")


class ProjectCreateService:
    def __init__(self, org_id: int, user: User):
        self.org_id = org_id
        self.user = user

    def _prefill_field_values(self, config_data: List[dict]) -> List[dict]:
        org_config: OrganizationCountryConfigCacheData = OrganizationCountryConfigCache.get(instance_id=self.org_id)
        for section in config_data:
            for field in section.get("fields"):
                field["is_readonly"] = False
                if field.get("uuid") == COUNTRY_UUID:
                    field["value"] = {
                        "id": HashIdConverter.encode(org_config.country.id),
                        "name": org_config.country.name,
                    }
                elif field.get("uuid") == PROJECT_CURRENCY_UUID:
                    field["value"] = {
                        "id": HashIdConverter.encode(org_config.currency.id),
                        "name": f"{org_config.currency.name} ({org_config.currency.code})",
                    }
                elif field.get("uuid") == PROJECT_TIMEZONE_UUID:
                    field["value"] = {
                        "id": HashIdConverter.encode(org_config.timezone.id),
                        "name": org_config.timezone.name,
                    }
                    field["is_readonly"] = True
                elif field.get("uuid") == PROJECT_TAX_TYPE_UUID:
                    field["value"] = {
                        "id": HashIdConverter.encode(org_config.tax_type.id),
                        "name": org_config.tax_type.name,
                    }
                else:
                    field["value"] = None
        return config_data

    def prefetch_project_config_and_roles(self):
        config = project_custom_field_unarchived_config_fetch(org_id=self.org_id)
        if not config:
            config_data = get_project_custom_field_default_config()
        else:
            config_data = ConfigPrefetchCustomSectionSerializer(config, many=True).data
        config_data = self._prefill_field_values(config_data)
        role_list_data = self.get_prefill_project_user_role_list()
        return {"section_list": config_data, "role_list": role_list_data}

    def get_prefill_project_user_role_list(self) -> list[dict]:
        roles = OrganizationConfigRoleService.project_prefilled_role_list(org_id=self.org_id)
        role_ids = [role.pk for role in roles]
        permissions = OrgPermissionHelper.get_permissions(self.user)
        if Permissions.CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE in permissions:
            role_user_list = []
            if self.user.default_project_role_id not in role_ids:
                role_user_list.append(ProjectUserRoleData(user=self.user, role=self.user.default_project_role))
            role_user_list += [ProjectUserRoleData(user=self.user, role=role) for role in roles]
            return ProjecUserRoleSerializer(role_user_list, many=True).data
        elif Permissions.CAN_ASSIGN_PROJECT_USER in permissions:
            default_project_role = self.user.default_project_role
            role_user_list = [ProjectUserRoleData(user=self.user, role=default_project_role)]
            if self.user.default_project_role_id in role_ids:
                roles.remove(self.user.default_project_role)
            for role in roles:
                role_user_list.append(ProjectUserRoleData(user=None, role=role))
            return ProjecUserRoleSerializer(role_user_list, many=True).data
        else:
            raise ProjectCreationPrefillDataException("User does not have permission to assign user on project.")

    def create_project_attachments(self, attachments: List[dict], project_id: int):
        logger.info("Creating project attachments.")
        attachment_objs = []
        for attachment in attachments:
            attachment_obj = ProjectAttachment()
            attachment_obj.project_id = project_id
            attachment_obj.file = get_relative_path(attachment.get("url"))
            attachment_obj.name = attachment.get("name")
            attachment_obj.uploaded_by_id = self.user.pk
            attachment_obj.full_clean()
            attachment_objs.append(attachment_obj)

        ProjectAttachment.objects.bulk_create(objs=attachment_objs)
        logger.info("Project attachments created successfully.")

    def store_custom_field_data(self, custom_field_dict: dict, project_id: int):
        logger.info("Storing custom field data.", custom_field_dict=custom_field_dict)
        dropdown_data_objs: List[ProjectCustomDropDownFieldData] = []
        number_data_objs: List[ProjectCustomNumberFieldData] = []
        text_data_objs: List[ProjectCustomTextFieldData] = []
        rich_text_data_objs: List[ProjectCustomRichTextFieldData] = []
        phone_number_data_objs: List[ProjectCustomPhoneNumberFieldData] = []
        date_data_objs: List[ProjectCustomDateFieldData] = []
        file_data_objs: List[ProjectCustomFileFieldData] = []
        multiple_files_fields_objs: List[ProjectCustomMultipleFilesField] = []
        multiple_file_data_objs: List[ProjectCustomMultipleFilesFieldData] = []
        field_counter_update_uuids: List[UUID] = []
        dropdown_option_counter_update_ids: List[int] = []

        field_uuids = list(custom_field_dict.keys())

        fields = get_custom_fields(org_id=self.org_id, field_uuids=field_uuids)
        uuid_to_field_mapping: dict[UUID, ProjectCustomField] = {field.uuid: field for field in fields}

        for key, value in custom_field_dict.items():
            field = uuid_to_field_mapping.get(UUID(key))

            if not field:
                logger.info("Custom field not found with the given UUID.", field_uuid=key)
                raise CustomFieldNotFoundException("No Custom Field found with the given UUID.")

            if not is_empty(value):
                field_counter_update_uuids.append(key)
            if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                dropdown_option = (
                    dropdown_option_fetch_by_id(org_id=self.org_id, field_id=field.pk, dropdown_option_id=value)
                    if value
                    else None
                )
                if dropdown_option:
                    dropdown_option_counter_update_ids.append(dropdown_option.pk)
                dropdown_data_objs.append(
                    ProjectCustomDropDownFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data_id=dropdown_option.pk if dropdown_option else None,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.DECIMAL.value:
                number_data_objs.append(
                    ProjectCustomNumberFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.TEXT.value:
                text_data_objs.append(
                    ProjectCustomTextFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                rich_text_data_objs.append(
                    ProjectCustomRichTextFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                phone_number_data_objs.append(
                    ProjectCustomPhoneNumberFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.DATE.value:
                date_data_objs.append(
                    ProjectCustomDateFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.FILE.value:
                file_data_objs.append(
                    ProjectCustomFileFieldData(
                        field_id=field.pk,
                        project_id=project_id,
                        data=value.get("url") if value else None,
                        name=value.get("name") if value else None,
                        created_by_id=self.user.pk,
                    )
                )
            elif field.type == CustomFieldTypeEnum.MULTIPLE_FILES.value:
                multiple_files_fields_objs.append(
                    ProjectCustomMultipleFilesField(
                        field_id=field.pk,
                        project_id=project_id,
                        created_by_id=self.user.pk,
                    )
                )
                for attachment in value:
                    multiple_file_data_objs.append(
                        ProjectCustomMultipleFilesFieldData(
                            field=multiple_files_fields_objs[-1],
                            data=attachment.get("url") if attachment else None,
                            name=attachment.get("name") if attachment else None,
                            created_by_id=self.user.pk,
                        )
                    )

        ProjectCustomDropDownFieldData.objects.bulk_create(objs=dropdown_data_objs)
        ProjectCustomNumberFieldData.objects.bulk_create(objs=number_data_objs)
        ProjectCustomTextFieldData.objects.bulk_create(objs=text_data_objs)
        ProjectCustomRichTextFieldData.objects.bulk_create(objs=rich_text_data_objs)
        ProjectCustomPhoneNumberFieldData.objects.bulk_create(objs=phone_number_data_objs)
        ProjectCustomDateFieldData.objects.bulk_create(objs=date_data_objs)
        ProjectCustomFileFieldData.objects.bulk_create(objs=file_data_objs)

        ProjectCustomMultipleFilesField.objects.bulk_create(objs=multiple_files_fields_objs)
        ProjectCustomMultipleFilesFieldData.objects.bulk_create(objs=multiple_file_data_objs)
        logger.info("Custom field data objects created successfully.")

        ProjectCustomField.objects.available().filter(
            organization_id=self.org_id, uuid__in=field_counter_update_uuids
        ).update(data_counter=F("data_counter") + 1)
        logger.info("Custom field data counter updated.")
        ProjectCustomFieldDropDownOption.objects.select_related("field").available().filter(
            field__organization_id=self.org_id, id__in=dropdown_option_counter_update_ids
        ).update(counter=F("counter") + 1)
        logger.info("Custom dropdown data counter updated.")
        logger.info("Custom field data stored successfully.")

    def create_project(self, data: dict):
        from project.share.domain.service.services import ProjectClientPocAssignmentService

        logger.info("Project creation service started.")
        system_fields_data = data.get("system_fields")
        custom_fields_data = data.get("custom_fields")
        project = project_create(
            user=self.user,
            name=system_fields_data.get("name"),
            client_id=system_fields_data.get("client_id"),
            estimated_cost=system_fields_data.get("estimated_cost"),
            store_type_id=None,
            business_category_id=system_fields_data.get("business_category_id"),
            recce_due_at=system_fields_data.get("recce_due_at"),
            expected_started_at=system_fields_data.get("expected_started_at"),
            kam_completion_due_at=system_fields_data.get("kam_completion_due_at"),
            organization_id=self.org_id,
        )
        logger.info("Project created", project_id=project.pk)
        self.store_custom_field_data(custom_field_dict=custom_fields_data, project_id=project.pk)

        many_to_many_field_set(obj=project, key="scopes", value=system_fields_data.get("project_scope_ids"))

        logger.info("calling store create service.")
        store_create(
            project_id=project.pk,
            address_line_1=system_fields_data.get("address_line_1"),
            address_line_2=system_fields_data.get("address_line_2"),
            city_id=system_fields_data.get("city_id"),
            state_id=system_fields_data.get("state_id"),
            country_id=system_fields_data.get("country_id"),
            zip_code=system_fields_data.get("zip_code"),
            dealer_name=system_fields_data.get("dealer_name"),
            dealer_phone_number=system_fields_data.get("dealer_phone_number"),
            area=system_fields_data.get("area"),
            user_id=self.user.pk,
        )
        if system_fields_data.get("client_id") != self.org_id:
            logger.info("Project client is not same as organization, adding pmc to project.")
            pmc_role = PmcRoleService.create_project_organization_role(
                name="PMC",
                created_by_id=self.user.pk,
                organization_id=self.org_id,
                type=OrganizationType.PMC,
            )
            project_organization_assign(
                organization_id=self.org_id,
                project_id=project.pk,
                assigned_by_organization_id=self.org_id,
                created_by_id=self.user.pk,
                role_id=pmc_role.pk,
            )
            project_org_item_type_config_assignment(
                assignee_org_id=self.org_id,
                project_id=project.pk,
                creator_id=self.user.pk,
                assigner_org_id=self.org_id,
            )

        logger.info("Adding clinet to project")
        client_role = get_client_role(
            organization_id=self.org_id,
            created_by_id=self.user.pk,
        )

        project_client_assign(
            organization_id=system_fields_data.get("client_id"),
            project_id=project.pk,
            assigned_by_organization_id=self.org_id,
            created_by_id=self.user.pk,
            role_id=client_role.pk,
        )
        ProjectClientPocAssignmentService(
            project=project,
            organization_id=system_fields_data.get("client_id"),
            created_by=self.user,
        ).assign_project_to_primary_poc()

        OrganizationConfig.objects.get_or_create(organization_id=system_fields_data.get("client_id"))
        # create project config
        ProjectConfig.objects.create(
            project_id=project.pk,
            timezone_id=system_fields_data.get("timezone_id"),
            currency_id=system_fields_data.get("currency_id"),
            tax_type_id=system_fields_data.get("tax_type_id"),
        )

        logger.info("Validating role mappings and assigning project users.")
        validated_role_mappings = get_project_role_mapping(data.get("role_mappings"))
        for role_id in validated_role_mappings:
            project_user_assign_many(
                role_id=role_id,
                user_ids=validated_role_mappings.get(role_id),
                project=project,
                user=self.user,
                organization_id=self.org_id,
            )

        creator_org_config = OrganizationConfig.objects.filter(organization_id=self.org_id).first()
        if creator_org_config and not creator_org_config.use_client_code:
            project.job_id = job_id_generate(client_id=self.org_id)
        else:
            project.job_id = job_id_generate(client_id=system_fields_data.get("client_id"))
        project.save(update_fields=["job_id"])

        logger.info("Attching project documents.")
        self.create_project_attachments(attachments=data.get("attachments"), project_id=project.pk)
        if data.get("lead_id"):
            logger.info("Adding project to lead")
            service: LeadToProjectAbstractService = LeadToProjectServiceFactory.get_service()
            service.update_lead_from_project(project_id=project.pk, lead_id=data.get("lead_id"), user_id=self.user.pk)

        logger.info("Project created successfully", project_id=project.pk)
        return project.pk


class ProjectSectionUpdateService:
    class ProjectSectionUpdateServiceException(BaseValidationError):
        pass

    class InvalidFieldDataException(ProjectSectionUpdateServiceException):
        pass

    def __init__(self, org_id: int, user: User, project_id: int):
        self.org_id = org_id
        self.user = user
        self.project_id = project_id
        self.text_objs = []
        self.richtext_objs = []
        self.decimal_objs = []
        self.date_objs = []
        self.phonenumber_objs = []
        self.dropdown_objs = []
        self.file_objs = []
        self.multiple_file_objs_for_creation = []
        self.multiple_file_objs_for_updation = []
        self.multiple_file_objs_for_deletion = []

    def prepare_updated_data_objects(self, data_objects: QuerySet, field: ProjectCustomField, data: dict):
        instance_list = []
        updated_data = {"data": data.get(str(field.uuid))}
        fields = ["data"]
        dropdown_added_ids = []
        field_added_ids = []
        dropdown_removed_ids = []
        field_removed_ids = []

        if field.type == CustomFieldTypeEnum.DROPDOWN.value:
            option_instance = (
                dropdown_option_fetch_by_id(
                    org_id=self.org_id, field_id=field.pk, dropdown_option_id=data.get(str(field.uuid))
                )
                if data.get(str(field.uuid))
                else None
            )
            updated_data = {"data": option_instance}
        if field.type == CustomFieldTypeEnum.FILE.value:
            file_data = data.get(str(field.uuid))
            updated_data["name"] = file_data.get("name") if file_data else None
            updated_data["data"] = file_data.get("url") if file_data else None
            fields = ["data", "name"]

        for data_object in data_objects:
            instance, is_updated, _ = model_update(
                instance=deepcopy(data_object),
                fields=fields,
                data=updated_data,
                updated_by_id=self.user.pk,
                clean=True,
                save=False,
            )
            if is_updated:
                instance_list.append(instance)
                if is_empty(data_object.data):
                    field_added_ids.append(field.pk)
                    if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                        dropdown_added_ids.append(instance.data.pk)
                elif is_empty(instance.data):
                    field_removed_ids.append(field.pk)
                    if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                        dropdown_removed_ids.append(data_object.data.pk)
                else:
                    if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                        dropdown_added_ids.append(instance.data.pk)
                        dropdown_removed_ids.append(data_object.data.pk)

        updated_counters = DataCounter(
            dropdown_added_ids=dropdown_added_ids,
            field_added_ids=field_added_ids,
            dropdown_removed_ids=dropdown_removed_ids,
            field_removed_ids=field_removed_ids,
        )

        return (instance_list, updated_counters)

    def prepare_updated_multiple_data_objects(
        self,
        existing_data_objects: QuerySet,
        field: ProjectCustomField,
        data: dict,
        multiple_files_field: ProjectCustomMultipleFilesField,
    ):
        file_ids_to_file_data_mapping = {}
        field_counter = len(existing_data_objects)

        field_added_ids = []
        field_removed_ids = []

        instance_for_updation = []
        instance_for_creation = []
        instance_ids_for_deletion = []

        for file_data in data.get(str(field.uuid)):
            if file_data.get("object_status") == ObjectStatus.ADD.value:
                field_counter += 1
                instance_for_creation.append(
                    ProjectCustomMultipleFilesFieldData(
                        field_id=multiple_files_field.pk,
                        data=file_data.get("url"),
                        name=file_data.get("name"),
                        created_by_id=self.user.pk,
                    )
                )
            elif file_data.get("object_status") == ObjectStatus.UPDATE.value:
                file_data["data"] = file_data.get("url")
                file_ids_to_file_data_mapping[file_data.get("id")] = file_data
            elif file_data.get("object_status") == ObjectStatus.DELETE.value:
                field_counter -= 1
                instance_ids_for_deletion.append(file_data.get("id"))

        for data_object in existing_data_objects:
            if data_object.pk in file_ids_to_file_data_mapping:
                instance, is_updated, _ = model_update(
                    instance=deepcopy(data_object),
                    fields=["data", "name"],
                    data=file_ids_to_file_data_mapping.get(data_object.pk),
                    updated_by_id=self.user.pk,
                    clean=True,
                    save=False,
                )
                instance_for_updation.append(instance)

        if len(existing_data_objects) == 0 and field_counter > 0:
            field_added_ids.append(field.pk)
        elif len(existing_data_objects) > 0 and field_counter == 0:
            field_removed_ids.append(field.pk)

        updated_counters = DataCounter(
            dropdown_added_ids=[],
            field_added_ids=field_added_ids,
            dropdown_removed_ids=[],
            field_removed_ids=field_removed_ids,
        )

        if field.is_required and field_counter == 0:
            raise self.InvalidFieldDataException(f"Field {field.name} is required but no data given.")

        return (instance_for_creation, instance_for_updation, instance_ids_for_deletion, updated_counters)

    def update_custom_fields(self, data: dict, fields: QuerySet):
        logger.info("Custom fields updation started.")
        dropdown_added_ids = []
        field_added_ids = []
        dropdown_removed_ids = []
        field_removed_ids = []

        for field in fields:
            if field.type == CustomFieldTypeEnum.TEXT.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_text_data.all(), field=field, data=data
                )
                self.text_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_richtext_data.all(),
                    field=field,
                    data=data,
                )
                self.richtext_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.DECIMAL.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_decimal_data.all(),
                    field=field,
                    data=data,
                )
                self.decimal_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.DATE.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_date_data.all(),
                    field=field,
                    data=data,
                )
                self.date_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_phonenumber_data.all(),
                    field=field,
                    data=data,
                )
                self.phonenumber_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.DROPDOWN.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_dropdown_data.all(),
                    field=field,
                    data=data,
                )
                self.dropdown_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.FILE.value:
                instance_list, updated_counters = self.prepare_updated_data_objects(
                    data_objects=field.custom_file_data.all(),
                    field=field,
                    data=data,
                )
                self.file_objs.extend(instance_list)
            elif field.type == CustomFieldTypeEnum.MULTIPLE_FILES.value:
                # Project Custom field will have linking to only one multiple attachment field (FK)
                (
                    instances_for_creation,
                    instances_for_updation,
                    instance_ids_for_deletion,
                    updated_counters,
                ) = self.prepare_updated_multiple_data_objects(
                    multiple_files_field=field.custom_multiple_files_fields.first(),
                    existing_data_objects=field.custom_multiple_files_fields.first().custom_multiple_files_data.available(),
                    field=field,
                    data=data,
                )
                self.multiple_file_objs_for_creation.extend(instances_for_creation)
                self.multiple_file_objs_for_updation.extend(instances_for_updation)
                self.multiple_file_objs_for_deletion.extend(instance_ids_for_deletion)

            dropdown_added_ids.extend(updated_counters.dropdown_added_ids)
            field_added_ids.extend(updated_counters.field_added_ids)
            dropdown_removed_ids.extend(updated_counters.dropdown_removed_ids)
            field_removed_ids.extend(updated_counters.field_removed_ids)

        logger.info("Updated data objects prepared.")
        logger.info(
            dropdown_added_ids=dropdown_added_ids,
            field_added_ids=field_added_ids,
            dropdown_removed_ids=dropdown_removed_ids,
            field_removed_ids=field_removed_ids,
        )
        if self.text_objs:
            ProjectCustomTextFieldData.objects.bulk_update(
                objs=self.text_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.decimal_objs:
            ProjectCustomNumberFieldData.objects.bulk_update(
                objs=self.decimal_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.date_objs:
            ProjectCustomDateFieldData.objects.bulk_update(
                objs=self.date_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.richtext_objs:
            ProjectCustomRichTextFieldData.objects.bulk_update(
                objs=self.richtext_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.phonenumber_objs:
            ProjectCustomPhoneNumberFieldData.objects.bulk_update(
                objs=self.phonenumber_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.dropdown_objs:
            ProjectCustomDropDownFieldData.objects.bulk_update(
                objs=self.dropdown_objs, fields=["data", "updated_by_id", "updated_at"]
            )
        if self.file_objs:
            ProjectCustomFileFieldData.objects.bulk_update(
                objs=self.file_objs, fields=["data", "name", "updated_by_id", "updated_at"]
            )
        if self.multiple_file_objs_for_creation:
            ProjectCustomMultipleFilesFieldData.objects.bulk_create(objs=self.multiple_file_objs_for_creation)
        if self.multiple_file_objs_for_updation:
            ProjectCustomMultipleFilesFieldData.objects.bulk_update(
                objs=self.multiple_file_objs_for_updation, fields=["data", "name", "updated_by_id", "updated_at"]
            )
        if self.multiple_file_objs_for_deletion:
            ProjectCustomMultipleFilesFieldData.objects.filter(id__in=self.multiple_file_objs_for_deletion).update(
                deleted_by=self.user.pk, deleted_at=timezone.now()
            )

        logger.info("Custom fields objects updated.")

        ProjectCustomField.objects.available().filter(id__in=field_added_ids).update(data_counter=F("data_counter") + 1)
        ProjectCustomFieldDropDownOption.objects.available().filter(id__in=dropdown_added_ids).update(
            counter=F("counter") + 1
        )
        ProjectCustomField.objects.available().filter(id__in=field_removed_ids).update(
            data_counter=F("data_counter") - 1
        )
        ProjectCustomFieldDropDownOption.objects.available().filter(id__in=dropdown_removed_ids).update(
            counter=F("counter") - 1
        )
        logger.info("Custom field data counter updated.")

    def update_system_fields(self, data: dict, store: Store, remarks: str):
        logger.info("System fields updation started.", data=data)
        data["reason"] = remarks
        project, updated_fields = project_update(project=store.project, data=data, updated_by_id=self.user.pk)
        logger.info("Project updated.", updated_fields=updated_fields)
        store_update(store=store, data=data, updated_by_id=self.user.pk)
        if set(updated_fields).intersection(set(["recce_due_at", "expected_started_at", "kam_completion_due_at"])):
            project_date_events_trigger(
                fields=updated_fields,
                project=project,
                perv_recce_due_at=project.recce_due_at,
                prev_kam_completion_due_at=project.kam_completion_due_at,
                prev_expected_started_at=project.expected_started_at,
                changed_by=self.user,
            )

        logger.info("System fields updation finished.")

    def update(self, data: dict, store: Store):
        logger.info("Project section update service started.")
        updated_custom_fields: QuerySet = custom_fields_to_update_fetch(
            field_uuids=data.get("custom_fields").keys(), project_id=self.project_id, org_id=self.org_id
        )
        self.update_system_fields(data=data.get("system_fields"), store=store, remarks=data.get("remarks"))
        self.update_custom_fields(data=data.get("custom_fields"), fields=updated_custom_fields)
        logger.info("Project section update service finished.")


class ProjectCustomSectionImportService:
    def __init__(self, org_id: int, user: User, project_id: int):
        self.org_id = org_id
        self.user = user
        self.project_id = project_id

    def create_data_objects(self, model: BaseModel, field_id: int):
        return model(field_id=field_id, project_id=self.project_id, data=None, created_by_id=self.user.pk)

    def import_section(self, field_uuids: List[UUID]):
        logger.info("Project custom section import service started.")
        custom_fields = custom_field_fetch_by_uuids(field_uuids=field_uuids, org_id=self.org_id)

        dropdown_data_objs: List[ProjectCustomDropDownFieldData] = []
        number_data_objs: List[ProjectCustomNumberFieldData] = []
        text_data_objs: List[ProjectCustomTextFieldData] = []
        rich_text_data_objs: List[ProjectCustomRichTextFieldData] = []
        phone_number_data_objs: List[ProjectCustomPhoneNumberFieldData] = []
        date_data_objs: List[ProjectCustomDateFieldData] = []
        file_data_objs: List[ProjectCustomFileFieldData] = []
        multiple_files_field_objs: List[ProjectCustomMultipleFilesField] = []

        for field in custom_fields:
            if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                dropdown_data_objs.append(
                    self.create_data_objects(model=ProjectCustomDropDownFieldData, field_id=field.pk)
                )
            elif field.type == CustomFieldTypeEnum.DECIMAL.value:
                number_data_objs.append(self.create_data_objects(model=ProjectCustomNumberFieldData, field_id=field.pk))
            elif field.type == CustomFieldTypeEnum.TEXT.value:
                text_data_objs.append(self.create_data_objects(model=ProjectCustomTextFieldData, field_id=field.pk))
            elif field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                rich_text_data_objs.append(
                    self.create_data_objects(model=ProjectCustomRichTextFieldData, field_id=field.pk)
                )
            elif field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                phone_number_data_objs.append(
                    self.create_data_objects(model=ProjectCustomPhoneNumberFieldData, field_id=field.pk)
                )
            elif field.type == CustomFieldTypeEnum.DATE.value:
                date_data_objs.append(self.create_data_objects(model=ProjectCustomDateFieldData, field_id=field.pk))
            elif field.type == CustomFieldTypeEnum.FILE.value:
                file_data_objs.append(self.create_data_objects(model=ProjectCustomFileFieldData, field_id=field.pk))
            elif field.type == CustomFieldTypeEnum.MULTIPLE_FILES.value:
                multiple_files_field_objs.append(
                    ProjectCustomMultipleFilesField(
                        field_id=field.pk, project_id=self.project_id, created_by_id=self.user.pk
                    )
                )

        if dropdown_data_objs:
            ProjectCustomDropDownFieldData.objects.bulk_create(objs=dropdown_data_objs)
        if number_data_objs:
            ProjectCustomNumberFieldData.objects.bulk_create(objs=number_data_objs)
        if text_data_objs:
            ProjectCustomTextFieldData.objects.bulk_create(objs=text_data_objs)
        if rich_text_data_objs:
            ProjectCustomRichTextFieldData.objects.bulk_create(objs=rich_text_data_objs)
        if phone_number_data_objs:
            ProjectCustomPhoneNumberFieldData.objects.bulk_create(objs=phone_number_data_objs)
        if date_data_objs:
            ProjectCustomDateFieldData.objects.bulk_create(objs=date_data_objs)
        if file_data_objs:
            ProjectCustomFileFieldData.objects.bulk_create(objs=file_data_objs)
        if multiple_files_field_objs:
            ProjectCustomMultipleFilesField.objects.bulk_create(objs=multiple_files_field_objs)
        logger.info("Custom field data objects created successfully.")
        logger.info("Project custom section import service finished.")


def mark_unmark_expense_closure(
    project_id: int, organization_id: int, action: ProjectExpenseClosureChoices, user_id: int
):
    ProjectStatusUpdateService.process(
        project_id=project_id,
        module=Module.ORDER.value,
        status=OrderStatus.OPEN if action == ProjectExpenseClosureChoices.UNMARK else OrderStatus.EXPENSE_CLOSED,
        organization_id=organization_id,
        user_id=user_id,
    )


def get_project_list_column_settings(org_id: int) -> List[ColumnSettingData]:
    roles: QuerySet[OrganizationConfigRole] = OrganizationConfigRoleService.organization_search_config_role_list(
        org_id=org_id
    )
    role_list = RoleSerializer(roles, many=True).data

    project_sections: List[dict] = fetch_project_section_config(org_id=org_id)
    column_config = [
        ColumnSettingData(
            id=PROJECT_JOB_ID_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_JOB_ID_UUID),
            is_enabled=True,
            is_fixed=PROJECT_JOB_ID_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.TEXT.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=PROJECT_STAGE_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_STAGE_UUID),
            is_enabled=True,
            is_fixed=PROJECT_STAGE_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DROPDOWN.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=PROJECT_CREATED_BY_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_CREATED_BY_UUID),
            is_enabled=True,
            is_fixed=PROJECT_CREATED_BY_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DROPDOWN.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=PROJECT_CREATION_DATE_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_CREATION_DATE_UUID),
            is_enabled=True,
            is_fixed=PROJECT_CREATION_DATE_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DATE.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=ACTUAL_COMPLETION_DATE_UUID,
            name=ColumnSettingFieldNameMapping.get(ACTUAL_COMPLETION_DATE_UUID),
            is_enabled=True,
            is_fixed=ACTUAL_COMPLETION_DATE_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DATE.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=RECCE_COMPLETION_DATE_UUID,
            name=ColumnSettingFieldNameMapping.get(RECCE_COMPLETION_DATE_UUID),
            is_enabled=True,
            is_fixed=RECCE_COMPLETION_DATE_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DATE.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=PROJECT_SCOPE_PROGRESS_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_SCOPE_PROGRESS_UUID),
            is_enabled=True,
            is_fixed=PROJECT_SCOPE_PROGRESS_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DECIMAL.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
        ColumnSettingData(
            id=PROJECT_SCHEDULE_PROGRESS_UUID,
            name=ColumnSettingFieldNameMapping.get(PROJECT_SCHEDULE_PROGRESS_UUID),
            is_enabled=True,
            is_fixed=PROJECT_SCHEDULE_PROGRESS_UUID in FIXED_COLUMN_FIELD_UUIDS,
            type=CustomFieldTypeEnum.DECIMAL.value,
            section=ColumnSettingSectionTypeEnum.DETAIL.value,
        ),
    ]
    for section in project_sections:
        for field in section.get("fields"):
            if is_field_visible_in_setting(field=field, section=section):
                if field.get("uuid") == CITY_UUID:
                    column_config.append(
                        ColumnSettingData(
                            id=field.get("uuid"),
                            name=ColumnSettingFieldNameMapping.get(field.get("uuid"), field.get("name")),
                            is_enabled=True,
                            is_fixed=field.get("uuid") in FIXED_COLUMN_FIELD_UUIDS,
                            type=CustomFieldTypeEnum.TEXT.value,
                            section=ColumnSettingSectionTypeEnum.DETAIL.value,
                        )
                    )
                else:
                    column_config.append(
                        ColumnSettingData(
                            id=field.get("uuid"),
                            name=ColumnSettingFieldNameMapping.get(field.get("uuid"), field.get("name")),
                            is_enabled=(not field.get("is_archived")),
                            is_fixed=field.get("uuid") in FIXED_COLUMN_FIELD_UUIDS,
                            type=field.get("type"),
                            section=(
                                ColumnSettingSectionTypeEnum.ARCHIVED.value
                                if field.get("is_archived")
                                else ColumnSettingSectionTypeEnum.DETAIL.value
                            ),
                        )
                    )

    for role in role_list:
        column_config.append(
            ColumnSettingData(
                id=role.get("id"),
                name=role.get("name"),
                is_enabled=role.get("is_visible"),
                is_fixed=False,
                type=CustomFieldTypeEnum.DROPDOWN.value,
                section=ColumnSettingSectionTypeEnum.ROLE.value,
            )
        )

    sorted_column_list = sorted(column_config, key=get_sorting_key)
    return sorted_column_list


def get_sorting_key(x):
    key = (-x.is_fixed, ColumnSectionOrder.get(x.section))
    if x.section == ColumnSettingSectionTypeEnum.DETAIL.value:
        return key + (ColumnOrderDict.get(x.id, 100),)
    return key


def is_field_visible_in_setting(field: dict, section: dict) -> bool:
    if field.get("type") not in [
        CustomFieldTypeEnum.TEXT.value,
        CustomFieldTypeEnum.DECIMAL.value,
        CustomFieldTypeEnum.DATE.value,
        CustomFieldTypeEnum.DROPDOWN.value,
        CustomFieldTypeEnum.PHONE_NUMBER.value,
    ]:
        return False
    if section.get("type") == ProjectSectionTypeEnum.ADVANCE.value and field.get("uuid") not in SYSTEM_FIELD_UUIDS:
        return False
    if field.get("uuid") in SYSTEM_FIELD_UUIDS and field.get("uuid") not in ColumnOrderDict.keys():
        return False
    return True


def transform_project_data(
    project: Project, data: dict, has_project_permission: bool, user_permissions: list[Permissions]
) -> dict:
    detail = {
        "id": data.get("id"),
        PROJECT_JOB_ID_UUID: data.get("job_id"),
        PROJECT_NAME_UUID: data.get("name"),
        CLIENT_UUID: data.get("client"),
        BUSINESS_CATEGORY_UUID: data.get("business_category"),
        STATE_UUID: data.get("state"),
        CITY_UUID: data.get("city"),
        COUNTRY_UUID: data.get("country"),
        PROJECT_STAGE_UUID: data.get("stage"),
        PROJECT_ESTIMATE_UUID: data.get("probable_estimate"),
        RECCE_DUE_DATE_UUID: data.get("recce_due_at"),
        PROJECT_CREATION_DATE_UUID: data.get("created_at"),
        EXECUTION_DUE_DATE_UUID: data.get("kam_completion_due_at"),
        ACTUAL_COMPLETION_DATE_UUID: data.get("execution_completed_at"),
        PROJECT_CREATED_BY_UUID: data.get("created_by"),
        RECCE_COMPLETION_DATE_UUID: data.get("recce_completed_at"),
        PROJECT_IS_ARCHIVED_UUID: project.is_archived,
        PROJECT_CURRENCY_UUID: {
            "id": HashIdConverter.encode(project.config.currency.id),
            "name": project.config.currency.code,
            "symbol": project.config.currency.symbol,
            "code": project.config.currency.code,
        },
        PROJECT_TAX_TYPE_UUID: {
            "id": HashIdConverter.encode(project.config.tax_type.id),
            "name": project.config.tax_type.name,
        },
        PROJECT_TIMEZONE_UUID: {
            "id": HashIdConverter.encode(project.config.timezone.id),
            "name": project.config.timezone.name,
            "locale": project.config.timezone.locale,
        },
    }

    for role_id, assignment in data.get("assignments").items():
        detail[role_id] = assignment

    if has_project_permission:
        project_timezone = pytz.timezone(project.config.timezone.name)
        custom_field_data = prepare_custom_field_data(project=project, timezone=project_timezone, is_list_view=True)
        detail.update(custom_field_data)
    if Permissions.CAN_VIEW_SCOPE_PROGRESS_ON_PROJECT_LIST in user_permissions:
        detail[PROJECT_SCOPE_PROGRESS_UUID] = data.get("scope_progress")
    if Permissions.CAN_VIEW_SCHEDULE_PROGRESS_ON_PROJECT_LIST in user_permissions:
        detail[PROJECT_SCHEDULE_PROGRESS_UUID] = data.get("schedule_progress")
    return detail


def get_project_client_id(project_id: int, organization_id: int) -> int:
    return ProjectDataCache.get(
        project_details=ProjectOrganizationEntity(project_id=project_id, organization_id=organization_id)
    ).client_id


def get_creator_org_id(project_id: int, organization_id: int) -> int:
    return ProjectDataCache.get(
        project_details=ProjectOrganizationEntity(project_id=project_id, organization_id=organization_id)
    ).organization_id


def get_parent_organization_ids(project_id: int, organization_id: int) -> list[int]:
    ids = []
    project_assignments = ProjectAssignmentCache.get(instance_id=project_id)
    for org_id, assignment in project_assignments.items():
        if str(organization_id) == org_id:
            ids.extend(assignment["role_ids"].keys())
    return [int(i) for i in set(ids) if int(i) != organization_id]


def get_child_organization_ids(project_id: int, organization_id: int) -> list[int]:
    ids = []
    project_assignments = ProjectAssignmentCache.get(instance_id=project_id)
    for org_id, assignment in project_assignments.items():
        if str(organization_id) in assignment["role_ids"]:
            ids.append(str(org_id))
    return [int(i) for i in set(ids) if int(i) != organization_id]


def get_immediate_client_id(project_id: int, organization_id: int) -> Union[int, None]:
    project_client_id = get_project_client_id(project_id=project_id, organization_id=organization_id)
    if project_client_id == organization_id:
        # project client
        return None

    creator_org_id = get_creator_org_id(project_id=project_id, organization_id=organization_id)
    if organization_id == creator_org_id == project_client_id:
        # project client but also creator
        return None

    if creator_org_id == organization_id:
        # creator is not client
        return project_client_id

    # vendor / project shared without order cases
    parent_org_ids = get_parent_organization_ids(project_id=project_id, organization_id=organization_id)
    no_of_parent_orgs = len(parent_org_ids)
    if no_of_parent_orgs > 1:
        # Multiple Parent Org Ids found
        logger.error(
            "Multiple Clients Case Found, Needs To Be Handled Later.",
            current_org_id=organization_id,
            creator_org_id=creator_org_id,
            parent_org_ids=parent_org_ids,
            project_id=project_id,
        )
        return None

    if no_of_parent_orgs == 1:
        return parent_org_ids[-1]

    logger.warning(
        "Unknown Case",
        current_org_id=organization_id,
        creator_org_id=creator_org_id,
        parent_org_ids=parent_org_ids,
        project_id=project_id,
    )
    return None


def project_poc_assigned_trigger_event(organization_id: int, poc_id: int):
    event_data = ProjectPocData(
        organization_id=organization_id,
        poc_id=poc_id,
    )
    trigger_event(event=Events.PROJECT_POC_ASSIGNED, event_data=event_data)


def update_project_poc_role_level(poc_id: int, level: int):
    poc_org_id = User.objects.filter(id=poc_id).first().org_id
    role = Role.objects.get(
        organization_id=poc_org_id,
        name=ReservedRoleNames.PROJECT_POC.value,
        scope=PermissionScope.PROJECT,
        role_type=RoleType.DYNAMIC,
    )
    role.level = level
    role.save()
