import json

import pytz
from drf_yasg.utils import swagger_serializer_method
from phonenumber_field import serializerfields
from rest_framework import serializers

from common.choices import StateChoices
from common.constants import STANDARD_DECIMAL_CONFIG, CustomFieldTypeEnum
from common.entities import ProjectDataEntity
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomDateField,
    CustomEndDateField,
    HashIdField,
    HashIdSerializerMixin,
)
from core.serializers import (
    CountrySerializer,
    OrganizationSerializer,
    PocSerializer,
    RoleModelSerializer,
    StateSerializer,
    UserModelSerializer,
    UserProfileSerializer,
)
from crm.interface.helpers import add_lead_number_padding
from project.data.models import (
    BusinessCategory,
    CityStateMapping,
    Project,
    ProjectAttachment,
    ProjectCustomField,
    ProjectCustomFieldDropDownOption,
    ProjectCustomSection,
    ProjectFieldHistory,
    ProjectOrgExtraData,
    ProjectProgressSchedule,
    ProjectStatus,
    ProjectStatusHistory,
    Project<PERSON>ser,
    <PERSON>ope,
    Store,
    StoreFieldHistory,
)
from project.domain.constants import (
    ASSIGNED_PROJECT_USER_SECTION_UUID,
    BASIC_DETAIL_SECTION_UUID,
    READONLY_FIELD_UUIDS,
    SYSTEM_FIELD_NAMES_UUID_MAPPING,
    SYSTEM_FIELD_UUIDS,
    SYSTEM_SECTION_UUIDS,
    ProjectFieldActionEnum,
    ProjectSectionActionEnum,
    SystemFieldNameEnum,
)
from project.domain.entities import (
    CustomFieldDropdownOptionInputData,
    ProjectCustomFieldInputData,
    ProjectCustomSectionInputData,
    ProjectSharedDataEntity,
)
from project.domain.exceptions import ProjectConfigExceptions
from project.domain.status_color_code import (
    Module,
    ProjectStatusColorCode,
    RDStatus,
    StatusHelper,
)
from project.interface.serializer_mixins import ArchivedFieldMixin, CustomFieldDataValidatorMixin
from project.selectors import section_import_check
from project.share.interface.data_serializers import OrganizationFetchDataSerializer
from project.utils import rd_status_dict
from rollingbanners.hash_id_converter import HashIdConverter
from rollingbanners.storage_backends import PublicMediaFileStorage


class ProjectModelSerializer(BaseModelSerializer):
    stage = serializers.SerializerMethodField()

    def get_stage(self, obj):
        if hasattr(obj, "project_status"):
            return StageSerializer(rd_status_dict(obj)).data if obj.project_status else {}
        return {}

    class Meta:
        model = Project
        fields = "__all__"


class ProjectAttachmentSerializer(BaseModelSerializer):
    uploaded_by = UserProfileSerializer()
    updated_by = UserProfileSerializer()

    class Meta:
        model = ProjectAttachment
        fields = "__all__"


class ProjectUserModelSerializer(BaseModelSerializer):
    class RoleSerializer(RoleModelSerializer):
        class Meta(RoleModelSerializer.Meta):
            fields = ("id", "name", "is_required", "order_weight")
            ref_name = "ProjectRoleData"

    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name", "photo", "phone_number")
            ref_name = "ProjectUserData"

    user = UserSerializer()
    role = RoleSerializer()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["role"].context.update(self.context)

    class Meta:
        model = ProjectUser
        fields = "__all__"


class BusinessCategoryModelSerializer(BaseModelSerializer):
    class Meta:
        model = BusinessCategory
        fields = "__all__"


class ScopeSerializer(serializers.ModelSerializer, HashIdSerializerMixin):
    class Meta:
        model = Scope
        fields = ("id", "name")


class ProjectStoreSerializer(serializers.ModelSerializer):
    state = serializers.SerializerMethodField()

    class Meta:
        model = Store
        fields = ("state", "city")

    @staticmethod
    def get_state(obj):
        return dict(StateChoices.choices)[obj.state]


class RecceStoreSerializer(serializers.ModelSerializer):
    dealer = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()

    @staticmethod
    def get_state(obj):
        return dict(StateChoices.choices)[obj.state]

    @staticmethod
    def get_dealer(obj):
        return {
            "name": obj.dealer_name,
            "contact": obj.dealer_phone_number.as_e164 if obj.dealer_phone_number else None,
        }

    @staticmethod
    def get_location(obj):
        return (
            {"longitude": obj.location.x, "latitude": obj.location.y}
            if obj.location
            else {"longitude": None, "latitude": None}
        )

    class Meta:
        model = Store
        fields = ("address", "city", "state", "dealer", "location")


class ProjectDetailsStoreSerializer(RecceStoreSerializer):
    local_name = serializers.SerializerMethodField()
    store_area = serializers.DecimalField(source="area", max_digits=15, decimal_places=2)

    @staticmethod
    def get_local_name(obj):
        return obj.local_name if obj.local_name else None

    class Meta:
        model = Store
        fields = ("address", "city", "state", "dealer", "location", "local_name", "local_name_file", "store_area")


class ProjectSerializer(serializers.ModelSerializer, HashIdSerializerMixin):
    client = OrganizationSerializer()
    store = ProjectStoreSerializer()
    stage = serializers.SerializerMethodField()
    recce_due_at = CustomEndDateField()
    kam_completion_due_at = CustomEndDateField()
    execution_completed_at = CustomEndDateField()

    @staticmethod
    def get_stage(obj):
        return dict(Project.StageChoices.choices)[obj.stage]

    class Meta:
        model = Project
        fields = (
            "id",
            "name",
            "job_id",
            "client",
            "stage",
            "estimated_cost",
            "recce_due_at",
            "recce_completed_at",
            "kam_completion_due_at",
            "ops_completion_due_at",
            "execution_completed_at",
            "store",
        )


class ProjectWatiEventSerializer(serializers.ModelSerializer, HashIdSerializerMixin):
    store_name = serializers.CharField(source="name")
    city = serializers.CharField(source="store.city")
    state = serializers.CharField(source="store.state_name")
    client_name = serializers.CharField(source="client.name")
    project_id = serializers.SerializerMethodField()

    @staticmethod
    def get_project_id(obj):
        return obj.pk

    class Meta:
        model = Project
        fields = ("project_id", "job_id", "store_name", "city", "state", "client_name")
        output_hash_id_fields = ("project_id",)


class DueDateModifiedAlertMentorSerializer(BaseModelSerializer):
    responsible = serializers.SerializerMethodField()
    client_name = serializers.CharField(source="client.name")
    store_name = serializers.CharField(source="name")
    city = serializers.CharField(source="store.city")
    state = serializers.CharField(source="store.state_name")
    mentor = serializers.CharField(source="client.business_category.mentor.name")
    mentor_contact = serializers.CharField(source="client.business_category.mentor.phone_number")

    @staticmethod
    def get_responsible(obj):
        updated_by = obj.updated_by
        return f"{updated_by.name}" if updated_by else ""

    class Meta:
        model = Project
        fields = ("responsible", "city", "state", "job_id", "store_name", "client_name", "mentor", "mentor_contact")


class StatusSerializer(BaseSerializer):
    name = serializers.CharField()
    color_code = serializers.CharField()

    class Meta:
        pass


class ProjectHeaderSerializer(ProjectModelSerializer):
    class HeaderStepperSerializer(BaseSerializer):
        section_key = serializers.CharField()
        name = serializers.CharField()
        status = StatusSerializer()
        stepper_status = serializers.CharField()

        class Meta:
            pass

    status = serializers.SerializerMethodField()
    header_list = HeaderStepperSerializer(many=True)

    @swagger_serializer_method(serializer_or_field=StatusSerializer())
    def get_status(self, obj):
        if hasattr(obj, "project_status"):
            status = {
                "name": dict(RDStatus.choices)[obj.project_status] if obj.project_status else RDStatus.labels[0],
                "color_code": (
                    ProjectStatusColorCode.get_color_code(obj.project_status)
                    if obj.project_status
                    else ProjectStatusColorCode.get_color_code(1)
                ),
            }
            return status

    class Meta(ProjectModelSerializer.Meta):
        ref_name = "ProjectHeaderOutput"
        fields = ("id", "name", "job_id", "status", "header_list")


class ProjectStatusHistoryModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectStatusHistory


class ProjectStatusHistorySerializer(ProjectStatusHistoryModelSerializer):
    class UserOrgSerializer(UserModelSerializer):
        organization_name = serializers.CharField(source="org.name")

        def get_organization_name(self, obj):
            project_shared_data: ProjectSharedDataEntity | None = self.context.get("project_shared_data", None)
            if project_shared_data is None:
                return obj.org.name

            if obj.org.id in project_shared_data.clients:
                return project_shared_data.clients[obj.org.id].name
            if obj.org.id in project_shared_data.vendors:
                return project_shared_data.vendors[obj.org.id].name
            else:
                return obj.org.name

        class Meta(UserModelSerializer.Meta):
            ref_name = "ProjectStatusHistoryUserOutput"
            fields = ("id", "name", "photo", "organization_name")

    status = serializers.SerializerMethodField()
    updated_at = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    is_initial = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=UserOrgSerializer())
    def get_updated_by(self, obj):
        return (
            self.UserOrgSerializer(obj.created_by).data
            if obj.created_by.org_id in self.context.get("known_organization_ids", [])
            else None
        )

    @swagger_serializer_method(serializer_or_field=StatusSerializer())
    def get_status(self, obj):
        status_name = StatusHelper.get_status(module=obj.module, status=obj.status)
        status_color_code = StatusHelper.get_color_code(module=obj.module, status=obj.status)
        status_object = {"name": status_name, "color_code": status_color_code}
        return status_object

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_updated_at(self, obj):
        return obj.created_at

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_is_initial(self, obj):
        if obj.module == Module.PROJECT.value and obj.status == RDStatus.PROJECT_CREATED.value:
            return True
        return False

    class Meta(ProjectStatusHistoryModelSerializer.Meta):
        ref_name = "ProjectStatusHistoryOutput"
        fields = (
            "status",
            "updated_by",
            "updated_at",
            "is_initial",
        )


class StageSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    color_code = serializers.CharField()

    class Meta:
        output_hash_id_fields = ("id",)


class CreatedBySerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "ProjectListSerializerCreatedBy"
        fields = ("id", "name", "photo", "organization_name")


class ProjectListSerializer(ProjectModelSerializer):
    class ProjectBusinessCategorySerializer(BusinessCategoryModelSerializer):
        class Meta(BusinessCategoryModelSerializer.Meta):
            fields = ("id", "name")

    client = OrganizationSerializer()
    city = serializers.SerializerMethodField()
    state = StateSerializer(source="store.state")
    country = CountrySerializer(source="store.country")
    assignments = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    probable_estimate = serializers.SerializerMethodField()
    kam_completion_due_at = serializers.SerializerMethodField()
    ops_completion_due_at = serializers.SerializerMethodField()
    execution_completed_at = serializers.SerializerMethodField()
    recce_due_at = serializers.SerializerMethodField()
    recce_completed_at = serializers.SerializerMethodField()
    business_category = ProjectBusinessCategorySerializer()
    scope_progress = serializers.DecimalField(
        source="progress_percentage",
        allow_null=True,
        max_digits=STANDARD_DECIMAL_CONFIG.get("max_digits"),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
    )
    schedule_progress = serializers.DecimalField(
        allow_null=True,
        max_digits=STANDARD_DECIMAL_CONFIG.get("max_digits"),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
    )

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_city(self, obj):
        return obj.store.city.name if obj.store.city else None

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_probable_estimate(self, obj):
        if (
            self.context.get("organization_id") == obj.organization_id
            and self.context.get("has_project_permission") is True
        ):
            return f"{obj.config.currency.symbol} {obj.estimated_cost}"
        return None

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_kam_completion_due_at(self, obj):
        timezone = pytz.timezone(obj.config.timezone.name)
        return (
            obj.kam_completion_due_at.astimezone(timezone)
            if self.context.get("organization_id") == obj.organization_id and obj.kam_completion_due_at
            else None
        )

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_ops_completion_due_at(self, obj):
        timezone = pytz.timezone(obj.config.timezone.name)
        return (
            obj.ops_completion_due_at.astimezone(timezone)
            if self.context.get("organization_id") == obj.organization_id and obj.ops_completion_due_at
            else None
        )

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_execution_completed_at(self, obj):
        timezone = pytz.timezone(obj.config.timezone.name)
        return (
            obj.execution_completed_at.astimezone(timezone)
            if self.context.get("organization_id") == obj.organization_id and obj.execution_completed_at
            else None
        )

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_recce_due_at(self, obj):
        return obj.recce_due_at if self.context.get("organization_id") == obj.organization_id else None

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_recce_completed_at(self, obj):
        return obj.recce_completed_at if self.context.get("organization_id") == obj.organization_id else None

    @swagger_serializer_method(serializer_or_field=CreatedBySerializer())
    def get_created_by(self, obj):
        history = obj.project_status_history.all().first()
        if history is None:
            return None
        return CreatedBySerializer(history.created_by).data

    def get_assignments(self, obj):
        if self.context.get("assignment_data") and obj.id in self.context.get("assignment_data"):
            return self.context.get("assignment_data").get(obj.id)
        return {}

    class Meta(ProjectModelSerializer.Meta):
        fields = (
            "id",
            "name",
            "client",
            "probable_estimate",
            "job_id",
            "recce_due_at",
            "state",
            "stage",
            "city",
            "country",
            "recce_completed_at",
            "kam_completion_due_at",
            "ops_completion_due_at",
            "execution_completed_at",
            "created_at",
            "assignments",
            "created_by",
            "business_category",
            "scope_progress",
            "schedule_progress",
        )


class ProjectStatusModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectStatus


class RoleSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    is_visible = serializers.BooleanField()

    class Meta:
        ref_name = "RoleSerializer"
        input_hash_id_fields = ("id",)


class CounterListSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    count = serializers.IntegerField()

    class Meta:
        ref_name = "CounterListSerializer"
        input_hash_id_fields = ("id",)


class SearchConfigSerializer(BaseSerializer):
    counter_list = CounterListSerializer(many=True)
    role_list = RoleSerializer(many=True)

    class Meta:
        ref_name = "SearchConfigOutputSerializer"


class BaseFieldHistoryModelSerializer(BaseModelSerializer):
    model = None

    value = serializers.SerializerMethodField()
    updated_at = serializers.DateTimeField(source="created_at")
    updated_by = UserProfileSerializer(source="created_by")
    prev_value = serializers.SerializerMethodField()

    def get_value(self, obj):
        field = self.context.get("field", None)
        assert field is not None, "Field should be a passed in context"
        if self.model is None:
            raise NotImplementedError("model should not be None")
        value = self.model.from_field_history(field=field, value=obj.value)
        self.context["prev_value"] = value
        return value

    def get_prev_value(self, obj):
        return self.context.get("prev_value", None)

    class Meta:
        abstract = True


class ProjectFieldHistoryModelSerializer(BaseFieldHistoryModelSerializer):
    model = Project

    class Meta:
        model = ProjectFieldHistory
        fields = "__all__"


class StoreFieldHistoryModelSerializer(BaseFieldHistoryModelSerializer):
    model = Store

    class Meta:
        model = StoreFieldHistory
        fields = "__all__"


class ProjectDetailsSerializer(ProjectSerializer):
    progress_percentage = serializers.IntegerField()
    immediate_client_id = serializers.SerializerMethodField()

    class ProjectBusinessCategorySerializer(BusinessCategoryModelSerializer):
        class Meta(BusinessCategoryModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "ProjectBusinessCategorySerializer"
            output_hash_id_fields = ("id",)

    class RoleMappingSerializer(ProjectUserModelSerializer):
        class Meta(ProjectUserModelSerializer.Meta):
            fields = ("user", "role")
            ref_name = "RoleMappingSerializer"

    class ProjectAttachmentSerializer(ProjectAttachmentSerializer):
        class Meta(ProjectAttachmentSerializer.Meta):
            ref_name = "ProjectDetailsAttachmentSerializer"
            fields = ("id", "file", "name", "uploaded_by", "updated_by", "updated_at", "uploaded_at")

    class OtherOrganizationSerializer(OrganizationFetchDataSerializer):
        poc = serializers.SerializerMethodField()
        logo = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=PocSerializer(allow_null=True, default=None))
        def get_poc(self, obj):
            if hasattr(obj, "config") and obj.config.poc:
                return PocSerializer(obj.config.poc).data
            return None

        def get_logo(self, obj):
            if hasattr(obj, "logo") and obj.logo.name:
                return PublicMediaFileStorage.url(obj.logo.name)
            return None

        class Meta(OrganizationFetchDataSerializer.Meta):
            fields = ("id", "name", "assignment_type", "poc", "logo")
            ref_name = "OtherOrganizationSerializer"

    scopes = ScopeSerializer(many=True)
    store = ProjectDetailsStoreSerializer()
    stepper_data = serializers.SerializerMethodField()
    business_category = ProjectBusinessCategorySerializer()
    role_mappings = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    attachments = ProjectAttachmentSerializer(many=True)
    created_by = serializers.SerializerMethodField()
    other_organizations = OtherOrganizationSerializer(many=True)
    permission_list = serializers.ListField(child=serializers.CharField())

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_immediate_client_id(self, obj):
        if hasattr(obj, "immediate_client_id") and obj.immediate_client_id:
            return HashIdConverter.encode(obj.immediate_client_id)

    @swagger_serializer_method(serializer_or_field=CreatedBySerializer())
    def get_created_by(self, obj):
        history = obj.project_status_history.all().first()
        if history is None:
            return None
        return CreatedBySerializer(history.created_by).data

    @swagger_serializer_method(
        serializer_or_field=serializers.DictField(child=serializers.ListField(child=serializers.CharField()))
    )
    def get_permissions(self, obj):
        if hasattr(obj, "permissions"):
            return obj.permissions
        return {}

    @swagger_serializer_method(serializer_or_field=RoleMappingSerializer(many=True))
    def get_role_mappings(self, obj):
        if hasattr(obj, "role_mappings"):
            return self.RoleMappingSerializer(
                obj.role_mappings, many=True, context={"org_id": self.context["org_id"]}
            ).data
        return []

    @staticmethod
    def get_stepper_data(obj):
        data = {
            "recce": obj.recce_completed,
            "design": False,
            "boq": False,
            "allotment": False,
            "planning": False,
            "work_reports": False,
            "finances": False,
        }
        return data

    class Meta:
        model = Project
        ref_name = "ProjectDetailsSerializer"
        fields = (
            "id",
            "name",
            "job_id",
            "client",
            "immediate_client_id",
            "stage",
            "estimated_cost",
            "recce_due_at",
            "recce_completed_at",
            "kam_completion_due_at",
            "ops_completion_due_at",
            "execution_completed_at",
            "store",
            "scopes",
            "final_cost",
            "stepper_data",
            "business_category",
            "expected_started_at",
            "role_mappings",
            "permissions",
            "attachments",
            "created_by",
            "created_at",
            "other_organizations",
            "progress_percentage",
            "permission_list",
            "ui_schema",
            "form_schema",
            "form_data",
        )


class ProjectDataEntityDataSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = ProjectDataEntity


class CityStateModelSerializer(BaseModelSerializer):
    class Meta:
        model = CityStateMapping
        fields = "__all__"


class ProjectProgressScheduleModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectProgressSchedule
        fields = "__all__"


class ProjectOrgExtraDataModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectOrgExtraData
        fields = "__all__"


class ProjecUserRoleSerializer(BaseSerializer):
    class RoleSerializer(RoleModelSerializer):
        class Meta(RoleModelSerializer.Meta):
            fields = ("id", "name", "is_required")

    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name", "photo", "phone_number")

    user = UserSerializer(allow_null=True)
    role = RoleSerializer()

    class Meta:
        ref_name = "ProjecUserRoleSerializer"
        fields = ("role", "user")


class ProjectCustomFieldModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectCustomField
        fields = "__all__"


class ProjectCustomSectionModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectCustomSection
        fields = "__all__"


class ProjectCustomFieldDropdownOptionModelSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectCustomFieldDropDownOption
        fields = "__all__"


class ProjecCustomFieldDropdownOptionSerializer(ProjectCustomFieldDropdownOptionModelSerializer, ArchivedFieldMixin):
    actions = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        return []

    class Meta(ProjectCustomFieldDropdownOptionModelSerializer.Meta):
        ref_name = "ProjectCustomFieldDropdownOptionSerializer"
        fields = (
            "id",
            "uuid",
            "name",
            "is_archived",
            "actions",
        )


class ProjectCustomFieldSerializer(ProjectCustomFieldModelSerializer, ArchivedFieldMixin):
    options = ProjecCustomFieldDropdownOptionSerializer(many=True)
    actions = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        actions = []
        if str(obj.uuid) not in SYSTEM_FIELD_UUIDS:
            actions.extend(
                [
                    ProjectFieldActionEnum.MOVE.value,
                    ProjectFieldActionEnum.ARCHIVE.value,
                    ProjectFieldActionEnum.DELETE.value,
                    ProjectFieldActionEnum.REQUIRED.value,
                ]
            )
            if obj.type == CustomFieldTypeEnum.DROPDOWN.value:
                actions.append(ProjectFieldActionEnum.EDIT.value)
        return actions

    class Meta(ProjectCustomFieldModelSerializer.Meta):
        ref_name = "ProjectCustomFieldSerializer"
        fields = (
            "id",
            "uuid",
            "name",
            "is_required",
            "position",
            "type",
            "options",
            "actions",
            "is_archived",
        )


class ProjectCustomSectionSerializer(ProjectCustomSectionModelSerializer):
    is_archived = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()
    fields = ProjectCustomFieldSerializer(many=True)

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        actions = []
        if str(obj.uuid) not in [BASIC_DETAIL_SECTION_UUID, ASSIGNED_PROJECT_USER_SECTION_UUID]:
            actions.append(ProjectSectionActionEnum.ADD_FIELD.value)
        if str(obj.uuid) not in SYSTEM_SECTION_UUIDS:
            actions.extend(
                [
                    ProjectSectionActionEnum.MOVE.value,
                    ProjectSectionActionEnum.ARCHIVE.value,
                    ProjectSectionActionEnum.DELETE.value,
                ]
            )
        return actions

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_is_archived(self, obj):
        for field in obj.fields.all():
            if field.archived_at is None:
                return False
        return True

    class Meta(ProjectCustomSectionModelSerializer.Meta):
        ref_name = "ProjectCustomSectionSerializer"
        fields = (
            "id",
            "name",
            "uuid",
            "position",
            "type",
            "is_archived",
            "actions",
            "fields",
        )


class CustomFieldDropdownOptionInputSerializer(BaseDataclassSerializer):
    class Meta:
        ref_name = "CustomFieldDropdownOptionInputSerializer"
        dataclass = CustomFieldDropdownOptionInputData


class ProjectCustomFieldInputSerializer(BaseDataclassSerializer):
    options = serializers.ListField(
        child=CustomFieldDropdownOptionInputSerializer(), allow_empty=True, required=False, default=[]
    )
    is_added_in_existing_projects = serializers.BooleanField(required=False, default=False)

    class Meta:
        ref_name = "ProjectCustomFieldInputSerializer"
        dataclass = ProjectCustomFieldInputData


class ProjectCustomSectionInputSerializer(BaseDataclassSerializer):
    fields = serializers.ListField(child=ProjectCustomFieldInputSerializer(), allow_empty=False, required=True)

    class Meta:
        ref_name = "ProjectCustomSectionInputSerializer"
        dataclass = ProjectCustomSectionInputData


class ProjectCustomFieldConfigInputSerializer(BaseSerializer):
    sections = serializers.ListSerializer(child=ProjectCustomSectionInputSerializer(), allow_empty=False, required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)

        if len(attrs) > 1:
            for section in range(1, len(attrs)):
                if attrs[section].uuid in SYSTEM_SECTION_UUIDS and attrs[section - 1].uuid not in SYSTEM_SECTION_UUIDS:
                    raise ProjectConfigExceptions.InvalidProjectConfigException("Invalid position for system section")

                if len(attrs[section].fields) > 1:
                    for field in range(1, len(attrs[section].fields)):
                        if (
                            attrs[section].fields[field].uuid in SYSTEM_FIELD_UUIDS
                            and attrs[section].fields[field - 1].uuid not in SYSTEM_FIELD_UUIDS
                        ):
                            raise ProjectConfigExceptions.InvalidProjectConfigException(
                                "Invalid position for system field"
                            )

        return attrs

    class Meta:
        ref_name = "ProjectCustomFieldConfigInputSerializer"


class ProjectDetailSectionSerializer(ProjectCustomSectionModelSerializer):
    class ProjectDetailFieldSerializer(ProjectCustomFieldModelSerializer):
        value = serializers.SerializerMethodField()
        readonly = serializers.SerializerMethodField()

        def get_value(self, obj):
            return ""

        def get_readonly(self, obj):
            return False

        class Meta(ProjectCustomFieldModelSerializer.Meta):
            ref_name = "ProjectDetailFieldSerializer"
            fields = (
                "id",
                "uuid",
                "name",
                "is_required",
                "position",
                "type",
                "value",
                "readonly",
            )

    fields = ProjectDetailFieldSerializer(many=True)
    actions = serializers.SerializerMethodField()

    def get_actions(self, obj):
        return ["edit"]

    class Meta(ProjectCustomSectionModelSerializer.Meta):
        ref_name = "ProjectDetailSectionSerializer"
        fields = (
            "id",
            "name",
            "uuid",
            "position",
            "type",
            "actions",
            "fields",
        )


class ProjectDetailSerializerV2(BaseSerializer):
    class LeadSerializer(BaseSerializer):
        id = HashIdField()
        name = serializers.CharField()

        class BoardSerializer(BaseSerializer):
            id = HashIdField()
            name = serializers.CharField()

            class Meta:
                fields = ("id", "name")
                ref_name = "BoardSerializer"
                output_hash_id_fields = ()

        is_deleted = serializers.SerializerMethodField()
        lead_number = serializers.SerializerMethodField()
        board = BoardSerializer()

        @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        def get_is_deleted(self, obj):
            return True if obj.deleted_at else False

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_lead_number(self, obj):
            return add_lead_number_padding(obj.serial_number)

        class Meta:
            fields = ("id", "name", "is_deleted", "lead_number", "board")
            ref_name = "LeadSerializer"
            output_hash_id_fields = ()

    class RoleMappingSerializer(ProjectUserModelSerializer):
        class Meta(ProjectUserModelSerializer.Meta):
            fields = ("user", "role")
            ref_name = "RoleMappingSerializer"

    class ProjectAttachmentSerializer(ProjectAttachmentSerializer):
        class Meta(ProjectAttachmentSerializer.Meta):
            ref_name = "ProjectDetailsAttachmentSerializer"
            fields = ("id", "file", "name", "uploaded_by", "updated_by", "updated_at", "uploaded_at")

    class OtherOrganizationSerializer(OrganizationFetchDataSerializer):
        poc = serializers.SerializerMethodField()
        logo = serializers.SerializerMethodField()
        name = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=PocSerializer(allow_null=True, default=None))
        def get_poc(self, obj):
            if hasattr(obj, "config") and obj.config.poc:
                return PocSerializer(obj.config.poc).data
            return None

        def get_logo(self, obj):
            if hasattr(obj, "logo") and obj.logo.name:
                return PublicMediaFileStorage.url(obj.logo.name)
            return None

        def get_name(self, obj):
            project_shared_data: ProjectSharedDataEntity | None = self.context.get("project_shared_data", None)
            if project_shared_data is None:
                return obj.name

            if obj.id in project_shared_data.clients:
                return project_shared_data.clients[obj.id].name
            elif obj.id in project_shared_data.vendors:
                return project_shared_data.vendors[obj.id].name
            else:
                return obj.name

        class Meta(OrganizationFetchDataSerializer.Meta):
            fields = ("id", "name", "assignment_type", "poc", "logo")
            ref_name = "OtherOrganizationSerializer"

    sections = serializers.ListField(child=serializers.DictField())
    role_mappings = serializers.SerializerMethodField()
    other_organizations = OtherOrganizationSerializer(many=True)
    attachments = ProjectAttachmentSerializer(many=True)
    can_import_section = serializers.SerializerMethodField()
    can_give_project_estimate_reason = serializers.SerializerMethodField()
    lead = LeadSerializer()

    @swagger_serializer_method(serializer_or_field=RoleMappingSerializer(many=True))
    def get_role_mappings(self, obj):
        if hasattr(obj, "role_mappings"):
            return self.RoleMappingSerializer(
                obj.role_mappings, many=True, context={"org_id": self.context["org_id"]}
            ).data
        return []

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_can_import_section(self, obj):
        return self.context.get("org_id") == self.context.get("creator_org_id") and section_import_check(
            org_id=self.context.get("creator_org_id"), project_id=obj.pk
        )

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_can_give_project_estimate_reason(self, obj):
        return self.context.get("can_give_project_estimate_reason", True)

    class Meta:
        ref_name = "ProjectDetailSerializerV2"
        fields = (
            "sections",
            "role_mappings",
            "lead",
            "attachments",
            "other_organizations",
            "can_import_section",
            "can_give_project_estimate_reason",
        )


class ConfigPrefetchCustomSectionSerializer(ProjectCustomSectionModelSerializer):
    class ConfigPrefetchCustomFieldSerializer(ProjectCustomFieldModelSerializer):
        class Meta(ProjectCustomFieldModelSerializer.Meta):
            ref_name = "ConfigPrefetchCustomFieldSerializer"
            fields = (
                "id",
                "uuid",
                "name",
                "is_required",
                "position",
                "type",
            )

    fields = ConfigPrefetchCustomFieldSerializer(many=True)

    class Meta(ProjectCustomSectionModelSerializer.Meta):
        ref_name = "ConfigPrefetchCustomSectionSerializer"
        fields = (
            "id",
            "name",
            "uuid",
            "position",
            "type",
            "fields",
        )


class ProjectCreateSystemFieldSerializer(BaseSerializer):
    name = serializers.CharField(max_length=250)
    client_id = serializers.IntegerField()
    business_category_id = serializers.IntegerField()
    address_line_1 = serializers.CharField()
    address_line_2 = serializers.CharField(allow_null=True, allow_blank=True)
    city_id = serializers.IntegerField(allow_null=True)
    state_id = serializers.IntegerField(allow_null=True)
    country_id = serializers.IntegerField()
    zip_code = serializers.CharField(max_length=10, allow_null=True, allow_blank=True)
    project_scope_ids = serializers.ListSerializer(child=HashIdField())
    estimated_cost = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    dealer_name = serializers.CharField(allow_null=True, allow_blank=True, max_length=100)
    dealer_phone_number = serializerfields.PhoneNumberField(allow_null=True, allow_blank=True)
    recce_due_at = CustomEndDateField(default=None, allow_null=True)
    kam_completion_due_at = CustomEndDateField(default=None, allow_null=True)
    expected_started_at = CustomDateField(default=None, allow_null=True)
    area = serializers.DecimalField(max_digits=15, decimal_places=5, default=0)
    currency_id = serializers.IntegerField()
    tax_type_id = serializers.IntegerField()
    timezone_id = serializers.IntegerField()

    class Meta:
        input_hash_id_fields = (
            "client_id",
            "business_category_id",
            "city_id",
            "state_id",
            "country_id",
            "currency_id",
            "tax_type_id",
            "timezone_id",
        )
        ref_name = "ProjectCreateSystemFieldSerializer"


def get_system_field_serializer(fields: list[str]):
    class DynamicSystemFieldSerializer(ProjectCreateSystemFieldSerializer):
        @property
        def _writable_fields(self):
            for name, field in self.fields.items():
                if not field.read_only and name in fields:
                    yield field

        class Meta(ProjectCreateSystemFieldSerializer.Meta):
            pass

    return DynamicSystemFieldSerializer


class ProjectSectionUpdateInputSerializer(BaseSerializer, CustomFieldDataValidatorMixin):
    field_data = serializers.DictField()
    system_fields = serializers.DictField(required=False, default={})  # not received by user
    custom_fields = serializers.DictField(required=False, default={})  # not received by user
    remarks = serializers.CharField(max_length=250, required=False, allow_blank=True, allow_null=True)

    def validate(self, obj):
        data = super().validate(obj)
        error_list = []
        custom_field_data = self.context.get("custom_field_data")
        system_field_data = self.context.get("system_field_data")

        project_data = self.validate_system_field_data(
            system_field_dict=system_field_data,
            error_list=error_list,
        )
        self.validate_custom_field_data(
            custom_field_dict=custom_field_data,
            field_uuid_data_mapping=self.context.get("field_uuid_data_mapping"),
            error_list=error_list,
        )
        if error_list:
            raise serializers.ValidationError(error_list)
        data["custom_fields"] = self.custom_field_serialized_data
        data["system_fields"] = project_data
        return data

    def validate_system_field_data(self, system_field_dict: dict, error_list: list):
        uuids = system_field_dict.keys()
        if set(uuids).intersection(set(READONLY_FIELD_UUIDS)):
            raise serializers.ValidationError("Readonly field cannot be updated.")
        fields = []

        for name, uuid in SYSTEM_FIELD_NAMES_UUID_MAPPING.items():
            if uuid in uuids:
                fields.append(name)

        serializer_obj: BaseSerializer = get_system_field_serializer(fields=fields)
        data = self.prepare_system_field_data(system_field_dict=system_field_dict, fields=fields)
        serializer = serializer_obj(data=data, context=self.context)
        if not serializer.is_valid(raise_exception=False):
            errors = serializer.errors
            errors = json.loads(json.dumps(errors))
            for field, error in errors.items():
                error_list.append({SYSTEM_FIELD_NAMES_UUID_MAPPING[field]: error})
        return serializer.validated_data

    def prepare_system_field_data(self, system_field_dict: dict, fields: list[str]):
        data = {}
        for field in fields:
            if field in [SystemFieldNameEnum.PROJECT_ESTIMATE.value, SystemFieldNameEnum.PROJECT_AREA.value]:
                data[field] = (
                    system_field_dict.get(SYSTEM_FIELD_NAMES_UUID_MAPPING[field])
                    if system_field_dict.get(SYSTEM_FIELD_NAMES_UUID_MAPPING[field])
                    else 0
                )
            else:
                data[field] = system_field_dict.get(SYSTEM_FIELD_NAMES_UUID_MAPPING[field])
        return data

    class Meta:
        ref_name = "ProjectSectionUpdateInputSerializer"


class CustomSectionImportListSerializer(ProjectCustomSectionSerializer):
    class FieldSerializer(ProjectCustomFieldSerializer):
        class Meta(ProjectCustomFieldSerializer.Meta):
            ref_name = "FieldSerializer"
            fields = (
                "id",
                "uuid",
                "name",
                "is_required",
                "position",
                "type",
                "actions",
                "is_archived",
            )

    fields = serializers.ListSerializer(child=FieldSerializer())

    class Meta(ProjectCustomSectionSerializer.Meta):
        ref_name = "SectionSerializer"


class ProjectDropdownFieldUpdateSerializer(BaseSerializer):
    fields = serializers.ListField(child=serializers.JSONField())

    class Meta:
        ref_name = "ProjectDropdownFieldUpdateSerializer"


class ProjectDropdownFieldSerializer(BaseSerializer):
    uuid = serializers.UUIDField()
    options = serializers.ListField(child=serializers.CharField())

    class Meta:
        ref_name = "ProjectDropdownFieldSerializer"


class ProjectPocRoleUpdateSerializer(BaseSerializer):
    poc_id = serializers.IntegerField()
    level = serializers.IntegerField()

    class Meta:
        ref_name = "ProjectPocRoleUpdateCallbackSerializer"
