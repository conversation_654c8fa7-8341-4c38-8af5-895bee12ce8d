from functools import partial
from operator import itemgetter
from typing import List, Union

import pytz
import structlog
from django.conf import settings
from django.db import transaction
from django.db.models import DecimalField, F, Prefetch, Q, QuerySet, Value
from django.db.models.functions import Coalesce, Concat
from django.utils import timezone
from django.utils.translation import gettext as _
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from phonenumber_field import serializerfields
from rest_framework import mixins, serializers
from rest_framework.exceptions import ValidationError
from rest_framework.mixins import ListModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_202_ACCEPTED,
    HTTP_204_NO_CONTENT,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
)

from authorization.domain.constants import Permissions
from authorization.domain.services import permission_categorize
from client.interface.serializers import ClientStoreTypeSerializer
from common.apis import BaseApi, BaseKeyProtectedApi, BaseOpenApi
from common.choices import OrderType, OrganizationType
from common.context_checker.context_checker_factory import ContextAllowedActionCheckerFactory
from common.entities import ContextCheckerData, ProjectOrganizationEntity
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer
from common.mixins import ExternalApiMixin
from common.serializers import (
    BaseDataclassSerializer,
    BaseKeyProtectedSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomEndDateField,
    HashIdField,
    HashIdListField,
    SplitCharHashIdListField,
)
from common.services import many_to_many_field_set
from common.utils import get_id_list_from_string, ordering_queryset
from core.apis import OrgBaseApi
from core.entities import CountryData, OrgIdProjectIdEntity, OrgUserEntity, ProjectUserEntity
from core.helpers import OrgPermissionHelper
from core.models import Organization, OrganizationConfig
from core.permissions import ExternalAppTokenPermission
from core.serializers import (
    OrganizationModelSerializer,
    OrganizationSerializer,
    UserProfileSerializer,
    UserSerializer,
)
from core.service.role import PmcRoleService
from element.data.selectors import client_fetch_all
from microcontext.choices import MicroContextActions
from microcontext.domain.constants import (
    MicroContext,
)
from project.data.choices import ProjectExpenseClosureChoices, SheetType
from project.data.filters import AppProjectListFilter, ProjectListFilter
from project.data.models import (
    BusinessCategory,
    Project,
    ProjectCustomFieldDropDownOption,
    ProjectOrgData,
    ProjectUser,
    Scope,
    Store,
)
from project.domain.caches import (
    ProjectAssignmentCache,
    ProjectCountryConfigCache,
    ProjectDataCache,
    ProjectJobIdCache,
    ProjectListConfigCache,
    ProjectSharedDataCache,
    UserCountryListCache,
    UserStateListCache,
)
from project.domain.constants import (
    PROJECT_CREATED_BY_UUID,
    PROJECT_NAME_UUID,
    PROJECT_SCHEDULE_PROGRESS_UUID,
    PROJECT_SCOPE_PROGRESS_UUID,
    PROJECT_STAGE_UUID,
)
from project.domain.entities import ColumnSettingData, ProjectCountryConfigData
from project.domain.exceptions import (
    ProjectConfigExceptions,
    ProjectCreationPrefillDataException,
    SectionUpdationNotPermittedException,
)
from project.domain.factories import ProjectServiceFactory
from project.domain.helpers import ProjectPermissionHelper
from project.domain.mappings import ProjectHoldLostMappings
from project.domain.services import (
    ProjectCreateService,
    ProjectCustomSectionImportService,
    ProjectCustomSectionService,
    ProjectSectionUpdateService,
    ProjectStatusUpdateService,
    assignment_role_data_prepare,
    check_context_permission,
    client_store_type_list_fetch,
    create_field_uuid_data_mapping,
    fetch_project_search_config,
    fetch_project_section_config,
    get_can_give_project_estimate_reason,
    get_child_organization_ids,
    get_client_role,
    get_creator_org_id,
    get_immediate_client_id,
    get_parent_organization_ids,
    get_project_client_id,
    get_project_data,
    get_project_role_mapping,
    job_id_generate,
    mark_unmark_expense_closure,
    project_archive_status_update,
    project_attachment_create,
    project_attachment_delete,
    project_attachment_update,
    project_client_assign,
    project_create,
    project_created_by_list,
    project_creation_config_fetch,
    project_date_events_trigger,
    project_detail_config_fetch,
    project_instance_fetch,
    project_list_fetch_for_created_by,
    project_list_fetch_for_roles,
    project_org_item_type_config_assignment,
    project_organization_assign,
    project_progress_shedule_create,
    project_progress_sheet_fetch,
    project_role_assignment_list,
    project_role_mappings_validate,
    project_sheet_create,
    project_sheet_fetch,
    project_stage_list,
    project_update,
    project_user_assign_many,
    project_user_remove_many,
    project_user_removed_trigger_event,
    segregate_system_and_custom_field_data,
    store_create,
    store_update,
    transform_project_data,
    update_project_poc_role_level,
    user_client_list,
)
from project.domain.status import Module, ProjectRestrictStatus, RDStatus
from project.interface.config_validator import ProjectCreateInputSerializer
from project.interface.permissions import ExternalProjectPermission, ProjectActionPermission, ProjectPermission
from project.selectors import (
    fetch_all_dropdown_options_on_projects,
    fetch_project_custom_field_config,
    get_project_organizations,
    get_project_users_using_project_id_and_organization_id,
    project_cities_fetch_all,
    project_fetch_all,
    project_field_history_fetch_all,
    project_list_add_created_by,
    project_users_with_organization_admins_fetch,
    section_import_config_fetch,
    store_field_history_fetch_all,
    user_currency_list_fetch,
    user_tax_list_fetch,
    user_timezone_list_fetch,
)
from project.serializers import (
    BusinessCategoryModelSerializer,
    CityStateModelSerializer,
    CustomSectionImportListSerializer,
    ProjectAttachmentSerializer,
    ProjectCustomFieldConfigInputSerializer,
    ProjectCustomFieldDropdownOptionModelSerializer,
    ProjectCustomSectionSerializer,
    ProjectDetailSerializerV2,
    ProjectDetailsSerializer,
    ProjectDropdownFieldUpdateSerializer,
    ProjectFieldHistoryModelSerializer,
    ProjectListSerializer,
    ProjectModelSerializer,
    ProjectPocRoleUpdateSerializer,
    ProjectProgressScheduleModelSerializer,
    ProjectSectionUpdateInputSerializer,
    ScopeSerializer,
    SearchConfigSerializer,
    StageSerializer,
    StoreFieldHistoryModelSerializer,
)
from project.share.domain.service.services import ProjectClientPocAssignmentService
from project.utils import FilteredFields, ProjectListFilterService, get_query_params, rd_status_dict
from rollingbanners.storage_backends import PublicMediaFileStorage

logger = structlog.get_logger(__file__)


class ProjectApiMixin:
    def get_project_id(self) -> int:
        if "project_id" not in self.kwargs and "boq_id" not in self.kwargs:
            raise Exception("project_id path param not found.")
        return self.kwargs.get("project_id", self.kwargs.get("boq_id"))

    def get_project_organization_ids(self) -> list[int]:
        organization_ids = [self.get_organization_id()]
        if not self.is_vendor():
            organization_ids.append(None)
        return organization_ids

    def get_creator_org_id(self) -> int:
        return get_creator_org_id(project_id=self.get_project_id(), organization_id=self.get_organization_id())

    def get_parent_organization_ids(self) -> list[int]:
        return get_parent_organization_ids(project_id=self.get_project_id(), organization_id=self.get_organization_id())

    def get_all_parent_organization_ids(self) -> list[int]:
        project_id = self.get_project_id()
        organization_id = self.get_organization_id()
        project_assignments = ProjectAssignmentCache.get(instance_id=project_id)

        def get_parent(ids, project_assignments, visited=None):
            if visited is None:
                visited = set()

            parents = []
            for i in ids:
                if i in visited:
                    continue
                visited.add(i)

                org_ids = list(project_assignments.get(str(i)).get("role_ids").keys())
                if len(org_ids) == 0:
                    continue

                parents.extend(org_ids)
                parents.extend(get_parent(org_ids, project_assignments, visited))

            return parents

        return get_parent([organization_id], project_assignments)

    def get_child_organization_ids(self) -> list[int]:
        return get_child_organization_ids(project_id=self.get_project_id(), organization_id=self.get_organization_id())

    def get_known_organization_ids(self, include_parent_orgs=True) -> list[int]:
        ids = []
        project_id = self.get_project_id()
        organization_id = self.get_organization_id()
        project_assignments = ProjectAssignmentCache.get(instance_id=project_id)
        for org_id, assignment in project_assignments.items():
            if str(organization_id) == org_id and include_parent_orgs:
                # parent orgs
                ids.extend(assignment["role_ids"].keys())
            elif str(organization_id) in assignment["role_ids"]:
                # child orgs
                ids.append(str(org_id))
        return [int(i) for i in set(ids) if int(i) != organization_id]

    def get_organization_filter(self) -> Q:
        organization_ids = self.get_project_organization_ids()
        if None in organization_ids:
            return Q(organization_id__in=organization_ids) | Q(organization_id__isnull=True)

        return Q(organization_id__in=organization_ids)

    def is_vendor(self) -> bool:
        return ProjectDataCache.get(
            project_details=ProjectOrganizationEntity(
                project_id=self.get_project_id(), organization_id=self.get_organization_id()
            )
        ).is_vendor

    def is_pure_client(self) -> bool:
        return self.get_organization_id() == self.get_project_client_id()

    def get_vendor_organization_ids(self) -> list[int]:
        client_id = ProjectDataCache.get(
            project_details=ProjectOrganizationEntity(
                project_id=self.get_project_id(), organization_id=self.get_organization_id()
            )
        ).client_id

        if client_id != self.get_organization_id():
            vendor_ids = self.get_known_organization_ids(include_parent_orgs=False)
            if client_id in vendor_ids:
                vendor_ids.remove(client_id)
        else:
            vendor_ids = self.get_known_organization_ids(include_parent_orgs=True)

        return vendor_ids

    def get_project_client_id(self) -> int:
        return get_project_client_id(project_id=self.get_project_id(), organization_id=self.get_organization_id())

    def get_user_permissions(self):
        return ProjectPermissionHelper.get_permissions(self.get_project_id(), user=self.request.user)

    def get_immediate_client_id(self) -> Union[int, None]:
        return get_immediate_client_id(project_id=self.get_project_id(), organization_id=self.get_organization_id())

    def set_project_timezone(self):
        self.set_timezone(self.get_project_timezone())

    def get_project_timezone(self) -> pytz.BaseTzInfo:
        return pytz.timezone(ProjectCountryConfigCache.get(instance_id=self.get_project_id()).timezone.name)

    def get_input_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_project_timezone()
        return context

    def get_output_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_project_timezone()
        return context

    def get_filter_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_project_timezone()
        return context

    def set_timezone(self, pytz_timezone: pytz.BaseTzInfo):
        timezone.activate(pytz_timezone)

    def get_project_shared_data(self):
        return ProjectSharedDataCache.get(
            key=OrgIdProjectIdEntity(
                project_id=self.get_project_id(),
                org_id=self.get_organization_id(),
            )
        )


class ProjectBaseApi(ProjectApiMixin, BaseApi):
    REQUIRED_PERMISSIONS = None  # Do Not Touch : Please do not change it to empty list
    permission_classes = [IsAuthenticated, ProjectPermission]

    def get_input_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_project_timezone()
        return context

    def get_project_user_entity(self) -> ProjectUserEntity:
        return ProjectUserEntity(
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            project_id=self.get_project_id(),
        )


class ExternalProjectBaseV2Api(ProjectApiMixin, BaseApi, ExternalApiMixin):
    permission_classes = [IsAuthenticated, ExternalProjectPermission]

    def get_input_context(self):
        context = self.get_serializer_context()
        context["timezone"] = self.get_project_timezone()
        return context

    def get_project_user_entity(self) -> ProjectUserEntity:
        return ProjectUserEntity(
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            project_id=self.get_project_id(),
        )


class ProjectActionBaseApi(ProjectApiMixin, BaseApi):
    permission_classes = [IsAuthenticated, ProjectActionPermission]
    context_action = None
    context_key = None
    context = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.context_id = None

    def get_resource_context(self):
        if not self.context:
            raise Exception("Context not defined")

        return self.context

    def get_context_action(self):
        if not self.context_action:
            raise Exception("Action not defined")
        return self.context_action

    def get_context_id(self):
        if not self.context_key:
            return None
        return self.kwargs.get(self.context_key)


# This API is created to be inherited by APIs that have job_id in their URL parameters.
class ExternalProjectBaseAPI(ProjectBaseApi):
    permission_classes = [IsAuthenticated, ProjectPermission, ExternalAppTokenPermission]

    def get_project_id(self) -> int:
        return self.kwargs.get("project_id")

    def dispatch(self, request, *args, **kwargs):
        if "job_id" not in kwargs:
            raise Exception("job_id path param not found.")
        try:
            kwargs["project_id"] = ProjectJobIdCache.get(instance_id=kwargs.get("job_id"))
        except Project.DoesNotExist:
            self.headers = self.default_response_headers
            self.set_response_message("Job Id not found.")
            return self.finalize_response(request, Response(status=HTTP_404_NOT_FOUND), *args, **kwargs)
        return super().dispatch(request, *args, **kwargs)


class BusinessCategoryListApi(BaseApi, mixins.ListModelMixin):
    class OutputSerializer(BusinessCategoryModelSerializer):
        class Meta(BusinessCategoryModelSerializer.Meta):
            fields = ("id", "name")
            output_hash_id_fields = ("id",)
            ref_name = "BusinessCategoryListOutput"

    serializer_class = OutputSerializer
    pagination_class = None
    queryset = BusinessCategory.objects.all()

    def get_queryset(self):
        return super().get_queryset().filter(organization_id=self.get_organization_id())

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_business_category_list",
        operation_summary="Project business category list Api.",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


# NOTE: Deprecated
class ProjectCreateApi(BaseApi):
    class InputSerializer(BaseSerializer):
        name = serializers.CharField(max_length=250)
        store_type_id = serializers.CharField()
        business_category_id = serializers.CharField(default=None, required=False, allow_null=True)
        store_address = serializers.CharField(max_length=250)
        store_city = serializers.CharField(max_length=100)
        store_state = serializers.IntegerField()
        project_scope_ids = HashIdListField()
        estimated_cost = serializers.DecimalField(max_digits=12, decimal_places=2, min_value=1)
        dealer_name = serializers.CharField(allow_null=True, allow_blank=True, max_length=100)
        dealer_phone_number = serializerfields.PhoneNumberField(allow_null=True, allow_blank=True)
        key_account_manager_ids = HashIdListField()
        project_manager_ids = HashIdListField()
        design_leads_ids = HashIdListField()
        production_manager_ids = HashIdListField()

        class Meta:
            input_hash_id_fields = ("store_type_id", "business_category_id")
            ref_name = "ProjectCreateInput"

    class OutputSerializer(ProjectModelSerializer):
        # TODO: confirm with FE about output
        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectCreateOutput"
            fields = ("id",)
            output_hash_id_fields = ("id",)

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="project_create",
        operation_summary="Create new project.",
        deprecated=True,
    )
    @transaction.atomic
    def post(self, request, client_id, *args, **kwargs):
        data = self.validate_input_data()

        project = project_create(
            user=request.user,
            name=data.get("name"),
            client_id=client_id,
            estimated_cost=data.get("estimated_cost"),
            store_type_id=data.get("store_type_id"),
            business_category_id=data.get("business_category_id"),
            organization_id=self.get_organization_id(),
        )

        many_to_many_field_set(obj=project, key="scopes", value=data.get("project_scope_ids"))

        store_create(
            project_id=project.pk,
            address=data.get("store_address"),
            city=data.get("store_city"),
            state=data.get("store_state"),
            dealer_name=data.get("dealer_name"),
            dealer_phone_number=data.get("dealer_phone_number"),
            area=0,
        )

        project.job_id = job_id_generate(client_id=client_id)
        project.save(update_fields=["job_id"])
        return Response(self.OutputSerializer(project).data, status=HTTP_201_CREATED)


class ProjectFieldHistoryFetchApi(BaseOpenApi):
    pagination_class = None

    class OutputSerializer(ProjectFieldHistoryModelSerializer):
        class Meta(ProjectFieldHistoryModelSerializer.Meta):
            fields = ("prev_value", "value", "updated_at", "updated_by", "is_initial", "reason")
            ref_name = "ProjectFieldHistoryFetchOutput"

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="project_field_history",
        operation_summary="Get project field history.",
    )
    def get(self, request, project_id, field, *args, **kwargs):
        if field == "project_created_on":
            project = Project.objects.get(pk=project_id)
            response = [
                {
                    "prev_value": None,
                    "value": project.created_at,
                    "updated_at": project.created_at,
                    "updated_by": UserProfileSerializer(project.created_by).data,
                    "is_initial": True,
                }
            ]
        else:
            project_field_histories = project_field_history_fetch_all(project_id=project_id, field=field)
            response = self.OutputSerializer(project_field_histories, many=True, context={"field": field}).data
            response.reverse()
        return Response(response, status=HTTP_200_OK)


class StoreFieldHistoryFetchApi(BaseOpenApi):
    pagination_class = None

    class OutputSerializer(StoreFieldHistoryModelSerializer):
        class Meta(StoreFieldHistoryModelSerializer.Meta):
            fields = ("prev_value", "value", "updated_at", "updated_by", "is_initial")
            ref_name = "StoreFieldHistoryFetchOutput"

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="project_field_history",
        operation_summary="Get project field history.",
    )
    def get(self, request, project_id, field, *args, **kwargs):
        store_field_histories = store_field_history_fetch_all(project_id=project_id, field=field)
        response = self.OutputSerializer(store_field_histories, many=True, context={"field": field}).data
        response.reverse()
        return Response(response, status=HTTP_200_OK)


# NOTE: Deprecated
class ProjectCreateV2Api(BaseApi):
    class InputSerializer(BaseSerializer):
        class RoleMapping(BaseSerializer):
            user_id = serializers.CharField()
            role_id = serializers.CharField()

            class Meta:
                ref_name = "RoleMappingV2"
                input_hash_id_fields = ("user_id", "role_id")

        name = serializers.CharField(max_length=250)
        business_category_id = serializers.CharField(default=None, required=False, allow_null=True)
        store_address = serializers.CharField(max_length=250)
        store_city = serializers.CharField(max_length=100)
        store_state = serializers.IntegerField()
        project_scope_ids = HashIdListField()
        estimated_cost = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
        dealer_name = serializers.CharField(allow_null=True, allow_blank=True, max_length=100)
        dealer_phone_number = serializerfields.PhoneNumberField(allow_null=True, allow_blank=True)
        recce_due_at = serializers.DateTimeField(default=None, allow_null=True)
        kam_completion_due_at = serializers.DateTimeField(default=None, allow_null=True)
        expected_started_at = serializers.DateTimeField(default=None, allow_null=True)
        role_mappings = serializers.ListField(child=RoleMapping())
        store_area = serializers.DecimalField(max_digits=15, decimal_places=5)

        class Meta:
            input_hash_id_fields = ("store_type_id", "business_category_id")
            ref_name = "ProjectCreateV2Input"

    class OutputSerializer(ProjectModelSerializer):
        redirect = serializers.BooleanField(default=False)

        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectCreateV2Output"
            fields = ("id", "redirect")
            output_hash_id_fields = ("id",)

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="project_create_v2",
        operation_summary="Create new project.",
    )
    @transaction.atomic
    def post(self, request, client_id, *args, **kwargs):
        data = self.validate_input_data()
        org_id = self.get_organization_id()

        project = project_create(
            user=request.user,
            name=data.get("name"),
            client_id=client_id,
            estimated_cost=data.get("estimated_cost"),
            store_type_id=data.get("store_type_id"),
            business_category_id=data.get("business_category_id"),
            recce_due_at=data.get("recce_due_at"),
            expected_started_at=data.get("expected_started_at"),
            kam_completion_due_at=data.get("kam_completion_due_at"),
            organization_id=self.get_organization_id(),
        )

        many_to_many_field_set(obj=project, key="scopes", value=data.get("project_scope_ids"))

        store_create(
            project_id=project.pk,
            address=data.get("store_address"),
            city=data.get("store_city"),
            state=data.get("store_state"),
            dealer_name=data.get("dealer_name"),
            dealer_phone_number=data.get("dealer_phone_number"),
            area=data.get("store_area"),
            user_id=request.user.pk,
        )
        if client_id != org_id:
            # adding pmc to project
            pmc_role = PmcRoleService.create_project_organization_role(
                name="PMC",
                created_by_id=request.user.pk,
                organization_id=org_id,
                type=OrganizationType.PMC,
            )

            project_organization_assign(
                organization_id=org_id,
                project_id=project.pk,
                assigned_by_organization_id=org_id,
                created_by_id=request.user.pk,
                role_id=pmc_role.pk,
            )
            project_org_item_type_config_assignment(
                assignee_org_id=org_id,
                project_id=project.pk,
                creator_id=self.get_user_id(),
                assigner_org_id=org_id,
            )

        # adding client to project
        client_role = get_client_role(
            organization_id=org_id,
            created_by_id=request.user.pk,
        )

        project_client_assign(
            organization_id=client_id,
            project_id=project.pk,
            assigned_by_organization_id=org_id,
            created_by_id=request.user.pk,
            role_id=client_role.pk,
        )

        ProjectClientPocAssignmentService(
            project=project, organization_id=client_id, created_by=request.user
        ).assign_project_to_primary_poc()

        OrganizationConfig.objects.get_or_create(organization_id=client_id)

        validated_role_mappings = get_project_role_mapping(data.get("role_mappings"))
        redirect = False
        for role_id in validated_role_mappings:
            if str(request.user.pk) in validated_role_mappings.get(role_id):
                redirect = True
            project_user_assign_many(
                role_id=role_id,
                user_ids=validated_role_mappings.get(role_id),
                project=project,
                user=request.user,
                organization_id=org_id,
            )
        project.job_id = job_id_generate(client_id=client_id)
        project.save(update_fields=["job_id"])

        response = dict(self.OutputSerializer(project).data)
        response["redirect"] = redirect
        return Response(response, status=HTTP_201_CREATED)


class ProjectUserAssignManyApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        class RoleMapping(BaseSerializer):
            user_id = serializers.CharField()
            role_id = serializers.CharField()

            class Meta:
                input_hash_id_fields = ("user_id", "role_id")
                ref_name = "ProjectUserRoleMapping"

        role_mappings = serializers.ListField(child=RoleMapping())

        class Meta:
            input_hash_id_fields = ("store_type_id", "business_category_id")
            ref_name = "ProjectUserAssignManyInput"

    class OutputSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectUserAssignManyOutput"
            fields = ("id",)
            output_hash_id_fields = ("id",)

    input_serializer_class = InputSerializer
    queryset = Project.objects.all()
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_202_ACCEPTED: OutputSerializer()},
        operation_id="project_user_assign_many",
        operation_summary="Assign multiple users to project with roles.",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        project: Project = self.get_object()
        organization_id = self.get_organization_id()
        validated_role_mappings = project_role_mappings_validate(
            data.get("role_mappings"), project_id=project.pk, org_id=organization_id, user=self.get_user()
        )
        role_ids = []
        for role_id in validated_role_mappings:
            if project_user_assign_many(
                role_id=role_id,
                user_ids=validated_role_mappings.get(role_id),
                project=project,
                user=request.user,
                organization_id=organization_id,
            ):
                role_ids.append(role_id)

        return Response(self.OutputSerializer(project).data, status=HTTP_201_CREATED)


class StoreDetailsUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        name = serializers.CharField()
        client_id = serializers.CharField()
        store_type_id = serializers.CharField(default=None, required=False, allow_null=True)
        business_category_id = serializers.CharField(default=None, required=False, allow_null=True)
        address = serializers.CharField(max_length=250)
        city = serializers.CharField(max_length=100)
        state = serializers.IntegerField()

        class Meta:
            input_hash_id_fields = ("client_id", "store_type_id", "business_category_id")
            ref_name = "StoreDetailsUpdateInput"

    class OutputSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            fields = ("id", "job_id")
            ref_name = "StoreDetailsUpdateOutput"
            output_hash_id_fields = ("id",)

    input_serializer_class = InputSerializer
    queryset = Store.objects.select_related("project").all()
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_202_ACCEPTED: OutputSerializer()},
        operation_id="update_store_details",
        operation_summary="Update store details.",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        instance: Store = self.get_object()
        project, _ = project_update(project=instance.project, data=data, updated_by_id=request.user.pk)

        store_update(store=instance, data=data, updated_by_id=request.user.pk)
        return Response(self.OutputSerializer(project).data, status=HTTP_202_ACCEPTED)


class ProjectScopeUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        project_scope_ids = HashIdListField()
        estimated_cost = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
        store_area = serializers.DecimalField(max_digits=15, decimal_places=5)

        class Meta:
            ref_name = "ScopeUpdateInputSerializer"

    input_serializer_class = InputSerializer
    queryset = Project.objects.all().prefetch_related("store")
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_204_NO_CONTENT: ""},
        operation_id="project_scope_and_cost_update",
        operation_summary="Project scope and estimated cost update",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        instance: Project = self.get_object()
        store_area = data.pop("store_area")
        store = instance.store
        store, _ = store_update(store=store, data={"area": store_area}, updated_by_id=request.user.pk)
        project_update(project=instance, data=data, updated_by_id=request.user.pk)

        return Response(status=HTTP_204_NO_CONTENT)


class StoreDealerDetailsUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        dealer_name = serializers.CharField(max_length=100, allow_null=True, allow_blank=True)
        dealer_phone_number = serializerfields.PhoneNumberField(allow_null=True, allow_blank=True)

        class Meta:
            ref_name = "DealerDetailsUpdateInputSerializer"

    input_serializer_class = InputSerializer
    queryset = Store.objects.select_related("project").all()
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_204_NO_CONTENT: ""},
        operation_id="store_dealer_details_update",
        operation_summary="Dealer name and contact update",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        instance = self.get_object()
        store_update(store=instance, data=data, updated_by_id=request.user.pk)
        return Response(status=HTTP_204_NO_CONTENT)


class ProjectDatesUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        recce_due_at = CustomEndDateField(default=None, allow_null=True)
        kam_completion_due_at = CustomEndDateField(default=None, allow_null=True)
        expected_started_at = CustomEndDateField(default=None, allow_null=True)
        reason = serializers.CharField(allow_null=True, required=False, default=None)

        class Meta:
            ref_name = "ProjectDatesUpdateInputSerializer"

    input_serializer_class = InputSerializer
    queryset = Project.objects.all()
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_204_NO_CONTENT: ""},
        operation_id="project_dates_update",
        operation_summary="Project important dates update",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        instance = self.get_object()

        perv_recce_due_at = instance.recce_due_at
        prev_kam_completion_due_at = instance.kam_completion_due_at
        prev_expected_started_at = instance.expected_started_at
        project, fields = project_update(project=instance, data=data, updated_by_id=request.user.pk)
        project_date_events_trigger(
            fields=fields,
            project=project,
            perv_recce_due_at=perv_recce_due_at,
            prev_kam_completion_due_at=prev_kam_completion_due_at,
            prev_expected_started_at=prev_expected_started_at,
            changed_by=request.user,
        )

        return Response(status=HTTP_204_NO_CONTENT)


class ProjectUserRemoveV2Api(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        user_id = serializers.CharField()
        role_id = serializers.CharField()

        class Meta:
            input_hash_id_fields = ("user_id", "role_id")
            ref_name = "ProjectUserRemoveV2InputSerializer"

    input_serializer_class = InputSerializer
    queryset = Project.objects.all()
    lookup_url_kwarg = "project_id"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_204_NO_CONTENT: "No Data"},
        operation_id="remove_user_v2",
        operation_summary="Remove User V2",
    )
    @transaction.atomic
    def delete(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        instance = self.get_object()
        project_user_remove_many(user_ids=[data.get("user_id")], project_id=instance.pk, role_id=data.get("role_id"))

        ######## Project User removed Event Trigger ############
        transaction.on_commit(
            partial(
                project_user_removed_trigger_event,
                project=instance,
                removed_by=request.user,
                recipient_ids=[data.get("user_id")],
            )
        )
        self.set_response_message("User removed successfully.")
        return Response(status=HTTP_200_OK)


class ProjectUserCountryListApi(BaseApi):
    serializer_class = CountryData.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: CountryData.drf_serializer(many=True)},
        operation_id="project_user_country_list",
        operation_summary="User's Project Country List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = UserCountryListCache.get(user=request.user)
        return Response(self.serializer_class(data, many=True).data, status=HTTP_200_OK)


class ProjectUserStateListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectStateListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_204_NO_CONTENT: OutputSerializer(many=True)},
        operation_id="project_user_state_list",
        operation_summary="User's Project State List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = UserStateListCache.get(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectCurrencyListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectCurrencyListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_currency_list",
        operation_summary="Project Currency List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = user_currency_list_fetch(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectTaxListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectTaxListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_tax_list",
        operation_summary="Project Tax List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = user_tax_list_fetch(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectTimezoneListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectTimezoneListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_timezone_list",
        operation_summary="Project Timezone List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = user_timezone_list_fetch(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectUserClientListApi(BaseApi):
    class OutputSerializer(OrganizationSerializer):
        class Meta(OrganizationSerializer.Meta):
            output_hash_id_fields = ("id",)
            ref_name = "ProjectClientListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_204_NO_CONTENT: OutputSerializer(many=True)},
        operation_id="project_client_list",
        operation_summary="Project Client List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = user_client_list(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectStageListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectStageListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_204_NO_CONTENT: OutputSerializer(many=True)},
        operation_id="project_stage_list",
        operation_summary="Project Stage List for Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = project_stage_list(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectAssignmentUserListApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        role_id = serializers.CharField(required=True)

        class Meta:
            ref_name = "ProjectRoleFilterSerializer"
            input_hash_id_fields = ("role_id",)

    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            output_hash_id_fields = ("id",)
            ref_name = "ProjectAssignmentUserListOutputSerializer"

    # serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_204_NO_CONTENT: OutputSerializer(many=True)},
        operation_id="project_assignment_list",
        operation_summary="Project Assignment List Filter",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        input_data = self.validate_filter_data()
        data = project_role_assignment_list(user=request.user, role_id=input_data.get("role_id"))
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectSearchConfigListApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: SearchConfigSerializer(many=True)},
        operation_id="project_search_config_api",
        operation_summary="Project Search Config",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = fetch_project_search_config(user=request.user)
        return Response(data, status=HTTP_200_OK)


class ProjectStoreTypeListApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: ClientStoreTypeSerializer(many=True)},
        operation_id="project_user_store_type_list_api",
        operation_summary="List of all Store Type for project",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = client_store_type_list_fetch(user=request.user)
        return Response(data, status=HTTP_200_OK)


#  NOTE: deprecated
class ProjectListApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        name = serializers.CharField(required=False)
        client = serializers.CharField(required=False)
        city = serializers.CharField(required=False)
        stage = SplitCharHashIdListField(required=False)
        job_id = serializers.CharField(required=False)
        role_list = serializers.CharField(required=False)
        state = serializers.CharField(required=False)
        ordering = serializers.CharField(required=False)
        business_category = SplitCharHashIdListField(required=False)
        is_archived = serializers.BooleanField(required=False)

        class Meta:
            ref_name = "ProjectFilterSerializer"

    filter_backends = [DjangoFilterBackend]

    filterset_class = ProjectListFilter
    ordering_dict = {
        "id": "id",
        "name": "name",
        "job_id": "job_id",
        "city": "store__city",
        "client_name": "client__name",
        "probable_estimate": "estimated_cost",
        "recce_due_at": "recce_due_at",
        "recce_completed_at": "recce_completed_at",
        "kam_completion_due_at": "kam_completion_due_at",
        "ops_completion_due_at": "ops_completion_due_at",
        "execution_completed_at": "execution_completed_at",
        "created_at": "created_at",
    }
    ordering = ["-id"]

    class OutputSerializer(ProjectListSerializer):
        class Meta(ProjectListSerializer.Meta):
            output_hash_id_fields = ("id",)
            ref_name = "ProjectListOutputSerializer"

    @swagger_auto_schema(
        request_body=FilterSerializer(),
        responses={HTTP_204_NO_CONTENT: OutputSerializer(many=True)},
        operation_id="project_list_api",
        operation_summary="Project List Api with Filter",
    )
    def get_queryset(self):
        data = self.validate_filter_data()
        query_param_data = self.request.query_params
        # Passed Organization_id
        project_data = (
            project_fetch_all(user=self.request.user)
            .select_related("client", "store", "store_type", "organization", "business_category")
            .order_by("-created_at")
            .annotate_project_status(organization_id=self.get_organization_id())
            .annotate_project_is_archived(organization_id=self.get_organization_id())
        )

        project_data = project_list_add_created_by(queryset=project_data)

        project_data = project_list_fetch_for_roles(
            queryset=project_data, role_list=data.get("role_list"), query_param_data=query_param_data
        )
        project_data = project_list_fetch_for_created_by(queryset=project_data, query_param_data=query_param_data)
        project_data = ordering_queryset(
            ordering_fields=data.get("ordering"), allowed_fields_mapping=self.ordering_dict, queryset=project_data
        )
        return project_data

    @transaction.atomic
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(queryset=self.get_queryset())
        page = self.paginate_queryset(queryset)
        org_id = request.user.token_data.org_id if request.user.token_data else None
        has_project_permission = OrgPermissionHelper.has_permission(
            request.user, Permissions.CAN_ACCESS_PROJECT_ATTACHMENT
        )
        if page is not None:
            assignment_data = assignment_role_data_prepare(project_queryset=page, org_id=org_id)
            serializer = self.OutputSerializer(
                page,
                many=True,
                context={
                    "assignment_data": assignment_data,
                    "organization_id": self.get_organization_id(),
                    "has_project_permission": has_project_permission,
                },
            )
            return self.get_paginated_response(serializer.data)

        assignment_data = assignment_role_data_prepare(project_queryset=queryset, org_id=org_id)
        serializer = self.OutputSerializer(
            queryset,
            many=True,
            context={
                "assignment_data": assignment_data,
                "organization_id": self.get_organization_id(),
                "has_project_permission": has_project_permission,
            },
        )

        return Response(serializer.data, status=HTTP_200_OK)


class ProjectPermissionApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectListSerializer):
        permissions = serializers.DictField(child=serializers.ListField(child=serializers.CharField()))
        permission_list = serializers.ListField(child=serializers.CharField())

        class Meta(ProjectListSerializer.Meta):
            fields = ("permissions", "permission_list")
            output_hash_id_fields = ("id",)
            ref_name = "ProjectPermissionOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_permission_list",
        operation_summary="Project Permission List",
    )
    def get(self, request, project_id, *args, **kwargs):
        permission_list = ProjectPermissionHelper.get_permissions(project_id=project_id, user=request.user)
        permissions = permission_categorize(permission_list)
        return Response({"permissions": permissions, "permission_list": permission_list}, status=HTTP_200_OK)


class ProjectBusinessCategoryListApi(ProjectBaseApi, mixins.ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BusinessCategoryModelSerializer):
        class Meta(BusinessCategoryModelSerializer.Meta):
            fields = ("id", "name")
            output_hash_id_fields = ("id",)
            ref_name = "BusinessCategx`oryListOutput"

    serializer_class = OutputSerializer
    pagination_class = None
    queryset = BusinessCategory.objects.all()

    def get_queryset(self):
        org_id = Project.objects.filter(id=self.kwargs.get("pk")).values_list("organization_id", flat=True)
        return super().get_queryset().filter(organization_id__in=org_id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_business_category_list",
        operation_summary="Project business category list Api.",
    )
    def get(self, request, pk, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class ProjectRestrictApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        status = serializers.ChoiceField(choices=ProjectRestrictStatus.choices)

        class Meta:
            ref_name = "ProjectRestrictionInputSerializer"

    input_serializer_class = InputSerializer
    pagination_class = None

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "No Data"},
        operation_id="project_restrict_to_hold_lost_rectification",
        operation_summary="Restrict Project to HOLD, LOST and Rectification and also to RESUME,"
        " Restore, and Make Rectification Complete",
    )
    @transaction.atomic
    def post(self, request, project_id, **kwargs):
        data = self.validate_input_data()

        status = ProjectHoldLostMappings.get_project_status_value_from_string(data.get("status"))

        ProjectStatusUpdateService.process(
            project_id=project_id,
            module=Module.PROJECT.value,
            status=status,
            organization_id=self.get_organization_id(),
            user_id=request.user.pk,
        )

        return Response(status=HTTP_200_OK)


class ProjectArchiveApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        is_archived = serializers.BooleanField()

        class Meta:
            ref_name = "ProjectArchiveInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="archive_project",
        operation_summary="Archive Project",
    )
    @transaction.atomic
    def put(self, request, project_id, **kwargs):
        data = self.validate_input_data()
        project_archive_status_update(
            project_id=project_id,
            user_id=request.user.pk,
            org_id=self.get_organization_id(),
            is_archived=data.get("is_archived"),
        )
        return Response(status=HTTP_200_OK)


class ProjectAttachmentUploadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        url = serializers.URLField()
        name = serializers.CharField()

        class Meta:
            ref_name = "ProjectAttachmentInputSerializer"

    class OutputSerializer(ProjectAttachmentSerializer):
        class Meta(ProjectAttachmentSerializer.Meta):
            fields = (
                "id",
                "file",
                "name",
                "uploaded_by",
                "uploaded_at",
            )
            ref_name = "ProjectAttachmentOutputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: " OutputSerializer"},
        operation_id="project_attachment_create",
        operation_summary="Create Project attachment",
    )
    @transaction.atomic
    def post(self, request, project_id, **kwargs):
        data = self.validate_input_data()

        attachment_obj = project_attachment_create(data=data, project_id=project_id, user_id=request.user.pk)

        return Response(self.OutputSerializer(attachment_obj).data, status=HTTP_200_OK)


class ProjectAttachmentUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        name = serializers.CharField()

        class Meta:
            ref_name = "ProjectAttachmentUpdateSerializer"

    class OutputSerializer(ProjectAttachmentSerializer):
        class Meta(ProjectAttachmentSerializer.Meta):
            fields = (
                "id",
                "file",
                "name",
                "uploaded_by",
                "updated_by",
                "uploaded_at",
                "updated_at",
            )
            ref_name = "ProjectAttachmentUpdateOutputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_attachment_update",
        operation_summary="Update Project Attachment",
    )
    @transaction.atomic
    def put(self, request, project_id, attachment_id, **kwargs):
        data = self.validate_input_data()

        attachment_obj = project_attachment_update(
            data=data, attachment_id=attachment_id, project_id=project_id, user_id=request.user.pk
        )

        return Response(self.OutputSerializer(attachment_obj).data, status=HTTP_200_OK)


class ProjectAttachmentDeleteApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="project_attachment_remove",
        operation_summary="remove Project attachment",
    )
    @transaction.atomic
    def delete(self, request, project_id, attachment_id, *args, **kwargs):
        project_attachment_delete(attachment_id=attachment_id, user_id=request.user.pk)
        return Response(status=HTTP_200_OK)


class ProjectCreatedByListApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        id = HashIdField()
        name = serializers.CharField()
        organization_name = serializers.CharField()
        photo = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.URLField())
        def get_photo(self, obj):
            return PublicMediaFileStorage.url(obj["photo"]) if obj["photo"] else None

        class Meta:
            ref_name = "ProjectCreatedByListApiOutput"
            output_hash_id_fields = ()

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_created_by_list",
        operation_summary="Project Created By List",
    )
    def get(self, request, *args, **kwargs):
        data = project_created_by_list(user=request.user)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class ProjectDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    lookup_url_kwarg = "project_id"

    class OutputSerializer(ProjectDetailsSerializer):
        stage = serializers.SerializerMethodField()
        org_id = HashIdField()
        progress_percentage = serializers.IntegerField()
        recce_due_at = serializers.SerializerMethodField()
        expected_started_at = serializers.SerializerMethodField()
        kam_completion_due_at = serializers.SerializerMethodField()
        timezone = serializers.SerializerMethodField()
        currency = serializers.SerializerMethodField()
        tax_type = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=StageSerializer())
        @staticmethod
        def get_stage(obj):
            return StageSerializer(rd_status_dict(obj)).data if obj.rd_status else {}

        def get_timezone(self, obj):
            return self.context.get("config").get("timezone")

        def get_currency(self, obj):
            return self.context.get("config").get("currency")

        def get_tax_type(self, obj):
            return self.context.get("config").get("tax_type")

        def get_recce_due_at(self, obj):
            return obj.recce_due_at.date() if obj.recce_due_at else None

        def get_expected_started_at(self, obj):
            return obj.expected_started_at.date() if obj.expected_started_at else None

        def get_kam_completion_due_at(self, obj):
            return obj.kam_completion_due_at.date() if obj.kam_completion_due_at else None

        class Meta(ProjectDetailsSerializer.Meta):
            ref_name = "ProjectDetailsApiOutput"
            fields = (
                "id",
                "name",
                "job_id",
                "client",
                "stage",
                "permissions",
                "permission_list",
                "immediate_client_id",
                "org_id",
                "progress_percentage",
                "recce_due_at",
                "expected_started_at",
                "kam_completion_due_at",
                "timezone",
                "currency",
                "tax_type",
            )

    def get_queryset(self):
        return (
            project_fetch_all(user=self.request.user)
            .select_related("client")
            .annotate_project_status(organization_id=F("organization_id"))
            .annotate_project_progress_percentage(organization_id=self.get_organization_id())
        )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_details",
        operation_summary="Project Details",
    )
    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        permissions = ProjectPermissionHelper.get_permissions(project_id=instance.pk, user=request.user)
        permissions_categorize = permission_categorize(permissions=permissions)
        immediate_client_id = self.get_immediate_client_id()
        setattr(instance, "permissions", permissions_categorize)
        setattr(instance, "permission_list", permissions)
        setattr(instance, "immediate_client_id", immediate_client_id)
        setattr(instance, "org_id", self.get_organization_id())
        config: ProjectCountryConfigData = ProjectCountryConfigData.drf_serializer(
            ProjectCountryConfigCache.get(instance_id=instance.pk)
        ).data
        return Response(self.OutputSerializer(instance, context={"config": config}).data, status=HTTP_200_OK)


class ProjectOriginCityStateMappingApi(BaseApi):
    class OutputSerializer(BaseSerializer):
        class CityStateSerializer(CityStateModelSerializer):
            class Meta(CityStateModelSerializer.Meta):
                fields = ("city", "state")

        class Meta:
            ref_name = "ProjectOriginCityStateOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_city_state_mapping_list",
        operation_summary="Project city state mapping list",
    )
    def get(self, request):
        city_state_mapping = project_cities_fetch_all()
        city_list = []
        for mapping in city_state_mapping:
            city_list.append({"city": mapping.city, "state": mapping.state})
        sorted_cities = sorted(city_list, key=itemgetter("city"))
        return Response(self.OutputSerializer.CityStateSerializer(sorted_cities, many=True).data)


class ProjectProgressScheduleCreateApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        organization_id = serializers.IntegerField()
        sheet_url = serializers.URLField()
        api_key = serializers.CharField()
        job_id = serializers.CharField()

        @staticmethod
        def validate_api_key(value):
            if value != settings.RECCE_RATING_UPDATE_KEY:
                raise serializers.ValidationError(_("Api key is not valid."))

        class Meta:
            ref_name = "ProjectProgressScheduleCreateInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="project_progress_schedule_create",
        operation_summary="Project Progress Schedule Sheet Create",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        project_id = Project.objects.filter(job_id=data.get("job_id")).values_list("id", flat=True).first()
        if project_id is None:
            raise ValidationError({"job_id": _("Job id not found")})
        project_progress_shedule_create(
            project_id=project_id, org_id=data.get("organization_id"), sheet_url=data.get("sheet_url")
        )

        return Response(status=HTTP_200_OK)


class ProjectProgressScheduleSheetGetApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectProgressScheduleModelSerializer):
        class Meta(ProjectProgressScheduleModelSerializer.Meta):
            fields = ("sheet_url",)
            ref_name = "ProjectProgressScheduleOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_progress_schedule_get",
        operation_summary="Project Progress Schedule Sheet",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        data = project_progress_sheet_fetch(project_id=project_id, org_id=self.get_organization_id())
        if data is None:
            return Response(None, status=HTTP_404_NOT_FOUND)

        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class ProjectSheetCreateApi(BaseKeyProtectedApi):
    class InputSerializer(BaseKeyProtectedSerializer):
        organization_id = serializers.IntegerField()
        sheet_url = serializers.URLField()
        sheet_type = serializers.ChoiceField(choices=SheetType.choices)
        job_id = serializers.CharField()

        class Meta:
            ref_name = "ProjectSheetCreateInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="project_sheet_create",
        operation_summary="Project Sheet Create",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        if data.get("sheet_type") == SheetType.BOQ_VS_ORDER_SHEET.value:
            logger.info("Creation not allowed for boq vs order sheet")
            return Response(status=HTTP_200_OK)
        project_id = Project.objects.filter(job_id=data.get("job_id")).values_list("id", flat=True).first()
        if project_id is None:
            raise ValidationError({"job_id": _("Job id not found")})
        project_sheet_create(
            project_id=project_id,
            org_id=data.get("organization_id"),
            sheet_url=data.get("sheet_url"),
            sheet_type=data.get("sheet_type"),
        )

        return Response(status=HTTP_200_OK)


class ProjectSheetGetApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        type = serializers.ChoiceField(
            choices=SheetType.choices,
            required=True,
        )

        class Meta:
            ref_name = "ProjetSheetFilterSerializer"

    filter_serializer_class = FilterSerializer

    class OutputSerializer(BaseSerializer):
        sheet_url = serializers.URLField()

        class Meta:
            ref_name = "ProjectSheetOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_sheet_fecth",
        operation_summary="Project Sheet Fetch",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        data = self.validate_filter_data()
        if data.get("sheet_type") == SheetType.BOQ_VS_ORDER_SHEET.value:
            logger.info("Retrieval not allowed for boq vs order sheet")
            return Response(data=None, status=HTTP_404_NOT_FOUND)
        data = project_sheet_fetch(
            project_id=project_id, org_id=self.get_organization_id(), sheet_type=data.get("type")
        )
        if data is None:
            return Response(None, status=HTTP_404_NOT_FOUND)

        return Response(self.OutputSerializer({"sheet_url": data}).data, status=HTTP_200_OK)


class AppProjectListApi(BaseApi, ListModelMixin):
    class FilterSerializer(BaseSerializer):
        search_query = serializers.CharField(required=False)
        stage = SplitCharHashIdListField(required=False)
        client = SplitCharHashIdListField(required=False)
        created_by = SplitCharHashIdListField(required=False)
        role_list = serializers.CharField(required=False)
        country = SplitCharHashIdListField(required=False)
        ordering = serializers.CharField(required=False)

        class Meta:
            ref_name = "AppProjectFilterSerializer"

    filter_backends = [DjangoFilterBackend]
    filterset_class = AppProjectListFilter

    ordering_fields = [
        "id",
        "name",
        "estimated_cost",
        "created_at",
        "kam_completion_due_at",
        "execution_completed_at",
    ]

    class OutputSerializer(ProjectListSerializer):
        progress_percentage = serializers.IntegerField(default=0)

        class Meta(ProjectListSerializer.Meta):
            fields = ("id", "name", "job_id", "city", "state", "stage", "kam_completion_due_at", "progress_percentage")
            output_hash_id_fields = ("id",)
            ref_name = "AppProjectListOutputSerializer"

    serializer_class = OutputSerializer

    def get_serializer_context(self):
        return {"organization_id": self.get_organization_id()}

    @swagger_auto_schema(
        request_body=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="app_project_list_api",
        operation_summary="Project List Api for app",
    )
    def get_queryset(self):
        data = self.validate_filter_data()
        org_from_id = self.get_organization_id()
        query_param_data = self.request.query_params

        prefetch_project_org = Prefetch("project_org_data", ProjectOrgData.objects.filter(organization_id=org_from_id))

        project_data: QuerySet = (
            project_fetch_all(user=self.request.user)
            .select_related(
                "client", "store", "store__city", "store__state", "store_type", "organization", "config__timezone"
            )
            .prefetch_related(prefetch_project_org)
            .order_by("-created_at")
            .annotate_project_status(organization_id=org_from_id)
            .annotate_project_is_archived(organization_id=self.get_organization_id())
            .annotate_project_progress_percentage(organization_id=self.get_organization_id())
            .annotate_project_created_by()
        )
        if not data.get("stage"):
            project_data = project_data.filter(is_archived=False)
        project_data = project_list_fetch_for_roles(
            queryset=project_data, role_list=data.get("role_list"), query_param_data=query_param_data
        )

        ordering = data.get("ordering")
        ordering = ordering.strip() if ordering else None
        if ordering and ordering.strip("-") in self.ordering_fields:
            if len(ordering.split(",")) > 1:
                raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Only One Ordering Field allowed")})

            if "-" in ordering:
                return project_data.order_by(F(ordering.strip("-")).desc(nulls_last=True))
            else:
                return project_data.order_by(F(ordering).asc(nulls_last=True))

        return project_data.order_by("-id")

    @transaction.atomic
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class ProjectCustomFieldUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        ui_schema = serializers.JSONField(default=None, allow_null=True)
        form_schema = serializers.JSONField(default=None, allow_null=True)
        form_data = serializers.JSONField(default=None, allow_null=True)

        class Meta:
            ref_name = "ProjectCustomFieldUpdateInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        operation_id="project_custom_field_update",
        operation_summary="Project Custom Field Config And Data Update",
    )
    @transaction.atomic
    def put(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        Project.objects.filter(id=project_id).update(
            ui_schema=data.get("ui_schema"), form_schema=data.get("form_schema"), form_data=data.get("form_data")
        )
        return Response(status=HTTP_200_OK)


class ContextPermissionCheckApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        context = serializers.ChoiceField(
            required=True,
            choices=[
                MicroContext.EXPENSE.value,
                MicroContext.INVOICE.value,
                MicroContext.PAYMENT_REQUEST.value,
                MicroContext.INSTA_ORDER.value,
                MicroContext.ORDER.value,
            ],
        )
        type = serializers.ChoiceField(choices=OrderType.choices, default=OrderType.OUTGOING.value, required=False)
        action = serializers.CharField(required=False)
        context_id = HashIdField(required=False)

        class Meta:
            ref_name = "ContextPermissionCheckFilterSerializer"

    filter_serializer_class = FilterSerializer

    class OutputSerializer(BaseSerializer):
        is_allowed = serializers.BooleanField(required=True)

        class Meta:
            ref_name = "ContextPermissionCheckOutputSerializer"

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_summary="Context Permission Check Api",
        operation_id="check_context_permission",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        data = self.validate_filter_data()
        context = data.get("context")
        context_type = data.get("type")
        action = data.get("action")

        if context in [MicroContext.EXPENSE.value, MicroContext.ORDER.value, MicroContext.PAYMENT_REQUEST.value]:
            if context in [MicroContext.EXPENSE.value, MicroContext.ORDER.value]:
                action = MicroContextActions.CREATE.value
            elif context == MicroContext.PAYMENT_REQUEST.value:
                if context_type == OrderType.OUTGOING.value:
                    context = MicroContext.VENDOR_PAYMENT_REQUEST.value
                else:
                    context = MicroContext.CLIENT_PAYMENT_REQUEST.value

            checker = ContextAllowedActionCheckerFactory().get_checker(
                context=context,
                action=action,
                data=ContextCheckerData(
                    project_id=project_id,
                    user=request.user,
                    organization_id=self.get_organization_id(),
                    context_id=data.get("context_id"),
                ),
            )
            is_allowed = checker.check_if_action_allowed()
            if is_allowed:
                self.set_response_message("Has permission to access resource")
                return Response(status=HTTP_200_OK, data=self.OutputSerializer({"is_allowed": True}).data)
        elif check_context_permission(
            context=context,
            user=self.get_user(),
            org_id=self.get_organization_id(),
            project_id=project_id,
            type=data.get("type"),
        ):
            self.set_response_message("Has permission to access resource")
            return Response(status=HTTP_200_OK, data=self.OutputSerializer({"is_allowed": True}).data)

        self.set_response_message("Not has permission to access resource")
        return Response(status=HTTP_200_OK, data=self.OutputSerializer({"is_allowed": False}).data)


class ProjectContextPermissionActionCheckApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        context = serializers.ChoiceField(
            required=True,
            choices=[
                MicroContext.EXPENSE.value,
                MicroContext.ORDER.value,
                MicroContext.PROJECT.value,
                MicroContext.PROPOSAL.value,
                MicroContext.CLIENT_PAYMENT_REQUEST.value,
                MicroContext.VENDOR_PAYMENT_REQUEST.value,
            ],
        )
        action = serializers.CharField(required=True)

        context_id = HashIdField(required=False)

        class Meta:
            ref_name = "ProjectContextPermissionActionCheckFilterSerializer"

    filter_serializer_class = FilterSerializer

    class OutputSerializer(BaseSerializer):
        body = TypeOutputSerializer(
            many=True,
            context={
                "allowed_type_choices": {
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.BULLET: None,
                }
            },
        )
        next_action = serializers.CharField()
        message_type = serializers.CharField()

        class Meta:
            ref_name = "ProjectContextPermissionActionCheckOutputSerializer"

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_summary="Project Context Permission Action Check Api",
        operation_id="check_project_context_permission_action",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        data = self.validate_filter_data()

        checker = ContextAllowedActionCheckerFactory().get_checker(
            context=data.get("context"),
            action=data.get("action"),
            data=ContextCheckerData(
                project_id=project_id,
                user=request.user,
                organization_id=self.get_organization_id(),
                context_id=data.get("context_id"),
            ),
        )
        json_message, is_allowed = checker.check()

        return Response(status=HTTP_200_OK, data=self.OutputSerializer(json_message).data)


class ProjectUserAndOrganisationAdminListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_ORDER]

    class OutputSerializer(UserSerializer):
        class Meta(UserSerializer.Meta):
            fields = ("id", "name")
            ref_name = "ProjectUserAndOrganisationAdminListOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_and_organisation_admin_list_api",
        operation_summary="Get List  of project users and organisation admins.",
    )
    def get(self, request, project_id, *args, **kwargs):
        project_users_and_organization_admins: QuerySet = project_users_with_organization_admins_fetch(
            project_id=project_id, org_id=self.get_organization_id()
        )
        self.set_response_message("Project users and organisation admins list fetch successfully.")
        response = self.OutputSerializer(project_users_and_organization_admins, many=True).data
        return Response(response, status=HTTP_200_OK)


class ProjectCustomFieldConfigFetchApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = ProjectCustomSectionSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ProjectCustomSectionSerializer()},
        operation_id="project_custom_field_config_fetch_api",
        operation_summary="Project Custom Field Config Fetch Api",
    )
    def get(self, request, *args, **kwargs):
        config_data = fetch_project_section_config(org_id=self.get_organization_id())
        self.set_response_message("Project custom field config fetched successfully.")
        return Response(
            config_data,
            status=HTTP_200_OK,
        )


class ProjectCustomFieldConfigUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = ProjectCustomFieldConfigInputSerializer
    serializer_class = ProjectCustomSectionSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ProjectCustomSectionSerializer()},
        operation_id="project_custom_field_config_update_api",
        operation_summary="Project Custom Field Config Update Api",
    )
    @transaction.atomic()
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            ProjectCustomSectionService(org_id=self.get_organization_id(), user_id=self.get_user_id()).update(
                sections=data.get("sections")
            )
            config = fetch_project_custom_field_config(org_id=self.get_organization_id())
            self.set_response_message("Project custom field config updated successfully.")
            return Response(
                self.serializer_class(config, many=True).data,
                status=HTTP_200_OK,
            )
        except (
            ProjectConfigExceptions.InvalidProjectConfigException,
            ProjectCustomSectionService.ProjectCustomSectionServiceException,
        ) as e:
            self.set_response_message(e.message)
            raise e


class ProjectCustomFieldDeleteCheckApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        field_uuids = serializers.ListField(child=serializers.UUIDField(), required=True, allow_empty=True)
        dropdown_uuids = serializers.ListField(child=serializers.UUIDField(), required=True, allow_empty=True)

        class Meta:
            ref_name = "ProjectCustomFieldDeleteCheckInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "{'can_delete':True}"},
        operation_id="project_custom_field_delete_check_api",
        operation_summary="Project Custom Field Delete Check Api",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        if len(data.get("field_uuids")):
            can_delete = ProjectCustomSectionService(
                org_id=self.get_organization_id(), user_id=self.get_user_id()
            ).check_field_deletion(field_uuids=data.get("field_uuids"))
        else:
            can_delete = ProjectCustomSectionService(
                org_id=self.get_organization_id(), user_id=self.get_user_id()
            ).check_dropdown_option_deletion(dropdown_uuids=data.get("dropdown_uuids"))
        logger.info(can_delete=can_delete)
        return Response({"can_delete": can_delete}, status=HTTP_200_OK)


class ProjectCreationConfigPrefetchApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        operation_id="project_creation_config_prefetch_api",
        operation_summary="Project Creation Config Prefetch Api",
    )
    def get(self, request, *args, **kwargs):
        try:
            config_and_role_data = ProjectCreateService(
                org_id=self.get_organization_id(), user=self.get_user()
            ).prefetch_project_config_and_roles()
        except ProjectCreationPrefillDataException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            config_and_role_data,
            status=HTTP_200_OK,
        )


class ProjectCreateApiV3(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        project_id = HashIdField()

        class Meta:
            ref_name = "ProjectCreateOutputSerializer"

    input_serializer_class = ProjectCreateInputSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        sections = project_creation_config_fetch(org_id=self.get_organization_id())
        context["timezone"] = self.get_organization_timezone()
        context["field_uuid_data_mapping"] = create_field_uuid_data_mapping(section_list=sections)
        return context

    @swagger_auto_schema(
        request_body=ProjectCreateInputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_create_v3",
        operation_summary="Project Create V3",
    )
    @transaction.atomic()
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        project_id = ProjectCreateService(org_id=self.get_organization_id(), user=self.get_user()).create_project(
            data=data
        )

        return Response(
            self.OutputSerializer({"project_id": project_id}).data,
            status=HTTP_200_OK,
        )


class ProjectDetailApiV2(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    serializer_class = ProjectDetailSerializerV2

    @swagger_auto_schema(
        responses={HTTP_200_OK: ProjectDetailSerializerV2()},
        operation_id="project_detail_v2",
        operation_summary="Project Detail V2",
    )
    def get(self, request, project_id, *args, **kwargs):
        user = self.get_user()
        timezone = self.get_project_timezone()
        org_id = self.get_organization_id()
        creator_org_id = self.get_creator_org_id()
        project_shared_data = self.get_project_shared_data()

        user_permissions = ProjectPermissionHelper.get_permissions(project_id=project_id, user=user)
        user_org_permissions = OrgPermissionHelper.get_permissions(user=user)
        can_view_advance_section = (
            Permissions.CAN_ACCESS_PROJECT_ATTACHMENT in user_permissions
            and Permissions.CAN_ACCESS_PROJECT_ATTACHMENT in user_org_permissions
        )
        config_sections: List[dict] = project_detail_config_fetch(
            org_id=creator_org_id, project_id=project_id, basic_section_only=(not can_view_advance_section)
        )
        project = project_instance_fetch(user=user, project_id=project_id)
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=project_id)
        project_data = get_project_data(
            project=project,
            org_id=org_id,
            sections=config_sections,
            parent_org_ids=self.get_parent_organization_ids(),
            child_org_ids=self.get_child_organization_ids(),
            user_permissions=user_permissions,
            project_config=project_config,
            timezone=timezone,
        )
        user_entity = OrgUserEntity(user_id=user.pk, org_id=self.get_organization_id())

        factory = ProjectServiceFactory(user=user, user_entity=user_entity)

        lead_service = factory.get_lead_service()

        project_data.lead = lead_service.get_lead_details_from_project_id(project_id=project_id)

        can_give_project_estimate_reason = get_can_give_project_estimate_reason(project_id=project_id)
        return Response(
            self.serializer_class(
                project_data,
                context={
                    "creator_org_id": creator_org_id,
                    "org_id": org_id,
                    "can_give_project_estimate_reason": can_give_project_estimate_reason,
                    "timezone": timezone,
                    "project_shared_data": project_shared_data,
                },
            ).data,
            status=HTTP_200_OK,
        )


class CustomFieldDropDownOptionsFetchApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectCustomFieldDropdownOptionModelSerializer):
        class Meta(ProjectCustomFieldDropdownOptionModelSerializer.Meta):
            ref_name = "OutputSerializer"
            fields = ("id", "name")
            output_hash_id_fields = ["id"]

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="custom_field_dropdown_options_fetch",
        operation_summary="Custom Field Dropdown Options Fetch",
    )
    def get(self, request, field_id, *args, **kwargs):
        try:
            dropdown_options = ProjectCustomSectionService(
                org_id=self.get_organization_id(), user_id=self.get_user_id()
            ).fetch_dropdown_options(field_id=field_id)
        except ProjectCustomSectionService.ProjectCustomSectionServiceException as e:
            self.set_response_message(e.message)
            raise e
        return Response(self.serializer_class(dropdown_options, many=True).data, status=HTTP_200_OK)


class CustomFieldDropDownOptionsInProjectFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectCustomFieldDropdownOptionModelSerializer):
        class Meta(ProjectCustomFieldDropdownOptionModelSerializer.Meta):
            ref_name = "OutputSerializer"
            fields = ("id", "name")
            output_hash_id_fields = ["id"]

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="custom_field_dropdown_options_in_project_fetch",
        operation_summary="Custom Field Dropdown Options in Project Fetch",
    )
    def get(self, request, project_id, field_id, *args, **kwargs):
        try:
            dropdown_options = ProjectCustomSectionService(
                org_id=self.get_creator_org_id(), user_id=self.get_user_id()
            ).fetch_dropdown_options(field_id=field_id)
        except ProjectCustomSectionService.ProjectCustomSectionServiceException as e:
            self.set_response_message(e.message)
            raise e
        return Response(self.serializer_class(dropdown_options, many=True).data, status=HTTP_200_OK)


class ProjectSectionUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_PROJECT]

    input_serializer_class = ProjectSectionUpdateInputSerializer

    queryset = Store.objects.select_related("project").all()
    lookup_url_kwarg = "project_id"

    def get_serializer_context(self):
        context = super().get_serializer_context()
        sections = project_detail_config_fetch(org_id=self.get_creator_org_id(), project_id=self.get_project_id())
        system_field_data, custom_field_data = segregate_system_and_custom_field_data(
            self.request.data.get("field_data")
        )
        context["field_uuid_data_mapping"] = create_field_uuid_data_mapping(section_list=sections)
        context["system_field_data"] = system_field_data
        context["custom_field_data"] = custom_field_data
        return context

    @swagger_auto_schema(
        request_body=ProjectSectionUpdateInputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="project_section_update_api",
        operation_summary="Project Section Update Api",
    )
    @transaction.atomic
    def put(self, request, project_id, section_id, *args, **kwargs):
        data = self.validate_input_data()
        is_permitted = ProjectPermissionHelper.has_permission(
            project_id=project_id, user=self.get_user(), permission=Permissions.CAN_EDIT_PROJECT
        )
        if not is_permitted:
            raise SectionUpdationNotPermittedException("Action not permitted.")
        store: Store = self.get_object()
        try:
            ProjectSectionUpdateService(
                org_id=self.get_creator_org_id(), user=self.get_user(), project_id=project_id
            ).update(data=data, store=store)
        except ProjectSectionUpdateService.InvalidFieldDataException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("Project section updated successfully.")
        return Response(status=HTTP_200_OK)


class ProjectCustomSectionImportListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    serializer_class = CustomSectionImportListSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: CustomSectionImportListSerializer()},
        operation_id="project_custom_section_import_list",
        operation_summary="Project Custom Section Import List Api",
    )
    def get(self, request, project_id, *args, **kwargs):
        sections = section_import_config_fetch(org_id=self.get_creator_org_id(), project_id=project_id)
        return Response(self.serializer_class(sections, many=True).data, status=HTTP_200_OK)


class ProjectCustomSectionImportApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        field_uuids = serializers.ListField(child=serializers.UUIDField(), required=True, allow_empty=False)

        class Meta:
            ref_name = "InputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "Project custom section imported successfully."},
        operation_id="project_custom_section_import",
        operation_summary="Project Custom Section Import Api",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        ProjectCustomSectionImportService(
            org_id=self.get_organization_id(), user=self.get_user(), project_id=project_id
        ).import_section(field_uuids=data.get("field_uuids"))
        self.set_response_message("Project custom section imported successfully.")
        return Response(status=HTTP_200_OK)


class ProjectParentOrganizationListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationModelSerializer):
        class Meta(UserSerializer.Meta):
            fields = ("id", "name")
            ref_name = "ProjectParentOrganizationOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_parent_organization_api",
        operation_summary="Project Element Library Org Api",
    )
    def get_queryset(self):
        parent_org_ids = self.get_all_parent_organization_ids()
        parent_org_ids.append(self.get_project_client_id())
        return client_fetch_all(user=self.get_user()).filter(id__in=parent_org_ids)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self_org = self.get_user().org
        self_org.name = self_org.name + " (My Organization)"
        data = self.OutputSerializer(queryset, many=True).data + [self.OutputSerializer(self_org).data]
        return Response(data, status=HTTP_200_OK)


class ScopeListApi(BaseApi, mixins.ListModelMixin):
    class OutputSerializer(ScopeSerializer):
        class Meta(ScopeSerializer.Meta):
            fields = ("id", "name")
            output_hash_id_fields = ("id",)
            ref_name = "ScopeListOutput"

    serializer_class = OutputSerializer
    pagination_class = None
    queryset = Scope.objects.all().order_by("created_at")

    def get_queryset(self):
        return super().get_queryset().filter(organization_id=self.get_organization_id())

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_Scope_list",
        operation_summary="Project Scope list Api.",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class ProjectScopeListApi(ProjectBaseApi, mixins.ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ScopeSerializer):
        class Meta(ScopeSerializer.Meta):
            fields = ("id", "name")
            output_hash_id_fields = ("id",)
            ref_name = "ScopeListOutput"

    serializer_class = OutputSerializer
    pagination_class = None
    queryset = Scope.objects.all()

    def get_queryset(self):
        org_id = Project.objects.filter(id=self.kwargs.get("project_id")).values_list("organization_id", flat=True)
        return super().get_queryset().filter(organization_id__in=org_id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="project_scope_list",
        operation_summary="Project scope list Api.",
    )
    def get(self, request, project_id, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class MarkUnmarkProjectExpenseClosureApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        action = serializers.ChoiceField(choices=ProjectExpenseClosureChoices.choices, required=True)

        class Meta:
            ref_name = "MarkUnmarkProjectExpenseClosureApiInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="mark_unmark_project_expense_closure",
        operation_summary="Mark/Unmark Project Expense Closure",
    )
    @transaction.atomic
    def put(self, request, project_id, *args, **kwargs):
        action = self.validate_input_data().get("action")

        context = ContextAllowedActionCheckerFactory().get_checker(
            context=MicroContext.PROJECT.value,
            action=(
                MicroContextActions.UNMARK_EXPENSE_CLOSURE.value
                if action == ProjectExpenseClosureChoices.UNMARK
                else MicroContextActions.MARK_EXPENSE_CLOSURE.value
            ),
            data=ContextCheckerData(
                project_id=project_id,
                user=self.get_user(),
                organization_id=self.get_organization_id(),
                context_id=None,
            ),
        )

        json_message, is_allowed = context.check()

        if is_allowed:
            mark_unmark_expense_closure(
                project_id=project_id,
                organization_id=self.get_organization_id(),
                action=action,
                user_id=self.get_user_id(),
            )
        else:
            if isinstance(json_message, str):
                self.set_response_message(json_message)
                return Response(status=HTTP_400_BAD_REQUEST)
            else:
                self.set_response_message(f"{action} Action not allowed for Project Expense Closure")

            return Response(status=HTTP_400_BAD_REQUEST)

        return Response(status=HTTP_200_OK)


class ProjectListColumnSettingApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseDataclassSerializer):
        class Meta:
            dataclass = ColumnSettingData
            ref_name = "ProjectListColumnSettingSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_list_column_setting",
        operation_summary="Project List Column Setting Api",
    )
    def get(self, request, *args, **kwargs):
        column_setting = ProjectListConfigCache.get(instance_id=self.get_organization_id())
        permissions = OrgPermissionHelper.get_permissions(user=self.get_user())
        visible_columns = []
        for column in column_setting:
            if column.id not in [PROJECT_SCOPE_PROGRESS_UUID, PROJECT_SCHEDULE_PROGRESS_UUID]:
                visible_columns.append(column)
                continue
            elif (
                column.id == PROJECT_SCOPE_PROGRESS_UUID
                and Permissions.CAN_VIEW_SCOPE_PROGRESS_ON_PROJECT_LIST in permissions
            ):
                visible_columns.append(column)
            elif (
                column.id == PROJECT_SCHEDULE_PROGRESS_UUID
                and Permissions.CAN_VIEW_SCHEDULE_PROGRESS_ON_PROJECT_LIST in permissions
            ):
                visible_columns.append(column)
        return Response(self.OutputSerializer(visible_columns, many=True).data, status=HTTP_200_OK)


class ProjectListDropdownOptionApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        field_id = serializers.CharField(required=True)

        class Meta:
            ref_name = "ProjectRoleFilterSerializer"

    class OutputSerializer(BaseSerializer):
        uuid = serializers.UUIDField()
        name = serializers.CharField()

        class Meta:
            ref_name = "ProjectListDropdownOptionOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_list_dropdown_option",
        operation_summary="Project List Dropdown Option Api",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        dropdown_options: QuerySet[ProjectCustomFieldDropDownOption] = fetch_all_dropdown_options_on_projects(
            field_uuid=data.get("field_id"), org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(dropdown_options, many=True).data, status=HTTP_200_OK)


class ProjectListApiV2(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectListSerializer):
        def to_representation(self, instance):
            data = super().to_representation(instance)
            project_data = transform_project_data(
                project=instance,
                data=data,
                has_project_permission=self.context.get("has_project_permission"),
                user_permissions=self.context.get("user_permissions"),
            )
            return project_data

        class Meta(ProjectListSerializer.Meta):
            ref_name = "ProjectListOutputSerializer"

    def filter_queryset(self, queryset):
        query_params = get_query_params(input_params=self.request.query_params.copy())
        if "limit" in query_params:
            query_params.pop("limit")
        if "offset" in query_params:
            query_params.pop("offset")

        if PROJECT_STAGE_UUID in query_params:
            query_params[PROJECT_STAGE_UUID] = get_id_list_from_string(value=query_params.get(PROJECT_STAGE_UUID))
            if RDStatus.PROJECT_ARCHIVED.value in query_params.get(PROJECT_STAGE_UUID):
                queryset = queryset.filter(is_archived=True)
                query_params.get(PROJECT_STAGE_UUID).remove(RDStatus.PROJECT_ARCHIVED.value)
            else:
                queryset = queryset.filter(is_archived=False)
        else:
            # default case for project list
            queryset = queryset.filter(is_archived=False)

        column_setting = ProjectListConfigCache.get(instance_id=self.get_organization_id())
        service = ProjectListFilterService()
        filtered_fields: FilteredFields = service.fetch_filter_fields(
            query_param=query_params, column_setting=column_setting
        )

        if filtered_fields.system_fields:
            system_fields = service.convert_system_field_uuid_to_name(system_fields=filtered_fields.system_fields)
            filter_dict = service.create_system_field_filter(system_fields=system_fields)
            queryset = queryset.filter(**filter_dict)
            if PROJECT_NAME_UUID in filtered_fields.system_fields:
                queryset = service.filter_on_name(queryset=queryset, value=system_fields.get("name"))
            if PROJECT_CREATED_BY_UUID in filtered_fields.system_fields:
                queryset = project_list_fetch_for_created_by(
                    queryset=queryset, user_id_list=system_fields.get("created_by")
                )

        if filtered_fields.custom_fields:
            queryset = service.filter_on_custom_fields(queryset=queryset, custom_fields=filtered_fields.custom_fields)

        if filtered_fields.role_fields:
            queryset = project_list_fetch_for_roles(
                queryset=queryset, role_list=filtered_fields.role_fields.keys(), query_param_data=query_params
            )

        if filtered_fields.ordering_field:
            if filtered_fields.ordering_field.is_system_field:
                queryset = service.sort_on_system_fields(queryset=queryset, field=filtered_fields.ordering_field)
            else:
                queryset = service.sort_on_custom_fields(queryset=queryset, field=filtered_fields.ordering_field)

        return queryset

    def get_queryset(self):
        projects = (
            project_fetch_all(user=self.request.user)
            .prefetch_text_data()
            .prefetch_decimal_data()
            .prefetch_date_data()
            .prefetch_phonenumber_data()
            .prefetch_dropdown_data()
            .annotate_project_progress_percentage(organization_id=self.get_organization_id())
            .select_related(
                "client",
                "store",
                "store__state",
                "store__city",
                "store__country",
                "store_type",
                "organization",
                "business_category",
                "config",
                "config__currency",
                "config__timezone",
                "config__tax_type",
            )
            .annotate(
                schedule_progress=Coalesce(
                    F("schedule__completion_percentage"), Value(0.0), output_field=DecimalField()
                )
            )
            .order_by("-created_at")
            .annotate_project_status(organization_id=self.get_organization_id())
            .annotate_project_is_archived(organization_id=self.get_organization_id())
        )
        projects = project_list_add_created_by(queryset=projects)
        return projects

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_list_v2",
        operation_summary="Project List V2 Api",
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(queryset=self.get_queryset())
        page = self.paginate_queryset(queryset)
        user_permissions = OrgPermissionHelper.get_permissions(user=self.get_user())

        has_project_permission = Permissions.CAN_ACCESS_PROJECT_ATTACHMENT in user_permissions
        assignment_data = assignment_role_data_prepare(project_queryset=page, org_id=self.get_organization_id())
        serializer = self.OutputSerializer(
            page,
            many=True,
            context={
                "assignment_data": assignment_data,
                "organization_id": self.get_organization_id(),
                "has_project_permission": has_project_permission,
                "user_permissions": user_permissions,
            },
        )
        return self.get_paginated_response(serializer.data)


class ProjectDropdownFieldUpdateApi(BaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = ProjectDropdownFieldUpdateSerializer

    @swagger_auto_schema(
        request_body=ProjectDropdownFieldUpdateSerializer(),
        responses={HTTP_200_OK: ""},
        operation_id="project_dropdown_field_update_api",
        operation_summary="Project Dropdown Field Update Api",
    )
    @transaction.atomic()
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            ProjectCustomSectionService(
                org_id=self.get_organization_id(), user_id=self.get_user_id()
            ).create_dropdown_options(fields_data=data.get("fields"))
        except ProjectCustomSectionService.ProjectCustomSectionServiceException as e:
            self.set_response_message(str(e))
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Dropdown options updated successfully")

        return Response(status=HTTP_200_OK)


class ProjectPocRoleUpdateApi(BaseOpenApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        data = ProjectPocRoleUpdateSerializer()

        class Meta:
            ref_name = "ProjectPocRoleUpdateInputSerializer"

    input_serializer_class = InputSerializer

    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        poc_data_dict = data.get("data")
        logger.info("Updating POC Role Level", poc_data_dict)
        update_project_poc_role_level(poc_id=poc_data_dict.get("poc_id"), level=poc_data_dict.get("level"))
        return Response(status=HTTP_200_OK)


class ProjectConfigFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = ProjectCountryConfigData.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ProjectCountryConfigData.drf_serializer},
        operation_id="project_config_fetch_api",
        operation_summary="Project Config Fetch Api",
    )
    def get(self, request, project_id, *args, **kwargs):
        # TODO: add permission_list in swagger schema
        user = self.get_user()
        project_user_permissions = ProjectPermissionHelper.get_permissions(project_id=project_id, user=user)
        project_config = ProjectCountryConfigCache.get(instance_id=project_id)
        response_data = self.serializer_class(project_config).data
        response_data["permission_list"] = project_user_permissions
        self.set_response_message("Project config fetched successfully.")
        return Response(response_data, status=HTTP_200_OK)


class ProjectUsersListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseModelSerializer):
        id = serializers.CharField(source="user.id")
        name = serializers.CharField(source="user.name")
        photo = serializers.ImageField(source="user.photo")

        class Meta:
            model = ProjectUser
            fields = ("id", "name", "photo")

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: serializer_class},
        operation_id="project_schedule_users_list_api",
    )
    @transaction.atomic
    def get(self, request, project_id: int):
        project_users = (
            get_project_users_using_project_id_and_organization_id(
                project_id=project_id, organization_id=self.get_organization_id()
            )
            .select_related("user")
            .annotate(full_name=Concat("user__first_name", Value(" "), "user__last_name"))
            .order_by("full_name")
            .distinct("user__id", "full_name")
        )
        return Response(self.serializer_class(project_users, many=True).data, status=HTTP_200_OK)


class ProjectOrganizationsListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationModelSerializer):
        is_order_given = serializers.SerializerMethodField()

        def get_is_order_given(self, obj):
            return obj.is_order_given

        class Meta:
            ref_name = "ProjectOrganizationsListApiOutput"
            model = Organization
            ref_name = "ProjectOrganizationsListApiOutput"
            fields = ("id", "name", "logo", "is_order_given")

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: serializer_class},
        operation_id="project_schedule_activity_organizations_list_api",
    )
    @transaction.atomic
    def get(self, request, project_id: int):
        project_organizations = get_project_organizations(
            project_id=project_id, org_id=self.get_organization_id(), child_org_ids=self.get_child_organization_ids()
        )
        return Response(self.serializer_class(project_organizations, many=True).data, status=HTTP_200_OK)
