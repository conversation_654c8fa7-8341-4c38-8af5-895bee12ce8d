from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.serializers import BaseSerializer
from project.domain.services import project_header_data_fetch
from project.domain.status import Module
from project.interface.apis.internal.apis import ProjectB<PERSON><PERSON><PERSON>
from project.selectors import project_status_history_list
from project.serializers import ProjectHeaderSerializer, ProjectStatusHistorySerializer


class ProjectHeaderFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(responses={HTTP_200_OK: ProjectHeaderSerializer()}, operation_id="project_header_fetch_api")
    def get(self, request, project_id, *args, **kwargs):
        # Passed Organization_id in project header
        project_header = project_header_data_fetch(
            project_id=project_id,
            organization_id=self.get_organization_id(),
            user=request.user,
        )
        return Response(ProjectHeaderSerializer(project_header).data, status=HTTP_200_OK)


class ProjectStatusHistoryFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        module = serializers.ChoiceField(choices=Module.choices, required=False, allow_null=True)

        class Meta:
            ref_name = "ProjectStatusHistoryFilter"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(), responses={HTTP_200_OK: ProjectStatusHistorySerializer(many=True)}
    )
    def get(self, request, project_id, *args, **kwargs):
        data = self.validate_filter_data()
        organization_id = self.get_organization_id()
        known_organization_ids = self.get_known_organization_ids()
        project_shared_data = self.get_project_shared_data()

        known_organization_ids.append(organization_id)
        status_history_list = project_status_history_list(project_id=project_id, organization_id=organization_id)
        status_history_list = status_history_list.filter(module=data.get("module", Module.PROJECT.value))

        return Response(
            ProjectStatusHistorySerializer(
                status_history_list,
                many=True,
                context={
                    "known_organization_ids": known_organization_ids,
                    "project_shared_data": project_shared_data,
                },
            ).data,
            status=HTTP_200_OK,
        )
