import uuid

from django.conf import settings
from django.contrib.gis.db import models
from django.core.exceptions import ValidationError
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from phonenumber_field import modelfields

from authorization.domain.constants import Actions
from client.data.models import StoreType
from common.choices import PermissionScope, StateChoices
from common.constants import FILE_FIELD_MAX_LENGTH, CustomFieldTypeChoices
from common.helpers import get_upload_path
from common.mixins import FieldHistoryMixin, HashidsModelMixin
from common.models import (
    ArchiveModel,
    BaseFieldHistory,
    BaseModel,
    CreateModel,
    CreateModelWithDateOverride,
    CreateUpdateDeleteModel,
    CreateUpdateModel,
    CustomFieldDataModel,
    CustomFieldModel,
    UpdateDeleteModel,
    UpdateModel,
    UploadUpdateDeleteModel,
)
from core.models import AddressBaseModel, <PERSON><PERSON><PERSON>cy, Organization, Role, TaxType, Timezone, User
from core.selectors import organization_user_exists, role_exists
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementItemType
from microcontext.choices import MicroContextChoices
from project.data.querysets import (
    ProjectCustomFieldDropDownOptionQuerySet,
    ProjectCustomFieldQuerySet,
    ProjectCustomMultipleFilesFieldDataQuerySet,
    ProjectCustomSectionQuerySet,
    ProjectQuerySet,
)
from project.domain.constants import ProjectSectionTypeEnum
from project.domain.status import (
    AllModuleStatus,
    BoqStatus,
    DesignStatus,
    Module,
    OrderStatus,
    RDStatus,
    RecceStatus,
    WorkReportStatus,
)


class Scope(models.Model):
    name = models.CharField(_("name"), max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="scope", null=True)

    class Meta:
        unique_together = ["organization", "name"]

    def __str__(self):
        return str(self.name)


class BusinessCategory(models.Model):
    name = models.CharField(_("name"), max_length=30)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="business_categories")

    def __str__(self):
        return str(self.name)

    class Meta:
        db_table = "project_business_categories"
        unique_together = ["organization", "name"]


class Project(UpdateDeleteModel, HashidsModelMixin, FieldHistoryMixin, CustomFieldDataModel):
    organization_id: int

    class StageChoices(models.IntegerChoices):
        PRE_APPROVAL = 0, "Pre-approval"
        EXECUTION = 1, "Execution"
        POST_EXECUTION = 2, "Post-execution"
        SITE_PLANNING = 3, "Site Planning"

    NEW_PROJECT = 0
    RECCE = 1
    TWO_D_ADAPTATION = 2
    BOQ_COST_SHEET = 3
    TWO_D_BOQ_PENDING_APPROVAL = 4
    TREE_D_DESIGN_ARTWORK = 5
    PENDING_APPROVAL = 6
    HOLD = 7
    APPROVED = 8
    PRODUCTION_DRAWING = 9
    PRE_SITE_KICK_OFF = 10
    EXECUTION_INITIATED = 11
    RECTIFICATION = 12
    COMPLETION = 13
    PENDING_INVOICES = 14
    CLIENT_INVOICE_RAISED = 15
    JOB_ID_CLOSED = 16
    PAYMENT_RECEIVED = 17
    KAM_RECCE_REVIEW = 18
    EXECUTION_COMPLETED_DOCS_PENDING = 19
    CLOSED_LOST = 20
    IGNORE = 21
    NOT_MAPPED = 99
    PROJECT_MANAGER_REVIEW = 22
    PROJECT_ENGINEER_REVIEW = 23
    KEY_ACCOUNT_MANAGER_REVIEW = 24
    PO_RELEASE = 25
    INVOICES_ACCEPTED = 26
    STATUSES = [
        (NEW_PROJECT, "New Project Creation"),
        (RECCE, "Recce"),
        (TWO_D_ADAPTATION, "2D / Adaptation"),
        (BOQ_COST_SHEET, "BOQ / Cost Sheet"),
        (TWO_D_BOQ_PENDING_APPROVAL, "2D/BOQ Pending Approval"),
        (TREE_D_DESIGN_ARTWORK, "3D Design/Artwork"),
        (PENDING_APPROVAL, "Pending Approval"),
        (HOLD, "Hold"),
        (APPROVED, "Approved"),
        (PRODUCTION_DRAWING, "Production Drawings"),
        (PRE_SITE_KICK_OFF, "Pre Site Kick-off"),
        (EXECUTION_INITIATED, "Execution Initiated"),
        (RECTIFICATION, "Rectification"),
        (COMPLETION, "Completion"),
        (PENDING_INVOICES, "Pending Invoices"),
        (CLIENT_INVOICE_RAISED, "Client Invoice Raised"),
        (JOB_ID_CLOSED, "Job ID closed"),
        (PAYMENT_RECEIVED, "Payment Received"),
        (KAM_RECCE_REVIEW, "KAM Recce Review"),
        (EXECUTION_COMPLETED_DOCS_PENDING, "Execution Completed, Docs Pending"),
        (CLOSED_LOST, "Closed lost"),
        (IGNORE, "Ignore"),
        (NOT_MAPPED, "Not Mapped"),
        (PROJECT_MANAGER_REVIEW, "Project Manager Review"),
        (PROJECT_ENGINEER_REVIEW, "Project Engineer Review"),
        (KEY_ACCOUNT_MANAGER_REVIEW, "Key Account Manager Review"),
        (PO_RELEASE, "PO Release"),
        (INVOICES_ACCEPTED, "Invoices Accepted"),
    ]

    # storage_uuid = models.UUIDField(default=None, unique=False, null=True)
    storage_uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(_("name"), max_length=250)
    job_id = models.CharField(_("job id"), max_length=10, unique=True, null=True, blank=True)
    client = models.ForeignKey(
        Organization, verbose_name=_("client"), on_delete=models.RESTRICT, related_name="client_projects"
    )

    stage = models.PositiveSmallIntegerField(
        _("stage"), choices=StageChoices.choices, default=StageChoices.PRE_APPROVAL
    )  # TODO: not used
    store_type = models.ForeignKey(StoreType, on_delete=models.RESTRICT, null=True, blank=True, related_name="projects")
    # TODO: update blank=False after FE development
    business_category = models.ForeignKey(
        BusinessCategory, on_delete=models.RESTRICT, null=True, blank=True, related_name="projects"
    )
    status = models.PositiveSmallIntegerField(_("status"), choices=STATUSES, default=NEW_PROJECT)
    remark = models.TextField(null=True, blank=True)  # TODO: not used
    estimated_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0, blank=False)
    final_cost = models.DecimalField(max_digits=11, decimal_places=2, default=0, null=True)  # TODO: not used

    # TODO : move to specific apps ?
    # boolean
    recce_completed = models.BooleanField(default=False)  # TODO: not used
    execution_completed = models.BooleanField(default=False)  # TODO: not used

    # m2m
    scopes = models.ManyToManyField(Scope, through="ProjectScope")

    # datetime
    # project
    project_started_at = models.DateTimeField()
    project_ended_at = models.DateTimeField(null=True, blank=True)  # TODO: not used
    # recce
    recce_due_at = models.DateTimeField(null=True, blank=True)
    recce_completed_at = models.DateTimeField(null=True, blank=True)  # TODO: not used
    # execution
    kam_completion_due_at = models.DateTimeField(null=True, blank=True)
    expected_started_at = models.DateTimeField(null=True, blank=True)
    actual_started_at = models.DateTimeField(null=True, blank=True)
    ops_completion_due_at = models.DateTimeField(null=True, blank=True)  # TODO: not used
    execution_completed_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.RESTRICT,
        related_name="+",
    )

    updated_at = models.DateTimeField(auto_now=True, blank=True)
    deleted_at = models.DateTimeField(null=True, blank=True)
    organizations = models.ManyToManyField(
        Organization, through="ProjectOrganization", through_fields=("project", "organization")
    )
    users = models.ManyToManyField(
        User, through="ProjectUser", related_name="projects", through_fields=("project", "user")
    )
    rd_status = models.PositiveSmallIntegerField(
        choices=RDStatus.choices, null=True, blank=False, default=RDStatus.PROJECT_CREATED
    )
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, blank=False, null=False, related_name="+")
    version = models.IntegerField(
        default=1
    )  # v=1 for projects before rewiring, v=2 for projects after rewiring, v=3 after proposal request for change

    objects = ProjectQuerySet.as_manager()

    def __str__(self):
        return f"{self.name} ({self.job_id})"

    @property
    def due_at(self):
        return self.kam_completion_due_at


class ProjectStatus(BaseModel):
    id = models.AutoField(primary_key=True)
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="statuses", default=1, blank=False)
    project_status = models.PositiveSmallIntegerField(choices=RDStatus.choices, default=RDStatus.PROJECT_CREATED)
    recce_status = models.PositiveSmallIntegerField(
        choices=RecceStatus.choices, default=RecceStatus.RECCE_NOT_CREATED.value
    )
    design_status = models.PositiveSmallIntegerField(
        choices=DesignStatus.choices, default=DesignStatus.DESIGN_PENDING.value
    )
    boq_status = models.PositiveSmallIntegerField(choices=BoqStatus.choices, default=BoqStatus.SCOPE_NOT_CREATED.value)
    work_report_status = models.PositiveSmallIntegerField(
        choices=WorkReportStatus.choices, default=WorkReportStatus.NOT_STARTED.value
    )
    order_status = models.PositiveSmallIntegerField(choices=OrderStatus.choices, default=OrderStatus.OPEN.value)
    organization = models.ForeignKey(Organization, null=True, blank=True, on_delete=models.RESTRICT, related_name="+")

    def get_module_status(self, module: Module) -> int:
        if module == Module.PROJECT:
            return self.project_status
        elif module == Module.RECCE:
            return self.recce_status
        elif module == Module.DESIGN:
            return self.design_status
        elif module == Module.BOQ:
            return self.boq_status
        elif module == Module.WORK_REPORT:
            return self.work_report_status
        elif module == Module.ORDER:
            return self.order_status
        raise Exception("Invalid module.")

    def set_module_status(self, module: Module, status: int) -> None:
        if module == Module.PROJECT:
            self.project_status = status
        elif module == Module.RECCE:
            self.recce_status = status
        elif module == Module.DESIGN:
            self.design_status = status
        elif module == Module.BOQ:
            self.boq_status = status
        elif module == Module.WORK_REPORT:
            self.work_report_status = status
        elif module == Module.ORDER:
            self.order_status = status
        else:
            raise Exception("Invalid module.")

    class Meta:
        unique_together = ["project", "organization"]
        db_table = "project_statuses"


class ProjectStatusHistory(CreateModelWithDateOverride):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="project_status_history")
    status = models.PositiveSmallIntegerField(choices=AllModuleStatus.choices)
    module = models.PositiveSmallIntegerField(choices=Module.choices)
    organization = models.ForeignKey(Organization, null=True, blank=True, on_delete=models.RESTRICT, related_name="+")

    def clean(self):
        if self.module in [Module.ORDER_OUTGOING.value, Module.ORDER_INCOMING.value]:
            message = f"Module choice {dict(Module.choices).get(self.module)} not allowed"
            raise ValidationError(_(message))

    class Meta:
        db_table = "project_status_histories"


class Store(UpdateModel, FieldHistoryMixin, AddressBaseModel):
    project = models.OneToOneField(
        Project, verbose_name=_("store"), on_delete=models.CASCADE, primary_key=True, related_name="store"
    )
    address = models.TextField(_("address"), null=True, blank=True)
    city_data = models.CharField(_("city"), max_length=100, null=True, blank=True)
    state_data = models.PositiveSmallIntegerField(
        _("state"), default=StateChoices.NOT_SET, choices=StateChoices.choices
    )
    location = models.PointField(verbose_name=_("location"), null=True, blank=True)
    dealer_name = models.CharField(_("dealer name"), max_length=100, null=True, blank=True)
    dealer_phone_number = modelfields.PhoneNumberField(verbose_name=_("dealer phone number"), null=True, blank=True)
    dealer_gstin = models.CharField(_("dealer gstin"), max_length=15, null=True, blank=True)
    local_name = models.CharField(_("local name"), max_length=250, null=True, blank=True)
    local_name_file = models.FileField(null=True, max_length=2000, upload_to=get_upload_path, blank=True)
    area = models.DecimalField(_("area"), default=None, null=True, max_digits=15, decimal_places=5)

    @property
    def state_name(self):
        return self.state.name

    def __str__(self):
        return str(self.project.name)


class ProjectAttachment(UploadUpdateDeleteModel):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="attachments")
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = "project_attachments"


class ProjectScope(models.Model):
    scope = models.ForeignKey(Scope, on_delete=models.RESTRICT)
    project = models.ForeignKey(Project, on_delete=models.RESTRICT)
    added_at = models.DateTimeField(auto_now_add=True)


class ProjectOrganization(CreateModel):
    organization_id: int

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name="projects")
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="project_organizations")
    role = models.ForeignKey(Role, on_delete=models.RESTRICT)
    assigned_by = models.ForeignKey(Organization, on_delete=models.CASCADE)

    def clean(self):
        if self.created_by and not organization_user_exists(org_id=self.assigned_by.id, user_id=self.created_by.id):
            raise ValidationError(_("User should present in the assigned by organization."))

        if self.role and not role_exists(role_id=self.role.id, scope=PermissionScope.ORGANIZATION):
            raise ValidationError(_("Role with scope ORGANIZATION assignment is only allowed."))

        if self.assigned_by and not ProjectOrganization.objects.filter(organization_id=self.assigned_by_id).exists():
            raise ValidationError(_("Assigned by organization should also be in project organization."))

    class Meta:
        db_table = "project_organizations"
        unique_together = ["project", "organization", "role"]


class ProjectUser(CreateModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.RESTRICT)

    def clean(self):
        if self.role:
            if not organization_user_exists(org_id=self.role.organization.id, user_id=self.created_by.id):
                raise ValidationError(_("created by user should present in the organization"))

            if not organization_user_exists(org_id=self.role.organization.id, user_id=self.user.id):
                raise ValidationError(_("user should present in the organization"))

    class Meta:
        db_table = "project_users"
        unique_together = ["project", "user", "role"]


class ProjectFieldHistory(BaseFieldHistory):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT)
    reason = models.TextField(max_length=300, null=True, blank=True)

    class Meta:
        db_table = "project_field_histories"


class StoreFieldHistory(BaseFieldHistory):
    store = models.ForeignKey(Store, on_delete=models.RESTRICT)

    class Meta:
        db_table = "project_store_field_histories"


class CityStateMapping(models.Model):
    city = models.CharField(max_length=100)
    state = models.IntegerField(choices=StateChoices.choices)

    class Meta:
        unique_together = ("city", "state")


class ProjectProgressSchedule(BaseModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    sheet_url = models.URLField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["project", "organization"]


class ProjectOrgExtraData(BaseModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    cost_sheet_url = models.URLField()
    cost_compare_sheet_url = models.URLField(null=True)
    boq_vs_order_sheet_url = models.URLField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = [
            "project",
            "organization",
        ]


class ProjectOrgData(BaseModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="project_org_data")
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="project_org_data")
    is_archived = models.BooleanField(default=False)
    progress_percentage = models.PositiveSmallIntegerField(
        default=0, validators=[MaxValueValidator(100), MinValueValidator(0)]
    )
    boq_rate_contract = models.ForeignKey(
        "ratecontract.RateContract", on_delete=models.RESTRICT, null=True, blank=True, related_name="+"
    )

    class Meta:
        db_table = "project_org_data"
        unique_together = ["project", "organization"]


class ProjectOrgCustomFieldConfig(CustomFieldModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    context = models.CharField(max_length=100, choices=MicroContextChoices.choices)

    class Meta:
        unique_together = ["project", "org", "context"]


class ClientProjectUser(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class KeyAccountManager(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class ProjectManager(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class RecceProjectEngineer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class ExecutionProjectEngineer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class ProductionManager(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class ProductionEngineer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class DesignLead(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class Designer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class Designer3d(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class GraphicDesigner(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)


class RecceVendor(models.Model):
    vendor = models.ForeignKey(Organization, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    scopes = models.ManyToManyField(Scope, through="RecceVendorScope")
    assigned_at = models.DateTimeField(auto_now_add=True)


class ExecutionVendor(models.Model):
    vendor = models.ForeignKey(Organization, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    scopes = models.ManyToManyField(Scope, through="ExecutionVendorScope")
    assigned_at = models.DateTimeField(auto_now_add=True)


class ProductionVendor(models.Model):
    vendor = models.ForeignKey(Organization, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    scopes = models.ManyToManyField(Scope, through="ProductionVendorScope")
    assigned_at = models.DateTimeField(auto_now_add=True)


class ExecutionVendorScope(models.Model):
    scope = models.ForeignKey(Scope, on_delete=models.CASCADE)
    execution_vendor = models.ForeignKey(ExecutionVendor, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)


class ProductionVendorScope(models.Model):
    scope = models.ForeignKey(Scope, on_delete=models.CASCADE)
    production_vendor = models.ForeignKey(ProductionVendor, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)


class RecceVendorScope(models.Model):
    scope = models.ForeignKey(Scope, on_delete=models.CASCADE)
    production_vendor = models.ForeignKey(RecceVendor, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)


class ProjectUserAction(BaseModel):
    project_user = models.ForeignKey(ProjectUser, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    registered_at = models.DateTimeField(auto_now_add=True)
    action = models.CharField(max_length=100, choices=Actions.choices)

    class Meta:
        db_table = "project_user_actions"
        unique_together = ["project_user", "action"]


class ProjectOrgItemTypeConfig(CreateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    item_type = models.ForeignKey(ElementItemType, on_delete=models.RESTRICT, related_name="+")
    update_method = models.CharField(max_length=50, choices=ItemTypeUpdateMethodChoices.choices)

    class Meta:
        unique_together = ("project", "organization", "item_type")
        db_table = "boq_project_org_item_type_configs"


class ProjectCustomSection(CreateUpdateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="custom_sections")
    name = models.CharField(max_length=150)
    uuid = models.UUIDField()
    position = models.SmallIntegerField()
    type = models.CharField(choices=ProjectSectionTypeEnum.choices, max_length=100)

    objects = ProjectCustomSectionQuerySet.as_manager()

    class Meta:
        db_table = "project_custom_sections"
        constraints = [
            models.UniqueConstraint(
                name="custom_section_organization_and_section_uuid_unique",
                fields=["organization_id", "uuid"],
                condition=Q(deleted_at__isnull=True),
            ),
            models.UniqueConstraint(
                name="custom_section_organization_and_section_name_unique",
                fields=["organization_id", "name"],
                condition=Q(deleted_at__isnull=True),
            ),
        ]


class ProjectCustomField(CreateUpdateDeleteModel, ArchiveModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="custom_fields")
    section = models.ForeignKey(ProjectCustomSection, on_delete=models.RESTRICT, related_name="fields")
    name = models.CharField(max_length=150)
    type = models.CharField(choices=CustomFieldTypeChoices.choices, max_length=100)
    position = models.SmallIntegerField()
    uuid = models.UUIDField()
    is_required = models.BooleanField(default=True)
    data_counter = models.PositiveSmallIntegerField(default=0)
    is_added_in_existing_projects = models.BooleanField(default=False)

    objects = ProjectCustomFieldQuerySet.as_manager()

    class Meta:
        db_table = "project_custom_fields"
        constraints = [
            models.UniqueConstraint(
                name="custom_field_organization_and_field_uuid_unique",
                fields=["organization_id", "uuid"],
                condition=Q(deleted_at__isnull=True),
            ),
            models.UniqueConstraint(
                name="custom_field_organization_and_field_name_unique",
                fields=["organization_id", "name"],
                condition=Q(deleted_at__isnull=True),
            ),
        ]


class ProjectCustomFieldDropDownOption(CreateUpdateDeleteModel, ArchiveModel):
    uuid = models.UUIDField()
    name = models.CharField(max_length=50)
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="options")
    counter = models.PositiveSmallIntegerField(default=0)

    objects = ProjectCustomFieldDropDownOptionQuerySet.as_manager()

    class Meta:
        db_table = "project_custom_field_dropdown_options"
        constraints = [
            models.UniqueConstraint(
                name="custom_dropdown_uuid_and_field_unique",
                fields=["field_id", "uuid"],
                condition=Q(deleted_at__isnull=True),
            ),
            models.UniqueConstraint(
                name="custom_dropdown_name_and_field_unique",
                fields=["field_id", "name"],
                condition=Q(deleted_at__isnull=True),
            ),
        ]


class ProjectCustomMultipleFilesField(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_multiple_files_fields")
    field = models.ForeignKey(
        ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_multiple_files_fields"
    )

    class Meta:
        db_table = "project_custom_multiple_files_fields"
        unique_together = ("project", "field")


class ProjectCustomMultipleFilesFieldData(CreateUpdateDeleteModel):
    field = models.ForeignKey(
        ProjectCustomMultipleFilesField, on_delete=models.RESTRICT, related_name="custom_multiple_files_data"
    )
    data = models.FileField(upload_to=get_upload_path, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100, null=True, blank=True)

    objects = ProjectCustomMultipleFilesFieldDataQuerySet.as_manager()

    class Meta:
        db_table = "project_custom_multiple_files_field_data"


class ProjectCustomTextFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_text_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_text_data")
    data = models.CharField(max_length=50, null=True, blank=True)

    class Meta:
        db_table = "project_custom_text_field_data"
        unique_together = ("project", "field")


class ProjectCustomNumberFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_decimal_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_decimal_data")
    data = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    class Meta:
        db_table = "project_custom_number_field_data"
        unique_together = ("project", "field")


class ProjectCustomDateFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_date_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_date_data")
    data = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "project_custom_date_field_data"
        unique_together = ("project", "field")


class ProjectCustomRichTextFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_richtext_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_richtext_data")
    data = models.JSONField(null=True, blank=True)

    class Meta:
        db_table = "project_custom_rich_text_field_data"
        unique_together = ("project", "field")


class ProjectCustomPhoneNumberFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_phonenumber_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_phonenumber_data")
    data = modelfields.PhoneNumberField(null=True, blank=True)

    class Meta:
        db_table = "project_custom_phone_number_field_data"
        unique_together = ("project", "field")


class ProjectCustomDropDownFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_dropdown_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_dropdown_data")
    data = models.ForeignKey(
        ProjectCustomFieldDropDownOption, on_delete=models.RESTRICT, null=True, blank=True, related_name="+"
    )

    class Meta:
        db_table = "project_custom_drop_down_field_data"
        unique_together = ("project", "field")


class ProjectCustomFileFieldData(CreateUpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="custom_file_data")
    field = models.ForeignKey(ProjectCustomField, on_delete=models.RESTRICT, related_name="custom_file_data")
    data = models.FileField(upload_to=get_upload_path, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = "project_custom_file_field_data"
        unique_together = ("project", "field")


class ProjectConfig(BaseModel):
    project = models.OneToOneField(Project, on_delete=models.RESTRICT, related_name="config")
    currency = models.ForeignKey(Currency, on_delete=models.RESTRICT, related_name="+")
    tax_type = models.ForeignKey(TaxType, on_delete=models.RESTRICT, related_name="+")
    timezone = models.ForeignKey(Timezone, on_delete=models.RESTRICT, related_name="+")

    class Meta:
        db_table = "project_configs"
