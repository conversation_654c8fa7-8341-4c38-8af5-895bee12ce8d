from typing import Optional, overload

from rest_framework import serializers

from authorization.domain.constants import Permissions, PermissionType
from common.entities import ObjectStatus
from common.utils import padding_for_serial_number
from core.helpers import Base<PERSON><PERSON><PERSON>elper, OrgPermissionHelper
from core.models import User
from crm.board.data.caches import BoardAssignmentCache
from crm.board.domain.constants import (
    BOARD_DELETE_SETTING_MAPPING,
    BOARD_EDIT_SETTING_MAPPING,
    BOARD_USER_CREATION_SETTING_CONFIG,
    BOARD_USER_DELETE_SETTING_CONFIG,
    BOARD_USER_EDIT_SETTING_CONFIG,
    BOARD_USER_VIEW_SETTING_CONFIG,
    BOARD_VIEW_SETTING_MAPPING,
)
from crm.board.domain.entities import BoardAssignmentCacheData, BoardUserRepoEntity
from crm.board.domain.enums import (
    BoardPermissionEnum,
    BoardUserSettingEnum,
)
from crm.data.models import Lead
from crm.interface.type import BoardPermissionType
from crm.lead.domain.constants import LEAD_NUMBER_PADDING
from rollingbanners.authentication import TokenData


class BoardPermissionHelper(BaseRoleHelper):
    @classmethod
    @overload
    def can_view_all_leads(cls, user: User, board_id: int) -> bool: ...

    @classmethod
    @overload
    def can_view_all_leads(cls, *, permissions: BoardPermissionType) -> bool: ...

    @classmethod
    @overload
    def is_action_permitted(
        cls,
        permission: BoardPermissionEnum,
        user: User,
        board_id: int,
    ) -> bool: ...

    @classmethod
    @overload
    def is_action_permitted(
        cls,
        *,
        permission: BoardPermissionEnum,
        permissions: BoardPermissionType,
    ) -> bool: ...

    @classmethod
    def add_permission_reason(
        cls,
        permission: BoardPermissionEnum,
        reason: PermissionType,
        board_permissions: BoardPermissionType,
    ) -> BoardPermissionType:
        if permission not in board_permissions:
            board_permissions[permission] = [reason]
        else:
            board_permissions[permission].append(reason)
        return board_permissions

    @classmethod
    def add_all_board_permissions(cls, reason: PermissionType, board_permissions: BoardPermissionType):
        cls.add_permission_reason(
            permission=BoardPermissionEnum.CAN_ACCESS_BOARDS,
            reason=reason,
            board_permissions=board_permissions,
        )

        cls.add_permission_reason(
            permission=BoardPermissionEnum.CAN_EDIT_BOARD,
            reason=reason,
            board_permissions=board_permissions,
        )

        for permissions in list(BOARD_VIEW_SETTING_MAPPING.values()):
            for permission in permissions:
                cls.add_permission_reason(permission=permission, reason=reason, board_permissions=board_permissions)

        cls.add_permission_reason(
            permission=BoardPermissionEnum.CAN_CREATE_LEADS,
            reason=reason,
            board_permissions=board_permissions,
        )

        for permissions in list(BOARD_EDIT_SETTING_MAPPING.values()):
            for permission in permissions:
                cls.add_permission_reason(permission=permission, reason=reason, board_permissions=board_permissions)

        for permissions in list(BOARD_DELETE_SETTING_MAPPING.values()):
            for permission in permissions:
                cls.add_permission_reason(permission=permission, reason=reason, board_permissions=board_permissions)

        return board_permissions

    @classmethod
    def add_board_user_type_permissions(
        cls,
        board_user_data: BoardUserRepoEntity,
        board_permissions: BoardPermissionType,
    ):
        if board_user_data.view_setting:
            view_setting = BOARD_VIEW_SETTING_MAPPING[board_user_data.view_setting]
            for per in view_setting:
                cls.add_permission_reason(
                    permission=per,
                    reason=PermissionType.BOARD_USER,
                    board_permissions=board_permissions,
                )

        if board_user_data.can_create:
            cls.add_permission_reason(
                permission=BoardPermissionEnum.CAN_CREATE_LEADS,
                reason=PermissionType.BOARD_USER,
                board_permissions=board_permissions,
            )

        if board_user_data.edit_setting:
            edit_setting = BOARD_EDIT_SETTING_MAPPING[board_user_data.edit_setting]
            for permission in edit_setting:
                cls.add_permission_reason(
                    permission=permission,
                    reason=PermissionType.BOARD_USER,
                    board_permissions=board_permissions,
                )

        if board_user_data.delete_setting:
            delete_setting = BOARD_DELETE_SETTING_MAPPING[board_user_data.delete_setting]
            for permission in delete_setting:
                cls.add_permission_reason(
                    permission=permission,
                    reason=PermissionType.BOARD_USER,
                    board_permissions=board_permissions,
                )

        return board_permissions

    @classmethod
    def get_permissions(cls, user: User, board_id: int) -> BoardPermissionType:
        token_data: TokenData = cls.validate_user_token_data(user=user)
        org_user_permissions = OrgPermissionHelper.get_permissions(user=user)

        board_permissions: BoardPermissionType = {}

        if Permissions.CAN_ACCESS_BOARDS not in org_user_permissions:
            return {}

        if token_data.is_admin:
            return cls.add_all_board_permissions(reason=PermissionType.ADMIN, board_permissions=board_permissions)

        if Permissions.CAN_EDIT_BOARD in org_user_permissions:
            return cls.add_all_board_permissions(reason=PermissionType.ORG_USER, board_permissions=board_permissions)

        cls.add_permission_reason(
            reason=PermissionType.ORG_USER,
            permission=BoardPermissionEnum.CAN_ACCESS_BOARDS,
            board_permissions=board_permissions,
        )

        # check if user has access to all boards
        if Permissions.CAN_VIEW_ALL_BOARDS in org_user_permissions:
            for permissions in list(BOARD_VIEW_SETTING_MAPPING.values()):
                for permission in permissions:
                    cls.add_permission_reason(
                        permission=permission, reason=PermissionType.ORG_USER, board_permissions=board_permissions
                    )

        # check board user permissions
        board_assignment_cache_data: BoardAssignmentCacheData = BoardAssignmentCache.get(instance_id=board_id)  # type: ignore

        if user.pk not in board_assignment_cache_data.board_user_data:
            return board_permissions

        board_user_data: BoardUserRepoEntity = board_assignment_cache_data.board_user_data[user.pk]

        return cls.add_board_user_type_permissions(board_user_data=board_user_data, board_permissions=board_permissions)

    @classmethod
    def is_action_permitted(
        cls,
        permission: BoardPermissionEnum,
        user: Optional[User] = None,
        board_id: Optional[int] = None,
        permissions: Optional[BoardPermissionType] = None,
    ) -> bool:
        if user and board_id:
            user_permissions = cls.get_permissions(user=user, board_id=board_id)
        elif permissions is not None:
            user_permissions = permissions
        else:
            raise ValueError("Either (user, board_id) or permissions must be provided")

        if permission in user_permissions:
            return True
        return False

    @classmethod
    def can_edit_all_leads(
        cls,
        user: Optional[User] = None,
        board_id: Optional[int] = None,
        permissions: Optional[BoardPermissionType] = None,
    ) -> bool:
        if user and board_id:
            user_permissions = cls.get_permissions(user=user, board_id=board_id)
        elif permissions is not None:
            user_permissions = permissions
        else:
            raise ValueError("Either (user, board_id) or permissions must be provided")

        for edit_permissions in BOARD_EDIT_SETTING_MAPPING.values():
            for permission in edit_permissions:
                if permission not in user_permissions:
                    return False
        return True

    @classmethod
    def can_view_all_leads(
        cls,
        user: Optional[User] = None,
        board_id: Optional[int] = None,
        permissions: Optional[BoardPermissionType] = None,
    ) -> bool:
        if user and board_id:
            user_permissions = cls.get_permissions(user=user, board_id=board_id)
        elif permissions is not None:
            user_permissions = permissions
        else:
            raise ValueError("Either (user, board_id) or permissions must be provided")

        for view_permissions in BOARD_VIEW_SETTING_MAPPING.values():
            for permission in view_permissions:
                if permission not in user_permissions:
                    return False
        return True

    @classmethod
    def can_delete_all_leads(
        cls,
        user: Optional[User] = None,
        board_id: Optional[int] = None,
        permissions: Optional[BoardPermissionType] = None,
    ) -> bool:
        if user and board_id:
            user_permissions = cls.get_permissions(user=user, board_id=board_id)
        elif permissions is not None:
            user_permissions = permissions
        else:
            raise ValueError("Either (user, board_id) or permissions must be provided")

        for delete_permissions in BOARD_DELETE_SETTING_MAPPING.values():
            for permission in delete_permissions:
                if permission not in user_permissions:
                    return False
        return True


class LeadPermissionHelper(BaseRoleHelper):
    @classmethod
    def can_edit_lead(cls, user: User, lead: Lead) -> bool:
        assert hasattr(lead, "assigned_to_ids"), "Lead must have assigned_to_ids"

        board_permission = BoardPermissionHelper.get_permissions(user=user, board_id=lead.board_id)

        can_edit_other_leads = False
        can_edit_created_leads = False
        can_edit_assigned_leads = False

        is_created_by = lead.created_by_id == user.pk
        is_assigned_to = user.pk in lead.assigned_to_ids

        for permission in board_permission:
            if permission == BoardPermissionEnum.CAN_EDIT_OTHER_LEADS:
                can_edit_other_leads = True
            elif permission == BoardPermissionEnum.CAN_EDIT_CREATED_LEADS:
                can_edit_created_leads = True
            elif permission == BoardPermissionEnum.CAN_EDIT_ASSIGNED_LEADS:
                can_edit_assigned_leads = True

        if can_edit_other_leads and can_edit_created_leads and can_edit_assigned_leads:
            return True
        elif can_edit_assigned_leads and is_assigned_to:
            return True
        elif can_edit_created_leads and is_created_by:
            return True
        return False

    @classmethod
    def can_view_lead(cls, user: User, lead: Lead) -> bool:
        assert hasattr(lead, "assigned_to_ids"), "Lead must have assigned_to_ids"

        board_permission = BoardPermissionHelper.get_permissions(user=user, board_id=lead.board_id)

        can_view_other_leads = False
        can_view_created_leads = False
        can_view_assigned_leads = False

        is_created_by = lead.created_by_id == user.pk
        is_assigned_to = user.pk in lead.assigned_to_ids

        for permission in board_permission:
            if permission == BoardPermissionEnum.CAN_VIEW_OTHER_LEADS:
                can_view_other_leads = True
            elif permission == BoardPermissionEnum.CAN_VIEW_CREATED_LEADS:
                can_view_created_leads = True
            elif permission == BoardPermissionEnum.CAN_VIEW_ASSIGNED_LEADS:
                can_view_assigned_leads = True

        if can_view_other_leads and can_view_created_leads and can_view_assigned_leads:
            return True

        elif can_view_assigned_leads and is_assigned_to:
            return True
        elif can_view_created_leads and is_created_by:
            return True
        return False

    @classmethod
    def can_delete_lead(cls, user: User, lead: Lead) -> bool:
        assert hasattr(lead, "assigned_to_ids"), "Lead must have assigned_to_ids"

        board_permission = BoardPermissionHelper.get_permissions(user=user, board_id=lead.board_id)

        can_delete_other_leads = False
        can_delete_created_leads = False
        can_delete_assigned_leads = False

        is_created_by = lead.created_by_id == user.pk
        is_assigned_to = user.pk in lead.assigned_to_ids

        for permission in board_permission:
            if permission == BoardPermissionEnum.CAN_DELETE_OTHER_LEADS:
                can_delete_other_leads = True
            elif permission == BoardPermissionEnum.CAN_DELETE_CREATED_LEADS:
                can_delete_created_leads = True
            elif permission == BoardPermissionEnum.CAN_DELETE_ASSIGNED_LEADS:
                can_delete_assigned_leads = True

        if can_delete_other_leads and can_delete_created_leads and can_delete_assigned_leads:
            return True
        elif can_delete_assigned_leads and is_assigned_to:
            return True
        elif can_delete_created_leads and is_created_by:
            return True
        return False


class NodeDeletionValidatorMixin:
    def validate_nodes(self, attrs):
        deleted_entity_uuids = []
        for element in attrs.elements:
            if element.object_status == ObjectStatus.DELETE:
                deleted_entity_uuids.append(element.uuid)
        for section in attrs.sections:
            if section.object_status == ObjectStatus.DELETE:
                deleted_entity_uuids.append(section.uuid)
        for node in attrs.tree:
            if (node.object_status is ObjectStatus.DELETE and node.uuid not in deleted_entity_uuids) or (
                node.object_status != ObjectStatus.DELETE and node.uuid in deleted_entity_uuids
            ):
                raise serializers.ValidationError("Deleted node's object status mismatched.")
        return attrs


def validate_board_user_setting(value):
    setting_keys = value.keys()
    if set(BoardUserSettingEnum.choices).difference(set(setting_keys)):
        raise serializers.ValidationError("Invalid setting key")

    view_setting = value.get(BoardUserSettingEnum.VIEW_SETTING.value)
    if view_setting not in set(BOARD_USER_VIEW_SETTING_CONFIG["allowed_values"]):
        raise serializers.ValidationError("Invalid view setting value")

    create_setting = value.get(BoardUserSettingEnum.CREATE_SETTING.value)
    if create_setting not in set(BOARD_USER_CREATION_SETTING_CONFIG["allowed_values"]):
        raise serializers.ValidationError("Invalid create setting value")

    edit_setting = value.get(BoardUserSettingEnum.EDIT_SETTING.value)
    if edit_setting not in set(BOARD_USER_EDIT_SETTING_CONFIG["allowed_values"]):
        raise serializers.ValidationError("Invalid edit setting value")

    delete_setting = value.get(BoardUserSettingEnum.DELETE_SETTING.value)
    if delete_setting not in set(BOARD_USER_DELETE_SETTING_CONFIG["allowed_values"]):
        raise serializers.ValidationError("Invalid delete setting value")


def add_lead_number_padding(lead_number: int) -> str:
    padded_number = padding_for_serial_number(serial_number=lead_number, padding=LEAD_NUMBER_PADDING)
    return f"LD{padded_number}"
