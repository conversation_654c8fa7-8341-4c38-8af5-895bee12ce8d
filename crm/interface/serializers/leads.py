from collections import defaultdict
from typing import List

from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from common.constants import CustomFieldTypeEnum
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomDateField,
    CustomEndDateField,
    HashIdField,
    PhoneNumberSerializer,
    SplitCharHashIdListField,
    SplitCharListField,
)
from core.models import City, State, User
from core.serializers import CountryModelSerializer, UserModelSerializer
from crm.data.models import (
    BoardLeadBooleanFieldData,
    BoardLeadDateFieldData,
    BoardLeadDropDownFieldData,
    BoardLeadDropDownFieldOption,
    BoardLeadEmailFieldData,
    BoardLeadField,
    BoardLeadFileFieldData,
    BoardLeadNumberFieldData,
    BoardLeadPhoneNumberFieldData,
    BoardLeadRichTextFieldData,
    BoardLeadSection,
    BoardLeadTextFieldData,
    Company,
    Contact,
    Lead,
    LeadAssignedEventData,
    LeadAssignee,
    LeadCommentCreatedEventData,
    LeadContact,
    LeadConvertedToProjectEventData,
    LeadCreatedEventData,
    LeadEvent,
    LeadQuotationSubmittedEventData,
    LeadReminderCreatedEventData,
    LeadStageChangedEventData,
    LeadTaskCreatedEventData,
)
from crm.interface.helpers import BoardPermissionHelper, add_lead_number_padding
from crm.interface.type import BoardPermissionEnum
from crm.lead.data.choices import LeadSourceChoices
from crm.lead.domain.entities import (
    CompanyEntity,
    LeadAssigneeDetailRepoEntity,
    LeadCompanyDetailRepoEntity,
    LeadContactDetailRepoEntity,
    LeadFieldForImportSectionEntity,
    LeadUserActionData,
)
from crm.lead.domain.services.lead import LeadActionServiceV2, get_breadcrumb
from rollingbanners.hash_id_converter import HashIdConverter
from rollingbanners.storage_backends import PublicMediaFileStorage
from task.interfaces.serializers import AttachmentSerializer


class LeadDropDownOptionModelSerializer(BaseModelSerializer):
    class Meta:
        model = BoardLeadDropDownFieldOption
        fields = "__all__"


class LeadFieldModelSerializer(BaseModelSerializer):
    class LeadDropDownOptionSerializer(LeadDropDownOptionModelSerializer):
        class Meta(LeadDropDownOptionModelSerializer.Meta):
            fields = ("id", "name")

    options = LeadDropDownOptionSerializer(many=True)

    class Meta:
        model = BoardLeadField
        fields = "__all__"


class LeadAssigneeDetailRepoEntitySerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = LeadAssigneeDetailRepoEntity


class LeadContactDetailRepoEntitySerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = LeadContactDetailRepoEntity


class LeadCompanyDetailRepoEntitySerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = LeadCompanyDetailRepoEntity


class LeadDetailDropDownOptionSerializer(BaseSerializer):
    id = serializers.CharField()
    name = serializers.CharField()

    class Meta:
        ref_name = "LeadDetailDropDownOptionSerializer"
        output_hash_id_fields = ("id",)


class LeadSectionModelSerializer(BaseModelSerializer):
    class LeadFieldSerializer(LeadFieldModelSerializer):
        class Meta(LeadFieldModelSerializer.Meta):
            fields = ("uuid", "name", "position", "is_required", "type", "options")

    fields = LeadFieldSerializer(many=True)

    class Meta:
        model = BoardLeadSection
        fields = "__all__"


class LeadContactModelSerializer(BaseModelSerializer):
    name = serializers.CharField(source="contact.name")
    phone = PhoneNumberSerializer(source="contact.phone")
    email = serializers.EmailField(source="contact.email")

    def get_phone(self, obj):
        contact = obj.contact
        if hasattr(contact, "phone") and contact.phone:
            return {"number": contact.phone.national_number, "country_code": contact.phone.country_code}

    class Meta:
        model = LeadContact
        fields = "__all__"


class LeadAssigneeModelSerializer(BaseModelSerializer):
    name = serializers.SerializerMethodField()
    photo = serializers.ImageField(source="user.photo")
    phone = PhoneNumberSerializer(source="user.phone_number")

    def get_name(self, obj):
        if hasattr(obj, "user") and obj.user.first_name:
            return f"{obj.user.first_name} {obj.user.last_name}"

    class Meta:
        model = LeadAssignee
        fields = "__all__"


class LeadModelSerializer(BaseModelSerializer):
    class LeadContactSerializer(LeadContactModelSerializer):
        class Meta(LeadContactModelSerializer.Meta):
            fields = ("name", "phone", "email")

    class LeadAssigneeSerializer(LeadAssigneeModelSerializer):
        class Meta(LeadAssigneeModelSerializer.Meta):
            fields = ("name", "photo", "phone")

    class StateModelSerializer(BaseModelSerializer):
        class Meta:
            model = State
            fields = ("id", "name")

    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name", "photo")
            ref_name = "LeadUserSerializer"

    lead_number = serializers.SerializerMethodField()
    name = serializers.CharField()
    custom_fields = serializers.SerializerMethodField()
    company = serializers.SerializerMethodField()
    country = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    address_line_1 = serializers.SerializerMethodField()
    address_line_2 = serializers.SerializerMethodField()
    zip_code = serializers.SerializerMethodField()
    contacts = LeadContactSerializer(many=True)
    assignees = LeadAssigneeSerializer(many=True)
    stage = StateModelSerializer()
    created_by = UserSerializer()
    sorted_at = serializers.DateTimeField()
    actions = serializers.SerializerMethodField()
    board_id = HashIdField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_lead_number(self, obj):
        return add_lead_number_padding(obj.serial_number)

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        user: User = self.context.get("user")
        board_permissions = self.context.get("board_permissions")
        if board_permissions is None:
            board_permissions = BoardPermissionHelper.get_permissions(user=user, board_id=obj.board_id)
            self.context["board_permissions"] = board_permissions

        if self.context.get("can_create_leads") is None:
            self.context["can_create_leads"] = BoardPermissionHelper.is_action_permitted(
                permission=BoardPermissionEnum.CAN_CREATE_LEADS, permissions=board_permissions
            )

        if self.context.get("can_edit_all_leads") is None:
            self.context["can_edit_all_leads"] = BoardPermissionHelper.can_edit_all_leads(permissions=board_permissions)

        if hasattr(obj, "can_create_lead"):
            return LeadActionServiceV2(
                user=user,
                lead_data=LeadUserActionData(
                    id=obj.pk,
                    board_id=obj.board_id,
                    can_create_lead=self.context.get("can_create_leads", False),
                    can_edit_lead=obj.can_edit_lead,
                    can_add_field=self.context.get("can_add_field", False),
                    is_project_type=obj.is_project_conversion_allowed,
                    project_id=obj.project_id,
                    can_delete_lead=obj.can_delete_lead,
                ),
            ).get_actions(can_edit_all_leads=self.context.get("can_edit_all_leads", False))
        return []

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_country(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.country:
            return obj.lead_project_data.country.name

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_state(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.state:
            return obj.lead_project_data.state.name

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_city(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.city:
            return obj.lead_project_data.city.name

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_address_line_1(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.address_line_1:
            return obj.lead_project_data.address_line_1

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_address_line_2(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.address_line_2:
            return obj.lead_project_data.address_line_2

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_zip_code(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.zip_code:
            return obj.lead_project_data.zip_code

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_company(self, obj):
        if hasattr(obj, "company") and obj.company:
            return obj.company.name

    def get_text_data(self, obj):
        text_data_list = []
        lead_text_data: BoardLeadTextFieldData = obj.custom_text_data.all()
        for text_data in lead_text_data:
            text_data_list.append(
                {
                    "name": text_data.field.name,
                    "value": text_data.data,
                    "type": text_data.field.type,
                    "position": text_data.field.position,
                    "section_id": text_data.field.section_id,
                }
            )
        return text_data_list

    def get_email_data(self, obj):
        email_data_list = []
        lead_email_data: BoardLeadEmailFieldData = obj.custom_email_data.all()
        for email_data in lead_email_data:
            email_data_list.append(
                {
                    "name": email_data.field.name,
                    "value": email_data.data,
                    "type": email_data.field.type,
                    "position": email_data.field.position,
                    "section_id": email_data.field.section_id,
                }
            )
        return email_data_list

    def get_number_data(self, obj):
        number_data_list = []
        lead_number_data: BoardLeadNumberFieldData = obj.custom_number_data.all()
        for number_data in lead_number_data:
            number_data_list.append(
                {
                    "name": number_data.field.name,
                    "value": number_data.data,
                    "type": number_data.field.type,
                    "position": number_data.field.position,
                    "section_id": number_data.field.section_id,
                }
            )
        return number_data_list

    def get_date_data(self, obj):
        date_data_list = []
        lead_date_data: BoardLeadDateFieldData = obj.custom_date_data.all()
        for date_data in lead_date_data:
            date_data_list.append(
                {
                    "name": date_data.field.name,
                    "value": date_data.data,
                    "type": date_data.field.type,
                    "position": date_data.field.position,
                    "section_id": date_data.field.section_id,
                }
            )
        return date_data_list

    def get_datetime_data(self, obj):
        datetime_data_list = []
        lead_datetime_data: BoardLeadDateFieldData = obj.custom_datetime_data.all()
        for datetime_data in lead_datetime_data:
            datetime_data_list.append(
                {
                    "name": datetime_data.field.name,
                    "value": datetime_data.data,
                    "type": datetime_data.field.type,
                    "position": datetime_data.field.position,
                    "section_id": datetime_data.field.section_id,
                }
            )
        return datetime_data_list

    def get_boolean_data(self, obj):
        boolean_data_list = []
        lead_boolean_data: BoardLeadBooleanFieldData = obj.custom_boolean_data.all()
        for boolean_data in lead_boolean_data:
            boolean_data_list.append(
                {
                    "name": boolean_data.field.name,
                    "value": boolean_data.data,
                    "type": boolean_data.field.type,
                    "position": boolean_data.field.position,
                    "section_id": boolean_data.field.section_id,
                }
            )
        return boolean_data_list

    def get_rich_text_data(self, obj):
        rich_text_data_list = []
        lead_rich_text_data: BoardLeadRichTextFieldData = obj.custom_rich_text_data.all()
        for rich_text_data in lead_rich_text_data:
            rich_text_data_list.append(
                {
                    "name": rich_text_data.field.name,
                    "value": rich_text_data.data,
                    "type": rich_text_data.field.type,
                    "position": rich_text_data.field.position,
                    "section_id": rich_text_data.field.section_id,
                }
            )
        return rich_text_data_list

    def get_phone_number_data(self, obj):
        phone_number_data_list = []
        lead_phone_number_data: BoardLeadPhoneNumberFieldData = obj.custom_phone_number_data.all()
        for phone_number_data in lead_phone_number_data:
            phone_number_data_list.append(
                {
                    "name": phone_number_data.field.name,
                    "value": (
                        PhoneNumberSerializer(
                            {
                                "national_number": phone_number_data.data.national_number,
                                "country_code": phone_number_data.data.country_code,
                            }
                        ).data
                        if phone_number_data.data
                        else None
                    ),
                    "type": phone_number_data.field.type,
                    "position": phone_number_data.field.position,
                    "section_id": phone_number_data.field.section_id,
                }
            )
        return phone_number_data_list

    def get_file_data(self, obj):
        file_data_list = []
        lead_file_data: BoardLeadFileFieldData = obj.custom_file_data.all()
        for file_data in lead_file_data:
            file_data_list.append(
                {
                    "name": file_data.field.name,
                    "value": {"name": file_data.name, "url": file_data.data.url} if file_data.data else None,
                    "type": file_data.field.type,
                    "position": file_data.field.position,
                    "section_id": file_data.field.section_id,
                }
            )
        return file_data_list

    def get_multi_dropdown_data(self, obj):
        multi_dropdown_data_list = []
        multi_dropdown_data: List[BoardLeadDropDownFieldData] = obj.custom_drop_down_data.all()
        multi_dropdown_map = defaultdict(list)
        for drop_down in multi_dropdown_data:
            if drop_down.field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                if drop_down.field.id not in multi_dropdown_map:
                    multi_dropdown_map[drop_down.field.id] = [drop_down.data.name] if drop_down.data else []
                else:
                    if drop_down.data:
                        multi_dropdown_map[drop_down.field.id].append(drop_down.data.name)

        field_map = {d.field_id: d for d in multi_dropdown_data}
        if multi_dropdown_map:
            for key, value in multi_dropdown_map.items():
                multi_dropdown_data_list.append(
                    {
                        "name": field_map[key].field.name,
                        "value": value,
                        "type": field_map[key].field.type,
                        "position": field_map[key].field.position,
                        "section_id": field_map[key].field.section_id,
                    }
                )
        return multi_dropdown_data_list

    def get_dropdown_data(self, obj):
        dropdown_data_list = []
        lead_dropdown_data: List[BoardLeadDropDownFieldData] = obj.custom_drop_down_data.all()
        for drop_down in lead_dropdown_data:
            if drop_down.field.type == CustomFieldTypeEnum.DROPDOWN.value:
                dropdown_data_list.append(
                    {
                        "name": drop_down.field.name,
                        "value": drop_down.data.name if drop_down.data else None,
                        "type": drop_down.field.type,
                        "position": drop_down.field.position,
                        "section_id": drop_down.field.section_id,
                    }
                )
        return dropdown_data_list

    def get_custom_fields(self, obj):
        data = []
        text_data = self.get_text_data(obj)
        multi_dropdown_data = self.get_multi_dropdown_data(obj)
        drop_down_data = self.get_dropdown_data(obj)
        email_data = self.get_email_data(obj)
        number_data = self.get_number_data(obj)
        date_data = self.get_date_data(obj)
        datetime_data = self.get_datetime_data(obj)
        boolean_data = self.get_boolean_data(obj)
        rich_text_data = self.get_rich_text_data(obj)
        phone_number_data = self.get_phone_number_data(obj)
        file_data = self.get_file_data(obj)

        if text_data:
            data.extend(text_data)
        if drop_down_data:
            data.extend(drop_down_data)
        if multi_dropdown_data:
            data.extend(multi_dropdown_data)
        if email_data:
            data.extend(email_data)
        if number_data:
            data.extend(number_data)
        if date_data:
            data.extend(date_data)
        if datetime_data:
            data.extend(datetime_data)
        if boolean_data:
            data.extend(boolean_data)
        if rich_text_data:
            data.extend(rich_text_data)
        if phone_number_data:
            data.extend(phone_number_data)
        if file_data:
            data.extend(file_data)
        data = sorted(data, key=lambda x: (x["section_id"], x["position"]))
        return data

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not hasattr(instance, "lead_project_data"):
            data.pop("state", None)
            data.pop("city", None)
            data.pop("address", None)
        return data

    class Meta:
        model = Lead
        fields = (
            "id",
            "name",
            "custom_fields",
            "company",
            "country",
            "state",
            "city",
            "contacts",
            "assignees",
            "stage",
            "position",
            "amount",
            "created_by",
            "created_at",
            "sorted_at",
            "actions",
            "address_line_1",
            "address_line_2",
            "zip_code",
            "board_id",
            "lead_number",
        )


class LeadAssigneeListSerialzer(LeadAssigneeModelSerializer):
    name = serializers.SerializerMethodField()
    id = serializers.CharField(source="user_id")

    def get_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"

    class Meta(LeadAssigneeModelSerializer.Meta):
        fields = (
            "id",
            "name",
        )


class LeadNamesListSerializer(BaseModelSerializer):
    name = serializers.SerializerMethodField()
    lead_number = serializers.SerializerMethodField()

    def get_name(self, obj):
        return f"{obj.name}"

    def get_lead_number(self, obj):
        return add_lead_number_padding(obj.serial_number)

    class Meta:
        model = Lead
        fields = ("id", "name", "lead_number")


class LeadCompanyListSerializer(BaseSerializer):
    name = serializers.CharField(source="company__name")
    id = serializers.CharField(source="company_id")

    class Meta:
        fields = ("id", "name")


class LeadStateListSerializer(BaseSerializer):
    name = serializers.SerializerMethodField()
    id = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_name(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.state:
            return obj.lead_project_data.state.name
        return None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_id(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.state:
            return obj.lead_project_data.state.id
        return None

    class Meta:
        fields = ("id", "name")


class LeadCityListSerializer(BaseSerializer):
    name = serializers.SerializerMethodField()
    id = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_name(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.city:
            return obj.lead_project_data.city.name
        return None

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_id(self, obj):
        if hasattr(obj, "lead_project_data") and obj.lead_project_data and obj.lead_project_data.city:
            return obj.lead_project_data.city.id
        return None

    class Meta:
        fields = ("id", "name")


class LeadCreatorListSerializer(BaseSerializer):
    name = serializers.SerializerMethodField()
    id = serializers.CharField(source="created_by")

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_name(self, obj):
        return f"{obj.get('created_by__first_name')} {obj.get('created_by__last_name')}"

    class Meta:
        fields = ("id", "name")


class LeadContactListSerializer(LeadContactModelSerializer):
    name = serializers.SerializerMethodField()
    id = serializers.CharField(source="contact_id")

    def get_name(self, obj):
        return obj.contact.name

    class Meta(LeadContactModelSerializer.Meta):
        fields = ("id", "name")


class LeadActivityMetaDetaSerializer(BaseSerializer):
    title = serializers.CharField(allow_null=True, required=False)


class LeadEventModelSerializer(BaseModelSerializer):
    class Meta:
        model = LeadEvent
        fields = "__all__"


class LeadCreatedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    source = serializers.SerializerMethodField()

    def get_source(self, obj):
        return obj.event.lead.source.replace("_", " ").title() if obj.event.lead.source else None

    class Meta:
        model = LeadCreatedEventData
        fields = "__all__"


class LeadAssignedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    name = serializers.CharField(source="assigned_user.name")
    photo = serializers.ImageField(source="assigned_user.photo")

    class Meta:
        model = LeadAssignedEventData
        fields = "__all__"


class LeadStageChangeEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    from_stage_name = serializers.CharField(source="from_stage.name")
    to_stage_name = serializers.CharField(source="to_stage.name")

    class Meta:
        model = LeadStageChangedEventData
        fields = "__all__"


class LeadReminderCreatedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    class Meta:
        model = LeadReminderCreatedEventData
        fields = "__all__"


class LeadTaskCreatedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    breadcrumb = serializers.SerializerMethodField()
    task_assignee = serializers.CharField(source="event.task_assignee")
    description = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()
    due_at = serializers.DateTimeField(source="task.due_at")
    task_reply_count = serializers.IntegerField(source="event.task_reply_count")
    task_id = HashIdField()

    @swagger_serializer_method(
        serializer_or_field=serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
    )
    def get_description(self, obj):
        serializer = TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
        attachments = []
        description = []
        for block in obj.task.description:
            data = serializer.to_representation(block)
            if block.get("type") in (const.ATTACHMENT, const.IMAGE):
                attachments.append(
                    {
                        "type": block.get("type"),
                        "file_name": block.get("meta").get("file_name"),
                        "url": PublicMediaFileStorage.url(block.get("meta").get("url")),
                    }
                )
            else:
                description.append(data)

        self.context["attachments"] = attachments
        return description

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_breadcrumb(self, obj):
        return get_breadcrumb(board_name=obj.event.lead.board.name, lead_name=obj.event.lead.name)

    @swagger_serializer_method(serializer_or_field=AttachmentSerializer(many=True))
    def get_attachments(self, obj):
        return AttachmentSerializer(self.context.get("attachments"), many=True).data

    class Meta:
        model = LeadTaskCreatedEventData
        fields = "__all__"


class LeadCommentCreatedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    blocks = serializers.SerializerMethodField()
    comment_reply_count = serializers.IntegerField(source="event.comment_reply_count")
    comment_id = HashIdField()

    def get_blocks(self, obj):
        serializer_data = TypeOutputSerializer(
            instance=obj.comment.blocks,
            many=True,
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.LINE_BREAK: None,
                    const.APPROVAL: None,
                    const.REJECTED: None,
                    const.PIN: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                },
                "request": self.context.get("request"),
            },
        ).data
        return serializer_data

    class Meta:
        model = LeadCommentCreatedEventData
        fields = "__all__"


class LeadConvertedToProjectEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    project_id = HashIdField(source="lead.project.id")
    project_name = serializers.CharField(source="lead.project.name")
    job_id = serializers.CharField(source="lead.project.job_id")

    class Meta:
        model = LeadConvertedToProjectEventData
        fields = "__all__"


class LeadQuotationSubmittedEventModelSerializer(BaseModelSerializer, LeadActivityMetaDetaSerializer):
    quotation_id = HashIdField()
    ref_number = serializers.SerializerMethodField()
    amount = serializers.DecimalField(source="quotation_amount", max_digits=20, decimal_places=4)
    preview_pdf = serializers.CharField()

    def get_ref_number(self, obj):
        return f"QUOT/{obj.quotation.ref_number}"

    class Meta:
        model = LeadQuotationSubmittedEventData
        fields = "__all__"


class LeadActivitySerializer(LeadEventModelSerializer):
    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "LeadActivityUserSerializer"
            fields = ("id", "name", "photo")

    class LeadCreatedEventSerializer(LeadCreatedEventModelSerializer):
        class Meta(LeadCreatedEventModelSerializer.Meta):
            fields = ("name", "source")

    class LeadAssignedEventSerializer(LeadAssignedEventModelSerializer):
        class Meta(LeadAssignedEventModelSerializer.Meta):
            fields = ("name", "photo")

    class LeadStageChangeEventSerializer(LeadStageChangeEventModelSerializer):
        class Meta(LeadStageChangeEventModelSerializer.Meta):
            fields = ("from_stage_name", "to_stage_name")

    class LeadReminderCreatedEventSerializer(LeadReminderCreatedEventModelSerializer):
        class Meta(LeadReminderCreatedEventModelSerializer.Meta):
            fields = "__all__"

    class LeadTaskCreatedEventSerializer(LeadTaskCreatedEventModelSerializer):
        class Meta(LeadTaskCreatedEventModelSerializer.Meta):
            fields = (
                "task_id",
                "breadcrumb",
                "task_assignee",
                "description",
                "due_at",
                "task_reply_count",
                "attachments",
            )

    class LeadCommentCreatedEventSerializer(LeadCommentCreatedEventModelSerializer):
        class Meta(LeadCommentCreatedEventModelSerializer.Meta):
            fields = ("comment_id", "blocks", "comment_reply_count")

    class LeadConvertedToProjectEventSerializer(LeadConvertedToProjectEventModelSerializer):
        class Meta(LeadConvertedToProjectEventModelSerializer.Meta):
            fields = ("project_name", "job_id", "project_id")

    class LeadQuotationSubmittedEventSerializer(LeadQuotationSubmittedEventModelSerializer):
        class Meta(LeadQuotationSubmittedEventModelSerializer.Meta):
            fields = ("quotation_id", "ref_number", "amount", "preview_pdf")

    lead_created_data = LeadCreatedEventSerializer(source="created_event_data")
    lead_assigned_data = LeadAssignedEventSerializer(source="assigned_event_data", many=True)
    lead_stage_changed_data = LeadStageChangeEventSerializer(source="stage_changed_event_data")
    lead_reminder_created_data = LeadReminderCreatedEventSerializer(source="reminder_created_event_data")
    lead_task_created_data = LeadTaskCreatedEventSerializer(source="task_created_event_data")
    lead_comment_created_data = LeadCommentCreatedEventSerializer(source="comment_created_event_data")
    lead_converted_to_project_data = serializers.SerializerMethodField()
    lead_quotation_submitted_data = LeadQuotationSubmittedEventSerializer(source="quotation_submitted_event_data")
    event_type = serializers.CharField(source="type")
    created_at = serializers.DateTimeField()
    created_by = UserSerializer()

    @swagger_serializer_method(serializer_or_field=LeadConvertedToProjectEventModelSerializer())
    def get_lead_converted_to_project_data(self, obj):
        if obj.type == "lead_converted_to_project":
            return self.LeadConvertedToProjectEventSerializer(obj).data
        return None

    class Meta(LeadEventModelSerializer.Meta):
        fields = (
            "event_type",
            "lead_created_data",
            "lead_assigned_data",
            "lead_stage_changed_data",
            "lead_reminder_created_data",
            "lead_task_created_data",
            "lead_comment_created_data",
            "lead_converted_to_project_data",
            "lead_quotation_submitted_data",
            "created_at",
            "created_by",
        )


class StateModelSerializer(BaseModelSerializer):
    class Meta:
        ref_name = "StateModelSerializer"
        model = State
        fields = ("id", "name")


class CompanyModelSerializer(BaseModelSerializer):
    class CountrySerializer(CountryModelSerializer):
        class Meta(CountryModelSerializer.Meta):
            ref_name = "CompanyCountrySerializer"
            fields = ("id", "name")

    class StateModelSerializer(BaseModelSerializer):
        class Meta:
            ref_name = "CompanyStateModelSerializer"
            model = State
            fields = ("id", "name")

    class CityModelSerializer(BaseModelSerializer):
        class Meta:
            ref_name = "CompanyCityModelSerializer"
            model = City
            fields = ("id", "name")

    class ContactModelSerializer(BaseModelSerializer):
        name = serializers.CharField(source="contact_name")
        phone = serializers.SerializerMethodField()
        email = serializers.EmailField(source="contact_email")

        def get_phone(self, obj):
            if hasattr(obj, "contact_phone") and obj.contact_phone:
                return {"number": obj.contact_phone.national_number, "country_code": obj.contact_phone.country_code}

        class Meta:
            model = Contact
            fields = ("name", "phone", "email")

    country = CountrySerializer()
    state = StateModelSerializer()
    city = CityModelSerializer()
    contact = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=ContactModelSerializer())
    def get_contact(self, obj):
        return {
            "name": obj.contact_name,
            "phone": (
                {"country_code": obj.contact_phone[:-10], "number": int(obj.contact_phone[-10:])}
                if obj.contact_phone
                else None
            ),
            "email": obj.contact_email,
        }

    class Meta:
        model = Company
        fields = "__all__"


class CompanyInputDataSerializer(BaseDataclassSerializer):
    id = HashIdField(required=False, allow_null=True)
    name = serializers.CharField(max_length=100)
    state_id = HashIdField(required=False, allow_null=True)
    city_id = HashIdField(required=False, allow_null=True)
    zip_code = serializers.CharField(max_length=10, required=False, allow_null=True, allow_blank=True)
    address_line_1 = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    address_line_2 = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    contact_ids = serializers.ListField(child=HashIdField(), required=False, allow_null=True)
    country_id = HashIdField(required=False, allow_null=True)

    class Meta:
        dataclass = CompanyEntity
        ref_name = "CompanyCreateInput"


class ContatModelSerializer(BaseModelSerializer):
    class Meta:
        model = Contact
        fields = "__all__"


class CompanyToClientSerializer(CompanyModelSerializer):
    class ContactSerializer(ContatModelSerializer):
        name = serializers.CharField(source="contact.name")
        email = serializers.EmailField(source="contact.email")
        phone = serializers.SerializerMethodField()

        def get_phone(self, obj):
            if hasattr(obj, "contact.phone") and obj.contact.phone:
                return {"number": obj.contact.phone.national_number, "country_code": obj.contact.phone.country_code}

        class Meta(ContatModelSerializer.Meta):
            fields = ("id", "name", "email", "phone")
            ref_name = "CompanyContactSerializer"

    addresses = serializers.SerializerMethodField()
    contacts = ContactSerializer(many=True)

    @swagger_serializer_method(serializer_or_field=serializers.ListField())
    def get_addresses(self, obj):
        return [
            {
                "address_line_1": obj.address_line_1,
                "address_line_2": obj.address_line_2,
                "city": {"id": HashIdConverter.encode(obj.city.id), "name": obj.city.name} if obj.city else None,
                "state": {"id": HashIdConverter.encode(obj.state.id), "name": obj.state.name} if obj.state else None,
                "country": (
                    {"id": HashIdConverter.encode(obj.country.id), "name": obj.country.name} if obj.country else None
                ),
                "zip_code": obj.zip_code,
            }
        ]

    class Meta(CompanyModelSerializer.Meta):
        fields = ("id", "name", "addresses", "contacts")
        ref_name = "CompanyToClientDataSerializer"


class LeadFilterSerializer(BaseSerializer):
    lead_ids = SplitCharHashIdListField(required=False, allow_null=True)
    company_ids = SplitCharHashIdListField(allow_empty=True, required=False)
    assignee_ids = SplitCharHashIdListField(allow_empty=True, required=False)
    contact_ids = SplitCharHashIdListField(allow_empty=True, required=False)
    created_by_ids = SplitCharHashIdListField(allow_empty=True, required=False)
    search_text = serializers.CharField(required=False, allow_null=True)
    sources = SplitCharListField(required=False)
    lead_creation_start_date = CustomDateField(required=False)
    lead_creation_end_date = CustomEndDateField(required=False)
    city_ids = SplitCharHashIdListField(allow_empty=True, required=False)
    state_ids = SplitCharHashIdListField(allow_empty=True, required=False)

    def validate_sources(self, value):
        for source in value:
            if source not in LeadSourceChoices.values:
                raise serializers.ValidationError({source: "Invalid source"})
        return value

    class Meta:
        ref_name = "LeadListFilter"


class ContactFilterSerializer(BaseSerializer):
    company_id = HashIdField(required=False)
    search_text = serializers.CharField(required=False, allow_null=True)

    class Meta:
        ref_name = "ContactListFilter"


class BoardAvailableFieldsOutputSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = LeadFieldForImportSectionEntity
        ref_name = "BoardAvailableFieldsOutputSerializer"
