from typing import Dict, List
from uuid import UUID

import structlog
from django.utils import timezone

from common.constants import CustomFieldTypeEnum
from common.exceptions import BaseValidationError
from common.services import entity_update
from crm.board.data.abstract_repositories import (
    BoardAbstractRepo,
    BoardSectionAbstractRepo,
    BoardStageAbstractRepo,
    BoardUserAbstractRepo,
)
from crm.board.data.caches import BoardAssignmentCache
from crm.board.domain.constants import (
    BOARD_CARD_SETTING_DISABLED_FIELD_UUIDS,
    BOARD_LEAD_NON_EDITABLE_FIELD_UUIDS,
    BOARD_USER_CONFIG,
    DEFAULT_SECTION_DATA,
    DEFAULT_SECTION_DATA_FOR_PROJECT_TYPE_LEADS,
    DEFAULT_STAGE_DATA,
    LEAD_CONVERT_TO_PROJECT_FIELD_UUIDS,
    LEAD_SYSTEM_FIELD_UUIDS,
    LEAD_SYSTEM_SECTION_UUIDS,
)
from crm.board.domain.entities import (
    BoardInputEntity,
    BoardLeadDropDownOptionRepoInputEntity,
    BoardLeadFieldEntity,
    BoardLeadFieldRepoInputEntity,
    BoardLeadSectionEntity,
    BoardLeadSectionRepoInputEntity,
    BoardRepoInputEntity,
    BoardStageInputEntity,
    BoardStageRepoInputEntity,
    BoardStageRepoOutputEntity,
    BoardUserRepoEntity,
    BoardUsersSettingUpdateConfigEntity,
)
from crm.board.domain.enums import (
    BoardUserCreationSettingEnum,
    BoardUserDeleteSettingEnum,
    BoardUserEditSettingEnum,
    BoardUserSettingEnum,
    BoardUserViewSettingEnum,
)

logger = structlog.getLogger(__name__)


class BoardStageService:
    class BoardStageCreateException(BaseValidationError):
        pass

    class BoardStageUpdateException(BaseValidationError):
        pass

    class BoardStageDuplicateException(BaseValidationError):
        pass

    def __init__(self, repo: BoardStageAbstractRepo, lead_service):
        self.board_stage_repo = repo
        self.lead_service = lead_service

    def create_default_stages(self, board_id: int, user_id: int):
        default_stage_data = DEFAULT_STAGE_DATA
        stages_data = []
        for stage_data in default_stage_data:
            stages_data.append(
                BoardStageRepoInputEntity(
                    name=stage_data.name,
                    board_id=board_id,
                    position=stage_data.position,
                )
            )
        self.board_stage_repo.create_stages(stages_data=stages_data, user_id=user_id)

    def _get_stage_ids(self, board_id: int):
        return self.board_stage_repo.get_stage_ids(board_id=board_id)

    def _check_duplicate_stages(self, stages_data: BoardStageInputEntity):
        stage_names = set([stage.name.lower() for stage in stages_data])
        return len(stage_names) != len(stages_data)

    def _create_stage_name_to_id_mapping(self, stages_data: list[BoardStageRepoOutputEntity]):
        return {stage.name: stage.id for stage in stages_data} if stages_data else {}

    def update_stages(self, stages_data: BoardStageInputEntity, board_id: int, user_id: int):
        if self._check_duplicate_stages(stages_data):
            raise self.BoardStageDuplicateException("Duplicate stage names are not allowed")

        stages_to_be_created = []
        stages_to_be_updated = []
        from_to_stage_ids_mapping_list = []
        stage_name_to_stage_ids_mapping = {}

        stage_ids_to_be_updated = []

        for stage_data in stages_data:
            if stage_data.id:
                stage_ids_to_be_updated.append(stage_data.id)
                stages_to_be_updated.append(
                    BoardStageRepoInputEntity(
                        id=stage_data.id,
                        name=stage_data.name,
                        board_id=board_id,
                        position=stage_data.position,
                    )
                )
            else:
                stages_to_be_created.append(
                    BoardStageRepoInputEntity(
                        name=stage_data.name,
                        board_id=board_id,
                        position=stage_data.position,
                    )
                )

            stage_name_to_stage_ids_mapping[stage_data.name] = stage_data.cards_stage_ids

        existing_stage_ids = self._get_stage_ids(board_id=board_id)
        stage_ids_to_be_deleted = list(set(existing_stage_ids) - set(stage_ids_to_be_updated))

        new_stages_data = self.board_stage_repo.update_stage_data(
            board_id=board_id,
            user_id=user_id,
            stage_data_for_updation=stages_to_be_updated,
            stage_data_for_creation=stages_to_be_created,
            stage_ids_for_deletion=stage_ids_to_be_deleted,
        )

        new_stages_mapping = self._create_stage_name_to_id_mapping(stages_data=new_stages_data)

        for stage_data in stages_data:
            if stage_data.cards_stage_ids:
                if not stage_data.id:
                    from_to_stage_ids_mapping_list.append(
                        {
                            "to_stage_id": new_stages_mapping[stage_data.name],
                            "from_stage_ids": stage_data.cards_stage_ids,
                        }
                    )
                else:
                    from_to_stage_ids_mapping_list.append(
                        {"to_stage_id": stage_data.id, "from_stage_ids": stage_data.cards_stage_ids}
                    )

        if from_to_stage_ids_mapping_list:
            self.lead_service.transfer_leads_to_stage_ids(from_to_stage_ids_mapping_list=from_to_stage_ids_mapping_list)


class BoardSectionService:
    class BoardSectionServiceException(BaseValidationError):
        pass

    def __init__(self, repo: BoardSectionAbstractRepo):
        self.board_section_repo = repo

    def _create_mappings(self, section_data: list[BoardLeadSectionRepoInputEntity]):
        section_uuid_to_fields_mapping = {}
        field_uuid_to_dropdown_options_mapping = {}
        for section in section_data:
            section_uuid_to_fields_mapping[section.uuid] = section.fields
            for field in section.fields:
                field_uuid_to_dropdown_options_mapping[field.uuid] = field.options
        return section_uuid_to_fields_mapping, field_uuid_to_dropdown_options_mapping

    def create_default_sections(self, board_id: int, user_id: int, is_project_conversion_allowed: bool):
        if is_project_conversion_allowed:
            default_section_data = DEFAULT_SECTION_DATA_FOR_PROJECT_TYPE_LEADS
        else:
            default_section_data = DEFAULT_SECTION_DATA
        sections_data = []
        for section_data in default_section_data:
            fields_data = []
            for field_data in section_data.fields:
                fields_data.append(
                    BoardLeadFieldRepoInputEntity(
                        name=field_data.name,
                        board_id=board_id,
                        uuid=field_data.uuid,
                        position=field_data.position,
                        is_required=field_data.is_required,
                        is_archived=False,
                        type=field_data.type,
                        created_at=timezone.now(),
                        created_by_id=user_id,
                    )
                )
            sections_data.append(
                BoardLeadSectionRepoInputEntity(
                    name=section_data.name,
                    board_id=board_id,
                    position=section_data.position,
                    uuid=section_data.uuid,
                    fields=fields_data,
                    created_at=timezone.now(),
                    created_by_id=user_id,
                )
            )
        section_uuid_to_fields_mapping, field_uuid_to_dropdown_options_mapping = self._create_mappings(sections_data)
        return self.board_section_repo.create_sections_with_fields_and_dropdowns(
            sections_data=sections_data,
            section_uuid_to_fields_mapping=section_uuid_to_fields_mapping,
            field_uuid_to_dropdown_options_mapping=field_uuid_to_dropdown_options_mapping,
        )

    def get_sections(self, board_id: int):
        return self.board_section_repo.get_sections(board_id=board_id)

    def get_sections_pydantic(self, board_id: int):
        return self.board_section_repo.get_sections_pydantic(board_id=board_id)

    def check_duplicate_sections(self, sections_data: list[BoardLeadSectionEntity]):
        section_names = set([section.name.lower() for section in sections_data])
        section_uuids = set([section.uuid for section in sections_data])
        return len(section_names) != len(sections_data) or len(section_uuids) != len(sections_data)

    def check_duplicate_fields(self, fields_data: list[BoardLeadFieldEntity]):
        field_names = set([field.name.lower() for field in fields_data])
        field_uuids = set([field.uuid for field in fields_data])
        return len(field_names) != len(fields_data) or len(field_uuids) != len(fields_data)

    def _check_fields_delete(self, delete_fields_uuids: set):
        delete_fields_uuids = set(map(str, delete_fields_uuids))
        if delete_fields_uuids.intersection(LEAD_SYSTEM_FIELD_UUIDS):
            return True
        return False

    def _check_sections_delete(self, delete_sections_uuids: set):
        delete_sections_uuids = set(map(str, delete_sections_uuids))
        if delete_sections_uuids.intersection(LEAD_SYSTEM_FIELD_UUIDS):
            return True
        return False

    def _prepare_section_data_for_creation(self, section_data: BoardLeadSectionEntity, board_id: int, user_id: int):
        return BoardLeadSectionRepoInputEntity(
            name=section_data.name,
            board_id=board_id,
            position=section_data.position,
            uuid=section_data.uuid,
            fields=[
                BoardLeadFieldRepoInputEntity(
                    name=field_data.name,
                    board_id=board_id,
                    uuid=field_data.uuid,
                    position=field_data.position,
                    is_required=field_data.is_required,
                    is_archived=field_data.is_archived,
                    type=field_data.type,
                    created_at=timezone.now(),
                    created_by_id=user_id,
                    archived_at=timezone.now() if field_data.is_archived else None,
                    archived_by_id=user_id if field_data.is_archived else None,
                    options=[
                        BoardLeadDropDownOptionRepoInputEntity(
                            name=option.name,
                            uuid=option.uuid,
                            is_archived=option.is_archived,
                            created_at=timezone.now(),
                            created_by_id=user_id,
                            archived_at=timezone.now() if option.is_archived else None,
                            archived_by_id=user_id if option.is_archived else None,
                        )
                        for option in field_data.options
                        if hasattr(field_data, "options")
                    ],
                )
                for field_data in section_data.fields
            ],
            created_at=timezone.now(),
            created_by_id=user_id,
        )

    def _prepare_field_data_for_creation(self, field_data: BoardLeadFieldEntity, board_id: int, user_id: int):
        return BoardLeadFieldRepoInputEntity(
            name=field_data.name,
            board_id=board_id,
            uuid=field_data.uuid,
            position=field_data.position,
            is_required=field_data.is_required,
            is_archived=field_data.is_archived,
            type=field_data.type,
            created_at=timezone.now(),
            created_by_id=user_id,
            archived_at=timezone.now() if field_data.is_archived else None,
            archived_by_id=user_id if field_data.is_archived else None,
            options=(
                [
                    BoardLeadDropDownOptionRepoInputEntity(
                        name=option.name,
                        uuid=option.uuid,
                        is_archived=option.is_archived,
                        archived_at=timezone.now() if option.is_archived else None,
                        archived_by_id=user_id if option.is_archived else None,
                        created_at=timezone.now(),
                        created_by_id=user_id,
                    )
                    for option in field_data.options
                ]
                if field_data.type in [CustomFieldTypeEnum.DROPDOWN.value, CustomFieldTypeEnum.MULTI_DROPDOWN.value]
                else []
            ),
        )

    def _prepare_dropdown_option_data_for_creation(
        self, option_data: BoardLeadDropDownOptionRepoInputEntity, user_id: int
    ):
        return BoardLeadDropDownOptionRepoInputEntity(
            name=option_data.name,
            uuid=option_data.uuid,
            is_archived=False,
            created_at=timezone.now(),
            created_by_id=user_id,
        )

    def _prepare_field_data_for_updation(
        self, field_data: BoardLeadFieldEntity, board_id: int, user_id: int, existing_dropdown_uuids: set
    ):
        return BoardLeadFieldRepoInputEntity(
            name=field_data.name,
            board_id=board_id,
            uuid=field_data.uuid,
            position=field_data.position,
            is_required=field_data.is_required,
            is_archived=field_data.is_archived,
            type=field_data.type,
            created_at=None,
            created_by_id=None,
            options=(
                [
                    BoardLeadDropDownOptionRepoInputEntity(
                        name=option.name,
                        uuid=option.uuid,
                        is_archived=option.is_archived,
                        archived_at=None,
                        archived_by_id=None,
                    )
                    for option in field_data.options
                    if option.uuid in existing_dropdown_uuids
                ]
                if field_data.type in [CustomFieldTypeEnum.DROPDOWN.value, CustomFieldTypeEnum.MULTI_DROPDOWN.value]
                else []
            ),
        )

    def _prepare_updation_data(
        self,
        sections_to_be_updated: list[BoardLeadSectionRepoInputEntity],
        board_id: int,
        user_id: int,
        fields_for_board_lead_field: list[str],
        fields_for_board_lead_dropdown: list[str],
        section_uuid_to_obj_mapping: dict,
        field_uuid_to_obj_mapping: dict,
        option_uuid_to_obj_mapping: dict,
    ):
        updated_fields_objs = []
        updated_section_objs = []
        updated_dropdown_options_objs = []
        archive_flag_for_field = False
        archive_flag_for_option = False

        for section in sections_to_be_updated:
            for board_field in section.fields:
                for option_data in board_field.options:
                    option_entity = option_uuid_to_obj_mapping.get(option_data.uuid)
                    fields_for_comparison = fields_for_board_lead_dropdown.copy()
                    if hasattr(option_data, "is_archived"):
                        if option_data.is_archived is True and option_entity.archived_at is None:
                            option_data.archived_by_id = user_id
                            option_data.archived_at = timezone.now()
                            fields_for_comparison.extend(["archived_at", "archived_by_id"])
                            archive_flag_for_option = True
                        elif option_data.is_archived is False and option_entity.archived_at is not None:
                            option_data.archived_by_id = None
                            option_data.archived_at = None
                            fields_for_comparison.extend(["archived_at", "archived_by_id"])
                            archive_flag_for_option = True
                    updated_option, is_updated = entity_update(
                        entity=option_entity,
                        fields=fields_for_comparison,
                        data=option_data,
                        updated_by_id=user_id,
                    )
                    if is_updated:
                        updated_dropdown_options_objs.append(updated_option)

                board_field_entity = field_uuid_to_obj_mapping.get(board_field.uuid)
                fields_for_comparison = fields_for_board_lead_field.copy()
                if hasattr(board_field, "is_archived"):
                    if board_field.is_archived is True and board_field_entity.archived_at is None:
                        board_field.archived_by_id = user_id
                        board_field.archived_at = timezone.now()
                        fields_for_comparison.extend(["archived_at", "archived_by_id"])
                        archive_flag_for_field = True
                    elif board_field.is_archived is False and board_field_entity.archived_at is not None:
                        board_field.archived_by_id = None
                        board_field.archived_at = None
                        fields_for_comparison.extend(["archived_at", "archived_by_id"])
                        archive_flag_for_field = True
                updated_board_field, is_updated = entity_update(
                    entity=board_field_entity,
                    fields=fields_for_comparison,
                    data=board_field,
                    updated_by_id=user_id,
                )
                if is_updated:
                    updated_fields_objs.append(updated_board_field)
            section_entity = section_uuid_to_obj_mapping.get(section.uuid)
            updated_section, is_updated = entity_update(
                entity=section_entity,
                fields=["position"],
                data=section,
                updated_by_id=user_id,
            )
            if is_updated:
                updated_section_objs.append(updated_section)

        if archive_flag_for_field:
            fields_for_board_lead_field.extend(["archived_at", "archived_by_id"])
        if archive_flag_for_option:
            fields_for_board_lead_dropdown.extend(["archived_at", "archived_by_id"])

        return {
            "updated_section_objs_and_updated_fields": [updated_section_objs, ["position"]],
            "updated_fields_objs_and_updated_fields": [updated_fields_objs, fields_for_board_lead_field],
            "updated_dropdown_options_objs_and_updated_fields": [
                updated_dropdown_options_objs,
                fields_for_board_lead_dropdown,
            ],
        }

    def update_section(self, sections_data: list[BoardLeadSectionEntity], board_id: int, user_id: int):
        logger.info("Board Lead Details Update Started")
        # Duplicacy Check
        fields_data = []
        field_uuids_to_add_in_existing_leads: list[UUID] = []
        for section in sections_data:
            fields_data.extend(section.fields)
            for field in section.fields:
                if field.add_to_existing_leads is True:
                    field_uuids_to_add_in_existing_leads.append(field.uuid)

        if self.check_duplicate_sections(sections_data=sections_data) or self.check_duplicate_fields(
            fields_data=fields_data
        ):
            raise self.BoardSectionServiceException("Duplicate section names or field names are not allowed")

        # Get existing sections, fields and dropdown UUIDS
        (
            section_uuid_to_field_uuids_mapping,
            field_uuid_to_dropdown_uuids_mapping,
            section_uuid_to_obj_mapping,
            field_uuid_to_obj_mapping,
            option_uuid_to_obj_mapping,
            section_uuid_to_section_id_mapping,
            field_uuid_to_field_id_mapping,
        ) = self.board_section_repo.get_uuid_mappings(board_id=board_id)

        existing_sections_uuids = set()
        existing_fields_uuids = set()
        existing_dropdown_uuids = set()

        for section_uuid, field_uuids in section_uuid_to_field_uuids_mapping.items():
            existing_fields_uuids = existing_fields_uuids.union(field_uuids)
            existing_sections_uuids.add(section_uuid)

        for field_uuid, dropdown_uuids in field_uuid_to_dropdown_uuids_mapping.items():
            existing_dropdown_uuids = existing_dropdown_uuids.union(dropdown_uuids)

        ##########
        section_uuids_in_updated_data = set()
        field_uuids_in_updated_data = set()
        dropdown_options_uuids_in_updated_data = set()

        section_uuid_to_section_entity_mapping = {}

        # New Sections alongwith Fields and Dropdown Options
        sections_to_be_created = []

        # New Fields and Dropdown Options mappings for new fields in existing sections
        section_uuid_to_new_fields_entities_mapping = {}
        new_fields_uuids_to_new_dropdown_options_entities_mapping = {}

        # New Dropdown Options mappings for existing fields
        field_uuid_to_new_dropdown_options_entities_mapping = {}

        # Traversing through the data
        for section in sections_data:
            if section.uuid not in existing_sections_uuids:
                sections_to_be_created.append(
                    self._prepare_section_data_for_creation(section_data=section, board_id=board_id, user_id=user_id)
                )
            else:
                fields_list = []
                new_fields_list = []
                for field_data in section.fields:
                    if str(field_data.uuid) in BOARD_LEAD_NON_EDITABLE_FIELD_UUIDS:
                        continue

                    new_dropdown_options = []
                    if field_data.type in [
                        CustomFieldTypeEnum.DROPDOWN.value,
                        CustomFieldTypeEnum.MULTI_DROPDOWN.value,
                    ]:
                        if not field_data.options:
                            raise self.BoardSectionServiceException("Dropdown options cannot be empty")

                        for option in field_data.options:
                            if option.uuid not in existing_dropdown_uuids:
                                new_dropdown_options.append(
                                    self._prepare_dropdown_option_data_for_creation(option_data=option, user_id=user_id)
                                )
                            else:
                                dropdown_options_uuids_in_updated_data.add(option.uuid)

                    if field_data.uuid not in existing_fields_uuids:
                        new_fields_list.append(
                            self._prepare_field_data_for_creation(
                                field_data=field_data, board_id=board_id, user_id=user_id
                            )
                        )
                        if new_dropdown_options:
                            new_fields_uuids_to_new_dropdown_options_entities_mapping[field_data.uuid] = (
                                new_dropdown_options
                            )

                    else:
                        field_uuids_in_updated_data.add(field_data.uuid)
                        fields_list.append(
                            self._prepare_field_data_for_updation(
                                field_data=field_data,
                                board_id=board_id,
                                user_id=user_id,
                                existing_dropdown_uuids=existing_dropdown_uuids,
                            )
                        )
                        if new_dropdown_options:
                            field_uuid_to_new_dropdown_options_entities_mapping[field_data.uuid] = new_dropdown_options

                section_uuids_in_updated_data.add(section.uuid)
                section_uuid_to_section_entity_mapping[section.uuid] = BoardLeadSectionRepoInputEntity(
                    name=section.name,
                    board_id=board_id,
                    position=section.position,
                    uuid=section.uuid,
                    fields=fields_list,
                    created_at=None,
                    created_by_id=None,
                )
                if new_fields_list:
                    section_uuid_to_new_fields_entities_mapping[section.uuid] = new_fields_list

        ####################
        # Get sections, fields and dropdown options UUIDS for Deletion

        delete_sections_uuids = list(
            set(map(str, existing_sections_uuids)) - section_uuids_in_updated_data - set(LEAD_SYSTEM_SECTION_UUIDS)
        )
        delete_fields_uuids = list(
            set(map(str, existing_fields_uuids)) - field_uuids_in_updated_data - set(LEAD_SYSTEM_FIELD_UUIDS)
        )
        delete_dropdown_options_uuids = list(
            set(map(str, existing_dropdown_uuids)) - dropdown_options_uuids_in_updated_data
        )

        logger.info(
            "Board Lead Details - sections, fields and dropdowns deletion",
            delete_sections_uuids=delete_sections_uuids,
            delete_fields_uuids=delete_fields_uuids,
            delete_dropdown_options_uuids=delete_dropdown_options_uuids,
        )

        ####################

        if self._check_fields_delete(delete_fields_uuids) or self._check_sections_delete(delete_sections_uuids):
            raise self.BoardSectionServiceException("System fields or sections cannot be deleted")

        updated_sections_data = section_uuid_to_section_entity_mapping.values()

        # Needed while creating sections
        section_uuid_to_fields_mapping, field_uuid_to_dropdown_options_mapping = self._create_mappings(
            sections_to_be_created
        )
        #####

        updated_sections_data = self._prepare_updation_data(
            sections_to_be_updated=updated_sections_data,
            board_id=board_id,
            user_id=user_id,
            fields_for_board_lead_field=["position", "is_required"],
            fields_for_board_lead_dropdown=[],
            section_uuid_to_obj_mapping=section_uuid_to_obj_mapping,
            field_uuid_to_obj_mapping=field_uuid_to_obj_mapping,
            option_uuid_to_obj_mapping=option_uuid_to_obj_mapping,
        )

        logger.info(
            "Board Lead Details - sections, fields and dropdowns updation", updated_sections_data=updated_sections_data
        )
        logger.info(
            "Board Lead Details - sections, fields and dropdowns creation",
            sections_to_be_created=sections_to_be_created,
        )

        try:
            self.board_section_repo.update_sections(
                updated_sections_data=updated_sections_data,
                sections_to_be_created=sections_to_be_created,
                board_id=board_id,
                user_id=user_id,
                section_uuid_to_fields_mapping=section_uuid_to_fields_mapping,
                field_uuid_to_dropdown_options_mapping=field_uuid_to_dropdown_options_mapping,
                section_uuid_to_new_fields_mapping=section_uuid_to_new_fields_entities_mapping,
                new_fields_uuids_to_new_dropdown_options_entities_mapping=new_fields_uuids_to_new_dropdown_options_entities_mapping,
                field_uuid_to_new_dropdown_options_mapping=field_uuid_to_new_dropdown_options_entities_mapping,
                section_uuid_to_section_id_mapping=section_uuid_to_section_id_mapping,
                field_uuid_to_field_id_mapping=field_uuid_to_field_id_mapping,
            )
            self.board_section_repo.delete_sections(
                delete_sections_uuids=delete_sections_uuids,
                delete_fields_uuids=delete_fields_uuids,
                delete_dropdown_options_uuids=delete_dropdown_options_uuids,
                board_id=board_id,
                user_id=user_id,
            )

        except BoardSectionAbstractRepo.FieldDataCounterException as e:
            raise self.BoardSectionServiceException(e.message)

        if field_uuids_to_add_in_existing_leads:
            logger.info(
                "Dispatching field creation in all leads",
                field_uuids_to_add_in_existing_leads=field_uuids_to_add_in_existing_leads,
            )
            self.board_section_repo.dispatch_field_creation_in_all_leads(
                field_uuids=field_uuids_to_add_in_existing_leads, board_id=board_id, user_id=user_id
            )
            logger.info("Field creation in all leads dispatched successfully.")

        logger.info("Board Lead Details Update Completed.")

    def update_card_settings(self, fields_data: list, user_id: int, board_id: int):
        visible_fields_uuids = []
        invisible_fields_uuids = []
        for field in fields_data:
            if field.is_visible:
                visible_fields_uuids.append(field.uuid)
            else:
                invisible_fields_uuids.append(field.uuid)
        # Check if card setting required fields are being disabled
        if set(invisible_fields_uuids).intersection(
            set(BOARD_CARD_SETTING_DISABLED_FIELD_UUIDS) - set(LEAD_CONVERT_TO_PROJECT_FIELD_UUIDS)
        ):
            raise self.BoardSectionServiceException("System fields cannot be hidden.")

        self.board_section_repo.update_fields_visibility(
            visible_fields_uuids=visible_fields_uuids,
            invisible_fields_uuids=invisible_fields_uuids,
            user_id=user_id,
            board_id=board_id,
        )

    def check_field_deletion(self, field_uuids: list[str], board_id: int):
        if self.board_section_repo.get_number_of_fields_with_data(field_uuids=field_uuids, board_id=board_id):
            return False
        return True

    def check_dropdown_option_deletion(self, dropdown_uuids: list[str], board_id: int):
        if self.board_section_repo.get_number_of_dropdown_options_with_data(
            dropdown_uuids=dropdown_uuids, board_id=board_id
        ):
            return False
        return True


class BoardUserService:
    class BoardUserUpdateException(BaseValidationError):
        pass

    def __init__(self, repo: BoardUserAbstractRepo):
        self.board_user_repo = repo

    def _get_board_user_edit_setting(
        self,
        view_setting: BoardUserViewSettingEnum | None,
        edit_setting: BoardUserEditSettingEnum | None,
    ) -> BoardUserEditSettingEnum | None:
        if edit_setting is None:
            return None
        if view_setting == BoardUserViewSettingEnum.ONLY_CREATED_LEADS:
            return BoardUserEditSettingEnum.ONLY_CREATED_LEADS
        elif view_setting == BoardUserViewSettingEnum.ASSIGNED_AND_CREATED_LEADS:
            if edit_setting in [
                BoardUserEditSettingEnum.ONLY_CREATED_LEADS,
                BoardUserEditSettingEnum.ONLY_ASSIGNED_LEADS,
            ]:
                return BoardUserEditSettingEnum(edit_setting)
            return BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS
        return BoardUserEditSettingEnum(edit_setting)

    def _get_board_user_delete_setting(
        self,
        edit_setting: BoardUserEditSettingEnum | None,
        delete_setting: BoardUserDeleteSettingEnum | None,
    ) -> BoardUserDeleteSettingEnum | None:
        if delete_setting is None or edit_setting is None:
            return None

        if edit_setting == BoardUserEditSettingEnum.ONLY_CREATED_LEADS:
            return BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS
        elif edit_setting == BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS:
            if delete_setting in [
                BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS,
                BoardUserDeleteSettingEnum.ONLY_ASSIGNED_LEADS,
            ]:
                return BoardUserDeleteSettingEnum(delete_setting)
            return BoardUserDeleteSettingEnum.ASSIGNED_AND_CREATED_LEADS
        return BoardUserDeleteSettingEnum(delete_setting)

    def _get_board_user_repo_data_for_creation(
        self,
        data: BoardUsersSettingUpdateConfigEntity,
        user_id: int,
        board_id: int,
        saved_board_user_ids: set,
        is_invited: bool,
        is_assigned: bool,
    ) -> List[BoardUserRepoEntity]:
        new_board_user_entities = []
        for user_id in data.user_ids:
            if user_id not in saved_board_user_ids:
                view_setting = data.settings.get(BoardUserSettingEnum.VIEW_SETTING.value)
                edit_setting = data.settings.get(BoardUserSettingEnum.EDIT_SETTING.value)
                delete_setting = data.settings.get(BoardUserSettingEnum.DELETE_SETTING.value)

                view_setting = BoardUserViewSettingEnum(view_setting) if view_setting else None
                edit_setting = BoardUserEditSettingEnum(edit_setting) if edit_setting else None
                delete_setting = BoardUserDeleteSettingEnum(delete_setting) if delete_setting else None

                validated_edit_setting = self._get_board_user_edit_setting(
                    view_setting=view_setting,
                    edit_setting=edit_setting,
                )
                validated_delete_setting = self._get_board_user_delete_setting(
                    edit_setting=validated_edit_setting,
                    delete_setting=delete_setting,
                )
                new_board_user_entities.append(
                    BoardUserRepoEntity(
                        board_id=board_id,
                        user_id=user_id,
                        is_invited=is_invited,
                        is_assigned=is_assigned,
                        view_setting=view_setting.value if view_setting else None,
                        can_create=True if data.settings.get(BoardUserSettingEnum.CREATE_SETTING.value) else False,
                        edit_setting=validated_edit_setting.value if validated_edit_setting else None,
                        delete_setting=validated_delete_setting.value if validated_delete_setting else None,
                    )
                )
        return new_board_user_entities

    def create_board_users(
        self,
        data: BoardUsersSettingUpdateConfigEntity,
        user_id: int,
        board_id: int,
        is_invited: bool = True,
        is_assigned: bool = False,
    ):
        saved_board_user_ids = self.board_user_repo.get_board_user_ids_and_mapping(board_id=board_id)
        logger.info("Saved Board User IDs", saved_board_user_ids=saved_board_user_ids)
        repo_data = self._get_board_user_repo_data_for_creation(
            data=data,
            user_id=user_id,
            board_id=board_id,
            saved_board_user_ids=saved_board_user_ids,
            is_invited=is_invited,
            is_assigned=is_assigned,
        )
        logger.info("Prepared Inviting board users data", board_id=board_id, repo_data=repo_data)

        try:
            self.board_user_repo.create_board_users(board_user_data=repo_data, user_id=user_id)
            logger.info("Board Users Invited", board_id=board_id)
            BoardAssignmentCache.delete(instance_id=board_id)
            logger.info("Board Assignment Cache Deleted", board_id=board_id)
        except BoardUserAbstractRepo.BoardUserCreateException as e:
            logger.info("Board Users Invite Failed", board_id=board_id, error=e.message)
            raise self.BoardUserUpdateException(e.message)

    def update_board_user_settings(self, settings: Dict, board_user_id: int, board_id: int, user_id: int):
        edit_setting = self._get_board_user_edit_setting(
            view_setting=settings.get(BoardUserSettingEnum.VIEW_SETTING.value),
            edit_setting=settings.get(BoardUserSettingEnum.EDIT_SETTING.value),
        )
        delete_setting = self._get_board_user_delete_setting(
            edit_setting=edit_setting,
            delete_setting=settings.get(BoardUserSettingEnum.DELETE_SETTING.value),
        )
        settings_to_save = {
            "view_setting": settings.get(BoardUserSettingEnum.VIEW_SETTING.value),
            "create_setting": (
                settings.get(BoardUserSettingEnum.CREATE_SETTING.value)
                == BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value
            ),
            "edit_setting": edit_setting.value if edit_setting else None,
            "delete_setting": delete_setting.value if delete_setting else None,
        }
        try:
            self.board_user_repo.update_board_user_settings(
                settings=settings_to_save, board_user_id=board_user_id, board_id=board_id, updated_by_id=user_id
            )
            BoardAssignmentCache.delete(instance_id=board_id)
        except BoardUserAbstractRepo.BoardUserUpdateException as e:
            raise self.BoardUserUpdateException(e.message)

    def delete_board_user(self, board_user_id: int, board_id: int, user_id: int):
        try:
            self.board_user_repo.delete_board_user(
                board_user_id=board_user_id, board_id=board_id, deleted_by_id=user_id
            )
            BoardAssignmentCache.delete(instance_id=board_id)
        except BoardUserAbstractRepo.BoardUserUpdateException as e:
            raise self.BoardUserUpdateException(e.message)

    def create_board_user_while_lead_creation(self, board_user_id: int, board_id: int):
        logger.info("Creating board user for lead creator", board_user_id=board_user_id, board_id=board_id)
        self.create_board_users(
            data=BoardUsersSettingUpdateConfigEntity(
                user_ids=[board_user_id],
                settings={
                    BoardUserSettingEnum.VIEW_SETTING.value: BoardUserViewSettingEnum.ONLY_CREATED_LEADS.value,
                    BoardUserSettingEnum.CREATE_SETTING.value: BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value,
                    BoardUserSettingEnum.EDIT_SETTING.value: BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,
                    BoardUserSettingEnum.DELETE_SETTING.value: BoardUserDeleteSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,  # noqa
                },
            ),
            user_id=board_user_id,
            board_id=board_id,
            is_assigned=False,
            is_invited=False,
        )
        logger.info("Board user created for lead creator", board_user_id=board_user_id, board_id=board_id)

    def create_board_users_while_lead_assignee(self, board_user_ids: List[int], board_id: int, user_id: int):
        logger.info("Creating board users for lead assignees", board_user_ids=board_user_ids, board_id=board_id)
        self.create_board_users(
            data=BoardUsersSettingUpdateConfigEntity(
                user_ids=board_user_ids,
                settings={
                    BoardUserSettingEnum.VIEW_SETTING.value: BoardUserViewSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,
                    BoardUserSettingEnum.CREATE_SETTING.value: BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value,
                    BoardUserSettingEnum.EDIT_SETTING.value: BoardUserEditSettingEnum.ONLY_CREATED_LEADS.value,
                    BoardUserSettingEnum.DELETE_SETTING.value: BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS.value,
                },
            ),
            user_id=user_id,
            board_id=board_id,
            is_assigned=True,
            is_invited=False,
        )
        logger.info("Board users created for lead assignees", board_user_ids=board_user_ids, board_id=board_id)


class BoardService:
    class BoardCreateException(BaseValidationError):
        pass

    class BoardUpdateException(BaseValidationError):
        pass

    class BoardDoesNotExistsException(BaseValidationError):
        pass

    def __init__(
        self,
        repo: BoardAbstractRepo,
        board_section_repo: BoardSectionAbstractRepo,
        board_stage_service: BoardStageService,
        board_section_service: BoardSectionService,
        board_user_service: BoardUserService,
        lead_service,
    ):
        self.board_repo = repo
        self.board_section_repo = board_section_repo
        self.board_stage_service = board_stage_service
        self.board_section_service = board_section_service
        self.board_user_service = board_user_service
        self.lead_service = lead_service

    def create_board(self, board_data: BoardInputEntity, org_id: int, user_id: int):
        board_repo_entity = BoardRepoInputEntity(
            name=board_data.name,
            is_active=board_data.is_active,
            is_project_conversion_allowed=board_data.is_project_conversion_allowed,
        )
        try:
            board = self.board_repo.create(board_data=board_repo_entity, org_id=org_id, user_id=user_id)
        except BoardAbstractRepo.BoardNameDuplicateException as e:
            raise self.BoardCreateException(e.message)

        self.board_stage_service.create_default_stages(board_id=board.id, user_id=user_id)
        self.board_section_service.create_default_sections(
            board_id=board.id, user_id=user_id, is_project_conversion_allowed=board_data.is_project_conversion_allowed
        )
        self.board_user_service.create_board_users(
            data=BoardUsersSettingUpdateConfigEntity(
                user_ids=[user_id],
                settings={
                    BoardUserSettingEnum.VIEW_SETTING.value: BoardUserViewSettingEnum.VIEW_ALL_LEADS.value,
                    BoardUserSettingEnum.CREATE_SETTING.value: BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value,
                    BoardUserSettingEnum.EDIT_SETTING.value: BoardUserEditSettingEnum.EDIT_ALL_LEADS.value,
                    BoardUserSettingEnum.DELETE_SETTING.value: BoardUserDeleteSettingEnum.DELETE_ALL_LEADS.value,
                },
            ),
            user_id=user_id,
            board_id=board.pk,
        )

        return board

    def get_board(self, board_id: int):
        return self.board_repo.get_board(board_id)

    def update_board_configuration(self, board_data: BoardInputEntity, user_id: int, board_id: int):
        board_repo_entity = BoardRepoInputEntity(
            name=board_data.name,
            is_active=board_data.is_active,
            is_project_conversion_allowed=board_data.is_project_conversion_allowed,
        )
        fields = ["name", "is_active", "is_project_conversion_allowed"]
        try:
            self.board_repo.update(board_data=board_repo_entity, fields=fields, user_id=user_id, board_id=board_id)
            if board_data.is_project_conversion_allowed:
                if not self.board_section_repo.is_convert_lead_to_project_fields_exists(board_id=board_id):
                    self.board_section_repo.dispatch_field_creation_in_all_leads(
                        field_uuids=[], board_id=board_id, user_id=user_id, is_convert_lead_to_project_field=True
                    )
                else:
                    self.board_section_repo.unarchive_and_unhide_convert_to_project_board_fields(
                        board_id=board_id, user_id=user_id
                    )
            else:
                self.board_section_repo.archive_and_hide_convert_to_project_board_fields(
                    board_id=board_id, user_id=user_id
                )
        except (BoardAbstractRepo.BoardNameDuplicateException, BoardAbstractRepo.BoardNotFoundException) as e:
            raise self.BoardUpdateException(e.message)

    def update_board_swimlane_settings(
        self, is_lead_count_visible: bool, is_lead_amount_visible: bool, user_id: int, board_id: int
    ):
        board_repo_entity = BoardRepoInputEntity(
            is_lead_amount_visible=is_lead_amount_visible,
            is_lead_count_visible=is_lead_count_visible,
        )
        fields = ["is_lead_count_visible", "is_lead_amount_visible"]
        try:
            self.board_repo.update(board_data=board_repo_entity, fields=fields, user_id=user_id, board_id=board_id)
        except BoardAbstractRepo.BoardNameDuplicateException as e:
            raise self.BoardUpdateException(e.message)

    def delete_board(self, board_id: int, user_id: int):
        try:
            self.lead_service.delete_board_leads(board_id=board_id)
            self.board_repo.delete(board_id=board_id, user_id=user_id)
        except BoardAbstractRepo.BoardNotFoundException as e:
            raise self.BoardDoesNotExistsException(e) from e

    def fetch_board_user_config(self) -> Dict:
        return BOARD_USER_CONFIG

    def get_board_name(self, board_id: int) -> str:
        return self.board_repo.get_board_name(board_id=board_id)
