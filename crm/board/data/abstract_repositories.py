import abc
from typing import Dict, List
from uuid import UUID

from common.exceptions import BaseValidationError
from controlroom.data.models import User
from controlroom.data.querysets import QuerySet
from crm.board.domain.entities import (
    BoardLeadSectionRepoInputEntity,
    BoardRepoInputEntity,
    BoardStageRepoInputEntity,
    BoardStageRepoOutputEntity,
    BoardUserRepoEntity,
    BoardUserRepoOutputEntity,
)
from crm.data.entities import BoardEntity, BoardLeadSectionResEntity
from crm.data.models import Board


class BoardAbstractRepo:
    class BoardNameDuplicateException(BaseValidationError):
        pass

    class BoardNotFoundException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create(self, board_data, org_id: int, user_id: int) -> Board:
        ...

    @abc.abstractmethod
    def createBoard(self, board_data, org_id: int, user_id: int) -> BoardEntity:
        ...

    @abc.abstractmethod
    def get_board(self, board_id: int) -> Board:
        ...

    @abc.abstractmethod
    def get_board_pydantic(self, board_id: int) -> BoardEntity:
        ...

    @abc.abstractmethod
    def get_board_name(self, board_id: int) -> str:
        ...

    @abc.abstractmethod
    def fetch_organization_boards(self, org_id: int, user: User, is_admin: bool, is_active: bool) -> QuerySet:
        ...

    @abc.abstractmethod
    def update(self, board_data: BoardRepoInputEntity, fields: list[str], user_id: int, board_id: int) -> None:
        ...

    @abc.abstractmethod
    def delete(self, board_id: int, user_id: int) -> None:
        ...


class BoardStageAbstractRepo:
    class BoardStageNameDuplicateException(BaseValidationError):
        pass

    class BoardStageCreateException(BaseValidationError):
        pass

    class BoardStageUpdateException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create_stages(
        self, stages_data: list[BoardStageRepoInputEntity], user_id: int
    ) -> list[BoardStageRepoOutputEntity]:
        pass

    @abc.abstractmethod
    def get_stages(self, board_id: int) -> QuerySet:
        pass

    @abc.abstractmethod
    def get_stage_ids(self, board_id: int) -> list:
        pass

    @abc.abstractmethod
    def delete_stages(self, stage_ids: list, user_id: int) -> None:
        pass

    @abc.abstractmethod
    def update_stage_data(
        self,
        board_id: int,
        user_id: int,
        stage_data_for_updation: list[BoardStageRepoInputEntity],
        stage_data_for_creation: list[BoardStageRepoInputEntity],
        stage_ids_for_deletion: list,
    ) -> list[BoardStageRepoOutputEntity]:
        pass


class BoardSectionAbstractRepo:
    class FieldDataCounterException(BaseValidationError):
        pass

    class BoardLeadFieldCreateException(BaseValidationError):
        pass

    class BoardLeadDropDownCreateException(BaseValidationError):
        pass

    class BoardLeadSectionCreateException(BaseValidationError):
        pass

    class BoardLeadSectionUpdateException(BaseValidationError):
        pass

    class BoardLeadFieldUpdateException(BaseValidationError):
        pass

    class BoardLeadDropDownUpdateException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create_sections_with_fields_and_dropdowns(
        self,
        sections_data: list[BoardLeadSectionRepoInputEntity],
        section_uuid_to_fields_mapping: dict,
        field_uuid_to_dropdown_options_mapping: dict,
    ) -> None:
        pass

    @abc.abstractmethod
    def get_sections(self, board_id: int) -> QuerySet:
        pass

    @abc.abstractmethod
    def get_sections_pydantic(self, board_id: int) -> list[BoardLeadSectionResEntity]:
        pass

    @abc.abstractmethod
    def get_uuid_mappings(self, board_id: int) -> dict:
        pass

    @abc.abstractmethod
    def update_fields_visibility(
        self, visible_fields_uuids: list, invisible_fields_uuids: list, user_id: int, board_id: int
    ) -> None:
        pass

    @abc.abstractmethod
    def is_convert_lead_to_project_fields_exists(self, board_id: int) -> bool:
        pass

    @abc.abstractmethod
    def archive_and_hide_convert_to_project_board_fields(self, board_id: int, user_id: int) -> None:
        pass

    @abc.abstractmethod
    def unarchive_and_unhide_convert_to_project_board_fields(self, board_id: int, user_id: int) -> None:
        pass

    @abc.abstractmethod
    def update_sections(
        self,
        updated_sections_data: dict,
        sections_to_be_created: list[BoardLeadSectionRepoInputEntity],
        board_id: int,
        user_id: int,
        section_uuid_to_fields_mapping: dict,
        field_uuid_to_dropdown_options_mapping: dict,
        section_uuid_to_new_fields_mapping: dict,
        new_fields_uuids_to_new_dropdown_options_entities_mapping: dict,
        field_uuid_to_new_dropdown_options_mapping: dict,
        section_uuid_to_section_id_mapping: dict,
        field_uuid_to_field_id_mapping: dict,
    ) -> None:
        pass

    @abc.abstractmethod
    def delete_sections(
        self,
        delete_sections_uuids: list[str],
        delete_fields_uuids: list[str],
        delete_dropdown_options_uuids: list[str],
        board_id: int,
        user_id: int,
    ) -> None:
        pass

    @abc.abstractmethod
    def get_number_of_fields_with_data(self, field_uuids: list[str], board_id: int) -> int:
        pass

    @abc.abstractmethod
    def get_number_of_dropdown_options_with_data(self, dropdown_uuids: list[str], board_id: int) -> int:
        pass

    @abc.abstractmethod
    def dispatch_field_creation_in_all_leads(
        self, field_uuids: list[UUID], board_id: int, user_id: int, is_convert_lead_to_project_field: bool = False
    ):
        pass


class BoardUserAbstractRepo:
    class BoardUserUpdateException(BaseValidationError):
        pass

    class BoardUserCreateException(BaseValidationError):
        pass

    @abc.abstractmethod
    def get_board_user_ids_and_mapping(self, board_id: int) -> set[int]:
        pass

    @abc.abstractmethod
    def update_board_user_settings(self, settings: Dict, board_user_id: int, board_id: int, updated_by_id: int):
        pass

    @abc.abstractmethod
    def delete_board_user(self, board_user_id: int, board_id: int, deleted_by_id: int) -> None:
        pass

    @abc.abstractmethod
    def create_board_users(self, board_user_data: list[BoardUserRepoEntity], user_id: int) -> None:
        pass

    @abc.abstractmethod
    def get_board_users(self, board_id: int) -> List[BoardUserRepoOutputEntity]:
        pass
