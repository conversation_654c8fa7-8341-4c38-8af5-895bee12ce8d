from typing import Iterator, List, <PERSON><PERSON>

import pytz
import structlog

from common.constants import CustomFieldTypeEnum
from common.excel.constants import (
    FontEnum,
    HorizontalAlignmentEnum,
    LeadsExcelColumnEnum,
    SheetCellBorderEnum,
    SheetCellTypeEnum,
    SheetTypeEnum,
    VerticalAlignmentEnum,
)
from common.excel.data_builder import ExcelDataBuilder
from common.excel.entities import ElementExcelSheetBaseData, HeaderCell, SheetCell, SheetRowData
from common.utils import get_current_local_time
from core.models import User
from crm.interface.helpers import BoardPermissionHelper
from crm.lead.domain.entities import LeadFieldDetailRepoEntity, LeadSectionWithFieldDataRepoEntity
from crm.lead.domain.factories import LeadExcelServiceFactory
from crm.lead.excel.entities import LeadsExcelSheetData

logger = structlog.get_logger(__name__)


class LeadsExcelDataBuilder(ExcelDataBuilder):
    def __init__(self, board_id: int, user: User, organization_timezone: pytz.tzinfo.BaseTzInfo, board_name: str):
        super().__init__()
        self._leads_cols = []
        self.board_id = board_id
        self.board_name = board_name
        self.user = user
        self.board_permissions = BoardPermissionHelper.get_permissions(
            user=user,
            board_id=board_id,
        )
        self.organization_timezone = organization_timezone
        self.sections_config = self.get_lead_sections_config()
        self.leads = self.get_leads_data()
        logger.info("Leads Excel Export Data")

    def get_lead_sections_config(self) -> List[LeadSectionWithFieldDataRepoEntity]:
        service_factory = LeadExcelServiceFactory(
            board_id=self.board_id,
            user_id=self.user.pk,
            org_id=self.user.org.pk,
            organization_timezone=self.organization_timezone,
        )
        interactor = service_factory.get_interactor()

        return interactor.get_lead_sections_config()

    def get_leads_data(
        self,
    ) -> Iterator[Tuple[int, List[LeadSectionWithFieldDataRepoEntity]]]:
        service_factory = LeadExcelServiceFactory(
            board_id=self.board_id,
            user_id=self.user.pk,
            org_id=self.user.org.pk,
            organization_timezone=self.organization_timezone,
        )
        interactor = service_factory.get_interactor()

        return interactor.get_all_leads_details(
            board_id=self.board_id,
            board_permissions=self.board_permissions,
            user_id=self.user.pk,
            sections_config=self.sections_config,
        )

    def get_file_name(self) -> str:
        return f"{self.board_name}_{get_current_local_time().date()}.xlsx"

    def validate(self):
        return super().validate()

    def get_sheets_data(self) -> list[LeadsExcelSheetData]:
        sheets: list[LeadsExcelSheetData] = []
        for sheet in self._sheets:
            if sheet == SheetTypeEnum.LEADS:
                sheets.append(self._build_leads_sheet())
        return sheets

    def _build_leads_sheet(self) -> ElementExcelSheetBaseData:
        """Build the leads sheet with headers and rows."""
        # Build headers
        headers = {}
        for col in self._leads_cols:
            col_enum = LeadsExcelColumnEnum.from_field_name(col)
            headers[col_enum] = HeaderCell(
                value=col,
                type=SheetCellTypeEnum.TEXT,
                font=FontEnum.BOLD,
                horizontal_alignment=HorizontalAlignmentEnum.CENTER,
                vertical_alignment=VerticalAlignmentEnum.CENTER,
                border=SheetCellBorderEnum.DEFAULT,
            )

        rows = []
        for lead_id, sections in self.leads:
            row_data = {}
            for section in sections:
                for field in section.fields:
                    col_name = field.name
                    if col_name not in self._leads_cols:
                        continue
                    col_enum = LeadsExcelColumnEnum.from_field_name(col_name)
                    cell_value = self._format_field_value(field)
                    row_data[col_enum] = SheetCell(
                        value=cell_value,
                        type=SheetCellTypeEnum.TEXT,
                        border=SheetCellBorderEnum.DEFAULT,
                        horizontal_alignment=HorizontalAlignmentEnum.LEFT,
                        vertical_alignment=VerticalAlignmentEnum.TOP,
                    )
            rows.append(SheetRowData(data=row_data))
        return ElementExcelSheetBaseData(
            title="Leads", type=SheetTypeEnum.LEADS, rows=rows, headers=headers, border=True
        )

    def _format_field_value(self, field: LeadFieldDetailRepoEntity) -> str:
        """Format field value for Excel display."""
        if field.value is None:
            return ""

        if field.type == CustomFieldTypeEnum.LEAD_NUMBER.value:
            return str(field.value)
        elif field.type in [CustomFieldTypeEnum.LEAD_ASSIGNEE.value, CustomFieldTypeEnum.LEAD_CONTACT.value]:
            return ", ".join([str(v["name"]) for v in field.value])
        elif field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
            return ", ".join(option["name"] for option in field.value) if field.value else ""
        else:
            return str(field.value)

    def add_leads_cols(self, *cols: str):
        self._leads_cols.extend(cols)
        return self
