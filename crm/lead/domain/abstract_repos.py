import abc
import inspect
from typing import Iterator, List, Optional

from django.db.models import QuerySet

from common.exceptions import BaseValidationError
from crm.data.models import Contact
from crm.interface.type import BoardPermissionType
from crm.lead.data.entities import LeadFieldForImportSectionRepoEntity
from crm.lead.domain.entities import (
    CompanyRepoEntity,
    ContactRepoEntity,
    LeadAssigneeUpdateRepoEntity,
    LeadCreateRepoEntity,
    LeadCustomDataRepoEntity,
    LeadEventCreateRepoEntity,
    LeadExcelEntity,
    LeadFieldTypeUUIDRepoEntity,
    LeadPosRepoEntity,
    LeadSectionWithFieldDataRepoEntity,
    LeadSystemDataRepoEntity,
    LeadSystemUpdateDataRepoEntity,
    LeadToProjectRoleData,
    LeadToProjectSectionRepoEntity,
    LeadToProjectSystemDataRepoEntity,
    LeadUpdateRepoEntity,
    TargetLeadRepoEntity,
)


class LeadAbstractRepo(abc.ABC):
    class LeadCreationRepoConstraintException(BaseValidationError):
        pass

    class LeadServiceRepoException(BaseValidationError):
        pass

    class LeadCreationRepoException(BaseValidationError):
        pass

    class LeadUpdationRepoException(BaseValidationError):
        pass

    class LeadPositionUpdateRepoException(BaseValidationError):
        pass

    class LeadDoesNotExistException(BaseValidationError):
        pass

    class LeadSearchVectorIndexException(BaseValidationError):
        pass

    @abc.abstractmethod
    def get_fields_type(self, uuids: List[str]) -> List[LeadFieldTypeUUIDRepoEntity]:
        pass

    @abc.abstractmethod
    def get_next_position(self, board_id: int, stage_id: int) -> int:
        pass

    @abc.abstractmethod
    def get_next_serial_number(self, org_id: int) -> int:
        pass

    @abc.abstractmethod
    def create(self, board_id: int, data: LeadCreateRepoEntity, user_id: int, org_id: int) -> int:
        pass

    @abc.abstractmethod
    def get_lead_sections_config(self, board_id: int) -> List[LeadSectionWithFieldDataRepoEntity]:
        pass

    @abc.abstractmethod
    def get_lead_latched_project(self, lead_id: int) -> int | None:
        pass

    @abc.abstractmethod
    def get_lead_system_data(
        self, lead_id: int, user_id: int, board_permissions: BoardPermissionType
    ) -> LeadSystemDataRepoEntity:
        pass

    @abc.abstractmethod
    def get_lead_system_update_data(self, lead_id: int) -> LeadSystemUpdateDataRepoEntity:
        pass

    @abc.abstractmethod
    def get_lead_assignee_data(self, lead_id: int) -> set[int]:
        pass

    @abc.abstractmethod
    def get_lead_custom_data(self, board_id: int, lead_id: int) -> List[LeadCustomDataRepoEntity]:
        pass

    @abc.abstractmethod
    def get_lead_custom_update_data(self, board_id: int, lead_id: int) -> List[LeadCustomDataRepoEntity]:
        """
        This layer is same as above, but the data will have foreign keys rather than
        foreign key data
        """
        pass

    @abc.abstractmethod
    def update_stage(self, lead_id: int, stage_id: int, user_id: int, lead_position: int):
        pass

    @abc.abstractmethod
    def update(self, data: LeadUpdateRepoEntity, user_id: int, lead_id: int):
        pass

    @abc.abstractmethod
    def get_lead_position(self, lead_id: int) -> int:
        pass

    @abc.abstractmethod
    def get_target_lead_data(self, lead_id: int) -> TargetLeadRepoEntity:
        pass

    @abc.abstractmethod
    def get_max_lead_position(self, stage_id: int) -> int:
        pass

    @abc.abstractmethod
    def move_lead(self, lead_id: int, target_data: TargetLeadRepoEntity, stage_id: int, user_id: int):
        pass

    @abc.abstractmethod
    def update_lead_assignee_data(self, lead_id: int, assignees: List[LeadAssigneeUpdateRepoEntity], user_id: int):
        pass

    @abc.abstractmethod
    def delete(self, lead_id: int, user_id: int):
        pass

    @abc.abstractmethod
    def delete_board_leads(self, board_id: int, user_id: int) -> None:
        pass

    @abc.abstractmethod
    def transfer_leads_to_stage_ids(self, stage_ids: List[int], from_stage_id_to_stage_id_mapping: dict):
        pass

    @abc.abstractmethod
    def get_lead_pos_in_range(
        self, stage_id: int, current_lead_id: int, target_lead_id: int, max_pos: int
    ) -> List[LeadPosRepoEntity]:
        pass

    @abc.abstractmethod
    def update_lead_pos_in_range(self, data: List[LeadPosRepoEntity], stage_id: int):
        pass

    @abc.abstractmethod
    def get_targe_lead_pos(self, lead_id: int) -> int:
        pass

    @abc.abstractmethod
    def update_lead_pos_in_diff_stage(self, stage_id: int, target_lead_pos: int):
        pass

    @abc.abstractmethod
    def update_curr_lead_pos_in_diff_stage(self, lead_id: int, target_lead_pos: int, stage_id: int):
        pass

    @abc.abstractmethod
    def get_leads_for_import(*, user) -> Optional[QuerySet]:
        pass

    @abc.abstractmethod
    def get_quotations_for_import(*, user) -> Optional[QuerySet]:
        pass

    @abc.abstractmethod
    def fetch_lead_fields_for_import(self, board_id: int, lead_id: int) -> list[LeadFieldForImportSectionRepoEntity]:
        pass

    @abc.abstractmethod
    def get_section_and_field_config_for_lead(
        self, lead_id: int | None, board_id: int
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        pass


class LeadToProjectAbstractRepo(abc.ABC):
    class LeadDoesNotExistException(BaseValidationError):
        pass

    class LeadToProjectImportNotAllowedException(BaseValidationError):
        pass

    @abc.abstractmethod
    def get_project_data(self, org_id: int) -> List[LeadToProjectSectionRepoEntity]:
        pass

    @abc.abstractmethod
    def get_lead_data(self, lead_id: int) -> LeadToProjectSystemDataRepoEntity:
        pass

    @abc.abstractmethod
    def get_project_role_list(self, org_id: int) -> List[LeadToProjectRoleData]:
        pass

    @abc.abstractmethod
    def update_lead_from_project(self, lead_id: int, project_id: int, user_id: int):
        pass


class CompanyAbstractRepo(abc.ABC):
    class CompanyDoesNotExistException(BaseValidationError):
        pass

    class CompanyContactException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create(self, data: CompanyRepoEntity, user_id: int, client_id: int) -> CompanyRepoEntity:
        pass

    @abc.abstractmethod
    def update(self, data: CompanyRepoEntity, user_id: int) -> CompanyRepoEntity:
        pass

    @abc.abstractmethod
    def update_company_from_client(self, company_id: int, client_id: int, user_id: int):
        pass


class ContactAbstractRepo(abc.ABC):
    class ContactDoesNotExistException(BaseValidationError):
        pass

    class ContactExceptionException(BaseValidationError):
        pass

    @abc.abstractmethod
    def create(self, data: ContactRepoEntity, user_id: int, org_id: int) -> Contact:
        pass

    @abc.abstractmethod
    def delete(self, contact_id: int, user_id: int, company_id: int):
        pass

    @abc.abstractmethod
    def update(self, data: ContactRepoEntity, user_id: int) -> Contact:
        pass

    @abc.abstractmethod
    def get_lead_company_contacts(self, lead_id: int) -> Optional[List[Contact]]:
        pass


class LeadEventAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def create(self, data: LeadEventCreateRepoEntity, user_id: int):
        pass

    @abc.abstractmethod
    def get_lead_events_data(self, lead_id: int):
        pass


class LeadReminderAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def create(self, data: LeadEventCreateRepoEntity, user_id: int):
        pass

    @abc.abstractmethod
    def get_lead_reminders(self, lead_id: int):
        pass


class LeadExcelAbstractRepo(abc.ABC):
    class LeadExcelAbstractRepoException(BaseValidationError):
        pass

    @abc.abstractmethod
    def __init__(self, user_id: int, board_id: int, **kwargs): ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "board_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def get_board_name(self) -> str: ...

    @abc.abstractmethod
    def get_lead_sections_config(self, board_id: int) -> List[LeadSectionWithFieldDataRepoEntity]:
        pass

    @abc.abstractmethod
    def get_lead_data_for_excel_export(
        self, user_id: int, board_permissions: BoardPermissionType, board_id: int
    ) -> List[LeadExcelEntity]:
        pass

    @abc.abstractmethod
    def get_board_leads_custom_field_queryset(
        self, board_id: int, lead_ids: list[int]
    ) -> Iterator[tuple[int, List[LeadCustomDataRepoEntity]]]:
        pass
