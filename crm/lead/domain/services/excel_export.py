from typing import Dict, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>
from uuid import UUID

import pytz
import structlog

from common.constants import CustomFieldTypeEnum
from common.exceptions import BaseValidationError
from common.helpers import convert_blocks_to_lines
from common.utils import get_current_local_time
from crm.board.domain.constants import (
    LEAD_CREATION_DATE_UUID,
    LEAD_STAGE_NAME_UUID,
    LEAD_SYSTEM_FIELD_UUIDS,
    LEAD_SYSTEM_SECTION_UUIDS,
)
from crm.interface.helpers import add_lead_number_padding
from crm.interface.type import BoardPermissionType
from crm.lead.domain.abstract_repos import LeadExcelAbstractRepo
from crm.lead.domain.entities import (
    CityDataRepoEntity,
    CountryDataRepoEntity,
    LeadAssigneeDetailRepoEntity,
    LeadCompanyDetailRepoEntity,
    LeadContactDetailRepoEntity,
    LeadCustomDataRepoEntity,
    LeadExcelEntity,
    LeadFieldDetailRepoEntity,
    LeadSectionWithFieldDataRepoEntity,
    LeadStageRepoEntity,
    LeadSystemDataRepoEntity,
    PhoneNumberRepoEntity,
    StateDataRepoEntity,
)
from crm.lead.domain.services.lead import LeadService
from crm.lead.domain.triggers import trigger_leads_export_excel_generated
from microcontext.domain.constants import MicroContext
from report.download.domain.enums import DownloadProgressStatusEnum
from report.download.domain.service import DownloadService

logger = structlog.get_logger(__name__)


class LeadExcelService:
    def __init__(
        self,
        repo: LeadExcelAbstractRepo,
        board_id: int,
        user_id: int,
        org_id: int,
        organization_timezone: pytz.tzinfo.BaseTzInfo,
    ):
        self.repo = repo
        self.board_id = board_id
        self.user_id = user_id
        self.org_id = org_id
        self.organization_timezone = organization_timezone
        self.system_field_uuids = LEAD_SYSTEM_FIELD_UUIDS
        self.system_sections_uuids = LEAD_SYSTEM_SECTION_UUIDS

    def get_board_name(self):
        return self.repo.get_board_name()

    def apply_custom_fields_data(
        self,
        lead_id: int,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
        custom_data_map: Dict[int, List[LeadCustomDataRepoEntity]],
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        """Apply custom fields data to sections config for a single lead."""
        field_id_data_map = {str(field.field_uuid): field for field in custom_data_map.get(lead_id, [])}
        for section in sections_config:
            for field in section.fields:
                if str(field.uuid) not in self.system_field_uuids:
                    field_data = field_id_data_map.get(str(field.uuid))
                    if field_data:
                        if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                            field_value_dict = field_data.value.value if field_data.value else None
                            if isinstance(field_value_dict, dict) and field_value_dict.get("name"):
                                field.value = field_value_dict["name"]
                            else:
                                field.value = None
                        elif field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                            field.value = [value.value for value in field_data.value] if field_data.value else []
                        elif field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                            field_value_dict = field_data.value.value if field_data.value else None
                            if (
                                isinstance(field_value_dict, dict)
                                and field_value_dict.get("country_code")
                                and field_value_dict.get("number")
                            ):
                                field.value = field_value_dict["country_code"] + field_value_dict["number"]
                            else:
                                field.value = None
                        elif field.type == CustomFieldTypeEnum.FILE.value:
                            field_value_dict = field_data.value.value if field_data.value else None
                            if isinstance(field_value_dict, dict) and field_value_dict.get("url"):
                                field.value = field_value_dict["url"]
                            else:
                                field.value = None
                        elif field.type == CustomFieldTypeEnum.BOOLEAN.value:
                            field.value = (
                                field_data.value.value if (field_data.value and field_data.value.value) else False
                            )
                        elif field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                            field.value = (
                                "\n".join(convert_blocks_to_lines(field_data.value.value))
                                if (field_data.value and field_data.value.value)
                                else None
                            )
                        else:
                            field.value = (
                                field_data.value.value if (field_data.value and field_data.value.value) else None
                            )
        return sections_config

    def get_lead_sections_config(self):
        return self.repo.get_lead_sections_config(board_id=self.board_id)

    def get_lead_data_for_excel_export(
        self, user_id: int, board_permissions: BoardPermissionType
    ) -> List[LeadExcelEntity]:
        return self.repo.get_lead_data_for_excel_export(
            user_id=user_id, board_permissions=board_permissions, board_id=self.board_id
        )

    def apply_system_fields_data(
        self,
        lead: LeadSystemDataRepoEntity,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
        lead_field_uuids: list[str],
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        """Apply system fields data to sections config for a single lead."""
        for section in sections_config:
            if str(section.uuid) in self.system_sections_uuids:
                for field in section.fields:
                    if field.type == CustomFieldTypeEnum.LEAD_NUMBER.value:
                        field.value = add_lead_number_padding(lead.serial_number)
                        continue
                    if field.uuid == LEAD_CREATION_DATE_UUID:
                        field.value = lead.created_at
                        continue
                    if field.uuid == LEAD_STAGE_NAME_UUID:
                        field.value = lead.stage.name
                        continue

                    if field.archived_at and lead.created_at > field.archived_at:
                        # When Lead created after the field is archived, then we should not show that field
                        continue
                    if field.created_at > lead.created_at and str(field.uuid) not in lead_field_uuids:
                        continue

                    # if field.created_at > lead.created_at:
                    #     continue
                    if str(field.uuid) in self.system_field_uuids:
                        if field.type == CustomFieldTypeEnum.LEAD_NAME.value:
                            field.value = lead.name
                        elif field.type == CustomFieldTypeEnum.LEAD_VALUE.value:
                            field.value = lead.amount
                        elif field.type == CustomFieldTypeEnum.LEAD_ASSIGNEE.value:
                            value = []
                            for assignee in lead.assignees:
                                assignee_dict = assignee.__dict__
                                # assignee_dict["phone"] = assignee.phone.__dict__ if assignee.phone else None
                                value.append(assignee_dict)
                            field.value = value
                        elif field.type == CustomFieldTypeEnum.LEAD_COMPANY.value:
                            field.value = lead.company.name if lead.company else None
                        elif field.type == CustomFieldTypeEnum.LEAD_CONTACT.value:
                            value = []
                            for contact in lead.contacts:
                                contact_dict = contact.__dict__
                                contact_dict["name"] = ""
                                if contact.name:
                                    contact_dict["name"] += f"{contact.name}"
                                if contact.phone:
                                    if len(contact_dict["name"]):
                                        contact_dict["name"] += ", "
                                    contact_dict["name"] += f"{contact.phone.country_code}{contact.phone.number}"
                                if contact.email:
                                    if len(contact_dict["name"]):
                                        contact_dict["name"] += ", "
                                    contact_dict["name"] += f"{contact.email}"
                                value.append(contact_dict)
                            field.value = value
                        elif field.type == CustomFieldTypeEnum.LEAD_COUNTRY.value:
                            if lead.country:
                                field.value = lead.country.name
                        elif field.type == CustomFieldTypeEnum.LEAD_STATE.value:
                            if lead.state:
                                field.value = lead.state.name
                        elif field.type == CustomFieldTypeEnum.LEAD_CITY.value:
                            if lead.city:
                                field.value = lead.city.name
                        elif field.type == CustomFieldTypeEnum.LEAD_ADDRESS.value:
                            field.value = lead.address_line_1
                        elif field.type == CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value:
                            field.value = lead.address_line_2
                        elif field.type == CustomFieldTypeEnum.LEAD_ZIPCODE.value:
                            field.value = lead.zip_code
        return sections_config

    def deep_copy_sections_config(
        self, sections_config: List[LeadSectionWithFieldDataRepoEntity]
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        """Create a deep copy of sections config to avoid modifying the original."""
        new_sections = []
        for section in sections_config:
            new_fields = [
                LeadFieldDetailRepoEntity(
                    uuid=field.uuid,
                    name=field.name,
                    type=field.type,
                    position=field.position,
                    is_required=field.is_required,
                    created_at=field.created_at,
                    archived_at=field.archived_at,
                    actions=field.actions[:],
                    value=None,
                    id=field.id,
                )
                for field in section.fields
            ]
            new_section = LeadSectionWithFieldDataRepoEntity(
                uuid=section.uuid,
                name=section.name,
                position=section.position,
                fields=new_fields,
                created_at=section.created_at,
                archived_at=section.archived_at,
            )
            new_sections.append(new_section)
        return new_sections

    def build_lead_system_data_list(
        self, lead_excel_entity_list: List[LeadExcelEntity], board_permissions=None
    ) -> List[LeadSystemDataRepoEntity]:
        """Build system data entities for all leads."""
        logger.info("Building Lead System Data List", lead_count=len(lead_excel_entity_list))
        leads_data = []
        for lead in lead_excel_entity_list:
            lead_entity = LeadSystemDataRepoEntity(
                id=lead.id,
                name=lead.name,
                project_id=lead.project_id,
                serial_number=lead.serial_number,
                stage=LeadStageRepoEntity(id=lead.stage_id, name=lead.stage.name),
                company=(
                    LeadCompanyDetailRepoEntity(id=lead.company_id, name=lead.company.name) if lead.company_id else None
                ),
                amount=lead.amount,
                source=lead.source,
                country=(
                    CountryDataRepoEntity(
                        id=lead.lead_project_data.country.id, name=lead.lead_project_data.country.name
                    )
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.country
                    else None
                ),
                can_create_lead=False,
                can_add_field=False,
                can_edit_lead=False,
                can_delete_lead=False,
                is_project_type=False,
                state=(
                    StateDataRepoEntity(id=lead.lead_project_data.state.id, name=lead.lead_project_data.state.name)
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.state
                    else None
                ),
                city=(
                    CityDataRepoEntity(id=lead.lead_project_data.city.id, name=lead.lead_project_data.city.name)
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.city
                    else None
                ),
                address_line_1=(
                    lead.lead_project_data.address_line_1
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_1
                    else None
                ),
                address_line_2=(
                    lead.lead_project_data.address_line_2
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_2
                    else None
                ),
                zip_code=(
                    lead.lead_project_data.zip_code
                    if hasattr(lead, "lead_project_data") and lead.lead_project_data.zip_code
                    else None
                ),
                contacts=[
                    LeadContactDetailRepoEntity(
                        id=contact.id,
                        name=contact.name,
                        phone=(
                            PhoneNumberRepoEntity(
                                country_code=f"{contact.phone.country_code}",
                                number=contact.phone.number,
                            )
                            if contact.phone
                            else None
                        ),
                        email=contact.email,
                    )
                    for contact in lead.contacts
                ],
                assignees=[
                    LeadAssigneeDetailRepoEntity(
                        id=assignee.id,
                        name=assignee.name,
                        phone=(
                            PhoneNumberRepoEntity(
                                country_code=f"+{assignee.phone_number.country_code}",
                                number=assignee.phone_number.number,
                            )
                            if assignee.phone_number
                            else None
                        ),
                        email=assignee.email,
                        photo=assignee.photo if assignee.photo else None,
                    )
                    for assignee in lead.assignees
                ],
                created_at=lead.created_at,
            )
            leads_data.append(lead_entity)
        return leads_data

    def get_all_leads_custom_fields_data(
        self, board_id: int, lead_ids: List[int]
    ) -> Iterator[tuple[int, List[LeadCustomDataRepoEntity]]]:
        return self.repo.get_board_leads_custom_field_queryset(board_id=board_id, lead_ids=lead_ids)

    def get_all_leads_details(
        self,
        board_id: int,
        board_permissions: BoardPermissionType,
        user_id: int,
        lead_service: LeadService,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
    ) -> Iterator[Tuple[int, List[LeadSectionWithFieldDataRepoEntity]]]:
        """
        Retrieves details of all leads in a board, with fields sorted by section and field positions.

        Args:
            board_id: ID of the board to fetch leads from.
            board_permissions: Permissions for the board.

        Returns:
            List of tuples, each containing a lead ID and a list of LeadSectionWithFieldDataRepoEntity
            with sorted fields.
        """
        # Fetch section configurations

        # Fetch all leads for the board
        leads_data_for_excel_export = self.get_lead_data_for_excel_export(
            user_id=user_id, board_permissions=board_permissions
        )

        # Build system data for all leads
        leads_data = self.build_lead_system_data_list(leads_data_for_excel_export, board_permissions=board_permissions)

        lead_custom_data_iter = self.get_all_leads_custom_fields_data(
            board_id=board_id, lead_ids=[lead.id for lead in leads_data]
        )

        # Create a mapping from lead_id to lead object (once)
        lead_id_map = {lead.id: lead for lead in leads_data}

        # Process one lead at a time
        for lead_id, custom_fields in lead_custom_data_iter:
            lead = lead_id_map.get(lead_id)
            if not lead:
                continue

            # Deep copy sections config to avoid modifying the original
            lead_sections = self.deep_copy_sections_config(sections_config)

            field_id_data_map = {str(field.field_uuid): field for field in custom_fields}
            lead_field_uuids = list(field_id_data_map.keys())

            # Apply system fields data
            lead_sections = self.apply_system_fields_data(lead, lead_sections, lead_field_uuids=lead_field_uuids)

            # Apply custom fields data
            lead_sections = self.apply_custom_fields_data(lead.id, lead_sections, {lead_id: custom_fields})

            # Remove sections/fields created after lead
            lead_sections = lead_service._remove_sections_and_field_which_are_created_after_lead(
                sections_config=lead_sections,
                lead_created_at=lead.created_at,
                lead_field_uuids=lead_field_uuids,
            )

            # Sort sections and fields by position
            lead_sections = sorted(lead_sections, key=lambda x: x.position)
            for section in lead_sections:
                section.fields = sorted(section.fields, key=lambda x: x.position)

            yield lead.id, lead_sections

            # Manually clear large data refs
            del custom_fields, field_id_data_map, lead_sections


class LeadExcelInteractor:
    class Exception(BaseValidationError):
        pass

    def __init__(self, service: LeadExcelService, lead_service: LeadService, download_service: DownloadService):
        self.service = service
        self.lead_service = lead_service
        self.download_service = download_service

    def get_export_leads_excel_data(self, uuid: UUID):
        file_ext = ".xlsx"
        board_name = self.service.get_board_name()
        file_name = f"{board_name} {get_current_local_time().date()}"

        download_id = self.download_service.create_download(
            name=file_name + file_ext,
            progress_status=DownloadProgressStatusEnum.IN_PROGRESS,
            context=MicroContext.BOARD,
            uuid=uuid,
        )

        trigger_leads_export_excel_generated(
            download_id=download_id,
            board_id=self.service.board_id,
            uuid=uuid,
            user_id=self.service.user_id,
            org_id=self.service.org_id,
            board_name=board_name,
        )

        return download_id

    def get_all_leads_details(
        self,
        board_id: int,
        board_permissions: BoardPermissionType,
        user_id: int,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
    ) -> Iterator[Tuple[int, List[LeadSectionWithFieldDataRepoEntity]]]:
        """
        Retrieves details of all leads in a board, with fields sorted by section and field positions.

        Args:
            board_id: ID of the board to fetch leads from.
            board_permissions: Permissions for the board.

        Returns:
            List of tuples, each containing a lead ID and a list of LeadSectionWithFieldDataRepoEntity
            with sorted fields.
        """
        # Fetch section configurations
        return self.service.get_all_leads_details(
            board_id=board_id,
            board_permissions=board_permissions,
            user_id=user_id,
            lead_service=self.lead_service,
            sections_config=sections_config,
        )

    def get_lead_sections_config(self) -> List[LeadSectionWithFieldDataRepoEntity]:
        return self.service.get_lead_sections_config()
