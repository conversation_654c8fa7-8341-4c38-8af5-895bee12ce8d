import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Type

import pytz
import structlog
from django.db.models import QuerySet

from common.constants import CustomFieldTypeEnum
from common.entities import CustomFieldEntity, ObjectStatus
from common.exceptions import BaseValidationError
from common.services import is_empty
from common.validators import CustomFieldDataValidator, CustomFieldListDataValidator
from core.models import User
from crm.board.domain.constants import (
    LEAD_CREATION_DATE_UUID,
    LEAD_NUMBER_UUID,
    LEAD_STAGE_NAME_UUID,
    LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING,
    LEAD_SYSTEM_FIELD_UUIDS,
    LEAD_SYSTEM_SECTION_UUIDS,
)
from crm.board.domain.entities import BoardUserInputEntity
from crm.board.domain.enums import BoardPermissionEnum
from crm.board.domain.services import BoardUserService
from crm.interface.helpers import BoardPermissionHelper, add_lead_number_padding
from crm.interface.type import BoardPermissionType
from crm.lead.domain.abstract_repos import (
    CompanyAbstractRepo,
    ContactAbstractRepo,
    LeadAbstractRepo,
    LeadEventAbstractRepo,
    LeadReminderAbstractRepo,
)
from crm.lead.domain.caches import LeadSerialNumberCache, LeadStagePositionCache
from crm.lead.domain.constants import LeadActionEnum, LeadEventTypeEnum
from crm.lead.domain.entities import (
    BooleanFieldRepoEntity,
    BooleanFieldUpdateRepoEntity,
    CompanyEntity,
    CompanyRepoEntity,
    ContactEntity,
    ContactRepoEntity,
    DataCounterRepoEntity,
    DateFieldRepoEntity,
    DateFieldUpdateRepoEntity,
    DateTimeFieldRepoEntity,
    DateTimeFieldUpdateRepoEntity,
    DropdownFieldRepoEntity,
    DropdownFieldUpdateRepoEntity,
    EmailFieldRepoEntity,
    EmailFieldUpdateRepoEntity,
    FileFieldRepoEntity,
    FileFieldUpdateRepoEntity,
    LeadAssignedEventEntity,
    LeadAssigneeUpdateRepoEntity,
    LeadBaseDataCacheEntity,
    LeadBulkServiceEntity,
    LeadCommentCreatedEventEntity,
    LeadContactUpdateRepoEntity,
    LeadCreatedEventEntity,
    LeadCreateRepoEntity,
    LeadCustomDataRepoEntity,
    LeadCustomFieldValueRepoEntity,
    LeadEventCreateRepoEntity,
    LeadFieldForImportFieldEntity,
    LeadFieldForImportSectionEntity,
    LeadFieldTypeUUIDRepoEntity,
    LeadMoveDataEntity,
    LeadPosRepoEntity,
    LeadQuotationSubmittedEventEntity,
    LeadReminderCreatedEventEntity,
    LeadSectionWithFieldDataRepoEntity,
    LeadServiceEntity,
    LeadServiceUpdateEntity,
    LeadStageChangedEventEntity,
    LeadStageRepoEntity,
    LeadSystemDataRepoEntity,
    LeadSystemFieldEntity,
    LeadSystemUpdateDataRepoEntity,
    LeadTaskCreatedEventEntity,
    LeadUpdateRepoEntity,
    LeadUserActionData,
    MultiDropdownFieldRepoEntity,
    MultiDropdownFieldUpdateRepoEntity,
    NumberFieldRepoEntity,
    NumberFieldUpdateRepoEntity,
    PhoneFieldRepoEntity,
    PhoneFieldUpdateRepoEntity,
    RichTextFieldRepoEntity,
    RichTextFieldUpdateRepoEntity,
    TextFieldRepoEntity,
    TextFieldUpdateRepoEntity,
)
from crm.lead.domain.services.triggers import trigger_lead_assignment_event, trigger_lead_create_event
from crm.lead.domain.validators import LeadSystemFieldValidator
from project.interface.external.entities import ProjectDetailsForLeadEntity
from project.interface.external.lead import LeadToProjectFactory
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


class LeadEventsService:
    def __init__(self, repo: LeadEventAbstractRepo):
        self.repo = repo

    def get_event_entity(self, lead_id: int, event_type: str, context_data: Dict[str, Any], user_id: int):
        lead_event_create_repo_entity = LeadEventCreateRepoEntity(
            lead_id=lead_id,
            type=event_type,
        )
        if event_type == LeadEventTypeEnum.CREATED.value:
            lead_event_entity = LeadCreatedEventEntity(name=context_data.get("name"))
            lead_event_create_repo_entity.lead_created_event_entity = lead_event_entity
        if event_type == LeadEventTypeEnum.ASSIGNED.value:
            lead_event_entity = LeadAssignedEventEntity(lead_id=lead_id, assignee_ids=context_data.get("assignee_ids"))
            lead_event_create_repo_entity.lead_assigned_event_entities = lead_event_entity
        if event_type == LeadEventTypeEnum.STAGE_CHANGED.value:
            lead_event_entity = LeadStageChangedEventEntity(
                lead_id=lead_id,
                from_stage_id=context_data.get("from_stage_id"),
                to_stage_id=context_data.get("to_stage_id"),
            )
            lead_event_create_repo_entity.lead_stage_changed_event_entity = lead_event_entity
        if event_type == LeadEventTypeEnum.REMINDER_CREATED.value:
            lead_event_entity = LeadReminderCreatedEventEntity(
                lead_id=lead_id, reminder_id=context_data.get("reminder_id")
            )
            lead_event_create_repo_entity.lead_reminder_created_event_entity = lead_event_entity
        if event_type == LeadEventTypeEnum.TASK_CREATED.value:
            lead_event_entity = LeadTaskCreatedEventEntity(lead_id=lead_id, task_id=context_data.get("task_id"))
            lead_event_create_repo_entity.lead_task_created_event_entity = lead_event_entity
        if event_type == LeadEventTypeEnum.COMMENT_CREATED.value:
            lead_event_entity = LeadCommentCreatedEventEntity(
                lead_id=lead_id, comment_id=context_data.get("comment_id")
            )
            lead_event_create_repo_entity.lead_comment_created_event_entity = lead_event_entity
        if event_type == LeadEventTypeEnum.QUOTATION_SUBMITTED.value:
            lead_event_entity = LeadQuotationSubmittedEventEntity(
                lead_id=lead_id, quotation_id=context_data.get("quotation_id")
            )
            lead_event_create_repo_entity.lead_quotation_submitted_event_entity = lead_event_entity
        return lead_event_create_repo_entity

    def prepare_lead_event_create_data(self, lead_id: int, event_type: str, user_id: int, context_data: Dict[str, Any]):
        lead_event_create_repo_entity = self.get_event_entity(
            lead_id=lead_id, event_type=event_type, user_id=user_id, context_data=context_data
        )
        return lead_event_create_repo_entity

    def create_lead_event(self, lead_id: int, event_type: str, user_id: int, context_data: Dict[str, Any]):
        logger.info(
            "Started Creating Lead Event",
            lead_id=lead_id,
            user_id=user_id,
            context_data=context_data,
            event_type=event_type,
        )
        lead_event_create_repo_entity = self.prepare_lead_event_create_data(
            lead_id=lead_id, event_type=event_type, user_id=user_id, context_data=context_data
        )
        logger.info("Event Repo Data Created", lead_event_create_repo_entity=lead_event_create_repo_entity)
        self.repo.create(data=lead_event_create_repo_entity, user_id=user_id)
        logger.info("Lead Event Created", lead_id=lead_id)

    def get_activity_timeline(self, lead_id: int):
        pass


class LeadService:
    class LeadServiceException(BaseValidationError):
        pass

    class LeadFieldValidationException(LeadServiceException):
        pass

    class LeadDoesNotExistException(BaseValidationError):
        pass

    def __init__(
        self,
        repo: LeadAbstractRepo,
        user_id: int,
        lead_events_service: LeadEventsService,
        board_user_service: BoardUserService,
        timezone: pytz.tzinfo.BaseTzInfo,
    ) -> None:
        self.repo = repo
        self.system_field_uuids = LEAD_SYSTEM_FIELD_UUIDS
        self.system_sections_uuids = LEAD_SYSTEM_SECTION_UUIDS
        self.error_list = []
        self.user_id = user_id
        self.lead_events_service = lead_events_service
        self.board_user_service = board_user_service
        self.timezone = timezone

    @property
    def error(self):
        return self.error_list

    def _prepare_system_field_data_to_validate(self, system_fields_data: Dict[str, Any]) -> Dict:
        return {
            "name": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_NAME.value)
            ),
            "amount": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_VALUE.value)
            ),
            "company_id": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_COMPANY.value)
            ),
            "country_id": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_COUNTRY.value)
            ),
            "state_id": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_STATE.value)
            ),
            "city_id": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_CITY.value)
            ),
            "assignee_ids": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_ASSIGNEE.value)
            ),
            "contact_ids": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_CONTACT.value)
            ),
            "address_line_1": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_ADDRESS.value)
            ),
            "address_line_2": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value)
            ),
            "zip_code": system_fields_data.get(
                LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING.get(CustomFieldTypeEnum.LEAD_ZIPCODE.value)
            ),
        }

    def _prepare_custom_field_data_to_validate(
        self, uuid_field_types: List[LeadFieldTypeUUIDRepoEntity], custom_fields_data: Dict[str, Any]
    ) -> List[Dict]:
        custom_field_data_to_validate = []
        for uuid_field_type in uuid_field_types:
            if uuid_field_type.uuid not in self.system_field_uuids:
                field_type = uuid_field_type.type
                value = custom_fields_data.get(str(uuid_field_type.uuid))
                custom_field_data_to_validate.append(
                    {
                        "field_uuid": str(uuid_field_type.uuid),
                        "type": field_type,
                        "value": value,
                        "is_required": uuid_field_type.is_required,
                        "field_id": uuid_field_type.field_id,
                    }
                )
        return custom_field_data_to_validate

    def _seggregate_system_and_custom_fields(self, data: LeadServiceEntity) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        system_fields_data = {}
        custom_fields_data = {}
        for field_uuid, field_data in data.fields.items():
            if field_uuid in self.system_field_uuids:
                system_fields_data[field_uuid] = field_data
            else:
                custom_fields_data[field_uuid] = field_data
        return system_fields_data, custom_fields_data

    def _get_system_field_error_key(self, field: str) -> str:
        if field == "name":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_NAME.value]
        if field == "amount":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_VALUE.value]
        if field == "company_id":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_COMPANY.value]
        if field == "country_id":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_COUNTRY.value]
        if field == "state_id":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_STATE.value]
        if field == "city_id":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_CITY.value]
        if field == "assignee_ids":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_ASSIGNEE.value]
        if field == "contact_ids":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_CONTACT.value]
        if field == "address_line_1":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_ADDRESS.value]
        if field == "address_line_2":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value]
        if field == "zip_code":
            return LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING[CustomFieldTypeEnum.LEAD_ZIPCODE.value]

    def _validate_incoming_data_for_mandatory_fields(
        self, incoming_data: Dict[str, Any], field_is_required_map: Dict[str, bool]
    ):
        # Validate if data is coming for mandatory fields
        # For backward lead data, where custom field data might not be present on lead (NULL data not stored case)
        assert len(incoming_data) >= len(field_is_required_map), "All fields are not present in input."

        for uuid, value in incoming_data.items():
            if uuid not in field_is_required_map:
                # NOTE: Should Not Happen
                # self.error_list.append({uuid: ["This field is not valid"]})
                continue

            if field_is_required_map[uuid] is True and is_empty(value):
                self.error_list.append({uuid: ["This field is required"]})

    def _validate_system_field_data(
        self, system_fields_data: Dict[str, Any], system_field_is_required_map: Dict[str, bool]
    ) -> LeadSystemFieldValidator:
        logger.info(
            "Validating system field input data",
            system_fields_data=system_fields_data,
            system_field_is_required_map=system_field_is_required_map,
        )
        self._validate_incoming_data_for_mandatory_fields(
            incoming_data=system_fields_data, field_is_required_map=system_field_is_required_map
        )
        # Validate if valid data is coming for the fields
        system_field_validator = LeadSystemFieldValidator(
            data=self._prepare_system_field_data_to_validate(system_fields_data)
        )
        if not system_field_validator.is_valid():
            errors = system_field_validator.errors
            errors = json.loads(json.dumps(errors))
            for field, error in errors.items():
                self.error_list.append({self._get_system_field_error_key(field=field): error})
            logger.error("System Field Validation Failed", errors=self.error_list)
        return system_field_validator

    def _validate_custom_field_data(
        self, custom_fields_data: Dict[str, Any], custom_field_is_required_map: dict[str, bool]
    ) -> CustomFieldDataValidator:
        logger.info(
            "Validating custom field input data",
            custom_fields_data=custom_fields_data,
            custom_field_is_required_map=custom_field_is_required_map,
        )
        self._validate_incoming_data_for_mandatory_fields(
            incoming_data=custom_fields_data, field_is_required_map=custom_field_is_required_map
        )
        field_uuids = list(set(list(custom_fields_data.keys()) + list(custom_field_is_required_map.keys())))
        # Validate if valid data is coming for the fields
        uuid_field_types = self.repo.get_fields_type(uuids=field_uuids)
        custom_field_validator = CustomFieldListDataValidator(
            data={
                "data": self._prepare_custom_field_data_to_validate(
                    uuid_field_types=uuid_field_types, custom_fields_data=custom_fields_data
                )
            },
            utc_date_conversion=False,
            context={"timezone": self.timezone},
        )
        if not custom_field_validator.is_valid():
            errors = custom_field_validator.get_error_list
            for error in errors:
                self.error_list.append(error)
            logger.error("Custom Field Validation Failed", errors=self.error_list)
        return custom_field_validator

    def _get_field_requirement_mappings(
        self, lead_id: int | None, board_id: int
    ) -> tuple[Dict[str, bool], Dict[str, bool]]:
        logger.info("Preparing field is required map.", lead_id=lead_id, board_id=board_id)
        sections_config = self.repo.get_section_and_field_config_for_lead(lead_id=lead_id, board_id=board_id)
        system_field_uuid_to_is_required_map = {}
        custom_field_uuid_to_is_required_map = {}
        for section in sections_config:
            for field in section.fields:
                if str(field.uuid) in self.system_field_uuids:
                    if field.uuid == LEAD_NUMBER_UUID:
                        continue
                    system_field_uuid_to_is_required_map[str(field.uuid)] = field.is_required
                else:
                    custom_field_uuid_to_is_required_map[str(field.uuid)] = field.is_required
        return system_field_uuid_to_is_required_map, custom_field_uuid_to_is_required_map

    def _prepare_and_validate_lead_data(
        self, data: LeadServiceEntity, board_id: int, lead_id: int | None = None
    ) -> Tuple[LeadSystemFieldEntity, List[CustomFieldEntity]]:
        logger.info("Preparing and Validating Lead Data", data=data)

        (
            system_field_uuid_to_is_required_map,
            custom_field_uuid_to_is_required_map,
        ) = self._get_field_requirement_mappings(lead_id=lead_id, board_id=board_id)
        system_fields_data, custom_fields_data = self._seggregate_system_and_custom_fields(data=data)
        system_field_validator = self._validate_system_field_data(
            system_fields_data=system_fields_data, system_field_is_required_map=system_field_uuid_to_is_required_map
        )
        custom_field_validator = self._validate_custom_field_data(
            custom_fields_data=custom_fields_data, custom_field_is_required_map=custom_field_uuid_to_is_required_map
        )
        if len(self.error_list):
            raise self.LeadFieldValidationException("Lead Field Validation Failed")

        custom_fields_validated_data: List[CustomFieldEntity] = custom_field_validator.validated_data
        logger.info("Lead Field Data Validated", custom_fields_validated_data=custom_fields_validated_data)
        system_fields_validated_data: LeadSystemFieldEntity = system_field_validator.validated_data
        logger.info("Lead System Field Data Validated", system_fields_validated_data=system_fields_validated_data)
        return system_fields_validated_data, custom_fields_validated_data

    def _get_repo_data_for_lead_creation(
        self,
        stage_id: int,
        system_field_data: LeadSystemFieldEntity,
        custom_fields_data: List[CustomFieldEntity],
        lead_position: int,
        serial_number: int,
        source: Optional[str] = None,
    ):
        dropdown_fields_data = []
        number_fields_data = []
        text_fields_data = []
        file_fields_data = []
        rich_text_fields_data = []
        email_fields_data = []
        phone_fields_data = []
        boolean_fields_data = []
        multi_dropdown_fields_data = []
        date_fields_data = []
        datetime_fields_data = []

        data_counter_objs = []

        for custom_field in custom_fields_data:
            if custom_field.type == CustomFieldTypeEnum.DROPDOWN.value:
                dropdown_fields_data.append(
                    DropdownFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.DECIMAL.value:
                number_fields_data.append(
                    NumberFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.TEXT.value:
                text_fields_data.append(
                    TextFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.FILE.value:
                file_fields_data.append(
                    FileFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                rich_text_fields_data.append(
                    RichTextFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.EMAIL.value:
                email_fields_data.append(
                    EmailFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                phone_fields_data.append(
                    PhoneFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.BOOLEAN.value:
                boolean_fields_data.append(
                    BooleanFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                multi_dropdown_fields_data.append(
                    MultiDropdownFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                if custom_field.value:
                    data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.DATE.value:
                date_fields_data.append(
                    DateFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))
            elif custom_field.type == CustomFieldTypeEnum.DATETIME.value:
                datetime_fields_data.append(
                    DateTimeFieldRepoEntity(
                        field_uuid=custom_field.field_uuid, field_id=custom_field.field_id, value=custom_field.value
                    )
                )
                data_counter_objs.append(DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id))

        return LeadCreateRepoEntity(
            name=system_field_data.name,
            stage_id=stage_id,
            position=lead_position,
            amount=system_field_data.amount,
            company_id=system_field_data.company_id,
            country_id=system_field_data.country_id,
            state_id=system_field_data.state_id,
            city_id=system_field_data.city_id,
            address_line_1=system_field_data.address_line_1,
            address_line_2=system_field_data.address_line_2,
            zip_code=system_field_data.zip_code,
            assignee_ids=system_field_data.assignee_ids,
            contact_ids=system_field_data.contact_ids,
            dropdown_fields_data=dropdown_fields_data,
            number_fields_data=number_fields_data,
            text_fields_data=text_fields_data,
            file_fields_data=file_fields_data,
            rich_text_fields_data=rich_text_fields_data,
            email_fields_data=email_fields_data,
            phone_fields_data=phone_fields_data,
            boolean_fields_data=boolean_fields_data,
            multi_dropdown_fields_data=multi_dropdown_fields_data,
            date_fields_data=date_fields_data,
            datetime_fields_data=datetime_fields_data,
            data_counters=data_counter_objs,
            source=source,
            serial_number=serial_number,
        )

    def _prepare_board_user_service_data(self, assignee_ids: list[int]):
        return [BoardUserInputEntity(user_id=assignee_id, can_edit_leads=True) for assignee_id in assignee_ids]

    def create_lead(
        self,
        board_id: int,
        data: LeadServiceEntity,
        stage_id: int,
        org_id: int,
        lead_stage_position_cache: Type[LeadStagePositionCache],
        lead_serial_number_cache: Type[LeadSerialNumberCache],
    ):
        logger.info("Creating lead", board_id=board_id, data=data)
        lead_position = lead_stage_position_cache.incr(
            key=LeadBaseDataCacheEntity(board_id=board_id, stage_id=stage_id)
        )
        next_serial_number = lead_serial_number_cache.incr(key=org_id)
        system_field_data, custom_fields_data = self._prepare_and_validate_lead_data(data=data, board_id=board_id)
        logger.info(
            "Data Prepared",
            board_id=board_id,
            system_fields_data=system_field_data,
            custom_fields_data=custom_fields_data,
        )
        repo_data = self._get_repo_data_for_lead_creation(
            stage_id=stage_id,
            system_field_data=system_field_data,
            custom_fields_data=custom_fields_data,
            lead_position=lead_position,
            serial_number=next_serial_number,
            source=data.source,
        )
        logger.info("Repo Data Prepared", board_id=board_id, repo_data=repo_data)
        try:
            lead_id = self.repo.create(board_id=board_id, data=repo_data, user_id=self.user_id, org_id=org_id)
            self.board_user_service.create_board_user_while_lead_creation(board_user_id=self.user_id, board_id=board_id)
            if self.user_id in system_field_data.assignee_ids:
                system_field_data.assignee_ids.remove(self.user_id)

            self.board_user_service.create_board_users_while_lead_assignee(
                board_id=board_id,
                board_user_ids=system_field_data.assignee_ids,
                user_id=self.user_id,
            )
        except LeadAbstractRepo.LeadCreationRepoConstraintException as e:
            logger.error("Lead Creation Failed", board_id=board_id, error=e)
            self.error_list.append(e.message)
            raise self.LeadServiceException("Lead Creation Failed")
        except LeadAbstractRepo.LeadCreationRepoException as e:
            logger.error("Lead Creation Failed", board_id=board_id, error=e)
            self.error_list.append(str(e))
            raise self.LeadServiceException("Lead Creation Failed")
        except LeadAbstractRepo.LeadSearchVectorIndexException as e:
            logger.error("Lead Search Vector Indexing Failed", error=e)
            self.error_list.append("You can not assign more than 30 assignees to a lead")
            raise self.LeadServiceException("You can not assign more than 30 assignees to a lead")
        except LeadAbstractRepo.LeadServiceRepoException as e:
            logger.error("Lead Service Repo Exception", board_id=board_id, error=e)
            self.error_list.append(str(e))
            raise self.LeadServiceException("Lead Creation Failed")
        self.lead_events_service.create_lead_event(
            lead_id=lead_id,
            event_type=LeadEventTypeEnum.CREATED.value,
            user_id=self.user_id,
            context_data={"name": repo_data.name},
        )
        if system_field_data.assignee_ids:
            self.lead_events_service.create_lead_event(
                lead_id=lead_id,
                event_type=LeadEventTypeEnum.ASSIGNED.value,
                user_id=self.user_id,
                context_data={"assignee_ids": system_field_data.assignee_ids},
            )
        trigger_lead_create_event(
            lead_id=lead_id,
            board_id=board_id,
            created_by_id=self.user_id,
        )
        logger.info("Lead Created For Board", board_id=board_id, lead_id=lead_id)

        return lead_id

    def bulk_create_lead(self, board_id: int, leads_data: LeadBulkServiceEntity, org_id: int):
        logger.info("Bulk Creating lead", board_id=board_id, leads_data=leads_data)

        status_list = []

        for lead_data in leads_data.field_data_list:
            stage_id = lead_data.stage_id

            data = LeadServiceEntity(fields=lead_data.fields, source=leads_data.source)
            lead_position = LeadStagePositionCache.incr(
                key=LeadBaseDataCacheEntity(board_id=board_id, stage_id=stage_id)
            )
            next_serial_number = LeadSerialNumberCache.incr(key=org_id)

            system_field_data, custom_fields_data = self._prepare_and_validate_lead_data(data=data, board_id=board_id)
            logger.info(
                "Data Prepared",
                board_id=board_id,
                system_fields_data=system_field_data,
                custom_fields_data=custom_fields_data,
            )

            repo_data = self._get_repo_data_for_lead_creation(
                stage_id=stage_id,
                system_field_data=system_field_data,
                custom_fields_data=custom_fields_data,
                lead_position=lead_position,
                serial_number=next_serial_number,
                source=leads_data.source,
            )
            logger.info("Repo Data Prepared", board_id=board_id, repo_data=repo_data)
            try:
                lead_id = self.repo.create(board_id=board_id, data=repo_data, user_id=self.user_id, org_id=org_id)
                self.board_user_service.create_board_user_while_lead_creation(
                    board_user_id=self.user_id, board_id=board_id
                )
                if self.user_id in system_field_data.assignee_ids:
                    system_field_data.assignee_ids.remove(self.user_id)

                self.board_user_service.create_board_users_while_lead_assignee(
                    board_id=board_id,
                    board_user_ids=system_field_data.assignee_ids,
                    user_id=self.user_id,
                )

            except LeadAbstractRepo.LeadCreationRepoException as e:
                status_list.append((lead_data.fields["id"], str(e)))
                continue
            except LeadAbstractRepo.LeadSearchVectorIndexException:
                status_list.append((lead_data.fields["id"], "You can not assign more than 30 assignees to a lead"))
                continue
            except LeadAbstractRepo.LeadServiceRepoException as e:
                status_list.append((lead_data.fields["id"], str(e)))
                continue

            self.lead_events_service.create_lead_event(
                lead_id=lead_id,
                event_type=LeadEventTypeEnum.CREATED.value,
                user_id=self.user_id,
                context_data={"name": repo_data.name},
            )

            if system_field_data.assignee_ids:
                self.lead_events_service.create_lead_event(
                    lead_id=lead_id,
                    event_type=LeadEventTypeEnum.ASSIGNED.value,
                    user_id=self.user_id,
                    context_data={"assignee_ids": system_field_data.assignee_ids},
                )
            logger.info("Lead Created For Board", board_id=board_id, lead_id=lead_id)

            status_list.append((lead_id, "Lead Created"))

        return status_list

    def _get_lead_system_fields_data(
        self,
        lead_id: int,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
        board_permissions: BoardPermissionType,
    ) -> Tuple[List[LeadSectionWithFieldDataRepoEntity], LeadSystemDataRepoEntity]:
        """
        Get the custom fields data fom Lead,LeadAssignee, LeadContact Table
        """
        logger.info("Getting lead system fields data", lead_id=lead_id)
        try:
            data = self.repo.get_lead_system_data(
                lead_id=lead_id,
                user_id=self.user_id,
                board_permissions=board_permissions,
            )
        except LeadAbstractRepo.LeadDoesNotExistException as e:
            logger.error("Lead Does Not Exist", lead_id=lead_id, error=e)
            raise self.LeadDoesNotExistException("Lead Does Not Exist")
        logger.info("Lead system fields repo data fetched.", data=data)
        for section in sections_config:
            if str(section.uuid) in self.system_sections_uuids:
                for field in section.fields:
                    if field.type == CustomFieldTypeEnum.LEAD_NUMBER.value:
                        field.value = add_lead_number_padding(data.serial_number)
                        continue

                    if field.created_at > data.created_at:
                        continue
                    if str(field.uuid) in self.system_field_uuids:
                        if field.type == CustomFieldTypeEnum.LEAD_NAME.value:
                            field.value = data.name

                        elif field.type == CustomFieldTypeEnum.LEAD_VALUE.value:
                            field.value = data.amount

                        elif field.type == CustomFieldTypeEnum.LEAD_ASSIGNEE.value:
                            value = []
                            for assignee in data.assignees:
                                assignee_dict = assignee.__dict__
                                assignee_dict["phone"] = assignee.phone.__dict__ if assignee.phone else None
                                value.append(assignee_dict)
                            field.value = value

                        elif field.type == CustomFieldTypeEnum.LEAD_COMPANY.value:
                            field.value = data.company

                        elif field.type == CustomFieldTypeEnum.LEAD_CONTACT.value:
                            value = []
                            for contact in data.contacts:
                                contact_dict = contact.__dict__
                                contact_dict["phone"] = contact.phone.__dict__ if contact.phone else None
                                value.append(contact_dict)
                            field.value = value

                        elif field.type == CustomFieldTypeEnum.LEAD_COUNTRY.value:
                            if data.country:
                                field.value = {"id": HashIdConverter.encode(data.country.id), "name": data.country.name}

                        elif field.type == CustomFieldTypeEnum.LEAD_STATE.value:
                            if data.state:
                                field.value = {"id": HashIdConverter.encode(data.state.id), "name": data.state.name}

                        elif field.type == CustomFieldTypeEnum.LEAD_CITY.value:
                            if data.city:
                                field.value = {"id": HashIdConverter.encode(data.city.id), "name": data.city.name}
                        elif field.type == CustomFieldTypeEnum.LEAD_ADDRESS.value:
                            field.value = data.address_line_1
                        elif field.type == CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value:
                            field.value = data.address_line_2
                        elif field.type == CustomFieldTypeEnum.LEAD_ZIPCODE.value:
                            field.value = data.zip_code

        logger.info("Lead system fields data processed.", sections_config=sections_config, data=data)
        return sections_config, data

    def _remove_sections_and_field_which_are_created_after_lead(
        self,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
        lead_created_at: datetime,
        lead_field_uuids: list[str],
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        """
        This function removes the sections and fields which are created after the lead
        so it won't be shown in the lead detail page because in the data table there is
        no entry for these fields and sections
        """
        final_sections = []
        for section in sections_config:
            if section.archived_at and lead_created_at > section.archived_at:
                # When Lead created after the section is archived, then we should not show that section
                continue

            fields = []
            for field in section.fields:
                if (
                    field.uuid == LEAD_NUMBER_UUID
                    or field.uuid == LEAD_CREATION_DATE_UUID
                    or field.uuid == LEAD_STAGE_NAME_UUID
                ):
                    fields.append(field)
                    continue

                if field.archived_at and lead_created_at > field.archived_at:
                    # When Lead created after the field is archived, then we should not show that field
                    continue
                if field.created_at > lead_created_at and str(field.uuid) not in lead_field_uuids:
                    continue
                fields.append(field)

            section.fields = fields
            if len(fields) > 0:
                final_sections.append(section)
        return final_sections

    def _get_lead_custom_fields_data(
        self,
        lead_id: int,
        board_id: int,
        sections_config: List[LeadSectionWithFieldDataRepoEntity],
    ) -> tuple[List[LeadSectionWithFieldDataRepoEntity], list[str]]:
        """
        Get the custom fields data fom BoardLeadFieldData Table
        """
        logger.info("Getting lead custom fields data", lead_id=lead_id, board_id=board_id)
        data = self.repo.get_lead_custom_data(board_id=board_id, lead_id=lead_id)
        logger.info("Lead custom fields repo data fetched.", data=data)
        field_id_data_map = {str(field.field_uuid): field for field in data}
        for section in sections_config:
            for field in section.fields:
                if str(field.uuid) not in self.system_field_uuids:
                    field_data = field_id_data_map.get(str(field.uuid))
                    if field_data:
                        if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                            field.value = field_data.value.value if field_data.value else None
                        elif field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                            field.value = [value.value for value in field_data.value] if field_data.value else []
                        else:
                            field.value = field_data.value.value if field_data.value else None
        logger.info("Lead custom fields data processed.", sections_config=sections_config)
        return sections_config, list(field_id_data_map.keys())

    def get_lead(
        self, lead_id: int, board_id: int, board_permissions: BoardPermissionType
    ) -> Tuple[List[LeadSectionWithFieldDataRepoEntity], LeadSystemDataRepoEntity]:
        logger.info("Getting Lead Data", lead_id=lead_id, board_id=board_id)
        sections_config = self.repo.get_lead_sections_config(board_id=board_id)
        logger.info("Sections Config", sections_config=sections_config)
        sections_config, lead_data = self._get_lead_system_fields_data(
            lead_id=lead_id,
            sections_config=sections_config,
            board_permissions=board_permissions,
        )
        logger.info("System lead data fetched.", sections_config=sections_config, lead_data=lead_data)
        sections_config, lead_field_uuids = self._get_lead_custom_fields_data(
            lead_id=lead_id, board_id=board_id, sections_config=sections_config
        )
        logger.info("Custom lead data fetched.", sections_config=sections_config, lead_field_uuids=lead_field_uuids)
        # lead_field_uuids -> field uuids which have data stored on lead
        sections_config = self._remove_sections_and_field_which_are_created_after_lead(
            sections_config=sections_config, lead_created_at=lead_data.created_at, lead_field_uuids=lead_field_uuids
        )
        logger.info(
            "Sections and Fields created after lead removed.", sections_config=sections_config, lead_data=lead_data
        )
        return sections_config, lead_data

    def get_lead_latched_project(self, lead_id: int) -> ProjectDetailsForLeadEntity | None:
        project_id = self.repo.get_lead_latched_project(lead_id=lead_id)
        if project_id:
            service = LeadToProjectFactory(project_id=project_id).service
            return service.get_lead_latched_project()
        return None

    def _remove_lead_number_field(self, section_config: List[LeadSectionWithFieldDataRepoEntity]):
        for section in section_config:
            section.fields = [field for field in section.fields if field.uuid != LEAD_NUMBER_UUID]
        return section_config

    def get_duplicate_lead(
        self, lead_id: int, board_id: int, board_permissions: BoardPermissionType
    ) -> Tuple[List[LeadSectionWithFieldDataRepoEntity], LeadStageRepoEntity]:
        sections_config = self.repo.get_lead_sections_config(board_id=board_id)
        sections_config, lead_data = self._get_lead_system_fields_data(
            lead_id=lead_id,
            sections_config=sections_config,
            board_permissions=board_permissions,
        )
        sections_config, _ = self._get_lead_custom_fields_data(
            lead_id=lead_id,
            board_id=board_id,
            sections_config=sections_config,
        )

        sections_config = self._remove_lead_number_field(section_config=sections_config)
        return sections_config, lead_data.stage

    def update_lead_stage(
        self, board_id: int, lead_id: int, stage_id: int, user_id: int, board_permissions: BoardPermissionType
    ):
        lead_position = self.repo.get_next_position(board_id=board_id, stage_id=stage_id)
        try:
            lead_data = self.repo.get_lead_system_data(
                lead_id=lead_id,
                user_id=self.user_id,
                board_permissions=board_permissions,
            )
        except LeadAbstractRepo.LeadDoesNotExistException as e:
            logger.info("Lead Does Not Exist", lead_id=lead_id, error=e)
            return
        if lead_data.stage.id == stage_id:
            logger.info("Lead Stage is Same", lead_id=lead_id, stage_id=stage_id)
            return
        self.repo.update_stage(lead_id=lead_id, stage_id=stage_id, user_id=user_id, lead_position=lead_position)
        self.lead_events_service.create_lead_event(
            lead_id=lead_id,
            event_type=LeadEventTypeEnum.STAGE_CHANGED.value,
            user_id=self.user_id,
            context_data={"from_stage_id": lead_data.stage.id, "to_stage_id": stage_id},
        )

    def _get_assignee_repo_data(
        self, system_field_data: LeadSystemUpdateDataRepoEntity, saved_system_data: LeadSystemUpdateDataRepoEntity
    ) -> List[LeadAssigneeUpdateRepoEntity]:
        assignee_objs = []
        if system_field_data.assignee_ids is None:
            system_field_data.assignee_ids = []
        for new_assignee in system_field_data.assignee_ids:
            if new_assignee not in set(saved_system_data.assignee_ids):
                assignee_objs.append(
                    LeadAssigneeUpdateRepoEntity(id=new_assignee, object_status=ObjectStatus.ADD.value)
                )
        for saved_assignee in saved_system_data.assignee_ids:
            if saved_assignee not in set(system_field_data.assignee_ids):
                assignee_objs.append(
                    LeadAssigneeUpdateRepoEntity(id=saved_assignee, object_status=ObjectStatus.DELETE.value)
                )
        return assignee_objs

    def _get_contact_repo_data(
        self, system_field_data: LeadSystemUpdateDataRepoEntity, saved_system_data: LeadSystemUpdateDataRepoEntity
    ) -> List[LeadContactUpdateRepoEntity]:
        contact_objs = []
        if system_field_data.contact_ids is None:
            system_field_data.contact_ids = []
        for new_contact in system_field_data.contact_ids:
            if new_contact not in set(saved_system_data.contact_ids):
                contact_objs.append(LeadContactUpdateRepoEntity(id=new_contact, object_status=ObjectStatus.ADD.value))
        for saved_contact in saved_system_data.contact_ids:
            if saved_contact not in set(system_field_data.contact_ids):
                contact_objs.append(
                    LeadContactUpdateRepoEntity(id=saved_contact, object_status=ObjectStatus.DELETE.value)
                )
        return contact_objs

    def _get_multi_dropdwown_repo_data(
        self,
        custom_fields_data: List[CustomFieldEntity],
        saved_custom_fields_data: List[LeadCustomDataRepoEntity],
        data_counter_objs: List[DataCounterRepoEntity],
    ) -> Tuple[List[MultiDropdownFieldRepoEntity], List[DataCounterRepoEntity]]:
        multi_dropdown_fields_data = []
        saved_multi_dropdown_field_map: Dict[str, List[LeadCustomFieldValueRepoEntity]] = {
            str(field.field_uuid): field.value
            for field in saved_custom_fields_data
            if field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value
        }
        new_multi_dropdown_field_map: Dict[str, List[int]] = {
            str(field.field_uuid): field.value
            for field in custom_fields_data
            if field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value
        }
        for new_multi_dropdown_field in custom_fields_data:
            if new_multi_dropdown_field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                multi_dropdown_value = []
                for new_multi_dropdown_value in new_multi_dropdown_field.value:
                    saved_value_entity = saved_multi_dropdown_field_map.get(new_multi_dropdown_field.field_uuid, [])
                    saved_values = {value.value for value in saved_value_entity}
                    if new_multi_dropdown_value not in saved_values:
                        # Here Multidropdown value is added so id is None
                        multi_dropdown_value.append(
                            LeadCustomFieldValueRepoEntity(
                                id=None,
                                value=new_multi_dropdown_value,
                            )
                        )
                if multi_dropdown_value:
                    multi_dropdown_fields_data.append(
                        MultiDropdownFieldUpdateRepoEntity(
                            field_uuid=new_multi_dropdown_field.field_uuid,
                            field_id=new_multi_dropdown_field.field_id,
                            value=multi_dropdown_value,
                            object_status=ObjectStatus.ADD.value,
                        )
                    )
                    data_counter_objs.append(
                        DataCounterRepoEntity(is_increment=True, field_id=new_multi_dropdown_field.field_id)
                    )

        for saved_multi_dropdwon_field in saved_custom_fields_data:
            if saved_multi_dropdwon_field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                multi_dropdown_value = []
                for saved_multi_dropdown_value_entity in saved_multi_dropdwon_field.value:
                    saved_multi_dropdown_value = saved_multi_dropdown_value_entity.value
                    saved_multi_dropdown_value_id = saved_multi_dropdown_value_entity.id
                    if saved_multi_dropdown_value not in new_multi_dropdown_field_map.get(
                        str(saved_multi_dropdwon_field.field_uuid), []
                    ):
                        multi_dropdown_value.append(
                            LeadCustomFieldValueRepoEntity(
                                id=saved_multi_dropdown_value_id, value=saved_multi_dropdown_value
                            )
                        )

                if multi_dropdown_value:
                    multi_dropdown_fields_data.append(
                        MultiDropdownFieldUpdateRepoEntity(
                            field_uuid=saved_multi_dropdwon_field.field_uuid,
                            field_id=saved_multi_dropdwon_field.field_id,
                            value=multi_dropdown_value,
                            object_status=ObjectStatus.DELETE.value,
                        )
                    )
                    data_counter_objs.append(
                        DataCounterRepoEntity(is_increment=False, field_id=saved_multi_dropdwon_field.field_id)
                    )
        return multi_dropdown_fields_data, data_counter_objs

    def _get_field_data_object_status(
        self, saved_field_value: Any, new_field_value: Any, is_saved_field_none: bool
    ) -> ObjectStatus:
        if is_saved_field_none:
            # It Means data is added in the form
            # Now, Data for Fields with null value will also be added (Enhancement - Lead Field Configuration) # noqa
            return ObjectStatus.ADD.value
        if is_empty(saved_field_value) is True and is_empty(new_field_value) is False:
            # It Means data is added in the form
            return ObjectStatus.UPDATE.value
        if (
            is_empty(saved_field_value) is False and is_empty(new_field_value) is False
        ) and new_field_value != saved_field_value:
            # It Means data is updated in the form
            return ObjectStatus.UPDATE.value
        if is_empty(saved_field_value) is False and is_empty(new_field_value) is True:
            # It Means data is deleted in the form
            return ObjectStatus.DELETE.value

    def _get_data_counter(self, object_status: ObjectStatus, custom_field: CustomFieldEntity) -> DataCounterRepoEntity:
        if object_status == ObjectStatus.ADD.value and custom_field.value:
            return DataCounterRepoEntity(is_increment=True, field_id=custom_field.field_id)
        if object_status == ObjectStatus.DELETE.value:
            return DataCounterRepoEntity(is_increment=False, field_id=custom_field.field_id)

    def _get_repo_data_for_lead_update(
        self,
        system_field_data: LeadSystemFieldEntity,
        saved_custom_fields_data: List[LeadCustomDataRepoEntity],
        custom_fields_data: List[CustomFieldEntity],
        saved_system_data: LeadSystemUpdateDataRepoEntity,
        stage_id: int,
    ) -> LeadUpdateRepoEntity:
        saved_field_uuid_map = {str(field.field_uuid): field for field in saved_custom_fields_data}
        logger.info("Saved Field UUID Map", saved_field_uuid_map=saved_field_uuid_map)
        dropdown_fields_data = []
        number_fields_data = []
        text_fields_data = []
        file_fields_data = []
        rich_text_fields_data = []
        email_fields_data = []
        phone_fields_data = []
        boolean_fields_data = []
        date_fields_data = []
        datetime_fields_data = []
        data_counter_objs = []
        multi_dropdown_fields_data, data_counter_objs = self._get_multi_dropdwown_repo_data(
            custom_fields_data=custom_fields_data,
            saved_custom_fields_data=saved_custom_fields_data,
            data_counter_objs=data_counter_objs,
        )
        logger.info("Multi Dropdown Fields Data", multi_dropdown_fields_data=multi_dropdown_fields_data)
        assignee_objs = self._get_assignee_repo_data(
            system_field_data=system_field_data, saved_system_data=saved_system_data
        )
        logger.info("Assignee Objects", assignee_objs=assignee_objs)
        contact_objs = self._get_contact_repo_data(
            system_field_data=system_field_data, saved_system_data=saved_system_data
        )
        logger.info("Contact Objects", contact_objs=contact_objs)
        for custom_field in custom_fields_data:
            if custom_field.field_uuid not in self.system_field_uuids:
                saved_field = saved_field_uuid_map.get(str(custom_field.field_uuid))

                saved_field_value_id = None
                saved_field_value = None
                is_saved_field_none = False
                if saved_field is None:
                    is_saved_field_none = True
                elif saved_field and saved_field.value:
                    if saved_field.type != CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                        saved_field_value_id = saved_field.value.id
                        saved_field_value = saved_field.value.value
                    else:
                        saved_field_value = set(field.value for field in saved_field.value)

                new_field_value = custom_field.value
                if custom_field.value is not None and custom_field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                    new_field_value = set(custom_field.value)

                # Now, storing value = null for lead in DB as well
                object_status = self._get_field_data_object_status(
                    saved_field_value=saved_field_value,
                    new_field_value=new_field_value,
                    is_saved_field_none=is_saved_field_none,
                )

                if custom_field.type == CustomFieldTypeEnum.DROPDOWN.value:
                    dropdown_fields_data.append(
                        DropdownFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )

                elif custom_field.type == CustomFieldTypeEnum.DECIMAL.value:
                    number_fields_data.append(
                        NumberFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.TEXT.value:
                    text_fields_data.append(
                        TextFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.FILE.value:
                    file_fields_data.append(
                        FileFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.RICH_TEXT.value:
                    rich_text_fields_data.append(
                        RichTextFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.EMAIL.value:
                    email_fields_data.append(
                        EmailFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
                    phone_fields_data.append(
                        PhoneFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.BOOLEAN.value:
                    boolean_fields_data.append(
                        BooleanFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.DATE.value:
                    date_fields_data.append(
                        DateFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                elif custom_field.type == CustomFieldTypeEnum.DATETIME.value:
                    datetime_fields_data.append(
                        DateTimeFieldUpdateRepoEntity(
                            field_uuid=custom_field.field_uuid,
                            field_id=custom_field.field_id,
                            value=LeadCustomFieldValueRepoEntity(id=saved_field_value_id, value=custom_field.value),
                            object_status=object_status,
                        )
                    )
                if custom_field.type != CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                    data_counter_obj = self._get_data_counter(object_status=object_status, custom_field=custom_field)
                    if data_counter_obj:
                        data_counter_objs.append(data_counter_obj)

        return LeadUpdateRepoEntity(
            name=system_field_data.name,
            amount=system_field_data.amount,
            company_id=system_field_data.company_id,
            country_id=system_field_data.country_id,
            state_id=system_field_data.state_id,
            city_id=system_field_data.city_id,
            address_line_1=system_field_data.address_line_1,
            address_line_2=system_field_data.address_line_2,
            zip_code=system_field_data.zip_code,
            assignees=assignee_objs,
            contacts=contact_objs,
            dropdown_fields_data=dropdown_fields_data,
            number_fields_data=number_fields_data,
            text_fields_data=text_fields_data,
            file_fields_data=file_fields_data,
            rich_text_fields_data=rich_text_fields_data,
            email_fields_data=email_fields_data,
            phone_fields_data=phone_fields_data,
            boolean_fields_data=boolean_fields_data,
            multi_dropdown_fields_data=multi_dropdown_fields_data,
            date_fields_data=date_fields_data,
            datetime_fields_data=datetime_fields_data,
            data_counters=data_counter_objs,
            stage_id=stage_id,
        )

    def _seggregate_final_assignee_ids(self, assignee_objs: list[LeadAssigneeUpdateRepoEntity]):
        return [
            assignee_obj.id for assignee_obj in assignee_objs if assignee_obj.object_status == ObjectStatus.ADD.value
        ]

    def update_lead(self, lead_id: int, data: LeadServiceUpdateEntity, board_id: int) -> LeadUpdateRepoEntity:
        """
        1. Validate Input Data and seggregate System and Custom Fields
        2. Fetch Lead System Field Data
        3. Fetch Lead Custom Field Data
        4. Seggegate Which field data to add, update and delete
        5. Send seggregated data to repo to save
        """
        system_field_data, custom_fields_data = self._prepare_and_validate_lead_data(
            data=data, board_id=board_id, lead_id=lead_id
        )
        logger.info(
            "Data Validated While Lead Updation",
            lead_id=lead_id,
            system_fields_data=system_field_data,
            custom_fields_data=custom_fields_data,
        )
        try:
            saved_system_data = self.repo.get_lead_system_update_data(lead_id=lead_id)
        except LeadAbstractRepo.LeadDoesNotExistException as e:
            logger.error("Lead Does Not Exist", lead_id=lead_id, error=e)
            raise self.LeadDoesNotExistException("Lead Already Deleted")

        logger.info("Saved System Data", lead_id=lead_id, saved_system_data=saved_system_data)
        saved_custom_fields_data = self.repo.get_lead_custom_update_data(board_id=board_id, lead_id=lead_id)
        logger.info("Saved Custom Fields Data", lead_id=lead_id, saved_custom_fields_data=saved_custom_fields_data)
        lead_repo_data = self._get_repo_data_for_lead_update(
            system_field_data=system_field_data,
            saved_custom_fields_data=saved_custom_fields_data,
            custom_fields_data=custom_fields_data,
            saved_system_data=saved_system_data,
            stage_id=data.stage_id,
        )
        assignee_ids = self._seggregate_final_assignee_ids(assignee_objs=lead_repo_data.assignees)

        logger.info("Repo Data Prepared For Lead Updation", lead_id=lead_id, repo_data=lead_repo_data)
        try:
            self.repo.update(data=lead_repo_data, lead_id=lead_id, user_id=self.user_id)
            self.board_user_service.create_board_users_while_lead_assignee(
                board_user_ids=assignee_ids, board_id=board_id, user_id=self.user_id
            )
            if set(system_field_data.assignee_ids) and (
                set(system_field_data.assignee_ids) != set(saved_system_data.assignee_ids)
            ):
                self.lead_events_service.create_lead_event(
                    lead_id=lead_id,
                    event_type=LeadEventTypeEnum.ASSIGNED.value,
                    user_id=self.user_id,
                    context_data={"assignee_ids": system_field_data.assignee_ids},
                )
        except LeadAbstractRepo.LeadUpdationRepoException as e:
            logger.error("Lead Updation Failed", lead_id=lead_id, error=e)
            self.error_list.append(str(e))
            raise self.LeadServiceException("Lead Updation Failed")
        except LeadAbstractRepo.LeadSearchVectorIndexException as e:
            logger.error("Lead Search Vector Indexing Failed", lead_id=lead_id, error=e)
            self.error_list.append("You can not assign more than 30 assignees to a lead")
            raise self.LeadServiceException("You can not assign more than 30 assignees to a lead")
        except LeadAbstractRepo.LeadServiceRepoException as e:
            logger.error("Lead Updation Failed", lead_id=lead_id, error=e)
            self.error_list.append(str(e))
            raise self.LeadServiceException("Lead Creation Failed")
        if data.stage_id != saved_system_data.stage_id:
            self.lead_events_service.create_lead_event(
                lead_id=lead_id,
                event_type=LeadEventTypeEnum.STAGE_CHANGED.value,
                user_id=self.user_id,
                context_data={"from_stage_id": saved_system_data.stage_id, "to_stage_id": data.stage_id},
            )
        return lead_repo_data

    def move_lead_v2(self, data: LeadMoveDataEntity, user_id: int):
        # TODO: Refactor this method
        if data.target_stage_id == data.curr_stage_id:
            # If target stage is same as current stage means lead moves within the same stage
            if data.target_lead_id:
                # Case When Lead is moved to a specific position within the stage
                # Fetch lead position in the range from current lead to target lead position in within the stage
                lead_ranges = self.repo.get_lead_pos_in_range(
                    stage_id=data.curr_stage_id,
                    current_lead_id=data.current_lead_id,
                    target_lead_id=data.target_lead_id,
                    max_pos=None,
                )
                lead_id_range_map = {lead.id: lead.pos for lead in lead_ranges}
                current_lead_pos: int = lead_id_range_map.get(data.current_lead_id)
                target_lead_pos: int = lead_id_range_map.get(data.target_lead_id)

                lead_objs = []
                if current_lead_pos > target_lead_pos:
                    # Case When Current Lead is moved above the target lead
                    for lead_range in lead_ranges:
                        if lead_range.id == data.current_lead_id:
                            # Change the position of current lead to target lead position
                            lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=target_lead_pos))
                        else:
                            # Increase the position of leads which are between current lead and target lead
                            lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=lead_range.pos + 1))
                else:
                    # Case When Current Lead is moved below the target lead
                    for lead_range in lead_ranges:
                        if lead_range.id == data.current_lead_id:
                            # Change the position of current lead to target lead position
                            lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=target_lead_pos))
                        else:
                            # Decrease the position of leads which are between current lead and target lead
                            lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=lead_range.pos - 1))
                # Update the position of leads in the range
                self.repo.update_lead_pos_in_range(data=lead_objs, stage_id=data.curr_stage_id)
            else:
                # Case When Lead is moved to the end of the stage
                # Fetch the max position of the leads in the stage
                max_pos = self.repo.get_max_lead_position(stage_id=data.curr_stage_id) or 0
                # Fetch the lead position in the range from current lead to end of the stage
                lead_ranges = self.repo.get_lead_pos_in_range(
                    stage_id=data.curr_stage_id,
                    current_lead_id=data.current_lead_id,
                    target_lead_id=data.target_lead_id,
                    max_pos=max_pos,
                )
                lead_objs = []
                for lead_range in lead_ranges:
                    if lead_range.id == data.current_lead_id:
                        # Change the position of current lead to end of the stage
                        lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=target_lead_pos))
                    else:
                        # Decrease the position of leads which are between current lead and end of the stage
                        lead_objs.append(LeadPosRepoEntity(id=lead_range.id, pos=lead_range.pos - 1))
                # Update the position of leads in the range
                self.repo.update_lead_pos_in_range(
                    data=[LeadPosRepoEntity(id=data.current_lead_id, pos=max_pos)] + lead_objs,
                    stage_id=data.curr_stage_id,
                )
        else:
            # If target stage is different from current stage means lead moves to different stage
            if data.target_lead_id:
                # Case When Lead is moved to a specific position within the stage
                # Fetch target lead position
                target_lead_pos = self.repo.get_targe_lead_pos(lead_id=data.target_lead_id)
                # Update the leads positions in range between current lead and target lead position
                self.repo.update_lead_pos_in_diff_stage(stage_id=data.target_stage_id, target_lead_pos=target_lead_pos)
                # Update the current lead position to the target position
                self.repo.update_curr_lead_pos_in_diff_stage(
                    stage_id=data.target_stage_id, lead_id=data.current_lead_id, target_lead_pos=target_lead_pos
                )
            else:
                # Case When Lead is moved to the end of the stage
                # Fetch the max position of the leads in the stage
                max_pos = self.repo.get_max_lead_position(stage_id=data.target_stage_id) or 0
                # Update the current lead position to the end of the stage
                self.repo.update_curr_lead_pos_in_diff_stage(
                    stage_id=data.target_stage_id, lead_id=data.current_lead_id, target_lead_pos=max_pos + 1
                )
            self.lead_events_service.create_lead_event(
                lead_id=data.current_lead_id,
                event_type=LeadEventTypeEnum.STAGE_CHANGED.value,
                user_id=self.user_id,
                context_data={"from_stage_id": data.curr_stage_id, "to_stage_id": data.target_stage_id},
            )

    def update_lead_assignee(self, lead_id: int, assignee_ids: List[int], board_id: int):
        saved_assignees_ids = self.repo.get_lead_assignee_data(lead_id=lead_id)
        updated_assignees: List[LeadAssigneeUpdateRepoEntity] = []
        for assignee_id in assignee_ids:
            if assignee_id not in saved_assignees_ids:
                updated_assignees.append(
                    LeadAssigneeUpdateRepoEntity(id=assignee_id, object_status=ObjectStatus.ADD.value)
                )

        if updated_assignees:
            self.board_user_service.create_board_users_while_lead_assignee(
                board_id=board_id, board_user_ids=[assignee.id for assignee in updated_assignees], user_id=self.user_id
            )

        for saved_assignee_id in saved_assignees_ids:
            if saved_assignee_id not in assignee_ids:
                updated_assignees.append(
                    LeadAssigneeUpdateRepoEntity(id=saved_assignee_id, object_status=ObjectStatus.DELETE.value)
                )
        self.repo.update_lead_assignee_data(lead_id=lead_id, assignees=updated_assignees, user_id=self.user_id)
        if set(assignee_ids) and (set(saved_assignees_ids) != set(assignee_ids)):
            self.lead_events_service.create_lead_event(
                lead_id=lead_id,
                event_type=LeadEventTypeEnum.ASSIGNED.value,
                user_id=self.user_id,
                context_data={"assignee_ids": assignee_ids},
            )
            if self.user_id in assignee_ids:
                assignee_ids.remove(self.user_id)
            if assignee_ids:
                trigger_lead_assignment_event(lead_id=lead_id, assignee_ids=assignee_ids, assigned_by_id=self.user_id)

    def delete_lead(self, lead_id: int):
        self.repo.delete(lead_id=lead_id, user_id=self.user_id)

    def delete_board_leads(self, board_id: int):
        self.repo.delete_board_leads(board_id=board_id, user_id=self.user_id)

    def transfer_leads_to_stage_ids(self, from_to_stage_ids_mapping_list: dict):
        stage_ids = []
        from_stage_id_to_stage_id_mapping = {}
        for from_to_stage_id_mapping in from_to_stage_ids_mapping_list:
            stage_ids.extend(from_to_stage_id_mapping.get("from_stage_ids"))
            for stage_id in from_to_stage_id_mapping.get("from_stage_ids"):
                from_stage_id_to_stage_id_mapping[stage_id] = from_to_stage_id_mapping.get("to_stage_id")

        self.repo.transfer_leads_to_stage_ids(
            from_stage_id_to_stage_id_mapping=from_stage_id_to_stage_id_mapping, stage_ids=stage_ids
        )

    def get_leads_for_import(self, user) -> QuerySet:
        return self.repo.get_leads_for_import(user=user)

    def get_quotations_for_import(self, user) -> Optional[QuerySet]:
        return self.repo.get_quotations_for_import(user=user)

    def fetch_lead_fields_for_import(self, board_id: int, lead_id: int) -> list[LeadFieldForImportSectionEntity]:
        sections = self.repo.fetch_lead_fields_for_import(board_id=board_id, lead_id=lead_id)
        return [
            LeadFieldForImportSectionEntity(
                uuid=section.uuid,
                name=section.name,
                position=section.position,
                fields=[
                    LeadFieldForImportFieldEntity(
                        uuid=field.uuid,
                        name=field.name,
                        type=field.type,
                        position=field.position,
                        is_required=field.is_required,
                    )
                    for field in section.fields
                ],
            )
            for section in sections
        ]


class LeadInteractor:
    class Exception(BaseValidationError):
        pass

    def __init__(self, service: LeadService):
        self.service = service

    def get_lead_latched_project(self, lead_id: int) -> ProjectDetailsForLeadEntity | None:
        return self.service.get_lead_latched_project(lead_id=lead_id)


class CompanyService:
    class CompanyServiceException(BaseValidationError):
        pass

    def __init__(self, repo: CompanyAbstractRepo):
        self.repo = repo

    def create_company(self, data: CompanyEntity, user_id: int, client_id: int) -> CompanyRepoEntity:
        try:
            return self.repo.create(
                data=CompanyRepoEntity(
                    name=data.name,
                    state_id=data.state_id,
                    address_line_1=data.address_line_1,
                    address_line_2=data.address_line_2,
                    city_id=data.city_id,
                    zip_code=data.zip_code,
                    contact_ids=data.contact_ids,
                    country_id=data.country_id,
                ),
                user_id=user_id,
                client_id=client_id,
            )
        except CompanyAbstractRepo.CompanyContactException as e:
            logger.error("Company Creation Failed", error=e)
            raise self.CompanyServiceException("Company Creation Failed")

    def update_company(self, data: CompanyEntity, user_id: int) -> CompanyRepoEntity:
        try:
            return self.repo.update(
                data=CompanyRepoEntity(
                    id=data.id,
                    name=data.name,
                    state_id=data.state_id,
                    address_line_1=data.address_line_1,
                    address_line_2=data.address_line_2,
                    city_id=data.city_id,
                    zip_code=data.zip_code,
                    contact_ids=data.contact_ids,
                    country_id=data.country_id,
                ),
                user_id=user_id,
            )
        except CompanyAbstractRepo.CompanyContactException as e:
            logger.error("Company Updation Failed", error=e)
            raise self.CompanyServiceException("Company Updation Failed")


class ContactService:
    class ContactServiceException(BaseValidationError):
        pass

    def __init__(self, repo: ContactAbstractRepo):
        self.repo = repo

    def create_contact(self, data: ContactEntity, user_id: int, org_id: int) -> QuerySet:
        try:
            return self.repo.create(
                data=ContactRepoEntity(
                    name=data.name, email=data.email, phone=data.phone, company_ids=data.company_ids
                ),
                user_id=user_id,
                org_id=org_id,
            )
        except ContactAbstractRepo.ContactExceptionException as e:
            logger.error("Contact Creation Failed", error=e)
            raise self.ContactServiceException("Contact Creation Failed")

    def delete_contact(self, contact_id: int, company_id: int, user_id: int):
        self.repo.delete(contact_id=contact_id, company_id=company_id, user_id=user_id)

    def update_contact(self, data: ContactEntity, user_id: int, org_id: int) -> QuerySet:
        try:
            return self.repo.update(
                data=ContactRepoEntity(
                    id=data.id,
                    name=data.name,
                    email=data.email,
                    phone=data.phone,
                    company_ids=data.company_ids,
                ),
                user_id=user_id,
            )
        except ContactAbstractRepo.ContactExceptionException as e:
            logger.error("Contact Updation Failed", error=e)
            raise self.ContactServiceException("Contact Updation Failed")


class LeadReminderService:
    def __init__(self, repo: LeadReminderAbstractRepo, lead_events_service: LeadEventsService):
        self.repo = repo
        self.lead_events_service = lead_events_service

    def create(self, lead_id: int, data: Dict[str, Any], user_id: int):
        lead_reminder_id = self.repo.create(data=data, user_id=user_id, lead_id=lead_id)
        self.lead_events_service.create_lead_event(
            lead_id=lead_id,
            event_type=LeadEventTypeEnum.REMINDER_CREATED.value,
            user_id=user_id,
            context_data={"reminder_id": lead_reminder_id},
        )
        return lead_reminder_id


def remove_space(string: str) -> str:
    return string.replace(" ", "")


def get_breadcrumb(*, board_name: str, lead_name: str) -> str:
    return f"Leads > {board_name} > {lead_name}"


class LeadActionServiceV2:
    def __init__(self, user: User, lead_data: Optional[LeadUserActionData] = None):
        self.lead_data = lead_data
        self.user = user

    def can_create(self):
        assert self.lead_data is not None, "Lead Data is Required"

        return self.lead_data.can_create_lead

    def can_edit(self):
        assert self.lead_data is not None, "Lead Data is Required"

        return self.lead_data.can_edit_lead

    def can_add_field(self):
        assert self.lead_data is not None, "Lead Data is Required"

        return self.lead_data.can_add_field

    def can_delete(self):
        assert self.lead_data is not None, "Lead Data is Required"

        return self.lead_data.can_delete_lead

    def get_actions(self, can_edit_all_leads: bool):
        assert self.lead_data is not None, "Lead Data is Required"

        actions = []

        if self.can_create():
            actions.append(LeadActionEnum.CREATE_LEAD.value)
            actions.append(LeadActionEnum.DUPLICATE.value)
        if self.can_delete():
            actions.append(LeadActionEnum.DELETE.value)
        if self.can_edit() or can_edit_all_leads:
            actions.append(LeadActionEnum.EDIT.value)
            if self.lead_data.is_project_type and self.lead_data.project_id is None:
                actions.append(LeadActionEnum.CONVERT_TO_PROJECT.value)
        if self.can_add_field():
            actions.append(LeadActionEnum.ADD_FIELD.value)
        return actions

    def get_actions_for_homepage(self, board_id: int):
        can_create_lead = BoardPermissionHelper.is_action_permitted(
            user=self.user, board_id=board_id, permission=BoardPermissionEnum.CAN_CREATE_LEADS
        )

        if can_create_lead:
            return [LeadActionEnum.CREATE_LEAD.value]
        return []
