from functools import partial
from uuid import UUID

from django.db.transaction import on_commit

from common.events import Events
from common.events.lead import LeadExportExcelEventData
from common.events.services import trigger_event


def trigger_leads_export_excel_generated(
    download_id: int,
    board_id: int,
    uuid: UUID,
    org_id: int,
    user_id: int,
    board_name: str,
):
    on_commit(
        partial(
            trigger_event,
            event=Events.LEAD_EXPORT_EXCEL_GENERATED,
            event_data=LeadExportExcelEventData(
                download_id=download_id,
                board_id=board_id,
                uuid=uuid,
                org_id=org_id,
                user_id=user_id,
                board_name=board_name,
            ),
        ),
    )
