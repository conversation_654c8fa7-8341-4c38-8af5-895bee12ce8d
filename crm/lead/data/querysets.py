from django.contrib.postgres.aggregates import <PERSON><PERSON>y<PERSON>gg
from django.db.models import <PERSON><PERSON>an<PERSON>ield, Case, Exists, F, OuterRef, Q, QuerySet, Subquery, Value, When
from django.db.models.functions import Coalesce

from common.querysets import AvailableQuerySetMixin
from crm.board.domain.enums import BoardPermissionEnum
from crm.interface.type import BoardPermissionType


class BoardLeadFieldQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_lead_id(self) -> QuerySet:
        """
        Annotates the queryset with the lead_id field.
        This is useful for ensuring that the queryset can be used in subqueries or annotations.
        """
        return self.annotate(
            lead_id=Case(
                When(custom_text_data__isnull=False, then=F("custom_text_data__lead_id")),
                When(custom_drop_down_data__isnull=False, then=F("custom_drop_down_data__lead_id")),
                When(custom_email_data__isnull=False, then=F("custom_email_data__lead_id")),
                When(custom_number_data__isnull=False, then=F("custom_number_data__lead_id")),
                When(custom_date_data__isnull=False, then=F("custom_date_data__lead_id")),
                When(custom_boolean_data__isnull=False, then=F("custom_boolean_data__lead_id")),
                When(custom_rich_text_data__isnull=False, then=F("custom_rich_text_data__lead_id")),
                When(custom_phone_number_data__isnull=False, then=F("custom_phone_number_data__lead_id")),
                When(custom_file_data__isnull=False, then=F("custom_file_data__lead_id")),
                When(custom_datetime_data__isnull=False, then=F("custom_datetime_data__lead_id")),
                default=Value(None),
            )
        )


class LeadFieldQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class BoardQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LeadQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_assigned_to_ids(self) -> QuerySet:
        from crm.data.models import LeadAssignee

        subquery = (
            LeadAssignee.objects.filter(lead_id=OuterRef("pk"), deleted_at__isnull=True)
            .values("lead_id")
            .annotate(user_ids=ArrayAgg("user_id"))
            .values("user_ids")
        )
        return self.annotate(assigned_to_ids=Coalesce(Subquery(subquery), []))

    def annotate_edit_and_view_permissions(self, user_id: int, board_permissions: BoardPermissionType) -> QuerySet:
        from crm.data.models import LeadAssignee
        from crm.interface.helpers import BoardPermissionHelper

        can_edit_all_leads = BoardPermissionHelper.can_edit_all_leads(permissions=board_permissions)
        can_edit_assigned_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_EDIT_ASSIGNED_LEADS, permissions=board_permissions
        )
        can_edit_created_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_EDIT_CREATED_LEADS, permissions=board_permissions
        )

        can_view_all_leads = BoardPermissionHelper.can_view_all_leads(permissions=board_permissions)
        can_view_assigned_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_VIEW_ASSIGNED_LEADS, permissions=board_permissions
        )
        can_view_created_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_VIEW_CREATED_LEADS, permissions=board_permissions
        )

        can_delete_all_leads = BoardPermissionHelper.can_delete_all_leads(permissions=board_permissions)
        can_delete_assigned_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_DELETE_ASSIGNED_LEADS, permissions=board_permissions
        )
        can_delete_created_leads = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_DELETE_CREATED_LEADS, permissions=board_permissions
        )

        is_assignee = LeadAssignee.objects.available().filter(
            lead_id=OuterRef("pk"),
            user_id=user_id,
        )

        # Annotating the leads queryset
        queryset = self.annotate(
            can_edit_lead=Case(
                # Case 1: User can edit if they have EDIT_ALL_LEADS permission
                When(Q(Value(can_edit_all_leads)), then=Value(True)),
                # Case 2: User can edit if they are assigned or created the lead (ASSIGNED_AND_CREATED_LEADS)
                When(
                    Q(Value(can_edit_assigned_leads)) & (Exists(is_assignee)),
                    then=Value(True),
                ),
                # Case 3: User can only edit their created leads (ONLY_CREATED_LEADS)
                When(
                    Q(Value(can_edit_created_leads)) & Q(created_by_id=user_id),
                    then=Value(True),
                ),
                # Default: User cannot edit
                default=Value(False),
                output_field=BooleanField(),
            ),
            can_view_lead=Case(
                When(Q(Value(can_view_all_leads)), then=Value(True)),
                When(
                    Q(Value(can_view_assigned_leads)) & (Exists(is_assignee)),
                    then=Value(True),
                ),
                When(
                    Q(Value(can_view_created_leads)) & Q(created_by_id=user_id),
                    then=Value(True),
                ),
                default=Value(False),
                output_field=BooleanField(),
            ),
            can_delete_lead=Case(
                When(Q(Value(can_delete_all_leads)), then=Value(True)),
                When(
                    Q(Value(can_delete_assigned_leads)) & (Exists(is_assignee)),
                    then=Value(True),
                ),
                When(
                    Q(Value(can_delete_created_leads)) & Q(created_by_id=user_id),
                    then=Value(True),
                ),
                default=Value(False),
                output_field=BooleanField(),
            ),
        )
        return queryset.distinct()
