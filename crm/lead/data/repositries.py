import collections
from copy import deepcopy
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Iterator, List, Optional

import structlog
from django.contrib.postgres.search import SearchVector
from django.db import OperationalError, models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Exists, F, Max, OuterRef, Prefetch, Q, QuerySet, Value
from django.db.models.functions import Concat
from django.db.utils import IntegrityError
from django.utils import timezone

from authorization.domain.constants import Permissions
from client.data.models import VendorClientMapping
from common.constants import CustomFieldTypeEnum
from common.entities import ObjectStatus
from common.pydantic.base_model import PhoneEntity
from common.serializers import PhoneNumberSerializer
from common.services import model_update
from core.caches import OrganizationCountryConfigCache
from core.exceptions import ResourceDoesNotExistException
from core.helpers import OrgPermissionHelper
from core.models import OrganizationConfigRole
from crm.board.domain.constants import (
    BASIC_DETAIL_SECTION_UUID,
    LEAD_CREATION_DATE_UUID,
    LEAD_NUMBER_UUID,
    LEAD_STAGE_NAME_UUID,
    LEAD_SYSTEM_FIELD_UUIDS,
)
from crm.data.models import (
    Board,
    BoardLeadBooleanFieldData,
    BoardLeadDateFieldData,
    BoardLeadDateTimeFieldData,
    BoardLeadDropDownFieldData,
    BoardLeadEmailFieldData,
    BoardLeadField,
    BoardLeadFileFieldData,
    BoardLeadNumberFieldData,
    BoardLeadPhoneNumberFieldData,
    BoardLeadRichTextFieldData,
    BoardLeadSection,
    BoardLeadTextFieldData,
    BoardUser,
    Company,
    CompanyContact,
    Contact,
    Lead,
    LeadAssignedEventData,
    LeadAssignee,
    LeadCommentCreatedEventData,
    LeadContact,
    LeadCreatedEventData,
    LeadEvent,
    LeadProjectData,
    LeadQuotationSubmittedEventData,
    LeadReminder,
    LeadReminderCreatedEventData,
    LeadStageChangedEventData,
    LeadTaskCreatedEventData,
    Quotation,
)
from crm.interface.helpers import BoardPermissionHelper
from crm.interface.type import BoardPermissionEnum, BoardPermissionType
from crm.lead.data.entities import LeadFieldForImportFieldRepoEntity, LeadFieldForImportSectionRepoEntity
from crm.lead.data.selectors import (
    get_lead_can_view_queryset,
    get_lead_events,
    get_lead_fields_with_data,
    get_sections_with_fields,
)
from crm.lead.domain.abstract_repos import (
    CompanyAbstractRepo,
    ContactAbstractRepo,
    LeadAbstractRepo,
    LeadEventAbstractRepo,
    LeadExcelAbstractRepo,
    LeadReminderAbstractRepo,
    LeadToProjectAbstractRepo,
)
from crm.lead.domain.entities import (
    AssigneeEntity,
    BooleanFieldRepoEntity,
    BooleanFieldUpdateRepoEntity,
    CityDataRepoEntity,
    CompanyRepoEntity,
    ContactRepoEntity,
    CountryDataRepoEntity,
    DataCounterRepoEntity,
    DateFieldRepoEntity,
    DateFieldUpdateRepoEntity,
    DateTimeFieldRepoEntity,
    DateTimeFieldUpdateRepoEntity,
    DropdownFieldRepoEntity,
    DropdownFieldUpdateRepoEntity,
    EmailFieldRepoEntity,
    EmailFieldUpdateRepoEntity,
    FileFieldRepoEntity,
    FileFieldUpdateRepoEntity,
    IDNameEntity,
    LeadAssignedEventEntity,
    LeadAssigneeDetailRepoEntity,
    LeadAssigneeUpdateRepoEntity,
    LeadCommentCreatedEventEntity,
    LeadCompanyDetailRepoEntity,
    LeadCompanyEntity,
    LeadContactDetailRepoEntity,
    LeadContactEntity,
    LeadContactUpdateRepoEntity,
    LeadCreatedEventEntity,
    LeadCreateRepoEntity,
    LeadCustomDataRepoEntity,
    LeadCustomFieldValueRepoEntity,
    LeadEventCreateRepoEntity,
    LeadExcelEntity,
    LeadFieldDetailRepoEntity,
    LeadFieldTypeUUIDRepoEntity,
    LeadPosRepoEntity,
    LeadProjectDataEntity,
    LeadQuotationSubmittedEventEntity,
    LeadReminderCreatedEventEntity,
    LeadReminderCreateEntity,
    LeadSectionWithFieldDataRepoEntity,
    LeadStageChangedEventEntity,
    LeadStageRepoEntity,
    LeadSystemDataRepoEntity,
    LeadSystemUpdateDataRepoEntity,
    LeadTaskCreatedEventEntity,
    LeadToProjectCityRepoEntity,
    LeadToProjectCompanyContactRepoEntity,
    LeadToProjectCompanyRepoEntity,
    LeadToProjectCountryRepoEntity,
    LeadToProjectFieldRepoEntity,
    LeadToProjectRoleData,
    LeadToProjectSectionRepoEntity,
    LeadToProjectStateRepoEntity,
    LeadToProjectSystemDataRepoEntity,
    LeadUpdateRepoEntity,
    MultiDropdownFieldRepoEntity,
    MultiDropdownFieldUpdateRepoEntity,
    NumberFieldRepoEntity,
    NumberFieldUpdateRepoEntity,
    PhoneFieldRepoEntity,
    PhoneFieldUpdateRepoEntity,
    PhoneNumberRepoEntity,
    RichTextFieldRepoEntity,
    RichTextFieldUpdateRepoEntity,
    StageEntity,
    StateDataRepoEntity,
    TargetLeadRepoEntity,
    TextFieldRepoEntity,
    TextFieldUpdateRepoEntity,
)
from crm.lead.domain.services.lead import remove_space
from crm.lead.domain.services.triggers import trigger_lead_assignment_event
from project.data.models import ProjectCustomField, ProjectCustomSection
from project.domain.constants import PROJECT_CUSTOM_FIELD_CONFIG
from project.selectors import project_custom_field_unarchived_config_fetch

logger = structlog.get_logger(__name__)


class LeadRepo(LeadAbstractRepo):
    def get_fields_type(self, uuids: List[str]) -> List[LeadFieldTypeUUIDRepoEntity]:
        fields = BoardLeadField.objects.filter(uuid__in=uuids).all()
        return [
            LeadFieldTypeUUIDRepoEntity(
                field_id=field.pk, uuid=field.uuid, type=field.type, is_required=field.is_required
            )
            for field in fields
        ]

    def get_next_position(self, board_id: int, stage_id: int) -> int:
        return Lead.objects.filter(board_id=board_id, stage_id=stage_id).count() + 1

    def get_next_serial_number(self, org_id: int) -> int:
        current_latest_serial_number = (
            Lead.objects.select_related("board")
            .filter(board__organization_id=org_id)
            .order_by("-serial_number")
            .first()
        )

        if current_latest_serial_number and current_latest_serial_number.serial_number:
            return current_latest_serial_number.serial_number + 1
        return 1

    def _create_phone_field_data(self, lead_id: int, user_id: int, data: List[PhoneFieldRepoEntity]):
        phone_field_data_objs = []
        for phone_field_data in data:
            phone_field_data_objs.append(
                BoardLeadPhoneNumberFieldData(
                    lead_id=lead_id,
                    field_id=phone_field_data.field_id,
                    data=phone_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadPhoneNumberFieldData.objects.bulk_create(phone_field_data_objs)
        except BoardLeadPhoneNumberFieldData.BoardLeadPhoneFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_drop_down_field_data(self, lead_id: int, user_id: int, data: List[DropdownFieldRepoEntity]):
        dropdown_field_data_objs = []
        for dropdown_field_data in data:
            dropdown_field_data_objs.append(
                BoardLeadDropDownFieldData(
                    lead_id=lead_id,
                    field_id=dropdown_field_data.field_id,
                    data_id=dropdown_field_data.value,
                    created_by_id=user_id,
                )
            )
        BoardLeadDropDownFieldData.objects.bulk_create(dropdown_field_data_objs)

    def _create_number_field_data(self, lead_id: int, user_id: int, data: List[NumberFieldRepoEntity]):
        number_field_data_objs = []
        for number_field_data in data:
            number_field_data_objs.append(
                BoardLeadNumberFieldData(
                    lead_id=lead_id,
                    field_id=number_field_data.field_id,
                    data=number_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadNumberFieldData.objects.bulk_create(number_field_data_objs)
        except BoardLeadNumberFieldData.BoardLeadNumberFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_text_field_data(self, lead_id: int, user_id: int, data: List[TextFieldRepoEntity]):
        text_field_data_objs = []
        for text_field_data in data:
            text_field_data_objs.append(
                BoardLeadTextFieldData(
                    lead_id=lead_id,
                    field_id=text_field_data.field_id,
                    data=text_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadTextFieldData.objects.bulk_create(text_field_data_objs)
        except BoardLeadTextFieldData.BoardLeadTextFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_file_field_data(self, lead_id: int, user_id: int, data: List[FileFieldRepoEntity]):
        file_field_data_objs = []
        for file_field_data in data:
            file_field_data_objs.append(
                BoardLeadFileFieldData(
                    lead_id=lead_id,
                    field_id=file_field_data.field_id,
                    name=file_field_data.value.get("name") if file_field_data.value else None,
                    data=file_field_data.value.get("url") if file_field_data.value else None,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadFileFieldData.objects.bulk_create(file_field_data_objs)
        except BoardLeadFileFieldData.BoardLeadFileFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_rich_text_field_data(self, lead_id: int, user_id: int, data: List[RichTextFieldRepoEntity]):
        rich_text_field_data_objs = []
        for rich_text_field_data in data:
            rich_text_field_data_objs.append(
                BoardLeadRichTextFieldData(
                    lead_id=lead_id,
                    field_id=rich_text_field_data.field_id,
                    data=rich_text_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadRichTextFieldData.objects.bulk_create(rich_text_field_data_objs)
        except BoardLeadRichTextFieldData.BoardLeadRichTextFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_email_field_data(self, lead_id: int, user_id: int, data: List[EmailFieldRepoEntity]):
        email_field_data_objs = []
        for email_field_data in data:
            email_field_data_objs.append(
                BoardLeadEmailFieldData(
                    lead_id=lead_id,
                    field_id=email_field_data.field_id,
                    data=email_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadEmailFieldData.objects.bulk_create(email_field_data_objs)
        except BoardLeadEmailFieldData.BoardLeadEmailFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_boolean_field_data(self, lead_id: int, user_id: int, data: List[BooleanFieldRepoEntity]):
        boolean_field_data_objs = []
        for boolean_field_data in data:
            boolean_field_data_objs.append(
                BoardLeadBooleanFieldData(
                    lead_id=lead_id,
                    field_id=boolean_field_data.field_id,
                    data=boolean_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadBooleanFieldData.objects.bulk_create(boolean_field_data_objs)
        except BoardLeadBooleanFieldData.BoardLeadBooleanFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_multi_dropdown_field_data(self, lead_id: int, user_id: int, data: List[MultiDropdownFieldRepoEntity]):
        multi_dropdown_field_data_objs = []
        for multi_dropdown_field_data in data:
            for data in multi_dropdown_field_data.value:
                multi_dropdown_field_data_objs.append(
                    BoardLeadDropDownFieldData(
                        lead_id=lead_id,
                        field_id=multi_dropdown_field_data.field_id,
                        data_id=data,
                        created_by_id=user_id,
                    )
                )
        BoardLeadDropDownFieldData.objects.bulk_create(multi_dropdown_field_data_objs)

    def _create_date_field_data(self, lead_id: int, user_id: int, data: List[DateFieldRepoEntity]):
        date_field_data_objs = []
        for date_field_data in data:
            date_field_data_objs.append(
                BoardLeadDateFieldData(
                    lead_id=lead_id,
                    field_id=date_field_data.field_id,
                    data=date_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadDateFieldData.objects.bulk_create(date_field_data_objs)
        except BoardLeadDateFieldData.BoardLeadDateFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_datetime_field_data(self, lead_id: int, user_id: int, data: List[DateTimeFieldRepoEntity]):
        datetime_field_data_objs = []
        for datetime_field_data in data:
            datetime_field_data_objs.append(
                BoardLeadDateTimeFieldData(
                    lead_id=lead_id,
                    field_id=datetime_field_data.field_id,
                    data=datetime_field_data.value,
                    created_by_id=user_id,
                )
            )
        try:
            BoardLeadDateTimeFieldData.objects.bulk_create(datetime_field_data_objs)
        except BoardLeadDateTimeFieldData.BoardLeadDateTimeFieldDataUniqueContrainException as e:
            raise self.LeadCreationRepoException(e)

    def _create_assignee_data(self, lead_id: int, user_id: int, data: List[int]):
        lead_assignee_objs = []
        for assignee_id in data:
            lead_assignee_objs.append(LeadAssignee(lead_id=lead_id, user_id=assignee_id, created_by_id=user_id))
        try:
            LeadAssignee.objects.bulk_create(lead_assignee_objs)
            if user_id in data:
                data.remove(user_id)
            if data:
                trigger_lead_assignment_event(lead_id=lead_id, assignee_ids=data, assigned_by_id=user_id)
        except IntegrityError as e:
            if "unique_lead_assignee" in str(e):
                raise self.LeadServiceRepoException("Assignee already exists")
            raise e

    def _create_contact_data(self, lead_id: int, user_id: int, data: List[int]):
        lead_contact_objs = []
        for contact_id in data:
            lead_contact_objs.append(LeadContact(lead_id=lead_id, contact_id=contact_id, created_by_id=user_id))
        try:
            LeadContact.objects.bulk_create(lead_contact_objs)
        except IntegrityError as e:
            if "lead_contact_unique" in str(e):
                raise self.LeadServiceRepoException("Contact already exists")
            raise e

    def _update_field_data_counter(self, data: List[DataCounterRepoEntity]):
        fields_ids_to_increment = []
        fields_ids_to_decrement = []

        for counter in data:
            if counter.is_increment:
                fields_ids_to_increment.append(counter.field_id)
            else:
                fields_ids_to_decrement.append(counter.field_id)

        if fields_ids_to_increment:
            BoardLeadField.objects.filter(id__in=fields_ids_to_increment).update(data_counter=F("data_counter") + 1)
        if fields_ids_to_decrement:
            BoardLeadField.objects.filter(id__in=fields_ids_to_decrement).update(data_counter=F("data_counter") - 1)

    def create(self, board_id: int, data: LeadCreateRepoEntity, user_id: int, org_id: int) -> int:
        lead_obj = Lead()
        lead_obj.stage_id = data.stage_id
        lead_obj.board_id = board_id
        lead_obj.name = data.name
        lead_obj.position = data.position
        lead_obj.company_id = data.company_id
        lead_obj.amount = data.amount
        lead_obj.source = data.source
        lead_obj.created_by_id = user_id
        lead_obj.sorted_at = timezone.now()
        lead_obj.organization_id = org_id
        lead_obj.serial_number = data.serial_number

        lead_obj.clean()

        try:
            lead_obj.save()
        except Lead.LeadSerialNumberUniqueConstraintException as e:
            raise self.LeadCreationRepoConstraintException(e)

        if data.assignee_ids:
            self._create_assignee_data(lead_id=lead_obj.pk, user_id=user_id, data=data.assignee_ids)
        if data.contact_ids:
            self._create_contact_data(lead_id=lead_obj.pk, user_id=user_id, data=data.contact_ids)
        LeadProjectData.objects.create(
            lead_id=lead_obj.pk,
            country_id=data.country_id,
            state_id=data.state_id,
            city_id=data.city_id,
            address_line_1=data.address_line_1,
            address_line_2=data.address_line_2,
            zip_code=data.zip_code,
            created_by_id=user_id,
        )
        if data.text_fields_data:
            self._create_text_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.text_fields_data)
        if data.phone_fields_data:
            self._create_phone_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.phone_fields_data)
        if data.dropdown_fields_data:
            self._create_drop_down_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.dropdown_fields_data)
        if data.number_fields_data:
            self._create_number_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.number_fields_data)
        if data.file_fields_data:
            self._create_file_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.file_fields_data)
        if data.rich_text_fields_data:
            self._create_rich_text_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.rich_text_fields_data)
        if data.email_fields_data:
            self._create_email_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.email_fields_data)
        if data.boolean_fields_data:
            self._create_boolean_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.boolean_fields_data)
        if data.multi_dropdown_fields_data:
            self._create_multi_dropdown_field_data(
                lead_id=lead_obj.pk, user_id=user_id, data=data.multi_dropdown_fields_data
            )
        if data.date_fields_data:
            self._create_date_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.date_fields_data)
        if data.datetime_fields_data:
            self._create_datetime_field_data(lead_id=lead_obj.pk, user_id=user_id, data=data.datetime_fields_data)
        if data.data_counters:
            self._update_field_data_counter(data=data.data_counters)
        self._update_lead_search_vector(lead_id=lead_obj.pk)
        return lead_obj.pk

    def get_lead_sections_config(self, board_id: int) -> List[LeadSectionWithFieldDataRepoEntity]:
        sections: List[BoardLeadSection] = get_sections_with_fields(board_id=board_id)
        section_objs = []
        for section in sections:
            fields: List[BoardLeadField] = section.fields.all()
            fields_objs = []

            if str(section.uuid) == BASIC_DETAIL_SECTION_UUID:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        name="Lead ID",
                        uuid=LEAD_NUMBER_UUID,
                        position=0,
                        is_required=False,
                        type=CustomFieldTypeEnum.LEAD_NUMBER.value,
                        created_at=timezone.now(),
                        archived_at=None,
                        actions=[],
                    )
                )

            for field in fields:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        uuid=field.uuid,
                        name=field.name,
                        position=field.position,
                        type=field.type,
                        is_required=field.is_required,
                        created_at=field.created_at,
                        archived_at=field.archived_at,
                    )
                )
            section_objs.append(
                LeadSectionWithFieldDataRepoEntity(
                    uuid=section.uuid,
                    name=section.name,
                    position=section.position,
                    fields=fields_objs,
                    created_at=section.created_at,
                    archived_at=section.archived_at,
                )
            )
        return section_objs

    def get_lead_latched_project(self, lead_id: int) -> int | None:
        try:
            return Lead.objects.get(id=lead_id).project_id
        except Lead.DoesNotExist:
            return None

    def get_lead_system_data(
        self, lead_id: int, user_id: int, board_permissions: BoardPermissionType
    ) -> LeadSystemDataRepoEntity:
        can_create_lead = BoardPermissionHelper.is_action_permitted(
            permission=BoardPermissionEnum.CAN_CREATE_LEADS, permissions=board_permissions
        )

        lead = (
            get_lead_can_view_queryset(user_id=user_id, board_permissions=board_permissions)
            .filter(id=lead_id)
            .annotate_edit_and_view_permissions(user_id=user_id, board_permissions=board_permissions)
            .select_related(
                "company",
                "project",
                "lead_project_data__state",
                "lead_project_data__city",
                "board",
                "lead_project_data",
                "lead_project_data__country",
                "lead_project_data__city",
            )
            .prefetch_related(
                Prefetch("contacts", LeadContact.objects.filter(lead_id=lead_id).select_related("contact").available()),
                Prefetch("assignees", LeadAssignee.objects.filter(lead_id=lead_id).select_related("user").available()),
            )
            .available()
            .first()
        )
        if not lead:
            raise self.LeadDoesNotExistException("Lead does not exist")

        can_add_field = self.check_can_add_field(board_id=lead.board_id, lead_id=lead.pk)

        return LeadSystemDataRepoEntity(
            id=lead.pk,
            name=lead.name,
            project_id=lead.project_id if (lead.project and lead.project.deleted_at is None) else None,
            serial_number=lead.serial_number,
            stage=LeadStageRepoEntity(id=lead.stage_id, name=lead.stage.name),
            company=(
                LeadCompanyDetailRepoEntity(id=lead.company_id, name=lead.company.name) if lead.company_id else None
            ),
            amount=lead.amount,
            source=lead.source,
            country=(
                CountryDataRepoEntity(id=lead.lead_project_data.country.id, name=lead.lead_project_data.country.name)
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.country
                else None
            ),
            can_create_lead=can_create_lead,
            can_edit_lead=lead.can_edit_lead,
            can_add_field=can_add_field,
            can_delete_lead=lead.can_delete_lead,
            is_project_type=lead.board.is_project_conversion_allowed,
            state=(
                StateDataRepoEntity(id=lead.lead_project_data.state.id, name=lead.lead_project_data.state.name)
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.state
                else None
            ),
            city=(
                CityDataRepoEntity(id=lead.lead_project_data.city.id, name=lead.lead_project_data.city.name)
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.city
                else None
            ),
            address_line_1=(
                lead.lead_project_data.address_line_1
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_1
                else None
            ),
            address_line_2=(
                lead.lead_project_data.address_line_2
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_2
                else None
            ),
            zip_code=(
                lead.lead_project_data.zip_code
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.zip_code
                else None
            ),
            contacts=[
                LeadContactDetailRepoEntity(
                    id=contact.contact.id,
                    name=contact.contact.name,
                    phone=(
                        PhoneNumberRepoEntity(
                            country_code=f"+{contact.contact.phone.country_code}",
                            number=contact.contact.phone.national_number,
                        )
                        if contact.contact.phone
                        else None
                    ),
                    email=contact.contact.email,
                )
                for contact in lead.contacts.all()
            ],
            assignees=[
                LeadAssigneeDetailRepoEntity(
                    id=assignee.user_id,
                    name=assignee.user.name,
                    phone=(
                        PhoneNumberRepoEntity(
                            country_code=f"+{assignee.user.phone_number.country_code}",
                            number=assignee.user.phone_number.national_number,
                        )
                        if assignee.user.phone_number
                        else None
                    ),
                    email=assignee.user.email,
                    photo=assignee.user.photo.url if assignee.user.photo else None,
                )
                for assignee in lead.assignees.all()
            ],
            created_at=lead.created_at,
        )

    def get_lead_system_update_data(self, lead_id: int) -> LeadSystemUpdateDataRepoEntity:
        lead = (
            Lead.objects.filter(id=lead_id)
            .select_related("company", "lead_project_data__state", "lead_project_data__city")
            .prefetch_related(
                Prefetch("contacts", LeadContact.objects.filter(lead_id=lead_id).select_related("contact").available()),
                Prefetch("assignees", LeadAssignee.objects.filter(lead_id=lead_id).select_related("user").available()),
            )
            .available()
            .first()
        )
        if not lead:
            raise self.LeadDoesNotExistException("Lead does not exist")
        return LeadSystemUpdateDataRepoEntity(
            id=lead.pk,
            name=lead.name,
            stage_id=lead.stage_id,
            company_id=lead.company_id,
            amount=lead.amount,
            source=lead.source,
            country_id=(
                lead.lead_project_data.country_id
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.country
                else None
            ),
            state_id=(
                lead.lead_project_data.state.id
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.state
                else None
            ),
            city_id=(
                lead.lead_project_data.city.id
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.city
                else None
            ),
            contact_ids=[contact.contact_id for contact in lead.contacts.all()],
            assignee_ids=[assignee.user_id for assignee in lead.assignees.all()],
            created_at=lead.created_at,
            address_line_1=(
                lead.lead_project_data.address_line_1
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_1
                else None
            ),
            address_line_2=(
                lead.lead_project_data.address_line_2
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_2
                else None
            ),
            zip_code=(
                lead.lead_project_data.zip_code
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.zip_code
                else None
            ),
        )

    def get_lead_assignee_data(self, lead_id: int) -> set[int]:
        assignees = LeadAssignee.objects.filter(lead_id=lead_id).available().all()
        return {assignee.user_id for assignee in assignees}

    def _get_custom_text_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_text_data: BoardLeadTextFieldData = (
            field.custom_text_data.all()[0] if field.custom_text_data.all() else None
        )
        if custom_text_data:
            return LeadCustomFieldValueRepoEntity(id=custom_text_data.pk, value=custom_text_data.data)

    def _get_custom_drop_down_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_drop_down_data: BoardLeadDropDownFieldData = (
            field.custom_drop_down_data.all()[0] if field.custom_drop_down_data.available().all() else None
        )
        if custom_drop_down_data and custom_drop_down_data.data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_drop_down_data.pk,
                value={"id": custom_drop_down_data.data.pk, "name": custom_drop_down_data.data.name},
            )

    def _get_custom_drop_down_update_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_drop_down_data: BoardLeadDropDownFieldData = (
            field.custom_drop_down_data.all()[0] if field.custom_drop_down_data.available().all() else None
        )
        if custom_drop_down_data:
            return LeadCustomFieldValueRepoEntity(id=custom_drop_down_data.pk, value=custom_drop_down_data.data_id)

    def _get_custom_multi_drop_down_data(self, field: QuerySet[BoardLeadField]) -> List[LeadCustomFieldValueRepoEntity]:
        custom_multi_drop_down_data: List[BoardLeadDropDownFieldData] = field.custom_drop_down_data.available().all()
        if custom_multi_drop_down_data:
            return [
                LeadCustomFieldValueRepoEntity(id=data.pk, value={"id": data.data.pk, "name": data.data.name})
                for data in custom_multi_drop_down_data
                if data.data is not None
            ]

    def _get_custom_multi_drop_down_update_data(
        self, field: QuerySet[BoardLeadField]
    ) -> List[LeadCustomFieldValueRepoEntity]:
        custom_multi_drop_down_data: List[BoardLeadDropDownFieldData] = field.custom_drop_down_data.available().all()
        if custom_multi_drop_down_data:
            return [
                LeadCustomFieldValueRepoEntity(id=data.pk, value=data.data_id) for data in custom_multi_drop_down_data
            ]
        return []

    def _get_custom_email_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_email_data: BoardLeadEmailFieldData = (
            field.custom_email_data.all()[0] if field.custom_email_data.all() else None
        )
        if custom_email_data:
            return LeadCustomFieldValueRepoEntity(id=custom_email_data.pk, value=custom_email_data.data)

    def _get_custom_number_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_number_data = field.custom_number_data.all()[0] if field.custom_number_data.all() else None
        if custom_number_data:
            return LeadCustomFieldValueRepoEntity(id=custom_number_data.pk, value=custom_number_data.data)

    def _get_custom_date_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_date_data: BoardLeadDateFieldData = (
            field.custom_date_data.all()[0] if field.custom_date_data.all() else None
        )
        if custom_date_data:
            return LeadCustomFieldValueRepoEntity(id=custom_date_data.pk, value=custom_date_data.data)

    def _get_custom_datetime_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_datetime_data: BoardLeadDateTimeFieldData = (
            field.custom_datetime_data.all()[0] if field.custom_datetime_data.all() else None
        )
        if custom_datetime_data:
            return LeadCustomFieldValueRepoEntity(id=custom_datetime_data.pk, value=custom_datetime_data.data)

    def _get_custom_boolean_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_boolean_data: BoardLeadBooleanFieldData = (
            field.custom_boolean_data.all()[0] if field.custom_boolean_data.all() else None
        )
        if custom_boolean_data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_boolean_data.pk, value=custom_boolean_data.data if custom_boolean_data.data else False
            )

    def _get_custom_rich_text_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_rich_text_data: BoardLeadRichTextFieldData = (
            field.custom_rich_text_data.all()[0] if field.custom_rich_text_data.all() else None
        )
        if custom_rich_text_data:
            return LeadCustomFieldValueRepoEntity(id=custom_rich_text_data.pk, value=custom_rich_text_data.data)

    def _get_custom_phone_number_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_phone_number_data: BoardLeadPhoneNumberFieldData = (
            field.custom_phone_number_data.all()[0] if field.custom_phone_number_data.all() else None
        )
        if custom_phone_number_data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_phone_number_data.pk,
                value=(
                    PhoneNumberSerializer(
                        {
                            "country_code": custom_phone_number_data.data.country_code,
                            "national_number": custom_phone_number_data.data.national_number,
                        }
                    ).data
                    if custom_phone_number_data.data
                    else None
                ),
            )

    def _get_custom_file_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_file_data: BoardLeadFileFieldData = (
            field.custom_file_data.all()[0] if field.custom_file_data.all() else None
        )
        if custom_file_data and custom_file_data.data.name:
            return LeadCustomFieldValueRepoEntity(
                id=custom_file_data.pk, value={"name": custom_file_data.name, "url": custom_file_data.data.url}
            )

    def _get_custom_file_update_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        custom_file_data: BoardLeadFileFieldData = (
            field.custom_file_data.all()[0] if field.custom_file_data.all() else None
        )
        if custom_file_data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_file_data.pk,
                value=(
                    {"name": custom_file_data.name, "url": custom_file_data.data.url}
                    if custom_file_data.data and custom_file_data.data.name
                    else None
                ),
            )

    def _get_custom_field_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        if field.type == CustomFieldTypeEnum.TEXT.value:
            return self._get_custom_text_data(field)
        if field.type == CustomFieldTypeEnum.DROPDOWN.value:
            return self._get_custom_drop_down_data(field)
        if field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
            return self._get_custom_multi_drop_down_data(field)
        if field.type == CustomFieldTypeEnum.EMAIL.value:
            return self._get_custom_email_data(field)
        if field.type == CustomFieldTypeEnum.DECIMAL.value:
            return self._get_custom_number_data(field)
        if field.type == CustomFieldTypeEnum.DATE.value:
            return self._get_custom_date_data(field)
        if field.type == CustomFieldTypeEnum.DATETIME.value:
            return self._get_custom_datetime_data(field)
        if field.type == CustomFieldTypeEnum.BOOLEAN.value:
            return self._get_custom_boolean_data(field)
        if field.type == CustomFieldTypeEnum.RICH_TEXT.value:
            return self._get_custom_rich_text_data(field)
        if field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
            return self._get_custom_phone_number_data(field)
        if field.type == CustomFieldTypeEnum.FILE.value:
            return self._get_custom_file_data(field)

    def _get_custom_field_update_data(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        if field.type == CustomFieldTypeEnum.TEXT.value:
            return self._get_custom_text_data(field)
        if field.type == CustomFieldTypeEnum.DROPDOWN.value:
            return self._get_custom_drop_down_update_data(field)
        if field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
            return self._get_custom_multi_drop_down_update_data(field)
        if field.type == CustomFieldTypeEnum.EMAIL.value:
            return self._get_custom_email_data(field)
        if field.type == CustomFieldTypeEnum.DECIMAL.value:
            return self._get_custom_number_data(field)
        if field.type == CustomFieldTypeEnum.DATE.value:
            return self._get_custom_date_data(field)
        if field.type == CustomFieldTypeEnum.BOOLEAN.value:
            return self._get_custom_boolean_data(field)
        if field.type == CustomFieldTypeEnum.RICH_TEXT.value:
            return self._get_custom_rich_text_data(field)
        if field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
            return self._get_custom_phone_number_data(field)
        if field.type == CustomFieldTypeEnum.FILE.value:
            return self._get_custom_file_update_data(field)
        if field.type == CustomFieldTypeEnum.DATETIME.value:
            return self._get_custom_datetime_data(field)

    def get_lead_custom_data(self, board_id: int, lead_id: int) -> List[LeadCustomDataRepoEntity]:
        fields = get_lead_fields_with_data(lead_id=lead_id, board_id=board_id)
        fields_obj = []
        for field in fields:
            data = self._get_custom_field_data(field)
            fields_obj.append(
                LeadCustomDataRepoEntity(
                    field_uuid=field.uuid,
                    field_id=field.pk,
                    name=field.name,
                    type=field.type,
                    position=field.position,
                    is_required=field.is_required,
                    value=data,
                    created_at=field.created_at,
                    data_counter=field.data_counter,
                )
            )
        return fields_obj

    def get_lead_custom_update_data(self, board_id: int, lead_id: int) -> List[LeadCustomDataRepoEntity]:
        fields = get_lead_fields_with_data(lead_id=lead_id, board_id=board_id)
        fields_obj = []
        for field in fields:
            data = self._get_custom_field_update_data(field)
            fields_obj.append(
                LeadCustomDataRepoEntity(
                    field_uuid=field.uuid,
                    field_id=field.pk,
                    name=field.name,
                    type=field.type,
                    position=field.position,
                    is_required=field.is_required,
                    value=data,
                    created_at=field.created_at,
                    data_counter=field.data_counter,
                )
            )
        return fields_obj

    def update_stage(self, lead_id: int, stage_id: int, user_id: int, lead_position: int):
        Lead.objects.filter(id=lead_id).update(
            stage_id=stage_id, updated_by_id=user_id, updated_at=timezone.now(), position=lead_position
        )

    def _update_lead_assignee_data(self, lead_id: int, assignees: List[LeadAssigneeUpdateRepoEntity], user_id: int):
        to_create = []
        to_delete = []
        for assignee in assignees:
            if assignee.object_status == ObjectStatus.ADD.value:
                to_create.append(assignee.id)
            elif assignee.object_status == ObjectStatus.DELETE.value:
                to_delete.append(assignee.id)
        if to_create:
            self._create_assignee_data(lead_id=lead_id, user_id=user_id, data=to_create)
        if to_delete:
            LeadAssignee.objects.filter(lead_id=lead_id, user_id__in=to_delete).update(
                deleted_at=timezone.now(), deleted_by_id=user_id
            )

    def update_lead_assignee_data(self, lead_id: int, assignees: List[LeadAssigneeUpdateRepoEntity], user_id: int):
        self._update_lead_assignee_data(lead_id=lead_id, assignees=assignees, user_id=user_id)

    def _update_lead_contact_data(self, lead_id: int, contacts: List[LeadContactUpdateRepoEntity], user_id: int):
        to_create = []
        to_delete = []
        for contact in contacts:
            if contact.object_status == ObjectStatus.ADD.value:
                to_create.append(contact.id)
            elif contact.object_status == ObjectStatus.DELETE.value:
                to_delete.append(contact.id)

        if to_create:
            self._create_contact_data(lead_id=lead_id, user_id=user_id, data=to_create)
        if to_delete:
            LeadContact.objects.filter(lead_id=lead_id, contact_id__in=to_delete).update(
                deleted_at=timezone.now(), deleted_by_id=user_id
            )

    def _update_text_field_data(self, lead_id: int, user_id: int, data: List[TextFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for text_field_data in data:
            if text_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadTextFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=text_field_data.field_id,
                        data=text_field_data.value.value if text_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadTextFieldData(
                        id=text_field_data.value.id,
                        lead_id=lead_id,
                        field_id=text_field_data.field_id,
                        data=text_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadTextFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadTextFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_phone_field_data(self, lead_id: int, user_id: int, data: List[PhoneFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for phone_field_data in data:
            if phone_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadPhoneNumberFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=phone_field_data.field_id,
                        data=phone_field_data.value.value if phone_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadPhoneNumberFieldData(
                        id=phone_field_data.value.id,
                        lead_id=lead_id,
                        field_id=phone_field_data.field_id,
                        data=phone_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadPhoneNumberFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadPhoneNumberFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_drop_down_field_data(self, lead_id: int, user_id: int, data: List[DropdownFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for drop_down_field_data in data:
            if drop_down_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadDropDownFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=drop_down_field_data.field_id,
                        data_id=drop_down_field_data.value.value if drop_down_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadDropDownFieldData(
                        id=drop_down_field_data.value.id,
                        lead_id=lead_id,
                        field_id=drop_down_field_data.field_id,
                        data_id=drop_down_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadDropDownFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadDropDownFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_number_field_data(self, lead_id: int, user_id: int, data: List[NumberFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for number_field_data in data:
            if number_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadNumberFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=number_field_data.field_id,
                        data=number_field_data.value.value if number_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadNumberFieldData(
                        id=number_field_data.value.id,
                        lead_id=lead_id,
                        field_id=number_field_data.field_id,
                        data=number_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadNumberFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadNumberFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_file_field_data(self, lead_id: int, user_id: int, data: List[FileFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for file_field_data in data:
            if file_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadFileFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=file_field_data.field_id,
                        name=(
                            file_field_data.value.value.get("name")
                            if file_field_data.value and file_field_data.value.value
                            else None
                        ),
                        data=(
                            file_field_data.value.value.get("url")
                            if file_field_data.value and file_field_data.value.value
                            else None
                        ),
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadFileFieldData(
                        id=file_field_data.value.id,
                        lead_id=lead_id,
                        name=file_field_data.value.value.get("name") if file_field_data.value.value else None,
                        field_id=file_field_data.field_id,
                        data=file_field_data.value.value.get("url") if file_field_data.value.value else None,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadFileFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadFileFieldData.objects.bulk_update(
                to_update, fields=["data", "name", "updated_at", "updated_by_id"]
            )

    def _update_rich_text_field_data(self, lead_id: int, user_id: int, data: List[RichTextFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for rich_text_field_data in data:
            if rich_text_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadRichTextFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=rich_text_field_data.field_id,
                        data=rich_text_field_data.value.value if rich_text_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadRichTextFieldData(
                        id=rich_text_field_data.value.id,
                        lead_id=lead_id,
                        field_id=rich_text_field_data.field_id,
                        data=rich_text_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadRichTextFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadRichTextFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_email_field_data(self, lead_id: int, user_id: int, data: List[EmailFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for email_field_data in data:
            if email_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadEmailFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=email_field_data.field_id,
                        data=email_field_data.value.value if email_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadEmailFieldData(
                        id=email_field_data.value.id,
                        lead_id=lead_id,
                        field_id=email_field_data.field_id,
                        data=email_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadEmailFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadEmailFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_boolean_field_data(self, lead_id: int, user_id: int, data: List[BooleanFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for boolean_field_data in data:
            if boolean_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadBooleanFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=boolean_field_data.field_id,
                        data=boolean_field_data.value.value if boolean_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadBooleanFieldData(
                        id=boolean_field_data.value.id,
                        lead_id=lead_id,
                        field_id=boolean_field_data.field_id,
                        data=boolean_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadBooleanFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadBooleanFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_date_field_data(self, lead_id: int, user_id: int, data: List[DateFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for date_field_data in data:
            if date_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadDateFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=date_field_data.field_id,
                        data=date_field_data.value.value if date_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadDateFieldData(
                        id=date_field_data.value.id,
                        lead_id=lead_id,
                        field_id=date_field_data.field_id,
                        data=date_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadDateFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadDateFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_datetime_field_data(self, lead_id: int, user_id: int, data: List[DateTimeFieldUpdateRepoEntity]):
        to_create = []
        to_update = []
        for datetime_field_data in data:
            if datetime_field_data.object_status == ObjectStatus.ADD.value:
                to_create.append(
                    BoardLeadDateTimeFieldData(
                        id=None,
                        lead_id=lead_id,
                        field_id=datetime_field_data.field_id,
                        data=datetime_field_data.value.value if datetime_field_data.value else None,
                        created_by_id=user_id,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
            else:
                to_update.append(
                    BoardLeadDateTimeFieldData(
                        id=datetime_field_data.value.id,
                        lead_id=lead_id,
                        field_id=datetime_field_data.field_id,
                        data=datetime_field_data.value.value,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
        if to_create:
            BoardLeadDateTimeFieldData.objects.bulk_create(to_create)
        if to_update:
            BoardLeadDateTimeFieldData.objects.bulk_update(to_update, fields=["data", "updated_at", "updated_by_id"])

    def _update_multi_dropdown_field_data(
        self, lead_id: int, user_id: int, data: List[MultiDropdownFieldUpdateRepoEntity]
    ):
        to_create = []
        to_delete = []
        for multi_dropdown_field_data in data:
            if multi_dropdown_field_data.object_status == ObjectStatus.ADD.value:
                for multi_dropdown_field_value in multi_dropdown_field_data.value:
                    to_create.append(
                        BoardLeadDropDownFieldData(
                            lead_id=lead_id,
                            field_id=multi_dropdown_field_data.field_id,
                            data_id=multi_dropdown_field_value.value,
                            created_by_id=user_id,
                        )
                    )
            elif multi_dropdown_field_data.object_status == ObjectStatus.DELETE.value:
                for multi_dropdown_field_value in multi_dropdown_field_data.value:
                    to_delete.append(multi_dropdown_field_value.id)

        if to_create:
            BoardLeadDropDownFieldData.objects.bulk_create(to_create)
        if to_delete:
            BoardLeadDropDownFieldData.objects.filter(lead_id=lead_id, id__in=to_delete).update(
                deleted_at=timezone.now(), deleted_by_id=user_id
            )

    def update(self, data: LeadUpdateRepoEntity, user_id: int, lead_id: int):
        Lead.objects.filter(id=lead_id).update(
            name=data.name,
            amount=data.amount,
            company_id=data.company_id,
            updated_at=timezone.now(),
            updated_by_id=user_id,
            stage_id=data.stage_id,
        )
        LeadProjectData.objects.update_or_create(
            lead_id=lead_id,
            defaults={
                "country_id": data.country_id,
                "state_id": data.state_id,
                "city_id": data.city_id,
                "address_line_1": data.address_line_1,
                "address_line_2": data.address_line_2,
                "zip_code": data.zip_code,
                "created_by_id": user_id,
            },
        )
        if data.assignees:
            self._update_lead_assignee_data(lead_id=lead_id, assignees=data.assignees, user_id=user_id)
        if data.contacts:
            self._update_lead_contact_data(lead_id=lead_id, contacts=data.contacts, user_id=user_id)
        if data.text_fields_data:
            self._update_text_field_data(lead_id=lead_id, user_id=user_id, data=data.text_fields_data)
        if data.phone_fields_data:
            self._update_phone_field_data(lead_id=lead_id, user_id=user_id, data=data.phone_fields_data)
        if data.dropdown_fields_data:
            self._update_drop_down_field_data(lead_id=lead_id, user_id=user_id, data=data.dropdown_fields_data)
        if data.number_fields_data:
            self._update_number_field_data(lead_id=lead_id, user_id=user_id, data=data.number_fields_data)
        if data.file_fields_data:
            self._update_file_field_data(lead_id=lead_id, user_id=user_id, data=data.file_fields_data)
        if data.rich_text_fields_data:
            self._update_rich_text_field_data(lead_id=lead_id, user_id=user_id, data=data.rich_text_fields_data)
        if data.email_fields_data:
            self._update_email_field_data(lead_id=lead_id, user_id=user_id, data=data.email_fields_data)
        if data.boolean_fields_data:
            self._update_boolean_field_data(lead_id=lead_id, user_id=user_id, data=data.boolean_fields_data)
        if data.multi_dropdown_fields_data:
            self._update_multi_dropdown_field_data(
                lead_id=lead_id, user_id=user_id, data=data.multi_dropdown_fields_data
            )
        if data.date_fields_data:
            self._update_date_field_data(lead_id=lead_id, user_id=user_id, data=data.date_fields_data)
        if data.datetime_fields_data:
            self._update_datetime_field_data(lead_id=lead_id, user_id=user_id, data=data.datetime_fields_data)
        if data.data_counters:
            self._update_field_data_counter(data=data.data_counters)
        self._update_lead_search_vector(lead_id=lead_id)

    def _remove_space(self, string: str) -> str:
        return remove_space(string)

    def _get_company_name_search_vector(self, company: Company) -> SearchVector:
        company_name = company.name if company else ""
        return SearchVector(models.Value(self._remove_space(company_name)), weight="B")

    def _get_assignee_name_search_vector(self, assignees: List[LeadAssignee]) -> SearchVector:
        assignee_names = " ".join([self._remove_space(assignee.user.name) for assignee in assignees])
        return SearchVector(models.Value(assignee_names), weight="D")

    def _get_contact_name_search_vector(self, contacts: List[LeadContact]) -> SearchVector:
        contact_names = " ".join(
            [self._remove_space(contact.contact.name) for contact in contacts if contact.contact.name]
        )
        return SearchVector(models.Value(contact_names), weight="C")

    def _get_lead_name_search_vector(self) -> SearchVector:
        return SearchVector("name", weight="A")

    def _update_lead_search_vector(self, lead_id: int):
        lead = (
            Lead.objects.filter(id=lead_id)
            .select_related("company")
            .prefetch_related(
                Prefetch(
                    "assignees", LeadAssignee.objects.filter(lead_id=lead_id).select_related("user").available().all()
                ),
                Prefetch(
                    "contacts", LeadContact.objects.filter(lead_id=lead_id).select_related("contact").available().all()
                ),
            )
            .first()
        )
        search_vector = (
            self._get_lead_name_search_vector()
            + self._get_company_name_search_vector(company=lead.company)
            + self._get_contact_name_search_vector(contacts=lead.contacts.all())
            + self._get_assignee_name_search_vector(assignees=lead.assignees.all())
        )
        try:
            Lead.objects.filter(id=lead_id).update(search_vector=search_vector)
        except OperationalError as e:
            if "lead_search_vector_index" in str(e):
                raise self.LeadSearchVectorIndexException("Lead search vector index already exists")

    def get_lead_position(self, lead_id: int) -> int:
        position = Lead.objects.filter(id=lead_id).available().values_list("position", flat=True).first()
        if position is None:
            raise self.LeadDoesNotExistException("Lead does not exist")
        return position

    def get_target_lead_data(self, lead_id: int):
        lead = Lead.objects.filter(id=lead_id).first()
        if lead.deleted_at:
            raise self.LeadDoesNotExistException("Lead does not exist")
        first_lead = (
            Lead.objects.filter(stage_id=lead.stage_id)
            .order_by("position", F("sorted_at").desc(nulls_last=True))
            .available()
            .first()
        )
        return TargetLeadRepoEntity(
            position=lead.position,
            stage_id=lead.stage_id,
            sorted_at=lead.sorted_at,
            is_first_lead=first_lead.pk == lead.pk,
        )

    def get_max_lead_position(self, stage_id: int) -> int:
        return Lead.objects.filter(stage_id=stage_id).available().aggregate(Max("position"))["position__max"]

    def move_lead(self, lead_id: int, target_data: TargetLeadRepoEntity, stage_id: int, user_id: int):
        sorted_at = timezone.now()
        if not target_data.is_first_lead:
            Lead.objects.filter(
                stage_id=stage_id, position__lte=target_data.position, sorted_at__gte=target_data.sorted_at
            ).exclude(id=lead_id).update(sorted_at=sorted_at + timedelta(seconds=1))
        Lead.objects.filter(id=lead_id).update(
            position=target_data.position,
            updated_at=timezone.now(),
            updated_by_id=user_id,
            stage_id=stage_id,
            sorted_at=sorted_at,
        )

    def get_lead_pos_in_range(
        self, stage_id: int, current_lead_id: int, target_lead_id: int, max_pos: int
    ) -> List[LeadPosRepoEntity]:
        if target_lead_id:
            current_lead_pos: int = Lead.objects.filter(id=current_lead_id).values_list("position", flat=True).first()
            target_lead_pos: int = Lead.objects.filter(id=target_lead_id).values_list("position", flat=True).first()
            if current_lead_pos > target_lead_pos:
                lead_ranges = Lead.objects.filter(
                    stage_id=stage_id, position__gte=target_lead_pos, position__lte=current_lead_pos
                ).all()
            else:
                lead_ranges = Lead.objects.filter(
                    stage_id=stage_id, position__gte=current_lead_pos, position__lte=target_lead_pos
                ).all()
            objs = []
            for lead in lead_ranges:
                objs.append(
                    LeadPosRepoEntity(
                        id=lead.id,
                        pos=lead.position,
                    )
                )
            return objs
        else:
            current_lead_pos: int = Lead.objects.filter(id=current_lead_id).values_list("position", flat=True).first()
            lead_ranges = Lead.objects.filter(
                stage_id=stage_id, position__gt=current_lead_pos, position__lte=max_pos
            ).all()
            objs = []
            for lead in lead_ranges:
                objs.append(
                    LeadPosRepoEntity(
                        id=lead.id,
                        pos=lead.position,
                    )
                )
            return objs

    def get_targe_lead_pos(self, lead_id: int) -> int:
        return Lead.objects.filter(id=lead_id).values_list("position", flat=True).first()

    def update_lead_pos_in_diff_stage(self, stage_id: int, target_lead_pos: int):
        Lead.objects.filter(stage_id=stage_id, position__gte=target_lead_pos).update(position=F("position") + 1)

    def update_curr_lead_pos_in_diff_stage(self, lead_id: int, target_lead_pos: int, stage_id: int):
        Lead.objects.filter(id=lead_id).update(position=target_lead_pos, stage_id=stage_id)

    def update_lead_pos_in_range(self, data: List[LeadPosRepoEntity], stage_id: int):
        objs = []
        for lead in data:
            objs.append(Lead(id=lead.id, position=lead.pos, stage_id=stage_id))
        Lead.objects.bulk_update(objs, fields=["position", "stage_id"])

    def delete(self, lead_id: int, user_id: int):
        lead = (
            Lead.objects.filter(id=lead_id)
            .prefetch_related(
                "custom_text_data",
                "custom_email_data",
                "custom_number_data",
                "custom_date_data",
                "custom_boolean_data",
                "custom_rich_text_data",
                "custom_phone_number_data",
                "custom_file_data",
                Prefetch(
                    "custom_drop_down_data", queryset=BoardLeadDropDownFieldData.objects.all().select_related("field")
                ),
            )
            .first()
        )
        text_data = lead.custom_text_data.all()
        multi_dropdown_data = lead.custom_drop_down_data.all()
        drop_down_data = lead.custom_drop_down_data.all()
        email_data = lead.custom_email_data.all()
        number_data = lead.custom_number_data.all()
        date_data = lead.custom_date_data.all()
        boolean_data = lead.custom_boolean_data.all()
        rich_text_data = lead.custom_rich_text_data.all()
        phone_number_data = lead.custom_phone_number_data.all()
        file_data = lead.custom_file_data.all()

        field_id_to_data_counter_map = collections.defaultdict(int)
        for data in text_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in drop_down_data:
            if data.field.type == CustomFieldTypeEnum.DROPDOWN.value:
                if hasattr(data, "data") and data.data is not None:
                    field_id_to_data_counter_map[data.field_id] += 1
        for data in email_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in number_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in date_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in boolean_data:
            if hasattr(data, "data") and data.data is not None:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in rich_text_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in phone_number_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        for data in file_data:
            if hasattr(data, "data") and data.data:
                field_id_to_data_counter_map[data.field_id] += 1
        if multi_dropdown_data:
            seen = set()
            for data in multi_dropdown_data:
                if data.field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                    if data.field_id not in seen and hasattr(data, "data") and data.data:
                        field_id_to_data_counter_map[data.field_id] += 1
                        seen.add(data.field_id)

        board_lead_field_objs = BoardLeadField.objects.filter(id__in=field_id_to_data_counter_map.keys()).all()
        for board_lead_field_obj in board_lead_field_objs:
            board_lead_field_obj.data_counter -= field_id_to_data_counter_map[board_lead_field_obj.id]
        BoardLeadField.objects.bulk_update(board_lead_field_objs, fields=["data_counter"])
        LeadContact.objects.filter(lead_id=lead_id).soft_delete(user_id=user_id)
        LeadAssignee.objects.filter(lead_id=lead_id).soft_delete(user_id=user_id)
        lead.soft_delete(user_id=user_id)

    def delete_board_leads(self, board_id: int, user_id: int):
        leads = Lead.objects.filter(board_id=board_id).available().all()
        updates = []
        current_time = timezone.now()
        for lead in leads:
            lead.deleted_at = current_time
            lead.deleted_by_id = user_id

            updates.append(lead)

        Lead.objects.bulk_update(updates, fields=["deleted_at", "deleted_by_id"])

    def transfer_leads_to_stage_ids(self, stage_ids: List[int], from_stage_id_to_stage_id_mapping: list):
        lead_objs = Lead.objects.filter(stage_id__in=stage_ids).available().all()
        lead_objs_to_update = []
        for lead_obj in lead_objs:
            lead_objs_to_update.append(
                Lead(
                    id=lead_obj.id,
                    stage_id=from_stage_id_to_stage_id_mapping[lead_obj.stage_id],
                )
            )
        Lead.objects.bulk_update(lead_objs_to_update, fields=["stage_id"])

    def get_leads_for_import(self, user) -> Optional[QuerySet]:
        org_user_permissions = OrgPermissionHelper.get_permissions(user=user)
        quotation_exists_subquery = Exists(Quotation.objects.available().filter(lead_id=OuterRef("id")))
        # if user has view all boards permission
        if Permissions.CAN_VIEW_ALL_BOARDS in org_user_permissions:
            return (
                Lead.objects.filter(board__organization_id=user.org_id)
                .select_related("company")
                .available()
                .annotate(has_quotation=quotation_exists_subquery)
                .filter(has_quotation=True)
            )
        board_ids = BoardUser.objects.filter(user_id=user.pk).available().values_list("board_id", flat=True)
        return (
            Lead.objects.filter(board_id__in=board_ids)
            .select_related("company")
            .available()
            .annotate(has_quotation=quotation_exists_subquery)
            .filter(has_quotation=True)
        )

    def get_quotations_for_import(self, user) -> Optional[QuerySet]:
        leads = self.get_leads_for_import(user)
        return (
            Quotation.objects.filter(lead_id__in=leads.values_list("id", flat=True))
            .select_related("lead", "created_by")
            .annotate(reference_number=Concat(Value("QUOT"), F("ref_number"), output_field=CharField()))
            .available()
            .annotate_amount()
            .all()
        )

    def fetch_lead_fields_for_import_base_selector(self, board_id: int, lead_id: int):
        excluded_field_query_filter = Q(
            Q(custom_text_data__lead_id=lead_id)
            | Q(custom_email_data__lead_id=lead_id)
            | Q(custom_number_data__lead_id=lead_id)
            | Q(custom_date_data__lead_id=lead_id)
            | Q(custom_datetime_data__lead_id=lead_id)
            | Q(custom_boolean_data__lead_id=lead_id)
            | Q(custom_rich_text_data__lead_id=lead_id)
            | Q(custom_phone_number_data__lead_id=lead_id)
            | Q(custom_file_data__lead_id=lead_id)
            | Q(custom_drop_down_data__lead_id=lead_id)
        )

        return (
            BoardLeadSection.objects.get_queryset()
            .filter(board_id=board_id)
            .prefetch_related(
                Prefetch(
                    "fields",
                    queryset=BoardLeadField.objects.available()
                    .exclude(excluded_field_query_filter)
                    .exclude(uuid__in=LEAD_SYSTEM_FIELD_UUIDS)
                    .order_by("position")
                    .all(),
                    to_attr="importable_fields",
                )
            )
            .all()
            .order_by("position")
        )

    def fetch_lead_fields_for_import(self, board_id: int, lead_id: int) -> list[LeadFieldForImportSectionRepoEntity]:
        sections = self.fetch_lead_fields_for_import_base_selector(board_id=board_id, lead_id=lead_id)
        sections = [section for section in sections if len(section.importable_fields) > 0]

        return [
            LeadFieldForImportSectionRepoEntity(
                name=section.name,
                uuid=section.uuid,
                position=section.position,
                fields=[
                    LeadFieldForImportFieldRepoEntity(
                        uuid=field.uuid,
                        name=field.name,
                        position=field.position,
                        type=field.type,
                        is_required=field.is_required,
                    )
                    for field in section.importable_fields
                ],
            )
            for section in sections
        ]

    def check_can_add_field(self, board_id: int, lead_id: int) -> bool:
        sections = self.fetch_lead_fields_for_import_base_selector(board_id=board_id, lead_id=lead_id)
        sections = [section for section in sections if len(section.importable_fields) > 0]
        return len(sections) > 0

    def get_section_and_field_config_for_lead_base_selector(self, lead_id: int | None, board_id: int):
        field_data_query_filter = Q(
            Q(custom_text_data__lead_id=lead_id)
            | Q(custom_email_data__lead_id=lead_id)
            | Q(custom_number_data__lead_id=lead_id)
            | Q(custom_date_data__lead_id=lead_id)
            | Q(custom_datetime_data__lead_id=lead_id)
            | Q(custom_boolean_data__lead_id=lead_id)
            | Q(custom_rich_text_data__lead_id=lead_id)
            | Q(custom_phone_number_data__lead_id=lead_id)
            | Q(custom_file_data__lead_id=lead_id)
            | Q(custom_drop_down_data__lead_id=lead_id)
        )

        system_field_query_filter = Q(uuid__in=LEAD_SYSTEM_FIELD_UUIDS)

        lead_field_filter = Q(archived_at__isnull=True)

        # if lead_id is not None, fetch system lead fields & custom fields having data on that lead
        if lead_id:
            lead_created_at = Lead.objects.filter(pk=lead_id).first().created_at
            lead_field_filter = Q(
                Q(Q(field_data_query_filter) | Q(created_at__lt=lead_created_at, archived_at__isnull=True))
                | Q(Q(system_field_query_filter) | Q(created_at__lt=lead_created_at, archived_at__isnull=True))
            )

        return (
            BoardLeadSection.objects.get_queryset()
            .filter(board_id=board_id)
            .prefetch_related(
                Prefetch(
                    "fields",
                    queryset=BoardLeadField.objects.available().filter(lead_field_filter).order_by("position").all(),
                )
            )
            .all()
            .order_by("position")
        )

    def get_section_and_field_config_for_lead(
        self, lead_id: int | None, board_id: int
    ) -> List[LeadSectionWithFieldDataRepoEntity]:
        sections = self.get_section_and_field_config_for_lead_base_selector(lead_id=lead_id, board_id=board_id)

        section_objs = []
        for section in sections:
            fields: List[BoardLeadField] = section.fields.all()
            fields_objs = []

            if str(section.uuid) == BASIC_DETAIL_SECTION_UUID:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        name="Lead ID",
                        uuid=LEAD_NUMBER_UUID,
                        position=0,
                        is_required=False,
                        type=CustomFieldTypeEnum.LEAD_NUMBER.value,
                        created_at=timezone.now(),
                        archived_at=None,
                        actions=[],
                    )
                )

            for field in fields:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        uuid=field.uuid,
                        name=field.name,
                        position=field.position,
                        type=field.type,
                        is_required=field.is_required,
                        created_at=field.created_at,
                        archived_at=field.archived_at,
                    )
                )
            section_objs.append(
                LeadSectionWithFieldDataRepoEntity(
                    uuid=section.uuid,
                    name=section.name,
                    position=section.position,
                    fields=fields_objs,
                    created_at=section.created_at,
                    archived_at=section.archived_at,
                )
            )
        return section_objs


class LeadToProjectRepo(LeadToProjectAbstractRepo):
    def _get_default_sections_config(self):
        sections_config = deepcopy(PROJECT_CUSTOM_FIELD_CONFIG)
        section_config_entities = []
        for section in sections_config:
            fields = section.get("fields")
            fields_obj = []
            for field in fields:
                fields_obj.append(
                    LeadToProjectFieldRepoEntity(
                        id=None,
                        uuid=field.get("uuid"),
                        name=field.get("name"),
                        position=field.get("position"),
                        type=field.get("type"),
                        is_required=field.get("is_required"),
                    )
                )
            section_config_entities.append(
                LeadToProjectSectionRepoEntity(
                    id=None,
                    name=section.get("name"),
                    uuid=section.get("uuid"),
                    position=section.get("position"),
                    type=section.get("type"),
                    fields=fields_obj,
                )
            )
        return section_config_entities

    def get_project_data(self, org_id: int) -> List[LeadToProjectSectionRepoEntity]:
        sections_config: List[ProjectCustomSection] = project_custom_field_unarchived_config_fetch(org_id=org_id)
        section_config_entities = []
        for section in sections_config:
            fields: List[ProjectCustomField] = section.fields.all()
            fields_obj = []
            for field in fields:
                fields_obj.append(
                    LeadToProjectFieldRepoEntity(
                        id=field.pk,
                        uuid=field.uuid,
                        name=field.name,
                        position=field.position,
                        type=field.type,
                        is_required=field.is_required,
                    )
                )
            section_config_entities.append(
                LeadToProjectSectionRepoEntity(
                    id=section.pk,
                    name=section.name,
                    uuid=section.uuid,
                    position=section.position,
                    type=section.type,
                    fields=fields_obj,
                )
            )
        if not sections_config:
            return self._get_default_sections_config()

        return section_config_entities

    def get_lead_data(self, lead_id: int) -> LeadToProjectSystemDataRepoEntity:
        lead: Lead = (
            Lead.objects.filter(id=lead_id)
            .select_related("company", "lead_project_data__state", "lead_project_data__city", "board")
            .available()
            .first()
        )
        if not lead:
            raise self.LeadDoesNotExistException("Lead does not exist")

        if lead.board.is_project_conversion_allowed is False:
            raise self.LeadToProjectImportNotAllowedException("Lead is not allowed to be converted to project")
        company_contact = (
            CompanyContact.objects.filter(company_id=lead.company_id)
            .select_related("contact", "company__org__client")
            .order_by("id")
            .first()
        )
        # TODO: Move this Logic to service layer
        is_invited = False
        if company_contact and company_contact.company.org:
            mapping = (
                VendorClientMapping.objects.filter(org_from_id=lead.company.org.pk, org_to_id=lead.company.org_from_id)
                .annotate_is_users_invited()
                .first()
            )
            is_invited = mapping.is_users_invited if mapping else False

        return LeadToProjectSystemDataRepoEntity(
            id=lead.pk,
            name=lead.name,
            org_config=OrganizationCountryConfigCache.get(instance_id=lead.board.organization_id),
            company=(
                LeadToProjectCompanyRepoEntity(
                    id=lead.company.pk,
                    name=lead.company.name,
                    is_invited=is_invited,
                    client_id=lead.company.org.pk if lead.company.org else None,
                    client_name=lead.company.org.name if lead.company.org else None,
                    client_code=lead.company.org.client.code if lead.company.org else None,
                    contact=(
                        LeadToProjectCompanyContactRepoEntity(
                            name=company_contact.contact.name,
                            email=company_contact.contact.email,
                            phone=(
                                PhoneNumberRepoEntity(
                                    country_code=f"+{company_contact.contact.phone.country_code}",
                                    number=company_contact.contact.phone.national_number,
                                )
                                if company_contact.contact.phone
                                else None
                            ),
                        )
                        if company_contact
                        else None
                    ),
                    country=(
                        LeadToProjectCountryRepoEntity(id=lead.company.country.pk, name=lead.company.country.name)
                        if lead.company.country_id
                        else None
                    ),
                    state=(
                        LeadToProjectStateRepoEntity(id=lead.company.state.pk, name=lead.company.state.name)
                        if lead.company.state_id
                        else None
                    ),
                    city=(
                        LeadToProjectCityRepoEntity(id=lead.company.city.pk, name=lead.company.city.name)
                        if lead.company.city_id
                        else None
                    ),
                    address_line_1=lead.company.address_line_1,
                    address_line_2=lead.company.address_line_2,
                    pincode=lead.company.zip_code,
                )
                if lead.company_id
                else None
            ),
            country=(
                LeadToProjectCountryRepoEntity(
                    id=lead.lead_project_data.country.pk, name=lead.lead_project_data.country.name
                )
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.country_id
                else None
            ),
            state=(
                LeadToProjectStateRepoEntity(id=lead.lead_project_data.state.pk, name=lead.lead_project_data.state.name)
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.state_id
                else None
            ),
            city=(
                LeadToProjectCityRepoEntity(id=lead.lead_project_data.city.pk, name=lead.lead_project_data.city.name)
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.city_id
                else None
            ),
            amount=lead.amount,
            address_line_1=(
                lead.lead_project_data.address_line_1
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_1
                else None
            ),
            address_line_2=(
                lead.lead_project_data.address_line_2
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.address_line_2
                else None
            ),
            zip_code=(
                lead.lead_project_data.zip_code
                if hasattr(lead, "lead_project_data") and lead.lead_project_data.zip_code
                else None
            ),
        )

    def get_project_role_list(self, org_id: int) -> List[LeadToProjectRoleData]:
        return [
            LeadToProjectRoleData(
                id=org_conf_role.role_id, name=org_conf_role.role.name, is_required=org_conf_role.is_required
            )
            for org_conf_role in OrganizationConfigRole.objects.filter(
                organization_config_id=org_id, is_required=True
            ).select_related("role")
        ]

    def update_lead_from_project(self, lead_id: int, project_id: int, user_id: int):
        Lead.objects.filter(id=lead_id).update(project_id=project_id, updated_by_id=user_id, updated_at=timezone.now())


class CompanyRepo(CompanyAbstractRepo):
    def _create_company_contacts(self, company_id: int, data: List[int], user_id: int):
        company_contact_objs = []
        for contact_id in data:
            company_contact_objs.append(
                CompanyContact(company_id=company_id, contact_id=contact_id, created_by_id=user_id)
            )
        try:
            CompanyContact.objects.bulk_create(company_contact_objs)
        except IntegrityError as e:
            if "company_contact_unique" in str(e):
                logger.error(f"Error while creating company contact: {e}")
                raise self.CompanyContactException("Company contact already exists")
            raise e

    def create(self, data: CompanyRepoEntity, user_id: int, client_id: int) -> CompanyRepoEntity:
        company = Company.objects.create(
            name=data.name,
            state_id=data.state_id,
            address_line_1=data.address_line_1,
            address_line_2=data.address_line_2,
            city_id=data.city_id,
            created_by_id=user_id,
            zip_code=data.zip_code,
            org_from_id=client_id,
            country_id=data.country_id,
        )
        if data.contact_ids:
            self._create_company_contacts(company_id=company.pk, data=data.contact_ids, user_id=user_id)

        return CompanyRepoEntity(
            id=company.pk,
            name=company.name,
            state_id=company.state_id,
            city_id=company.city_id,
            address_line_1=company.address_line_1,
            address_line_2=company.address_line_2,
            zip_code=company.zip_code,
            contact_ids=data.contact_ids,
            country_id=company.country_id,
        )

    def _update_contacts(self, company_id: int, saved_contact_ids: set[int], new_contact_ids: set[int], user_id: int):
        to_create = []
        to_delete = []
        for contact_id in new_contact_ids:
            if contact_id not in saved_contact_ids:
                to_create.append(contact_id)
        for contact_id in saved_contact_ids:
            if contact_id not in new_contact_ids:
                to_delete.append(contact_id)
        if to_create:
            self._create_company_contacts(company_id=company_id, data=to_create, user_id=user_id)

        if to_delete:
            CompanyContact.objects.filter(company_id=company_id, contact_id__in=to_delete).update(
                deleted_at=timezone.now(), deleted_by_id=user_id
            )

    def update(self, data: CompanyRepoEntity, user_id: int) -> CompanyRepoEntity:
        company = (
            Company.objects.filter(id=data.id)
            .prefetch_related(Prefetch("contacts", CompanyContact.objects.available()))
            .first()
        )
        if not company:
            logger.error(f"Company does not exist with id: {data.id}")
            raise self.CompanyDoesNotExistException("Company does not exist")
        company, _, _ = model_update(
            instance=company,
            data=data,
            fields=["name", "country_id", "address_line_1", "address_line_2", "zip_code", "city_id", "state_id"],
            updated_by_id=user_id,
        )
        saved_contact_ids: set[int] = {contact.contact_id for contact in company.contacts.all()}
        self._update_contacts(
            company_id=company.pk,
            saved_contact_ids=saved_contact_ids,
            new_contact_ids=set(data.contact_ids),
            user_id=user_id,
        )
        return CompanyRepoEntity(
            id=company.pk,
            name=company.name,
            state_id=company.state_id,
            city_id=company.city_id,
            address_line_1=company.address_line_1,
            address_line_2=company.address_line_2,
            zip_code=company.zip_code,
            contact_ids=data.contact_ids,
            country_id=company.country_id,
        )

    def update_company_from_client(self, company_id: int, client_id: int, user_id: int):
        company = Company.objects.filter(id=company_id).first()
        model_update(instance=company, data={"org_id": client_id}, updated_by_id=user_id, fields=["org_id"])


class ContactRepo(ContactAbstractRepo):
    def create(self, data: ContactRepoEntity, user_id: int, org_id: int) -> Contact:
        try:
            contact = Contact.objects.create(
                name=data.name,
                email=data.email,
                phone=data.phone,
                created_by_id=user_id,
                org_from_id=org_id,
            )
        except Contact.ContactModelException as e:
            logger.error(f"Error while creating contact: {e}")
            raise self.ContactExceptionException("Error while creating contact")
        if data.company_ids:
            to_create = []
            for company_id in data.company_ids:
                to_create.append(CompanyContact(company_id=company_id, contact_id=contact.pk, created_by_id=user_id))
            if to_create:
                CompanyContact.objects.bulk_create(to_create)
        return contact

    def delete(self, contact_id: int, user_id: int, company_id: int):
        CompanyContact.objects.filter(contact_id=contact_id, company_id=company_id).update(
            deleted_at=timezone.now(), deleted_by_id=user_id
        )

    def update(self, data: ContactRepoEntity, user_id: int) -> Contact:
        contact = Contact.objects.filter(id=data.id).first()
        if not contact:
            logger.error(f"Contact does not exist with id: {data.id}")
            raise self.ContactDoesNotExistException("Contact does not exist")

        try:
            contact, _, _ = model_update(
                instance=contact,
                data=data,
                fields=["name", "email", "phone"],
                updated_by_id=user_id,
            )
        except Contact.ContactModelException as e:
            logger.error(f"Error while updating contact: {e}")
            raise self.ContactExceptionException("Error while updating contact")
        if data.company_ids:
            # TODO: Move This Logic to service layer
            to_create = []
            to_delete = []
            saved_company_contacts = CompanyContact.objects.filter(contact_id=contact.pk).all()
            for company_id in data.company_ids:
                if company_id not in [company_contact.company_id for company_contact in saved_company_contacts]:
                    to_create.append(
                        CompanyContact(company_id=company_id, contact_id=contact.pk, created_by_id=user_id)
                    )
            for company_contact in saved_company_contacts:
                if company_contact.company_id not in data.company_ids:
                    to_delete.append(
                        CompanyContact(id=company_contact.pk, deleted_at=timezone.now(), deleted_by_id=user_id)
                    )
            if to_create:
                CompanyContact.objects.bulk_create(to_create)
            if to_delete:
                CompanyContact.objects.bulk_update(to_delete, fields=["deleted_at", "deleted_by_id"])
        return contact

    def get_lead_company_contacts(self, lead_id: int) -> Optional[List[Contact]]:
        contacts = []
        # To remove duplicate contacts
        contact_set = set()
        lead = Lead.objects.filter(id=lead_id).available().first()
        if lead and lead.company_id:
            # Lead Contact Must be on top of the list
            lead_contacts: List[LeadContact] = (
                LeadContact.objects.filter(lead_id=lead_id, contact__email__isnull=False)
                .select_related("contact")
                .available()
                .all()
            )
            # Company Contact Must be on the bottom of the list
            company_contacts: List[CompanyContact] = (
                CompanyContact.objects.filter(company_id=lead.company_id, contact__email__isnull=False)
                .select_related("contact")
                .available()
                .all()
            )
            for lead_contact in lead_contacts:
                if lead_contact.contact_id not in contact_set:
                    contact_set.add(lead_contact.contact_id)
                    contacts.append(lead_contact.contact)

            for company_contact in company_contacts:
                if company_contact.contact_id not in contact_set:
                    contact_set.add(company_contact.contact_id)
                    contacts.append(company_contact.contact)
        return contacts


class LeadEventRepo(LeadEventAbstractRepo):
    def _create_lead_created_event(self, data: LeadCreatedEventEntity, event_id: int):
        LeadCreatedEventData.objects.create(name=data.name, event_id=event_id)

    def _create_lead_assigned_event(self, data: LeadAssignedEventEntity, event_id: int):
        lead_assignee_event_objs = []
        for assignee_id in data.assignee_ids:
            lead_assignee_event_objs.append(LeadAssignedEventData(assigned_user_id=assignee_id, event_id=event_id))
        LeadAssignedEventData.objects.bulk_create(lead_assignee_event_objs)

    def _create_lead_stage_changed_event(self, data: LeadStageChangedEventEntity, event_id: int):
        LeadStageChangedEventData.objects.create(
            from_stage_id=data.from_stage_id, to_stage_id=data.to_stage_id, event_id=event_id
        )

    def _create_lead_task_created_event(self, data: LeadTaskCreatedEventEntity, event_id: int):
        LeadTaskCreatedEventData.objects.create(task_id=data.task_id, event_id=event_id)

    def _create_lead_comment_created_event(self, data: LeadCommentCreatedEventEntity, event_id: int):
        LeadCommentCreatedEventData.objects.create(comment_id=data.comment_id, event_id=event_id)

    def _create_lead_reminder_created_event(self, data: LeadReminderCreatedEventEntity, event_id: int):
        LeadReminderCreatedEventData.objects.create(reminder_id=data.reminder_id, event_id=event_id)

    def _create_lead_quotation_submitted_event(self, data: LeadQuotationSubmittedEventEntity, event_id: int):
        LeadQuotationSubmittedEventData.objects.create(quotation_id=data.quotation_id, event_id=event_id)

    def create(self, data: LeadEventCreateRepoEntity, user_id: int) -> int:
        lead_event = LeadEvent(lead_id=data.lead_id, type=data.type, created_by_id=user_id, created_at=timezone.now())
        lead_event.save()

        if data.lead_created_event_entity:
            self._create_lead_created_event(data=data.lead_created_event_entity, event_id=lead_event.pk)
        elif data.lead_assigned_event_entities:
            self._create_lead_assigned_event(data=data.lead_assigned_event_entities, event_id=lead_event.pk)
        elif data.lead_stage_changed_event_entity:
            self._create_lead_stage_changed_event(data=data.lead_stage_changed_event_entity, event_id=lead_event.pk)
        elif data.lead_task_created_event_entity:
            self._create_lead_task_created_event(data=data.lead_task_created_event_entity, event_id=lead_event.pk)
        elif data.lead_comment_created_event_entity:
            self._create_lead_comment_created_event(data=data.lead_comment_created_event_entity, event_id=lead_event.pk)
        elif data.lead_reminder_created_event_entity:
            self._create_lead_reminder_created_event(
                data=data.lead_reminder_created_event_entity, event_id=lead_event.pk
            )
        elif data.lead_quotation_submitted_event_entity:
            self._create_lead_quotation_submitted_event(
                data=data.lead_quotation_submitted_event_entity, event_id=lead_event.pk
            )

    def get_lead_events_data(self, lead_id: int):
        return get_lead_events(lead_id=lead_id)

    def get_activity_timeline(self, lead_id: int):
        return self.get_lead_events_data(lead_id=lead_id)


class LeadReminderRepo(LeadReminderAbstractRepo):
    def create(self, data: LeadReminderCreateEntity, user_id: int, lead_id: int):
        lead_reminder = LeadReminder(
            lead_id=lead_id,
            time=data.get("time"),
            remark=data.get("remark"),
            created_by_id=user_id,
            created_at=timezone.now(),
        )
        lead_reminder.save()
        return lead_reminder.pk

    def get_lead_reminders(self, lead_id: int):
        pass


class LeadExcelRepo(LeadExcelAbstractRepo, LeadRepo):
    class LeadExcelRepoException(LeadExcelAbstractRepo.LeadExcelAbstractRepoException):
        pass

    def __init__(self, board_id: int, org_id: int, user_id: int):
        self.org_id = org_id
        self.board_id = board_id
        self.user_id = user_id
        self._board = None

    def _get_board(self) -> Board:
        if self._board is None:
            try:
                self._board = Board.objects.get(id=self.board_id)
            except Board.DoesNotExist:
                raise ResourceDoesNotExistException()
        return self._board

    def get_board_name(self) -> str:
        return self._get_board().name

    def get_lead_sections_config(self, board_id: int) -> List[LeadSectionWithFieldDataRepoEntity]:
        sections: List[BoardLeadSection] = get_sections_with_fields(board_id=board_id)
        section_objs = []
        for section in sections:
            fields: List[BoardLeadField] = section.fields.all()
            fields_objs = []

            if str(section.uuid) == BASIC_DETAIL_SECTION_UUID:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        name="Lead ID",
                        uuid=LEAD_NUMBER_UUID,
                        position=0,
                        is_required=False,
                        type=CustomFieldTypeEnum.LEAD_NUMBER.value,
                        created_at=timezone.now(),
                        archived_at=None,
                        actions=[],
                    )
                )
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        name="Lead Creation Date",
                        uuid=LEAD_CREATION_DATE_UUID,
                        position=1,
                        is_required=False,
                        type=CustomFieldTypeEnum.DATE.value,
                        created_at=timezone.now(),
                        archived_at=None,
                        actions=[],
                    )
                )
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        name="Stage Name",
                        uuid=LEAD_STAGE_NAME_UUID,
                        position=2,
                        is_required=False,
                        type=CustomFieldTypeEnum.TEXT.value,
                        created_at=timezone.now(),
                        archived_at=None,
                        actions=[],
                    )
                )

            for field in fields:
                fields_objs.append(
                    LeadFieldDetailRepoEntity(
                        uuid=field.uuid,
                        name=field.name,
                        position=field.position,
                        type=field.type,
                        is_required=field.is_required,
                        created_at=field.created_at,
                        archived_at=field.archived_at,
                    )
                )
            section_objs.append(
                LeadSectionWithFieldDataRepoEntity(
                    uuid=section.uuid,
                    name=section.name,
                    position=section.position,
                    fields=fields_objs,
                    created_at=section.created_at,
                    archived_at=section.archived_at,
                )
            )
        return section_objs

    @staticmethod
    def lead_excel_queryset_to_entity(lead: Lead) -> LeadExcelEntity:
        """
        Convert a Lead queryset object to a LeadExcelEntity.

        Args:
            lead: A Lead model instance with related data pre-fetched.

        Returns:
            LeadExcelEntity with all lead data populated.
        """
        stage_entity = StageEntity(id=lead.stage_id, name=lead.stage.name if lead.stage else "")

        company_entity = None
        if lead.company_id and lead.company:
            company_entity = LeadCompanyEntity(id=lead.company_id, name=lead.company.name)

        # Handle lead project data
        lead_project_data_entity = None
        if hasattr(lead, "lead_project_data") and lead.lead_project_data:
            lead_project_data_entity = LeadProjectDataEntity(
                country=(
                    IDNameEntity(id=lead.lead_project_data.country.id, name=lead.lead_project_data.country.name)
                    if lead.lead_project_data.country
                    else None
                ),
                state=(
                    IDNameEntity(id=lead.lead_project_data.state.id, name=lead.lead_project_data.state.name)
                    if lead.lead_project_data.state
                    else None
                ),
                city=(
                    IDNameEntity(id=lead.lead_project_data.city.id, name=lead.lead_project_data.city.name)
                    if lead.lead_project_data.city
                    else None
                ),
                address_line_1=lead.lead_project_data.address_line_1,
                address_line_2=lead.lead_project_data.address_line_2,
                zip_code=lead.lead_project_data.zip_code,
            )

        contacts = [
            LeadContactEntity(
                id=contact.contact.id,
                name=contact.contact.name,
                email=contact.contact.email,
                phone=(
                    PhoneEntity(
                        country_code=f"+{contact.contact.phone.country_code}",
                        number=str(contact.contact.phone.national_number),
                    )
                    if contact.contact.phone
                    else None
                ),
            )
            for contact in getattr(lead, "prefetched_contacts", [])
            if contact.contact
        ]

        assignees = [
            AssigneeEntity(
                id=assignee.user_id,
                name=assignee.user.name,
                email=assignee.user.email,
                phone_number=(
                    PhoneEntity(
                        country_code=f"+{assignee.user.phone_number.country_code}",
                        number=str(assignee.user.phone_number.national_number),
                    )
                    if assignee.user.phone_number
                    else None
                ),
                photo=assignee.user.photo.url if assignee.user.photo else None,
            )
            for assignee in getattr(lead, "prefetched_assignees", [])
        ]

        return LeadExcelEntity(
            id=lead.id,
            name=lead.name,
            project_id=lead.project_id,
            serial_number=lead.serial_number,
            stage_id=lead.stage_id,
            stage=stage_entity,
            company_id=lead.company_id,
            company=company_entity,
            amount=float(lead.amount) if lead.amount is not None else None,
            source=lead.source,
            lead_project_data=lead_project_data_entity,
            contacts=contacts,
            assignees=assignees,
            created_at=lead.created_at,
        )

    def get_lead_data_for_excel_export(
        self, user_id: int, board_permissions: BoardPermissionType, board_id: int
    ) -> List[LeadExcelEntity]:
        can_view_all_leads = BoardPermissionHelper.can_view_all_leads(permissions=board_permissions)

        queryset = Lead.objects.available()

        if not can_view_all_leads:
            queryset = queryset.annotate_edit_and_view_permissions(
                user_id=user_id, board_permissions=board_permissions
            ).filter(can_view_lead=True)
        queryset = (
            queryset.filter(board_id=board_id)
            .select_related(
                "company",
                "lead_project_data__state",
                "lead_project_data__city",
                "board",
                "lead_project_data",
                "lead_project_data__country",
                "stage",
            )
            .prefetch_related(
                Prefetch(
                    "contacts",
                    queryset=LeadContact.objects.select_related("contact").filter(deleted_at__isnull=True),
                    to_attr="prefetched_contacts",
                ),
                Prefetch(
                    "assignees",
                    queryset=LeadAssignee.objects.select_related("user"),
                    to_attr="prefetched_assignees",
                ),
            )
            .available()
            .order_by("-created_at")
        )

        return [self.lead_excel_queryset_to_entity(lead) for lead in queryset]

    def get_board_leads_custom_field_queryset(
        self, board_id: int, lead_ids: list[int]
    ) -> Iterator[tuple[int, List[LeadCustomDataRepoEntity]]]:
        logger.info("Fetching custom field data for leads", board_id=board_id, lead_count=len(lead_ids))

        if not lead_ids:
            return

        base_queryset = (
            BoardLeadField.objects.filter(board_id=board_id)
            .exclude(uuid__in=LEAD_SYSTEM_FIELD_UUIDS)
            .only("uuid", "pk", "name", "type", "position", "is_required", "created_at", "data_counter")
            .annotate_lead_id()
        )

        logger.info("Custom field queryset fetched")

        queryset: Any

        chunk_size = 200

        for index, lead_id in enumerate(lead_ids):
            if index % chunk_size == 0:
                chunked_lead_ids = lead_ids[index : index + chunk_size]
                logger.info("Getting excel Leads Custom Fields", index=index, chunked_lead_ids=chunked_lead_ids)
                queryset = (
                    base_queryset.filter(lead_id__in=chunked_lead_ids)
                    .order_by("position")
                    .prefetch_related(
                        Prefetch("custom_text_data", to_attr="prefetched_text_data"),
                        Prefetch(
                            "custom_drop_down_data",
                            queryset=(
                                BoardLeadDropDownFieldData.objects.filter(lead_id__in=chunked_lead_ids)
                                .select_related("data", "field")
                                .available()
                            ),
                            to_attr="prefetched_dropdown_data",
                        ),
                        Prefetch("custom_email_data", to_attr="prefetched_email_data"),
                        Prefetch("custom_number_data", to_attr="prefetched_number_data"),
                        Prefetch("custom_date_data", to_attr="prefetched_date_data"),
                        Prefetch("custom_boolean_data", to_attr="prefetched_boolean_data"),
                        Prefetch("custom_rich_text_data", to_attr="prefetched_rich_text_data"),
                        Prefetch("custom_phone_number_data", to_attr="prefetched_phone_number_data"),
                        Prefetch("custom_file_data", to_attr="prefetched_file_data"),
                        Prefetch("custom_datetime_data", to_attr="prefetched_datetime_data"),
                    )
                    .available()
                )

            current_field_entities = []

            for field in queryset:
                if field.lead_id == lead_id:
                    if field.type == CustomFieldTypeEnum.DROPDOWN.value:
                        value = self._get_custom_drop_down_data_for_excel_export(field=field)
                    elif field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                        value = self._get_custom_multi_drop_down_data_for_excel_export(field=field)
                    else:
                        value = self._get_custom_field_data_for_excel_export(field=field)

                    current_field_entities.append(
                        LeadCustomDataRepoEntity(
                            field_uuid=field.uuid,
                            field_id=field.pk,
                            name=field.name,
                            type=field.type,
                            position=field.position,
                            is_required=field.is_required,
                            value=value,
                            created_at=field.created_at,
                            data_counter=field.data_counter,
                        )
                    )

            yield lead_id, current_field_entities

    def _get_custom_drop_down_data_for_excel_export(
        self, field: BoardLeadField
    ) -> LeadCustomFieldValueRepoEntity | None:
        """Handle single-select dropdown fields"""
        if hasattr(field, "prefetched_dropdown_data"):
            # Filter in Python memory for single-select dropdown values
            single_dropdowns = [
                item
                for item in field.prefetched_dropdown_data
                if (item.field.type == CustomFieldTypeEnum.DROPDOWN.value and item.lead_id == field.lead_id)
            ]

            if single_dropdowns:
                data = single_dropdowns[0]
                if data.data:
                    return LeadCustomFieldValueRepoEntity(
                        id=data.pk,
                        value={"id": data.data.pk, "name": data.data.name},
                    )
        return None

    def _get_custom_multi_drop_down_data_for_excel_export(
        self, field: BoardLeadField
    ) -> List[LeadCustomFieldValueRepoEntity]:
        """Handle multi-select dropdown fields"""
        if hasattr(field, "prefetched_dropdown_data"):
            # Filter in Python memory for multi-select dropdown values
            multi_dropdowns = [
                item
                for item in field.prefetched_dropdown_data
                if (item.field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value and item.lead_id == field.lead_id)
            ]
            return [
                LeadCustomFieldValueRepoEntity(id=data.pk, value={"id": data.data.pk, "name": data.data.name})
                for data in multi_dropdowns
                if data.data is not None
            ]
        return []

    def _get_custom_text_data_for_excel_export(self, field: BoardLeadField) -> LeadCustomFieldValueRepoEntity:
        prefetched_text_data_list = getattr(field, "prefetched_text_data", [])

        custom_text_data = next(
            (data_obj for data_obj in prefetched_text_data_list if data_obj.lead_id == field.lead_id),
            None,
        )

        if custom_text_data:
            return LeadCustomFieldValueRepoEntity(id=custom_text_data.pk, value=custom_text_data.data)

    def _get_custom_email_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_email_data_list = getattr(field, "prefetched_email_data", [])

        custom_email_data = next(
            (data_obj for data_obj in prefetched_email_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_email_data:
            return LeadCustomFieldValueRepoEntity(id=custom_email_data.pk, value=custom_email_data.data)

    def _get_custom_number_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_number_data_list = getattr(field, "prefetched_number_data", [])

        custom_number_data = next(
            (data_obj for data_obj in prefetched_number_data_list if data_obj.lead_id == field.lead_id), None
        )
        if custom_number_data:
            return LeadCustomFieldValueRepoEntity(id=custom_number_data.pk, value=custom_number_data.data)

    def _get_custom_date_data_for_excel_export(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        prefetched_date_data_list = getattr(field, "prefetched_date_data", [])

        custom_date_data = next(
            (data_obj for data_obj in prefetched_date_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_date_data:
            return LeadCustomFieldValueRepoEntity(id=custom_date_data.pk, value=custom_date_data.data)

    def _get_custom_datetime_date_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_datetime_data_list = getattr(field, "prefetched_datetime_data", [])

        custom_datetime_data = next(
            (data_obj for data_obj in prefetched_datetime_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_datetime_data:
            return LeadCustomFieldValueRepoEntity(id=custom_datetime_data.pk, value=custom_datetime_data.data)

    def _get_custom_boolean_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_boolean_data_list = getattr(field, "prefetched_boolean_data", [])

        custom_boolean_data = next(
            (data_obj for data_obj in prefetched_boolean_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_boolean_data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_boolean_data.pk, value=custom_boolean_data.data if custom_boolean_data.data else False
            )

    def _get_custom_rich_text_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_rich_text_data_list = getattr(field, "prefetched_rich_text_data", [])

        custom_rich_text_data: BoardLeadRichTextFieldData = next(
            (data_obj for data_obj in prefetched_rich_text_data_list if data_obj.lead_id == field.lead_id), None
        )
        if custom_rich_text_data:
            return LeadCustomFieldValueRepoEntity(id=custom_rich_text_data.pk, value=custom_rich_text_data.data)

    def _get_custom_phone_number_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        prefetched_phone_number_data_list = getattr(field, "prefetched_phone_number_data", [])

        custom_phone_number_data = next(
            (data_obj for data_obj in prefetched_phone_number_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_phone_number_data:
            return LeadCustomFieldValueRepoEntity(
                id=custom_phone_number_data.pk,
                value=(
                    PhoneNumberSerializer(
                        {
                            "country_code": custom_phone_number_data.data.country_code,
                            "national_number": custom_phone_number_data.data.national_number,
                        }
                    ).data
                    if custom_phone_number_data.data
                    else None
                ),
            )

    def _get_custom_file_data_for_excel_export(self, field: QuerySet[BoardLeadField]) -> LeadCustomFieldValueRepoEntity:
        prefetched_file_data_list = getattr(field, "prefetched_file_data", [])

        custom_file_data = next(
            (data_obj for data_obj in prefetched_file_data_list if data_obj.lead_id == field.lead_id), None
        )

        if custom_file_data and custom_file_data.data.name:
            return LeadCustomFieldValueRepoEntity(
                id=custom_file_data.pk, value={"name": custom_file_data.name, "url": custom_file_data.data.url}
            )

    def _get_custom_field_data_for_excel_export(
        self, field: QuerySet[BoardLeadField]
    ) -> LeadCustomFieldValueRepoEntity:
        if field.type == CustomFieldTypeEnum.TEXT.value:
            return self._get_custom_text_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.EMAIL.value:
            return self._get_custom_email_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.DECIMAL.value:
            return self._get_custom_number_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.DATE.value:
            return self._get_custom_date_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.DATETIME.value:
            return self._get_custom_datetime_date_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.BOOLEAN.value:
            return self._get_custom_boolean_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.RICH_TEXT.value:
            return self._get_custom_rich_text_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.PHONE_NUMBER.value:
            return self._get_custom_phone_number_data_for_excel_export(field)
        if field.type == CustomFieldTypeEnum.FILE.value:
            return self._get_custom_file_data_for_excel_export(field)
