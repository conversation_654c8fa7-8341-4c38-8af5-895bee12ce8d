from django import forms

from core.models import Organization


class CloneProjectForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    organization = forms.ModelChoiceField(
        queryset=Organization.objects.all().only("id", "name").order_by("-id"),
        widget=forms.Select(
            attrs={
                "placeholder": "Organization",
                "style": ("width: 272px; height: 40px; padding: 10px; font-size: 14px;border-radius: 8px;"),
            }
        ),
        empty_label="Select organization *",
        to_field_name="id",
        required=True,
        label="",
    )
    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={
                "placeholder": "Notification Email *",
                "style": ("width: 250px; height: 20px; padding: 10px; font-size: 14px;border-radius: 8px;"),
            }
        ),
        required=True,
        label="",
    )

    project_name = forms.Field(
        widget=forms.TextInput(
            attrs={
                "placeholder": "Project Name *",
                "style": ("width: 250px; height: 20px; padding: 10px; font-size: 14px;border-radius: 8px;"),
            }
        ),
        required=True,
        label="",
    )

    def __init__(self, *args, **kwargs):
        super(CloneProjectForm, self).__init__(*args, **kwargs)
        self.fields["organization"].label_from_instance = self.label_from_instance

    @staticmethod
    def label_from_instance(obj):
        return f"{obj.id} - {obj.name}"
