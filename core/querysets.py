from django.db import IntegrityError
from django.db.models import (
    QuerySet,
)

from common.querysets import AvailableQuerySetMixin
from core.order_type_config.data.exceptions import (
    DuplicateNonEditablePOTypeException,
    DuplicatePOTypeNameException,
)


class RoleQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationOutgoingOrderNotificationPocQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationIncomingOrderNotificationPocQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationDocumentQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationPaymentTermQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationPurchaseOrderTypeQuerySet(QuerySet, AvailableQuerySetMixin):
    pass
