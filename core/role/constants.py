from django.utils.translation import gettext_lazy as _

from authorization.domain.constants import Permissions
from common.choices import ProjectUserRoleLevelChoices, SectionKey
from common.constants import BaseEnum

ROLE_LEVEL_PERMISSION_MAPPING = {}
# Level 1
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_1] = {
    "action_permissions": [
        Permissions.CAN_ACCESS_RECCE,
        Permissions.CAN_UPLOAD_RECCE_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_VIEW_DESIGN_FILES,
        Permissions.CAN_ACCESS_BOQ,
        Permissions.CAN_ACCESS_MY_SCOPE,
        Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT,
        Permissions.CAN_ACCESS_ORDER,
        Permissions.CAN_ACCESS_OUTGOING_ORDER,
        Permissions.CAN_CREATE_OUTGOING_ORDER,
        Permissions.CAN_DELETE_OUTGOING_ORDER,
        Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
        Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_UPDATE_WORK_PROGRESS,
        Permissions.CAN_ACCESS_SNAG,
        Permissions.CAN_ACCESS_OTHER_EXPENSE,
        Permissions.CAN_LINK_SNAG_ITEMS,
    ],
    "notification_permissions": [
        Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION,
        Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION,
    ],
}

# Level 2
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_2] = {
    "action_permissions": (ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_1]["action_permissions"])
    + [
        Permissions.CAN_EDIT_DESIGN_FILES,
        Permissions.CAN_DOWNLOAD_DESIGN_FILES,
        Permissions.CAN_DELETE_DESIGN_FILES,
        Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES,
        Permissions.CAN_MARK_REVIEWED_DESIGN_FILES,
        Permissions.CAN_REJECT_DESIGN_FILES,
        Permissions.CAN_APPROVE_DESIGN_FILES,
    ],
    "notification_permissions": (
        ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_1]["notification_permissions"]
    ),
}

# Level 3
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_3] = {
    "action_permissions": (ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_2]["action_permissions"])
    + [
        Permissions.CAN_UPDATE_PROJECT_STATUS,
    ],
    "notification_permissions": (
        ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_2]["notification_permissions"]
    ),
}

# Level 4
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_4] = {
    "action_permissions": (ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_3]["action_permissions"])
    + [
        Permissions.CAN_ACCESS_PROJECT_ATTACHMENT,
        Permissions.CAN_ACCESS_PROJECT_CUSTOM_FIELDS,
        Permissions.CAN_ASSIGN_PROJECT_USER,
        Permissions.CAN_CREATE_RECCE_LINK,
        Permissions.CAN_EDIT_RECCE_DATA,
        Permissions.CAN_VIEW_CLIENT_RATE,
        Permissions.CAN_EDIT_BOQ,
        Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT,
        Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY,
        Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT,
        Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL,
        Permissions.CAN_ACCESS_INCOMING_ORDER,
        Permissions.CAN_SEND_OUTGOING_ORDER,
        Permissions.CAN_CANCEL_OUTGOING_ORDER,
        Permissions.CAN_CLOSE_SNAG,
        Permissions.CAN_VIEW_ORDER_RATE,
    ],
    "notification_permissions": (
        ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_3]["notification_permissions"]
    )
    + [
        Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS,
        Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS,
    ],
}

# Level 5
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_5] = {
    "action_permissions": (ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_4]["action_permissions"])
    + [
        Permissions.CAN_EDIT_PROJECT,
        Permissions.CAN_CREATE_INCOMING_ORDER,
        Permissions.CAN_CANCEL_INCOMING_ORDER,
        Permissions.CAN_CREATE_PROPOSAL,
        Permissions.CAN_EDIT_PROPOSAL,
        Permissions.CAN_SEND_PROPOSAL,
        Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
        Permissions.CAN_APPROVE_CLIENT_PROPOSAL,
    ],
    "notification_permissions": (
        ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_4]["notification_permissions"]
    ),
}

# Level 6
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_6] = {
    "action_permissions": (ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_5]["action_permissions"])
    + [
        Permissions.CAN_APPROVE_RECCE,
        Permissions.CAN_FREEZE_DESIGN,
        Permissions.CAN_CLOSE_ORDER,
        Permissions.CAN_APPROVE_PROPOSAL,
        Permissions.CAN_VIEW_DRAFT_DESIGN_FILES,
        Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES,
        Permissions.CAN_VIEW_CLIENT_REJECTED_FILES,
        Permissions.CAN_VIEW_REJECTED_DESIGN_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_ACCESS_MATERIALS,
        Permissions.CAN_CREATE_GRN,
        Permissions.CAN_UPDATE_STOCK_CONSUMPTION,
        Permissions.CAN_VIEW_BUDGET_RATE,
        Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD,
        Permissions.CAN_CANCEL_STOCK_TRANSFER,
        Permissions.CAN_CANCEL_PO,
        Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX,
        Permissions.CAN_CAPTURE_FILE_IN_DPR,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
        Permissions.CAN_EXPORT_PROGRESS_REPORT,
        Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR,
        Permissions.CAN_DELETE_FILE_IN_DPR,
        Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE,
        Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE,
    ],
    "notification_permissions": (
        ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_5]["notification_permissions"]
    ),
}

# order reviewer
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_98] = {
    "action_permissions": [
        Permissions.CAN_ACCESS_BOQ,
        Permissions.CAN_ACCESS_MY_SCOPE,
        Permissions.CAN_ACCESS_INCOMING_ORDER,
    ],
    "notification_permissions": [],
}

# order receiver
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_99] = {
    "action_permissions": [
        Permissions.CAN_EDIT_DESIGN_FILES,
        Permissions.CAN_APPROVE_DESIGN_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_VIEW_DESIGN_FILES,
        Permissions.CAN_ACCESS_BOQ,
        Permissions.CAN_FREEZE_DESIGN,
        Permissions.CAN_ACCESS_MY_SCOPE,
        Permissions.CAN_ACCESS_INCOMING_ORDER,
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_UPDATE_WORK_PROGRESS,
        Permissions.CAN_CAPTURE_FILE_IN_DPR,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
        Permissions.CAN_EXPORT_PROGRESS_REPORT,
        Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR,
        Permissions.CAN_DELETE_FILE_IN_DPR,
        Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE,
        Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT,
        Permissions.CAN_EDIT_DESIGN_FILES,
        Permissions.CAN_APPROVE_DESIGN_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_VIEW_DRAFT_DESIGN_FILES,
        Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES,
        Permissions.CAN_VIEW_CLIENT_REJECTED_FILES,
        Permissions.CAN_VIEW_REJECTED_DESIGN_FILES,
        Permissions.CAN_DOWNLOAD_DESIGN_FILES,
        Permissions.CAN_DELETE_DESIGN_FILES,
        Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES,
        Permissions.CAN_MARK_REVIEWED_DESIGN_FILES,
        Permissions.CAN_FREEZE_DESIGN,
        Permissions.CAN_REJECT_DESIGN_FILES,
        Permissions.CAN_VIEW_CLIENT_RATE,
        Permissions.CAN_EDIT_PROPOSAL,
        Permissions.CAN_CREATE_PROPOSAL,
        Permissions.CAN_EDIT_BOQ,
    ],
    "notification_permissions": [],
}

# proposal approver
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_97] = {
    "action_permissions": [
        Permissions.CAN_EDIT_DESIGN_FILES,
        Permissions.CAN_APPROVE_DESIGN_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_VIEW_DESIGN_FILES,
        Permissions.CAN_FREEZE_DESIGN,
        Permissions.CAN_ACCESS_ORDER,
        Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
        Permissions.CAN_ACCESS_OUTGOING_ORDER,
        Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
        Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
        Permissions.CAN_APPROVE_PROPOSAL,
        Permissions.CAN_VIEW_CLIENT_RATE,
        Permissions.CAN_VIEW_ORDER_RATE,
        Permissions.CAN_EDIT_DESIGN_FILES,
        Permissions.CAN_APPROVE_DESIGN_FILES,
        Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
        Permissions.CAN_VIEW_DRAFT_DESIGN_FILES,
        Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES,
        Permissions.CAN_VIEW_CLIENT_REJECTED_FILES,
        Permissions.CAN_VIEW_REJECTED_DESIGN_FILES,
        Permissions.CAN_DOWNLOAD_DESIGN_FILES,
        Permissions.CAN_DELETE_DESIGN_FILES,
        Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES,
        Permissions.CAN_MARK_REVIEWED_DESIGN_FILES,
        Permissions.CAN_FREEZE_DESIGN,
        Permissions.CAN_REJECT_DESIGN_FILES,
    ],
    "notification_permissions": [],
}

# jsw one homes
ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_96] = {
    "action_permissions": list(
        set((ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_6]["action_permissions"]))
        - set(
            [
                Permissions.CAN_ACCESS_OUTGOING_ORDER,
                Permissions.CAN_ACCESS_OTHER_EXPENSE,
                Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
                Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
                Permissions.CAN_ACCESS_WORK_PROGRESS,
                Permissions.CAN_LINK_SNAG_ITEMS,
            ]
        )
    ),
    "notification_permissions": [],
}


ROLE_LEVEL_PERMISSION_MAPPING[ProjectUserRoleLevelChoices.LEVEL_100] = {
    "action_permissions": [
        Permissions.CAN_ACCESS_RECCE,
        Permissions.CAN_EDIT_RECCE_DATA,
        Permissions.CAN_UPLOAD_RECCE_FILES,
    ],
    "notification_permissions": [],
}


PROJECT_NOTIFICATION_PERMISSIONS_SET = [
    Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_DELAYED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_OVERDUE_NOTIFICATIONS,
]


class PermissionSet(BaseEnum):
    PROJECT_LEVEL_1 = "project_level_1"
    PROJECT_LEVEL_2 = "project_level_2"
    PROJECT_LEVEL_3 = "project_level_3"
    RECCE_LEVEL_1 = "recce_level_1"
    RECCE_LEVEL_2 = "recce_level_2"
    RECCE_LEVEL_3 = "recce_level_3"
    DESIGN_LEVEL_1 = "design_level_1"
    DESIGN_LEVEL_2 = "design_level_2"
    DESIGN_LEVEL_3 = "design_level_3"
    DESIGN_LEVEL_4 = "design_level_4"
    DESIGN_LEVEL_5 = "design_level_5"
    DESIGN_LEVEL_6 = "design_level_6"
    DESIGN_LEVEL_7 = "design_level_7"
    DESIGN_VIEW_ALL = "design_view_all"
    # Edit Permissions group
    DESIGN_LEVEL_8 = "design_level_8"
    DESIGN_LEVEL_9 = "design_level_9"
    DESIGN_LEVEL_10 = "design_level_10"
    DESIGN_EDIT_ALL = "design_edit_all"
    # Approver Permissions group
    DESIGN_LEVEL_11 = "design_level_11"
    DESIGN_LEVEL_12 = "design_level_12"
    DESIGN_LEVEL_13 = "design_level_13"
    DESIGN_APPROVER_ALL = "design_approver_all"
    MY_SCOPE_LEVEL_1 = "my_scope_level_1"
    MY_SCOPE_LEVEL_2 = "my_scope_level_2"
    INCOMING_ORDER_LEVEL_1 = "incoming_order_level_1"
    PROPOSAL_FOR_CLIENT_LEVEL_1 = "proposal_for_client_level_1"
    PROPOSAL_FOR_CLIENT_LEVEL_2 = "proposal_for_client_level_2"
    PROPOSAL_FOR_CLIENT_LEVEL_3 = "proposal_for_client_level_3"
    OUTGOING_ORDER_LEVEL_1 = "outgoing_order_level_1"
    OUTGOING_ORDER_LEVEL_2 = "outgoing_order_level_2"
    OUTGOING_ORDER_LEVEL_3 = "outgoing_order_level_3"
    OUTGOING_ORDER_LEVEL_4 = "outgoing_order_level_4"
    OUTGOING_ORDER_LEVEL_5 = "outgoing_order_level_5"
    PROPOSAL_FROM_VENDOR_LEVEL_1 = "proposal_from_vendor_level_1"
    PROPOSAL_FROM_VENDOR_LEVEL_2 = "proposal_from_vendor_level_2"
    VENDOR_WISE_SCOPE_LEVEL_1 = "vendor_wise_scope_level_1"
    VENDOR_WISE_PROGRESS_LEVEL_1 = "vendor_wise_progress_level_1"

    PROGRESS_UPDATE_VIEW_ALL = "progress_update_view_all"
    PROGRESS_UPDATE_LEVEL_1 = "progress_update_level_1"
    PROGRESS_UPDATE_UPDATE_ALL = "progress_update_update_all"
    PROGRESS_UPDATE_LEVEL_2 = "progress_update_level_2"
    PROGRESS_UPDATE_LEVEL_3 = "progress_update_level_3"
    PROGRESS_UPDATE_LEVEL_4 = "progress_update_level_4"
    PROGRESS_UPDATE_LEVEL_5 = "progress_update_level_5"
    PROGRESS_UPDATE_LEVEL_6 = "progress_update_level_6"
    PROGRESS_UPDATE_LEVEL_7 = "progress_update_level_7"
    PROGRESS_UPDATE_EDIT_ALL = "progress_update_edit_all"
    PROGRESS_UPDATE_LEVEL_8 = "progress_update_level_8"
    PROGRESS_UPDATE_LEVEL_9 = "progress_update_level_9"
    PROGRESS_UPDATE_LEVEL_10 = "progress_update_level_10"
    PROGRESS_UPDATE_LEVEL_11 = "progress_update_level_11"
    PROGRESS_UPDATE_LEVEL_12 = "progress_update_level_12"
    PROGRESS_UPDATE_LEVEL_13 = "progress_update_level_13"

    SNAG_LEVEL_1 = "snag_level_1"
    SNAG_LEVEL_2 = "snag_level_2"
    INVENTORY_PERMISSION_LEVEL_1 = "inventory_permission_level_1"
    INVENTORY_PERMISSION_LEVEL_2 = "inventory_permission_level_2"
    INVENTORY_PERMISSION_LEVEL_3 = "inventory_permission_level_3"
    INVENTORY_PERMISSION_LEVEL_4 = "inventory_permission_level_4"
    PROJECT_ACTIVITY_SCHEDULE_LEVEL_1 = "project_activity_schedule_level_1"
    PROJECT_ACTIVITY_SCHEDULE_LEVEL_2 = "project_activity_schedule_level_2"
    PROJECT_ACTIVITY_SCHEDULE_LEVEL_3 = "project_activity_schedule_level_3"
    VENDOR_INVOICES_LEVEL_1 = "vendor_invoices_level_1"
    VENDOR_PAYMENT_LEVEL_1 = "vendor_payment_level_1"
    VENDOR_PAYMENT_LEVEL_2 = "vendor_payment_level_2"
    VENDOR_PAYMENT_LEVEL_3 = "vendor_payment_level_3"


PERMISSION_SET_MAPPING = {PermissionSet.PROJECT_LEVEL_1: []}

# Project
PERMISSION_SET_MAPPING[PermissionSet.PROJECT_LEVEL_2] = PERMISSION_SET_MAPPING[PermissionSet.PROJECT_LEVEL_1] + [
    Permissions.CAN_ACCESS_PROJECT_ATTACHMENT,
    Permissions.CAN_ACCESS_PROJECT_CUSTOM_FIELDS,
]
PERMISSION_SET_MAPPING[PermissionSet.PROJECT_LEVEL_3] = PERMISSION_SET_MAPPING[PermissionSet.PROJECT_LEVEL_2] + [
    Permissions.CAN_EDIT_PROJECT,
    Permissions.CAN_ASSIGN_PROJECT_USER,
    Permissions.CAN_UPDATE_PROJECT_STATUS,
]

# Recce
PERMISSION_SET_MAPPING[PermissionSet.RECCE_LEVEL_1] = [Permissions.CAN_ACCESS_RECCE]
PERMISSION_SET_MAPPING[PermissionSet.RECCE_LEVEL_2] = PERMISSION_SET_MAPPING[PermissionSet.RECCE_LEVEL_1] + [
    Permissions.CAN_CREATE_RECCE_LINK,
    Permissions.CAN_EDIT_RECCE_DATA,
    Permissions.CAN_UPLOAD_RECCE_FILES,
]
PERMISSION_SET_MAPPING[PermissionSet.RECCE_LEVEL_3] = PERMISSION_SET_MAPPING[PermissionSet.RECCE_LEVEL_2] + [
    Permissions.CAN_APPROVE_RECCE,
]


# Design View Permissions
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_1] = [Permissions.CAN_VIEW_DRAFT_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_2] = [Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_3] = [Permissions.CAN_VIEW_APPROVED_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_4] = [Permissions.CAN_VIEW_REJECTED_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_5] = [Permissions.CAN_VIEW_CLIENT_REJECTED_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_VIEW_ALL] = (
    PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_1]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_2]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_3]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_4]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_5]
)

# Design Edit Permissions
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_6] = [Permissions.CAN_DOWNLOAD_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_7] = [Permissions.CAN_EDIT_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_8] = [Permissions.CAN_DELETE_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_EDIT_ALL] = (
    PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_6]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_7]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_8]
)

# Design Approver Permissions
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_9] = [Permissions.CAN_APPROVE_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_10] = [Permissions.CAN_REJECT_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_11] = [Permissions.CAN_MARK_REVIEWED_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_12] = [Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_13] = [Permissions.CAN_FREEZE_DESIGN]
PERMISSION_SET_MAPPING[PermissionSet.DESIGN_APPROVER_ALL] = (
    PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_9]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_10]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_11]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_12]
    + PERMISSION_SET_MAPPING[PermissionSet.DESIGN_LEVEL_13]
)

# My Scope
PERMISSION_SET_MAPPING[PermissionSet.MY_SCOPE_LEVEL_1] = [Permissions.CAN_ACCESS_BOQ, Permissions.CAN_ACCESS_MY_SCOPE]
PERMISSION_SET_MAPPING[PermissionSet.MY_SCOPE_LEVEL_2] = PERMISSION_SET_MAPPING[PermissionSet.MY_SCOPE_LEVEL_1] + [
    Permissions.CAN_EDIT_BOQ,
    Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT,
    Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL,
]

# Incoming Order
PERMISSION_SET_MAPPING[PermissionSet.INCOMING_ORDER_LEVEL_1] = [
    Permissions.CAN_ACCESS_BOQ,
    Permissions.CAN_ACCESS_INCOMING_ORDER,
]


# Proposal for Client
PERMISSION_SET_MAPPING[PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_1] = [
    Permissions.CAN_ACCESS_BOQ,
    Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT,
]
PERMISSION_SET_MAPPING[PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_1
] + [Permissions.CAN_CREATE_PROPOSAL, Permissions.CAN_EDIT_PROPOSAL]
PERMISSION_SET_MAPPING[PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_3] = PERMISSION_SET_MAPPING[
    PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_2
] + [Permissions.CAN_SEND_PROPOSAL, Permissions.CAN_APPROVE_CLIENT_PROPOSAL]

# Outgoing Order
PERMISSION_SET_MAPPING[PermissionSet.OUTGOING_ORDER_LEVEL_1] = [
    Permissions.CAN_ACCESS_ORDER,
    Permissions.CAN_ACCESS_OUTGOING_ORDER,
    Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
]
PERMISSION_SET_MAPPING[PermissionSet.OUTGOING_ORDER_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.OUTGOING_ORDER_LEVEL_1
] + [Permissions.CAN_CREATE_OUTGOING_ORDER, Permissions.CAN_DELETE_OUTGOING_ORDER]
PERMISSION_SET_MAPPING[PermissionSet.OUTGOING_ORDER_LEVEL_3] = PERMISSION_SET_MAPPING[
    PermissionSet.OUTGOING_ORDER_LEVEL_2
] + [Permissions.CAN_SEND_OUTGOING_ORDER]
PERMISSION_SET_MAPPING[PermissionSet.OUTGOING_ORDER_LEVEL_4] = PERMISSION_SET_MAPPING[
    PermissionSet.OUTGOING_ORDER_LEVEL_3
] + [Permissions.CAN_CANCEL_OUTGOING_ORDER, Permissions.CAN_CANCEL_PO]

PERMISSION_SET_MAPPING[PermissionSet.OUTGOING_ORDER_LEVEL_5] = PERMISSION_SET_MAPPING[
    PermissionSet.OUTGOING_ORDER_LEVEL_4
] + [Permissions.CAN_CLOSE_ORDER]
# Proposal from Vendor
PERMISSION_SET_MAPPING[PermissionSet.PROPOSAL_FROM_VENDOR_LEVEL_1] = [
    Permissions.CAN_ACCESS_ORDER,
    Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
]
PERMISSION_SET_MAPPING[PermissionSet.PROPOSAL_FROM_VENDOR_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.PROPOSAL_FROM_VENDOR_LEVEL_1
] + [Permissions.CAN_APPROVE_PROPOSAL]

# Vendor Wise Scope
PERMISSION_SET_MAPPING[PermissionSet.VENDOR_WISE_SCOPE_LEVEL_1] = [
    Permissions.CAN_ACCESS_ORDER,
]

# Vendor Wise Progress
PERMISSION_SET_MAPPING[PermissionSet.VENDOR_WISE_PROGRESS_LEVEL_1] = [
    Permissions.CAN_ACCESS_ORDER,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
]

# Work Progress View Permissions
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_1] = [Permissions.CAN_ACCESS_WORK_PROGRESS]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_VIEW_ALL] = PERMISSION_SET_MAPPING[
    PermissionSet.PROGRESS_UPDATE_LEVEL_1
]

# Work Progress Update Permissions
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_2] = [Permissions.CAN_UPDATE_WORK_PROGRESS]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_3] = [Permissions.CAN_CAPTURE_FILE_IN_DPR]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_4] = [Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_5] = [Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_6] = [Permissions.CAN_GENERATE_PROGRESS_REPORT]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_7] = [Permissions.CAN_EXPORT_PROGRESS_REPORT]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_8] = [Permissions.CAN_MANAGE_DPR_SUBSCRIPTION]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_UPDATE_ALL] = (
    PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_2]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_3]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_4]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_5]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_6]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_7]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_8]
)


# Work Progress Edit Permissions
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_9] = [Permissions.CAN_DELETE_FILE_IN_DPR]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_10] = [Permissions.CAN_DELETE_PROGRESS_REPORT]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_11] = [Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_12] = [Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_13] = [Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE]
PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_EDIT_ALL] = (
    PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_9]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_10]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_11]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_12]
    + PERMISSION_SET_MAPPING[PermissionSet.PROGRESS_UPDATE_LEVEL_13]
)


# Snag
PERMISSION_SET_MAPPING[PermissionSet.SNAG_LEVEL_1] = [Permissions.CAN_ACCESS_SNAG, Permissions.CAN_LINK_SNAG_ITEMS]
PERMISSION_SET_MAPPING[PermissionSet.SNAG_LEVEL_2] = PERMISSION_SET_MAPPING[PermissionSet.SNAG_LEVEL_1] + [
    Permissions.CAN_CLOSE_SNAG
]

PERMISSION_SET_MAPPING[PermissionSet.INVENTORY_PERMISSION_LEVEL_1] = [Permissions.CAN_ACCESS_MATERIALS]
PERMISSION_SET_MAPPING[PermissionSet.INVENTORY_PERMISSION_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.INVENTORY_PERMISSION_LEVEL_1
] + [Permissions.CAN_CREATE_GRN, Permissions.CAN_UPDATE_STOCK_CONSUMPTION]
PERMISSION_SET_MAPPING[PermissionSet.INVENTORY_PERMISSION_LEVEL_3] = PERMISSION_SET_MAPPING[
    PermissionSet.INVENTORY_PERMISSION_LEVEL_2
] + [Permissions.CAN_CANCEL_STOCK_TRANSFER]

PERMISSION_SET_MAPPING[PermissionSet.INVENTORY_PERMISSION_LEVEL_4] = PERMISSION_SET_MAPPING[
    PermissionSet.INVENTORY_PERMISSION_LEVEL_3
] + [Permissions.CAN_APPROVE_STOCK_TRANSFER]


PERMISSION_SET_MAPPING[PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_1] = [
    Permissions.CAN_ACCESS_WORK_PROGRESS,
    Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE,
]
PERMISSION_SET_MAPPING[PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_1
] + [
    Permissions.CAN_UPDATE_PROJECT_ACTIVITY_SCHEDULE,
]
PERMISSION_SET_MAPPING[PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_3] = PERMISSION_SET_MAPPING[
    PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_2
] + [Permissions.CAN_EDIT_PROJECT_ACTIVITY_SCHEDULE]

PERMISSION_SET_MAPPING[PermissionSet.VENDOR_INVOICES_LEVEL_1] = [Permissions.CAN_ACCESS_VENDOR_INVOICES]

PERMISSION_SET_MAPPING[PermissionSet.VENDOR_PAYMENT_LEVEL_1] = [Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST]
PERMISSION_SET_MAPPING[PermissionSet.VENDOR_PAYMENT_LEVEL_2] = PERMISSION_SET_MAPPING[
    PermissionSet.VENDOR_PAYMENT_LEVEL_1
] + [Permissions.CAN_CREATE_VENDOR_PAYMENT_REQUEST]
PERMISSION_SET_MAPPING[PermissionSet.VENDOR_PAYMENT_LEVEL_3] = PERMISSION_SET_MAPPING[
    PermissionSet.VENDOR_PAYMENT_LEVEL_2
] + [Permissions.CAN_CREATE_PAYMENT_ENTRY]


KEY_TEXT_MAPPING = {
    SectionKey.PROJECT: _("Project Details"),
    SectionKey.RECCE: _("Recce"),
    SectionKey.DESIGN: _("Design"),
    SectionKey.MY_SCOPE: _("My Scope"),
    SectionKey.INCOMING_ORDER: _("Incoming Order"),
    SectionKey.PROPOSAL_FOR_CLIENT: _("Proposal for Client"),
    SectionKey.OUTGOING_ORDER: _("Vendor orders"),  # Outgoing Order
    SectionKey.PROPOSAL_FROM_VENDOR: _("Proposal from Vendor"),
    SectionKey.VENDOR_WISE_SCOPE: _("Vendor Wise Scope"),
    SectionKey.VENDOR_WISE_PROGRESS: _("Vendor Wise Progress"),
    SectionKey.PROGRESS_UPDATE: _("Scope Progress"),
    SectionKey.SNAG: _("Snags"),
    SectionKey.INVENTORY: _("Materials"),
    SectionKey.PROJECT_ACTIVITY_SCHEDULE: _("Activity Schedule"),
    SectionKey.VENDOR_INVOICES: _("Vendor Invoices"),
    SectionKey.VENDOR_PAYMENTS: _("Vendor Payments"),
    PermissionSet.PROJECT_LEVEL_1.value: _("View only basic project details"),  # View Basic
    PermissionSet.PROJECT_LEVEL_2.value: _("View all project details including attachments and cost"),  # View Full
    PermissionSet.PROJECT_LEVEL_3.value: _(" Edit all project details and project status"),  # Edit
    PermissionSet.RECCE_LEVEL_1.value: _("View only"),
    PermissionSet.RECCE_LEVEL_2.value: _("View, edit, upload and create"),
    PermissionSet.RECCE_LEVEL_3.value: _("View, edit, upload, create and approve"),  # done
    PermissionSet.DESIGN_LEVEL_1.value: _("View draft files"),
    PermissionSet.DESIGN_LEVEL_2.value: _("View reviewed files"),
    PermissionSet.DESIGN_LEVEL_3.value: _("View approved files"),
    PermissionSet.DESIGN_LEVEL_4.value: _("View rejected files"),
    PermissionSet.DESIGN_LEVEL_5.value: _("View client rejected files"),
    PermissionSet.DESIGN_VIEW_ALL.value: _("Select All"),
    PermissionSet.DESIGN_LEVEL_6.value: _("Can download"),
    PermissionSet.DESIGN_LEVEL_7.value: _("Can edit and upload"),
    PermissionSet.DESIGN_LEVEL_8.value: _("Can Delete"),
    PermissionSet.DESIGN_EDIT_ALL.value: _("Select All"),
    PermissionSet.DESIGN_LEVEL_9.value: _("Can Approve"),
    PermissionSet.DESIGN_LEVEL_10.value: _("Can Reject"),
    PermissionSet.DESIGN_LEVEL_11.value: _("Can Mark Reviewed"),
    PermissionSet.DESIGN_LEVEL_12.value: _("Can Mark Client Rejected"),
    PermissionSet.DESIGN_LEVEL_13.value: _("Can Freeze"),
    PermissionSet.DESIGN_APPROVER_ALL.value: _("Select All"),  # -- done
    PermissionSet.MY_SCOPE_LEVEL_1.value: _("View only"),
    PermissionSet.MY_SCOPE_LEVEL_2.value: _("View, add and edit"),
    PermissionSet.INCOMING_ORDER_LEVEL_1.value: _("View only"),
    PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_1.value: _("View only"),
    PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_2.value: _("View, create, edit and send"),
    PermissionSet.PROPOSAL_FOR_CLIENT_LEVEL_3.value: _("View, create, edit, send and approve"),
    PermissionSet.OUTGOING_ORDER_LEVEL_1.value: _("View only"),
    PermissionSet.OUTGOING_ORDER_LEVEL_2.value: _("View, create and delete"),
    PermissionSet.OUTGOING_ORDER_LEVEL_3.value: _("View, create, delete and send"),
    PermissionSet.OUTGOING_ORDER_LEVEL_4.value: _("View, create, delete, send, cancel order and cancel PO"),
    PermissionSet.OUTGOING_ORDER_LEVEL_5.value: _(
        "View, create, delete, send, cancel order, cancel PO and close order"
    ),
    PermissionSet.PROPOSAL_FROM_VENDOR_LEVEL_1.value: _("View only"),
    PermissionSet.PROPOSAL_FROM_VENDOR_LEVEL_2.value: _("View, modify and approve"),
    PermissionSet.VENDOR_WISE_SCOPE_LEVEL_1.value: _("View"),
    PermissionSet.VENDOR_WISE_PROGRESS_LEVEL_1.value: _("View"),
    PermissionSet.PROGRESS_UPDATE_VIEW_ALL.value: _("Select All"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_1.value: _("View scope progress and progress report"),
    PermissionSet.PROGRESS_UPDATE_UPDATE_ALL.value: _("Select All"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_2.value: _("Can update scope progress"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_3.value: _("Can capture image/video"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_4.value: _("Can upload image/video via gallery"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_5.value: _("Can change method of update"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_6.value: _("Can generate progress report"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_7.value: _("Can export progress report"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_8.value: _("Can manage DPR subscriptions"),
    PermissionSet.PROGRESS_UPDATE_EDIT_ALL.value: _("Select All"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_9.value: _("Can delete image/video"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_10.value: _("Can delete progress report"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_11.value: _("Can edit progress report settings"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_12.value: _("Can edit export report settings"),
    PermissionSet.PROGRESS_UPDATE_LEVEL_13.value: _("Can mark and unmark execution complete"),
    PermissionSet.SNAG_LEVEL_1.value: _("View and edit"),
    PermissionSet.SNAG_LEVEL_2.value: _("View, edit and close"),
    PermissionSet.INVENTORY_PERMISSION_LEVEL_1.value: _("View only"),
    PermissionSet.INVENTORY_PERMISSION_LEVEL_2.value: _("Can create GRN and update consumption"),
    PermissionSet.INVENTORY_PERMISSION_LEVEL_3.value: _(
        "Can create GRN, update consumption, and cancel stock transfer"
    ),
    PermissionSet.INVENTORY_PERMISSION_LEVEL_4.value: _(
        "Can create GRN, update consumption, cancel and approve stock transfer"
    ),
    PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_1.value: _("View only"),
    PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_2.value: _("View and update"),
    PermissionSet.PROJECT_ACTIVITY_SCHEDULE_LEVEL_3.value: _("View, update, create, edit and delete"),
    PermissionSet.VENDOR_INVOICES_LEVEL_1.value: _("View and Upload"),
    PermissionSet.VENDOR_PAYMENT_LEVEL_1.value: _("View only"),
    PermissionSet.VENDOR_PAYMENT_LEVEL_2.value: _("View and create"),
    PermissionSet.VENDOR_PAYMENT_LEVEL_3.value: _("View, create and add payment entry"),
}
