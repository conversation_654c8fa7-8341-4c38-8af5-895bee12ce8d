# Generated by Django 3.2.15 on 2025-07-01 06:08

import common.mixins
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0172_auto_20250613_0658'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationPurchaseOrderType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='core_organizationpurchaseordertype_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='core_organizationpurchaseordertype_deleted', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='purchase_order_types', to='core.organization')),
            ],
            options={
                'db_table': 'organization_po_types',
                'unique_together': {('organization', 'name')},
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
    ]
