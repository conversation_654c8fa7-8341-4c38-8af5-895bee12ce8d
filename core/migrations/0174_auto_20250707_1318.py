# Generated by Django 3.2.15 on 2025-07-07 13:18

import common.mixins
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0173_organizationconfig_embed_images_in_snag_excel_export'),
    ]

    operations = [
        migrations.CreateModel(
            name='TDSType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
            options={
                'db_table': 'core_tds_types',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.AddField(
            model_name='organization',
            name='code',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='code_uuid',
            field=models.UUIDField(blank=True, editable=False, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='verification_status',
            field=models.CharField(choices=[('not_verified', 'Not Verified'), ('verified', 'Verified'), ('rejected', 'Rejected'), ('pending', 'Pending'), ('in_progress', 'In Progress'), ('documents_required', 'Documents Required')], default='not_verified', max_length=50),
        ),
        migrations.AlterField(
            model_name='organizationconfigcorepermission',
            name='permission',
            field=models.CharField(choices=[('can_view_rate_contract', 'Can View Rate Contract'), ('can_view_element_library', 'Can View Element Library'), ('can_add_store_type', 'Can Add Store Type'), ('can_view_my_element_library', 'Can View My Element Library'), ('can_view_shared_element_library', 'Can View Shared Element Library'), ('can_create_element_library', 'Can Create Element Library'), ('can_edit_element_library_global_scope', 'Can Edit Element Library Global Scope'), ('can_edit_element_library_local_scope', 'Can Edit Element Library Local Scope'), ('can_access_google_sheet', 'Can Access Google Sheet'), ('can_create_project', 'Can Create Project'), ('can_view_client_rate', 'Can View Client Rate'), ('can_view_order_rate', 'Can View Order Rate'), ('can_view_schedule_sheet', 'Can View Schedule Sheet'), ('can_access_finance', 'Can Access Finance'), ('can_access_cost_compare', 'Can Access Cost Comparison'), ('can_access_running_spends', 'Can Access Running Spends'), ('can_share_project', 'Can Share Project'), ('can_access_requests', 'Can Access Requests'), ('can_access_approvals', 'Can Access Approvals'), ('can_create_client', 'Can Create Client'), ('can_capture_file_in_dpr', 'Can Capture File In DPR'), ('can_access_manage_vendor', 'Can Access Manage Vendor'), ('can_access_materials', 'Can Access Materials'), ('can_create_grn', 'Can Create GRN'), ('can_update_stock_consumption', 'Can Update Stock Consumption'), ('can_cancel_stock_transfer', 'Can Cancel Stock Transfer'), ('can_approve_stock_transfer', 'Can Approve Stock Transfer'), ('can_edit_manage_vendor', 'Can Edit Manage Vendor'), ('can_change_vendor_status', 'Can Change Vendor Status'), ('can_access_metabase_dashboard', 'Can Access Metabase Dashboard'), ('can_access_manage_client', 'Can Access Manage Client'), ('can_access_work_progress_materials', 'Can Access Work Progress Materials'), ('can_edit_client', 'Can Edit Client'), ('can_edit_approval_hierarchy', 'Can Edit Approval Hierarchy'), ('can_view_base_amount', 'Can View Base Amount'), ('can_view_service_charge', 'Can View Service Charge'), ('can_edit_service_charge', 'Can Edit Service Charge'), ('can_view_discount', 'Can View Discount'), ('can_edit_discount', 'Can Edit Discount'), ('can_access_boards', 'Can Access Boards'), ('can_view_all_boards', 'Can View All Boards'), ('can_edit_board', 'Can Edit Board'), ('can_view_lead_contacts', 'Can View Lead Contacts'), ('can_view_lead_companies', 'Can View Lead Companies'), ('can_access_insights', 'Can Access Insights'), ('can_create_dashboard_collections', 'Can Create Dashboard Collection'), ('can_view_budget_rate', 'Can View Budget Rate'), ('can_edit_org_user', 'Can Edit Org User'), ('can_edit_project_role', 'Can Edit Project Role'), ('can_view_all_projects', 'Can View All Projects'), ('can_receive_project_share_notifications', 'Can Receive Project Share Notifications'), ('can_access_org_settings', 'Can Access Org Settings'), ('can_access_module_configurations', 'Can Access Module Configurations'), ('can_edit_recce_template', 'Can Edit Recce Template'), ('can_access_other_expense', 'Can Access Other Expense'), ('can_view_all_expense_request', 'Can View All Expense Request'), ('can_view_all_tasks', 'Can View All Tasks'), ('can_view_scope_progress_on_project_list', 'Can View Scope Progress On Project List'), ('can_view_schedule_progress_on_project_list', 'Can View Schedule Progress On Project List'), ('can_assign_user_on_multiple_project_user_role', 'Can Assign User On Multiple Project User Role'), ('for_testing', 'For Testing'), ('can_access_vms', 'Can Access VMS'), ('can_edit_vms', 'Can Edit VMS'), ('can_approve_all_approval_requests', 'Can Approve All Approval Requests'), ('can_cancel_all_approval_requests', 'Can Cancel All Approval Requests'), ('can_create_recce_link', 'Can Create Recce Link'), ('can_view_design_module', 'Can View Design Module'), ('can_view_design_files', 'Can View Design Files'), ('can_view_draft_design_files', 'Can View Draft Design Files'), ('can_view_rejected_design_files', 'Can View Rejected Design Files'), ('can_view_reviewed_design_files', 'Can View Reviewed Design Files'), ('can_view_approved_design_files', 'Can View Approved Design Files'), ('can_view_client_rejected_files', 'Can View Client Rejected Files'), ('can_edit_design_files', 'Can Edit Design Files'), ('can_freeze_design', 'Can Freeze Design'), ('can_download_design_files', 'Can Download Design Files'), ('can_delete_design_files', 'Can Delete Design Files'), ('can_approve_design_files', 'Can Approve Design Files'), ('can_reject_design_files', 'Can Reject Design Files'), ('can_mark_reviewed_design_files', 'Can Mark Reviewed Design Files'), ('can_mark_client_rejected_files', 'Can Mark Client Rejected Files'), ('can_access_project_attachment', 'Can Access Project All Details'), ('can_access_project_custom_fields', 'Can Access Project Custom Fields'), ('can_edit_project', 'Can Edit Project'), ('can_access_recce', 'Can Access Recce'), ('can_edit_recce_data', 'Can Edit Recce Data'), ('can_upload_recce_files', 'Can Upload Recce Files'), ('can_approve_recce', 'Can Approve Recce'), ('can_access_boq', 'Can Access Boq'), ('can_edit_boq', 'Can Edit Boq'), ('can_assign_project_user', 'Can Assign Project User'), ('can_create_order', 'Can Create Outgoing Order'), ('can_send_order', 'Can Send Outgoing Order'), ('can_cancel_order', 'Can Cancel Outgoing Order'), ('can_delete_order', 'Can Delete Outgoing Order'), ('can_receive_project_notifications', 'Can Receive Project Notifications'), ('can_cancel_incoming_order', 'Can Cancel Incoming Order'), ('can_edit_proposal', 'Can Edit Proposal'), ('can_view_proposal_mail_box', 'Can View Proposal Mail Box'), ('can_send_proposal', 'Can Send Proposal'), ('can_approve_proposal', 'Can Approve Proposal'), ('can_receive_project_comment_notifications', 'Can Receive Project Comment Notifications'), ('can_access_consolidated_progress_report', 'Can Access Consolidated Progress Report'), ('can_access_export_boq_pdf', 'Can Access Export Boq PDF'), ('can_access_export_and_share_boq_pdf', 'Can Access Export And Share Boq PDF'), ('can_access_export_snag_pdf', 'Can Access Export Snag PDF'), ('can_access_export_grn_pdf', 'Can Access Export GRN PDF'), ('can_access_my_scope', 'Can Access My Scope'), ('can_access_incoming_order', 'Can Access Incoming Order'), ('can_access_proposal_for_client', 'Can Access Proposal For Client'), ('can_access_outgoing_order', 'Can Access Outgoing Order'), ('can_access_proposal_from_vendor', 'Can Access Proposal From Vendor'), ('can_access_vendor_wise_scope', 'Can Access Vendor Wise Scope'), ('can_access_vendor_wise_progress', 'Can Access Vendor Wise Progress'), ('can_receive_recce_creation_notifications', 'Can Receive Recce Creation Notifications'), ('can_receive_recce_start_notifications', 'Can Receive Recce Start Notifications'), ('can_receive_recce_submission_notifications', 'Can Receive Recce Submission Notifications'), ('can_receive_recce_update_notifications', 'Can Receive Recce Update Notifications'), ('can_receive_recce_approve_notifications', 'Can Receive Recce Approve Notifications'), ('can_receive_design_approve_notifications', 'Can Receive Design Approve Notifications'), ('can_receive_design_freeze_notifications', 'Can Receive Design Freeze Notifications'), ('can_receive_incoming_order_received_notifications', 'Can Receive Incoming Order Received Notifications'), ('can_receive_incoming_order_modified_notifications', 'Can Receive Incoming Order Modified Notifications'), ('can_receive_incoming_order_completed_notifications', 'Can Receive Incoming Order Completed Notifications'), ('can_receive_incoming_order_po_received_notifications', 'Can Receive Incoming Order PO Received Notifications'), ('can_receive_incoming_order_po_cancelled_notifications', 'Can Receive Incoming Order PO Cancelled Notifications'), ('can_receive_outgoing_order_sent_notifications', 'Can Receive Outgoing Order Sent Notifications'), ('can_receive_outgoing_order_completed_notifications', 'Can Receive Outgoing Order Completed Notifications'), ('can_receive_outgoing_order_po_sent_notifications', 'Can Receive Outgoing Order PO Sent Notifications'), ('can_receive_outgoing_order_po_cancelled_notifications', 'Can Receive Outgoing Order PO Cancelled Notifications'), ('can_receive_outgoing_order_modified_notifications', 'Can Receive Outgoing Order Modified Notifications'), ('can_receive_outgoing_order_new_invoice_uploaded_notifications', 'Can Receive Outgoing Order New Invoice Uploaded Notifications'), ('can_receive_outgoing_order_all_invoice_marked_uploaded_notifications', 'Can Receive Outgoing Order All Invoice Marked Uploaded Notifications'), ('can_access_snag', 'Can Access Snag'), ('can_link_snag_items', 'Can Link Snag Items'), ('can_receive_progress_report_notifications', 'Can Receive Progress Report Notifications'), ('can_receive_proposal_from_vendor_notifications', 'Can Receive Proposal from Vendor Notifications'), ('can_receive_proposal_rejected_notifications', 'Can Receive Proposal REJECTED Notifications'), ('can_receive_design_new_version_notification', 'Can Receive Design New Version Notification'), ('can_receive_design_new_version_post_freeze_notification', 'Can Receive Design New Version Post Freeze Notification'), ('can_receive_project_schedule_completed_notification', 'Can Receive Project Schedule Completed Notification'), ('can_receive_project_schedule_delayed_notification', 'Can Receive Project Schedule Delayed Notification'), ('can_receive_project_schedule_overdue_notification', 'Can Receive Project Schedule Overdue Notification'), ('can_receive_project_schedule_activities_deleted_notification', 'Can Receive Project Schedule Activities Deleted Notification'), ('can_receive_project_schedule_activities_assigned_notification', 'Can Receive Project Schedule Activities Assigned Notification'), ('can_preview_purchase_order', 'Can Preview Purchase Order'), ('can_update_project_status', 'Can Update Project Hold/Lost Status'), ('can_add_custom_boq_element', 'Can Add Custom BOQ Element'), ('can_import_boq_element_from_project', 'Can Import BOQ Element From Project'), ('can_import_boq_element_from_library', 'Can Import BOQ Element From Library'), ('can_access_order', 'Can Access Order'), ('can_create_incoming_order', 'Can Create Incoming Order'), ('can_create_proposal', 'Can Create Proposal'), ('can_close_order', 'Can Close Order'), ('can_upload_boq_element_using_excel', 'Can Upload BOQ Element Using Excel'), ('can_approve_client_proposal', 'Can Approve Client Proposal'), ('can_restrict_client_edit_proposal', 'Can Restrict Client Edit Proposal'), ('can_close_snag', 'Can Close Snag'), ('can_cancel_po', 'Can Cancel PO'), ('can_access_project_activity_schedule', 'Can Access Project Activity Schedule'), ('can_update_project_activity_schedule', 'Can Update Project Activity Schedule'), ('can_edit_project_activity_schedule', 'Can Edit Project Activity Schedule'), ('can_access_vendor_invoices', 'Can Access Vendor Invoices'), ('can_receive_order_cancel_notifications', 'Can Receive Outgoing Order Cancel Notifications'), ('can_receive_incoming_order_cancel_notifications', 'Can Receive Incoming Order Cancel Notifications'), ('can_access_client_payment_request', 'Can Access Client Payment Request'), ('can_access_vendor_payment_request', 'Can Access Vendor Payment Request'), ('can_create_vendor_payment_request', 'Can Create Vendor Payment Request'), ('can_create_payment_entry', 'Can Create Payment Entry'), ('can_create_expense_payment_entry', 'Can Create Expense Payment Entry'), ('can_archive_projects', 'Can Archive Projects'), ('can_mark_expense_closure', 'Can Mark Expense Closure'), ('can_receive_snag_notifications', 'Can Receive Snag Notifications'), ('can_configure_payment_terms_for_vendor_order', 'Can Configure Payment Terms For Vendor Order'), ('can_configure_other_terms_and_conditions_for_vendor_order', 'Can Configure Other Terms And Conditions For Vendor Order'), ('can_configure_term_and_conditions_for_quotation', 'Can Configure Terms And Conditions For Quotation'), ('can_configure_client_view', 'Can Configure Client View'), ('can_integrate_crm_with_meta', 'Can Integrate CRM With Meta'), ('can_compare_boq_vs_order', 'Can Compare BOQ Vs Order'), ('can_access_work_progress', 'Can Access Work Progress'), ('can_update_work_progress', 'Can Update Work Progress'), ('can_generate_progress_report', 'Can Generate Progress Report'), ('can_edit_generate_report_settings', 'Can Edit Generate Report Settings'), ('can_edit_export_report_settings', 'Can Edit Export Report Settings'), ('can_delete_progress_report', 'Can Delete Progress Report'), ('can_export_progress_report', 'Can Export Progress Report'), ('can_delete_file_in_dpr', 'Can Delete File In DPR'), ('can_mark_and_unmark_execution_complete', 'Can Mark And Unmark Execution Complete'), ('can_access_my_reports', 'Can Access My Reports'), ('can_change_progress_update_method', 'Can Change Progress Update Method'), ('can_manage_dpr_subscription', 'Can Manage DPR Subscription'), ('can_upload_gallery_file_in_dpr', 'Can Upload Gallery File In DPR'), ('can_edit_tds_amount', 'Can Edit TDS Amount')], max_length=100),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='permission',
            field=models.CharField(choices=[('can_view_rate_contract', 'Can View Rate Contract'), ('can_view_element_library', 'Can View Element Library'), ('can_add_store_type', 'Can Add Store Type'), ('can_view_my_element_library', 'Can View My Element Library'), ('can_view_shared_element_library', 'Can View Shared Element Library'), ('can_create_element_library', 'Can Create Element Library'), ('can_edit_element_library_global_scope', 'Can Edit Element Library Global Scope'), ('can_edit_element_library_local_scope', 'Can Edit Element Library Local Scope'), ('can_access_google_sheet', 'Can Access Google Sheet'), ('can_create_project', 'Can Create Project'), ('can_view_client_rate', 'Can View Client Rate'), ('can_view_order_rate', 'Can View Order Rate'), ('can_view_schedule_sheet', 'Can View Schedule Sheet'), ('can_access_finance', 'Can Access Finance'), ('can_access_cost_compare', 'Can Access Cost Comparison'), ('can_access_running_spends', 'Can Access Running Spends'), ('can_share_project', 'Can Share Project'), ('can_access_requests', 'Can Access Requests'), ('can_access_approvals', 'Can Access Approvals'), ('can_create_client', 'Can Create Client'), ('can_capture_file_in_dpr', 'Can Capture File In DPR'), ('can_access_manage_vendor', 'Can Access Manage Vendor'), ('can_access_materials', 'Can Access Materials'), ('can_create_grn', 'Can Create GRN'), ('can_update_stock_consumption', 'Can Update Stock Consumption'), ('can_cancel_stock_transfer', 'Can Cancel Stock Transfer'), ('can_approve_stock_transfer', 'Can Approve Stock Transfer'), ('can_edit_manage_vendor', 'Can Edit Manage Vendor'), ('can_change_vendor_status', 'Can Change Vendor Status'), ('can_access_metabase_dashboard', 'Can Access Metabase Dashboard'), ('can_access_manage_client', 'Can Access Manage Client'), ('can_access_work_progress_materials', 'Can Access Work Progress Materials'), ('can_edit_client', 'Can Edit Client'), ('can_edit_approval_hierarchy', 'Can Edit Approval Hierarchy'), ('can_view_base_amount', 'Can View Base Amount'), ('can_view_service_charge', 'Can View Service Charge'), ('can_edit_service_charge', 'Can Edit Service Charge'), ('can_view_discount', 'Can View Discount'), ('can_edit_discount', 'Can Edit Discount'), ('can_access_boards', 'Can Access Boards'), ('can_view_all_boards', 'Can View All Boards'), ('can_edit_board', 'Can Edit Board'), ('can_view_lead_contacts', 'Can View Lead Contacts'), ('can_view_lead_companies', 'Can View Lead Companies'), ('can_access_insights', 'Can Access Insights'), ('can_create_dashboard_collections', 'Can Create Dashboard Collection'), ('can_view_budget_rate', 'Can View Budget Rate'), ('can_edit_org_user', 'Can Edit Org User'), ('can_edit_project_role', 'Can Edit Project Role'), ('can_view_all_projects', 'Can View All Projects'), ('can_receive_project_share_notifications', 'Can Receive Project Share Notifications'), ('can_access_org_settings', 'Can Access Org Settings'), ('can_access_module_configurations', 'Can Access Module Configurations'), ('can_edit_recce_template', 'Can Edit Recce Template'), ('can_access_other_expense', 'Can Access Other Expense'), ('can_view_all_expense_request', 'Can View All Expense Request'), ('can_view_all_tasks', 'Can View All Tasks'), ('can_view_scope_progress_on_project_list', 'Can View Scope Progress On Project List'), ('can_view_schedule_progress_on_project_list', 'Can View Schedule Progress On Project List'), ('can_assign_user_on_multiple_project_user_role', 'Can Assign User On Multiple Project User Role'), ('for_testing', 'For Testing'), ('can_access_vms', 'Can Access VMS'), ('can_edit_vms', 'Can Edit VMS'), ('can_approve_all_approval_requests', 'Can Approve All Approval Requests'), ('can_cancel_all_approval_requests', 'Can Cancel All Approval Requests'), ('can_create_recce_link', 'Can Create Recce Link'), ('can_view_design_module', 'Can View Design Module'), ('can_view_design_files', 'Can View Design Files'), ('can_view_draft_design_files', 'Can View Draft Design Files'), ('can_view_rejected_design_files', 'Can View Rejected Design Files'), ('can_view_reviewed_design_files', 'Can View Reviewed Design Files'), ('can_view_approved_design_files', 'Can View Approved Design Files'), ('can_view_client_rejected_files', 'Can View Client Rejected Files'), ('can_edit_design_files', 'Can Edit Design Files'), ('can_freeze_design', 'Can Freeze Design'), ('can_download_design_files', 'Can Download Design Files'), ('can_delete_design_files', 'Can Delete Design Files'), ('can_approve_design_files', 'Can Approve Design Files'), ('can_reject_design_files', 'Can Reject Design Files'), ('can_mark_reviewed_design_files', 'Can Mark Reviewed Design Files'), ('can_mark_client_rejected_files', 'Can Mark Client Rejected Files'), ('can_access_project_attachment', 'Can Access Project All Details'), ('can_access_project_custom_fields', 'Can Access Project Custom Fields'), ('can_edit_project', 'Can Edit Project'), ('can_access_recce', 'Can Access Recce'), ('can_edit_recce_data', 'Can Edit Recce Data'), ('can_upload_recce_files', 'Can Upload Recce Files'), ('can_approve_recce', 'Can Approve Recce'), ('can_access_boq', 'Can Access Boq'), ('can_edit_boq', 'Can Edit Boq'), ('can_assign_project_user', 'Can Assign Project User'), ('can_create_order', 'Can Create Outgoing Order'), ('can_send_order', 'Can Send Outgoing Order'), ('can_cancel_order', 'Can Cancel Outgoing Order'), ('can_delete_order', 'Can Delete Outgoing Order'), ('can_receive_project_notifications', 'Can Receive Project Notifications'), ('can_cancel_incoming_order', 'Can Cancel Incoming Order'), ('can_edit_proposal', 'Can Edit Proposal'), ('can_view_proposal_mail_box', 'Can View Proposal Mail Box'), ('can_send_proposal', 'Can Send Proposal'), ('can_approve_proposal', 'Can Approve Proposal'), ('can_receive_project_comment_notifications', 'Can Receive Project Comment Notifications'), ('can_access_consolidated_progress_report', 'Can Access Consolidated Progress Report'), ('can_access_export_boq_pdf', 'Can Access Export Boq PDF'), ('can_access_export_and_share_boq_pdf', 'Can Access Export And Share Boq PDF'), ('can_access_export_snag_pdf', 'Can Access Export Snag PDF'), ('can_access_export_grn_pdf', 'Can Access Export GRN PDF'), ('can_access_my_scope', 'Can Access My Scope'), ('can_access_incoming_order', 'Can Access Incoming Order'), ('can_access_proposal_for_client', 'Can Access Proposal For Client'), ('can_access_outgoing_order', 'Can Access Outgoing Order'), ('can_access_proposal_from_vendor', 'Can Access Proposal From Vendor'), ('can_access_vendor_wise_scope', 'Can Access Vendor Wise Scope'), ('can_access_vendor_wise_progress', 'Can Access Vendor Wise Progress'), ('can_receive_recce_creation_notifications', 'Can Receive Recce Creation Notifications'), ('can_receive_recce_start_notifications', 'Can Receive Recce Start Notifications'), ('can_receive_recce_submission_notifications', 'Can Receive Recce Submission Notifications'), ('can_receive_recce_update_notifications', 'Can Receive Recce Update Notifications'), ('can_receive_recce_approve_notifications', 'Can Receive Recce Approve Notifications'), ('can_receive_design_approve_notifications', 'Can Receive Design Approve Notifications'), ('can_receive_design_freeze_notifications', 'Can Receive Design Freeze Notifications'), ('can_receive_incoming_order_received_notifications', 'Can Receive Incoming Order Received Notifications'), ('can_receive_incoming_order_modified_notifications', 'Can Receive Incoming Order Modified Notifications'), ('can_receive_incoming_order_completed_notifications', 'Can Receive Incoming Order Completed Notifications'), ('can_receive_incoming_order_po_received_notifications', 'Can Receive Incoming Order PO Received Notifications'), ('can_receive_incoming_order_po_cancelled_notifications', 'Can Receive Incoming Order PO Cancelled Notifications'), ('can_receive_outgoing_order_sent_notifications', 'Can Receive Outgoing Order Sent Notifications'), ('can_receive_outgoing_order_completed_notifications', 'Can Receive Outgoing Order Completed Notifications'), ('can_receive_outgoing_order_po_sent_notifications', 'Can Receive Outgoing Order PO Sent Notifications'), ('can_receive_outgoing_order_po_cancelled_notifications', 'Can Receive Outgoing Order PO Cancelled Notifications'), ('can_receive_outgoing_order_modified_notifications', 'Can Receive Outgoing Order Modified Notifications'), ('can_receive_outgoing_order_new_invoice_uploaded_notifications', 'Can Receive Outgoing Order New Invoice Uploaded Notifications'), ('can_receive_outgoing_order_all_invoice_marked_uploaded_notifications', 'Can Receive Outgoing Order All Invoice Marked Uploaded Notifications'), ('can_access_snag', 'Can Access Snag'), ('can_link_snag_items', 'Can Link Snag Items'), ('can_receive_progress_report_notifications', 'Can Receive Progress Report Notifications'), ('can_receive_proposal_from_vendor_notifications', 'Can Receive Proposal from Vendor Notifications'), ('can_receive_proposal_rejected_notifications', 'Can Receive Proposal REJECTED Notifications'), ('can_receive_design_new_version_notification', 'Can Receive Design New Version Notification'), ('can_receive_design_new_version_post_freeze_notification', 'Can Receive Design New Version Post Freeze Notification'), ('can_receive_project_schedule_completed_notification', 'Can Receive Project Schedule Completed Notification'), ('can_receive_project_schedule_delayed_notification', 'Can Receive Project Schedule Delayed Notification'), ('can_receive_project_schedule_overdue_notification', 'Can Receive Project Schedule Overdue Notification'), ('can_receive_project_schedule_activities_deleted_notification', 'Can Receive Project Schedule Activities Deleted Notification'), ('can_receive_project_schedule_activities_assigned_notification', 'Can Receive Project Schedule Activities Assigned Notification'), ('can_preview_purchase_order', 'Can Preview Purchase Order'), ('can_update_project_status', 'Can Update Project Hold/Lost Status'), ('can_add_custom_boq_element', 'Can Add Custom BOQ Element'), ('can_import_boq_element_from_project', 'Can Import BOQ Element From Project'), ('can_import_boq_element_from_library', 'Can Import BOQ Element From Library'), ('can_access_order', 'Can Access Order'), ('can_create_incoming_order', 'Can Create Incoming Order'), ('can_create_proposal', 'Can Create Proposal'), ('can_close_order', 'Can Close Order'), ('can_upload_boq_element_using_excel', 'Can Upload BOQ Element Using Excel'), ('can_approve_client_proposal', 'Can Approve Client Proposal'), ('can_restrict_client_edit_proposal', 'Can Restrict Client Edit Proposal'), ('can_close_snag', 'Can Close Snag'), ('can_cancel_po', 'Can Cancel PO'), ('can_access_project_activity_schedule', 'Can Access Project Activity Schedule'), ('can_update_project_activity_schedule', 'Can Update Project Activity Schedule'), ('can_edit_project_activity_schedule', 'Can Edit Project Activity Schedule'), ('can_access_vendor_invoices', 'Can Access Vendor Invoices'), ('can_receive_order_cancel_notifications', 'Can Receive Outgoing Order Cancel Notifications'), ('can_receive_incoming_order_cancel_notifications', 'Can Receive Incoming Order Cancel Notifications'), ('can_access_client_payment_request', 'Can Access Client Payment Request'), ('can_access_vendor_payment_request', 'Can Access Vendor Payment Request'), ('can_create_vendor_payment_request', 'Can Create Vendor Payment Request'), ('can_create_payment_entry', 'Can Create Payment Entry'), ('can_create_expense_payment_entry', 'Can Create Expense Payment Entry'), ('can_archive_projects', 'Can Archive Projects'), ('can_mark_expense_closure', 'Can Mark Expense Closure'), ('can_receive_snag_notifications', 'Can Receive Snag Notifications'), ('can_configure_payment_terms_for_vendor_order', 'Can Configure Payment Terms For Vendor Order'), ('can_configure_other_terms_and_conditions_for_vendor_order', 'Can Configure Other Terms And Conditions For Vendor Order'), ('can_configure_term_and_conditions_for_quotation', 'Can Configure Terms And Conditions For Quotation'), ('can_configure_client_view', 'Can Configure Client View'), ('can_integrate_crm_with_meta', 'Can Integrate CRM With Meta'), ('can_compare_boq_vs_order', 'Can Compare BOQ Vs Order'), ('can_access_work_progress', 'Can Access Work Progress'), ('can_update_work_progress', 'Can Update Work Progress'), ('can_generate_progress_report', 'Can Generate Progress Report'), ('can_edit_generate_report_settings', 'Can Edit Generate Report Settings'), ('can_edit_export_report_settings', 'Can Edit Export Report Settings'), ('can_delete_progress_report', 'Can Delete Progress Report'), ('can_export_progress_report', 'Can Export Progress Report'), ('can_delete_file_in_dpr', 'Can Delete File In DPR'), ('can_mark_and_unmark_execution_complete', 'Can Mark And Unmark Execution Complete'), ('can_access_my_reports', 'Can Access My Reports'), ('can_change_progress_update_method', 'Can Change Progress Update Method'), ('can_manage_dpr_subscription', 'Can Manage DPR Subscription'), ('can_upload_gallery_file_in_dpr', 'Can Upload Gallery File In DPR'), ('can_edit_tds_amount', 'Can Edit TDS Amount')], max_length=100),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['code'], name='idx_organization_code'),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['code_uuid'], name='idx_organization_code_uuid'),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['verification_status'], name='idx_org_verification_status'),
        ),
        migrations.AddField(
            model_name='country',
            name='tds',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to='core.tdstype'),
        ),
    ]
