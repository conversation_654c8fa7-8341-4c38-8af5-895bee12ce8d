from dataclasses import dataclass
from typing import Annotated, List, Optional

from authorization.domain.constants import Permissions
from common.entities import BaseNestedObject
from common.pydantic.base_model import BaseModel, BaseModelV2
from common.serializers import Hash<PERSON>d<PERSON>ield
from core.models import User


@dataclass(frozen=True)
class UserRoleData:
    user: User
    role_id: int


@dataclass(frozen=True)
class UserOrgData:
    user_id: int
    org_id: int


@dataclass
class OrganizationPaymentTermEntity:
    title: str
    description: list


@dataclass
class OrganizationPaymentTermInputEntity(OrganizationPaymentTermEntity):
    api_key: str


@dataclass
class OrganizationAddressUpdateData(BaseNestedObject):
    address_line_one: Optional[str]
    address_line_two: Optional[str]
    city: Optional[str]
    state: Optional[int]
    country: Optional[str]
    pincode: Optional[str]


@dataclass
class OrganizationBusinessCardUpdateData(BaseNestedObject):
    file: Optional[str]  # In case deleting the file, file and filename will be null
    file_name: Optional[str]


@dataclass
class OrganizationBasicDetailUpdateData:
    name: str
    business_cards: List[OrganizationBusinessCardUpdateData]
    addresses: List[OrganizationAddressUpdateData]


@dataclass
class OrganizationDocumentData(BaseNestedObject):
    file: Optional[str]  # In case deleting the file, file and filename will be null
    file_name: Optional[str]


@dataclass
class OrganizationPANUpdateData:
    number: str
    files: List[OrganizationDocumentData]


@dataclass
class OrganizationAadharFileData(BaseNestedObject):
    file: str
    file_name: str


@dataclass
class OrganizationAadharUpdateData:
    number: str
    files: List[OrganizationDocumentData]


@dataclass
class OrganizationMSMEFileUpdateData:
    file: str
    file_name: str


@dataclass
class OrganizationMSMEUpdateData:
    number: str
    files: List[OrganizationDocumentData]


@dataclass
class OrganizationGSTUpdateData(BaseNestedObject):
    number: Optional[str]
    file: Optional[str]
    file_name: Optional[str]
    state: Optional[int]


@dataclass
class OrganizationGSTData(BaseNestedObject):
    number: Optional[str]
    file: Optional[str]
    file_name: Optional[str]
    state: Optional[int]


@dataclass
class OrganizationKYCUpdateData:
    pan: OrganizationPANUpdateData
    aadhar: OrganizationAadharUpdateData
    msme: OrganizationMSMEUpdateData
    gst: List[OrganizationGSTUpdateData]


@dataclass
class OrganizationPocUpdateData:
    name: Optional[str]
    phone_number: Optional[str]
    email: Optional[str]


@dataclass
class OrganizationOtherDocumentData:
    file: str
    name: str
    tags: List[str]


class CountryData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str


class CurrencyData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    symbol: str
    code: str
    locale: str = "en-IN"


class TaxTypeData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    max_slab_percent: float


class TDSTypeData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    max_slab_percent: float


class TimezoneData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    locale: str


class OrganizationCountryConfigCacheData(BaseModel):
    currency: CurrencyData
    tax_type: TaxTypeData
    timezone: TimezoneData
    country: CountryData
    tds_type: Optional[TDSTypeData] = None


@dataclass
class BaseDataCacheEntity:
    pass


class OrgUserEntity(BaseModelV2):
    user_id: int
    org_id: int


class ProjectUserEntity(OrgUserEntity):
    project_id: int


class OrgUserPermissionEntity(BaseModelV2):
    permissions: list[Permissions]
