import datetime
import os
import re
import uuid
from uuid import uuid4

import structlog
from django.apps import apps
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import Q, UniqueConstraint
from django.utils import timezone
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _
from django_jsonform.models.fields import ArrayField
from phonenumber_field import modelfields
from timezone_field import TimeZoneField

from authentication.domain.services import email_login_bypass_token_generator
from authorization.domain.constants import Actions, Permissions
from common.choices import (
    CityChoices,
    CountryChoices,
    OrganizationType,
    OrganizationVerificationStatus,
    PermissionScope,
    ProjectUserRoleLevelChoices,
    RoleType,
    SectionKeyChoices,
    SourceChoices,
    StateChoices,
    VendorStatusChoices,
)
from common.constants import FILE_FIELD_MAX_LENGTH, SYSTEM_USER_ID, CustomFieldTypeChoices
from common.events.constants import Events
from common.exceptions import BaseValidationError
from common.helpers import get_upload_path
from common.mixins import BaseModelMixin, HashidsModelMixin, TableNameMixin
from common.models import (
    BaseHistory,
    BaseModel,
    CreateDeleteModel,
    CreateModel,
    CreateModelWithDateOverride,
    CreateUpdateDeleteModel,
    CreateUpdateModel,
    CustomFieldModel,
    UpdateDeleteModel,
    UpdateModel,
    UploadDeleteModel,
)
from common.validators import tax_percent_validator
from core.choices import (
    ExternalWebhookChoices,
    OrganizationDocumentChoices,
    PermissionLevelChoice,
)
from core.exception_handlers import handle_org_gst_number_exceptions
from core.managers import OrganizationAddressManager, OrganizationGstNumberManager
from core.querysets import (
    OrganizationDocumentQuerySet,
    OrganizationPaymentTermQuerySet,
    RoleQuerySet,
)
from core.tnc_config.data.choices import OrganizationTnCTypeChoice
from core.utils import get_current_date_in_timezone
from expense.data.querysets import ItemExpenseQuerySet
from microcontext.choices import MicroContextChoices

logger = structlog.get_logger(__name__)


class CoreHistory(BaseHistory):
    pass


class Country(CreateUpdateModel):
    uid_field_id: int | None
    uid_document_id: int | None

    name = models.CharField(max_length=255)
    flag = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    code = models.CharField(max_length=4)
    is_active = models.BooleanField(default=True)
    request_year = models.PositiveSmallIntegerField(default=2023)
    timezone = TimeZoneField(default="Asia/Kolkata")
    phone_number_length = models.PositiveSmallIntegerField(default=10)
    phone_number_regex = models.CharField(max_length=100, null=True, blank=True)
    uid_field = models.ForeignKey(
        "organization.OrganizationDocumentFieldConfig",
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name="+",
    )
    uid_document = models.ForeignKey(
        "organization.OrganizationDocumentConfig",
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name="+",
    )
    is_state_available = models.BooleanField(default=True)
    tds = models.ForeignKey("TDSType", on_delete=models.RESTRICT, related_name="+", null=True, blank=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.uid_field_id:
            field_obj = (
                apps.get_model("organization", "OrganizationDocumentFieldConfig")
                .objects.filter(id=self.uid_field_id)
                .select_related("document_config")
                .first()
            )
            if field_obj.type != CustomFieldTypeChoices.TEXT:
                raise ValidationError("uid field must be a text field")
            if field_obj.document_config.multiple_allowed is True:
                raise ValidationError("uid field must be single allowed")
        return super().save(*args, **kwargs)

    class Meta:
        db_table = "core_countries"


class State(BaseModel):
    name = models.CharField(max_length=100)
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="states")
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "core_states"
        unique_together = ("name", "country")


class City(BaseModel):
    name = models.CharField(max_length=100)
    state = models.ForeignKey(State, on_delete=models.RESTRICT, related_name="cities", null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="+", null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=True)

    def __str__(self):
        text = self.name
        if self.state:
            text += f", {self.state.name}"
        if self.country:
            text += f", {self.country.name}"
        return text

    class Meta:
        db_table = "core_cities"
        unique_together = ("name", "state")


class AddressBaseModel(BaseModel):
    header = models.CharField(max_length=250, null=True, blank=True)
    address_line_1 = models.TextField(null=True, blank=True)
    address_line_2 = models.TextField(null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.RESTRICT, null=True, blank=True)
    state = models.ForeignKey(State, on_delete=models.RESTRICT, null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, null=True, blank=True)
    zip_code = models.CharField(max_length=10, null=True, blank=True)

    # Problem: city, state, country all are queried separately
    @property
    def full_address(self):
        address = ""
        if self.address_line_1:
            address += self.address_line_1
        if self.address_line_2:
            address += f", {self.address_line_2}" if address else f"{self.address_line_2}"
        if self.city:
            address += f", {self.city.name}" if address else f"{self.city.name}"
        if self.state:
            address += f", {self.state.name}" if address else f"{self.state.name}"
        if self.zip_code:
            address += f", Zip Code: {self.zip_code}" if address else f"Zip Code: {self.zip_code}"
        if self.country:
            address += f", {self.country.name}" if address else f"{self.country.name}"
        return address

    class Meta:
        abstract = True


class User(AbstractUser, HashidsModelMixin, TableNameMixin, BaseModelMixin):
    default_project_role_id: int
    username_validator = UnicodeUsernameValidator()
    username = models.CharField(
        _("username"),
        max_length=150,
        unique=False,
        help_text=_("Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."),
        validators=[username_validator],
        error_messages={
            "unique": _("A user with that username already exists."),
        },
    )
    email = models.EmailField(_("email address"), null=True)
    phone_number = modelfields.PhoneNumberField(null=True)
    updated_at = models.DateTimeField(null=True, blank=True, default=None)
    photo = models.ImageField(max_length=2000, null=True, blank=True, upload_to=get_upload_path)
    org = models.ForeignKey("Organization", on_delete=models.RESTRICT, null=True, default=None)
    default_project_role = models.ForeignKey("Role", on_delete=models.RESTRICT, null=True, blank=True)
    updated_by = models.ForeignKey(
        "self",
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    deleted_by = models.ForeignKey(
        "self",
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    deleted_at = models.DateTimeField(null=True, blank=True)
    login_expired_at = models.DateTimeField(null=True, blank=True)
    is_verified = models.BooleanField(null=True, blank=True, default=None)
    timezone = models.ForeignKey("Timezone", on_delete=models.RESTRICT, null=True, blank=True, related_name="+")

    REQUIRED_FIELDS = []

    @property
    def uid(self):
        return urlsafe_base64_encode(force_bytes(self.hash_id))

    @property
    def token(self):
        # return default_token_generator.make_token(self)
        return email_login_bypass_token_generator.make_token(self)

    @property
    def created_at(self):
        return self.date_joined

    @property
    def name(self):
        return f"{self.first_name} {self.last_name}".strip() if self.org_id is not None else "Guest User"

    @property
    def organization(self):  # backward compatibility
        return self.organizations.all()

    @property
    def contact(self):
        return self.phone_number.as_e164 if self.phone_number else None

    @property
    def is_expired(self):
        if self.login_expired_at and self.login_expired_at < timezone.now():
            return True
        return False

    @property
    def is_allowed(self):
        if not self.is_expired and self.is_active:
            return True
        return False

    def __str__(self):
        return self.name.strip()

    def clean(self) -> None:
        self.email = self.email.lower() if self.email else None

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = uuid4().hex
        if self.is_staff:
            self.username = self.email
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:
            if "phone_number_unique" in str(e):
                raise ValidationError({"phone_number": "Phone number already exists"})
            if "email_unique" in str(e):
                raise ValidationError({"email": "Email already exists"})
            if "username_unique" in str(e):
                raise ValidationError({"email": "Email already exists"})
            logger.info("Unable to understand Integrity Error", message=str(e))
            raise ValidationError({"message": "Unable to process request at the moment"})

    class Meta(AbstractUser.Meta):
        swappable = "AUTH_USER_MODEL"
        constraints = [
            UniqueConstraint(
                name="email_unique",
                fields=["email"],
                condition=Q(deleted_by__isnull=True),
            ),
            UniqueConstraint(
                name="phone_number_unique",
                fields=["phone_number"],
                condition=Q(deleted_by__isnull=True),
            ),
            UniqueConstraint(
                name="username_unique",
                fields=["username"],
                condition=Q(deleted_by__isnull=True),
            ),
        ]


class Organization(models.Model, HashidsModelMixin, TableNameMixin, BaseModelMixin):
    country_id: int

    # storage_uuid = models.UUIDField(default=None, unique=False, null=True)
    storage_uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(_("name"), max_length=250)
    # TODO: remove is_client and is_vendor after checking for any direct and indirect usage
    is_client = models.BooleanField(default=False)
    is_vendor = models.BooleanField(default=False)
    users = models.ManyToManyField(
        User, through="OrganizationUser", related_name="organizations", through_fields=("organization", "user")
    )
    # TODO: Update type for all existing orgs and then remove null=True
    type = models.CharField(max_length=50, choices=OrganizationType.choices, null=True)
    logo = models.ImageField(max_length=2000, null=True, blank=True, default=None, upload_to=get_upload_path)

    pan_number = models.CharField(null=True, blank=True, unique=True, max_length=100)
    referral_org = models.ForeignKey("self", on_delete=models.RESTRICT, null=True, blank=True)
    referral_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    country = models.ForeignKey(
        "Country", on_delete=models.RESTRICT, related_name="+", default=1
    )  # 1 is for Country India
    system_user = models.ForeignKey(User, related_name="+", null=True, blank=True, on_delete=models.RESTRICT)
    app_user = models.ForeignKey(User, related_name="app_user", null=True, blank=True, on_delete=models.RESTRICT)
    code = models.CharField(null=True, blank=True, unique=True, max_length=100)
    code_uuid = models.UUIDField(null=True, blank=True, editable=False, unique=True)
    verification_status = models.CharField(
        max_length=50,
        choices=OrganizationVerificationStatus.choices,
        default=OrganizationVerificationStatus.NOT_VERIFIED,
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Marking Organization Inactive will log out all its users. Unselect this instead of deleting accounts.",
        # noqa
        verbose_name="active",
    )

    class Meta:
        indexes = [
            models.Index(fields=["code"], name="idx_organization_code"),
            models.Index(fields=["code_uuid"], name="idx_organization_code_uuid"),
            models.Index(fields=["verification_status"], name="idx_org_verification_status"),
        ]

    @property
    def logo_url(self):
        if hasattr(self, "logo") and self.logo:
            return self.logo.url
        return None

    def _generate_unique_code(self, requested_code=None):
        if requested_code:
            base_code = re.sub(r"[^A-Za-z0-9]", "", requested_code).lower()[:98]
            existing_org = Organization.objects.filter(code=base_code).exclude(pk=self.pk).first()
            if existing_org:
                raise ValidationError({"code": "Organization code already exists"})
        else:
            base_code = re.sub(r"[^A-Za-z0-9]", "", self.name or "").lower()[:98]

        code = base_code
        suffix = 1
        while Organization.objects.filter(code=code).exclude(pk=self.pk).exists():
            code = f"{base_code}{suffix}"
            suffix += 1
        return code

    def save(self, *args, **kwargs):
        if not self.code_uuid:
            self.code_uuid = uuid.uuid4()
        if not self.code or not self.pk:
            if self.code:
                self.code = self._generate_unique_code(self.code)
            else:
                self.code = self._generate_unique_code()
        if self.type == OrganizationType.CLIENT.value:
            self.is_vendor = False
            self.is_client = True
        elif self.type == OrganizationType.VENDOR.value:
            self.is_vendor = True
            self.is_client = False
        else:
            self.is_vendor = False
            self.is_client = False
        try:
            self.pan_number = self.pan_number.upper() if self.pan_number else None
            return super(Organization, self).save(*args, **kwargs)
        except IntegrityError as e:
            if "pan_number" in str(e):
                raise IntegrityError("Pan number already exists") from e
            raise e

    def __str__(self):
        return str(self.name)


class Role(CreateUpdateDeleteModel):
    organization = models.ForeignKey(Organization, related_name="roles", on_delete=models.RESTRICT)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=50, choices=OrganizationType.choices)  # TODO:  to remove, not used
    scope = models.CharField(max_length=50, choices=PermissionScope.choices)
    role_type = models.CharField(max_length=50, choices=RoleType.choices, default=RoleType.GENERIC)
    is_active = models.BooleanField(default=True)
    level = models.PositiveSmallIntegerField(choices=ProjectUserRoleLevelChoices.choices, null=True, default=None)

    objects = RoleQuerySet.as_manager()

    history = GenericRelation(CoreHistory)

    def save(self, *args, **kwargs):
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:
            if "core_roles_organization_id_name_ba8fe2f7_uniq" in str(e):
                raise ValidationError({"name": "Role with this name already exists"})
            raise e

    class Meta:
        db_table = "core_roles"
        unique_together = ["organization", "name", "role_type"]


class RolePermission(BaseModel):
    role_id: int

    role = models.ForeignKey(Role, on_delete=models.RESTRICT)
    permission = models.CharField(max_length=100, choices=Permissions.choices)

    class Meta:
        db_table = "core_role_permissions"
        unique_together = ["role", "permission"]


class OrganizationUser(UpdateModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, unique=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    joined_at = models.DateTimeField(auto_now_add=True)
    is_admin = models.BooleanField(default=False)
    role = models.ForeignKey(Role, on_delete=models.RESTRICT, null=True, blank=True)
    accessed_at = models.DateTimeField(null=True, blank=True)
    joined_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, related_name="users_invited")
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        super(OrganizationUser, self).save(*args, **kwargs)
        User.objects.filter(id=self.user_id).update(org_id=self.organization_id)

    def delete(self, *args, **kwargs):
        User.objects.filter(id=self.user_id).update(org_id=None)
        super(OrganizationUser, self).delete()

    def clean(self):
        errors = dict()
        if (
            self.role
            and not Role.objects.filter(
                id=self.role_id, organization_id=self.organization_id, scope=PermissionScope.CORE
            ).exists()
        ):
            errors["role_id"] = _("Invalid role.")
        if len(errors):
            raise ValidationError(errors)

    class Meta:
        unique_together = ["organization", "user"]


# TODO: deprecated function
def upload_to_local_name_file(instance, filename):
    upload_to = "store/local-name"
    ext = filename.split(".")[-1]
    filename = f"{uuid4().hex}.{ext}"
    return os.path.join(upload_to, filename)


class RoleAction(BaseModel):
    role = models.ForeignKey(Role, on_delete=models.RESTRICT)
    action = models.CharField(max_length=100, choices=Actions.choices)

    def __str__(self):
        return f"{self.action}"

    class Meta:
        db_table = "core_role_actions"
        unique_together = ["role", "action"]


class PmcClientMapping(BaseModel):
    pmc = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="pmc_client_mapping_pmc")
    client = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="pmc_client_mapping_client")

    class Meta:
        unique_together = ("pmc", "client")


class PmcVendorMapping(BaseModel):
    pmc = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="pmc_vendor_mapping_pmc")
    vendor = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="pmc_vendor_mapping_vendor")

    class Meta:
        unique_together = ("pmc", "vendor")


class OrganizationConfig(BaseModel):
    class OrderFlowChoices(models.TextChoices):
        DEFAULT = "default", _("Default")
        PO_FLOW = "po_flow", _("PO Flow")

    organization = models.OneToOneField(
        Organization, primary_key=True, on_delete=models.RESTRICT, related_name="config"
    )
    roles = models.ManyToManyField(Role, through="OrganizationConfigRole")
    order_cc_emails = ArrayField(models.EmailField(_("Order CC Emails")), default=list, blank=True)
    order_receiver_emails = ArrayField(models.EmailField(_("Order Receiver Emails")), default=list, blank=True)
    poc = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True)
    metabase_dashboard_id = models.IntegerField(null=True, blank=True)
    domain_name = models.CharField(max_length=255, null=True, blank=True)
    order_flow = models.CharField(max_length=50, choices=OrderFlowChoices.choices, default=OrderFlowChoices.DEFAULT)
    use_client_code = models.BooleanField(default=True)
    timezone = models.ForeignKey("Timezone", on_delete=models.RESTRICT, null=True, blank=True, related_name="+")
    currency = models.ForeignKey("Currency", on_delete=models.RESTRICT, null=True, blank=True, related_name="+")
    tax_type = models.ForeignKey("TaxType", on_delete=models.RESTRICT, null=True, blank=True, related_name="+")
    block_all_actions = models.BooleanField(
        default=False, verbose_name="Block all notifications"
    )  # TODO: implemented for notifications only as of now but can be done for all actions if needed
    is_phone_number_login_enabled = models.BooleanField(default=True, verbose_name="Can login using phone number")
    allowed_users = models.PositiveIntegerField(null=True, blank=True)
    subscription_end_date = models.DateField(null=True, blank=True)
    sales_poc = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, related_name="+")
    tam_poc = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, related_name="+")

    def clean(self) -> None:
        from core.selectors import fetch_default_org_timezone_obj

        errors = {}
        if self.poc and self.poc.org_id != self.organization_id:
            errors["poc"] = _("Poc must be in current organization")

        # Validation to check subscription end date cannot be set in the past
        org_timezone_str: str | None = getattr(self.timezone, "name", None)
        if org_timezone_str is None:
            org_timezone_obj = fetch_default_org_timezone_obj()
            org_timezone_str = org_timezone_obj.name
        today_date_of_org_timezone: datetime.date = get_current_date_in_timezone(org_timezone_str)
        if self.subscription_end_date and self.subscription_end_date < today_date_of_org_timezone:
            errors["subscription_end_date"] = _("Subscription end date cannot be in the past.")

        if len(errors):
            raise ValidationError(errors)
        return super().clean()


class OrganizationDomain(CreateModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="domains")
    domain_name = models.CharField(max_length=255, unique=True)

    class Meta:
        db_table = "organization_domains"


class OrganizationOrderPaymentTerm(CreateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="payment_terms")
    title = models.CharField(max_length=200, null=True, blank=True)
    # deprecated
    description = ArrayField(models.TextField(), default=list, blank=True)
    blocks = models.JSONField(default=list)
    type = models.CharField(
        max_length=150,
        choices=OrganizationTnCTypeChoice.choices,
        default=OrganizationTnCTypeChoice.ORDER_PAYMENT_TERM_AND_CONDITIONS.value,
    )
    is_active = models.BooleanField(default=True)

    objects = OrganizationPaymentTermQuerySet.as_manager()


class OrganizationConfigRole(BaseModel):
    organization_config = models.ForeignKey(OrganizationConfig, on_delete=models.RESTRICT)
    role = models.OneToOneField(Role, on_delete=models.RESTRICT, related_name="organization_config_role")
    is_visible = models.BooleanField(default=False, help_text="Visible in project list search")
    is_required = models.BooleanField(default=False, help_text="Required for project creation")
    is_included = models.BooleanField(default=False, help_text="Included in project list search")
    order_weight = models.IntegerField(default=0, help_text="Order weight of project user list")
    is_recce_role = models.BooleanField(default=False, help_text="Used in recce assignment")
    is_order_cc_role = models.BooleanField(default=False, help_text="User assigned on role get order CC Email")

    class Meta:
        unique_together = ("organization_config", "role")

    def clean(self):
        errors = dict()
        if self.role.scope != PermissionScope.PROJECT:
            errors["role"] = _("Invalid role! It's scope must be project.")
        if self.role.organization_id != self.organization_config_id:
            errors["role"] = _("Invalid role! Organization and role mismatch.")
        if len(errors):
            raise ValidationError(errors)
        return super().clean()


class OrganizationLegalEntity(BaseModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="legal_entities")
    name = models.CharField(max_length=250)

    class Meta:
        unique_together = (
            "organization",
            "name",
        )


class OrganizationAddress(AddressBaseModel, CreateModelWithDateOverride, UpdateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="addresses")
    address = models.TextField(null=True)  # deprecated
    address_line_one = models.CharField(max_length=100, null=True, default=None)
    address_line_two = models.CharField(max_length=100, null=True, default=None)
    city_data = models.CharField(choices=CityChoices.choices, null=True, max_length=50, default=None)
    state_data = models.PositiveSmallIntegerField(
        _("state"), default=StateChoices.NOT_SET, choices=StateChoices.choices, null=True
    )
    country_data = models.CharField(choices=CountryChoices.choices, null=True, max_length=20, default=None)
    pincode = models.CharField(max_length=6, null=True, default=None)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
        default=SYSTEM_USER_ID,
    )
    objects = OrganizationAddressManager()


class OrganizationConfigCorePermission(BaseModel):
    organization_config = models.ForeignKey(OrganizationConfig, on_delete=models.RESTRICT)
    permission = models.CharField(max_length=100, choices=Permissions.choices)

    class Meta:
        unique_together = ("organization_config", "permission")


# TODO : need to add constraints on  unique name with case insensitive after discussion.
class VendorOrgTag(CreateModel):
    organization = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="vendor_org_tags", null=True, blank=True
    )
    name = models.CharField(max_length=100)

    class Meta:
        db_table = "vendor_org_tags"


class FromToOrgMapping(CreateUpdateDeleteModel):
    org_from = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="from_orgs")  # Client
    org_to = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="to_orgs")  # Vendor

    vendor_status = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        default=VendorStatusChoices.ACTIVE.value,
    )
    invited_by_org = models.ForeignKey(Organization, related_name="+", on_delete=models.RESTRICT, null=True, blank=True)
    is_client_active = models.BooleanField(default=True)  # Client Active for Vendor
    # TODO: fields below are not in use. Remove this field after checking for any direct and indirect usage
    is_active = models.BooleanField(default=True)  # Vendor Active # this will be deprecated
    vendor_poc = models.ForeignKey(User, related_name="vendor_pocs", on_delete=models.RESTRICT, null=True, blank=True)
    vendor_poc_name = models.CharField(max_length=100, null=True, blank=True)
    vendor_poc_phone_number = modelfields.PhoneNumberField(null=True, blank=True)
    vendor_poc_email = models.EmailField(null=True, blank=True)
    client_poc_name = models.CharField(max_length=100, null=True, blank=True)
    client_poc_phone_number = modelfields.PhoneNumberField(null=True, blank=True)
    client_poc_email = models.EmailField(null=True, blank=True)
    client_poc = models.ForeignKey(User, related_name="client_pocs", on_delete=models.RESTRICT, null=True, blank=True)
    # ----------

    vendor_tag = models.ManyToManyField(VendorOrgTag, through="VendorTagMapping", related_name="vendor_tags")

    is_invited = models.BooleanField(default=False)
    is_client_invited = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
        null=True,
        default=None,
        blank=True,
    )

    class Meta:
        db_table = "core_from_to_mappings"
        indexes = [
            models.Index(
                fields=["org_from", "org_to", "id"],
                name="idx_from_to_mappings_to_id",
                condition=Q(deleted_at__isnull=True),
            )
        ]


class VendorTagMapping(BaseModel):
    cv_mapping = models.ForeignKey(FromToOrgMapping, on_delete=models.RESTRICT)
    vendor_org_tag = models.ForeignKey(VendorOrgTag, on_delete=models.RESTRICT)

    class Meta:
        db_table = "vendor_tag_mapping"
        verbose_name = "Vendor Tag"
        verbose_name_plural = "Vendor Tags"


class UserLocationHistory(CreateModel):
    location = models.PointField(verbose_name=_("location"))

    class Meta:
        db_table = "core_user_location_history"


class OneTimeUseToken(BaseModel):
    context = models.CharField(max_length=100)
    context_id = models.IntegerField()
    created_for = models.ForeignKey(User, on_delete=models.RESTRICT)
    ip = models.CharField(max_length=100, null=True, blank=True)
    used_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, related_name="+")
    is_active = models.BooleanField(default=True)
    used_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "core_one_time_use_token"


class OrganizationCustomFieldConfig(CustomFieldModel):
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    context = models.CharField(max_length=100, choices=MicroContextChoices.choices)


class UserData(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="+")
    approval_count = models.JSONField(null=True, default=None)

    class Meta:
        db_table = "core_user_data"


class UserLoginHistory(BaseModel):
    user = models.ForeignKey(User, related_name="login_history", on_delete=models.RESTRICT)
    source = models.CharField(max_length=7, choices=SourceChoices.choices)
    ip_address = models.GenericIPAddressField()
    login_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "core_user_login_histories"
        indexes = [
            models.Index(fields=["user", "-id"], include=["login_at"], name="idx_user_id_id_loginat"),
        ]


class OrganizationCallbackUrl(BaseModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="callback_urls")
    url = models.URLField(max_length=2000)
    fallback_email = models.EmailField()

    class Meta:
        db_table = "core_organization_callback_urls"


class WebhookCallbackUrl(BaseModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="webhook_urls")
    url = models.URLField(max_length=2000)
    fallback_email = models.EmailField()

    class Meta:
        db_table = "core_webhook_callback_urls"


class WebhookCallbackUrlEvent(BaseModel):
    callback_url = models.ForeignKey(WebhookCallbackUrl, on_delete=models.RESTRICT, related_name="webhook_events")
    event = models.CharField(max_length=100, choices=Events.choices)

    class Meta:
        db_table = "core_webhook_callback_url_events"
        unique_together = ("event", "callback_url")


class OrganizationCallbackUrlEvent(BaseModel):
    callback_url = models.ForeignKey(OrganizationCallbackUrl, on_delete=models.RESTRICT, related_name="events")
    event = models.CharField(max_length=100, choices=Events.choices)

    class Meta:
        db_table = "core_organization_callback_url_events"
        unique_together = ("event", "callback_url")


class SystemUserAccessHistory(BaseModel):
    accessed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
    )
    accessed_at = models.DateTimeField(auto_now_add=True)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    system_user = models.ForeignKey(User, on_delete=models.RESTRICT)

    def __str__(self):
        return self.accessed_by.name

    class Meta:
        db_table = "core_system_user_access_histories"


class SystemCallbackUrl(BaseModel):
    url = models.URLField(max_length=2000)
    fallback_email = models.EmailField()

    class Meta:
        db_table = "core_system_callback_urls"


class SystemCallbackUrlEvent(BaseModel):
    callback_url = models.ForeignKey(SystemCallbackUrl, on_delete=models.RESTRICT, related_name="events")
    event = models.CharField(max_length=100, choices=Events.choices)

    class Meta:
        db_table = "core_system_callback_url_events"
        unique_together = ("event", "callback_url")


class SystemWebhookCallbackUrl(BaseModel):
    url = models.URLField(max_length=2000)
    fallback_email = models.EmailField()

    class Meta:
        db_table = "core_system_webhook_callback_urls"


class SystemWebhookCallbackUrlEvent(BaseModel):
    callback_url = models.ForeignKey(
        SystemWebhookCallbackUrl, on_delete=models.RESTRICT, related_name="system_webhook_events"
    )
    event = models.CharField(max_length=100, choices=Events.choices)

    class Meta:
        db_table = "core_system_webhook_callback_url_events"
        unique_together = ("event", "callback_url")


class CommonDomain(CreateModel):
    domain_name = models.CharField(max_length=256, unique=True)

    class Meta:
        db_table = "common_domains"


class OrganizationGSTNumber(CreateUpdateDeleteModel):
    class GSTNumberAndStateUniqueException(BaseValidationError):
        pass

    organization = models.ForeignKey(Organization, related_name="organization_gst", on_delete=models.RESTRICT)
    gst_number = models.CharField(max_length=20, null=True, blank=True)
    gst_state = models.PositiveSmallIntegerField(
        max_length=20, choices=StateChoices.choices, null=True, blank=True, default=None
    )  # fill state field for old data and mark null=False
    file = models.FileField(null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=150, null=True)

    objects = OrganizationGstNumberManager()

    def clean(self):
        errors = dict()
        if self.gst_number:
            if OrganizationGSTNumber.objects.exclude(pk=self.pk).filter(gst_number=self.gst_number).exists():
                errors["gst_number"] = _("GST number already exists.")

        if len(errors):
            raise ValidationError(errors)

    def save(self, *args, **kwargs) -> None:
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            handle_org_gst_number_exceptions(e)

    class Meta:
        db_table = "organization_gst_number"
        constraints = [
            models.UniqueConstraint(
                fields=["gst_number", "gst_state"],
                condition=Q(deleted_at__isnull=True),
                name="organization_gst_number_state_wise_unique",
            ),
        ]


class OrganizationDocument(UploadDeleteModel):
    organization = models.ForeignKey(Organization, related_name="organization_document", on_delete=models.RESTRICT)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=150)
    type = models.CharField(max_length=20, choices=OrganizationDocumentChoices.choices)

    objects = OrganizationDocumentQuerySet.as_manager()

    @property
    def org_foreign_key(self):
        return "organization_id"

    class Meta:
        db_table = "organization_documents"


class PlatformVersion(BaseModel):
    platform = models.CharField(max_length=10, choices=SourceChoices.choices)
    version = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "platform_versions"


class Region(BaseModel):
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(Organization, related_name="+", on_delete=models.RESTRICT)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "regions"
        unique_together = ("name", "organization")


class RegionStateMapping(BaseModel):
    region = models.ForeignKey(Region, related_name="region_state_mapping", on_delete=models.RESTRICT)
    state = models.PositiveSmallIntegerField(_("state"), choices=StateChoices.choices)

    class Meta:
        db_table = "region_state_mapping"
        unique_together = ("region", "state")

    def clean(self):
        existing_mapping = RegionStateMapping.objects.filter(
            region__organization=self.region.organization, state=self.state
        ).exclude(pk=self.pk)
        if existing_mapping.exists():
            raise ValidationError(
                {"state": _("This organization and state combination already exists for another region.")}
            )


class GstSlab(BaseModel):
    gst_percent = models.DecimalField(max_digits=5, decimal_places=2, unique=True, validators=[tax_percent_validator])
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "gst_slabs"


class ItemExpenseTypeCategory(BaseModel):
    name = models.CharField(max_length=255, unique=True)
    image = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    is_default_type = models.BooleanField(default=False)

    class Meta:
        db_table = "core_item_expense_type_categories"
        verbose_name = "Item Expense Type Category"
        verbose_name_plural = "Item Expense Type Categories"


class ItemExpenseType(CreateUpdateModel):
    CUSTOM_INDEX = "item_expense_type_name_lower_case_unique"
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    icon = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    category = models.ForeignKey(
        ItemExpenseTypeCategory, on_delete=models.RESTRICT, related_name="item_expense_types", null=True, default=None
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.RESTRICT,
        default=None,
        null=True,
        blank=True,
    )
    archived_at = models.DateTimeField(null=True, blank=True, default=None)
    archived_by = models.ForeignKey(
        User,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
        default=None,
    )
    objects = ItemExpenseQuerySet.as_manager()

    def clean(self):
        errors = {}
        if (
            ItemExpenseType.objects.annotate_lower_name()
            .filter(name_lower=self.name.lower(), organization=self.organization)
            .exclude(pk=self.pk)
            .exists()
        ):
            errors = {"name": _("Cannot create multiple item types with the same name within the same organization.")}
        if len(errors) > 0:
            raise ItemExpenseType.ModelDataIntegrityException(errors)

    @property
    def is_archived(self):
        return self.archived_at is not None

    class Meta:
        db_table = "core_item_expense_types"
        verbose_name = "Item Expense Type"
        verbose_name_plural = "Item Expense Types"


class ProductionDrawingTag(BaseModel):
    name = models.CharField(max_length=50)
    type = models.CharField(max_length=10)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "core_production_drawing_tags"
        verbose_name = "Production Drawing Tag"
        verbose_name_plural = "Production Drawing Tags"


class UnitOfMeasurement(CreateModel):
    CUSTOM_INDEX = "unit_of_measurement_name_lower_case_unique"
    name = models.CharField(max_length=15)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "core_unit_of_measurements"
        verbose_name = "Unit of Measurement"
        verbose_name_plural = "Unit of Measurements"


class UnitOfMeasurementOrgMapping(BaseModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    uom = models.ForeignKey(UnitOfMeasurement, on_delete=models.RESTRICT)
    is_active = models.BooleanField(default=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "core_unit_of_measurement_org_mappings"
        unique_together = ["organization", "uom"]


class BasePOCModel(CreateUpdateDeleteModel):
    mapping = models.ForeignKey(FromToOrgMapping, on_delete=models.RESTRICT, related_name="+")
    name = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(null=True)
    phone_number = modelfields.PhoneNumberField(null=True)
    is_invited = models.BooleanField(default=False)
    is_primary = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, related_name="+")

    class Meta:
        abstract = True


class ExternalWebhook(BaseModel):
    type = models.CharField(choices=ExternalWebhookChoices.choices, max_length=100)
    url = models.URLField(max_length=2000)

    class Meta:
        db_table = "core_external_webhooks"


class TestingPhoneNumber(BaseModel):
    phone_number = modelfields.PhoneNumberField(unique=True)
    otp = models.CharField(max_length=6)
    is_active = models.BooleanField(default=True)

    def clean(self):
        if len(self.otp) != 6:
            raise ValidationError({"otp": "OTP length is incorrect"})
        try:
            int(self.otp)
        except ValueError:
            raise ValidationError({"otp": "OTP must be integer"})

    def __str__(self):
        return self.phone_number.as_e164

    class Meta:
        db_table = "core_testing_phone_numbers"


class UserTokenView(models.Model):
    id = models.BigIntegerField(primary_key=True)
    first_name = models.TextField()
    last_name = models.TextField()
    email = models.EmailField()
    phone_number = modelfields.PhoneNumberField()
    org_id = models.BigIntegerField(default=None)
    is_admin = models.BooleanField(default=False)
    org_type = models.TextField(default=None)
    key = models.TextField(default="")
    is_app_token = models.BooleanField(default=False)
    org_observer = models.BooleanField(default=False)
    org_name = models.TextField(default=None)
    country_name = models.TextField(default=None)

    class Meta:
        managed = False
        db_table = "core_user_token_view"


class Timezone(BaseModel):
    locale = models.CharField(max_length=50)
    tz = TimeZoneField(default="Asia/Kolkata")

    @property
    def name(self):
        return str(self.tz)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "core_timezones"


class Currency(BaseModel):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=3)
    symbol = models.CharField(max_length=5)
    locale = models.CharField(max_length=50, default="en-IN")

    def __str__(self):
        return self.name

    class Meta:
        db_table = "core_currencies"
        constraints = [
            models.UniqueConstraint(
                name="currency_name_code_locale_unique",
                fields=["name", "code", "locale"],
            ),
        ]


class TaxType(BaseModel):
    name = models.CharField(max_length=100)
    remark = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.remark})" if self.remark else f"{self.name}"

    class Meta:
        db_table = "core_tax_types"


class TaxSlab(BaseModel):
    tax_type = models.ForeignKey("TaxType", on_delete=models.RESTRICT, related_name="tax_slabs")
    tax_percent = models.DecimalField(max_digits=5, decimal_places=2, validators=[tax_percent_validator])
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "core_tax_slabs"
        unique_together = ["tax_type", "tax_percent"]


class TDSType(BaseModel):
    name = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.name}"

    class Meta:
        db_table = "core_tds_types"


class CountryTimezoneMapping(BaseModel):
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="country_timezone_mapping")
    timezone = models.ForeignKey(Timezone, on_delete=models.RESTRICT, related_name="country_timezone_mapping")
    is_default = models.BooleanField(default=False)

    class Meta:
        db_table = "core_country_timezone_mappings"
        unique_together = ("country", "timezone")


class CountryTaxMapping(BaseModel):
    tax_type = models.ForeignKey(TaxType, on_delete=models.RESTRICT, related_name="country_tax_mapping")
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="country_tax_mapping")
    is_default = models.BooleanField(default=False)
    max_slab_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    class Meta:
        db_table = "core_country_tax_mappings"
        unique_together = ("tax_type", "country")


class CountryCurrencyMapping(BaseModel):
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="country_currency_mapping")
    currency = models.ForeignKey(Currency, on_delete=models.RESTRICT, related_name="country_currency_mapping")
    is_default = models.BooleanField(default=False)

    class Meta:
        db_table = "core_country_currency_mappings"
        unique_together = ("country", "currency")


class OrganizationDefaultModuleSetting(BaseModel):
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="default_org_module_settings")
    module = models.CharField(choices=SectionKeyChoices.choices, max_length=100)
    is_client = models.BooleanField()

    class Meta:
        db_table = "core_default_org_module_settings"
        unique_together = ("org", "module", "is_client")


class OrganizationDefaultModuleLevelMapping(BaseModel):
    module_setting = models.ForeignKey(
        OrganizationDefaultModuleSetting, on_delete=models.RESTRICT, related_name="level_mappings"
    )
    level = models.CharField(choices=PermissionLevelChoice.choices, max_length=100)

    class Meta:
        db_table = "core_default_org_module_level_mapping"


class OrganizationBlockedAction(BaseModel):
    organization_config = models.ForeignKey(
        OrganizationConfig, on_delete=models.RESTRICT, related_name="blocked_actions"
    )
    action = models.CharField(choices=Actions.choices, max_length=100)

    class Meta:
        db_table = "core_organization_blocked_actions"
        unique_together = ["organization_config", "action"]


class CloudFile(models.Model):
    STATUS_CHOICES = [("initialized", "Initialized"), ("uploaded", "Uploaded")]

    project = models.ForeignKey(
        "project.Project", on_delete=models.RESTRICT, related_name="uploaded_cloud_files", null=True
    )
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="uploaded_cloud_files", null=True)
    upload_id = models.CharField(max_length=10000, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="initialized")
    user = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="uploaded_cloud_files")
    timestamp = models.DateTimeField(auto_now_add=True)
    size = models.PositiveBigIntegerField(help_text="File size in bytes")  # Store size in bytes

    def size_in_mb(self):
        """Returns file size in MB"""
        return round(self.size / (1024 * 1024), 2)

    def __str__(self):
        return f"{self.upload_id} - {self.status}"

    class Meta:
        db_table = "core_cloud_files"
