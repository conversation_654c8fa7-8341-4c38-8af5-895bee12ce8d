from dataclasses import dataclass
from typing import Optional

from core.tnc_config.domain.enums import OrganizationTnCTypeEnum


@dataclass(frozen=True)
class OrganizationTnCDataEntity:
    title: str
    block: list


@dataclass(frozen=True)
class OrganizationTermAndConditionDataRepoEntity(OrganizationTnCDataEntity):
    type: OrganizationTnCTypeEnum


@dataclass
class BaseTnCDataEntity:
    id: int
    title: str
    block: list


@dataclass
class PaymentTnCDataEntity(BaseTnCDataEntity):
    pass


@dataclass
class OtherTnCDataEntity(BaseTnCDataEntity):
    pass


@dataclass
class OrderTncDataEntity:
    payment_tnc: Optional[PaymentTnCDataEntity]
    other_tnc: Optional[OtherTnCDataEntity]


@dataclass
class OrgTncRepoEntity(OrderTncDataEntity):
    pass


@dataclass
class QuotationTnCDataEntity:
    term: Optional[PaymentTnCDataEntity]


@dataclass
class PurchaseOrderTypeEntity:
    id: int
    name: str
