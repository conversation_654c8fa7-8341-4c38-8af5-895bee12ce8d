from typing import Dict

import structlog
from celery import shared_task
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from phonenumber_field import serializerfields
from rest_framework import serializers
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_202_ACCEPTED,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_406_NOT_ACCEPTABLE,
)

from authorization.domain.constants import Permissions
from common.apis import BaseApi, BaseKeyProtectedApi, BaseOpenApi
from common.aws_s3_utils import S3UploadCompleteEntity, S3UploadStartEntity, complete_s3_upload, start_s3_upload
from common.choices import FilePathChoices, SourceChoices
from common.constants import RequestHeaders
from common.context_checker.context_checker_factory import ContextAllowedActionCheckerFactory
from common.entities import ContextCheckerData
from common.exceptions import ThumbnailGeneratorNotFound, ThumbnailNotGeneratedException
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer
from common.mixins import ExternalApiMixin
from common.serializers import (
    BaseDataclassSerializer,
    BaseKeyProtectedDataClassSerializer,
    BaseKeyProtectedSerializer,
    BaseModelSerializer,
    BaseSerializer,
    HashIdField,
)
from common.services import upload_file
from common.thumbnail_generator import BaseThumbnailGenerator, ThumbnailGeneratorFactory
from controlroom.data.models import ItemExpenseType
from core.caches import CountryListCache, OrganizationUOMCache, PlatformVersionCache
from core.entities import OrganizationPaymentTermInputEntity
from core.models import City, Organization, OrganizationConfig, OrganizationUser, State, User
from core.permissions import (
    ExternalOrgPermission,
    ExternalOrgPermissionV2,
    OrganizationActionPermission,
    OrgPermission,
)
from core.selectors import (
    OrganizationConfigRoleService,
    category_expense_item_type_fetch,
    get_country_currencies,
    get_country_tax_types,
    get_country_timezones,
    get_org_level_uoms_list,
    get_tax_slab_list,
    gst_slab_active_fetch,
    organization_active_expense_item_type_fetch,
    organization_user_fetch_active,
    organization_user_fetch_all,
    organization_user_role_fetch,
    production_drawing_tags_active_fetch,
    project_user_generic_role_fetch,
)
from core.serializers import (
    CityModelSerializer,
    CountryListSerializer,
    CurrencyModelSerializer,
    ItemExpenseTypeCategoryModelSerializer,
    ItemExpenseTypeDropdownListModelSerializer,
    ItemExpenseTypeModelSerializer,
    OrganizationDetailSerializer,
    OrganizationOrderPaymentTermSerializer,
    OrganizationUserModelSerializer,
    ProductionDrawingTagSerializer,
    RoleModelSerializer,
    TaxTypeModelSerializer,
    TimezoneInputSerializer,
    TimezoneModelSerializer,
    UserModelSerializer,
)
from core.services import (
    EventTrackerFactory,
    ItemExpenseTypeService,
    enable_disable_org_level_uom,
    organization_details,
    organization_payment_terms_create,
    organization_user_get_or_create,
    organization_user_update,
    platform_version_create,
    set_user_profile_to_analytics_platform,
    user_get_using_phone_number,
)
from core.tnc_config.data.choices import OrganizationTnCTypeChoice
from core.tnc_config.interface.serializers import OrganizationTnCModelSerializer
from core.utils import MetaBaseService, clean_timezone
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.getLogger(__name__)


class OrgBaseApi(BaseApi):
    REQUIRED_PERMISSIONS = None
    permission_classes = [IsAuthenticated, OrgPermission]


class ExternalOrgBaseApi(OrgBaseApi):
    permission_classes = [IsAuthenticated, ExternalOrgPermission]


class ExternalOrgBaseV2Api(BaseApi, ExternalApiMixin):
    permission_classes = [IsAuthenticated, ExternalOrgPermissionV2]


class UploadFileToCloudApi(BaseApi):
    class InputSerializer(BaseSerializer):
        file = serializers.FileField()
        path = serializers.CharField()
        thumbnail_required = serializers.BooleanField(default=False)
        project_id = HashIdField(allow_null=True, required=False)
        org_id = HashIdField(allow_null=True, required=False)

        class Meta:
            ref_name = "UploadFileToCloud"

    class OutputSerializer(BaseSerializer):
        path = serializers.CharField()

        class Meta:
            ref_name = "UploadFileOutputSerializer"

    input_serializer_class = InputSerializer
    parser_classes = (MultiPartParser,)

    @swagger_auto_schema(
        request_body=InputSerializer(),
        operation_id="upload_file_to_cloud",
        operation_summary="Upload file to cloud",
        responses={HTTP_200_OK: OutputSerializer()},
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data(copy=False)
        file = data.get("file")
        path = data.get("path")
        thumbnail_url = None

        org, project = self.get_org_and_project_for_org_separation(
            org_id=data.get("org_id"), project_id=data.get("project_id")
        )

        if data.get("thumbnail_required"):
            try:
                thumbnail_generator: BaseThumbnailGenerator = ThumbnailGeneratorFactory(file=file).get_generator()
                thumbnail_url = thumbnail_generator.generate(path=str(path) + "-thumbnail")
            except (ThumbnailGeneratorNotFound, ThumbnailNotGeneratedException):
                pass
        # to avoid "i/o operation on closed file" cloud upload is done after thumbnail generation
        # file_url = "https://ik.imagekit.io/91sqft/dev/user/c5228f158a2a4d4d94359aba2776b175.jpg"

        file_url = upload_file(
            file=file,
            destination_blob_name=path,
            filename=data.get("file").name,
            project_uuid=project.storage_uuid if project else None,
            org_uuid=org.storage_uuid if org else None,
        )
        return Response({"path": file_url, "thumbnail_url": thumbnail_url})


class OrgOtherTermListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationTnCModelSerializer):
        class Meta(OrganizationTnCModelSerializer.Meta):
            fields = ("id", "title", "is_active", "block")
            ref_name = "OrganizationPaymentTermListProposal"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="organization_order_other_term_list_api_proposal",
        operation_summary="organization order other term list API",
    )
    def get(self, request, org_id, *args, **kwargs):
        return Response(
            data=self.OutputSerializer(
                OrganizationConfigRoleService.get_active_tnc_list(
                    org_id=org_id,
                    type=OrganizationTnCTypeChoice.ORDER_OTHER_TERM_AND_CONDITIONS,
                ),
                many=True,
            ).data,
            status=HTTP_200_OK,
        )


class OrgPaymentTermsListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationTnCModelSerializer):
        class Meta(OrganizationTnCModelSerializer.Meta):
            ref_name = "PaymentTermsListApi"
            fields = ("id", "title", "is_active", "block")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="payment_terms_list",
        operation_summary="Payment terms list for user's organization",
    )
    def get(self, request, org_id, *args, **kwargs):
        # org_id = self.get_organization_id()
        instances = OrganizationConfigRoleService.get_active_tnc_list(
            org_id=org_id, type=OrganizationTnCTypeChoice.ORDER_PAYMENT_TERM_AND_CONDITIONS
        )
        return Response(
            self.OutputSerializer(
                instance=instances,
                many=True,
            ).data,
            status=HTTP_200_OK,
        )


class OrgPaymentTermsCreateApi(BaseKeyProtectedApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class InputSerializer(BaseKeyProtectedDataClassSerializer):
        description = serializers.ListField(child=serializers.CharField())

        title = serializers.CharField()

        class Meta(BaseKeyProtectedDataClassSerializer.Meta):
            ref_name = "OrganizationPaymentTermJsonSerializer"
            dataclass = OrganizationPaymentTermInputEntity

    class OutputSerializer(OrganizationOrderPaymentTermSerializer):
        class Meta(OrganizationOrderPaymentTermSerializer.Meta):
            ref_name = "PaymentTermsOutputSerializer"
            output_hash_id_fields = ()
            fields = ("id", "title", "is_active", "description")

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="payment_terms_create",
        operation_summary="Payment terms list for user's organization",
    )
    def post(self, request, org_id, *args, **kwargs):
        data: OrganizationPaymentTermInputEntity = self.validate_input_data()
        payment_term = organization_payment_terms_create(created_by_id=1, organization_id=org_id, data=data)

        return Response(
            self.OutputSerializer(
                instance=payment_term,
            ).data,
            status=HTTP_200_OK,
        )


class OrgPaymentTermDescriptionListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationOrderPaymentTermSerializer):
        class Meta(OrganizationOrderPaymentTermSerializer.Meta):
            ref_name = "PaymentTermDescriptionListApi"
            fields = (
                "id",
                "description",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="payment_term_description_list",
        operation_summary="Payment term description list for user's organization",
    )
    def get(self, request, payment_term_id, org_id, *args, **kwargs):
        instances = OrganizationConfigRoleService.get_payment_term_description_list(
            org_id=org_id, payment_term_id=payment_term_id
        ).first()
        return Response(
            self.OutputSerializer(instance=instances).data,
            status=HTTP_200_OK,
        )


class ProjectRoleListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(RoleModelSerializer):
        class Meta(RoleModelSerializer.Meta):
            ref_name = "ProjectRoleListOutput"
            fields = ("id", "name", "is_required", "order_weight")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_role_list",
        operation_summary="Project role list for user's organization",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        instances = (
            project_user_generic_role_fetch(org_id=org_id).order_by("name").prefetch_related("organization_config_role")
        )
        return Response(
            self.OutputSerializer(instance=instances, many=True, context={"org_id": org_id}).data, status=HTTP_200_OK
        )


class OrganizationUserAddApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        first_name = serializers.CharField(max_length=30)
        last_name = serializers.CharField(max_length=30)
        phone_number = serializerfields.PhoneNumberField()
        is_admin = serializers.BooleanField()

        class Meta:
            ref_name = "AddOrganizationUserInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: """{"message": str }"""},
        operation_id="add_organization_user",
        operation_summary="Add user in the organization",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        validated_data = self.validate_input_data()
        org_id = self.get_organization_id()
        is_admin = validated_data.get("is_admin")
        user = user_get_using_phone_number(
            phone_number=validated_data.get("phone_number"),
            first_name=validated_data.get("first_name"),
            last_name=validated_data.get("last_name"),
            create=True,
        )
        organization_user_get_or_create(user_id=user.pk, org_id=org_id, is_admin=is_admin, joined_by_id=request.user.pk)

        return Response({"message": _("User Added Successfully")}, status=HTTP_201_CREATED)


class OrganizationUserListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationUserModelSerializer):
        class Meta(OrganizationUserModelSerializer.Meta):
            ref_name = "OrganizationUserListOutput"
            fields = ("id", "name", "phone_number", "is_active", "is_admin")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="organization users list  ",
        operation_summary="organization users list",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        instances = organization_user_fetch_all(org_id=org_id).select_related("user")
        return Response(self.OutputSerializer(instance=instances, many=True).data, status=HTTP_200_OK)


class ProjectUserSuggestionListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationUserModelSerializer):
        id = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_id(self, obj):
            return obj.user_id

        class Meta(OrganizationUserModelSerializer.Meta):
            ref_name = "ProjectUserSuggestionListOutput"
            fields = ("id", "name", "photo", "phone_number", "default_project_role")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_user_suggestion_list",
        operation_summary="Project user suggestion list for current user's organization",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        instances = (
            organization_user_fetch_active(org_id=org_id)
            .exclude(user__deleted_at__isnull=False)
            .order_by("user__first_name", "user__last_name")
            .select_related("user")
        )
        return Response(self.OutputSerializer(instance=instances, many=True).data, status=HTTP_200_OK)


class ProjectUserRolePrefilledListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        class RoleSerializer(RoleModelSerializer):
            class Meta(RoleModelSerializer.Meta):
                fields = ("id", "name", "is_required")

        class UserSerializer(UserModelSerializer):
            class Meta(UserModelSerializer.Meta):
                fields = ("id", "name", "photo", "phone_number")

        class Meta:
            ref_name = "ProjectUserRolePrefilledListOutput"
            fields = ("role", "user")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_user_role_prefilled_list",
        operation_summary="Project user role prefilled list",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        roles = OrganizationConfigRoleService.project_prefilled_role_list(org_id=org_id)
        return Response(
            [
                {
                    "user": self.OutputSerializer.UserSerializer(request.user).data,
                    "role": self.OutputSerializer.RoleSerializer(role, context={"org_id": org_id}).data,
                }
                for role in roles
            ],
            status=HTTP_200_OK,
        )


class OrganizationUserChangeStatusApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        id = serializers.CharField()
        is_active = serializers.BooleanField()

        class Meta:
            input_hash_id_fields = ("id",)
            ref_name = "change_organization_user_status"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "message"},
        operation_id="change_organization_user_status",
        operation_summary="Set an organization user active-inactive",
    )
    def post(self, request, *args, **kwargs):
        validated_data = self.validate_input_data()

        instance = get_object_or_404(OrganizationUser, pk=validated_data.get("id"))

        organization_user_update(instance=instance, data=validated_data, updated_by_id=request.user.pk)

        return Response({"message": "status updated"}, status=HTTP_200_OK)


class UploadFileToCloudV2Api(BaseApi):
    class InputSerializer(BaseSerializer):
        file = serializers.FileField()
        path = serializers.ChoiceField(choices=FilePathChoices.choices)
        project_id = HashIdField(allow_null=True, required=False)
        org_id = HashIdField(allow_null=True, required=False)
        thumbnail_required = serializers.BooleanField(default=False)

        class Meta:
            ref_name = "UploadFileToCloudV2ApiInput"

    class OutputSerializer(BaseSerializer):
        path = serializers.CharField()

        class Meta:
            ref_name = "UploadFileToCloudV2ApiOutput"

    parser_classes = (MultiPartParser,)

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="upload_file_to_cloud_v2",
        operation_summary="Upload files to cloud Version 2",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data(copy=False)
        file = data.get("file")
        path = data.get("path")
        thumbnail_url = None

        org, project = self.get_org_and_project_for_org_separation(
            org_id=data.get("org_id"), project_id=data.get("project_id")
        )

        if data.get("thumbnail_required"):
            try:
                thumbnail_generator: BaseThumbnailGenerator = ThumbnailGeneratorFactory(file=file).get_generator()
                thumbnail_url = thumbnail_generator.generate(path=str(path) + "-thumbnail")
            except (ThumbnailGeneratorNotFound, ThumbnailNotGeneratedException):
                pass
        # file_url = "https://ik.imagekit.io/rdashstage/organization/ee27e501596a40688dea1ffc9d57d538.jpeg"
        file_url = upload_file(
            file=data.get("file"),
            destination_blob_name=data.get("path"),
            filename=data.get("file").name,
            project_uuid=project.storage_uuid if project else None,
            org_uuid=org.storage_uuid if org else None,
        )
        return Response({"path": file_url, "thumbnail_url": thumbnail_url})


class StartFileUploadToCloudApi(BaseApi):
    class InputSerializer(BaseSerializer):
        path = serializers.ChoiceField(choices=FilePathChoices.choices)
        file_size = serializers.IntegerField()  # bytes
        file_name = serializers.CharField()  # TODO: validation left
        project_id = HashIdField(allow_null=True, required=False)
        org_id = HashIdField(allow_null=True, required=False)
        number_of_parts_from_client = serializers.IntegerField(allow_null=True, required=False)

        class Meta:
            ref_name = "StartFileUploadToCloudApiInput"

    class OutputSerializer(BaseDataclassSerializer):
        class Meta:
            dataclass = S3UploadStartEntity
            ref_name = "StartFileUploadToCloudApiOutput"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="start_file_upload",
        operation_summary="Start chunked file upload to cloud",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        org_id = data.get("org_id")
        project_id = data.get("project_id")

        if project_id:
            org, project = self.get_org_and_project_for_org_separation(project_id=data.get("project_id"))
        else:
            if not org_id:
                org_id = self.get_organization_id()
            org, project = self.get_org_and_project_for_org_separation(org_id=org_id)

        s3_upload_object: S3UploadStartEntity = start_s3_upload(
            folder_path=data.get("path"),
            file_name=data.get("file_name"),
            file_size=data.get("file_size"),
            project=project,
            user=self.get_user(),
            org=org,
            number_of_parts_from_client=data.get("number_of_parts_from_client"),
        )
        logger.info("s3_upload_object", data=s3_upload_object)
        return Response(self.OutputSerializer(s3_upload_object).data)


class CompleteFileUploadToCloudApi(BaseApi):
    class InputSerializer(BaseDataclassSerializer):
        class Meta:
            dataclass = S3UploadCompleteEntity
            ref_name = "CompleteFileUploadToCloudApiInput"

    class OutputSerializer(BaseSerializer):
        path = serializers.CharField()

        class Meta:
            ref_name = "CompleteFileUploadToCloudApiOutput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="complete_file_upload",
        operation_summary="Complete chunked file upload to cloud",
    )
    def post(self, request, *args, **kwargs):
        data: S3UploadCompleteEntity = self.validate_input_data()
        url = complete_s3_upload(upload_complete_entity=data)
        return Response({"path": url})


class FirebaseDynamicLinkAssetLinkApi(BaseOpenApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="firebase_dynamic_link_asset_link",
        operation_summary="Firebase Dynamic Link Asset Link",
    )
    def get(self, request, *args, **kwargs):
        return JsonResponse(settings.FIREBASE_ASSET_LINKS, safe=False)


class OrganizationUserRoleListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(RoleModelSerializer):
        id = HashIdField()

        class Meta(RoleModelSerializer.Meta):
            ref_name = "OrganizationUserRoleListApiOutput"
            fields = ("id", "name")
            output_hash_id_fields = ()

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="organization_user_role_list",
        operation_summary="Organization user role list",
    )
    def get(self, request, *args, **kwargs):
        roles = organization_user_role_fetch(org_id=self.get_organization_id(), user=request.user)
        return Response(self.OutputSerializer(roles, many=True).data, status=HTTP_200_OK)


class MetabaseAuthenticationApi(BaseApi):
    class InputSerializer(BaseSerializer):
        card_id = serializers.IntegerField()
        metabase_query = serializers.JSONField()

        class Meta:
            ref_name = "MetabaseAuthenticationApiInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="metabase_authentication",
        operation_summary="Metabase Authentication",
    )
    def post(self, request, *args, **kwargs):
        data: Dict = self.validate_input_data()
        logger.info("MetabaseAuthenticationApi", request_body=data)
        try:
            metabase_response = MetaBaseService.request(query=data.get("metabase_query"), card_id=data.get("card_id"))
        except Exception as e:
            logger.info("MetabaseAuthenticationApi", error=str(e))
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(metabase_response, status=HTTP_201_CREATED)


class MetabaseDashboardApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="metabase_dashboard",
        operation_summary="Metabase Dashboard",
    )
    def get(self, request, *args, **kwargs):
        try:
            dashboard_id = OrganizationConfig.objects.get(
                organization_id=self.get_organization_id()
            ).metabase_dashboard_id
            if dashboard_id is None:
                return Response(status=HTTP_404_NOT_FOUND)
        except OrganizationConfig.DoesNotExist:
            return Response(status=HTTP_404_NOT_FOUND)
        dashboard_url = MetaBaseService.get_dashboard_url(dashboard_id=dashboard_id)
        return Response({"dashboard_url": dashboard_url}, status=HTTP_200_OK)


class HashIdConverterApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        api_key = serializers.CharField()
        ids = serializers.ListField(child=serializers.IntegerField())

        @staticmethod
        def validate_api_key(value):
            if value != settings.RECCE_RATING_UPDATE_KEY:
                raise serializers.ValidationError(_("Api key is not valid."))

        class Meta:
            ref_name = "HashIdConverterInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="hash_id_converter",
        operation_summary="Convert integers to hash ids",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        response = {i: HashIdConverter.encode(i) for i in data.get("ids")}
        return Response(response, status=HTTP_200_OK)


class HashIdDecoderApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        api_key = serializers.CharField()
        ids = serializers.ListField(child=serializers.CharField())

        @staticmethod
        def validate_api_key(value):
            if value != settings.RECCE_RATING_UPDATE_KEY:
                raise serializers.ValidationError(_("Api key is not valid."))

        class Meta:
            ref_name = "HashIdDecodeInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="hash_id_decoder",
        operation_summary="Convert hash ids to integers",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        response = {i: HashIdConverter.decode(i) for i in data.get("ids")}
        return Response(response, status=HTTP_200_OK)


class SetUserProfileToAnalyticsPlatformApi(BaseApi):
    class InputSerializer(BaseSerializer):
        additional_fields = serializers.DictField(required=False, default={})

        class Meta:
            ref_name = "SetUserProfileApi"

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="analytics_set_user_profile",
        operation_summary="Set User Profile to the Analytics Platform",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        if not settings.IS_EVENT_ANALYTICS_ENABLED:
            self.set_response_message("Analytics disabled for environments other than production")
            return Response(status=HTTP_406_NOT_ACCEPTABLE)
        elif self.get_organization_id() not in [25824]:
            self.set_response_message("Not forwarded to analytics platform")
            return Response(status=HTTP_200_OK)
        user: User = self.get_user()
        data: Dict = self.validate_input_data()
        set_user_profile_to_analytics_platform(user=user, additional_fields=data.get("additional_fields", {}))
        self.set_response_message("Successfully set user profile to analytics platform")

        return Response(status=HTTP_200_OK)


class SendEventsForAnalyticsApi(BaseApi):
    class InputSerializer(BaseSerializer):
        class EventSerializer(BaseSerializer):
            event_name = serializers.CharField()
            event_properties = serializers.DictField(required=False, default={})

            class Meta:
                ref_name = "EventSerializerInput"

        events = EventSerializer(many=True)

        class Meta:
            ref_name = "AnalyticsSendEventsInput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="analytics_send_events",
        operation_summary="Send Events to Analytics Platform",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        if not settings.IS_EVENT_ANALYTICS_ENABLED:
            self.set_response_message("Analytics disabled for environments other than production")
            return Response(status=HTTP_406_NOT_ACCEPTABLE)
        elif self.get_organization_id() not in [25824]:
            self.set_response_message("Not forwarded to analytics platform")
            return Response(status=HTTP_200_OK)
        data: Dict = self.validate_input_data()
        user_id = self.get_user_id()

        et = EventTrackerFactory.get_event_tracker()
        headers: Dict = self.get_request_headers()
        bypass_user_id = self.get_bypass_user_id()
        platform = self.get_platform()
        client_version = headers.get(RequestHeaders.APP_VERSION) or headers.get(RequestHeaders.OLD_CLIENT_VERSION)
        device_os = headers.get(RequestHeaders.DEVICE_MODEL) or headers.get(RequestHeaders.OLD_DEVICE_OS)
        device_version = headers.get(RequestHeaders.DEVICE_VERSION) or headers.get(RequestHeaders.OLD_DEVICE_VERSION)
        default_properties = {
            "platform": platform,
            "client_version": client_version,
            "device_os": device_os,
            "device_version": device_version,
            "bypass_user_id": bypass_user_id,
        }
        for event in data.get("events", []):
            event["event_properties"].update(default_properties)
            et.track(user_id, event["event_name"], event["event_properties"])

        self.set_response_message("Successfully sent events to analytics platform")

        return Response(status=HTTP_200_OK)


class CountryListApi(BaseOpenApi):
    """
    If the user is not logged in, then the countries are sorted based on the timezone
    else the countries are sorted based on the user's timezone
    """

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: CountryListSerializer(many=True)},
        operation_id="country_list_api",
        operation_summary="Country List API ",
    )
    def get(self, request, *args, **kwargs):
        timezone = self.validate_data(
            TimezoneInputSerializer,
            {
                "timezone": self.get_request_header(RequestHeaders.TIMEZONE)
                or self.get_request_header(RequestHeaders.OLD_TIMEZONE)
            },
            context=self.get_input_context(),
        ).get("timezone")

        if hasattr(self.request, "user") and self.request.user.is_authenticated:
            timezone = clean_timezone(self.get_user_timezone().zone)

        countries = CountryListCache.get()
        countries = sorted(
            countries,
            key=lambda country_dict: str(country_dict["timezone"]) == timezone,
            reverse=True,
        )
        return Response(
            CountryListSerializer(countries, many=True).data,
            status=HTTP_200_OK,
        )


class CityListApiV2(BaseApi):
    class FilterSerializer(BaseSerializer):
        country_id = HashIdField(allow_null=True, required=False)
        state_id = HashIdField(allow_null=True, required=False)

        class Meta:
            ref_name = "CityListApiV2Filter"

    class OutputSerializer(CityModelSerializer):
        class Meta(CityModelSerializer.Meta):
            ref_name = "CityListApiV2Output"
            fields = ("id", "name")

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="city_list_api_v2",
        operation_summary="City List API V2",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        cities = City.objects.filter(is_active=True, is_verified=True).order_by("name")
        if data.get("country_id"):
            cities = cities.filter(country_id=data.get("country_id"))
        if data.get("state_id"):
            cities = cities.filter(state_id=data.get("state_id"))
        return Response(self.OutputSerializer(cities, many=True).data, status=HTTP_200_OK)


# deprecated
class OrganizationLogoUploadApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_ORG_SETTINGS]

    class InputSerializer(BaseSerializer):
        logo = serializers.FileField(allow_null=True, default=None)

        class Meta:
            ref_name = "OrganizationLogoUploadInput"

    class OutputSerializer(BaseSerializer):
        logo = serializers.URLField(allow_null=True)

        class Meta:
            ref_name = "OrganizationLogoUploadOutput"

    parser_classes = (MultiPartParser,)
    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="organization_logo_upload",
        operation_summary="Organization Logo Upload",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        org = Organization.objects.filter(id=self.get_organization_id()).first()
        if not org:
            raise ValidationError("Organization not found")
        org.logo = data.get("logo")
        org.save()
        return Response(self.OutputSerializer({"logo": org.logo.url if org.logo else None}).data, status=HTTP_200_OK)


class PlatformVersionUpdateApi(BaseKeyProtectedApi):
    class InputSerializer(BaseKeyProtectedSerializer):
        target_client_version = serializers.CharField()
        # TODO: Remove this field after all platforms are updated
        platform = serializers.ChoiceField(choices=SourceChoices.choices)

        class Meta:
            ref_name = "PlatformVersionUpdateInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="platform_version_update",
        operation_summary="Platform Version Update",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        platform = data.get("platform") or self.get_source()  # Ordering is because of frontend workflow file
        platform_version_create(platform=platform, version=data.get("target_client_version"))
        PlatformVersionCache.delete(instance_id=platform)
        return Response(status=HTTP_201_CREATED)


class GSTListAPI(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: "[5,12,18,28]"},
        operation_id="gst_list_api",
        operation_description="Api for GST List",
        deprecated=True,
    )
    def get(self, request, *args, **kwargs):
        self.set_response_message("Gst slab list fetch sucessfully")
        return Response(data=gst_slab_active_fetch(), status=HTTP_200_OK)


class TaxSlabListAPI(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: "[5,12,18,28]"},
        operation_id="tax_slab_list_api",
        operation_description="Api for Tax Slab List",
    )
    def get(self, request, tax_id, *args, **kwargs):
        self.set_response_message("Tax slabs fetch sucessfully")
        return Response(data=get_tax_slab_list(tax_id=tax_id), status=HTTP_200_OK)


class ItemExpenseTypeDropdownListAPI(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ItemExpenseTypeDropdownListModelSerializer):
        class Meta(ItemExpenseTypeDropdownListModelSerializer.Meta):
            ref_name = "ItemExpenseTypeOutputSerializer"
            fields = ("id", "name", "icon")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="item_expense_type_list_api",
        operation_description="Api for Item Expense Type List",
    )
    def get(self, request, *args, **kwargs):
        instances = organization_active_expense_item_type_fetch(org_id=self.get_organization_id()).filter(
            archived_at=None
        )
        self.set_response_message("Item expense type list fetch sucessfully")
        return Response(self.OutputSerializer(instance=instances, many=True).data, status=HTTP_200_OK)


class ItemExpenseTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ItemExpenseTypeModelSerializer):
        is_archived = serializers.SerializerMethodField()

        class Meta(ItemExpenseTypeModelSerializer.Meta):
            ref_name = "ItemExpenseTypeListOutput"
            fields = ("id", "name", "category", "is_archived")

        def get_is_archived(self, obj):
            return obj.is_archived

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="item_expense_type_list",
        operation_summary="Fetch list of item expense types along with categories.",
    )
    def get(self, request, *args, **kwargs):
        item_types = (
            organization_active_expense_item_type_fetch(org_id=self.get_organization_id())
            .select_related("category")
            .order_by("-archived_at", "id")
        )
        self.set_response_message("Item expense type list fetch sucessfully")
        return Response(self.OutputSerializer(instance=item_types, many=True).data, status=HTTP_200_OK)


class ItemExpenseTypeCategoryListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ItemExpenseTypeCategoryModelSerializer):
        class Meta(ItemExpenseTypeCategoryModelSerializer.Meta):
            ref_name = "CategoryItemListOutput"
            fields = ("id", "name", "image")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="category_item_list",
        operation_summary="Fetch list of item categories.",
    )
    def get(self, request, *args, **kwargs):
        categories = category_expense_item_type_fetch()
        self.set_response_message("Item category list fetched successfully")
        return Response(self.OutputSerializer(categories, many=True).data, status=HTTP_200_OK)


class ItemExpenseTypeCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        category_id = HashIdField()
        name = serializers.CharField(max_length=255)

        def validate_name(self, value):
            value = value.strip()
            return " ".join(value.split())

        class Meta:
            ref_name = "ItemTypeCreateInputSerializer"

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        operation_id="create_item_type",
        operation_summary="Create Item Type",
        request_body=InputSerializer(),
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        category_id = data["category_id"]
        name = data["name"]
        organization_id = self.get_organization_id()
        user_id = self.get_user_id()
        try:
            ItemExpenseTypeService().create(
                category_id=category_id, organization_id=organization_id, name=name, user_id=user_id
            )
            self.set_response_message("Item type created successfully")
            return Response(status=HTTP_201_CREATED)
        except ItemExpenseType.ModelDataIntegrityException:
            self.set_response_message("Item expense type with same name already exists")
            return Response(status=HTTP_400_BAD_REQUEST)


class ItemExpenseTypeUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        category_id = HashIdField()

        class Meta:
            ref_name = "ItemTypeUpdateInputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="update_item_type",
        operation_summary="Update Item Type",
        request_body=InputSerializer(),
    )
    def put(self, request, item_type_id, *args, **kwargs):
        data = self.validate_input_data()
        category_id = data.get("category_id")
        user_id = self.get_user_id()
        try:
            ItemExpenseTypeService().update(
                organization_id=self.get_organization_id(),
                item_type_id=item_type_id,
                category_id=category_id,
                user_id=user_id,
            )
        except ItemExpenseType.ModelDataIntegrityException as e:
            self.set_response_message("Item expense type with same name already exists")
            raise e
        self.set_response_message("Item type updated successfully")
        return Response(status=HTTP_200_OK)


class ItemExpenseTypeArchiveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="archive_item_expense_type",
        operation_summary="Archive Item Expense Type",
    )
    def put(self, request, item_type_id, **kwargs):
        user_id = self.get_user_id()
        ItemExpenseTypeService().archive(
            organization_id=self.get_organization_id(),
            item_type_id=item_type_id,
            user_id=user_id,
        )

        self.set_response_message("Item type archived successfully")
        return Response(status=HTTP_200_OK)


class ItemExpenseTypeUnArchiveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="unarchive_item_expense_type",
        operation_summary="UnArchive Item Expense Type",
    )
    def put(self, request, item_type_id, **kwargs):
        user_id = self.get_user_id()
        ItemExpenseTypeService().unarchive(
            organization_id=self.get_organization_id(),
            item_type_id=item_type_id,
            user_id=user_id,
        )

        self.set_response_message("Item type unarchived successfully")
        return Response(status=HTTP_200_OK)


class ProductionDrawingTagListApi(BaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ProductionDrawingTagSerializer(many=True)},
        operation_id="production_drawing_tag_list_api",
        operation_description="Api for Production Drawing Tag List",
    )
    def get(self, request, *args, **kwargs):
        instances = production_drawing_tags_active_fetch()
        self.set_response_message("Production drawing tag list fetched successfully")
        return Response(data=ProductionDrawingTagSerializer(instance=instances, many=True).data, status=HTTP_200_OK)


class OrganizationDetailApi(BaseApi):
    pagination_class = None

    class OutputSerializer(OrganizationDetailSerializer):
        class Meta(OrganizationDetailSerializer.Meta):
            ref_name = "OrganizationDetailSerializer"
            fields = (
                "id",
                "company_name",
                "company_addresses",
                "company_business_card",
                "pan",
                "aadhar",
                "msme",
                "gst",
                "is_kyc_done",
                "logo",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="organization_detail_api",
        operation_summary="Organization Detail API",
    )
    def get(self, request, *args, **kwargs):
        data = organization_details(organization_id=self.get_organization_id())
        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class StateListApi(BaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseModelSerializer):
        class Meta:
            model = State
            ref_name = "StateListOutputSerializer"
            fields = ("id", "name")

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="state_list_api",
        operation_description="Api for State List",
    )
    def get(self, request, country_id, *args, **kwargs):
        states = State.objects.filter(country_id=country_id, is_active=True).all().order_by("name")
        return Response(data=self.OutputSerializer(states, many=True).data, status=HTTP_200_OK)


class CityListApi(BaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseModelSerializer):
        class Meta:
            model = City
            ref_name = "CityListOutputSerializer"
            fields = ("id", "name")

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="city_list_api",
        operation_description="Api for City List",
    )
    def get(self, request, country_id, state_id, *args, **kwargs):
        cities = (
            City.objects.filter(state__country_id=country_id, state_id=state_id, is_active=True).order_by("name").all()
        )
        return Response(data=self.OutputSerializer(cities, many=True).data, status=HTTP_200_OK)


class OrganizationContextPermissionActionCheckApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        context = serializers.ChoiceField(
            required=True,
            choices=[
                MicroContext.LEAD_QUOTATION.value
                # TODO : yet to be defined
            ],
        )
        action = serializers.CharField(required=True)

        context_id = HashIdField(required=False)

        class Meta:
            ref_name = "ProjectContextPermissionActionCheckFilterSerializer"

    filter_serializer_class = FilterSerializer

    class OutputSerializer(BaseSerializer):
        body = TypeOutputSerializer(
            many=True,
            context={
                "allowed_type_choices": {
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.BULLET: None,
                }
            },
        )
        next_action = serializers.CharField()
        message_type = serializers.CharField()

        class Meta:
            ref_name = "OrganizationContextPermissionActionCheckOutputSerializer"

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_summary="Organization Context Permission Action Check Api",
        operation_id="check_organization_context_permission_action",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()

        checker = ContextAllowedActionCheckerFactory().get_checker(
            context=data.get("context"),
            action=data.get("action"),
            data=ContextCheckerData(
                user=request.user,
                organization_id=self.get_organization_id(),
                context_id=data.get("context_id"),
            ),
        )
        json_message, is_allowed = checker.check()

        return Response(status=HTTP_200_OK, data=self.OutputSerializer(json_message).data)


class OrganizationActionBaseApi(BaseApi):
    permission_classes = [IsAuthenticated, OrganizationActionPermission]
    context_action = None
    context_key = None
    context = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.context_id = None

    def get_resource_context(self):
        if not self.context:
            raise Exception("Context not defined")

        return self.context

    def get_context_action(self):
        if not self.context_action:
            raise Exception("Action not defined")
        return self.context_action

    def get_context_id(self):
        if not self.context_key:
            return None
        return self.kwargs.get(self.context_key)


class OrgLevelUomListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()
        is_active = serializers.BooleanField()

        class Meta:
            ref_name = "OrgLevelUomListOutputSerializer"

    serializer_class = OutputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="uom_org_level_list_api",
        operation_summary="Org level Uom list API",
    )
    def get(self, request, *args, **kwargs):
        org_uoms = get_org_level_uoms_list(organization_id=self.get_organization_id())
        self.set_response_message("UOM list fetched successfully.")
        return Response(data=self.serializer_class(org_uoms, many=True).data, status=HTTP_200_OK)


class OrgUOMActiveUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        is_active = serializers.BooleanField()

        class Meta:
            ref_name = "OrgUOMActiveUpdateApiSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="uom_active_update_api",
        operation_summary="UOM mark active/inactive API",
    )
    @transaction.atomic
    def put(self, request, uom_id, *args, **kwargs):
        data = self.validate_input_data()
        enable_disable_org_level_uom(
            uom_id=uom_id,
            org_id=self.get_organization_id(),
            is_active=data.get("is_active"),
        )
        # Delete Cache
        OrganizationUOMCache.delete(instance_id=self.get_organization_id())
        if data.get("is_active"):
            self.set_response_message("UOM is marked Active.")
        else:
            self.set_response_message("UOM is marked Inactive.")
        return Response(data=None, status=HTTP_202_ACCEPTED)


class CountryCurrencyListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(CurrencyModelSerializer):
        class Meta(CurrencyModelSerializer.Meta):
            ref_name = "CountryCurrencyListOutputSerializer"
            fields = ("id", "code", "name", "symbol")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="country_currency_list_api",
        operation_description="Api for Country Currency List",
    )
    def get(self, request, *args, **kwargs):
        currency = get_country_currencies(country_id=self.get_organization_country_id())
        return Response(data=self.OutputSerializer(currency, many=True).data, status=HTTP_200_OK)


class CountryTaxTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(TaxTypeModelSerializer):
        name = serializers.SerializerMethodField()

        def get_name(self, obj):
            return f"{obj.name} ({obj.remark})" if obj.remark else obj.name

        class Meta(TaxTypeModelSerializer.Meta):
            ref_name = "CountryTaxTypeListOutputSerializer"
            fields = ("id", "name")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="country_tax_type_list_api",
        operation_description="Api for Country Tax Type List",
    )
    def get(self, request, *args, **kwargs):
        tax_type = get_country_tax_types(country_id=self.get_organization_country_id())
        return Response(data=self.OutputSerializer(tax_type, many=True).data, status=HTTP_200_OK)


class EncodeRedirectUrlPathApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class Serializer(BaseSerializer):
        path = serializers.CharField(required=True)

        class Meta(TimezoneModelSerializer.Meta):
            ref_name = "EncodeRedirectUrlPathSerializer"

    input_serializer_class = Serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: Serializer()},
        operation_id="encode_redirect_url_api",
        operation_description="Api for encoding dashboard int containing path",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        parts = data.get("path").split("/")
        final_parts = []
        for part in parts:
            try:
                int(part)
                final_parts.append(HashIdConverter.encode(part))
            except ValueError:
                final_parts.append(part)
                continue
        return Response(data={"path": "/".join(final_parts)}, status=HTTP_200_OK)


class CountryTimezoneListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(TimezoneModelSerializer):
        class Meta(TimezoneModelSerializer.Meta):
            ref_name = "CountryTimezoneListOutputSerializer"
            fields = ("id", "name", "locale")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="country_timezone_list_api",
        operation_description="Api for Country Timezone List",
    )
    def get(self, request, *args, **kwargs):
        timezone = get_country_timezones(country_id=self.get_organization_country_id())
        return Response(data=self.OutputSerializer(timezone, many=True).data, status=HTTP_200_OK)


@shared_task
def run_script():
    from script.feature_middle_east import main

    main()


class ScriptRunner(BaseApi):
    def get(self, request, *args, **kwargs):
        run_script.apply_async(queue="script_runner_queue")
        # run_script()
        return Response(
            "Script triggered successfully",
        )
