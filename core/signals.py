from functools import partial

from django.db import transaction
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from django_structlog import signals
from rest_framework.authtoken.models import Token

from authorization.domain.constants import Permissions
from common.constants import SYSTEM_USER_ID
from common.utils import RequestDataExtractor
from core.caches import (
    OrganizationUOMCache,
    OrgAssignmentCache,
    RoleActionCache,
    RolePermissionCache,
    TestingPhoneNumberCache,
    UnitOfMeasurementCache,
)
from core.models import (
    ItemExpenseType,
    ItemExpenseTypeCategory,
    Organization,
    OrganizationUser,
    Role,
    RolePermission,
    TestingPhoneNumber,
    UnitOfMeasurement,
    User,
)
from core.services import token_delete_using_user
from core.triggers import organization_create_trigger
from core.user.services import organization_user_create_trigger, user_create_trigger
from order.data.models import OrganizationPurchaseOrderType


@receiver(signals.bind_extra_request_metadata)
def bind_request_headers(request, logger, **kwargs):
    try:
        _telemetry_request_token_data = RequestDataExtractor.extract_request_token_data(request)
        _telemetry_request_headers_data = RequestDataExtractor.extract_request_headers_data(request)
        if _telemetry_request_token_data:
            setattr(request, "_telemetry_request_token_data", _telemetry_request_token_data)
            logger.bind(**_telemetry_request_token_data)
        if _telemetry_request_headers_data:
            setattr(request, "_telemetry_request_headers_data", _telemetry_request_headers_data)
            logger.bind(**_telemetry_request_headers_data)
    except Exception as e:
        logger.error("Error in bind_request_headers", error=str(e))


@receiver(post_save, sender=Role, dispatch_uid="role_post_save")
def role_post_save(sender, instance, created, **kwargs):
    RolePermissionCache.delete(instance_id=instance.pk)
    RoleActionCache.delete(instance_id=instance.pk)


@receiver(post_save, sender=TestingPhoneNumber, dispatch_uid="testing_phone_number_post_save")
def testing_phone_number_post_save(sender, instance, created, **kwargs):
    TestingPhoneNumberCache.delete()


@receiver(post_delete, sender=TestingPhoneNumber, dispatch_uid="testing_phone_number_post_delete")
def testing_phone_number_post_delete(sender, instance, **kwargs):
    TestingPhoneNumberCache.delete()


@receiver(post_save, sender=OrganizationUser, dispatch_uid="organization_user_post_save")
def organization_user_post_save(sender, instance, created, **kwargs):
    OrgAssignmentCache.delete(instance_id=instance.organization_id)
    if created:
        organization_user_create_trigger(
            user_id=instance.user_id,
            joined_by_id=instance.joined_by_id,
        )
    # Do not add token_delete_using_user here as org user is updated from process_request auth service


@receiver(post_save, sender=UnitOfMeasurement, dispatch_uid="unit_of_measurement_post_save")
def unit_of_measurement_post_save(sender, instance, created, **kwargs):
    UnitOfMeasurementCache.delete()
    OrganizationUOMCache.delete_many()


@receiver(post_delete, sender=OrganizationUser, dispatch_uid="organization_user_post_delete")
def organization_user_post_delete(sender, instance, **kwargs):
    OrgAssignmentCache.delete(instance_id=instance.organization_id)
    token_delete_using_user(user_id=instance.user_id)


def can_view_all_projects_post_change(instance: RolePermission):
    if instance.permission == Permissions.CAN_VIEW_ALL_PROJECTS:
        user_ids = OrganizationUser.objects.filter(role_id=instance.role_id).values_list("user_id", flat=True)
        Token.objects.filter(user_id__in=user_ids).delete()


@receiver(post_save, sender=RolePermission, dispatch_uid="role_permission_post_save")
def role_permission_post_save(sender, instance, **kwargs):
    can_view_all_projects_post_change(instance=instance)


@receiver(post_delete, sender=RolePermission, dispatch_uid="role_permission_post_delete")
def role_permission_post_delete(sender, instance, **kwargs):
    can_view_all_projects_post_change(instance=instance)


@receiver(post_save, sender=User, dispatch_uid="user_post_save")
def user_post_save(sender, instance, created, **kwargs):
    if created and instance.name != "System User":
        user_create_trigger(user_id=instance.pk)


def create_default_item_expense_types(organization: Organization):
    categories = ItemExpenseTypeCategory.objects.filter(is_default_type=True)
    item_expense_type_list = []
    for category in categories:
        item_expense_type_list.append(
            ItemExpenseType(
                organization=organization,
                category=category,
                name=category.name,
                icon=category.image,
                created_by_id=SYSTEM_USER_ID,
            )
        )
    ItemExpenseType.objects.bulk_create(item_expense_type_list)


def create_default_purchase_order_type(organization: Organization):
    OrganizationPurchaseOrderType.objects.create(
        organization=organization,
        name="Purchase Order Type",
        is_default=False,
        is_active=True,
        is_editable=False,
        created_by_id=SYSTEM_USER_ID,
    )


@receiver(post_save, sender=Organization, dispatch_uid="organization_create_post_save_signal")
def organization_create_post_save(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(partial(organization_create_trigger, organization=instance))
        create_default_item_expense_types(instance)
        create_default_purchase_order_type(instance)
