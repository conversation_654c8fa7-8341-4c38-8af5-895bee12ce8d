from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from core.models import (
    CoreHistory,
    Country,
    ExternalWebhook,
    Organization,
    OrganizationCallbackUrl,
    OrganizationCallbackUrlEvent,
    OrganizationConfig,
    OrganizationCustomFieldConfig,
    OrganizationLegalEntity,
    OrganizationOrderPaymentTerm,
    OrganizationUser,
    Role,
    RoleAction,
    RolePermission,
    SystemUserAccessHistory,
    TDSType,
    TestingPhoneNumber,
    User,
    VendorOrgTag,
)


@admin.register(VendorOrgTag)
class VendorOrgTagAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )
    search_fields = ("name",)


@admin.register(TestingPhoneNumber)
class TestingPhoneNumberAdmin(admin.ModelAdmin):
    list_display = ("id", "phone_number", "otp", "is_active")
    search_fields = ("phone_number",)


class OrganizationUserInline(admin.TabularInline):
    model = OrganizationUser
    extra = 1

    class Meta:
        model = User
        fields = ("username", "email")


@admin.register(User)
class UserAdmin(UserAdmin):
    list_display = ("id", "hashid", "name", "email", "phone_number", "org")
    search_fields = ("first_name", "last_name", "email", "phone_number")
    readonly_fields = ("last_login", "org")
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("first_name", "last_name", "email", "phone_number", "password1", "password2"),
            },
        ),
    )

    fieldsets = UserAdmin.fieldsets + (
        (None, {"fields": ("phone_number", "org", "updated_by", "updated_at", "deleted_by", "deleted_at")}),
    )

    def save_model(self, request, obj, form, change):
        return super().save_model(request, obj, form, change)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    inlines = (OrganizationUserInline,)
    list_display = ("id", "hashid", "name", "is_client", "is_vendor", "logo")
    search_fields = ["name"]


class RolePermissionInline(admin.StackedInline):
    model = RolePermission
    extra = 0


class RoleActionInline(admin.StackedInline):
    model = RoleAction
    extra = 0


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ("name", "organization", "type", "scope")
    inlines = (RolePermissionInline, RoleActionInline)
    fields = ("name", "organization", "type", "scope", "created_by", "role_type", "is_active")

    def has_delete_permission(self, request, obj=None) -> bool:
        return False


@admin.register(OrganizationUser)
class OrganizationUserAdmin(admin.ModelAdmin):
    list_display = ("user", "organization", "is_admin", "role")
    search_fields = (
        "user__first_name",
        "user__last_name",
        "user__email",
        "user__phone_number",
        "role__name",
        "organization__name",
    )


@admin.register(OrganizationConfig)
class OrganizationConfigAdmin(admin.ModelAdmin):
    list_display = ("organization",)
    search_fields = ("organization__name",)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ("organization",)
        else:
            return tuple()


@admin.register(OrganizationLegalEntity)
class OrganizationLegalEntityAdmin(admin.ModelAdmin):
    list_display = ("organization", "name")
    search_fields = ("organization__name", "name")


# @admin.register(OrganizationAddress)
# class OrganizationAddressAdmin(admin.ModelAdmin):
#     list_display = ("organization", "header", "address")
#     search_fields = ("organization__name", "header", "address")


@admin.register(OrganizationOrderPaymentTerm)
class OrganizationOrderPaymentTermAdmin(admin.ModelAdmin):
    list_display = ("title", "description", "organization")


@admin.register(CoreHistory)
class CoreHistoryAdmin(admin.ModelAdmin):
    list_display = ("id", "updated_by", "updated_at")


class OrganizationCallbackUrlEventAdmin(admin.TabularInline):
    model = OrganizationCallbackUrlEvent
    extra = 1


@admin.register(OrganizationCallbackUrl)
class OrganizationCallbackUrlAdmin(admin.ModelAdmin):
    list_display = ("organization", "url")
    search_fields = ("organization__name", "url")
    inlines = (OrganizationCallbackUrlEventAdmin,)


@admin.register(SystemUserAccessHistory)
class SystemUserAccessHistoryAdmin(admin.ModelAdmin):
    list_display_links = None
    actions_on_top = False
    search_fields = ("organization__name",)
    list_display = ("organization", "accessed_by", "accessed_at", "system_user_id")

    def has_add_permission(self, request):
        return False


admin.site.register(OrganizationCustomFieldConfig)


@admin.register(Country)
class CoreCountryAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "code")
    search_fields = ("name", "code")


@admin.register(ExternalWebhook)
class ExternalWebhookAdmin(admin.ModelAdmin):
    list_display = ("id", "type", "url")
    search_fields = ("type", "url")


@admin.register(TDSType)
class TDSTypeAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )
    search_fields = (
        "id",
        "name",
    )
