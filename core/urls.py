from django.urls import include, path

from core import views
from core.apis import (
    CityListApi,
    CompleteFileUploadToCloudApi,
    CountryCurrencyListApi,
    CountryListApi,
    CountryTaxTypeListApi,
    CountryTimezoneListApi,
    GSTListAPI,
    HashIdConverterApi,
    HashIdDecoderApi,
    ItemExpenseTypeDropdownListAPI,
    MetabaseAuthenticationApi,
    MetabaseDashboardApi,
    OrganizationContextPermissionActionCheckApi,
    OrganizationDetailApi,
    OrganizationLogoUploadApi,
    OrganizationUserRoleListApi,
    OrgLevelUomListApi,
    OrgPaymentTermsCreateApi,
    OrgUOMActiveUpdateApi,
    PlatformVersionUpdateApi,
    ProductionDrawingTagListApi,
    ProjectRoleListApi,
    ScriptRunner,
    SendEventsForAnalyticsApi,
    SetUserProfileToAnalyticsPlatformApi,
    StartFileUploadToCloudApi,
    StateListApi,
    TaxSlabListAPI,
    UploadFileToCloudApi,
    UploadFileToCloudV2Api,
)
from core.order_type_config.interface.apis import (
    OrgPurchaseOrderTypeCreateApi,
    OrgPurchaseOrderTypeDeleteApi,
    OrgPurchaseOrderTypeListApi,
    OrgPurchaseOrderTypeUpdateApi,
    PurchaseOrderTypeListApi,
)
from core.tnc_config.interface.apis.lead_apis import (
    OrganizationQuotationCheckTnCDataApi,
    OrganizationQuotationPaymentTermActiveInactiveApi,
    OrganizationQuotationPaymentTermCreateApi,
    OrganizationQuotationPaymentTermDeleteApi,
    OrganizationQuotationPaymentTermDetailApi,
    OrganizationQuotationPaymentTermListApi,
    OrganizationQuotationPaymentTermUpdateApi,
)
from core.tnc_config.interface.apis.order_apis import (
    OrganizationOrderCheckTnCDataApi,
    OrganizationOrderOtherTermActiveInactiveApi,
    OrganizationOrderOtherTermCreateApi,
    OrganizationOrderOtherTermDetailApi,
    OrganizationOrderOtherTermListApi,
    OrganizationOrderOtherTermUpdateApi,
    OrganizationOrderPaymentTermActiveInactiveApi,
    OrganizationOrderPaymentTermCreateApi,
    OrganizationOrderPaymentTermDeleteApi,
    OrganizationOrderPaymentTermDetailApi,
    OrganizationOrderPaymentTermListApi,
    OrganizationOrderPaymentTermUpdateApi,
)

urlpatterns = [
    path("file-upload/", UploadFileToCloudApi.as_view()),
    path("platform-version-update/", PlatformVersionUpdateApi.as_view()),
    path("health/", views.Health.as_view()),
    path("country/list/", CountryListApi.as_view()),
    path("country/<hash_id:country_id>/state/list/", StateListApi.as_view()),
    path("country/<hash_id:country_id>/state/<hash_id:state_id>/city/list/", CityListApi.as_view()),
    path("uom/<hash_id:uom_id>/active/update/", OrgUOMActiveUpdateApi.as_view(), name="org_uom_active_update"),
    path("organization-uoms/", OrgLevelUomListApi.as_view(), name="organization-uoms"),
    path("exception/", views.RaiseExceptionAPI.as_view()),
    path("sentry-debug/", views.SentryDebug.as_view()),
    path("file-upload-v2/", UploadFileToCloudV2Api.as_view()),
    path("initiate-file-upload/", StartFileUploadToCloudApi.as_view()),
    path("complete-file-upload/", CompleteFileUploadToCloudApi.as_view()),
    path("user/", include("core.user.urls"), name="user"),
    path("org-user-role/", OrganizationUserRoleListApi.as_view(), name="org-user-role-list"),
    path("project-user-role/", ProjectRoleListApi.as_view(), name="project-user-role-list"),
    path("role/", include("core.role.urls"), name="role"),
    path("metabase-app/", MetabaseAuthenticationApi.as_view(), name="metabase_app"),
    path("metabase-dashboard/", MetabaseDashboardApi.as_view(), name="metabase_dashboard"),
    path("hash-ids-converter/", HashIdConverterApi.as_view(), name="hash_ids_converter"),
    path("hash-ids-decoder/", HashIdDecoderApi.as_view(), name="hash_ids_decoder"),
    path("logo-upload/", OrganizationLogoUploadApi.as_view(), name="logo_upload"),
    path("organization/<int:org_id>/payment-terms/create/", OrgPaymentTermsCreateApi.as_view()),
    path("organization/detail/", OrganizationDetailApi.as_view()),
    path("tracking/events/", SendEventsForAnalyticsApi.as_view(), name="tracking"),
    path(
        "tracking/user-profiles/", SetUserProfileToAnalyticsPlatformApi.as_view(), name="set_user_profiles_for_tracking"
    ),
    path("gst/list/", GSTListAPI.as_view(), name="gst_list"),
    path("tax/<hash_id:tax_id>/slab-list/", TaxSlabListAPI.as_view(), name="tax_slab_list"),
    path("expense-type/list/", ItemExpenseTypeDropdownListAPI.as_view(), name="expense_type_list"),
    path("production-drawing-tag/list/", ProductionDrawingTagListApi.as_view(), name="production_drawing_tags"),
    path(
        "check-action-permission/",
        OrganizationContextPermissionActionCheckApi.as_view(),
        name="organization-check-action-permission",
    ),
    path(
        "order/payment-terms/create/", OrganizationOrderPaymentTermCreateApi.as_view(), name="order-payment-term-create"
    ),
    path(
        "order/payment-terms/<hash_id:payment_term_id>/update/",
        OrganizationOrderPaymentTermUpdateApi.as_view(),
        name="order-payment-term-update",
    ),
    path(
        "order/payment-terms/<hash_id:payment_term_id>/active-inactive/",
        OrganizationOrderPaymentTermActiveInactiveApi.as_view(),
        name="order-payment-term-active-inactive",
    ),
    path(
        "order/payment-terms/list/",
        OrganizationOrderPaymentTermListApi.as_view(),
        name="order-payment-term-list",
    ),
    path(
        "order/payment-terms/<hash_id:payment_term_id>/",
        OrganizationOrderPaymentTermDetailApi.as_view(),
        name="order-payment-term-detail",
    ),
    path(
        "quotation/terms/create/",
        OrganizationQuotationPaymentTermCreateApi.as_view(),
        name="quotation-payment-term-create",
    ),
    path(
        "quotation/terms/<hash_id:tnc_id>/update/",
        OrganizationQuotationPaymentTermUpdateApi.as_view(),
        name="quotation-payment-term-update",
    ),
    path(
        "quotation/terms/<hash_id:tnc_id>/active-inactive/",
        OrganizationQuotationPaymentTermActiveInactiveApi.as_view(),
        name="quotation-payment-term-active-inactive",
    ),
    path(
        "quotation/terms/list/",
        OrganizationQuotationPaymentTermListApi.as_view(),
        name="quotation-payment-term-list",
    ),
    path(
        "quotation/terms/<hash_id:tnc_id>/",
        OrganizationQuotationPaymentTermDetailApi.as_view(),
        name="quotation-payment-term-detail",
    ),
    path(
        "quotation/terms/<hash_id:tnc_id>/delete/",
        OrganizationQuotationPaymentTermDeleteApi.as_view(),
        name="quotation-payment-term-delete",
    ),
    path(
        "order/other-terms/<hash_id:tnc_id>/delete/",
        OrganizationOrderPaymentTermDeleteApi.as_view(),
        name="quotation-payment-term-delete",
    ),
    path("order/other-terms/create/", OrganizationOrderOtherTermCreateApi.as_view(), name="order-other-term-create"),
    path(
        "order/other-terms/<hash_id:other_term_id>/update/",
        OrganizationOrderOtherTermUpdateApi.as_view(),
        name="order-other-term-update",
    ),
    path(
        "order/other-terms/<hash_id:other_term_id>/active-inactive/",
        OrganizationOrderOtherTermActiveInactiveApi.as_view(),
        name="order-other-term-active-inactive",
    ),
    path(
        "order/other-terms/list/",
        OrganizationOrderOtherTermListApi.as_view(),
        name="order-other-term-list",
    ),
    path(
        "order/other-terms/<hash_id:other_term_id>/",
        OrganizationOrderOtherTermDetailApi.as_view(),
        name="order-other-term-detail",
    ),
    path(
        "order/check-tnc-data-refreshed/",
        OrganizationOrderCheckTnCDataApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "order/purchase-order-types/list/",
        PurchaseOrderTypeListApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "order/purchase-order-types/org-list/",
        OrgPurchaseOrderTypeListApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "order/purchase-order-types/create/",
        OrgPurchaseOrderTypeCreateApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "order/purchase-order-types/<hash_id:purchase_order_type_id>/update/",
        OrgPurchaseOrderTypeUpdateApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "order/purchase-order-types/<hash_id:purchase_order_type_id>/delete/",
        OrgPurchaseOrderTypeDeleteApi.as_view(),
        name="order-tnc-data-refreshed",
    ),
    path(
        "quotation/check-tnc-data-refreshed/",
        OrganizationQuotationCheckTnCDataApi.as_view(),
        name="quotation-tnc-data-refreshed",
    ),
    path("country/currency/", CountryCurrencyListApi.as_view(), name="country_currency_list"),
    path("country/tax-type/", CountryTaxTypeListApi.as_view(), name="country_tax_type_list"),
    path("country/timezone/", CountryTimezoneListApi.as_view(), name="country_timezone_list"),
    path("run-script/", ScriptRunner.as_view(), name="run_script"),
]
