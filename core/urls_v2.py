from django.urls import include, path

from core.apis import CityListApiV2, EncodeRedirectUrlPathApi

urlpatterns = [
    path("role/", include("core.role.interface.urls"), name="role-v2"),
    path("organization/", include("core.organization.interface.urls.v2"), name="organization-v2"),
    path("city-list/", CityListApiV2.as_view(), name="city-list"),
    path("encode-redirect-url-path/", EncodeRedirectUrlPathApi.as_view(), name="encode-redirect-url-path"),
]
