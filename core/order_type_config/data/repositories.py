from core.models import (
    OrganizationPurchaseOrderType,
)
from core.order_type_config.data.selectors import get_active_purchase_order_types_qs, get_org_purchase_order_types_qs
from order.data.entities import PurchaseOrderTypeDataEntity
from order.domain.abstract_repos import (
    PurchaseOrderTypeAbstractRepo,
)


class OrganizationPurchaseOrderTypeRepo(PurchaseOrderTypeAbstractRepo):
    def get_org_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        org_purchase_order_types_qs = get_org_purchase_order_types_qs(org_id=org_id)
        org_purchase_order_types = []
        for purchase_order_type in org_purchase_order_types_qs:
            org_purchase_order_types.append(
                PurchaseOrderTypeDataEntity(
                    id=purchase_order_type.id,
                    name=purchase_order_type.name,
                    is_default=purchase_order_type.is_default,
                    is_active=purchase_order_type.is_active,
                )
            )
        return org_purchase_order_types

    def get_active_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        active_purchase_order_type_qs = get_active_purchase_order_types_qs(org_id=org_id)
        active_purchase_order_type = []
        for purchase_order_type in active_purchase_order_type_qs:
            active_purchase_order_type.append(
                PurchaseOrderTypeDataEntity(
                    id=purchase_order_type.id,
                    name=purchase_order_type.name,
                    is_default=purchase_order_type.is_default,
                    is_active=purchase_order_type.is_active,
                )
            )
        return active_purchase_order_type

    def create_org_purchase_order_type(self, data: PurchaseOrderTypeDataEntity, user_id: int, org_id: int):
        purchase_order_type = OrganizationPurchaseOrderType.objects.create(
            organization_id=org_id,
            name=data.name,
            is_default=False,
            is_active=False,
            created_by_id=user_id,
        )
        return PurchaseOrderTypeDataEntity(
            id=purchase_order_type.id,
            name=purchase_order_type.name,
            is_default=purchase_order_type.is_default,
            is_active=purchase_order_type.is_active,
        )

    def update_org_purchase_order_type(self, is_active: bool, is_default: bool, purchase_order_type_id: int):
        OrganizationPurchaseOrderType.objects.filter(id=purchase_order_type_id).update(
            is_default=is_default, is_active=is_active
        )

    def delete_org_purchase_order_type(self, purchase_order_type_id: int, user_id: int):
        OrganizationPurchaseOrderType.objects.filter(id=purchase_order_type_id).soft_delete(user_id=user_id)
