from django.db.models import QuerySet

from core.models import OrganizationPurchaseOrderType


def get_active_purchase_order_types_qs(org_id: int) -> QuerySet[OrganizationPurchaseOrderType]:
    return OrganizationPurchaseOrderType.objects.filter(organization_id=org_id, is_active=True).available()


def get_org_purchase_order_types_qs(org_id: int) -> QuerySet[OrganizationPurchaseOrderType]:
    return OrganizationPurchaseOrderType.objects.filter(organization_id=org_id).available()


def get_global_org_purchase_order_type(org_id: int) -> OrganizationPurchaseOrderType:
    return OrganizationPurchaseOrderType.objects.filter(organization_id=org_id, is_editable=False).available().first()
