import abc

from order.data.entities import PurchaseOrderTypeDataEntity


class PurchaseOrderTypeAbstractRepo:
    @abc.abstractmethod
    def get_org_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        ...

    @abc.abstractmethod
    def get_active_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        ...

    @abc.abstractmethod
    def create_purchase_order_type(
        self, data: PurchaseOrderTypeDataEntity, user_id: int, org_id: int
    ) -> PurchaseOrderTypeDataEntity:
        ...

    @abc.abstractmethod
    def update_purchase_order_type(self, purchase_order_type_id: int, is_active: bool, is_default: bool) -> None:
        ...

    @abc.abstractmethod
    def delete_purchase_order_type(self, purchase_order_type_id: int) -> None:
        ...
