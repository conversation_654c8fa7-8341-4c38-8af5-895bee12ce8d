from drf_yasg.utils import swagger_auto_schema
from pydantic import Field
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.order_type_config.data.repositories import OrganizationPurchaseOrderTypeRepo
from order.data.entities import PurchaseOrderTypeDataEntity, PurchaseOrderTypeListEntity


class PurchaseOrderTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PurchaseOrderTypeListEntity)},
        operation_id="order_purchase_order_type_list",
        operation_summary="Get Purchase Order Type List",
    )
    def get(self, request, *args, **kwargs):
        purchase_order_types = OrganizationPurchaseOrderTypeRepo().get_active_purchase_order_types(
            org_id=self.get_organization_id()
        )
        return Response(pydantic_dump(purchase_order_types), status=HTTP_200_OK)


class OrgPurchaseOrderTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PurchaseOrderTypeListEntity)},
        operation_id="order_org_purchase_order_type_list",
        operation_summary="Get Organization Purchase Order Type List",
    )
    def get(self, request, *args, **kwargs):
        purchase_order_types = OrganizationPurchaseOrderTypeRepo().get_org_purchase_order_types(
            org_id=self.get_organization_id()
        )
        return Response(pydantic_dump(purchase_order_types), status=HTTP_200_OK)


class OrgPurchaseOrderTypeCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputModel(PydanticInputBaseModel):
        name: str = Field(max_length=100)

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: pydantic_schema(PurchaseOrderTypeDataEntity)},
        operation_id="order_org_purchase_order_type_create",
        operation_summary="Create Organization Purchase Order Type",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_pydantic_input_data()
        purchase_order_type_data = OrganizationPurchaseOrderTypeRepo().create_org_purchase_order_type(
            org_id=self.get_organization_id(), name=data.name, user_id=self.get_user_id()
        )
        return Response(pydantic_dump(purchase_order_type_data), status=HTTP_200_OK)


class OrgPurchaseOrderTypeUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputModel(PydanticInputBaseModel):
        is_active: bool
        is_default: bool

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(PurchaseOrderTypeDataEntity),
        responses={HTTP_200_OK: ""},
        operation_id="order_org_purchase_order_type_update",
        operation_summary="Update Organization Purchase Order Type",
    )
    def put(self, request, purchase_order_type_id, *args, **kwargs):
        data = self.validate_pydantic_input_data()
        purchase_order_type_data = OrganizationPurchaseOrderTypeRepo().update_org_purchase_order_type(
            purchase_order_type_id=purchase_order_type_id, is_active=data.is_active, is_default=data.is_default
        )
        return Response(pydantic_dump(purchase_order_type_data), status=HTTP_200_OK)


class OrgPurchaseOrderTypeDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="order_org_purchase_order_type_delete",
        operation_summary="Delete Organization Purchase Order Type",
    )
    def delete(self, request, purchase_order_type_id, *args, **kwargs):
        OrganizationPurchaseOrderTypeRepo().delete_org_purchase_order_type(
            user_id=self.get_user_id(), purchase_order_type_id=purchase_order_type_id
        )
        return Response(status=HTTP_200_OK)
