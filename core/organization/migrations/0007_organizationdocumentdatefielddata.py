# Generated by Django 3.2.15 on 2025-07-11 12:47

import common.mixins
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('organization', '0006_linkedorganizationdocumentdatefielddata'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationDocumentDateFieldData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', models.DateField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentdatefielddata_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentdatefielddata_deleted', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='date_field_data', to='organization.organizationdocumentv2')),
                ('field_config', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='date_field_data', to='organization.organizationdocumentfieldconfig')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentdatefielddata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_document_date_field_data',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
    ]
