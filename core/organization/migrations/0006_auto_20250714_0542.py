# Generated by Django 3.2.15 on 2025-07-14 05:41

import common.helpers
import common.mixins
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0176_organization_uid'),
        ('organization', '0005_alter_organizationdocumentfieldcontextconfig_context'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='linkedorganizationbusinesscard',
            options={'verbose_name': 'Linked Organization Business Card', 'verbose_name_plural': 'Linked Organization Business Cards'},
        ),
        migrations.AlterField(
            model_name='organizationdocumentfieldconfig',
            name='type',
            field=models.CharField(choices=[('text', 'Text'), ('long_text', 'Long Text'), ('rich_text', 'Rich Text'), ('decimal', 'Decimal'), ('dropdown', 'Dropdown'), ('phone_number', 'Phone Number'), ('date', 'Date'), ('datetime', 'Date Time'), ('file', 'File'), ('multiple_files', 'Multiple Files'), ('email', 'Email'), ('boolean', 'Boolean'), ('multi_dropdown', 'Multi Dropdown'), ('lead_assignee', 'Lead Assignee'), ('lead_company', ' Lead Company'), ('lead_name', 'Lead Name'), ('lead_amount', 'Lead Value'), ('lead_contact', 'Lead Contact'), ('lead_country', 'Lead Country'), ('lead_state', 'Lead State'), ('lead_city', 'Lead City'), ('lead_address', 'Lead Address'), ('state', 'State'), ('lead_address_line_2', 'Lead Address Line 2'), ('lead_zipcode', 'Lead Zipcode')], max_length=255),
        ),
        migrations.AlterField(
            model_name='organizationsectionconfig',
            name='type',
            field=models.CharField(choices=[('kyc_details', 'Kyc details'), ('bank_details', 'Bank details'), ('other_details', 'Other details')], max_length=255),
        ),
        migrations.CreateModel(
            name='OrganizationDocumentPhoneNumberFieldData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentphonenumberfielddata_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentphonenumberfielddata_deleted', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='phone_number_field_data', to='organization.organizationdocumentv2')),
                ('field_config', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='phone_number_field_data', to='organization.organizationdocumentfieldconfig')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentphonenumberfielddata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_document_phone_number_field_data',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='OrganizationDocumentLongTextFieldData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', models.TextField()),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentlongtextfielddata_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentlongtextfielddata_deleted', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='long_text_field_data', to='organization.organizationdocumentv2')),
                ('field_config', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='long_text_field_data', to='organization.organizationdocumentfieldconfig')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationdocumentlongtextfielddata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_document_long_text_field_data',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='OrganizationBillingEntity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=255)),
                ('logo', models.ImageField(blank=True, default=None, max_length=250, null=True, upload_to=common.helpers.get_upload_path)),
                ('is_default', models.BooleanField(default=False)),
                ('is_primary', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='billing_entities', to='core.country')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationbillingentity_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationbillingentity_deleted', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='billing_entities', to='core.organization')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_organizationbillingentity_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_billing_entities',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='LinkedOrganizationDocumentPhoneNumberFieldData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentphonenumberfielddata_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentphonenumberfielddata_deleted', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='phone_number_field_data', to='organization.linkedorganizationdocument')),
                ('field_config', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='linked_org_phone_number_field_data', to='organization.organizationdocumentfieldconfig')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentphonenumberfielddata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_linked_organization_document_phone_number_field_data',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='LinkedOrganizationDocumentLongTextFieldData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', models.TextField()),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentlongtextfielddata_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentlongtextfielddata_deleted', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='long_text_field_data', to='organization.linkedorganizationdocument')),
                ('field_config', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='linked_org_long_text_field_data', to='organization.organizationdocumentfieldconfig')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationdocumentlongtextfielddata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_linked_organization_document_long_text_field_data',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='LinkedOrganizationBillingEntity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=255)),
                ('logo', models.ImageField(blank=True, default=None, max_length=2000, null=True, upload_to=common.helpers.get_upload_path)),
                ('is_default', models.BooleanField(default=False)),
                ('is_primary', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='linked_billing_entities', to='core.country')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationbillingentity_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationbillingentity_deleted', to=settings.AUTH_USER_MODEL)),
                ('mapping', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='billing_entities', to='organization.linkedorganizations')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='organization_linkedorganizationbillingentity_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_linked_organization_billing_entities',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.AddField(
            model_name='linkedorganizationaddress',
            name='billing_entity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='linked_addresses', to='organization.linkedorganizationbillingentity'),
        ),
        migrations.AddField(
            model_name='linkedorganizationbusinesscard',
            name='billing_entity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='linked_business_cards', to='organization.linkedorganizationbillingentity'),
        ),
        migrations.AddField(
            model_name='linkedorganizationdocument',
            name='billing_entity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='linked_documents', to='organization.linkedorganizationbillingentity'),
        ),
        migrations.AddField(
            model_name='linkedorganizations',
            name='primary_billing_entity',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='primary_mappings', to='organization.linkedorganizationbillingentity'),
        ),
        migrations.AddField(
            model_name='organizationdocumentv2',
            name='billing_entity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='documents', to='organization.organizationbillingentity'),
        ),
        migrations.AlterUniqueTogether(
            name='linkedorganizationbusinesscard',
            unique_together={('mapping', 'billing_entity')},
        ),
        migrations.AddConstraint(
            model_name='organizationbillingentity',
            constraint=models.UniqueConstraint(condition=models.Q(('is_primary', True)), fields=('organization', 'is_primary'), name='unique_primary_billing_entity_per_org'),
        ),
        migrations.AddConstraint(
            model_name='organizationbillingentity',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True)), fields=('organization', 'is_default'), name='unique_default_billing_entity_per_org'),
        ),
        migrations.AddConstraint(
            model_name='linkedorganizationbillingentity',
            constraint=models.UniqueConstraint(condition=models.Q(('is_primary', True)), fields=('mapping', 'is_primary'), name='unique_primary_billing_entity_per_mapping'),
        ),
        migrations.AddConstraint(
            model_name='linkedorganizationbillingentity',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True)), fields=('mapping', 'is_default'), name='unique_default_billing_entity_per_mapping'),
        ),
    ]
