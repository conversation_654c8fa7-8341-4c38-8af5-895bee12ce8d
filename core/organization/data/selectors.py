from typing import Optional

import structlog
from django.core.exceptions import ValidationError
from django.db.models import F, OuterRef, Prefetch, QuerySet, Subquery

from core.choices import OrganizationDocumentChoices
from core.models import Organization, OrganizationAddress, OrganizationDocument
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.models import (
    Country,
    LinkedOrganizationDocument,
    LinkedOrganizationDocumentDateFieldData,
    LinkedOrganizationDocumentFileFieldFieldData,
    LinkedOrganizationDocumentStateFieldData,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationDocumentConfig,
    OrganizationDocumentDateFieldData,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationDocumentFileFieldFieldData,
    OrganizationDocumentStateFieldData,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
    OrganizationSectionConfig,
)
from microcontext.choices import MicroContextChoices

logger = structlog.get_logger()


def get_mapping_for_client(org_id: int, client_id: int) -> LinkedOrganizations:
    mapping = (
        LinkedOrganizations.objects.filter(client_id=client_id, vendor_id=org_id, org_id=org_id)
        .select_related("client__country__uid_field")
        .annotate(
            uid_field_id=F("client__country__uid_field_id"),
            uid_field_name=F("client__country__uid_field__name"),
        )
        .first()
    )
    if not mapping:
        raise ValidationError("Linked organization mapping not found.")
    return mapping


def get_mapping_for_vendor(org_id: int, vendor_id: int) -> LinkedOrganizations:
    mapping = (
        LinkedOrganizations.objects.filter(client_id=org_id, vendor_id=vendor_id, org_id=org_id)
        .select_related("vendor__country__uid_field")
        .annotate(
            uid_field_id=F("vendor__country__uid_field_id"),
            uid_field_name=F("vendor__country__uid_field__name"),
        )
        .first()
    )
    if not mapping:
        raise ValidationError("Linked organization mapping not found.")
    return mapping


def get_organization_with_basic_details(org_id: int) -> Optional[Organization]:
    primary_address_query = (
        OrganizationAddress.objects.filter(organization_id=OuterRef("id")).order_by("id").values("id")[:1]
    )
    return (
        Organization.objects.filter(id=org_id)
        .select_related("country")
        .prefetch_related(
            Prefetch(
                "organization_document",
                OrganizationDocument.objects.filter(type=OrganizationDocumentChoices.BUSINESS_CARD).available(),
                to_attr="business_cards",
            )
        )
        .prefetch_related(Prefetch("addresses", OrganizationAddress.objects.all()))
        .prefetch_related(
            Prefetch(
                "addresses",
                OrganizationAddress.objects.filter(id__in=Subquery(primary_address_query)).select_related(
                    "state", "city", "country"
                ),
                to_attr="primary_address",
            )
        )
        .first()
    )


def get_organization_documents_by_section(
    org_id: int, section_type: OrganizationSectionTypeChoices
) -> QuerySet[OrganizationDocumentV2]:
    text_field_data = Prefetch(
        "text_field_data",
        OrganizationDocumentTextFieldData.objects.all().order_by("field_config__position").available(),
    )
    file_field_data = Prefetch(
        "file_field_data",
        OrganizationDocumentFileFieldFieldData.objects.all().order_by("field_config__position").available(),
    )
    state_field_data = Prefetch(
        "state_field_data",
        OrganizationDocumentStateFieldData.objects.all()
        .order_by("field_config__position")
        .available()
        .select_related("state"),
    )
    date_field_data = Prefetch(
        "date_field_data",
        OrganizationDocumentDateFieldData.objects.all().order_by("field_config__position").available(),
    )
    return (
        OrganizationDocumentV2.objects.filter(organization_id=org_id, document_config__section__type=section_type)
        .order_by("created_at")
        .available()
        .prefetch_related(text_field_data, file_field_data, state_field_data, date_field_data)
    )


def get_organization_sections_config(
    org_id: Optional[int] = None, country_id: Optional[int] = None
) -> QuerySet[OrganizationSectionConfig]:
    logger.info("Fetching organization sections config", org_id=org_id, country_id=country_id)
    if not country_id:
        country_ids = Organization.objects.filter(id=org_id).values_list("country_id", flat=True)
        queryset = OrganizationSectionConfig.objects.filter(country_id__in=country_ids)
    else:
        queryset = OrganizationSectionConfig.objects.filter(country_id=country_id)
    return queryset.prefetch_related(
        Prefetch(
            "documents_config",
            OrganizationDocumentConfig.objects.all()
            .prefetch_related(
                Prefetch("fields_config", OrganizationDocumentFieldConfig.objects.all().order_by("position"))
            )
            .order_by("position"),
        )
    ).all()


def get_linked_organization_documents_by_section(
    section_type: OrganizationSectionTypeChoices, mapping_id: int
) -> QuerySet[LinkedOrganizationDocument]:
    text_field_data = Prefetch(
        "text_field_data",
        LinkedOrganizationDocumentTextFieldData.objects.all().order_by("field_config__position").available(),
    )
    file_field_data = Prefetch(
        "file_field_data",
        LinkedOrganizationDocumentFileFieldFieldData.objects.all().order_by("field_config__position").available(),
    )
    state_field_data = Prefetch(
        "state_field_data",
        LinkedOrganizationDocumentStateFieldData.objects.all().order_by("field_config__position").available(),
    )
    date_field_data = Prefetch(
        "date_field_data",
        LinkedOrganizationDocumentDateFieldData.objects.all().order_by("field_config__position").available(),
    )
    return (
        LinkedOrganizationDocument.objects.filter(mapping_id=mapping_id, document_config__section__type=section_type)
        .all()
        .order_by("created_at")
        .available()
        .prefetch_related(text_field_data, file_field_data, state_field_data, date_field_data)
    )


def get_uid_field_by_country(country_id: int) -> OrganizationDocumentFieldConfig:
    country = Country.objects.filter(id=country_id).select_related("uid_field").first()
    if country and country.uid_field:
        return country.uid_field
    return None


def get_uid_field_by_org(org_id: int) -> OrganizationDocumentFieldConfig:
    org = Organization.objects.filter(id=org_id).select_related("country__uid_field").first()
    if org and org.country and org.country.uid_field:
        return org.country.uid_field
    return None


def get_context_field(context_config_id: int, org_id: int, vendor_id: int) -> OrganizationDocumentFieldContextConfig:
    mapping_id = get_mapping_for_vendor(org_id=org_id, vendor_id=vendor_id)
    return (
        OrganizationDocumentFieldContextConfig.objects.filter(id=context_config_id)
        .select_related("field_config")
        .prefetch_related(
            Prefetch(
                "field_config__linked_org_text_field_data",
                queryset=LinkedOrganizationDocumentTextFieldData.objects.available().filter(
                    document__mapping_id=mapping_id
                ),
                to_attr="text_data",
            )
        )
        .first()
    )


def get_context_config_by_country(
    country_id: int, context: MicroContextChoices
) -> Optional[OrganizationDocumentFieldContextConfig]:
    # NOTE: Assumption - Each context has only one field config
    context_config = OrganizationDocumentFieldContextConfig.objects.filter(
        context=context, country_id=country_id
    ).first()
    return context_config
