import abc
from collections import defaultdict
from typing import Tu<PERSON>, Union

import structlog
from django.db.models import F, OuterRef, Prefetch, Subquery
from django.utils import timezone
from phonenumber_field.phonenumber import to_python as to_python_phone_number

from common.constants import CustomFieldTypeChoices
from common.services import validate_on_regex
from controlroom.data.models import OrganizationDocumentConfig
from core.choices import OrganizationDocumentChoices
from core.entities import (
    CountryData,
    CurrencyData,
    OrgUserEntity,
    TaxTypeData,
    TimezoneData,
)
from core.models import (
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    Organization,
    OrganizationAddress,
    OrganizationConfig,
    OrganizationDocument,
)
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.data.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationBillingEntity,
    LinkedOrganizationBusinessCard,
    LinkedOrganizationDocument,
    LinkedOrganizationDocumentFileFieldFieldData,
    LinkedOrganizationDocumentLongTextFieldData,
    LinkedOrganizationDocumentPhoneNumberFieldData,
    LinkedOrganizationDocumentStateFieldData,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationBillingEntity,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFileFieldFieldData,
    OrganizationDocumentLongTextFieldData,
    OrganizationDocumentPhoneNumberFieldData,
    OrganizationDocumentStateFieldData,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
    OrganizationSectionConfig,
)
from core.organization.data.selectors import get_uid_field_by_org
from core.organization.domain.abstract_repos import LinkedOrganizationAbstractRepo, OrganizationAbstractRepo
from core.organization.domain.entities import (
    CityData,
    DocumentFieldValueBaseData,
    DocumentFileFieldValueData,
    DocumentLongTextFieldValueData,
    DocumentPhoneNumberFieldValueData,
    DocumentStateFieldValueData,
    DocumentTextFieldValueData,
    FileData,
    LinkedOrganizationBillingEntityBaseData,
    LinkedOrganizationBillingEntityData,
    OrganizationAddressData,
    OrganizationAddressUpdateData,
    OrganizationBasicDetailCountryData,
    OrganizationBasicDetailsUpdateData,
    OrganizationBasicDetailUIDData,
    OrganizationBillingEntityBaseData,
    OrganizationBillingEntityData,
    OrganizationBillingEntityDropdownData,
    OrganizationBusinessCardData,
    OrganizationDocumentConfigData,
    OrganizationDocumentData,
    OrganizationFieldData,
    OrganizationOrgConfigEntity,
    OrganizationSectionConfigData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
    PhoneNumberData,
    StateData,
)
from core.selectors import get_default_currency_tax_timezone
from core.utils import get_relative_path
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


class OrganizationRepo(OrganizationAbstractRepo):
    KEY_POC_NAME = "Key POC Name"

    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id
        self.current_time = timezone.now()

    def process_errors(self, error: dict) -> dict:
        processed_errors = {}
        for key, value in error.items():
            if any(value):
                processed_errors[key] = value
        return processed_errors

    def get_billing_entity(
        self,
        billing_entity_id: int | None = None,
        org_id: int | None = None,
    ) -> OrganizationBillingEntityBaseData:
        billing_entity = (
            OrganizationBillingEntity.objects.filter(organization_id=org_id or self.org_id)
            .select_related("country")
            .available()
        )

        if billing_entity_id is None:
            billing_entity = billing_entity.filter(is_primary=True).first()
        else:
            billing_entity = billing_entity.filter(id=billing_entity_id).first()

        if not billing_entity:
            raise self.BillingEntityNotFound("Legal entity not found")

        return OrganizationBillingEntityBaseData(
            id=billing_entity.pk,
            name=billing_entity.name,
            is_primary=billing_entity.is_primary,
            is_default=billing_entity.is_default,
            is_active=billing_entity.is_active,
            logo=billing_entity.logo.url if billing_entity.logo else None,
            country=OrganizationBasicDetailCountryData(
                id=billing_entity.country.pk,
                name=billing_entity.country.name,
            ),
        )

    def get_billing_entities(self, billing_entity_id: int | None) -> list[OrganizationBillingEntityData]:
        billing_entities = OrganizationBillingEntity.objects.filter(
            organization_id=self.org_id,
            deleted_at__isnull=True,
        ).order_by("created_at")

        if billing_entity_id:
            billing_entities = billing_entities.filter(id=billing_entity_id)

        text_data = (
            OrganizationDocumentTextFieldData.objects.filter(
                document__billing_entity_id__in=[be.pk for be in billing_entities]
            )
            .select_related(
                "field_config",
                "document__billing_entity",
                "field_config__document_config__country__uid_field",
            )
            .values(
                "document__billing_entity__id",
                "data",
                "field_config__name",
                "field_config__id",
                "field_config__document_config__country__uid_field__id",
                "field_config__document_config__country__uid_field__name",
            )
        )

        poc_data_map = {}
        uid_data_map = {}
        uid_name_map = {}

        for item in text_data:
            entity_id = item["document__billing_entity__id"]
            field_name = item["field_config__name"]
            field_id = item["field_config__id"]
            uid_field_id = item["field_config__document_config__country__uid_field__id"]
            uid_field_name = item["field_config__document_config__country__uid_field__name"]
            data = item["data"]

            if field_name == self.KEY_POC_NAME:
                poc_data_map[entity_id] = data

            if field_id == uid_field_id:
                uid_data_map[entity_id] = data

            uid_name_map[entity_id] = uid_field_name

        return (
            [
                OrganizationBillingEntityData(
                    id=billing_entity.pk,
                    name=billing_entity.name,
                    is_primary=billing_entity.is_primary,
                    is_default=billing_entity.is_default,
                    is_active=billing_entity.is_active,
                    poc_name=poc_data_map.get(billing_entity.pk, None),
                    uid=uid_data_map.get(billing_entity.pk, None),
                    uid_name=uid_name_map.get(billing_entity.pk, None),
                )
                for billing_entity in billing_entities
            ]
            if billing_entities
            else []
        )

    def get_billing_entity_dropdown_list(self) -> list[OrganizationBillingEntityDropdownData]:
        billing_entities = (
            OrganizationBillingEntity.objects.filter(
                organization_id=self.org_id,
                deleted_at__isnull=True,
            )
            .order_by("created_at")
            .values("id", "name", "is_default")
        )

        return [
            OrganizationBillingEntityDropdownData(
                id=billing_entity["id"],
                name=billing_entity["name"],
                auto_select=billing_entity["is_default"],
            )
            for billing_entity in billing_entities
        ]

    def update_addresses(self, addresses: list[OrganizationAddressUpdateData], billing_entity_id: int):
        current_addresses_ids = OrganizationAddress.objects.filter(billing_entity_id=billing_entity_id).values_list(
            "id", flat=True
        )

        created_addresses = []
        updated_addresses = []
        updated_addresses_ids = []
        for address_data in addresses:
            if address_data.id and address_data.id in current_addresses_ids:
                updated_addresses.append(
                    OrganizationAddress(
                        id=address_data.id,
                        organization_id=self.org_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        updated_by_id=self.user_id,
                        updated_at=self.current_time,
                        billing_entity_id=billing_entity_id,
                    )
                )
                updated_addresses_ids.append(address_data.id)
            else:
                created_addresses.append(
                    OrganizationAddress(
                        organization_id=self.org_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        created_by_id=self.user_id,
                        billing_entity_id=billing_entity_id,
                    )
                )
        removed_addresses_ids = set(current_addresses_ids) - set(updated_addresses_ids)
        OrganizationAddress.objects.bulk_create(created_addresses)
        OrganizationAddress.objects.bulk_update(
            updated_addresses,
            fields=[
                "address_line_1",
                "address_line_2",
                "city_id",
                "state_id",
                "country_id",
                "zip_code",
                "updated_by_id",
                "updated_at",
            ],
        )

        OrganizationAddress.objects.filter(id__in=removed_addresses_ids).update(
            deleted_by_id=self.user_id,
            deleted_at=self.current_time,
        )

    def create_business_card(self, data: OrganizationBusinessCardData, billing_entity_id: int) -> None:
        logger.info("Creating organization business card", org_id=self.org_id, data=data)
        document = OrganizationDocument()
        document.organization_id = self.org_id
        document.type = OrganizationDocumentChoices.BUSINESS_CARD
        document.name = data.file_name
        document.file = get_relative_path(data.file)
        document.billing_entity_id = billing_entity_id
        document.uploaded_by_id = self.user_id

        document.save()

        logger.info("Organization business card created", document_id=document.pk)

    def update_business_card(self, data: OrganizationBusinessCardData, billing_entity_id: int) -> None:
        logger.info("Updating organization business card", org_id=self.org_id, data=data)

        OrganizationDocument.objects.filter(
            type=OrganizationDocumentChoices.BUSINESS_CARD,
            organization_id=self.org_id,
            billing_entity_id=billing_entity_id,
            id=data.id,
        ).available().update(
            name=data.file_name,
            file=data.file,
        )

    def delete_business_card(self, billing_entity_id: int) -> None:
        logger.info("Deleting organization business card", org_id=self.org_id)

        OrganizationDocument.objects.filter(
            type=OrganizationDocumentChoices.BUSINESS_CARD,
            organization_id=self.org_id,
            billing_entity_id=billing_entity_id,
        ).soft_delete(self.user_id)

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, billing_entity_id: int) -> None:
        if data.business_card:
            if data.business_card.id:
                self.update_business_card(data.business_card, billing_entity_id=billing_entity_id)
            else:
                self.create_business_card(data.business_card, billing_entity_id=billing_entity_id)
        else:
            self.delete_business_card(billing_entity_id=billing_entity_id)

        if data.addresses:
            self.update_addresses(addresses=data.addresses, billing_entity_id=billing_entity_id)

        OrganizationBillingEntity.objects.filter(id=billing_entity_id).update(
            name=data.name,
            updated_by_id=self.user_id,
            updated_at=self.current_time,
        )

    def get_section_configs(self, country_id: int | None = None):
        logger.info("Fetching organization sections config", org_id=self.org_id, country_id=country_id)
        if not country_id:
            country_ids = Organization.objects.filter(id=self.org_id).values_list("country_id", flat=True)
            queryset = OrganizationSectionConfig.objects.filter(country_id__in=country_ids)
        else:
            queryset = OrganizationSectionConfig.objects.filter(country_id=country_id)
        return queryset.prefetch_related(
            Prefetch(
                "documents_config",
                OrganizationDocumentConfig.objects.all()
                .prefetch_related(
                    Prefetch("fields_config", OrganizationDocumentFieldConfig.objects.all().order_by("position"))
                )
                .order_by("position"),
            )
        ).all()

    def get_section_configs_entity(self, country_id: int | None = None) -> list[OrganizationSectionConfigData]:
        section_configs = self.get_section_configs(country_id=country_id)

        sections = []

        for section_config in section_configs:
            documents = []

            for doc_config in section_config.documents_config.all():
                fields = []
                for field_config in doc_config.fields_config.all():
                    field = OrganizationFieldData(
                        id=field_config.pk,
                        name=field_config.name,
                        type=field_config.type,
                        position=field_config.position,
                        is_required=field_config.is_required,
                        regex=field_config.regex,
                        is_visible_on_app=field_config.is_visible_on_app,
                        is_capitalized=field_config.is_capitalized,
                    )
                    fields.append(field)

                document = OrganizationDocumentConfigData(
                    id=doc_config.pk,
                    name=doc_config.name,
                    multiple_allowed=doc_config.multiple_allowed,
                    position=doc_config.position,
                    is_required=doc_config.is_required,
                    fields=fields,
                    is_visible_on_app=doc_config.is_visible_on_app,
                )
                documents.append(document)

            sections.append(
                OrganizationSectionConfigData(
                    id=section_config.pk,
                    name=section_config.name,
                    position=section_config.position,
                    type=section_config.type,
                    documents=documents,
                )
            )

        return sections

    def get_documents_by_section(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
    ):
        text_field_data = Prefetch(
            "text_field_data",
            OrganizationDocumentTextFieldData.objects.all().order_by("field_config__position").available(),
        )
        file_field_data = Prefetch(
            "file_field_data",
            OrganizationDocumentFileFieldFieldData.objects.all().order_by("field_config__position").available(),
        )
        state_field_data = Prefetch(
            "state_field_data",
            OrganizationDocumentStateFieldData.objects.all()
            .order_by("field_config__position")
            .select_related("state")
            .available(),
        )
        phone_number_field_data = Prefetch(
            "phone_number_field_data",
            OrganizationDocumentPhoneNumberFieldData.objects.all().order_by("field_config__position").available(),
        )
        long_field_data = Prefetch(
            "long_text_field_data",
            OrganizationDocumentLongTextFieldData.objects.all().order_by("field_config__position").available(),
        )

        return (
            OrganizationDocumentV2.objects.filter(
                organization_id=self.org_id,
                billing_entity_id=billing_entity_id,
                document_config__section__type=section_type.value,
            )
            .all()
            .order_by("created_at")
            .prefetch_related(
                text_field_data,
                file_field_data,
                state_field_data,
                phone_number_field_data,
                long_field_data,
            )
            .available()
        )

    def get_section_details(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
        country_id: int,
    ) -> OrganizationSectionData:
        section_config = self.get_section_configs(country_id=country_id).filter(type=section_type.value).first()
        org_docs = self.get_documents_by_section(section_type=section_type, billing_entity_id=billing_entity_id)

        doc_config_org_doc_mapping: dict[int, list[OrganizationDocumentV2]] = defaultdict(list)
        for org_doc in org_docs:
            doc_config_org_doc_mapping[org_doc.document_config_id].append(org_doc)

        org_doc_id_field_config_id_value_mapping: dict[
            tuple(int, int), DocumentFieldValueBaseData
        ] = {}  # dict[(org_doc_id, field_config_id)] = field_value
        for org_doc in org_docs:
            for field_data in org_doc.text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

            for field_data in org_doc.file_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentFileFieldValueData(
                        id=field_data.pk,
                        data=(
                            FileData(file_name=field_data.file_name, file=field_data.file.url)
                            if field_data.file_name and field_data.file.name
                            else None
                        ),
                    )
                )

            for field_data in org_doc.state_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentStateFieldValueData(
                        id=field_data.pk, data=StateData(id=field_data.state.pk, name=field_data.state.name)
                    )
                )

            for field_data in org_doc.phone_number_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentPhoneNumberFieldValueData(
                        id=field_data.pk,
                        data=PhoneNumberData(
                            country_code=f"+{field_data.data.country_code}",
                            number=f"{field_data.data.national_number}",
                        )
                        if field_data.data
                        else None,
                    )
                )

            for field_data in org_doc.long_text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentLongTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

        documents: list[OrganizationDocumentData] = []
        for doc_config in section_config.documents_config.all():
            fields: list[OrganizationFieldData] = []
            fields_data_list: list[dict[int, DocumentFieldValueBaseData]] = []

            for field_config in doc_config.fields_config.all():
                field = OrganizationFieldData(
                    id=field_config.pk,
                    name=field_config.name,
                    type=field_config.type,
                    position=field_config.position,
                    is_required=field_config.is_required,
                    regex=field_config.regex,
                    is_visible_on_app=field_config.is_visible_on_app,
                    is_capitalized=field_config.is_capitalized,
                )
                fields.append(field)

            for org_doc in doc_config_org_doc_mapping[doc_config.pk]:
                field_data_dict: dict[Union[str, int], Union[int, DocumentFieldValueBaseData]] = defaultdict()
                for field_config in doc_config.fields_config.all():
                    if (org_doc.pk, field_config.pk) in org_doc_id_field_config_id_value_mapping:
                        field_data_dict[field_config.pk] = org_doc_id_field_config_id_value_mapping[
                            (org_doc.pk, field_config.pk)
                        ]
                field_data_dict["id"] = org_doc.pk
                fields_data_list.append(field_data_dict)

            document = OrganizationDocumentData(
                id=doc_config.pk,
                name=doc_config.name,
                multiple_allowed=doc_config.multiple_allowed,
                position=doc_config.position,
                is_required=doc_config.is_required,
                fields=fields,
                fields_data=fields_data_list,
                is_visible_on_app=doc_config.is_visible_on_app,
            )
            documents.append(document)

        section = OrganizationSectionData(
            id=section_config.pk,
            name=section_config.name,
            position=section_config.position,
            type=section_config.type,
            documents=documents,
        )
        return section

    def update_sections_data(
        self,
        data: OrganizationSectionUpdateData,
        billing_entity_id: int,
        is_primary_billing_entity: bool = False,
    ):
        logger.info(
            "Updating organization section data",
            org_id=self.org_id,
            billing_entity_id=billing_entity_id,
            data=data,
        )

        doc_config_id_values_mapping: dict[int, list[dict]] = {}
        updated_field_config_ids = []
        section_ids = []
        errors = {}
        is_data_valid = True
        field_configs: dict[int, OrganizationDocumentFieldConfig] = (
            OrganizationDocumentFieldConfig.objects.all().in_bulk()
        )
        uid_field_config = get_uid_field_by_org(org_id=self.org_id)
        uid_value = None
        for section_id, section_value in data.sections.items():
            section_ids.append(section_id)
            for doc_id, doc_values in section_value.items():
                doc_config_id_values_mapping[doc_id] = doc_values
                for doc_val in doc_values:
                    updated_field_config_ids.extend([key for key in doc_val.keys() if key != "id"])
                    for field_config_id, field_val in doc_val.items():
                        if field_config_id == "id":
                            continue
                        config = field_configs.get(field_config_id)
                        error = ""
                        if (
                            config.regex
                            and field_val.get("data")
                            and not validate_on_regex(regex=config.regex, value=field_val.get("data"))
                        ):
                            error = f"Please enter valid {config.name}"
                            is_data_valid = False

                        if config.type == CustomFieldTypeChoices.PHONE_NUMBER and field_val.get("data"):
                            if not to_python_phone_number(field_val.get("data")).is_valid():
                                error = f"Please enter valid {config.name}"
                                is_data_valid = False

                        if uid_field_config and field_config_id == uid_field_config.pk:
                            uid_value = field_val.get("data")
                        if HashIdConverter.encode(field_config_id) in errors:
                            errors[HashIdConverter.encode(field_config_id)].append(error)
                        else:
                            errors[HashIdConverter.encode(field_config_id)] = [error]

        if not is_data_valid:
            raise self.SectionUpdateException(self.process_errors(errors))
        if uid_value:
            if is_primary_billing_entity:
                org = Organization.objects.filter(id=self.org_id).first()

                if not org:
                    raise self.OrgNotFound("Organization not found")

                if org.uid is None:
                    logger.info(
                        "Updating UID for primary billing entity",
                        org_id=self.org_id,
                        uid_value=uid_value,
                    )
                    org.uid = uid_value
                    org.save()

            vendor_mapping_ids = LinkedOrganizations.objects.filter(
                client_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True
            ).values_list("id", flat=True)
            client_mapping_ids = LinkedOrganizations.objects.filter(
                vendor_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True
            ).values_list("id", flat=True)
            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=vendor_mapping_ids,
                field_config_id=F("document__mapping__vendor__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config.pk)] = [
                    f"This {uid_field_config.name} is already used (type 3)"
                ]
                raise self.SectionUpdateException(self.process_errors(errors))

            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=client_mapping_ids,
                field_config_id=F("document__mapping__client__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config.pk)] = [
                    f"This {uid_field_config.name} is already used (type 4)"
                ]
                raise self.SectionUpdateException(self.process_errors(errors))

        Organization.objects.filter(id=self.org_id).update(pan_number=uid_value)
        updated_field_config_ids = list(updated_field_config_ids)

        created_documents = []
        for doc_config_id, doc_vals in doc_config_id_values_mapping.items():
            for doc_val in doc_vals:
                if doc_val.get("id") is None:
                    created_documents.append(
                        OrganizationDocumentV2(
                            organization_id=self.org_id,
                            billing_entity_id=billing_entity_id,
                            document_config_id=doc_config_id,
                            created_by_id=self.user_id,
                        )
                    )
        if created_documents:
            created_documents = OrganizationDocumentV2.objects.bulk_create(created_documents)

        doc_config_new_created_document_mapping = defaultdict(list)
        for doc in created_documents:
            doc_config_new_created_document_mapping[doc.document_config_id].append(doc.pk)

        current_document_ids = (
            OrganizationDocumentV2.objects.filter(
                organization_id=self.org_id,
                billing_entity_id=billing_entity_id,
                document_config_id__in=doc_config_id_values_mapping.keys(),
            )
            .available()
            .values_list("id", flat=True)
        )

        current_text_field_value_ids = (
            OrganizationDocumentTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_file_field_value_ids = (
            OrganizationDocumentFileFieldFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_state_field_value_ids = (
            OrganizationDocumentStateFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_phone_number_field_value_ids = (
            OrganizationDocumentPhoneNumberFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_long_text_field_value_ids = (
            OrganizationDocumentLongTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )

        updated_document_ids = []

        created_text_field_values = []
        created_file_field_values = []
        created_state_field_values = []
        created_phone_number_field_values = []
        created_long_text_field_values = []

        updated_text_field_values = []
        updated_text_field_value_ids = []
        updated_file_field_values = []
        updated_file_field_value_ids = []
        updated_state_field_values = []
        updated_state_field_value_ids = []
        updated_phone_number_field_values = []
        updated_phone_number_field_value_ids = []
        updated_long_text_field_values = []
        updated_long_text_field_value_ids = []

        for doc_config_id, doc_values in doc_config_id_values_mapping.items():
            for doc_val in doc_values:
                if not doc_val.get("id"):
                    document_id = doc_config_new_created_document_mapping[doc_config_id].pop()
                else:
                    document_id = doc_val["id"]
                    updated_document_ids.append(document_id)
                doc_val.pop("id", None)
                for field_config_id, field_val in doc_val.items():
                    field_config = field_configs[field_config_id]
                    if field_config.type == CustomFieldTypeChoices.TEXT:
                        # TODO: Have to add a logic for checking field_val.get("id") is correct
                        # it must to value against field_config_id and document
                        if field_val.get("id"):
                            updated_text_field_values.append(
                                OrganizationDocumentTextFieldData(
                                    id=field_val["id"],
                                    field_config_id=field_config_id,
                                    document_id=document_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=self.current_time,
                                )
                            )
                            updated_text_field_value_ids.append(field_val["id"])
                        else:
                            created_text_field_values.append(
                                OrganizationDocumentTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.FILE:
                        if field_val.get("id"):
                            updated_file_field_values.append(
                                OrganizationDocumentFileFieldFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    updated_by_id=self.user_id,
                                    updated_at=self.current_time,
                                )
                            )
                            updated_file_field_value_ids.append(field_val["id"])
                        else:
                            created_file_field_values.append(
                                OrganizationDocumentFileFieldFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.STATE:
                        if field_val.get("id"):
                            updated_state_field_values.append(
                                OrganizationDocumentStateFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    updated_by_id=self.user_id,
                                    updated_at=self.current_time,
                                )
                            )
                            updated_state_field_value_ids.append(field_val["id"])
                        else:
                            created_state_field_values.append(
                                OrganizationDocumentStateFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.PHONE_NUMBER:
                        if field_val.get("id"):
                            updated_phone_number_field_values.append(
                                OrganizationDocumentPhoneNumberFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=self.current_time,
                                )
                            )
                            updated_phone_number_field_value_ids.append(field_val["id"])
                        else:
                            created_phone_number_field_values.append(
                                OrganizationDocumentPhoneNumberFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.LONG_TEXT:
                        if field_val.get("id"):
                            updated_long_text_field_values.append(
                                OrganizationDocumentLongTextFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=self.current_time,
                                )
                            )
                            updated_long_text_field_value_ids.append(field_val["id"])
                        else:
                            created_long_text_field_values.append(
                                OrganizationDocumentLongTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    else:
                        raise Exception("Unknown field type")

        removed_text_field_ids = set(current_text_field_value_ids) - set(updated_text_field_value_ids)
        removed_file_field_ids = set(current_file_field_value_ids) - set(updated_file_field_value_ids)
        removed_state_field_ids = set(current_state_field_value_ids) - set(updated_state_field_value_ids)
        removed_phone_number_field_ids = set(current_phone_number_field_value_ids) - set(
            updated_phone_number_field_value_ids
        )
        removed_long_text_field_ids = set(current_long_text_field_value_ids) - set(updated_long_text_field_value_ids)

        removed_document_ids = (
            set(current_document_ids) - set(updated_document_ids) - set([doc.id for doc in created_documents])
        )
        OrganizationDocumentV2.objects.filter(id__in=removed_document_ids).soft_delete(user_id=self.user_id)

        OrganizationDocumentTextFieldData.objects.bulk_create(created_text_field_values)
        OrganizationDocumentTextFieldData.objects.bulk_update(updated_text_field_values, ["data"])

        OrganizationDocumentFileFieldFieldData.objects.bulk_create(created_file_field_values)
        OrganizationDocumentFileFieldFieldData.objects.bulk_update(updated_file_field_values, ["file", "file_name"])

        OrganizationDocumentStateFieldData.objects.bulk_create(created_state_field_values)
        OrganizationDocumentStateFieldData.objects.bulk_update(updated_state_field_values, ["state_id"])

        OrganizationDocumentPhoneNumberFieldData.objects.bulk_create(created_phone_number_field_values)
        OrganizationDocumentPhoneNumberFieldData.objects.bulk_update(updated_phone_number_field_values, ["data"])

        OrganizationDocumentLongTextFieldData.objects.bulk_create(created_long_text_field_values)
        OrganizationDocumentLongTextFieldData.objects.bulk_update(updated_long_text_field_values, ["data"])

        OrganizationDocumentTextFieldData.objects.filter(id__in=removed_text_field_ids).soft_delete(
            user_id=self.user_id
        )
        OrganizationDocumentFileFieldFieldData.objects.filter(id__in=removed_file_field_ids).soft_delete(
            user_id=self.user_id
        )
        OrganizationDocumentStateFieldData.objects.filter(id__in=removed_state_field_ids).soft_delete(
            user_id=self.user_id
        )
        OrganizationDocumentPhoneNumberFieldData.objects.filter(id__in=removed_phone_number_field_ids).soft_delete(
            user_id=self.user_id
        )
        OrganizationDocumentLongTextFieldData.objects.filter(id__in=removed_long_text_field_ids).soft_delete(
            user_id=self.user_id
        )

        logger.info("Organization section data updated")

    def get_allowed_users(self) -> int | None:
        org = OrganizationConfig.objects.filter(organization_id=self.org_id).first()
        if org is not None:
            return org.allowed_users
        return None

    def get_addresses(self, billing_entity_id: int | None = None, org_id: int | None = None):
        org_addresses = (
            OrganizationAddress.objects.select_related("country", "state", "city")
            .order_by("id")
            .filter(organization_id=org_id or self.org_id)
        )

        if billing_entity_id:
            org_addresses = org_addresses.filter(billing_entity_id=billing_entity_id)

        addresses: list[OrganizationAddressData] = []
        for address in org_addresses:
            addresses.append(
                OrganizationAddressData(
                    id=address.pk,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city=(CityData(id=address.city.pk, name=address.city.name) if address.city else None),
                    state=(StateData(id=address.state.pk, name=address.state.name) if address.state else None),
                    country=(
                        CountryData(id=address.country.pk, name=address.country.name) if address.country else None
                    ),
                    zip_code=address.zip_code,
                    value=address.full_address,
                )
            )

        return addresses

    def get_business_card(self, billing_entity_id: int):
        business_card = (
            OrganizationDocument.objects.filter(
                billing_entity_id=billing_entity_id,
                type=OrganizationDocumentChoices.BUSINESS_CARD,
            )
            .available()
            .first()
        )

        return (
            OrganizationBusinessCardData(
                id=business_card.pk,
                file=business_card.file.url,
                file_name=business_card.name,
            )
            if business_card
            else None
        )

    def get_org_config(self) -> OrganizationOrgConfigEntity:
        config: OrganizationConfig | None = (
            OrganizationConfig.objects.filter(organization_id=self.org_id)
            .select_related("organization", "organization__country")
            .annotate(
                max_slab_percent=Subquery(
                    CountryTaxMapping.objects.filter(
                        country_id=OuterRef("organization__country_id"), tax_type_id=OuterRef("tax_type_id")
                    ).values("max_slab_percent")[:1]
                )
            )
            .first()
        )
        default_currency_data, default_tax_data, default_timezone_data = get_default_currency_tax_timezone()

        if config:
            if config.max_slab_percent is None:
                logger.error(
                    f"Max slab percent is None for config: {config.pk}, org_id: {org_id}, orgs country: {config.organization.country_id}, tax_type: {config.tax_type_id}"  # noqa
                )
                raise ValueError("Max slab percent is None")
            return OrganizationOrgConfigEntity(
                currency=(
                    CurrencyData(
                        id=config.currency_id,
                        name=config.currency.name,
                        symbol=config.currency.symbol,
                        code=config.currency.code,
                        locale=config.currency.locale,
                    )
                    if config.currency
                    else default_currency_data
                ),
                tax_type=(
                    TaxTypeData(
                        id=config.tax_type_id,
                        name=config.tax_type.name,
                        max_slab_percent=config.max_slab_percent,
                    )
                    if config.tax_type
                    else default_tax_data
                ),
                timezone=(
                    TimezoneData(
                        id=config.timezone_id,
                        name=config.timezone.name,
                        locale=config.timezone.locale,
                    )
                    if config.timezone
                    else default_timezone_data
                ),
                country=CountryData(
                    id=config.organization.country_id,
                    name=config.organization.country.name,
                ),
                uid_field=OrganizationBasicDetailUIDData(
                    value=config.organization.uid,
                    name=config.organization.country.uid_field.name,
                ),
            )
        org = (
            Organization.objects.filter(id=self.org_id)
            .select_related("country")
            .prefetch_related(
                Prefetch(
                    "country__country_timezone_mapping",
                    CountryTimezoneMapping.objects.filter(is_default=True),
                ),
                Prefetch(
                    "country__country_tax_mapping",
                    CountryTaxMapping.objects.filter(is_default=True),
                ),
                Prefetch(
                    "country__country_currency_mapping",
                    CountryCurrencyMapping.objects.filter(is_default=True),
                ),
            )
            .first()
        )

        currency = org.country.country_currency_mapping.all().first().currency
        tax_type_mapping = org.country.country_tax_mapping.all().first()
        tax_type = tax_type_mapping.tax_type
        timezone = org.country.country_timezone_mapping.all().first().timezone

        return OrganizationOrgConfigEntity(
            currency=(
                CurrencyData(
                    id=currency.pk,
                    name=currency.name,
                    symbol=currency.symbol,
                    code=currency.code,
                    locale=currency.locale,
                )
                if currency
                else default_currency_data
            ),
            tax_type=(
                TaxTypeData(
                    id=tax_type.pk,
                    name=tax_type.name,
                    max_slab_percent=tax_type_mapping.max_slab_percent,
                )
                if tax_type
                else default_tax_data
            ),
            timezone=(
                TimezoneData(
                    id=timezone.pk,
                    name=timezone.name,
                    locale=timezone.locale,
                )
                if timezone
                else default_timezone_data
            ),
            country=CountryData(
                id=org.country_id,
                name=org.country.name,
            ),
            uid_field=OrganizationBasicDetailUIDData(
                value=org.uid,
                name=org.country.uid_field.name,
            ),
        )

    def create_billing_entity(self, name: str, country_id: int) -> OrganizationBillingEntityBaseData:
        logger.info("Creating organization billing entity", org_id=self.org_id, name=name)

        billing_entity = OrganizationBillingEntity()
        billing_entity.organization_id = self.org_id
        billing_entity.name = name
        billing_entity.created_by_id = self.user_id
        billing_entity.country_id = country_id

        billing_entity.clean()

        billing_entity.save()

        logger.info("Organization billing entity created", billing_entity_id=billing_entity.pk)

        return OrganizationBillingEntityBaseData(
            id=billing_entity.pk,
            name=billing_entity.name,
            is_primary=billing_entity.is_primary,
            is_default=billing_entity.is_default,
            is_active=billing_entity.is_active,
            logo=billing_entity.logo.url if billing_entity.logo else None,
            country=OrganizationBasicDetailCountryData(
                id=billing_entity.country.pk,
                name=billing_entity.country.name,
            ),
        )

    def mark_billing_entity_default(self, billing_entity_id: int, is_default: bool):
        OrganizationBillingEntity.objects.filter(organization_id=self.org_id).update(is_default=False)

        if is_default:
            OrganizationBillingEntity.objects.filter(
                organization_id=self.org_id,
                id=billing_entity_id,
            ).update(is_default=True)

    def mark_billing_entity_active(self, billing_entity_id: int, is_active: bool) -> None:
        OrganizationBillingEntity.objects.filter(
            organization_id=self.org_id,
            id=billing_entity_id,
        ).update(is_active=is_active)

    def delete_billing_entity(self, billing_entity_id: int) -> None:
        OrganizationBillingEntity.objects.filter(
            organization_id=self.org_id,
            id=billing_entity_id,
        ).soft_delete(user_id=self.user_id)

    def update_billing_entity_logo(self, billing_entity_id: int, is_primary: bool, logo: str | None):
        if logo is not None:
            logo = get_relative_path(logo)

        OrganizationBillingEntity.objects.filter(id=billing_entity_id).update(logo=logo)

        if is_primary:
            Organization.objects.filter(id=self.org_id).update(logo=logo)


class LinkedOrganizationRepo(LinkedOrganizationAbstractRepo):
    used_as_client = False
    used_as_vendor = False

    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id
        self.mapping = self.get_mapping()
        self.current_time = timezone.now()

    @abc.abstractmethod
    def get_mapping(self) -> LinkedOrganizations:
        pass

    def process_errors(self, error: dict) -> dict:
        processed_errors = {}
        for key, value in error.items():
            if any(value):
                processed_errors[key] = value
        return processed_errors

    def get_billing_entity(self, billing_entity_id: int | None = None) -> LinkedOrganizationBillingEntityBaseData:
        billing_entity = (
            LinkedOrganizationBillingEntity.objects.filter(mapping_id=self.mapping.pk)
            .select_related("country")
            .available()
        )

        if billing_entity_id is None:
            billing_entity = billing_entity.filter(is_primary=True).first()
        else:
            billing_entity = billing_entity.filter(id=billing_entity_id).first()

        if not billing_entity:
            raise self.BillingEntityNotFound("Billing entity not found")

        return LinkedOrganizationBillingEntityBaseData(
            id=billing_entity.pk,
            name=billing_entity.name,
            is_primary=billing_entity.is_primary,
            is_default=billing_entity.is_default,
            is_active=billing_entity.is_active,
            logo=billing_entity.logo.url if billing_entity.logo else None,
            country=OrganizationBasicDetailCountryData(
                id=billing_entity.country.id,
                name=billing_entity.country.name,
            ),
        )

    def get_linked_addresses(self, billing_entity_id: int):
        linked_addresses = (
            LinkedOrganizationAddress.objects.filter(billing_entity_id=billing_entity_id)
            .select_related("country", "state", "city")
            .available()
            .order_by("id")
        )

        addresses: list[OrganizationAddressData] = []
        for address in linked_addresses:
            addresses.append(
                OrganizationAddressData(
                    id=address.pk,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city=(CityData(id=address.city.pk, name=address.city.name) if address.city else None),
                    state=(StateData(id=address.state.pk, name=address.state.name) if address.state else None),
                    country=(
                        CountryData(id=address.country.pk, name=address.country.name) if address.country else None
                    ),
                    zip_code=address.zip_code,
                    value=address.full_address,
                )
            )

        return addresses

    def get_linked_business_card(self, billing_entity_id: int):
        business_card = (
            LinkedOrganizationBusinessCard.objects.filter(billing_entity_id=billing_entity_id).available().first()
        )

        return (
            OrganizationBusinessCardData(
                id=business_card.pk,
                file=business_card.file.url if business_card.file else None,
                file_name=business_card.name,
            )
            if business_card
            else None
        )

    def update_addresses(self, addresses: list[OrganizationAddressUpdateData], billing_entity_id: int):
        current_addresses_ids = (
            LinkedOrganizationAddress.objects.filter(billing_entity_id=billing_entity_id)
            .available()
            .values_list("id", flat=True)
        )
        created_addresses = []
        updated_addresses = []
        updated_addresses_ids = []
        for address_data in addresses:
            if address_data.id and address_data.id in current_addresses_ids:
                updated_addresses.append(
                    LinkedOrganizationAddress(
                        id=address_data.id,
                        mapping_id=self.mapping.pk,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        updated_by_id=self.user_id,
                        updated_at=self.current_time,
                        billing_entity_id=billing_entity_id,
                    )
                )
                updated_addresses_ids.append(address_data.id)
            else:
                created_addresses.append(
                    LinkedOrganizationAddress(
                        mapping_id=self.mapping.pk,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        created_by_id=self.user_id,
                        billing_entity_id=billing_entity_id,
                    )
                )
        removed_addresses_ids = set(current_addresses_ids) - set(updated_addresses_ids)
        LinkedOrganizationAddress.objects.bulk_create(created_addresses)
        LinkedOrganizationAddress.objects.bulk_update(
            updated_addresses,
            fields=[
                "address_line_1",
                "address_line_2",
                "city_id",
                "state_id",
                "country_id",
                "zip_code",
                "updated_by_id",
                "updated_at",
            ],
        )
        LinkedOrganizationAddress.objects.filter(id__in=removed_addresses_ids).update(
            deleted_by_id=self.user_id,
            deleted_at=self.current_time,
        )

    def create_business_card(self, data: OrganizationBusinessCardData, billing_entity_id: int):
        logger.info("Creating linked organization business card", org_id=self.org_id, data=data)

        business_card = LinkedOrganizationBusinessCard()
        business_card.mapping_id = self.mapping.pk
        business_card.billing_entity_id = billing_entity_id
        business_card.file = get_relative_path(data.file)
        business_card.name = data.file_name
        business_card.created_by_id = self.user_id
        business_card.save()

        logger.info("Linked organization business card created", business_card_id=business_card.pk)

    def update_business_card(self, data: OrganizationBusinessCardData, billing_entity_id: int) -> None:
        logger.info("Updating linked organization business card", org_id=self.org_id, data=data)

        LinkedOrganizationBusinessCard.objects.filter(billing_entity_id=billing_entity_id).available().update(
            name=data.file_name,
            file=get_relative_path(data.file),
            updated_by_id=self.user_id,
            updated_at=self.current_time,
        )

    def delete_business_card(self, billing_entity_id: int) -> None:
        logger.info("Deleting linked organization business card", org_id=self.org_id)

        LinkedOrganizationBusinessCard.objects.filter(billing_entity_id=billing_entity_id).soft_delete(self.user_id)

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, billing_entity_id: int):
        if data.business_card:
            if data.business_card.id:
                self.update_business_card(data.business_card, billing_entity_id)
            else:
                self.create_business_card(data.business_card, billing_entity_id)
        else:
            self.delete_business_card(billing_entity_id)

        if data.addresses:
            self.update_addresses(data.addresses, billing_entity_id)

        LinkedOrganizationBillingEntity.objects.filter(id=billing_entity_id).update(
            name=data.name,
            updated_by_id=self.user_id,
            updated_at=self.current_time,
        )

    def get_section_configs(self, section_type: OrganizationSectionTypeEnum, country_id: int | None = None):
        logger.info("Fetching organization sections config", org_id=self.org_id, country_id=country_id)
        if not country_id:
            country_ids = Organization.objects.filter(id=self.org_id).values_list("country_id", flat=True)
            queryset = OrganizationSectionConfig.objects.filter(country_id__in=country_ids)
        else:
            queryset = OrganizationSectionConfig.objects.filter(country_id=country_id)
        return (
            queryset.prefetch_related(
                Prefetch(
                    "documents_config",
                    OrganizationDocumentConfig.objects.all()
                    .prefetch_related(
                        Prefetch("fields_config", OrganizationDocumentFieldConfig.objects.all().order_by("position"))
                    )
                    .order_by("position"),
                )
            )
            .all()
            .filter(type=section_type.value)
            .first()
        )

    def get_documents_by_section(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
    ):
        text_field_data = Prefetch(
            "text_field_data",
            LinkedOrganizationDocumentTextFieldData.objects.all().order_by("field_config__position").available(),
        )
        file_field_data = Prefetch(
            "file_field_data",
            LinkedOrganizationDocumentFileFieldFieldData.objects.all().order_by("field_config__position").available(),
        )
        state_field_data = Prefetch(
            "state_field_data",
            LinkedOrganizationDocumentStateFieldData.objects.all()
            .order_by("field_config__position")
            .select_related("state")
            .available(),
        )
        phone_number_field_data = Prefetch(
            "phone_number_field_data",
            LinkedOrganizationDocumentPhoneNumberFieldData.objects.all().order_by("field_config__position").available(),
        )
        long_text_field_data = Prefetch(
            "long_text_field_data",
            LinkedOrganizationDocumentLongTextFieldData.objects.all().order_by("field_config__position").available(),
        )

        return (
            LinkedOrganizationDocument.objects.filter(
                billing_entity_id=billing_entity_id,
                document_config__section__type=section_type.value,
            )
            .all()
            .order_by("created_at")
            .prefetch_related(
                text_field_data,
                file_field_data,
                state_field_data,
                phone_number_field_data,
                long_text_field_data,
            )
            .available()
        )

    def get_section_details(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
        country_id: int,
    ) -> OrganizationSectionData:
        section_config = self.get_section_configs(section_type=section_type, country_id=country_id)
        doc_data = self.get_documents_by_section(section_type=section_type, billing_entity_id=billing_entity_id)

        doc_config_org_doc_mapping: dict[int, list[LinkedOrganizationDocument]] = defaultdict(list)
        for org_doc in doc_data:
            doc_config_org_doc_mapping[org_doc.document_config_id].append(org_doc)

        org_doc_id_field_config_id_value_mapping: dict[Tuple[int, int], DocumentFieldValueBaseData] = {}
        for org_doc in doc_data:
            for field_data in org_doc.text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

            for field_data in org_doc.file_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentFileFieldValueData(
                        id=field_data.pk,
                        data=(
                            FileData(file_name=field_data.file_name, file=field_data.file.url)
                            if field_data.file_name and field_data.file.name
                            else None
                        ),
                    )
                )

            for field_data in org_doc.state_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentStateFieldValueData(
                        id=field_data.pk, data=StateData(id=field_data.state.pk, name=field_data.state.name)
                    )
                )

            for field_data in org_doc.phone_number_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentPhoneNumberFieldValueData(
                        id=field_data.pk,
                        data=PhoneNumberData(
                            country_code=f"+{field_data.data.country_code}",
                            number=f"{field_data.data.national_number}",
                        )
                        if field_data.data
                        else None,
                    )
                )

            for field_data in org_doc.long_text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentLongTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

        documents: list[OrganizationDocumentData] = []
        for doc_config in section_config.documents_config.all():
            fields: list[OrganizationFieldData] = []
            fields_data_list: list[dict[int, DocumentFieldValueBaseData]] = []

            for field_config in doc_config.fields_config.all():
                field = OrganizationFieldData(
                    id=field_config.pk,
                    name=field_config.name,
                    type=field_config.type,
                    position=field_config.position,
                    is_required=field_config.is_required,
                    regex=field_config.regex,
                    is_visible_on_app=field_config.is_visible_on_app,
                    is_capitalized=field_config.is_capitalized,
                )
                fields.append(field)

            for org_doc in doc_config_org_doc_mapping[doc_config.pk]:
                field_data_dict: dict[Union[str, int], Union[int, DocumentFieldValueBaseData]] = {}
                for field_config in doc_config.fields_config.all():
                    if (org_doc.pk, field_config.pk) in org_doc_id_field_config_id_value_mapping:
                        field_data_dict[field_config.pk] = org_doc_id_field_config_id_value_mapping[
                            (org_doc.pk, field_config.pk)
                        ]
                field_data_dict["id"] = org_doc.pk
                fields_data_list.append(field_data_dict)

            document = OrganizationDocumentData(
                id=doc_config.pk,
                name=doc_config.name,
                multiple_allowed=doc_config.multiple_allowed,
                position=doc_config.position,
                is_required=doc_config.is_required,
                fields=fields,
                fields_data=fields_data_list,
                is_visible_on_app=doc_config.is_visible_on_app,
            )
            documents.append(document)

        section = OrganizationSectionData(
            id=section_config.pk,
            name=section_config.name,
            position=section_config.position,
            type=section_config.type,
            documents=documents,
        )
        return section

    def update_sections_data(
        self,
        data: OrganizationSectionUpdateData,
        billing_entity_id: int,
        is_primary_billing_entity: bool = False,
    ):
        logger.info(
            "Updating linked organization section data",
            mapping_id=self.mapping.pk,
            billing_entity_id=billing_entity_id,
            data=data,
        )

        doc_config_id_values_mapping: dict[int, list[dict]] = {}
        updated_field_config_ids = []
        section_ids = []
        errors = defaultdict(list)
        is_data_valid = True
        field_configs: dict[int, OrganizationDocumentFieldConfig] = (
            OrganizationDocumentFieldConfig.objects.all().in_bulk()
        )
        uid_field_config_id = self.mapping.uid_field_id
        uid_field_config_name = self.mapping.uid_field_name
        uid_value = None

        for section_id, section_value in data.sections.items():
            section_ids.append(section_id)
            for doc_id, doc_values in section_value.items():
                doc_config_id_values_mapping[doc_id] = doc_values
                for doc_val in doc_values:
                    updated_field_config_ids.extend([key for key in doc_val.keys() if key != "id"])
                    for field_config_id, field_val in doc_val.items():
                        if field_config_id == "id":
                            continue
                        config = field_configs.get(field_config_id)
                        error = ""
                        if (
                            config.regex
                            and field_val.get("data")
                            and not validate_on_regex(regex=config.regex, value=field_val.get("data"))
                        ):
                            error = f"Please enter valid {config.name}"
                            is_data_valid = False

                        if config.type == CustomFieldTypeChoices.PHONE_NUMBER and field_val.get("data"):
                            if not to_python_phone_number(field_val.get("data")).is_valid():
                                error = f"Please enter valid {config.name}"
                                is_data_valid = False

                        if uid_field_config_id and uid_field_config_id == field_config_id:
                            uid_value = field_val.get("data")
                        if error:
                            errors[HashIdConverter.encode(field_config_id)].append(error)

        if not is_data_valid:
            raise self.SectionUpdateException(errors)

        if uid_value:
            if is_primary_billing_entity:
                org = Organization.objects.filter(
                    id=self.mapping.client.id if self.used_as_client else self.mapping.vendor.id
                ).first()

                if not org:
                    raise self.MappingNotFound("Organization not found for the given mapping")

                if org.uid is None:
                    logger.info(
                        "Updating UID for primary billing entity",
                        org_id=self.org_id,
                        uid_value=uid_value,
                    )
                    org.uid = uid_value
                    org.save()

            vendor_mapping_ids = (
                LinkedOrganizations.objects.filter(client_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True)
                .exclude(vendor_id=self.mapping.vendor_id)
                .values_list("id", flat=True)
            )
            client_mapping_ids = (
                LinkedOrganizations.objects.filter(vendor_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True)
                .exclude(client_id=self.mapping.client_id)
                .values_list("id", flat=True)
            )
            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=vendor_mapping_ids,
                field_config_id=F("document__mapping__vendor__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config_id)] = [
                    f"This {uid_field_config_name} is already used (type 1)"
                ]
                raise self.SectionUpdateException(self.process_errors(errors))

            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=client_mapping_ids,
                field_config_id=F("document__mapping__client__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config_id)] = [
                    f"This {uid_field_config_name} is already used (type 2)"
                ]
                raise self.SectionUpdateException(self.process_errors(errors))

        updated_field_config_ids = list(set(updated_field_config_ids))

        created_documents = []
        for doc_config_id, doc_vals in doc_config_id_values_mapping.items():
            for doc_val in doc_vals:
                if doc_val.get("id") is None:
                    created_documents.append(
                        LinkedOrganizationDocument(
                            mapping_id=self.mapping.pk,
                            billing_entity_id=billing_entity_id,
                            document_config_id=doc_config_id,
                            created_by_id=self.user_id,
                        )
                    )
        if created_documents:
            created_documents = LinkedOrganizationDocument.objects.bulk_create(created_documents)

        doc_config_new_created_document_mapping = defaultdict(list)
        for doc in created_documents:
            doc_config_new_created_document_mapping[doc.document_config_id].append(doc.pk)

        current_document_ids = (
            LinkedOrganizationDocument.objects.filter(
                mapping_id=self.mapping.pk,
                billing_entity_id=billing_entity_id,
                document_config_id__in=doc_config_id_values_mapping.keys(),
            )
            .available()
            .values_list("id", flat=True)
        )
        updated_document_ids = []

        current_text_field_value_ids = (
            LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=self.mapping.pk,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_file_field_value_ids = (
            LinkedOrganizationDocumentFileFieldFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=self.mapping.pk,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_state_field_value_ids = (
            LinkedOrganizationDocumentStateFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=self.mapping.pk,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )

        current_phone_number_field_value_ids = (
            LinkedOrganizationDocumentPhoneNumberFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=self.mapping.pk,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )

        current_long_text_field_value_ids = (
            LinkedOrganizationDocumentLongTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=self.mapping.pk,
                document__billing_entity_id=billing_entity_id,
            )
            .values_list("id", flat=True)
            .available()
        )

        created_text_field_values = []
        created_file_field_values = []
        created_state_field_values = []
        created_phone_number_field_values = []
        created_long_text_field_values = []

        updated_text_field_values = []
        updated_text_field_value_ids = []
        updated_file_field_values = []
        updated_file_field_value_ids = []
        updated_state_field_values = []
        updated_state_field_value_ids = []
        updated_phone_number_field_values = []
        updated_phone_number_field_value_ids = []
        updated_long_text_field_values = []
        updated_long_text_field_value_ids = []
        timezone_now = timezone.now()

        for doc_config_id, doc_values in doc_config_id_values_mapping.items():
            for doc_val in doc_values:
                if not doc_val.get("id"):
                    document_id = doc_config_new_created_document_mapping[doc_config_id].pop()
                else:
                    document_id = doc_val["id"]
                    updated_document_ids.append(document_id)
                doc_val.pop("id", None)
                for field_config_id, field_val in doc_val.items():
                    field_config = field_configs[field_config_id]
                    if field_config.type == CustomFieldTypeChoices.TEXT:
                        if field_val.get("id"):
                            updated_text_field_values.append(
                                LinkedOrganizationDocumentTextFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=timezone_now,
                                )
                            )
                            updated_text_field_value_ids.append(field_val["id"])
                        else:
                            created_text_field_values.append(
                                LinkedOrganizationDocumentTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.FILE:
                        if field_val.get("id"):
                            updated_file_field_values.append(
                                LinkedOrganizationDocumentFileFieldFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    updated_by_id=self.user_id,
                                    updated_at=timezone_now,
                                )
                            )
                            updated_file_field_value_ids.append(field_val["id"])
                        else:
                            created_file_field_values.append(
                                LinkedOrganizationDocumentFileFieldFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.STATE:
                        if field_val.get("id"):
                            updated_state_field_values.append(
                                LinkedOrganizationDocumentStateFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    updated_by_id=self.user_id,
                                    updated_at=timezone_now,
                                )
                            )
                            updated_state_field_value_ids.append(field_val["id"])
                        else:
                            created_state_field_values.append(
                                LinkedOrganizationDocumentStateFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.PHONE_NUMBER:
                        if field_val.get("id"):
                            updated_phone_number_field_values.append(
                                LinkedOrganizationDocumentPhoneNumberFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=timezone_now,
                                )
                            )
                            updated_phone_number_field_value_ids.append(field_val["id"])
                        else:
                            created_phone_number_field_values.append(
                                LinkedOrganizationDocumentPhoneNumberFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.LONG_TEXT:
                        if field_val.get("id"):
                            updated_long_text_field_values.append(
                                LinkedOrganizationDocumentLongTextFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=self.user_id,
                                    updated_at=timezone_now,
                                )
                            )
                            updated_long_text_field_value_ids.append(field_val["id"])
                        else:
                            created_long_text_field_values.append(
                                LinkedOrganizationDocumentLongTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=self.user_id,
                                )
                            )
                    else:
                        raise Exception("Unknown field type")

        removed_text_field_ids = set(current_text_field_value_ids) - set(updated_text_field_value_ids)
        removed_file_field_ids = set(current_file_field_value_ids) - set(updated_file_field_value_ids)
        removed_state_field_ids = set(current_state_field_value_ids) - set(updated_state_field_value_ids)
        removed_phone_number_field_ids = set(current_phone_number_field_value_ids) - set(
            updated_phone_number_field_value_ids
        )
        removed_long_text_field_ids = set(current_long_text_field_value_ids) - set(updated_long_text_field_value_ids)

        removed_document_ids = (
            set(current_document_ids) - set(updated_document_ids) - set([doc.pk for doc in created_documents])
        )
        LinkedOrganizationDocument.objects.filter(id__in=removed_document_ids).soft_delete(user_id=self.user_id)

        LinkedOrganizationDocumentTextFieldData.objects.bulk_create(created_text_field_values)
        LinkedOrganizationDocumentTextFieldData.objects.bulk_update(updated_text_field_values, ["data"])

        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_create(created_file_field_values)
        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_update(
            updated_file_field_values, ["file", "file_name"]
        )

        LinkedOrganizationDocumentStateFieldData.objects.bulk_create(created_state_field_values)
        LinkedOrganizationDocumentStateFieldData.objects.bulk_update(updated_state_field_values, ["state_id"])

        LinkedOrganizationDocumentPhoneNumberFieldData.objects.bulk_create(created_phone_number_field_values)
        LinkedOrganizationDocumentPhoneNumberFieldData.objects.bulk_update(updated_phone_number_field_values, ["data"])

        LinkedOrganizationDocumentLongTextFieldData.objects.bulk_create(created_long_text_field_values)
        LinkedOrganizationDocumentLongTextFieldData.objects.bulk_update(updated_long_text_field_values, ["data"])

        LinkedOrganizationDocumentTextFieldData.objects.filter(id__in=removed_text_field_ids).soft_delete(
            user_id=self.user_id
        )
        LinkedOrganizationDocumentFileFieldFieldData.objects.filter(id__in=removed_file_field_ids).soft_delete(
            user_id=self.user_id
        )
        LinkedOrganizationDocumentStateFieldData.objects.filter(id__in=removed_state_field_ids).soft_delete(
            user_id=self.user_id
        )
        LinkedOrganizationDocumentPhoneNumberFieldData.objects.filter(
            id__in=removed_phone_number_field_ids
        ).soft_delete(user_id=self.user_id)
        LinkedOrganizationDocumentLongTextFieldData.objects.filter(id__in=removed_long_text_field_ids).soft_delete(
            user_id=self.user_id
        )
        logger.info("Linked organization section data updated.")

    def get_uid_field_config_id(self) -> str:
        uid_field_config_id = (
            Organization.objects.filter(id=self.org_id)
            .select_related("country__uid_field")
            .values_list("country__uid_field_id", flat=True)
            .first()
        )

        if not uid_field_config_id:
            raise self.UIDFieldConfigNotFound("UID field configuration ID not found for the organization.")

        return uid_field_config_id

    def get_billing_entities(self, billing_entity_id: int | None) -> list[LinkedOrganizationBillingEntityData]:
        billing_entities = LinkedOrganizationBillingEntity.objects.filter(
            mapping_id=self.mapping.pk,
            deleted_at__isnull=True,
        ).order_by("created_at")

        if billing_entity_id:
            billing_entities = billing_entities.filter(id=billing_entity_id)

        text_data = (
            LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__billing_entity_id__in=[be.pk for be in billing_entities]
            )
            .select_related(
                "field_config",
                "document__billing_entity",
                "field_config__document_config__country__uid_field",
            )
            .values(
                "document__billing_entity__id",
                "data",
                "field_config__name",
                "field_config__id",
                "field_config__document_config__country__uid_field__id",
                "field_config__document_config__country__uid_field__name",
            )
        )

        poc_data_map = {}
        uid_data_map = {}
        uid_name_map = {}

        for item in text_data:
            entity_id = item["document__billing_entity__id"]
            field_name = item["field_config__name"]
            field_id = item["field_config__id"]
            uid_field_id = item["field_config__document_config__country__uid_field__id"]
            uid_field_name = item["field_config__document_config__country__uid_field__name"]
            data = item["data"]

            if field_name == "Key POC Name":
                poc_data_map[entity_id] = data

            if field_id == uid_field_id:
                uid_data_map[entity_id] = data

            uid_name_map[entity_id] = uid_field_name

        return (
            [
                LinkedOrganizationBillingEntityData(
                    id=billing_entity.pk,
                    name=billing_entity.name,
                    is_primary=billing_entity.is_primary,
                    is_default=billing_entity.is_default,
                    is_active=billing_entity.is_active,
                    poc_name=poc_data_map.get(billing_entity.pk, None),
                    uid=uid_data_map.get(billing_entity.pk, None),
                    uid_name=uid_name_map.get(billing_entity.pk, None),
                )
                for billing_entity in billing_entities
            ]
            if billing_entities
            else []
        )

    def create_billing_entity(self, name: str, country_id: int) -> LinkedOrganizationBillingEntityBaseData:
        logger.info("Creating linked organization billing entity", org_id=self.org_id, name=name)

        billing_entity = LinkedOrganizationBillingEntity()
        billing_entity.mapping = self.mapping
        billing_entity.created_by_id = self.user_id
        billing_entity.country_id = country_id

        billing_entity.clean()

        billing_entity.save()

        logger.info("Linked organization billing entity created", billing_entity_id=billing_entity.pk)

        return LinkedOrganizationBillingEntityBaseData(
            id=billing_entity.pk,
            name=billing_entity.name,
            is_primary=billing_entity.is_primary,
            is_default=billing_entity.is_default,
            is_active=billing_entity.is_active,
            logo=billing_entity.logo.url if billing_entity.logo else None,
            country=OrganizationBasicDetailCountryData(
                id=billing_entity.country.pk,
                name=billing_entity.country.name,
            ),
        )

    def mark_billing_entity_default(self, billing_entity_id: int, is_default: bool):
        LinkedOrganizationBillingEntity.objects.filter(mapping_id=self.mapping.pk).update(is_default=False)

        if is_default:
            LinkedOrganizationBillingEntity.objects.filter(
                mapping_id=self.mapping.pk,
                id=billing_entity_id,
            ).update(is_default=True)

    def mark_billing_entity_active(self, billing_entity_id: int, is_active: bool) -> None:
        LinkedOrganizationBillingEntity.objects.filter(
            mapping_id=self.mapping.pk,
            id=billing_entity_id,
        ).update(is_active=is_active)

    def delete_billing_entity(self, billing_entity_id: int) -> None:
        LinkedOrganizationBillingEntity.objects.filter(
            mapping_id=self.mapping.pk,
            id=billing_entity_id,
        ).soft_delete(user_id=self.user_id)

    def update_billing_entity_logo(self, billing_entity_id: int, logo: str | None):
        if logo is not None:
            logo = get_relative_path(logo)
        LinkedOrganizationBillingEntity.objects.filter(id=billing_entity_id).update(logo=logo)
