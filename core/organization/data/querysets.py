from django.db.models import QuerySet

from common.querysets import AvailableQuerySetMixin


class OrganizationDocumentV2QuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationDocumentFileDataBaseQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationDocumentFieldContextConfigQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationDocumentFieldContextConfigDataBaseQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class OrganizationBillingEntityQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationDocumentQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationDocumentFileDataBaseQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationBusinessCardQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationAddressQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationBillingEntityQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class LinkedOrganizationQuerySet(QuerySet, AvailableQuerySetMixin):
    pass
