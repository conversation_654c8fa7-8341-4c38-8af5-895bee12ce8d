from django.db import models
from django.db.models import Q
from phonenumber_field import modelfields

from common.constants import FILE_FIELD_MAX_LENGTH, STANDARD_DECIMAL_CONFIG
from common.helpers import get_upload_path
from common.models import CreateUpdateDeleteModel
from core.models import AddressBaseModel, Country, Organization, State
from core.organization.data.models import OrganizationDocumentConfig, OrganizationDocumentFieldConfig
from core.organization.data.querysets import (
    LinkedOrganizationAddressQuerySet,
    LinkedOrganizationBillingEntityQuerySet,
    LinkedOrganizationBusinessCardQuerySet,
    LinkedOrganizationDocumentFileDataBaseQuerySet,
    LinkedOrganizationDocumentQuerySet,
    LinkedOrganizationQuerySet,
)


class LinkedOrganizations(CreateUpdateDeleteModel):
    client_id: int
    vendor_id: int
    org_id: int
    primary_billing_entity_id: int | None

    client = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="linked_client_mapping")
    vendor = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="linked_vendor_mapping")
    org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="linked_organization")

    primary_billing_entity = models.ForeignKey(
        "LinkedOrganizationBillingEntity",
        on_delete=models.RESTRICT,
        related_name="primary_mappings",
        null=True,
        blank=True,
        default=None,
    )

    objects = LinkedOrganizationQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organizations"


class LinkedOrganizationBillingEntity(CreateUpdateDeleteModel):
    mapping_id: int
    country_id: int

    mapping = models.ForeignKey(LinkedOrganizations, on_delete=models.RESTRICT, related_name="billing_entities")
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="linked_billing_entities")
    name = models.CharField(max_length=255)
    logo = models.ImageField(
        max_length=2000,
        null=True,
        blank=True,
        default=None,
        upload_to=get_upload_path,
    )
    is_default = models.BooleanField(default=False)
    is_primary = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    objects = LinkedOrganizationBillingEntityQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_billing_entities"
        constraints = [
            models.UniqueConstraint(
                fields=["mapping", "is_primary"],
                name="unique_primary_billing_entity_per_mapping",
                condition=Q(is_primary=True),
            ),
            models.UniqueConstraint(
                fields=["mapping", "is_default"],
                name="unique_default_billing_entity_per_mapping",
                condition=Q(is_default=True),
            ),
        ]


class LinkedOrganizationDocument(CreateUpdateDeleteModel):
    mapping = models.ForeignKey(LinkedOrganizations, on_delete=models.RESTRICT, related_name="linked_documents")
    document_config = models.ForeignKey(
        OrganizationDocumentConfig,
        on_delete=models.RESTRICT,
        related_name="linked_documents",
    )
    billing_entity = models.ForeignKey(
        LinkedOrganizationBillingEntity,
        on_delete=models.RESTRICT,
        related_name="linked_documents",
        null=True,
        blank=True,
    )

    objects = LinkedOrganizationDocumentQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_documents"


class LinkedOrganizationDocumentTextFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="linked_org_text_field_data"
    )
    document = models.ForeignKey(LinkedOrganizationDocument, on_delete=models.RESTRICT, related_name="text_field_data")
    data = models.TextField()

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_text_field_data"


class LinkedOrganizationDocumentDecimalFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="linked_org_decimal_field_data"
    )
    document = models.ForeignKey(
        LinkedOrganizationDocument, on_delete=models.RESTRICT, related_name="decimal_field_data"
    )
    data = models.DecimalField(
        max_digits=STANDARD_DECIMAL_CONFIG.get("max_digits"),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
    )

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_decimal_field_data"


class LinkedOrganizationDocumentFileFieldFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="linked_org_file_field_data"
    )
    document = models.ForeignKey(LinkedOrganizationDocument, on_delete=models.RESTRICT, related_name="file_field_data")
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    file_name = models.CharField(max_length=255)

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_file_field_data"


class LinkedOrganizationDocumentStateFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="linked_org_state_field_data"
    )
    document = models.ForeignKey(LinkedOrganizationDocument, on_delete=models.RESTRICT, related_name="state_field_data")
    state = models.ForeignKey(State, on_delete=models.RESTRICT)

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_state_field_data"


class LinkedOrganizationDocumentPhoneNumberFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig,
        on_delete=models.RESTRICT,
        related_name="linked_org_phone_number_field_data",
    )
    document = models.ForeignKey(
        LinkedOrganizationDocument,
        on_delete=models.RESTRICT,
        related_name="phone_number_field_data",
    )
    data = modelfields.PhoneNumberField(null=True, blank=True)

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_phone_number_field_data"


class LinkedOrganizationDocumentLongTextFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig,
        on_delete=models.RESTRICT,
        related_name="linked_org_long_text_field_data",
    )
    document = models.ForeignKey(
        LinkedOrganizationDocument,
        on_delete=models.RESTRICT,
        related_name="long_text_field_data",
    )
    data = models.TextField()

    objects = LinkedOrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_document_long_text_field_data"


class LinkedOrganizationAddress(AddressBaseModel, CreateUpdateDeleteModel):
    billing_entity_id: int
    mapping_id: int

    mapping = models.ForeignKey(LinkedOrganizations, on_delete=models.RESTRICT, related_name="addresses")
    billing_entity = models.ForeignKey(
        LinkedOrganizationBillingEntity,
        on_delete=models.RESTRICT,
        related_name="linked_addresses",
        null=True,
        blank=True,
    )

    objects = LinkedOrganizationAddressQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_addresses"


class LinkedOrganizationBusinessCard(CreateUpdateDeleteModel):
    billing_entity_id: int
    mapping_id: int

    mapping = models.ForeignKey(LinkedOrganizations, on_delete=models.RESTRICT, related_name="business_cards")
    billing_entity = models.ForeignKey(
        LinkedOrganizationBillingEntity,
        on_delete=models.RESTRICT,
        related_name="linked_business_cards",
        null=True,
        blank=True,
    )
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=255)

    objects = LinkedOrganizationBusinessCardQuerySet.as_manager()

    class Meta:
        db_table = "organization_linked_organization_business_cards"
        verbose_name = "Linked Organization Business Card"
        verbose_name_plural = "Linked Organization Business Cards"
        unique_together = (("mapping", "billing_entity"),)
