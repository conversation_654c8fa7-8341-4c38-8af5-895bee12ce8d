from django.db import models
from django.db.models import Q
from phonenumber_field import modelfields

from common.constants import FILE_FIELD_MAX_LENGTH, STANDARD_DECIMAL_CONFIG, CustomFieldTypeChoices
from common.helpers import get_upload_path
from common.models import BaseModel, CreateUpdateDeleteModel
from core.models import Country, Organization, State
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.querysets import (
    OrganizationBillingEntityQuerySet,
    OrganizationDocumentFieldContextConfigQuerySet,
    OrganizationDocumentFileDataBaseQuerySet,
    OrganizationDocumentV2QuerySet,
)
from microcontext.choices import MicroContextChoices


class OrganizationBillingEntity(CreateUpdateDeleteModel):
    organization_id: int
    country_id: int

    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="billing_entities")
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="billing_entities")
    name = models.CharField(max_length=255)
    logo = models.ImageField(
        upload_to=get_upload_path,
        max_length=FILE_FIELD_MAX_LENGTH,
        null=True,
        blank=True,
        default=None,
    )
    is_default = models.BooleanField(default=False)
    is_primary = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    objects = OrganizationBillingEntityQuerySet.as_manager()

    class Meta:
        db_table = "organization_billing_entities"
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "is_primary"],
                name="unique_primary_billing_entity_per_org",
                condition=Q(is_primary=True),
            ),
            models.UniqueConstraint(
                fields=["organization", "is_default"],
                name="unique_default_billing_entity_per_org",
                condition=Q(is_default=True),
            ),
        ]


class OrganizationSectionConfig(BaseModel):
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="sections_config")
    name = models.CharField(max_length=255)
    position = models.IntegerField()
    type = models.CharField(max_length=255, choices=OrganizationSectionTypeChoices.choices)

    class Meta:
        db_table = "organization_section_configs"
        unique_together = ("country", "name", "type")


class OrganizationDocumentConfig(BaseModel):
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="documents_config")
    section = models.ForeignKey(OrganizationSectionConfig, on_delete=models.RESTRICT, related_name="documents_config")
    name = models.CharField(max_length=255)
    multiple_allowed = models.BooleanField(default=False)
    position = models.IntegerField()
    is_required = models.BooleanField(default=False)
    is_visible_on_app = models.BooleanField(default=False)

    class Meta:
        db_table = "organization_document_configs"
        unique_together = ("country", "section", "name")


class OrganizationDocumentFieldConfig(BaseModel):
    document_config = models.ForeignKey(
        OrganizationDocumentConfig, on_delete=models.RESTRICT, related_name="fields_config"
    )
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=CustomFieldTypeChoices.choices)
    position = models.IntegerField()
    is_required = models.BooleanField(default=False)
    regex = models.CharField(max_length=255, null=True, blank=True)
    is_visible_on_app = models.BooleanField(default=False)
    is_capitalized = models.BooleanField(default=False)

    class Meta:
        db_table = "organization_document_field_configs"
        unique_together = ("document_config", "name", "type")


class OrganizationDocumentV2(CreateUpdateDeleteModel):
    document_config_id: int
    organization_id: int
    billing_entity_id: int | None = None

    document_config = models.ForeignKey(OrganizationDocumentConfig, on_delete=models.RESTRICT, related_name="documents")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="documents")
    billing_entity = models.ForeignKey(
        OrganizationBillingEntity,
        on_delete=models.RESTRICT,
        related_name="documents",
        null=True,
        blank=True,
    )
    is_verified = models.BooleanField(default=False)
    objects = OrganizationDocumentV2QuerySet.as_manager()

    class Meta:
        db_table = "organization_documents_v2"


class OrganizationDocumentTextFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="text_field_data"
    )
    document = models.ForeignKey(OrganizationDocumentV2, on_delete=models.RESTRICT, related_name="text_field_data")
    data = models.TextField()
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_text_field_data"


class OrganizationDocumentDecimalFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="decimal_field_data"
    )
    document = models.ForeignKey(OrganizationDocumentV2, on_delete=models.RESTRICT, related_name="decimal_field_data")
    data = models.DecimalField(
        max_digits=STANDARD_DECIMAL_CONFIG.get("max_digits"),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
    )
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_decimal_field_data"


class OrganizationDocumentFileFieldFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="file_field_data"
    )
    document = models.ForeignKey(OrganizationDocumentV2, on_delete=models.RESTRICT, related_name="file_field_data")
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    file_name = models.CharField(max_length=255)
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_file_field_data"


class OrganizationDocumentStateFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="state_field_data"
    )
    document = models.ForeignKey(OrganizationDocumentV2, on_delete=models.RESTRICT, related_name="state_field_data")
    state = models.ForeignKey(State, on_delete=models.RESTRICT)
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_state_field_data"


class OrganizationDocumentLongTextFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig,
        on_delete=models.RESTRICT,
        related_name="long_text_field_data",
    )
    document = models.ForeignKey(OrganizationDocumentV2, on_delete=models.RESTRICT, related_name="long_text_field_data")
    data = models.TextField()
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_long_text_field_data"


class OrganizationDocumentPhoneNumberFieldData(CreateUpdateDeleteModel):
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig,
        on_delete=models.RESTRICT,
        related_name="phone_number_field_data",
    )
    document = models.ForeignKey(
        OrganizationDocumentV2,
        on_delete=models.RESTRICT,
        related_name="phone_number_field_data",
    )
    data = modelfields.PhoneNumberField(null=True, blank=True)
    objects = OrganizationDocumentFileDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_phone_number_field_data"


class OrganizationDocumentFieldContextConfig(BaseModel):
    context = models.CharField(max_length=100, choices=MicroContextChoices.choices)
    country = models.ForeignKey(Country, on_delete=models.RESTRICT, related_name="context_fields")
    field_config = models.ForeignKey(
        OrganizationDocumentFieldConfig, on_delete=models.RESTRICT, related_name="context_fields"
    )
    is_client_data = models.BooleanField(default=False)
    name = models.CharField(max_length=100)  # name of field w.r.t. a particular context

    objects = OrganizationDocumentFieldContextConfigQuerySet.as_manager()

    class Meta:
        db_table = "organization_document_field_context_configs"
        unique_together = ("context", "country", "field_config")
