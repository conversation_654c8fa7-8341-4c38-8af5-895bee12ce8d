from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _

from core.organization.data.constants import OrganizationSectionTypeEnum


class OrganizationSectionTypeChoices(TextChoices):
    KYC_DETAILS = OrganizationSectionTypeEnum.KYC_DETAILS.value, _("Kyc details")
    BANK_DETAILS = OrganizationSectionTypeEnum.BANK_DETAILS.value, _("Bank details")
    OTHER_DETAILS = OrganizationSectionTypeEnum.OTHER_DETAILS.value, _("Other details")
