from django.urls import include, path

from core.organization.interface.apis.internal.allowed_user_with_active_user_count import (
    AllowedUsersWithActiveUsersCountApi,
)
from core.organization.interface.apis.internal.bank_details import OrganizationBankDetailsApi
from core.organization.interface.apis.internal.basic_details import (
    OrganizationBasicDetailsApi,
    OrganizationBasicDetailsUpdateApi,
)
from core.organization.interface.apis.internal.billing_entity import (
    OrganizationBillingEntityDropdownListApi,
    OrganizationBillingEntityListApi,
)
from core.organization.interface.apis.internal.billing_entity_address import OrganizationBillingEntityAddressListApi
from core.organization.interface.apis.internal.billing_entity_config import OrganizationBillingEntityConfigApi
from core.organization.interface.apis.internal.billing_entity_create import OrganizationBillingEntityCreateApi
from core.organization.interface.apis.internal.billing_entity_delete import OrganizationBillingEntityDeleteApi
from core.organization.interface.apis.internal.billing_entity_mark_active import OrganizationBillingEntityMarkActiveApi
from core.organization.interface.apis.internal.billing_entity_mark_default import (
    OrganizationBillingEntityMarkDefaultApi,
)
from core.organization.interface.apis.internal.context_field_option import ContextFieldOptionApi
from core.organization.interface.apis.internal.kyc_details import OrganizationKycDetailsApi
from core.organization.interface.apis.internal.logo_upload import OrganizationLogoUploadApi
from core.organization.interface.apis.internal.other_details import OrganizationOtherDetailsApi
from core.organization.interface.apis.internal.section_update import OrganizationSectionsUpdateApi

BILLING_ENTITY = [
    path("config/", OrganizationBillingEntityConfigApi.as_view(), name="org-billing-entity-config"),
    path("list/", OrganizationBillingEntityListApi.as_view(), name="org-billing-entity-list"),
    path("dropdown/list/", OrganizationBillingEntityDropdownListApi.as_view(), name="org-billing-entity-dropdown-list"),
    path("create/", OrganizationBillingEntityCreateApi.as_view(), name="org-billing-entity-create"),
    path(
        "<hash_id:billing_entity_id>/mark-default/",
        OrganizationBillingEntityMarkDefaultApi.as_view(),
        name="org-billing-entity-mark-default",
    ),
    path(
        "<hash_id:billing_entity_id>/mark-active/",
        OrganizationBillingEntityMarkActiveApi.as_view(),
        name="org-billing-entity-mark-active",
    ),
    path(
        "<hash_id:billing_entity_id>/delete/",
        OrganizationBillingEntityDeleteApi.as_view(),
        name="org-billing-entity-delete",
    ),
    path("address/list/", OrganizationBillingEntityAddressListApi.as_view(), name="org-billing-entity-address-list"),
]

urlpatterns = [
    path("basic-details/", OrganizationBasicDetailsApi.as_view(), name="org-basic-details"),
    path("basic-details/update/", OrganizationBasicDetailsUpdateApi.as_view(), name="org-basic-details-update"),
    path("kyc-details/", OrganizationKycDetailsApi.as_view(), name="org-kyc-details"),
    path("bank-details/", OrganizationBankDetailsApi.as_view(), name="org-bank-details"),
    path("other-details/", OrganizationOtherDetailsApi.as_view(), name="org-other-details"),
    path("sections/update/", OrganizationSectionsUpdateApi.as_view(), name="org-sections-update"),
    path("allowed-users/", AllowedUsersWithActiveUsersCountApi.as_view(), name="org-allowed-users"),
    path(
        "context-field/<hash_id:field_id>/options/",
        ContextFieldOptionApi.as_view(),
        name="org-context-field-options",
    ),
    path("logo-upload/", OrganizationLogoUploadApi.as_view(), name="org-logo-upload"),
    path("billing-entity/", include(BILLING_ENTITY)),
]
