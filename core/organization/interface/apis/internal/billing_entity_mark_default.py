from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import OrganizationBillingEntityData
from core.organization.domain.factory import OrganizationFactory


class OrganizationBillingEntityMarkDefaultApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_BILLING_ENTITIES]

    class InputModel(PydanticInputBaseModel):
        is_default: bool

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityData)},
        operation_id="organization_billing_entity_mark_default",
        operation_summary="Mark Organization Billing Entity Default",
    )
    def post(self, request, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=billing_entity_id)
        service = factory.get_service()

        try:
            service.mark_billing_entity_default(is_default=data.is_default)
            billing_entity = service.get_billing_entities(billing_entity_id=billing_entity_id)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(
            f"Organization billing entity {'marked' if data.is_default else 'unmarked'} as default successfully."
        )
        return Response(pydantic_dump(billing_entity[0]), status=HTTP_200_OK)
