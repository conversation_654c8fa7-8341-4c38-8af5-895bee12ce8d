from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt
from core.apis import OrgBaseApi
from core.organization.domain.factory import OrganizationFactory


class OrganizationLogoUploadApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_ORG_SETTINGS]

    class InputModel(PydanticInputBaseModel):
        logo_url: CustomFileUrlStr | None = None
        billing_entity_id: HashIdInt | None = None

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: {}},
        operation_id="organization_logo_upload",
        operation_summary="Upload Organization Logo",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        input_data = self.validate_pydantic_input_data()

        factory = OrganizationFactory(
            user_entity=user_entity,
            billing_entity_id=input_data.billing_entity_id,
        )
        service = factory.get_service()

        try:
            service.update_billing_entity_logo(logo=input_data.logo_url)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization logo uploaded successfully.")
        return Response({"logo_url": input_data.logo_url}, status=HTTP_200_OK)
