from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import (
    OrganizationBasicDetailsData,
    OrganizationBasicDetailsUpdateData,
    OrganizationBillingEntityFilter,
)
from core.organization.domain.factory import OrganizationFactory


class OrganizationBasicDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    filter_pydantic_class = OrganizationBillingEntityFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBasicDetailsData)},
        operation_id="organization_basic_details",
        operation_summary="Get Organization Basic Details",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data: OrganizationBillingEntityFilter = self.validate_pydantic_filter_data()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=filter_data.billing_entity_id)
        service = factory.get_service()

        try:
            data = service.get_basic_details()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization basic details retrieved successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class OrganizationBasicDetailsUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_pydantic_class = OrganizationBasicDetailsUpdateData

    @swagger_auto_schema(
        request_body=pydantic_schema(OrganizationBasicDetailsUpdateData),
        responses={HTTP_200_OK: {}},
        operation_id="organization_basic_details_update",
        operation_summary="Update Organization Basic Details",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=data.billing_entity_id)
        service = factory.get_service()

        try:
            service.update_basic_details(data=data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization basic details updated successfully.")
        return Response(status=HTTP_200_OK)
