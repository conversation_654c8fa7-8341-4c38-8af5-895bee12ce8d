from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import OrganizationBillingEntityFilter, OrganizationSectionData
from core.organization.domain.factory import OrganizationFactory


class OrganizationBankDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    filter_pydantic_class = OrganizationBillingEntityFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationSectionData)},
        operation_id="organization_bank_details",
        operation_summary="Get Organization Bank Details",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data: OrganizationBillingEntityFilter = self.validate_pydantic_filter_data()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=filter_data.billing_entity_id)
        service = factory.get_service()

        try:
            data = service.get_bank_details()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization bank details retrieved successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
