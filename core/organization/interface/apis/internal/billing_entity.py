from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import OrganizationBillingEntityData, OrganizationBillingEntityDropdownData
from core.organization.domain.factory import OrganizationFactory


class OrganizationBillingEntityListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_BILLING_ENTITIES]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityData)},
        operation_id="organization_billing_entity_list",
        operation_summary="Get Organization Billing Entity List",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        data = service.get_billing_entities()

        self.set_response_message("Organization billing entity list retrieved successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class OrganizationBillingEntityDropdownListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityDropdownData)},
        operation_id="organization_billing_entity_dropdown_list",
        operation_summary="Get Organization Billing Entity Dropdown List",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        data = service.get_billing_entity_dropdown_list()

        self.set_response_message("Organization billing entity dropdown list retrieved successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
