from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from core.apis import Org<PERSON>ase<PERSON><PERSON>
from core.organization.domain.entities import OrganizationSectionUpdateData
from core.organization.domain.factory import OrganizationFactory


class OrganizationSectionsUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_BILLING_ENTITIES]

    input_serializer_class = OrganizationSectionUpdateData.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationSectionUpdateData.drf_serializer},
        operation_id="organization_section_update",
        operation_summary="Update Organization Section",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data: OrganizationSectionUpdateData = self.validate_input_data()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=data.billing_entity_id)
        service = factory.get_service()

        try:
            service.update_sections_data(data=data)
        except service.SectionUpdateException as e:
            return Response(e.message_dict, status=HTTP_400_BAD_REQUEST)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization sections updated successfully.")
        return Response(status=HTTP_200_OK)
