from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.serializers import BaseSerializer
from core.apis import Org<PERSON>ase<PERSON><PERSON>
from core.organization.domain.factory import OrganizationFactory
from core.user.services import ActiveUsersCountService


class AllowedUsersWithActiveUsersCountApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        active_users = serializers.IntegerField()
        allowed_users = serializers.IntegerField()

        class Meta:
            ref_name = "AllowedUsersWithActiveUsersCountApi"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="allowed_and_active_users_count",
        operation_summary="Allowed And Active Users Count",
    )
    def get(self, request):
        user_entity = self.get_org_user_entity()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        allowed_users = service.get_allowed_users()
        active_users = ActiveUsersCountService.fetch_active_users_count(org_id=user_entity.org_id)

        return Response(
            self.OutputSerializer(
                {
                    "active_users": active_users,
                    "allowed_users": allowed_users,
                }
            ).data,
            status=HTTP_200_OK,
        )
