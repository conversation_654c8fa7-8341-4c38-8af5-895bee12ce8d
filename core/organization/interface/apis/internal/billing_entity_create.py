from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import OrganizationBillingEntityCreateData
from core.organization.domain.factory import OrganizationFactory


class OrganizationBillingEntityCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_CREATE_BILLING_ENTITIES]

    input_pydantic_class = OrganizationBillingEntityCreateData

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityCreateData)},
        operation_id="organization_billing_entity_create",
        operation_summary="Create Organization Billing Entity",
    )
    def post(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        try:
            billing_entity = service.create_billing_entity(data=data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization billing entity created successfully.")
        return Response(pydantic_dump(billing_entity), status=HTTP_200_OK)
