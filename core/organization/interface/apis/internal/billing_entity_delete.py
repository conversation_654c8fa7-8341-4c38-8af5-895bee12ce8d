from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from core.apis import Org<PERSON>ase<PERSON><PERSON>
from core.organization.domain.factory import OrganizationFactory


class OrganizationBillingEntityDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_BILLING_ENTITIES]

    @swagger_auto_schema(
        responses={HTTP_200_OK: {}},
        operation_id="organization_billing_entity_delete",
        operation_summary="Delete Organization Billing Entity",
    )
    def delete(self, request, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = OrganizationFactory(user_entity=user_entity, billing_entity_id=billing_entity_id)
        service = factory.get_service()

        try:
            service.delete_billing_entity()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Organization billing entity deleted successfully.")
        return Response(status=HTTP_200_OK)
