from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.serializers import BaseSerializer, HashIdField
from core.apis import OrgBase<PERSON>pi
from core.organization.domain.entities import DropdownOptionSerializer
from core.organization.domain.factory import OrganizationFactory


class ContextFieldOptionApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        vendor_id = HashIdField()

        class Meta:
            ref_name = "ContextFieldOptionInputSerializer"

    serializer_class = DropdownOptionSerializer.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: DropdownOptionSerializer.drf_serializer},
        operation_id="linked_organization_context_field_option_sapi",
        operation_summary="Get Linked Organization Context Field Options Api",
    )
    def get(self, request, field_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_filter_data()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        data = service.get_context_field_options(context_config_id=field_id, vendor_id=data.get("vendor_id"))

        self.set_response_message("Context field options fetched successfully.")
        return Response(self.serializer_class(data, many=True).data, status=HTTP_200_OK)
