from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.base_model import HashIdInt, PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import OrgBaseApi
from core.organization.domain.entities import OrganizationSectionConfigData
from core.organization.domain.factory import OrganizationFactory


class OrganizationBillingEntityConfigApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterModel(PydanticInputBaseModel):
        country_id: HashIdInt

    filter_pydantic_class = FilterModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationSectionConfigData)},
        operation_id="organization_billing_entity_config",
        operation_summary="Get Organization Billing Entity Config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data = self.validate_pydantic_filter_data()

        factory = OrganizationFactory(user_entity=user_entity)
        service = factory.get_service()

        data = service.get_billing_entity_config(country_id=filter_data.country_id)

        self.set_response_message("Organization billing entity config retrieved successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
