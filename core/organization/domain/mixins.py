import abc
import json

import requests
import structlog

from common.exceptions import BaseValidationError
from core.apis import OrgBaseApi
from core.constants import ExternalWebhookType
from core.models import ExternalWebhook

logger = structlog.get_logger(__name__)


class ExternalWebhookFileMixin(OrgBaseApi):
    class ExternalWebhookException(BaseValidationError):
        pass

    @abc.abstractmethod
    def get_webhook_data(self, *args, **kwargs) -> dict:
        ...

    def get_filename(self, *args, **kwargs) -> str:
        ...

    def get_webhook_url(self, webhook_type: ExternalWebhookType) -> str:
        external_webhook_obj = ExternalWebhook.objects.filter(type=webhook_type.value).values("url").first()
        if not external_webhook_obj:
            logger.info(f"External Webhook not found for {webhook_type.value}")
            raise self.ExternalWebhookException(f"External Webhook not found for {webhook_type.value}")
        return external_webhook_obj.get("url")  # type: ignore

    def get_remote_file_url(self, data: dict, webhook_type: ExternalWebhookType):
        url = self.get_webhook_url(webhook_type=webhook_type)
        headers = {"Content-Type": "application/json"}
        response = requests.request("POST", url, headers=headers, data=json.dumps(data))
        return json.loads(response.content)

    def post(self, webhook_type: ExternalWebhookType):
        try:
            response = self.get_remote_file_url(data=self.get_webhook_data(), webhook_type=webhook_type)
        except ExternalWebhookFileMixin.ExternalWebhookException as e:
            logger.info(f"Error while fetching remote Excel File(data) from {webhook_type.value}: {str(e)}")
            raise e
        return response
