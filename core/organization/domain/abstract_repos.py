import abc

from common.exceptions import BaseValidationError
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.domain.entities import (
    LinkedOrganizationBillingEntityBaseData,
    LinkedOrganizationBillingEntityData,
    OrganizationAddressData,
    OrganizationBasicDetailsUpdateData,
    OrganizationBillingEntityBaseData,
    OrganizationBillingEntityData,
    OrganizationBillingEntityDropdownData,
    OrganizationBusinessCardData,
    OrganizationOrgConfigEntity,
    OrganizationSectionConfigData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
)


class OrganizationAbstractRepo(abc.ABC):
    class BaseException(BaseValidationError):
        pass

    class OrgNotFound(BaseException):
        pass

    class BillingEntityNotFound(BaseException):
        pass

    class SectionUpdateException(BaseException):
        pass

    class UIDFieldConfigNotFound(BaseException):
        pass

    @abc.abstractmethod
    def get_billing_entity(
        self,
        billing_entity_id: int | None = None,
        org_id: int | None = None,
    ) -> OrganizationBillingEntityBaseData:
        pass

    @abc.abstractmethod
    def get_addresses(
        self,
        billing_entity_id: int | None = None,
        org_id: int | None = None,
    ) -> list[OrganizationAddressData]:
        pass

    @abc.abstractmethod
    def get_business_card(self, billing_entity_id: int) -> OrganizationBusinessCardData | None:
        pass

    @abc.abstractmethod
    def get_org_config(self) -> OrganizationOrgConfigEntity:
        pass

    @abc.abstractmethod
    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, billing_entity_id: int) -> None:
        pass

    @abc.abstractmethod
    def get_section_details(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
        country_id: int,
    ) -> OrganizationSectionData:
        pass

    @abc.abstractmethod
    def update_sections_data(
        self,
        data: OrganizationSectionUpdateData,
        billing_entity_id: int,
        is_primary_billing_entity: bool,
    ):
        pass

    @abc.abstractmethod
    def get_billing_entities(self, billing_entity_id: int | None) -> list[OrganizationBillingEntityData]:
        pass

    @abc.abstractmethod
    def get_billing_entity_dropdown_list(self) -> list[OrganizationBillingEntityDropdownData]:
        pass

    @abc.abstractmethod
    def get_allowed_users(self) -> int | None:
        pass

    @abc.abstractmethod
    def create_billing_entity(self, name: str, country_id: int) -> OrganizationBillingEntityBaseData:
        pass

    @abc.abstractmethod
    def mark_billing_entity_default(self, billing_entity_id: int, is_default: bool) -> None:
        pass

    @abc.abstractmethod
    def mark_billing_entity_active(self, billing_entity_id: int, is_active: bool) -> None:
        pass

    @abc.abstractmethod
    def delete_billing_entity(self, billing_entity_id: int) -> None:
        pass

    @abc.abstractmethod
    def get_section_configs_entity(self, country_id: int | None) -> list[OrganizationSectionConfigData]:
        pass

    @abc.abstractmethod
    def update_billing_entity_logo(self, billing_entity_id: int, is_primary: bool, logo: str | None):
        pass


class LinkedOrganizationAbstractRepo(abc.ABC):
    class Exception(BaseValidationError):
        pass

    class MappingNotFound(Exception):
        pass

    class BillingEntityNotFound(Exception):
        pass

    class SectionUpdateException(Exception):
        pass

    class UIDFieldConfigNotFound(Exception):
        pass

    @abc.abstractmethod
    def get_billing_entity(self, billing_entity_id: int | None) -> LinkedOrganizationBillingEntityBaseData:
        pass

    @abc.abstractmethod
    def get_linked_addresses(self, billing_entity_id: int) -> list[OrganizationAddressData]:
        pass

    @abc.abstractmethod
    def get_linked_business_card(self, billing_entity_id: int) -> OrganizationBusinessCardData | None:
        pass

    @abc.abstractmethod
    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, billing_entity_id: int) -> None:
        pass

    @abc.abstractmethod
    def get_section_details(
        self,
        section_type: OrganizationSectionTypeEnum,
        billing_entity_id: int,
        country_id: int,
    ) -> OrganizationSectionData:
        pass

    @abc.abstractmethod
    def update_sections_data(
        self,
        data: OrganizationSectionUpdateData,
        billing_entity_id: int,
        is_primary_billing_entity: bool,
    ):
        pass

    @abc.abstractmethod
    def get_uid_field_config_id(self) -> str:
        pass

    @abc.abstractmethod
    def get_billing_entities(self, billing_entity_id: int | None) -> list[LinkedOrganizationBillingEntityData]:
        pass

    @abc.abstractmethod
    def create_billing_entity(self, name: str, country_id: int) -> LinkedOrganizationBillingEntityBaseData:
        pass

    @abc.abstractmethod
    def mark_billing_entity_default(self, billing_entity_id: int, is_default: bool):
        pass

    @abc.abstractmethod
    def mark_billing_entity_active(self, billing_entity_id: int, is_active: bool) -> None:
        pass

    @abc.abstractmethod
    def delete_billing_entity(self, billing_entity_id: int) -> None:
        pass

    @abc.abstractmethod
    def update_billing_entity_logo(self, billing_entity_id: int, logo: str | None):
        pass
