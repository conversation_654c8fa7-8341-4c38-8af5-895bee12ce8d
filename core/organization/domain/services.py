from collections import defaultdict
from typing import Any, Optional, Union

import structlog
from django.db.models import F, QuerySet
from django.utils import timezone

from common.choices import VendorStatusChoices
from common.constants import CustomFieldTypeChoices
from common.exceptions import BaseValidationError
from common.services import validate_on_regex
from controlroom.data.models import OrganizationDocumentConfig
from core.choices import OrganizationDocumentChoices
from core.entities import CountryData
from core.models import (
    FromToOrgMapping,
    Organization,
    OrganizationAddress,
    OrganizationConfig,
    OrganizationDocument,
)
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.selectors import (
    get_context_field,
    get_organization_documents_by_section,
    get_organization_sections_config,
    get_organization_with_basic_details,
    get_uid_field_by_org,
)
from core.organization.domain.entities import (
    CityData,
    DocumentFieldValueBaseData,
    DocumentFileFieldValueData,
    DocumentStateFieldValueData,
    DocumentTextFieldValueData,
    DropdownOptionSerializer,
    FileData,
    OrganizationAddressData,
    OrganizationAddressUpdateData,
    OrganizationBasicDetailsData,
    OrganizationBasicDetailsUpdateData,
    OrganizationBusinessCardData,
    OrganizationDocumentConfigData,
    OrganizationDocumentData,
    OrganizationFieldData,
    OrganizationSectionConfigData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
    StateData,
)
from core.organization.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationBusinessCard,
    LinkedOrganizationDocument,
    LinkedOrganizationDocumentFileFieldFieldData,
    LinkedOrganizationDocumentStateFieldData,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationDocumentFileFieldFieldData,
    OrganizationDocumentStateFieldData,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
    OrganizationSectionConfig,
)
from core.selectors import get_organization_config
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import MicroContext
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.get_logger(__name__)


def get_section_config_data(
    section_config: OrganizationSectionConfig,
    uid_field_id: int = None,
    uid_required: bool = False,
    bank_details_required: bool = False,
) -> OrganizationSectionConfigData:
    documents: list[OrganizationDocumentConfigData] = []
    is_documents_required = False
    if section_config.type == OrganizationSectionTypeChoices.BANK_DETAILS:
        is_documents_required = bank_details_required
    for doc_config in section_config.documents_config.all():
        fields: list[OrganizationFieldData] = []
        for field_config in doc_config.fields_config.all():
            # because vendor config only set is_required on document level,
            # not on field level
            # is_required = field_config.is_required
            if field_config.id == uid_field_id and uid_required:
                # is_required = uid_required
                is_documents_required = True
            field = OrganizationFieldData(
                id=field_config.pk,
                name=field_config.name,
                type=field_config.type,
                position=field_config.position,
                is_required=field_config.is_required if field_config.id != uid_field_id else uid_required,
                regex=field_config.regex,
                is_visible_on_app=field_config.is_visible_on_app,
                is_capitalized=field_config.is_capitalized,
            )
            fields.append(field)

        document = OrganizationDocumentConfigData(
            id=doc_config.pk,
            name=doc_config.name,
            multiple_allowed=doc_config.multiple_allowed,
            position=doc_config.position,
            is_required=is_documents_required,
            fields=fields,
            is_visible_on_app=doc_config.is_visible_on_app,
        )
        is_documents_required = False
        documents.append(document)
    return OrganizationSectionConfigData(
        id=section_config.pk,
        name=section_config.name,
        position=section_config.position,
        type=section_config.type,
        documents=documents,
    )


def process_errors(error: dict) -> dict:
    processed_errors = {}
    for key, value in error.items():
        if any(value):
            processed_errors[key] = value
    return processed_errors


def prepare_order_fields_data(
    document: OrganizationDocumentConfig, context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig]
):
    fields_data = []
    field_data_dict = {}
    for item in context_config_objects:
        if item.field_config.document_config_id == document.id:
            if item.field_config.type == CustomFieldTypeChoices.TEXT:
                for obj in item.vendor_order_text_field_data.all():
                    field_data_dict[item.id] = DocumentTextFieldValueData(id=obj.pk, data=obj.data)
            else:
                # Handle for other field type data
                pass
            field_data_dict["id"] = None
    fields_data.append(field_data_dict)
    return fields_data


def prepare_proposal_fields_data(document, context_config_objects):
    fields_data = []
    field_data_dict = {}
    for item in context_config_objects:
        if item.field_config.document_config_id == document.id:
            if item.field_config.type == CustomFieldTypeChoices.TEXT:
                for obj in item.proposal_text_field_data.all():
                    field_data_dict[item.id] = DocumentTextFieldValueData(id=obj.pk, data=obj.data)
            else:
                # Handle for other field type data
                pass
            field_data_dict["id"] = None
    fields_data.append(field_data_dict)
    return fields_data


def prepare_context_fields_data(
    document: OrganizationDocumentConfig,
    context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig],
    context: MicroContext,
):
    if context == MicroContextChoices.ORDER:
        return prepare_order_fields_data(document=document, context_config_objects=context_config_objects)
    elif context == MicroContextChoices.PROPOSAL:
        return prepare_proposal_fields_data(document=document, context_config_objects=context_config_objects)
    else:
        return []


def prepare_nested_data_for_section_config(
    context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig],
    context: MicroContextChoices,
    with_data: bool = False,
) -> list[OrganizationSectionData]:
    section_map = {}

    # Loop through the queryset
    for item in context_config_objects:
        section = item.field_config.document_config.section
        document = item.field_config.document_config
        field = item.field_config

        # Prepare section data
        if section.id not in section_map:
            section_map[section.id] = OrganizationSectionData(
                id=section.id, name=section.name, position=section.position, type=section.type, documents=[]
            )

        # Prepare document data and ensure no duplicate documents
        section_data = section_map[section.id]
        document_data = next((doc for doc in section_data.documents if doc.id == document.id), None)
        if not document_data:
            document_data = OrganizationDocumentData(
                id=document.id,
                name=document.name,
                multiple_allowed=document.multiple_allowed,
                position=document.position,
                is_required=document.is_required,
                is_visible_on_app=document.is_visible_on_app,
                fields=[],
                fields_data=(
                    prepare_context_fields_data(
                        document=document, context_config_objects=context_config_objects, context=context
                    )
                    if with_data
                    else []
                ),
            )
            section_data.documents.append(document_data)

        # Prepare field data and ensure no duplicate fields
        field_data = next((fld for fld in document_data.fields if fld.id == field.id), None)
        if not field_data:
            field_data = OrganizationFieldData(
                id=item.pk,  # Use the context config id as the field id
                name=item.name,  # Use the context field name as the field name
                type=CustomFieldTypeChoices.DROPDOWN,
                position=field.position,
                is_required=field.is_required,
                regex=field.regex,
                is_visible_on_app=field.is_visible_on_app,
                is_capitalized=field.is_capitalized,
            )
            document_data.fields.append(field_data)

    return list(section_map.values())


def get_org_basic_detail_data(org_id: int) -> OrganizationBasicDetailsData:
    org = get_organization_with_basic_details(org_id=org_id)
    business_card = org.business_cards[0] if org.business_cards else None
    org_config = get_organization_config(org_id=org_id)
    return OrganizationBasicDetailsData(
        id=org.pk,
        name=org.name,
        logo=org.logo.url if org.logo.name else None,
        country=CountryData(id=org.country.pk, name=org.country.name),
        currency=org_config.currency,
        tax_type=org_config.tax_type,
        timezone=org_config.timezone,
        business_card=(
            OrganizationBusinessCardData(
                id=business_card.pk,
                file=business_card.file.url if business_card.file.name else None,
                file_name=business_card.name,
            )
            if business_card
            else None
        ),
        addresses=[
            OrganizationAddressData(
                id=address.pk,
                address_line_1=address.address_line_1,
                address_line_2=address.address_line_2,
                city=(CityData(id=address.city.pk, name=address.city.name) if address.city else None),
                state=(StateData(id=address.state.pk, name=address.state.name) if address.state else None),
                country=(CountryData(id=address.country.pk, name=address.country.name) if address.country else None),
                zip_code=address.zip_code,
                value=address.full_address,
            )
            for address in org.addresses.all()
        ],
    )


class OrganizationOnboardBaseService:
    class OrganizationOnboardBaseServiceException(BaseValidationError):
        pass

    def create_linked_addresses(
        self, addresses_data: list[OrganizationAddressUpdateData], mapping_id: int, user_id: int
    ):
        addresses = []
        for address_data in addresses_data:
            addresses.append(
                LinkedOrganizationAddress(
                    mapping_id=mapping_id,
                    address_line_1=address_data.address_line_1,
                    address_line_2=address_data.address_line_2,
                    city_id=address_data.city_id,
                    state_id=address_data.state_id,
                    country_id=address_data.country_id,
                    zip_code=address_data.zip_code,
                    created_by_id=user_id,
                )
            )
        LinkedOrganizationAddress.objects.bulk_create(addresses)

    def create_addresses(self, addresses_data: list[OrganizationAddressUpdateData], org_id: int, user_id: int):
        addresses = []
        for address_data in addresses_data:
            addresses.append(
                OrganizationAddress(
                    organization_id=org_id,
                    address_line_1=address_data.address_line_1,
                    address_line_2=address_data.address_line_2,
                    city_id=address_data.city_id,
                    state_id=address_data.state_id,
                    country_id=address_data.country_id,
                    zip_code=address_data.zip_code,
                    created_by_id=user_id,
                )
            )
        OrganizationAddress.objects.bulk_create(addresses)

    def create_from_to_org_mapping(
        self,
        client_id: int,
        vendor_id: int,
        user_id: int,
        client_poc_id: Optional[int] = None,
        vendor_poc_id: Optional[int] = None,
        vendor_status: Optional[VendorStatusChoices] = VendorStatusChoices.ACTIVE,
    ) -> int:
        if client_id == vendor_id:
            raise self.OrganizationOnboardBaseServiceException("Client and Vendor cannot be same")
        mapping = FromToOrgMapping.objects.create(
            org_from_id=client_id,
            org_to_id=vendor_id,
            invited_by_org_id=vendor_id,
            client_poc_id=client_poc_id,
            vendor_poc_id=vendor_poc_id,
            created_by_id=user_id,
            vendor_status=vendor_status,
        )
        return mapping.pk


class OrganizationBaseService:
    class OrganizationBaseServiceException(BaseValidationError):
        pass

    class OrganizationSectionUpdateException(OrganizationBaseServiceException):
        pass

    def update_linked_organization_section_data(
        self, data: OrganizationSectionUpdateData, mapping: LinkedOrganizations, user_id: int, org_id: int
    ):
        logger.info("Updating linked organization section data", mapping_id=mapping.pk, data=data)
        doc_config_id_values_mapping: dict[int, list[dict]] = {}
        updated_field_config_ids = []
        section_ids = []
        errors = defaultdict(list)
        is_data_valid = True
        field_configs: dict[
            int, OrganizationDocumentFieldConfig
        ] = OrganizationDocumentFieldConfig.objects.all().in_bulk()
        uid_field_config_id = mapping.uid_field_id
        uid_field_config_name = mapping.uid_field_name
        uid_value = None

        for section_id, section_value in data.sections.items():
            section_ids.append(section_id)
            for doc_id, doc_values in section_value.items():
                doc_config_id_values_mapping[doc_id] = doc_values
                for doc_val in doc_values:
                    updated_field_config_ids.extend([key for key in doc_val.keys() if key != "id"])
                    for field_config_id, field_val in doc_val.items():
                        if field_config_id == "id":
                            continue
                        config = field_configs.get(field_config_id)
                        error = ""
                        if (
                            config.regex
                            and field_val.get("data")
                            and not validate_on_regex(regex=config.regex, value=field_val.get("data"))
                        ):
                            error = f"Please enter valid {config.name}"
                            is_data_valid = False
                        if uid_field_config_id and uid_field_config_id == field_config_id:
                            uid_value = field_val.get("data")
                        if error:
                            errors[HashIdConverter.encode(field_config_id)].append(error)

        if not is_data_valid:
            raise self.OrganizationSectionUpdateException(errors)
        if uid_value:
            vendor_mapping_ids = (
                LinkedOrganizations.objects.filter(client_id=org_id, org_id=org_id, deleted_at__isnull=True)
                .exclude(vendor_id=mapping.vendor_id)
                .values_list("id", flat=True)
            )
            client_mapping_ids = (
                LinkedOrganizations.objects.filter(vendor_id=org_id, org_id=org_id, deleted_at__isnull=True)
                .exclude(client_id=mapping.client_id)
                .values_list("id", flat=True)
            )
            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=vendor_mapping_ids,
                field_config_id=F("document__mapping__vendor__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config_id)] = [
                    f"This {uid_field_config_name} is already used (type 1)"
                ]
                raise self.OrganizationSectionUpdateException(process_errors(errors))

            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=client_mapping_ids,
                field_config_id=F("document__mapping__client__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_config_id)] = [
                    f"This {uid_field_config_name} is already used (type 2)"
                ]
                raise self.OrganizationSectionUpdateException(process_errors(errors))

        updated_field_config_ids = list(set(updated_field_config_ids))

        created_documents = []
        for doc_config_id, doc_vals in doc_config_id_values_mapping.items():
            for doc_val in doc_vals:
                if doc_val.get("id") is None:
                    created_documents.append(
                        LinkedOrganizationDocument(
                            mapping_id=mapping.pk, document_config_id=doc_config_id, created_by_id=user_id
                        )
                    )
        if created_documents:
            created_documents = LinkedOrganizationDocument.objects.bulk_create(created_documents)

        doc_config_new_created_document_mapping = defaultdict(list)
        for doc in created_documents:
            doc_config_new_created_document_mapping[doc.document_config_id].append(doc.pk)

        current_document_ids = (
            LinkedOrganizationDocument.objects.filter(
                mapping_id=mapping.pk, document_config_id__in=doc_config_id_values_mapping.keys()
            )
            .available()
            .values_list("id", flat=True)
        )
        updated_document_ids = []

        current_text_field_value_ids = (
            LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=mapping.pk,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_file_field_value_ids = (
            LinkedOrganizationDocumentFileFieldFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=mapping.pk,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_state_field_value_ids = (
            LinkedOrganizationDocumentStateFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__mapping_id=mapping.pk,
            )
            .values_list("id", flat=True)
            .available()
        )

        created_text_field_values = []
        created_file_field_values = []
        created_state_field_values = []

        updated_text_field_values = []
        updated_text_field_value_ids = []
        updated_file_field_values = []
        updated_file_field_value_ids = []
        updated_state_field_values = []
        updated_state_field_value_ids = []

        for doc_config_id, doc_values in doc_config_id_values_mapping.items():
            for doc_val in doc_values:
                if not doc_val.get("id"):
                    document_id = doc_config_new_created_document_mapping[doc_config_id].pop()
                else:
                    document_id = doc_val["id"]
                    updated_document_ids.append(document_id)
                doc_val.pop("id", None)
                for field_config_id, field_val in doc_val.items():
                    field_config = field_configs[field_config_id]
                    if field_config.type == CustomFieldTypeChoices.TEXT:
                        if field_val.get("id"):
                            updated_text_field_values.append(
                                LinkedOrganizationDocumentTextFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_text_field_value_ids.append(field_val["id"])
                        else:
                            created_text_field_values.append(
                                LinkedOrganizationDocumentTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.FILE:
                        if field_val.get("id"):
                            updated_file_field_values.append(
                                LinkedOrganizationDocumentFileFieldFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_file_field_value_ids.append(field_val["id"])
                        else:
                            created_file_field_values.append(
                                LinkedOrganizationDocumentFileFieldFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    created_by_id=user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.STATE:
                        if field_val.get("id"):
                            updated_state_field_values.append(
                                LinkedOrganizationDocumentStateFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_state_field_value_ids.append(field_val["id"])
                        else:
                            created_state_field_values.append(
                                LinkedOrganizationDocumentStateFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    created_by_id=user_id,
                                )
                            )
                    else:
                        raise Exception("Unknown field type")

        removed_text_field_ids = set(current_text_field_value_ids) - set(updated_text_field_value_ids)
        removed_file_field_ids = set(current_file_field_value_ids) - set(updated_file_field_value_ids)
        removed_state_field_ids = set(current_state_field_value_ids) - set(updated_state_field_value_ids)

        removed_document_ids = (
            set(current_document_ids) - set(updated_document_ids) - set([doc.id for doc in created_documents])
        )
        LinkedOrganizationDocument.objects.filter(id__in=removed_document_ids).soft_delete(user_id)

        LinkedOrganizationDocumentTextFieldData.objects.bulk_create(created_text_field_values)
        LinkedOrganizationDocumentTextFieldData.objects.bulk_update(updated_text_field_values, ["data"])
        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_create(created_file_field_values)
        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_update(
            updated_file_field_values, ["file", "file_name"]
        )
        LinkedOrganizationDocumentStateFieldData.objects.bulk_create(created_state_field_values)
        LinkedOrganizationDocumentStateFieldData.objects.bulk_update(updated_state_field_values, ["state_id"])
        LinkedOrganizationDocumentTextFieldData.objects.filter(id__in=removed_text_field_ids).soft_delete(
            user_id=user_id
        )
        LinkedOrganizationDocumentFileFieldFieldData.objects.filter(id__in=removed_file_field_ids).soft_delete(
            user_id=user_id
        )
        LinkedOrganizationDocumentStateFieldData.objects.filter(id__in=removed_state_field_ids).soft_delete(
            user_id=user_id
        )
        logger.info("Linked organization section data updated.")

    def update_linked_organization_addresses(
        self, addresses_data: list[OrganizationAddressUpdateData], mapping_id: int, user_id: int
    ):
        current_addresses_ids = LinkedOrganizationAddress.objects.filter(mapping_id=mapping_id).values_list(
            "id", flat=True
        )
        created_addresses = []
        updated_addresses = []
        updated_addresses_ids = []
        for address_data in addresses_data:
            if address_data.id and address_data.id in current_addresses_ids:
                updated_addresses.append(
                    LinkedOrganizationAddress(
                        id=address_data.id,
                        mapping_id=mapping_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
                updated_addresses_ids.append(address_data.id)
            else:
                created_addresses.append(
                    LinkedOrganizationAddress(
                        mapping_id=mapping_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        created_by_id=user_id,
                    )
                )
        removed_addresses_ids = set(current_addresses_ids) - set(updated_addresses_ids)
        LinkedOrganizationAddress.objects.bulk_create(created_addresses)
        LinkedOrganizationAddress.objects.bulk_update(
            updated_addresses,
            fields=[
                "address_line_1",
                "address_line_2",
                "city_id",
                "state_id",
                "country_id",
                "zip_code",
                "updated_by_id",
                "updated_at",
            ],
        )
        LinkedOrganizationAddress.objects.filter(id__in=removed_addresses_ids).update(
            deleted_by_id=user_id, deleted_at=timezone.now()
        )

    def _update_basic_details(
        self, data: OrganizationBasicDetailsUpdateData, user_id: int, mapping: LinkedOrganizations
    ):
        if data.business_card:
            if data.business_card.id:
                business_card = (
                    LinkedOrganizationBusinessCard.objects.filter(
                        mapping_id=mapping.pk,
                    )
                    .available()
                    .update(
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                        file=data.business_card.file,
                        name=data.business_card.file_name,
                    )
                )
            else:
                business_card = LinkedOrganizationBusinessCard(
                    mapping_id=mapping.pk,
                    created_by_id=user_id,
                    file=data.business_card.file,
                    name=data.business_card.file_name,
                )
                business_card.save()
        else:
            LinkedOrganizationBusinessCard.objects.filter(
                mapping_id=mapping.pk,
            ).delete()
        if data.addresses:
            self.update_linked_organization_addresses(
                addresses_data=data.addresses, mapping_id=mapping.pk, user_id=user_id
            )


class OrganizationService:
    class OrganizationServiceBaseException(BaseValidationError):
        pass

    class OrganizationSectionUpdateException(BaseValidationError):
        pass

    def __init__(self, org_id: int):
        self.org_id = org_id

    def get_basic_details(self) -> OrganizationBasicDetailsData:
        return get_org_basic_detail_data(org_id=self.org_id)

    def get_allowed_users(self) -> Any | None:
        org = OrganizationConfig.objects.filter(organization_id=self.org_id).first()
        if org is not None:
            return org.allowed_users
        return None

    def update_addresses(self, addresses_data: list[OrganizationAddressUpdateData], user_id: int):
        current_addresses_ids = OrganizationAddress.objects.filter(organization_id=self.org_id).values_list(
            "id", flat=True
        )
        created_addresses = []
        updated_addresses = []
        updated_addresses_ids = []
        for address_data in addresses_data:
            if address_data.id and address_data.id in current_addresses_ids:
                updated_addresses.append(
                    OrganizationAddress(
                        id=address_data.id,
                        organization_id=self.org_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        updated_by_id=user_id,
                        updated_at=timezone.now(),
                    )
                )
                updated_addresses_ids.append(address_data.id)
            else:
                created_addresses.append(
                    OrganizationAddress(
                        organization_id=self.org_id,
                        address_line_1=address_data.address_line_1,
                        address_line_2=address_data.address_line_2,
                        city_id=address_data.city_id,
                        state_id=address_data.state_id,
                        country_id=address_data.country_id,
                        zip_code=address_data.zip_code,
                        created_by_id=user_id,
                    )
                )
        removed_addresses_ids = set(current_addresses_ids) - set(updated_addresses_ids)
        OrganizationAddress.objects.bulk_create(created_addresses)
        OrganizationAddress.objects.bulk_update(
            updated_addresses,
            fields=[
                "address_line_1",
                "address_line_2",
                "city_id",
                "state_id",
                "country_id",
                "zip_code",
                "updated_by_id",
                "updated_at",
            ],
        )
        OrganizationAddress.objects.filter(id__in=removed_addresses_ids).update(
            deleted_by_id=user_id, deleted_at=timezone.now()
        )

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, user_id: int):
        logger.info("Updating organization basic details", org_id=self.org_id, data=data)
        Organization.objects.filter(id=data.id).update(name=data.name)
        if data.business_card:
            if data.business_card.id:
                (
                    OrganizationDocument.objects.filter(
                        type=OrganizationDocumentChoices.BUSINESS_CARD,
                        organization_id=data.id,
                        id=data.business_card.id,
                    )
                    .available()
                    .update(
                        name=data.business_card.file_name,
                        file=data.business_card.file,
                    )
                )
            else:
                OrganizationDocument(
                    organization_id=data.id,
                    type=OrganizationDocumentChoices.BUSINESS_CARD,
                    name=data.business_card.file_name,
                    file=data.business_card.file,
                    uploaded_by_id=user_id,
                ).save()
        else:
            OrganizationDocument.objects.filter(
                type=OrganizationDocumentChoices.BUSINESS_CARD, organization_id=data.id
            ).soft_delete(user_id)
        if data.addresses:
            self.update_addresses(addresses_data=data.addresses, user_id=user_id)

    def _get_section_details(self, section_type: OrganizationSectionTypeChoices) -> OrganizationSectionData:
        section_config = get_organization_sections_config(org_id=self.org_id).filter(type=section_type).first()
        org_docs = get_organization_documents_by_section(org_id=self.org_id, section_type=section_type)
        doc_config_org_doc_mapping: dict[int, list[OrganizationDocumentV2]] = defaultdict(list)
        for org_doc in org_docs:
            doc_config_org_doc_mapping[org_doc.document_config_id].append(org_doc)

        org_doc_id_field_config_id_value_mapping: dict[
            tuple(int, int), DocumentFieldValueBaseData
        ] = {}  # dict[(org_doc_id, field_config_id)] = field_value
        for org_doc in org_docs:
            for field_data in org_doc.text_field_data.all():
                org_doc_id_field_config_id_value_mapping[
                    (org_doc.pk, field_data.field_config_id)
                ] = DocumentTextFieldValueData(id=field_data.pk, data=field_data.data)

            for field_data in org_doc.file_field_data.all():
                org_doc_id_field_config_id_value_mapping[
                    (org_doc.pk, field_data.field_config_id)
                ] = DocumentFileFieldValueData(
                    id=field_data.pk,
                    data=(
                        FileData(file_name=field_data.file_name, file=field_data.file.url)
                        if field_data.file_name and field_data.file.name
                        else None
                    ),
                )

            for field_data in org_doc.state_field_data.all():
                org_doc_id_field_config_id_value_mapping[
                    (org_doc.pk, field_data.field_config_id)
                ] = DocumentStateFieldValueData(
                    id=field_data.pk, data=StateData(id=field_data.state.pk, name=field_data.state.name)
                )

        documents: list[OrganizationDocumentData] = []
        for doc_config in section_config.documents_config.all():
            fields: list[OrganizationFieldData] = []
            fields_data_list: list[dict[int, DocumentFieldValueBaseData]] = []

            for field_config in doc_config.fields_config.all():
                field = OrganizationFieldData(
                    id=field_config.pk,
                    name=field_config.name,
                    type=field_config.type,
                    position=field_config.position,
                    is_required=field_config.is_required,
                    regex=field_config.regex,
                    is_visible_on_app=field_config.is_visible_on_app,
                    is_capitalized=field_config.is_capitalized,
                )
                fields.append(field)

            for org_doc in doc_config_org_doc_mapping[doc_config.pk]:
                field_data_dict: dict[Union[str, int], Union[int, DocumentFieldValueBaseData]] = defaultdict()
                for field_config in doc_config.fields_config.all():
                    if (org_doc.pk, field_config.pk) in org_doc_id_field_config_id_value_mapping:
                        field_data_dict[field_config.pk] = org_doc_id_field_config_id_value_mapping[
                            (org_doc.pk, field_config.pk)
                        ]
                field_data_dict["id"] = org_doc.pk
                fields_data_list.append(field_data_dict)

            document = OrganizationDocumentData(
                id=doc_config.pk,
                name=doc_config.name,
                multiple_allowed=doc_config.multiple_allowed,
                position=doc_config.position,
                is_required=doc_config.is_required,
                fields=fields,
                fields_data=fields_data_list,
                is_visible_on_app=doc_config.is_visible_on_app,
            )
            documents.append(document)

        section = OrganizationSectionData(
            id=section_config.pk,
            name=section_config.name,
            position=section_config.position,
            type=section_config.type,
            documents=documents,
        )
        return section

    def get_kyc_details(self) -> OrganizationSectionData:
        return self._get_section_details(section_type=OrganizationSectionTypeChoices.KYC_DETAILS)

    def get_bank_details(self) -> OrganizationSectionData:
        return self._get_section_details(section_type=OrganizationSectionTypeChoices.BANK_DETAILS)

    def update_sections_data(self, data: OrganizationSectionUpdateData, user_id: int):
        logger.info("Updating organization section data", org_id=self.org_id, data=data)
        doc_config_id_values_mapping: dict[int, list[dict]] = {}
        updated_field_config_ids = []
        section_ids = []
        errors = {}
        is_data_valid = True
        field_configs: dict[
            int, OrganizationDocumentFieldConfig
        ] = OrganizationDocumentFieldConfig.objects.all().in_bulk()
        uid_field_conifg = get_uid_field_by_org(org_id=self.org_id)
        uid_value = None
        for section_id, section_value in data.sections.items():
            section_ids.append(section_id)
            for doc_id, doc_values in section_value.items():
                doc_config_id_values_mapping[doc_id] = doc_values
                for doc_val in doc_values:
                    updated_field_config_ids.extend([key for key in doc_val.keys() if key != "id"])
                    for field_config_id, field_val in doc_val.items():
                        if field_config_id == "id":
                            continue
                        config = field_configs.get(field_config_id)
                        error = ""
                        if (
                            config.regex
                            and field_val.get("data")
                            and not validate_on_regex(regex=config.regex, value=field_val.get("data"))
                        ):
                            error = f"Please enter valid {config.name}"
                            is_data_valid = False
                        if uid_field_conifg and field_config_id == uid_field_conifg.pk:
                            uid_value = field_val.get("data")
                        if HashIdConverter.encode(field_config_id) in errors:
                            errors[HashIdConverter.encode(field_config_id)].append(error)
                        else:
                            errors[HashIdConverter.encode(field_config_id)] = [error]

        if not is_data_valid:
            raise self.OrganizationSectionUpdateException(process_errors(errors))
        if uid_value:
            # if Organization.objects.filter(pan_number=uid_value).exclude(id=self.org_id).exists():
            #     errors[HashIdConverter.encode(uid_field_conifg.pk)] = [f"This {uid_field_conifg.name}
            #     is already used (type 0)"]
            #     raise self.OrganizationSectionUpdateException(process_errors(errors))

            vendor_mapping_ids = LinkedOrganizations.objects.filter(
                client_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True
            ).values_list("id", flat=True)
            client_mapping_ids = LinkedOrganizations.objects.filter(
                vendor_id=self.org_id, org_id=self.org_id, deleted_at__isnull=True
            ).values_list("id", flat=True)
            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=vendor_mapping_ids,
                field_config_id=F("document__mapping__vendor__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_conifg.pk)] = [
                    f"This {uid_field_conifg.name} is already used (type 3)"
                ]
                raise self.OrganizationSectionUpdateException(process_errors(errors))

            if LinkedOrganizationDocumentTextFieldData.objects.filter(
                document__mapping_id__in=client_mapping_ids,
                field_config_id=F("document__mapping__client__country__uid_field_id"),
                data=uid_value,
            ).exists():
                errors[HashIdConverter.encode(uid_field_conifg.pk)] = [
                    f"This {uid_field_conifg.name} is already used (type 4)"
                ]
                raise self.OrganizationSectionUpdateException(process_errors(errors))

        Organization.objects.filter(id=self.org_id).update(pan_number=uid_value)
        updated_field_config_ids = list(updated_field_config_ids)

        created_documents = []
        for doc_config_id, doc_vals in doc_config_id_values_mapping.items():
            for doc_val in doc_vals:
                if doc_val.get("id") is None:
                    created_documents.append(
                        OrganizationDocumentV2(
                            organization_id=self.org_id, document_config_id=doc_config_id, created_by_id=user_id
                        )
                    )
        if created_documents:
            created_documents = OrganizationDocumentV2.objects.bulk_create(created_documents)

        doc_config_new_created_document_mapping = defaultdict(list)
        for doc in created_documents:
            doc_config_new_created_document_mapping[doc.document_config_id].append(doc.pk)

        current_document_ids = (
            OrganizationDocumentV2.objects.filter(
                organization_id=self.org_id, document_config_id__in=doc_config_id_values_mapping.keys()
            )
            .available()
            .values_list("id", flat=True)
        )
        updated_document_ids = []

        current_text_field_value_ids = (
            OrganizationDocumentTextFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_file_field_value_ids = (
            OrganizationDocumentFileFieldFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
            )
            .values_list("id", flat=True)
            .available()
        )
        current_state_field_value_ids = (
            OrganizationDocumentStateFieldData.objects.filter(
                document__document_config__section_id__in=section_ids,
                document__organization_id=self.org_id,
            )
            .values_list("id", flat=True)
            .available()
        )

        created_text_field_values = []
        created_file_field_values = []
        created_state_field_values = []

        updated_text_field_values = []
        updated_text_field_value_ids = []
        updated_file_field_values = []
        updated_file_field_value_ids = []
        updated_state_field_values = []
        updated_state_field_value_ids = []

        for doc_config_id, doc_values in doc_config_id_values_mapping.items():
            for doc_val in doc_values:
                if not doc_val.get("id"):
                    document_id = doc_config_new_created_document_mapping[doc_config_id].pop()
                else:
                    document_id = doc_val["id"]
                    updated_document_ids.append(document_id)
                doc_val.pop("id", None)
                for field_config_id, field_val in doc_val.items():
                    field_config = field_configs[field_config_id]
                    if field_config.type == CustomFieldTypeChoices.TEXT:
                        # TODO: Have to add a logic for checking field_val.get("id") is correct
                        # it must to value against field_config_id and document
                        if field_val.get("id"):
                            updated_text_field_values.append(
                                OrganizationDocumentTextFieldData(
                                    id=field_val["id"],
                                    field_config_id=field_config_id,
                                    document_id=document_id,
                                    data=field_val["data"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_text_field_value_ids.append(field_val["id"])
                        else:
                            created_text_field_values.append(
                                OrganizationDocumentTextFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    data=field_val["data"],
                                    created_by_id=user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.FILE:
                        if field_val.get("id"):
                            updated_file_field_values.append(
                                OrganizationDocumentFileFieldFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_file_field_value_ids.append(field_val["id"])
                        else:
                            created_file_field_values.append(
                                OrganizationDocumentFileFieldFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    file=field_val["data"]["file"],
                                    file_name=field_val["data"]["file_name"],
                                    created_by_id=user_id,
                                )
                            )
                    elif field_config.type == CustomFieldTypeChoices.STATE:
                        if field_val.get("id"):
                            updated_state_field_values.append(
                                OrganizationDocumentStateFieldData(
                                    id=field_val["id"],
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    updated_by_id=user_id,
                                    updated_at=timezone.now(),
                                )
                            )
                            updated_state_field_value_ids.append(field_val["id"])
                        else:
                            created_state_field_values.append(
                                OrganizationDocumentStateFieldData(
                                    document_id=document_id,
                                    field_config_id=field_config_id,
                                    state_id=field_val["data"]["id"],
                                    created_by_id=user_id,
                                )
                            )
                    else:
                        raise Exception("Unknown field type")

        removed_text_field_ids = set(current_text_field_value_ids) - set(updated_text_field_value_ids)
        removed_file_field_ids = set(current_file_field_value_ids) - set(updated_file_field_value_ids)
        removed_state_field_ids = set(current_state_field_value_ids) - set(updated_state_field_value_ids)

        removed_document_ids = (
            set(current_document_ids) - set(updated_document_ids) - set([doc.id for doc in created_documents])
        )
        OrganizationDocumentV2.objects.filter(id__in=removed_document_ids).soft_delete(user_id)

        OrganizationDocumentTextFieldData.objects.bulk_create(created_text_field_values)
        OrganizationDocumentTextFieldData.objects.bulk_update(updated_text_field_values, ["data"])
        OrganizationDocumentFileFieldFieldData.objects.bulk_create(created_file_field_values)
        OrganizationDocumentFileFieldFieldData.objects.bulk_update(updated_file_field_values, ["file", "file_name"])
        OrganizationDocumentStateFieldData.objects.bulk_create(created_state_field_values)
        OrganizationDocumentStateFieldData.objects.bulk_update(updated_state_field_values, ["state_id"])
        OrganizationDocumentTextFieldData.objects.filter(id__in=removed_text_field_ids).soft_delete(user_id)
        OrganizationDocumentFileFieldFieldData.objects.filter(id__in=removed_file_field_ids).soft_delete(user_id)
        OrganizationDocumentStateFieldData.objects.filter(id__in=removed_state_field_ids).soft_delete(user_id)

        logger.info("Organization section data updated")

    def get_context_field_options(self, context_config_id: int, org_id: int, vendor_id: int):
        context_config = get_context_field(context_config_id=context_config_id, org_id=org_id, vendor_id=vendor_id)
        option_objects = []
        if context_config.field_config.type == CustomFieldTypeChoices.TEXT:
            option_objects = context_config.field_config.text_data

        options = []
        for option in option_objects:
            options.append(DropdownOptionSerializer(id=option.pk, name=option.data))
        return options
