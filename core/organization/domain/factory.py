from common import decorators
from core.entities import OrgUserEntity
from core.organization.data.repositories import OrganizationRepo
from core.organization.domain.cache import OrganizationCache
from core.organization.domain.services.organization import OrganizationService
from core.organization.domain.services.permission import OrganizationPermissionService


class OrganizationFactory:
    def __init__(self, user_entity: OrgUserEntity, billing_entity_id: int | None = None):
        self.user_entity = user_entity
        self.billing_entity_id = billing_entity_id

    @decorators.service_lazy()
    def get_repo(self):
        return OrganizationRepo(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_service(self):
        return OrganizationService(
            user_entity=self.user_entity,
            repo=self.get_repo(),
            billing_entity_id=self.billing_entity_id,
            permission_service=self.get_permission_service(),
        )

    @decorators.service_lazy()
    def get_permission_service(self):
        return OrganizationPermissionService(cache=self.get_cache())

    @decorators.service_lazy()
    def get_cache(self):
        return OrganizationCache(user_entity=self.user_entity)
