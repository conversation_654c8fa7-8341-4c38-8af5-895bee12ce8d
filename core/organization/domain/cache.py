from core.caches import OrgUserPermissionCache
from core.entities import OrgUserEntity


class OrganizationCache:
    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id

    def get_user_org_permissions(self):
        return OrgUserPermissionCache.get(
            key=OrgUserEntity(
                user_id=self.user_id,
                org_id=self.org_id,
            )
        ).permissions
