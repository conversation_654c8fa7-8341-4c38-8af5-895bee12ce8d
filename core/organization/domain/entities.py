from typing import Annotated, Optional

from pydantic import Field, field_serializer
from rest_framework import serializers

from common.pydantic.base_model import BaseModel, BaseModelV2, PydanticInputBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt
from core.entities import CountryData, CurrencyData, TaxTypeData, TimezoneData
from core.organization.domain.enums import BillingEntityActionEnum
from rollingbanners.hash_id_converter import HashIdConverter


class CityData(BaseModel):
    id: HashIdInt | None = None
    name: str


class StateData(BaseModel):
    id: HashIdInt | None = None
    name: str


class FileData(BaseModel):
    file_name: str
    file: str


class PhoneNumberData(BaseModel):
    country_code: str
    number: str


class OrganizationBusinessCardData(BaseModel):
    id: HashIdInt | None = None
    file: CustomFileUrlStr
    file_name: str


class OrganizationAddressData(BaseModel):
    id: HashIdInt | None = None
    address_line_1: Optional[str]
    address_line_2: Optional[str]
    city: Optional[CityData]
    state: Optional[StateData]
    country: Optional[CountryData]
    zip_code: Optional[str]
    value: Optional[str]


class OrganizationAddressUpdateData(BaseModel):
    id: HashIdInt | None = None
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city_id: HashIdInt | None = None
    state_id: HashIdInt | None = None
    country_id: HashIdInt | None = None
    zip_code: Optional[str] = None


class OrganizationBusinessCardUpdateData(BaseModelV2):
    id: HashIdInt | None = None
    file: CustomFileUrlStr
    name: str


class OrganizationBasicDetailUIDData(BaseModel):
    value: str | None
    name: str


class OrganizationBasicDetailCountryData(BaseModel):
    id: HashIdInt
    name: str


class OrganizationBasicDetailsData(BaseModel):
    id: HashIdInt | None = None
    name: str
    logo: str | None
    business_card: OrganizationBusinessCardData | None
    code: str | None
    verification_status: str | None
    business_card: OrganizationBusinessCardData | None
    addresses: list[OrganizationAddressData]
    country: OrganizationBasicDetailCountryData
    currency: Optional[CurrencyData]
    tax_type: Optional[TaxTypeData]
    timezone: Optional[TimezoneData]
    uid_field: OrganizationBasicDetailUIDData | None = None


class OrganizationBasicDetailsUpdateData(PydanticInputBaseModel):
    billing_entity_id: HashIdInt | None = None
    id: HashIdInt | None = None
    name: str
    addresses: list[OrganizationAddressUpdateData]
    business_card: Optional[OrganizationBusinessCardData] = None


class OrganizationFieldData(BaseModel):
    id: HashIdInt | None = None
    name: str
    type: str
    position: int
    is_required: bool
    regex: Optional[str]
    is_visible_on_app: bool
    is_capitalized: bool


class DocumentFieldValueBaseData(BaseModel):
    id: HashIdInt | None = None


class DocumentTextFieldValueData(DocumentFieldValueBaseData):
    data: str


class DocumentFileFieldValueData(DocumentFieldValueBaseData):
    data: Optional[FileData]


class DocumentStateFieldValueData(DocumentFieldValueBaseData):
    data: StateData


class DocumentPhoneNumberFieldValueData(DocumentFieldValueBaseData):
    data: PhoneNumberData | None


class DocumentLongTextFieldValueData(DocumentFieldValueBaseData):
    data: str


class OrganizationDocumentConfigData(BaseModel):
    id: HashIdInt | None = None
    name: str
    multiple_allowed: bool
    position: int
    is_required: bool
    fields: list[OrganizationFieldData]
    is_visible_on_app: bool


class OrganizationSectionConfigData(BaseModel):
    id: HashIdInt | None = None
    name: str
    position: int
    type: str
    documents: list[OrganizationDocumentConfigData]


class OrganizationConfigBaseData(BaseModel):
    sections: list[OrganizationSectionConfigData]


class OrganizationCountryConfigData(OrganizationConfigBaseData):
    uid_field: Optional[OrganizationFieldData]
    uid_document_id: HashIdInt | None = None


class OrganizationBillingEntityBaseData(BaseModelV2):
    id: HashIdInt
    name: str
    logo: CustomFileUrlStr | None
    is_default: bool
    is_primary: bool
    is_active: bool
    country: OrganizationBasicDetailCountryData


class OrganizationDocumentData(OrganizationDocumentConfigData):
    fields_data: list[dict] = Field(default_factory=list)

    @field_serializer("fields_data")
    def serialize_fields_data(self, fields_data: list[dict], _info):
        representation = []
        for values in fields_data:
            values_dict = {}
            for field_id, value in values.items():
                if field_id == "id":
                    values_dict["id"] = HashIdConverter.encode(value)
                elif type(value) in [str, int, float, bool]:
                    values_dict[HashIdConverter.encode(field_id)] = value
                elif isinstance(value, BaseModel):
                    values_dict[HashIdConverter.encode(field_id)] = value.model_dump()
                elif isinstance(value, dict):
                    value_data_dict = {}
                    value_data_dict["id"] = HashIdConverter.encode(value["id"])
                    if isinstance(value["data"], BaseModel):
                        value_data_dict["data"] = value["data"].model_dump()
                    else:
                        value_data_dict["data"] = value["data"]
                    if isinstance(value["data"], dict) and value["data"].get("id"):
                        value_data_dict["data"]["id"] = HashIdConverter.encode(value["data"]["id"])
                    values_dict[HashIdConverter.encode(field_id)] = value_data_dict
                else:
                    raise ValueError(f"Unknown value type: {type(value)}")
            representation.append(values_dict)
        return representation


class OrganizationSectionData(OrganizationSectionConfigData):
    documents: list[OrganizationDocumentData]


class OrganizationSectionUpdateData(BaseModel):
    class SectionUpdateField(serializers.Field):
        def _decode_field_value(self, field_value):
            if field_value.get("id"):
                field_value["id"] = HashIdConverter.decode(field_value["id"])
            if (
                isinstance(field_value, dict)
                and isinstance(field_value.get("data"), dict)
                and field_value.get("data").get("id")
            ):
                field_value["data"]["id"] = HashIdConverter.decode(field_value["data"]["id"])
            return field_value

        def _decode_doc_values(self, doc_values):
            decoded_doc_values = []
            for doc_val in doc_values:
                decoded_doc_val = {}
                if "id" not in doc_val:
                    doc_val["id"] = None
                for field_id, field_value in doc_val.items():
                    if field_id == "id":
                        decoded_doc_val["id"] = HashIdConverter.decode(field_value) if field_value else None
                        continue
                    decoded_doc_val[HashIdConverter.decode(field_id)] = self._decode_field_value(field_value)
                decoded_doc_values.append(decoded_doc_val)
            return decoded_doc_values

        def _decode_section_value(self, section_value):
            decoded_section_value = {}
            for doc_id, doc_values in section_value.items():
                decoded_section_value[HashIdConverter.decode(doc_id)] = self._decode_doc_values(doc_values)
            return decoded_section_value

        def to_internal_value(self, data):
            sections = {}
            for section_id, section_value in data.items():
                sections[HashIdConverter.decode(section_id)] = self._decode_section_value(section_value)
            return sections

    billing_entity_id: HashIdInt | None = None
    sections: Annotated[dict, SectionUpdateField()]


class OrganizationOnboardInputData(BaseModel):
    id: HashIdInt | None = None
    name: str
    addresses: list[OrganizationAddressUpdateData]
    country_id: HashIdInt | None = None
    business_card: OrganizationBusinessCardData | None = None


class OrganizationBillingEntityCreateData(PydanticInputBaseModel):
    name: str
    addresses: list[OrganizationAddressUpdateData]
    country_id: HashIdInt
    business_card: OrganizationBusinessCardData | None = None
    sections: Annotated[dict, OrganizationSectionUpdateData.SectionUpdateField()]


class DropdownOptionSerializer(BaseModel):
    id: HashIdInt | None = None
    name: str

    class Meta:
        ref_name = "DropdownOptionSerializer"


class LinkedOrganizationBillingEntityBaseData(OrganizationBillingEntityBaseData):
    pass


class OrganizationBasicDetail(BaseModelV2):
    id: HashIdInt
    name: str
    logo: CustomFileUrlStr | None
    business_card: Optional[OrganizationBusinessCardData] = None
    addresses: list[OrganizationAddressData] = []
    country: OrganizationBasicDetailCountryData


class OrganizationBillingEntityPOC(BaseModelV2):
    name: str
    photo: CustomFileUrlStr | None


class OrganizationBillingEntityData(BaseModelV2):
    id: HashIdInt
    name: str
    is_default: bool
    is_primary: bool
    is_active: bool
    uid: str | None = None
    uid_name: str | None = None
    poc_name: str | None = None
    actions: list[BillingEntityActionEnum] = []


class OrganizationBillingEntityDropdownData(BaseModelV2):
    id: HashIdInt
    name: str
    auto_select: bool = False


class LinkedOrganizationBillingEntityData(OrganizationBillingEntityData):
    pass


class OrganizationBillingEntityFilter(PydanticInputBaseModel):
    billing_entity_id: HashIdInt | None = None


class OrganizationBillingEntityCreateEntity(BaseModelV2):
    id: HashIdInt


class OrganizationOrgConfigEntity(BaseModelV2):
    currency: CurrencyData
    tax_type: TaxTypeData
    timezone: TimezoneData
    country: CountryData
    uid_field: OrganizationBasicDetailUIDData
