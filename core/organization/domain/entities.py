from typing import Annotated, Optional

from rest_framework import serializers

from common.pydantic.base_model import BaseModel
from common.pydantic.custom_fields import CustomFileUrlStr
from common.serializers import HashIdField
from core.entities import CountryData, CurrencyData, TaxTypeData, TimezoneData
from rollingbanners.hash_id_converter import HashIdConverter


class CityData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str


class StateData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str


class FileData(BaseModel):
    file_name: str
    file: str


class OrganizationBusinessCardData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    file: CustomFileUrlStr
    file_name: str


class OrganizationAddressData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    address_line_1: Optional[str]
    address_line_2: Optional[str]
    city: Optional[CityData]
    state: Optional[StateData]
    country: Optional[CountryData]
    zip_code: Optional[str]
    value: Optional[str]


class OrganizationAddressUpdateData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city_id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    state_id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    country_id: Annotated[Optional[int], HashIdField()]
    zip_code: Optional[str] = None


class OrganizationBasicDetailsData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    logo: Optional[str]
    code: Optional[str]
    verification_status: Optional[str]
    business_card: Optional[OrganizationBusinessCardData]
    addresses: list[OrganizationAddressData]
    country: Optional[CountryData]
    currency: Optional[CurrencyData]
    tax_type: Optional[TaxTypeData]
    timezone: Optional[TimezoneData]


class OrganizationBasicDetailsUpdateData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    addresses: list[OrganizationAddressUpdateData]
    business_card: Optional[OrganizationBusinessCardData] = None


class OrganizationFieldData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    type: str
    position: int
    is_required: bool
    regex: Optional[str]
    is_visible_on_app: bool
    is_capitalized: bool


class DocumentFieldValueBaseData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]


class DocumentTextFieldValueData(DocumentFieldValueBaseData):
    data: str


class DocumentFileFieldValueData(DocumentFieldValueBaseData):
    data: Optional[FileData]


class DocumentStateFieldValueData(DocumentFieldValueBaseData):
    data: StateData


class OrganizationDocumentConfigData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    multiple_allowed: bool
    position: int
    is_required: bool
    fields: list[OrganizationFieldData]
    is_visible_on_app: bool


class OrganizationSectionConfigData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    position: int
    type: str
    documents: list[OrganizationDocumentConfigData]


class OrganizationConfigBaseData(BaseModel):
    sections: list[OrganizationSectionConfigData]


class OrganizationCountryConfigData(OrganizationConfigBaseData):
    uid_field: Optional[OrganizationFieldData]
    uid_document_id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]


class OrganizationDocumentData(OrganizationDocumentConfigData):
    class FieldsDataField(serializers.Field):
        def to_internal_value(self, data):
            return data

        def to_representation(self, data: list[dict]):
            representation = []
            for values in data:
                values_dict = {}

                for field_id, value in values.items():
                    if field_id == "id":
                        values_dict["id"] = HashIdConverter.encode(value)
                    elif type(value) in [str, int, float, bool]:
                        values_dict[HashIdConverter.encode(field_id)] = value
                    elif isinstance(value, BaseModel):
                        values_dict[HashIdConverter.encode(field_id)] = type(value).drf_serializer(value).data
                    elif isinstance(value, dict):
                        value_data_dict = {}
                        value_data_dict["id"] = HashIdConverter.encode(value["id"])
                        if isinstance(value["data"], BaseModel):
                            value_data_dict["data"] = type(value["data"]).drf_serializer(value["data"]).data
                        else:
                            value_data_dict["data"] = value["data"]
                        if isinstance(value["data"], dict) and value["data"].get("id"):
                            value_data_dict["data"]["id"] = HashIdConverter.encode(value["data"]["id"])
                        values_dict[HashIdConverter.encode(field_id)] = value_data_dict
                    else:
                        raise Exception("Unknown value type")
                representation.append(values_dict)
            return representation

    fields_data: Annotated[list[dict], FieldsDataField()]


class OrganizationSectionData(OrganizationSectionConfigData):
    documents: list[OrganizationDocumentData]


class OrganizationSectionUpdateData(BaseModel):
    class SectionUpdateField(serializers.Field):
        def _decode_field_value(self, field_value):
            if field_value.get("id"):
                field_value["id"] = HashIdConverter.decode(field_value["id"])
            if (
                isinstance(field_value, dict)
                and isinstance(field_value.get("data"), dict)
                and field_value.get("data").get("id")
            ):
                field_value["data"]["id"] = HashIdConverter.decode(field_value["data"]["id"])
            return field_value

        def _decode_doc_values(self, doc_values):
            decoded_doc_values = []
            for doc_val in doc_values:
                decoded_doc_val = {}
                if "id" not in doc_val:
                    doc_val["id"] = None
                for field_id, field_value in doc_val.items():
                    if field_id == "id":
                        decoded_doc_val["id"] = HashIdConverter.decode(field_value) if field_value else None
                        continue
                    decoded_doc_val[HashIdConverter.decode(field_id)] = self._decode_field_value(field_value)
                decoded_doc_values.append(decoded_doc_val)
            return decoded_doc_values

        def _decode_section_value(self, section_value):
            decoded_section_value = {}
            for doc_id, doc_values in section_value.items():
                decoded_section_value[HashIdConverter.decode(doc_id)] = self._decode_doc_values(doc_values)
            return decoded_section_value

        def to_internal_value(self, data):
            sections = {}
            for section_id, section_value in data.items():
                sections[HashIdConverter.decode(section_id)] = self._decode_section_value(section_value)
            return sections

    sections: Annotated[dict, SectionUpdateField()]


class OrganizationOnboardInputData(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str
    addresses: list[OrganizationAddressUpdateData]
    country_id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]


class DropdownOptionSerializer(BaseModel):
    id: Annotated[Optional[int], HashIdField(allow_null=True, default=None)]
    name: str

    class Meta:
        ref_name = "DropdownOptionSerializer"
