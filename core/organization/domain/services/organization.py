import structlog
from django.db.models import QuerySet

from authorization.domain.constants import Permissions
from common.constants import CustomFieldTypeChoices
from common.exceptions import BaseValidationError
from controlroom.data.models import OrganizationDocumentConfig
from core.entities import OrgUserEntity
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.data.models import OrganizationDocumentFieldContextConfig, OrganizationSectionConfig
from core.organization.data.selectors import get_context_field
from core.organization.domain.abstract_repos import OrganizationAbstractRepo
from core.organization.domain.entities import (
    DocumentTextFieldValueData,
    DropdownOptionSerializer,
    OrganizationAddressData,
    OrganizationBasicDetail,
    OrganizationBasicDetailsData,
    OrganizationBasicDetailsUpdateData,
    OrganizationBillingEntityCreateData,
    OrganizationBillingEntityCreateEntity,
    OrganizationDocumentConfigData,
    OrganizationDocumentData,
    OrganizationFieldData,
    OrganizationSectionConfigData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
)
from core.organization.domain.enums import BillingEntityActionEnum
from core.organization.domain.services.permission import OrganizationPermissionService
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import MicroContext

logger = structlog.get_logger(__name__)


def get_section_config_data(
    section_config: OrganizationSectionConfig,
    uid_field_id: int = None,
    uid_required: bool = False,
    bank_details_required: bool = False,
) -> OrganizationSectionConfigData:
    documents: list[OrganizationDocumentConfigData] = []
    is_documents_required = False
    if section_config.type == OrganizationSectionTypeChoices.BANK_DETAILS:
        is_documents_required = bank_details_required
    for doc_config in section_config.documents_config.all():
        fields: list[OrganizationFieldData] = []
        for field_config in doc_config.fields_config.all():
            # because vendor config only set is_required on document level,
            # not on field level
            # is_required = field_config.is_required
            if field_config.id == uid_field_id and uid_required:
                # is_required = uid_required
                is_documents_required = True
            field = OrganizationFieldData(
                id=field_config.pk,
                name=field_config.name,
                type=field_config.type,
                position=field_config.position,
                is_required=field_config.is_required if field_config.id != uid_field_id else uid_required,
                regex=field_config.regex,
                is_visible_on_app=field_config.is_visible_on_app,
                is_capitalized=field_config.is_capitalized,
            )
            fields.append(field)

        document = OrganizationDocumentConfigData(
            id=doc_config.pk,
            name=doc_config.name,
            multiple_allowed=doc_config.multiple_allowed,
            position=doc_config.position,
            is_required=is_documents_required,
            fields=fields,
            is_visible_on_app=doc_config.is_visible_on_app,
        )
        is_documents_required = False
        documents.append(document)
    return OrganizationSectionConfigData(
        id=section_config.pk,
        name=section_config.name,
        position=section_config.position,
        type=section_config.type,
        documents=documents,
    )


def prepare_order_fields_data(
    document: OrganizationDocumentConfig, context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig]
):
    fields_data = []
    field_data_dict = {}
    for item in context_config_objects:
        if item.field_config.document_config_id == document.id:
            if item.field_config.type == CustomFieldTypeChoices.TEXT:
                for obj in item.vendor_order_text_field_data.all():
                    field_data_dict[item.id] = DocumentTextFieldValueData(id=obj.pk, data=obj.data)
            else:
                # Handle for other field type data
                pass
            field_data_dict["id"] = None
    fields_data.append(field_data_dict)
    return fields_data


def prepare_proposal_fields_data(document, context_config_objects):
    fields_data = []
    field_data_dict = {}
    for item in context_config_objects:
        if item.field_config.document_config_id == document.id:
            if item.field_config.type == CustomFieldTypeChoices.TEXT:
                for obj in item.proposal_text_field_data.all():
                    field_data_dict[item.id] = DocumentTextFieldValueData(id=obj.pk, data=obj.data)
            else:
                # Handle for other field type data
                pass
            field_data_dict["id"] = None
    fields_data.append(field_data_dict)
    return fields_data


def prepare_context_fields_data(
    document: OrganizationDocumentConfig,
    context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig],
    context: MicroContext,
):
    if context == MicroContextChoices.ORDER:
        return prepare_order_fields_data(document=document, context_config_objects=context_config_objects)
    elif context == MicroContextChoices.PROPOSAL:
        return prepare_proposal_fields_data(document=document, context_config_objects=context_config_objects)
    else:
        return []


def prepare_nested_data_for_section_config(
    context_config_objects: QuerySet[OrganizationDocumentFieldContextConfig],
    context: MicroContextChoices,
    with_data: bool = False,
) -> list[OrganizationSectionData]:
    section_map = {}

    # Loop through the queryset
    for item in context_config_objects:
        section = item.field_config.document_config.section
        document = item.field_config.document_config
        field = item.field_config

        # Prepare section data
        if section.id not in section_map:
            section_map[section.id] = OrganizationSectionData(
                id=section.id, name=section.name, position=section.position, type=section.type, documents=[]
            )

        # Prepare document data and ensure no duplicate documents
        section_data = section_map[section.id]
        document_data = next((doc for doc in section_data.documents if doc.id == document.id), None)
        if not document_data:
            document_data = OrganizationDocumentData(
                id=document.id,
                name=document.name,
                multiple_allowed=document.multiple_allowed,
                position=document.position,
                is_required=document.is_required,
                is_visible_on_app=document.is_visible_on_app,
                fields=[],
                fields_data=(
                    prepare_context_fields_data(
                        document=document, context_config_objects=context_config_objects, context=context
                    )
                    if with_data
                    else []
                ),
            )
            section_data.documents.append(document_data)

        # Prepare field data and ensure no duplicate fields
        field_data = next((fld for fld in document_data.fields if fld.id == field.id), None)
        if not field_data:
            field_data = OrganizationFieldData(
                id=item.pk,  # Use the context config id as the field id
                name=item.name,  # Use the context field name as the field name
                type=CustomFieldTypeChoices.DROPDOWN,
                position=field.position,
                is_required=field.is_required,
                regex=field.regex,
                is_visible_on_app=field.is_visible_on_app,
                is_capitalized=field.is_capitalized,
            )
            document_data.fields.append(field_data)

    return list(section_map.values())


class OrganizationService:
    class BaseException(BaseValidationError):
        pass

    class BillingEntityNotFound(BaseException):
        pass

    class SectionUpdateException(BaseException):
        pass

    class DeletePrimaryBillingEntityException(BaseException):
        pass

    class MarkInActivePrimaryBillingEntityException(BaseException):
        pass

    class MarkDefaultInActiveBillingEntityException(BaseException):
        pass

    class MarkInActiveDefaultBillingEntityException(BaseException):
        pass

    def __init__(
        self,
        user_entity: OrgUserEntity,
        repo: OrganizationAbstractRepo,
        permission_service: OrganizationPermissionService,
        billing_entity_id: int | None = None,
    ):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id
        self.repo = repo
        self.billing_entity_id = billing_entity_id
        self.permission_service = permission_service

    def get_allowed_users(self):
        return self.repo.get_allowed_users()

    def get_billing_entity(self):
        try:
            return self.repo.get_billing_entity(billing_entity_id=self.billing_entity_id)
        except OrganizationAbstractRepo.BillingEntityNotFound as e:
            raise self.BillingEntityNotFound(e) from e

    def get_basic_details(self) -> OrganizationBasicDetailsData:
        billing_entity = self.get_billing_entity()

        addresses = self.repo.get_addresses(billing_entity_id=billing_entity.id)
        business_card = self.repo.get_business_card(billing_entity_id=billing_entity.id)
        org_config = self.repo.get_org_config()

        return OrganizationBasicDetailsData(
            id=billing_entity.id,
            name=billing_entity.name,
            addresses=addresses,
            business_card=business_card,
            logo=billing_entity.logo,
            country=billing_entity.country,
            currency=org_config.currency,
            tax_type=org_config.tax_type,
            timezone=org_config.timezone,
            uid_field=org_config.uid_field if billing_entity.is_primary else None,
            code=None,
            verification_status=None,
            # code=org.code,
            # verification_status=(
            #     org.verification_status if org.verification_status else OrganizationVerificationStatus.NOT_VERIFIED.value
            # ),
        )

    def get_prefill_basic_details(self, org_id: int) -> OrganizationBasicDetail:
        billing_entity = self.repo.get_billing_entity(org_id=org_id)
        addresses = self.repo.get_addresses(billing_entity_id=billing_entity.id, org_id=org_id)
        business_card = self.repo.get_business_card(billing_entity_id=billing_entity.id)

        return OrganizationBasicDetail(
            id=org_id,
            name=billing_entity.name,
            addresses=addresses,
            business_card=business_card,
            logo=billing_entity.logo,
            country=billing_entity.country,
        )

    def get_billing_entity_addresses(self) -> list[OrganizationAddressData]:
        if self.billing_entity_id:
            billing_entity = self.get_billing_entity()
            return self.repo.get_addresses(billing_entity_id=billing_entity.id)

        return self.repo.get_addresses()

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData):
        billing_entity = self.get_billing_entity()

        logger.info("Updating basic details", data=data, billing_entity_id=billing_entity.id)
        self.repo.update_basic_details(data=data, billing_entity_id=billing_entity.id)
        logger.info("Basic details updated")

    def get_kyc_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.KYC_DETAILS,
            country_id=billing_entity.country.id,
        )

    def get_bank_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.BANK_DETAILS,
            country_id=billing_entity.country.id,
        )

    def get_other_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.OTHER_DETAILS,
            country_id=billing_entity.country.id,
        )

    def update_sections_data(self, data: OrganizationSectionUpdateData):
        billing_entity = self.get_billing_entity()

        logger.info("Updating organization section data", billing_entity_id=billing_entity.id, data=data)
        try:
            self.repo.update_sections_data(
                data=data,
                billing_entity_id=billing_entity.id,
                is_primary_billing_entity=billing_entity.is_primary,
            )
        except OrganizationAbstractRepo.SectionUpdateException as e:
            raise self.SectionUpdateException(e) from e

        logger.info("Organization section data updated")

    def get_context_field_options(self, context_config_id: int, vendor_id: int):
        context_config = get_context_field(context_config_id=context_config_id, org_id=self.org_id, vendor_id=vendor_id)
        option_objects = []
        if context_config.field_config.type == CustomFieldTypeChoices.TEXT:
            option_objects = context_config.field_config.text_data

        options = []
        for option in option_objects:
            options.append(DropdownOptionSerializer(id=option.pk, name=option.data))
        return options

    def get_billing_entities(self, billing_entity_id: int | None = None):
        billing_entities = self.repo.get_billing_entities(billing_entity_id=billing_entity_id)

        if billing_entity_id and len(billing_entities) != 1:
            raise self.BillingEntityNotFound("Billing entity not found")

        can_edit_billing_entity = self.permission_service.is_action_permitted(
            permission=Permissions.CAN_EDIT_BILLING_ENTITIES
        )

        if not can_edit_billing_entity:
            return billing_entities

        for billing_entity in billing_entities:
            actions = []

            if not billing_entity.is_primary:
                if billing_entity.is_active:
                    actions.append(BillingEntityActionEnum.mark_as_inactive)

                    if billing_entity.is_default:
                        actions.append(BillingEntityActionEnum.unmark_as_default)
                    else:
                        actions.append(BillingEntityActionEnum.mark_as_default)

                else:
                    actions.append(BillingEntityActionEnum.mark_as_active)

                actions.append(BillingEntityActionEnum.delete)
            else:
                if billing_entity.is_default:
                    actions.append(BillingEntityActionEnum.unmark_as_default)
                else:
                    actions.append(BillingEntityActionEnum.mark_as_default)

            billing_entity.actions = actions

        return billing_entities

    def get_billing_entity_dropdown_list(self):
        return self.repo.get_billing_entity_dropdown_list()

    def create_billing_entity(self, data: OrganizationBillingEntityCreateData):
        logger.info("Creating billing entity", data=data)
        billing_entity = self.repo.create_billing_entity(name=data.name, country_id=data.country_id)
        logger.info("Billing entity created", billing_entity_id=billing_entity.id)

        self.repo.update_basic_details(
            data=OrganizationBasicDetailsUpdateData(
                id=None,
                name=data.name,
                addresses=data.addresses,
                business_card=data.business_card,
            ),
            billing_entity_id=billing_entity.id,
        )

        logger.info("Updating organization section data")
        try:
            self.repo.update_sections_data(
                data=OrganizationSectionUpdateData(sections=data.sections),
                billing_entity_id=billing_entity.id,
                is_primary_billing_entity=billing_entity.is_primary,
            )
        except OrganizationAbstractRepo.SectionUpdateException as e:
            raise self.SectionUpdateException(e) from e

        logger.info("Organization section data updated")

        return OrganizationBillingEntityCreateEntity(id=billing_entity.id)

    def mark_billing_entity_default(self, is_default: bool):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_default == is_default:
            logger.info(
                "Organization billing entity already has the desired default status",
                org_id=self.org_id,
                billing_entity_id=billing_entity.id,
                is_default=is_default,
            )
            return

        if not billing_entity.is_active and is_default:
            raise self.MarkDefaultInActiveBillingEntityException("Cannot mark an inactive billing entity as default.")

        logger.info(
            "Marking organization billing entity as default",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
            is_default=is_default,
        )
        self.repo.mark_billing_entity_default(billing_entity_id=billing_entity.id, is_default=is_default)
        logger.info("Organization billing entity marked as default")

    def mark_billing_entity_active(self, is_active: bool):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_primary and not is_active:
            raise self.MarkInActivePrimaryBillingEntityException("Primary billing entity cannot be deactivated.")

        if billing_entity.is_default and not is_active:
            raise self.MarkInActiveDefaultBillingEntityException("Default billing entity cannot be marked as inactive")

        if billing_entity.is_active == is_active:
            logger.info(
                "Organization billing entity already has the desired active status",
                org_id=self.org_id,
                billing_entity_id=billing_entity.id,
                is_active=is_active,
            )
            return

        logger.info(
            "Marking organization billing entity as active",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
            is_active=is_active,
        )
        self.repo.mark_billing_entity_active(billing_entity_id=billing_entity.id, is_active=is_active)
        logger.info("Organization billing entity marked as active")

    def delete_billing_entity(self):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_primary or billing_entity.is_default:
            raise self.DeletePrimaryBillingEntityException("Primary billing entity cannot be deleted.")

        logger.info(
            "Deleting organization billing entity",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
        )
        self.repo.delete_billing_entity(billing_entity_id=billing_entity.id)
        logger.info("Organization billing entity deleted")

    def get_billing_entity_config(self, country_id: int) -> list[OrganizationSectionConfigData]:
        return self.repo.get_section_configs_entity(country_id=country_id)

    def update_billing_entity_logo(self, logo: str | None):
        billing_entity = self.get_billing_entity()

        logger.info("Updating billing entity logo", billing_entity=billing_entity, logo=logo)
        self.repo.update_billing_entity_logo(
            billing_entity_id=billing_entity.id,
            is_primary=billing_entity.is_primary,
            logo=logo,
        )
        logger.info("Billing entity logo updated")
