import structlog

from common.exceptions import BaseValidationError
from core.entities import OrgUserEntity
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.domain.abstract_repos import LinkedOrganizationAbstractRepo
from core.organization.domain.entities import (
    OrganizationBasicDetail,
    OrganizationBasicDetailsUpdateData,
    OrganizationBillingEntityCreateData,
    OrganizationBillingEntityCreateEntity,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
)
from core.organization.domain.enums import BillingEntityActionEnum

logger = structlog.get_logger(__name__)


class LinkedOrganizationService:
    class BaseException(BaseValidationError):
        pass

    class BillingEntityNotFound(BaseException):
        pass

    class SectionUpdateException(BaseException):
        pass

    class UIDFieldConfigNotFound(BaseException):
        pass

    class DeletePrimaryBillingEntityException(BaseException):
        pass

    class MarkInActivePrimaryBillingEntityException(BaseException):
        pass

    class MarkDefaultInActiveBillingEntityException(BaseException):
        pass

    class MarkInActiveDefaultBillingEntityException(BaseException):
        pass

    def __init__(
        self,
        user_entity: OrgUserEntity,
        repo: LinkedOrganizationAbstractRepo,
        billing_entity_id: int | None = None,
    ):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id
        self.repo = repo
        self.billing_entity_id = billing_entity_id

    def get_billing_entity(self):
        try:
            return self.repo.get_billing_entity(billing_entity_id=self.billing_entity_id)
        except LinkedOrganizationAbstractRepo.BillingEntityNotFound as e:
            raise self.BillingEntityNotFound(e) from e

    def get_basic_details(self) -> OrganizationBasicDetail:
        billing_entity = self.get_billing_entity()

        addresses = self.repo.get_linked_addresses(billing_entity_id=billing_entity.id)
        business_card = self.repo.get_linked_business_card(billing_entity_id=billing_entity.id)

        return OrganizationBasicDetail(
            id=billing_entity.id,
            name=billing_entity.name,
            addresses=addresses,
            business_card=business_card,
            logo=billing_entity.logo,
            country=billing_entity.country,
        )

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData):
        billing_entity = self.get_billing_entity()

        logger.info("Updating linked organization basic details", billing_entity_id=billing_entity.id, data=data)
        self.repo.update_basic_details(data=data, billing_entity_id=billing_entity.id)
        logger.info("Linked organization basic details updated")

    def get_kyc_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.KYC_DETAILS,
            country_id=billing_entity.country.id,
        )

    def get_bank_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.BANK_DETAILS,
            country_id=billing_entity.country.id,
        )

    def get_other_details(self) -> OrganizationSectionData:
        billing_entity = self.get_billing_entity()

        return self.repo.get_section_details(
            billing_entity_id=billing_entity.id,
            section_type=OrganizationSectionTypeEnum.OTHER_DETAILS,
            country_id=billing_entity.country.id,
        )

    def update_sections_data(self, data: OrganizationSectionUpdateData):
        billing_entity = self.get_billing_entity()

        logger.info("Updating linked organization section data", billing_entity_id=billing_entity.id, data=data)
        try:
            self.repo.update_sections_data(
                data=data,
                billing_entity_id=billing_entity.id,
                is_primary_billing_entity=billing_entity.is_primary,
            )
        except LinkedOrganizationAbstractRepo.SectionUpdateException as e:
            raise self.SectionUpdateException(e) from e

        logger.info("Linked organization section data updated")

    def get_billing_entities(self, billing_entity_id: int | None = None):
        billing_entities = self.repo.get_billing_entities(billing_entity_id=billing_entity_id)

        if billing_entity_id and len(billing_entities) != 1:
            raise self.BillingEntityNotFound("Billing entity not found")

        for billing_entity in billing_entities:
            actions = []

            if not billing_entity.is_primary:
                if billing_entity.is_active:
                    actions.append(BillingEntityActionEnum.mark_as_inactive)

                    if billing_entity.is_default:
                        actions.append(BillingEntityActionEnum.unmark_as_default)
                    else:
                        actions.append(BillingEntityActionEnum.mark_as_default)

                else:
                    actions.append(BillingEntityActionEnum.mark_as_active)

                actions.append(BillingEntityActionEnum.delete)
            else:
                if billing_entity.is_default:
                    actions.append(BillingEntityActionEnum.unmark_as_default)
                else:
                    actions.append(BillingEntityActionEnum.mark_as_default)

            billing_entity.actions = actions

        return billing_entities

    def create_billing_entity(self, data: OrganizationBillingEntityCreateData):
        logger.info("Creating linked organization billing entity", data=data)
        billing_entity = self.repo.create_billing_entity(name=data.name, country_id=data.country_id)
        logger.info("Linked organization billing entity created", billing_entity_id=billing_entity.id)

        self.repo.update_basic_details(
            data=OrganizationBasicDetailsUpdateData(
                id=None,
                name=data.name,
                addresses=data.addresses,
                business_card=data.business_card,
            ),
            billing_entity_id=billing_entity.id,
        )

        logger.info("Updating linked organization section data")
        try:
            self.repo.update_sections_data(
                data=OrganizationSectionUpdateData(sections=data.sections),
                billing_entity_id=billing_entity.id,
                is_primary_billing_entity=billing_entity.is_primary,
            )
        except LinkedOrganizationAbstractRepo.SectionUpdateException as e:
            raise self.SectionUpdateException(e) from e

        logger.info("Linked organization section data updated")

        return OrganizationBillingEntityCreateEntity(id=billing_entity.id)

    def mark_billing_entity_default(self, is_default: bool):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_default == is_default:
            logger.info(
                "Linked organization billing entity already has the desired default status",
                org_id=self.org_id,
                billing_entity_id=billing_entity.id,
                is_default=is_default,
            )
            return

        if not billing_entity.is_active and is_default:
            raise self.MarkDefaultInActiveBillingEntityException("Cannot mark an inactive billing entity as default")

        logger.info(
            "Marking linked organization billing entity as default",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
            is_default=is_default,
        )
        self.repo.mark_billing_entity_default(billing_entity_id=billing_entity.id, is_default=is_default)
        logger.info("Linked organization billing entity marked as default")

    def mark_billing_entity_active(self, is_active: bool):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_primary and not is_active:
            raise self.MarkInActivePrimaryBillingEntityException("Primary billing entity cannot be marked as inactive")

        if billing_entity.is_default and not is_active:
            raise self.MarkInActiveDefaultBillingEntityException("Default billing entity cannot be marked as inactive")

        if billing_entity.is_active == is_active:
            logger.info(
                "Linked organization billing entity already has the desired active status",
                org_id=self.org_id,
                billing_entity_id=billing_entity.id,
                is_active=is_active,
            )
            return

        logger.info(
            "Marking linked organization billing entity as active",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
            is_active=is_active,
        )
        self.repo.mark_billing_entity_active(billing_entity_id=billing_entity.id, is_active=is_active)
        logger.info("Linked organization billing entity marked as active")

    def delete_billing_entity(self):
        billing_entity = self.get_billing_entity()

        if billing_entity.is_primary or billing_entity.is_default:
            raise self.DeletePrimaryBillingEntityException("Primary billing entity cannot be deleted")

        logger.info(
            "Deleting linked organization billing entity",
            org_id=self.org_id,
            billing_entity_id=billing_entity.id,
        )
        self.repo.delete_billing_entity(billing_entity_id=billing_entity.id)
        logger.info("Linked organization billing entity deleted")

    def update_billing_entity_logo(self, logo: str | None):
        billing_entity = self.get_billing_entity()

        logger.info("Updating billing entity logo", billing_entity=billing_entity, logo=logo)
        self.repo.update_billing_entity_logo(billing_entity_id=billing_entity.id, logo=logo)
        logger.info("Billing entity logo updated")
