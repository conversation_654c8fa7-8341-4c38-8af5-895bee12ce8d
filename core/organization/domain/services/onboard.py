import re
from typing import Optional

import structlog

from common.choices import VendorStatusChoices
from common.exceptions import BaseValidationError
from core.choices import OrganizationDocumentChoices
from core.models import FromToOrgMapping, Organization, OrganizationAddress, OrganizationDocument
from core.organization.data.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationBillingEntity,
    LinkedOrganizationBusinessCard,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationBillingEntity,
)
from core.organization.data.selectors import get_organization_sections_config, get_uid_field_by_country
from core.organization.domain.entities import (
    OrganizationAddressUpdateData,
    OrganizationBusinessCardUpdateData,
)

logger = structlog.get_logger(__name__)


class OrganizationOnboardBaseService:
    class BaseException(BaseValidationError):
        pass

    class InvalidFromToOrgMappingException(BaseException):
        pass

    class UIDConfigNotFound(BaseException):
        pass

    class InvalidUidException(BaseException):
        pass

    class UIDAlreadyExists(BaseException):
        pass

    def create_linked_addresses(
        self,
        addresses_data: list[OrganizationAddressUpdateData],
        mapping_id: int,
        billing_entity_id: int,
        user_id: int,
    ):
        addresses = []
        for address_data in addresses_data:
            addresses.append(
                LinkedOrganizationAddress(
                    mapping_id=mapping_id,
                    address_line_1=address_data.address_line_1,
                    address_line_2=address_data.address_line_2,
                    city_id=address_data.city_id,
                    state_id=address_data.state_id,
                    country_id=address_data.country_id,
                    zip_code=address_data.zip_code,
                    created_by_id=user_id,
                    billing_entity_id=billing_entity_id,
                )
            )
        LinkedOrganizationAddress.objects.bulk_create(addresses)

    def create_addresses(
        self,
        addresses_data: list[OrganizationAddressUpdateData],
        org_id: int,
        user_id: int,
        billing_entity_id: int,
    ):
        addresses = []
        for address_data in addresses_data:
            addresses.append(
                OrganizationAddress(
                    organization_id=org_id,
                    address_line_1=address_data.address_line_1,
                    address_line_2=address_data.address_line_2,
                    city_id=address_data.city_id,
                    state_id=address_data.state_id,
                    country_id=address_data.country_id,
                    zip_code=address_data.zip_code,
                    created_by_id=user_id,
                    billing_entity_id=billing_entity_id,
                )
            )
        OrganizationAddress.objects.bulk_create(addresses)

    def create_from_to_org_mapping(
        self,
        client_id: int,
        vendor_id: int,
        user_id: int,
        client_poc_id: Optional[int] = None,
        vendor_poc_id: Optional[int] = None,
        vendor_status: Optional[VendorStatusChoices] = VendorStatusChoices.ACTIVE,
    ) -> int:
        if client_id == vendor_id:
            raise self.InvalidFromToOrgMappingException("Client and Vendor cannot be same")

        mapping = FromToOrgMapping.objects.create(
            org_from_id=client_id,
            org_to_id=vendor_id,
            invited_by_org_id=vendor_id,
            client_poc_id=client_poc_id,
            vendor_poc_id=vendor_poc_id,
            created_by_id=user_id,
            vendor_status=vendor_status,
        )
        return mapping.pk

    def create_billing_entity(
        self,
        org_id: int,
        country_id: int,
        name: str,
        user_id: int,
    ) -> int:
        logger.info(
            "Creating billing entity",
            org_id=org_id,
            country_id=country_id,
            name=name,
            user_id=user_id,
        )
        billing_entity = OrganizationBillingEntity()
        billing_entity.organization_id = org_id
        billing_entity.country_id = country_id
        billing_entity.name = name
        billing_entity.created_by_id = user_id
        billing_entity.is_default = True
        billing_entity.is_primary = True
        billing_entity.save()
        logger.info("Billing entity created", billing_entity_id=billing_entity.pk)
        return billing_entity.pk

    def create_linked_billing_entity(
        self,
        mapping_id: int,
        country_id: int,
        name: str,
        user_id: int,
        logo: str | None = None,
    ) -> int:
        logger.info(
            "Creating linked billing entity",
            mapping_id=mapping_id,
            name=name,
            user_id=user_id,
            country_id=country_id,
        )
        billing_entity = LinkedOrganizationBillingEntity()
        billing_entity.mapping_id = mapping_id
        billing_entity.name = name
        billing_entity.logo = logo
        billing_entity.is_default = True
        billing_entity.is_primary = True
        billing_entity.country_id = country_id
        billing_entity.created_by_id = user_id

        billing_entity.save()

        logger.info("Linked billing entity created", billing_entity_id=billing_entity.pk)
        return billing_entity.pk

    def create_linked_business_card(
        self,
        data: OrganizationBusinessCardUpdateData,
        mapping_id: int,
        billing_entity_id: int,
        user_id: int,
    ):
        logger.info(
            "Creating linked business card",
            mapping_id=mapping_id,
            billing_entity_id=billing_entity_id,
            user_id=user_id,
            data=data,
        )

        business_card = LinkedOrganizationBusinessCard()
        business_card.mapping_id = mapping_id
        business_card.billing_entity_id = billing_entity_id
        business_card.name = data.name
        business_card.file = data.file
        business_card.created_by_id = user_id

        business_card.save()

        logger.info("Linked business card created", business_card_id=business_card.pk)

    def create_business_card(
        self,
        data: OrganizationBusinessCardUpdateData,
        org_id: int,
        user_id: int,
        billing_entity_id: int,
    ):
        business_card = OrganizationDocument()

        business_card.organization_id = org_id
        business_card.name = data.name
        business_card.file = data.file
        business_card.uploaded_by_id = user_id
        business_card.billing_entity_id = billing_entity_id
        business_card.type = OrganizationDocumentChoices.BUSINESS_CARD

        business_card.save()

    def get_uid_field(self, uid: str, country_id: int):
        uid_field_config = get_uid_field_by_country(country_id=country_id)
        if uid_field_config is None:
            logger.info("uid_field not found for country", country_id=country_id)
            raise self.UIDConfigNotFound("uid_field is not found for country")

        if uid_field_config.regex and not re.match(uid_field_config.regex, uid):
            logger.info("uid validation failed", uid=uid, regex=uid_field_config.regex)
            raise self.InvalidUidException("Please enter correct uid value")

        return uid_field_config

    def check_uid_in_linked_vendor(self, uid: str, org_id: int):
        """
        Check if the uid is already linked to a vendor in the organization.
        """
        vendor_mapping_ids = LinkedOrganizations.objects.filter(
            client_id=org_id,
            org_id=org_id,
            deleted_at__isnull=True,
        ).values_list("id", flat=True)

        if LinkedOrganizationDocumentTextFieldData.objects.filter(
            data=uid,
            document__mapping_id__in=vendor_mapping_ids,
            document__deleted_at__isnull=True,
        ).exists():
            raise self.UIDAlreadyExists("UID already exists in linked vendor")

    def check_uid_in_linked_client(self, uid: str, org_id: int):
        """
        Check if the uid is already linked to a client in the organization.
        """
        client_mapping_ids = LinkedOrganizations.objects.filter(
            vendor_id=org_id,
            org_id=org_id,
            deleted_at__isnull=True,
        ).values_list("id", flat=True)

        if LinkedOrganizationDocumentTextFieldData.objects.filter(
            data=uid,
            document__mapping_id__in=client_mapping_ids,
            document__deleted_at__isnull=True,
        ).exists():
            raise self.UIDAlreadyExists("UID already exists in linked client")

    def check_uid_in_organization(self, uid: str) -> int | None:
        """
        Check if the uid is already exists in any organization.
        """

        org = Organization.objects.filter(uid=uid).first()

        if org:
            logger.info("UID already exists in organization", uid=uid, org_id=org.pk)
            return org.pk
        logger.info("UID does not exist in any organization", uid=uid)
        return None

    def get_org_sections_config(self, country_id: int):
        return get_organization_sections_config(country_id=country_id)
