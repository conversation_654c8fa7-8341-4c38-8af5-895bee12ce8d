from phonenumber_field import serializerfields
from rest_framework import serializers

from common.serializers import BaseDataclassSerializer, BaseModelSerializer, IdNameSerializer
from core.organization.entities import OnboardUserEntity
from core.organization.models import LinkedOrganizationAddress
from core.organization.validators import poc_data_validator


class UserAddInputSerializer(BaseDataclassSerializer):
    phone_number = serializerfields.PhoneNumberField(allow_null=True)
    email = serializers.EmailField(allow_null=True)

    def validate(self, data):
        poc_data_validator(data=data)
        return data

    class Meta:
        dataclass = OnboardUserEntity


class LinkedOrganizationAddressBaseModelSerializer(BaseModelSerializer):
    id = serializers.CharField(source="id")
    country = IdNameSerializer()
    state = IdNameSerializer()
    city = IdNameSerializer()
    value = serializers.CharField(source="full_address", required=False, allow_null=True)

    class Meta:
        model = LinkedOrganizationAddress
        fields = ("id", "address_line_1", "address_line_2", "city", "state", "country", "zip_code", "value")
        output_hash_id_fields = ("id",)
