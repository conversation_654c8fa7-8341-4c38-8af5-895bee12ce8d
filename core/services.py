import datetime
from collections import defaultdict
from datetime import date
from typing import Any, Dict, List, Optional, Tuple

import structlog
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.gis.geos import Point
from django.core.exceptions import ValidationError
from django.db import connection, transaction
from django.db.utils import OperationalError
from django.utils import timezone
from redis.exceptions import ConnectionError
from rest_framework.authtoken.models import Token

from authorization.domain.constants import Actions
from common.choices import OrganizationType, SourceChoices
from common.services import model_update, nested_object_segregation
from core.analytics import EventTracker, PostHogService
from core.choices import OrganizationDocumentChoices
from core.entities import (
    OrganizationGSTUpdateData,
    OrganizationPANUpdateData,
    OrganizationPaymentTermEntity,
)
from core.exceptions import (
    DatabaseConnectionException,
    InactiveUserException,
    OrganizationException,
    RedisConnectionException,
    UserNotRegistered,
)
from core.models import (
    City,
    Currency,
    FromToOrgMapping,
    ItemExpenseType,
    OneTimeUseToken,
    Organization,
    OrganizationConfig,
    OrganizationDocument,
    OrganizationGSTNumber,
    OrganizationLegalEntity,
    OrganizationOrderPaymentTerm,
    OrganizationUser,
    PlatformVersion,
    PmcClientMapping,
    RegionStateMapping,
    SystemUserAccessHistory,
    TaxType,
    Timezone,
    UnitOfMeasurementOrgMapping,
    User,
    UserLocationHistory,
    UserLoginHistory,
)
from core.selectors import (
    category_expense_item_type_fetch,
    fetch_organization_config_using_org_id,
    organization_active_admin_users_fetch,
    organization_active_expense_item_type_fetch,
    organization_active_users_fetch,
    organization_details_fetch,
    organization_user_fetch_all_cache,
    platform_version_get,
)
from core.utils import convert_local_expiry_to_utc, get_current_date_in_timezone
from project.domain.status import Module
from rollingbanners.authentication import TokenData
from rollingbanners.custom_caches import REDIS_INSTANCE

logger = structlog.get_logger(__name__)


def set_user_profile_to_analytics_platform(user: User, additional_fields: Optional[Dict] = None):
    if settings.IS_EVENT_ANALYTICS_ENABLED and user.org_id in [25824]:
        user_profile = {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "phone": user.phone_number.as_e164 if user.phone_number else None,
            "organization_name": user.org.name if user.org else None,
            "organization_id": user.org.id if user.org else None,
        }
        if additional_fields:
            for key, value in additional_fields.items():
                if value:
                    user_profile.update({key: value})

        et = EventTrackerFactory.get_event_tracker()
        try:
            et.set_user(user.pk, user_profile)
        except Exception as e:
            logger.error(f"Error while setting user profile to analytics platform: {e}")


def organization_user_role_mappings_get(*, org_id: int) -> dict:
    org_users = organization_user_fetch_all_cache(org_id=org_id)
    mappings = defaultdict(list)
    for org_user in org_users:
        if org_user.role_id is None:
            mappings[str(org_user.user_id)] = []
            continue
        mappings[str(org_user.user_id)].append(org_user.role_id)
    return dict(mappings)


def user_get_using_phone_number(
    *,
    phone_number: str,
    first_name: str = "",
    last_name: str = "",
    password: Optional[str] = None,
    create: bool = False,
    timezone: Optional[Timezone] = None,
) -> User:
    if create:
        user, is_created = User.objects.get_or_create(
            phone_number=phone_number,
            deleted_at__isnull=True,
            defaults={
                "username": phone_number,
                "first_name": first_name,
                "last_name": last_name,
                "is_verified": True,
            },
        )
        if is_created:
            user.set_password(password if password else User.objects.make_random_password())
            user.timezone_id = timezone.pk if timezone else None
            user.save()
    else:
        user = (
            User.objects.filter(phone_number=phone_number, deleted_by_id__isnull=True, deleted_at__isnull=True)
            .select_related("org")
            .first()
        )
    if user is None:
        raise UserNotRegistered(f"User with phone number {phone_number} is not registered.")
    if not user.is_active:
        raise InactiveUserException(f"User with phone number {phone_number} is inactivated.")
    return user


def user_get_using_email(
    email: str, first_name: str, last_name: str, password: Optional[str] = None, create: bool = False
) -> User:
    if create:
        user, is_created = User.objects.get_or_create(
            email=email,
            defaults={"username": email, "first_name": first_name, "last_name": last_name},
        )
        if is_created:
            logger.info("New User created", email=email, first_name=first_name, last_name=last_name)
            user.set_password(password if password else User.objects.make_random_password())
            user.save()
        return user
    try:
        return User.objects.get(email=email)
    except User.DoesNotExist:
        raise UserNotRegistered(f"User with email {email} is not registered.")


# deprecated
def add_user_to_organization(user: User, organization: Organization) -> None:
    organization.users.add(user)


@transaction.atomic
def organization_user_get_or_create(
    *, user_id: int, org_id: int, joined_by_id: int, is_admin: bool = False, role_id: int = None
) -> OrganizationUser:
    instance, created = OrganizationUser.objects.get_or_create(
        user_id=user_id,
        organization_id=org_id,
        defaults={"is_admin": is_admin, "joined_by_id": joined_by_id, "role_id": role_id},
    )
    if created:
        instance.clean()
    return instance


def organization_user_update(
    *, instance: OrganizationUser, data: Dict[str, Any], updated_by_id: Optional[int] = None
) -> OrganizationUser:
    fields = ["is_active", "is_admin", "accessed_at", "role_id"]
    organization_user, _, _ = model_update(instance=instance, fields=fields, data=data, updated_by_id=updated_by_id)
    return organization_user


def check_if_current_org_is_91_sqft(*, org_id: int):
    if "91Squarefeet" != Organization.objects.get(id=org_id).name:
        return False
    return True


def organization_create(name: str, type: OrganizationType) -> Organization:
    organization = Organization(name=name, type=type)
    organization.full_clean()
    organization.save()
    return organization


def organization_create_by_referral_org(
    name: str,
    org_type: OrganizationType,
    referral_org_id: int,
    referred_by_id: int,
    country_id: Optional[int] = None,
    pan_number: Optional[str] = None,
    uid: Optional[str] = None,
):
    if pan_number and Organization.objects.filter(pan_number=pan_number).exists():
        logger.info("Pan Number already exists", pan_number=pan_number, name=name)
        # raise ValidationError("Pan Number already exists")

    organization = Organization(
        name=name,
        # can be used for saving initial org_type- CLIENT OR VENDOR
        type=org_type,
        referral_org_id=referral_org_id,
        referral_by_id=referred_by_id,
        country_id=country_id,
        uid=uid,  # Unique Identifier for the organization, if applicable
    )
    organization.full_clean()
    organization.save()
    return organization


def organization_update(id: int, name: str) -> None:
    Organization.objects.filter(id=id).update(name=name)


def pmc_client_mapping_create(pmc_id: int, client_id: int) -> None:
    PmcClientMapping.objects.create(pmc_id=pmc_id, client_id=client_id)


def token_delete_using_user(user_id: int):
    Token.objects.filter(user_id=user_id).delete()


class ItemExpenseTypeService:
    def update(self, organization_id: int, item_type_id: int, category_id: int, user_id: int):
        item_type = organization_active_expense_item_type_fetch(org_id=organization_id).filter(pk=item_type_id).first()
        category = category_expense_item_type_fetch().filter(id=category_id).first()
        if item_type is None:
            logger.error("Item type not found", item_type_id=item_type_id, organization_id=organization_id)
            raise ValidationError("Item type not found")
        item_type.updated_by_id = user_id
        item_type.updated_at = timezone.now()
        if item_type.category_id != category_id:
            item_type.category_id = category_id
            item_type.icon = category.image if category.image else None
        item_type.clean()
        item_type.save()

    def create(self, category_id: int, organization_id: int, name: str, user_id: int):
        category = category_expense_item_type_fetch().filter(id=category_id).first()
        icon_url = category.image if category.image else None

        item_expense_type = ItemExpenseType(
            category_id=category_id, name=name, organization_id=organization_id, icon=icon_url, created_by_id=user_id
        )
        item_expense_type.clean()
        item_expense_type.save()

    def archive(self, organization_id: int, item_type_id: int, user_id: int):
        item_type = organization_active_expense_item_type_fetch(org_id=organization_id).filter(id=item_type_id).first()
        item_type.archived_at = timezone.now()
        item_type.archived_by_id = user_id
        item_type.save()

    def unarchive(self, organization_id: int, item_type_id: int, user_id: int):
        item_type = organization_active_expense_item_type_fetch(org_id=organization_id).filter(id=item_type_id).first()
        item_type.archived_at = None
        item_type.archived_by = None
        item_type.updated_by_id = user_id
        item_type.save()


def user_location_history_create(*, user_id: int, latitude: Optional[float], longitude: Optional[float]) -> None:
    if latitude and longitude:
        UserLocationHistory.objects.create(created_by_id=user_id, location=Point(longitude, latitude))


class OneTimeUseApiServices:
    @staticmethod
    def generate_tokens(context: Module.choices, context_id: int, user_ids: List[int]) -> List[OneTimeUseToken]:
        tokens = []
        for user_id in user_ids:
            token = OneTimeUseToken(context=context, context_id=context_id, created_for_id=user_id)
            tokens.append(token)
        return OneTimeUseToken.objects.bulk_create(tokens)

    @staticmethod
    def expire_token(instance: OneTimeUseToken, ip: str, user_id: int) -> None:
        fields = ["ip", "used_by_id", "is_active"]
        data = {"ip": ip, "used_by_user_id": user_id, "is_active": False}
        instance, _, _ = model_update(instance=instance, fields=fields, data=data, save=True)


def get_user_with_token_data(org_id: int, user_id: int) -> User:
    org_user = OrganizationUser.objects.filter(organization_id=org_id, user_id=user_id).select_related("user").first()
    user = org_user.user

    setattr(user, "token_data", TokenData(key=" ", is_admin=org_user.is_admin, org_id=org_id, user_id=user_id))
    return user


def get_runtime_user_with_token_data(org_id: int, user_id: int, is_admin) -> User:
    user = User(id=user_id, org_id=org_id)
    setattr(user, "token_data", TokenData(key=" ", is_admin=is_admin, org_id=org_id, user_id=user_id))
    return user


def replace_vendor_poc(organization_id: int, user_id: int):
    poc_to_update = FromToOrgMapping.objects.filter(vendor_poc_id=user_id)
    if not poc_to_update:
        return
    try:
        config = OrganizationConfig.objects.get(organization_id=organization_id)
        poc_to_update.update(vendor_poc_id=config.poc_id)
    except OrganizationConfig.DoesNotExist:
        raise ValidationError("Standard POC not found for this Organization. Kindly contact support")


def create_login_history(ip_address, user_id, source=SourceChoices.CUSTOM):
    UserLoginHistory.objects.create(
        ip_address=ip_address,
        user_id=user_id,
        source=source,
    )


def organization_payment_terms_create(created_by_id: int, organization_id: int, data: OrganizationPaymentTermEntity):
    payment_term = OrganizationOrderPaymentTerm(
        organization_id=organization_id,
        created_by_id=created_by_id,
        description=data.description,
        is_active=True,
        title=data.title,
    )
    payment_term.full_clean()
    payment_term.save()
    return payment_term


def system_user_history_create(accessed_by_id: int, system_user_id: int, organization_id: int):
    history = SystemUserAccessHistory()
    history.accessed_by_id = accessed_by_id
    history.system_user_id = system_user_id
    history.organization_id = organization_id
    history.save()
    return history


def platform_version_create(*, platform: str, version: str) -> None:
    PlatformVersion(platform=platform, version=version).save()


class EventTrackerFactory:
    @staticmethod
    def get_event_tracker() -> EventTracker:
        et = EventTracker(external_services=[PostHogService])
        return et


class OrganizationGSTService:
    _gst_number_model = OrganizationGSTNumber

    @classmethod
    def gst_bulk_create(cls, data: List[OrganizationGSTUpdateData], organization_id: int, user_id: int):
        data = [data for data in data if data.number or data.state or (data.file and data.file_name)]
        gst_list = []
        for gst_object in data:
            gst_list.append(
                cls._gst_number_model(
                    organization_id=organization_id,
                    gst_number=gst_object.number,
                    file=gst_object.file,
                    name=gst_object.file_name,
                    created_by_id=user_id,
                    gst_state=gst_object.state,
                )
            )
        if gst_list:
            cls._gst_number_model.objects.bulk_create(objs=gst_list)

    @classmethod
    def gst_bulk_delete(cls, gst_ids: List[int], user_id: int):
        cls._gst_number_model.objects.filter(id__in=gst_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())

    @classmethod
    def gst_bulk_update(cls, data: List[OrganizationGSTUpdateData], user_id: int):
        to_update = sorted(data, key=lambda x: x.id)
        to_update_instances = (
            cls._gst_number_model.objects.filter(id__in=[gst_object.id for gst_object in to_update])
            .order_by("id")
            .all()
        )
        to_update_objs = []
        for gst_object, to_update_instance in zip(to_update, to_update_instances):
            updated_obj, _, _ = model_update(
                instance=to_update_instance,
                fields=["gst_number", "file", "name", "gst_state"],
                data={
                    "gst_number": gst_object.number,
                    "file": gst_object.file,
                    "name": gst_object.file_name,
                    "gst_state": gst_object.state,
                },
                updated_by_id=user_id,
                save=False,
                clean=False,
            )
            to_update_objs.append(updated_obj)

        cls._gst_number_model.objects.bulk_update(
            objs=to_update_objs, fields=["gst_number", "file", "name", "gst_state"]
        )

    @classmethod
    def gst_data_update(cls, data: List[OrganizationGSTUpdateData], organization_id: int, user_id: int):
        to_create, to_update, to_delete = nested_object_segregation(docs_list=data)
        if to_create:
            cls.gst_bulk_create(data=to_create, organization_id=organization_id, user_id=user_id)
        if to_update:
            cls.gst_bulk_update(data=to_update, user_id=user_id)
        if to_delete:
            cls.gst_bulk_delete(gst_ids=[gst_object.id for gst_object in to_delete], user_id=user_id)


class OrganizationDocumentService:
    _document_model = OrganizationDocument

    @classmethod
    def create_bulk_documents(cls, files, organization_id: int, user_id: int, type: OrganizationDocumentChoices):
        to_create_objs = []
        for doc in files:
            to_create_objs.append(
                cls._document_model(
                    organization_id=organization_id,
                    file=doc.file,
                    name=doc.file_name,
                    type=type,
                    uploaded_by_id=user_id,
                )
            )
        cls._document_model.objects.bulk_create(objs=to_create_objs)

    @classmethod
    def delete_many_documents(cls, document_ids: List[int], user_id: int):
        cls._document_model.objects.filter(id__in=document_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())


class OrganizationPanService:
    document_service = OrganizationDocumentService

    @classmethod
    def pan_data_update(cls, data: OrganizationPANUpdateData, organization: Organization, user_id: int):
        pan_number = data.number
        updated_fields = []
        if pan_number != organization.pan_number:
            other_organization = (
                Organization.objects.exclude(id=organization.pk, pan_number__isnull=False)
                .filter(pan_number=pan_number)
                .first()
            )
            if pan_number and other_organization:
                raise OrganizationException("Pan Number Exist for Other Organization, Kindly Contact Support")
            organization, _, updated_fields = model_update(
                instance=organization,
                fields=["pan_number"],
                data={"pan_number": pan_number},
                updated_by_id=user_id,
                save=False,
                clean=False,
            )
        if data.files:
            to_create, _, to_delete = nested_object_segregation(docs_list=data.files)
            if to_create:
                cls.document_service.create_bulk_documents(
                    files=to_create,
                    organization_id=organization.pk,
                    user_id=user_id,
                    type=OrganizationDocumentChoices.PAN,
                )
            if to_delete:
                cls.document_service.delete_many_documents(document_ids=[doc.id for doc in to_delete], user_id=user_id)
        organization.save(update_fields=updated_fields)
        return organization, updated_fields


def organization_old_users_email_fetch(organization_id: int, exclude_poc_user_id: int):
    user_email_list = list(
        organization_active_admin_users_fetch(
            organization_id=organization_id, exclude_poc_id=exclude_poc_user_id
        ).values_list("user__email", flat=True)
    )

    if not user_email_list:
        user_email_list = list(
            organization_active_users_fetch(
                organization_id=organization_id, exclude_poc_id=exclude_poc_user_id
            ).values_list("email", flat=True)
        )

    return user_email_list


def platform_target_version_fetch(*, platform: SourceChoices) -> str:
    platform_version = platform_version_get(platform=platform)
    if platform_version:
        return platform_version.version
    return ""


def get_org_blocked_action(org_id: int) -> List[str]:
    config: Optional[OrganizationConfig] = (
        OrganizationConfig.objects.filter(pk=org_id).prefetch_related("blocked_actions").first()
    )
    if config is None:
        return []
    if config.block_all_actions:
        return [x[0] for x in Actions.choices]
    return list(config.blocked_actions.all().values_list("action", flat=True))


def region_state_mapping_fetch(*, state: int, org_id: int) -> Optional[int]:
    reg_state_mapping = RegionStateMapping.objects.filter(state=state, region__organization_id=org_id).first()
    if not reg_state_mapping:
        return None
    return reg_state_mapping.region.pk


def add_organization_legal_name(organization_id: int, company_name: str):
    organization_legal_entity = OrganizationLegalEntity(organization_id=organization_id, name=company_name)
    organization_legal_entity.full_clean()
    organization_legal_entity.save()
    return organization_legal_entity


def check_db_connection():
    try:
        connection.ensure_connection()
    except OperationalError:
        logger.error("Database connection failed")
        raise DatabaseConnectionException("Database connection failed")


def check_redis_connection():
    try:
        REDIS_INSTANCE.ping()
    except ConnectionError:
        logger.error("Redis connection failed")
        raise RedisConnectionException("Redis connection failed")


def organization_details(organization_id: int):
    organization_data = organization_details_fetch(organization_id=organization_id)

    if not organization_data:
        return
    is_kyc_done = False
    if not organization_data.msme and hasattr(organization_data, "vendor") and getattr(organization_data, "vendor"):
        setattr(
            organization_data,
            "msme",
            {"number": organization_data.vendor.msme_id, "file": None, "file_name": None, "id": None},
        )

    if not organization_data.pan:
        setattr(
            organization_data,
            "pan",
            {"number": organization_data.pan_number, "file": None, "file_name": None, "id": None},
        )

    aadhar_data = {
        "number": organization_data.vendor.aadhar_number if hasattr(organization_data, "vendor") else "",
        "files": [],
    }
    for data in organization_data.aadhar_data_prefetch:
        aadhar_data["files"].append({"id": data.id, "file": data.file, "file_name": data.name})

    setattr(organization_data, "aadhar", aadhar_data)

    if organization_data.pan.get("number") and organization_data.pan.get("file"):
        is_kyc_done = True
    setattr(organization_data, "is_kyc_done", is_kyc_done)
    return organization_data


def create_city_from_state_city_mapping(state_id: int, city_name: str):
    City.objects.create(name=city_name, state_id=state_id)


def get_or_create_org_config(org_id: int):
    org_config = OrganizationConfig.objects.filter(organization_id=org_id).first()
    if not org_config:
        timezone, _ = Timezone.objects.get_or_create(name="Asia/Kolkata")
        currency, _ = Currency.objects.get_or_create(symbol="₹", code="INR")
        tax_type, _ = TaxType.objects.get_or_create(name="GST")
        org_config, _ = OrganizationConfig.objects.get_or_create(
            organization_id=org_id,
            defaults={"timezone_id": timezone.pk, "currency_id": currency.pk, "tax_type_id": tax_type.pk},
        )
    return org_config


def check_if_organization_po_flow_enabled(organization_id: int) -> bool:
    config = OrganizationConfig.objects.filter(organization_id=organization_id).first()
    if not config:
        return False
    if config.order_flow == OrganizationConfig.OrderFlowChoices.PO_FLOW.value:
        return True
    return False


def enable_disable_org_level_uom(*, uom_id: int, org_id: int, is_active: bool):
    uom_org_mapping, _ = UnitOfMeasurementOrgMapping.objects.update_or_create(uom_id=uom_id, organization_id=org_id)
    uom_org_mapping.is_active = is_active
    uom_org_mapping.save()
    return uom_org_mapping


def is_phone_number_login_allowed(user):
    if hasattr(user, "org_id") and user.org_id:
        org_config = OrganizationConfig.objects.filter(organization_id=user.org_id).first()
        if org_config is not None and org_config.is_phone_number_login_enabled is False:
            return False

    return True


"""
    Takes an organization config object as input and returns a tuple
    (
        boolean = whether that organization's subscription is still active or not,
        UTC Timestamp = the exact timestamp when that org's subscription will end
    )
"""


def _validate_subscription(org_config: OrganizationConfig) -> Tuple[bool, datetime.datetime]:
    org_subscription_end_date: date | None = getattr(org_config, "subscription_end_date", None)
    if org_subscription_end_date is None:
        org_subscription_end_date = timezone.now().date() + relativedelta(
            years=1
        )  # default value for no org config or no subscription end date cases()
    org_timezone_str: str = getattr(org_config.timezone, "name")
    today_date_of_org_timezone: date = get_current_date_in_timezone(org_timezone_str)
    is_valid: bool = today_date_of_org_timezone <= org_subscription_end_date
    org_expiry_utc: datetime.datetime = convert_local_expiry_to_utc(org_subscription_end_date, org_timezone_str)
    return (is_valid, org_expiry_utc)


"""
    A wrapper function on top of _validate_subscription
    method used at multiple places, created for resusability
"""


def validate_subscription_using_org_id(org_id: int) -> Tuple[bool, Optional[date]]:
    from core.selectors import get_default_currency_tax_timezone

    org_config = fetch_organization_config_using_org_id(org_id)
    if org_config:
        subscription_end_date: date | None = getattr(org_config, "subscription_end_date", None)
        if org_config.timezone is None:
            default_timezone_data = get_default_currency_tax_timezone()
            setattr(
                org_config,
                "timezone",
                Timezone(
                    tz=default_timezone_data.name,
                    locale=default_timezone_data.locale,
                ),
            )
        is_valid, __ = _validate_subscription(org_config)
        return is_valid, subscription_end_date

    return True, None
