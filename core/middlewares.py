import datetime
import time

import jwt
import structlog
from django.conf import settings
from django.contrib.sessions.models import Session
from django.http import JsonResponse
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import gettext_lazy as _
from rest_framework.authentication import get_authorization_header

from common.choices import SourceChoices
from common.constants import RequestHeaders
from common.threadlocals import ThreadLocalCache
from common.utils import RequestDataExtractor
from core.caches import PlatformVersionCache
from rollingbanners.authentication import BearerAuthentication, TokenData, generate_token_data_object
from rollingbanners.jwt import decode

logger = structlog.get_logger(__name__)


class OneSessionPerUserMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        if request.path == "/admin/login/" and request.method == "POST" and request.user.is_authenticated:
            user_sessions = Session.objects.filter(expire_date__gte=datetime.datetime.now())
            for session in user_sessions:
                session_data = session.get_decoded()
                if session_data.get("_auth_user_id") == str(request.user.id):
                    if session.session_key != request.session.session_key:
                        session.delete()
        return response


class ResetToDefaultTimezoneMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        timezone.deactivate()
        return response


class RequestIdResponseHeaderMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        response["Request-Id"] = logger._context.get("request_id", "")  # noqa
        return response


class ValidateAndDecodeTokenMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    @staticmethod
    def process_unauthorised(message):
        code = 401
        return JsonResponse(
            {"code": code, "message": message, "success": False, "data": None, "errors": {}}, status=code
        )

    def __call__(self, request):
        auth = get_authorization_header(request).split()
        if auth:
            if auth[0].lower() != BearerAuthentication.keyword.lower().encode():
                msg = _("Invalid token header. Wrong keyword provided.")
                return self.process_unauthorised(msg)
            elif len(auth) == 1:
                msg = _("Invalid token header. No credentials provided.")
                return self.process_unauthorised(msg)
            elif len(auth) > 2:
                msg = _("Invalid token header. Token string should not contain spaces.")
                return self.process_unauthorised(msg)

            try:
                token = auth[1].decode()
            except UnicodeError:
                msg = _("Invalid token header. Token string should not contain invalid characters.")
                return self.process_unauthorised(msg)
            try:
                token_data: TokenData = generate_token_data_object(data=decode(token))
                setattr(request, "token_data", token_data)
            except jwt.PyJWTError as e:
                logger.info("Could not decode token.", error=str(e))
                msg = _("Invalid token.")
                return self.process_unauthorised(msg)
        response = self.get_response(request)
        return response


class PlatformVersionResponseHeaderMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        platform = request.META.get(
            f"HTTP_{RequestHeaders.PLATFORM.value.replace('-', '_').upper()}"
        ) or request.META.get(
            f"HTTP_{RequestHeaders.OLD_PLATFORM.value.replace('-', '_').upper()}", SourceChoices.CUSTOM.value
        )
        response["target-client-version"] = PlatformVersionCache.get(instance_id=platform)
        return response


class RequestTelemetryMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.blacklist_apis = frozenset(getattr(settings, "LOGGING_TELEMETRY_BLACKLIST_APIS", []))

    def __call__(self, request):
        setattr(request, "start_time", time.time())
        response = self.get_response(request)

        try:
            resolver_match_data = RequestDataExtractor.extract_resolver_match(request)
            api_name = resolver_match_data.get("api", None)
            if not api_name or api_name in self.blacklist_apis:
                return response
            _telemetry_request_token_data = getattr(request, "_telemetry_request_token_data", None)
            _telemetry_request_headers_data = getattr(request, "_telemetry_request_headers_data", None)
            if not _telemetry_request_token_data:
                _telemetry_request_token_data = RequestDataExtractor.extract_request_token_data(request)
            if not _telemetry_request_headers_data:
                _telemetry_request_headers_data = RequestDataExtractor.extract_request_headers_data(request)
            bind_data = {
                "request_id": RequestDataExtractor.get_request_header(request, "x-request-id", "HTTP_X_REQUEST_ID"),
                **RequestDataExtractor.extract_request_metadata(request),
                **RequestDataExtractor.calculate_latency_data(request),
                **resolver_match_data,
                **_telemetry_request_token_data,
                **_telemetry_request_headers_data,
            }
            if not bind_data:
                return response
            method = getattr(request, "method", "UNKNOWN_METHOD")
            status_code = getattr(response, "status_code", 0)
            logger.info(
                "end_request",
                extract_labels=True,
                code=status_code,
                method=method,
                **bind_data,
            )
        except Exception as e:
            api_name_for_error_log = "unknown_api"
            try:
                resolver_match_data = RequestDataExtractor.extract_resolver_match(request)
                if resolver_match_data.get("api", None):
                    api_name_for_error_log = resolver_match_data.get("api", None)
                elif hasattr(request, "path"):
                    api_name_for_error_log = request.path
            except Exception:
                pass
            logger.error(
                "Error in telemetry middleware",
                api=api_name_for_error_log,
                error=str(e),
                exc_info=True,
            )
        return response


class ThreadLocalMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        ThreadLocalCache.set(settings.THREAD_LOCAL_KEY, {})
        response = self.get_response(request)
        ThreadLocalCache.delete_all()
        return response
