import abc
from abc import ABC, ABCMeta
from typing import Any, Generic, List, Type, TypeVar, Union

import structlog
from django.conf import settings
from django.db.utils import ProgrammingError
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from rest_framework.authentication import exceptions

from authorization.domain.constants import Permissions
from common import threadlocals
from common.choices import PermissionScope, ProjectUserRoleLevelChoices
from common.pydantic.base_cache import PydanticBaseCache
from common.pydantic.base_model import BaseModel
from core.entities import (
    BaseDataCacheEntity,
    OrganizationCountryConfigCacheData,
    OrgUserEntity,
    OrgUserPermissionEntity,
    UserOrgData,
)
from core.models import (
    Country,
    OrganizationConfig,
    OrganizationConfigCorePermission,
    TestingPhoneNumber,
    UnitOfMeasurement,
    User,
    UserTokenView,
)
from core.role.constants import ROLE_LEVEL_PERMISSION_MAPPING
from core.role.repository import RoleRepository
from core.selectors import get_organization_active_uoms, get_organization_config, role_action_list, role_permission_list
from core.serializers import UserTokenViewSerializer
from core.services import (
    get_org_blocked_action,
    get_user_with_token_data,
    organization_user_role_mappings_get,
    platform_target_version_fetch,
)
from rollingbanners.authentication import RequestMetaData, TokenData
from rollingbanners.custom_caches import BaseCache

CACHE: BaseCache = import_string(settings.CUSTOM_CACHE)
logger = structlog.get_logger()

BaseDataCacheInstanceIdType = Union[int, str, BaseDataCacheEntity]

T = TypeVar("T")


class GenericBaseDataCache(abc.ABC, Generic[T]):
    CACHE_KEY = None
    CACHE_TTL = 60 * 60  # 1 hour

    @classmethod
    def get_cache_key(cls, key: BaseDataCacheInstanceIdType) -> str:
        if not cls.CACHE_KEY or not isinstance(cls.CACHE_KEY, str) or cls.CACHE_KEY.count("_{}") != 1:  # "something_{}"
            raise Exception("Invalid cache key.")

        if isinstance(key, str) or isinstance(key, int):
            return cls.CACHE_KEY.format(str(key))

        raise Exception("Convert key to string before passing it to get_cache_key.")

    @classmethod
    def get_cache_key_regex(cls) -> str:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    @threadlocals.thread_local_cache()
    def get(cls, key: BaseDataCacheInstanceIdType) -> T:
        cache_key = cls.get_cache_key(key=key)
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(key=key)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    def delete(cls, key: BaseDataCacheInstanceIdType) -> None:
        CACHE.delete(cls.get_cache_key(key=key))

    @classmethod
    def delete_many(cls) -> None:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    @abc.abstractmethod
    def get_data(cls, key: BaseDataCacheInstanceIdType) -> T:
        pass


class IncrAndDecrCounterCache(GenericBaseDataCache[int]):
    @classmethod
    def incr(cls, key: BaseDataCacheInstanceIdType) -> int:
        cache_key = cls.get_cache_key(key=key)
        cls.get(key=key)
        return CACHE.incr(cache_key)

    @classmethod
    def decr(cls, key: BaseDataCacheInstanceIdType) -> int:
        cache_key = cls.get_cache_key(key=key)
        cls.get(key=key)
        return CACHE.decr(cache_key)

    @classmethod
    def get(cls, key: BaseDataCacheInstanceIdType) -> int:
        data = super().get(key=key)
        assert isinstance(data, int)
        return data


class BaseDataCache(abc.ABC):
    CACHE_KEY = None
    CACHE_TTL = 60 * 60  # 1 hour

    @classmethod
    def get_cache_key(cls, key: str) -> str:
        if not cls.CACHE_KEY or not isinstance(cls.CACHE_KEY, str) or cls.CACHE_KEY.count("_{}") != 1:  # "something_{}"
            raise Exception("Invalid cache key.")
        return cls.CACHE_KEY.format(key)

    @classmethod
    def get_cache_key_regex(cls) -> str:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    # @threadlocals.thread_local_cache()
    def get(cls, instance_id: Union[int, str]) -> Union[list, dict]:
        cache_key = cls.get_cache_key(key=str(instance_id))
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(instance_id=instance_id)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    def delete(cls, instance_id: Union[int, str]) -> None:
        CACHE.delete(cls.get_cache_key(key=str(instance_id)))

    @classmethod
    def delete_many(cls) -> None:
        raise NotImplementedError("Method not implemented.")

    @classmethod
    @abc.abstractmethod
    def get_data(cls, instance_id: int) -> Union[list, dict]:
        pass


class BaseDataEntityCacheMeta(ABCMeta):
    def __new__(mcs, name, bases, namespace):
        cls = super().__new__(mcs, name, bases, namespace)
        # Skip validation for the base class itself
        if not any(base is BaseDataCache for base in bases):
            dataclass = getattr(cls, "dataclass", None)
            if dataclass is None:
                raise AttributeError(f"'{name}' must define 'dataclass'.")
            if not issubclass(dataclass, BaseModel):
                raise ValueError(f"'dataclass' in '{name}' must be a subclass of {BaseModel.__name__}.")
        return cls


class BaseDataEntityCache(BaseDataCache, metaclass=BaseDataEntityCacheMeta):
    dataclass: Type[BaseModel] = None

    @classmethod
    def get(cls, instance_id: Union[int, str]) -> BaseModel:
        cache_key = cls.get_cache_key(key=str(instance_id))
        raw_data = CACHE.get(cache_key, decode_json=True)
        if raw_data is None:
            data, raw_data = cls._set_data(instance_id=instance_id)
            return data
        # TODO: Have to change drf_serializer validation
        # when pydantic module_dump is merged
        serializer = cls.dataclass.drf_serializer(data=raw_data)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    @classmethod
    def get_raw_data(cls, instance_id: Union[int, str]) -> dict:
        cache_key = cls.get_cache_key(key=str(instance_id))
        raw_data = CACHE.get(cache_key, decode_json=True)
        if raw_data is None:
            _, raw_data = cls._set_data(instance_id=instance_id)
        return raw_data

    @classmethod
    def _set_data(cls, instance_id: Union[int, str]) -> tuple[BaseModel, dict]:
        cache_key = cls.get_cache_key(key=str(instance_id))
        data = cls.get_data(instance_id=instance_id)
        raw_data = cls.dataclass.drf_serializer(data).data
        CACHE.set_with_ttl(cache_key, raw_data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data, raw_data

    @classmethod
    @abc.abstractmethod
    def get_data(cls, instance_id: int) -> BaseModel:
        pass


class BaseCoreCache(abc.ABC):
    CACHE_KEY = None
    CACHE_TTL = 60 * 60  # 1 hour

    @classmethod
    @threadlocals.thread_local_cache()
    def get(cls) -> Any:
        cache_key = cls.CACHE_KEY
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data()
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    def delete(cls) -> None:
        CACHE.delete(cls.CACHE_KEY)

    @classmethod
    @abc.abstractmethod
    def get_data(cls) -> Any:
        pass


class UnitOfMeasurementCache(BaseCoreCache):
    CACHE_KEY = "unit_of_measurement"

    @classmethod
    def get_data(cls) -> dict[str, str]:
        try:
            return {str(i.pk): i.name for i in UnitOfMeasurement.objects.filter(is_active=True)}
        except ProgrammingError:
            return {}


class OrganizationUOMCache(BaseDataCache):
    CACHE_KEY = "unit_of_measurement_org_{}"
    CACHE_KEY_REGEX = "unit_of_measurement_org"  # Remove the last variable part _{} to delete all

    @classmethod
    def get_data(cls, instance_id: int) -> dict[str, str]:
        org_uoms = get_organization_active_uoms(organization_id=instance_id)
        return {str(org_uom.id): org_uom.name for org_uom in org_uoms}

    @classmethod
    def get_cache_key_regex(cls) -> str:
        return cls.CACHE_KEY.split("_{}")[0]

    @classmethod
    def delete_many(cls) -> None:
        CACHE.delete_many(cls.get_cache_key_regex())


class TestingPhoneNumberCache(BaseCoreCache):
    CACHE_KEY = "testing_phone_numbers"
    CACHE_TTL = 60 * 60 * 24  # 1 day

    @classmethod
    def get_data(cls) -> dict:
        return {i.phone_number.as_e164: i.otp for i in TestingPhoneNumber.objects.filter(is_active=True)}


class UserDataCache(BaseDataCache, ABC):
    CACHE_TTL = 60 * 5

    @classmethod
    def get(cls, user: User) -> Union[list, dict]:
        cache_key = cls.get_cache_key(key=f"{user.pk}_{user.token_data.org_id}")
        data = CACHE.get(cache_key, decode_json=True)
        if data is None:
            data = cls.get_data(user=user)
            CACHE.set_with_ttl(cache_key, data, ttl_seconds=cls.CACHE_TTL, encode_json=True)
        return data

    @classmethod
    @abc.abstractmethod
    def get_data(cls, user: User) -> Union[list, dict]:
        pass

    @classmethod
    def delete(cls, user_org_data: UserOrgData) -> None:
        CACHE.delete(cls.get_cache_key(key=f"{user_org_data.user_id}_{user_org_data.org_id}"))


class LevelPermissionsCache:
    @classmethod
    def get(cls, instance_id: int) -> dict:
        return ROLE_LEVEL_PERMISSION_MAPPING.get(
            instance_id, {"action_permissions": [], "notification_permissions": []}
        )


class RolePermissionCache(BaseDataCache):
    CACHE_KEY = "role_perms_v3_{}"

    @classmethod
    def get_role_type(cls, role):
        if role.scope == PermissionScope.PROJECT.value:
            return "project_user"
        elif role.scope == PermissionScope.CORE.value:
            return "organization_user"
        else:
            return "project_organization"

    @classmethod
    def get_data(cls, instance_id: int) -> dict:
        role = RoleRepository().get_role(role_id=instance_id)
        permissions: List[Permissions] = list(role_permission_list(role_id=instance_id))
        return {"level": role.level, "permissions": permissions}

    @classmethod
    def get_direct_permission(cls, instance_id: int) -> Union[list, dict]:
        data: dict = super().get(instance_id=instance_id)
        return data.get("permissions", [])

    @classmethod
    def get(cls, instance_id: int) -> Union[list, dict]:
        data: dict = super().get(instance_id=instance_id)
        permissions = set(data.get("permissions", []))
        if data.get("level", None):
            level_permissions = set(LevelPermissionsCache.get(instance_id=data.get("level")).get("action_permissions"))
            if data.get("level") == ProjectUserRoleLevelChoices.LEVEL_96:
                return list(level_permissions)
            permissions = permissions.union(level_permissions)
        permissions.add(Permissions.CAN_ACCESS_OTHER_EXPENSE)
        return list(permissions)


class RoleActionCache(BaseDataCache):
    CACHE_KEY = "role_actions_{}"

    @classmethod
    def get_data(cls, instance_id: int) -> list:
        return list(role_action_list(role_id=instance_id))


class UserTokenCache(BaseDataCache):
    CACHE_KEY = "user_token_v2_{}"
    CACHE_TTL = 60 * 30  # 0.5 hour

    @classmethod
    def get_data(cls, instance_id: str) -> dict:
        try:
            user_token = UserTokenView.objects.get(key=instance_id)
        except UserTokenView.DoesNotExist:
            logger.info("User token not found.", user_token_id=instance_id)
            raise exceptions.AuthenticationFailed(_("Invalid token."))
        return UserTokenViewSerializer(user_token).data

    @classmethod
    def get(cls, instance_id: str) -> User:
        data = super().get(instance_id=instance_id)
        user = User(
            id=data.get("id"),
            org_id=data.get("org_id"),
            email=data.get("email"),
            first_name=data.get("first_name"),
            last_name=data.get("last_name"),
            phone_number=data.get("phone_number"),
        )
        setattr(
            user,
            "token_data",
            TokenData(
                key=data.get("key"),
                user_id=data.get("id"),
                is_admin=data.get("is_admin"),
                org_id=data.get("org_id"),
                org_type=data.get("org_type"),
                org_observer=data.get("org_observer"),
                is_app_token=data.get("is_app_token"),
            ),
        )
        setattr(
            user,
            "request_metadata",
            RequestMetaData(
                org_name=data.get("org_name"),
                user_name=f"{data.get('first_name')} {data.get('last_name')}".strip(),
                country_name=data.get("country_name"),
                email=data.get("email"),
            ),
        )
        return user


class UserAuthCache(UserTokenCache):
    CACHE_KEY = "user_auth_v2_{}"

    @classmethod
    def get_data(cls, instance_id: str) -> dict:
        try:
            user_token = UserTokenView.objects.get(id=instance_id)
        except UserTokenView.DoesNotExist:
            logger.info("User token not found.", user_token_id=instance_id)
            raise exceptions.AuthenticationFailed(_("Invalid token."))
        return UserTokenViewSerializer(user_token).data


class OrgAssignmentCache(BaseDataCache):
    CACHE_KEY = "org_users_{}"

    @classmethod
    def get_data(cls, instance_id: int) -> dict:
        return organization_user_role_mappings_get(org_id=instance_id)


class OrganizationConfigPermissionCache(BaseDataCache):
    CACHE_KEY = "org_config_perms_{}"

    @classmethod
    def get_data(cls, instance_id: int) -> list:
        permissions = list(
            OrganizationConfigCorePermission.objects.filter(organization_config_id=instance_id).values_list(
                "permission", flat=True
            )
        )
        try:
            if OrganizationConfig.objects.get(pk=instance_id).metabase_dashboard_id:
                permissions.append(Permissions.CAN_ACCESS_METABASE_DASHBOARD.value)
        except OrganizationConfig.DoesNotExist:
            pass
        return permissions


class PlatformVersionCache(BaseDataCache):
    CACHE_KEY = "platform_version_{}"
    CACHE_TTL = 3600 * 24 * 30  # 30 days

    @classmethod
    def get_data(cls, instance_id) -> str:
        return platform_target_version_fetch(platform=instance_id)


class OrganizationCountryConfigCache(BaseDataCache):
    CACHE_KEY = "org_config_v2_{}"  # TDS is introduced in v2

    @classmethod
    def get_data(cls, instance_id: int) -> dict:
        config: OrganizationCountryConfigCacheData = get_organization_config(org_id=instance_id)
        return OrganizationCountryConfigCacheData.drf_serializer(config).data

    @classmethod
    def get(cls, instance_id: int) -> OrganizationCountryConfigCacheData:
        data = super().get(instance_id)
        serializer = OrganizationCountryConfigCacheData.drf_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        return serializer.save()


class OrganizationBlockedActionCache(BaseDataCache):
    CACHE_KEY = "org_blocked_actions_{}"
    CACHE_TTL = 3600 * 1  # 1 hour

    @classmethod
    def get_data(cls, instance_id) -> list:
        return get_org_blocked_action(org_id=instance_id)


class CountryListCache(BaseCoreCache):
    CACHE_KEY = "country_list"
    CACHE_TTL = 60 * 60 * 24  # 1 day

    @classmethod
    def get_data(cls) -> list:
        return list(
            Country.objects.filter(is_active=True)
            .order_by("name")
            .values(
                "id",
                "name",
                "code",
                "flag",
                "phone_number_length",
                "phone_number_regex",
                "timezone",
                "is_state_available",
            )
        )


class OrgUserPermissionCache(PydanticBaseCache[OrgUserEntity, OrgUserPermissionEntity]):
    CACHE_KEY = "user_org_permission_{}"
    CACHE_TTL = 10

    @classmethod
    def get_cache_key(cls, key: OrgUserEntity):
        return cls.CACHE_KEY.format(f"{key.user_id}_{key.org_id}")

    @classmethod
    def get(cls, key: OrgUserEntity):
        cache_key = cls.get_cache_key(key=key)
        cache_data = CACHE.get(cache_key, decode_json=True)

        if cache_data is None:
            data = cls.get_data(key=key)
            data_dump = data.model_dump()
            CACHE.set_with_ttl(cache_key, data_dump, ttl_seconds=cls.CACHE_TTL, encode_json=True)
            return data

        return OrgUserPermissionEntity.model_validate(cache_data)

    @classmethod
    def get_data(cls, key: OrgUserEntity):
        from core.helpers import OrgPermissionHelper

        user = get_user_with_token_data(org_id=key.org_id, user_id=key.user_id)

        permissions = OrgPermissionHelper.get_permissions(user=user)

        permission_members = Permissions.__members__.values()

        permission_enums = [Permissions(permission) for permission in permissions if permission in permission_members]
        return OrgUserPermissionEntity(permissions=permission_enums)
