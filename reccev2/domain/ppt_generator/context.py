from django.db.models import Prefetch, QuerySet
from django.utils import timezone

from common.json_parser.constants import const
from common.ppt_generator.assets import COVER_IMAGE, EXCEL_ICON, IMAGE_ICON, PDF_ICON, PPT_ICON, VIDEO_ICON
from common.ppt_generator.generator import BasePPTContextGenerator
from core.selectors import organization_get
from project.data.models import Project
from recce.data.models import RecceField, RecceSection
from reccev2.data.models import Recce, RecceFile
from reccev2.domain.ppt_generator import FileType, SlideType
from reccev2.domain.ppt_generator.entities import (
    FileFieldData,
    ReccePPTData,
    ReccePPTFilesFieldSlideData,
    ReccePPTProjectDetailsSlideData,
    ReccePPTRichTextFieldSlideData,
    ReccePPTSlideData,
    ReccePPTSpaceFieldElementData,
    ReccePPTSpacesFieldSlideData,
    ReccePPTTextFieldsSlideData,
    ReccePPTTitleSlideData,
    RichTextField,
    TextFieldData,
)
from reccev2.domain.services import recce_file_elements_data_get
from rollingbanners.storage_backends import PublicMediaFileStorage


def get_icon(path: str) -> str:
    extension = path.split(".")[-1].lower()

    if extension in ["mp4", "mp4v", "mpg4", "mpeg", "mpg", "mpe", "m1v", "m2v", "mov"]:
        return VIDEO_ICON
    elif extension in ["png", "jpg", "jpeg"]:
        return IMAGE_ICON
    elif extension in ["pptx", "ppt"]:
        return PPT_ICON
    elif extension in ["xlsx"]:
        return EXCEL_ICON
    elif extension in ["pdf"]:
        return PDF_ICON
    return path


class BaseReccePPTContextGenerator(BasePPTContextGenerator):
    def __init__(self, project_id: int, recce_ids: list, org_id: int) -> None:
        self._project_id = project_id
        self._recce_ids = recce_ids
        self.org_id = org_id
        self.logo = (
            self.get_organization().logo.url if self.get_organization().logo.name else self.get_organization().name
        )

    def get_queryset(self):
        files_prefetch = RecceFile.objects.available().select_related("thumbnail").order_by("-uploaded_at")

        fields_prefetch = (
            RecceField.objects.available().prefetch_related(Prefetch("files", files_prefetch)).order_by("created_at")
        )
        sections_prefetch = (
            RecceSection.objects.all().prefetch_related(Prefetch("fields", fields_prefetch)).order_by("created_at")
        )
        return (
            Recce.objects.available()
            .select_related("project", "project__client", "project__store__city")
            .filter(id__in=self._recce_ids, project_id=self._project_id)
            .prefetch_related(Prefetch("sections", sections_prefetch))
            .order_by("-created_at")
        )

    def project_detail_slide_data(self, project: Project):
        return ReccePPTProjectDetailsSlideData(
            type=SlideType.PROJECT_DETAILS,
            logo=self.logo,
            project_name=project.name,
            job_id=project.job_id,
            client_name=project.client.name,
            city=project.store.city.name if project.store.city else "",
            address=project.store.full_address,
            generated_at=str(timezone.now()),
            cover_image=COVER_IMAGE,
        )

    def get_title_slide_data(self, project: Project, recce: Recce):
        return ReccePPTTitleSlideData(
            type=SlideType.TITLE,
            project_name=project.name,
            job_id=project.job_id,
            logo=self.logo,
            generated_at=str(recce.created_at),
            city=project.store.city,
            recce_name=recce.name if recce.name else f"Recce {recce.version}",
            submitted_at="-" if recce.submitted_at is None else str(timezone.localtime(recce.submitted_at)),
            submitted_by="-" if recce.submitted_by is None else str(recce.submitted_by),
            contact="" if recce.submitted_by is None else str(recce.submitted_by.contact),
        )

    def get_file_field_data(self, file: RecceFile):
        url = file.file.url
        extension = url.split(".")[-1].lower()

        if extension in ["mp4", "mp4v", "mpg4", "mpeg", "mpg", "mpe", "m1v", "m2v", "mov"]:
            return FileFieldData(
                hyperlink=url,
                thumbnail=(
                    file.thumbnail.thumbnail.url
                    if hasattr(file, "thumbnail") and file.thumbnail.thumbnail.name
                    else VIDEO_ICON
                ),
                icon=VIDEO_ICON,
                type=FileType.VIDEO,
                name=file.name,
            )
        elif extension in ["png", "jpg", "jpeg"]:
            return FileFieldData(hyperlink=url, thumbnail=url, type=FileType.IMAGE, name=file.name)
        elif extension in ["pptx", "ppt"]:
            return FileFieldData(hyperlink=url, icon=PPT_ICON, type=FileType.ATTACHMENT, name=file.name)
        elif extension in ["xlsx"]:
            return FileFieldData(hyperlink=url, icon=EXCEL_ICON, type=FileType.ATTACHMENT, name=file.name)
        else:
            return FileFieldData(hyperlink=url, icon=PDF_ICON, type=FileType.ATTACHMENT, name=file.name)

    def get_recce_file_field_slide_data(self, section: RecceSection, field: RecceField, recce: Recce, is_first: bool):
        slides: list[ReccePPTFilesFieldSlideData] = []
        files: QuerySet[RecceFile] = field.files.all()
        files_count = len(files)
        slides_required = (files_count + 1) // 2
        i = 0
        for _ in range(slides_required):
            slides.append(
                ReccePPTFilesFieldSlideData(
                    type=SlideType.FILES,
                    job_id=str(recce.project.job_id),
                    project_name=recce.project.name,
                    recce_name=str(recce.name),
                    section_name=section.name,
                    city=recce.project.store.city,
                    logo=self.logo,
                    generated_at=str(recce.created_at),
                    is_first=is_first,
                    field_name=field.name,
                    file1=self.get_file_field_data(file=files[i]),
                    file2=self.get_file_field_data(file=files[i + 1]) if (i + 1) < len(files) else None,
                )
            )
            is_first = False
            i += 2
        return slides

    def get_recce_spaces_field_slide_data(self, section: RecceSection, field: RecceField, recce: Recce, is_first: bool):
        slides: list[ReccePPTSpacesFieldSlideData] = []
        files: QuerySet[RecceFile] = field.files.all()
        slidewise_elements = []
        for file in files:
            slide_required = 1
            remaining_rows = 16
            slidewise_elements = []
            elements_data = recce_file_elements_data_get(recce_file=file)
            elements: list[ReccePPTSpaceFieldElementData] = []
            for data in elements_data:
                rows_required = (
                    len(data.get("name")) // 12 if len(data.get("name")) % 12 == 0 else len(data.get("name")) // 12 + 1
                )
                if remaining_rows >= rows_required:
                    remaining_rows = remaining_rows - rows_required
                else:
                    slide_required += 1
                    slidewise_elements.append(elements)
                    elements = []
                    remaining_rows = 13 - rows_required
                elements.append(
                    ReccePPTSpaceFieldElementData(
                        name=data.get("name"),
                        height=data.get("height"),
                        width=data.get("width"),
                        uom=data.get("uom"),
                        sno=data.get("sno"),
                    )
                )
            slidewise_elements.append(elements)

            for i in range(len(slidewise_elements)):
                slides.append(
                    ReccePPTSpacesFieldSlideData(
                        type=SlideType.SPACE,
                        job_id=str(recce.project.job_id),
                        project_name=recce.project.name,
                        recce_name=str(recce.name),
                        section_name=section.name,
                        city=recce.project.store.city,
                        logo=self.logo,
                        generated_at=str(recce.created_at),
                        is_first=is_first,
                        field_name=field.name,
                        detail=file.detail if file.detail else "",
                        file=self.get_file_field_data(file=file) if i == 0 else None,
                        elements=slidewise_elements[i],
                    )
                )
                is_first = False

        return slides

    def get_recce_rich_text_field_slide_data(
        self, section: RecceSection, fields: list[RecceField], recce: Recce, is_first: bool
    ):
        slides: list[ReccePPTRichTextFieldSlideData] = []
        rows_available = 15
        if is_first:
            rows_available -= 1
        data_list: list[RichTextField] = []

        for field in fields:
            field_images: list[FileFieldData] = []
            field_attachments: list[FileFieldData] = []
            field_text: str = ""
            field_first_slide = True
            if field.data is None:
                continue

            for block in field.data:
                if block.get("type") == const.TEXT:
                    field_text += block.get("text")
                elif block.get("type") == const.IMAGE:
                    field_images.append(
                        FileFieldData(
                            hyperlink=PublicMediaFileStorage.url(block.get("meta").get("url")),
                            thumbnail=PublicMediaFileStorage.url(block.get("meta").get("url")),
                            type=FileType.IMAGE,
                            name=block.get("meta").get("file_name"),
                        )
                    )
                elif block.get("type") == const.ATTACHMENT:
                    field_attachments.append(
                        FileFieldData(
                            hyperlink=PublicMediaFileStorage.url(block.get("meta").get("url")),
                            type=FileType.ATTACHMENT,
                            name=block.get("meta").get("file_name"),
                            icon=get_icon(block.get("meta").get("url")),
                        )
                    )
            text_row_required = len(field_text) // 170 + 1
            image_row_required = (
                len(field_images) // 3 + 1 if len(field_images) % 3 != 0 else len(field_images) // 3
            ) * 7
            attachment_row_required = (
                len(field_attachments) // 3 + 1 if len(field_images) % 3 != 0 else len(field_attachments) // 3
            ) * 2
            rows_required = 1 + text_row_required + image_row_required + attachment_row_required

            while field_text != "" or len(field_images) != 0 or len(field_attachments) != 0:
                if rows_available <= 0:
                    slides.append(
                        ReccePPTRichTextFieldSlideData(
                            type=SlideType.RICH_TEXTS,
                            job_id=str(recce.project.job_id),
                            project_name=recce.project.name,
                            recce_name=str(recce.name),
                            section_name=section.name,
                            city=recce.project.store.city,
                            logo=self.logo,
                            generated_at=str(recce.created_at),
                            data=data_list,
                            is_first=is_first,
                        )
                    )
                    if len(slides) == 1:
                        is_first = False
                    data_list = []
                    rows_available = 14
                # for field name
                if field_first_slide:
                    rows_available -= 1
                    rows_required -= 1
                current_slide_text = ""
                current_slide_images = []
                current_slide_attachments = []
                if field_text != "":
                    current_slide_text = field_text[: rows_available * 170]
                    text_row_used = len(current_slide_text) // 170 + 1
                    rows_available = rows_available - text_row_used
                    field_text = field_text[len(current_slide_text) :]
                    rows_required = rows_required - text_row_used
                if field_text == "" and len(field_images) != 0:
                    image_row_available = rows_available // 7
                    current_slide_images = field_images[: image_row_available * 3]
                    image_row_used = (
                        len(current_slide_images) // 3 + 1
                        if len(current_slide_images) % 3 != 0
                        else len(current_slide_images) // 3
                    )
                    rows_available = rows_available - image_row_used * 7
                    field_images = field_images[len(current_slide_images) :]
                    rows_required = rows_required - image_row_used * 7
                if field_text == "" and len(field_images) == 0 and len(field_attachments) != 0:
                    attachment_row_available = rows_available // 2
                    current_slide_attachments = field_attachments[: attachment_row_available * 3]
                    attachment_row_used = (
                        len(current_slide_attachments) // 3 + 1
                        if len(current_slide_attachments) % 3 != 0
                        else len(current_slide_attachments) // 3
                    )
                    rows_available = rows_available - attachment_row_used * 2
                    field_attachments = field_attachments[len(current_slide_attachments) :]
                    rows_required = rows_required - attachment_row_used * 2
                if current_slide_text == "" and len(current_slide_images) == 0 and len(current_slide_attachments) == 0:
                    rows_available = 0
                else:
                    data_list.append(
                        RichTextField(
                            field_name=field.name if field_first_slide else "",
                            value=current_slide_text,
                            images=current_slide_images,
                            attachments=current_slide_attachments,
                        )
                    )
                    field_first_slide = False
        slides.append(
            ReccePPTRichTextFieldSlideData(
                type=SlideType.RICH_TEXTS,
                job_id=str(recce.project.job_id),
                project_name=recce.project.name,
                recce_name=str(recce.name),
                section_name=section.name,
                city=recce.project.store.city,
                logo=self.logo,
                generated_at=str(recce.created_at),
                data=data_list,
                is_first=is_first,
            )
        )

        return slides

    def get_recce_multi_dropdown_field_slide_data(
        self, section: RecceSection, field: RecceField, recce: Recce, is_first: bool
    ):
        slides: list[ReccePPTTextFieldsSlideData] = []
        rows_available = 28
        fields_data: list[TextFieldData] = []
        values = field.data.get("data", "") if field.data else ""
        if values is None or len(values) == 0:
            """
            If the value of the multi-dropdown field is not a list, it means it is a single string -> (-).
            """
            values = ["-"]
        for idx, value in enumerate(values):
            if idx == 0:
                rows_required = (len(field.name) + 62) // 63 + (len(value) + 62) // 63
            else:
                rows_required = (len(value) + 62) // 63
            if rows_available < rows_required:
                slides.append(
                    ReccePPTTextFieldsSlideData(
                        type=SlideType.MULTI_DROPDOWN,
                        job_id=str(recce.project.job_id),
                        project_name=str(recce.project.name),
                        recce_name=str(recce.name),
                        section_name=section.name,
                        city=recce.project.store.city if recce.project.store.city else "",
                        logo=self.logo,
                        generated_at=str(recce.created_at),
                        fields=fields_data,
                        is_first=is_first,
                    )
                )
                is_first = False
                rows_available = 29
                fields_data = []

            fields_data.append(TextFieldData(name=field.name, value=value))
            rows_available -= rows_required
        slides.append(
            ReccePPTTextFieldsSlideData(
                type=SlideType.MULTI_DROPDOWN,
                job_id=str(recce.project.job_id),
                project_name=str(recce.project.name),
                recce_name=str(recce.name),
                section_name=section.name,
                city=recce.project.store.city if recce.project.store.city else "",
                logo=self.logo,
                generated_at=str(recce.created_at),
                fields=fields_data,
                is_first=is_first,
            )
        )
        return slides

    def get_text_fields_slide_data(self, section: RecceSection, fields: list[RecceField], recce: Recce, is_first: bool):
        slides: list[ReccePPTTextFieldsSlideData] = []
        rows_available = 28
        fields_data: list[TextFieldData] = []
        is_first = True
        for field in fields:
            if not field.data:
                value = ""
            else:
                if isinstance(field.data.get("data"), bool):
                    if field.data.get("data") is True:
                        value = "YES"
                    elif field.data.get("data") is False:
                        value = "NO"
                    else:
                        value = ""
                elif field.data.get("data") is None:
                    value = ""
                else:
                    value = str(field.data.get("data", ""))
            if value == "":
                value = "-"
            value = value.replace("\n", " ")
            rows_required = (len(field.name) + 62) // 63 + (len(value) + 62) // 63
            if rows_available < rows_required:
                slides.append(
                    ReccePPTTextFieldsSlideData(
                        type=SlideType.SIGNAGE_AND_FACADE,
                        job_id=str(recce.project.job_id),
                        project_name=str(recce.project.name),
                        recce_name=str(recce.name),
                        section_name=section.name,
                        city=recce.project.store.city,
                        logo=self.logo,
                        generated_at=str(recce.created_at),
                        fields=fields_data,
                        is_first=is_first,
                    )
                )
                is_first = False
                rows_available = 29
                fields_data = []

            fields_data.append(TextFieldData(name=field.name, value=value))
            rows_available -= rows_required
        slides.append(
            ReccePPTTextFieldsSlideData(
                type=SlideType.SIGNAGE_AND_FACADE,
                job_id=str(recce.project.job_id),
                project_name=str(recce.project.name),
                recce_name=str(recce.name),
                section_name=section.name,
                city=recce.project.store.city,
                logo=self.logo,
                generated_at=str(recce.created_at),
                fields=fields_data,
                is_first=is_first,
            )
        )
        return slides

    def get_section_slide_data(self, section: RecceSection, recce: Recce) -> list[ReccePPTSlideData]:
        slides: list[ReccePPTSlideData] = []
        text_fields = []
        consecutive_rich_text = []
        fields = section.fields.all()
        is_first = True
        idx = 0
        while idx < len(fields):
            field = fields[idx]
            if field.type in [RecceField.FieldChoices.FILES, RecceField.FieldChoices.ATTACHMENT]:
                slides.extend(
                    self.get_recce_file_field_slide_data(section=section, field=field, recce=recce, is_first=is_first)
                )
                is_first = False
                idx += 1
            elif field.type == RecceField.FieldChoices.SPACES:
                slides.extend(
                    self.get_recce_spaces_field_slide_data(section=section, field=field, recce=recce, is_first=is_first)
                )
                is_first = False
                idx += 1
            elif field.type == RecceField.FieldChoices.RICH_TEXT:
                while idx < len(fields) and fields[idx].type == RecceField.FieldChoices.RICH_TEXT:
                    consecutive_rich_text.append(fields[idx])
                    idx = idx + 1
                slides_data = self.get_recce_rich_text_field_slide_data(
                    section=section, fields=consecutive_rich_text, recce=recce, is_first=is_first
                )
                for slide in slides_data:
                    if len(slide.data) > 0:
                        slides.append(slide)
                consecutive_rich_text = []
                is_first = False
            elif field.type == RecceField.FieldChoices.MULTI_DROPDOWN:
                slides.extend(
                    self.get_recce_multi_dropdown_field_slide_data(
                        section=section, field=field, recce=recce, is_first=is_first
                    )
                )
                is_first = False
                idx += 1
            else:
                while idx < len(fields) and fields[idx].type not in [
                    RecceField.FieldChoices.RICH_TEXT,
                    RecceField.FieldChoices.SPACES,
                    RecceField.FieldChoices.FILES,
                    RecceField.FieldChoices.ATTACHMENT,
                ]:
                    text_fields.append(fields[idx])
                    idx += 1
                slides.extend(
                    self.get_text_fields_slide_data(section=section, fields=text_fields, recce=recce, is_first=is_first)
                )
                text_fields = []
                is_first = False
        return slides

    def get_recce_slides_data(self, recce: Recce):
        slides: list[ReccePPTSlideData] = []
        for section in recce.sections.all():
            slides.extend(self.get_section_slide_data(section=section, recce=recce))
        return slides

    def get_organization(self):
        return organization_get(org_id=self.org_id)


class RecceSinglePPTContextGenerator(BaseReccePPTContextGenerator):
    def get_context(self) -> ReccePPTData:
        recces: list[Recce] = self.get_queryset()
        slides: list[ReccePPTSlideData] = []
        if len(recces) == 0:
            raise Exception("No Recce found")
        slides.append(self.project_detail_slide_data(project=recces[0].project))
        for recce in recces:
            slides.append(self.get_title_slide_data(project=recce.project, recce=recce))
            slides.extend(self.get_recce_slides_data(recce))
        context = ReccePPTData(
            name=f"recce_report_{recce.project.name}_{recce.project.job_id}.pptx",
            slides=slides,
        )
        return context


class RecceManyPPTContextGenerator(BaseReccePPTContextGenerator):
    def get_context(self) -> list[ReccePPTData]:
        recces: list[Recce] = self.get_queryset()
        context: list[ReccePPTData] = []
        for recce in recces:
            slides: list[ReccePPTSlideData] = []
            slides.append(
                self.project_detail_slide_data(
                    project=recce.project,
                )
            )
            slides.append(self.get_title_slide_data(project=recce.project, recce=recce))
            slides.extend(self.get_recce_slides_data(recce))
            context.append(
                ReccePPTData(
                    name=f"recce_report_{recce.project.name}_{recce.project.job_id}.pptx",
                    slides=slides,
                )
            )
        return context
