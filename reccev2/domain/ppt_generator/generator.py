import zipfile
from datetime import datetime
from io import BytesIO

from common.ppt_generator.assets import CONTACT_ICON, PROFILE_ICON, RDASH_LOGO
from common.ppt_generator.components import (
    ImageBox,
    Line,
    Rectangle,
    RoundedRectangle,
    Textbox,
)
from common.ppt_generator.generator import BasePPTGenerator
from reccev2.domain.ppt_generator import (
    FileType,
    RecceManyPPTContextGenerator,
    ReccePPTData,
    ReccePPTFilesFieldSlideData,
    ReccePPTProjectDetailsSlideData,
    ReccePPTRichTextFieldSlideData,
    ReccePPTSpacesFieldSlideData,
    ReccePPTTextFieldsSlideData,
    ReccePPTTitleSlideData,
    RecceSinglePPTContextGenerator,
    SlideType,
)


class ReccePPTGenerator(BasePPTGenerator):
    _RDASH_LOGO_PATH = RDASH_LOGO

    def generate(self):
        self.add_ppt()
        context_data = self.get_context_data()

        for slide_data in context_data.slides:
            slide = self.add_slide()

            if slide_data.type != SlideType.PROJECT_DETAILS:
                self.add_header(slide=slide, slide_data=slide_data)
                # self.add_footer(slide=slide, slide_data=slide_data)
            if slide_data.type == SlideType.PROJECT_DETAILS:
                self.add_project_details(slide=slide, slide_data=slide_data)
            elif slide_data.type == SlideType.TITLE:
                self.add_title(slide=slide, slide_data=slide_data)
            elif slide_data.type == SlideType.FILES:
                self.add_recce_file_field(slide=slide, slide_data=slide_data)
            elif slide_data.type == SlideType.SPACE:
                self.add_recce_space_field(slide=slide, slide_data=slide_data)
            elif slide_data.type == SlideType.RICH_TEXTS:
                self.add_recce_rich_text_field(slide=slide, slide_data=slide_data)
            elif slide_data.type == SlideType.MULTI_DROPDOWN:
                self.add_recce_multi_dropdown_field(slide=slide, slide_data=slide_data)
            else:
                self.add_recce_text_fields(slide=slide, slide_data=slide_data)
        return self._ppt

    def add_header(self, slide, slide_data):
        Line(slide=slide, start_x=0, start_y=0.93, end_x=13.5, end_y=0.93).set_color(color=(148, 148, 148))
        combined_text = ""
        combined_text += f"JOB ID : {slide_data.job_id} | Project Name : {slide_data.project_name}, {slide_data.city}\n"
        Textbox(slide=slide, left=0.4, top=-0.1, width=23.14, height=0.44).set_text(text=combined_text, font_size=15)
        if slide_data.logo.startswith("https"):
            ImageBox(
                slide=slide,
                path=slide_data.logo,
                left=11.8,
                top=0.15,
                width=1.75,
                height=0.6,
                preserve_aspect_ratio=True,
                hyperlink_path=slide_data.logo,
            )
        else:
            Textbox(
                slide=slide,
                left=12,
                top=0,
                width=1.75,
                height=0.75,
            ).set_text(text=slide_data.logo, font_size=14)

    def add_footer(self, slide, slide_data):
        footer_box = Rectangle(slide=slide, left=0, top=6.76, width=self._width, height=0.75)
        footer_box.fill()
        footer_box.set_foreground_color((255, 255, 255))
        footer_box.set_border_color((255, 255, 255))
        Textbox(slide=slide, left=9.2, top=6.7, width=1.68, height=0.4).set_text(
            text="GENERATED BY", font_size=15, color=(134, 134, 134), is_bold=True
        )
        ImageBox(
            slide=slide,
            path=self._RDASH_LOGO_PATH,
            left=10.6,
            top=7.02,
            width=0.97,
            height=0.23,
            preserve_aspect_ratio=True,
        )
        parsed_datetime = datetime.strptime(slide_data.generated_at, "%Y-%m-%d %H:%M:%S.%f%z")
        formatted_date = parsed_datetime.strftime("%d/%m/%Y")
        Textbox(slide=slide, left=11.45, top=6.7, width=1.89, height=0.4).set_text(
            text=f"ON : {formatted_date}", font_size=15, color=(134, 134, 134), is_bold=True
        )

    def add_project_details(self, slide, slide_data: ReccePPTProjectDetailsSlideData):
        left_box = Rectangle(slide=slide, left=0, top=0, width=6.01, height=7.5)
        left_box.fill()
        left_box.set_foreground_color((255, 255, 255))
        left_box.set_border_width(0)

        ImageBox(
            slide=slide,
            path=slide_data.cover_image,
            left=-0.01,
            top=-0.01,
            width=6.8,
            height=7.51,
        )
        Textbox(slide=slide, left=3.6, top=5.9, width=4.32, height=0.53).set_text(
            text="Generated By", font_size=15, color=(0, 0, 0), is_bold=True
        )
        ImageBox(
            slide=slide,
            path=self._RDASH_LOGO_PATH,
            left=4.80,
            top=6.24,
            width=0.91,
            height=0.21,
            preserve_aspect_ratio=True,
        )
        parsed_datetime = datetime.strptime(slide_data.generated_at, "%Y-%m-%d %H:%M:%S.%f%z")
        formatted_date = parsed_datetime.strftime("%d %B %Y")
        Textbox(slide=slide, left=3.6, top=6.16, width=4.32, height=0.53).set_text(
            f"{formatted_date}", font_size=15, color=(134, 134, 134), is_bold=True
        )
        ImageBox(slide=slide, path=slide_data.logo, left=7.6, top=0.15, width=2, height=1.7, preserve_aspect_ratio=True)
        Textbox(slide=slide, left=7.5, top=1.7, width=4.32, height=0.53).set_text(
            text="Project Details", font_size=18.5, is_bold=True, color=(0, 0, 0)
        )
        Line(slide=slide, start_x=7.5, start_y=2.5, end_x=12.8, end_y=2.5).set_color((170, 170, 170))
        Textbox(
            slide=slide,
            left=7.5,
            top=2.45,
            width=1.55,
            height=0.19,
        ).set_text(text="Project Name :", font_size=15, color=(105, 105, 105))
        Textbox(slide=slide, left=7.8, top=2.45, width=5, height=0.19).set_text(
            text=slide_data.project_name, font_size=15, color=(0, 0, 0)
        ).text_align_right()
        Textbox(
            slide=slide,
            left=7.5,
            top=2.84,
            width=1.55,
            height=0.19,
        ).set_text(text="Job ID :", font_size=15, color=(105, 105, 105))
        Textbox(slide=slide, left=7.8, top=2.84, width=5, height=0.19).set_text(
            text=slide_data.job_id, font_size=15, color=(0, 0, 0)
        ).text_align_right()
        Textbox(
            slide=slide,
            left=7.5,
            top=3.24,
            width=1.55,
            height=0.19,
        ).set_text(text="Client :", font_size=15, color=(105, 105, 105))
        Textbox(slide=slide, left=7.8, top=3.24, width=5, height=0.19).set_text(
            text=slide_data.client_name, font_size=15, color=(0, 0, 0)
        ).text_align_right()
        Textbox(
            slide=slide,
            left=7.5,
            top=3.59,
            width=1.55,
            height=0.19,
        ).set_text(text="City :", font_size=15, color=(105, 105, 105))
        Textbox(slide=slide, left=6.58, top=3.596, width=6.2, height=0.19).set_text(
            text=slide_data.city, font_size=15, color=(0, 0, 0)
        ).text_align_right()
        Textbox(
            slide=slide,
            left=7.5,
            top=4,
            width=1.55,
            height=0.19,
        ).set_text(text="Address :", font_size=15, color=(105, 105, 105))
        Textbox(slide=slide, left=7.5, top=4.3, width=6, height=0.19).set_text(
            text=slide_data.address, font_size=15, color=(0, 0, 0)
        ).text_wrap()

    def add_title(self, slide, slide_data: ReccePPTTitleSlideData):
        Textbox(slide=slide, left=5.5, top=2.5, width=2, height=1).set_text(
            text=slide_data.recce_name, font_size=25, is_bold=True
        ).text_align_center()
        if slide_data.submitted_at == "-":
            return
        Textbox(slide=slide, left=5.2, top=3.3, width=2, height=1).set_text(
            text="Submitted on :", font_size=15, is_bold=True
        )
        parsed_datetime = datetime.strptime(slide_data.submitted_at, "%Y-%m-%d %H:%M:%S.%f%z")
        formatted_date = parsed_datetime.strftime("%d %B %Y")
        Textbox(slide=slide, left=6.5, top=3.3, width=4, height=1).set_text(
            text=formatted_date, font_size=15, is_bold=True
        )
        Textbox(slide=slide, left=6.2, top=3.6, width=2, height=1).set_text(text="by : ", font_size=15, is_bold=True)
        ImageBox(slide=slide, path=PROFILE_ICON, left=5.6, top=4.29, width=0.2, height=0.2)
        Textbox(slide=slide, left=5.8, top=3.9, width=4, height=1).set_text(text=slide_data.submitted_by, font_size=15)
        ImageBox(slide=slide, path=CONTACT_ICON, left=5.6, top=4.59, width=0.2, height=0.2)
        Textbox(slide=slide, left=5.8, top=4.2, width=4.2, height=1).set_text(text=slide_data.contact, font_size=15)

    def add_recce_file_field(self, slide, slide_data: ReccePPTFilesFieldSlideData):
        combined_text = ""
        combined_text += f"{slide_data.recce_name} | {slide_data.section_name}\n"
        Textbox(slide=slide, left=0.4, top=0.18, width=23.14, height=0.44).set_text(
            text=combined_text, font_size=13.5, color=(0, 0, 0)
        )
        width = 6
        height = 4.2
        top = 2.3
        if slide_data.is_first:
            Textbox(slide=slide, left=0.4, top=0.70, width=6.75, height=0.22).set_text(
                text=slide_data.section_name, font_size=22, is_bold=True
            )
        Textbox(
            slide=slide,
            left=0.4,
            top=1.20,
            width=6.75,
            height=0.22,
        ).set_text(text=slide_data.field_name, font_size=16, is_bold=True)
        rect1 = RoundedRectangle(slide=slide, left=0.47, top=top, width=width, height=height, round_cornor=0.01)
        rect1.fill()
        rect1.set_foreground_color(color=(255, 255, 255))
        rect1.set_border_color(color=(107, 101, 102))
        rect1.set_border_width(0.2)
        if slide_data.file1.thumbnail:
            ImageBox(
                slide=slide,
                path=slide_data.file1.thumbnail,
                left=0.47,
                top=top + 0.01,
                width=width,
                height=height,
                preserve_aspect_ratio=True,
                hyperlink_path=slide_data.file1.hyperlink,
            )
        if slide_data.file1.type != FileType.IMAGE:
            ImageBox(
                slide=slide,
                path=slide_data.file1.icon,
                left=2.9,
                top=3.8,
                width=1,
                height=1,
                preserve_aspect_ratio=True,
                hyperlink_path=slide_data.file1.hyperlink,
            )

        if slide_data.file2:
            rect2 = RoundedRectangle(slide=slide, left=6.93, top=top, width=width, height=height, round_cornor=0.01)
            rect2.fill()
            rect2.set_foreground_color(color=(255, 255, 255))
            rect2.set_border_color(color=(107, 101, 102))
            rect2.set_border_width(0.2)
            if slide_data.file2.thumbnail:
                ImageBox(
                    slide=slide,
                    path=slide_data.file2.thumbnail,
                    left=6.93,
                    top=top + 0.01,
                    width=width,
                    height=height,
                    preserve_aspect_ratio=True,
                    hyperlink_path=slide_data.file2.hyperlink,
                )
            if slide_data.file2.type != FileType.IMAGE:
                ImageBox(
                    slide=slide,
                    path=slide_data.file2.icon,
                    left=9.4,
                    top=3.8,
                    width=1,
                    height=1,
                    preserve_aspect_ratio=True,
                    hyperlink_path=slide_data.file2.hyperlink,
                )

    def add_recce_space_field(self, slide, slide_data: ReccePPTSpacesFieldSlideData):
        combined_text = ""
        combined_text += f"{slide_data.recce_name} | {slide_data.section_name}\n"

        Textbox(slide=slide, left=0.4, top=0.18, width=23.14, height=0.44).set_text(
            text=combined_text, font_size=13.5, color=(0, 0, 0)
        )
        if slide_data.is_first:
            Textbox(slide=slide, left=0.4, top=0.70, width=6.75, height=0.22).set_text(
                text=slide_data.section_name, font_size=22, is_bold=True
            )
        Textbox(
            slide=slide,
            left=0.4,
            top=1.15,
            width=6.75,
            height=0.22,
        ).set_text(text=slide_data.field_name, font_size=16, is_bold=True)
        left = 5.6
        top = 1.2
        width = 7.78
        height = 5.4
        if slide_data.file:
            rect = RoundedRectangle(slide=slide, left=left, top=top, width=width, height=height, round_cornor=0.01)
            rect.fill()
            rect.set_foreground_color(color=(255, 255, 255))
            rect.set_border_color(color=(107, 101, 102))
            rect.set_border_width(0.2)
        if slide_data.file and slide_data.file.type in [FileType.IMAGE, FileType.VIDEO]:
            ImageBox(
                slide=slide,
                path=slide_data.file.thumbnail,
                left=left,
                top=top,
                width=width,
                height=height,
                preserve_aspect_ratio=True,
                hyperlink_path=slide_data.file.hyperlink,
            )
        if slide_data.file and slide_data.file.type != FileType.IMAGE:
            ImageBox(
                slide=slide,
                path=slide_data.file.icon,
                left=8.9,
                top=3.7,
                width=1.4,
                height=1.4,
                preserve_aspect_ratio=True,
                hyperlink_path=slide_data.file.hyperlink,
            )

        Textbox(slide=slide, left=0.4, top=1.6, width=3.24, height=0.23).set_text(
            text=slide_data.detail,
            font_size=14,
        )
        if not slide_data.elements:
            return
        Textbox(
            slide=slide,
            left=0.4,
            top=1.95,
            width=0.76,
            height=0.27,
        ).set_text(text="S.No.", font_size=14, is_bold=True, color=(61, 73, 91))
        Textbox(
            slide=slide,
            left=1.1,
            top=1.95,
            width=3.1,
            height=0.27,
        ).set_text(text="Name of Element", font_size=14, is_bold=True, color=(61, 73, 91))
        Textbox(
            slide=slide,
            left=4.2,
            top=1.95,
            width=1.31,
            height=0.27,
        ).set_text(text="Dimension", font_size=14, is_bold=True, color=(61, 73, 91)).text_align_right()
        top = 2.2
        diff = 0.05
        slide_data.elements = slide_data.elements
        for i, element in enumerate(slide_data.elements):
            name = element.name
            rows_req = len(element.name) // 36 if len(element.name) % 36 == 0 else (len(element.name) // 36) + 1
            height = 0.27 * rows_req
            Textbox(slide=slide, left=0.4, top=top, width=0.76, height=height).set_text(text=str(i + 1), font_size=14)
            name = Textbox(
                slide=slide,
                left=1.1,
                top=top,
                width=3.1,
                height=height,
            )
            name.set_text(text=element.name, font_size=14)
            name.text_wrap()
            dimension = ""
            if element.height and element.width:
                dimension = f"{element.width} X {element.height} {element.uom}"
            elif element.width:
                dimension = f"{element.width} {element.uom}"
            Textbox(slide=slide, left=4.2, top=top, width=1.31, height=height).set_text(
                text=dimension, font_size=14
            ).text_align_right()
            top += height + diff

    def add_recce_rich_text_field(self, slide, slide_data: ReccePPTRichTextFieldSlideData):
        combined_text = ""
        combined_text += f"{slide_data.recce_name} | {slide_data.section_name}\n"
        Textbox(slide=slide, left=0.4, top=0.18, width=23.14, height=0.44).set_text(
            text=combined_text, font_size=13.5, color=(0, 0, 0)
        )
        left = 0.4
        top = 1
        width = 3.80
        height = 2.5
        if slide_data.is_first:
            Textbox(slide=slide, left=0.4, top=0.70, width=6.75, height=0.22).set_text(
                text=slide_data.section_name, font_size=22, is_bold=True
            )
            top += 0.3

        for i in range(0, len(slide_data.data)):
            if slide_data.data[i].field_name:
                field_name_box = Textbox(slide=slide, left=left, top=top, width=12.5, height=height)
                field_name_box.set_text(text=slide_data.data[i].field_name, font_size=16, is_bold=True)
                field_name_box.text_wrap()
                top += 0.3

            if slide_data.data[i].value:
                value_box = Textbox(slide=slide, left=left, top=top, width=12.5, height=height)
                value_box.set_text(text=slide_data.data[i].value, font_size=15)
                value_box.text_wrap()
                top += 0.3 * (len(slide_data.data[i].value) // 170) + 0.4
            top += 0.3
            cnt = 0
            if slide_data.data[i].images:
                for image in slide_data.data[i].images:
                    if cnt % 3 == 0 and cnt != 0:
                        top += 3
                        left = 0.4
                    cnt += 1
                    rect = RoundedRectangle(
                        slide=slide, left=left, top=top, width=width, height=height, round_cornor=0.01
                    )
                    rect.fill()
                    rect.set_foreground_color(color=(255, 255, 255))
                    rect.set_border_color(color=(107, 101, 102))
                    rect.set_border_width(0.2)
                    ImageBox(
                        slide=slide,
                        path=image.hyperlink,
                        left=left,
                        top=top - 0.01,
                        width=width,
                        height=height,
                        preserve_aspect_ratio=True,
                        hyperlink_path=image.hyperlink,
                    )
                    left += 4.3
            left = 0.4
            if cnt > 0:
                top += 3
            if slide_data.data[i].attachments:
                cnt = 0
                height = 1

                for attachment in slide_data.data[i].attachments:
                    if cnt % 3 == 0 and cnt != 0:
                        top += 0.75
                        left = 0.4
                    cnt += 1
                    rect = RoundedRectangle(
                        slide=slide, left=left, top=top + 0.1, width=width, height=height - 0.5, round_cornor=0.1
                    )
                    rect.fill()
                    rect.set_foreground_color(color=(225, 225, 225))
                    rect.set_border_color(color=(148, 148, 148))

                    ImageBox(
                        slide=slide,
                        path=attachment.icon,
                        left=left - 1.3,
                        top=top + 0.2,
                        width=width - 0.5,
                        height=height - 0.7,
                        preserve_aspect_ratio=True,
                        hyperlink_path=attachment.hyperlink,
                    )
                    max_text_width = 20
                    if len(attachment.name) > max_text_width:
                        truncated_text = attachment.name[0:12] + "..." + attachment.name[len(attachment.name) - 5 :]
                    else:
                        truncated_text = attachment.name
                    Textbox(
                        slide=slide,
                        left=left + 0.5,
                        top=top - 0.15,
                        width=width,
                        height=height,
                    ).set_text(text=truncated_text, font_size=15, is_bold=True, hyperlink=attachment.hyperlink)
                    left += 4.3
            if cnt > 0:
                top += 0.2
                left = 0.4

    def add_recce_multi_dropdown_field(self, slide, slide_data: ReccePPTTextFieldsSlideData):
        combined_text = ""
        combined_text += f"{slide_data.recce_name} | {slide_data.section_name}\n"
        Textbox(slide=slide, left=0.4, top=0.18, width=23.14, height=0.44).set_text(
            text=combined_text, font_size=13.5, color=(0, 0, 0)
        )
        left = 0.4
        top = 1
        width = 3.80
        row_available = 14
        if slide_data.is_first:
            Textbox(slide=slide, left=0.4, top=0.70, width=6.75, height=0.22).set_text(
                text=slide_data.section_name, font_size=22, is_bold=True
            )
            top += 0.3
        multi_dropdown_field = slide_data.fields[0]
        name_row_required = (
            len(multi_dropdown_field.name) // 63
            if len(multi_dropdown_field.name) % 63 == 0
            else (len(multi_dropdown_field.name) // 63) + 1
        )
        remain_txt = ""
        if name_row_required > row_available:
            txt = multi_dropdown_field.name[: row_available * 63]
            field_name_height = row_available * 0.2
            remain_txt = multi_dropdown_field.name[row_available * 63 :]
            row_available = 0

        else:
            txt = multi_dropdown_field.name
            field_name_height = name_row_required * 0.2
            row_available -= name_row_required
        Textbox(slide=slide, left=left, top=top, width=width, height=field_name_height).set_text(
            text=txt, font_size=16, is_bold=True
        ).text_wrap()
        top += field_name_height + 0.1
        if row_available == 0:
            row_available = 15
            top = 1.1
            left = 6.8

        if remain_txt:
            remain_row_required = len(remain_txt) // 63 if len(remain_txt) % 63 == 0 else (len(remain_txt) // 63) + 1
            field_name_height = remain_row_required * 0.2
            Textbox(slide=slide, left=left, top=top, width=width, height=field_name_height).set_text(
                text=remain_txt, font_size=16, is_bold=True
            ).text_wrap()
            top += field_name_height + 0.1
            row_available -= remain_row_required

        for field in slide_data.fields:
            value_row_required = len(field.value) // 63 if len(field.value) % 63 == 0 else (len(field.value) // 63) + 1
            remain_txt = ""
            if value_row_required > row_available:
                txt = field.value[: row_available * 63]
                field_value_height = row_available * 0.2
                remain_txt = field.value[row_available * 63 :]
                row_available = 0
            else:
                txt = field.value
                field_value_height = value_row_required * 0.2
                row_available -= value_row_required
            Textbox(slide=slide, left=left, top=top, width=width, height=field_value_height).set_text(
                text=txt, font_size=14
            ).text_wrap()
            top += field_value_height + 0.3
            if row_available == 0:
                row_available = 15
                top = 1.1
                left = 6.8
            if remain_txt:
                remain_row_required = (
                    len(remain_txt) // 63 if len(remain_txt) % 63 == 0 else (len(remain_txt) // 63) + 1
                )
                field_value_height = remain_row_required * 0.2
                Textbox(slide=slide, left=left, top=top, width=width, height=field_value_height).set_text(
                    text=remain_txt, font_size=14
                ).text_wrap()
                top += field_value_height + 0.3
                row_available -= remain_row_required

    def add_recce_text_fields(self, slide, slide_data: ReccePPTTextFieldsSlideData):
        width = 6.5
        height = 0.8
        top = 1.1
        left = 0.4
        row_available = 14
        combined_text = ""
        combined_text += f"{slide_data.recce_name} | {slide_data.section_name}\n"
        Textbox(slide=slide, left=0.4, top=0.18, width=23.14, height=0.44).set_text(
            text=combined_text, font_size=13.5, color=(0, 0, 0)
        )
        if slide_data.is_first:
            Textbox(
                slide=slide,
                left=0.4,
                top=0.7,
                width=width,
                height=height,
            ).set_text(
                text=slide_data.section_name,
                font_size=22,
                is_bold=True,
            )
        for field in slide_data.fields:
            name_row_required = len(field.name) // 63 if len(field.name) % 63 == 0 else (len(field.name) // 63) + 1
            remain_txt = ""
            if name_row_required > row_available:
                txt = field.name[: row_available * 63]
                field_name_height = row_available * 0.2
                remain_txt = field.name[row_available * 63 :]
                row_available = 0

            else:
                txt = field.name
                field_name_height = name_row_required * 0.2
                row_available -= name_row_required
            Textbox(slide=slide, left=left, top=top, width=width, height=field_name_height).set_text(
                text=txt, font_size=16, is_bold=True
            ).text_wrap()
            top += field_name_height + 0.1
            if row_available == 0:
                row_available = 15
                top = 1.1
                left = 6.8

            if remain_txt:
                remain_row_required = (
                    len(remain_txt) // 63 if len(remain_txt) % 63 == 0 else (len(remain_txt) // 63) + 1
                )
                field_name_height = remain_row_required * 0.2
                Textbox(slide=slide, left=left, top=top, width=width, height=field_name_height).set_text(
                    text=remain_txt, font_size=16, is_bold=True
                ).text_wrap()
                top += field_name_height + 0.1
                row_available -= remain_row_required
            value_row_required = len(field.value) // 63 if len(field.value) % 63 == 0 else (len(field.value) // 63) + 1
            remain_txt = ""
            if value_row_required > row_available:
                txt = field.value[: row_available * 63]
                field_value_height = row_available * 0.2
                remain_txt = field.value[row_available * 63 :]
                row_available = 0
            else:
                txt = field.value
                field_value_height = value_row_required * 0.2
                row_available -= value_row_required
            Textbox(slide=slide, left=left, top=top, width=width, height=field_value_height).set_text(
                text=txt, font_size=14
            ).text_wrap()
            top += field_value_height + 0.3
            if row_available == 0:
                row_available = 15
                top = 1.1
                left = 6.8
            if remain_txt:
                remain_row_required = (
                    len(remain_txt) // 63 if len(remain_txt) % 63 == 0 else (len(remain_txt) // 63) + 1
                )
                field_value_height = remain_row_required * 0.2
                Textbox(slide=slide, left=left, top=top, width=width, height=field_value_height).set_text(
                    text=remain_txt, font_size=14
                ).text_wrap()
                top += field_value_height + 0.3
                row_available -= remain_row_required


class RecceSinglePPTGenerator:
    _context: RecceSinglePPTContextGenerator = None

    def __init__(self, project_id: int, recce_ids: list[int], org_id: int):
        self._context = RecceSinglePPTContextGenerator(
            project_id=project_id, recce_ids=recce_ids, org_id=org_id
        ).get_context()

    @staticmethod
    def save_ppt(ppt):
        pptx_buffer = BytesIO()
        ppt.save(pptx_buffer)
        pptx_buffer.seek(0)
        return pptx_buffer.getvalue()

    def generate(self):
        ppt = ReccePPTGenerator(context=self._context).generate()
        return self.save_ppt(ppt), self._context.name


class RecceManyPPTGenerator:
    _context: list[ReccePPTData] = None

    def __init__(self, project_id: int, recce_ids: list[int], org_id: int):
        self._context = RecceManyPPTContextGenerator(
            project_id=project_id, recce_ids=recce_ids, org_id=org_id
        ).get_context()

    def create_zip(self, ppts, ppt_names):
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, "w") as zip_file:
            for ppt, name in zip(ppts, ppt_names):
                ppt_buffer = BytesIO()
                ppt.save(ppt_buffer)
                ppt_buffer.seek(0)
                zip_file.writestr(name, ppt_buffer.getvalue())
        zip_buffer.seek(0)
        return zip_buffer.read()

    def generate(self):
        ppts = []
        ppt_names = []
        for data in self._context:
            ppt = ReccePPTGenerator(context=data).generate()
            ppts.append(ppt)
            ppt_names.append(data.name)
        return self.create_zip(ppts, ppt_names), "recce_reports.zip"
