from typing import List

from authorization.domain.constants import Permissions
from common.services import AllowedActions
from core.models import User
from project.selectors import project_guest_organization_check
from recce.data.models import <PERSON>cce, Recce<PERSON>ield, RecceSection
from reccev2.data.choices import (
    NATIVE_FIELDS,
    RecceActionsChoices,
    RecceFieldActionsChoices,
    RecceFileActionsChoices,
    RecceSectionActionsChoices,
)
from reccev2.domain.constants import SIGNAGE_AND_FACADE_FIELD_UUIDS


class RecceActions(AllowedActions):
    action_choices = RecceActionsChoices

    @classmethod
    def get_user_actions(
        cls, recce_status: Recce.StatusChoices, user_permissions_list: Permissions, project_id: int, user: User
    ):
        actions = []
        if recce_status in [
            Recce.StatusChoices.NOT_STARTED,
            Recce.StatusChoices.PENDING,
            Recce.StatusChoices.SUBMISSION_STARTED,
        ]:
            actions = [cls.action_choices.SUBMIT.value]
        elif recce_status == Recce.StatusChoices.SUBMITTED:
            actions = [cls.action_choices.APPROVE.value]
        elif recce_status == Recce.StatusChoices.APPROVED:
            actions = [cls.action_choices.UNAPPROVE.value]

        is_project_guest_user_mapping = project_guest_organization_check(
            project_id=project_id, organization_id=user.org_id
        )
        if not is_project_guest_user_mapping and (
            recce_status == Recce.StatusChoices.APPROVED.value
            or Permissions.CAN_EDIT_RECCE_DATA not in user_permissions_list
        ):
            return actions

        actions.append(cls.action_choices.ADD_SECTION.value)

        return actions


class RecceSectionActions(AllowedActions):
    action_choices = RecceSectionActionsChoices

    @classmethod
    def get_user_actions(
        cls,
        section: RecceSection,
        user_permissions_list: List,
        recce_status: Recce.StatusChoices,
        user: User,
        *args,
        **kwargs,
    ):
        if recce_status == Recce.StatusChoices.APPROVED.value:
            return []

        actions = [
            cls.action_choices.ADD_INPUT_FIELD.value,
            cls.action_choices.ADD_SPACE.value,
            cls.action_choices.ADD_IMAGE_AND_VIDEO.value,
            cls.action_choices.ADD_ATTACHMENT.value,
            cls.action_choices.ADD_DATE.value,
            cls.action_choices.ADD_DROPDOWN.value,
            cls.action_choices.ADD_MULTI_DROPDOWN.value,
        ]

        section_project_id = section.recce.project_id
        is_project_guest_user_mapping = project_guest_organization_check(
            project_id=section_project_id, organization_id=user.org_id
        )
        if not is_project_guest_user_mapping and (
            recce_status == Recce.StatusChoices.APPROVED.value
            or Permissions.CAN_EDIT_RECCE_DATA not in user_permissions_list
        ):
            return actions

        if (
            section.created_by_id == user.pk or (section.created_by.org_id == user.org_id and user.token_data.is_admin)
        ) and section.is_custom:
            actions.extend([cls.action_choices.REMOVE.value, cls.action_choices.RENAME.value])

        return actions


class RecceFieldActions(AllowedActions):
    action_choices = RecceFieldActionsChoices

    @classmethod
    def get_user_actions(
        cls,
        user: User,
        field: RecceField,
        user_permissions_list: list,
        recce_status: Recce.StatusChoices,
        is_project_guest_user_mapping: dict = {},
    ):
        field_section_project_id = field.section.recce.project_id
        if field_section_project_id not in is_project_guest_user_mapping:
            is_project_guest_user_mapping[field_section_project_id] = project_guest_organization_check(
                project_id=field_section_project_id, organization_id=user.org_id
            )
        if not is_project_guest_user_mapping[field_section_project_id] and (
            recce_status == Recce.StatusChoices.APPROVED.value
            or Permissions.CAN_EDIT_RECCE_DATA not in user_permissions_list
        ):
            return []

        actions = [cls.action_choices.ADD.value]

        if field.type in NATIVE_FIELDS:
            actions.append(cls.action_choices.EDIT.value)
        elif field.type in [
            RecceField.FieldChoices.SPACES,
            RecceField.FieldChoices.FILES,
            RecceField.FieldChoices.ATTACHMENT,
        ]:
            actions.append(cls.action_choices.UPLOAD.value)

        if (
            (field.created_by_id == user.pk or (field.created_by.org_id == user.org_id and user.token_data.is_admin))
            and (str(field.uuid) not in SIGNAGE_AND_FACADE_FIELD_UUIDS)
            and field.is_custom
        ):
            actions.extend([cls.action_choices.REMOVE.value, cls.action_choices.RENAME.value])
        return actions


class RecceFileActions(AllowedActions):
    action_choices = RecceFileActionsChoices

    @classmethod
    def get_user_actions(
        cls,
        field: RecceField,
        user_permissions_list: List,
        user: User,
        file_created_by_id: int,
        recce_status: Recce.StatusChoices,
        *args,
        **kwargs,
    ):
        if (
            Permissions.CAN_EDIT_RECCE_DATA in user_permissions_list or file_created_by_id == user.pk
        ) and recce_status != Recce.StatusChoices.APPROVED.value:
            if field.type in [RecceField.FieldChoices.FILES, RecceField.FieldChoices.ATTACHMENT]:
                return [cls.action_choices.REMOVE.value]
            elif field.type == RecceField.FieldChoices.SPACES:
                return [cls.action_choices.EDIT.value, cls.action_choices.REMOVE.value]

        return []
