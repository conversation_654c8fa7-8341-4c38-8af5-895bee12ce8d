import copy
import uuid
from functools import partial
from typing import List, <PERSON><PERSON>
from urllib.parse import urlsplit

import pytz
import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from phonenumbers import parse as phonenumbers_parse

from authorization.domain.constants import Permissions
from common.events.constants import Events
from common.events.recce import (
    RecceApprovedEventData,
    RecceLinkCreatedEventData,
    RecceStartedEventData,
    RecceSubmittedEventData,
    RecceUpdatedEventData,
    SingleRecceApprovedEventData,
)
from common.events.services import trigger_event
from common.exceptions import ThumbnailGeneratorNotFound, ThumbnailNotGeneratedException
from common.injector import inject
from common.services import model_update, nested_object_segregation
from common.thumbnail_generator import ThumbnailGeneratorFactory
from common.zip_file_generation import Zip<PERSON>ly<PERSON><PERSON>per
from core.models import OrganizationConfigRole, User
from core.selectors import get_india_country_id, organization_get
from core.utils import get_relative_path
from events.domain.task import whatsapp_process_worker
from events.interface.dispatchers import WhatsappNotificationDispatcherV2
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import MicroContextChoices
from project.data.models import ProjectUser, Store
from project.domain.caches import ProjectAssignmentCache
from project.domain.services import ProjectStatusUpdateService
from project.domain.status import Module, RecceStatus
from project.selectors import project_guest_organization_check
from recce.data.models import (
    RecceDropDownOption,
    RecceField,
    RecceFile,
    RecceFileThumbnail,
    RecceSection,
    RecceStatusHistory,
    RecceTemplate,
    RecceTemplateRole,
)
from reccev2.data.choices import (
    NATIVE_FIELDS,
    RecceActionsChoices,
    RecceFieldActionsChoices,
    RecceFileActionsChoices,
    RecceSectionActionsChoices,
)
from reccev2.data.entities import (
    RecceCreateData,
    RecceCreateInputData,
    RecceCreateInputDataSectionData,
    RecceSectionCreateData,
    RecceSectionFieldCreateData,
    RecceSectionGuideCreateData,
    RecceUserCreateData,
)
from reccev2.data.models import Recce, RecceUser
from reccev2.data.repositories import (
    RecceCreateRepository,
    RecceUserCreateRepository,
    recce_template_last_used_at_update,
)
from reccev2.data.selectors import (
    get_many_recce_field_using_uuid,
    get_many_recce_files_using_ids,
    get_recce_field,
    get_recce_field_of_section,
    get_recce_history,
    get_recce_object_using_id,
    get_recce_user_id_list,
    get_recce_using_id,
    recce_files_get_using_ids,
    recce_template_get_by_id,
)
from reccev2.domain.constants import DEFAULT_RECCE_TEMPLATE
from reccev2.domain.exceptions import (
    RecceCreateException,
    RecceDropDownOptionException,
    RecceFieldException,
    RecceFileException,
    RecceNotFoundException,
    RecceSectionException,
    RecceSectionNotFoundException,
    RecceSectionPermissionException,
    RecceSpaceFileError,
    RecceStatusException,
)
from reccev2.domain.helpers import create_recce_field_dropdown_options
from reccev2.domain.services import RecceActions, RecceFieldActions, RecceFileActions, RecceSectionActions
from reccev2.interface.serializers import RecceFileDataUpdateEntitySerializer, validate_recce_field_data
from rollingbanners.comment_base_service import CommentBaseService

logger = structlog.get_logger(__name__)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


def check_if_user_is_recce_user(user_id: int, recce_id: int):
    return RecceUser.objects.filter(
        user_id=user_id,
        recce_id=recce_id,
    ).exists()


def check_recce_action_permission(
    user_permissions_list: Permissions, recce_status: Recce.StatusChoices, required_action, project_id: int, user: User
):
    action_list = RecceActions.get_user_actions(
        recce_status=recce_status,
        user_permissions_list=user_permissions_list,
        project_id=project_id,
        user=user,
    )
    if required_action in action_list:
        return True
    raise RecceSectionPermissionException(f"{required_action} is Not Allowed")


def recce_history_create(recce_id: int, status: Recce.StatusChoices, user_id: int):
    recce_history = RecceStatusHistory(recce_id=recce_id, status=status, created_by_id=user_id)
    recce_history.save()
    return recce_history


def recce_update_event_trigger(recce: Recce):
    logger.debug(f"Project id: {recce.project_id}, recce id: {recce.pk}")
    event_data = RecceUpdatedEventData(
        project_id=recce.project_id,
        recce_id=recce.pk,
        recce_version=recce.version,
    )
    trigger_event(
        event=Events.RECCE_UPDATED,
        event_data=event_data,
    )


def recce_modified_history_create(recce_id: int, user_id: int):
    recce = get_recce_using_id(recce_id=recce_id)
    if recce.status in [Recce.StatusChoices.SUBMITTED.value, Recce.StatusChoices.APPROVED]:
        last_history = get_recce_history(recce_id=recce_id).first()
        if last_history and (
            last_history.created_by_id != user_id
            or (last_history.created_by_id == user_id and timezone.now().date() != last_history.created_at.date())
        ):
            recce_history_create(recce_id=recce_id, status=Recce.StatusChoices.MODIFIED, user_id=user_id)
            recce_update_event_trigger(recce=recce)


def prepare_json_block_for_rich_text(is_attachment: bool, file):
    block = dict()
    block["type"] = "attachment" if is_attachment else "image"
    block["meta"] = {
        "file_name": file.name,
        "url": get_relative_path(file.url),
    }
    block["text"] = ""
    return block


class RecceFieldService:
    action_service = RecceFieldActions

    def __init__(self, user: User, section_id: int, timezone: pytz.tzinfo.BaseTzInfo, user_permissions_list=None):
        if user_permissions_list is None:
            user_permissions_list = []
        self.user = user
        self.section_id = section_id
        self.user_permissions_list = user_permissions_list
        self.timezone = timezone

    @classmethod
    def check_if_field_exist_many(cls, field_name_list: list):
        if len(field_name_list) != len(set(field_name_list)):
            raise RecceFieldException(_("Field names Duplicated"))

    def _get_field(self, field_id):
        return get_recce_field_of_section(section_id=self.section_id, field_id=field_id)

    def check_action_permission(self, required_action, field, recce_status: Recce.StatusChoices):
        action_list = self.action_service.get_user_actions(
            user=self.user, field=field, user_permissions_list=self.user_permissions_list, recce_status=recce_status
        )
        if required_action in action_list:
            return True
        raise RecceFieldException(f"{required_action} is Not Allowed")

    def add_field(self, data: dict, save=True, clean=True):
        recce_field = RecceField(
            uuid=uuid.uuid4() if not data.get("uuid") else data.get("uuid"),
            name=data.get("name"),
            type=data.get("type"),
            section_id=self.section_id,
            created_by_id=self.user.pk,
            is_custom=True,
        )

        if clean:
            recce_field.clean()
        if save:
            recce_field.save()

            if data.get("type") in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                recce_drop_down_field_objs = []
                options = data.get("options")
                if not options:
                    raise RecceDropDownOptionException(_("Options are required for dropdown field"))
                recce_drop_down_field_objs.append(RecceDropDownOption(field=recce_field, data=options))
                RecceDropDownOption.objects.bulk_create(recce_drop_down_field_objs)

        return recce_field

    def add_field_many(self, fields_data, meta_data):
        field_objs = []
        field_name_list = []
        field_uuid_to_options_mapping = {}
        for field in fields_data:
            data = {
                "uuid": field.uuid,
                "name": field.name,
                "type": field.type,
            }
            if field.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                field_uuid_to_options_mapping[str(field.uuid)] = meta_data[str(field.uuid)].get("options")
                data["data"] = meta_data[str(field.uuid)].get("value")
            field_name_list.append(field.name)
            field_objs.append(self.add_field(data=data, clean=False, save=False))
        self.check_if_field_exist_many(field_name_list=field_name_list)
        try:
            RecceField.objects.bulk_create(field_objs)
            create_recce_field_dropdown_options(field_objs, field_uuid_to_options_mapping)
        except IntegrityError as e:
            if RecceField.INDEX_NAME in str(e):
                raise RecceFieldException(_("Field names duplicated"))

    def edit_field(
        self,
        data: dict,
        field_id: int,
    ):  # for renaming field
        field = self._get_field(field_id=field_id)
        self.check_action_permission(
            required_action=RecceFieldActionsChoices.RENAME.value, field=field, recce_status=field.section.recce.status
        )
        field, _, _ = model_update(instance=field, fields=["name"], updated_by_id=self.user.pk, data=data)
        return field

    def edit_field_many(
        self,
        fields_data,
    ):
        #  for renaming many field from app
        field_update_objs = []
        fields_name_dict = {field.uuid: field.name for field in fields_data}
        recce_fields = get_many_recce_field_using_uuid(
            section_id=self.section_id, uuid_list=list(fields_name_dict.keys())
        ).select_related("section__recce")
        recce_field_dict = {field.uuid: field for field in recce_fields}

        for field_uuid, obj in recce_field_dict.items():
            self.check_action_permission(
                required_action=RecceFieldActionsChoices.RENAME.value, field=obj, recce_status=obj.section.recce.status
            )
            obj.name = fields_name_dict.get(field_uuid)
            field_update_objs.append(obj)
        try:
            RecceField.objects.bulk_update(objs=field_update_objs, fields=["name"])
        except IntegrityError as e:
            if RecceField.INDEX_NAME in str(e):
                raise RecceFieldException(_("Field names duplicated"))

    def delete(
        self,
        field_id: int,
    ):  # for deleting field
        field = self._get_field(field_id=field_id)
        self.check_action_permission(
            required_action=RecceFieldActionsChoices.REMOVE.value, field=field, recce_status=field.section.recce.status
        )
        field.soft_delete(user_id=self.user.pk)
        return True

    def delete_many(self, uuid_list: List):
        fields = get_many_recce_field_using_uuid(section_id=self.section_id, uuid_list=uuid_list).select_related(
            "section__recce"
        )
        for field in fields:
            self.check_action_permission(
                required_action=RecceFieldActionsChoices.REMOVE.value,
                field=field,
                recce_status=field.section.recce.status,
            )
            if field.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                RecceDropDownOption.objects.filter(field_id=field.id).soft_delete(user_id=self.user.pk)
        return fields.update(deleted_at=timezone.now(), deleted_by_id=self.user.pk)

    def update_data(self, field, data: dict, save=True):  # for updating field data
        self.check_action_permission(
            required_action=RecceFieldActionsChoices.EDIT.value, field=field, recce_status=field.section.recce.status
        )
        validate_recce_field_data(field_type=field.type, field=field, data=data, timezone=self.timezone)
        field.data = data
        field.updated_by_id = self.user.pk
        field.updated_at = timezone.now()
        if save:
            field.save(update_fields=["data", "updated_by_id", "updated_at"])
        return field

    def process_data_update(self, field_id: int, data: dict):
        # used by web to update single field data
        field = get_recce_field_of_section(section_id=self.section_id, field_id=field_id)
        self.update_data(field=field, data=data, save=True)

    def update_data_many(self, field_data_list: List[dict], fields_obj_dict: dict):
        obj_list = []
        for field in field_data_list:
            field_obj = fields_obj_dict.get((field["uuid"]))
            data = (
                {"data": field["data"]["value"]}
                if field_obj.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]
                else field["data"]
            )
            obj_list.append(self.update_data(field=field_obj, data=data, save=False))

        RecceField.objects.bulk_update(objs=obj_list, fields=["updated_by_id", "updated_at", "data"])


class RecceSectionService:
    action_service = RecceSectionActions

    def __init__(self, user: User, recce_id: int, project_id=None, user_permissions_list=None):
        self.user = user
        self.recce_id = recce_id
        self.user_permissions_list = user_permissions_list
        self.project_id = project_id

    def check_if_recce_exists(self):
        if self.project_id:
            return Recce.objects.filter(id=self.recce_id, project_id=self.project_id).exists()
        return Recce.objects.filter(id=self.recce_id).exists()

    def check_action_permission(self, required_action, section: RecceSection, recce_status: Recce.StatusChoices):
        action_list = self.action_service.get_user_actions(
            section=section, user=self.user, user_permissions_list=self.user_permissions_list, recce_status=recce_status
        )
        if required_action in action_list:
            return True
        raise RecceSectionPermissionException(f"{required_action} is Not Allowed")

    def check_if_section_already_exists_with_name(self, name):
        if self.project_id:
            return (
                RecceSection.objects.select_related("recce")
                .filter(name=name, recce_id=self.recce_id, recce__project_id=self.project_id)
                .exists()
            )
        return RecceSection.objects.filter(name=name, recce_id=self.recce_id).exists()

    def check_if_section_exists(self, section_id: int):
        if self.project_id:
            return (
                RecceSection.objects.select_related("recce")
                .filter(id=section_id, recce_id=self.recce_id, recce__project_id=self.project_id)
                .exists()
            )
        return RecceSection.objects.filter(id=section_id, recce_id=self.recce_id).exists()

    def add_section(self, data: dict):
        if self.check_if_recce_exists():
            recce = get_recce_object_using_id(recce_id=self.recce_id)
            check_recce_action_permission(
                user_permissions_list=self.user_permissions_list,
                recce_status=recce.status,
                required_action=RecceActionsChoices.ADD_SECTION.value,
                project_id=self.project_id,
                user=self.user,
            )

            try:
                recce_section = RecceSection(
                    recce_id=self.recce_id, name=data.get("name"), created_by_id=self.user.pk, is_custom=True
                )
                recce_section.save()
            except IntegrityError:
                raise RecceSectionException(_("Section Name Duplicated"))
        else:
            raise RecceNotFoundException(_("Recce not found"))

        return recce_section

    def rename_section(self, data: dict, section_id: int):
        if self.check_if_section_exists(section_id):
            section = RecceSection.objects.select_related("recce").get(id=section_id)
            self.check_action_permission(
                required_action=RecceSectionActionsChoices.RENAME.value,
                section=section,
                recce_status=section.recce.status,
            )
            if self.check_if_section_already_exists_with_name(data.get("name")):
                raise RecceSectionException(_("Section Name Duplicated"))

            RecceSection.objects.filter(id=section_id).update(name=data.get("name"), updated_at=timezone.now())
        else:
            raise RecceSectionNotFoundException(_("Recce Section not found"))

    def delete_section(self, section_id: int):
        if self.check_if_section_exists(section_id):
            section = RecceSection.objects.select_related("recce").get(id=section_id)
            self.check_action_permission(
                required_action=RecceSectionActionsChoices.REMOVE.value,
                section=section,
                recce_status=section.recce.status,
            )

            RecceSection.objects.filter(id=section_id).soft_delete(self.user.pk)
        else:
            raise RecceSectionNotFoundException(_("Recce Section not found"))


class RecceFileService:
    action_service = RecceFileActions

    def __init__(
        self,
        user,
        user_permissions_list=None,
    ):
        if user_permissions_list is None:
            user_permissions_list = []
        self.user = user
        self.user_permissions_list = user_permissions_list

    def check_action_permission(
        self,
        required_action,
        field,
        file_created_by_id,
        recce_status: Recce.StatusChoices,
    ):
        action_list = self.action_service.get_user_actions(
            field=field,
            user_permissions_list=self.user_permissions_list,
            user=self.user,
            file_created_by_id=file_created_by_id,
            recce_status=recce_status,
        )
        if required_action in action_list:
            return True
        raise RecceFileException(f"{required_action} is Not Allowed")

    def edit_space_name(self, file_id: int, field_id: int, data: dict):
        file = RecceFile.objects.select_related("recce").filter(id=file_id, field_id=field_id).first()
        self.check_action_permission(
            field=file.field,
            required_action=RecceFileActionsChoices.EDIT.value,
            file_created_by_id=file.uploaded_by_id,
            recce_status=file.recce.status,
        )
        file.updated_at = timezone.now()
        file.updated_by_id = self.user.pk
        file.detail = data.get("detail")
        file.save(update_fields=["updated_by_id", "updated_at", "detail"])
        return file

    def file_upload(self, recce_id: int, field_id: int, data: dict, source: str):
        field = get_recce_field(field_id=field_id)
        if field.type == RecceField.FieldChoices.SPACES and not data.get("detail"):
            raise RecceSpaceFileError("Space File Does Not Have Caption")

        if field.type == RecceField.FieldChoices.ATTACHMENT:
            recce_file, _ = RecceFile.objects.update_or_create(
                field_id=field_id,
                recce_id=recce_id,
                defaults={
                    "file": data.get("file"),
                    "name": data.get("file").name,
                    "detail": data.get("detail"),
                    "uploaded_by_id": self.user.pk,
                    "source": source if source else RecceFile.ANDROID,
                },
            )
            return recce_file

        recce_file = RecceFile()
        recce_file.file = data.get("file")
        recce_file.detail = data.get("detail")
        recce_file.field_id = field_id
        recce_file.uploaded_by_id = self.user.pk
        recce_file.meta = data.get("meta")
        recce_file.name = data.get("file").name
        recce_file.recce_id = recce_id
        recce_file.source = source if source else RecceFile.ANDROID
        thumbnail = None
        if data.get("thumbnail"):
            thumbnail = RecceFileThumbnail(recce_file=recce_file, thumbnail=data.get("thumbnail"))
        else:
            try:
                thumbnail_url = (
                    ThumbnailGeneratorFactory(file=data.get("file"))
                    .get_generator()
                    .generate(path="recce-file-thumbnail")
                )
                if thumbnail_url:
                    thumbnail = RecceFileThumbnail(recce_file=recce_file, thumbnail=get_relative_path(thumbnail_url))
            except (ThumbnailGeneratorNotFound, ThumbnailNotGeneratedException):
                pass
        # to avoid "i/o operation on closed file" cloud upload is done after thumbnail generation
        recce_file.save()
        if thumbnail is not None:
            thumbnail.save()
        if field.type == RecceField.FieldChoices.RICH_TEXT:
            block = prepare_json_block_for_rich_text(is_attachment=data.get("is_attachment"), file=recce_file.file)
            if not field.data:
                field.data = []
            field.data.append(block)
            field.save(update_fields=["data"])

        return recce_file

    def update_many_space_files(self, section_id: int, files: List):
        recce_files = get_many_recce_files_using_ids(
            section_id=section_id, recce_file_ids=[recce_file.id for recce_file in files]
        ).select_related("recce")
        recce_file_id_obj_dict = {recce.id: recce for recce in files}
        recce_files_objs = []
        for file in recce_files:
            self.check_action_permission(
                field=file.field,
                required_action=RecceFileActionsChoices.EDIT.value,
                file_created_by_id=file.created_by_id,
                recce_status=file.recce.status,
            )
            recce_file_data = recce_file_id_obj_dict.get(file.id)
            file.detail = recce_file_data.detail
            file.meta = recce_file_data.meta
            file.updated_by_id = self.user.pk
            file.updated_at = timezone.now()
            recce_files_objs.append(file)

        RecceFile.objects.bulk_update(objs=recce_files_objs, fields=["meta", "detail", "updated_by_id", "updated_at"])

    def delete_many(self, section_id: int, file_id_list: list):
        recce_files = get_many_recce_files_using_ids(section_id=section_id, recce_file_ids=file_id_list).select_related(
            "recce"
        )
        for file in recce_files:
            self.check_action_permission(
                field=file.field,
                required_action=RecceFileActionsChoices.REMOVE.value,
                file_created_by_id=file.created_by_id,
                recce_status=file.recce.status,
            )
        # archive comment on recce file gets deleted
        CommentHelperService.archive_many(
            data={MicroContextChoices.RECCE_FILE.value: list(recce_files.values_list("id", flat=True))},
            user_id=self.user.pk,
        )
        recce_files.update(deleted_at=timezone.now(), deleted_by_id=self.user.pk)


def recce_submission_event_trigger(recce: Recce, user_id: int):
    logger.debug(f"Project id: {recce.project_id}, recce id: {recce.pk}")
    event_data = RecceSubmittedEventData(
        project_id=recce.project_id, recce_id=recce.pk, recce_version=recce.version, user_id=user_id
    )
    trigger_event(
        event=Events.RECCE_SUBMITTED,
        event_data=event_data,
    )


class RecceStatusService:
    recce = None
    action_service = RecceActions

    def __init__(self, recce_id: int, user: User, project_id: int, user_permissions_list=None):
        if user_permissions_list is None:
            user_permissions_list = []
        self.recce_id = recce_id
        self.user = user
        self.user_permissions_list = user_permissions_list
        self.project_id = project_id

    def get_recce(self):
        self.recce = get_recce_using_id(recce_id=self.recce_id)
        return self.recce

    def get_status(self):
        return self.get_recce().status

    def check_action_permission(
        self,
        required_action,
        recce_status,
    ):
        action_list = self.action_service.get_user_actions(
            recce_status=recce_status,
            user_permissions_list=self.user_permissions_list,
            project_id=self.project_id,
            user=self.user,
        )
        if required_action in action_list:
            return True
        raise RecceStatusException(f"{required_action} is Not Allowed")

    def status_update(self, status: Recce.StatusChoices):
        recce = self.get_recce()
        recce.status = Recce.StatusChoices.SUBMITTED.value if status == Recce.StatusChoices.UNAPPROVE.value else status
        recce.submitted_at = timezone.now() if status == Recce.StatusChoices.SUBMITTED.value else None
        recce.submitted_by_id = self.user.pk if status == Recce.StatusChoices.SUBMITTED.value else None
        recce.save(update_fields=["status", "submitted_at", "submitted_by"])
        recce_history_create(recce_id=self.recce_id, status=status, user_id=self.user.pk)

    def approve(self):
        self.check_action_permission(required_action=RecceActionsChoices.APPROVE.value, recce_status=self.get_status())
        self.status_update(status=Recce.StatusChoices.APPROVED.value)
        return True

    def submit(
        self,
    ):
        self.check_action_permission(required_action=RecceActionsChoices.SUBMIT.value, recce_status=self.get_status())
        self.status_update(status=Recce.StatusChoices.SUBMITTED.value)
        recce_submission_event_trigger(recce=self.recce, user_id=self.user.pk)
        return True

    def history_list(
        self,
    ):
        return get_recce_history(recce_id=self.recce_id)

    def unapprove(self):
        self.check_action_permission(
            required_action=RecceActionsChoices.UNAPPROVE.value, recce_status=self.get_status()
        )
        self.status_update(status=Recce.StatusChoices.UNAPPROVE.value)
        return True


class RecceSectionUpdateService:
    def __init__(
        self,
        user: User,
        section_id: int,
        timezone: pytz.tzinfo.BaseTzInfo,
        user_permissions_list=None,
    ):
        if user_permissions_list is None:
            user_permissions_list = []
        self.user = user
        self.section_id = section_id
        self.user_permissions_list = user_permissions_list
        self.timezone = timezone

    @classmethod
    def serialize_recce_space_files_data(cls, data):
        recce_files_serializer = RecceFileDataUpdateEntitySerializer(data=data, many=True)
        recce_files_serializer.is_valid(raise_exception=True)
        return recce_files_serializer.validated_data

    def process_fields(self, fields_data, extra_data):
        to_add, to_update, to_delete = nested_object_segregation(docs_list=fields_data)
        field_service = RecceFieldService(
            section_id=self.section_id,
            user=self.user,
            user_permissions_list=self.user_permissions_list,
            timezone=self.timezone,
        )
        if to_add:
            field_service.add_field_many(
                fields_data=to_add,
                meta_data=extra_data,
            )

        if to_update:
            field_service.edit_field_many(fields_data=to_update)

        if to_delete:
            field_service.delete_many(uuid_list=[field.uuid for field in to_delete])

    def process_data_update(self, data: dict):
        recce_fields = get_many_recce_field_using_uuid(
            section_id=self.section_id, uuid_list=list(data.keys())
        ).select_related("section__recce")
        recce_field_uuid_object_dict = {field.uuid: field for field in recce_fields}
        native_fields_update_list = []
        recce_file_for_updates = []
        recce_file_to_delete = []
        for field_uuid, field_obj in recce_field_uuid_object_dict.items():
            if field_obj.type in NATIVE_FIELDS:
                native_fields_update_list.append(
                    {
                        "uuid": field_uuid,
                        "data": data.get(str(field_uuid)),
                    }
                )

            elif field_obj.type in [
                RecceField.FieldChoices.SPACES,
                RecceField.FieldChoices.FILES,
                RecceField.FieldChoices.ATTACHMENT,
            ]:
                recce_space_files_data = self.serialize_recce_space_files_data(data=data.get(str(field_uuid)))
                to_add, to_update, to_delete = nested_object_segregation(docs_list=recce_space_files_data)

                if to_update:
                    recce_file_for_updates.extend(to_update)

                if to_delete:
                    recce_file_to_delete.extend([file.id for file in to_delete])

        field_service = RecceFieldService(
            section_id=self.section_id,
            user=self.user,
            user_permissions_list=self.user_permissions_list,
            timezone=self.timezone,
        )

        if native_fields_update_list:
            field_service.update_data_many(
                field_data_list=native_fields_update_list,
                fields_obj_dict=recce_field_uuid_object_dict,
            )

        file_services = RecceFileService(user_permissions_list=self.user_permissions_list, user=self.user)
        if recce_file_for_updates:
            file_services.update_many_space_files(
                section_id=self.section_id,
                files=recce_file_for_updates,
            )

        if recce_file_to_delete:
            file_services.delete_many(
                section_id=self.section_id,
                file_id_list=[recce_file.id for recce_file in recce_file_for_updates],
            )


def recce_creation_trigger(
    project_id: int, recce_id: int, recce_version: int, assignee_ids: list[int], user_id: int, org_id: int
):
    event_data = RecceLinkCreatedEventData(
        project_id=project_id,
        recce_id=recce_id,
        recce_version=recce_version,
        recce_user_ids=assignee_ids,
        user_id=user_id,
        org_id=org_id,
    )
    on_commit(
        partial(
            trigger_event,
            event=Events.RECCE_LINK_CREATED,
            event_data=event_data,
        )
    )


class RecceCreateService:
    class RecceCreateServiceException(Exception):
        pass

    @inject(params={"repository": RecceCreateRepository})
    def __init__(self, repository: RecceCreateRepository):
        self.repository = repository

    def _get_recce_users(
        self, assignee_ids: list[int], stakeholder_ids: list[int], project_user_data: dict[int, ProjectUser]
    ) -> Tuple[list[RecceUserCreateData], list[int]]:
        recce_users: list[RecceUserCreateData] = []
        assignee_user_ids = []
        for assignee_id in assignee_ids:
            assignee_data = project_user_data.get(assignee_id)
            assignee = RecceUserCreateData(
                user_id=assignee_data.get("user_id"),
                role_id=assignee_data.get("role_id"),
                type=RecceUser.RecceUserChoices.ASSIGNED,
            )
            assignee_user_ids.append(assignee_data.get("user_id"))
            recce_users.append(assignee)
        for stakeholder_id in stakeholder_ids:
            stakeholder_data = project_user_data.get(stakeholder_id)
            stakeholder = RecceUserCreateData(
                user_id=stakeholder_data.get("user_id"),
                role_id=stakeholder_data.get("role_id"),
                type=RecceUser.RecceUserChoices.STAKEHOLDER,
            )
            recce_users.append(stakeholder)
        logger.info("Assignees and stakeholders created")
        return recce_users, assignee_user_ids

    def _get_recce_sections(
        self, template_id: int, org_id: int, sections_create_data: list[RecceCreateInputDataSectionData]
    ) -> Tuple[list[RecceSectionCreateData], list[dict]]:
        section_data_dict = self.repository.get_template_section_data(template_id=template_id, org_id=org_id)
        template_sections: list[dict] = []
        sections: list[RecceSectionCreateData] = []
        for section_id_data in sections_create_data:
            section_uuid, field_uuids = section_id_data.uuid, section_id_data.field_ids
            section_data = section_data_dict.get(section_uuid)
            fields: list[RecceSectionFieldCreateData] = []
            template_section_fields: list[dict] = []
            seen_field_uuids = set()
            for field_data in section_data.get("fields"):
                if field_data.get("uuid") not in field_uuids:
                    continue
                if field_data.get("uuid") in seen_field_uuids:
                    field_data["uuid"] = str(uuid.uuid4())
                field = RecceSectionFieldCreateData(
                    uuid=field_data.get("uuid"),
                    name=field_data.get("name"),
                    type=field_data.get("type"),
                    is_required=field_data.get("is_required"),
                    options=field_data.get("options"),
                )
                seen_field_uuids.add(field_data.get("uuid"))
                fields.append(field)
                template_section_fields.append(field.__dict__)
            guide = None
            if section_data.get("guide"):
                guide = RecceSectionGuideCreateData(
                    name=section_data.get("guide").get("name"), url=section_data.get("guide").get("url")
                )
            section = RecceSectionCreateData(name=section_data.get("name"), guide=guide, fields=fields)
            sections.append(section)
            template_sections.append(
                {
                    "name": section_data.get("name"),
                    "guide": guide.__dict__ if guide else None,
                    "fields": template_section_fields,
                    "uuid": section_data.get("uuid"),
                }
            )
        logger.info("Recce sections created")
        return sections, template_sections

    def assign_project_users(
        self,
        project_id: int,
        user_id: int,
        role_id: int,
        assignee_ids: list[int],
        project_user_data: dict[int, dict],
    ):
        user_id_role_id_mapping = {}
        for data in project_user_data.values():
            user_id = data["user_id"]
            if user_id in user_id_role_id_mapping:
                user_id_role_id_mapping[user_id].append(data.get("role_id"))
            else:
                user_id_role_id_mapping[user_id] = [data.get("role_id")]
        new_project_users = []
        for assingee_id in assignee_ids:
            if assingee_id in user_id_role_id_mapping and role_id in user_id_role_id_mapping.get(assingee_id, []):
                continue
            new_project_users.append(
                ProjectUser(
                    project_id=project_id,
                    user_id=assingee_id,
                    role_id=role_id,
                    created_by_id=user_id,
                )
            )
        if new_project_users:
            ProjectUser.objects.bulk_create(new_project_users)
        ProjectAssignmentCache.delete(instance_id=project_id)

    def create(self, project_id: int, user_id: int, org_id, data: RecceCreateInputData) -> int:
        logger.info("Recce creation started", project_id=project_id, user_id=user_id, org_id=org_id)
        project_user_data = self.repository.get_project_user_data_by_ids(org_id=org_id)
        recce_users, assignee_ids = self._get_recce_users(
            assignee_ids=data.assignee_ids, stakeholder_ids=data.stakeholder_ids, project_user_data=project_user_data
        )
        if not data.sections:
            raise RecceCreateException(_("Recce sections are required"))
        sections, template_sections = self._get_recce_sections(
            template_id=data.template_id, org_id=org_id, sections_create_data=data.sections
        )
        role_id = self.repository.get_or_create_recce_assignee_role_id(org_id=org_id, user_id=user_id)
        self.assign_project_users(
            project_user_data=project_user_data,
            role_id=role_id,
            project_id=project_id,
            assignee_ids=assignee_ids,
            user_id=user_id,
        )
        recce = RecceCreateData(
            project_id=project_id,
            name=data.name,
            status=Recce.StatusChoices.NOT_STARTED,
            instructions=data.instructions,
            template_id=data.template_id,
            recce_users=recce_users,
            sections=sections,
            template_sections=template_sections,
            client_poc=data.client_poc,
            due_at=data.due_at,
            version=self.repository.get_recce_version(project_id),
        )
        logger.info("Recce data created")
        try:
            recce_id = self.repository.save_data(data=recce, user_id=user_id)
        except RecceCreateRepository.RecceCreateException as e:
            logger.info("Recce creation failed", project_id=project_id, user_id=user_id, org_id=org_id, error=str(e))
            raise RecceCreateException(str(e))
        logger.info("Recce created successfully", recce_id=recce_id)
        recce_creation_trigger(
            project_id=project_id,
            recce_id=recce_id,
            recce_version=recce.version,
            assignee_ids=assignee_ids,
            user_id=user_id,
            org_id=org_id,
        )
        recce_history_create(recce_id=recce_id, status=Recce.StatusChoices.NOT_STARTED, user_id=user_id)
        recce_template_last_used_at_update(template_id=data.template_id)
        recce_project_status_update(project_id=project_id, organization_id=org_id, user_id=user_id)
        return recce_id


def recce_create_prefill_data_get(project_id: int, org_id: int, template_id: int):
    prefill_data = {"details": {}, "sections": [], "client_poc": None}
    assignee_role_ids, stakeholder_role_ids = [], []
    if template_id == 0:
        recce_role_ids = list(
            OrganizationConfigRole.objects.filter(organization_config_id=org_id, is_recce_role=True).values_list(
                "role_id", flat=True
            )
        )
        assignee_role_ids.extend(recce_role_ids)
        stakeholder_role_ids.extend(recce_role_ids)
        template = copy.deepcopy(DEFAULT_RECCE_TEMPLATE)
        prefill_data["details"]["instructions"] = template.get("instructions")
        prefill_data["sections"] = template.get("sections")

        # removing signage and facade section if country is not India
        if get_india_country_id() != organization_get(org_id=org_id).country_id:
            prefill_data["sections"].pop(3)

    else:
        template_roles = RecceTemplateRole.objects.filter(template_id=template_id)
        for template_role in template_roles:
            if template_role.type == RecceTemplateRole.RecceTemplateRoleChoices.ASSIGNEE:
                assignee_role_ids.append(template_role.role_id)
            else:
                stakeholder_role_ids.append(template_role.role_id)
        template: RecceTemplate = recce_template_get_by_id(template_id=template_id, org_id=org_id)
        prefill_data["details"]["instructions"] = template.instructions
        prefill_data["sections"] = template.sections
    prefill_data["details"]["assignees"] = ProjectUser.objects.filter(
        project_id=project_id, role_id__in=assignee_role_ids
    ).select_related("user", "role")
    prefill_data["details"]["stakeholders"] = ProjectUser.objects.filter(
        project_id=project_id, role_id__in=stakeholder_role_ids
    ).select_related("user", "role")
    client_poc = Store.objects.filter(project_id=project_id).values("dealer_name", "dealer_phone_number").first()
    if client_poc.get("dealer_name") and client_poc.get("dealer_phone_number"):
        contact_number = phonenumbers_parse(client_poc.get("dealer_phone_number"))
        prefill_data["client_poc"] = {
            "name": client_poc.get("dealer_name"),
            "phone_number": {
                "country_code": f"+{contact_number.country_code}",
                "number": f"{contact_number.national_number}",
            },
        }
    else:
        prefill_data["client_poc"] = None
    return prefill_data


def recce_settings_get(project_id: int, recce_id: int):
    recce: Recce = (
        Recce.objects.available()
        .filter(project_id=project_id, id=recce_id)
        .select_related("client_poc", "template")
        .prefetch_related("recce_users")
        .first()
    )
    assignees, stakeholders = [], []
    for recce_user in recce.recce_users.all():
        if recce_user.type == RecceUser.RecceUserChoices.ASSIGNED:
            assignees.append(recce_user)
        elif recce_user.type == RecceUser.RecceUserChoices.STAKEHOLDER:
            stakeholders.append(recce_user)

    recce_settings = {}
    recce_settings["sections"] = recce.template_sections
    recce_settings["details"] = {
        "assignees": assignees,
        "stakeholders": stakeholders,
        "instructions": recce.instructions,
        "name": recce.name,
        "version": recce.version,
        "due_at": recce.due_at,
        "template_id": recce.template_id if recce.template_id else 0,
        "template_name": recce.template.name if recce.template else DEFAULT_RECCE_TEMPLATE.get("name"),
    }
    recce_settings["client_poc"] = recce.client_poc if hasattr(recce, "client_poc") else None

    return recce_settings


class RecceActionService:
    def __init__(self, recce_id, user: User):
        self.recce_id = recce_id
        self.user = user

    def get_recce(self):
        self.recce = get_recce_using_id(recce_id=self.recce_id)
        return self.recce

    def recce_start(
        self,
        location_data,
    ):
        recce = self.get_recce()
        update_field = []
        if recce.status == Recce.StatusChoices.NOT_STARTED:
            recce.status = Recce.StatusChoices.PENDING
            recce_history_create(recce_id=self.recce_id, status=Recce.StatusChoices.PENDING, user_id=self.user.pk)
            update_field.append("status")
            if location_data:
                recce.location = location_data
                update_field.append("location")

            recce.save(update_fields=update_field)
            recce_start_event_trigger(recce=self.recce)

    def recce_end(self):
        recce = self.get_recce()
        if recce.status == Recce.StatusChoices.SUBMISSION_STARTED:
            recce_history_create(recce_id=self.recce_id, status=Recce.StatusChoices.SUBMITTED, user_id=self.user.pk)
            recce.status = Recce.StatusChoices.SUBMITTED
            recce.submitted_at = timezone.now()
            recce.submitted_by = self.user
            recce.save(update_fields=["status", "submitted_at", "submitted_by"])
            recce_submission_event_trigger(recce=recce, user_id=self.user.pk)

    def recce_submission_started(self):
        recce = self.get_recce()
        if recce.status == Recce.StatusChoices.PENDING:
            recce.status = Recce.StatusChoices.SUBMISSION_STARTED
            recce_history_create(
                recce_id=self.recce_id, status=Recce.StatusChoices.SUBMISSION_STARTED, user_id=self.user.pk
            )
            recce.save(update_fields=["status"])

    def assign_dynamic_user(self) -> bool:
        recce = self.get_recce()
        recce_users = get_recce_user_id_list(recce_id=self.recce_id)

        if self.user.pk in recce_users:
            return True
        elif project_guest_organization_check(project_id=recce.project_id, organization_id=self.user.org_id):
            recce_user = RecceUserCreateData(
                user_id=self.user.pk,
                role_id=None,
                type=RecceUser.RecceUserChoices.DYNAMIC,
            )
            RecceUserCreateRepository().save_data(recce_users_data=[recce_user], save=True, recce_id=self.recce_id)
            return True
        else:
            raise ValidationError(
                "You don't have permission to view this information. Please contact your admin for permission."
            )


def recce_link_share_data_get(recce_id: int):
    recce = (
        Recce.objects.filter(id=recce_id)
        .select_related("project", "project__store", "project__store__city", "project__store__state")
        .available()
        .first()
    )
    url_parts = urlsplit(recce.recce_link)
    recce_link = url_parts.path + "?" + url_parts.query
    return {
        "recce_link": recce_link,
        "job_id": recce.project.job_id,
        "store_name": recce.project.name,
        "city": recce.project.store.city.name if recce.project.store.city else None,
        "state": recce.project.store.state.name if recce.project.store.state else None,
        "client_name": recce.project.client.name,
    }


def recce_link_share(user_ids: list[int], custom_users: list[dict], recce_id: int):
    recipients = []
    for custom_user in custom_users:
        recipients.append({"name": custom_user.get("name"), "contact": custom_user.get("phone_number").as_e164})
    users = User.objects.filter(id__in=user_ids)
    for user in users:
        phone_number = user.phone_number
        if phone_number:
            recipients.append({"name": user.name, "contact": phone_number.as_e164})
        else:
            logger.info(f"Phone Number not found for user: {user}")

    if len(recipients) > 0:
        whatsapp_process_worker.apply_async(
            queue=WhatsappNotificationDispatcherV2.queue,
            kwargs={
                "whatsapp_template": EventTemplates.RECCE_LINK_CREATED.value,
                "recipients": recipients,
                "context": recce_link_share_data_get(recce_id),
                "log_id": None,
            },
        )
    else:
        logger.info(f"No valid recipients found for recce: {recce_id}")
    # whatsapp_process_worker(
    #     whatsapp_template=WhatsAppEventHandler.EventTemplates.RECCE_LINK_CREATED.value,
    #     recipients=recipients,
    #     context=recce_link_share_data_get(recce_id),
    # )


def recce_approve_event_trigger(project_id: int, approve_by_id: int):
    logger.debug(f"Project id: {project_id}, approve by id: {approve_by_id}")
    event_data = RecceApprovedEventData(
        project_id=project_id,
        user_id=approve_by_id,
    )
    trigger_event(
        event=Events.RECCE_APPROVED,
        event_data=event_data,
    )


def recce_section_first_update(section_id: int):
    section = RecceSection.objects.get(id=section_id)
    if not section.updated_at:
        section.updated_at = timezone.now()
        section.save(update_fields=["updated_at"])


def recce_start_event_trigger(recce: Recce):
    event_data = RecceStartedEventData(project_id=recce.project_id, recce_id=recce.pk, recce_version=recce.version)
    trigger_event(event=Events.RECCE_STARTED, event_data=event_data)


def get_recce_status(project_id: int):
    distinct_status = (
        Recce.objects.filter(project_id=project_id).order_by("status").values_list("status", flat=True).distinct()
    )
    status = RecceStatus.RECCE_NOT_CREATED
    if len(distinct_status) == 0:
        status = RecceStatus.RECCE_NOT_CREATED
    elif len(distinct_status) == 1:
        if distinct_status[0] in [Recce.NOT_STARTED, Recce.PENDING, Recce.SUBMISSION_STARTED]:
            status = RecceStatus.RECCE_PENDING
        elif distinct_status[0] == Recce.SUBMITTED:
            status = RecceStatus.RECCE_SUBMITTED
        elif distinct_status[0] == Recce.APPROVED:
            status = RecceStatus.RECCE_APPROVED
    elif len(distinct_status) == 2 and Recce.APPROVED in distinct_status and Recce.SUBMITTED in distinct_status:
        status = RecceStatus.RECCE_SUBMITTED
    else:
        status = RecceStatus.RECCE_PENDING

    return status


def recce_project_status_update(project_id: int, organization_id: int, user_id: int):
    ProjectStatusUpdateService.process(
        project_id=project_id,
        module=Module.RECCE.value,
        status=get_recce_status(project_id=project_id),
        organization_id=organization_id,
        user_id=user_id,
    )


def recce_zip_file_download(file_id_list: List[int], recce_id: int):
    recce_files = recce_files_get_using_ids(file_id_list=file_id_list, recce_id=recce_id)
    if not recce_files:
        raise RecceFileException(_("No File Found"))
    path_list = []
    for file in recce_files:
        path_list.append(
            {
                "fs": file.file.name,
                "n": file.name if file.name else file.file.name.split("/")[-1],
            },
        )
    logger.info("recce path list prepared")

    zip_filename = f"recce_files_{timezone.now().strftime('%Y-%m-%d-%H-%M-%S')}.zip"

    zfly_obj = ZipFlyWrapper(paths=path_list)
    logger.info("zip fly obj created")
    generator = zfly_obj.generator()
    logger.info("generator called")
    return generator, zip_filename


def recce_update(project_id: int, recce_id: int, name: str, due_at: timezone.datetime):
    Recce.objects.available().filter(project_id=project_id, id=recce_id).update(name=name, due_at=due_at)


def recce_file_elements_data_get(recce_file: RecceFile):
    element_list = []
    meta = []
    if recce_file.meta and isinstance(recce_file.meta, list):
        meta = recce_file.meta
    elif isinstance(recce_file.meta, dict) and recce_file.meta.get("meta", None):
        meta = recce_file.meta.get("meta")
    for data in meta:
        if data.get("quad") and data.get("quad").get("quadSpecifications"):
            quad_data = data.get("quad").get("quadSpecifications")
            height = quad_data.get("elementHeight", None)
            width = quad_data.get("elementWidth", None)
            if not height:
                height = ""
            if not width:
                width = ""
            element_data = {
                "uom": quad_data.get("uom", "-"),
                "height": height,
                "width": width,
                "name": quad_data.get("elementName", "-"),
            }
            element_list.append(element_data)
    return element_list


def single_recce_approve_event_trigger(project_id: int, recce_id: int):
    logger.info("Single Approve Event Trigger", project_id=project_id, recce_id=recce_id)
    event_data = SingleRecceApprovedEventData(project_id=project_id, recce_id=recce_id)
    trigger_event(
        event=Events.SINGLE_RECCE_APPROVED,
        event_data=event_data,
    )
