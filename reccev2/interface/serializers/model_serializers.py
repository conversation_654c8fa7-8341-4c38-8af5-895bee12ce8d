import pytz
from django.conf import settings
from phonenumbers import parse as phonenumbers_parse
from rest_framework import serializers

from common.json_parser.serializers import TEXT_TYPE_LIST, TypeOutputSerializer, const
from common.serializers import BaseModelSerializer, BaseSerializer, CustomDate<PERSON>ield, HashIdField
from core.serializers import (
    OrganizationSerializer,
    ProfileSerializer,
    RoleSerializer,
    UserModelSerializer,
    UserOrgSerializer,
)
from element.interface.serializers.serializers import ElementLibraryMappingModelSerializer
from project.data.models import ProjectUser
from project.serializers import ProjectModelSerializer, ProjectUserModelSerializer
from recce.data.models import (
    RecceClientPoc,
    RecceField,
    RecceFileThumbnail,
    RecceSection,
    RecceSectionGuide,
    RecceStatusHistory,
)
from reccev2.data.choices import RecceActionsChoices
from reccev2.data.models import <PERSON>cc<PERSON>, Recce<PERSON><PERSON>, RecceUser
from reccev2.domain.constants import DROPDOWN_UUID_MAPPING, RECCE_STATUS_COLOR_CODE
from reccev2.domain.helpers import get_options_for_dropdown_field
from reccev2.domain.services import RecceActions, RecceFieldActions, RecceFileActions, RecceSectionActions
from reccev2.interface.serializers.template_serializers import RecceTemplateSectionSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage


class RecceBaseModelSerializer(BaseModelSerializer):
    name = serializers.SerializerMethodField()

    def get_name(self, obj: Recce):
        return obj.name if obj.name else f"Recce {obj.version}"

    status = serializers.SerializerMethodField()
    link = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    due_at = CustomDateField()

    def get_link(self, obj):
        return settings.LINK_URL + obj.recce_link

    def get_status(self, obj):
        return {
            "key": obj.status,
            "value": dict(Recce.StatusChoices.choices).get(obj.status),
            "color_code": RECCE_STATUS_COLOR_CODE.get(obj.status),
        }

    def get_location(self, obj):
        if obj.location:
            return {"longitude": obj.location.x, "latitude": obj.location.y}
        elif obj.project.store.location:
            return {"longitude": obj.project.store.location.x, "latitude": obj.project.store.location.y}
        else:
            return {"longitude": None, "latitude": None}

    class Meta:
        model = Recce
        fields = "__all__"


class RecceListModelSerializer(RecceBaseModelSerializer):
    actions = serializers.SerializerMethodField()

    def get_actions(self, obj):
        return RecceActions.get_user_actions(
            recce_status=obj.status,
            user_permissions_list=self.context.get("permissions"),
            project_id=obj.project_id,
            user=self.context.get("user"),
        )

    class Meta(RecceBaseModelSerializer.Meta):
        fields = (
            "id",
            "name",
            "version",
            "status",
            "link",
            "actions",
        )


class RecceSectionGuideBaseModelSerializer(BaseModelSerializer):
    url = serializers.SerializerMethodField()

    def get_url(self, obj):
        # TODO : TO be removed after release recce 1
        return obj.url if obj.url and obj.url.startswith("http") else PublicMediaFileStorage.url(obj.url)

    class Meta:
        model = RecceSectionGuide
        fields = "__all__"


class RecceSectionBaseModelSerializer(BaseModelSerializer):
    class Meta:
        model = RecceSection
        fields = "__all__"


class RecceSectionSerializer(RecceSectionBaseModelSerializer):
    class GuideSerializer(RecceSectionGuideBaseModelSerializer):
        class Meta(RecceSectionGuideBaseModelSerializer.Meta):
            fields = ("name", "url")

    guide = GuideSerializer()
    is_submitted = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()

    def get_is_submitted(self, obj):
        return True if obj.updated_at else False

    def get_actions(self, obj):
        return RecceSectionActions.get_user_actions(
            section=obj,
            user_permissions_list=self.context.get("permissions"),
            recce_status=obj.recce.status,
            user=self.context.get("user"),
        )

    class Meta(RecceSectionBaseModelSerializer.Meta):
        fields = ("id", "name", "guide", "is_submitted", "actions")


class RecceFieldBaseModelSerializer(BaseModelSerializer):
    class Meta:
        model = RecceField
        fields = "__all__"


class RecceFileSerializer(BaseModelSerializer):
    element_count = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    source = serializers.SerializerMethodField()
    meta = serializers.SerializerMethodField()

    def get_meta(self, obj):
        if obj.meta:
            return obj.meta
        return []

    actions = serializers.SerializerMethodField()

    def get_actions(self, obj):
        return RecceFileActions.get_user_actions(
            field=self.context.get("field"),
            user_permissions_list=self.context.get("permissions"),
            user=self.context.get("user"),
            file_created_by_id=obj.uploaded_by_id,
            recce_status=self.context.get("recce_status"),
        )

    class RecceThumbnailSerializer(BaseModelSerializer):
        class Meta:
            model = RecceFileThumbnail
            fields = ("thumbnail",)

    def get_thumbnail(self, obj):
        if hasattr(obj, "thumbnail") and obj.thumbnail:
            data = self.RecceThumbnailSerializer(obj.thumbnail).data
            if data["thumbnail"]:
                return data["thumbnail"]

        if obj.file.url and obj.file.url.split(".")[-1].lower() in ["jpeg", "jpg", "png", "svg"]:
            return PublicMediaFileStorage.url(obj.file.url)

    def get_element_count(self, obj):
        return 0 if not obj.meta else len(obj.meta)

    def get_comment_count(self, obj):
        return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

    def get_source(self, obj):
        return RecceFile.ANDROID if hasattr(obj, "source") and obj.source == RecceFile.APP else obj.source

    class Meta:
        model = RecceFile
        fields = (
            "id",
            "file",
            "name",
            "detail",
            "thumbnail",
            "element_count",
            "comment_count",
            "source",
            "actions",
            "meta",
        )


class RecceAttachedFileSerializer(RecceFileSerializer):
    class Meta(RecceFileSerializer.Meta):
        fields = (
            "id",
            "file",
            "name",
            "detail",
        )


class RecceFieldSerializer(RecceFieldBaseModelSerializer):
    data = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()

    def get_options(self, obj):
        old_field_uuid_to_options_mapping = self.context.get("old_field_uuid_to_options_mapping", {})
        current_field_uuid_to_options_mapping = self.context.get("current_field_uuid_to_options_mapping", {})
        final_options = get_options_for_dropdown_field(
            field_uuid=obj.uuid,
            field_options=obj.options.data,
            old_field_uuid_to_options_mapping=old_field_uuid_to_options_mapping,
            current_field_uuid_to_options_mapping=current_field_uuid_to_options_mapping,
        )
        return sorted(final_options)

    def get_data(self, obj):
        if obj.type in [
            RecceField.FieldChoices.SPACES,
            RecceField.FieldChoices.FILES,
        ]:
            return RecceFileSerializer(
                obj.files,
                many=True,
                context={
                    "permissions": self.context.get("permissions"),
                    "field": obj,
                    "user": self.context.get("user"),
                    "recce_status": self.context.get("recce_status"),
                },
            ).data
        elif obj.type == RecceField.FieldChoices.ATTACHMENT:
            return RecceAttachedFileSerializer(
                obj.files.first(),
            ).data

        if obj.data and isinstance(obj.data, dict):
            if obj.type == RecceField.FieldChoices.PHONE_NUMBER:
                if not obj.data.get("data"):
                    return {"country_code": "+91", "number": ""}
                number = obj.data.get("data")
                if len(number) == 10:
                    number = f"+91{obj.data.get('data')}"
                contact_number = phonenumbers_parse(number)
                return {
                    "country_code": f"+{contact_number.country_code}",
                    "number": f"{contact_number.national_number}",
                }

            elif obj.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                if str(obj.uuid) in DROPDOWN_UUID_MAPPING:
                    options = DROPDOWN_UUID_MAPPING.get(
                        str(obj.uuid), [] if not obj.data.get("data") else [obj.data.get("data")]
                    )
                else:
                    options = self.get_options(obj)
                return {
                    "value": obj.data.get("data"),
                    "options": options,
                }
            elif obj.type == RecceField.FieldChoices.BOOLEAN:
                return obj.data.get("data") if obj.data.get("data") is not None else False
            elif obj.data.get("data") is not None:
                return obj.data.get("data")
        elif obj.type == RecceField.FieldChoices.RICH_TEXT:
            if obj.data and isinstance(obj.data, list):
                serializer = TypeOutputSerializer(
                    instance=obj.data,
                    many=True,
                    context={
                        "allowed_type_choices": {
                            const.MENTION: None,
                            const.TEXT: TEXT_TYPE_LIST,
                            const.ATTACHMENT: None,
                            const.IMAGE: None,
                            const.LINE_BREAK: None,
                            const.APPROVAL: None,
                            const.REJECTED: None,
                            const.PIN: None,
                            const.HYPERLINK: TEXT_TYPE_LIST,
                        },
                        "request": self.context.get("request"),
                    },
                )
                return serializer.data
            return []
        elif obj.type == RecceField.FieldChoices.TEXT:
            return ""
        if obj.type == RecceField.FieldChoices.BOOLEAN:
            return False
        elif obj.type == RecceField.FieldChoices.PHONE_NUMBER:
            return {"country_code": "+91", "number": ""}
        elif obj.type == RecceField.FieldChoices.DECIMAL:
            return None
        elif obj.type == RecceField.FieldChoices.INTEGER:
            return None
        elif obj.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
            if str(obj.uuid) in DROPDOWN_UUID_MAPPING:
                return {"value": "", "options": DROPDOWN_UUID_MAPPING.get(str(obj.uuid), [])}
            else:
                return {"value": obj.data, "options": self.get_options(obj)}
        else:
            return ""

    def get_actions(self, obj):
        is_project_guest_user_mapping = {}
        return RecceFieldActions.get_user_actions(
            field=obj,
            user_permissions_list=self.context.get("permissions"),
            user=self.context.get("user"),
            recce_status=self.context.get("recce_status"),
            is_project_guest_user_mapping=is_project_guest_user_mapping,
        )

    class Meta(RecceFieldBaseModelSerializer.Meta):
        fields = (
            "id",
            "uuid",
            "name",
            "type",
            "is_required",
            "is_custom",
            "data",
            "actions",
        )


class RecceProjectSerializer(ProjectModelSerializer):
    address = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    client = OrganizationSerializer()
    location = serializers.SerializerMethodField()

    def get_location(self, obj):
        return (
            {"longitude": obj.store.location.x, "latitude": obj.store.location.y}
            if obj.store.location
            else {"longitude": None, "latitude": None}
        )

    def get_address(self, obj):
        return obj.store.full_address if obj.store else None

    def get_city(self, obj):
        if obj.store and obj.store.city and obj.store.city.name:
            return obj.store.city.name
        return None

    class Meta(ProjectModelSerializer.Meta):
        ref_name = "ProjectSerializerRecceApp"
        fields = ("id", "name", "job_id", "address", "city", "client", "location")


class RecceListSerializerApp(RecceBaseModelSerializer):
    class ProjectSerializer(RecceProjectSerializer):
        class Meta(RecceProjectSerializer.Meta):
            fields = (
                "id",
                "name",
                "job_id",
                "address",
                "city",
            )

    project = ProjectSerializer()
    due_at = serializers.SerializerMethodField()

    def get_due_at(self, obj):
        if obj.due_at is None:
            return None
        timezone = pytz.timezone(obj.project.config.timezone.name)
        return obj.due_at.astimezone(timezone).strftime("%Y-%m-%d")

    class Meta(RecceBaseModelSerializer.Meta):
        fields = ("id", "name", "version", "status", "project", "due_at")


class RecceUserBaseModelSerializer(BaseModelSerializer):
    class Meta:
        fields = "__all__"
        model = RecceUser


class RecceUserSerializer(RecceUserBaseModelSerializer):
    class UserSerializer(ProfileSerializer):
        class Meta(ProfileSerializer.Meta):
            fields = ("id", "name", "photo", "phone_number")
            ref_name = "RecceUserSerializer'"

    user = UserSerializer()
    role = RoleSerializer()

    class Meta(RecceUserBaseModelSerializer.Meta):
        fields = ("id", "user", "role")


class ReccDetailSerializerApp(RecceBaseModelSerializer):
    instructions = serializers.ListField(
        child=TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.LINE_BREAK: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
    )

    class ProjectSerializer(RecceProjectSerializer):
        class Meta(RecceProjectSerializer.Meta):
            fields = ("id", "name", "job_id", "address", "city", "client")

    stakeholders = RecceUserSerializer(source="recce_users", many=True)

    project = ProjectSerializer()
    sections = RecceSectionSerializer(many=True)
    location = serializers.SerializerMethodField()
    due_at = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()

    def get_actions(self, obj):
        recce_actions = RecceActions.get_user_actions(
            recce_status=obj.status,
            user_permissions_list=self.context.get("permissions"),
            project_id=obj.project_id,
            user=self.context.get("user"),
        )

        allowed_actions = (
            [RecceActionsChoices.ADD_SECTION.value] if RecceActionsChoices.ADD_SECTION.value in recce_actions else []
        )

        return allowed_actions

    def get_location(self, obj):
        return (
            {"longitude": obj.location.x, "latitude": obj.location.y}
            if obj.location
            else {"longitude": None, "latitude": None}
        )

    def get_due_at(self, obj):
        if obj.due_at is None:
            return None
        timezone = pytz.timezone(obj.project.config.timezone.name)
        return obj.due_at.astimezone(timezone).strftime("%Y-%m-%d")

    class Meta(RecceBaseModelSerializer.Meta):
        ref_name = "RecceDetailSerializerAPP"
        fields = (
            "id",
            "name",
            "version",
            "status",
            "project",
            "instructions",
            "link",
            "due_at",
            "stakeholders",
            "sections",
            "created_at",
            "location",
            "actions",
        )


# class RecceFileElementBaseModelSerializer(BaseModelSerializer):
#     class Meta:
#         fields = "__all__"
#         model = RecceFileElement


# class RecceFileElementSerializer(RecceFileElementBaseModelSerializer):
#     class Meta(RecceFileElementBaseModelSerializer.Meta):
#         fields = (
#             "id",
#             "points",
#             "type",
#             "meta_data",
#         )


class RecceFileFileEditorSerializer(RecceFileSerializer):
    uploaded_by = UserOrgSerializer()
    section_name = serializers.SerializerMethodField()
    version = serializers.SerializerMethodField()
    # elements = RecceFileElementSerializer(many=True, source="file_elements")

    def get_version(self, obj):
        return obj.recce.version

    def get_section_name(self, obj):
        return obj.field.section.name

    class Meta(RecceFileSerializer.Meta):
        fields = (
            "source",
            "name",
            "section_name",
            "detail",
            "uploaded_by",
            "uploaded_at",
            "version",
            # "elements",
        )


class RecceStatusHistoryBaseModelSerializer(BaseModelSerializer):
    class Meta:
        model = RecceStatusHistory
        fields = "__all__"


class RecceStatusHistorySerializer(RecceStatusHistoryBaseModelSerializer):
    class UserSerializer(ProfileSerializer):
        class Meta(ProfileSerializer.Meta):
            fields = ("id", "name", "photo", "phone_number")
            ref_name = "RecceUserSerializer'"

    created_by = serializers.SerializerMethodField()

    status = serializers.SerializerMethodField()

    def get_created_by(self, obj):
        return (
            self.UserSerializer(obj.created_by).data
            if obj.created_by.org_id in self.context.get("known_organization_ids", [])
            else None
        )

    def get_status(self, obj):
        return {
            "key": obj.status,
            "value": (
                "Created"
                if obj.status == Recce.StatusChoices.NOT_STARTED
                else dict(Recce.StatusChoices.choices).get(obj.status)
            ),
            "color_code": RECCE_STATUS_COLOR_CODE.get(obj.status),
        }

    class Meta(RecceStatusHistoryBaseModelSerializer.Meta):
        fields = ("id", "status", "created_by", "created_at")


class ProjectUserSerializer(ProjectUserModelSerializer):
    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name", "photo")
            ref_name = "ProjectUserSerializerUserSerializer"

    user = UserSerializer()
    role = RoleSerializer()

    class Meta(ProjectUserModelSerializer.Meta):
        model = ProjectUser
        fields = ("id", "user", "role")
        ref_name = "ProjectUserSerializerRecce"


class PrefillDataTemplateSectionSerializer(RecceTemplateSectionSerializer):
    class GuideSerializer(BaseSerializer):
        name = serializers.CharField()
        url = serializers.SerializerMethodField()

        @staticmethod
        def get_url(obj):
            return PublicMediaFileStorage.url(obj.get("url")) if obj.get("url") else None

        class Meta:
            ref_name = "PrefillDataTemplateSectionnGuideSerializer"

    guide = GuideSerializer(allow_null=True)


class ProjectRecceCreatePrefillDataSerializer(BaseSerializer):
    class TemplateDetailsSerializer(BaseSerializer):
        assignees = ProjectUserSerializer(many=True)
        stakeholders = ProjectUserSerializer(many=True)
        instructions = serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )

        class Meta:
            ref_name = "ProjectRecceCreatePrefillDataTemplateDetailsSerializer"

    details = TemplateDetailsSerializer()
    sections = PrefillDataTemplateSectionSerializer(many=True)
    client_poc = serializers.DictField(allow_null=True)

    class Meta:
        ref_name = "ProjectRecceCreatePrefillDataSerializer"


class RecceClientPocModelSerializer(BaseModelSerializer):
    class Meta:
        model = RecceClientPoc
        fields = "__all__"
        ref_name = "RecceClientPocModelSerializer"


class ProjectRecceSettingsSerializer(BaseSerializer):
    class RecceClientPocSerializer(RecceClientPocModelSerializer):
        phone_number = serializers.SerializerMethodField()

        def get_phone_number(self, obj):
            contact_number = phonenumbers_parse(obj.phone_number.as_e164)
            return {
                "country_code": f"+{contact_number.country_code}",
                "number": f"{contact_number.national_number}",
            }

        class Meta(RecceClientPocModelSerializer.Meta):
            fields = ("name", "phone_number")
            ref_name = "RecceClientPocSerializer"

    class TemplateDetailsSerializer(BaseSerializer):
        assignees = RecceUserSerializer(many=True)
        stakeholders = RecceUserSerializer(many=True)
        instructions = serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
        name = serializers.CharField()
        version = serializers.IntegerField()
        template_id = HashIdField()
        template_name = serializers.CharField()
        due_at = CustomDateField()

        class Meta:
            ref_name = "ProjectRecceSettingsTemplateDetailsSerializer"

    details = TemplateDetailsSerializer()
    sections = PrefillDataTemplateSectionSerializer(many=True)
    client_poc = RecceClientPocSerializer()

    class Meta:
        ref_name = "ProjectRecceSettingsSerializer"


class RecceClientLibraryElementSerializer(ElementLibraryMappingModelSerializer):
    class PreviewFileSerializer(BaseSerializer):
        type = serializers.CharField()
        file = serializers.SerializerMethodField()
        is_main = serializers.BooleanField()
        name = serializers.CharField()

        def get_file(self, obj):
            return PublicMediaFileStorage.url(obj.get("file")) if obj.get("file") else None

        class Meta:
            ref_name = "RecceClientLibraryElementSerializerPreviewFile"

    element_id = HashIdField(source="element.id")
    name = serializers.CharField(source="element.name")
    library_name = serializers.CharField(source="library.name")
    uom = serializers.SerializerMethodField()
    preview_file = serializers.SerializerMethodField()

    def get_uom(self, obj):
        return {"value": obj.element.uom, "name": self.uom_name_get(uom_id=obj.element.uom)}

    def get_preview_file(self, obj):
        return self.PreviewFileSerializer(obj.preview_file).data if obj.preview_file else None

    class Meta(ElementLibraryMappingModelSerializer.Meta):
        ref_name = "RecceClientLibraryElementSerializer"
        fields = ("element_id", "name", "library_name", "uom", "preview_file")
