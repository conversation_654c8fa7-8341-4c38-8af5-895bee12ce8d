import pytz
from django.utils.translation import gettext_lazy as _
from phonenumber_field import serializerfields
from rest_framework import serializers

from common.json_parser.constants import const
from common.json_parser.serializers import TypeSerializer
from common.serializers import BaseSerializer, CustomDateField
from recce.data.models import RecceField
from reccev2.domain.exceptions import RecceFieldSerializerException

recce_field_types_register_dict = {}


def register_recce_field_serializer(field_type: str):
    def wrapper(serializer, *args, **kwargs):
        if serializer in recce_field_types_register_dict.values():
            raise RecceFieldSerializerException("Serializer already registered")
        if field_type in recce_field_types_register_dict:
            raise RecceFieldSerializerException("Field Type already registered")

        recce_field_types_register_dict[field_type] = serializer

        return serializer

    return wrapper


@register_recce_field_serializer(field_type=RecceField.FieldChoices.RICH_TEXT)
class RecceFieldRichTextSerializer(BaseSerializer):
    data = serializers.ListField(
        child=TypeSerializer(
            context={
                "allowed_type_choices": {
                    const.IMAGE: None,
                    const.ATTACHMENT: None,
                    const.TEXT: [const.NORMAL],
                    const.LINE_BREAK: None,
                }
            }
        )
    )

    class Meta:
        ref_name = "RecceFieldRichTextSerializer"


@register_recce_field_serializer(field_type=RecceField.FieldChoices.INTEGER)
class RecceFieldIntegerSerializer(BaseSerializer):
    data = serializers.IntegerField(default=0)

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.DECIMAL)
class RecceFieldDecimalSerializer(BaseSerializer):
    data = serializers.DecimalField(decimal_places=2, max_digits=10, default=0)

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.BOOLEAN)
class RecceFieldBooleanSerializer(BaseSerializer):
    data = serializers.BooleanField(allow_null=True)

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.PHONE_NUMBER)
class RecceFieldPhoneNumberSerializer(BaseSerializer):
    data = serializerfields.PhoneNumberField()

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.TEXT)
class RecceFieldTextFieldSerializer(BaseSerializer):
    data = serializers.CharField()

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.DROPDOWN)
class RecceFieldDropdownFieldSerializer(BaseSerializer):
    data = serializers.CharField(allow_null=True)

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.MULTI_DROPDOWN)
class RecceFieldMultiDropdownFieldSerializer(BaseSerializer):
    data = serializers.ListField(child=serializers.CharField(), allow_empty=True, allow_null=False)

    class Meta:
        pass


@register_recce_field_serializer(field_type=RecceField.FieldChoices.DATE)
class RecceFieldDateFieldSerializer(BaseSerializer):
    data = CustomDateField(allow_null=True)

    class Meta:
        pass


def validate_recce_field_data(
    field_type: RecceField.FieldChoices, field: RecceField, data, timezone: pytz.tzinfo.BaseTzInfo
):
    serializer = recce_field_types_register_dict.get(field_type)
    if not serializer:
        raise RecceFieldSerializerException(_("Invalid Field Type"))
    if field_type == RecceField.FieldChoices.RICH_TEXT:
        serializer(data={"data": data}).is_valid(raise_exception=True)
    elif field_type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
        serializer(data={"data": data.get("data")}).is_valid(raise_exception=True)
    else:
        serializer(data=data, context={"timezone": timezone}).is_valid(raise_exception=True)
    return True
