from rest_framework import serializers

from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer, TypeSerializer
from common.serializers import BaseModelSerializer, BaseSerializer, HashIdField
from core.serializers import RoleSerializer
from recce.data.models import RecceField, RecceTemplate
from reccev2.domain.enums import UserAction


class RecceTemplateBaseModelSerializer(BaseModelSerializer):
    class Meta:
        model = RecceTemplate
        fields = "__all__"


class RecceTemplateSerializer(RecceTemplateBaseModelSerializer):
    actions = serializers.SerializerMethodField()

    def get_actions(self, obj):
        return [
            UserAction.EDIT.value,
            UserAction.DUPLICATE.value,
            UserAction.DELETE.value,
            UserAction.RENAME.value,
        ]

    class Meta(RecceTemplateBaseModelSerializer.Meta):
        fields = ("id", "name", "actions", "position")


class FieldSerializer(BaseSerializer):
    type = serializers.ChoiceField(choices=RecceField.FieldChoices.choices)
    name = serializers.CharField()
    is_required = serializers.BooleanField()
    uuid = serializers.CharField(allow_null=True)
    options = serializers.ListField(child=serializers.CharField(), allow_null=True)

    # def validate_type(self, value):
    #     if value not in [
    #         RecceField.FieldChoices.RICH_TEXT,
    #         RecceField.FieldChoices.SPACES,
    #         RecceField.FieldChoices.FILES,
    #     ]:
    #         raise WrongFieldChoiceException("Supported field types are rich_text, spaces and files")
    #     return value

    class Meta:
        ref_name = "RecceTemplateSectionField"


class RecceTemplateSectionSerializer(BaseSerializer):
    class GuideSerializer(BaseSerializer):
        name = serializers.CharField()
        url = serializers.URLField(allow_null=True)

        class Meta:
            ref_name = "RecceTemplateSectionGuide"

    name = serializers.CharField()
    guide = GuideSerializer(allow_null=True)
    fields = FieldSerializer(many=True)
    uuid = serializers.CharField(allow_null=True)

    class Meta:
        ref_name = "RecceTemplateSection"


class RecceTemplateUpdateSerializer(BaseSerializer):
    class TemplateDetailsSerializer(BaseSerializer):
        assignee_role_ids = serializers.ListField(child=HashIdField())
        stakeholder_role_ids = serializers.ListField(child=HashIdField())
        instructions = serializers.ListField(
            child=TypeSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )

        class Meta:
            ref_name = "RecceTemplateTemplateDetails"

    details = TemplateDetailsSerializer()
    sections = RecceTemplateSectionSerializer(many=True)

    class Meta:
        ref_name = "RecceTemplateUpdateSerializer"


class RecceTemplateCreateSerializer(RecceTemplateUpdateSerializer):
    name = serializers.CharField()

    class Meta:
        ref_name = "RecceTemplateCreateSerializer"


class RecceTemplateDetailsSerializer(RecceTemplateSerializer):
    class TemplateDetailsSerializer(BaseSerializer):
        assignee_roles = RoleSerializer(many=True)
        stakeholder_roles = RoleSerializer(many=True)
        instructions = serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )

        class Meta:
            ref_name = "TemplateDetailsTemplateDetails"

    class RecceTemplateSectionDetailsSerializer(RecceTemplateSectionSerializer):
        class FieldDetailsSerializer(FieldSerializer):
            actions = serializers.SerializerMethodField()
            options = serializers.SerializerMethodField()

            def get_options(self, obj):
                return obj.get("options") if obj.get("options") else []

            def get_actions(self, obj):
                actions = [
                    UserAction.RENAME.value,
                    UserAction.DELETE.value,
                    UserAction.DUPLICATE.value,
                ]
                if obj.get("type") in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                    actions.append(UserAction.EDIT.value)
                return actions

            class Meta:
                ref_name = "FieldDetailsSerializer"

        class GuideSerializer(BaseSerializer):
            name = serializers.CharField()
            url = serializers.SerializerMethodField()

            @staticmethod
            def get_url(obj):
                return obj.get("url") if obj.get("url") else None

            class Meta:
                ref_name = "RecceTemplateSectionDetailsGuideSerializer"

        actions = serializers.SerializerMethodField()
        fields = FieldDetailsSerializer(many=True)
        guide = GuideSerializer(allow_null=True)

        def get_actions(self, obj):
            return [UserAction.RENAME.value, UserAction.DELETE.value, UserAction.DUPLICATE.value, UserAction.EDIT.value]

        class Meta:
            ref_name = "RecceTemplateSectionDetailsSerializer"

    details = TemplateDetailsSerializer()
    sections = RecceTemplateSectionDetailsSerializer(many=True)

    class Meta(RecceTemplateBaseModelSerializer.Meta):
        ref_name = "RecceTemplateDetailsTemplateDetails"
        fields = ("id", "name", "details", "sections", "actions", "position")
