from enum import Enum

from django.db.models import TextChoices

from recce.data.models import RecceField


class RecceFieldCreateChoices(TextChoices):
    RICH_TEXT = "rich_text"
    TEXT = "text"
    SPACES = "spaces"
    FILES = "files"
    INTEGER = "integer"
    DECIMAL = "decimal"
    BOOLEAN = "boolean"
    PHONE_NUMBER = "phone_number"
    DROPDOWN = "dropdown"
    MULTI_DROPDOWN = "multi_dropdown"
    DATE = "date"
    ATTACHMENT = "attachment"


class RecceActionsChoices(Enum):
    APPROVE = "approve"
    SUBMIT = "submit"
    UNAPPROVE = "unapprove"
    ADD_SECTION = "add_section"


class RecceSectionActionsChoices(Enum):
    ADD_INPUT_FIELD = "add_input_field"
    ADD_SPACE = "add_space"
    ADD_IMAGE_AND_VIDEO = "add_image_and_video"
    ADD_ATTACHMENT = "add_attachment"
    ADD_DATE = "add_date"
    ADD_DROPDOWN = "add_dropdown"
    ADD_MULTI_DROPDOWN = "add_multi_dropdown"
    REMOVE = "remove"
    RENAME = "rename"


class RecceFieldActionsChoices(Enum):
    ADD = "add"  # For Adding
    REMOVE = "remove"  # For Removing Field
    UPLOAD = "upload"  # For RecceFile
    EDIT = "edit"  # Field Data Edit
    RENAME = "rename"  # Field Name Edit/Rename


class RecceFileActionsChoices(Enum):
    EDIT = "edit"  # for  space file rename
    REMOVE = "remove"  # remove both recce files


NATIVE_FIELDS = [
    RecceField.FieldChoices.RICH_TEXT,
    RecceField.FieldChoices.TEXT,
    RecceField.FieldChoices.INTEGER,
    RecceField.FieldChoices.DECIMAL,
    RecceField.FieldChoices.BOOLEAN,
    RecceField.FieldChoices.DROPDOWN,
    RecceField.FieldChoices.MULTI_DROPDOWN,
    RecceField.FieldChoices.PHONE_NUMBER,
    RecceField.FieldChoices.DATE,
]
