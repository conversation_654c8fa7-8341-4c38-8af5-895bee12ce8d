import copy

from django.conf import settings
from django.db.models import F
from django.db.models.functions import Lower
from django.utils import timezone
from django.utils.module_loading import import_string
from rest_framework.exceptions import ValidationError

from authorization.domain.constants import Permissions
from common.choices import OrganizationType, PermissionScope, ProjectUserRoleLevelChoices, ReservedRoleNames, RoleType
from common.injector import Injectable, inject
from core.helpers import OrgPermissionHelper
from core.models import OrganizationConfigRole, Role, User
from microcontext.choices import MicroContextChoices
from project.data.models import ProjectUser
from recce.data.models import (
    RecceClientPoc,
    RecceDropDownOption,
    RecceField,
    RecceSection,
    RecceSectionGuide,
    RecceTemplate,
    RecceTemplateRole,
)
from reccev2.data.entities import (
    RecceCreateData,
    RecceSectionCreateData,
    RecceSectionFieldCreateData,
    RecceUserCreateData,
)
from reccev2.data.models import Recce, RecceUser
from reccev2.data.selectors import recce_file_get_many, recce_template_get_by_id
from reccev2.domain.constants import DEFAULT_RECCE_TEMPLATE
from reccev2.domain.enums import UserAction
from reccev2.domain.exceptions import ApprovedRecceFileDeleteException
from rollingbanners.comment_base_service import CommentBaseService
from rollingbanners.hash_id_converter import HashIdConverter

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class RecceTemplateBaseRepository(Injectable):
    def create_stakeholder_template_roles(
        self, stakeholder_role_ids: list, template_id: int, user_id: int, save: bool = True
    ):
        template_roles = []
        for role_id in stakeholder_role_ids:
            template_role = RecceTemplateRole(
                template_id=template_id,
                role_id=role_id,
                type=RecceTemplateRole.RecceTemplateRoleChoices.STAKEHOLDER,
                created_by_id=user_id,
            )
            template_role.clean()
            template_roles.append(template_role)
        if save:
            RecceTemplateRole.objects.bulk_create(template_roles)
        return template_roles

    def create_assignee_template_roles(
        self, assignee_role_ids: list, template_id: int, user_id: int, save: bool = True
    ):
        template_roles = []
        for role_id in assignee_role_ids:
            template_role = RecceTemplateRole(
                template_id=template_id,
                role_id=role_id,
                type=RecceTemplateRole.RecceTemplateRoleChoices.ASSIGNEE,
                created_by_id=user_id,
            )
            template_role.clean()
            template_roles.append(template_role)
        if save:
            RecceTemplateRole.objects.bulk_create(template_roles)
        return template_roles


class RecceTemplateCreateRepository(RecceTemplateBaseRepository):
    def save_data(self, data: dict, org_id: int, user_id: int, position: int, save: bool = True) -> int:
        template = RecceTemplate()
        template.organization_id = org_id
        template.instructions = data.get("details").get("instructions")
        template.sections = data.get("sections")
        template.created_by_id = user_id
        template.name = data.get("name")
        template.position = position
        if save:
            template.save()

        self.create_stakeholder_template_roles(data.get("details").get("stakeholder_role_ids"), template.pk, user_id)
        self.create_assignee_template_roles(data.get("details").get("assignee_role_ids"), template.pk, user_id)
        return template.pk


class RecceTemplateUpdateRepository(RecceTemplateBaseRepository):
    def get_template_role_ids(self, template_id: int):
        template_roles = RecceTemplateRole.objects.filter(template_id=template_id)
        assignee_role_ids, stakeholder_role_ids = [], []
        for template_role in template_roles:
            if template_role.type == RecceTemplateRole.RecceTemplateRoleChoices.ASSIGNEE:
                assignee_role_ids.append(template_role.role_id)
            else:
                stakeholder_role_ids.append(template_role.role_id)
        return assignee_role_ids, stakeholder_role_ids

    def delete_stakeholder_template_roles(self, stakeholder_role_ids: list, template_id: int):
        RecceTemplateRole.objects.filter(
            template_id=template_id,
            role_id__in=stakeholder_role_ids,
            type=RecceTemplateRole.RecceTemplateRoleChoices.STAKEHOLDER,
        ).delete()

    def delete_assignee_template_roles(self, assignee_role_ids: list, template_id: int):
        RecceTemplateRole.objects.filter(
            template_id=template_id,
            role_id__in=assignee_role_ids,
            type=RecceTemplateRole.RecceTemplateRoleChoices.ASSIGNEE,
        ).delete()

    def save_data(self, data: dict, template_id: int, user_id: int, org_id: int, save: bool = True):
        template: RecceTemplate = recce_template_get_by_id(template_id=template_id, org_id=org_id)
        template.instructions = data.get("details").get("instructions")
        template.sections = data.get("sections")
        template.updated_by_id = user_id
        if save:
            template.save()

        self.create_stakeholder_template_roles(data.get("new_stakeholder_role_ids"), template_id, user_id)
        self.create_assignee_template_roles(data.get("new_assignee_role_ids"), template_id, user_id)
        self.delete_stakeholder_template_roles(data.get("removed_stakeholder_role_ids"), template_id)
        self.delete_assignee_template_roles(data.get("removed_assignee_role_ids"), template_id)
        return template.pk


def recce_template_delete(template_id: int, user_id: int, org_id: int):
    template: RecceTemplate = recce_template_get_by_id(template_id=template_id, org_id=org_id)
    template.soft_delete(user_id=user_id)


def recce_template_rename(template_id: int, name: str, user_id: int, org_id: int):
    template: RecceTemplate = recce_template_get_by_id(template_id=template_id, org_id=org_id)
    template.name = name
    template.updated_by_id = user_id
    template.updated_at = timezone.now()
    template.save(update_fields=["name", "updated_by_id", "updated_at"])


def swap_recce_templates_position(template_1: int, template_2: int, org_id: int, user_id: int):
    templates = (
        RecceTemplate.objects.available()
        .filter(id__in=[template_1, template_2], organization_id=org_id)
        .values("id", "position")
    )
    if len(templates) != 2:
        raise ValidationError("Invalid template ids")
    position_1 = None
    position_2 = None
    for template in templates:
        if template["id"] == template_1:
            position_1 = template["position"]
        else:
            position_2 = template["position"]
    RecceTemplate.objects.bulk_update(
        [
            RecceTemplate(id=template_1, position=None),
            RecceTemplate(id=template_2, position=None),
        ],
        fields=["position"],
    )

    updated_at = timezone.now()
    RecceTemplate.objects.bulk_update(
        [
            RecceTemplate(id=template_1, position=position_2, updated_by_id=user_id, updated_at=updated_at),
            RecceTemplate(id=template_2, position=position_1, updated_by_id=user_id, updated_at=updated_at),
        ],
        fields=["position", "updated_by_id", "updated_at"],
    )


def recce_template_last_used_at_update(template_id):
    RecceTemplate.objects.filter(id=template_id).update(last_used_at=timezone.now())


def default_recce_template_get(org_id: int, user: User):
    roles_data = list(
        OrganizationConfigRole.objects.filter(organization_config_id=org_id, is_recce_role=True, role__is_active=True)
        .select_related("role")
        .values("role_id", "role__name")
        .annotate(id=F("role_id"), name=F("role__name"))
        .values("id", "name")
    )
    for data in roles_data:
        data["id"] = HashIdConverter.encode(data["id"])
    default_template = copy.deepcopy(DEFAULT_RECCE_TEMPLATE)
    default_template["details"] = {"assignee_roles": roles_data, "stakeholder_roles": roles_data, "instructions": []}
    if OrgPermissionHelper.has_permission(user=user, permission=Permissions.CAN_EDIT_RECCE_TEMPLATE):
        default_template["actions"] = [UserAction.DUPLICATE.value]
    else:
        default_template["actions"] = []
    return default_template


class RecceUserCreateRepository(Injectable):
    def save_data(
        self, recce_users_data: list[RecceUserCreateData], recce_id: int, save: bool = True
    ) -> list[RecceUser]:
        recce_users: list[RecceUser] = []
        for recce_user in recce_users_data:
            recce_user = RecceUser(
                recce_id=recce_id,
                user_id=recce_user.user_id,
                role_id=recce_user.role_id,
                type=recce_user.type,
            )
            recce_user.clean()
            recce_users.append(recce_user)
        if save:
            RecceUser.objects.bulk_create(recce_users)
        return recce_users


class RecceCreateRepository(Injectable):
    class RecceCreateException(Exception):
        pass

    class RecceAssigneeRoleNotFoundException(RecceCreateException):
        pass

    @inject(params={"recce_user_create_repository": RecceUserCreateRepository})
    def __init__(self, recce_user_create_repository: RecceUserCreateRepository) -> None:
        self.recce_user_create_repository = recce_user_create_repository

    @staticmethod
    def get_project_user_data_by_ids(org_id: int):
        project_users = ProjectUser.objects.filter(role__organization_id=org_id).values("id", "user_id", "role_id")
        user_data_dict = {}
        for project_user in project_users:
            user_data_dict[project_user.get("id")] = project_user
        return user_data_dict

    @staticmethod
    def get_template_section_data(template_id: int, org_id: int) -> dict:
        if template_id == 0:
            sections = DEFAULT_RECCE_TEMPLATE.get("sections")
        else:
            sections = recce_template_get_by_id(template_id=template_id, org_id=org_id).sections
        section_data_dict = {}
        for section in sections:
            section_data_dict[section.get("uuid")] = section
        return section_data_dict

    @staticmethod
    def get_recce_version(project_id: int):
        return Recce.objects.filter(project_id=project_id).count() + 1

    def create_recce(self, data: RecceCreateData, user_id: int, save: bool = True) -> Recce:
        recce = Recce(
            project_id=data.project_id,
            name=data.name,
            status=data.status,
            instructions=data.instructions,
            template_id=data.template_id if data.template_id else None,
            created_by_id=user_id,
            template_sections=data.template_sections,
            due_at=data.due_at,
            version=data.version,
        )
        recce.clean()
        if save:
            try:
                recce.save()
            except Recce.RecceVersionDuplicateException as e:
                raise self.RecceCreateException(e)

        if data.client_poc:
            client_poc = RecceClientPoc(
                recce_id=recce.pk,
                name=data.client_poc.name,
                phone_number=data.client_poc.phone_number,
            )
            client_poc.clean()
            client_poc.save()
        return recce

    def create_recce_users(
        self, recce_users_data: list[RecceUserCreateData], recce_id: int, save: bool = True
    ) -> list[RecceUser]:
        return self.recce_user_create_repository.save_data(
            recce_users_data=recce_users_data, recce_id=recce_id, save=save
        )

    def create_recce_section_fields(
        self, fields_data: list[RecceSectionFieldCreateData], section_id: int, user_id: int, save: bool = True
    ):
        recce_fields: list[RecceField] = []
        recce_options: list[RecceDropDownOption] = []
        for field in fields_data:
            recce_field = RecceField(
                section_id=section_id,
                uuid=field.uuid,
                name=field.name,
                type=field.type,
                is_required=field.is_required,
                created_by_id=user_id,
            )
            if field.type in [RecceField.FieldChoices.DROPDOWN, RecceField.FieldChoices.MULTI_DROPDOWN]:
                recce_option = RecceDropDownOption(
                    data=field.options,
                    field=recce_field,
                )
                recce_options.append(recce_option)
            recce_field.clean()
            recce_fields.append(recce_field)
        if save:
            recce_fields = RecceField.objects.bulk_create(recce_fields)
            recce_options = RecceDropDownOption.objects.bulk_create(recce_options)
        return recce_fields, recce_options

    def create_recce_sections(
        self, sections_data: list[RecceSectionCreateData], recce_id: int, user_id: int, save: bool = True
    ) -> list[RecceSection]:
        recce_sections: list[RecceSection] = []
        for section_data in sections_data:
            recce_section = RecceSection(recce_id=recce_id, name=section_data.name, created_by_id=user_id)
            recce_section.clean()
            recce_sections.append(recce_section)
        if save:
            recce_sections = RecceSection.objects.bulk_create(recce_sections)
        recce_fields: list[RecceField] = []
        recce_dropdown_options: list[RecceDropDownOption] = []
        recce_section_guide: list[RecceSectionGuide] = []
        for recce_section, section_data in zip(recce_sections, sections_data):
            recce_field_data, recce_field_dropdown_options = self.create_recce_section_fields(
                fields_data=section_data.fields, section_id=recce_section.pk, user_id=user_id, save=False
            )
            recce_fields.extend(recce_field_data)
            recce_dropdown_options.extend(recce_field_dropdown_options)

            if section_data.guide:
                recce_section_guide.append(
                    RecceSectionGuide(
                        name=section_data.guide.name,
                        url=section_data.guide.url,
                        section_id=recce_section.pk,
                        created_by_id=user_id,
                    )
                )
        if save:
            RecceField.objects.bulk_create(recce_fields)
            RecceSectionGuide.objects.bulk_create(recce_section_guide)
            RecceDropDownOption.objects.bulk_create(recce_dropdown_options)
        return recce_sections

    def save_data(self, data: RecceCreateData, user_id: int) -> int:
        recce = self.create_recce(data=data, user_id=user_id)
        self.create_recce_users(recce_users_data=data.recce_users, recce_id=recce.pk)
        self.create_recce_sections(sections_data=data.sections, recce_id=recce.pk, user_id=user_id)
        return recce.pk

    def get_or_create_recce_assignee_role_id(self, org_id: int, user_id: int) -> int:
        role_id = (
            Role.objects.available()
            .annotate(lower_name=Lower("name"))
            .filter(lower_name=ReservedRoleNames.RECCIE_ASSIGNEE.value.lower(), organization_id=org_id)
            .values_list("id", flat=True)
            .first()
        )
        if role_id:
            return role_id
        role = Role(
            name=ReservedRoleNames.RECCIE_ASSIGNEE.value,
            level=ProjectUserRoleLevelChoices.LEVEL_100,
            type=OrganizationType.PMC,
            organization_id=org_id,
            created_by_id=user_id,
            scope=PermissionScope.PROJECT,
            role_type=RoleType.DYNAMIC,
        )
        role.save()
        return role.pk


class RecceFileRepository:
    @staticmethod
    def delete_many(user_id: int, file_ids: list, recce_id: int, project_id: int):
        files = recce_file_get_many(project_id=project_id, recce_id=recce_id, file_ids=file_ids)
        if files[0].recce.status == Recce.StatusChoices.APPROVED:
            raise ApprovedRecceFileDeleteException("Recce is approved, you can not delete any file.")
        CommentHelperService.archive_many(
            data={MicroContextChoices.RECCE_FILE.value: list(files.values_list("id", flat=True))}, user_id=user_id
        )
        files.update(deleted_at=timezone.now(), deleted_by_id=user_id)
