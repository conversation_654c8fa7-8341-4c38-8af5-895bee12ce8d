import decimal
import json
from collections import defaultdict
from datetime import datetime
from functools import partial
from io import Bytes<PERSON>
from typing import Dict, List, Optional, Tuple, Union

import requests
import structlog
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import EmailMultiAlternatives
from django.db.models import Count, F, OuterRef, Prefetch, Q, Subquery
from django.db.models.functions import J<PERSON>NObject
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.module_loading import import_string
from google.cloud._helpers import _to_bytes
from rest_framework.exceptions import ValidationError
from rest_framework.settings import api_settings

from authorization.domain.constants import Permissions
from authorization.domain.services import permission_get_all
from boq.data.choices import BoqElementAction, BoqElementStatus
from boq.data.entities import BoqElementHistoryData
from boq.data.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    BoqElementGuideline,
    BoqElementPreviewFile,
    BoqElementProductionDrawing,
)
from boq.services.status_history import StatusService
from client.data.choices import ClientFieldChoices
from client.data.entities import <PERSON>lient<PERSON>ield
from client.domain.constants import ClientFields, ClientFieldTypes
from client.domain.mappings import CLIENT_FIELD_TO_TYPE_MAPPING, READ_ONLY_CLIENT_FIELDS
from client.domain.services.services import get_client_field_settings, prepare_client_field_settings
from common.choices import FilePathChoices
from common.entities import ObjectStatus
from common.events import Events
from common.events.actions.proposal import (
    ProposalRequestForNewOrderActionData,
    ProposalRequestForOrderChangeActionData,
)
from common.events.proposal import (
    ProposalApprovedEventData,
    ProposalRejectedEventData,
    ProposalRejectedNotifyEventData,
    ProposalSentEventData,
)
from common.events.services import trigger_event
from common.excel_file_generation import (
    create_excel_file,
    excel_data_file_with_no_record,
)
from common.exceptions import BaseValidationError
from common.services import model_update, nested_object_segregation, upload_file
from common.utils import validate_quantity_dimensions
from core.helpers import OrgPermissionHelper
from core.models import (
    FromToOrgMapping,
    Organization,
    OrganizationConfig,
    OrganizationConfigRole,
    OrganizationUser,
    User,
)
from core.organization.domain.entities import OrganizationSectionData
from core.organization.domain.services.organization import prepare_nested_data_for_section_config
from core.selectors import organization_user_fetch_all
from element.data.models import ElementCategory
from microcontext.choices import MicroContextChoices
from order.data.models import VendorOrder
from order.data.selectors.selector_v1 import get_order_number, get_organizations_users_emails, get_shipping_address
from project.data.models import Project, ProjectUser
from project.domain.caches import ProjectCountryConfigCache
from project.domain.helpers import ProjectPermissionHelper
from project.domain.services import get_project_proposal_client
from project.selectors import project_user_fetch_all
from proposal.data.entities import (
    ProposalApproveData,
    ProposalClientFields,
    ProposalPrefillDetails,
    ProposalRequestChangeData,
)
from proposal.data.models import (
    Proposal,
    ProposalDocument,
    ProposalElementGuideline,
    ProposalElementMapping,
    ProposalElementPreviewFile,
    ProposalElementProductionDrawing,
    ProposalEmail,
    ProposalGuidelineAttachment,
    ProposalMetaData,
    ProposalSection,
    ProposalStatusMapping,
)
from proposal.data.selectors import (
    fetch_proposal_document_config,
    get_elements_data,
    get_org_name,
    get_proposal_ref_number,
    proposal_receiver_user,
)
from proposal.domain.constants import Action, ProposalStatus
from proposal.domain.helpers import (
    get_html_context,
    get_html_context_for_new_order,
    get_html_context_for_order_change,
)
from proposal.domain.order_services import ProposalToOrder
from proposal.interface.exceptions import (
    ProposalAlreadyApprovedException,
    ProposalAlreadyRejectedException,
    ProposalElementApprovedNotAllowed,
)
from proposal.interface.serializers import (
    ProposalElementExportSerializer,
    ProposalElementGroupbyCategorySerializer,
)
from rollingbanners.comment_base_service import CommentBaseService
from rollingbanners.hash_id_converter import HashIdConverter
from smtp_email.domain.services import DjangoEmailService
from work_progress_v2.interface.factory_helper import WorkProgressTimelineSync

logger = structlog.getLogger(__name__)
CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


def get_proposal_autoincremented_ref_number(project_id: int):
    proposals_count = (
        Proposal.objects.filter(project_id=project_id)
        .order_by("-created_at")
        .values_list("ref_number", flat=True)
        .first()
    )
    if proposals_count is None:
        return 1
    return proposals_count + 1


def proposal_reject_trigger_event(
    project_id: int, user_id: int, proposal_ref_num: int, vendor_org_id: int, proposal_id: int
):
    logger.info(
        "proposal_reject_trigger_event",
        project_id=project_id,
        user_id=user_id,
        proposal_ref_num=proposal_ref_num,
        vendor_org_id=vendor_org_id,
        proposal_id=proposal_id,
    )
    event_data = ProposalRejectedNotifyEventData(
        project_id=project_id,
        user_id=user_id,
        vendor_org_id=vendor_org_id,
        proposal_ref_num=proposal_ref_num,
        created_at=str(datetime.now()),
        proposal_id=proposal_id,
    )
    logger.info("triggered_event", event_type=Events.PROPOSAL_REJECTED)
    trigger_event(event=Events.PROPOSAL_REJECTED, event_data=event_data)


def proposal_request_for_new_order_trigger_event(
    proposal: Proposal, proposal_request: ProposalRequestChangeData, user_id: int
):
    event_data = ProposalRequestForNewOrderActionData(
        from_name=proposal.proposal_from.name.title(),
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user_id,
        subject=f"Proposal For New Order {proposal.project.job_id}/{proposal.ref_number} | \
            From: {proposal.proposal_from.name}",
        from_email=settings.EMAIL_HOST_USER,
        body=proposal_request.custom_message,
        cc=None,
        bcc=None,
        attachments=(
            [{"name": attachment.name, "url": attachment.url} for attachment in proposal_request.attachments]
            if proposal_request.attachments
            else []
        ),
        receiver_emails=proposal_request.receiver,
    )
    trigger_event(event=Events.PROPOSAL_REQUEST_FOR_CHANGE, event_data=event_data)


def proposal_request_for_order_change_trigger_event(
    proposal: Proposal, proposal_request: ProposalRequestChangeData, user_id: int, old_order_value: decimal.Decimal
):
    event_data = ProposalRequestForOrderChangeActionData(
        from_name=proposal.proposal_from.name.title(),
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user_id,
        subject=f"Proposal For Order change {proposal.project.job_id}/{proposal.order.order_number} | \
            From: {proposal.proposal_from.name}",
        from_email=settings.EMAIL_HOST_USER,
        body=proposal_request.custom_message,
        cc=None,
        bcc=None,
        attachments=(
            [{"name": attachment.name, "url": attachment.url} for attachment in proposal_request.attachments]
            if proposal_request.attachments
            else []
        ),
        receiver_emails=proposal_request.receiver,
        old_order_value=old_order_value,
    )
    trigger_event(event=Events.PROPOSAL_REQUEST_FOR_ORDER_CHANGE, event_data=event_data)


def proposal_approve_for_new_order_trigger_event(
    proposal: Proposal, proposal_approve: ProposalApproveData, user: User, email_data: Dict
):
    event_data = ProposalApprovedEventData(
        order_id=proposal.order_id,
        from_name=proposal.proposal_for.name.title(),
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user.pk,
        subject=email_data["subject"],
        from_email=settings.EMAIL_HOST_USER,
        body=None,
        cc=proposal_approve.cc if proposal_approve.cc else email_data["cc_emails"],
        bcc=(
            email_data["bcc_emails"]
            if email_data["bcc_emails"]
            else []
            if not proposal_approve.to
            else proposal_approve.bcc
        ),
        attachments=(
            [email_data["attachment"]]
            if email_data.get("attachment")
            else [] + proposal_approve.attachments
            if proposal_approve.attachments
            else []
        ),
        receiver_emails=(
            email_data["recipient_emails"]
            if email_data["recipient_emails"]
            else []
            if not proposal_approve.to
            else proposal_approve.to
        ),
        elements=[],
        old_order_value=None,
    )
    trigger_event(event=Events.PROPOSAL_APPROVE_FOR_NEW_ORDER, event_data=event_data)


def proposal_approve_for_order_change_trigger_event(
    proposal: Proposal,
    proposal_approve: ProposalApproveData,
    user: User,
    email_data: Dict,
    elements: List,
    old_order_value: decimal.Decimal,
):
    event_data = ProposalApprovedEventData(
        order_id=proposal.order_id,
        from_name=proposal.proposal_for.name.title(),
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user.pk,
        subject=email_data["subject"],
        from_email=settings.EMAIL_HOST_USER,
        body=None,
        cc=proposal_approve.cc if proposal_approve.cc else email_data["cc_emails"],
        bcc=(
            email_data["bcc_emails"]
            if email_data["bcc_emails"]
            else []
            if not proposal_approve.to
            else proposal_approve.bcc
        ),
        attachments=(
            [email_data["attachment"]]
            if email_data.get("attachment")
            else [] + proposal_approve.attachments
            if proposal_approve.attachments
            else []
        ),
        receiver_emails=proposal_approve.to if proposal_approve.to else email_data["recipient_emails"],
        elements=json.dumps(elements),
        old_order_value=old_order_value,
    )
    trigger_event(event=Events.PROPOSAL_APPROVE_FOR_ORDER_CHANGE, event_data=event_data)


def prepare_proposal_reject_for_new_order_email_data(*, proposal: Proposal):
    cc_emails = []
    org_emails = (
        OrganizationConfig.objects.filter(organization_id=proposal.proposal_from_id)
        .values_list("order_receiver_emails", flat=True)
        .first()
    )
    if org_emails:
        user_emails = org_emails
    else:
        user_emails: OrganizationUser = get_organizations_users_emails(
            organization_id=proposal.proposal_from_id
        ).filter(is_admin=True)
    subject = f"Proposal For New Order Proposal Number : {proposal.project.job_id}/{proposal.ref_number}"

    order_cc_emails = (
        OrganizationConfig.objects.filter(organization_id=proposal.proposal_from_id)
        .values_list("order_cc_emails", flat=True)
        .first()
    )
    if order_cc_emails:
        cc_emails.extend(order_cc_emails)

    cc_role_ids = OrganizationConfigRole.objects.filter(
        organization_config_id=proposal.proposal_from_id, is_order_cc_role=True
    ).values_list("role_id", flat=True)

    cc_role_user_emails = set(
        ProjectUser.objects.filter(project_id=proposal.project_id, role_id__in=cc_role_ids)
        .select_related("user")
        .values_list("user__email", flat=True)
    )
    cc_emails = list(set(cc_emails).union(cc_role_user_emails))
    client_cc_emails = (
        ProposalEmail.objects.filter(proposal_id=proposal.id).values_list("to_receiver", flat=True).first()
    )
    if client_cc_emails:
        cc_emails.extend(client_cc_emails)
    return {"subject": subject, "recipient_emails": user_emails, "cc_emails": cc_emails, "bcc_emails": []}


def proposal_reject_for_new_order_trigger_event(*, proposal: Proposal, user: User):
    logger.info("proposal_reject_for_new_order_trigger_event", proposal_id=proposal.pk, user_id=user.pk)
    email_data = prepare_proposal_reject_for_new_order_email_data(proposal=proposal)
    logger.info("email data prepared")
    event_data = ProposalRejectedEventData(
        from_name=proposal.proposal_for.name.title(),
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user.pk,
        subject=f"Proposal For New Order Rejected | Proposal Number:{proposal.project.job_id}/{proposal.ref_number}",
        from_email=settings.EMAIL_HOST_USER,
        body=None,
        # cc=list(email_data["cc_emails"]),
        cc=[user.email],
        bcc=list(email_data["bcc_emails"]),
        attachments=[],
        # receiver_emails=list(email_data["recipient_emails"]),
        receiver_emails=[proposal.created_by.email],
    )
    trigger_event(event=Events.PROPOSAL_REJECT_FOR_NEW_ORDER, event_data=event_data)


def proposal_reject_for_order_change_trigger_event(*, proposal: Proposal, user: User):
    logger.info("proposal_reject_for_order_change_trigger_event", proposal_id=proposal.pk, user_id=user.pk)
    email_data = prepare_proposal_reject_for_new_order_email_data(proposal=proposal)
    logger.info("email data prepared")
    event_data = ProposalRejectedEventData(
        from_name=proposal.proposal_for.name.title() if proposal.proposal_for.name else None,
        shipping_address=proposal.shipping_address,
        proposal_id=proposal.pk,
        user_id=user.pk,
        subject=f"Proposal For Order Change Rejected | Proposal Number:{proposal.project.job_id}/{proposal.ref_number}",
        from_email=settings.EMAIL_HOST_USER,
        body=None,
        # cc=list(email_data["cc_emails"]) + [user.email],
        cc=[user.email],
        bcc=list(email_data["bcc_emails"]),
        attachments=[],
        receiver_emails=[proposal.created_by.email],
    )
    trigger_event(event=Events.PROPOSAL_REJECT_FOR_ORDER_CHANGE, event_data=event_data)


def bulk_unlatch_elements(element_ids: list[int], unlatched_by_id: int, proposal_id: int):
    proposal_element_ids = ProposalElementMapping.objects.filter(
        proposal_id=proposal_id, element_id__in=element_ids
    ).values_list("id", flat=True)
    CommentHelperService.archive_many(
        data={MicroContextChoices.PROPOSAL_ITEM.value: proposal_element_ids}, user_id=unlatched_by_id
    )
    return ProposalElementMapping.objects.filter(proposal_id=proposal_id, element_id__in=element_ids).update(
        deleted_at=timezone.now(), deleted_by_id=unlatched_by_id
    )


def insert_tnc_attachments(proposal: Proposal, tnc_attachments: list, uploaded_by_id: int):
    tnc_documents = []
    for tnc_attachment in tnc_attachments:
        if tnc_attachment.get("id") is None:
            tnc_documents.append(
                ProposalDocument(
                    proposal=proposal,
                    source=ProposalDocument.DocumentSource.TNC,
                    file_url=tnc_attachment["file_url"],
                    file_name=tnc_attachment["file_name"],
                    type=tnc_attachment["type"],
                    uploaded_by_id=uploaded_by_id,
                )
            )
    ProposalDocument.objects.bulk_create(objs=tnc_documents)


def insert_supporting_documents(proposal: Proposal, supporting_documents: list, uploaded_by_id: int):
    proposal_documents = []
    for supporting_document in supporting_documents:
        if supporting_document.get("id") is None:
            proposal_documents.append(
                ProposalDocument(
                    proposal=proposal,
                    source=ProposalDocument.DocumentSource.SUPPORTING_DOCUMENT,
                    file_url=supporting_document["file_url"],
                    file_name=supporting_document["file_name"],
                    type=ProposalDocument.DocumentType.DOC,
                    uploaded_by_id=uploaded_by_id,
                )
            )
    ProposalDocument.objects.bulk_create(objs=proposal_documents)


def get_proposal_element_ids(proposal_id: int):
    elements = list(
        ProposalElementMapping.objects.filter(proposal_id=proposal_id, deleted_at__isnull=True)
        .all()
        .values_list("element_id", flat=True)
    )
    return elements


def delete_proposal(proposal_id: int, deleted_by_id: int):
    proposal = Proposal.objects.get(pk=proposal_id)
    proposal.soft_delete(user_id=deleted_by_id)
    element_ids = list(
        ProposalElementMapping.objects.filter(proposal_id=proposal_id).values_list("element_id", flat=True)
    )
    CommentHelperService.archive(
        context=MicroContextChoices.PROPOSAL.value, context_id=proposal_id, user_id=deleted_by_id
    )
    bulk_unlatch_elements(element_ids=element_ids, unlatched_by_id=deleted_by_id, proposal_id=proposal_id)


def update_proposal_element_status(proposal_id: int, element_status: str):
    element_ids = list(
        ProposalElementMapping.objects.filter(proposal_id=proposal_id, deleted_at__isnull=True).values_list(
            "element_id", flat=True
        )
    )
    BoqElement.objects.filter(id__in=element_ids).update(element_status=element_status, updated_at=timezone.now())


def insert_proposal_status(proposal_id: int, action: str, created_by_id: int) -> str:
    logger.info("insert_proposal_status", proposal_id=proposal_id, action=action, created_by_id=created_by_id)
    status = None
    old_status = None
    proposal_status = (
        ProposalStatusMapping.objects.select_related("proposal")
        .filter(proposal_id=proposal_id)
        .order_by("-created_at")
        .first()
    )
    if proposal_status:
        old_status = proposal_status.status
        logger.info("old status", old_status=proposal_status.status)
    if action == Action.SEND:
        status = ProposalStatusMapping.ProposalStatus.PENDING_APPROVAL
    elif action == Action.CANCEL:
        proposal_status = ProposalStatusMapping.objects.filter(proposal_id=proposal_id).order_by("-created_at").first()
        if proposal_status.status == ProposalStatusMapping.ProposalStatus.DRAFT:
            delete_proposal(proposal_id=proposal_id, deleted_by_id=created_by_id)
            logger.info("Proposal has been deleted", proposal_id=proposal_id, last_status=proposal_status.status)
            return "DELETED"
        else:
            status = ProposalStatusMapping.ProposalStatus.DISCARDED
    elif action == Action.APPROVE:
        if ProposalStatusMapping.ProposalStatus.REJECTED == old_status:
            raise ProposalAlreadyApprovedException(
                {api_settings.NON_FIELD_ERRORS_KEY: "Proposal already Rejected, Cannot be Approved"}
            )

        if ProposalStatusMapping.ProposalStatus.APPROVED == old_status:
            raise ProposalAlreadyApprovedException(
                {api_settings.NON_FIELD_ERRORS_KEY: "Proposal already Approved, Cannot be Approved Again"}
            )

        status = ProposalStatusMapping.ProposalStatus.APPROVED
    elif action == Action.REJECT:
        if ProposalStatusMapping.ProposalStatus.APPROVED == old_status:
            raise ProposalAlreadyRejectedException(
                {api_settings.NON_FIELD_ERRORS_KEY: "Proposal already Approved, Cannot be Rejected"}
            )

        if ProposalStatusMapping.ProposalStatus.REJECTED == old_status:
            raise ProposalAlreadyRejectedException(
                {api_settings.NON_FIELD_ERRORS_KEY: "Proposal already Rejected, Cannot be Rejected Again"}
            )

        status = ProposalStatusMapping.ProposalStatus.REJECTED
        proposal_ref_number = get_proposal_ref_number(
            project_id=proposal_status.proposal.project_id, ref_number=proposal_status.proposal.ref_number
        )

        on_commit(
            partial(
                proposal_reject_trigger_event,
                project_id=proposal_status.proposal.project_id,
                user_id=created_by_id,
                proposal_ref_num=proposal_ref_number,
                vendor_org_id=proposal_status.proposal.proposal_from_id,
                proposal_id=proposal_id,
            )
        )

    else:
        status = ProposalStatusMapping.ProposalStatus.DRAFT
    proposal_status_mapping = ProposalStatusMapping(proposal_id=proposal_id, status=status, created_by_id=created_by_id)
    proposal_status_mapping.full_clean()
    proposal_status_mapping.save()
    logger.info("Proposal Status for have been updated", proposal_id=proposal_id, status=status)
    return status


def bulk_latch_elements(element_ids: list[int], latched_by_id: int, proposal_id: int):
    proposal_element_mappings = []
    for element_id in element_ids:
        proposal_element_mappings.append(
            ProposalElementMapping(proposal_id=proposal_id, element_id=element_id, created_by_id=latched_by_id)
        )
    return ProposalElementMapping.objects.bulk_create(objs=proposal_element_mappings)


def proposal_element_seggregation(add_elements):
    to_create = []
    to_latch = []
    for add_element in add_elements:
        if not getattr(add_element, "id"):
            to_create.append(add_element)
        else:
            to_latch.append(add_element)
    return to_create, to_latch


def delete_boq_related_data(*, boq_element_ids: List[int]):
    BoqElementPreviewFile.objects.filter(boq_element_id__in=boq_element_ids).delete()
    BoqElementGuideline.objects.filter(boq_element_id__in=boq_element_ids).delete()
    BoqElementProductionDrawing.objects.filter(boq_element_id__in=boq_element_ids).delete()


def get_proposal_details(org_id: int, proposal_id: int):
    prefetch_sections = Prefetch(
        "sections",
        ProposalSection.objects.filter(deleted_at__isnull=True)
        .order_by("created_at")
        .annotate(element_count=Count("proposal_elements", Q(proposal_elements__deleted_at__isnull=True))),
    )
    elements_subquery = ProposalElementMapping.objects.filter(id=OuterRef("id"), deleted_at__isnull=True).annotate(
        element_data=JSONObject(
            client_rate="element__client_rate",
            quantity="element__quantity",
            service_charge_percent="element__service_charge_percent",
            is_service_charge_with_base_amount="element__is_service_charge_with_base_amount",
            tax_percent="element__tax_percent",
            quantity_dimensions="element__quantity_dimensions",
        )
    )

    try:
        proposal = (
            Proposal.objects.filter(id=proposal_id, deleted_at__isnull=True)
            .select_related("order", "project", "proposal_for", "proposal_from", "project__store", "meta_data")
            .prefetch_related("order__purchase_orders")
            .annotate(
                comment_count=CommentHelperService.get_count(
                    context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
                )
            )
            .prefetch_related(
                "documents",
                "proposal_for__addresses",
                "proposal_from__vendor",
                Prefetch(
                    "proposal_element_mappings",
                    queryset=ProposalElementMapping.objects.filter(deleted_at__isnull=True)
                    .order_by("created_at")
                    .select_related("item_type", "category", "element")
                    .prefetch_related(
                        Prefetch("guidelines", ProposalElementGuideline.objects.available().all()),
                        Prefetch("production_drawings", ProposalElementProductionDrawing.objects.available().all()),
                        Prefetch("preview_files", ProposalElementPreviewFile.objects.available().all()),
                        Prefetch("guidelines__attachments", ProposalGuidelineAttachment.objects.available().all()),
                    )
                    .annotate(element_data=Subquery(elements_subquery.values("element_data")[:1]))
                    .annotate(
                        comment_count=CommentHelperService.get_count(
                            context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL_ITEM.name, org_id=org_id
                        ),
                        last_approved_quantity=F("element_data__quantity"),
                        last_approved_client_rate=F("element_data__client_rate"),
                        last_approved_service_charge_percent=F("element_data__service_charge_percent"),
                        last_approved_is_service_charge_with_base_amount=F(
                            "element_data__is_service_charge_with_base_amount"
                        ),
                        last_approved_tax_percent=F("element_data__tax_percent"),
                        last_approved_quantity_dimensions=F("element_data__quantity_dimensions"),
                    ),
                ),
                Prefetch("statuses", queryset=ProposalStatusMapping.objects.order_by("-created_at")),
                Prefetch(
                    "documents",
                    queryset=ProposalDocument.objects.filter(source="TNC", deleted_at__isnull=True),
                    to_attr="tnc_attachments",
                ),
                Prefetch(
                    "documents",
                    queryset=ProposalDocument.objects.filter(source="SUPPORTING_DOCUMENT", deleted_at__isnull=True),
                    to_attr="supporting_documents",
                ),
                prefetch_sections,
            )
            .get()
        )
    except ObjectDoesNotExist:
        raise Exception("Proposal Does Not Exist")
    setattr(
        proposal,
        "document_config",
        fetch_document_config_for_proposal_detail(
            country_id=proposal.proposal_from.country_id, proposal_id=proposal.pk
        ),
    )
    return proposal


def get_element_details(org_id: int, proposal_id: int, element_id: int):
    element_details = (
        ProposalElementMapping.objects.filter(element__id=element_id, proposal__id=proposal_id, deleted_at__isnull=True)
        .select_related("item_type", "category", "proposal")
        .prefetch_related(
            Prefetch("guidelines", ProposalElementGuideline.objects.available().all()),
            Prefetch("production_drawings", ProposalElementProductionDrawing.objects.available().all()),
            Prefetch("preview_files", ProposalElementPreviewFile.objects.available().all()),
            Prefetch("guidelines__attachments", ProposalGuidelineAttachment.objects.available().all()),
        )
        .annotate_in_progress()
        .annotate_is_progress_update_allowed()
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL_ITEM.name, org_id=org_id
            ),
            last_approved_quantity=F("element__quantity"),
            last_approved_client_rate=F("element__client_rate"),
            last_approved_service_charge_percent=F("element__service_charge_percent"),
            budget_rate=F("element__budget_rate"),
            proposal_from_id=F("proposal__proposal_from_id"),
        )
        .first()
    )

    return element_details


# TODO:  TO remove this function, just for compatibility with App
def get_proposal_details_from_ref_number(project_id: int, ref_number: int, org_id: int):
    elements_subquery = ProposalElementMapping.objects.filter(id=OuterRef("id"), deleted_at__isnull=True).annotate(
        element_data=JSONObject(
            client_rate="element__client_rate",
            quantity="element__quantity",
            service_charge_percent="element__service_charge_percent",
        )
    )

    try:
        proposals = (
            Proposal.objects.filter(project_id=project_id, ref_number=ref_number, deleted_at__isnull=True)
            .select_related("order", "project", "proposal_for", "proposal_from")
            .prefetch_related("order__purchase_orders")
            .annotate(
                comment_count=CommentHelperService.get_count(
                    context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
                )
            )
            .prefetch_related(
                "documents",
                "proposal_for__addresses",
                "proposal_from__vendor",
                Prefetch(
                    "proposal_element_mappings",
                    queryset=ProposalElementMapping.objects.filter(deleted_at__isnull=True)
                    .order_by("created_at")
                    .select_related("item_type", "category")
                    .prefetch_related(
                        Prefetch("guidelines", ProposalElementGuideline.objects.available().all()),
                        Prefetch("production_drawings", ProposalElementProductionDrawing.objects.available().all()),
                        Prefetch("preview_files", ProposalElementPreviewFile.objects.available().all()),
                        Prefetch("guidelines__attachments", ProposalGuidelineAttachment.objects.available().all()),
                    )
                    .annotate(element_data=Subquery(elements_subquery.values("element_data")[:1]))
                    .annotate(
                        comment_count=CommentHelperService.get_count(
                            context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL_ITEM.name, org_id=org_id
                        ),
                        last_approved_quantity=F("element_data__quantity"),
                        last_approved_client_rate=F("element_data__client_rate"),
                        last_approved_service_charge_percent=F("element_data__service_charge_percent"),
                    ),
                ),
                Prefetch("statuses", queryset=ProposalStatusMapping.objects.order_by("-created_at")),
                Prefetch(
                    "documents",
                    queryset=ProposalDocument.objects.filter(source="TNC", deleted_at__isnull=True),
                    to_attr="tnc_attachments",
                ),
                Prefetch(
                    "documents",
                    queryset=ProposalDocument.objects.filter(source="SUPPORTING_DOCUMENT", deleted_at__isnull=True),
                    to_attr="supporting_documents",
                ),
            )
            .last()
        )

    except ObjectDoesNotExist:
        raise Exception("Proposal Does Not Exist")
    if proposals:
        setattr(
            proposals,
            "document_config",
            fetch_document_config_for_proposal_detail(
                country_id=proposals.proposal_from.country_id, proposal_id=proposals.pk
            ),
        )
    return proposals


def prefetch_proposal_details(vendor_organization_id: int, user_id: int, project_id: int):
    client = get_project_proposal_client(project_id=project_id, organization_id=vendor_organization_id)
    created_by = User.objects.get(pk=user_id)
    vendor = Organization.objects.get(pk=vendor_organization_id)
    try:
        vendor.vendor
    except ObjectDoesNotExist:
        raise ValidationError(
            {api_settings.NON_FIELD_ERRORS_KEY: "Not a Vendor. Kindly Connect with Support to get this resolved"}
        )

    return (
        client.hash_id,
        client.name,
        created_by,
        vendor.vendor,
        client.addresses.all(),
        ProposalStatusMapping.ProposalStatus.DRAFT.title(),
    )


def get_approver_permitted_actions(user: User, receiver: User, project_id: int) -> bool:
    permission = Permissions.CAN_APPROVE_PROPOSAL
    is_user_permitted = ProjectPermissionHelper.has_permission(project_id=project_id, user=user, permission=permission)
    if receiver:
        return user.pk == receiver.pk or is_user_permitted
    else:
        return is_user_permitted


def get_proposal_actions(user: User, proposal: Proposal, project_id: int) -> list[str]:
    org_id = user.org_id
    actions = []
    # vendor view
    if org_id == proposal.proposal_from_id:
        actions.append(Action.EDIT)
        permission = Permissions.CAN_APPROVE_CLIENT_PROPOSAL
        is_user_permitted = ProjectPermissionHelper.has_permission(
            user=user, project_id=project_id, permission=permission
        ) and OrgPermissionHelper.has_permission(user=user, permission=permission)
        if is_user_permitted:
            actions.append(Action.APPROVE)
            actions.append(Action.REJECT)

    # client view
    elif org_id == proposal.proposal_for_id:
        is_approve_permitted = get_approver_permitted_actions(
            user=user, receiver=proposal.created_by, project_id=project_id
        )
        if is_approve_permitted:
            actions.append(Action.APPROVE)
            actions.append(Action.REJECT)

        org_permissions = permission_get_all(org_id=proposal.proposal_from_id)
        if Permissions.CAN_RESTRICT_CLIENT_EDIT_PROPOSAL not in org_permissions:
            actions.append(Action.EDIT)
    return actions


def update_status_update_time(proposal_id: int, user_id: int):
    proposal = Proposal.objects.get(pk=proposal_id)
    proposal.updated_at = timezone.now()
    proposal.updated_by_id = user_id
    proposal.full_clean()
    proposal.save()
    return proposal.updated_at


def permitted_user_email_list(*, project_users: List[ProjectUser], project_id: int, org_id: int) -> List[str]:
    email_list = []
    for user in project_users:
        if ProjectPermissionHelper.check_user_permission(
            project_id=project_id,
            user_id=user.user.pk,
            permissions_to_check=[Permissions.CAN_APPROVE_PROPOSAL, Permissions.CAN_VIEW_CLIENT_RATE],
            org_id=org_id,
        ):
            email_list.append(user.user.email)
    return email_list


def proposal_approve_default_recipient(
    *, project_id: int, order_number: str, proposal_number: str, email_list: List[str]
) -> str:
    if proposal_number:
        proposal = (
            Proposal.objects.filter(project_id=project_id, ref_number=proposal_number).order_by("-version").first()
        )
        mapping = (
            ProposalStatusMapping.objects.filter(proposal_id=proposal.id, status=ProposalStatus.APPROVED)
            .order_by("-created_at")
            .select_related("created_by")
            .first()
        )
        if mapping and mapping.created_by.email in email_list:
            return mapping.created_by.email
        return email_list[0]

    if order_number:
        order = (
            VendorOrder.objects.filter(project_id=project_id, order_number=order_number)
            .select_related("issued_by")
            .first()
        )
        if order and order.issued_by.email in email_list:
            return order.issued_by.email
        return email_list[0]

    if not order_number and not proposal_number:
        return email_list[0]


def proposal_approve_default_recipient_v2(*, project_id: int, email_list: List[str]) -> str:
    proposal_id = (
        Proposal.objects.filter(project_id=project_id, status=ProposalStatus.PENDING_APPROVAL)
        .select_related("created_by")
        .order_by("-created_at")
        .values_list("id", flat=True)
        .first()
    )
    email = ProposalEmail.objects.filter(proposal_id=proposal_id).first()
    if email and email.to_receiver[0] in email_list:
        return email.to_receiver[0]
    return email_list[0]


def fetch_previous_proposal_receivers(project_id: int, proposal_from: int, proposal_for: int) -> List[str]:
    proposals = Proposal.objects.filter(
        project_id=project_id, proposal_from=proposal_from, proposal_for=proposal_for
    ).select_related("received_by")
    previous_receiver_emails = set()
    for proposal in proposals:
        if proposal.received_by:
            previous_receiver_emails.add(proposal.received_by.email)

    return list(previous_receiver_emails)


def fetch_client_poc_email(project_id: int, org_from_id: int, org_to_id: int) -> List[dict]:
    client_poc_emails = []
    vendor_client_mapping = FromToOrgMapping.objects.filter(org_to=org_to_id, org_from=org_from_id).first()

    if vendor_client_mapping.client_poc_email:
        client_user = User.objects.filter(email=vendor_client_mapping.client_poc_email, is_active=True).first()
        if client_user and client_user.org_id == org_from_id:
            client_poc_emails.append({"email": vendor_client_mapping.client_poc_email, "invite_status": "invited"})
        elif client_user is None:
            country_code = ""
            national_number = ""
            if vendor_client_mapping.client_poc_phone_number:
                country_code = f"+{str(vendor_client_mapping.client_poc_phone_number.country_code)}"
                national_number = str(vendor_client_mapping.client_poc_phone_number.national_number)

            client_poc_emails.append(
                {
                    "email": vendor_client_mapping.client_poc_email,
                    "invite_status": "not_invited",
                    "name": vendor_client_mapping.client_poc_name if vendor_client_mapping.client_poc_name else "",
                    "phone": {
                        "country_code": country_code,
                        "number": national_number,
                    },
                }
            )

    previous_receiver_emails = fetch_previous_proposal_receivers(
        project_id=project_id, proposal_from=org_to_id, proposal_for=org_from_id
    )

    for previous_receiver_email in previous_receiver_emails:
        if vendor_client_mapping.client_poc_email and vendor_client_mapping.client_poc_email != previous_receiver_email:
            client_poc_emails.append({"email": previous_receiver_email, "invite_status": "invited"})

    return client_poc_emails


def make_email_dict_list(email_list: List[str], client_poc_email_list: List[dict]) -> List[dict]:
    emails_dict_list = []
    for email in email_list:
        emails_dict_list.append({"email": email, "invite_status": "invited"})

    if len(client_poc_email_list) != 0:
        emails_dict_list[1:1] = client_poc_email_list
    return emails_dict_list


def proposal_recipient_email_list(*, org_from_id: int, org_to_id: int, project_id: int) -> List[dict]:
    user_ids = User.objects.filter(org_id=org_from_id).values_list("id", flat=True)
    project_users: List[ProjectUser] = (
        project_user_fetch_all(project_id=project_id).filter(user_id__in=user_ids).select_related("user")
    )
    email_list = permitted_user_email_list(project_users=project_users, project_id=project_id, org_id=org_from_id)
    if not email_list:
        email_list = list(
            organization_user_fetch_all(org_id=org_from_id)
            .filter(is_admin=True, user__email__isnull=False)
            .select_related("user")
            .values_list("user__email", flat=True)
        )

    client_poc_email_list: List[dict] = fetch_client_poc_email(
        project_id=project_id, org_from_id=org_from_id, org_to_id=org_to_id
    )

    if email_list:
        default_email = proposal_approve_default_recipient_v2(project_id=project_id, email_list=email_list)
        if default_email in email_list:
            email_list.remove(default_email)
        email_list.insert(0, default_email)
        return make_email_dict_list(email_list, client_poc_email_list)

    return client_poc_email_list


def serializer_element_data(fields: list, elements: List[BoqElement]) -> Dict:
    # TODO: Write test cases for this function
    return {
        "sheet_title": "Elements",
        "sheet_data": (
            ProposalElementExportSerializer(elements, many=True, fields=fields).data
            if elements
            else excel_data_file_with_no_record(sheet_name="Elements")
        ),
        "with_serial_number": bool(elements),
        "hyperlink-data": {"item_name": "public_url"},
    }


def serialize_and_group_element_data_by_category(*, grouped_elements: List[BoqElement]) -> Dict:
    # TODO: Write test cases for this function
    return {
        "sheet_title": "Summary",
        "sheet_data": (
            ProposalElementGroupbyCategorySerializer(grouped_elements, many=True).data
            if grouped_elements
            else excel_data_file_with_no_record(sheet_name="Summary")
        ),
        "with_serial_number": bool(grouped_elements),
    }


def category_wise_group_boq_elements(*, elements: List[BoqElement], category_mapping: Dict) -> List[Dict]:
    # TODO: Write test cases for this function
    category_wise_amount_grouping = {}
    for element in elements:
        category_name = category_mapping[element.category_id].name
        if category_name in category_wise_amount_grouping:
            category_wise_amount_grouping[category_name] += element.quantity * element.client_rate
        else:
            category_wise_amount_grouping[category_name] = element.quantity * element.client_rate
    return ({"Category": category, "Amount": amount} for category, amount in category_wise_amount_grouping.items())


def get_grouped_boq_elements(*, elements: List[BoqElement]) -> List[Dict]:
    category_mapping = ElementCategory.objects.in_bulk()
    return category_wise_group_boq_elements(elements=elements, category_mapping=category_mapping)


def generate_and_upload_excel_file(ref_number: str, fields: list, boq_elements: List[BoqElement]) -> str:
    to_insert_excel_data = []
    to_insert_excel_data.append(serializer_element_data(fields=fields, elements=boq_elements))
    to_insert_excel_data.append(
        serialize_and_group_element_data_by_category(grouped_elements=get_grouped_boq_elements(elements=boq_elements))
    )
    excel_data = create_excel_file(data=to_insert_excel_data)
    data = _to_bytes(excel_data, encoding="utf-8")
    string_buffer = BytesIO(data)
    attachment_url = upload_file(
        file=string_buffer,
        filename=f"{ref_number}_element.xls",
        destination_blob_name=FilePathChoices.SEND_PROPOSAL_IN_MAIL_ATTACHMENT,
    )
    return attachment_url


def get_excel_attachments(
    *, proposal_ref_number: str, proposal_id: int, compant_name: str, attachments: List = None
) -> List[Dict]:
    # TODO: Write test cases for this function
    attachment = {
        "name": f"Proposal_{proposal_ref_number}_{compant_name}.xls",
        "url": generate_and_upload_excel_file(
            ref_number=proposal_ref_number,
            fields=["item_name", "description", "item_code", "item_type", "client_rate", "Quantity", "UOM", "Amount"],
            boq_elements=get_elements_data(proposal_id=proposal_id),
        ),
    }
    if attachments:
        attachments.append(attachment)
    else:
        attachments = [attachment]
    return attachments


def get_excel_attachments_v2(
    *,
    proposal_ref_number: str,
    proposal_id: int,
    compant_name: str,
    elements: List[ProposalElementMapping],
    attachments: List = None,
) -> List[Dict]:
    # TODO: Write test cases for this function
    attachment = {
        "name": f"Proposal_{proposal_ref_number}_{compant_name}.xls",
        "url": generate_and_upload_excel_file(
            ref_number=proposal_ref_number,
            fields=[
                "item_name",
                "description",
                "brand_make",
                "item_code",
                "item_type",
                "client_rate",
                "Quantity",
                "UOM",
                "HSN",
                "tax_percent",
                "Amount",
            ],
            boq_elements=elements,
        ),
    }
    if attachments:
        attachments.append(attachment)
    else:
        attachments = [attachment]
    return attachments


def send_proposal_email(
    *,
    proposal: Proposal,
    user: User,
    attachments: List,
    subject: str,
    body: str,
    receiver: str,
    elements: List,
    cc: str = None,
    bcc: str = None,
):
    proposal_ref_number = get_proposal_ref_number(project_id=proposal.project_id, ref_number=proposal.ref_number)
    compant_name = get_org_name(org_id=proposal.proposal_for_id)
    receiver_user = proposal_receiver_user(receiver_email=receiver)
    currency = ProjectCountryConfigCache.get(instance_id=proposal.project_id).currency

    ProposalEmailService().send_email(
        proposal_id=proposal.pk,
        subject=subject,
        to=[receiver],
        cc=cc,
        bcc=bcc,
        from_name=proposal.proposal_from.name.title() if proposal.proposal_from.name else None,
        from_email=settings.EMAIL_HOST_USER,
        body=body,
        context=get_html_context(
            proposal_ref_number=proposal_ref_number,
            proposal=proposal,
            user=user,
            body=body,
            elements=elements,
            compant_name=compant_name,
            receiver_user=receiver_user,
            currency_symbol=currency.symbol,
        ),
        attachments=get_excel_attachments(
            proposal_ref_number=proposal_ref_number,
            proposal_id=proposal.pk,
            compant_name=compant_name,
            attachments=attachments,
        ),
        html_email_template_name="proposal/proposal_send.html",
        created_by_id=user.pk,
    )
    on_commit(
        partial(
            proposal_sent_trigger_event,
            project_id=proposal.project.id,
            user=user,
            proposal_ref_num=proposal_ref_number,
            proposal_id=proposal.pk,
            total_items=len(elements),
            amount=proposal.amount,
        )
    )


def send_proposal_email_for_new_order(
    *,
    proposal: Proposal,
    user: User,
    attachments: List,
    subject: str,
    body: str,
    receiver: str,
    cc: str = None,
    bcc: str = None,
):
    proposal_ref_number = get_proposal_ref_number(project_id=proposal.project_id, ref_number=proposal.ref_number)
    compant_name = get_org_name(org_id=proposal.proposal_for_id)
    receiver_user = proposal_receiver_user(receiver_email=receiver)
    elements = ProposalElementMapping.objects.filter(
        proposal_id=proposal.pk, object_status__in=[ObjectStatus.ADD, ObjectStatus.UPDATE, ObjectStatus.DELETE]
    ).all()
    currency = ProjectCountryConfigCache.get(instance_id=proposal.project_id).currency

    ProposalEmailService().send_email(
        proposal_id=proposal.pk,
        subject=subject,
        to=[receiver],
        cc=cc,
        bcc=bcc,
        from_name=proposal.proposal_from.name.title() if proposal.proposal_from.name else None,
        from_email=settings.EMAIL_HOST_USER,
        body=body,
        context=get_html_context_for_new_order(
            proposal_ref_number=proposal_ref_number,
            proposal=proposal,
            user=user,
            body=body,
            elements=elements,
            compant_name=compant_name,
            receiver_user=receiver_user,
            currency_symbol=currency.symbol,
        ),
        attachments=get_excel_attachments_v2(
            proposal_ref_number=proposal_ref_number,
            proposal_id=proposal.pk,
            compant_name=compant_name,
            attachments=attachments,
            elements=elements,
        ),
        html_email_template_name="proposal/proposal_send.html",
        created_by_id=user.pk,
    )
    on_commit(
        partial(
            proposal_sent_trigger_event,
            project_id=proposal.project.id,
            user=user,
            proposal_ref_num=proposal_ref_number,
            proposal_id=proposal.pk,
            total_items=len(elements),
            amount=proposal.amount,
        )
    )


def send_proposal_email_for_order_change(
    *,
    proposal: Proposal,
    user: User,
    attachments: List,
    subject: str,
    body: str,
    receiver: str,
    cc: str = None,
    bcc: str = None,
):
    proposal_ref_number = get_proposal_ref_number(project_id=proposal.project_id, ref_number=proposal.ref_number)
    order_number = get_order_number(project_id=proposal.project_id, ref_number=proposal.ref_number)
    compant_name = get_org_name(org_id=proposal.proposal_for_id)
    order_from = get_org_name(org_id=proposal.proposal_from_id)
    receiver_user = proposal_receiver_user(receiver_email=receiver)
    elements = ProposalElementMapping.objects.filter(
        proposal_id=proposal.pk, object_status__in=[ObjectStatus.ADD, ObjectStatus.UPDATE, ObjectStatus.DELETE]
    ).all()
    added_elements, updated_elements, deleted_elements = nested_object_segregation(docs_list=elements)
    currency = ProjectCountryConfigCache.get(instance_id=proposal.project_id).currency

    ProposalEmailService().send_email(
        proposal_id=proposal.pk,
        subject=subject,
        to=[receiver],
        cc=cc,
        bcc=bcc,
        from_name=proposal.proposal_from.name.title() if proposal.proposal_from.name else None,
        from_email=settings.EMAIL_HOST_USER,
        body=body,
        context=get_html_context_for_order_change(
            order_number=order_number,
            order_from=order_from,
            proposal=proposal,
            user=user,
            body=body,
            added_items=added_elements,
            updated_items=updated_elements,
            deleted_items=deleted_elements,
            compant_name=compant_name,
            receiver_user=receiver_user,
            currency_symbol=currency.symbol,
        ),
        attachments=get_excel_attachments_v2(
            proposal_ref_number=proposal_ref_number,
            proposal_id=proposal.pk,
            compant_name=compant_name,
            attachments=attachments,
            elements=elements,
        ),
        html_email_template_name="proposal/proposal_send.html",
        created_by_id=user.pk,
    )
    on_commit(
        partial(
            proposal_sent_trigger_event,
            project_id=proposal.project.id,
            user=user,
            proposal_ref_num=proposal_ref_number,
            proposal_id=proposal.pk,
            total_items=len(elements),
            amount=proposal.amount,
        )
    )


def proposal_dates_update(
    *, proposal: Proposal, user_id: int, start_date: datetime, due_date: datetime, shipping_address: str
) -> Proposal:
    logger.info(
        "proposal_dates_update",
        proposal_id=proposal.pk,
        user_id=user_id,
    )
    proposal, _, _ = model_update(
        instance=proposal,
        updated_by_id=user_id,
        data={"start_date": start_date, "due_date": due_date, "shipping_address": shipping_address},
        fields=["start_date", "due_date", "shipping_address"],
    )
    logger.info("proposal_dates_updated")
    return proposal


class ProposalEmailService(DjangoEmailService):
    @staticmethod
    def attach_attachments(attachments: List, email_message: EmailMultiAlternatives):
        for attachment in attachments:
            response = requests.get(attachment.get("url"))
            email_message.attach(attachment.get("name"), response.content, "application/vnd.ms-excel")
        return email_message

    def send_email(
        self,
        proposal_id: int,
        subject: str,
        to: List,
        cc: List,
        bcc: List,
        from_email: str,
        from_name: str = "RDash",
        body: str = None,
        context: Union[Dict, None] = None,
        attachments: Union[List, None] = [],
        html_email_template_name: str = None,
        created_by_id: int = None,
    ):
        proposal_email = ProposalEmail()
        proposal_email.cc_receiver = cc
        proposal_email.subject = "".join(subject.splitlines())
        proposal_email.bcc_receiver = bcc
        proposal_email.proposal_id = proposal_id
        proposal_email.to_receiver = list(to)
        proposal_email.files = [attachment.get("url") for attachment in attachments]
        proposal_email.created_at = timezone.now()
        proposal_email.full_clean()
        proposal_email.save()

        return super().send_email(
            subject=subject,
            body=body,
            to=list(to),
            cc=cc,
            bcc=bcc,
            from_name=from_name,
            from_email=from_email,
            attachments=attachments,
            context=context,
            html_email_template_name=html_email_template_name,
            created_by_id=created_by_id,
        )


def proposal_sent_trigger_event(
    project_id: int, user: User, proposal_ref_num: int, proposal_id: int, total_items: int, amount: float
):
    client_org_id = get_project_proposal_client(project_id=project_id, organization_id=user.org_id).id

    event_data = ProposalSentEventData(
        project_id=project_id,
        user_id=user.id,
        client_org_id=client_org_id,
        proposal_ref_num=proposal_ref_num,
        org_id=user.org_id,
        created_at=str(datetime.now()),
        proposal_id=proposal_id,
        total_items=total_items,
        amount=str(round(amount, 2)),
    )
    trigger_event(event=Events.PROPOSAL_SENT, event_data=event_data)


def proposal_approve_v2(
    *, proposal_id: int, user: User, org_id: int, project_id: int, proposal_approve_data: ProposalApproveData
) -> Union[int, None]:
    from order.services import order_cancel_email_trigger

    logger.info(
        "proposal_approve_v2 started", proposal_id=proposal_id, user_id=user.pk, org_id=org_id, project_id=project_id
    )

    for element in proposal_approve_data.elements:
        validate_quantity_dimensions(
            quantity_dimensions=element.quantity_dimensions, quantity=element.quantity, quantity_uom=element.uom
        )

    proposal: Proposal = Proposal.objects.select_related("order", "proposal_from").filter(id=proposal_id).first()

    logger.info("Fetching Proposal Document Config Data For Proposal Approve.")
    proposal_document_config_data: list[OrganizationSectionData] = fetch_document_config_for_proposal_detail(
        country_id=proposal.proposal_from.country_id, proposal_id=proposal.pk
    )
    logger.info("Proposal Document Config Data Fetched.", proposal_document_config_data=proposal_document_config_data)
    logger.info("Preparing Section Config Update Data Format From Document Config Data.")
    proposal_approve_data.document_config_data = prepare_section_update_data_from_section_data(
        config_data=proposal_document_config_data
    )
    logger.info("Section Config Update Data Format Prepared.", proposal_approve_data=proposal_approve_data)

    if not proposal.order:
        logger.info("proposal order not found")
        # Fresh proposal approve
        insert_proposal_status(proposal_id=proposal_id, action=Action.APPROVE, created_by_id=user.pk)
        if proposal_approve_data.start_date and proposal_approve_data.due_date:
            proposal_dates_update(
                proposal=proposal,
                user_id=user.pk,
                start_date=proposal_approve_data.start_date,
                due_date=proposal_approve_data.due_date,
                shipping_address=proposal_approve_data.shipping_address,
            )
        order_id, email_data = ProposalToOrder.create_and_send_order(
            proposal=proposal,
            org_id=org_id,
            user=user,
            project_id=project_id,
            proposal_approve_data=proposal_approve_data,
        )
        proposal, _, updated_fields = model_update(
            instance=proposal,
            updated_by_id=user.pk,
            data={"order_id": order_id, "status": ProposalStatus.APPROVED},
            fields=["order_id", "status"],
        )
        on_commit(
            partial(
                proposal_approve_for_new_order_trigger_event,
                proposal=proposal,
                user=user,
                proposal_approve=proposal_approve_data,
                email_data=email_data,
            )
        )
        return order_id
    # TODO:  send email to client
    order_id, _, email_data, old_order_value = ProposalToOrder.update_order(
        proposal=proposal, user=user, proposal_approve_data=proposal_approve_data
    )
    # request for change proposal approval
    insert_proposal_status(proposal_id=proposal_id, action=Action.APPROVE, created_by_id=user.pk)

    proposal, _, updated_fields = model_update(
        instance=proposal,
        updated_by_id=user.pk,
        data={"order_id": order_id, "status": ProposalStatus.APPROVED},
        fields=["order_id", "status"],
        save=False,
    )
    if proposal_approve_data.start_date and proposal_approve_data.due_date and proposal_approve_data.shipping_address:
        proposal.start_date = proposal_approve_data.start_date
        proposal.due_date = proposal_approve_data.due_date
        proposal.shipping_address = proposal.shipping_address
        updated_fields.extend(["start_date", "due_date", "shipping_address"])
    proposal.save(update_fields=updated_fields)

    elements = []
    for element in proposal_approve_data.elements:
        elements.append(
            {
                "element_id": element.boq_element_id,
                "quantity": str(element.quantity),
                "client_rate": str(element.client_rate),
                "object_status": element.object_status.value,
            }
        )
    on_commit(
        partial(
            proposal_approve_for_order_change_trigger_event,
            proposal=proposal,
            user=user,
            proposal_approve=proposal_approve_data,
            elements=elements,
            email_data=email_data,
            old_order_value=old_order_value,
        )
    )
    order = VendorOrder.objects.filter(id=order_id).first()
    if order.outgoing_status == VendorOrder.OutgoingStatus.CANCELLED:
        on_commit(partial(order_cancel_email_trigger, order=order, email_data=email_data, user_id=user.pk))
    logger.info("proposal_approve_v2 finished")
    return order_id


def proposal_reject(*, proposal_id: int, user: User):
    logger.info("proposal_reject started", proposal_id=proposal_id, user_id=user.pk)
    insert_proposal_status(proposal_id=proposal_id, action=Action.REJECT, created_by_id=user.pk)
    proposal_elements: List[ProposalElementMapping] = (
        ProposalElementMapping.objects.filter(
            proposal_id=proposal_id,
            object_status__in=[ObjectStatus.ADD.value, ObjectStatus.UPDATE.value, ObjectStatus.DELETE.value],
        )
        .select_related("element", "proposal__project", "proposal", "proposal__order", "element__work_progress_element")
        .all()
        .available()
    )
    logger.info("proposal_elements fetched", count=proposal_elements.count())
    proposal = Proposal.objects.filter(id=proposal_id).select_related("created_by", "project").first()

    ProposalElementMapping.objects.filter(proposal=proposal).update(linked_element_id=None)

    if not proposal_elements:
        raise ValidationError("Proposal elements not found")
    model_update(
        instance=proposal,
        updated_by_id=user.pk,
        data={"status": ProposalStatusMapping.ProposalStatus.REJECTED},
        fields=["status"],
    )
    history_data = []
    boq_element_to_updated_fields_map: dict[BoqElement, list[str]] = {}
    for proposal_element in proposal_elements:
        if proposal_element.element.element_status == BoqElementStatus.CANCELLED.value:
            raise ProposalElementApprovedNotAllowed(
                "Element is already cancelled, Please ask your vendor to make changes in proposal"
            )
        job_id = proposal_element.proposal.project.job_id
        number = (
            proposal_element.proposal.order.order_number
            if proposal_element.proposal.order
            else proposal_element.proposal.ref_number
        )
        if proposal_element.object_status == ObjectStatus.ADD.value:
            history_data.append(
                BoqElementHistoryData(
                    element_id=proposal_element.element_id,
                    action=BoqElementAction.REQUEST_REJECTED,
                    item_type_id=proposal_element.element.item_type_id,
                    category_id=proposal_element.element.category_id,
                    quantity=proposal_element.element.quantity,
                    client_rate=proposal_element.element.client_rate,
                    budget_rate=proposal_element.element.budget_rate,
                    uom=proposal_element.element.uom,
                    source=f"{job_id}/{number}",
                    status=StatusService.get_status(
                        old_element_status=proposal_element.element.element_status,
                        action=BoqElementAction.REQUEST_REJECTED,
                    ),
                    discount_percent=proposal_element.element.discount_percent,
                    service_charge_percent=proposal_element.element.service_charge_percent,
                    is_service_charge_with_base_amount=proposal_element.element.is_service_charge_with_base_amount,
                    quantity_dimensions=proposal_element.element.quantity_dimensions,
                    name=proposal_element.element.name,
                    description=proposal_element.element.description,
                )
            )
        elif proposal_element.object_status == ObjectStatus.UPDATE.value:
            history_data.append(
                BoqElementHistoryData(
                    element_id=proposal_element.element_id,
                    action=BoqElementAction.CHANGE_REQUESTED_REJECTED,
                    item_type_id=proposal_element.element.item_type_id,
                    category_id=proposal_element.element.category_id,
                    quantity=proposal_element.element.quantity,
                    client_rate=proposal_element.element.client_rate,
                    budget_rate=proposal_element.element.budget_rate,
                    uom=proposal_element.element.uom,
                    source=f"{job_id}/{number}",
                    status=StatusService.get_status(
                        old_element_status=proposal_element.element.element_status,
                        action=BoqElementAction.CHANGE_REQUESTED_REJECTED,
                    ),
                    discount_percent=proposal_element.element.discount_percent,
                    service_charge_percent=proposal_element.element.service_charge_percent,
                    is_service_charge_with_base_amount=proposal_element.element.is_service_charge_with_base_amount,
                    quantity_dimensions=proposal_element.element.quantity_dimensions,
                    name=proposal_element.element.name,
                    description=proposal_element.element.description,
                )
            )
        elif proposal_element.object_status == ObjectStatus.DELETE.value:
            history_data.append(
                BoqElementHistoryData(
                    element_id=proposal_element.element_id,
                    action=BoqElementAction.CANCELLATION_REQUESTED_REJECTED,
                    item_type_id=proposal_element.element.item_type_id,
                    category_id=proposal_element.element.category_id,
                    quantity=proposal_element.element.quantity,
                    client_rate=proposal_element.element.client_rate,
                    budget_rate=proposal_element.element.budget_rate,
                    uom=proposal_element.element.uom,
                    source=f"{job_id}/{number}",
                    status=StatusService.get_status(
                        old_element_status=proposal_element.element.element_status,
                        action=BoqElementAction.CANCELLATION_REQUESTED_REJECTED,
                    ),
                    discount_percent=proposal_element.element.discount_percent,
                    service_charge_percent=proposal_element.element.service_charge_percent,
                    is_service_charge_with_base_amount=proposal_element.element.is_service_charge_with_base_amount,
                    quantity_dimensions=proposal_element.element.quantity_dimensions,
                    name=proposal_element.element.name,
                    description=proposal_element.element.description,
                )
            )
        """
        Prepare boq_element_to_updated_fields_map data for Updating linked WP Element
        and creating timeline history
        - Only checking quantity because item_type_id & uom can be changed while creating proposal
        - Compare PEM.quantity (Current BOQ version quantity) and previous state quantity (i.e. BoqElement.quantity)
        """
        if proposal_element.quantity != proposal_element.element.quantity:
            boq_element_to_updated_fields_map[proposal_element.element] = ["quantity"]

    action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user.pk)
    logger.info("history created", count=len(history_data))
    # Updating linked work progress element
    if boq_element_to_updated_fields_map:
        logger.info(
            "Update work progress element and create timeline and percentage history",
            source="proposal rejected",
        )
        WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
            element_to_updated_fields_map=boq_element_to_updated_fields_map,
            action_histories=action_histories,
            user_id=user.pk,
            project_id=proposal.project_id,
            org_id=user.org_id,
        )
        logger.info("Work progress elements timeline and percentage history created.")

    if proposal_elements[0].proposal.order_id:
        on_commit(partial(proposal_reject_for_order_change_trigger_event, proposal=proposal, user=user))
    else:
        on_commit(partial(proposal_reject_for_new_order_trigger_event, proposal=proposal, user=user))

    logger.info("proposal_reject finished")


def fetch_document_config_for_proposal_detail(country_id: int, proposal_id: int) -> list[OrganizationSectionData]:
    proposal_config_field = fetch_proposal_document_config(country_id=country_id, proposal_id=proposal_id)
    config: list[OrganizationSectionData] = prepare_nested_data_for_section_config(
        context_config_objects=proposal_config_field, context=MicroContextChoices.PROPOSAL, with_data=True
    )
    return config


def prepare_section_update_data_from_section_data(config_data: list[OrganizationSectionData]) -> dict:
    logger.info("Preparing Section Update Data Format From Section Config Data", config_data=config_data)
    sections = defaultdict(dict)

    # Iterate over each section
    for section in config_data:
        sections[section.id] = {}

        # Iterate over documents in each section
        for document in section.documents:
            sections[section.id][document.id] = []

            # Iterate over fields data in each document
            for field_data in document.fields_data:
                field_representation = {}
                field_representation["id"] = document.id

                # Iterate over each field in field_data and prepare nested structure
                for field_id, value in field_data.items():
                    if field_id == "id":
                        continue
                    field_representation[field_id] = type(value).drf_serializer(value).data
                    if field_representation[field_id].get("id"):
                        field_representation[field_id]["id"] = HashIdConverter.decode(
                            field_representation[field_id]["id"]
                        )
                    if isinstance(field_representation[field_id]["data"], dict) and field_representation[field_id][
                        "data"
                    ].get("id"):
                        data_obj = field_representation[field_id]["data"]
                        field_representation[field_id]["data"]["id"] = HashIdConverter.decode(data_obj["id"])

                sections[section.id][document.id].append(field_representation)
    logger.info("Section Update Data Format Prepared", sections=sections)
    return {"sections": sections}


def get_default_prefill_details_for_proposal(
    *, organization_id: int, project_id: int, proposal_client_id: int
) -> Tuple[ProposalPrefillDetails, Dict]:
    org_client_field_settings = get_client_field_settings(organization_id=organization_id)
    return ProposalPrefillDetails(**org_client_field_settings.model_dump()), get_shipping_address_dropdown_list(
        project=Project.objects.select_related("store").get(id=project_id),
        proposal_client_id=proposal_client_id,
    )


def get_prefill_details_for_proposal_using_current_data(
    *, organization_id: int, proposal_id: int
) -> Tuple[ProposalPrefillDetails, Dict]:
    try:
        proposal: Proposal = Proposal.objects.select_related("order", "meta_data").get(
            id=proposal_id, proposal_from_id=organization_id
        )
    except Proposal.DoesNotExist:
        raise BaseValidationError("Invalid proposal_id.")
    start_date = timezone.localtime(proposal.start_date).date() if proposal.start_date is not None else None
    due_date = timezone.localtime(proposal.due_date).date() if proposal.due_date is not None else None
    shipping_address = proposal.shipping_address
    is_proposal_created_before_order: bool = check_proposal_created_before_order(proposal=proposal)
    if is_proposal_created_before_order:
        try:
            org_client_field_settings = prepare_client_field_settings(hidden_fields=proposal.meta_data.hidden_fields)
        except Proposal.meta_data.RelatedObjectDoesNotExist:
            org_client_field_settings = get_client_field_settings(organization_id=organization_id)
        column_fields = org_client_field_settings.column_fields
        summary_fields = org_client_field_settings.summary_fields
    else:
        column_fields = []
        summary_fields = []
        for field in ClientFields:
            if CLIENT_FIELD_TO_TYPE_MAPPING.get(field, ClientFieldTypes.COLUMN) == ClientFieldTypes.SUMMARY:
                summary_fields.append(
                    ClientField(
                        id=field.value,
                        name=ClientFieldChoices(field.value).label,
                        type=ClientFieldTypes.SUMMARY.value,
                        is_visible=True,
                        is_read_only=True,
                    )
                )
            else:
                column_fields.append(
                    ClientField(
                        id=field.value,
                        name=ClientFieldChoices(field.value).label,
                        type=ClientFieldTypes.COLUMN.value,
                        is_visible=True,
                        is_read_only=True,
                    )
                )
    return ProposalPrefillDetails(
        start_date=start_date,
        due_date=due_date,
        shipping_address=shipping_address,
        column_fields=column_fields,
        summary_fields=summary_fields,
    ), get_shipping_address_dropdown_list(
        project=proposal.project,
        proposal_client_id=proposal.proposal_for_id,
    )


def get_shipping_address_dropdown_list(*, project: Project, proposal_client_id: int) -> Dict:
    store_address = project.store.full_address
    project_client_id = project.client_id
    custom_address = list(
        VendorOrder.objects.filter(
            project_id=project.pk,
            shipping_address_header="Custom",
            outgoing_status__in=[VendorOrder.OutgoingStatus.SENT, VendorOrder.OutgoingStatus.COMPLETED],
        )
        .annotate(header=F("shipping_address_header"), address=F("shipping_address"))
        .values("header", "address")
        .distinct()
    )
    shipping_address = get_shipping_address(
        store_address=store_address, org_id_list=[project_client_id, proposal_client_id]
    )
    return custom_address + shipping_address


def prepare_proposal_client_field_default_settings() -> ProposalClientFields:
    column_fields = []
    summary_fields = []
    for field in ClientFields:
        client_field = ClientField(
            id=field.value,
            name=ClientFieldChoices(field.value).label,
            type=CLIENT_FIELD_TO_TYPE_MAPPING.get(field, ClientFieldTypes.COLUMN).value,
            is_visible=True,
            is_read_only=False,
        )
        if client_field.type == ClientFieldTypes.COLUMN.value:
            column_fields.append(client_field)
        else:
            summary_fields.append(client_field)
    return ProposalClientFields(summary_fields=summary_fields, column_fields=column_fields)


def prepare_proposal_client_field_settings(hidden_fields: list[str]) -> ProposalClientFields:
    column_fields = []
    summary_fields = []
    for field in ClientFields:
        is_visible = True
        is_read_only = False
        if field in READ_ONLY_CLIENT_FIELDS:
            is_read_only = True
        type = ClientFieldTypes.COLUMN
        if field.value in hidden_fields:
            is_visible = False
        if CLIENT_FIELD_TO_TYPE_MAPPING.get(field, ClientFieldTypes.COLUMN) == ClientFieldTypes.SUMMARY:
            type = ClientFieldTypes.SUMMARY
        client_field = ClientField(
            id=field.value,
            name=ClientFieldChoices(field.value).label,
            type=type.value,
            is_visible=is_visible,
            is_read_only=is_read_only,
        )
        if type == ClientFieldTypes.COLUMN:
            column_fields.append(client_field)
        else:
            summary_fields.append(client_field)
    return ProposalClientFields(summary_fields=summary_fields, column_fields=column_fields)


def get_proposal_client_field_settings(proposal_id: int):
    meta_data = ProposalMetaData.objects.filter(proposal_id=proposal_id).first()
    if not meta_data:
        return prepare_proposal_client_field_default_settings()
    return prepare_proposal_client_field_settings(hidden_fields=meta_data.hidden_fields)


def check_proposal_created_before_order(proposal: Proposal) -> bool:
    return (
        Proposal.objects.filter(order_id=proposal.order.id, created_at__lt=proposal.order.created_at).exists()
        if proposal.order is not None
        else True
    )


def transform_field_names(field_name_mapping: dict, fields: list):
    transformed_field_names = []
    for field in fields:
        transformed_field_names.extend(field_name_mapping.get(field, field))
    return transformed_field_names


def get_hidden_fields(
    client_field_mapping: dict,  # field name and serializer key mapping
    org_id: int,
    proposal_id: Optional[int] = None,
    order_id: Optional[int] = None,
) -> list[str]:
    if proposal_id:
        proposal = Proposal.objects.select_related("meta_data").filter(id=proposal_id).first()

        # if client is viewing the proposal
        if org_id == proposal.proposal_for_id:
            hidden_fields = proposal.meta_data.hidden_fields if hasattr(proposal, "meta_data") else []
            hidden_fields = transform_field_names(field_name_mapping=client_field_mapping, fields=hidden_fields)

        # if vendor is viewing the proposal
        else:
            hidden_fields = []
        return hidden_fields

    elif order_id:
        proposal = (
            Proposal.objects.select_related("meta_data", "order")
            .filter(order_id=order_id, status=ProposalStatus.APPROVED)
            .order_by("-id")
            .first()
        )

        # if order is created by proposal and client is viewing the order
        if proposal and org_id == proposal.proposal_for_id:
            hidden_fields = proposal.meta_data.hidden_fields if hasattr(proposal, "meta_data") else []
            hidden_fields = transform_field_names(field_name_mapping=client_field_mapping, fields=hidden_fields)
        else:
            hidden_fields = []

        return hidden_fields

    else:
        return []


def get_hidden_fields_for_vendor_order_list(
    client_field_mapping: dict,  # field name and serializer key mapping
    org_id: int,
    order_ids: List[int] = [],
):
    vendor_order_id_to_hidden_fields_mapping = {}
    if order_ids:
        proposals = Proposal.objects.select_related("meta_data", "order").filter(
            order_id__in=order_ids, status=ProposalStatus.APPROVED
        )

        for proposal in proposals:
            hidden_fields = []
            if org_id == proposal.proposal_for_id:
                hidden_fields.extend(proposal.meta_data.hidden_fields if hasattr(proposal, "meta_data") else [])

            vendor_order_id_to_hidden_fields_mapping[proposal.order_id] = transform_field_names(
                field_name_mapping=client_field_mapping, fields=hidden_fields
            )

    return vendor_order_id_to_hidden_fields_mapping


# vendor-wise scope -> annotate on row level
