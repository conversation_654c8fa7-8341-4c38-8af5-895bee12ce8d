from functools import partial
from typing import List, Optional, Tuple

import structlog
from django.core.exceptions import ValidationError
from django.db.models import F, OuterRef, Prefetch, Q, Subquery, Sum
from django.db.models.functions import Lower
from django.db.transaction import on_commit
from django.utils import timezone
from rest_framework.exceptions import PermissionDenied

from authorization.domain.constants import Permissions
from boq.data.choices import BoqElementAction, BoqElementStatus
from boq.data.entities import BoqElementHistoryData
from boq.data.models import (
    BoqElement,
    BoqElementActionHistory,
    BoqElementGuideline,
    BoqElementPreviewFile,
    BoqElementProductionDrawing,
)
from boq.services.status_history import StatusService
from common.choices import OrganizationType, ProjectUserRoleLevelChoices, ReservedRoleNames
from common.element_base.entities import GuidelineAttachmentData, GuidelineData, PreviewFileData, ProductionDrawingData
from common.element_base.services import ElementRelatedCommonServiceNew, ElementRelatedServiceNew
from common.entities import ObjectStatus
from common.services import model_update
from common.utils import validate_quantity_dimensions
from core.caches import OrgAssignmentCache
from core.exceptions import OrgUserNotFoundException
from core.models import Role, User
from core.organization.data.models import OrganizationDocumentFieldContextConfig, OrganizationDocumentTextFieldData
from core.organization.data.selectors import get_context_config_by_country
from core.role.entities import RoleCreateData
from core.role.services import ProjectDynamicRoleCreateService
from core.selectors import active_user_get_all, organization_get
from microcontext.choices import MicroContextChoices
from order.data.models import (
    OrderTextFieldData,
    VendorOrder,
    VendorOrderElement,
    VendorOrderElementGuideline,
    VendorOrderElementPreviewFile,
    VendorOrderElementProductionDrawing,
)
from order.domain.status_choices import OrderStatus
from order.services import generate_proposal_and_order_number
from project.data.models import Project, ProjectUser
from project.domain.caches import ProjectAssignmentCache
from project.domain.exceptions import ProjectNotAssignedToOrgException
from project.domain.helpers import ProjectPermissionHelper
from project.domain.services import get_project_proposal_client, get_user_and_permission_check_with_token_data
from proposal.data.entities import (
    ProposalData,
    ProposalElementDataV2,
    ProposalRequestChangeData,
    ProposalRequestChangeDatas,
    ProposalRequestChangeElementData,
)
from proposal.data.models import (
    Proposal,
    ProposalElementGuideline,
    ProposalElementMapping,
    ProposalElementPreviewFile,
    ProposalElementProductionDrawing,
    ProposalElementProductionDrawingTag,
    ProposalGuidelineAttachment,
    ProposalMetaData,
    ProposalStatusMapping,
    ProposalTextFieldData,
)
from proposal.data.selectors import fetch_text_data_from_order_context, fetch_text_data_obj_of_context_field_config
from proposal.data.triggers import (
    proposal_created_trigger,
    proposal_updated_when_order_exist_trigger,
    proposal_updated_when_order_not_exist_trigger,
)
from proposal.domain.constants import Action, ProposalStatus
from proposal.domain.element_services_v2 import ProposalElementServiceV2
from proposal.domain.services import (
    insert_proposal_status,
    proposal_request_for_new_order_trigger_event,
    proposal_request_for_order_change_trigger_event,
)
from vendorv2.domain.services import VendorUserService
from work_progress_v2.interface.factory_helper import WorkProgressTimelineSync

logger = structlog.get_logger(__name__)


class ProposalElementRelatedService(ElementRelatedServiceNew):
    preview_file = ProposalElementPreviewFile
    guideline = ProposalElementGuideline
    guideline_attachment = ProposalGuidelineAttachment
    production_drawing = ProposalElementProductionDrawing
    production_drawing_tag = ProposalElementProductionDrawingTag


class ProposalService:
    @classmethod
    def proposal_create(cls, project_id: int, proposal_data: ProposalData, user_id: int) -> Proposal:
        logger.info("Creating proposal", project_id=project_id, user_id=user_id, proposal_data=proposal_data.__dict__)
        proposal = Proposal(
            project_id=project_id,
            ref_number=proposal_data.ref_number,
            proposal_for_id=proposal_data.proposal_for_id,
            proposal_from_id=proposal_data.proposal_from_id,
            created_by_id=user_id,
            order_id=proposal_data.order_id,
            status=proposal_data.status,
            version=proposal_data.version,
            received_by_id=proposal_data.received_by,
            is_discounted=proposal_data.is_discounted,
            is_service_charged=proposal_data.is_service_charged,
            start_date=proposal_data.start_date,
            due_date=proposal_data.due_date,
            shipping_address=proposal_data.shipping_address,
        )
        proposal.full_clean()
        proposal.save()

        if proposal_data.version > 1:
            """
            Handling the situation where a proposal is created with a version greater than 1,
            indicating that there are changes to an existing proposal.
            """
            proposal_updated_when_order_exist_trigger(
                proposal_id=proposal.pk,
                project_id=project_id,
                updated_by_id=user_id,
                org_id=proposal_data.proposal_from_id,
                version=proposal_data.version,
            )
            return proposal

        proposal_created_trigger(
            proposal_id=proposal.pk, project_id=project_id, created_by_id=user_id, org_id=proposal_data.proposal_from_id
        )
        return proposal

    @classmethod
    def create_proposal_text_field_data_when_order_does_not_exist(
        cls, proposal_id: int, vendor_id: int, country_id: int, user_id: int
    ) -> None:
        logger.info(
            "Creating proposal text field data when order does not exist.", proposal_id=proposal_id, vendor_id=vendor_id
        )
        context_config: OrganizationDocumentFieldContextConfig = get_context_config_by_country(
            country_id=country_id, context=MicroContextChoices.PROPOSAL
        )
        if not context_config:
            return
        org_data_text_object: OrganizationDocumentTextFieldData = fetch_text_data_obj_of_context_field_config(
            vendor_id=vendor_id, context_config=context_config
        )
        logger.info("Fetched OrganizationDocumentTextFieldData", org_data_text_object=org_data_text_object)
        if org_data_text_object:
            text_obj = ProposalTextFieldData(
                proposal_id=proposal_id,
                context_config_id=context_config.pk,
                data=org_data_text_object.data,
                created_by_id=user_id,
            )
            text_obj.save()
            logger.info("Text field data created.", data=org_data_text_object.data)
        logger.info("Proposal text field data creation finished.", proposal_id=proposal_id)

    @classmethod
    def create_proposal_text_field_data_when_order_exists(
        cls, proposal_id: int, order_id: int, country_id: int, user_id: int
    ) -> None:
        logger.info("Creating proposal text field data when order exists.", proposal_id=proposal_id, order_id=order_id)
        order_context_config: OrganizationDocumentFieldContextConfig = get_context_config_by_country(
            country_id=country_id, context=MicroContextChoices.ORDER
        )
        if not order_context_config:
            return
        proposal_context_config = get_context_config_by_country(
            country_id=country_id, context=MicroContextChoices.PROPOSAL
        )
        if proposal_context_config:
            return
        order_text_data_object: OrderTextFieldData = fetch_text_data_from_order_context(
            order_id=order_id, context_config=order_context_config
        )
        logger.info("Fetched OrderTextFieldData object.", order_text_data_object=order_text_data_object)
        if order_text_data_object:
            text_obj = ProposalTextFieldData(
                proposal_id=proposal_id,
                context_config_id=proposal_context_config.pk,
                data=order_text_data_object.data,
                created_by_id=user_id,
            )
            text_obj.save()
            logger.info("Text field data created.", data=order_text_data_object.data)
        logger.info("Proposal text field data creation finished.", proposal_id=proposal_id)

    @classmethod
    def create_proposal_element_data_from_boq(
        cls, element_entities: List[ProposalRequestChangeElementData]
    ) -> tuple[List[ProposalElementDataV2], dict[BoqElement, list[str]]]:
        logger.info("Creating proposal element data from boq")
        element_id_dict = {element.element_id: element for element in element_entities}
        elements_to_change: List[BoqElement] = (
            BoqElement.objects.filter(id__in=[element.element_id for element in element_entities])
            .select_related("section", "work_progress_element")
            .prefetch_related(Prefetch("preview_files", BoqElementPreviewFile.objects.available()))
            .prefetch_related(
                Prefetch("guidelines", BoqElementGuideline.objects.available().prefetch_related("attachments"))
            )
            .prefetch_related(Prefetch("production_drawings", BoqElementProductionDrawing.objects.available()))
            .all()
            .order_by("id")
        )
        element_objs = []

        # For updating linked WP Element and creating timeline history
        boq_element_to_updated_fields_map: dict[BoqElement, list[str]] = {}

        for element in elements_to_change:
            if (
                element_id_dict[element.id].object_status == ObjectStatus.UPDATE.value
                and element_id_dict[element.id].element_status
                in [BoqElementStatus.APPROVED.value, BoqElementStatus.CHANGE_REQUESTED.value]
                and element_id_dict[element.id].quantity == 0
            ):
                object_status = ObjectStatus.DELETE.value
            else:
                object_status = element_id_dict[element.id].object_status
            quantity = 0 if object_status == ObjectStatus.DELETE.value else element_id_dict[element.id].quantity
            if quantity == 0:
                quantity_dimensions = None
            else:
                quantity_dimensions = element_id_dict[element.id].quantity_dimensions

            client_rate = element_id_dict[element.id].client_rate
            discount_percent = element_id_dict[element.id].discount_percent
            is_service_charge_with_base_amount = element_id_dict[element.id].is_service_charge_with_base_amount
            service_charge_percent = (
                element_id_dict[element.id].service_charge_percent
                if is_service_charge_with_base_amount is not None
                else 0
            )
            tax_percent = element_id_dict[element.id].tax_percent
            brand_name = element_id_dict[element.id].brand_name
            hsn_code = element_id_dict[element.id].hsn_code
            preview_file_list = []
            for preview_file in element.preview_files.all():
                preview_file_list.append(
                    PreviewFileData(
                        object_status=ObjectStatus.ADD,
                        type=preview_file.type,
                        file=preview_file.file,
                        name=preview_file.name,
                        is_main=preview_file.is_main,
                    )
                )
            guideline_data_list = []
            for guideline in element.guidelines.all():
                attachments = guideline.attachments.all()
                attachment_list = []
                for attachment in attachments:
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )

                guideline_data_list.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        attachments=attachment_list,
                        description=guideline.description,
                        name=guideline.name,
                    )
                )
            production_drawing_data = []
            for drawings in element.production_drawings.all():
                production_drawing_data.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        file=drawings.file,
                        name=drawings.name,
                        tags=drawings.tags.values_list("id", flat=True),
                    )
                )
            element_objs.append(
                ProposalElementDataV2(
                    id=None,
                    object_status=object_status,
                    name=element.name,
                    description=element.description,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=client_rate,
                    discount_percent=discount_percent,
                    quantity=quantity,
                    client_id=element.client_id,
                    section_name=element.section.name if element.section else "Unsectioned",
                    code=element.code,
                    version=element.version,
                    serial_number=element.serial_number,
                    custom_type=element.custom_type,
                    element_id=element.id,
                    el_element_id=element.element_id,
                    element_status=element_id_dict[element.id].element_status,
                    db_object_status=object_status,
                    service_charge_percent=service_charge_percent,
                    is_service_charge_with_base_amount=is_service_charge_with_base_amount,
                    quantity_dimensions=quantity_dimensions,
                    preview_files=preview_file_list,
                    guidelines=guideline_data_list,
                    production_drawings=production_drawing_data,
                    budget_rate=element.budget_rate,
                    tax_percent=tax_percent,
                    brand_name=brand_name,
                    hsn_code=hsn_code,
                )
            )

            """
            prepare element_id_to_updated_fields data for Updating linked WP Element
            and creating timeline history
            """
            proposal_element_data = element_id_dict[element.pk]

            # Only checking quantity because item_type_id & uom can be changed while creating proposal
            if element.quantity != proposal_element_data.quantity:
                boq_element_to_updated_fields_map[element] = ["quantity"]

        return element_objs, boq_element_to_updated_fields_map

    @classmethod
    def create_proposal_element_data_from_order(
        cls, project_id: int, proposal_request: ProposalRequestChangeData
    ) -> List[ProposalElementDataV2]:
        logger.info("Creating proposal element data from order")
        if proposal_request.order_number:
            _, order_number = proposal_request.order_number.split("/")
            unchanged_elements: List[VendorOrderElement] = (
                VendorOrderElement.available_objects.filter(
                    vendor_order__project_id=project_id,
                    vendor_order__order_number=order_number,
                    status__in=[OrderStatus.PENDING, OrderStatus.COMPLETED, OrderStatus.SENT, OrderStatus.MODIFIED],
                )
                .exclude(linked_element_id__in=[element.element_id for element in proposal_request.elements])
                .select_related("linked_element", "linked_element__section")
                .prefetch_related(Prefetch("preview_files", VendorOrderElementPreviewFile.objects.available()))
                .prefetch_related(
                    Prefetch(
                        "guidelines", VendorOrderElementGuideline.objects.available().prefetch_related("attachments")
                    )
                )
                .prefetch_related(
                    Prefetch("production_drawings", VendorOrderElementProductionDrawing.objects.available())
                )
                .all()
            )
            element_objs = []
            for element in unchanged_elements:
                preview_file_list = []
                for preview_file in element.preview_files.all():
                    preview_file_list.append(
                        PreviewFileData(
                            object_status=ObjectStatus.ADD,
                            type=preview_file.type,
                            file=preview_file.file,
                            name=preview_file.name,
                            is_main=preview_file.is_main,
                        )
                    )
                guideline_data_list = []
                for guideline in element.guidelines.all():
                    attachments = guideline.attachments.all()
                    attachment_list = []
                    for attachment in attachments:
                        attachment_list.append(
                            GuidelineAttachmentData(
                                object_status=ObjectStatus.ADD,
                                file=attachment.file,
                                name=attachment.name,
                                type=attachment.type,
                            )
                        )

                    guideline_data_list.append(
                        GuidelineData(
                            object_status=ObjectStatus.ADD,
                            attachments=attachment_list,
                            description=guideline.description,
                            name=guideline.name,
                        )
                    )
                production_drawing_data = []
                for drawings in element.production_drawings.all():
                    production_drawing_data.append(
                        ProductionDrawingData(
                            object_status=ObjectStatus.ADD,
                            file=drawings.file,
                            name=drawings.name,
                            tags=drawings.tags.values_list("id", flat=True),
                        )
                    )
                element_objs.append(
                    ProposalElementDataV2(
                        object_status=None,
                        id=None,
                        name=element.linked_element.name,
                        description=element.linked_element.description,
                        uom=element.linked_element.uom,
                        category_id=element.linked_element.category_id,
                        item_type_id=element.linked_element.item_type_id,
                        client_rate=element.linked_element.client_rate,
                        discount_percent=element.linked_element.discount_percent,
                        quantity=element.linked_element.quantity,
                        client_id=element.linked_element.client_id,
                        section_name=(
                            element.linked_element.section.name if element.linked_element.section else "Unsectioned"
                        ),
                        code=element.linked_element.code,
                        version=element.linked_element.version,
                        serial_number=element.linked_element.serial_number,
                        custom_type=element.linked_element.custom_type,
                        element_id=element.linked_element.id,
                        el_element_id=element.linked_element.element_id,
                        db_object_status=None,
                        service_charge_percent=element.linked_element.service_charge_percent,
                        is_service_charge_with_base_amount=element.linked_element.is_service_charge_with_base_amount,
                        quantity_dimensions=element.linked_element.quantity_dimensions,
                        preview_files=preview_file_list,
                        guidelines=guideline_data_list,
                        production_drawings=production_drawing_data,
                        budget_rate=element.linked_element.budget_rate,
                        tax_percent=element.linked_element.tax_percent,
                        brand_name=element.linked_element.brand_name,
                        hsn_code=element.linked_element.hsn_code,
                    )
                )
            return element_objs
        return []

    @classmethod
    def create_proposal_element_data_from_old_proposal(
        cls,
        proposal_id: int,
        to_update: List[ProposalElementDataV2],
        to_delete: List[ProposalElementDataV2],
        to_remove: List[ProposalElementDataV2],
        element_id_dict,
    ) -> Tuple[
        List[ProposalElementDataV2],
        List[ProposalElementDataV2],
        List[ProposalElementDataV2],
        dict[BoqElement, list[str]],
    ]:
        logger.info("Creating proposal element data from old proposal", old_proposal_id=proposal_id)
        elements_to_update: List[ProposalElementMapping] = (
            ProposalElementMapping.objects.filter(
                proposal_id=proposal_id,
                element_id__in=[element.element_id for element in to_update + to_delete + to_remove],
                deleted_at__isnull=True,
            )
            .select_related("element", "element__work_progress_element")
            .prefetch_related(Prefetch("preview_files", ProposalElementPreviewFile.objects.available()))
            .prefetch_related(
                Prefetch("guidelines", ProposalElementGuideline.objects.available().prefetch_related("attachments"))
            )
            .prefetch_related(Prefetch("production_drawings", ProposalElementProductionDrawing.objects.available()))
            .all()
        )
        proposal_elements_to_update = []
        proposal_elements_to_delete = []
        proposal_elements_to_remove = []

        # For updating linked WP Element and creating timeline history
        boq_element_to_updated_fields_map: dict[BoqElement, list[str]] = {}

        for element in elements_to_update:
            preview_file_list = []
            for preview_file in element.preview_files.all():
                preview_file_list.append(
                    PreviewFileData(
                        object_status=ObjectStatus.ADD,
                        type=preview_file.type,
                        file=preview_file.file,
                        name=preview_file.name,
                        is_main=preview_file.is_main,
                    )
                )
            guideline_data_list = []
            for guideline in element.guidelines.all():
                attachments = guideline.attachments.all()
                attachment_list = []
                for attachment in attachments:
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )

                guideline_data_list.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        attachments=attachment_list,
                        description=guideline.description,
                        name=guideline.name,
                    )
                )
            production_drawing_data = []
            for drawings in element.production_drawings.all():
                production_drawing_data.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        file=drawings.file,
                        name=drawings.name,
                        tags=drawings.tags.values_list("id", flat=True),
                    )
                )
            quantity = element_id_dict[element.element.id].quantity
            client_rate = element_id_dict[element.element.id].client_rate
            discount_percent = element_id_dict[element.element.id].discount_percent
            is_service_charge_with_base_amount = element_id_dict[element.element.id].is_service_charge_with_base_amount
            service_charge_percent = (
                element_id_dict[element.element.id].service_charge_percent
                if is_service_charge_with_base_amount is not None
                else 0
            )
            if element_id_dict[element.element.id].object_status == ObjectStatus.UPDATE.value:
                if (
                    element.object_status == ObjectStatus.ADD.value
                    and element.element.element_status == BoqElementStatus.REQUESTED.value
                ):
                    db_object_status = ObjectStatus.ADD.value
                else:
                    db_object_status = element_id_dict[element.element.id].object_status

                proposal_elements_to_update.append(
                    ProposalElementDataV2(
                        id=element.id,
                        object_status=element_id_dict[element.element.id].object_status,
                        name=element.name,
                        description=element.description,
                        uom=element.uom,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=client_rate,
                        quantity=quantity,
                        client_id=element.client_id,
                        section_name="Unsectioned",
                        code=element.code,
                        version=element.version,
                        serial_number=element.serial_number,
                        custom_type=element.custom_type,
                        element_id=element.element_id,
                        el_element_id=element.element.element_id,
                        element_status=element_id_dict[element.element.id].element_status,
                        db_object_status=db_object_status,
                        discount_percent=discount_percent,
                        service_charge_percent=service_charge_percent,
                        is_service_charge_with_base_amount=is_service_charge_with_base_amount,
                        quantity_dimensions=element_id_dict[element.element.id].quantity_dimensions,
                        preview_files=preview_file_list,
                        guidelines=guideline_data_list,
                        production_drawings=production_drawing_data,
                        budget_rate=element.element.budget_rate,
                        tax_percent=element_id_dict[element.element.id].tax_percent,
                        brand_name=element_id_dict[element.element.id].brand_name,
                        hsn_code=element_id_dict[element.element.id].hsn_code,
                    )
                )
            elif (
                element_id_dict[element.element.id].object_status == ObjectStatus.DELETE.value
                and element_id_dict[element.element.id].element_status == BoqElementStatus.REQUESTED
            ):
                proposal_elements_to_remove.append(
                    ProposalElementDataV2(
                        id=element.id,
                        object_status=element_id_dict[element.element.id].object_status,
                        name=element.name,
                        description=element.description,
                        uom=element.uom,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=client_rate,
                        quantity=quantity,
                        client_id=element.client_id,
                        section_name="Unsectioned",
                        code=element.code,
                        version=element.version,
                        serial_number=element.serial_number,
                        custom_type=element.custom_type,
                        element_id=element.element_id,
                        el_element_id=element.element.element_id,
                        element_status=element_id_dict[element.element.id].element_status,
                        db_object_status=element_id_dict[element.element.id].object_status,
                        discount_percent=discount_percent,
                        service_charge_percent=service_charge_percent,
                        is_service_charge_with_base_amount=is_service_charge_with_base_amount,
                        quantity_dimensions=element_id_dict[element.element.id].quantity_dimensions,
                        preview_files=preview_file_list,
                        guidelines=guideline_data_list,
                        production_drawings=production_drawing_data,
                        budget_rate=element.element.budget_rate,
                        tax_percent=element_id_dict[element.element.id].tax_percent,
                        brand_name=element_id_dict[element.element.id].brand_name,
                        hsn_code=element_id_dict[element.element.id].hsn_code,
                    )
                )
            elif element_id_dict[element.element.id].object_status == ObjectStatus.DELETE.value:
                proposal_elements_to_delete.append(
                    ProposalElementDataV2(
                        id=element.id,
                        object_status=element_id_dict[element.element.id].object_status,
                        name=element.name,
                        description=element.description,
                        uom=element.uom,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=client_rate,
                        quantity=0,
                        client_id=element.client_id,
                        section_name="Unsectioned",
                        code=element.code,
                        version=element.version,
                        serial_number=element.serial_number,
                        custom_type=element.custom_type,
                        element_id=element.element_id,
                        el_element_id=element.element.element_id,
                        element_status=element_id_dict[element.element.id].element_status,
                        db_object_status=element_id_dict[element.element.id].object_status,
                        discount_percent=discount_percent,
                        service_charge_percent=service_charge_percent,
                        is_service_charge_with_base_amount=is_service_charge_with_base_amount,
                        quantity_dimensions=element_id_dict[element.element.id].quantity_dimensions,
                        preview_files=preview_file_list,
                        guidelines=guideline_data_list,
                        production_drawings=production_drawing_data,
                        budget_rate=element.element.budget_rate,
                        tax_percent=element_id_dict[element.element.id].tax_percent,
                        brand_name=element_id_dict[element.element.id].brand_name,
                        hsn_code=element_id_dict[element.element.id].hsn_code,
                    )
                )

            """
            Prepare boq_element_to_updated_fields_map data for Updating linked WP Element
            and creating timeline history
            - Only checking quantity because item_type_id & uom can be changed while creating proposal
            - Compare PEM.quantity (Current BOQ version quantity) and Incoming proposal element data quantity
            """
            if element.quantity != quantity:
                boq_element_to_updated_fields_map[element.element] = ["quantity"]

        return (
            proposal_elements_to_update,
            proposal_elements_to_delete,
            proposal_elements_to_remove,
            boq_element_to_updated_fields_map,
        )

    @classmethod
    def create_element_history_when_order_exist(
        cls,
        proposal_request: ProposalRequestChangeData,
        element_data_from_boq: List[ProposalElementDataV2],
        user_id: int,
        order: VendorOrder,
    ) -> List[BoqElementActionHistory]:
        logger.info("Creating element history when order exist")
        to_create, to_update, to_delete, to_remove = cls.proposal_entity_seggration(docs_list=element_data_from_boq)
        project = Project.objects.get(id=order.project_id)
        source = f"{project.job_id}/{order.order_number}"
        history_data = []
        for element in to_create:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.REQUESTED_FOR_ORDER,
                    status=StatusService.get_status(
                        action=BoqElementAction.REQUESTED_FOR_ORDER, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        for element in to_update:
            if element.element_status == BoqElementStatus.REQUESTED.value:
                history_data.append(
                    BoqElementHistoryData(
                        proposal_element_id=element.id,
                        element_id=element.element_id,
                        uom=element.uom,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=element.client_rate,
                        quantity=element.quantity,
                        source=source,
                        action=BoqElementAction.REQUEST_UPDATED,
                        status=StatusService.get_status(
                            action=BoqElementAction.REQUEST_UPDATED, old_element_status=element.element_status
                        ),
                        discount_percent=element.discount_percent,
                        service_charge_percent=(
                            element.service_charge_percent
                            if element.is_service_charge_with_base_amount is not None
                            else 0
                        ),
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=element.quantity_dimensions,
                        name=element.name,
                        description=element.description,
                        budget_rate=element.budget_rate,
                        tax_percent=element.tax_percent,
                        brand_name=element.brand_name,
                        hsn_code=element.hsn_code,
                    )
                )
            else:
                history_data.append(
                    BoqElementHistoryData(
                        proposal_element_id=element.id,
                        element_id=element.element_id,
                        uom=element.uom,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=element.client_rate,
                        quantity=element.quantity,
                        source=source,
                        action=BoqElementAction.CHANGE_REQUESTED,
                        status=StatusService.get_status(
                            action=BoqElementAction.CHANGE_REQUESTED, old_element_status=element.element_status
                        ),
                        discount_percent=element.discount_percent,
                        service_charge_percent=(
                            element.service_charge_percent
                            if element.is_service_charge_with_base_amount is not None
                            else 0
                        ),
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=element.quantity_dimensions,
                        name=element.name,
                        description=element.description,
                        budget_rate=element.budget_rate,
                        tax_percent=element.tax_percent,
                        brand_name=element.brand_name,
                        hsn_code=element.hsn_code,
                    )
                )
        for element in to_delete:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.CANCELLATION_REQUESTED,
                    status=StatusService.get_status(
                        action=BoqElementAction.CANCELLATION_REQUESTED, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        for element in to_remove:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.REMOVED_FROM_PROPOSAL,
                    status=StatusService.get_status(
                        action=BoqElementAction.REMOVED_FROM_PROPOSAL, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user_id)
        cls.reset_linked_element_id(elements=history_data)
        return action_histories

    @classmethod
    def create_element_history_when_order_not_exist(
        cls,
        proposal_request: ProposalRequestChangeData,
        element_data_from_boq: List[ProposalElementDataV2],
        user_id: int,
        proposal: Proposal,
    ) -> List[BoqElementActionHistory]:
        logger.info("Creating element history when order not exist", proposal_id=proposal.pk, user_id=user_id)
        to_create, to_update, _, to_remove = cls.proposal_entity_seggration(docs_list=element_data_from_boq)
        history_data = []
        project = Project.objects.get(id=proposal.project_id)
        source = f"{project.job_id}/{proposal.ref_number}"
        for element in to_create:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.REQUESTED_FOR_ORDER,
                    status=StatusService.get_status(
                        action=BoqElementAction.REQUESTED_FOR_ORDER, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        for element in to_update:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.REQUESTED_FOR_ORDER,
                    status=StatusService.get_status(
                        action=BoqElementAction.REQUESTED_FOR_ORDER, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        for element in to_remove:
            history_data.append(
                BoqElementHistoryData(
                    proposal_element_id=element.id,
                    element_id=element.element_id,
                    uom=element.uom,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.client_rate,
                    quantity=element.quantity,
                    source=source,
                    action=BoqElementAction.REMOVED_FROM_PROPOSAL,
                    status=StatusService.get_status(
                        action=BoqElementAction.REMOVED_FROM_PROPOSAL, old_element_status=element.element_status
                    ),
                    discount_percent=element.discount_percent,
                    service_charge_percent=(
                        element.service_charge_percent if element.is_service_charge_with_base_amount is not None else 0
                    ),
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.name,
                    description=element.description,
                    budget_rate=element.budget_rate,
                    tax_percent=element.tax_percent,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user_id)
        cls.reset_linked_element_id(elements=history_data)
        return action_histories

    @classmethod
    def reset_linked_element_id(cls, elements: List[BoqElementHistoryData]):
        logger.info("Resetting linked element id")
        update_elements: list[int] = []

        for element in elements:
            if element.status == BoqElementStatus.DRAFT and element.proposal_element_id:
                update_elements.append(element.proposal_element_id)

        if update_elements:
            ProposalElementMapping.objects.filter(id__in=update_elements).update(linked_element_id=None)

    @classmethod
    def get_user_id(cls, proposal_request: ProposalRequestChangeData):
        if len(proposal_request.receiver) > 0:
            user = User.objects.filter(email=proposal_request.receiver[0]).first()
            return user.pk if user else None
        return None

    @classmethod
    def create_new_proposal_when_order_exist(
        cls, proposal_request: ProposalRequestChangeData, user_id: int, project_id: int, ref_number: str, org_id: int
    ):
        logger.info(
            "Creating new proposal when order exist",
            project_id=project_id,
            user_id=user_id,
            ref_number=ref_number,
            org_id=org_id,
        )
        proposal: Proposal = (
            Proposal.objects.filter(project_id=project_id, ref_number=ref_number).order_by("-created_at").first()
        )
        order_number = proposal_request.order_number.split("/")[1]
        order = (
            VendorOrder.objects.annotate_elements_final_amount()
            .select_related("org_from")
            .filter(project_id=project_id, order_number=order_number)
            .annotate(
                elements_amount_value=Sum(
                    F("elements_final_amount"),
                    filter=Q(order_elements__deleted_at__isnull=True),
                ),
            )
            .first()
        )
        if not proposal:
            client = get_project_proposal_client(project_id=project_id, organization_id=org_id)
            client_id = client.pk
            version = 1
        else:
            client_id = proposal.proposal_for_id
            version = proposal.version + 1
        proposal_data = ProposalData(
            ref_number=ref_number,
            proposal_for_id=client_id,
            proposal_from_id=org_id,
            order_id=order.pk,
            version=version,
            received_by=cls.get_user_id(proposal_request=proposal_request),
            is_discounted=bool(proposal_request.is_discount_col_visible),
            is_service_charged=bool(proposal_request.is_service_charge_col_visible),
            start_date=proposal_request.start_date,
            due_date=proposal_request.due_date,
            shipping_address=proposal_request.shipping_address,
        )
        proposal = cls.proposal_create(project_id=project_id, proposal_data=proposal_data, user_id=user_id)

        #  create proposal context text field data
        cls.create_proposal_text_field_data_when_order_exists(
            proposal_id=proposal.pk, order_id=order.pk, country_id=order.org_from.country_id, user_id=user_id
        )

        insert_proposal_status(proposal_id=proposal.id, action=Action.SEND, created_by_id=user_id)
        element_data_from_boq, boq_element_to_updated_fields_map = cls.create_proposal_element_data_from_boq(
            element_entities=proposal_request.elements
        )
        logger.info(f"No of elements fethced from boq: {len(element_data_from_boq)}")

        # get unchanged element data from order
        element_data_from_order = cls.create_proposal_element_data_from_order(
            project_id=project_id, proposal_request=proposal_request
        )
        logger.info(f"No of elements fethced from order: {len(element_data_from_order)}")
        element_entities = element_data_from_boq + element_data_from_order
        elements = ProposalElementServiceV2.process_bulk_create(
            proposal=proposal, element_entities=element_entities, user_id=user_id
        )
        ElementRelatedCommonServiceNew.process_bulk_create(
            service=ProposalElementRelatedService,
            elements=elements,
            data=element_entities,
            created_by_id=user_id,
        )
        action_histories = cls.create_element_history_when_order_exist(
            proposal_request=proposal_request, element_data_from_boq=element_data_from_boq, user_id=user_id, order=order
        )

        # Updating linked work progress element
        if boq_element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal create when order exist",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=boq_element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user_id,
                project_id=project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        return proposal, order.elements_amount_value

    @classmethod
    def create_new_proposal_when_order_not_exist(
        cls, proposal_request: ProposalRequestChangeData, user_id: int, project_id: int, ref_number: str, org_id: int
    ) -> Proposal:
        logger.info(
            "Creating new proposal when order not exist",
            project_id=project_id,
            user_id=user_id,
            ref_number=ref_number,
            org_id=org_id,
        )
        client = get_project_proposal_client(project_id=project_id, organization_id=org_id)
        proposal_data = ProposalData(
            ref_number=ref_number,
            proposal_for_id=client.pk,
            proposal_from_id=org_id,
            version=1,
            received_by=cls.get_user_id(proposal_request=proposal_request),
            is_discounted=bool(proposal_request.is_discount_col_visible),
            is_service_charged=bool(proposal_request.is_service_charge_col_visible),
            start_date=proposal_request.start_date,
            due_date=proposal_request.due_date,
            shipping_address=proposal_request.shipping_address,
        )
        proposal = cls.proposal_create(project_id=project_id, proposal_data=proposal_data, user_id=user_id)

        #  create proposal context text field data
        vendor_org = organization_get(org_id=org_id)
        cls.create_proposal_text_field_data_when_order_does_not_exist(
            proposal_id=proposal.pk, vendor_id=org_id, country_id=vendor_org.country_id, user_id=user_id
        )

        insert_proposal_status(proposal_id=proposal.id, action=Action.SEND, created_by_id=user_id)
        element_data_from_boq, boq_element_to_updated_fields_map = cls.create_proposal_element_data_from_boq(
            element_entities=proposal_request.elements
        )

        # get unchanged elements data from order
        element_data_from_order = cls.create_proposal_element_data_from_order(
            project_id=project_id, proposal_request=proposal_request
        )
        elements = ProposalElementServiceV2.process_bulk_create(
            proposal=proposal, element_entities=element_data_from_boq + element_data_from_order, user_id=user_id
        )
        ElementRelatedCommonServiceNew.process_bulk_create(
            service=ProposalElementRelatedService,
            data=element_data_from_boq + element_data_from_order,
            elements=elements,
            created_by_id=user_id,
        )
        action_histories = cls.create_element_history_when_order_not_exist(
            proposal_request=proposal_request,
            element_data_from_boq=element_data_from_boq,
            user_id=user_id,
            proposal=proposal,
        )

        # Update linked Work Progress Element
        if boq_element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal create when order not exist",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=boq_element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user_id,
                project_id=project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        return proposal

    @classmethod
    def proposal_entity_seggration(cls, docs_list: List[ProposalRequestChangeElementData]):
        to_create_docs = []
        to_update_docs = []
        to_delete_docs = []
        to_remove_docs = []
        for doc in docs_list:
            if (
                doc.object_status == ObjectStatus.UPDATE.value
                and doc.element_status
                in [
                    BoqElementStatus.APPROVED.value,
                    BoqElementStatus.CHANGE_REQUESTED.value,
                ]
                and doc.quantity == 0
            ):
                to_delete_docs.append(doc)
            if doc.object_status == ObjectStatus.ADD.value:
                to_create_docs.append(doc)
            elif doc.object_status == ObjectStatus.UPDATE.value:
                to_update_docs.append(doc)
            elif doc.object_status == ObjectStatus.DELETE.value and doc.element_status == BoqElementStatus.REQUESTED:
                to_remove_docs.append(doc)
            elif doc.object_status == ObjectStatus.DELETE.value and doc.element_status in [
                BoqElementStatus.APPROVED.value,
                BoqElementStatus.CHANGE_REQUESTED.value,
            ]:
                to_delete_docs.append(doc)
            else:
                raise ValidationError(
                    f"Invalid object status {doc.object_status} with element status {doc.element_status}"
                )
        return (to_create_docs, to_update_docs, to_delete_docs, to_remove_docs)

    @classmethod
    def update_old_proposal_when_order_exist(
        cls, project_id: int, proposal_request: ProposalRequestChangeData, ref_number: str, user_id: int
    ):
        to_create, to_update, to_delete, to_remove = cls.proposal_entity_seggration(docs_list=proposal_request.elements)
        element_id_dict = {element.element_id: element for element in proposal_request.elements}
        proposal = (
            Proposal.objects.filter(ref_number=ref_number, project_id=project_id, deleted_at__isnull=True)
            .select_related("order")
            .order_by("-created_at")
            .first()
        )
        order_number = proposal_request.order_number.split("/")[1]
        order = (
            VendorOrder.objects.annotate_elements_final_amount()
            .filter(project_id=project_id, order_number=order_number)
            .annotate(
                elements_amount_value=Sum(
                    F("elements_final_amount"), filter=Q(order_elements__deleted_at__isnull=True)
                ),
            )
            .first()
        )
        (
            proposal_elements_to_create,
            boq_element_to_updated_fields_map_create,
        ) = cls.create_proposal_element_data_from_boq(element_entities=to_create)
        (
            proposal_elements_to_update,
            proposal_elements_to_delete,
            proposal_elements_to_remove,
            boq_element_to_updated_fields_map_update,
        ) = cls.create_proposal_element_data_from_old_proposal(
            proposal_id=proposal.pk,
            to_update=to_update,
            to_delete=to_delete,
            to_remove=to_remove,
            element_id_dict=element_id_dict,
        )
        if proposal_elements_to_create:
            ProposalElementServiceV2.process_bulk_create(
                proposal=proposal,
                element_entities=proposal_elements_to_create,
                user_id=user_id,
            )
        if proposal_elements_to_update:
            ProposalElementServiceV2.process_bulk_update(element_entities=proposal_elements_to_update, user_id=user_id)
        if proposal_elements_to_delete:
            ProposalElementServiceV2.process_bulk_delete(element_entities=proposal_elements_to_delete, user_id=user_id)
        if proposal_elements_to_remove:
            ProposalElementServiceV2.process_bulk_remove(element_entities=proposal_elements_to_remove, user_id=user_id)
        action_histories = cls.create_element_history_when_order_exist(
            proposal_request=proposal_request,
            element_data_from_boq=proposal_elements_to_create
            + proposal_elements_to_update
            + proposal_elements_to_delete
            + proposal_elements_to_remove,
            user_id=user_id,
            order=proposal.order,
        )

        # Update linked Work Progress Element
        boq_element_to_updated_fields_map = (
            boq_element_to_updated_fields_map_create | boq_element_to_updated_fields_map_update
        )
        if boq_element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal update when order exist",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=boq_element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user_id,
                project_id=project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        fields = ["status"]
        if proposal_request.is_discount_col_visible is not None:
            fields.append("is_discounted")

        if proposal_request.is_service_charge_col_visible is not None:
            fields.append("is_service_charged")
        if proposal_request.update_prefill_data:
            fields.extend(["start_date", "due_date", "shipping_address"])

        model_update(
            instance=proposal,
            data={
                "status": ProposalStatus.PENDING_APPROVAL,
                "is_discounted": proposal_request.is_discount_col_visible,
                "is_service_charged": proposal_request.is_service_charge_col_visible,
                "start_date": proposal_request.start_date,
                "due_date": proposal_request.due_date,
                "shipping_address": proposal_request.shipping_address,
            },
            fields=fields,
            updated_by_id=user_id,
        )
        # Unlatch proposal elements that are linked to order and cancelled by client directly from order
        # Vendor requested that element in proposal,
        # but client cancelled it directly from order to avoid approval of cancelled element we need to unlatch it
        ProposalElementMapping.objects.filter(
            proposal_id=proposal.pk, element__element_status=BoqElementStatus.CANCELLED.value
        ).update(deleted_at=timezone.now(), deleted_by_id=user_id)
        proposal_updated_when_order_exist_trigger(
            proposal_id=proposal.pk,
            project_id=project_id,
            updated_by_id=user_id,
            org_id=proposal.proposal_from_id,
            version=proposal.version,
        )
        return proposal, order.elements_amount_value

    @classmethod
    def update_old_proposal_when_order_not_exist(
        cls, project_id: int, proposal_request: ProposalRequestChangeData, ref_number: str, user_id: int
    ) -> Proposal:
        logger.info(
            "Updating old proposal when order not exist", project_id=project_id, user_id=user_id, ref_number=ref_number
        )
        to_create, to_update, to_delete, to_remove = cls.proposal_entity_seggration(docs_list=proposal_request.elements)
        element_id_dict = {element.element_id: element for element in proposal_request.elements}
        proposal = (
            Proposal.objects.filter(ref_number=ref_number, project_id=project_id, deleted_at__isnull=True)
            .select_related("project", "proposal_from")
            .order_by("-created_at")
            .first()
        )
        (
            proposal_elements_to_create,
            boq_element_to_updated_fields_map_create,
        ) = cls.create_proposal_element_data_from_boq(element_entities=to_create)
        (
            proposal_elements_to_update,
            proposal_elements_to_delete,
            proposal_elements_to_remove,
            boq_element_to_updated_fields_map_update,
        ) = cls.create_proposal_element_data_from_old_proposal(
            proposal_id=proposal.pk,
            to_update=to_update,
            to_delete=to_delete,
            to_remove=to_remove,
            element_id_dict=element_id_dict,
        )
        if proposal_elements_to_create:
            ProposalElementServiceV2.process_bulk_create(
                proposal=proposal,
                element_entities=proposal_elements_to_create,
                user_id=user_id,
            )
        if proposal_elements_to_update:
            ProposalElementServiceV2.process_bulk_update(element_entities=proposal_elements_to_update, user_id=user_id)
        if proposal_elements_to_delete:
            ProposalElementServiceV2.process_bulk_delete(element_entities=proposal_elements_to_delete, user_id=user_id)
        if proposal_elements_to_remove:
            ProposalElementServiceV2.process_bulk_remove(element_entities=proposal_elements_to_remove, user_id=user_id)
        action_histories = cls.create_element_history_when_order_not_exist(
            proposal_request=proposal_request,
            element_data_from_boq=proposal_elements_to_create
            + proposal_elements_to_update
            + proposal_elements_to_remove,
            user_id=user_id,
            proposal=proposal,
        )

        # Update linked Work Progress Element
        boq_element_to_updated_fields_map = (
            boq_element_to_updated_fields_map_create | boq_element_to_updated_fields_map_update
        )
        if boq_element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal update when order not exist",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=boq_element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user_id,
                project_id=project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        fields = ["status", "is_discounted", "is_service_charged"]
        if proposal_request.update_prefill_data:
            fields.extend(["start_date", "due_date", "shipping_address"])
        model_update(
            instance=proposal,
            data={
                "status": ProposalStatus.PENDING_APPROVAL,
                "is_discounted": proposal_request.is_discount_col_visible,
                "is_service_charged": proposal_request.is_service_charge_col_visible,
                "start_date": proposal_request.start_date,
                "due_date": proposal_request.due_date,
                "shipping_address": proposal_request.shipping_address,
            },
            fields=fields,
            updated_by_id=user_id,
        )
        proposal_updated_when_order_not_exist_trigger(
            proposal_id=proposal.pk,
            project_id=project_id,
            updated_by_id=user_id,
            org_id=proposal.proposal_from_id,
            version=proposal.version,
        )
        return proposal

    @classmethod
    def get_proposal_ref_number(cls, project_id: int, proposal_request: ProposalRequestChangeData) -> Optional[str]:
        logger.info("Getting proposal ref number", project_id=project_id, order_number=proposal_request.order_number)
        return (
            VendorOrder.objects.filter(project_id=project_id, order_number=proposal_request.order_number.split("/")[1])
            .annotate(
                proposal_ref_number=Subquery(
                    Proposal.objects.filter(order_id=OuterRef("pk"), deleted_at__isnull=True)
                    .order_by("-created_at")
                    .values("ref_number")[:1]
                )
            )
            .values_list("proposal_ref_number", flat=True)
            .first()
        )

    @classmethod
    def generate_new_proposal_number(cls, project_id: int, order_number: Optional[int] = None) -> int:
        logger.info("Generating new proposal number", project_id=project_id, order_number=order_number)
        if Project.objects.get(id=project_id).version > 2:
            if order_number:
                return order_number
            else:
                return generate_proposal_and_order_number(project_id=project_id)
        else:
            return generate_proposal_and_order_number(project_id=project_id)

    @classmethod
    def _validate_element_quantity_dimensions(cls, proposal_request_change_datas: ProposalRequestChangeDatas):
        element_data = [
            element for proposal in proposal_request_change_datas.request_data for element in proposal.elements
        ]
        for element in element_data:
            if element.quantity_dimensions and element.uom:
                validate_quantity_dimensions(
                    quantity_dimensions=element.quantity_dimensions, quantity=element.quantity, quantity_uom=element.uom
                )

    @classmethod
    def process(
        cls,
        project_id: int,
        proposal_request_change_datas: ProposalRequestChangeDatas,
        org_id: int,
        user: User,
    ):
        logger.info("Proposal request change started")
        cls._validate_element_quantity_dimensions(proposal_request_change_datas)
        logger.info("Element quantity dimensions validated")
        for proposal_request in proposal_request_change_datas.request_data:
            logger.info("Processing proposal request", proposal_request_order_number=proposal_request.order_number)
            is_new_proposal_created = False
            if proposal_request.order_number:
                proposal_ref_number = cls.get_proposal_ref_number(
                    project_id=project_id, proposal_request=proposal_request
                )
                logger.info("Proposal referece number fetched", proposal_ref_number=proposal_ref_number)
                if proposal_ref_number:
                    ref_number = proposal_ref_number
                else:
                    ref_number = cls.generate_new_proposal_number(
                        project_id=project_id, order_number=proposal_request.order_number.split("/")[-1]
                    )
                logger.info("Reference number generated", ref_number=ref_number)
                proposal_status_mapping = (
                    ProposalStatusMapping.objects.filter(
                        proposal__ref_number=ref_number, proposal__project__id=project_id
                    )
                    .order_by("-created_at")
                    .select_related("proposal")
                    .first()
                )
                logger.info("Proposal status mapping fetched", proposal_status_mapping=proposal_status_mapping)
                if not proposal_status_mapping:
                    proposal, old_order_value = cls.create_new_proposal_when_order_exist(
                        proposal_request=proposal_request,
                        user_id=user.pk,
                        project_id=project_id,
                        org_id=org_id,
                        ref_number=ref_number,
                    )
                    is_new_proposal_created = True
                    logger.info("Proposal created when proposal status mapping not exists", proposal_id=proposal.pk)
                else:
                    if proposal_status_mapping.status in [
                        ProposalStatusMapping.ProposalStatus.APPROVED,
                        ProposalStatusMapping.ProposalStatus.REJECTED,
                    ]:
                        proposal, old_order_value = cls.create_new_proposal_when_order_exist(
                            proposal_request=proposal_request,
                            user_id=user.pk,
                            project_id=project_id,
                            org_id=org_id,
                            ref_number=ref_number,
                        )
                        is_new_proposal_created = True
                        logger.info("Proposal created when proposal status mapping exists", proposal_id=proposal.pk)
                    else:
                        proposal, old_order_value = cls.update_old_proposal_when_order_exist(
                            project_id=project_id,
                            proposal_request=proposal_request,
                            ref_number=ref_number,
                            user_id=user.pk,
                        )
                        logger.info("Proposal updated", proposal_id=proposal.pk)

                if proposal_request_change_datas.to_send_email:
                    on_commit(
                        partial(
                            proposal_request_for_order_change_trigger_event,
                            proposal=proposal,
                            user_id=user.pk,
                            proposal_request=proposal_request,
                            old_order_value=old_order_value,
                        )
                    )
            else:
                if proposal_request.ref_number:
                    proposal = cls.update_old_proposal_when_order_not_exist(
                        project_id=project_id,
                        proposal_request=proposal_request,
                        ref_number=proposal_request.ref_number,
                        user_id=user.pk,
                    )
                else:
                    proposal = cls.create_new_proposal_when_order_not_exist(
                        proposal_request=proposal_request,
                        user_id=user.pk,
                        project_id=project_id,
                        org_id=org_id,
                        ref_number=cls.generate_new_proposal_number(project_id=project_id),
                    )
                    is_new_proposal_created = True

                if proposal_request_change_datas.to_send_email:
                    on_commit(
                        partial(
                            proposal_request_for_new_order_trigger_event,
                            proposal=proposal,
                            user_id=user.pk,
                            proposal_request=proposal_request,
                        )
                    )
            VendorUserService().create_poc_on_proposal_sent(proposal_creator=user, proposal=proposal)
            if proposal_request_change_datas.to_send_email:
                receiver = (
                    active_user_get_all()
                    .filter(email=[proposal_request.receiver[0]], org_id=proposal.proposal_for_id)
                    .first()
                )

                if receiver:
                    ProjectOrgPocService(
                        client_id=receiver.org.id, poc_id=receiver.id, project_id=project_id, user_id=user.pk
                    ).check_or_assign_poc()
            if proposal_request.update_prefill_data or is_new_proposal_created:
                hidden_client_fields = []
                for field in proposal_request.summary_fields + proposal_request.column_fields:
                    if field.is_visible is False:
                        hidden_client_fields.append(field.id)
                ProposalMetaData.objects.update_or_create(
                    proposal_id=proposal.pk,
                    defaults={"hidden_fields": hidden_client_fields, "updated_by_id": user.pk},
                )


class ProjectOrgPocService:
    """Service to check or assign dynamic role for proposal
    approver in client organization"""

    client_id: int = None  # client organization
    poc_id: int = None  # proposal approver
    project_id: int = None
    user_id: int = None  # current user
    permissions: List[str] = [
        Permissions.CAN_ACCESS_ORDER,
        Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
        Permissions.CAN_ACCESS_OUTGOING_ORDER,
        Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
        Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
        Permissions.CAN_APPROVE_PROPOSAL,
        Permissions.CAN_VIEW_CLIENT_RATE,
        Permissions.CAN_VIEW_ORDER_RATE,
    ]

    def __init__(self, client_id: int, poc_id: int, project_id: int, user_id: int):
        self.client_id = client_id
        self.poc_id = poc_id
        self.project_id = project_id
        self.user_id = user_id

    def get_or_create_proposal_approver_role(self) -> Role:
        project_assignments = ProjectAssignmentCache.get(instance_id=self.project_id)
        if str(self.client_id) not in project_assignments:
            raise ProjectNotAssignedToOrgException("Project is not assigned to given organization")

        org_assignment_cache_data = OrgAssignmentCache.get(instance_id=self.client_id)
        if str(self.poc_id) not in org_assignment_cache_data:
            raise OrgUserNotFoundException("User is not assigned in given organization")

        role = (
            Role.objects.annotate(lower_name=Lower("name"))
            .filter(organization_id=self.client_id, lower_name=ReservedRoleNames.PROPOSAL_APPROVER.value.lower())
            .first()
        )
        if role:
            return role
        role_data = RoleCreateData(
            name=ReservedRoleNames.PROPOSAL_APPROVER.value,
            level=ProjectUserRoleLevelChoices.LEVEL_97.value,
            notifications=[],
        )

        role = ProjectDynamicRoleCreateService().create_project_user_role(
            data=role_data,
            organization_id=self.client_id,
            org_type=OrganizationType.CLIENT,
            created_by_id=self.user_id,
        )

        return role

    def check_or_assign_poc(self) -> tuple:
        try:
            poc_user = get_user_and_permission_check_with_token_data(user_id=self.poc_id)
            ProjectPermissionHelper.is_permitted(project_id=self.project_id, user=poc_user)
        except OrgUserNotFoundException:
            return False, False
        except PermissionDenied:
            logger.info("POC not assigned already")
            proposal_approver_role: Role = self.get_or_create_proposal_approver_role()
            ProjectUser.objects.get_or_create(
                user_id=self.poc_id,
                project_id=self.project_id,
                role_id=proposal_approver_role.id,
                defaults={"created_by_id": self.user_id},
            )
            logger.info("New POC created", poc_id=self.poc_id)
            return False, True
