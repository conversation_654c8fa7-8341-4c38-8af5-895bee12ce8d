import decimal
from collections import defaultdict
from typing import List

import structlog
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    Count,
    F,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
)
from django.db.models.functions import Concat
from django.utils.module_loading import import_string

from authorization.domain.constants import Permissions
from boq.data.models import BoqElement
from common.helpers import calculate_element_final_amount
from controlroom.data.models import OrganizationDocumentFieldContextConfig
from core.helpers import OrgPermissionHelper
from core.models import Organization, User
from core.organization.data.models import OrganizationDocumentTextFieldData
from element.data.models import ElementCategory
from microcontext.choices import MicroContextChoices
from order.data.models import OrderTextFieldData, VendorOrder
from order.domain.constants import OrderStatusEnum
from project.data.models import Project
from proposal.data.entities import ProposalElementSummaryEntity
from proposal.data.models import (
    Proposal,
    ProposalElementGuideline,
    ProposalElementMapping,
    ProposalElementPreviewFile,
    ProposalElementProductionDrawing,
    ProposalGuidelineAttachment,
    ProposalStatusMapping,
    ProposalTextFieldData,
)
from proposal.domain.constants import ProposalStatus
from rollingbanners.comment_base_service import CommentBaseService

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)
logger = structlog.get_logger(__name__)


def get_vendor_proposal_list_v2(project_id: int, proposal_from_id: int, org_id: int):
    proposal_ids = (
        Proposal.objects.filter(project_id=project_id, proposal_from_id=proposal_from_id, deleted_at__isnull=True)
        .annotate(
            proposal_id=Subquery(
                Proposal.objects.filter(project_id=OuterRef("project_id"), ref_number=OuterRef("ref_number"))
                .order_by("-created_at")
                .values("id")[:1]
            )
        )
        .values_list("proposal_id", flat=True)
        .distinct("proposal_id")
    )
    return (
        Proposal.objects.filter(id__in=proposal_ids)
        .prefetch_related(
            Prefetch(
                "statuses",
                queryset=ProposalStatusMapping.objects.all().select_related("created_by").order_by("-created_at"),
            ),
        )
        .select_related("proposal_from", "updated_by", "proposal_for")
        .annotate(
            element_category=ArrayAgg(
                "proposal_element_mappings__element__category__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            ),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
            )
        )
        .annotate(
            item_type=ArrayAgg(
                "proposal_element_mappings__element__item_type__name",
                filter=Q(
                    proposal_element_mappings__deleted_at__isnull=True,
                    proposal_element_mappings__element__item_type__isnull=False,
                ),
                distinct=True,
            )
        )
        .annotate(
            element_count=Count(
                "proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True)
            )
        )
        .annotate_total_amount()
    )


def get_client_proposal_list_v2(project_id: int, proposal_for_id: int, org_id: int):
    proposal_ids = (
        Proposal.objects.filter(project_id=project_id, proposal_for_id=proposal_for_id, deleted_at__isnull=True)
        .annotate(
            proposal_id=Subquery(
                Proposal.objects.filter(project_id=OuterRef("project_id"), ref_number=OuterRef("ref_number"))
                .order_by("-created_at")
                .values("id")[:1]
            )
        )
        .values_list("proposal_id", flat=True)
        .distinct()
    )
    proposal_status_subquery = ProposalStatusMapping.objects.filter(proposal_id=OuterRef("pk")).order_by("-created_at")
    proposals = (
        Proposal.objects.filter(id__in=proposal_ids)
        .annotate(current_status=Subquery(proposal_status_subquery.values("status")[:1]))
        .exclude(current_status=ProposalStatusMapping.ProposalStatus.DRAFT)
        .prefetch_related(
            "proposal_from__vendor",
            Prefetch("statuses", queryset=ProposalStatusMapping.objects.all().order_by("-created_at")),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
            )
        )
        .annotate(
            element_category=ArrayAgg(
                "proposal_element_mappings__element__category__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            )
        )
        .annotate(
            item_type=ArrayAgg(
                "proposal_element_mappings__element__item_type__name",
                filter=Q(
                    proposal_element_mappings__deleted_at__isnull=True,
                    proposal_element_mappings__element__item_type__isnull=False,
                ),
                distinct=True,
            )
        )
        .annotate(
            element_count=Count(
                "proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True)
            )
        )
        .annotate_total_amount()
    )
    return proposals


def get_org_name(*, org_id: int) -> str:
    return Organization.objects.filter(id=org_id).values_list("name", flat=True).first()


def get_elements_data(*, proposal_id: int) -> List[BoqElement]:
    proposal_elements = ProposalElementMapping.objects.filter(proposal_id=proposal_id).select_related("element")
    boq_elements = []
    for element in proposal_elements:
        setattr(element.element, "amount", element.element.client_rate * element.element.quantity)
        boq_elements.append(element.element)
    return boq_elements


def get_elements_data_v2(*, proposal_id: int) -> List[ProposalElementMapping]:
    proposal_elements: List[ProposalElementMapping] = (
        ProposalElementMapping.objects.filter(
            proposal_id=proposal_id,
            # object_status__in=[ObjectStatus.ADD.value, ObjectStatus.UPDATE.value, ObjectStatus.DELETE.value],
        )
        .available()
        .all()
    )
    boq_elements = []
    for element in proposal_elements:
        setattr(
            element,
            "amount",
            calculate_element_final_amount(
                rate=element.client_rate,
                quantity=element.quantity,
                discount_percent=element.discount_percent,
                service_charge_percent=element.service_charge_percent,
                is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
            ),
        )
        setattr(element, "object_status", element.object_status)
        boq_elements.append(element)
    return boq_elements


def get_proposal_ref_number(*, project_id: int, ref_number: int) -> str:
    job_id = Project.objects.filter(id=project_id).values_list("job_id", flat=True).first()
    return f"{job_id}/{ref_number}"


def get_order_number(*, project_id: int, order_number: int) -> str:
    job_id = Project.objects.filter(id=project_id).values_list("job_id", flat=True).first()
    return f"{job_id}/{order_number}"


def get_proposal(*, proposal_id: int) -> QuerySet:
    return Proposal.objects.filter(id=proposal_id).annotate(
        elements_count=Count("proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True))
    )


def get_proposal_elements(*, proposal_id: int) -> QuerySet:
    return (
        ProposalElementMapping.objects.filter(proposal_id=proposal_id, deleted_at__isnull=True)
        .prefetch_related(
            Prefetch("guidelines", queryset=ProposalElementGuideline.objects.available().all()),
            Prefetch("production_drawings", queryset=ProposalElementProductionDrawing.objects.available().all()),
            Prefetch("preview_files", queryset=ProposalElementPreviewFile.objects.available().all()),
            Prefetch("guidelines__attachments", queryset=ProposalGuidelineAttachment.objects.available().all()),
        )
        .available()
        .all()
    )


def get_proposal_elements_count(*, proposal_id: int) -> int:
    return ProposalElementMapping.objects.filter(proposal_id=proposal_id, deleted_at__isnull=True).count()


def get_work_order_from(*, org_id: int) -> QuerySet:
    return (
        Organization.objects.filter(id=org_id).annotate(organization_id=F("id")).values_list("name", flat=True).first()
    )


def has_client_rate_permission(*, user: User) -> bool:
    return OrgPermissionHelper.has_permission(user=user, permission=Permissions.CAN_VIEW_CLIENT_RATE)


def proposal_receiver_user(*, receiver_email: str) -> User:
    return User.objects.filter(email=receiver_email).first()


def proposal_history_queryset(*, proposal_id: int, project_id: int) -> QuerySet:
    ref_number = Proposal.objects.filter(id=proposal_id).values_list("ref_number", flat=True).first()
    return (
        Proposal.objects.filter(project_id=project_id, ref_number=ref_number)
        .exclude(id=proposal_id)
        .all()
        .order_by("-created_at")
    )


def proposal_history_vendor_list(*, proposal_id: int, project_id: int, org_id: int) -> List[Proposal]:
    return (
        proposal_history_queryset(proposal_id=proposal_id, project_id=project_id)
        .prefetch_related(
            Prefetch("statuses", queryset=ProposalStatusMapping.objects.all().order_by("-created_at")),
        )
        .annotate(
            element_category=ArrayAgg(
                "proposal_element_mappings__element__category__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            ),
            proposal_amount=Sum(
                F("proposal_element_mappings__client_rate") * F("proposal_element_mappings__quantity"),
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
            ),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
            )
        )
        .annotate(
            item_type=ArrayAgg(
                "proposal_element_mappings__element__item_type__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            )
        )
        .annotate(
            element_count=Count(
                "proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True)
            )
        )
    )


def proposal_history_client_list(*, proposal_id: int, project_id: int, org_id: int) -> List[Proposal]:
    proposal_status_subquery = ProposalStatusMapping.objects.filter(proposal_id=OuterRef("pk")).order_by("-created_at")
    return (
        proposal_history_queryset(proposal_id=proposal_id, project_id=project_id)
        .annotate(current_status=Subquery(proposal_status_subquery.values("status")[:1]))
        .exclude(current_status=ProposalStatusMapping.ProposalStatus.DRAFT)
        .prefetch_related(
            "proposal_from__vendor",
            Prefetch("statuses", queryset=ProposalStatusMapping.objects.all().order_by("-created_at")),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.PROPOSAL.name, org_id=org_id
            )
        )
        .annotate(
            element_category=ArrayAgg(
                "proposal_element_mappings__element__category__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            ),
            proposal_amount=Sum(
                F("proposal_element_mappings__client_rate") * F("proposal_element_mappings__quantity"),
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
            ),
        )
        .annotate(
            item_type=ArrayAgg(
                "proposal_element_mappings__element__item_type__name",
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
                distinct=True,
            )
        )
        .annotate(
            element_count=Count(
                "proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True)
            )
        )
    )


def proposal_and_order_dropdown_list(*, project_id: int, org_id: int) -> List:
    subquery = (
        Proposal.objects.filter(project_id=project_id, order_id=OuterRef("id"), deleted_at__isnull=True)
        .filter(status__in=[ProposalStatus.APPROVED.value, ProposalStatus.PENDING_APPROVAL.value])
        .order_by("-created_at")
    )
    orders = list(
        VendorOrder.objects.annotate_elements_final_amount()
        .annotate_last_approved_snapshot()
        .filter(project_id=project_id, org_to_id=org_id)
        .annotate(
            elements_count=Count("order_elements", filter=Q(order_elements__deleted_at__isnull=True)),
            total_amount=Sum(
                F("elements_final_amount"),
                filter=Q(order_elements__deleted_at__isnull=True),
            ),
            proposal_number=Subquery(subquery.values("ref_number")[:1]),
            proposal_id=Subquery(subquery.values("id")[:1]),
            new_order_number=Concat(F("project__job_id"), Value("/"), F("order_number"), output_field=CharField()),
        )
        .filter(
            Q(outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value])
            | Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value, last_approved_snapshot__isnull=False)
        )
        .exclude(
            outgoing_status__in=[
                OrderStatusEnum.DRAFT.value,
                OrderStatusEnum.CANCELLED.value,
                OrderStatusEnum.REJECTED.value,
                OrderStatusEnum.CLOSED.value,
            ]
        )
        .all()
    )
    proposals = list(
        Proposal.objects.filter(
            project_id=project_id,
            proposal_from_id=org_id,
            deleted_at__isnull=True,
            order_id__isnull=True,
            status=ProposalStatusMapping.ProposalStatus.PENDING_APPROVAL,
        )
        .annotate(
            elements_count=Count(
                "proposal_element_mappings", filter=Q(proposal_element_mappings__deleted_at__isnull=True)
            ),
            total_amount=Sum(
                F("proposal_element_mappings__client_rate") * F("proposal_element_mappings__quantity")
                - F("proposal_element_mappings__client_rate")
                * F("proposal_element_mappings__quantity")
                * F("proposal_element_mappings__discount_percent")
                / 100,
                filter=Q(proposal_element_mappings__deleted_at__isnull=True),
            ),
            proposal_number=F("ref_number"),
        )
        .all()
    )
    return proposals + orders


def category_wise_group_boq_elements(*, elements, category_mapping: dict) -> List[dict]:
    # TODO: Write test cases for this function
    category_wise_amount_grouping = {}
    for element in elements:
        category_name = category_mapping[element.category_id].name
        if category_name in category_wise_amount_grouping:
            category_wise_amount_grouping[category_name] += element.quantity * element.client_rate
        else:
            category_wise_amount_grouping[category_name] = element.quantity * element.client_rate
    return ({"Category": category, "Amount": amount} for category, amount in category_wise_amount_grouping.items())


def get_grouped_boq_elements_v2(*, elements) -> List[dict]:
    category_mapping = ElementCategory.objects.in_bulk()
    return category_wise_group_boq_elements(elements=elements, category_mapping=category_mapping)


def group_element_by_category(*, elements: QuerySet[ProposalElementMapping]) -> dict[str, ProposalElementSummaryEntity]:
    summary_dict = defaultdict(lambda: ProposalElementSummaryEntity())
    for element in elements:
        summary_dict[element.category.name].category_name = element.category.name
        summary_dict[element.category.name].base_amount += decimal.Decimal(element.base_amount)
        summary_dict[element.category.name].service_charge_amount += decimal.Decimal(element.service_charge)
        summary_dict[element.category.name].gross_amount += decimal.Decimal(element.gross_amount)
        summary_dict[element.category.name].discounted_value += decimal.Decimal(element.discounted_value)
        summary_dict[element.category.name].amount_without_gst += decimal.Decimal(element.amount_without_tax)
        summary_dict[element.category.name].gst_value += decimal.Decimal(element.tax_amount)
        summary_dict[element.category.name].final_amount += decimal.Decimal(element.final_amount)
    return summary_dict


def fetch_text_data_obj_of_context_field_config(
    vendor_id: int, context_config: OrganizationDocumentFieldContextConfig
) -> OrganizationDocumentTextFieldData:
    logger.info(
        "Fetching OrganizationDocumentTextFieldData object",
        context_config_id=context_config.pk,
        vendor_id=vendor_id,
    )
    text_object = (
        OrganizationDocumentTextFieldData.objects.filter(
            field_config_id=context_config.field_config_id, document__organization_id=vendor_id
        )
        .available()
        .first()
    )
    return text_object


def fetch_text_data_from_order_context(
    order_id: int, context_config: OrganizationDocumentFieldContextConfig
) -> OrderTextFieldData:
    logger.info("Fetching OrderTextFieldData object", context_config_id=context_config.pk, order_id=order_id)
    order_text_obj = (
        OrderTextFieldData.objects.filter(order_id=order_id, context_config_id=context_config.pk).available().first()
    )
    return order_text_obj


def fetch_proposal_document_config_base_selector(country_id: int) -> QuerySet[OrganizationDocumentFieldContextConfig]:
    proposal_config_field = OrganizationDocumentFieldContextConfig.objects.filter(
        context=MicroContextChoices.PROPOSAL, country_id=country_id, is_client_data=False
    ).select_related("field_config", "field_config__document_config", "field_config__document_config__section")
    return proposal_config_field


def fetch_proposal_document_config(country_id: int, proposal_id: int):
    return fetch_proposal_document_config_base_selector(country_id=country_id).prefetch_related(
        Prefetch(
            "proposal_text_field_data",
            queryset=ProposalTextFieldData.objects.available().filter(proposal_id=proposal_id),
        )
    )
