from typing import Optional

import structlog

from boq.data.choices import BoqElementStatus
from boq.data.models import BoqElementActionHistory
from client.domain.constants import ClientFields
from common.element_base_serializer import ElementUomInitializer
from common.entities import ObjectStatus
from common.excel.constants import (
    ElementExcelColumnsEnum,
    FontEnum,
    HorizontalAlignmentEnum,
    SheetCellBorderEnum,
    SheetCellTypeEnum,
    SummaryExcelColumnsEnum,
    VerticalAlignmentEnum,
)
from common.excel.data_builder import ElementExcelDataBaseBuilder
from common.excel.entities import SheetCell, SheetRowData
from proposal.data.entities import ProposalElementSummaryEntity
from proposal.data.models import Proposal, ProposalElementMapping
from proposal.data.selectors import get_elements_data_v2, group_element_by_category

logger = structlog.get_logger(__name__)


class ProposalExcelDataBuilder(ElementExcelDataBaseBuilder):
    _element_col_display_text_mapping = {
        ElementExcelColumnsEnum.S_NO: "S No",
        ElementExcelColumnsEnum.ITEM_NAME: "Item Name",
        ElementExcelColumnsEnum.DESCRIPTION: "Description",
        ElementExcelColumnsEnum.BRAND_NAME: "Brand / Make",
        ElementExcelColumnsEnum.ITEM_CODE: "Item Code",
        ElementExcelColumnsEnum.QUANTITY: "Quantity",
        ElementExcelColumnsEnum.LENGTH: "Length",
        ElementExcelColumnsEnum.LENGTH_UOM: "Length UOM",
        ElementExcelColumnsEnum.BREADTH: "Breadth",
        ElementExcelColumnsEnum.BREADTH_UOM: "Breadth UOM",
        ElementExcelColumnsEnum.UOM: "UOM",
        ElementExcelColumnsEnum.CLIENT_RATE: "Client Rate",
        ElementExcelColumnsEnum.BASE_AMOUNT: "Base Amount",
        ElementExcelColumnsEnum.GROSS_AMOUNT: "Gross Amount",
        ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT: "Service Charge %",
        ElementExcelColumnsEnum.DISCOUNT_PERCENT: "Discount %",
        ElementExcelColumnsEnum.DISCOUNT_VALUE: "Discount Value",
        ElementExcelColumnsEnum.HSN_CODE: "HSN",
        ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX: "Amount (w/o Tax)",
        ElementExcelColumnsEnum.TAX_PERCENT: "Tax %",
        ElementExcelColumnsEnum.FINAL_AMOUNT: "Final Amount",
        ElementExcelColumnsEnum.REMARK: "Remark",
    }
    _summary_cols_display_text_mapping = {
        SummaryExcelColumnsEnum.S_NO: "S No",
        SummaryExcelColumnsEnum.CATEGORY: "Category",
        SummaryExcelColumnsEnum.AMOUNT: "Amount",
    }

    _proposal_id: int = None
    _proposal: Proposal = None
    _elements: list[ProposalElementMapping] = []
    _summary: dict[str, ProposalElementSummaryEntity] = {}
    _uom_helper: ElementUomInitializer = None
    _hidden_fields: list = []

    def __init__(
        self,
        proposal_id: int,
        hidden_fields: list = [],
    ):
        super().__init__()
        self._proposal_id = proposal_id
        self._proposal = (
            Proposal.objects.select_related("proposal_for", "project", "proposal_from", "created_by")
            .filter(pk=proposal_id)
            .first()
        )
        self._elements = get_elements_data_v2(proposal_id=proposal_id)
        self._summary = group_element_by_category(elements=self._elements)
        self._uom_helper = ElementUomInitializer()
        self._hidden_fields = hidden_fields

    def get_file_name(self) -> str:
        return (
            f"Proposal_{self._proposal.project.job_id}/{self._proposal.ref_number}_{self._proposal.proposal_for.name}"
        )

    def get_element_cell_data(self, element: ProposalElementMapping, col: ElementExcelColumnsEnum) -> SheetCell:
        if col == ElementExcelColumnsEnum.ITEM_NAME:
            return SheetCell(value=element.name)
        elif col == ElementExcelColumnsEnum.DESCRIPTION:
            return SheetCell(value=element.description)
        elif col == ElementExcelColumnsEnum.BRAND_NAME:
            return SheetCell(value=element.brand_name)
        elif col == ElementExcelColumnsEnum.ITEM_CODE:
            return SheetCell(value=element.element.get_element_code())
        elif col == ElementExcelColumnsEnum.QUANTITY:
            return SheetCell(value=round(element.quantity, 2))
        elif col == ElementExcelColumnsEnum.UOM:
            return SheetCell(value=self._uom_helper.uom_name_get(element.uom))
        elif col == ElementExcelColumnsEnum.CLIENT_RATE:
            return SheetCell(value=round(element.client_rate, 2))
        elif col == ElementExcelColumnsEnum.BASE_AMOUNT:
            return SheetCell(value=element.base_amount)
        elif col == ElementExcelColumnsEnum.DISCOUNT_PERCENT:
            return SheetCell(value=round(element.discount_percent, 2))
        elif col == ElementExcelColumnsEnum.DISCOUNT_VALUE:
            return SheetCell(
                value=(
                    element.updated_data.get("discounted_value")
                    if hasattr(element, "updated_data")
                    and element.updated_data
                    and element.updated_data.get("discounted_value") is not None
                    else element.discounted_value
                )
            )
        elif col == ElementExcelColumnsEnum.HSN_CODE:
            return SheetCell(value=element.hsn_code)
        elif col == ElementExcelColumnsEnum.TAX_PERCENT:
            return SheetCell(value=round(element.tax_percent, 2))
        elif col == ElementExcelColumnsEnum.FINAL_AMOUNT:
            return SheetCell(round(element.final_amount, 2))
        elif col == ElementExcelColumnsEnum.REMARK:
            remark, color = self.get_element_remark(element=element)
            return SheetCell(value=remark, color=color)
        elif col == ElementExcelColumnsEnum.GROSS_AMOUNT:
            return SheetCell(value=element.gross_amount)
        elif col == ElementExcelColumnsEnum.LENGTH:
            return SheetCell(value=element.get_length())
        elif col == ElementExcelColumnsEnum.BREADTH:
            return SheetCell(value=element.get_breadth())
        elif col == ElementExcelColumnsEnum.LENGTH_UOM:
            return SheetCell(value=self._uom_helper.uom_name_get(element.get_breadth_uom()))
        elif col == ElementExcelColumnsEnum.BREADTH_UOM:
            return SheetCell(value=self._uom_helper.uom_name_get(element.get_length_uom()))
        elif col == ElementExcelColumnsEnum.SERVICE_CHARGE_PERCENT:
            return SheetCell(value=element.service_charge)
        elif col == ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX:
            return SheetCell(value=element.amount_without_tax)
        else:
            raise ValueError(f"Unknown column {col}")

    def get_summary_sheet_rows(self) -> list[SheetRowData]:
        rows = [SheetRowData(data={key: SheetCell(value="") for key in self._summary_cols})]
        count = 1
        base_amount = 0
        service_charge_amount = 0
        gross_amount = 0
        discounted_value = 0
        amount_without_gst = 0
        tax_amount = 0
        final_amount = 0

        for category_name, data in self._summary.items():
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value=str(count))
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(value=category_name)
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(value=data.base_amount, type=SheetCellTypeEnum.DECIMAL)
                    base_amount += data.base_amount
                    service_charge_amount += data.service_charge_amount
                    gross_amount += data.gross_amount
                    discounted_value += data.discounted_value
                    amount_without_gst += data.amount_without_gst
                    tax_amount += data.gst_value
                    final_amount += data.final_amount
            rows.append(SheetRowData(data=row_data))
            count += 1

        row_data = {}
        if service_charge_amount and ClientFields.TOTAL_SERVICE_CHARGE.value not in self._hidden_fields:
            if ClientFields.TOTAL_BASE_AMOUNT.value not in self._hidden_fields:
                for key in self._summary_cols:
                    if key == SummaryExcelColumnsEnum.S_NO:
                        row_data[key] = SheetCell(value="")
                    elif key == SummaryExcelColumnsEnum.CATEGORY:
                        row_data[key] = SheetCell(
                            value="Base Amount",
                            font=FontEnum.DEFAULT,
                            border=SheetCellBorderEnum.TOP,
                            horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        )
                    elif key == SummaryExcelColumnsEnum.AMOUNT:
                        row_data[key] = SheetCell(
                            value=base_amount,
                            type=SheetCellTypeEnum.DECIMAL,
                            font=FontEnum.DEFAULT,
                            border=SheetCellBorderEnum.TOP,
                            horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        )
                rows.append(SheetRowData(data=row_data))
            row_data = {}

            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(
                        value="Service Charge Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=service_charge_amount,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        if discounted_value and ClientFields.TOTAL_DISCOUNT.value not in self._hidden_fields:
            if ClientFields.TOTAL_GROSS_AMOUNT.value not in self._hidden_fields:
                for key in self._summary_cols:
                    if key == SummaryExcelColumnsEnum.S_NO:
                        row_data[key] = SheetCell(value="")
                    elif key == SummaryExcelColumnsEnum.CATEGORY:
                        row_data[key] = SheetCell(
                            value="Gross Amount",
                            font=FontEnum.DEFAULT,
                            border=SheetCellBorderEnum.TOP,
                            horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        )
                    elif key == SummaryExcelColumnsEnum.AMOUNT:
                        row_data[key] = SheetCell(
                            value=gross_amount,
                            type=SheetCellTypeEnum.DECIMAL,
                            font=FontEnum.DEFAULT,
                            border=SheetCellBorderEnum.TOP,
                            horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        )
                rows.append(SheetRowData(data=row_data))
            row_data = {}

            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(
                        value="Discount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=discounted_value,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        # No client field available in org-setting to control Total Tax Amount
        # if Total amount w/o tax is shown, then total tax amount is shown
        if tax_amount and ClientFields.TOTAL_AMOUNT_WITHOUT_TAX.value not in self._hidden_fields:
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(
                        value="Amount (w/o Tax)",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=amount_without_gst,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                        value="Tax Amount",
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=tax_amount,
                        type=SheetCellTypeEnum.DECIMAL,
                        font=FontEnum.DEFAULT,
                        border=SheetCellBorderEnum.BOTTOM,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        row_data = {}
        for key in self._summary_cols:
            if key == SummaryExcelColumnsEnum.S_NO:
                row_data[key] = SheetCell(value="")
            elif key == SummaryExcelColumnsEnum.CATEGORY:
                final_amount_text = "Total Amount" if not tax_amount else "Total Amount (With Tax)"

                row_data[key] = SheetCell(
                    value=final_amount_text,
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
            elif key == SummaryExcelColumnsEnum.AMOUNT:
                row_data[key] = SheetCell(
                    value=final_amount,
                    type=SheetCellTypeEnum.DECIMAL,
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
        rows.append(SheetRowData(data=row_data))
        logger.info("Proposal summary sheet data created")

        return rows

    def get_element_header_background_color(self) -> str:
        return "FFC7CE"

    def get_element_sheet_header_font(self) -> FontEnum:
        return FontEnum.BOLD

    def get_element_sheet_header_horizontal_alignment(self) -> HorizontalAlignmentEnum:
        return HorizontalAlignmentEnum.CENTER

    def get_element_sheet_header_vertical_alignment(self) -> VerticalAlignmentEnum:
        return VerticalAlignmentEnum.CENTER

    def get_org_logo_url(self) -> Optional[str]:
        return self._proposal.proposal_from.logo.url if self._proposal.proposal_from.logo.name else None

    def get_org_name(self) -> str:
        return self._proposal.proposal_from.name

    def get_element_remark(self, element: ProposalElementMapping) -> tuple[str, str]:
        remark = None
        color = "000000"
        if element.object_status == ObjectStatus.ADD.value:
            remark = "Request for Order"
            color = "61C7CD"  # green
        elif element.object_status == ObjectStatus.UPDATE.value:
            remark = "Request for Change"
            color = "EA9245"  # yellow
            if (
                BoqElementActionHistory.objects.filter(
                    boq_element_id=element.element_id, status=BoqElementStatus.APPROVED
                ).count()
                >= 2
            ):
                action_history = (
                    BoqElementActionHistory.objects.filter(
                        boq_element_id=element.element_id, status=BoqElementStatus.APPROVED
                    )
                    .order_by("-created_at")
                    .values("client_rate", "quantity", "discount_percent")[1]
                )
                remark = f"Request for Change \n (prev rate:{action_history.get('client_rate', 0) - action_history.get('discount_percent', 0)},qty:{action_history.get('quantity')}"  # noqa
        elif element.object_status == ObjectStatus.DELETE.value:
            remark = "Request for Cancel"  # red
            color = "EC2245"
        return remark, color
