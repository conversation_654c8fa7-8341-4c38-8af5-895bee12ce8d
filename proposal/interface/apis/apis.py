from typing import Dict

from django.db import transaction
from django.db.models import OuterRef, Subquery
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST, HTTP_406_NOT_ACCEPTABLE

from authorization.domain.constants import Permissions
from authorization.domain.services import permission_get_all
from common.exceptions import QuantityValidationError, UOMValidationError
from common.serializers import BaseSerializer, HashIdField
from core.organization.domain.entities import OrganizationSectionData
from microcontext.choices import MicroContextActions, MicroContextChoices
from order.domain.entities.domain_entities import OrderSentInitializeData
from order.interface.serializers import OrderSentInitializerDataSerializer
from order.interface.vendor_serializers import VendorOrderModelSerializer
from order.services.order import OrderSentProcessorService
from project.data.models import Store
from project.interface.apis.internal.apis import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from proposal.data.entities import Proposal<PERSON><PERSON>rove<PERSON><PERSON>, Proposal<PERSON><PERSON><PERSON><PERSON>s
from proposal.data.models import <PERSON>posal, ProposalElementMapping, ProposalEmail, ProposalSection
from proposal.data.selectors import (
    get_client_proposal_list_v2,
    get_vendor_proposal_list_v2,
    proposal_and_order_dropdown_list,
    proposal_history_client_list,
    proposal_history_vendor_list,
)
from proposal.domain.constants import Action, ProposalRequestType
from proposal.domain.proposal_services import ProposalService
from proposal.domain.services import (
    get_default_prefill_details_for_proposal,
    get_element_details,
    get_hidden_fields,
    get_prefill_details_for_proposal_using_current_data,
    get_proposal_actions,
    get_proposal_client_field_settings,
    get_proposal_details,
    get_proposal_details_from_ref_number,
    get_shipping_address_dropdown_list,
    insert_proposal_status,
    prefetch_proposal_details,
    proposal_approve_v2,
    proposal_recipient_email_list,
    proposal_reject,
    update_status_update_time,
)
from proposal.interface.exceptions import ProposalApprovalDataValidationException, ProposalApprovedNotAllowed
from proposal.interface.mappings import PROPOSAL_FIELDS_MAPPING
from proposal.interface.serializers import (
    OrganizationAddressSerializer,
    ProposalApproveDataSerializer,
    ProposalElementMappingModelSerializer,
    ProposalModelSerializer,
    ProposalRequestChangeDatasSerializer,
    ProposalSectionModelSerializer,
    ProposalSupportingDocumentSerializer,
    ProposalTNCAttachmentSerializer,
    UserModelSerializer,
    VendorDetailSerializer,
)
from vendorv2.data.selectors.selectors import mapping_get
from vendorv2.domain.services.user import VendorUserService


class ProposalDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        class SectionSerializer(ProposalSectionModelSerializer):
            element_count = serializers.IntegerField()

            class Meta(ProposalSectionModelSerializer.Meta):
                fields = ("id", "name", "element_count")
                ref_name = "ProposalSectionSerializer"

        sections = serializers.SerializerMethodField()
        proposal_id = HashIdField(allow_null=True, source="id")
        vendor_details = serializers.SerializerMethodField()
        client = serializers.CharField(source="proposal_for")
        client_id = HashIdField(source="proposal_for.id")
        status = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        shipping_address_list = serializers.SerializerMethodField()
        supporting_documents = ProposalSupportingDocumentSerializer(many=True)
        tnc_attachments = ProposalTNCAttachmentSerializer(many=True)
        elements = ProposalElementMappingModelSerializer(many=True, source="proposal_element_mappings")
        comment_count = serializers.SerializerMethodField()
        purchase_orders = serializers.SerializerMethodField()
        order_id = HashIdField()
        order_number = serializers.SerializerMethodField()
        ref_number = serializers.SerializerMethodField()
        org_to_id = HashIdField(source="proposal_for.id")
        work_order_from = serializers.CharField(source="proposal_from.name")
        shipping_address = serializers.SerializerMethodField()
        start_date = serializers.SerializerMethodField()
        due_date = serializers.SerializerMethodField()
        actions = serializers.SerializerMethodField()
        document_config = serializers.SerializerMethodField()
        # control column visibility on vendor & client view
        hidden_fields = serializers.SerializerMethodField()
        can_view_hidden_fields = serializers.SerializerMethodField()

        def get_vendor_details(self, obj):
            vendor_details = VendorDetailSerializer(obj.proposal_from.vendor, context=self.context).data
            # if obj.order:
            #     vendor_details["gst"] = fetch_gst_number_from_order(order_id=obj.order_id)
            return vendor_details

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_shipping_address_list(self, obj):
            return get_shipping_address_dropdown_list(project=obj.project, proposal_client_id=obj.proposal_for_id)

        @swagger_serializer_method(serializer_or_field=serializers.DateField())
        def get_start_date(self, obj):
            datetime = obj.order.started_at if getattr(obj, "order") else obj.start_date
            if datetime is None:
                return None
            return timezone.localtime(datetime).date()

        @swagger_serializer_method(serializer_or_field=serializers.DateField())
        def get_due_date(self, obj):
            datetime = obj.order.due_at if obj.order else obj.due_date
            if datetime is None:
                return None
            return timezone.localtime(datetime).date()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_shipping_address(self, obj):
            return obj.order.shipping_address if getattr(obj, "order") else obj.shipping_address

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_order_number(self, obj):
            return (
                f"{obj.project.job_id}/{obj.order.order_number}"
                if obj.order
                else f"{obj.project.job_id}/{obj.ref_number}"
            )

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_ref_number(self, obj):
            return f"{obj.project.job_id}/{obj.ref_number}"

        @swagger_serializer_method(serializer_or_field=SectionSerializer(many=True))
        def get_sections(self, obj):
            sections = obj.sections.all()
            unsectioned = [
                {
                    "id": None,
                    "name": ProposalSection.DEFAULT_SECTION,
                    "element_count": ProposalElementMapping.objects.filter(proposal_id=obj.pk, section_id=None)
                    .available()
                    .count(),
                }
            ]
            return unsectioned + self.SectionSerializer(sections, many=True, context=self.context).data

        @swagger_serializer_method(
            serializer_or_field=VendorOrderModelSerializer.VendorPurchaseOrderSerializer(many=True)
        )
        def get_purchase_orders(self, obj):
            return (
                VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
                    obj.order.purchase_orders.all(), many=True, context=self.context
                ).data
                if obj.order
                else []
            )

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_status(self, obj):
            return obj.statuses.first().status.replace("_", " ").title()

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_actions(self, obj):
            return get_proposal_actions(user=self.context.get("user"), proposal=obj, project_id=obj.project_id)

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_document_config(self, obj):
            return OrganizationSectionData.drf_serializer(obj.document_config, many=True, context=self.context).data

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=PROPOSAL_FIELDS_MAPPING, proposal_id=obj.id, org_id=self.context.get("org_id")
            )

        @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        def get_can_view_hidden_fields(self, obj):
            if Permissions.CAN_CONFIGURE_CLIENT_VIEW in permission_get_all(org_id=self.context.get("org_id")):
                return True
            elif hasattr(obj, "meta_data"):
                return len(obj.meta_data.hidden_fields) > 0
            return False

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalDetailOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "status",
                "proposal_from",
                "client",
                "client_id",
                "shipping_address",
                "shipping_address_list",
                "start_date",
                "due_date",
                "tnc_text",
                "tnc_attachments",
                "supporting_documents",
                "vendor_details",
                "created_by",
                "elements",
                "comment_count",
                "purchase_orders",
                "sections",
                "order_id",
                "order_number",
                "org_to_id",
                "work_order_from",
                "request_type",
                "actions",
                "is_discount_col_visible",
                "is_service_charge_col_visible",
                "document_config",
                "hidden_fields",
                "can_view_hidden_fields",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="get_proposal",
        operation_summary="Get Proposal",
    )
    def get(self, request, project_id, proposal_id):
        self.set_project_timezone()
        proposal_details = get_proposal_details(proposal_id=proposal_id, org_id=self.get_organization_id())
        return Response(
            self.OutputSerializer(
                proposal_details,
                context={
                    "user": request.user,
                    "org_id": self.get_organization_id(),
                    "timezone": self.get_project_timezone(),
                    "is_vendor": proposal_details.proposal_from_id == self.get_organization_id(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class ProposalElementDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalElementMappingModelSerializer):
        preview_file_count = serializers.SerializerMethodField()
        total_amount = serializers.SerializerMethodField()
        budget_rate = serializers.SerializerMethodField()
        in_progress = serializers.BooleanField()
        is_progress_update_allowed = serializers.BooleanField()
        hidden_fields = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=15, decimal_places=2))
        def get_budget_rate(self, obj):
            return obj.budget_rate if self.context.get("is_vendor") else 0

        @swagger_serializer_method(serializer_or_field=serializers.DictField())
        def get_preview_file_count(self, obj):
            return obj.preview_files.filter(deleted_at__isnull=True).count()

        @swagger_serializer_method(serializer_or_field=serializers.DictField())
        def get_total_amount(self, obj):
            return obj.client_rate * obj.quantity

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                proposal_id=obj.proposal_id,
                client_field_mapping=PROPOSAL_FIELDS_MAPPING,
                org_id=self._context.get("org_id"),
            )

        class Meta(ProposalElementMappingModelSerializer.Meta):
            ref_name = "ProposalElementDetailsOutput"
            fields = (
                "id",
                "name",
                "code",
                "uom",
                "description",
                "preview_files",
                "preview_file_count",
                "category",
                "item_type",
                "total_amount",
                "quantity",
                "client_rate",
                "budget_rate",
                "guidelines",
                "production_drawings",
                "in_progress",
                "discount_percent",
                "is_progress_update_allowed",
                "quantity_dimensions",
                "brand_name",
                "tax_percent",
                "hsn_code",
                "amount_without_tax",
                "comment_count",
                "total_amount",
                "in_progress",
                "is_progress_update_allowed",
                "hidden_fields",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="get_proposal_element_details",
        operation_summary="Get Proposal Element Details",
    )
    def get(self, request, project_id, proposal_id, element_id):
        element_details = get_element_details(
            proposal_id=proposal_id, org_id=self.get_organization_id(), element_id=element_id
        )
        return Response(
            self.OutputSerializer(
                element_details,
                context={
                    "org_id": self.get_organization_id(),
                    "is_vendor": element_details.proposal_from_id == self.get_organization_id(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class ProposalDetailsWithRefNumberApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        proposal_id = HashIdField(allow_null=True, source="id")
        vendor_details = VendorDetailSerializer(source="proposal_from.vendor")
        client = serializers.CharField(source="proposal_for")
        client_id = HashIdField(source="proposal_for.id")
        status = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        shipping_address_list = OrganizationAddressSerializer(many=True, source="proposal_from.addresses")
        supporting_documents = ProposalSupportingDocumentSerializer(many=True)
        tnc_attachments = ProposalTNCAttachmentSerializer(many=True)
        elements = ProposalElementMappingModelSerializer(many=True, source="proposal_element_mappings")
        comment_count = serializers.SerializerMethodField()
        document_config = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_status(self, obj):
            return obj.statuses.first().status.replace("_", " ").title()

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_document_config(self, obj):
            return (
                OrganizationSectionData.drf_serializer(obj.document_config, many=True).data
                if hasattr(obj, "document_config")
                else []
            )

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalDetailOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "status",
                "proposal_from",
                "client",
                "client_id",
                "shipping_address",
                "shipping_address_list",
                "start_date",
                "due_date",
                "tnc_text",
                "tnc_attachments",
                "supporting_documents",
                "vendor_details",
                "created_by",
                "elements",
                "comment_count",
                "is_discount_col_visible",
                "is_service_charge_col_visible",
                "document_config",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="get_proposal_ref",
        operation_summary="Get Proposal",
    )
    def get(self, request, project_id, ref_number):
        proposal_details = get_proposal_details_from_ref_number(
            project_id=project_id, ref_number=ref_number, org_id=self.get_organization_id()
        )
        return Response(
            self.OutputSerializer(proposal_details, context=self.get_output_context()).data, status=HTTP_200_OK
        )


class ProposalPrefetchDetails(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="prefill_proposal",
        operation_summary="Prefill Proposal",
    )
    def get(self, request, project_id):
        client_id, client, created_by, vendor_details, shipping_address_list, status = prefetch_proposal_details(
            vendor_organization_id=self.get_organization_id(), user_id=request.user.pk, project_id=project_id
        )
        data = {
            "ref_number": "*",
            "client": client,
            "client_id": client_id,
            "vendor_details": VendorDetailSerializer(vendor_details).data,
            "created_by": UserModelSerializer(created_by).data,
            "shipping_address_list": OrganizationAddressSerializer(shipping_address_list, many=True).data
            + [
                {
                    "address": Store.objects.get(project_id=project_id).address,
                    "id": "project",
                    "header": "Project Location",
                    "organization": 0,
                }
            ],
            "status": status,
        }
        return Response(data, status=HTTP_200_OK)


class ProposalPrefillDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        proposal_id = HashIdField(allow_null=True, default=None, required=False)

        class Meta:
            input_hash_id_fields = []

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: ""},
        operation_id="prefill_proposal_details",
        operation_summary="Prefill Proposal Details",
    )
    def get(self, request, project_id):
        data = self.validate_filter_data()
        proposal_id = data.get("proposal_id", None)
        if proposal_id is None:
            data, shipping_address_list = get_default_prefill_details_for_proposal(
                organization_id=self.get_organization_id(),
                project_id=project_id,
                proposal_client_id=self.get_immediate_client_id(),
            )
        else:
            data, shipping_address_list = get_prefill_details_for_proposal_using_current_data(
                organization_id=self.get_organization_id(), proposal_id=proposal_id
            )
        return Response({**data.model_dump(), "shipping_address_list": shipping_address_list}, status=HTTP_200_OK)


class ProposalClientFieldsSettingApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    serializer_class = ProposalClientFields.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: serializer_class()},
        operation_id="proposal_client_fields",
        operation_summary="Proposal Client Fields",
    )
    def get(self, request, proposal_id, *args, **kwargs):
        proposal_client_fields: ProposalClientFields = get_proposal_client_field_settings(proposal_id=proposal_id)
        return Response(self.serializer_class(proposal_client_fields).data, status=HTTP_200_OK)


class ProposalForClientListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        proposal_id = HashIdField(source="id")
        client = serializers.CharField(source="proposal_for")
        status = serializers.SerializerMethodField()
        status_updated_by = serializers.SerializerMethodField()
        status_updated_at = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        updated_by = UserModelSerializer()
        vendor_organization = serializers.CharField(source="proposal_from")
        element_category = serializers.ListField(child=serializers.CharField(allow_null=True), allow_null=True)
        item_type = serializers.SerializerMethodField()
        element_count = serializers.IntegerField(default=0)
        comment_count = serializers.SerializerMethodField()
        has_history = serializers.SerializerMethodField()
        total_amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="proposal_amount")

        @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        def get_has_history(self, obj):
            return True if obj.version != 1 else False

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalListOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "client",
                "status",
                "status_updated_at",
                "status_updated_by",
                "created_at",
                "created_by",
                "vendor_organization",
                "updated_by",
                "updated_at",
                "total_amount",
                "element_category",
                "element_count",
                "item_type",
                "comment_count",
                "has_history",
                "request_type",
            )

        def get_item_type(self, obj):
            return obj.item_type if any(obj.item_type) else None

        def get_status(self, obj):
            return obj.statuses.all()[0].status.replace("_", " ").title() if obj.statuses.all() else None

        @swagger_serializer_method(serializer_or_field=UserModelSerializer())
        def get_status_updated_by(self, obj):
            return UserModelSerializer(obj.statuses.all()[0].created_by).data if obj.statuses.all() else None

        def get_status_updated_at(self, obj):
            return obj.statuses.all()[0].created_at if obj.statuses.all() else None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="vendor_proposal_list",
        operation_summary="Vendor Proposal List",
        tags=["Proposal"],
    )
    def get(self, request, project_id):
        vendor_id = self.get_organization_id()
        proposals = get_vendor_proposal_list_v2(
            project_id=project_id, proposal_from_id=vendor_id, org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(proposals, many=True).data, status=HTTP_200_OK)


class ProposalFromVendorListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        proposal_id = HashIdField(source="id")
        status = serializers.SerializerMethodField()
        status_updated_by = serializers.SerializerMethodField()
        status_updated_at = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        updated_by = UserModelSerializer()
        vendor_organization = serializers.CharField(source="proposal_from")
        vendor_code = serializers.CharField(source="proposal_from.vendor.code")
        element_category = serializers.ListField(child=serializers.CharField(allow_null=True), allow_null=True)
        item_type = serializers.SerializerMethodField()
        element_count = serializers.IntegerField(default=0)
        comment_count = serializers.SerializerMethodField()
        has_history = serializers.SerializerMethodField()
        total_amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="proposal_amount")

        @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        def get_has_history(self, obj):
            return True if obj.version != 1 else False

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalListOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "status",
                "status_updated_at",
                "status_updated_by",
                "created_at",
                "created_by",
                "vendor_organization",
                "updated_by",
                "updated_at",
                "vendor_code",
                "total_amount",
                "element_category",
                "element_count",
                "item_type",
                "comment_count",
                "has_history",
                "request_type",
            )

        def get_item_type(self, obj):
            return obj.item_type if any(obj.item_type) else None

        def get_status(self, obj):
            return obj.statuses.all()[0].status.replace("_", " ").title() if obj.statuses.all() else None

        @swagger_serializer_method(serializer_or_field=UserModelSerializer())
        def get_status_updated_by(self, obj):
            return UserModelSerializer(obj.statuses.all()[0].created_by).data if obj.statuses.all() else None

        def get_status_updated_at(self, obj):
            return obj.statuses.all()[0].created_at if obj.statuses.all() else None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="client_proposal_list",
        operation_summary="Client Proposal List",
        tags=["Proposal"],
    )
    def get(self, request, project_id):
        client_id = self.get_organization_id()
        proposals = get_client_proposal_list_v2(
            project_id=project_id, proposal_for_id=client_id, org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(proposals, many=True).data, status=HTTP_200_OK)


class ProposalStatusUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        action = serializers.ChoiceField(
            choices=[
                value for name, value in Action.__dict__.items() if not callable(value) and not name.startswith("__")
            ]
        )

        class Meta:
            ref_name = "VendorProposalStatusUpdateInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="update_proposal_status",
        operation_summary="Update Proposal Status",
    )
    @transaction.atomic
    def put(self, request, proposal_id: int, project_id: int):
        data = self.validate_input_data()
        status = insert_proposal_status(
            proposal_id=proposal_id, created_by_id=request.user.pk, action=data.get("action")
        )
        updated_time = update_status_update_time(proposal_id=proposal_id, user_id=request.user.pk)
        return Response(
            f"Proposal Status for {proposal_id} have been updated to {status} on updated_time {updated_time}",
            status=HTTP_200_OK,
        )


class ProposalRecipientEmailListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        org_id = HashIdField()
        proposal_number = serializers.CharField(allow_null=True, allow_blank=True, required=False)
        order_number = serializers.CharField(allow_null=True, allow_blank=True, required=False)

        class Meta:
            ref_name = "ProposalRecipientFilterListInput"

    class Meta:
        ref_name = "ProposalRecipientEmailListWithDefaultOutput"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: []},
        operation_id="proposal_recipient_email_list",
        operation_summary="Proposal Recipient Email List",
    )
    def get(self, request, project_id):
        data: Dict = self.validate_filter_data()
        emails_dict_list = proposal_recipient_email_list(
            org_from_id=data.get("org_id"),
            org_to_id=request.user.org.id,
            project_id=project_id,
        )

        return Response(emails_dict_list, status=HTTP_200_OK)


class ProposalPermissionActionBaseApi(ProjectActionBaseApi):
    context = MicroContextChoices.PROPOSAL.value


class ProposalApproveApiV2(ProposalPermissionActionBaseApi):
    context_key = "proposal_id"
    context_action = MicroContextActions.APPROVE.value

    class OutputSerializer(BaseSerializer):
        order_id = HashIdField()

        class Meta:
            ref_name = "ProposalApproveOutputV2"

    input_serializer_class = ProposalApproveDataSerializer

    @swagger_auto_schema(
        request_body=ProposalApproveDataSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="approve_proposal_v2",
        operation_summary="Approve Proposal V2",
        tags=["Proposal"],
    )
    @transaction.atomic
    def post(self, request, project_id, proposal_id, *args, **kwargs):
        proposal_approve_data: ProposalApproveData = self.validate_input_data()
        # TODO: check which elements has 0 quantity and set object status to cancelled
        try:
            order_id = proposal_approve_v2(
                proposal_id=proposal_id,
                project_id=project_id,
                user=request.user,
                org_id=self.get_organization_id(),
                proposal_approve_data=proposal_approve_data,
            )
        except ProposalApprovedNotAllowed as e:
            self.set_response_message(str(e))
            return Response(status=HTTP_406_NOT_ACCEPTABLE)
        except ProposalApprovalDataValidationException as e:
            transaction.set_rollback(True)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(self.OutputSerializer({"order_id": order_id}).data, status=HTTP_200_OK)


class ProposalHistoryClientListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        proposal_id = HashIdField(source="id")
        client = serializers.CharField(source="proposal_for")
        status = serializers.SerializerMethodField()
        status_updated_by = serializers.SerializerMethodField()
        status_updated_at = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        updated_by = UserModelSerializer()
        vendor_organization = serializers.CharField(source="proposal_from")
        total_amount = serializers.DecimalField(source="proposal_amount", max_digits=15, decimal_places=4)
        element_category = serializers.ListField(child=serializers.CharField(allow_null=True), allow_null=True)
        item_type = serializers.SerializerMethodField()
        element_count = serializers.IntegerField(default=0)
        comment_count = serializers.SerializerMethodField()
        request_type = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_request_type(self, obj):
            if getattr(obj, "order"):
                if obj.order.issued_at > obj.created_at and obj.version == 1:
                    return ProposalRequestType.PROPOSAL_FOR_NEW_ORDER
                return ProposalRequestType.PROPOSAL_FOR_ORDER_CHANGE
            return ProposalRequestType.PROPOSAL_FOR_ORDER_CHANGE

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalListOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "client",
                "status",
                "status_updated_at",
                "status_updated_by",
                "created_at",
                "created_by",
                "vendor_organization",
                "updated_by",
                "updated_at",
                "total_amount",
                "element_category",
                "element_count",
                "item_type",
                "comment_count",
                "request_type",
            )

        def get_item_type(self, obj):
            return obj.item_type if any(obj.item_type) else None

        def get_status(self, obj):
            return obj.statuses.all()[0].status.replace("_", " ").title() if obj.statuses.all() else None

        @swagger_serializer_method(serializer_or_field=UserModelSerializer())
        def get_status_updated_by(self, obj):
            return UserModelSerializer(obj.statuses.all()[0].created_by).data if obj.statuses.all() else None

        def get_status_updated_at(self, obj):
            return obj.statuses.all()[0].created_at if obj.statuses.all() else None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="proposal_history_client_list",
        operation_summary="Proposal History Client List",
        tags=["Proposal"],
    )
    def get(self, request, proposal_id, project_id, *args, **kwargs):
        proposals = proposal_history_vendor_list(
            project_id=project_id, proposal_id=proposal_id, org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(proposals, many=True).data, status=HTTP_200_OK)


class ProposalHistoryVendorListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProposalModelSerializer):
        proposal_id = HashIdField(source="id")
        status = serializers.SerializerMethodField()
        status_updated_by = serializers.SerializerMethodField()
        status_updated_at = serializers.SerializerMethodField()
        created_by = UserModelSerializer()
        updated_by = UserModelSerializer()
        vendor_organization = serializers.CharField(source="proposal_from")
        vendor_code = serializers.CharField(source="proposal_from.vendor.code")
        total_amount = serializers.DecimalField(source="proposal_amount", max_digits=15, decimal_places=4)
        element_category = serializers.ListField(child=serializers.CharField(allow_null=True), allow_null=True)
        item_type = serializers.SerializerMethodField()
        element_count = serializers.IntegerField(default=0)
        comment_count = serializers.SerializerMethodField()
        request_type = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_request_type(self, obj):
            if getattr(obj, "order"):
                if obj.order.issued_at > obj.created_at and obj.version == 1:
                    return ProposalRequestType.PROPOSAL_FOR_NEW_ORDER
                return ProposalRequestType.PROPOSAL_FOR_ORDER_CHANGE
            return ProposalRequestType.PROPOSAL_FOR_ORDER_CHANGE

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        class Meta(ProposalModelSerializer.Meta):
            ref_name = "ProposalListOutput"
            fields = (
                "proposal_id",
                "ref_number",
                "status",
                "status_updated_at",
                "status_updated_by",
                "created_at",
                "created_by",
                "vendor_organization",
                "updated_by",
                "updated_at",
                "vendor_code",
                "total_amount",
                "element_category",
                "element_count",
                "item_type",
                "comment_count",
                "request_type",
            )

        def get_item_type(self, obj):
            return obj.item_type if any(obj.item_type) else None

        def get_status(self, obj):
            return obj.statuses.all()[0].status.replace("_", " ").title() if obj.statuses.all() else None

        @swagger_serializer_method(serializer_or_field=UserModelSerializer())
        def get_status_updated_by(self, obj):
            return UserModelSerializer(obj.statuses.all()[0].created_by).data if obj.statuses.all() else None

        def get_status_updated_at(self, obj):
            return obj.statuses.all()[0].created_at if obj.statuses.all() else None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="proposal_history_vendor_list",
        operation_summary="Proposal History Vendor List",
        tags=["Proposal"],
    )
    def get(self, request, proposal_id, project_id, *args, **kwargs):
        proposals = proposal_history_client_list(
            project_id=project_id, proposal_id=proposal_id, org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(proposals, many=True).data, status=HTTP_200_OK)


class ProposalRequestForChangeApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = ProposalRequestChangeDatasSerializer

    @swagger_auto_schema(
        request_body=ProposalRequestChangeDatasSerializer(),
        responses={HTTP_200_OK: ""},
        operation_id="request_for_change_proposal",
        operation_summary="Request for Change Proposal",
        tags=["Proposal"],
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        request_change_data = self.validate_input_data()
        try:
            ProposalService.process(
                project_id=project_id,
                proposal_request_change_datas=request_change_data,
                org_id=self.get_organization_id(),
                user=request.user,
            )
        except UOMValidationError as e:
            self.set_response_message(str(e))
            return Response(status=HTTP_400_BAD_REQUEST)
        except QuantityValidationError as e:
            self.set_response_message(e.message)
            raise e
        return Response(status=HTTP_200_OK)


class ProposalDropDownApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        order_number = serializers.SerializerMethodField()
        proposal_number = serializers.SerializerMethodField()
        proposal_id = serializers.SerializerMethodField()
        elements_count = serializers.IntegerField()
        status = serializers.SerializerMethodField()
        amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="total_amount")

        def get_proposal_number(self, obj):
            return obj.proposal_number if hasattr(obj, "proposal_number") else None

        def get_proposal_id(self, obj):
            # obj is VendorOrder
            if hasattr(obj, "proposal_id"):
                return obj.proposal_id
            # obj if Proposal
            if hasattr(obj, "pk"):
                return obj.pk
            return None

        def get_order_number(self, obj):
            return obj.new_order_number if hasattr(obj, "new_order_number") else None

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_status(self, obj):
            if obj.proposal_number and getattr(obj, "new_order_number", None):
                return "Proposal Sent > Order Received"
            if getattr(obj, "new_order_number", None):
                return "Order Received"
            return "Proposal Sent"

        class Meta:
            output_hash_id_fields = ["proposal_id"]
            ref_name = "ProposalDropDownOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="proposal_drop_down",
        operation_summary="Proposal Drop Down",
        tags=["Proposal"],
    )
    def get(self, request, project_id, *args, **kwargs):
        proposal_and_orders = proposal_and_order_dropdown_list(project_id=project_id, org_id=self.get_organization_id())
        return Response(self.OutputSerializer(proposal_and_orders, many=True).data, status=HTTP_200_OK)


class ProposalRejectApi(ProposalPermissionActionBaseApi):
    context_key = "proposal_id"
    context_action = MicroContextActions.REJECT.value

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="reject_proposal",
        operation_summary="Reject Proposal",
        tags=["Proposal"],
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        proposal_reject(proposal_id=kwargs.get("proposal_id"), user=request.user)
        return Response({"status": "Rejected"}, status=HTTP_200_OK)


class ProposalApproveEmailDataFetch(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    filter_serializer_class = OrderSentInitializerDataSerializer

    class OutputSerializer(BaseSerializer):
        recipient_emails = serializers.ListField()
        cc_emails = serializers.ListField()
        bcc_emails = serializers.ListField()
        subject = serializers.CharField()

        class Meta:
            ref_name = "ProposalApproveEmailInfoOutput"

    @swagger_auto_schema(
        query_serializer=OrderSentInitializerDataSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="proposal-approve-email-data-fetch",
    )
    @transaction.atomic
    def get(self, request, project_id, proposal_id, *args, **kwargs):
        data: OrderSentInitializeData = self.validate_filter_data()
        email_data = OrderSentProcessorService.prepare_email_data(
            sent_initializer_data=data,
            user_email=request.user.email,
            org_id=data.org_to_id,
            project_id=project_id,
        )
        subquery = ProposalEmail.objects.filter(proposal_id=OuterRef("id")).order_by("-id")
        proposal = (
            Proposal.objects.filter(id=proposal_id)
            .select_related("created_by")
            .annotate(to_receiver=Subquery(subquery.values("to_receiver")[:1]))
            .first()
        )
        mapping = mapping_get(client_id=proposal.proposal_for_id, vendor_id=proposal.proposal_from_id)
        if (
            proposal.created_by.deleted_at is None
            and VendorUserService().check_if_user_exist_with_credential_excluding_current(
                mapping_id=mapping.id, user=proposal.created_by
            )
        ):
            created_by_email = [proposal.created_by.email] if proposal.created_by.email else []
        else:
            created_by_email = []
        if not proposal.to_receiver:
            receiver_email = []
        else:
            receiver_email = proposal.to_receiver

        email_data["recipient_emails"] = list(created_by_email)
        email_data["cc_emails"] = list(set(created_by_email + email_data["cc_emails"] + receiver_email))
        serializer = self.OutputSerializer(data=email_data)
        serializer.is_valid(raise_exception=True)
        return Response(data=serializer.data, status=HTTP_200_OK)
