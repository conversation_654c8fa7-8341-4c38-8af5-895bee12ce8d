from django.core.exceptions import ValidationError


class ProposalAlreadySentException(ValidationError):
    pass


class ProposalAlreadyApprovedException(ValidationError):
    pass


class ProposalAlreadyRejectedException(ValidationError):
    pass


class ProposalElementApprovedNotAllowed(ValidationError):
    pass


class ProposalApprovedNotAllowed(ValidationError):
    pass


class ProposalApprovalDataValidationException(ValidationError):
    pass


class ProposalToOrderPurchaseOrderTypeException(ValidationError):
    pass
