from typing import Optional, Union

from django.utils import timezone

from approval_request.approval_hierarchy.data.models import ApprovalHierarchy, HierarchyHistory
from approval_request.approval_hierarchy.data.selectors import hierarchy_fetch
from approval_request.approval_hierarchy.domain.abstract_repos import BaseHierarchyRepo
from approval_request.approval_hierarchy.domain.entities import HierarchyFetchData, HierarchyRepoOutputData
from approval_request.approval_hierarchy.domain.enums import HierarchyApproverTypesEnum
from approval_request.domain.constants import RequestErrorCodeEnum
from core.models import Role, User
from core.role.repository import RoleRepository
from microcontext.choices import MicroContextChoices


class HierarchyRepo(BaseHierarchyRepo):
    VERSION = 2

    def __init__(self, organization_id: int, context: MicroContextChoices) -> None:
        self.organization_id = organization_id
        self.context = context

    def mark_active_inactive_data(self, value: bool, user_id: int):
        return ApprovalHierarchy.objects.filter(organization_id=self.organization_id, context=self.context).update(
            is_active=value, updated_by_id=user_id, updated_at=timezone.now()
        )

    def mark_skip_unskip_data(self, value: bool, user_id: int):
        return ApprovalHierarchy.objects.filter(organization_id=self.organization_id, context=self.context).update(
            is_skipped=value, updated_by_id=user_id, updated_at=timezone.now()
        )

    def get_hierarchy(self) -> HierarchyRepoOutputData:
        hierarchy: Optional[ApprovalHierarchy] = hierarchy_fetch(
            organization_id=self.organization_id, context=self.context
        )
        if not hierarchy:
            raise self.HierarchyNotFoundException("Hierarchy not found")
        if hierarchy.version == 1:
            for condition in hierarchy.config.get("conditions", []):
                for approver in condition.get("approvers", []):
                    approver["ids"] = [approver["id"]]
        return HierarchyRepoOutputData(
            conditions=hierarchy.config,
            is_active=hierarchy.is_active,
            is_skipped=hierarchy.is_skipped,
            is_auto_approved_on_final_level_misconfiguration=hierarchy.is_auto_approved_on_final_level_misconfiguration,
        )

    def get_approver_error_code(self, approver_type: str, approver_instance: Union[User, Role]) -> Union[int, None]:
        if approver_type == HierarchyApproverTypesEnum.USER.value:
            if approver_instance.deleted_at is not None:
                return RequestErrorCodeEnum.USER_DELETED.value
            elif not approver_instance.is_active:
                return RequestErrorCodeEnum.USER_INACTIVE.value
            else:
                return None

        elif approver_type == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
            if approver_instance.deleted_at is not None:
                return RequestErrorCodeEnum.ROLE_DELETED.value
            elif not approver_instance.is_active:
                return RequestErrorCodeEnum.ROLE_INACTIVE.value
            else:
                return None

    def get_enriched_data(self) -> HierarchyFetchData:
        hierarchy_data = ApprovalHierarchy.objects.filter(
            organization_id=self.organization_id, context=self.context
        ).first()
        if hierarchy_data is None:
            return HierarchyFetchData(
                is_active=False,
                root_id=None,
                conditions=[],
                is_skipped=False,
            )

        user_ids = []
        project_role_ids = []
        all_approvers = []

        if hierarchy_data.version == 1:
            for condition in hierarchy_data.config.get("conditions", []):
                approvers = condition.get("approvers")
                for approver in approvers:
                    if approver["type"] == HierarchyApproverTypesEnum.USER.value:
                        user_ids.append(approver["id"])
                    elif approver["type"] == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
                        project_role_ids.append(approver["id"])
                    all_approvers.append(approver)
        else:
            for condition in hierarchy_data.config.get("conditions", []):
                approvers = condition.get("approvers")
                for approver in approvers:
                    if approver["type"] == HierarchyApproverTypesEnum.USER.value:
                        user_ids.extend(approver["ids"])
                    elif approver["type"] == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
                        project_role_ids.extend(approver["ids"])
                    all_approvers.append(approver)

        roles = (
            RoleRepository()
            .get_roles_by_ids(role_ids=project_role_ids)
            .only("id", "name", "deleted_at", "is_active")
            .in_bulk()
        )
        users = (
            User.objects.filter(id__in=user_ids)
            .only("id", "first_name", "last_name", "photo", "deleted_at", "is_active")
            .in_bulk()
        )
        for approver in all_approvers:
            if approver["type"] == HierarchyApproverTypesEnum.USER.value:
                if hierarchy_data.version == 1:
                    user = users[approver["id"]]
                    error_code = self.get_approver_error_code(approver_type=approver["type"], approver_instance=user)
                    approver["value"] = [
                        {
                            "id": user.id,
                            "name": user.name,
                            "photo": user.photo.url if user.photo and user.photo.name else None,
                            "error_code": error_code,
                        }
                    ]
                    approver["error_code"] = error_code
                else:
                    value = []
                    level_error_code = None
                    has_active_user = False
                    for user_id in approver["ids"]:
                        user = users[user_id]
                        user_data = {}
                        user_data["name"] = user.name
                        user_data["photo"] = user.photo.url if user.photo and user.photo.name else None
                        user_data["id"] = user.id
                        user_data["error_code"] = self.get_approver_error_code(
                            approver_type=approver["type"], approver_instance=user
                        )
                        if user_data["error_code"] is None:
                            has_active_user = True
                            level_error_code = None
                        if has_active_user is False and level_error_code != RequestErrorCodeEnum.USER_INACTIVE.value:
                            level_error_code = user_data["error_code"]
                        value.append(user_data)
                    approver["value"] = value
                    approver["error_code"] = level_error_code

            elif approver["type"] == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
                if hierarchy_data.version == 1:
                    role = roles[approver["id"]]
                    error_code = self.get_approver_error_code(approver_type=approver["type"], approver_instance=role)
                    approver["value"] = [
                        {
                            "id": role.id,
                            "name": role.name,
                            "error_code": error_code,
                        }
                    ]
                    approver["error_code"] = error_code
                else:
                    value = []
                    level_error_code = None
                    has_active_role = False
                    for role_id in approver["ids"]:
                        role = roles[role_id]
                        role_data = {}
                        role_data["name"] = role.name
                        role_data["id"] = role.id
                        role_data["error_code"] = self.get_approver_error_code(
                            approver_type=approver["type"], approver_instance=role
                        )
                        if role_data["error_code"] is None:
                            has_active_role = True
                            level_error_code = None
                        if has_active_role is False and level_error_code != RequestErrorCodeEnum.ROLE_INACTIVE.value:
                            level_error_code = role_data["error_code"]
                        value.append(role_data)
                    approver["value"] = value
                    approver["error_code"] = level_error_code

        return HierarchyFetchData(
            is_active=hierarchy_data.is_active,
            root_id=hierarchy_data.config.get("root_id") if hierarchy_data.config else None,
            conditions=hierarchy_data.config.get("conditions") if hierarchy_data.config else [],
            is_skipped=hierarchy_data.is_skipped,
        )

    def save_data(self, config: dict, user_id: int) -> int:
        """
        Create or Update Org Hierarchy
        :param conditions: dict
        :param organization_id: int
        :param context: str
        :return: OrganizationApprovalHierarchy
        """
        if "resource" in config:
            config.pop("resource")
        hierarchy = ApprovalHierarchy.objects.filter(organization_id=self.organization_id, context=self.context).first()
        if hierarchy:
            ApprovalHierarchy.objects.filter(organization_id=self.organization_id, context=self.context).update(
                config=config, updated_by_id=user_id, updated_at=timezone.now(), version=self.VERSION
            )
        else:
            hierarchy = ApprovalHierarchy.objects.create(
                organization_id=self.organization_id,
                context=self.context,
                config=config,
                created_by_id=user_id,
                version=self.VERSION,
            )
        HierarchyHistory.objects.create(
            hierarchy_id=hierarchy.pk, config_snapshot=hierarchy.config, created_by_id=user_id
        )
        return hierarchy.pk
