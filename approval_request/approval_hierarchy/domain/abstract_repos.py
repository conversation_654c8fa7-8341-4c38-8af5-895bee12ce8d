import abc

from approval_request.approval_hierarchy.exceptions import ApprovalHierarchyException
from common.injector import Injectable


class BaseHierarchyRepo(Injectable):
    class HierarchyNotFoundException(ApprovalHierarchyException):
        pass

    class UpdateType:
        MARK_ACTIVE = "mark_active"
        MARK_SKIP = "mark_skip"

    @abc.abstractmethod
    def save_data(self, config: dict, user_id: int) -> int: ...

    @abc.abstractmethod
    def get_hierarchy(self): ...

    @abc.abstractmethod
    def mark_skip_unskip_data(self): ...

    @abc.abstractmethod
    def mark_active_inactive_data(self): ...
