from dataclasses import dataclass
from typing import Dict, List, Union

import structlog
from django.conf import settings
from django.utils.module_loading import import_string

from approval_request.approval_hierarchy.domain.entities import (
    Approver,
    HierarchyManagerInputHierarchyData,
    RoleApprovalConfig,
    UserApprovalConfig,
)
from approval_request.approval_hierarchy.domain.enums import HierarchyApproverTypesEnum
from approval_request.approval_hierarchy.domain.services.conditions import Condition<PERSON>xecuter
from approval_request.approval_hierarchy.domain.services.hierarchy import HierarchyService
from approval_request.approval_hierarchy.exceptions import ApprovalHierarchyException
from common.context_values import BaseContextValues
from common.entities import ContextProviders, ProjectRoleAssignee
from common.intefaces import Context<PERSON>rov<PERSON>HelperAbstractInterface, ProjectRoleAssigneeHelperAbstractInterface
from core.helpers import UserHelper

logger = structlog.getLogger(__name__)


ContextProviderHelper: ContextProviderHelperAbstractInterface = import_string(settings.CONTEXT_PROVIDER_HELPER)


class HierarchyManager:
    _process_hierarchy_flag = False

    @dataclass(frozen=True)
    class SkippedData:
        data: bool

    @dataclass(frozen=True)
    class ConfigData:
        data: Dict

    @dataclass(frozen=True)
    class ApproverData:
        data: List[Approver]

    def set_is_skipped(self, data: bool):
        self.skipped_obj = self.SkippedData(data=data)

    def set_is_auto_approved_on_final_level_misconfiguration(self, data: bool):
        self.is_auto_approved_on_final_level_misconfiguration_obj = self.ApproverData(data=data)

    def set_approvers(self, data: List[Approver]):
        self.approver_obj = self.ApproverData(data=data)

    def set_config(self, data: dict):
        self.config_obj = self.ConfigData(data=data)

    def get_config(self) -> dict:
        assert self._process_hierarchy_flag, "Please call process_hierarchy first"
        assert self.config_obj, "Please call set_config first"
        return self.config_obj.data

    def get_is_skipped(self) -> bool:
        assert self._process_hierarchy_flag, "Please call process_hierarchy first"
        assert self.skipped_obj, "Please call set_is_skipped first"
        return self.skipped_obj.data

    def get_approvers(self) -> List[Approver]:
        assert self._process_hierarchy_flag, "Please call process_hierarchy first"
        assert self.approver_obj, "Please call set_approvers first"
        return self.approver_obj.data

    def get_is_auto_approved_on_final_level_misconfiguration(self) -> bool:
        assert self._process_hierarchy_flag, "Please call process_hierarchy first"
        assert hasattr(
            self, "is_auto_approved_on_final_level_misconfiguration_obj"
        ), "Please call set_is_auto_approved_on_final_level_misconfiguration first"
        return self.is_auto_approved_on_final_level_misconfiguration_obj.data

    class HierarchyManagerException(ApprovalHierarchyException):
        pass

    class HierarchyNotFoundException(HierarchyManagerException):
        pass

    class InactiveHierarchyException(HierarchyManagerException):
        pass

    class AutoApproveHierarchyException(HierarchyManagerException):
        pass

    class MisconfiguredHierarchyViaConfigException(HierarchyManagerException):
        pass

    class MisConfiguredHierarchyViaRoleDeletionException(HierarchyManagerException):
        pass

    class MisConfiguredHierarchyViaUserDeletionException(HierarchyManagerException):
        pass

    class MisConfiguredHierarchyViaUserInactiveException(HierarchyManagerException):
        pass

    class ProjectAssigneeNotFound(HierarchyManagerException):
        pass

    class MisConfiguredHierarchyViaRoleInactiveException(HierarchyManagerException):
        pass

    def __init__(
        self,
        hierarchy_service: HierarchyService,
        condition_executor: ConditionExecuter,
        project_role_assignee_helper: ProjectRoleAssigneeHelperAbstractInterface,
        user_helper: UserHelper,
    ):
        self.hierarchy_service = hierarchy_service
        self.condition_executor = condition_executor
        self.project_role_assignee_helper = project_role_assignee_helper
        self.user_helper = user_helper

    def _run_executer(
        self,
        context_input_data: BaseContextValues,
        hierarchy_data: HierarchyManagerInputHierarchyData,
        providers: ContextProviders,
    ):
        """
        Executes the condition executor with the given input data and hierarchy data.

        Args:
            context_input_data (BaseContextValues): The input data for the condition executor.
            hierarch_data (HierarchyManagerInputHierarchyData): The hierarchy data for the condition executor.
            providers (ContextProviders): The context providers for the condition executor.

        Returns:
            The result of the condition executor.

        Raises:
            MisconfiguredHierarchyViaConfigException: If the hierarchy condition is misconfigured.
            AutoApproveHierarchyException: If the hierarchy is auto-approved.
        """
        try:
            return self.condition_executor.execute(
                context_input_data=context_input_data, hierarchy_data=hierarchy_data, providers=providers
            )
        except self.condition_executor.MisconfiguredHierarchyViaConfigException:
            raise self.MisconfiguredHierarchyViaConfigException("Invalid hierarchy condition")
        except self.condition_executor.AutoApprovedHierarchyException:
            raise self.AutoApproveHierarchyException("Auto approve hierarchy")

    def validate_last_level_assignees_for_misconfiguration(
        self,
        is_auto_approved_on_final_level_misconfiguration: bool,
        project_id: int,
        last_level_approvers_config: RoleApprovalConfig | UserApprovalConfig,
    ):
        if not is_auto_approved_on_final_level_misconfiguration:
            if last_level_approvers_config.type == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
                self.project_role_assignee_helper.check_last_level_assignees_for_misconfiguration(
                    role_ids=last_level_approvers_config.role_ids,
                    project_id=project_id,
                )
            elif last_level_approvers_config.type == HierarchyApproverTypesEnum.USER.value:
                self.user_helper.check_last_level_assignees_for_misconfiguration(config=last_level_approvers_config)

    def _process_approvers(
        self,
        approvers_config: List[Union[RoleApprovalConfig, UserApprovalConfig]],
        project_id: int,
        is_auto_approved_on_final_level_misconfiguration: bool = False,
    ):
        """
        Process the list of approvers based on the provided configuration.

        Args:
            approvers_config (List[Union[RoleApprovalConfig, UserApprovalConfig]]): List of approver configurations.

        Raises:
            AutoApproveHierarchyException: If no assignee is found for the project.

        Returns:
            None
        """

        role_id_level_mapping = {}
        for approver_config in approvers_config:
            if hasattr(approver_config, "role_ids"):
                for role_id in approver_config.role_ids:
                    role_id_level_mapping[role_id] = approver_config.level

        # TODO: Check for request misconfiguration
        try:
            self.validate_last_level_assignees_for_misconfiguration(
                is_auto_approved_on_final_level_misconfiguration=is_auto_approved_on_final_level_misconfiguration,
                last_level_approvers_config=approvers_config[-1],
                project_id=project_id,
            )
        except ProjectRoleAssigneeHelperAbstractInterface.ProjectAssigneeNotFound as e:
            # If the role is not found then we will misconfigure the request
            logger.debug("Project Assignee Not Found")
            raise self.ProjectAssigneeNotFound(e.message)
        except ProjectRoleAssigneeHelperAbstractInterface.ProjectRoleDeleted as e:
            # If the role is deleted then we will misconfigure the request
            logger.debug("Project Role Deleted")
            raise self.MisConfiguredHierarchyViaRoleDeletionException(e.message)
        except ProjectRoleAssigneeHelperAbstractInterface.ProjectUserDeleted as e:
            # If the user is deleted then we will misconfigure the request
            logger.debug("Project User Deleted")
            raise self.MisConfiguredHierarchyViaUserDeletionException(e.message)
        except ProjectRoleAssigneeHelperAbstractInterface.ProjectUserInactive as e:
            # If the user is inactive then we will misconfigure the request
            logger.debug("Project User Inactive")
            raise self.MisConfiguredHierarchyViaUserInactiveException(e.message)
        except ProjectRoleAssigneeHelperAbstractInterface.ProjectRoleInactive as e:
            # If the role is inactive then we will misconfigure the request
            logger.debug("Project Role Inactive")
            raise self.MisConfiguredHierarchyViaRoleInactiveException(e.message)
        except UserHelper.UserInactive as e:
            # If the user is inactive then we will misconfigure the request
            logger.debug("User Inactive")
            raise self.MisConfiguredHierarchyViaUserInactiveException(e.message)
        except UserHelper.UserDeleted as e:
            # If the user is deleted then we will misconfigure the request
            logger.debug("User Deleted")
            raise self.MisConfiguredHierarchyViaUserDeletionException(e.message)

        approvers = []
        if role_id_level_mapping:
            project_role_users: List[ProjectRoleAssignee] = self.project_role_assignee_helper.get_role_assignees(
                role_id_level_mapping=role_id_level_mapping,
                project_id=project_id,
            )
            for project_role_user in project_role_users:
                approvers.append(
                    Approver(
                        user_id=project_role_user.user_id,
                        level=project_role_user.level,
                        role_id=project_role_user.role_id,
                        is_active=project_role_user.user_is_active,
                        is_deleted=project_role_user.user_is_deleted,
                        is_role_active=project_role_user.is_role_active,
                        is_role_deleted=project_role_user.is_role_deleted,
                    )
                )

        active_user_ids, deleted_user_ids = self.user_helper.validate_users(
            configs=[
                approver_config
                for approver_config in approvers_config
                if approver_config.type == HierarchyApproverTypesEnum.USER.value
            ]
        )

        for approver_config in approvers_config:
            if approver_config.type == HierarchyApproverTypesEnum.USER.value:
                user_ids = approver_config.user_ids
                approvers.extend(
                    [
                        Approver(
                            user_id=user_id,
                            role_id=None,
                            level=approver_config.level,
                            is_active=True if user_id in active_user_ids else False,
                            is_deleted=True if user_id in deleted_user_ids else False,
                        )
                        for user_id in user_ids
                    ]
                )

        self.set_approvers(data=approvers)

    def process_hierarchy(self, context_input_data: BaseContextValues):
        """
        Process the approval hierarchy based on the given context input data.

        Args:
            context_input_data (BaseContextValues): The input data for the approval hierarchy.

        Raises:
            HierarchyNotFoundException: If the hierarchy is not found.
            MisConfiguredHierarchyException: If the hierarchy condition is invalid.
            InactiveHierarchyException: If the hierarchy is inactive.

        Returns:
            None
        """
        try:
            self._process_hierarchy_flag = True
            hierarchy_data = self.hierarchy_service.get_hierarchy()
            # Check if the hierarchy is active
            self.set_config(self.hierarchy_service.get_config())
            if hierarchy_data.is_skipped:
                self.set_is_skipped(data=True)
            else:
                self.set_is_skipped(data=False)
            if hierarchy_data.is_auto_approved_on_final_level_misconfiguration:
                self.set_is_auto_approved_on_final_level_misconfiguration(data=True)
            else:
                self.set_is_auto_approved_on_final_level_misconfiguration(data=False)
        except self.hierarchy_service.HierarchyNotFoundException:
            self.set_config(data={})
            raise self.HierarchyNotFoundException("Hierarchy not found")
        except self.hierarchy_service.InvalidHierarchyConditionException:
            self.set_config(self.hierarchy_service.get_config())
            raise self.MisconfiguredHierarchyViaConfigException("Invalid hierarchy condition")

        if not hierarchy_data.is_active:
            raise self.InactiveHierarchyException("Hierarchy is inactive")

        # Get the providers based on the hierarchy
        providers = ContextProviderHelper.get_providers(hierarchy=hierarchy_data.hierarchy)

        # Run the executer to get the approvers configuration
        approvers_config = self._run_executer(
            context_input_data=context_input_data, hierarchy_data=hierarchy_data, providers=providers
        )

        # Process the approvers configuration
        self._process_approvers(
            approvers_config=approvers_config,
            project_id=context_input_data.project_id,
            is_auto_approved_on_final_level_misconfiguration=hierarchy_data.is_auto_approved_on_final_level_misconfiguration,
        )
