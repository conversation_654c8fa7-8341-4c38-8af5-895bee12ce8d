from enum import Enum

from common.constants import BaseEnum


class ConditionsEnum(Enum):
    PAYMENT_TERM = "payment_term"
    REGION = "region"
    EXPENSE_AMOUNT = "expense_amount"
    INVOICE_TYPE = "invoice_type"
    INVOICE_AMOUNT = "invoice_amount"
    PAYMENT_REQUEST_AMOUNT = "payment_request_amount"
    ORDER_AMOUNT = "order_amount"
    PURCHASE_ORDER_TYPE = (
        "order_type"  # Not changing the value to purchase_order_type because it is stored in the db (config_snapshot)
    )
    BUSINESS_CATEGORY = "business_category"
    APPROVER = "approver"  # Node type only


class OrderTypeConditionEnum(BaseEnum):
    NO_ORDER = 0, "No Order"
    INSTA_ORDER = 1, "Insta Order"
    REGULAR = 2, "Regular"


class PaymentTermConditionEnum(BaseEnum):
    CUSTOM_PAYMENT_TERM = 0, "Custom Payment Terms"
    NO_PAYMENT_TERM = -1, "No Payment Terms"
    INACTIVE_PAYMENT_TERM = -2, "Inactive Payment Terms"


class HierarchyApproverTypesEnum(Enum):
    USER = "user"
    PROJECT_ROLE = "project_role"


class BusinessCategoryTypeConditionEnum(BaseEnum):
    SHARED_PROJECT = 0, "Shared Projects"
    NO_BUSINESS_CATEGORY = -1, "No Business Category"
