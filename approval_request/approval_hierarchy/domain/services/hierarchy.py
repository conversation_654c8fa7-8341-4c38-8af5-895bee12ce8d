from dataclasses import dataclass
from typing import Dict, List

from approval_request.approval_hierarchy.domain.abstract_repos import BaseHierarchyRepo
from approval_request.approval_hierarchy.domain.entities import (
    BaseApprovalConfig,
    DropdownBaseRule,
    HierarchyManagerInputHierarchyData,
    HierarchyNode,
    HierarchyRepoOutputData,
    IntegerDropdownRule,
    RangeRule,
    RoleApprovalConfig,
    UserApprovalConfig,
)
from approval_request.approval_hierarchy.domain.enums import ConditionsEnum, HierarchyApproverTypesEnum
from approval_request.approval_hierarchy.domain.services.exceptions import ApprovalHierarchyException
from approval_request.approval_hierarchy.domain.services.resources import ResourceHelper


class HierarchyService:
    _get_hierarchy_flag = False

    @dataclass(frozen=True)
    class ConfigData:
        data: Dict

    class HierarchyNotFoundException(ApprovalHierarchyException):
        pass

    class InvalidHierarchyConditionException(ApprovalHierarchyException):
        pass

    def __init__(self, hierarchy_repo: BaseHierarchyRepo):
        self.hierarchy_repo: BaseHierarchyRepo = hierarchy_repo

    def set_config(self, data: Dict):
        self.config_obj = self.ConfigData(data=data)

    def get_config(self) -> Dict:
        assert self._get_hierarchy_flag, "Please call get_hierarchy first"
        assert self.set_config, "Please call set_config first"
        return self.config_obj.data

    @staticmethod
    def get_hierarchy_resources() -> List[Dict]:
        return ResourceHelper.get_hierarchy_resources()

    @staticmethod
    def get_resource_conditions(organization_id: int, context: str) -> List[Dict]:
        return ResourceHelper(org_id=organization_id).get_conditions(resource=context)

    def create(self, conditions: Dict, user_id: int):
        self.hierarchy_repo.save_data(config=conditions, user_id=user_id)

    def update(self, user_id: int, value: bool, update_type: str):
        if update_type == self.hierarchy_repo.UpdateType.MARK_SKIP:
            return self.hierarchy_repo.mark_skip_unskip_data(user_id=user_id, value=value)
        return self.hierarchy_repo.mark_active_inactive_data(user_id=user_id, value=value)

    def _transform_hierarchy_data(self, hierarchy_data: HierarchyRepoOutputData) -> Dict[str, HierarchyNode]:
        """
        Transforms the hierarchy data into a dictionary of HierarchyNode objects.

        Args:
            hierarchy_data (HierarchyRepoOutputData): The input hierarchy data.

        Returns:
            Dict[str, HierarchyNode]: A dictionary of HierarchyNode objects, where the keys are the condition IDs.
        """
        # Initialize an empty dictionary to store the hierarchy manager data
        hierahcy_manager_data = {}
        self.set_config(data=hierarchy_data.conditions)

        # Iterate over each condition in the hierarchy data
        for condition in hierarchy_data.conditions["conditions"]:
            if condition["node_type"] in [
                ConditionsEnum.EXPENSE_AMOUNT.value,
                ConditionsEnum.INVOICE_AMOUNT.value,
                ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value,
                ConditionsEnum.ORDER_AMOUNT.value,
            ]:
                rule_list = []
                # Iterate over each rule in the condition's rule list
                for rule in condition["children"]:
                    # Create a RangeRule object and append it to the rule list
                    rule_list.append(RangeRule(min=rule["min"], max=rule["max"], child_id=rule["id"]))
            elif condition["node_type"] in [
                ConditionsEnum.PAYMENT_TERM.value,
                ConditionsEnum.INVOICE_TYPE.value,
                ConditionsEnum.REGION.value,
                ConditionsEnum.PURCHASE_ORDER_TYPE.value,
                ConditionsEnum.BUSINESS_CATEGORY.value,
            ]:
                rule_list = []
                # Iterate over each rule in the condition's rule list
                for rule in condition["children"]:
                    # Create an IntegerDropdownRule object and append it to the rule list
                    rule_list.append(IntegerDropdownRule(value=rule["value"], child_id=rule["id"]))
            elif condition["node_type"] in [ConditionsEnum.INVOICE_TYPE.value]:
                rule_list = []
                # Iterate over each rule in the condition's rule list
                for rule in condition["children"]:
                    # Create a DropdownBaseRule object and append it to the rule list
                    rule_list.append(DropdownBaseRule(value=rule["value"], child_id=rule["id"]))
            elif condition["node_type"] == ConditionsEnum.APPROVER.value:
                rule_list = []
            else:
                # Set the configuration and raise an exception for invalid condition type
                # self.set_config(hierarchy_data.conditions)
                raise self.InvalidHierarchyConditionException(f"Invalid condition type for hierarchy {hierarchy_data}")

            approval_configs: list[BaseApprovalConfig] = []
            # Iterate over each approver in the condition's approvers list
            for i, approver in enumerate(condition.get("approvers")):
                # Check the type of the approver
                if approver.get("type") == HierarchyApproverTypesEnum.USER.value:
                    # Create a UserApprovalConfig object and append it to the approval configs list
                    approval_configs.append(
                        UserApprovalConfig(
                            user_ids=approver.get("ids"), level=i + 1, type=HierarchyApproverTypesEnum.USER.value
                        )
                    )
                elif approver.get("type") == HierarchyApproverTypesEnum.PROJECT_ROLE.value:
                    # Create a RoleApprovalConfig object and append it to the approval configs list
                    approval_configs.append(
                        RoleApprovalConfig(
                            role_ids=approver.get("ids"),
                            level=i + 1,
                            type=HierarchyApproverTypesEnum.PROJECT_ROLE.value,
                        )
                    )

            # Create a HierarchyNode object with the condition's data and add it to the hierarchy manager
            # data dictionary
            hierahcy_manager_data[condition["id"]] = HierarchyNode(
                auto_approve=condition.get("is_auto_approved"),
                approval_configs=approval_configs,
                condition=condition.get("node_type"),
                rule_list=rule_list,
            )

        # Return the hierarchy manager data dictionary
        return hierahcy_manager_data

    def get_hierarchy(self) -> HierarchyManagerInputHierarchyData:
        """
        Retrieves the hierarchy data from the hierarchy repository and transforms it into the required format.

        Returns:
            HierarchyManagerInputHierarchyData: The hierarchy data in the required format.

        Raises:
            HierarchyNotFoundExeption: If the hierarchy data is not found or incomplete.
        """
        try:
            hierarchy_data: HierarchyRepoOutputData = self.hierarchy_repo.get_hierarchy()
            self.set_config(data=hierarchy_data.conditions)
        except BaseHierarchyRepo.HierarchyNotFoundException:
            raise self.HierarchyNotFoundException("Hierarchy not found")
        self._get_hierarchy_flag = True
        if (
            not hierarchy_data
            or not hierarchy_data.conditions["root_id"]
            or not hierarchy_data.conditions["conditions"]
        ):
            raise self.HierarchyNotFoundException("Hierarchy not found")

        return HierarchyManagerInputHierarchyData(
            root_id=hierarchy_data.conditions["root_id"],
            hierarchy=self._transform_hierarchy_data(hierarchy_data),
            is_active=hierarchy_data.is_active,
            is_skipped=hierarchy_data.is_skipped,
            is_auto_approved_on_final_level_misconfiguration=hierarchy_data.is_auto_approved_on_final_level_misconfiguration,
        )
