from typing import Dict, List

from approval_request.approval_hierarchy.domain.enums import ConditionsEnum, OrderTypeConditionEnum
from approval_request.approval_hierarchy.domain.services.conditions import ConditionOptionHelper
from approval_request.approval_hierarchy.domain.services.exceptions import InvalidResourceException
from approval_request.approval_hierarchy.interface.conditions_validation import ConditionNodeChoices
from microcontext.choices import MicroContextChoices

ResourceOutputNameMapping = {
    # TODO: Need to update this if new resources added
    MicroContextChoices.EXPENSE: "Expense",
    MicroContextChoices.INVOICE: "Invoice",
    MicroContextChoices.PAYMENT_REQUEST: "Vendor Payment",
    MicroContextChoices.ORDER: "Order",
}


class ResourceHelper:
    DROPDOWN = "dropdown"
    RANGE = "range"

    def __init__(self, org_id: int) -> None:
        self.org_id = org_id

    @staticmethod
    def get_hierarchy_resources() -> List[Dict]:
        # TODO: Need to change this if new resources added
        return [
            {
                "id": MicroContextChoices.EXPENSE,
                "name": ResourceOutputNameMapping[MicroContextChoices.EXPENSE],
            },
            {
                "id": MicroContextChoices.INVOICE,
                "name": ResourceOutputNameMapping[MicroContextChoices.INVOICE],
            },
            {
                "id": MicroContextChoices.PAYMENT_REQUEST,
                "name": ResourceOutputNameMapping[MicroContextChoices.PAYMENT_REQUEST],
            },
            {
                "id": MicroContextChoices.ORDER,
                "name": ResourceOutputNameMapping[MicroContextChoices.ORDER],
            },
        ]

    def _get_region_condition_data(self):
        return {
            "id": ConditionsEnum.REGION.value,
            "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.REGION.value],
            "type": self.DROPDOWN,
            "options": ConditionOptionHelper(org_id=self.org_id).get_options(condition=ConditionsEnum.REGION.value),
        }

    def _get_payment_term_condition_data(self):
        return {
            "id": ConditionsEnum.PAYMENT_TERM.value,
            "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.PAYMENT_TERM.value],
            "type": self.DROPDOWN,
            "options": ConditionOptionHelper(org_id=self.org_id).get_options(
                condition=ConditionsEnum.PAYMENT_TERM.value
            ),
        }

    def _get_order_type_condition_data(self):
        return {
            "id": ConditionsEnum.PURCHASE_ORDER_TYPE.value,
            "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.PURCHASE_ORDER_TYPE.value],
            "type": self.DROPDOWN,
            "options": ConditionOptionHelper(org_id=self.org_id).get_options(
                condition=ConditionsEnum.PURCHASE_ORDER_TYPE.value
            ),
        }

    def _get_business_category_condition_data(self):
        return {
            "id": ConditionsEnum.BUSINESS_CATEGORY.value,
            "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.BUSINESS_CATEGORY.value],
            "type": self.DROPDOWN,
            "options": ConditionOptionHelper(org_id=self.org_id).get_options(
                condition=ConditionsEnum.BUSINESS_CATEGORY.value
            ),
        }

    def _get_other_expenses_conditions(self) -> List[Dict]:
        return [
            {
                "id": ConditionsEnum.EXPENSE_AMOUNT.value,
                "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.EXPENSE_AMOUNT.value],
                "type": self.RANGE,
            },
            self._get_region_condition_data(),
            self._get_business_category_condition_data(),
        ]

    def _get_invoice_conditions(self) -> List[Dict]:
        return [
            self._get_payment_term_condition_data(),
            self._get_region_condition_data(),
            {
                "id": ConditionsEnum.INVOICE_TYPE.value,
                "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.INVOICE_TYPE.value],
                "type": self.DROPDOWN,
                "options": ConditionOptionHelper(org_id=self.org_id).get_options(
                    condition=ConditionsEnum.INVOICE_TYPE.value
                ),
            },
            {
                "id": ConditionsEnum.INVOICE_AMOUNT.value,
                "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.INVOICE_AMOUNT.value],
                "type": self.RANGE,
            },
            self._get_business_category_condition_data(),
        ]

    def _get_payment_request_conditions(self) -> List[Dict]:
        order_type_condition_data = self._get_order_type_condition_data()
        order_type_condition_data["options"].append(
            {"id": OrderTypeConditionEnum.NO_ORDER.value[0], "name": OrderTypeConditionEnum.NO_ORDER.value[1]}
        )
        return [
            self._get_payment_term_condition_data(),
            self._get_region_condition_data(),
            {
                "id": ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value,
                "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value],
                "type": self.RANGE,
            },
            order_type_condition_data,
            self._get_business_category_condition_data(),
        ]

    def _get_order_conditions(self) -> List[Dict]:
        return [
            self._get_region_condition_data(),
            self._get_payment_term_condition_data(),
            self._get_order_type_condition_data(),
            {
                "id": ConditionsEnum.ORDER_AMOUNT.value,
                "name": dict(ConditionNodeChoices.choices)[ConditionsEnum.ORDER_AMOUNT.value],
                "type": self.RANGE,
            },
            self._get_business_category_condition_data(),
        ]

    def get_conditions(self, resource: MicroContextChoices) -> List[Dict]:
        if resource == MicroContextChoices.EXPENSE:
            return self._get_other_expenses_conditions()
        elif resource == MicroContextChoices.INVOICE:
            return self._get_invoice_conditions()
        elif resource == MicroContextChoices.PAYMENT_REQUEST:
            return self._get_payment_request_conditions()
        elif resource == MicroContextChoices.ORDER:
            return self._get_order_conditions()
        else:
            raise InvalidResourceException("Invalid Resource")
