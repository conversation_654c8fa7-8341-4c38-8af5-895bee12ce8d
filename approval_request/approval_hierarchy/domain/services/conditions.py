from typing import Dict, List, Optional, Union

import structlog
from django.db.models import F

from approval_request.approval_hierarchy.domain.entities import (
    BaseRule,
    HierarchyManagerInputHierarchyData,
    HierarchyNode,
    RoleApprovalConfig,
    UserApprovalConfig,
)
from approval_request.approval_hierarchy.domain.enums import (
    BusinessCategoryTypeConditionEnum,
    ConditionsEnum,
    PaymentTermConditionEnum,
)
from approval_request.approval_hierarchy.exceptions import ApprovalHierarchyException
from approval_request.approval_hierarchy.interface.serializers.child_conditions import (
    AmountRangeInputSerializer,
    BaseChildConditionSerializer,
    DropDownInputSerializer,
)
from common.context_values import BaseContextValues
from common.entities import ContextProviders
from common.injector import Injectable, inject
from common.intefaces import ContextValueProviderAbstractInterface
from controlroom.data.models import BusinessCategory
from core.selectors import OrganizationConfigRoleService, get_org_regions
from core.tnc_config.data.choices import OrganizationTnCTypeChoice
from order.config.data.selectors import get_active_purchase_order_types_qs
from order.invoice.data.models import InvoiceType

logger = structlog.get_logger(__name__)


class ConditionOptionHelper:
    def __init__(self, org_id: int) -> None:
        self.org_id = org_id

    def _get_region_options(self) -> List[Dict]:
        return list(get_org_regions(org_id=self.org_id).values("id", "name")) + [{"id": 0, "name": "Other"}]

    def _get_payment_terms_options(self) -> List[Dict]:
        return list(
            OrganizationConfigRoleService.get_active_tnc_list(
                org_id=self.org_id,
                type=OrganizationTnCTypeChoice.ORDER_PAYMENT_TERM_AND_CONDITIONS,
            )
            .annotate(name=F("title"))
            .values("id", "name")
        ) + [
            {
                "id": PaymentTermConditionEnum.CUSTOM_PAYMENT_TERM.value[0],
                "name": PaymentTermConditionEnum.CUSTOM_PAYMENT_TERM.value[1],
            },
            {
                "id": PaymentTermConditionEnum.NO_PAYMENT_TERM.value[0],
                "name": PaymentTermConditionEnum.NO_PAYMENT_TERM.value[1],
            },
            {
                "id": PaymentTermConditionEnum.INACTIVE_PAYMENT_TERM.value[0],
                "name": PaymentTermConditionEnum.INACTIVE_PAYMENT_TERM.value[1],
            },
        ]

    def _get_invoice_type_options(self) -> List[Dict]:
        return list(InvoiceType.objects.filter(is_active=True).values("id", "name"))

    def _get_purchase_order_type_options(self) -> List[Dict]:
        org_purchase_order_types = get_active_purchase_order_types_qs(org_id=self.org_id)
        return [
            {"id": org_purchase_order_type.id, "name": org_purchase_order_type.name}
            for org_purchase_order_type in org_purchase_order_types
        ] + [{"id": 0, "name": "Other"}]

    def _get_business_category_options(self) -> List[Dict]:
        return list(BusinessCategory.objects.filter(organization_id=self.org_id).values("id", "name")) + [
            {
                "id": BusinessCategoryTypeConditionEnum.SHARED_PROJECT.value[0],
                "name": BusinessCategoryTypeConditionEnum.SHARED_PROJECT.value[1],
            },
            # {
            #     "id": BusinessCategoryTypeConditionEnum.NO_BUSINESS_CATEGORY.value[0],
            #     "name": BusinessCategoryTypeConditionEnum.NO_BUSINESS_CATEGORY.value[1],
            # },
        ]

    def get_options(self, condition: ConditionsEnum):
        if condition == ConditionsEnum.REGION.value:
            return self._get_region_options()
        elif condition == ConditionsEnum.PAYMENT_TERM.value:
            return self._get_payment_terms_options()
        elif condition == ConditionsEnum.INVOICE_TYPE.value:
            return self._get_invoice_type_options()
        elif condition == ConditionsEnum.PURCHASE_ORDER_TYPE.value:
            return self._get_purchase_order_type_options()
        elif condition == ConditionsEnum.BUSINESS_CATEGORY.value:
            return self._get_business_category_options()


class NodeInputValidatorProvider:
    def _get_region_serializer(self) -> DropDownInputSerializer:
        return DropDownInputSerializer

    def _get_amount_range_serializer(self) -> AmountRangeInputSerializer:
        return AmountRangeInputSerializer

    def _get_payment_terms_serializer(self) -> DropDownInputSerializer:
        return DropDownInputSerializer

    def _get_invoice_type_serializer(self) -> DropDownInputSerializer:
        return DropDownInputSerializer

    def _get_order_type_serializer(self) -> DropDownInputSerializer:
        return DropDownInputSerializer

    def _get_business_category_serializer(self) -> DropDownInputSerializer:
        return DropDownInputSerializer

    def get_validator(self, node_type: str) -> BaseChildConditionSerializer:
        if node_type == ConditionsEnum.REGION.value:
            return self._get_region_serializer()
        elif node_type in [
            ConditionsEnum.EXPENSE_AMOUNT.value,
            ConditionsEnum.INVOICE_AMOUNT.value,
            ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value,
            ConditionsEnum.ORDER_AMOUNT.value,
        ]:
            return self._get_amount_range_serializer()
        elif node_type == ConditionsEnum.PAYMENT_TERM.value:
            return self._get_payment_terms_serializer()
        elif node_type == ConditionsEnum.INVOICE_TYPE.value:
            return self._get_invoice_type_serializer()
        elif node_type == ConditionsEnum.PURCHASE_ORDER_TYPE.value:
            return self._get_order_type_serializer()
        elif node_type == ConditionsEnum.BUSINESS_CATEGORY.value:
            return self._get_business_category_serializer()


class RuleExecuter(Injectable):
    def get_child_id(
        self,
        rule_list: list[BaseRule],
        provider: ContextValueProviderAbstractInterface,
        context_input_data: BaseContextValues,
    ) -> Optional[str]:
        value = provider.get_value(data=context_input_data)
        for rule in rule_list:
            if rule.evaluate(value=value):
                return rule.get_child_id()


class ConditionExecuter:
    hierarchy: HierarchyManagerInputHierarchyData = None
    providers: ContextProviders = None

    class ConditionExecuterException(ApprovalHierarchyException):
        pass

    class MisconfiguredHierarchyViaConfigException(ConditionExecuterException):
        pass

    class AutoApprovedHierarchyException(ConditionExecuterException):
        pass

    @inject(params={"rule_executer": RuleExecuter})
    def __init__(self, rule_executer: RuleExecuter):
        self.rule_executer = rule_executer

    def execute(
        self,
        hierarchy_data: HierarchyManagerInputHierarchyData,
        providers: ContextProviders,
        context_input_data: BaseContextValues,
    ) -> List[Union[RoleApprovalConfig, UserApprovalConfig]]:
        self.hierarchy_data = hierarchy_data
        self.providers = providers
        self.context_input_data = context_input_data
        return self._execute(hierarchy_data.root_id)

    def _execute(self, node_id: str) -> List[Union[RoleApprovalConfig, UserApprovalConfig]]:
        try:
            node: HierarchyNode = self.hierarchy_data.get_node(node_id=node_id)
        except HierarchyManagerInputHierarchyData.NodeNotFoundError:
            logger.debug("Node not found", node_id=node_id)
            raise self.MisconfiguredHierarchyViaConfigException("Misconfigured hierarchy")
        if node.auto_approve:
            raise self.AutoApprovedHierarchyException("Auto approved hierarchy")
        if node.approval_configs:
            return node.approval_configs
        child_node_id = self.rule_executer.get_child_id(
            rule_list=node.rule_list,
            provider=self.providers.get_provider(node.condition),
            context_input_data=self.context_input_data,
        )
        if child_node_id is None:
            logger.debug("No child node")
            raise self.MisconfiguredHierarchyViaConfigException("Misconfigured hierarchy")
        return self._execute(node_id=child_node_id)
