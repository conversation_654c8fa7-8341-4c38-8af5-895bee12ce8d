from typing import List

from django.db.models import TextChoices
from rest_framework.serializers import ValidationError

from approval_request.approval_hierarchy.domain.enums import ConditionsEnum
from microcontext.choices import MicroContextChoices


class ConditionNodeChoices(TextChoices):
    PAYMENT_TERM = ConditionsEnum.PAYMENT_TERM.value, "Payment Term"
    REGION = ConditionsEnum.REGION.value, "Region"
    AMOUNT_RANGE = ConditionsEnum.EXPENSE_AMOUNT.value, "Expense Amount"
    INVOICE_TYPE = ConditionsEnum.INVOICE_TYPE.value, "Invoice Type"
    INVOICE_AMOUNT_RANGE = ConditionsEnum.INVOICE_AMOUNT.value, "Invoice Amount"
    PAYMENT_REQUEST_AMOUNT_RANGE = ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value, "Payment Amount"
    ORDER_AMOUNT = ConditionsEnum.ORDER_AMOUNT.value, "Order Amount"
    ORDER_TYPE = ConditionsEnum.PURCHASE_ORDER_TYPE.value, "Purchase Order Type"
    BUSINESS_CATEGORY = ConditionsEnum.BUSINESS_CATEGORY.value, "Business Category"
    APPROVER = ConditionsEnum.APPROVER.value, "Approver"


class ApproverChoices(TextChoices):
    USER = "user", "User"
    PROJECT_ROLE = "project_role", "Project Role"


NodeConditionMapping = {
    MicroContextChoices.EXPENSE: [
        ConditionsEnum.REGION.value,
        ConditionsEnum.EXPENSE_AMOUNT.value,
        ConditionsEnum.APPROVER.value,
        ConditionsEnum.BUSINESS_CATEGORY.value,
    ],
    MicroContextChoices.INVOICE: [
        ConditionsEnum.REGION.value,
        ConditionsEnum.PAYMENT_TERM.value,
        ConditionsEnum.INVOICE_TYPE.value,
        ConditionsEnum.INVOICE_AMOUNT.value,
        ConditionsEnum.APPROVER.value,
        ConditionsEnum.BUSINESS_CATEGORY.value,
    ],
    MicroContextChoices.PAYMENT_REQUEST: [
        ConditionsEnum.REGION.value,
        ConditionsEnum.PAYMENT_TERM.value,
        ConditionsEnum.PAYMENT_REQUEST_AMOUNT.value,
        ConditionsEnum.PURCHASE_ORDER_TYPE.value,
        ConditionsEnum.APPROVER.value,
        ConditionsEnum.BUSINESS_CATEGORY.value,
    ],
    MicroContextChoices.ORDER: [
        ConditionsEnum.REGION.value,
        ConditionsEnum.ORDER_AMOUNT.value,
        ConditionsEnum.PURCHASE_ORDER_TYPE.value,
        ConditionsEnum.PAYMENT_TERM.value,
        ConditionsEnum.APPROVER.value,
        ConditionsEnum.BUSINESS_CATEGORY.value,
    ],
}


def get_resource_conditons(resource: str) -> List:
    return NodeConditionMapping.get(resource)


def validate_condition(
    condition: dict, conditions_list: List[dict], ancestor_ids_set=set(), ancestor_node_type_set=set()
):
    condition_id = condition["id"]
    ancestor_ids_set.add(condition_id)
    conditions_list.remove(condition)

    # If condition has children, there should be a condition present with id = <id of child element>
    for child in condition["children"]:
        child_id = child.get("id")

        # No child can have same id as any of its ancestor
        if child_id in ancestor_ids_set:
            raise ValidationError({child_id: "Found Same As Its Ancestor"})

        child_condition = next((c for c in conditions_list if c["id"] == child_id), None)
        if child_condition is not None:
            # No child can have same node type as any of its ancestor
            child_node_type = child_condition["node_type"]
            if child_node_type in ancestor_node_type_set:
                raise ValidationError({condition_id: "Node Type Found Same As Its Ancestor"})
            ancestor_node_type_set.add(child_node_type)
            validate_condition(
                child_condition,
                conditions_list=conditions_list,
                ancestor_ids_set=ancestor_ids_set,
                ancestor_node_type_set=ancestor_node_type_set,
            )
            ancestor_node_type_set.remove(child_node_type)


def validate_hierarchy_conditions(conditions_list: List[dict]) -> None:
    for condition in conditions_list:
        validate_condition(
            condition,
            conditions_list=conditions_list,
            ancestor_ids_set=set(),
            ancestor_node_type_set=set([condition["node_type"]]),
        )


def validate_approval_hierarchy_range(range_condition) -> None:
    condition_id = range_condition["id"]
    amount_range_levels = range_condition["children"]

    last_max_value = None
    if not amount_range_levels:
        raise ValidationError({condition_id: "Amount range condition must have children"})

    for level, child in enumerate(amount_range_levels):
        min_value = int(child.get("min"))
        max_value = int(child.get("max")) if child.get("max") else None

        # Rule 1: For the first level, min must be 0
        if level == 0 and min_value != 0:
            raise ValidationError({condition_id: "For the first level, min must be 0"})

        # Rule 2: Min value of each level must be one greater than the max value of the previous level
        if level > 0 and min_value != last_max_value + 1:
            raise ValidationError(
                {
                    condition_id: f"Min value of level {level} must be one greater than the max value \
                        of the previous level"
                }
            )

        # Rule 3: No overlapping in min and max of any level
        if level > 0 and min_value <= last_max_value:
            raise ValidationError({condition_id: f"Overlapping in min and max of level {level}"})

        # Rule 4: Max of the last level should be infinite (None)
        if level == len(amount_range_levels) - 1 and max_value is not None:
            raise ValidationError({condition_id: "Max of the last level should be infinite"})

        last_max_value = max_value
