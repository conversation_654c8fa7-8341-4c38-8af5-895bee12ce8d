import abc
from typing import Dict, List

from approval_request.domain.constants import Request<PERSON><PERSON><PERSON><PERSON>oryActionsEnum
from approval_request.domain.entities import (
    ApprovalRequestActionHistoryEntity,
    ApprovalRequestApproverEntity,
    ApprovalRequestEntity,
    ApproverData,
    ApproverRequestActionData,
    RequestCreateRepoData,
    RequestRepoData,
    RequestResetActionRepoData,
    RequestSubmitActionData,
)
from approval_request.domain.exceptions import ApprovalRequestException
from core.models import User


class RequestAbstractRepo:
    class ApproverNotFoundException(ApprovalRequestException):
        pass

    class RequestRepoException(ApprovalRequestException):
        pass

    class RequestNotFoundException(RequestRepoException):
        pass

    class RequestCreateException(RequestRepoException):
        pass

    class RequestResetException(RequestRepoException):
        pass

    class RequestSubmitException(RequestRepoException):
        pass

    class RequestCancelException(RequestRepoException):
        pass

    class RequestHoldException(RequestRepoException):
        pass

    class RequestSaveException(RequestRepoException):
        pass

    @abc.abstractmethod
    def get_action_data(self, request_id: int) -> ApproverRequestActionData:
        ...

    @abc.abstractmethod
    def save_action_data(self, action_data: ApproverRequestActionData, updated_by_id: int):
        ...

    @abc.abstractmethod
    def create(self, data: RequestCreateRepoData) -> int:
        pass

    @abc.abstractmethod
    def get_request_data(self, request_id: int) -> RequestRepoData:
        ...

    @abc.abstractmethod
    def reset(self, data: RequestResetActionRepoData):
        ...

    @abc.abstractmethod
    def submit(self, data: RequestSubmitActionData):
        ...

    @abc.abstractmethod
    def delete(self, request_id: int, user_id: int):
        ...


class RequestHistoryAbstractRepo:
    @abc.abstractmethod
    def get_request_action_history_data(self, request_id: int) -> List[ApprovalRequestActionHistoryEntity]:
        pass

    @abc.abstractmethod
    def get_pending_approvers(
        self, request_id: int, level: int, status: RequestActionHistoryActionsEnum
    ) -> List[ApprovalRequestApproverEntity]:
        pass

    @abc.abstractmethod
    def create_approver_data(self, user: User) -> ApproverData:
        pass

    @abc.abstractmethod
    def create_approval_request_entity(self, request_id: int) -> ApprovalRequestEntity:
        pass

    @abc.abstractmethod
    def create_approver_id_to_approver_name_mapping(self, errors_approver_ids_mapping: Dict) -> ApprovalRequestEntity:
        pass
