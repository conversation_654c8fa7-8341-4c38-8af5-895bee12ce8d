from dataclasses import dataclass
from enum import Enum
from typing import Dict, List

from external_services.messengers.base_messenger import BaseMessengerData


class EventTemplates(Enum):
    RECCE_STARTED = "recce_begin_v1"
    RECCE_SUBMITTED_SELF = "recce_submit_2_1_v1"
    RECCE_SUBMITTED = "recce_submit_1_1_v2"
    RECCE_FEEDBACK_FORM = "recce_feedback_v2"
    RECCE_LINK_CREATED = "recce_creation_final_1_v1"
    DESIGN_FREEZE = "design_freeze"
    DESIGN_FILE_COMMENT = "comment_design_file"
    DUE_DATE_MODIFICATION = "2_dd"
    PROGRESS_REPORT_CREATED = "dpr_revamp_submitted_v1"
    PROJECT_DATE_CHANGED = "dates_change"
    COMMENT_APPROVAL_REQUESTED = "comment_approval_requested_v4"
    COMMENT_APPROVAL_MENTIONED = "comment_approval_mentioned_v5"
    COMMENT_APPROVAL_ACCEPTED_REJECTED = "comment_approval_accepted_rejected_v4"
    MENTIONED_IN_COMMENT = "user_tag_v4"
    COMMENT_APPROVAL_APPROVED = "user_request_approved"
    COMMENT_APPROVAL_REJECTED = "user_request_rejected"
    MARK_EXECUTION_COMPLETED = "mark_execution_completed_v1"
    VENDOR_PROGRESS_REPORT_CREATED = "dpr_revamp_vendor_v1"
    WORK_PROGRESS_EXPORT_REPORT = "dpr_revamp_export_v2"
    PROPOSAL_SENT = "proposal_sent_v1"
    PROPOSAL_REJECTED = "proposal_rejected_v3"
    DESIGN_FILE_NEW_VERSION = "design_revision_internal_v3"
    DESIGN_FILE_NEW_VERSION_POST_FREEZE = "design_revision_delivery_v1"
    EXPENSE_REQUEST_CREATED = "expense_request_created_approved_v2"
    EXPENSE_REQUEST_APPROVED = "expense_request_created_approved_v2"
    EXPENSE_REQUEST_FINALLY_APPROVED = "expense_request_finally_approved_v1"
    EXPENSE_REQUEST_REJECTED = "expense_request_rejected_v1"
    EXPENSE_REQUEST_EDITED = "expense_request_edited_v1"
    EXPENSE_REQUEST_HOLD = "expense_request_hold_v1"
    INVOICE_REQUEST_CREATED = "invoice_request_created_approved_v2"
    INVOICE_REQUEST_APPROVED = "invoice_request_created_approved_v2"
    INVOICE_REQUEST_FINALLY_APPROVED = "invoice_request_finally_approved_v2"
    INVOICE_REQUEST_REJECTED = "invoice_request_rejected_v1"
    INVOICE_REQUEST_EDITED = "invoice_request_edited_v2"
    INVOICE_REQUEST_HOLD = "invoice_request_hold_v2"
    PAYMENT_REQUEST_AGAINST_INVOICE_CREATED = "payment_request_against_invoice_created_approved_v2"
    PAYMENT_REQUEST_AGAINST_INVOICE_APPROVED = "payment_request_against_invoice_created_approved_v2"
    PAYMENT_REQUEST_AGAINST_INVOICE_FINALLY_APPROVED = "payment_request_against_invoice_finally_approved_v1"
    PAYMENT_REQUEST_AGAINST_INVOICE_REJECTED = "payment_request_against_invoice_rejected_v1"
    PAYMENT_REQUEST_AGAINST_INVOICE_EDITED = "payment_request_against_invoice_edited_v2"
    PAYMENT_REQUEST_AGAINST_INVOICE_HOLD = "payment_request_against_invoice_hold_v2"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_CREATED = "payment_request_against_po_advance_created_approved_v2"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_APPROVED = "payment_request_against_po_advance_created_approved_v2"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_FINALLY_APPROVED = "payment_request_against_po_advance_finally_approved_v1"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_REJECTED = "payment_request_against_po_advance_rejected_v1"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_EDITED = "payment_request_against_po_advance_edited_v1"
    PAYMENT_REQUEST_AGAINST_PO_ADVANCE_HOLD = "payment_request_against_po_advance_hold_v2"
    VENDOR_ORDER_REGULAR_REQUEST_CREATED = "vendor_order_regular_request_created_approved_v2"
    VENDOR_ORDER_REGULAR_REQUEST_APPROVED = "vendor_order_regular_request_created_approved_v2"
    VENDOR_ORDER_REGULAR_REQUEST_FINALLY_APPROVED = "vendor_order_regular_request_finally_approved_v1"
    VENDOR_ORDER_REGULAR_REQUEST_REJECTED = "vendor_order_regular_request_rejected_v1"
    VENDOR_ORDER_REGULAR_REQUEST_EDITED = "vendor_order_regular_request_edited_v2"
    VENDOR_ORDER_REGULAR_REQUEST_HOLD = "vendor_order_regular_request_hold_v2"
    INSTA_ORDER_REQUEST_CREATED = "insta_order_request_created_approved_v1"
    INSTA_ORDER_REQUEST_APPROVED = "insta_order_request_created_approved_v1"
    INSTA_ORDER_REQUEST_FINALLY_APPROVED = "insta_order_request_finally_approved_v1"
    INSTA_ORDER_REQUEST_REJECTED = "insta_order_request_rejected_v1"
    INSTA_ORDER_REQUEST_EDITED = "insta_order_request_edited_v1"
    INSTA_ORDER_REQUEST_HOLD = "insta_order_request_hold_v2"
    INVENTORY_TRANSFER_BATCH_CREATED = "inventory_transfer_batch_created_v2"
    INVENTORY_TRANSFER_BATCH_ACTION = "inventory_transfer_batch_action_v2"
    SNAG_ASSIGNED = "snag_assigned_v3"
    SNAG_ALLOTTED_AND_TIMELINE_COMMITTED = "snag_allotted_and_timeline_committed_v4"
    SNAG_BULK_ASSIGNED = "snag_n_assignment_v2"
    SNAG_UNRESOLVED = "snag_unresolved_v3"
    SNAG_BULK_ALLOT_AND_TIMELINE_COMMITTED = "snag_n_number_allotment_and_timeline_commit_v1"
    USER_ONBOARDED = "user_onboard_v2"
    TASK_ASSIGNED = "task_assigned_v2"
    TASK_MENTIONED = "task_mentioned_v2"
    TASK_REPLY_MENTIONED = "task_reply_mentioned_v2"
    APPROVAL_REQUEST_REPLY_MENTIONED = "approval_request_reply_mentioned_v1"
    TASK_UPDATED = "task_updated_v3"
    TASK_DONE = "task_done_v3"
    TASK_REMINDER = "task_reminder_v2"
    TASK_ARCHIVED_FOR_ALL = "task_archive_for_all_v3"
    TODAYS_DUE_TASK_COUNT = "todays_due_task_count_v3"
    LOGIN_VIA_WHATSAPP = "login_via_whatsapp_v1"
    LEAD_ASSIGNED = "lead_assignment_to_assignee_v2"
    MIDDLE_EAST_LAUNCH_IOS = "middleeastlaunchios"
    MIDDLE_EAST_LAUNCH = "middleeastlaunch"
    PROJECT_SCHEDULE_COMPLETED = "activity_schedule_completed_v2"
    PROJECT_SCHEDULE_OVERDUE = "activity_schedule_overdue_v2"
    PROJECT_SCHEDULE_DELAY = "activity_schedule_delayed_v3"
    PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED = "activity_schedule_assigned_v2"
    PROJECT_SCHEDULE_ACTIVITIES_DELETED = "activity_schedule_bulk_deleted_v2"
    PROJECT_SCHEDULE_ACTIVITY_DELETED = "activity_schedule_activity_deleted_v3"

    def get_broadcast_name(self) -> str:
        if self in [
            self.RECCE_STARTED,
            self.RECCE_SUBMITTED_SELF,
            self.RECCE_SUBMITTED,
            self.RECCE_FEEDBACK_FORM,
            self.RECCE_LINK_CREATED,
        ]:
            return "recce_alert"
        if self in [
            self.DESIGN_FREEZE,
            self.DESIGN_FILE_COMMENT,
            self.DESIGN_FILE_NEW_VERSION,
            self.DESIGN_FILE_NEW_VERSION_POST_FREEZE,
        ]:
            return "design_alert"
        if self in [
            self.DUE_DATE_MODIFICATION,
            self.PROGRESS_REPORT_CREATED,
            self.PROJECT_DATE_CHANGED,
            self.MARK_EXECUTION_COMPLETED,
            self.VENDOR_PROGRESS_REPORT_CREATED,
            self.PROPOSAL_SENT,
            self.PROPOSAL_REJECTED,
            self.WORK_PROGRESS_EXPORT_REPORT,
        ]:
            return "project_alert"
        if self in [
            self.COMMENT_APPROVAL_REQUESTED,
            self.MENTIONED_IN_COMMENT,
            self.COMMENT_APPROVAL_APPROVED,
            self.COMMENT_APPROVAL_REJECTED,
            self.COMMENT_APPROVAL_MENTIONED,
            self.COMMENT_APPROVAL_ACCEPTED_REJECTED,
        ]:
            return "comment_alert"
        if self in [
            self.EXPENSE_REQUEST_CREATED,
            self.EXPENSE_REQUEST_APPROVED,
            self.EXPENSE_REQUEST_FINALLY_APPROVED,
            self.EXPENSE_REQUEST_EDITED,
            self.EXPENSE_REQUEST_REJECTED,
            self.EXPENSE_REQUEST_HOLD,
            self.INVOICE_REQUEST_CREATED,
            self.INVOICE_REQUEST_APPROVED,
            self.INVOICE_REQUEST_FINALLY_APPROVED,
            self.INVOICE_REQUEST_EDITED,
            self.INVOICE_REQUEST_REJECTED,
            self.INVOICE_REQUEST_HOLD,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_CREATED,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_APPROVED,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_FINALLY_APPROVED,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_EDITED,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_REJECTED,
            self.PAYMENT_REQUEST_AGAINST_INVOICE_HOLD,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_CREATED,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_APPROVED,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_FINALLY_APPROVED,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_EDITED,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_REJECTED,
            self.PAYMENT_REQUEST_AGAINST_PO_ADVANCE_HOLD,
            self.VENDOR_ORDER_REGULAR_REQUEST_CREATED,
            self.VENDOR_ORDER_REGULAR_REQUEST_APPROVED,
            self.VENDOR_ORDER_REGULAR_REQUEST_FINALLY_APPROVED,
            self.VENDOR_ORDER_REGULAR_REQUEST_EDITED,
            self.VENDOR_ORDER_REGULAR_REQUEST_REJECTED,
            self.VENDOR_ORDER_REGULAR_REQUEST_HOLD,
            self.INSTA_ORDER_REQUEST_CREATED,
            self.INSTA_ORDER_REQUEST_APPROVED,
            self.INSTA_ORDER_REQUEST_FINALLY_APPROVED,
            self.INSTA_ORDER_REQUEST_EDITED,
            self.INSTA_ORDER_REQUEST_REJECTED,
            self.INSTA_ORDER_REQUEST_HOLD,
            self.APPROVAL_REQUEST_REPLY_MENTIONED,
        ]:
            return "request_alert"
        if self in [
            self.INVENTORY_TRANSFER_BATCH_CREATED,
            self.INVENTORY_TRANSFER_BATCH_ACTION,
        ]:
            return "inventory_alert"
        if self in [
            self.USER_ONBOARDED,
        ]:
            return "user_alert"
        if self in [
            self.TASK_ASSIGNED,
            self.TASK_MENTIONED,
            self.TASK_REPLY_MENTIONED,
            self.TASK_UPDATED,
            self.TASK_DONE,
            self.TASK_ARCHIVED_FOR_ALL,
            self.TASK_REMINDER,
            self.TODAYS_DUE_TASK_COUNT,
        ]:
            return "task_alert"

        if self in [
            self.SNAG_ASSIGNED,
            self.SNAG_ALLOTTED_AND_TIMELINE_COMMITTED,
            self.SNAG_BULK_ASSIGNED,
            self.SNAG_UNRESOLVED,
            self.SNAG_BULK_ALLOT_AND_TIMELINE_COMMITTED,
        ]:
            return "snag_alert"

        if self == self.LOGIN_VIA_WHATSAPP:
            return "login"

        if self in [self.LEAD_ASSIGNED]:
            return "lead_alert"

        if self in [
            self.PROJECT_SCHEDULE_COMPLETED,
            self.PROJECT_SCHEDULE_OVERDUE,
            self.PROJECT_SCHEDULE_DELAY,
            self.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED,
            self.PROJECT_SCHEDULE_ACTIVITIES_DELETED,
            self.PROJECT_SCHEDULE_ACTIVITY_DELETED,
        ]:
            return "proejct_schedule_alert"

        return "default"


@dataclass(frozen=True)
class ReceiverData:
    phone_number: str
    name: str


@dataclass(frozen=True)
class WhatsAppTemplateData:
    name: EventTemplates
    context: Dict


@dataclass(frozen=True)
class WhatsappMessengerData(BaseMessengerData):
    template_data: WhatsAppTemplateData
    receivers: List[ReceiverData]


@dataclass(frozen=True)
class WhatsappReceiversMessageId:
    phone_number: str
    local_message_id: str
    is_valid_number: bool
