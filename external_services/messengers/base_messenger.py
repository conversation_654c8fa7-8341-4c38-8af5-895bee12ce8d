import abc
from dataclasses import dataclass


@dataclass(frozen=True)
class BaseMessengerData:
    ...


class BaseMessengerClient:
    _data: BaseMessengerData = None

    @abc.abstractmethod
    def send(self):
        ...


class BaseMessanger:
    def __init__(self, data: BaseMessengerData):
        self.client: BaseMessengerClient = None

    @abc.abstractmethod
    def process(self):
        ...

    def send_message(self):
        if not self.client:
            raise NotImplementedError("Client not set")
        return self.client.send()
