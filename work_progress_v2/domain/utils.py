from common.json_parser.constants import const as J<PERSON><PERSON>_TYPE
from work_progress_v2.domain.entities import BlockMetaDataEntity
from work_progress_v2.domain.report_entities import SystemGeneratedUpdatesEntity, WorkProgressBlockBaseModel


class WorkProgressReportItemBlockHelper:
    @classmethod
    def convert_v1_block_data_into_v2(cls, block: dict) -> WorkProgressBlockBaseModel:
        type = block.get("type")
        meta = block.get("meta")

        assert type is not None, "Type is required for block."

        new_block = WorkProgressBlockBaseModel(type=type, meta={})
        tag_type = block.get("tag_type")

        if tag_type:
            new_block.meta = meta

            if new_block.meta is None:
                new_block.meta = {}

            new_block.meta["tag_context"] = tag_type

        if type == JSON_TYPE.TAG:
            text = block.get("text", "").removeprefix("#")
            new_block.text = text
            new_block.name = text
            new_block.type = JSON_TYPE.HASHTAG

        new_block.text = block.get("text")
        new_block.text_type = block.get("text_type")

        if meta:
            new_block.id = meta.get("id")

        return new_block

    @classmethod
    def get_meta_data_from_blocks(cls, blocks: list[WorkProgressBlockBaseModel]) -> BlockMetaDataEntity:
        boq_element_id_list = []
        category_id_list = []
        library_element_id = []
        activity_id_list = []

        for block in blocks:
            if block.type == JSON_TYPE.TAG:
                assert block.meta is not None, "Meta is required for tag block."
                assert block.meta.get(JSON_TYPE.ID) is not None, "ID is required for tag block."

                tag_type = block.meta.get(JSON_TYPE.TAG_TYPE)

                if tag_type == JSON_TYPE.BOQ_ELEMENT:
                    boq_element_id_list.append(block.id)

                if tag_type == JSON_TYPE.ELEMENT_CATEGORY:
                    category_id_list.append(block.id)

                if tag_type == JSON_TYPE.LIBRARY_ELEMENT:
                    library_element_id.append(block.id)

                if tag_type == JSON_TYPE.ACTIVITY_SCHEDULE:
                    activity_id_list.append(block.id)

        return BlockMetaDataEntity(
            boq_element_id_list=boq_element_id_list,
            category_id_list=category_id_list,
            library_element_id=library_element_id,
            activity_id_list=activity_id_list,
        )

    @classmethod
    def create_system_generated_block(cls, data: SystemGeneratedUpdatesEntity) -> list[WorkProgressBlockBaseModel]:
        blocks: list[WorkProgressBlockBaseModel] = []

        blocks.append(
            WorkProgressBlockBaseModel(
                id=data.element_id,
                type=JSON_TYPE.HASHTAG,
                text=data.element_name,
                name=data.element_name,
                meta={
                    JSON_TYPE.TAG_TYPE: JSON_TYPE.BOQ_ELEMENT,
                },
            )
        )

        if data.previous_update is None:
            block = [
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text="Updated to",
                    text_type=JSON_TYPE.NORMAL,
                ),
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text=data.new_update,
                    text_type=JSON_TYPE.ITALICS if data.is_milestone_changed else JSON_TYPE.NORMAL,
                ),
            ]
            blocks.extend(block)

        else:
            block = [
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text="Updated from",
                    text_type=JSON_TYPE.NORMAL,
                ),
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text=data.previous_update,
                    text_type=JSON_TYPE.ITALICS if data.is_milestone_changed else JSON_TYPE.NORMAL,
                ),
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text="to",
                    text_type=JSON_TYPE.NORMAL,
                ),
                WorkProgressBlockBaseModel(
                    type=JSON_TYPE.TEXT,
                    text=data.new_update,
                    text_type=JSON_TYPE.ITALICS if data.is_milestone_changed else JSON_TYPE.NORMAL,
                ),
            ]
            blocks.extend(block)

        return blocks
