from common import decorators
from common.exceptions import BaseValidationError
from common.timeline.service import TimelineStatusService
from core.entities import OrgUserEntity, ProjectUserEntity
from core.exceptions import ResourceDoesNotExistException
from inventory.domain.services import InventoryWorkProgressService
from order.domain.services.work_progress import OrderWorkProgressService
from project.interface.external.work_progress import WorkProgressToProjectService
from project_schedule.interface.external.work_progress import WorkProgressToProjectScheduleService
from report.download.domain.service import DownloadService
from work_progress_v2.data.factories import (
    WorkProgressOrgRepoFactory,
    WorkProgressRepoFactory,
    WorkProgressReportPDFRepoFactory,
    WorkProgressReportRepoFactory,
)
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.services.bulk_update import WorkProgressBulkUpdateInteractor, WorkProgressBulkUpdateService
from work_progress_v2.domain.services.config import WorkProgressOrgConfigService, WorkProgressProjectConfigService
from work_progress_v2.domain.services.day_wise import WorkProgressDayWiseService
from work_progress_v2.domain.services.element import (
    WorkProgressElementInteractor,
    WorkProgressElementService,
    WorkProgressVendorScopeElementService,
)
from work_progress_v2.domain.services.interactor import WorkProgressScopeDataInteractor, WorkProgressScopeDataService
from work_progress_v2.domain.services.locking import WorkProgressLockingService
from work_progress_v2.domain.services.pdf_export import WorkProgressReportPDFInteractor, WorkProgressReportPDFService
from work_progress_v2.domain.services.permission import WorkProgressPermissionService
from work_progress_v2.domain.services.report import WorkProgressReportInteractor, WorkProgressReportService
from work_progress_v2.domain.services.scope_data import WorkProgressVendorScopeDataService
from work_progress_v2.domain.services.scope_timeline import WorkProgressScopeTimelineInteractor
from work_progress_v2.domain.services.status import WorkProgressStatusService
from work_progress_v2.domain.services.update import WorkProgressUpdateInteractor, WorkProgressUpdateService


class WorkProgressServiceFactory:
    class Exception(BaseValidationError):
        pass

    class ElementNotFoundException(Exception):
        pass

    def __init__(
        self,
        user_entity: ProjectUserEntity,
        version: int | None = None,
    ):
        self.user_entity = user_entity
        self.version = version
        self.repo_factory = self.get_repo_factory()
        self.repo = self.get_repo()

    @decorators.service_lazy()
    def get_repo_factory(self):
        return WorkProgressRepoFactory(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_repo(self):
        return self.repo_factory.get_repo()

    @decorators.service_lazy()
    def get_scope_data_service(self):
        return WorkProgressScopeDataService(
            repo=self.repo,
            user_entity=self.user_entity,
        )

    @decorators.service_lazy()
    def get_scope_data_interactor(self):
        return WorkProgressScopeDataInteractor(
            service=self.get_scope_data_service(),
            cache=self.get_cache(),
        )

    @decorators.service_lazy()
    def get_status_service(self):
        return WorkProgressStatusService(
            repo=self.repo,
            user_entity=self.user_entity,
            project_service=self.get_project_service(),
        )

    @decorators.service_lazy()
    def get_element_service(self):
        return WorkProgressElementService(
            repo=self.repo,
            user_entity=self.user_entity,
            locking_service=self.get_locking_service(),
            cache=self.get_cache(),
        )

    @decorators.service_lazy()
    def get_element_interactor(self):
        return WorkProgressElementInteractor(service=self.get_element_service(), cache=self.get_cache())

    @decorators.service_lazy()
    def get_locking_service(self):
        return WorkProgressLockingService()

    @decorators.service_lazy()
    def get_scope_timeline_service(self):
        return TimelineStatusService()

    @decorators.service_lazy()
    def get_scope_timeline_interactor(self):
        return WorkProgressScopeTimelineInteractor(
            repo=self.repo,
            service=self.get_scope_timeline_service(),
            status_service=self.get_status_service(),
            cache=self.get_cache(),
            scope_data_service=self.get_scope_data_service(),
        )

    @decorators.service_lazy()
    def get_update_service(self, element_id: int):
        try:
            return WorkProgressUpdateService(
                repo=self.repo,
                user_entity=self.user_entity,
                locking_service=self.get_locking_service(),
                status_service=self.get_status_service(),
                element_id=element_id,
                cache=self.get_cache(),
                permission_service=self.get_permission_service(),
            )
        except ResourceDoesNotExistException:
            raise self.ElementNotFoundException("Work progress element does not exist")

    @decorators.service_lazy()
    def get_update_interactor(self, element_id: int, is_vendor: bool):
        return WorkProgressUpdateInteractor(
            service=self.get_update_service(element_id=element_id),
            is_vendor=is_vendor,
            project_service=self.get_project_service(),
            scope_data_service=self.get_scope_data_service(),
            scope_timeline_interactor=self.get_scope_timeline_interactor(),
            day_wise_service=self.get_day_wise_service(),
        )

    @decorators.service_lazy()
    def get_bulk_update_service(self):
        return WorkProgressBulkUpdateService(
            repo=self.repo,
            user_entity=self.user_entity,
            status_service=self.get_status_service(),
            project_service=self.get_project_service(),
            cache=self.get_cache(),
            scope_data_service=self.get_scope_data_service(),
            scope_timeline_interactor=self.get_scope_timeline_interactor(),
            day_wise_service=self.get_day_wise_service(),
            locking_service=self.get_locking_service(),
        )

    @decorators.service_lazy()
    def get_bulk_update_interactor(self):
        return WorkProgressBulkUpdateInteractor(
            service=self.get_bulk_update_service(),
        )

    @decorators.service_lazy()
    def get_cache(self):
        return WorkProgressCache(
            project_id=self.user_entity.project_id,
            org_id=self.user_entity.org_id,
            user_id=self.user_entity.user_id,
            version=self.version,
        )

    @decorators.service_lazy()
    def get_project_service(self):
        return WorkProgressToProjectService(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_order_service(self):
        return OrderWorkProgressService(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_vendor_scope_element_service(self, vendor_ids: list[int]):
        return WorkProgressVendorScopeElementService(
            user_entity=self.user_entity,
            repo=self.repo_factory.get_vendor_scope_repo(vendor_ids=vendor_ids),
            vendor_ids=vendor_ids,
            locking_service=self.get_locking_service(),
            cache=self.get_cache(),
        )

    @decorators.service_lazy()
    def get_vendor_scope_element_interactor(self, vendor_ids: list[int]):
        return WorkProgressElementInteractor(
            service=self.get_vendor_scope_element_service(vendor_ids=vendor_ids),
            cache=self.get_cache(),
        )

    @decorators.service_lazy()
    def get_vendor_scope_data_service(self, vendor_ids: list[int]):
        return WorkProgressVendorScopeDataService(
            user_entity=self.user_entity,
            repo=self.repo_factory.get_vendor_scope_repo(vendor_ids=vendor_ids),
            vendor_ids=vendor_ids,
        )

    @decorators.service_lazy()
    def get_permission_service(self):
        return WorkProgressPermissionService(cache=self.get_cache())

    def get_day_wise_service(self):
        return WorkProgressDayWiseService(
            repo=self.repo_factory.get_day_wise_repo(),
        )


class WorkProgressReportServiceFactory:
    def __init__(self, user_entity: ProjectUserEntity):
        self.user_entity = user_entity
        self.repo_factory = self.get_repo_factory()
        self.repo = self.get_repo()

    @decorators.service_lazy()
    def get_repo_factory(self):
        return WorkProgressReportRepoFactory(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_repo(self):
        return self.repo_factory.get_repo()

    @decorators.service_lazy()
    def get_service(self):
        return WorkProgressReportService(
            repo=self.repo,
            user_entity=self.user_entity,
            config_service=self.get_config_service(),
            cache=self.get_cache(),
            permission_service=self.get_permission_service(),
        )

    @decorators.service_lazy()
    def get_interactor(self):
        return WorkProgressReportInteractor(
            service=self.get_service(),
            config_service=self.get_config_service(),
            user_entity=self.user_entity,
            permission_service=self.get_permission_service(),
        )

    @decorators.service_lazy()
    def get_inventory_service(self):
        return InventoryWorkProgressService(repo=self.repo_factory.get_inventory_repo())

    @decorators.service_lazy()
    def get_schedule_service(self):
        return WorkProgressToProjectScheduleService(
            user_entity=self.user_entity,
            repo=self.repo_factory.get_schedule_repo(),
        )

    @decorators.service_lazy()
    def get_config_service(self):
        return WorkProgressProjectConfigService(
            user_entity=self.user_entity,
            repo=self.repo,
            permission_service=self.get_permission_service(),
        )

    @decorators.service_lazy()
    def get_cache(self):
        return WorkProgressCache(
            project_id=self.user_entity.project_id,
            org_id=self.user_entity.org_id,
            user_id=self.user_entity.user_id,
        )

    @decorators.service_lazy()
    def get_permission_service(self):
        return WorkProgressPermissionService(cache=self.get_cache())


class WorkProgressOrgServiceFactory:
    def __init__(
        self,
        user_entity: OrgUserEntity,
    ):
        self.user_entity = user_entity
        self.repo = self.get_repo()

    @decorators.service_lazy()
    def get_repo(self):
        return WorkProgressOrgRepoFactory(user_entity=self.user_entity).get_repo()

    @decorators.service_lazy()
    def get_config_service(self):
        return WorkProgressOrgConfigService(
            user_entity=self.user_entity, repo=self.repo, permission_service=self.get_permission_service()
        )

    @decorators.service_lazy()
    def get_permission_service(self):
        return WorkProgressPermissionService(cache=self.get_cache())

    @decorators.service_lazy()
    def get_cache(self):
        return WorkProgressCache(
            # TODO: org level service does not need project_id, but we need to pass it for cache
            project_id=-1,
            org_id=self.user_entity.org_id,
            user_id=self.user_entity.user_id,
        )


class WorkProgressReportPDFServiceFactory:
    def __init__(self, user_entity: ProjectUserEntity):
        self.user_entity = user_entity
        self.repo_factory = self.get_repo_factory()
        self.repo = self.get_repo()

    @decorators.service_lazy()
    def get_repo_factory(self):
        return WorkProgressReportPDFRepoFactory(user_entity=self.user_entity)

    @decorators.service_lazy()
    def get_repo(self):
        return self.repo_factory.get_repo()

    @decorators.service_lazy()
    def get_service(self):
        return WorkProgressReportPDFService(
            repo=self.get_repo(),
            user_entity=self.user_entity,
            inventory_service=self.get_inventory_service(),
            schedule_service=self.get_schedule_service(),
            config_service=self.get_config_service(),
        )

    @decorators.service_lazy()
    def get_interactor(self):
        return WorkProgressReportPDFInteractor(
            service=self.get_service(),
            download_service=self.get_download_service(),
        )

    @decorators.service_lazy()
    def get_inventory_service(self):
        return InventoryWorkProgressService(repo=self.repo_factory.get_inventory_repo())

    @decorators.service_lazy()
    def get_schedule_service(self):
        return WorkProgressToProjectScheduleService(
            user_entity=self.user_entity,
            repo=self.repo_factory.get_schedule_repo(),
        )

    @decorators.service_lazy()
    def get_config_service(self):
        return WorkProgressProjectConfigService(
            user_entity=self.user_entity,
            repo=self.repo_factory.get_report_repo(),
            permission_service=self.get_permission_service(),
        )

    @decorators.service_lazy()
    def get_download_service(self):
        return DownloadService(repo=self.repo_factory.get_download_repo())

    @decorators.service_lazy()
    def get_permission_service(self):
        return WorkProgressPermissionService(cache=self.get_cache())

    @decorators.service_lazy()
    def get_cache(self):
        return WorkProgressCache(
            project_id=self.user_entity.project_id,
            org_id=self.user_entity.org_id,
            user_id=self.user_entity.user_id,
        )
