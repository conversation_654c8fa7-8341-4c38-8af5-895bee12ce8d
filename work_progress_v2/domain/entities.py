import datetime
import decimal
from typing import Op<PERSON>, TypedDict
from uuid import UUID

from pydantic import model_validator

from authorization.domain.constants import Permissions
from authorization.enums import PermissionLevelEnum
from boq.data.choices import BoqElementStatus
from common.pydantic.base_model import (
    BaseModelV2,
    PydanticFilterBaseModel,
    PydanticInputBaseModel,
    PydanticPaginatedBaseModel,
)
from common.pydantic.custom_fields import HashIdInt
from common.timeline.entities import TimelineStatusMessageEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.data.choices import WorkProgressElementActionChoices
from work_progress_v2.data.entities import (
    ElementListDataEntity,
    ItemTypeConfigWithMilestonesDataEntity,
)
from work_progress_v2.domain.enums import (
    WorkProgressConfigReportTypeEnum,
    WorkProgressElementAttachmentPermissionActionEnum,
    WorkProgressElementListOrderingEnum,
    WorkProgressElementListProgressStatusE<PERSON>,
    WorkProgressReportActionEnum,
    WorkProgressReportConfigIdEnum,
)
from work_progress_v2.domain.helper_entities import SectionTotalProgressEntity


class TodayAndTotalItemCountEntity(BaseModelV2):
    today: int
    total: int


class TodayAndTotalProgressEntity(BaseModelV2):
    total_amount: decimal.Decimal
    today_progress: decimal.Decimal
    total_progress: decimal.Decimal
    today_progress_amount: decimal.Decimal
    total_progress_amount: decimal.Decimal


class ScopeProgressEntity(BaseModelV2):
    total_item_count: int
    today_updated_item_count: int
    total_amount: decimal.Decimal
    today_progress: decimal.Decimal
    total_progress: decimal.Decimal
    today_progress_amount: decimal.Decimal
    total_progress_amount: decimal.Decimal


class ScopeStatusEntity(BaseModelV2):
    status: str
    color_code: str
    progress_percentage: decimal.Decimal
    expected_start_date: str
    expected_due_date: str
    scope_start_date: str
    scope_completion_data: str


class ScopeCompletionPercentageEntity(BaseModelV2):
    percentage: decimal.Decimal
    color_code: str


class ScopeTimelineDataEntity(BaseModelV2):
    scope_status: TimelineStatusMessageEntity
    scope_completion: ScopeCompletionPercentageEntity
    expected_start_date: datetime.date | None
    expected_due_date: datetime.date | None
    scope_start_date: datetime.date | None
    scope_completion_date: datetime.date | None


class SectionNameTotalProgressEntity(SectionTotalProgressEntity):
    name: str


class LockingServiceElementEntity(BaseModelV2):
    uom_id: int
    item_type_id: int | None
    quantity: decimal.Decimal
    update_method: ItemTypeUpdateMethodChoices | None


class ProgressAndCompletionAmount(BaseModelV2):
    progress_percentage: decimal.Decimal
    amount: decimal.Decimal


class TodayProgressAndCompletionAmount(ProgressAndCompletionAmount):
    count: int


class UpdateViaQuantityEntity(BaseModelV2):
    quantity: decimal.Decimal
    progress_percentage: decimal.Decimal


class UpdateViaProgressEntity(BaseModelV2):
    progress_percentage: decimal.Decimal


class UpdateViaMilestoneEntity(BaseModelV2):
    milestone_id: int
    progress_percentage: decimal.Decimal


class UpdateElementUpdateMethod(BaseModelV2):
    id: int
    update_method: ItemTypeUpdateMethodChoices


class UpdateServiceBaseElementEntity(LockingServiceElementEntity):
    id: int
    element_status: BoqElementStatus
    progress_percentage: decimal.Decimal
    input_progress_percentage: decimal.Decimal | None
    input_progress_quantity: decimal.Decimal | None
    previous_day_input_progress_percentage: decimal.Decimal | None
    previous_day_input_progress_quantity: decimal.Decimal | None
    previous_day_input_progress_milestone_id: HashIdInt | None
    unlocked_at: datetime.datetime | None
    progress_updated_at: datetime.datetime | None
    last_day_progress_updated_at: datetime.datetime | None


class PreviousDayElementEntity(BaseModelV2):
    uom_id: int
    item_type_id: int
    update_method: ItemTypeUpdateMethodChoices
    quantity: decimal.Decimal
    progress_percentage: decimal.Decimal
    progress_quantity: decimal.Decimal


class ElementRemoveAttachmentEntity(BaseModelV2):
    id: int
    uploaded_at: datetime.datetime


class UpdateMethodElementEntity(BaseModelV2):
    id: int
    element_status: BoqElementStatus
    update_method: ItemTypeUpdateMethodChoices | None


class UpdateElementDataEntity(BaseModelV2):
    id: int
    progress_percentage: decimal.Decimal | None = None
    progress_quantity_input: decimal.Decimal | None = None
    progress_percentage_input: decimal.Decimal | None = None
    milestone_input_id: int | None = None
    update_method: ItemTypeUpdateMethodChoices | None = None
    is_unlocked: bool | None = None
    is_progress_manually_updated: bool = False
    boq_element_action_history_id: int | None = None
    action: WorkProgressElementActionChoices
    boq_element_updated_fields: list[str] | None = None
    is_progress_quantity_input_updated_by_boq_update: bool | None = None
    current_visible_updated_method: ItemTypeUpdateMethodChoices | None = None


class UpdateElementOutputDataEntity(BaseModelV2):
    updated_element_entity: ElementListDataEntity
    updated_field: list[str]
    completion_amount_diff: decimal.Decimal
    is_updated_today: bool = False
    section_id: HashIdInt
    progress_percentage_diff: decimal.Decimal
    timeline_id: int | None


class UpdateElementFieldDataEntity(TypedDict):
    progress_percentage: decimal.Decimal | None
    progress_quantity_input: decimal.Decimal | None
    progress_percentage_input: decimal.Decimal | None
    update_method: ItemTypeUpdateMethodChoices | None
    unlocked_at: datetime.datetime | None


class UpdateElementUpdateMethodErrorEntity(BaseModelV2):
    element_id: HashIdInt
    message: str


class ElementCategoryEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str


class ElementStatusEntity(BaseModelV2):
    name: str
    value: str


class ElementUomEntity(BaseModelV2):
    name: str
    value: int


class ElementItemTypeEntity(BaseModelV2):
    id: HashIdInt
    name: str
    color_code: str | None


class ElementSectionEntity(BaseModelV2):
    id: Optional[HashIdInt] = None
    name: str


class ElementPreviewFileEntity(BaseModelV2):
    id: HashIdInt
    file: str
    name: str
    is_main: bool
    type: str


class ElementListEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str
    description: str
    quantity: decimal.Decimal
    item_type: ElementItemTypeEntity | None = None
    status: ElementStatusEntity
    uom: ElementUomEntity
    category: ElementCategoryEntity | None = None
    section: ElementSectionEntity
    rate: decimal.Decimal
    actual_progress_percentage: decimal.Decimal
    current_input_quantity: decimal.Decimal | None = None
    current_input_progress: decimal.Decimal | None = None
    current_input_milestone_id: HashIdInt | None = None
    previous_day_input_quantity: decimal.Decimal | None = None
    previous_day_input_progress: decimal.Decimal | None = None
    previous_day_input_milestone_id: HashIdInt | None = None
    update_method: str | None = None
    is_locked: bool = False
    can_change_update_method: bool = False
    comment_count: int = 0
    preview_file: ElementPreviewFileEntity | None = None
    progress_updated_at: Optional[datetime.datetime] = None
    current_input_milestone_name: (str | None)  # If current_input_milestone_id is None, then "Not Started"


class PaginatedElementListEntity(PydanticPaginatedBaseModel):
    data: list[ElementListEntity]


class SectionListEntity(BaseModelV2):
    id: HashIdInt
    name: str
    element_count: int
    progress_percentage: decimal.Decimal
    total_amount: decimal.Decimal


class DefaultItemTypeConfigEntity(BaseModelV2):
    update_methods: list[ItemTypeUpdateMethodChoices]
    default_update_method: ItemTypeUpdateMethodChoices


class ItemTypeConfigEntity(BaseModelV2):
    config: list[ItemTypeConfigWithMilestonesDataEntity]
    default_config: DefaultItemTypeConfigEntity


class UserEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: str | None


class HistoryHeaderEntity(BaseModelV2):
    text: str
    color_code: str


class ElementHistoryTimelineEntity(BaseModelV2):
    id: HashIdInt
    header: HistoryHeaderEntity
    title: str
    updated_at: datetime.datetime
    updated_by: UserEntity


class BulkUpdateElementUpdateMethodDataEntity(BaseModelV2):
    element_list: list[ElementListEntity]
    scope_data: ScopeProgressEntity | None = None
    section_data: list[SectionListEntity] | None = None
    scope_timeline_data: ScopeTimelineDataEntity | None = None


class BulkUpdateElementUpdateMethodReturnEntity(BaseModelV2):
    error_list: list[UpdateElementUpdateMethodErrorEntity]
    version: int | None = None
    data: BulkUpdateElementUpdateMethodDataEntity | None = None


class CreateSingleScopeUpdateBlock(BaseModelV2):
    value_from: str
    value_to: str
    type: str


class CreateScopeUpdateBlock(BaseModelV2):
    id: int
    name: str
    updates: list[CreateSingleScopeUpdateBlock]


class ReportListFilterDataEntity(PydanticFilterBaseModel):
    created_by_ids: list[HashIdInt] | None = None
    start_date: datetime.date | None = None
    end_date: datetime.date | None = None


class VendorReportListFilterDataEntity(ReportListFilterDataEntity):
    # vendor_id is from query params and the transformed value is stored in vendor_org_ids
    vendor_id: HashIdInt | None = None
    vendor_org_ids: list[HashIdInt] | None = None


class VendorReportCreatedByListFilterDataEntity(PydanticFilterBaseModel):
    # vendor_id is from query params and the transformed value is stored in vendor_org_ids
    vendor_id: HashIdInt | None = None
    vendor_org_ids: list[HashIdInt] | None = None


class GenerateReportConfigEntity(BaseModelV2):
    checked_ids: list[str]
    actions: list[WorkProgressReportActionEnum]


class ElementListFilterEntity(PydanticFilterBaseModel):
    category_ids: list[HashIdInt] | None = None
    section_ids: list[HashIdInt] | None = None
    item_type_ids: list[HashIdInt] | None = None
    search_text: str | None = None
    search_quantity: str | None = None
    uom_ids: list[int] | None = None
    element_status: list[str] | None = None
    progress_status: list[WorkProgressElementListProgressStatusEnum] | None = None
    ordering: list[WorkProgressElementListOrderingEnum] | None = None

    # app specific filters
    is_updated_recently: bool | None = None
    is_updated_earliest: bool | None = None
    is_not_updated_today: bool | None = None
    is_updated_in_last_7_days: bool | None = None
    is_not_updated_in_last_7_days: bool | None = None
    is_not_updated_yet: bool | None = None
    is_updated_today: bool | None = None


class VendorScopeElementListFilterEntity(ElementListFilterEntity):
    vendor_id: HashIdInt | None = None
    order_rate: int | None = None
    amount: int | None = None


class VendorScopeHeaderDetailFilterEntity(PydanticFilterBaseModel):
    vendor_id: HashIdInt | None = None


class WorkProgressCacheKeyEntity(BaseModelV2):
    org_id: int
    project_id: int


class WorkProgressScopeDataCacheEntity(BaseModelV2):
    version: int
    scope_data: ScopeProgressEntity


class WorkProgressSectionDataCacheEntity(BaseModelV2):
    version: int
    sections: list[SectionNameTotalProgressEntity]


class UpdateProgressElementDataEntity(BaseModelV2):
    element: ElementListEntity
    scope_data: ScopeProgressEntity
    section_data: list[SectionListEntity]
    scope_timeline_data: ScopeTimelineDataEntity


class UpdateProgressElementEntity(BaseModelV2):
    version: int
    data: UpdateProgressElementDataEntity


class SectionScopeDataEntity(BaseModelV2):
    id: HashIdInt
    name: str


class BlockMetaDataEntity(BaseModelV2):
    boq_element_id_list: list[HashIdInt]
    category_id_list: list[HashIdInt]
    library_element_id: list[HashIdInt]
    activity_id_list: list[HashIdInt]


class ExportReportInputEntity(PydanticInputBaseModel):
    start_date: datetime.date | None = None
    end_date: datetime.date | None = None
    is_latest: bool | None = None
    is_complete: bool | None = None
    is_last_7_days: bool | None = None
    is_vendor_report: bool = False
    vendor_id: HashIdInt | None = None
    uuid: UUID | None = None

    @model_validator(mode="after")
    def validate_model(self):
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValueError("start_date cannot be greater than end_date")

        return self


class ReportConfigEntity(BaseModelV2):
    id: WorkProgressReportConfigIdEnum
    name: str
    mandatory: bool = False
    checked: bool
    children: "list[ReportConfigEntity]"


class ReportConfigInputEntity(BaseModelV2):
    id: WorkProgressReportConfigIdEnum
    name: str
    checked: bool
    children: "list[ReportConfigInputEntity]"


class ReportConfigIdPermissionEntity(BaseModelV2):
    permission: Permissions
    permission_type: PermissionLevelEnum


class GetReportConfigEntity(BaseModelV2):
    config: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]]


class GetProjectReportConfigEntity(BaseModelV2):
    config: list[ReportConfigEntity]


class UpdatedSectionDataEntity(BaseModelV2):
    id: int
    progress_percentage_diff: decimal.Decimal
    completion_amount_diff: decimal.Decimal


class UpdatedScopeDatesEntity(BaseModelV2):
    projected_start_date: datetime.date
    projected_end_date: datetime.date


class VendorScopeElementOrganizationEntity(BaseModelV2):
    id: HashIdInt
    name: str


class VendorScopeElementListEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str
    description: str
    quantity: decimal.Decimal
    item_type: ElementItemTypeEntity | None = None
    status: ElementStatusEntity
    uom: ElementUomEntity
    category: ElementCategoryEntity | None = None
    order_rate: decimal.Decimal
    progress_percentage: decimal.Decimal
    preview_file: ElementPreviewFileEntity | None = None
    organization: VendorScopeElementOrganizationEntity
    amount: decimal.Decimal


class PaginatedVendorScopeElementListEntity(PydanticPaginatedBaseModel):
    data: list[VendorScopeElementListEntity]


class ElementAttachmentDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    file: str
    uploaded_at: datetime.datetime


class VendorScopeElementDetailEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str
    description: str
    quantity: decimal.Decimal
    item_type: ElementItemTypeEntity | None = None
    status: ElementStatusEntity
    uom: ElementUomEntity
    category: ElementCategoryEntity | None = None
    rate: decimal.Decimal
    current_quantity: decimal.Decimal | None = None
    current_progress: decimal.Decimal | None = None
    current_milestone_id: HashIdInt | None = None
    actual_progress_percentage: decimal.Decimal
    update_method: str | None = None
    preview_files: list[ElementPreviewFileEntity] = []
    attachments: list[ElementAttachmentDataEntity] = []


class ElementAttachmentEntity(BaseModelV2):
    id: HashIdInt
    name: str
    file: str
    uploaded_at: datetime.datetime
    actions: list[WorkProgressElementAttachmentPermissionActionEnum]


class ElementDetailEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str
    description: str
    quantity: decimal.Decimal
    item_type: ElementItemTypeEntity | None = None
    status: ElementStatusEntity
    uom: ElementUomEntity
    category: ElementCategoryEntity | None = None
    section: ElementSectionEntity
    rate: decimal.Decimal
    actual_progress_percentage: decimal.Decimal
    current_input_quantity: decimal.Decimal | None = None
    current_input_progress: decimal.Decimal | None = None
    current_input_milestone_id: HashIdInt | None = None
    previous_day_input_quantity: decimal.Decimal | None = None
    previous_day_input_progress: decimal.Decimal | None = None
    previous_day_input_milestone_id: HashIdInt | None = None
    update_method: str | None = None
    is_locked: bool = False
    can_change_update_method: bool = False
    comment_count: int = 0
    progress_updated_at: Optional[datetime.datetime] = None
    preview_files: list[ElementPreviewFileEntity] = []
    attachments: list[ElementAttachmentEntity] = []


class UpdateElementOutputEntity(BaseModelV2):
    updated_element_entity: ElementListEntity
    updated_field: list[str]
    completion_amount_diff: decimal.Decimal
    is_updated_today: bool = False
    section_id: HashIdInt
    progress_percentage_diff: decimal.Decimal
    timeline_id: int


class BulkUpdateTimelineActionHistoryEntity(BaseModelV2):
    id: HashIdInt
    boq_element_id: HashIdInt


class BulkUpdateTimelineWorkProgressElementEntity(BaseModelV2):
    update_method: ItemTypeUpdateMethodChoices | None
    progress_percentage: decimal.Decimal
    progress_quantity_input: decimal.Decimal | None


class BulkUpdateTimelineElementEntity(BaseModelV2):
    id: HashIdInt
    quantity: decimal.Decimal
    item_type_id: HashIdInt | None
    work_progress_element: BulkUpdateTimelineWorkProgressElementEntity

    def __hash__(self) -> HashIdInt:
        return hash(self.id)

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, BulkUpdateTimelineElementEntity):
            return False
        return self.id == other.id


class ExportReportPdfOutputEntity(BaseModelV2):
    download_id: HashIdInt
