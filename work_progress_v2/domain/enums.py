from common.constants import BaseEnum
from common.timeline.enum import TimelineStatusEnum


class WorkProgressElementActionEnum(BaseEnum):
    INPUT_QUANTITY_UPDATED = "input_quantity_updated"
    INPUT_PERCENTAGE_UPDATED = "input_percentage_updated"
    INPUT_MILESTONE_UPDATED = "input_milestone_updated"
    ATTACHMENT_UPDATED = "attachment_updated"
    UPDATE_METHOD_UPDATED = "update_method_updated"
    BOQ_ELEMENT_UPDATED = "boq_element_updated"


class WorkProgressElementAttachmentActionEnum(BaseEnum):
    UPLOADED = "attachment_uploaded"
    DELETED = "attachment_deleted"


class WorkProgressCreationTypeEnum(BaseEnum):
    MANUAL = "manual"
    DERIVED = "derived"


class WorkProgressDailyLogTypeEnum(BaseEnum):
    TODAY = "today"
    BLOCKER = "blocker"
    TOMORROW = "tomorrow_plan"


class WorkProgressElementListProgressStatusEnum(BaseEnum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"


class WorkProgressElementListOrderingEnum(BaseEnum):
    PROGRESS_ASC = "progress_percentage"
    PROGRESS_DESC = "-progress_percentage"
    QUANTITY_ASC = "quantity"
    QUANTITY_DESC = "-quantity"
    RATE_ASC = "rate"
    RATE_DESC = "-rate"
    AMOUNT_ASC = "amount"
    AMOUNT_DESC = "-amount"
    CREATED_AT_ASC = "created_at"
    CREATED_AT_DESC = "-created_at"


class ElementUpdatedAttributeEnum(BaseEnum):
    QUANTITY = "quantity"
    ITEM_TYPE = "item_type"
    UOM = "uom"
    METHOD_OF_UPDATE = "method_of_update"


class WorkProgressConfigReportTypeEnum(BaseEnum):
    GENERATE_REPORT = "generate_report"
    EXPORT_SINGLE_DAY_REPORT = "export_single_day_report"
    EXPORT_MULTI_DAY_REPORT = "export_multi_day_report"


class WorkProgressReportConfigIdEnum(BaseEnum):
    SCOPE_UPDATE = "scope_update"
    SCOPE_SUMMARY = "scope_summary"
    SECTION_WISE_UPDATE = "section_wise_update"
    SECTION_WISE_IMAGES = "section_wise_images"
    SECTION_WISE_ALL_SECTIONS = "section_wise_all_sections"
    ITEM_WISE_UPDATE = "item_wise_update"

    SCHEDULE_UPDATE = "schedule_update"
    SCHEDULE_SUMMARY = "schedule_summary"
    ACTIVITY_UPDATE = "activity_update"
    ACTIVITY_UPDATE_WITH_STATUS = "activity_update_with_status"

    DAILY_LOG = "daily_log"
    TODAY_UPDATE = "today_update"
    BLOCKER = "blocker"
    TOMORROW_PLAN = "tomorrow_plan"

    SITE_VIEW_POINT = "site_view_point"

    MATERIAL_UPDATE = "material_update"
    MATERIAL_SUMMARY = "material_summary"

    MANPOWER = "manpower"
    MANPOWER_SUMMARY = "manpower_summary"

    REPORTED_PROJECT_PROGRESS = "reported_project_progress"
    REPORTED_PROGRESS = "reported_progress"
    PROJECTED_END_DATE = "projected_end_date"

    CUMULATIVE_REPORT = "cumulative_report"
    DAY_WISE_REPORT = "day_wise_report"


class WorkProgressInventoryStockItemTypeEnum(BaseEnum):
    CONSUMED = "consumed"
    TRANSFERRED_OUT = "transferred_out"
    RECEIVED = "received"


class WorkProgressScheduleActivityStatusEnum(BaseEnum):
    ON_TIME = TimelineStatusEnum.ON_TIME.value
    DELAYED = TimelineStatusEnum.DELAYED.value
    OVERDUE = TimelineStatusEnum.OVERDUE.value
    NOT_SET = TimelineStatusEnum.NOT_SET.value


class WorkProgressReportActionEnum(BaseEnum):
    CAN_DELETE_ATTACHMENT = "can_delete_attachment"
    CAN_UPLOAD_FROM_GALLERY_ATTACHMENT = "can_upload_from_gallery_attachment"


class WorkProgressElementAttachmentPermissionActionEnum(BaseEnum):
    CAN_DELETE = "can_delete"


class WorkProgressReportDetailActionEnum(BaseEnum):
    CAN_DELETE = "can_delete"
