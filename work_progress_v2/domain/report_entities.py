import datetime
import decimal
from typing import Annotated, Optional

from pydantic import Field

from common.pydantic.base_model import (
    BaseModelV2,
    PydanticInputBaseModel,
    WorkProgressBlockBaseModel,
)
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt
from common.timeline.enum import TimelineStatusEnum
from report.download.domain.enums import DownloadProgressStatusEnum
from work_progress_v2.domain.entities import SectionTotalProgressEntity
from work_progress_v2.domain.enums import (
    WorkProgressCreationTypeEnum,
    WorkProgressDailyLogTypeEnum,
    WorkProgressInventoryStockItemTypeEnum,
    WorkProgressReportConfigIdEnum,
    WorkProgressReportDetailActionEnum,
    WorkProgressScheduleActivityStatusEnum,
)
from work_progress_v2.domain.helper_entities import TodayProgressDataEntity, TotalProgressDataEntity


class BaseAttachmentEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None
    uploaded_at: datetime.datetime


class HashtagElementEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: CustomFileUrlStr | None = None


class HashtagListDomainEntity(BaseModelV2):
    elements: list[HashtagElementEntity]
    categories: list[HashtagElementEntity]
    activities: list[HashtagElementEntity]


class SiteViewPointAttachmentEntity(BaseAttachmentEntity):
    pass


class PrefillSiteViewPointEntity(BaseModelV2):
    id: HashIdInt
    name: str
    previous_attachments: list[SiteViewPointAttachmentEntity]
    current_attachments: list[SiteViewPointAttachmentEntity]


class SiteViewPointMappingEntity(BaseModelV2):
    current_attachments: list[SiteViewPointAttachmentEntity]
    previous_attachments: list[SiteViewPointAttachmentEntity]


class PrefillReportedProjectProgressEntity(BaseModelV2):
    reported_progress_percentage: decimal.Decimal | None = None
    projected_end_date: datetime.date | None = None


class ScopeUpdateSectionAttachmentEntity(BaseAttachmentEntity):
    element_name: str


class PrefillScopeUpdateSectionEntity(BaseModelV2):
    id: HashIdInt
    name: str
    progress_percentage: decimal.Decimal
    attachments: list[ScopeUpdateSectionAttachmentEntity]
    updates: list[list[WorkProgressBlockBaseModel]]


class PrefillScopeSummaryEntity(BaseModelV2):
    section_total_progress: list[SectionTotalProgressEntity]
    today_progress_data: TodayProgressDataEntity
    total_progress_data: TotalProgressDataEntity


class PrefillScopeUpdateEntity(BaseModelV2):
    total_element_count: int
    total_progress: decimal.Decimal
    today_progress: decimal.Decimal
    section_updates: list[PrefillScopeUpdateSectionEntity] | None = None


class PrefillDailyLogUpdateAttachmentEntity(BaseAttachmentEntity):
    pass


class PrefillDailyLogUpdateEntity(BaseModelV2):
    updates: list
    attachments: list[PrefillDailyLogUpdateAttachmentEntity]


class PrefillDailyLogEntity(BaseModelV2):
    today_update: PrefillDailyLogUpdateEntity
    blocker: PrefillDailyLogUpdateEntity
    tomorrow_plan: PrefillDailyLogUpdateEntity


class PrefillMaterialUpdateElementEntity(BaseModelV2):
    id: HashIdInt
    name: str
    quantity: decimal.Decimal
    uom_name: str
    stock_value: decimal.Decimal


class PrefillMaterialUpdateEntity(BaseModelV2):
    received: list[PrefillMaterialUpdateElementEntity] = []
    consumed: list[PrefillMaterialUpdateElementEntity] = []
    transferred_out: list[PrefillMaterialUpdateElementEntity] = []


class PrefillScheduleUpdateActivityStatusEntity(BaseModelV2):
    name: str
    color_code: str
    status: Annotated[WorkProgressScheduleActivityStatusEnum, Field(exclude=True)]
    days: Annotated[Optional[int], Field(exclude=True)]


class PrefillScheduleUpdateActivityEntity(BaseModelV2):
    id: HashIdInt
    wbs: str
    name: str
    status: PrefillScheduleUpdateActivityStatusEntity | None = None
    day_progress: decimal.Decimal
    total_progress: decimal.Decimal


class PrefillScheduleUpdateAttachmentActivityEntity(BaseModelV2):
    id: HashIdInt
    name: str


class PrefillScheduleUpdateAttachmentEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None = None
    activity: PrefillScheduleUpdateAttachmentActivityEntity


class PrefillScheduleUpdateEntity(BaseModelV2):
    id: HashIdInt
    total_progress: decimal.Decimal | None = None
    today_progress: decimal.Decimal | None = None
    status: PrefillScheduleUpdateActivityStatusEntity | None = None
    planned_end_date: datetime.date | None = None
    activities: list[PrefillScheduleUpdateActivityEntity] | None = None
    attachments: list[PrefillScheduleUpdateAttachmentEntity] | None = None


class MaterialItemEntity(BaseModelV2):
    id: HashIdInt
    name: str
    quantity: decimal.Decimal
    uom_name: str
    type: WorkProgressInventoryStockItemTypeEnum
    stock_value: decimal.Decimal


class CreateReportDailyLogUpdateAttachmentInputEntity(PydanticInputBaseModel):
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None = None


class CreateReportDailyLogUpdateInputEntity(PydanticInputBaseModel):
    updates: list[list[WorkProgressBlockBaseModel]] = []
    attachments: list[CreateReportDailyLogUpdateAttachmentInputEntity] = []


class CreateReportDailyLogInputEntity(PydanticInputBaseModel):
    today_update: CreateReportDailyLogUpdateInputEntity | None
    blocker: CreateReportDailyLogUpdateInputEntity | None
    tomorrow_plan: CreateReportDailyLogUpdateInputEntity | None


class CreateReportReportedProjectProgressInputEntity(PydanticInputBaseModel):
    reported_progress_percentage: decimal.Decimal | None = None
    projected_end_date: datetime.datetime | None = None


class CreateReportSiteViewPointAttachmentInputEntity(PydanticInputBaseModel):
    id: HashIdInt | None = None
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None = None


class CreateReportSiteViewPointInputEntity(PydanticInputBaseModel):
    id: HashIdInt | None = None
    name: str = Field(..., max_length=150, description="View point name must not exceed 150 characters")
    attachments: list[CreateReportSiteViewPointAttachmentInputEntity] = Field(
        default_factory=list,
        max_length=4,
        description="Maximum 4 attachments allowed per site view point",
    )


class CreateReportManPowerDataEntity(BaseModelV2):
    name: str = Field(max_length=100)
    value: int


class CreateReportManPowerInputEntity(PydanticInputBaseModel):
    name: str
    value: Annotated[int, Field(gt=0, lt=10001)]


class CreateReportScopeUpdateSectionAttachmentInputEntity(PydanticInputBaseModel):
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None = None
    element_name: str | None = None


class CreateReportScopeUpdateSectionInputEntity(PydanticInputBaseModel):
    id: HashIdInt
    name: str
    attachments: list[CreateReportScopeUpdateSectionAttachmentInputEntity]


class CreateReportScopeUpdateInputEntity(PydanticInputBaseModel):
    section_updates: list[CreateReportScopeUpdateSectionInputEntity] = []


class CreateReportScheduleUpdateAttachmentInputEntity(PydanticInputBaseModel):
    name: str
    file: str
    thumbnail_url: CustomFileUrlStr | None = None
    activity_id: Annotated[Optional[int], HashIdInt] | None = None


class CreateReportInputEntity(PydanticInputBaseModel):
    daily_log: CreateReportDailyLogInputEntity | None = None
    manpower_category_list: list[CreateReportManPowerInputEntity] = []
    reported_project_progress: CreateReportReportedProjectProgressInputEntity | None = None
    site_view_point_list: list[CreateReportSiteViewPointInputEntity] | None = []
    scope_update: CreateReportScopeUpdateInputEntity | None = None
    schedule_update: list[CreateReportScheduleUpdateAttachmentInputEntity] = []


class CreateSiteViewPointAttachmentEntity(BaseModelV2):
    mapping_id: HashIdInt
    url: str
    name: str
    is_previous: bool
    thumbnail_url: CustomFileUrlStr | None = None
    uploaded_at: datetime.datetime | None = None


class CreateReportItemEntity(BaseModelV2):
    blocks: list[WorkProgressBlockBaseModel]
    creation_type: WorkProgressCreationTypeEnum
    section_id: HashIdInt | None = None
    boq_section_id: HashIdInt | None = None
    daily_log_type: WorkProgressDailyLogTypeEnum = WorkProgressDailyLogTypeEnum.TODAY
    category_ids: list[HashIdInt] = []
    boq_element_ids: list[HashIdInt] = []
    activity_ids: list[HashIdInt] = []


class CreateReportSectionEntity(BaseModelV2):
    name: str
    progress_percentage: decimal.Decimal


class ReportSectionEntity(BaseModelV2):
    id: HashIdInt
    name: str
    progress_percentage: decimal.Decimal


class CreateScheduleActivityEntity(BaseModelV2):
    id: HashIdInt
    activity_id: HashIdInt


class CreateReportAttachmentEntity(BaseModelV2):
    url: CustomFileUrlStr
    name: str
    thumbnail_url: CustomFileUrlStr | None = None
    boq_section_id: HashIdInt | None = None
    section_id: HashIdInt | None = None
    element_id: HashIdInt | None = None
    element_name: str | None = None
    daily_log_type: WorkProgressDailyLogTypeEnum | None = None
    creation_type: WorkProgressCreationTypeEnum


class ReportBaseDetailEntity(BaseModelV2):
    id: HashIdInt
    created_at: datetime.datetime
    created_by_id: HashIdInt
    deleted_at: datetime.datetime | None = None


class AddDailyLogItemWithAttachmentEntity(BaseModelV2):
    attachments: list[CreateReportAttachmentEntity]
    updates: list[CreateReportItemEntity]


class SystemGeneratedUpdatesEntity(BaseModelV2):
    element_id: HashIdInt
    element_name: str
    previous_update: str | None
    new_update: str
    is_milestone_changed: bool = False


class ReportDetailReportedProjectProgressEntity(BaseModelV2):
    reported_progress_percentage: decimal.Decimal | None = None
    projected_end_date: datetime.date | None = None


class ReportDetailCreatedByEntity(BaseModelV2):
    id: HashIdInt | None
    name: str
    photo: CustomFileUrlStr | None


class ReportDetailAttachmentEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: CustomFileUrlStr
    thumbnail_url: CustomFileUrlStr | None = None
    uploaded_at: datetime.datetime
    element_name: str | None = None
    activity_name: str | None = None


class ReportDetailSiteViewPointEntity(BaseModelV2):
    id: HashIdInt
    name: str
    previous_attachments: list[ReportDetailAttachmentEntity]
    todays_attachments: list[ReportDetailAttachmentEntity]


class ReportDetailManpowerCategoryEntity(BaseModelV2):
    id: HashIdInt
    name: str
    value: int


class ReportDetailDailyLogUpdateEntity(BaseModelV2):
    updates: list[list[WorkProgressBlockBaseModel]] = []
    attachments: list[ReportDetailAttachmentEntity]


class ReportDetailDailyLogEntity(BaseModelV2):
    today_update: ReportDetailDailyLogUpdateEntity
    blocker: ReportDetailDailyLogUpdateEntity
    tomorrow_plan: ReportDetailDailyLogUpdateEntity


class ReportSectionUpdateWithElementEntity(BaseModelV2):
    element_id: HashIdInt
    blocks: list[WorkProgressBlockBaseModel]


class ReportDetailAttachmentWithElementEntity(BaseModelV2):
    element_id: HashIdInt
    attachments: list[ReportDetailAttachmentEntity]


class ReportDetailScopeUpdateSectionWithElementEntity(BaseModelV2):
    id: HashIdInt
    name: str
    progress_percentage: decimal.Decimal
    attachments: list[ReportDetailAttachmentWithElementEntity]
    updates: list[ReportSectionUpdateWithElementEntity]


class ReportDetailScopeUpdateSectionEntity(BaseModelV2):
    id: HashIdInt
    name: str
    progress_percentage: decimal.Decimal
    attachments: list[ReportDetailAttachmentEntity]
    updates: list[list[WorkProgressBlockBaseModel]]


class ReportDetailScopeUpdateSectionProgressEntity(BaseModelV2):
    id: HashIdInt
    name: str
    total_progress: decimal.Decimal
    today_progress: decimal.Decimal


class ReportDetailScopeUpdateEntity(BaseModelV2):
    total_element_count: int
    total_progress: decimal.Decimal
    today_progress: decimal.Decimal
    section_progress: list[ReportDetailScopeUpdateSectionProgressEntity] = []
    section_updates: list[ReportDetailScopeUpdateSectionEntity]


class ReportDetailScopeUpdateWithElementDataEntity(BaseModelV2):
    total_element_count: int
    total_progress: decimal.Decimal
    today_progress: decimal.Decimal
    section_progress: list[ReportDetailScopeUpdateSectionProgressEntity] = []
    section_updates: list[ReportDetailScopeUpdateSectionWithElementEntity]


class ReportDetailMaterialUpdateElementEntity(BaseModelV2):
    id: HashIdInt
    name: str
    quantity: decimal.Decimal
    uom_name: str


class ReportDetailMaterialUpdateEntity(BaseModelV2):
    total_stock_value: decimal.Decimal
    consumed: list[ReportDetailMaterialUpdateElementEntity] = []
    received: list[ReportDetailMaterialUpdateElementEntity] = []
    transferred_out: list[ReportDetailMaterialUpdateElementEntity] = []


class ReportDetailScheduleUpdateActivityStatusEntity(BaseModelV2):
    name: str
    color_code: str
    status: TimelineStatusEnum
    days: int | None


class ReportDetailScheduleUpdateActivityEntity(BaseModelV2):
    id: HashIdInt
    name: str
    wbs: str
    status: ReportDetailScheduleUpdateActivityStatusEntity | None
    day_progress: decimal.Decimal
    total_progress: decimal.Decimal


class ReportDetailScheduleUpdateEntity(BaseModelV2):
    id: HashIdInt
    total_progress: decimal.Decimal | None
    today_progress: decimal.Decimal | None
    status: ReportDetailScheduleUpdateActivityStatusEntity | None
    planned_end_date: datetime.date | None
    activities: list[ReportDetailScheduleUpdateActivityEntity] = []
    attachments: list[ReportDetailAttachmentEntity] = []


class ReportDetailPdfEntity(BaseModelV2):
    url: str | None = None
    status: DownloadProgressStatusEnum


class ReportDetailEntity(BaseModelV2):
    id: HashIdInt
    created_at: datetime.datetime
    created_by: ReportDetailCreatedByEntity
    reported_project_progress: ReportDetailReportedProjectProgressEntity
    site_view_point_list: list[ReportDetailSiteViewPointEntity] = []
    manpower_category_list: list[ReportDetailManpowerCategoryEntity] = []
    comment_count: int = 0
    total_manpower_count: int = 0
    daily_log: ReportDetailDailyLogEntity
    scope_update: ReportDetailScopeUpdateEntity
    checked_config_ids: list[WorkProgressReportConfigIdEnum] = []
    material_update: ReportDetailMaterialUpdateEntity | None
    schedule_update: ReportDetailScheduleUpdateEntity | None
    pdf: ReportDetailPdfEntity
    actions: list[WorkProgressReportDetailActionEnum] = []


"""
Report PDF Data Entities
"""


class ReportPDFBlockDataEntity(BaseModelV2):
    blocks: list[WorkProgressBlockBaseModel]


class ReportPDFAttachmentEntity(BaseModelV2):
    id: HashIdInt
    url: CustomFileUrlStr
    name: str
    thumbnail_url: CustomFileUrlStr | None = None
    uploaded_at: str | datetime.datetime | None
    element_name: str | None = None
    activity_name: str | None = None


# Scope Update Data Entities
class ExportReportPDFScopeUpdateSectionEntity(BaseModelV2):
    id: HashIdInt
    name: str
    updates: list[list[WorkProgressBlockBaseModel]]
    period_progress: decimal.Decimal
    total_progress: decimal.Decimal
    attachments: list[ReportDetailAttachmentEntity]


class ExportReportPDFScopeUpdateEntity(BaseModelV2):
    total_items: int
    period_progress: decimal.Decimal
    scope_progress: decimal.Decimal
    sections: list[ExportReportPDFScopeUpdateSectionEntity]


class ReportPDFCreatorEntity(BaseModelV2):
    name: str
    photo: CustomFileUrlStr | None


class ReportPDFProjectEntity(BaseModelV2):
    name: str
    job_id: str


class ReportGeneratePDFHeaderEntity(BaseModelV2):
    project: ReportPDFProjectEntity
    user: ReportPDFCreatorEntity
    generated_at: datetime.datetime
    org: ReportPDFCreatorEntity


class ReportPDFFooterEntity(BaseModelV2):
    generated_by: ReportPDFCreatorEntity
    generated_at: datetime.date


class ReportPDFThemeEntity(BaseModelV2):
    color: str
    font: str


class ReportPDFBaseEntity(BaseModelV2):
    footer: ReportPDFFooterEntity
    theme: ReportPDFThemeEntity | None = None


class ReportGeneratedPdfDataEntity(ReportPDFBaseEntity):
    header: ReportGeneratePDFHeaderEntity
    data: ReportDetailEntity


class ReportExportPDFHeaderEntity(BaseModelV2):
    project: ReportPDFProjectEntity
    user: ReportPDFCreatorEntity
    generated_at: datetime.datetime
    org: ReportPDFCreatorEntity
    start_date: datetime.date
    end_date: datetime.date


class ExportReportPDFStockEntity(BaseModelV2):
    name: str
    quantity: decimal.Decimal
    uom_name: str


class ExportReportPDFMaterialEntity(BaseModelV2):
    total_stock_value: decimal.Decimal
    received: list[ExportReportPDFStockEntity]
    consumed: list[ExportReportPDFStockEntity]
    transferred_out: list[ExportReportPDFStockEntity]


class ExportScheduleActivityEntity(BaseModelV2):
    wbs: str
    name: str
    status: ReportDetailScheduleUpdateActivityStatusEntity | None
    period_progress: decimal.Decimal
    total_progress: decimal.Decimal


class ExportReportPDFScheduleEntity(BaseModelV2):
    schedule_progress: decimal.Decimal
    period_progress: decimal.Decimal
    status: ReportDetailScheduleUpdateActivityStatusEntity | None
    planned_end_date: datetime.date | None
    activities: list[ExportScheduleActivityEntity] = []
    attachments: list[ReportDetailAttachmentEntity] = []


class ExportReportPDFSiteViewPointEntity(BaseModelV2):
    id: HashIdInt
    name: str
    attachments: list[ReportPDFAttachmentEntity]


class ExportReportPDFManpowerCategoryEntity(BaseModelV2):
    name: str
    value: int


class ExportReportPDFManpowerEntity(BaseModelV2):
    total_manpower: int
    category_list: list[ExportReportPDFManpowerCategoryEntity]


class ExportREportPDFManpowerDateWiseEntity(BaseModelV2):
    date: datetime.date
    manpower_data: ExportReportPDFManpowerEntity


class ExportReportCumulativeEntity(BaseModelV2):
    scope_update: ExportReportPDFScopeUpdateEntity | None
    manpower: list[ExportREportPDFManpowerDateWiseEntity]
    site_view_point_list: list[ExportReportPDFSiteViewPointEntity]
    material_update: ExportReportPDFMaterialEntity | None
    schedule_update: ExportReportPDFScheduleEntity | None
    total_manpower_count: int


class ReportDetailDayWiseEntity(BaseModelV2):
    created_at: datetime.datetime
    created_by: ReportDetailCreatedByEntity
    reported_project_progress: ReportDetailReportedProjectProgressEntity
    site_view_point_list: list[ReportDetailSiteViewPointEntity] = []
    manpower_category_list: list[ReportDetailManpowerCategoryEntity] = []
    total_manpower_count: int
    daily_log: ReportDetailDailyLogEntity | None
    scope_update: ReportDetailScopeUpdateEntity
    material_update: ReportDetailMaterialUpdateEntity | None
    schedule_update: ReportDetailScheduleUpdateEntity | None


class ReportDetailDayWiseDataWithScopeElementDataEntity(BaseModelV2):
    created_at: datetime.datetime
    created_by: ReportDetailCreatedByEntity
    reported_project_progress: ReportDetailReportedProjectProgressEntity
    site_view_point_list: list[ReportDetailSiteViewPointEntity] = []
    manpower_category_list: list[ReportDetailManpowerCategoryEntity] = []
    total_manpower_count: int
    daily_log: ReportDetailDailyLogEntity | None
    scope_update: ReportDetailScopeUpdateWithElementDataEntity
    material_update: ReportDetailMaterialUpdateEntity | None
    schedule_update: ReportDetailScheduleUpdateEntity | None


class ExportReportEntity(BaseModelV2):
    cumulative_data: ExportReportCumulativeEntity | None
    day_wise_data: list[ReportDetailDayWiseEntity] | None
    is_single_day_report: bool
    checked_config_ids: list[WorkProgressReportConfigIdEnum]
    cumulative_checked_config_ids: list[WorkProgressReportConfigIdEnum]
    is_vendor_report: bool


class ExportReportPdfEntity(ReportPDFBaseEntity):
    header: ReportExportPDFHeaderEntity
    data: ExportReportEntity


class ReportPdfEntity(BaseModelV2):
    url: CustomFileUrlStr | None = None
    status: DownloadProgressStatusEnum


class PreviousReportDataEntity(BaseModelV2):
    created_at: datetime.datetime
    created_by: ReportDetailCreatedByEntity
    creator_org: ReportDetailCreatedByEntity
    reported_project_progress: ReportDetailReportedProjectProgressEntity
