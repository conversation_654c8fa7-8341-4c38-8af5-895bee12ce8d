from authorization.domain.constants import Permissions
from authorization.enums import PermissionLevelEnum
from work_progress_v2.domain.entities import ReportConfigEntity, ReportConfigIdPermissionEntity
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum

GENERATE_REPORT_CONFIG: list[ReportConfigEntity] = [
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
        name="Scope Update",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                name="Scope Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                name="Item wise Update",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE,
        name="Schedule Update",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY,
                name="Schedule Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE,
                name="Activity Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS,
                        name="With Status",
                        mandatory=False,
                        checked=True,
                        children=[],
                    )
                ],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.DAILY_LOG,
        name="Daily Log",
        mandatory=True,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TODAY_UPDATE,
                name="Today's Update",
                mandatory=True,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.BLOCKER,
                name="Blocker",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TOMORROW_PLAN,
                name="Tomorrow's Plan",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT,
        name="Site View Points",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MATERIAL_UPDATE,
        name="Material Update",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MANPOWER,
        name="Manpower",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.REPORTED_PROJECT_PROGRESS,
        name="Reported Project Progress",
        mandatory=False,
        checked=False,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.REPORTED_PROGRESS,
                name="Reported Progress",
                mandatory=False,
                checked=False,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.PROJECTED_END_DATE,
                name="Projected End Date",
                mandatory=False,
                checked=False,
                children=[],
            ),
        ],
    ),
]

EXPORT_SINGLE_DAY_REPORT_CONFIG: list[ReportConfigEntity] = [
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
        name="Scope Update",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                name="Scope Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SECTION_WISE_UPDATE,
                name="Section wise Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SECTION_WISE_ALL_SECTIONS,
                        name="All Sections",
                        mandatory=False,
                        checked=False,
                        children=[],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                name="Item wise Update",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE,
        name="Schedule Update",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY,
                name="Schedule Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE,
                name="Activity Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS,
                        name="With Status",
                        mandatory=False,
                        checked=True,
                        children=[],
                    )
                ],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.DAILY_LOG,
        name="Daily Log",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TODAY_UPDATE,
                name="Today's Update",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.BLOCKER,
                name="Blocker",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TOMORROW_PLAN,
                name="Tomorrow's Plan",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT,
        name="Site View Points",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MATERIAL_UPDATE,
        name="Material Update",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MANPOWER,
        name="Manpower",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.REPORTED_PROJECT_PROGRESS,
        name="Reported Project Progress",
        mandatory=False,
        checked=False,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.REPORTED_PROGRESS,
                name="Reported Progress",
                mandatory=False,
                checked=False,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.PROJECTED_END_DATE,
                name="Projected End Date",
                mandatory=False,
                checked=False,
                children=[],
            ),
        ],
    ),
]

EXPORT_MULTI_DAY_REPORT_CONFIG: list[ReportConfigEntity] = [
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.CUMULATIVE_REPORT,
        name="Cumulative Report",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
                name="Scope Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                        name="Scope Summary",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SECTION_WISE_UPDATE,
                        name="Section wise Update",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SECTION_WISE_IMAGES,
                        name="Section wise Images",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                        name="Item wise Update",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE,
                name="Schedule Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY,
                        name="Schedule Summary",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE,
                        name="Activity Update",
                        mandatory=False,
                        checked=True,
                        children=[
                            ReportConfigEntity(
                                id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS,
                                name="With Status",
                                mandatory=False,
                                checked=True,
                                children=[],
                            )
                        ],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT,
                name="Site View Points",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.MATERIAL_SUMMARY,
                name="Material Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.MANPOWER_SUMMARY,
                name="Manpower Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.DAY_WISE_REPORT,
        name="Day wise Report",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
                name="Scope Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                        name="Scope Summary",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                        name="Item wise Update",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE,
                name="Schedule Update",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY,
                        name="Schedule Summary",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE,
                        name="Activity Update",
                        mandatory=False,
                        checked=True,
                        children=[
                            ReportConfigEntity(
                                id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS,
                                name="With Status",
                                mandatory=False,
                                checked=True,
                                children=[],
                            )
                        ],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.DAILY_LOG,
                name="Daily Log",
                mandatory=False,
                checked=True,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.TODAY_UPDATE,
                        name="Today's Update",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.BLOCKER,
                        name="Blocker",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.TOMORROW_PLAN,
                        name="Tomorrow's Plan",
                        mandatory=False,
                        checked=True,
                        children=[],
                    ),
                ],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT,
                name="Site View Points",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.MATERIAL_UPDATE,
                name="Material Update",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.MANPOWER,
                name="Manpower",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.REPORTED_PROJECT_PROGRESS,
                name="Reported Project Progress",
                mandatory=False,
                checked=False,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.REPORTED_PROGRESS,
                        name="Reported Progress",
                        mandatory=False,
                        checked=False,
                        children=[],
                    ),
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.PROJECTED_END_DATE,
                        name="Projected End Date",
                        mandatory=False,
                        checked=False,
                        children=[],
                    ),
                ],
            ),
        ],
    ),
]

PERMISSION_MAPPING_CONFIG_ID: dict[WorkProgressReportConfigIdEnum, ReportConfigIdPermissionEntity] = {
    WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE: ReportConfigIdPermissionEntity(
        permission=Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE,
        permission_type=PermissionLevelEnum.PROJECT,
    ),
    WorkProgressReportConfigIdEnum.MATERIAL_UPDATE: ReportConfigIdPermissionEntity(
        permission=Permissions.CAN_ACCESS_MATERIALS,
        permission_type=PermissionLevelEnum.PROJECT_AND_ORG,
    ),
    WorkProgressReportConfigIdEnum.MANPOWER_SUMMARY: ReportConfigIdPermissionEntity(
        permission=Permissions.CAN_ACCESS_MATERIALS,
        permission_type=PermissionLevelEnum.PROJECT_AND_ORG,
    ),
}
