import abc
import datetime
import decimal
import inspect
from io import BytesIO

from boq.data.models import StatusChoices
from common.exceptions import BaseValidationError
from progressreport.models import ProgressReport
from project.domain.entities import ProjectOrgCreatedData
from report.download.domain.enums import DownloadProgressStatusEnum
from work_progress_v2.data.entities import (
    ElementAttachmentCreateDataEntity,
    ElementAttachmentDataEntity,
    ElementCategoryDataEntity,
    ElementDetailDataEntity,
    ElementItemTypeDataEntity,
    ElementListDataEntity,
    ElementPreviewFileDataEntity,
    ElementStatusDataEntity,
    ElementUomDataEntity,
    HistoryTimelineDataEntity,
    PaginatedElementListDataEntity,
    ScopeElementBaseEntity,
    ScopeTimelineDatesDataEntity,
    WPElementBaseEntity,
)
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.report_entities import (
    CreateProjectManpowerCategoryDataEntity,
    CreateSiteViewPointDataEntity,
    CreateSiteViewPointMappingDataEntity,
    ElementDayWiseDataEntity,
    LastReportForDailyLogDataEntity,
    LastReportReportedProjectProgressDataEntity,
    PrefillScopeUpdateDataEntity,
    ProjectOrgDaywiseDataEntity,
    ReportListPaginatedEntity,
    ReportPDFDataWithProjectDataEntity,
    SectionDayWiseDataEntity,
    SiteViewPointAttachmentDataEntity,
    SiteViewPointDataEntity,
    VendorReportCreatedByDataEntity,
)
from work_progress_v2.domain.entities import (
    ElementListFilterEntity,
    GetReportConfigEntity,
    PaginatedVendorScopeElementListEntity,
    ReportConfigEntity,
    ReportListFilterDataEntity,
    ScopeProgressEntity,
    SectionNameTotalProgressEntity,
    SectionScopeDataEntity,
    UpdateElementDataEntity,
    UpdateElementOutputDataEntity,
    UpdateElementOutputEntity,
    UpdateMethodElementEntity,
    VendorReportCreatedByListFilterDataEntity,
    VendorReportListFilterDataEntity,
    VendorScopeElementListFilterEntity,
)
from work_progress_v2.domain.enums import WorkProgressConfigReportTypeEnum
from work_progress_v2.domain.report_entities import (
    CreateReportAttachmentEntity,
    CreateReportItemEntity,
    CreateReportManPowerDataEntity,
    CreateReportScheduleUpdateAttachmentInputEntity,
    CreateReportSectionEntity,
    CreateScheduleActivityEntity,
    CreateSiteViewPointAttachmentEntity,
    HashtagElementEntity,
    MaterialItemEntity,
    PrefillScheduleUpdateEntity,
    ReportBaseDetailEntity,
    ReportDetailCreatedByEntity,
    ReportGeneratedPdfDataEntity,
    ReportPdfEntity,
    ReportSectionEntity,
)


class WorkProgressAbstractRepo(abc.ABC):
    user_id: int
    project_id: int
    org_id: int

    @abc.abstractmethod
    def __init__(self, user_id: int, project_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "project_id", "org_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def get_elements_for_scope_data(self, element_ids: list[int] | None = None) -> list[ScopeElementBaseEntity]:
        ...

    @abc.abstractmethod
    def get_sections_for_scope_data(self) -> list[SectionScopeDataEntity]:
        ...

    @abc.abstractmethod
    def get_bulk_wp_elements(self, element_ids: list[int]) -> list[ElementListDataEntity]:
        ...

    def get_scope_timeline_data(self) -> ScopeTimelineDatesDataEntity:
        ...

    @abc.abstractmethod
    def get_element_categories(self) -> list[ElementCategoryDataEntity]:
        ...

    @abc.abstractmethod
    def get_element_statuses(self) -> list[ElementStatusDataEntity]:
        ...

    @abc.abstractmethod
    def get_element_uoms(self) -> list[ElementUomDataEntity]:
        ...

    @abc.abstractmethod
    def get_element_item_types(self) -> list[ElementItemTypeDataEntity]:
        ...

    @abc.abstractmethod
    def update_element(
        self,
        element_id: int,
        updated_data: UpdateElementDataEntity,
        element: WorkProgressElement | None = None,
        save: bool = True,
    ) -> tuple[UpdateElementOutputDataEntity, WorkProgressElement]:
        ...

    @abc.abstractmethod
    def get_wp_element_entity(self, element_id: int) -> WPElementBaseEntity:
        ...

    @abc.abstractmethod
    def bulk_update_elements(self, updated_data: list[UpdateElementDataEntity]) -> list[UpdateElementOutputDataEntity]:
        ...

    @abc.abstractmethod
    def get_elements_for_update_method_entity(self, element_ids: list[int]) -> list[UpdateMethodElementEntity]:
        ...

    @abc.abstractmethod
    def get_work_progress_elements_paginated_data(
        self,
        filter_data: ElementListFilterEntity,
    ) -> PaginatedElementListDataEntity:
        ...

    def get_element_detail_data(self, element_id: int) -> ElementDetailDataEntity:
        ...

    def get_milestone_progress_percentage(self, milestone_id: int) -> decimal.Decimal:
        ...

    @abc.abstractmethod
    def get_element_history_data(self, element_id: int) -> list[HistoryTimelineDataEntity]:
        ...

    def get_element_all_attachments_entity(self, element_id: int) -> list[ElementAttachmentDataEntity]:
        ...

    @abc.abstractmethod
    def get_element_attachments_entity(
        self, element_id: int, attachment_ids: list[int]
    ) -> list[ElementAttachmentDataEntity]:
        ...

    @abc.abstractmethod
    def delete_attachments(self, element_id: int, attachment_ids: list[int]) -> None:
        ...

    @abc.abstractmethod
    def create_attachments(
        self, element_id: int, attachments: list[ElementAttachmentCreateDataEntity]
    ) -> list[ElementAttachmentDataEntity]:
        ...

    @abc.abstractmethod
    def check_all_elements_are_100_percent(self) -> bool:
        ...

    @abc.abstractmethod
    def update_work_report_status(self, status: StatusChoices):
        ...

    @abc.abstractmethod
    def get_element_preview_files(self, element_id: int) -> list[ElementPreviewFileDataEntity]:
        ...

    @abc.abstractmethod
    def get_project_created_at(self) -> datetime.datetime:
        ...


class WorkProgressReportAbstractRepo(abc.ABC):
    user_id: int
    project_id: int
    org_id: int

    class Exception(BaseValidationError):
        pass

    class ProjectManpowerCategoryFieldValidationException(Exception):
        pass

    class ReportDoesNotExistException(Exception):
        pass

    @abc.abstractmethod
    def __init__(self, user_id: int, project_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "project_id", "org_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def get_report_data(self, report_id: int) -> ReportBaseDetailEntity:
        ...

    @abc.abstractmethod
    def delete_report(self, report_id: int):
        ...

    @abc.abstractmethod
    def get_hashtag_element_list(self) -> list[HashtagElementEntity]:
        ...

    @abc.abstractmethod
    def get_hashtag_category_list(self) -> list[HashtagElementEntity]:
        ...

    @abc.abstractmethod
    def get_manpower_category_list(self) -> list[str]:
        ...

    @abc.abstractmethod
    def get_project_site_view_point_data(self) -> list[SiteViewPointDataEntity]:
        ...

    @abc.abstractmethod
    def get_site_view_attachment_data(
        self,
        number_of_previous_day_data_in_site_view_point: int,
        max_number_of_attachments_per_day: int,
    ) -> list[SiteViewPointAttachmentDataEntity]:
        ...

    @abc.abstractmethod
    def get_last_report_reported_project_progress_data(self) -> LastReportReportedProjectProgressDataEntity:
        ...

    @abc.abstractmethod
    def get_elements_for_prefill_scope_update_data(self) -> list[PrefillScopeUpdateDataEntity]:
        ...

    @abc.abstractmethod
    def get_today_latest_report_for_daily_log_data(self) -> LastReportForDailyLogDataEntity:
        ...

    @abc.abstractmethod
    def get_latest_generate_report_config_id(self) -> int:
        ...

    @abc.abstractmethod
    def create_report(
        self,
        input_project_progress_percentage: decimal.Decimal,
        input_projected_end_date: datetime.datetime | None,
        actual_progress_percentage: decimal.Decimal,
        total_manpower_count: int,
        total_element_count: int,
        today_progress_percentage: decimal.Decimal,
        config_id: int,
        save: bool = True,
    ) -> ProgressReport:
        ...

    @abc.abstractmethod
    def bulk_create_manpower(
        self,
        report_id: int,
        data: list[CreateReportManPowerDataEntity],
    ) -> None:
        ...

    @abc.abstractmethod
    def bulk_modify_view_points(
        self,
        rename_view_point_data: dict[int, str],
        delete_view_point_data: list[int],
    ) -> None:
        ...

    @abc.abstractmethod
    def bulk_create_view_points(self, data: list[str]) -> list[CreateSiteViewPointDataEntity]:
        ...

    @abc.abstractmethod
    def bulk_create_view_points_mapping(
        self, report_id: int, data: list[int]
    ) -> list[CreateSiteViewPointMappingDataEntity]:
        ...

    @abc.abstractmethod
    def bulk_create_view_point_attachments(self, data: list[CreateSiteViewPointAttachmentEntity]) -> None:
        ...

    @abc.abstractmethod
    def get_report_list_data(self, filter_data: ReportListFilterDataEntity) -> ReportListPaginatedEntity:
        ...

    @abc.abstractmethod
    def get_vendor_report_list_paginated_data(
        self, filter_data: VendorReportListFilterDataEntity
    ) -> ReportListPaginatedEntity:
        ...

    @abc.abstractmethod
    def get_prefill_manpower_data(self) -> list[CreateReportManPowerDataEntity]:
        ...

    @abc.abstractmethod
    def bulk_create_attachments(self, report_id: int, data: list[CreateReportAttachmentEntity]) -> None:
        ...

    @abc.abstractmethod
    def bulk_create_items(self, report_id: int, data: list[CreateReportItemEntity]) -> None:
        ...

    @abc.abstractmethod
    def create_project_manpower_category(self, name: str) -> CreateProjectManpowerCategoryDataEntity:
        ...

    @abc.abstractmethod
    def get_report_created_by_list(self) -> list[ReportDetailCreatedByEntity]:
        ...

    @abc.abstractmethod
    def get_vendor_report_created_by_list(
        self, filter_data: VendorReportCreatedByListFilterDataEntity
    ) -> list[VendorReportCreatedByDataEntity]:
        ...

    @abc.abstractmethod
    def bulk_create_sections(self, report_id: int, data: list[CreateReportSectionEntity]) -> list[ReportSectionEntity]:
        ...

    @abc.abstractmethod
    def get_report_configs(self, types: list[WorkProgressConfigReportTypeEnum]) -> GetReportConfigEntity:
        ...

    @abc.abstractmethod
    def update_report_config(
        self,
        type: WorkProgressConfigReportTypeEnum,
        config: list[ReportConfigEntity],
    ) -> None:
        ...

    @abc.abstractmethod
    def bulk_create_material_items(
        self,
        report_id: int,
        data: list[MaterialItemEntity],
    ) -> None:
        ...

    @abc.abstractmethod
    def create_schedule(
        self,
        report_id: int,
        data: PrefillScheduleUpdateEntity,
    ) -> tuple[int, list[CreateScheduleActivityEntity]]:
        ...

    @abc.abstractmethod
    def bulk_create_schedule_attachments(
        self, schedule_id: int, data: list[CreateReportScheduleUpdateAttachmentInputEntity]
    ) -> None:
        ...


class WorkProgressReportPDFAbstractRepo(abc.ABC):
    class WorkProgressReportPDFAbstractRepoException(BaseValidationError):
        pass

    @abc.abstractmethod
    def __init__(self, user_id: int, project_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "project_id", "org_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def save_work_progress_report_pdf(self, report_id: int, pdf: BytesIO):
        ...

    @abc.abstractmethod
    def get_work_progress_report_pdf(self, report_id: int) -> ReportPdfEntity:
        ...

    @abc.abstractmethod
    def update_single_report_pdf_status(self, report_id: int, status: DownloadProgressStatusEnum):
        ...

    @abc.abstractmethod
    def get_single_report_pdf_data(self, report_id: int) -> ReportGeneratedPdfDataEntity:
        ...

    @abc.abstractmethod
    def get_reports_between_date_range(
        self, start_date: datetime.date, end_date: datetime.date, org_id: int
    ) -> ReportPDFDataWithProjectDataEntity:
        ...

    @abc.abstractmethod
    def get_updated_project_org_data_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[ProjectOrgDaywiseDataEntity]:
        ...

    @abc.abstractmethod
    def get_updated_sections_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[SectionDayWiseDataEntity]:
        ...

    @abc.abstractmethod
    def get_updated_elements_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[ElementDayWiseDataEntity]:
        ...

    @abc.abstractmethod
    def get_project_organization_created_data(self) -> ProjectOrgCreatedData:
        ...


class WorkProgressOrgAbstractRepo(abc.ABC):
    user_id: int
    org_id: int

    @abc.abstractmethod
    def __init__(self, user_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "org_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def update_or_create_report_config(
        self, type: WorkProgressConfigReportTypeEnum, config: list[ReportConfigEntity]
    ) -> None:
        ...

    @abc.abstractmethod
    def get_configs(self, types: list[WorkProgressConfigReportTypeEnum]) -> GetReportConfigEntity:
        ...

    @abc.abstractmethod
    def create_project_configs(
        self, project_id: int, configs: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]]
    ) -> None:
        ...


class WorkProgressVendorScopeElementAbstractRepo(WorkProgressAbstractRepo):
    user_id: int
    project_id: int
    org_id: int
    vendor_ids: list[int]

    @abc.abstractmethod
    def __init__(self, user_id: int, project_id: int, org_id: int, vendor_ids: list[int], **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "project_id", "org_id", "vendor_ids"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    def get_element_paginated_list(
        self, filter_data: VendorScopeElementListFilterEntity
    ) -> PaginatedVendorScopeElementListEntity:
        ...


class WorkProgressDayWiseAbstractRepo(abc.ABC):
    user_id: int
    project_id: int
    org_id: int

    @abc.abstractmethod
    def __init__(self, user_id: int, project_id: int, org_id: int, **kwargs):
        ...

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Get the __init__ signature of the subclass.
        subclass_init = inspect.signature(cls.__init__)
        # Expected parameter names (order is less important than presence)
        required_params = {"user_id", "project_id", "org_id"}
        # Obtain parameters that are not 'self'
        params = {name: param for name, param in subclass_init.parameters.items() if name != "self"}
        missing = required_params - params.keys()
        if missing:
            raise TypeError(f"{cls.__name__}.__init__ is missing required parameters: {', '.join(missing)}")

    @abc.abstractmethod
    def update_project_daywise_data(self, data: ScopeProgressEntity):
        ...

    @abc.abstractmethod
    def update_section_daywise_data(self, data: list[SectionNameTotalProgressEntity]):
        ...

    @abc.abstractmethod
    def update_element_daywise_data(self, data: list[UpdateElementOutputEntity]):
        ...
