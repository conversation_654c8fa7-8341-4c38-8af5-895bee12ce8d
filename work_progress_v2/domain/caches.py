import decimal
from decimal import Decimal

import structlog
from django.conf import settings
from django.utils import timezone
from django.utils.module_loading import import_string

from common.pydantic.base_cache import PydanticBaseCache, PydanticIncrAndDecrCounterCache
from common.pydantic.base_model import OrgIdProjectIdEntity
from common.utils import get_local_time
from core.caches import OrgUser<PERSON><PERSON><PERSON><PERSON>ache
from core.entities import Org<PERSON>ser<PERSON>ntity, ProjectUserEntity
from project.domain.caches import ItemTypeConfigCache, ProjectUserPermissionCache
from rollingbanners.custom_caches import BaseCache
from work_progress_v2.domain.entities import (
    ScopeProgressEntity,
    SectionListEntity,
    SectionNameTotalProgressEntity,
    UpdatedSectionDataEntity,
    WorkProgressCacheKeyEntity,
    WorkProgressScopeDataCacheEntity,
    WorkProgressSectionDataCacheEntity,
)
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService
from work_progress_v2.interface.exceptions import WorkProgressVersionMismatch

CACHE: BaseCache = import_string(settings.CUSTOM_CACHE)
logger = structlog.get_logger(__name__)

VERSION_CACHE_VERSION = 1
SCOPE_DATA_CACHE_VERSION = 2
SECTION_DATA_CACHE_VERSION = 1


class WorkProgressDataVersionCache(PydanticIncrAndDecrCounterCache[WorkProgressCacheKeyEntity]):
    CACHE_KEY = f"work_progress_data_version_v{VERSION_CACHE_VERSION}_{{0}}"
    CACHE_TTL = 60 * 60 * 24

    @classmethod
    def get_cache_key(cls, key: WorkProgressCacheKeyEntity) -> str:
        return cls.CACHE_KEY.format(f"{key.org_id}_{key.project_id}")

    @classmethod
    def get_data(cls, key: WorkProgressCacheKeyEntity) -> int:
        return 1


class WorkProgressScopeDataCache(PydanticBaseCache[WorkProgressCacheKeyEntity, WorkProgressScopeDataCacheEntity]):
    CACHE_KEY = f"work_progress_scope_data_v{SCOPE_DATA_CACHE_VERSION}_{{0}}"
    CACHE_TTL = 60 * 5

    @classmethod
    def get_cache_key(cls, key: WorkProgressCacheKeyEntity):
        # current date is need because scope date has a key called today_updated_item_count
        # which is updated daily. So we need to have a different cache key for each day
        current_date = get_local_time(timezone.now()).date()
        return cls.CACHE_KEY.format(f"{key.org_id}_{key.project_id}_{current_date}")

    @classmethod
    def get(cls, key: WorkProgressCacheKeyEntity):  # type: ignore
        cache_key = cls.get_cache_key(key=key)
        cache_data = CACHE.get(cache_key, decode_json=True)

        if cache_data is not None:
            return WorkProgressScopeDataCacheEntity.model_validate(cache_data)

        return None


class WorkProgressSectionDataCache(PydanticBaseCache[WorkProgressCacheKeyEntity, WorkProgressSectionDataCacheEntity]):
    CACHE_KEY = f"work_progress_section_data_v{SECTION_DATA_CACHE_VERSION}_{{0}}"
    CACHE_TTL = 60 * 5

    @classmethod
    def get_cache_key(cls, key: WorkProgressCacheKeyEntity):
        return cls.CACHE_KEY.format(f"{key.org_id}_{key.project_id}")

    @classmethod
    def get(cls, key: WorkProgressCacheKeyEntity):  # type: ignore
        cache_key = cls.get_cache_key(key=key)
        cache_data = CACHE.get(cache_key, decode_json=True)

        if cache_data is not None:
            return WorkProgressSectionDataCacheEntity.model_validate(cache_data)

        return None


class WorkProgressCache:
    def __init__(self, org_id: int, project_id: int, user_id: int, version: int | None = None):
        self.org_id = org_id
        self.project_id = project_id
        self.version = version
        self.user_id = user_id
        self.cache_key = WorkProgressCacheKeyEntity(
            org_id=org_id,
            project_id=project_id,
        )

    def validate_work_progress_version(self):
        current_version = WorkProgressDataVersionCache.get(
            key=WorkProgressCacheKeyEntity(org_id=self.org_id, project_id=self.project_id)
        )

        if self.version != current_version:
            logger.info(
                "Work progress version mismatch",
                expected_version=self.version,
                current_version=current_version,
            )
            raise WorkProgressVersionMismatch

    def update_scope_and_section_cache(
        self, scope_data: ScopeProgressEntity, section_data: list[SectionNameTotalProgressEntity]
    ):
        logger.info("Updating scope and section cache", scope_data=scope_data, section_data=section_data)
        version = WorkProgressDataVersionCache.incr(key=self.cache_key)

        PydanticBaseCache.mset(
            {
                WorkProgressScopeDataCache.get_cache_key(key=self.cache_key): WorkProgressScopeDataCacheEntity(
                    scope_data=scope_data,
                    version=version,
                ),
                WorkProgressSectionDataCache.get_cache_key(key=self.cache_key): WorkProgressSectionDataCacheEntity(
                    sections=section_data,
                    version=version,
                ),
            }
        )

        return version

    def update_scope_data_cache(self, data: ScopeProgressEntity, force_version_update: bool = True):
        logger.info("Updating scope data cache", force_version_update=force_version_update, data=data)

        if force_version_update:
            version = WorkProgressDataVersionCache.incr(key=self.cache_key)
        else:
            version = WorkProgressDataVersionCache.get(key=self.cache_key)

        WorkProgressScopeDataCache.set(
            key=self.cache_key,
            data=WorkProgressScopeDataCacheEntity(scope_data=data, version=version),
        )

    def update_section_data_cache(self, data: list[SectionNameTotalProgressEntity], force_version_update: bool = True):
        logger.info("Updating section data cache", force_version_update=force_version_update, data=data)

        if force_version_update:
            version = WorkProgressDataVersionCache.incr(key=self.cache_key)
        else:
            version = WorkProgressDataVersionCache.get(key=self.cache_key)

        WorkProgressSectionDataCache.set(
            key=self.cache_key,
            data=WorkProgressSectionDataCacheEntity(sections=data, version=version),
        )

    def get_scope_data(
        self,
        scope_data_service: WorkProgressScopeDataService,
        cache: bool = True,
    ):
        cache_data = WorkProgressScopeDataCache.get(key=self.cache_key)
        logger.info("Getting scope data", cache_key=self.cache_key, cache=cache, cache_data=cache_data)

        if cache:
            if cache_data:
                logger.info("Cache hit")
                return cache_data.scope_data

            db_data = scope_data_service.get_scope_data()

            self.update_scope_data_cache(data=db_data, force_version_update=False)
            logger.info("Cache miss", db_data=db_data)
            return db_data

        db_data = scope_data_service.get_scope_data()

        if cache_data is None or cache_data.scope_data != db_data:
            self.update_scope_data_cache(data=db_data)

        return db_data

    def get_section_data(
        self,
        scope_data_service: WorkProgressScopeDataService,
        cache: bool = True,
    ):
        cache_data = WorkProgressSectionDataCache.get(key=self.cache_key)
        logger.info("Getting section data", cache_key=self.cache_key, cache=cache, cache_data=cache_data)

        if cache:
            if cache_data:
                logger.info("Cache hit")
                return cache_data.sections

            db_data = scope_data_service.get_section_total_progress_with_name()

            self.update_section_data_cache(data=db_data, force_version_update=False)
            logger.info("Cache miss", db_data=db_data)
            return db_data

        db_data = scope_data_service.get_section_total_progress_with_name()

        if cache_data is None or cache_data.sections != db_data:
            self.update_section_data_cache(data=db_data)

        return db_data

    def get_updated_section_data(
        self,
        section_data: list[UpdatedSectionDataEntity],
        scope_data_service: WorkProgressScopeDataService,
    ) -> tuple[list[SectionListEntity], list[SectionNameTotalProgressEntity]]:
        logger.info("Getting updated section data", section_data=section_data)
        cache_data = self.get_section_data(scope_data_service=scope_data_service, cache=True)

        section_id_to_data = {section.id: section for section in section_data}

        for section in cache_data:
            if section.id not in section_id_to_data:
                continue

            curr_section_data = section_id_to_data[section.id]

            if section.is_quantity_zero:
                section.progress_percentage += (
                    curr_section_data.progress_percentage_diff / section.element_count
                    if section.element_count > 0
                    else 0
                )
                continue

            section.total_completion_amount += curr_section_data.completion_amount_diff

            section.progress_percentage = (
                (section.total_completion_amount / section.total_amount) * 100
                if section.total_amount > 0
                else decimal.Decimal(0)
            )

            assert section.progress_percentage <= 100, "Assert: Progress percentage can not be greater than 100"

        logger.info("Updated section data", cache_data=cache_data)
        return [
            SectionListEntity(
                id=section.id,
                name=section.name,
                progress_percentage=section.progress_percentage,
                element_count=section.element_count,
                total_amount=section.total_amount,
            )
            for section in cache_data
        ], cache_data

    def get_updated_scope_data(
        self,
        scope_data_service: WorkProgressScopeDataService,
        completion_amount_diff: decimal.Decimal,
        total_element_updated_today: int = 0,
        cache: bool = True,
    ) -> ScopeProgressEntity:
        logger.info(
            "Getting updated scope data",
            completion_amount_diff=completion_amount_diff,
            total_element_updated_today=total_element_updated_today,
        )

        cache_data = self.get_scope_data(scope_data_service=scope_data_service, cache=cache)

        cache_data.today_progress_amount += completion_amount_diff
        cache_data.total_progress_amount += completion_amount_diff

        cache_data.today_progress = (
            (cache_data.today_progress_amount / cache_data.total_amount) * 100
            if cache_data.total_amount > 0
            else decimal.Decimal(0)
        )
        cache_data.total_progress = (
            (cache_data.total_progress_amount / cache_data.total_amount) * 100
            if cache_data.total_amount > 0
            else decimal.Decimal(0)
        )

        cache_data.today_updated_item_count += total_element_updated_today
        """
        PATCH: Although the below condition should not be true, but due to some unforeseeable reasons, it is possible.
        So we are adding this patch to handle the case.
        """
        if cache_data.today_updated_item_count > cache_data.total_item_count:
            cache_data.today_updated_item_count = cache_data.total_item_count

        """
        PATCH: Although the below condition should not be true, but due to some unforeseeable reasons, it is possible.
        So we are adding this patch to handle the case, request_id="5a56a3a5082fa30bbf9f2ec1bf10f983", timestamp -> 2025-06-23 12:18:36.701
        """
        if cache_data.total_progress > 100:
            cache_data.total_progress = Decimal("100.0")

        assert cache_data.today_progress <= 100, "Assert: Today progress percentage can not be greater than 100"
        assert cache_data.total_progress <= 100, "Assert: Total progress percentage can not be greater than 100"
        assert (
            cache_data.today_updated_item_count <= cache_data.total_item_count
        ), "Assert: Today updated item count can no be greater than total item count"

        logger.info("Updated scope data", cache_data=cache_data)
        return ScopeProgressEntity(
            total_item_count=cache_data.total_item_count,
            today_updated_item_count=cache_data.today_updated_item_count,
            total_amount=cache_data.total_amount,
            today_progress=cache_data.today_progress,
            total_progress=cache_data.total_progress,
            today_progress_amount=cache_data.today_progress_amount,
            total_progress_amount=cache_data.total_progress_amount,
        )

    def get_item_type_config(self):
        return ItemTypeConfigCache.get(key=OrgIdProjectIdEntity(org_id=self.org_id, project_id=self.project_id))

    def get_user_project_permissions(self):
        return ProjectUserPermissionCache.get(
            key=ProjectUserEntity(
                user_id=self.user_id,
                org_id=self.org_id,
                project_id=self.project_id,
            )
        ).permissions

    def get_user_org_permissions(self):
        return OrgUserPermissionCache.get(
            key=OrgUserEntity(
                user_id=self.user_id,
                org_id=self.org_id,
            )
        ).permissions
