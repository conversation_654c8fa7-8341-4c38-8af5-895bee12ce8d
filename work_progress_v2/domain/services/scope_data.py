import decimal

import structlog
from django.utils import timezone

from common.pydantic.base_model import OrgIdProjectIdEntity
from common.utils import get_local_time
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from project.domain.entities import ItemTypeConfigCacheEntity
from work_progress_v2.data.entities import ScopeElementBaseEntity
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo, WorkProgressVendorScopeElementAbstractRepo
from work_progress_v2.domain.entities import (
    ScopeProgressEntity,
    SectionListEntity,
    SectionNameTotalProgressEntity,
)
from work_progress_v2.domain.helper import WorkProgressScopeDataHelper
from work_progress_v2.domain.helper_entities import SectionTodayProgressElementEntity, SectionTotalProgressElementEntity

logger = structlog.get_logger(__name__)


class WorkProgressScopeDataService:
    def __init__(self, repo: WorkProgressAbstractRepo, user_entity: ProjectUserEntity):
        self.repo = repo
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id
        self.current_date = get_local_time(timezone.now()).date()
        self.scope_data_helper = WorkProgressScopeDataHelper()

    # PATCH: Cache should not be used like this here
    def get_item_type_config(self) -> ItemTypeConfigCacheEntity:
        from project.domain.caches import ItemTypeConfigCache

        return ItemTypeConfigCache.get(key=OrgIdProjectIdEntity(org_id=self.org_id, project_id=self.project_id))

    def get_repo(self) -> WorkProgressAbstractRepo:
        return self.repo

    def segregate_section_today_and_total_progress_data(
        self, elements: list[ScopeElementBaseEntity]
    ) -> tuple[list[SectionTodayProgressElementEntity], list[SectionTotalProgressElementEntity]]:
        section_today_progress_data: list[SectionTodayProgressElementEntity] = []
        section_total_progress_data: list[SectionTotalProgressElementEntity] = []

        item_type_configs = self.get_item_type_config()

        for element in elements:
            update_method = element.update_method

            if update_method is None:
                if element.item_type_id:
                    element_item_type_config = item_type_configs.item_types.get(element.item_type_id)

                    assert element_item_type_config is not None, "Item type config not found in cache"

                    element.update_method = ItemTypeUpdateMethodChoices(element_item_type_config.default_update_method)
                else:
                    element.update_method = ItemTypeUpdateMethodChoices(
                        item_type_configs.default_config.default_update_method
                    )

            section_today_progress_data.append(
                SectionTodayProgressElementEntity(
                    id=element.id,
                    section_id=element.section_id,
                    quantity=element.quantity,
                    amount=element.amount,
                    progress_percentage=element.progress_percentage,
                    previous_day_progress_percentage=(
                        element.previous_day_progress_percentage
                        if element.previous_day_progress_percentage
                        else decimal.Decimal(0)
                    ),
                    progress_updated_at=element.progress_updated_at,
                    attachment_uploaded_at=element.attachment_uploaded_at,
                    update_method=element.update_method,
                )
            )
            section_total_progress_data.append(
                SectionTotalProgressElementEntity(
                    id=element.id,
                    section_id=element.section_id,
                    quantity=element.quantity,
                    amount=element.amount,
                    progress_percentage=element.progress_percentage,
                    update_method=element.update_method,
                )
            )

        return section_today_progress_data, section_total_progress_data

    def get_scope_data(self, element_ids: list[int] | None = None) -> ScopeProgressEntity:
        elements = self.repo.get_elements_for_scope_data(element_ids=element_ids)
        section_today_progress_data, section_total_progress_data = self.segregate_section_today_and_total_progress_data(
            elements=elements
        )
        total_progress_data = self.scope_data_helper.get_total_progress(elements=section_total_progress_data)
        today_progress_data = self.scope_data_helper.get_today_progress(elements=section_today_progress_data)

        return ScopeProgressEntity(
            total_item_count=total_progress_data.total_element_count,
            today_updated_item_count=today_progress_data.updated_element_count,
            total_amount=total_progress_data.total_amount,
            today_progress=today_progress_data.progress_percentage,
            total_progress=total_progress_data.progress_percentage,
            today_progress_amount=today_progress_data.completion_amount,
            total_progress_amount=total_progress_data.completion_amount,
        )

    def get_section_total_progress_with_name(self) -> list[SectionNameTotalProgressEntity]:
        elements = self.repo.get_elements_for_scope_data()
        section_data = self.repo.get_sections_for_scope_data()

        _, section_total_progress_data = self.segregate_section_today_and_total_progress_data(elements=elements)

        section_total_progress = self.scope_data_helper.get_section_total_progress(elements=section_total_progress_data)

        section_id_to_section_total_progress = {section.id: section for section in section_total_progress}

        entities: list[SectionNameTotalProgressEntity] = []

        for section in section_data:
            if section.id not in section_id_to_section_total_progress:
                entities.append(
                    SectionNameTotalProgressEntity(
                        id=section.id,
                        progress_percentage=decimal.Decimal(0),
                        element_count=0,
                        name=section.name,
                        total_amount=decimal.Decimal(0),
                        total_completion_amount=decimal.Decimal(0),
                        is_quantity_zero=True,
                    )
                )

            else:
                entities.append(
                    SectionNameTotalProgressEntity(
                        id=section.id,
                        name=section.name,
                        progress_percentage=section_id_to_section_total_progress[section.id].progress_percentage,
                        element_count=section_id_to_section_total_progress[section.id].element_count,
                        total_amount=section_id_to_section_total_progress[section.id].total_amount,
                        total_completion_amount=section_id_to_section_total_progress[
                            section.id
                        ].total_completion_amount,
                        is_quantity_zero=section_id_to_section_total_progress[section.id].is_quantity_zero,
                    )
                )

        return entities

    def get_section_data(self) -> list[SectionListEntity]:
        section_total_progress = self.get_section_total_progress_with_name()

        return [
            SectionListEntity(
                id=section.id,
                progress_percentage=section.progress_percentage,
                element_count=section.element_count,
                name=section.name,
                total_amount=section.total_amount,
            )
            for section in section_total_progress
        ]


class WorkProgressVendorScopeDataService(WorkProgressScopeDataService):
    def __init__(
        self,
        repo: WorkProgressVendorScopeElementAbstractRepo,
        user_entity: ProjectUserEntity,
        vendor_ids: list[int],
    ):
        super().__init__(repo=repo, user_entity=user_entity)
        self.repo = repo
        self.vendor_ids = vendor_ids
