import datetime

import structlog
from django.db import IntegrityError
from django.utils import timezone

from authorization.domain.constants import Permissions
from authorization.enums import PermissionLevelEnum
from common.exceptions import BaseValidationError
from common.services import is_empty
from common.utils import format_trailing_zeros, get_local_time
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from inventory.domain.services import InventoryWorkProgressService
from project_schedule.interface.external.work_progress import WorkProgressToProjectScheduleService
from work_progress_v2.domain.abstract_repos import WorkProgressReportAbstractRepo
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import (
    GenerateReportConfigEntity,
    ReportListFilterDataEntity,
    SectionTotalProgressEntity,
    VendorReportCreatedByListFilterDataEntity,
    VendorReportListFilterDataEntity,
)
from work_progress_v2.domain.enums import (
    WorkProgressConfigReportTypeEnum,
    WorkProgressCreationTypeEnum,
    WorkProgressDailyLogTypeEnum,
    WorkProgressInventoryStockItemTypeEnum,
    WorkProgressReportActionEnum,
    WorkProgressReportConfigIdEnum,
    WorkProgressReportDetailActionEnum,
)
from work_progress_v2.domain.helper import WorkProgressScopeDataHelper
from work_progress_v2.domain.report_entities import (
    AddDailyLogItemWithAttachmentEntity,
    CreateReportAttachmentEntity,
    CreateReportDailyLogInputEntity,
    CreateReportDailyLogUpdateInputEntity,
    CreateReportInputEntity,
    CreateReportItemEntity,
    CreateReportManPowerDataEntity,
    CreateReportManPowerInputEntity,
    CreateReportScheduleUpdateAttachmentInputEntity,
    CreateReportScopeUpdateInputEntity,
    CreateReportSectionEntity,
    CreateReportSiteViewPointInputEntity,
    CreateSiteViewPointAttachmentEntity,
    HashtagListDomainEntity,
    MaterialItemEntity,
    PrefillDailyLogEntity,
    PrefillDailyLogUpdateAttachmentEntity,
    PrefillDailyLogUpdateEntity,
    PrefillMaterialUpdateElementEntity,
    PrefillMaterialUpdateEntity,
    PrefillReportedProjectProgressEntity,
    PrefillScheduleUpdateEntity,
    PrefillScopeSummaryEntity,
    PrefillScopeUpdateEntity,
    PrefillScopeUpdateSectionEntity,
    PrefillSiteViewPointEntity,
    ScopeUpdateSectionAttachmentEntity,
    SiteViewPointAttachmentEntity,
    SiteViewPointMappingEntity,
    SystemGeneratedUpdatesEntity,
    WorkProgressBlockBaseModel,
)
from work_progress_v2.domain.services.config import WorkProgressProjectConfigService
from work_progress_v2.domain.services.permission import WorkProgressPermissionService
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService
from work_progress_v2.domain.triggers import trigger_work_progress_report_generated
from work_progress_v2.domain.utils import WorkProgressReportItemBlockHelper

logger = structlog.get_logger(__name__)


class WorkProgressReportService:
    NUMBER_OF_PREVIOUS_DAY_DATA_IN_SITE_VIEW_POINT = 3
    MAX_NUMBER_OF_SITE_VIEW_POINT_ATTACHMENT_PER_DAY = 4
    REPORT_DELETE_TIME = datetime.timedelta(days=1)

    class Exception(BaseValidationError):
        pass

    class ProjectDoesNotExist(Exception):
        pass

    class FailedToCreateManpowerCategory(Exception):
        pass

    class FailedToCreateProjectManpowerCategory(Exception):
        pass

    class ProjectCategoryNameAlreadyExists(Exception):
        pass

    class ProjectCategoryShouldNotBeEmpty(Exception):
        pass

    class ReportDoesNotExist(Exception):
        pass

    class ReportIsLocked(Exception):
        pass

    class ReportIsNotCreatedByUser(Exception):
        pass

    class ReportAlreadyDeleted(Exception):
        pass

    def __init__(
        self,
        repo: WorkProgressReportAbstractRepo,
        user_entity: ProjectUserEntity,
        config_service: WorkProgressProjectConfigService,
        permission_service: WorkProgressPermissionService,
        cache: WorkProgressCache,
    ):
        self.repo = repo
        self.user_entity = user_entity
        self.current_time = get_local_time(timezone.now())
        self.current_day = self.current_time.date()
        self.config_service = config_service
        self.cache = cache
        self.permission_service = permission_service

    def get_hashtag_element_list(self):
        return self.repo.get_hashtag_element_list()

    def get_hashtag_category_list(self):
        return self.repo.get_hashtag_category_list()

    def get_manpower_category_list(self):
        return self.repo.get_manpower_category_list()

    def get_prefill_site_view_point_data(self) -> list[PrefillSiteViewPointEntity]:
        site_view_data = self.repo.get_project_site_view_point_data()

        attachment_data = self.repo.get_site_view_attachment_data(
            number_of_previous_day_data_in_site_view_point=self.NUMBER_OF_PREVIOUS_DAY_DATA_IN_SITE_VIEW_POINT,
            max_number_of_attachments_per_day=self.MAX_NUMBER_OF_SITE_VIEW_POINT_ATTACHMENT_PER_DAY,
        )

        site_view_attachment_mapping: dict[int, SiteViewPointMappingEntity] = {}

        for attachment in attachment_data:
            if attachment.site_view_point_id not in site_view_attachment_mapping:
                site_view_attachment_mapping[attachment.site_view_point_id] = SiteViewPointMappingEntity(
                    current_attachments=[],
                    previous_attachments=[],
                )

            current_site_view_attachments = site_view_attachment_mapping[attachment.site_view_point_id]

            attachment_entity = SiteViewPointAttachmentEntity(
                id=attachment.id,
                name=attachment.name,
                url=attachment.url,
                thumbnail_url=attachment.thumbnail_url,
                uploaded_at=attachment.uploaded_at,
            )

            if attachment.is_attachment_of_current_day:
                current_site_view_attachments.current_attachments.append(attachment_entity)
            else:
                current_site_view_attachments.previous_attachments.append(attachment_entity)

        entities: list[PrefillSiteViewPointEntity] = []

        for site_view in site_view_data:
            site_view_attachments = site_view_attachment_mapping.get(site_view.id, None)

            view_point = PrefillSiteViewPointEntity(
                id=site_view.id,
                name=site_view.name,
                previous_attachments=site_view_attachments.previous_attachments if site_view_attachments else [],
                current_attachments=site_view_attachments.current_attachments if site_view_attachments else [],
            )

            entities.append(view_point)

        return entities

    def get_prefill_reported_project_progress_data(self) -> PrefillReportedProjectProgressEntity:
        last_report_data = self.repo.get_last_report_reported_project_progress_data()

        checked_config_ids_for_generate_report = self.config_service.get_config_checked_ids(
            type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT
        )

        return PrefillReportedProjectProgressEntity(
            reported_progress_percentage=(
                last_report_data.reported_progress_percentage
                if WorkProgressReportConfigIdEnum.REPORTED_PROGRESS.value in checked_config_ids_for_generate_report
                else None
            ),
            projected_end_date=(
                last_report_data.projected_end_date
                if WorkProgressReportConfigIdEnum.PROJECTED_END_DATE.value in checked_config_ids_for_generate_report
                else None
            ),
        )

    def get_prefill_scope_update_data(
        self, section_total_progress: list[SectionTotalProgressEntity]
    ) -> list[PrefillScopeUpdateSectionEntity]:
        wp_elements = self.repo.get_elements_for_prefill_scope_update_data()

        section_total_progress_mapping: dict[int, SectionTotalProgressEntity] = {}
        for section in section_total_progress:
            section_total_progress_mapping[section.id] = section

        section_updates: dict[int, PrefillScopeUpdateSectionEntity] = {}

        item_type_cache = self.cache.get_item_type_config()

        # Creating empty section updates for sorting sections
        for section in section_total_progress:
            section_updates[section.id] = PrefillScopeUpdateSectionEntity(
                id=section.id,
                name="",
                progress_percentage=section.progress_percentage,
                attachments=[],
                updates=[],
            )

        for element in wp_elements:
            if element.section_id not in section_updates:
                section_updates[element.section_id] = PrefillScopeUpdateSectionEntity(
                    id=element.section_id,
                    name=element.section_name,
                    progress_percentage=section_total_progress_mapping[element.section_id].progress_percentage,
                    attachments=[],
                    updates=[],
                )
            else:
                section_updates[element.section_id].name = element.section_name

            section_update = section_updates[element.section_id]

            if element.attachments:
                for attachment in element.attachments:
                    if get_local_time(attachment.uploaded_at).date() != self.current_day:
                        continue

                    section_update.attachments.append(
                        ScopeUpdateSectionAttachmentEntity(
                            id=attachment.id,
                            name=attachment.name,
                            url=attachment.url,
                            thumbnail_url=attachment.thumbnail_url,
                            uploaded_at=attachment.uploaded_at,
                            element_name=element.name,
                        )
                    )

            blocks: list[WorkProgressBlockBaseModel] = []

            element_update_method = element.update_method
            item_type_data = None

            if element.item_type_id is not None:
                item_type_data = item_type_cache.item_types.get(element.item_type_id)

            if element_update_method is None and item_type_data:
                element_update_method = item_type_data.default_update_method
            elif element_update_method is None:
                element_update_method = item_type_cache.default_config.default_update_method

            # if (element_update_method == ItemTypeUpdateMethodChoices.QUANTITY) and (
            #     element.previous_day_progress_quantity_input == element.progress_quantity_input
            #     or (element.previous_day_progress_quantity_input is None and element.progress_quantity_input == 0)
            # ):
            #     continue

            # if element_update_method != ItemTypeUpdateMethodChoices.QUANTITY and (
            #     (element.previous_day_progress_percentage == element.progress_percentage)
            #     or (element.previous_day_progress_percentage is None and element.progress_percentage == 0)
            # ):
            #     continue

            new_update = ""
            previous_update = None
            is_milestone_changed = False

            if element_update_method == ItemTypeUpdateMethodChoices.QUANTITY:
                if element.progress_quantity_input is None:
                    new_update = f"0 {element.uom_name}"
                else:
                    new_update = f"{format_trailing_zeros(element.progress_quantity_input)} {element.uom_name}"

                if element.previous_day_progress_quantity_input is not None:
                    previous_update = (
                        f"{format_trailing_zeros(element.previous_day_progress_quantity_input)} {element.uom_name}"
                    )

            elif element_update_method == ItemTypeUpdateMethodChoices.PERCENTAGE:
                new_update = f"{element.progress_percentage}%"

                if element.previous_day_progress_percentage is not None:
                    previous_update = f"{element.previous_day_progress_percentage}%"

            else:
                assert item_type_data is not None, "Item type data is required for non-quantity update method"

                for milestone in item_type_data.milestones:
                    is_milestone_changed = True
                    if milestone.id == element.milestone_input_id:
                        new_update = milestone.name.upper()

                    if milestone.id == element.previous_day_progress_milestone_input_id:
                        previous_update = milestone.name.upper()

                    if new_update == "":
                        new_update = "Not Started"

            blocks.extend(
                WorkProgressReportItemBlockHelper.create_system_generated_block(
                    data=SystemGeneratedUpdatesEntity(
                        element_id=element.id,
                        element_name=element.name,
                        previous_update=previous_update,
                        new_update=new_update,
                        is_milestone_changed=is_milestone_changed,
                    )
                )
            )

            section_update.updates.append(blocks)

        final_section_updates: list[PrefillScopeUpdateSectionEntity] = []

        for section_id, section_update in section_updates.items():
            if section_update.attachments or section_update.updates:
                final_section_updates.append(section_update)

        return final_section_updates

    def get_prefill_daily_log_data(self) -> PrefillDailyLogEntity:
        today_latest_report_data = self.repo.get_today_latest_report_for_daily_log_data()

        return PrefillDailyLogEntity(
            today_update=PrefillDailyLogUpdateEntity(
                updates=today_latest_report_data.today_update.updates,
                attachments=[
                    PrefillDailyLogUpdateAttachmentEntity(**attachment.dict())
                    for attachment in today_latest_report_data.today_update.attachments
                ],
            ),
            blocker=PrefillDailyLogUpdateEntity(
                updates=today_latest_report_data.blocker.updates,
                attachments=[
                    PrefillDailyLogUpdateAttachmentEntity(**attachment.dict())
                    for attachment in today_latest_report_data.blocker.attachments
                ],
            ),
            tomorrow_plan=PrefillDailyLogUpdateEntity(
                updates=today_latest_report_data.tomorrow_plan.updates,
                attachments=[
                    PrefillDailyLogUpdateAttachmentEntity(**attachment.dict())
                    for attachment in today_latest_report_data.tomorrow_plan.attachments
                ],
            ),
        )

    def get_prefill_manpower_data(self):
        return self.repo.get_prefill_manpower_data()

    def rename_view_points(
        self,
        current_view_points: list[PrefillSiteViewPointEntity],
        incoming_view_points: list[CreateReportSiteViewPointInputEntity],
    ) -> dict[int, str]:
        incoming_view_point_ids = {view_point.id for view_point in incoming_view_points}

        renamed_view_points: dict[int, str] = {}

        for view_point in current_view_points:
            if view_point.id not in incoming_view_point_ids:
                continue

            new_view_point = next(filter(lambda x: x.id == view_point.id, incoming_view_points))

            if view_point.name != new_view_point.name:
                renamed_view_points[view_point.id] = new_view_point.name

        return renamed_view_points

    def delete_view_points(
        self,
        current_view_points: list[PrefillSiteViewPointEntity],
        incoming_view_points: list[CreateReportSiteViewPointInputEntity],
    ) -> list[int]:
        incoming_view_point_ids = {view_point.id for view_point in incoming_view_points}

        deleted_view_points: list[int] = []

        for view_point in current_view_points:
            if view_point.id not in incoming_view_point_ids:
                deleted_view_points.append(view_point.id)

        return deleted_view_points

    def add_view_points_with_attachments(
        self,
        report_id: int,
        incoming_create_view_points: list[CreateReportSiteViewPointInputEntity],
    ):
        """
        Create view points and attachments
        """
        create_view_point_name_list: list[str] = []
        name_to_incoming_view_point_mapping: dict[str, CreateReportSiteViewPointInputEntity] = {}

        # create project view points
        for view_point in incoming_create_view_points:
            # Fail safe: Skip view points that already have an id
            if view_point.id is not None:
                continue

            create_view_point_name_list.append(view_point.name)
            name_to_incoming_view_point_mapping[view_point.name] = view_point

        created_view_point = self.repo.bulk_create_view_points(create_view_point_name_list)

        # Filter out view points that have attachments
        # because attachment is mandatory for a view point to be created
        create_view_point_mapping_ids: list[int] = []
        view_point_id_to_view_point: dict[int, CreateReportSiteViewPointInputEntity] = {}
        for view_point in created_view_point:
            if name_to_incoming_view_point_mapping[view_point.name].attachments:
                create_view_point_mapping_ids.append(view_point.id)
                view_point_id_to_view_point[view_point.id] = name_to_incoming_view_point_mapping[view_point.name]

        created_view_point_mapping = self.repo.bulk_create_view_points_mapping(
            data=create_view_point_mapping_ids,
            report_id=report_id,
        )

        # Create view point attachments
        view_point_id_to_view_point_mapping_id: dict[int, int] = {}
        for mapping in created_view_point_mapping:
            view_point_id_to_view_point_mapping_id[mapping.site_view_point_id] = mapping.id

        view_point_attachments: list[CreateSiteViewPointAttachmentEntity] = []

        for view_point_id in view_point_id_to_view_point:
            for attachment in view_point_id_to_view_point[view_point_id].attachments:
                assert not is_empty(attachment.url), "Attachment URL should not be empty"
                view_point_attachments.append(
                    CreateSiteViewPointAttachmentEntity(
                        mapping_id=view_point_id_to_view_point_mapping_id[view_point_id],
                        url=attachment.url,
                        name=attachment.name,
                        is_previous=False,
                        thumbnail_url=attachment.thumbnail_url,
                    )
                )

        return view_point_attachments

    def update_view_points_with_attachments(
        self,
        report_id: int,
        incoming_update_view_points: list[CreateReportSiteViewPointInputEntity],
        current_view_points: list[PrefillSiteViewPointEntity],
    ):
        """
        Update view points and attachments
        """

        update_view_point_mapping_ids: list[int] = []
        view_point_id_to_view_point: dict[int, CreateReportSiteViewPointInputEntity] = {}

        # Create view point mappings
        for view_point in incoming_update_view_points:
            # Fail safe: Skip view points that do not have an id
            if view_point.id is None:
                continue

            # Only create view point mappings for view points that have attachments
            if not view_point.attachments:
                continue

            update_view_point_mapping_ids.append(view_point.id)
            view_point_id_to_view_point[view_point.id] = view_point

        updated_view_point_mapping = self.repo.bulk_create_view_points_mapping(
            data=update_view_point_mapping_ids,
            report_id=report_id,
        )

        # Create view point attachments
        view_point_id_to_view_point_mapping_id: dict[int, int] = {}
        for mapping in updated_view_point_mapping:
            view_point_id_to_view_point_mapping_id[mapping.site_view_point_id] = mapping.id

        view_point_attachments: list[CreateSiteViewPointAttachmentEntity] = []

        for view_point_id in view_point_id_to_view_point:
            for attachment in view_point_id_to_view_point[view_point_id].attachments:
                assert not is_empty(attachment.url), "Attachment URL should not be empty."
                view_point_attachments.append(
                    CreateSiteViewPointAttachmentEntity(
                        mapping_id=view_point_id_to_view_point_mapping_id[view_point_id],
                        url=attachment.url,
                        name=attachment.name,
                        is_previous=False,
                        thumbnail_url=attachment.thumbnail_url,
                    )
                )

            current_view_point = next(filter(lambda x: x.id == view_point_id, current_view_points))

            for attachment in current_view_point.previous_attachments:
                assert not is_empty(attachment.url), "Attachment URL should not be empty."
                view_point_attachments.append(
                    CreateSiteViewPointAttachmentEntity(
                        mapping_id=view_point_id_to_view_point_mapping_id[view_point_id],
                        url=attachment.url,
                        name=attachment.name,
                        is_previous=True,
                        thumbnail_url=attachment.thumbnail_url,
                        uploaded_at=attachment.uploaded_at,
                    )
                )

        return view_point_attachments

    def process_view_point(self, report_id: int, data: list[CreateReportSiteViewPointInputEntity]):
        current_view_points = self.get_prefill_site_view_point_data()

        rename_view_point_data = self.rename_view_points(
            current_view_points=current_view_points, incoming_view_points=data
        )
        delete_view_point_data = self.delete_view_points(
            current_view_points=current_view_points, incoming_view_points=data
        )

        self.repo.bulk_modify_view_points(
            rename_view_point_data=rename_view_point_data,
            delete_view_point_data=delete_view_point_data,
        )

        create_attachments = self.add_view_points_with_attachments(
            report_id=report_id,
            incoming_create_view_points=[x for x in data if x.id is None],
        )

        update_attachments = self.update_view_points_with_attachments(
            report_id=report_id,
            incoming_update_view_points=[x for x in data if x.id is not None],
            current_view_points=current_view_points,
        )

        self.repo.bulk_create_view_point_attachments(
            data=create_attachments + update_attachments,
        )

    def process_manpower(self, report_id: int, data: list[CreateReportManPowerInputEntity]):
        self.repo.bulk_create_manpower(
            data=[
                CreateReportManPowerDataEntity(
                    name=manpower.name,
                    value=manpower.value,
                )
                for manpower in data
            ],
            report_id=report_id,
        )

    def add_daily_log_items_with_attachments(
        self,
        daily_log_type: WorkProgressDailyLogTypeEnum,
        data: CreateReportDailyLogUpdateInputEntity | None,
    ) -> AddDailyLogItemWithAttachmentEntity:
        if not data:
            return AddDailyLogItemWithAttachmentEntity(attachments=[], updates=[])

        update_entities: list[CreateReportItemEntity] = []
        for item in data.updates:
            meta_data = WorkProgressReportItemBlockHelper.get_meta_data_from_blocks(blocks=item)
            update_entities.append(
                CreateReportItemEntity(
                    blocks=item,
                    creation_type=WorkProgressCreationTypeEnum.MANUAL,
                    daily_log_type=daily_log_type,
                    category_ids=meta_data.category_id_list,
                    boq_element_ids=meta_data.boq_element_id_list,
                    activity_ids=meta_data.activity_id_list,
                )
            )

        attachment_entities: list[CreateReportAttachmentEntity] = []
        for attachment in data.attachments:
            attachment_entities.append(
                CreateReportAttachmentEntity(
                    url=attachment.url,
                    name=attachment.name,
                    thumbnail_url=attachment.thumbnail_url,
                    creation_type=WorkProgressCreationTypeEnum.MANUAL,
                    daily_log_type=daily_log_type,
                )
            )

        return AddDailyLogItemWithAttachmentEntity(
            attachments=attachment_entities,
            updates=update_entities,
        )

    def process_daily_log(self, data: CreateReportDailyLogInputEntity):
        today_entity = self.add_daily_log_items_with_attachments(
            daily_log_type=WorkProgressDailyLogTypeEnum.TODAY,
            data=data.today_update,
        )
        blocker_entity = self.add_daily_log_items_with_attachments(
            daily_log_type=WorkProgressDailyLogTypeEnum.BLOCKER,
            data=data.blocker,
        )
        tomorrow_plan_entity = self.add_daily_log_items_with_attachments(
            daily_log_type=WorkProgressDailyLogTypeEnum.TOMORROW,
            data=data.tomorrow_plan,
        )

        return AddDailyLogItemWithAttachmentEntity(
            attachments=today_entity.attachments + blocker_entity.attachments + tomorrow_plan_entity.attachments,
            updates=today_entity.updates + blocker_entity.updates + tomorrow_plan_entity.updates,
        )

    def process_scope_update(
        self,
        report_id: int,
        data: PrefillScopeUpdateEntity,
        attachments: CreateReportScopeUpdateInputEntity,
    ):
        section_updates = data.section_updates or []
        update_entities: list[CreateReportItemEntity] = []

        sections: list[CreateReportSectionEntity] = []
        for section in section_updates:
            sections.append(
                CreateReportSectionEntity(
                    name=section.name,
                    progress_percentage=section.progress_percentage,
                )
            )

        created_sections = self.repo.bulk_create_sections(
            report_id=report_id,
            data=sections,
        )

        section_name_to_id_mapping: dict[str, int] = {section.name: section.id for section in created_sections}

        attachment_entities: list[CreateReportAttachmentEntity] = []

        for section in section_updates:
            for item in section.updates:
                meta_data = WorkProgressReportItemBlockHelper.get_meta_data_from_blocks(blocks=item)
                update_entities.append(
                    CreateReportItemEntity(
                        blocks=item,
                        creation_type=WorkProgressCreationTypeEnum.DERIVED,
                        daily_log_type=WorkProgressDailyLogTypeEnum.TODAY,
                        category_ids=meta_data.category_id_list,
                        boq_element_ids=meta_data.boq_element_id_list,
                        activity_ids=meta_data.activity_id_list,
                        section_id=section_name_to_id_mapping[section.name],
                        boq_section_id=section.id if section.id != 0 else None,
                    )
                )

        # attachments will be sent by frontend
        for section in attachments.section_updates:
            for attachment in section.attachments:
                attachment_entities.append(
                    CreateReportAttachmentEntity(
                        url=attachment.url,
                        name=attachment.name,
                        thumbnail_url=attachment.thumbnail_url,
                        creation_type=WorkProgressCreationTypeEnum.DERIVED,
                        boq_section_id=section.id if section.id != 0 else None,
                        section_id=section_name_to_id_mapping[section.name],
                        element_name=attachment.element_name,
                    )
                )

        return AddDailyLogItemWithAttachmentEntity(
            attachments=attachment_entities,
            updates=update_entities,
        )

    def add_material_items(
        self,
        type: WorkProgressInventoryStockItemTypeEnum,
        data: list[PrefillMaterialUpdateElementEntity],
    ) -> list[MaterialItemEntity]:
        material_items: list[MaterialItemEntity] = []

        for item in data:
            material_items.append(
                MaterialItemEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                    type=type,
                    stock_value=item.stock_value,
                )
            )

        return material_items

    def process_material(self, report_id: int, data: PrefillMaterialUpdateEntity):
        material_items: list[MaterialItemEntity] = []

        material_items.extend(
            self.add_material_items(type=WorkProgressInventoryStockItemTypeEnum.RECEIVED, data=data.received)
        )
        material_items.extend(
            self.add_material_items(type=WorkProgressInventoryStockItemTypeEnum.CONSUMED, data=data.consumed)
        )
        material_items.extend(
            self.add_material_items(
                type=WorkProgressInventoryStockItemTypeEnum.TRANSFERRED_OUT, data=data.transferred_out
            )
        )

        self.repo.bulk_create_material_items(report_id=report_id, data=material_items)

    def process_schedule(
        self,
        report_id: int,
        data: PrefillScheduleUpdateEntity,
        attachments: list[CreateReportScheduleUpdateAttachmentInputEntity],
    ):
        schedule_id, created_activities = self.repo.create_schedule(report_id=report_id, data=data)

        schedule_activities_mapping: dict[int, int] = {
            activity.activity_id: activity.id for activity in created_activities
        }

        for attachment in attachments:
            if attachment.activity_id is None:
                continue

            created_activity_id = schedule_activities_mapping.get(attachment.activity_id, None)

            if created_activity_id is None:
                assert attachment.activity_id is not None, "Activity ID should be present in this case"

            attachment.activity_id = created_activity_id

        self.repo.bulk_create_schedule_attachments(schedule_id, attachments)

    def create_report(
        self,
        data: CreateReportInputEntity,
        material_data: PrefillMaterialUpdateEntity | None,
        schedule_data: PrefillScheduleUpdateEntity | None,
        scope_update_data: PrefillScopeUpdateEntity,
    ) -> int:
        logger.info(
            "Creating report",
            data=data,
            material_data=material_data,
            schedule_data=schedule_data,
            scope_update_data=scope_update_data,
        )

        process_daily_log_data = None
        process_scope_update_data = None

        total_manpower_count = sum([manpower.value for manpower in data.manpower_category_list])

        config_id = self.repo.get_latest_generate_report_config_id()

        report = self.repo.create_report(
            input_project_progress_percentage=(
                data.reported_project_progress.reported_progress_percentage
                if data.reported_project_progress
                and data.reported_project_progress.reported_progress_percentage is not None
                else scope_update_data.total_progress
            ),
            input_projected_end_date=(
                data.reported_project_progress.projected_end_date if data.reported_project_progress else None
            ),
            actual_progress_percentage=scope_update_data.total_progress,
            total_manpower_count=total_manpower_count,
            total_element_count=scope_update_data.total_element_count,
            today_progress_percentage=scope_update_data.today_progress,
            config_id=config_id,
        )

        if data.manpower_category_list:
            try:
                self.process_manpower(report_id=report.pk, data=data.manpower_category_list)
            except Exception:
                raise self.FailedToCreateManpowerCategory("Failed to create manpower category")

        if data.site_view_point_list is not None:
            self.process_view_point(report_id=report.pk, data=data.site_view_point_list)

        if data.daily_log:
            process_daily_log_data = self.process_daily_log(data=data.daily_log)

        if data.scope_update:
            process_scope_update_data = self.process_scope_update(
                report_id=report.pk,
                data=scope_update_data,
                attachments=data.scope_update,
            )

        total_attachments = (process_daily_log_data.attachments if process_daily_log_data else []) + (
            process_scope_update_data.attachments if process_scope_update_data else []
        )

        if total_attachments:
            self.repo.bulk_create_attachments(
                report_id=report.pk,
                data=total_attachments,
            )

        total_items = (process_daily_log_data.updates if process_daily_log_data else []) + (
            process_scope_update_data.updates if process_scope_update_data else []
        )

        if total_items:
            self.repo.bulk_create_items(
                report_id=report.pk,
                data=total_items,
            )

        if material_data:
            self.process_material(report_id=report.pk, data=material_data)

        if schedule_data:
            self.process_schedule(report_id=report.pk, data=schedule_data, attachments=data.schedule_update)

        return report.pk

    def get_report_list(self, filter_data: ReportListFilterDataEntity):
        data = self.repo.get_report_list_data(filter_data=filter_data)

        can_delete = self.permission_service.is_action_permitted(
            permission=Permissions.CAN_DELETE_PROGRESS_REPORT,
            permission_type=PermissionLevelEnum.ORG,
        )

        for report in data.data:
            if report.created_at and report.created_at < self.current_time - self.REPORT_DELETE_TIME:
                continue

            if report.created_by.id != self.user_entity.user_id and not can_delete:
                continue

            report.actions.append(WorkProgressReportDetailActionEnum.CAN_DELETE)
        return data

    def get_vendor_report_list(self, filter_data: VendorReportListFilterDataEntity):
        return self.repo.get_vendor_report_list_paginated_data(filter_data=filter_data)

    def create_project_manpower_category(self, name: str):
        logger.info("Creating project manpower category with name", name=name)

        try:
            return self.repo.create_project_manpower_category(name=name)
        except WorkProgressReportAbstractRepo.ProjectManpowerCategoryFieldValidationException:
            raise self.ProjectCategoryShouldNotBeEmpty("Project manpower category name should not be empty")
        except IntegrityError:
            raise self.ProjectCategoryNameAlreadyExists("Project manpower category name already exists")
        except Exception:
            raise self.FailedToCreateProjectManpowerCategory("Failed to create project manpower category")

    def get_report_created_by_list(self):
        return self.repo.get_report_created_by_list()

    def get_vendor_report_created_by_list(self, filter_data: VendorReportCreatedByListFilterDataEntity):
        return self.repo.get_vendor_report_created_by_list(filter_data=filter_data)

    def delete_report(self, report_id: int):
        try:
            report = self.repo.get_report_data(report_id=report_id)

            can_delete = self.permission_service.is_action_permitted(
                permission=Permissions.CAN_DELETE_PROGRESS_REPORT,
                permission_type=PermissionLevelEnum.ORG,
            )

            logger.info("Deleting report", report_id=report_id, report=report)

            if report.deleted_at:
                raise self.ReportAlreadyDeleted("Report already deleted")

            # Check if the report is created 24 hours ago
            if report.created_at and report.created_at < self.current_time - self.REPORT_DELETE_TIME:
                raise self.ReportIsLocked("Report is locked")

            if report.created_by_id != self.user_entity.user_id and not can_delete:
                raise self.ReportIsNotCreatedByUser("Report is not created by user")

            self.repo.delete_report(report_id=report_id)
        except WorkProgressReportAbstractRepo.ReportDoesNotExistException:
            raise self.ReportDoesNotExist("Report does not exist")


class WorkProgressReportInteractor:
    class Exception(BaseValidationError):
        pass

    class ProjectDoesNotExist(Exception):
        pass

    class ProjectCategoryAlreadyExists(Exception):
        pass

    class ProjectCategoryCreationFailed(Exception):
        pass

    class ProjectCategoryShouldNotBeEmpty(Exception):
        pass

    class FailedToCreateReport(Exception):
        pass

    class ReportIsLocked(Exception):
        pass

    class ReportIsNotCreatedByUser(Exception):
        pass

    class ReportDoesNotExist(Exception):
        pass

    class ReportAlreadyDeleted(Exception):
        pass

    def __init__(
        self,
        user_entity: ProjectUserEntity,
        service: WorkProgressReportService,
        config_service: WorkProgressProjectConfigService,
        permission_service: WorkProgressPermissionService,
    ):
        self.user_entity = user_entity
        self.service = service
        self.config_service = config_service
        self.scope_data_helper = WorkProgressScopeDataHelper()
        self.permission_service = permission_service

    def get_hashtag_list_data(self, schedule_service: WorkProgressToProjectScheduleService) -> HashtagListDomainEntity:
        elements = self.service.get_hashtag_element_list()
        categories = self.service.get_hashtag_category_list()
        activities = schedule_service.get_hashtag_activity_list()

        return HashtagListDomainEntity(elements=elements, categories=categories, activities=activities)

    def get_manpower_category_list_data(self) -> list[str]:
        manpower_list = self.service.get_manpower_category_list()
        return sorted(manpower_list, key=lambda x: x.lower())

    def get_prefill_site_view_point_data(self):
        return self.service.get_prefill_site_view_point_data()

    def get_prefill_reported_project_progress_data(self):
        return self.service.get_prefill_reported_project_progress_data()

    def get_prefill_scope_summary_data(self, scope_service: WorkProgressScopeDataService):
        repo = scope_service.get_repo()
        elements = repo.get_elements_for_scope_data()
        (
            section_today_progress_data,
            section_total_progress_data,
        ) = scope_service.segregate_section_today_and_total_progress_data(elements=elements)

        section_total_progress = self.scope_data_helper.get_section_total_progress(elements=section_total_progress_data)
        today_progress_data = self.scope_data_helper.get_today_progress(elements=section_today_progress_data)
        total_progress_data = self.scope_data_helper.get_total_progress(elements=section_total_progress_data)

        return PrefillScopeSummaryEntity(
            section_total_progress=section_total_progress,
            total_progress_data=total_progress_data,
            today_progress_data=today_progress_data,
        )

    def get_prefill_scope_update_data(self, scope_service: WorkProgressScopeDataService):
        can_show_scope_updates = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE
        )

        scope_summary_data = self.get_prefill_scope_summary_data(scope_service=scope_service)

        scope_updates = None

        if can_show_scope_updates:
            scope_updates = self.service.get_prefill_scope_update_data(
                section_total_progress=scope_summary_data.section_total_progress
            )

        return PrefillScopeUpdateEntity(
            total_progress=scope_summary_data.total_progress_data.progress_percentage,
            today_progress=scope_summary_data.today_progress_data.progress_percentage,
            total_element_count=scope_summary_data.total_progress_data.total_element_count,
            section_updates=scope_updates,
        )

    def get_prefill_schedule_update_data(self, schedule_service: WorkProgressToProjectScheduleService):
        data = schedule_service.get_today_schedule_update_data()
        if data is None:
            return None

        activity_update = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE
        )
        schedule_summary = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY
        )
        activity_update_with_status = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS
        )

        exclude_fields = {}

        if not schedule_summary:
            exclude_fields["total_progress"] = True
            exclude_fields["today_progress"] = True
            exclude_fields["status"] = True
            exclude_fields["planned_end_date"] = True

        if not activity_update:
            exclude_fields["activities"] = True
            exclude_fields["attachments"] = True

        if not activity_update_with_status and activity_update:
            exclude_fields["activities"] = {"__all__": {"status"}}

        return PrefillScheduleUpdateEntity(**data.model_dump(exclude=exclude_fields))

    def get_prefill_daily_log_data(self):
        return self.service.get_prefill_daily_log_data()

    def get_prefill_material_update_data(self, inventory_service: InventoryWorkProgressService):
        today_material_update_data = inventory_service.get_today_material_update_data()

        return PrefillMaterialUpdateEntity(
            received=[
                PrefillMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                    stock_value=item.stock_value,
                )
                for item in today_material_update_data.received
            ],
            consumed=[
                PrefillMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                    stock_value=item.stock_value,
                )
                for item in today_material_update_data.consumed
            ],
            transferred_out=[
                PrefillMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                    stock_value=item.stock_value,
                )
                for item in today_material_update_data.transferred_out
            ],
        )

    def get_prefill_manpower_data(self):
        return self.service.get_prefill_manpower_data()

    def validate_create_report_data(
        self,
        data: CreateReportInputEntity,
        schedule_data: PrefillScheduleUpdateEntity | None,
        scope_update_data: PrefillScopeUpdateEntity,
        material_data: PrefillMaterialUpdateEntity | None,
    ):
        has_daily_log_data = 0
        has_automatic_data = 0

        if data.daily_log:
            has_daily_log_data += (
                len(data.daily_log.today_update.attachments) + len(data.daily_log.today_update.updates)
                if data.daily_log.today_update
                else 0
            )
            has_daily_log_data += (
                len(data.daily_log.blocker.attachments) + len(data.daily_log.blocker.updates)
                if data.daily_log.blocker
                else 0
            )
            has_daily_log_data += (
                len(data.daily_log.tomorrow_plan.attachments) + len(data.daily_log.tomorrow_plan.updates)
                if data.daily_log.tomorrow_plan
                else 0
            )

            if data.reported_project_progress:
                has_daily_log_data += (
                    1
                    if (
                        data.reported_project_progress.reported_progress_percentage
                        and data.reported_project_progress.reported_progress_percentage > 0
                    )
                    else 0
                )

                has_daily_log_data += 1 if data.reported_project_progress.projected_end_date else 0

            has_daily_log_data += len(data.manpower_category_list) + len(data.site_view_point_list or [])

        if schedule_data:
            has_automatic_data += len(schedule_data.attachments or []) + len(schedule_data.activities or [])

        if scope_update_data.section_updates:
            has_automatic_data += len(scope_update_data.section_updates)

        if material_data:
            has_automatic_data += (
                len(material_data.received) + len(material_data.consumed) + len(material_data.transferred_out)
            )

        if has_daily_log_data > 0 or has_automatic_data > 0:
            return

        raise self.FailedToCreateReport("Please add at least one item to the report")

    def create_report(
        self,
        data: CreateReportInputEntity,
        scope_service: WorkProgressScopeDataService,
        inventory_service: InventoryWorkProgressService,
        schedule_service: WorkProgressToProjectScheduleService,
    ):
        material_module_enabled = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.MATERIAL_UPDATE
        )
        schedule_module_enabled = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE
        )
        site_view_point_module_enabled = self.config_service.is_module_enabled(
            module_key=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT
        )

        material_data = None
        schedule_data = None

        if material_module_enabled:
            material_data = self.get_prefill_material_update_data(inventory_service=inventory_service)

        if schedule_module_enabled:
            schedule_data = self.get_prefill_schedule_update_data(schedule_service=schedule_service)

        if not site_view_point_module_enabled:
            data.site_view_point_list = None

        scope_update_data = self.get_prefill_scope_update_data(scope_service=scope_service)

        self.validate_create_report_data(
            data=data,
            schedule_data=schedule_data,
            scope_update_data=scope_update_data,
            material_data=material_data,
        )

        report_id = self.service.create_report(
            data=data,
            material_data=material_data,
            schedule_data=schedule_data,
            scope_update_data=scope_update_data,
        )

        trigger_work_progress_report_generated(
            report_id=report_id,
            user_entity=self.service.user_entity,
            should_notify=True,
        )

    def get_report_list(self, filter_data: ReportListFilterDataEntity):
        return self.service.get_report_list(filter_data=filter_data)

    def get_vendor_report_list(self, filter_data: VendorReportListFilterDataEntity):
        return self.service.get_vendor_report_list(filter_data=filter_data)

    def create_project_manpower_category(self, name: str):
        already_existing_categories = self.get_manpower_category_list_data()

        for category in already_existing_categories:
            if category.lower() == name.lower():
                raise self.ProjectCategoryAlreadyExists("Category name already exists")

        try:
            return self.service.create_project_manpower_category(name=name)
        except WorkProgressReportService.ProjectCategoryShouldNotBeEmpty:
            raise self.ProjectCategoryShouldNotBeEmpty("Category name should not be empty")
        except WorkProgressReportService.ProjectCategoryNameAlreadyExists:
            raise self.ProjectCategoryAlreadyExists("Category name already exists")
        except WorkProgressReportService.FailedToCreateProjectManpowerCategory:
            raise self.ProjectCategoryCreationFailed("Failed to create project manpower category")

    def get_report_created_by_list(self):
        return self.service.get_report_created_by_list()

    def get_vendor_report_created_by_list(self, filter_data: VendorReportCreatedByListFilterDataEntity):
        return self.service.get_vendor_report_created_by_list(filter_data=filter_data)

    def get_report_config(self) -> GenerateReportConfigEntity:
        checked_config_ids = self.config_service.get_config_checked_ids(
            type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT
        )
        can_delete_attachments = self.permission_service.is_action_permitted(
            permission=Permissions.CAN_DELETE_FILE_IN_DPR,
            permission_type=PermissionLevelEnum.PROJECT,
        )
        can_upload_from_gallery = self.permission_service.is_action_permitted(
            permission=Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR,
            permission_type=PermissionLevelEnum.PROJECT,
        )

        actions: list[WorkProgressReportActionEnum] = []

        if can_delete_attachments:
            actions.append(WorkProgressReportActionEnum.CAN_DELETE_ATTACHMENT)

        if can_upload_from_gallery:
            actions.append(WorkProgressReportActionEnum.CAN_UPLOAD_FROM_GALLERY_ATTACHMENT)

        return GenerateReportConfigEntity(checked_ids=checked_config_ids, actions=actions)

    def delete_report(self, report_id: int):
        try:
            self.service.delete_report(report_id=report_id)
        except WorkProgressReportService.ReportAlreadyDeleted:
            raise self.ReportAlreadyDeleted("Report already deleted")
        except WorkProgressReportService.ReportDoesNotExist:
            raise self.ReportDoesNotExist("Report does not exist")
        except WorkProgressReportService.ReportIsLocked:
            raise self.ReportIsLocked("Report cannot be deleted after 24 hours")
        except WorkProgressReportService.ReportIsNotCreatedByUser:
            raise self.ReportIsNotCreatedByUser("Report is created by another user")
