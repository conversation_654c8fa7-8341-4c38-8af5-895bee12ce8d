import structlog

from boq.data.choices import BoqElementStatus
from common.exceptions import BaseValidationError
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.data.choices import ElementUpdatedAttributeChoices, WorkProgressElementActionChoices
from work_progress_v2.data.entities import (
    HistoryTimelineDataEntity,
    ItemTypeConfigWithMilestonesDataEntity,
    MilestoneDataEntity,
)
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo, WorkProgressVendorScopeElementAbstractRepo
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import (
    DefaultItemTypeConfigEntity,
    ElementDetailEntity,
    ElementHistoryTimelineEntity,
    ElementListFilterEntity,
    HistoryHeaderEntity,
    ItemTypeConfigEntity,
    <PERSON>ginatedElementListEntity,
    PaginatedVendorScopeElementListEntity,
    UserEntity,
    VendorScopeElementListFilterEntity,
)
from work_progress_v2.domain.services.locking import WorkProgressLockingService
from work_progress_v2.domain.services.prepare import WorkProgressPrepareService

logger = structlog.get_logger(__name__)


class WorkProgressElementService(WorkProgressPrepareService):
    class Exception(BaseValidationError):
        pass

    class ElementNotFoundException(Exception):
        pass

    def __init__(
        self,
        repo: WorkProgressAbstractRepo,
        user_entity: ProjectUserEntity,
        locking_service: WorkProgressLockingService,
        cache: WorkProgressCache,
    ):
        self.repo = repo
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id
        self.locking_service = locking_service
        self.cache = cache

    def get_categories(self):
        categories = self.repo.get_element_categories()
        return sorted(categories, key=lambda x: x.name.lower())

    def get_statuses(self):
        status_list = self.repo.get_element_statuses()

        status_order: dict[BoqElementStatus, int] = {
            BoqElementStatus(status): index for index, status in enumerate(BoqElementStatus.values)
        }

        return sorted(
            status_list, key=lambda x: status_order.get(BoqElementStatus(x.value), len(BoqElementStatus.values))
        )

    def get_uoms(self):
        uoms = self.repo.get_element_uoms()
        return sorted(uoms, key=lambda x: x.name.lower())

    def get_item_types(self):
        item_types = self.repo.get_element_item_types()
        return sorted(item_types, key=lambda x: x.name.lower())

    def get_elements(self, filter_data: ElementListFilterEntity) -> PaginatedElementListEntity:
        paginated_element_data = self.repo.get_work_progress_elements_paginated_data(filter_data=filter_data)
        item_type_configs = self.cache.get_item_type_config()

        return PaginatedElementListEntity(
            count=paginated_element_data.count,
            data=[
                self._prepare_element_list_detail(element=element, item_type_configs=item_type_configs)
                for element in paginated_element_data.data
            ],
        )

    def get_element_detail(self, element_id: int) -> ElementDetailEntity:
        try:
            data = self.repo.get_element_detail_data(element_id=element_id)
            item_type_configs = self.cache.get_item_type_config()

            logger.info("Element detail data", data=data)

            return self._prepare_element_detail(data, item_type_configs)
        except WorkProgressElement.DoesNotExist:
            raise self.ElementNotFoundException("Element does not exist")

    def _get_action_header(self, history: HistoryTimelineDataEntity) -> HistoryHeaderEntity:
        if history.action == WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED:
            return HistoryHeaderEntity(text="Item Modified", color_code="#EA9245")
        else:
            return HistoryHeaderEntity(text="Item Progress Updated", color_code="#61C7CD")

    def _get_action_title(self, history: HistoryTimelineDataEntity) -> str:
        if history.action in [
            WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED,
            WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED,
        ]:
            return f"Progress updated to: {history.data.updated_data}"
        elif history.action == WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED:
            return f"Progress updated to: {history.data.updated_data} %"
        elif history.action == WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED:
            if history.data.previous_data:
                return f"{ElementUpdatedAttributeChoices(history.data.updated_attribute).label} modified from: {ItemTypeUpdateMethodChoices(history.data.previous_data).label} to {ItemTypeUpdateMethodChoices(history.data.updated_data).label}"  # noqa
            else:
                return f"{ElementUpdatedAttributeChoices(history.data.updated_attribute).label} modified to: {ItemTypeUpdateMethodChoices(history.data.updated_data).label}"  # noqa
        else:
            if history.data.previous_data:
                return f"{ElementUpdatedAttributeChoices(history.data.updated_attribute).label} modified from: {history.data.previous_data} to {history.data.updated_data}"  # noqa
            else:
                return f"{ElementUpdatedAttributeChoices(history.data.updated_attribute).label} modified to: {history.data.updated_data}"  # noqa

    def _prepare_history_data(
        self, history_data: list[HistoryTimelineDataEntity]
    ) -> list[ElementHistoryTimelineEntity]:
        history_data_list = []
        for history in history_data:
            history_data_list.append(
                ElementHistoryTimelineEntity(
                    id=history.id,
                    header=self._get_action_header(history),
                    title=self._get_action_title(history),
                    updated_at=history.created_at,
                    updated_by=UserEntity(
                        id=history.created_by.id, name=history.created_by.name, photo=history.created_by.photo
                    ),
                )
            )
        return history_data_list

    def get_element_history(self, element_id: int) -> list[ElementHistoryTimelineEntity]:
        history_data = self.repo.get_element_history_data(element_id)
        return self._prepare_history_data(history_data)

    def get_element_preview_files(self, element_id: int):
        try:
            return self.repo.get_element_preview_files(element_id)
        except WorkProgressElement.DoesNotExist:
            raise self.ElementNotFoundException("Element does not exist")


class WorkProgressElementInteractor:
    class Exception(BaseValidationError):
        pass

    class ElementNotFoundException(Exception):
        pass

    def __init__(self, service: WorkProgressElementService, cache: WorkProgressCache):
        self.service = service
        self.cache = cache

    def get_categories(self):
        return self.service.get_categories()

    def get_statuses(self):
        return self.service.get_statuses()

    def get_uoms(self):
        return self.service.get_uoms()

    def get_item_types(self):
        return self.service.get_item_types()

    def get_elements(self, filter_data) -> PaginatedElementListEntity:
        return self.service.get_elements(filter_data)

    def get_element_detail(self, element_id: int) -> ElementDetailEntity:
        try:
            return self.service.get_element_detail(element_id)
        except WorkProgressElementService.ElementNotFoundException:
            raise self.ElementNotFoundException("Element does not exist")

    def get_element_history(self, element_id: int):
        return self.service.get_element_history(element_id)

    def get_element_preview_files(self, element_id: int):
        try:
            return self.service.get_element_preview_files(element_id)
        except WorkProgressElementService.ElementNotFoundException:
            raise self.ElementNotFoundException("Element does not exist")

    def get_item_type_config(self):
        item_types = self.service.get_item_types()

        item_type_config = self.cache.get_item_type_config()

        config_entities: list[ItemTypeConfigWithMilestonesDataEntity] = []

        for item_type in item_types:
            cache_item_type = item_type_config.item_types.get(item_type.id)

            assert cache_item_type is not None, "Item type config not found in cache"

            config_entities.append(
                ItemTypeConfigWithMilestonesDataEntity(
                    id=cache_item_type.id,
                    name=cache_item_type.name,
                    default_update_method=cache_item_type.default_update_method,
                    update_methods=cache_item_type.allowed_update_methods,
                    milestones=[
                        MilestoneDataEntity(
                            id=milestone.id,
                            name=milestone.name,
                            percentage=milestone.percentage,
                            is_visible=milestone.is_visible,
                        )
                        for milestone in cache_item_type.milestones
                    ],
                )
            )

        return ItemTypeConfigEntity(
            config=config_entities,
            default_config=DefaultItemTypeConfigEntity(
                update_methods=item_type_config.default_config.allowed_update_methods,
                default_update_method=item_type_config.default_config.default_update_method,
            ),
        )


class WorkProgressVendorScopeElementService(WorkProgressElementService):
    def __init__(
        self,
        repo: WorkProgressVendorScopeElementAbstractRepo,
        user_entity: ProjectUserEntity,
        locking_service: WorkProgressLockingService,
        vendor_ids: list[int],
        cache: WorkProgressCache,
    ):
        super().__init__(repo=repo, user_entity=user_entity, locking_service=locking_service, cache=cache)
        self.repo = repo
        self.vendor_ids = vendor_ids

    def get_element_paginated_list(
        self, filter_data: VendorScopeElementListFilterEntity
    ) -> PaginatedVendorScopeElementListEntity:
        return self.repo.get_element_paginated_list(filter_data=filter_data)
