import datetime

from django.utils import timezone

from common.utils import get_local_time


class WorkProgressLockingService:
    """
    Service class to check if the progress is locked or not.

    When progress is reset
    1. Case: When uom_id is changed.
    2. Case: When item_type_id is changed.
    3. Case: When update_method is changed.

    When progress is unlocked
    1. Case: When quantity is changed.

    """

    def __init__(self):
        self.current_day = get_local_time(timezone.now()).date()

    def is_reset_progress(
        self,
        uom_changed: bool | None,
        item_type_changed: bool | None,
        update_method_changed: bool | None,
    ) -> bool:
        """
        Check if the progress is reset or not.

        Args:
            uom_changed (bool): True if uom is changed.
            item_type_changed (bool): True if item_type is changed.
            update_method_changed (bool): True if update_method is changed.

        """

        if uom_changed or item_type_changed or update_method_changed:
            return True

        return False

    def is_locked(
        self,
        unlocked_at: datetime.datetime | None,
        progress_updated_at: datetime.datetime | None,
        last_day_progress_updated_at: datetime.datetime | None,
    ) -> bool:
        """
        Check if the progress is locked or not.

        Args:
            unlocked_at: The date and time when the progress was unlocked.
            progress_updated_at: The date and time when the progress was updated.
            last_day_progress_updated_at: The date and time when the progress was updated on last day.

        """  # noqa

        if last_day_progress_updated_at and self.current_day == get_local_time(last_day_progress_updated_at).date():
            raise ValueError("last_day_progress_updated_at should never be equal to current_day")

        # Case: No progress is updated yet, then it is unlocked today.
        if progress_updated_at is None:
            return False

        # Case: If current day is greater than progress_updated_at,
        # then we assign the progress_updated_at to last_day_progress_updated_at.
        # This case is added when no progress is updated on next day.
        if self.current_day > get_local_time(progress_updated_at).date():
            last_day_progress_updated_at = progress_updated_at

        # Case: If there is no progress updated on last day, then it is unlocked today.
        if last_day_progress_updated_at is None:
            return False

        if unlocked_at is None:
            if last_day_progress_updated_at is not None:
                return True

            return False

        # Case: If progress is unlocked today, then it is unlocked today.
        if get_local_time(unlocked_at).date() == self.current_day:
            return False

        # Case: If progress is updated before unlocked_at, then it is unlocked today.
        if get_local_time(progress_updated_at).date() < get_local_time(unlocked_at).date():
            return False

        if get_local_time(last_day_progress_updated_at).date() < get_local_time(unlocked_at).date():
            return False

        return True
