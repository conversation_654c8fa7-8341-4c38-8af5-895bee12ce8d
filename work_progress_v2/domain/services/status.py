import structlog

from boq.data.models import StatusChoices
from common.exceptions import BaseValidationError
from core.entities import ProjectUserEntity
from project.domain.status import WorkReportStatus
from project.interface.external.work_progress import WorkProgressToProjectService
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo
from work_progress_v2.domain.triggers import trigger_mark_execution_completed

logger = structlog.get_logger(__name__)


class WorkProgressStatusService:
    class Exception(BaseValidationError):
        pass

    class WorkProgressInExecutionCompletedException(Exception):
        pass

    class AllElementNot100PercentException(Exception):
        pass

    def __init__(
        self,
        repo: WorkProgressAbstractRepo,
        user_entity: ProjectUserEntity,
        project_service: WorkProgressToProjectService,
    ):
        self.repo = repo
        self.user_entity = user_entity
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id
        self.user_id = user_entity.user_id
        self.project_service = project_service
        self.is_all_elements_100_percent = None
        self.work_progress_status = None

    def get_project_work_report_status(self) -> WorkReportStatus:
        if self.work_progress_status:
            return self.work_progress_status

        self.work_progress_status = self.project_service.get_work_progress_status()
        logger.info("Project status", project_work_report_status=self.work_progress_status)
        return self.work_progress_status

    def check_project_status_in_execution_completed(self):
        if self.get_project_work_report_status() == WorkReportStatus.EXECUTION_COMPLETED:
            logger.info("Project status is in execution completed state")
            raise self.WorkProgressInExecutionCompletedException("Project status is in execution completed state")

    def check_all_elements_are_100_percent(self):
        if self.is_all_elements_100_percent is None:
            self.is_all_elements_100_percent = self.repo.check_all_elements_are_100_percent()
        return self.is_all_elements_100_percent

    def update_work_report_status(self):
        is_all_100_percent = self.check_all_elements_are_100_percent()

        if is_all_100_percent:
            status = StatusChoices.COMPLETED
        else:
            status = StatusChoices.IN_PROGRESS

        self.repo.update_work_report_status(status=status)

    def update_project_status(self, is_vendor: bool):
        is_all_100_percent = self.check_all_elements_are_100_percent()

        if not is_all_100_percent and self.get_project_work_report_status() in [
            WorkReportStatus.NOT_STARTED,
            WorkReportStatus.EXECUTION_COMPLETED,
        ]:
            self.project_service.update_project_status(
                status=WorkReportStatus.EXECUTION_IN_PROGRESS,
                is_vendor=is_vendor,
            )

    def update_work_progress_to_mark_complete(self, update_project_status: bool, is_vendor: bool):
        logger.info(
            "Update work progress to mark complete",
            update_project_status=update_project_status,
            is_vendor=is_vendor,
        )

        self.check_project_status_in_execution_completed()

        is_all_100_percent = self.check_all_elements_are_100_percent()

        if not is_all_100_percent:
            raise self.AllElementNot100PercentException("All elements are not 100 percent")

        self.project_service.update_project_status(
            status=WorkReportStatus.EXECUTION_COMPLETED,
            should_update_rectification_status=update_project_status,
            is_vendor=is_vendor,
        )

        trigger_mark_execution_completed(user_entity=self.user_entity)

    def update_work_progress_to_mark_incomplete(self, is_vendor: bool):
        logger.info("Update work progress to mark incomplete", is_vendor=is_vendor)

        self.project_service.update_project_status(
            status=WorkReportStatus.EXECUTION_IN_PROGRESS,
            is_vendor=is_vendor,
        )
