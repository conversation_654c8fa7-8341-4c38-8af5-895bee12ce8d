from element.data.choices import ItemTypeUpdateMethodChoices
from project.domain.entities import ItemTypeConfigCacheEntity
from work_progress_v2.data.entities import ElementDetailDataEntity, ElementListDataEntity
from work_progress_v2.domain.entities import (
    ElementAttachmentEntity,
    ElementCategoryEntity,
    ElementDetailEntity,
    ElementItemTypeEntity,
    ElementListEntity,
    ElementPreviewFileEntity,
    ElementSectionEntity,
    ElementStatusEntity,
    ElementUomEntity,
    UpdateElementOutputDataEntity,
    UpdateElementOutputEntity,
)
from work_progress_v2.domain.services.locking import WorkProgressLockingService


class WorkProgressPrepareService:
    org_id: int
    locking_service: WorkProgressLockingService

    def get_default_update_method(
        self,
        item_type_configs: ItemTypeConfigCacheEntity,
        update_method: str | None,
        item_type_id: int | None,
    ) -> ItemTypeUpdateMethodChoices:
        if update_method:
            return ItemTypeUpdateMethodChoices(update_method)

        if item_type_id:
            element_item_type_config = item_type_configs.item_types.get(item_type_id)

            assert element_item_type_config is not None, "Item type config not found in cache"

            return ItemTypeUpdateMethodChoices(element_item_type_config.default_update_method)
        else:
            return ItemTypeUpdateMethodChoices(item_type_configs.default_config.default_update_method)

    def can_change_update_method(self, created_by_org_id: int) -> bool:
        if created_by_org_id == self.org_id:
            return True
        return False

    def _prepare_element_detail(
        self,
        element: ElementDetailDataEntity,
        item_type_configs: ItemTypeConfigCacheEntity,
    ) -> ElementDetailEntity:
        update_method = self.get_default_update_method(
            item_type_configs=item_type_configs,
            update_method=element.update_method,
            item_type_id=element.item_type.id if element.item_type else None,
        )

        return ElementDetailEntity(
            id=element.id,
            name=element.name,
            code=element.code,
            description=element.description,
            category=(
                ElementCategoryEntity(
                    id=element.category.id,
                    name=element.category.name,
                    code=element.category.code,
                )
                if element.category
                else None
            ),
            item_type=(
                ElementItemTypeEntity(
                    id=element.item_type.id,
                    name=element.item_type.name,
                    color_code=element.item_type.color_code,
                )
                if element.item_type
                else None
            ),
            uom=ElementUomEntity(
                name=element.uom.name,
                value=element.uom.value,
            ),
            quantity=element.quantity,
            status=ElementStatusEntity(
                name=element.status.name,
                value=element.status.value,
            ),
            section=ElementSectionEntity(
                id=element.section.id,
                name=element.section.name,
            ),
            rate=element.rate,
            current_input_quantity=element.current_input_quantity,
            current_input_progress=element.current_input_progress,
            actual_progress_percentage=element.actual_progress_percentage,
            can_change_update_method=self.can_change_update_method(created_by_org_id=element.created_by_org_id),
            is_locked=self.locking_service.is_locked(
                unlocked_at=element.unlocked_at,
                progress_updated_at=element.progress_updated_at,
                last_day_progress_updated_at=element.last_day_progress_updated_at,
            ),
            update_method=update_method,
            comment_count=element.comment_count,
            current_input_milestone_id=element.current_input_milestone_id,
            progress_updated_at=element.progress_updated_at,
            previous_day_input_progress=element.previous_day_input_progress,
            previous_day_input_quantity=element.previous_day_input_quantity,
            previous_day_input_milestone_id=element.previous_day_input_milestone_id,
            preview_files=[
                ElementPreviewFileEntity(
                    id=preview_file.id,
                    file=preview_file.file,
                    name=preview_file.name,
                    is_main=preview_file.is_main,
                    type=preview_file.type,
                )
                for preview_file in element.preview_files
            ],
            attachments=[
                ElementAttachmentEntity(
                    id=attachment.id,
                    name=attachment.name,
                    file=attachment.file,
                    uploaded_at=attachment.uploaded_at,
                    actions=attachment.actions,
                )
                for attachment in element.attachments
            ],
        )

    def _prepare_element_list_detail(
        self,
        element: ElementListDataEntity,
        item_type_configs: ItemTypeConfigCacheEntity,
    ) -> ElementListEntity:
        update_method = self.get_default_update_method(
            item_type_configs=item_type_configs,
            update_method=element.update_method,
            item_type_id=element.item_type.id if element.item_type else None,
        )

        current_input_milestone_name = None
        item_type_config = item_type_configs.item_types.get(element.item_type.id) if element.item_type else None
        milestone_data_map = (
            {milestone.id: milestone for milestone in item_type_config.milestones} if item_type_config else {}
        )
        if (
            item_type_config
            and element.current_input_milestone_id
            and element.current_input_milestone_id in item_type_config.milestones
        ):
            current_input_milestone_name = milestone_data_map[element.current_input_milestone_id].name

        return ElementListEntity(
            id=element.id,
            name=element.name,
            code=element.code,
            description=element.description,
            category=(
                ElementCategoryEntity(
                    id=element.category.id,
                    name=element.category.name,
                    code=element.category.code,
                )
                if element.category
                else None
            ),
            item_type=(
                ElementItemTypeEntity(
                    id=element.item_type.id,
                    name=element.item_type.name,
                    color_code=element.item_type.color_code,
                )
                if element.item_type
                else None
            ),
            uom=ElementUomEntity(
                name=element.uom.name,
                value=element.uom.value,
            ),
            quantity=element.quantity,
            status=ElementStatusEntity(
                name=element.status.name,
                value=element.status.value,
            ),
            section=ElementSectionEntity(
                id=element.section.id,
                name=element.section.name,
            ),
            rate=element.rate,
            current_input_quantity=element.current_input_quantity,
            current_input_progress=element.current_input_progress,
            actual_progress_percentage=element.actual_progress_percentage,
            can_change_update_method=self.can_change_update_method(created_by_org_id=element.created_by_org_id),
            is_locked=self.locking_service.is_locked(
                unlocked_at=element.unlocked_at,
                progress_updated_at=element.progress_updated_at,
                last_day_progress_updated_at=element.last_day_progress_updated_at,
            ),
            update_method=update_method,
            comment_count=element.comment_count,
            current_input_milestone_id=element.current_input_milestone_id,
            progress_updated_at=element.progress_updated_at,
            previous_day_input_progress=element.previous_day_input_progress,
            previous_day_input_quantity=element.previous_day_input_quantity,
            previous_day_input_milestone_id=element.previous_day_input_milestone_id,
            preview_file=(
                ElementPreviewFileEntity(
                    id=element.preview_file.id,
                    file=element.preview_file.file,
                    name=element.preview_file.name,
                    is_main=element.preview_file.is_main,
                    type=element.preview_file.type,
                )
                if element.preview_file
                else None
            ),
            current_input_milestone_name=current_input_milestone_name,
        )

    def _prepare_element_daywise_update_entities(
        self,
        updated_data: list[UpdateElementOutputDataEntity],
        item_type_configs: ItemTypeConfigCacheEntity,
    ) -> list[UpdateElementOutputEntity]:
        return [
            UpdateElementOutputEntity(
                updated_field=data.updated_field,
                updated_element_entity=self._prepare_element_list_detail(
                    element=data.updated_element_entity,
                    item_type_configs=item_type_configs,
                ),
                completion_amount_diff=data.completion_amount_diff,
                is_updated_today=data.is_updated_today,
                section_id=data.section_id,
                progress_percentage_diff=data.progress_percentage_diff,
                timeline_id=data.timeline_id,  # type: ignore[assignment]
            )
            for data in updated_data
        ]
