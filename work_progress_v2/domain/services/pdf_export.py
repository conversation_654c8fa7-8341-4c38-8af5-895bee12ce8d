import datetime
import decimal
from typing import Dict

import structlog
from django.utils import timezone

from common.exceptions import BaseValidationError
from common.pydantic.base_model import WorkProgressBlockBaseModel
from common.utils import get_local_time, get_utc_time
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from inventory.data.entities import InventoryWorkProgressUpdatedDataEntity
from inventory.domain.services import InventoryWorkProgressService
from microcontext.domain.constants import MicroContext
from order.data.selectors.selector_v1 import get_vendor_name
from project_schedule.domain.entities import TodayScheduleDataEntity
from project_schedule.interface.external.work_progress import WorkProgressToProjectScheduleService
from report.download.domain.enums import DownloadProgressStatusEnum
from report.download.domain.service import DownloadService
from work_progress_v2.data.report_entities import (
    ElementDayWiseDataEntity,
    ProjectOrgDaywiseDataEntity,
    ReportPDFDataEntity,
    SectionDayWiseDataEntity,
)
from work_progress_v2.domain.abstract_repos import WorkProgressReportPDFAbstractRepo
from work_progress_v2.domain.entities import (
    ExportReportInputEntity,
    ExportReportPdfOutputEntity,
    ReportConfigEntity,
)
from work_progress_v2.domain.enums import WorkProgressConfigReportTypeEnum, WorkProgressReportConfigIdEnum
from work_progress_v2.domain.report_entities import (
    ExportReportCumulativeEntity,
    ExportReportEntity,
    ExportReportPdfEntity,
    ExportReportPDFManpowerCategoryEntity,
    ExportREportPDFManpowerDateWiseEntity,
    ExportReportPDFManpowerEntity,
    ExportReportPDFMaterialEntity,
    ExportReportPDFScheduleEntity,
    ExportReportPDFScopeUpdateEntity,
    ExportReportPDFScopeUpdateSectionEntity,
    ExportReportPDFSiteViewPointEntity,
    ExportReportPDFStockEntity,
    ExportScheduleActivityEntity,
    PreviousReportDataEntity,
    ReportDetailAttachmentEntity,
    ReportDetailAttachmentWithElementEntity,
    ReportDetailCreatedByEntity,
    ReportDetailDayWiseDataWithScopeElementDataEntity,
    ReportDetailDayWiseEntity,
    ReportDetailMaterialUpdateElementEntity,
    ReportDetailMaterialUpdateEntity,
    ReportDetailReportedProjectProgressEntity,
    ReportDetailScheduleUpdateActivityEntity,
    ReportDetailScheduleUpdateActivityStatusEntity,
    ReportDetailScheduleUpdateEntity,
    ReportDetailScopeUpdateEntity,
    ReportDetailScopeUpdateSectionEntity,
    ReportDetailScopeUpdateSectionWithElementEntity,
    ReportDetailScopeUpdateWithElementDataEntity,
    ReportExportPDFHeaderEntity,
    ReportPDFAttachmentEntity,
    ReportPDFCreatorEntity,
    ReportPDFFooterEntity,
    ReportPDFProjectEntity,
    ReportSectionUpdateWithElementEntity,
    SystemGeneratedUpdatesEntity,
)
from work_progress_v2.domain.services.config import WorkProgressProjectConfigService
from work_progress_v2.domain.triggers import (
    trigger_work_progress_export_report_generated,
    trigger_work_progress_report_generated,
)
from work_progress_v2.domain.utils import WorkProgressReportItemBlockHelper

logger = structlog.get_logger(__name__)


class WorkProgressReportPDFService:
    class WorkProgressReportPDFServiceException(BaseValidationError):
        pass

    DEFAULT_GENERATED_BY_NAME = "RDash"
    DEFAULT_GENERATED_BY_PHOTO = "https://cdn.rdash.io/rdash-assets/67ad2824c5df407384e2731193b9353e.png"
    VERSION_3_RELEASE_DATETIME = get_utc_time(datetime.datetime(2025, 5, 9))

    def __init__(
        self,
        repo: WorkProgressReportPDFAbstractRepo,
        user_entity: ProjectUserEntity,
        inventory_service: InventoryWorkProgressService,
        schedule_service: WorkProgressToProjectScheduleService,
        config_service: WorkProgressProjectConfigService,
    ):
        self.repo = repo
        self.user_entity = user_entity
        self.org_id = user_entity.org_id
        self.project_id = user_entity.project_id
        self.inventory_service = inventory_service
        self.schedule_service = schedule_service
        self.config_service = config_service

    def get_single_report_pdf(self, report_id: int):
        return self.repo.get_work_progress_report_pdf(report_id=report_id)

    def update_single_report_pdf_status(self, report_id: int, status: DownloadProgressStatusEnum):
        return self.repo.update_single_report_pdf_status(report_id=report_id, status=status)

    def get_export_report_pdf_data(
        self,
        start_date: datetime.date,
        end_date: datetime.date,
        vendor_id: int | None,
        exported_by_name: str,
        exported_by_photo: str | None,
        is_vendor_report: bool,
    ):
        org_id = self.org_id
        logger.info("Fetching export report pdf data.", start_date=start_date, end_date=end_date)
        if is_vendor_report:
            assert vendor_id is not None, "Vendor ID is required for vendor report"
            org_id = vendor_id

        try:
            report_data_with_project_data = self.repo.get_reports_between_date_range(
                start_date=start_date,
                end_date=end_date,
                org_id=org_id,
            )
        except WorkProgressReportPDFAbstractRepo.WorkProgressReportPDFAbstractRepoException:
            raise self.WorkProgressReportPDFServiceException("No reports found in the given date range.")

        reports = report_data_with_project_data.report_data
        logger.info("Fetched reports with project data.")

        reports.sort(key=lambda x: x.created_at)
        report_created_at_to_report_map: dict[datetime.date, ReportPDFDataEntity] = {
            report.created_at.date(): report for report in reports
        }

        is_single_day_report = end_date - start_date == datetime.timedelta(days=1)

        daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity] = []

        # get updated elements, sections and project org data in the given date range
        logger.info("Fetching updated elements data in the given date range.")
        updated_elements_in_date_range = self.repo.get_updated_elements_in_date_range(
            start_date=start_date, end_date=end_date
        )
        logger.info("Fetching updated sections data in the given date range.")
        updated_sections_in_date_range = self.repo.get_updated_sections_in_date_range(
            start_date=start_date, end_date=end_date
        )
        logger.info("Fetching updated project org data in the given date range.")
        project_org_data_in_date_range = self.repo.get_updated_project_org_data_in_date_range(
            start_date=start_date, end_date=end_date
        )

        # get updated material data in given date range
        logger.info("Fetching updated material data in the given date range.")
        material_data_in_date_range = self.inventory_service.get_material_update_data_in_date_range(
            start_date=start_date, end_date=end_date
        )

        # get updated schedule data in given date range
        logger.info("Fetching updated schedule data in the given date range.")
        schedule_data_in_date_range = self.schedule_service.get_schedule_update_date_in_date_range(
            start_date=start_date, end_date=end_date
        )

        date_wise_element_map: dict[datetime.date, list[ElementDayWiseDataEntity]] = {}
        for element in updated_elements_in_date_range:
            if element.date not in date_wise_element_map:
                date_wise_element_map[element.date] = []
            date_wise_element_map[element.date].append(element)

        date_wise_section_map: dict[datetime.date, list[SectionDayWiseDataEntity]] = {}
        for section in updated_sections_in_date_range:
            if section.date not in date_wise_section_map:
                date_wise_section_map[section.date] = []
            date_wise_section_map[section.date].append(section)

        date_wise_project_org_map: dict[datetime.date, ProjectOrgDaywiseDataEntity] = {}
        for project_org_data in project_org_data_in_date_range:
            date_wise_project_org_map[project_org_data.date] = project_org_data

        date_wise_material_map: dict[datetime.date, InventoryWorkProgressUpdatedDataEntity] = {}
        for material_data in material_data_in_date_range:
            date_wise_material_map[material_data.date] = material_data.inventory_data

        date_wise_schedule_map: dict[datetime.date, TodayScheduleDataEntity] = {}
        for schedule_data in schedule_data_in_date_range:
            date_wise_schedule_map[schedule_data.date] = schedule_data.schedule_data

        previous_day_report_entity = None

        logger.info("Preparing daywise reports data.")
        date_iterator = start_date
        while date_iterator <= end_date:
            logger.info("Preparing daywise data.", date=date_iterator)
            current_day_report = report_created_at_to_report_map.get(date_iterator)

            logger.info("Preparing daywise scope data.")
            scope_update_with_element_data = self._prepare_daywise_scope_update_with_element_data(
                day_end_updated_elements=date_wise_element_map.get(date_iterator, []),
                day_end_updated_sections=date_wise_section_map.get(date_iterator, []),
                day_end_project_org_data=date_wise_project_org_map.get(date_iterator),
            )

            logger.info("Preparing daywise material data.")
            material_update = self._prepare_daywise_material_update_data(
                material_data=date_wise_material_map.get(date_iterator)
            )

            logger.info("Preparing daywise schedule data.")
            schedule_update = self._prepare_daywise_schedule_update_data(
                schedule_data=date_wise_schedule_map.get(date_iterator),
            )
            site_view_point_list = current_day_report.site_view_point_list if current_day_report else []
            manpower_category_list = current_day_report.manpower_category_list if current_day_report else []
            daily_log = current_day_report.daily_log if current_day_report else None
            total_manpower_count = current_day_report.total_manpower_count if current_day_report else 0

            daywise_report_data.append(
                ReportDetailDayWiseDataWithScopeElementDataEntity(
                    created_at=self._get_daywise_data_created_at(
                        report=current_day_report,
                        date=date_iterator,
                    ),
                    created_by=self._get_daywise_data_created_by(
                        report=current_day_report,
                        previous_report_entity=previous_day_report_entity,
                        is_vendor_report=is_vendor_report,
                    ),
                    reported_project_progress=self._get_daywise_data_reported_project_progress(
                        report=current_day_report,
                        previous_report_entity=previous_day_report_entity,
                    ),
                    site_view_point_list=site_view_point_list,
                    manpower_category_list=manpower_category_list,
                    total_manpower_count=total_manpower_count,
                    daily_log=daily_log,
                    scope_update=scope_update_with_element_data,
                    material_update=material_update,
                    schedule_update=schedule_update,
                )
            )
            if current_day_report is not None:
                previous_day_report_entity = PreviousReportDataEntity(
                    created_at=current_day_report.created_at,
                    created_by=ReportDetailCreatedByEntity(
                        id=current_day_report.created_by.id,
                        name=current_day_report.created_by.name,
                        photo=current_day_report.created_by.photo,
                    ),
                    creator_org=ReportDetailCreatedByEntity(
                        id=current_day_report.creator_org.id,
                        name=current_day_report.creator_org.name,
                        photo=current_day_report.creator_org.photo,
                    ),
                    reported_project_progress=current_day_report.reported_project_progress,
                )
            logger.info("Daywise data prepared.", date=date_iterator)
            date_iterator += datetime.timedelta(days=1)

        logger.info("Prepared daywise reports data.")
        # sort the daywise report data by created_at date
        daywise_report_data.sort(key=lambda x: x.created_at)

        if is_single_day_report:
            report_config_list = self.config_service.get_config(
                type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT
            )
            checked_config_ids = self._prepare_checked_config_ids(config_list=report_config_list)
            cumulative_checked_config_ids = []
        else:
            cumulative_report_config_list = self.config_service.get_config(
                type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT
            )
            checked_config_ids = self._prepare_checked_config_ids(config_list=cumulative_report_config_list[1].children)
            cumulative_checked_config_ids = self._prepare_checked_config_ids(
                config_list=cumulative_report_config_list[0].children
            )

        # start_date was decreased by 1 day, hence exclude start_date's daywise data
        daywise_report_data = daywise_report_data[1:]

        logger.info("Preparing cumulative data for reports.")
        logger.info("Preparing cumulative material data.")
        material_update = self._prepare_cumulative_material_data(daywise_report_data)
        logger.info("Preparing cumulative schedule data.")
        schedule_update = self._prepare_cumulative_schedule_data(daywise_report_data)
        logger.info("Preparing cumulative scope data.")
        scope_update = self._calculate_cumulative_scope_data(
            daywise_report_data,
            report_created_at_to_report_map=report_created_at_to_report_map,
        )
        logger.info("Preparing cumulative manpower data.")
        manpower_data, total_manpower_count = self._calculate_cumulative_manpower_data(daywise_report_data)
        logger.info("Preparing cumulative site view point data.")
        site_view_point_data = self._calculate_cumulative_site_view_point_data(daywise_report_data)

        transformed_daywise_data = [self._transform_daywise_scope_data(data) for data in daywise_report_data]

        # if is_single_day_report and len(transformed_daywise_data) > 0:
        #     transformed_daywise_data = [transformed_daywise_data[-1]]

        data = ExportReportEntity(
            cumulative_data=(
                None
                if is_single_day_report
                else ExportReportCumulativeEntity(
                    scope_update=scope_update,
                    manpower=manpower_data,
                    site_view_point_list=site_view_point_data,
                    material_update=material_update,
                    schedule_update=schedule_update,
                    total_manpower_count=total_manpower_count,
                )
            ),
            day_wise_data=transformed_daywise_data,
            checked_config_ids=checked_config_ids,
            cumulative_checked_config_ids=cumulative_checked_config_ids,
            is_single_day_report=is_single_day_report,
            is_vendor_report=is_vendor_report,
        )
        logger.info("Prepared pdf export data for reports.", data=data)

        return ExportReportPdfEntity(
            header=ReportExportPDFHeaderEntity(
                project=ReportPDFProjectEntity(
                    name=report_data_with_project_data.project_name,
                    job_id=report_data_with_project_data.project_job_id,
                ),
                user=ReportPDFCreatorEntity(
                    name=exported_by_name,
                    photo=exported_by_photo,
                ),
                generated_at=get_local_time(timezone.now()),
                org=ReportPDFCreatorEntity(
                    name=report_data_with_project_data.org.name,
                    photo=report_data_with_project_data.org.photo,
                ),
                start_date=start_date + datetime.timedelta(days=1),
                end_date=end_date,
            ),
            footer=ReportPDFFooterEntity(
                generated_by=ReportPDFCreatorEntity(
                    name=self.DEFAULT_GENERATED_BY_NAME,
                    photo=self.DEFAULT_GENERATED_BY_PHOTO,
                ),
                generated_at=get_local_time(timezone.now()).date(),
            ),
            data=data,
            theme=None,
        )

    def _transform_daywise_scope_data(
        self, day_wise_data: ReportDetailDayWiseDataWithScopeElementDataEntity
    ) -> ReportDetailDayWiseEntity:
        scope_data_with_element_id = day_wise_data.scope_update
        section_updates = scope_data_with_element_id.section_updates

        transformed_section_updates: list[ReportDetailScopeUpdateSectionEntity] = []

        for section in section_updates:
            transformed_attachments = []

            for attachment in section.attachments:
                transformed_attachments.extend(attachment.attachments)

            updates = [update.blocks for update in section.updates]

            if len(updates) == 0 and len(transformed_attachments) == 0:
                continue

            transformed_section_updates.append(
                ReportDetailScopeUpdateSectionEntity(
                    id=section.id,
                    name=section.name,
                    progress_percentage=section.progress_percentage,
                    updates=updates,
                    attachments=transformed_attachments,
                )
            )

        transformed_scope_data: ReportDetailScopeUpdateEntity = ReportDetailScopeUpdateEntity(
            total_element_count=scope_data_with_element_id.total_element_count,
            today_progress=scope_data_with_element_id.today_progress,
            total_progress=scope_data_with_element_id.total_progress,
            section_updates=transformed_section_updates,
        )

        return ReportDetailDayWiseEntity(
            created_at=day_wise_data.created_at,
            created_by=day_wise_data.created_by,
            reported_project_progress=day_wise_data.reported_project_progress,
            site_view_point_list=day_wise_data.site_view_point_list,
            manpower_category_list=day_wise_data.manpower_category_list,
            total_manpower_count=day_wise_data.total_manpower_count,
            daily_log=day_wise_data.daily_log,
            scope_update=transformed_scope_data,
            material_update=day_wise_data.material_update,
            schedule_update=day_wise_data.schedule_update,
        )

    def _get_daywise_data_created_at(
        self,
        report: ReportPDFDataEntity | None,
        date: datetime.date,
    ) -> datetime.datetime:
        """
        This method is used to get the created_at date of the report
        """
        if report:
            time = report.created_at
        else:
            time = datetime.datetime.combine(date, datetime.datetime.min.time())

        return get_local_time(time)

    def _get_daywise_data_created_by(
        self,
        report: ReportPDFDataEntity | None,
        previous_report_entity: PreviousReportDataEntity | None,
        is_vendor_report: bool,
    ) -> ReportDetailCreatedByEntity:
        """
        This method is used to get the created_by data of the report
        """
        if not is_vendor_report:
            if report:
                return ReportDetailCreatedByEntity(
                    id=report.created_by.id,
                    name=report.created_by.name,
                    photo=report.created_by.photo,
                )
            elif previous_report_entity:
                return ReportDetailCreatedByEntity(
                    id=None, name=previous_report_entity.created_by.name, photo=previous_report_entity.created_by.photo
                )
        else:
            if report:
                return ReportDetailCreatedByEntity(
                    id=report.creator_org.id,
                    name=report.creator_org.name,
                    photo=report.creator_org.photo,
                )
            elif previous_report_entity:
                return ReportDetailCreatedByEntity(
                    id=None,
                    name=previous_report_entity.creator_org.name,
                    photo=previous_report_entity.creator_org.photo,
                )

        logger.info(
            "Report created by data not found.",
            report=report,
            previous_report_entity=previous_report_entity,
            is_vendor_report=is_vendor_report,
        )
        return ReportDetailCreatedByEntity(
            id=0,
            name=self.DEFAULT_GENERATED_BY_NAME,
            photo=self.DEFAULT_GENERATED_BY_PHOTO,
        )

    def _get_daywise_data_reported_project_progress(
        self,
        report: ReportPDFDataEntity | None,
        previous_report_entity: PreviousReportDataEntity | None,
    ) -> ReportDetailReportedProjectProgressEntity:
        """
        This method is used to get the reported_project_progress data of the report
        """
        if report:
            return report.reported_project_progress
        elif previous_report_entity:
            return previous_report_entity.reported_project_progress
        else:
            return ReportDetailReportedProjectProgressEntity(
                reported_progress_percentage=decimal.Decimal(0),
                projected_end_date=None,
            )

    def _calculate_cumulative_manpower_data(
        self, daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity]
    ) -> tuple[list[ExportREportPDFManpowerDateWiseEntity], int]:
        manpower_data: list[ExportREportPDFManpowerDateWiseEntity] = []
        total_manpower_count: int = 0
        for daywise_data in daywise_report_data:
            total_manpower_count += daywise_data.total_manpower_count
            manpower_data.append(
                ExportREportPDFManpowerDateWiseEntity(
                    date=daywise_data.created_at.date(),
                    manpower_data=ExportReportPDFManpowerEntity(
                        total_manpower=daywise_data.total_manpower_count,
                        category_list=[
                            ExportReportPDFManpowerCategoryEntity(
                                name=category.name,
                                value=category.value,
                            )
                            for category in daywise_data.manpower_category_list
                        ],
                    ),
                )
            )
        return manpower_data, total_manpower_count

    def _calculate_cumulative_site_view_point_data(
        self, daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity]
    ) -> list[ExportReportPDFSiteViewPointEntity]:
        site_view_point_data_dict: dict[int, ExportReportPDFSiteViewPointEntity] = {}
        for daywise_data in daywise_report_data:
            for site_view_point in daywise_data.site_view_point_list:
                attachments = [
                    ReportPDFAttachmentEntity(
                        id=attachment.id,
                        name=attachment.name,
                        url=attachment.url,
                        thumbnail_url=attachment.thumbnail_url,
                        uploaded_at=attachment.uploaded_at,
                    )
                    for attachment in site_view_point.todays_attachments
                ]
                if site_view_point.id not in site_view_point_data_dict:
                    site_view_point_data_dict[site_view_point.id] = ExportReportPDFSiteViewPointEntity(
                        id=site_view_point.id,
                        name=site_view_point.name,
                        attachments=attachments,
                    )
                else:
                    site_view_point_data_dict[site_view_point.id].attachments.extend(attachments)

        return list(site_view_point_data_dict.values())

    def _get_new_and_previous_value_for_scope_update_block(
        self, element: ElementDayWiseDataEntity
    ) -> tuple[str, str | None]:
        new_update = ""
        previous_update = None
        if element.day_end_update_method == ItemTypeUpdateMethodChoices.QUANTITY:
            assert element.day_end_input_quantity is not None, "Day end input quantity is required."
            assert element.day_end_uom_name is not None, "Day end uom is required."

            new_update = f"{element.day_end_input_quantity} {element.day_end_uom_name}"
            previous_update = (
                f"{element.previous_day_end_input_quantity} {element.previous_day_end_uom_name}"
                if element.previous_day_end_input_quantity
                else None
            )

        elif element.day_end_update_method == ItemTypeUpdateMethodChoices.MILESTONE:
            if element.day_end_milestone_name is None:
                element.day_end_milestone_name = "Not Started"

            new_update = element.day_end_milestone_name
            previous_update = element.previous_day_milestone_name

        elif element.day_end_update_method == ItemTypeUpdateMethodChoices.PERCENTAGE:
            assert element.day_end_progress_percentage is not None, "day_end_progress_percentage is required"

            new_update = f"{element.day_end_progress_percentage}%"
            previous_update = (
                f"{element.previous_day_end_progress_percentage}%"
                if element.previous_day_end_progress_percentage
                else None
            )
        else:
            raise self.WorkProgressReportPDFServiceException(f"Invalid update method: {element.day_end_update_method}")

        return new_update, previous_update

    def _prepare_daywise_scope_update_with_element_data(
        self,
        day_end_updated_elements: list[ElementDayWiseDataEntity],
        day_end_updated_sections: list[SectionDayWiseDataEntity],
        day_end_project_org_data: ProjectOrgDaywiseDataEntity | None,
    ) -> ReportDetailScopeUpdateWithElementDataEntity:
        scope_section_data: Dict[int, ReportDetailScopeUpdateSectionWithElementEntity] = {}

        if day_end_project_org_data is None:
            return ReportDetailScopeUpdateWithElementDataEntity(
                total_element_count=0,
                today_progress=decimal.Decimal(0),
                total_progress=decimal.Decimal(0),
                section_updates=[],
            )

        for section in day_end_updated_sections:
            scope_section_data[section.id] = ReportDetailScopeUpdateSectionWithElementEntity(
                id=section.id,
                name=section.name,
                progress_percentage=section.progress_percentage,
                updates=[],
                attachments=[],
            )

        for element in day_end_updated_elements:
            if element.section_id not in scope_section_data:
                scope_section_data[element.section_id] = ReportDetailScopeUpdateSectionWithElementEntity(
                    id=element.section_id,
                    name=element.section_name,
                    progress_percentage=decimal.Decimal(0),
                    updates=[],
                    attachments=[],
                )
            section_data = scope_section_data[element.section_id]

            section_data.attachments.append(
                ReportDetailAttachmentWithElementEntity(
                    element_id=element.element_id,
                    attachments=element.attachments,
                )
            )

            new_update, previous_update = self._get_new_and_previous_value_for_scope_update_block(element=element)

            blocks: list[WorkProgressBlockBaseModel] = []
            blocks = WorkProgressReportItemBlockHelper.create_system_generated_block(
                data=SystemGeneratedUpdatesEntity(
                    element_id=element.element_id,
                    element_name=element.element_name,
                    previous_update=previous_update,
                    new_update=new_update,
                )
            )
            section_data.updates.append(
                ReportSectionUpdateWithElementEntity(element_id=element.element_id, blocks=blocks)
            )

        return ReportDetailScopeUpdateWithElementDataEntity(
            total_element_count=day_end_project_org_data.total_items,
            today_progress=day_end_project_org_data.day_progress,
            total_progress=day_end_project_org_data.total_progress,
            section_updates=list(scope_section_data.values()),
        )

    def _prepare_daywise_material_update_data(
        self, material_data: InventoryWorkProgressUpdatedDataEntity | None
    ) -> ReportDetailMaterialUpdateEntity | None:
        if material_data is None:
            return None

        total_stock_value = decimal.Decimal(0)
        received_material: list[ReportDetailMaterialUpdateElementEntity] = []
        consumed_material: list[ReportDetailMaterialUpdateElementEntity] = []
        transferred_out_material: list[ReportDetailMaterialUpdateElementEntity] = []

        for item in material_data.received:
            total_stock_value += item.stock_value
            received_material.append(
                ReportDetailMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                )
            )

        for item in material_data.consumed:
            total_stock_value += item.stock_value
            consumed_material.append(
                ReportDetailMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                )
            )

        for item in material_data.transferred_out:
            total_stock_value += item.stock_value
            transferred_out_material.append(
                ReportDetailMaterialUpdateElementEntity(
                    id=item.id,
                    name=item.name,
                    quantity=item.quantity,
                    uom_name=item.uom_name,
                )
            )

        return ReportDetailMaterialUpdateEntity(
            total_stock_value=total_stock_value,
            received=received_material,
            consumed=consumed_material,
            transferred_out=transferred_out_material,
        )

    def _prepare_daywise_schedule_update_data(
        self,
        schedule_data: TodayScheduleDataEntity | None,
    ) -> ReportDetailScheduleUpdateEntity | None:
        """
        This method is used to get the daywise schedule data
        """
        if not schedule_data:
            return None

        activities: list[ReportDetailScheduleUpdateActivityEntity] = []
        attachments: list[ReportDetailAttachmentEntity] = []

        for activity in schedule_data.activities:
            activities.append(
                ReportDetailScheduleUpdateActivityEntity(
                    id=activity.id,
                    name=activity.name,
                    wbs=activity.wbs,
                    status=ReportDetailScheduleUpdateActivityStatusEntity(
                        name=activity.status.name,
                        color_code=activity.status.color_code,
                        status=activity.status.status,
                        days=activity.status.days,
                    ),
                    day_progress=activity.day_progress,
                    total_progress=activity.total_progress,
                )
            )

        for attachment in schedule_data.attachments:
            attachments.append(
                ReportDetailAttachmentEntity(
                    id=attachment.id,
                    name=attachment.name,
                    url=attachment.url,
                    thumbnail_url=None,
                    uploaded_at=attachment.uploaded_at,
                    activity_name=attachment.activity.name,
                )
            )

        return ReportDetailScheduleUpdateEntity(
            id=schedule_data.id,
            total_progress=schedule_data.total_progress,
            today_progress=schedule_data.today_progress,
            status=ReportDetailScheduleUpdateActivityStatusEntity(
                name=schedule_data.status.name,
                color_code=schedule_data.status.color_code,
                status=schedule_data.status.status,
                days=schedule_data.status.days,
            ),
            planned_end_date=schedule_data.planned_end_date,
            activities=activities,
            attachments=attachments,
        )

    def _prepare_cumulative_data_for_new_version_dpr(
        self,
        scope_data: ReportDetailScopeUpdateWithElementDataEntity,
        sections_data_map: dict[str, ExportReportPDFScopeUpdateSectionEntity],
        section_element_data_map: dict[str, dict[int, list[WorkProgressBlockBaseModel]]],
        section_element_attachment_map: dict[str, dict[int, list[ReportDetailAttachmentEntity]]],
        section_id_first_day_progress: dict[str, decimal.Decimal],
    ):
        for section in scope_data.section_updates:
            if section.name not in sections_data_map:
                sections_data_map[section.name] = ExportReportPDFScopeUpdateSectionEntity(
                    id=section.id,
                    name=section.name,
                    period_progress=section.progress_percentage,
                    total_progress=section.progress_percentage,
                    updates=[],
                    attachments=[],
                )
                section_id_first_day_progress[section.name] = section.progress_percentage

            section_data = sections_data_map[section.name]
            section_data.period_progress = section.progress_percentage - section_id_first_day_progress[section.name]
            section_data.total_progress = section.progress_percentage

            # store and update element's latest block data
            for update_data in section.updates:
                if section.name not in section_element_data_map:
                    section_element_data_map[section.name] = {}

                # if update_data.element_id not in section_element_data_map[section.name]:
                #     section_element_data_map[section.name][update_data.element_id] = []

                section_element_data_map[section.name][update_data.element_id] = update_data.blocks

            # store and update element's latest attachment data
            for attachment_data in section.attachments:
                if section.name not in section_element_attachment_map:
                    section_element_attachment_map[section.name] = {}

                if attachment_data.element_id not in section_element_attachment_map[section.name]:
                    section_element_attachment_map[section.name][attachment_data.element_id] = []

                section_element_attachment_map[section.name][attachment_data.element_id].extend(
                    attachment_data.attachments
                )

    def _prepare_cumulative_data_for_old_version_dpr(
        self,
        report: ReportPDFDataEntity,
        sections_data_map: dict[str, ExportReportPDFScopeUpdateSectionEntity],
        section_id_first_day_progress: dict[str, decimal.Decimal],
    ):
        scope_data = report.scope_update
        for section in scope_data.section_updates:
            if section.name not in sections_data_map:
                sections_data_map[section.name] = ExportReportPDFScopeUpdateSectionEntity(
                    id=section.id,
                    name=section.name,
                    period_progress=section.progress_percentage,
                    total_progress=section.progress_percentage,
                    updates=section.updates,
                    attachments=section.attachments,
                )
                section_id_first_day_progress[section.name] = section.progress_percentage
            else:
                section_data = sections_data_map[section.name]
                section_data.period_progress = section.progress_percentage - section_id_first_day_progress[section.name]
                section_data.total_progress = section.progress_percentage
                section_data.updates.extend(section.updates)
                section_data.attachments.extend(section.attachments)

        return sections_data_map

    def _calculate_cumulative_scope_data(
        self,
        daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity],
        report_created_at_to_report_map: dict[datetime.date, ReportPDFDataEntity],
    ) -> ExportReportPDFScopeUpdateEntity:
        cumulative_total_element_count = 0
        period_progress = decimal.Decimal(0)
        scope_progress = decimal.Decimal(0)

        sections_data_map: dict[str, ExportReportPDFScopeUpdateSectionEntity] = {}
        section_element_data_map: dict[str, dict[int, list[WorkProgressBlockBaseModel]]] = {}
        section_element_attachment_map: dict[str, dict[int, list[ReportDetailAttachmentEntity]]] = {}

        section_id_first_day_progress: dict[str, decimal.Decimal] = {}

        for daywise_data in daywise_report_data:
            if daywise_data.created_at < self.VERSION_3_RELEASE_DATETIME:
                logger.info("Old version dpr data found.", date=daywise_data.created_at)
                # for old version dpr
                report = report_created_at_to_report_map.get(daywise_data.created_at.date())
                if report is None:
                    continue
                scope_data = report.scope_update
                cumulative_total_element_count += scope_data.total_element_count
                period_progress += scope_data.today_progress
                scope_progress = scope_data.total_progress
                sections_data_map = self._prepare_cumulative_data_for_old_version_dpr(
                    report=report,
                    sections_data_map=sections_data_map,
                    section_id_first_day_progress=section_id_first_day_progress,
                )
                # sections_data_map.update(old_report_section_data)
            else:
                logger.info("New version dpr data found.", date=daywise_data.created_at)
                scope_data = daywise_data.scope_update
                cumulative_total_element_count += scope_data.total_element_count
                period_progress += scope_data.today_progress
                scope_progress = scope_data.total_progress
                self._prepare_cumulative_data_for_new_version_dpr(
                    scope_data=scope_data,
                    sections_data_map=sections_data_map,
                    section_element_data_map=section_element_data_map,
                    section_element_attachment_map=section_element_attachment_map,
                    section_id_first_day_progress=section_id_first_day_progress,
                )

        logger.info(
            sections_data_map=sections_data_map,
            section_element_data_map=section_element_data_map,
            section_element_attachment_map=section_element_attachment_map,
        )
        for section_name, section_data in sections_data_map.items():
            if section_name in section_element_data_map:
                for element_id, blocks in section_element_data_map[section_name].items():
                    section_data.updates.append(blocks)

            if section_name in section_element_attachment_map:
                for element_id, attachments in section_element_attachment_map[section_name].items():
                    section_data.attachments.extend(attachments)

        cumulative_scope_data = ExportReportPDFScopeUpdateEntity(
            total_items=cumulative_total_element_count,
            period_progress=period_progress,
            scope_progress=scope_progress,
            sections=list(sections_data_map.values()),
        )
        logger.info("Cumulative scope data prepared.", cumulative_scope_data=cumulative_scope_data)
        return cumulative_scope_data

    def _prepare_cumulative_material_data(
        self,
        daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity],
    ) -> ExportReportPDFMaterialEntity:
        total_stock_value = decimal.Decimal(0)
        received: dict[int, ExportReportPDFStockEntity] = {}
        consumed: dict[int, ExportReportPDFStockEntity] = {}
        transferred_out: dict[int, ExportReportPDFStockEntity] = {}

        for daywise_data in daywise_report_data:
            material_data = daywise_data.material_update
            if material_data is None:
                continue

            total_stock_value += material_data.total_stock_value
            for item in material_data.received:
                if item.id not in received:
                    received[item.id] = ExportReportPDFStockEntity(
                        name=item.name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                else:
                    received[item.id].quantity += item.quantity

            for item in material_data.consumed:
                if item.id not in consumed:
                    consumed[item.id] = ExportReportPDFStockEntity(
                        name=item.name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                else:
                    consumed[item.id].quantity += item.quantity

            for item in material_data.transferred_out:
                if item.id not in transferred_out:
                    transferred_out[item.id] = ExportReportPDFStockEntity(
                        name=item.name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                else:
                    transferred_out[item.id].quantity += item.quantity

        return ExportReportPDFMaterialEntity(
            total_stock_value=total_stock_value,
            received=list(received.values()),
            consumed=list(consumed.values()),
            transferred_out=list(transferred_out.values()),
        )

    def _prepare_cumulative_schedule_data(
        self,
        daywise_report_data: list[ReportDetailDayWiseDataWithScopeElementDataEntity],
    ) -> ExportReportPDFScheduleEntity | None:
        """
        This method is used to get the cumulative schedule data
        """
        if len(daywise_report_data) == 0:
            return None

        start_day_schedule_data = None
        end_day_schedule_data = None

        activity_data_map: dict[int, ExportScheduleActivityEntity] = {}
        activity_first_day_progress_map: dict[int, decimal.Decimal] = {}

        for daywise_data in daywise_report_data:
            schedule_data = daywise_data.schedule_update
            if schedule_data is None:
                continue

            if not start_day_schedule_data:
                start_day_schedule_data = schedule_data

            end_day_schedule_data = schedule_data

            for activity in schedule_data.activities:
                if activity.id not in activity_data_map:
                    activity_data_map[activity.id] = ExportScheduleActivityEntity(
                        wbs=activity.wbs,
                        name=activity.name,
                        status=(
                            ReportDetailScheduleUpdateActivityStatusEntity(
                                name=activity.status.name,
                                color_code=activity.status.color_code,
                                status=activity.status.status,
                                days=activity.status.days,
                            )
                            if activity.status
                            else None
                        ),
                        total_progress=activity.total_progress,
                        period_progress=activity.day_progress,
                    )
                    activity_first_day_progress_map[activity.id] = activity.total_progress
                else:
                    activity_data = activity_data_map[activity.id]

                    # update the progress data
                    activity_data.wbs = activity.wbs
                    activity_data.name = activity.name
                    activity_data.status = (
                        ReportDetailScheduleUpdateActivityStatusEntity(
                            name=activity.status.name,
                            color_code=activity.status.color_code,
                            status=activity.status.status,
                            days=activity.status.days,
                        )
                        if activity.status
                        else None
                    )
                    activity_data.total_progress = activity.total_progress
                    # diff = activity.total_progress - activity_data.period_progress
                    # activity_data.period_progress += diff
                    activity_data.period_progress = (
                        activity.total_progress - activity_first_day_progress_map[activity.id]
                    )

        if end_day_schedule_data is None:
            return ExportReportPDFScheduleEntity(
                schedule_progress=decimal.Decimal(0),
                period_progress=decimal.Decimal(0),
                status=None,
                planned_end_date=None,
                activities=[],
                attachments=[],
            )

        assert start_day_schedule_data is not None, "Start day schedule data not found"
        assert end_day_schedule_data is not None, "End day schedule data not found"

        if end_day_schedule_data.total_progress and start_day_schedule_data.total_progress:
            schedule_period_progress = end_day_schedule_data.total_progress - start_day_schedule_data.total_progress
        elif end_day_schedule_data.total_progress:
            schedule_period_progress = end_day_schedule_data.total_progress
        else:
            schedule_period_progress = decimal.Decimal(0)

        return ExportReportPDFScheduleEntity(
            schedule_progress=(
                end_day_schedule_data.total_progress if end_day_schedule_data.total_progress else decimal.Decimal(0)
            ),
            period_progress=schedule_period_progress,
            status=(
                ReportDetailScheduleUpdateActivityStatusEntity(
                    name=end_day_schedule_data.status.name,
                    color_code=end_day_schedule_data.status.color_code,
                    status=end_day_schedule_data.status.status,
                    days=end_day_schedule_data.status.days,
                )
                if end_day_schedule_data.status
                else None
            ),
            planned_end_date=end_day_schedule_data.planned_end_date,
            activities=list(activity_data_map.values()),
            attachments=end_day_schedule_data.attachments,
        )

    def _prepare_checked_config_ids(
        self, config_list: list[ReportConfigEntity]
    ) -> list[WorkProgressReportConfigIdEnum]:
        checked_config_ids: list[WorkProgressReportConfigIdEnum] = []

        def get_checked_config_ids(config: list[ReportConfigEntity]):
            for section in config:
                if section.checked:
                    checked_config_ids.append(section.id)
                if section.children:
                    get_checked_config_ids(section.children)

        get_checked_config_ids(config=config_list)

        return checked_config_ids

    def get_project_organization_created_data(self):
        return self.repo.get_project_organization_created_data()


class WorkProgressReportPDFInteractor:
    class Exception(BaseValidationError):
        pass

    class StartDateGreaterThanEndDate(BaseValidationError):
        pass

    def __init__(self, service: WorkProgressReportPDFService, download_service: DownloadService):
        self.service = service
        self.user_entity = service.user_entity
        self.download_service = download_service
        self.current_date = get_local_time(timezone.now()).date()

    def get_report_pdf_url(self, report_id: int) -> str | None:
        pdf = self.service.get_single_report_pdf(report_id=report_id)

        if pdf.url is not None:
            return pdf.url

        if pdf.status == DownloadProgressStatusEnum.IN_PROGRESS:
            return None

        self.service.update_single_report_pdf_status(report_id=report_id, status=DownloadProgressStatusEnum.IN_PROGRESS)

        trigger_work_progress_report_generated(report_id=report_id, user_entity=self.user_entity)

    def _get_project_org_data_for_dpr_date(self):
        return self.service.get_project_organization_created_data()

    def get_export_report_pdf_data(
        self,
        data: ExportReportInputEntity,
        exported_by_name: str,
        exported_by_photo: str | None,
    ) -> ExportReportPdfOutputEntity:
        start_date = data.start_date if data.start_date else None
        end_date = data.end_date if data.end_date else None

        file_ext = ".pdf"

        if data.is_latest:
            start_date = self.current_date - datetime.timedelta(days=1)
            end_date = self.current_date
            file_name = f"DPR {end_date.strftime('%d %B %Y')}"
        elif data.is_last_7_days:
            start_date = self.current_date - datetime.timedelta(days=7)
            end_date = self.current_date
            file_name = f"DPR {(start_date + datetime.timedelta(days=1)).strftime('%d %B %Y')} - {end_date.strftime('%d %B %Y')}"  # noqa
        elif data.is_complete:
            project_org_data = self._get_project_org_data_for_dpr_date()
            start_date = project_org_data.created_date - datetime.timedelta(days=1)
            end_date = self.current_date
            file_name = f"DPR {project_org_data.created_date.strftime('%d %B %Y')} - {end_date.strftime('%d %B %Y')}"
        else:
            assert start_date is not None and end_date is not None, "Start and end date must be provided"
            if start_date > end_date:
                raise self.StartDateGreaterThanEndDate("Start date cannot be greater than end date")

            if start_date == end_date:
                file_name = f"DPR {start_date.strftime('%d %B %Y')}"
            else:
                file_name = f"DPR {start_date.strftime('%d %B %Y')} - {end_date.strftime('%d %B %Y')}"
            start_date = start_date - datetime.timedelta(days=1)

        if data.is_vendor_report is True:
            if data.vendor_id:
                vendor_name = get_vendor_name(data.vendor_id)
                file_name += f", {vendor_name}"
            else:
                file_name += ", All Vendors"

        logger.info(
            "Generating export report pdf data",
            data=data,
            start_date=start_date,
            end_date=end_date,
            file_name=file_name + file_ext,
        )

        download_id = self.download_service.create_download(
            name=file_name + file_ext,
            progress_status=DownloadProgressStatusEnum.IN_PROGRESS,
            context=MicroContext.PROGRESS_REPORT,
            uuid=data.uuid,
        )

        sync_trigger = False
        if end_date - start_date == datetime.timedelta(days=1):
            sync_trigger = True

        trigger_work_progress_export_report_generated(
            user_entity=self.user_entity,
            start_date=start_date,
            end_date=end_date,
            download_id=download_id,
            exported_by_name=exported_by_name,
            exported_by_photo=exported_by_photo,
            is_vendor_report=data.is_vendor_report,
            vendor_id=data.vendor_id,
            sync=sync_trigger,
        )

        return ExportReportPdfOutputEntity(download_id=download_id)
