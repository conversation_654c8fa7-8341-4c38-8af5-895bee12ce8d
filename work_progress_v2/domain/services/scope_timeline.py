import datetime

import structlog
from django.utils import timezone

from common.exceptions import BaseValidationError
from common.timeline.service import TimelineStatusService
from common.utils import get_local_time
from order.domain.services.work_progress import OrderWorkProgressService
from project.interface.external.work_progress import WorkProgressToProjectService
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import (
    ScopeCompletionPercentageEntity,
    ScopeProgressEntity,
    ScopeTimelineDataEntity,
)
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService
from work_progress_v2.domain.services.status import WorkProgressStatusService

logger = structlog.get_logger(__name__)


class WorkProgressScopeTimelineInteractor:
    class Exception(BaseValidationError):
        pass

    class ProjectDoesNotExistException(Exception):
        pass

    def __init__(
        self,
        repo: WorkProgressAbstractRepo,
        service: TimelineStatusService,
        status_service: WorkProgressStatusService,
        scope_data_service: WorkProgressScopeDataService,
        cache: WorkProgressCache,
    ):
        self.repo = repo
        self.service = service
        self.status_service = status_service
        self.current_day = get_local_time(timezone.now()).date()
        self.cache = cache
        self.scope_data_service = scope_data_service

    def get_scope_timeline_data(self, scope_data: ScopeProgressEntity | None = None) -> ScopeTimelineDataEntity:
        timeline_dates = self.repo.get_scope_timeline_data()
        if scope_data is None:
            scope_progress_percentage = self.cache.get_scope_data(
                scope_data_service=self.scope_data_service, cache=True
            ).total_progress
        else:
            scope_progress_percentage = scope_data.total_progress

        logger.info(
            "Get scope timeline data",
            expected_start_date=timeline_dates.expected_start_date,
            expected_due_date=timeline_dates.expected_due_date,
            scope_start_date=timeline_dates.scope_start_date,
            progress_percentage=scope_progress_percentage,
        )

        scope_status = self.service.get_status_message(
            actual_start_date=timeline_dates.scope_start_date,
            expected_start_date=timeline_dates.expected_start_date,
            expected_due_date=timeline_dates.expected_due_date,
            progress_percentage=scope_progress_percentage,
        )

        return ScopeTimelineDataEntity(
            scope_status=scope_status,
            scope_completion=ScopeCompletionPercentageEntity(
                percentage=scope_progress_percentage,
                color_code=scope_status.color_code,
            ),
            expected_start_date=timeline_dates.expected_start_date,
            expected_due_date=timeline_dates.expected_due_date,
            scope_start_date=timeline_dates.scope_start_date,
            scope_completion_date=timeline_dates.scope_completion_date,
        )

    def get_execution_due_date_history(
        self,
        is_vendor: bool,
        project_service: WorkProgressToProjectService,
        order_service: OrderWorkProgressService,
    ):
        start_date = get_local_time(self.repo.get_project_created_at()).date()
        if is_vendor:
            data = order_service.get_execution_due_date_field_history(
                start_date=start_date,
                end_date=self.current_day,
            )
        else:
            data = project_service.get_execution_due_date_field_history(
                start_date=start_date,
                end_date=self.current_day,
            )

        previous_value: datetime.datetime | None = None
        for history in data.history_entities:
            history.prev_value = previous_value

            previous_value = history.value

        return data

    def get_expected_start_date_history(
        self,
        is_vendor: bool,
        project_service: WorkProgressToProjectService,
        order_service: OrderWorkProgressService,
    ):
        start_date = get_local_time(self.repo.get_project_created_at()).date()
        if is_vendor:
            data = order_service.get_expected_start_date_field_history(
                start_date=start_date,
                end_date=self.current_day,
            )
        else:
            data = project_service.get_expected_start_date_field_history(
                start_date=start_date,
                end_date=self.current_day,
            )

        previous_value: datetime.datetime | None = None
        for history in data.history_entities:
            history.prev_value = previous_value

            previous_value = history.value

        return data
