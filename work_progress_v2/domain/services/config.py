import abc
import copy

import structlog

from authorization.enums import PermissionLevelEnum
from common.exceptions import BaseValidationError
from core.entities import Org<PERSON>serEntity, ProjectUserEntity
from work_progress_v2.data.models.config import ProjectReportConfig
from work_progress_v2.domain.abstract_repos import (
    WorkProgressOrgAbstractRepo,
    WorkProgressReportAbstractRepo,
)
from work_progress_v2.domain.constants import (
    EXPORT_MULTI_DAY_REPORT_CONFIG,
    EXPORT_SINGLE_DAY_REPORT_CONFIG,
    GENERATE_REPORT_CONFIG,
    PERMISSION_MAPPING_CONFIG_ID,
)
from work_progress_v2.domain.entities import ReportConfigEntity
from work_progress_v2.domain.enums import WorkProgressConfigReportTypeEnum, WorkProgressReportConfigIdEnum
from work_progress_v2.domain.report_entities import CreateReportInputEntity
from work_progress_v2.domain.services.permission import WorkProgressPermissionService

logger = structlog.get_logger(__name__)


class WorkProgressBaseConfigService:
    class Exception(BaseValidationError):
        pass

    class ConfigIsInvalidException(Exception):
        pass

    class DailyLogConfigException(Exception):
        pass

    def __init__(self, org_id: int, permission_service: WorkProgressPermissionService, project_id: int | None = None):
        self.org_id = org_id
        self.project_id = project_id
        self.permission_service = permission_service

    @abc.abstractmethod
    def get_configs(
        self,
        types: list[WorkProgressConfigReportTypeEnum],
    ) -> dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]]:
        raise NotImplementedError("get_config method is not implemented")

    def validate_permission(
        self,
        config: list[ReportConfigEntity],
        only_org_permissions: bool = False,
    ) -> list[ReportConfigEntity]:
        logger.info("Validating permission", config=config, only_org_permissions=only_org_permissions)

        permission_validated_config = []

        for section in config:
            if section.id not in PERMISSION_MAPPING_CONFIG_ID:
                if section.children:
                    validate_children = self.validate_permission(
                        section.children, only_org_permissions=only_org_permissions
                    )
                    section.children = validate_children
                permission_validated_config.append(section)
                continue
            else:
                permission = PERMISSION_MAPPING_CONFIG_ID[section.id]

                if permission.permission_type == PermissionLevelEnum.PROJECT_AND_ORG:
                    if only_org_permissions:
                        if self.permission_service.is_action_permitted(
                            permission=permission.permission, permission_type=PermissionLevelEnum.ORG
                        ):
                            if section.children:
                                validate_children = self.validate_permission(
                                    section.children, only_org_permissions=only_org_permissions
                                )
                                section.children = validate_children
                            permission_validated_config.append(section)
                        continue

                    assert self.project_id is not None, "Project id is required"

                    if self.permission_service.is_action_permitted(
                        permission=permission.permission, permission_type=PermissionLevelEnum.PROJECT_AND_ORG
                    ):
                        if section.children:
                            validate_children = self.validate_permission(
                                section.children, only_org_permissions=only_org_permissions
                            )
                            section.children = validate_children
                        permission_validated_config.append(section)
                        continue

                if (
                    permission.permission_type == PermissionLevelEnum.ORG
                    and self.permission_service.is_action_permitted(
                        permission=permission.permission, permission_type=PermissionLevelEnum.ORG
                    )
                ):
                    if section.children:
                        validate_children = self.validate_permission(
                            section.children, only_org_permissions=only_org_permissions
                        )
                        section.children = validate_children
                    permission_validated_config.append(section)
                    continue

                if permission.permission_type == PermissionLevelEnum.PROJECT:
                    if only_org_permissions:
                        has_permission = True
                    else:
                        assert self.project_id is not None, "Project id is required"

                        has_permission = only_org_permissions or self.permission_service.is_action_permitted(
                            permission=permission.permission, permission_type=PermissionLevelEnum.PROJECT
                        )
                    if not has_permission:
                        continue

                    if section.children:
                        validate_children = self.validate_permission(
                            section.children, only_org_permissions=only_org_permissions
                        )
                        section.children = validate_children
                    permission_validated_config.append(section)
                    continue

        return permission_validated_config

    def get_config_ids(
        self, config: list[ReportConfigEntity], only_checked: bool = False
    ) -> list[WorkProgressReportConfigIdEnum]:
        config_ids: list[WorkProgressReportConfigIdEnum] = []

        for section in config:
            if only_checked and not section.checked:
                continue

            config_ids.append(section.id)

            if section.children:
                children_config_ids = self.get_config_ids(config=section.children, only_checked=only_checked)
                config_ids.extend(children_config_ids)

        return config_ids

    def validate_config_ids(self, system_config: list[ReportConfigEntity], user_config: list[ReportConfigEntity]):
        system_config_ids = self.get_config_ids(system_config)
        user_config_ids = self.get_config_ids(user_config)

        if set(system_config_ids) != set(user_config_ids):
            raise self.ConfigIsInvalidException("Config is invalid")

    def validate_permission_for_config(
        self,
        system_config: list[ReportConfigEntity],
        user_config: list[ReportConfigEntity],
        only_org_permissions: bool = False,
    ):
        permission_checked_system_config = self.validate_permission(
            system_config,
            only_org_permissions=only_org_permissions,
        )

        self.validate_config_ids(system_config=permission_checked_system_config, user_config=user_config)

    def base_root_level_validate_config(self, config: list[ReportConfigEntity]):
        for root_section in config:
            if not root_section.children:
                continue

            has_checked_child = any(child.checked for child in root_section.children)

            # Invalid if parent is checked but no children are checked
            # or if parent is unchecked but some children are checked
            if root_section.checked != has_checked_child:
                return False

        return True

    # recursively check if any children is checked and parent is unchecked
    def base_second_level_validate_config(self, config: ReportConfigEntity):
        if not config.children:
            return True

        has_checked_child = any(child.checked for child in config.children)

        if config.checked and not has_checked_child:
            return True

        # Invalid if children is checked but parent is unchecked
        if config.checked != has_checked_child:
            return False

        for child in config.children:
            validate = self.base_second_level_validate_config(child)

            if not validate:
                return False

        return True

    def base_validate_config(self, config: list[ReportConfigEntity]):
        if not self.base_root_level_validate_config(config):
            return False

        for section in config:
            for child in section.children:
                validate = self.base_second_level_validate_config(child)

                if not validate:
                    return False

        return True

    def validate_daily_log_config(self, config: list[ReportConfigEntity]):
        daily_log_config = next(
            (config for config in config if config.id == WorkProgressReportConfigIdEnum.DAILY_LOG), None
        )

        if not daily_log_config:
            logger.info("Daily log config is missing")
            raise self.DailyLogConfigException("Daily log config is missing")

        if not daily_log_config.checked:
            logger.info("Daily log config should be checked")
            raise self.DailyLogConfigException("Daily log config should be checked")

        if len(daily_log_config.children) == 0:
            logger.info("Daily log config should have children")
            raise self.DailyLogConfigException("Daily log config should have children")

        for child in daily_log_config.children:
            if child.id == WorkProgressReportConfigIdEnum.TODAY_UPDATE and not child.checked:
                logger.info("Today update should be checked")
                raise self.DailyLogConfigException("Today update should be checked")

    def validate_generate_report_config(
        self,
        config: list[ReportConfigEntity],
        only_org_permissions: bool = False,
    ):
        validated = self.base_validate_config(config)

        if not validated:
            logger.info("Base validation failed")
            raise self.ConfigIsInvalidException("Generate report config is invalid")

        self.validate_daily_log_config(config)
        self.validate_permission_for_config(
            system_config=copy.deepcopy(GENERATE_REPORT_CONFIG),
            user_config=config,
            only_org_permissions=only_org_permissions,
        )

    def validate_export_single_day_report_config(
        self,
        config: list[ReportConfigEntity],
        only_org_permissions: bool = False,
    ):
        validated = self.base_validate_config(config)

        if not validated:
            raise self.ConfigIsInvalidException("Export single day report config is invalid")

        self.validate_permission_for_config(
            system_config=copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG),
            user_config=config,
            only_org_permissions=only_org_permissions,
        )

    def validate_export_multi_day_report_config(
        self,
        config: list[ReportConfigEntity],
        only_org_permissions: bool = False,
    ):
        validated = self.base_validate_config(config)

        if not validated:
            raise self.ConfigIsInvalidException("Export multi day report config is invalid")

        self.validate_permission_for_config(
            system_config=copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG),
            user_config=config,
            only_org_permissions=only_org_permissions,
        )

    # This function will fill the missing config from db_config with system_config
    # and fill every missing config from system_config with checked=False
    # adn return the final config
    def merge_configs_for_db(
        self,
        merge_into_config: list[ReportConfigEntity],
        merge_from_config: list[ReportConfigEntity] | None,
    ) -> list[ReportConfigEntity]:
        def merge_config(merge_into_config: list[ReportConfigEntity], merge_from_config: list[ReportConfigEntity]):
            for db_section in merge_into_config:
                system_section = next((section for section in merge_from_config if section.id == db_section.id), None)

                if not system_section:
                    continue

                if len(db_section.children) == 0:
                    continue

                if system_section.children:
                    db_section.children = merge_config(db_section.children, system_section.children)

            for system_section in merge_from_config:
                db_section = next((section for section in merge_into_config if section.id == system_section.id), None)

                if not db_section:
                    merge_into_config.append(system_section)

            return merge_into_config

        if not merge_from_config:
            return merge_into_config

        return merge_config(merge_into_config, merge_from_config)

    def merge_configs_for_ui(
        self,
        db_config: list[ReportConfigEntity],
        system_config: list[ReportConfigEntity],
    ) -> list[ReportConfigEntity]:
        def uncheck_all(config: ReportConfigEntity) -> ReportConfigEntity:
            config.checked = False

            if config.children:
                for child in config.children:
                    uncheck_all(child)

            return config

        def mark_mandatory(db_config: ReportConfigEntity, system_config: ReportConfigEntity) -> ReportConfigEntity:
            db_config.mandatory = system_config.mandatory

            if db_config.children:
                for db_child, system_child in zip(db_config.children, system_config.children):
                    mark_mandatory(db_child, system_child)

            return db_config

        def merge_config(db_config: list[ReportConfigEntity], system_config: list[ReportConfigEntity]):
            merged_config = []

            for db_section in db_config:
                system_section = next((section for section in system_config if section.id == db_section.id), None)

                if not system_section:
                    continue

                if len(db_section.children) == 0:
                    continue

                if system_section.children:
                    db_section.children = merge_config(db_section.children, system_section.children)

            for system_section in system_config:
                db_section = next((section for section in db_config if section.id == system_section.id), None)

                if not db_section:
                    merged_config.append(uncheck_all(system_section))
                else:
                    merged_config.append(mark_mandatory(db_section, system_section))

            return merged_config

        return merge_config(db_config, system_config)


class WorkProgressOrgConfigService(WorkProgressBaseConfigService):
    def __init__(
        self,
        user_entity: OrgUserEntity,
        repo: WorkProgressOrgAbstractRepo,
        permission_service: WorkProgressPermissionService,
    ):
        super().__init__(org_id=user_entity.org_id, permission_service=permission_service)
        self.user_entity = user_entity
        self.repo = repo

    def get_config(self, type: WorkProgressConfigReportTypeEnum) -> list[ReportConfigEntity]:
        return self.get_configs(types=[type])[type]

    def get_configs(
        self,
        types: list[WorkProgressConfigReportTypeEnum],
        check_permission: bool = True,
    ) -> dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]]:
        data = self.repo.get_configs(types=types)

        result: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]] = {}

        for type in types:
            config = data.config.get(type, None)

            if config:
                if type == WorkProgressConfigReportTypeEnum.GENERATE_REPORT:
                    merged_config = self.merge_configs_for_ui(
                        db_config=config,
                        system_config=copy.deepcopy(GENERATE_REPORT_CONFIG),
                    )

                    if check_permission:
                        result[type] = self.validate_permission(config=merged_config, only_org_permissions=True)
                    else:
                        result[type] = merged_config

                elif type == WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT:
                    merged_config = self.merge_configs_for_ui(
                        db_config=config,
                        system_config=copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG),
                    )

                    if check_permission:
                        result[type] = self.validate_permission(config=merged_config, only_org_permissions=True)
                    else:
                        result[type] = merged_config
                else:
                    merged_config = self.merge_configs_for_ui(
                        db_config=config,
                        system_config=copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG),
                    )

                    if check_permission:
                        result[type] = self.validate_permission(config=merged_config, only_org_permissions=True)
                    else:
                        result[type] = merged_config

                continue

            if type == WorkProgressConfigReportTypeEnum.GENERATE_REPORT:
                if check_permission:
                    result[type] = self.validate_permission(
                        config=copy.deepcopy(GENERATE_REPORT_CONFIG), only_org_permissions=True
                    )
                else:
                    result[type] = copy.deepcopy(GENERATE_REPORT_CONFIG)
            elif type == WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT:
                if check_permission:
                    result[type] = self.validate_permission(
                        config=copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG), only_org_permissions=True
                    )
                else:
                    result[type] = copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG)
            else:
                if check_permission:
                    result[type] = self.validate_permission(
                        config=copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG), only_org_permissions=True
                    )
                else:
                    result[type] = copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG)

        return result

    def update_generate_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating generate report config", config=config)

        super().validate_generate_report_config(config, only_org_permissions=True)
        data = self.repo.get_configs(types=[WorkProgressConfigReportTypeEnum.GENERATE_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=data.config[WorkProgressConfigReportTypeEnum.GENERATE_REPORT] if data.config else None,
        )

        self.repo.update_or_create_report_config(
            config=merged_config,
            type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT,
        )

    def update_export_single_day_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating export single day report config", config=config)

        super().validate_export_single_day_report_config(config, only_org_permissions=True)
        data = self.repo.get_configs(types=[WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=(
                data.config[WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT] if data.config else None
            ),
        )

        self.repo.update_or_create_report_config(
            config=merged_config,
            type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT,
        )

    def update_export_multi_day_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating export multi day report config", config=config)

        super().validate_export_multi_day_report_config(config, only_org_permissions=True)
        data = self.repo.get_configs(types=[WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=(
                data.config[WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT] if data.config else None
            ),
        )

        self.repo.update_or_create_report_config(
            config=merged_config,
            type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT,
        )

    def create_project_configs(self, project_id: int):
        configs = self.get_configs(
            types=[
                WorkProgressConfigReportTypeEnum.GENERATE_REPORT,
                WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT,
                WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT,
            ],
            check_permission=False,
        )

        self.repo.create_project_configs(project_id=project_id, configs=configs)


class WorkProgressProjectConfigService(WorkProgressBaseConfigService):
    class ConfigDoesNotExistException(Exception):
        pass

    def __init__(
        self,
        user_entity: ProjectUserEntity,
        repo: WorkProgressReportAbstractRepo,
        permission_service: WorkProgressPermissionService,
    ):
        super().__init__(
            org_id=user_entity.org_id,
            project_id=user_entity.project_id,
            permission_service=permission_service,
        )
        self.user_entity = user_entity
        self.repo = repo
        self._configs: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]] | None = None

    def get_config_checked_ids(self, type: WorkProgressConfigReportTypeEnum) -> list[str]:
        config = self.get_config(type=type)

        return [id.value for id in self.get_config_ids(config, only_checked=True)]

    def get_config(self, type: WorkProgressConfigReportTypeEnum) -> list[ReportConfigEntity]:
        if self._configs is None:
            self._configs = {}

        if type in self._configs:
            return self._configs[type]

        self._configs[type] = self.get_configs(types=[type])[type]
        return self._configs[type]

    def get_configs(
        self,
        types: list[WorkProgressConfigReportTypeEnum],
    ) -> dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]]:
        data = self.repo.get_report_configs(types=types)

        if set(data.config.keys()) != set(types):
            assert False, "Project should always have all the configs. Something is very wrong!!"

        result: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]] = {}

        for type, config in data.config.items():
            if type == WorkProgressConfigReportTypeEnum.GENERATE_REPORT:
                merged_config = self.merge_configs_for_ui(
                    db_config=config,
                    system_config=copy.deepcopy(GENERATE_REPORT_CONFIG),
                )

                result[type] = self.validate_permission(config=merged_config)
            elif type == WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT:
                merged_config = self.merge_configs_for_ui(
                    db_config=config,
                    system_config=copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG),
                )

                result[type] = self.validate_permission(config=merged_config)
            else:
                merged_config = self.merge_configs_for_ui(
                    db_config=config,
                    system_config=copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG),
                )

                result[type] = self.validate_permission(config=merged_config)

        return result

    def update_generate_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating generate report config", config=config)

        super().validate_generate_report_config(config)
        data = self.repo.get_report_configs(types=[WorkProgressConfigReportTypeEnum.GENERATE_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=data.config[WorkProgressConfigReportTypeEnum.GENERATE_REPORT] if data.config else None,
        )

        try:
            self.repo.update_report_config(
                config=merged_config,
                type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT,
            )
        except ProjectReportConfig.DoesNotExist:
            raise self.ConfigDoesNotExistException("Config does not exist")

    def update_export_single_day_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating export single day report config", config=config)

        super().validate_export_single_day_report_config(config)
        data = self.repo.get_report_configs(types=[WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=(
                data.config[WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT] if data.config else None
            ),
        )

        try:
            self.repo.update_report_config(
                config=merged_config,
                type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT,
            )
        except ProjectReportConfig.DoesNotExist:
            raise self.ConfigDoesNotExistException("Config does not exist")

    def update_export_multi_day_report(self, config: list[ReportConfigEntity]):
        logger.info("Updating export multi day report config", config=config)

        super().validate_export_multi_day_report_config(config)
        data = self.repo.get_report_configs(types=[WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT])

        merged_config = self.merge_configs_for_db(
            merge_into_config=config,
            merge_from_config=(
                data.config[WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT] if data.config else None
            ),
        )

        try:
            self.repo.update_report_config(
                config=merged_config,
                type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT,
            )
        except ProjectReportConfig.DoesNotExist:
            raise self.ConfigDoesNotExistException("Config does not exist")

    def is_module_enabled(self, module_key: WorkProgressReportConfigIdEnum) -> bool:
        config = self.get_config(type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT)

        if not config:
            return False

        config_ids = self.get_config_ids(config=config, only_checked=True)

        return module_key in config_ids

    def convert_input_data_to_config_ids(self, data: CreateReportInputEntity) -> list[WorkProgressReportConfigIdEnum]:
        config_ids: list[WorkProgressReportConfigIdEnum] = []

        if len(data.manpower_category_list) > 0:
            config_ids.append(WorkProgressReportConfigIdEnum.MANPOWER)

        if len(data.site_view_point_list or []) > 0:
            config_ids.append(WorkProgressReportConfigIdEnum.SITE_VIEW_POINT)

        if data.scope_update:
            config_ids.append(WorkProgressReportConfigIdEnum.SCOPE_UPDATE)

            if len(data.scope_update.section_updates) > 0:
                config_ids.append(WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE)

        if data.reported_project_progress:
            config_ids.append(WorkProgressReportConfigIdEnum.REPORTED_PROJECT_PROGRESS)

            if data.reported_project_progress.projected_end_date:
                config_ids.append(WorkProgressReportConfigIdEnum.PROJECTED_END_DATE)

            if data.reported_project_progress.reported_progress_percentage:
                config_ids.append(WorkProgressReportConfigIdEnum.REPORTED_PROGRESS)

        if data.daily_log:
            config_ids.append(WorkProgressReportConfigIdEnum.DAILY_LOG)

            today_update_len = (
                len(data.daily_log.today_update.attachments) + len(data.daily_log.today_update.updates)
                if data.daily_log.today_update
                else 0
            )
            blocker_len = (
                len(data.daily_log.blocker.attachments) + len(data.daily_log.blocker.updates)
                if data.daily_log.blocker
                else 0
            )
            tomorrow_plan_len = (
                len(data.daily_log.tomorrow_plan.attachments) + len(data.daily_log.tomorrow_plan.updates)
                if data.daily_log.tomorrow_plan
                else 0
            )

            if today_update_len > 0:
                config_ids.append(WorkProgressReportConfigIdEnum.TODAY_UPDATE)

            if blocker_len > 0:
                config_ids.append(WorkProgressReportConfigIdEnum.BLOCKER)

            if tomorrow_plan_len > 0:
                config_ids.append(WorkProgressReportConfigIdEnum.TOMORROW_PLAN)

        return config_ids

    def can_update_config(self, data: CreateReportInputEntity):
        config = self.get_config(type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT)

        if not config:
            return False

        system_user_ids = self.get_config_ids(config=config, only_checked=True)
        user_config_ids = self.convert_input_data_to_config_ids(data)

        return set(user_config_ids).issubset(set(system_user_ids))
