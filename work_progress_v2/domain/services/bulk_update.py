import decimal

import structlog
from jsonschema import ValidationError

from boq.data.choices import BoqElementStatus
from common.exceptions import BaseValidationError
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from project.domain.entities import ItemTypeConfigCacheEntity
from project.interface.external.work_progress import WorkProgressToProjectService
from work_progress_v2.data.choices import WorkProgressElementActionChoices
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import (
    BulkUpdateElementUpdateMethodDataEntity,
    BulkUpdateElementUpdateMethodReturnEntity,
    BulkUpdateTimelineActionHistoryEntity,
    BulkUpdateTimelineElementEntity,
    UpdatedSectionDataEntity,
    UpdateElementDataEntity,
    UpdateElementUpdateMethod,
    UpdateElementUpdateMethodErrorEntity,
    UpdateMethodElementEntity,
)
from work_progress_v2.domain.services.day_wise import WorkProgressDayWiseService
from work_progress_v2.domain.services.locking import WorkProgressLockingService
from work_progress_v2.domain.services.prepare import WorkProgressPrepareService
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService
from work_progress_v2.domain.services.scope_timeline import WorkProgressScopeTimelineInteractor
from work_progress_v2.domain.services.status import WorkProgressStatusService
from work_progress_v2.interface.exceptions import WorkProgressVersionMismatch

logger = structlog.get_logger(__name__)


class WorkProgressBulkUpdateService(WorkProgressPrepareService):
    class Exception(BaseValidationError):
        pass

    class CancelledElementUpdateException(Exception):
        pass

    class NoChangeInUpdateMethodException(Exception):
        pass

    class UpdateMethodUpdateException(Exception):
        pass

    class WorkProgressInExecutionCompletedException(Exception):
        pass

    class WorkProgressElementUpdateException(Exception):
        pass

    class WorkProgressVersionMismatch(Exception):
        pass

    class WorkProgressElementTimelineException(Exception):
        pass

    def __init__(
        self,
        repo: WorkProgressAbstractRepo,
        user_entity: ProjectUserEntity,
        status_service: WorkProgressStatusService,
        project_service: WorkProgressToProjectService,
        scope_data_service: WorkProgressScopeDataService,
        scope_timeline_interactor: WorkProgressScopeTimelineInteractor,
        day_wise_service: WorkProgressDayWiseService,
        locking_service: WorkProgressLockingService,
        cache: WorkProgressCache,
    ):
        self.repo = repo
        self.user_entity = user_entity
        self.status_service = status_service
        self.project_service = project_service
        self.scope_data_service = scope_data_service
        self.scope_timeline_interactor = scope_timeline_interactor
        self.day_wise_service = day_wise_service
        self.cache = cache
        self.org_id = user_entity.org_id
        self.locking_service = locking_service

    def validate_element_status(self, element_status: BoqElementStatus):
        if element_status == BoqElementStatus.CANCELLED:
            raise self.CancelledElementUpdateException("Cancelled element can not be updated")

    def update_element_update_method(
        self,
        element: UpdateMethodElementEntity,
        incoming_update_method: ItemTypeUpdateMethodChoices,
    ) -> UpdateElementUpdateMethod:
        self.validate_element_status(element.element_status)

        if element.update_method == incoming_update_method:
            raise self.NoChangeInUpdateMethodException("No change in update method")

        if (
            element.update_method == ItemTypeUpdateMethodChoices.MILESTONE
            and incoming_update_method == ItemTypeUpdateMethodChoices.PERCENTAGE
        ):
            raise self.UpdateMethodUpdateException(
                "Element have update method as milestone, can not be updated to percentage"
            )

        if (
            element.update_method == ItemTypeUpdateMethodChoices.PERCENTAGE
            and incoming_update_method == ItemTypeUpdateMethodChoices.MILESTONE
        ):
            raise self.UpdateMethodUpdateException(
                "Element have update method as percentage, can not be updated to milestone"
            )

        try:
            self.status_service.check_project_status_in_execution_completed()
        except WorkProgressStatusService.WorkProgressInExecutionCompletedException:
            raise self.WorkProgressInExecutionCompletedException(
                "Please Unmark execution completed in work progress section before this action"
            )
        return UpdateElementUpdateMethod(
            id=element.id,
            update_method=incoming_update_method,
        )

    def bulk_update_elements_update_method(
        self,
        element_ids: list[int],
        incoming_update_method: ItemTypeUpdateMethodChoices,
        is_vendor: bool,
    ) -> BulkUpdateElementUpdateMethodReturnEntity:
        logger.info(
            "Bulk update elements update method service starting",
            element_ids=element_ids,
            incoming_update_method=incoming_update_method,
            is_vendor=is_vendor,
        )

        if not element_ids:
            return BulkUpdateElementUpdateMethodReturnEntity(error_list=[])

        error_list: list[UpdateElementUpdateMethodErrorEntity] = []

        update_elements: list[UpdateElementDataEntity] = []

        element_data = self.repo.get_elements_for_update_method_entity(element_ids=element_ids)

        for element in element_data:
            try:
                data = self.update_element_update_method(
                    element=element,
                    incoming_update_method=incoming_update_method,
                )
                update_elements.append(
                    UpdateElementDataEntity(
                        id=element.id,
                        update_method=data.update_method,
                        progress_percentage=decimal.Decimal(0),
                        is_unlocked=True,
                        action=WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED,
                        current_visible_updated_method=data.update_method,
                    )
                )
            except WorkProgressBulkUpdateService.NoChangeInUpdateMethodException:
                continue
            except WorkProgressBulkUpdateService.WorkProgressInExecutionCompletedException as e:
                error_list.append(UpdateElementUpdateMethodErrorEntity(element_id=element.id, message=str(e)))
            except WorkProgressBulkUpdateService.CancelledElementUpdateException as e:
                error_list.append(UpdateElementUpdateMethodErrorEntity(element_id=element.id, message=str(e)))
            except WorkProgressBulkUpdateService.UpdateMethodUpdateException as e:
                error_list.append(UpdateElementUpdateMethodErrorEntity(element_id=element.id, message=str(e)))

        logger.info("Error list", error_list=error_list)

        if error_list:
            return BulkUpdateElementUpdateMethodReturnEntity(error_list=error_list)

        if not update_elements:
            return BulkUpdateElementUpdateMethodReturnEntity(error_list=[])

        try:
            updated_element_data = self.repo.bulk_update_elements(updated_data=update_elements)

            logger.info("Updated element data", updated_element_data=updated_element_data)

            total_completion_amount_diff = decimal.Decimal(0)
            total_is_updated_today = 0
            section_updated_data: dict[int, UpdatedSectionDataEntity] = {}

            for updated_data in updated_element_data:
                total_completion_amount_diff += updated_data.completion_amount_diff
                if updated_data.is_updated_today:
                    total_is_updated_today += 1

                curr_section_updated_data = None

                if updated_data.section_id not in section_updated_data:
                    section_updated_data[updated_data.section_id] = UpdatedSectionDataEntity(
                        id=updated_data.section_id,
                        completion_amount_diff=decimal.Decimal(0),
                        progress_percentage_diff=decimal.Decimal(0),
                    )

                curr_section_updated_data = section_updated_data[updated_data.section_id]

                curr_section_updated_data.completion_amount_diff += updated_data.completion_amount_diff
                curr_section_updated_data.progress_percentage_diff += updated_data.progress_percentage_diff

            updated_scope_data = self.cache.get_updated_scope_data(
                scope_data_service=self.scope_data_service,
                completion_amount_diff=total_completion_amount_diff,
                total_element_updated_today=total_is_updated_today,
            )

            updated_section_data, updated_section_data_cache = self.cache.get_updated_section_data(
                scope_data_service=self.scope_data_service,
                section_data=[
                    UpdatedSectionDataEntity(
                        id=section.id,
                        completion_amount_diff=section.completion_amount_diff,
                        progress_percentage_diff=section.progress_percentage_diff,
                    )
                    for section in section_updated_data.values()
                ],
            )

            self.cache.validate_work_progress_version()
            new_version = self.cache.update_scope_and_section_cache(
                scope_data=updated_scope_data,
                section_data=updated_section_data_cache,
            )

            item_type_configs = self.cache.get_item_type_config()

            element_daywise_data_entities = self._prepare_element_daywise_update_entities(
                updated_data=updated_element_data,
                item_type_configs=item_type_configs,
            )

            self.day_wise_service.update_project_daywise_data(data=updated_scope_data)
            self.day_wise_service.update_section_daywise_data(data=updated_section_data_cache)
            self.day_wise_service.update_element_daywise_data(data=element_daywise_data_entities)
            self.status_service.update_work_report_status()
            self.status_service.update_project_status(is_vendor=is_vendor)
            self.project_service.update_progress_percentage(progress_percentage=updated_scope_data.total_progress)

            scope_timeline_data = self.scope_timeline_interactor.get_scope_timeline_data(
                scope_data=updated_scope_data,
            )

            item_type_configs = self.cache.get_item_type_config()

            return BulkUpdateElementUpdateMethodReturnEntity(
                error_list=[],
                version=new_version,
                data=BulkUpdateElementUpdateMethodDataEntity(
                    element_list=[
                        self._prepare_element_list_detail(
                            element=updated_data.updated_element_entity,
                            item_type_configs=item_type_configs,
                        )
                        for updated_data in updated_element_data
                    ],
                    scope_data=updated_scope_data,
                    section_data=updated_section_data,
                    scope_timeline_data=scope_timeline_data,
                ),
            )
        except WorkProgressVersionMismatch as e:
            raise self.WorkProgressVersionMismatch(str(e))
        except ValidationError:
            raise self.WorkProgressElementUpdateException("Failed to update work progress elements")

    def bulk_update_work_progress_element(
        self,
        element_to_updated_fields_map: dict[BulkUpdateTimelineElementEntity, list[str]],
        action_histories: list[BulkUpdateTimelineActionHistoryEntity],
    ):
        """
        When Boq Elements' (quantity, item_type_id, uom) are updated in bulk,
        this method is invoked to create timeline and percentage history
        for the linked work progress elements.
        """
        logger.info("Bulk update work progress elements service started.")
        update_data_entities = self._prepare_updated_data_entities(
            element_to_updated_fields_map=element_to_updated_fields_map
        )
        if not update_data_entities:
            logger.info("No elements to update.")
            return
        element_id_to_action_history_map = {history.boq_element_id: history.id for history in action_histories}
        for update_data in update_data_entities:
            action_history_id = element_id_to_action_history_map.get(update_data.id)
            assert action_history_id is not None
            update_data.boq_element_action_history_id = action_history_id
        try:
            updated_element_data = self.repo.bulk_update_elements(updated_data=update_data_entities)
            total_completion_amount_diff = decimal.Decimal(0)
            total_is_updated_today = 0
            section_updated_data: dict[int, UpdatedSectionDataEntity] = {}

            for updated_data in updated_element_data:
                total_completion_amount_diff += updated_data.completion_amount_diff
                if updated_data.is_updated_today:
                    total_is_updated_today += 1

                curr_section_updated_data = None

                if updated_data.section_id not in section_updated_data:
                    section_updated_data[updated_data.section_id] = UpdatedSectionDataEntity(
                        id=updated_data.section_id,
                        completion_amount_diff=decimal.Decimal(0),
                        progress_percentage_diff=decimal.Decimal(0),
                    )

                curr_section_updated_data = section_updated_data[updated_data.section_id]

                curr_section_updated_data.completion_amount_diff += updated_data.completion_amount_diff
                curr_section_updated_data.progress_percentage_diff += updated_data.progress_percentage_diff

            updated_scope_data = self.cache.get_updated_scope_data(
                scope_data_service=self.scope_data_service,
                completion_amount_diff=total_completion_amount_diff,
                total_element_updated_today=total_is_updated_today,
                cache=False,
            )

            _, updated_section_data_cache = self.cache.get_updated_section_data(
                scope_data_service=self.scope_data_service,
                section_data=[
                    UpdatedSectionDataEntity(
                        id=section.id,
                        completion_amount_diff=section.completion_amount_diff,
                        progress_percentage_diff=section.progress_percentage_diff,
                    )
                    for section in section_updated_data.values()
                ],
            )

            self.cache.update_scope_and_section_cache(
                scope_data=updated_scope_data,
                section_data=updated_section_data_cache,
            )

            item_type_configs = self.cache.get_item_type_config()

            element_daywise_data_entities = self._prepare_element_daywise_update_entities(
                updated_data=updated_element_data, item_type_configs=item_type_configs
            )

            self.day_wise_service.update_project_daywise_data(data=updated_scope_data)
            self.day_wise_service.update_section_daywise_data(data=updated_section_data_cache)
            self.day_wise_service.update_element_daywise_data(data=element_daywise_data_entities)
            self.project_service.update_progress_percentage(progress_percentage=updated_scope_data.total_progress)
        except ValidationError:
            raise self.WorkProgressElementUpdateException("Failed to update work progress elements.")
        logger.info("Bulk update work progress elements service completed.")

    def _calculate_boq_element_update_method(
        self, element: BulkUpdateTimelineElementEntity, item_type_configs: ItemTypeConfigCacheEntity
    ) -> ItemTypeUpdateMethodChoices:
        if element.work_progress_element.update_method:
            return ItemTypeUpdateMethodChoices(element.work_progress_element.update_method)

        if element.item_type_id:
            element_item_type_config = item_type_configs.item_types.get(element.item_type_id)

            assert element_item_type_config is not None, "Item type config not found in cache"

            return ItemTypeUpdateMethodChoices(element_item_type_config.default_update_method)
        else:
            return ItemTypeUpdateMethodChoices(item_type_configs.default_config.default_update_method)

    def _prepare_updated_data_entities(
        self, element_to_updated_fields_map: dict[BulkUpdateTimelineElementEntity, list[str]]
    ) -> list[UpdateElementDataEntity]:
        updated_data_entities: list[UpdateElementDataEntity] = []
        wp_element_history_updated_fields = ["quantity", "item_type_id", "uom"]
        item_type_configs = self.cache.get_item_type_config()

        for boq_element, updated_fields in element_to_updated_fields_map.items():
            if not updated_fields:
                continue

            boq_updated_fields = list(set(updated_fields).intersection(set(wp_element_history_updated_fields)))
            current_visible_updated_method = self._calculate_boq_element_update_method(
                element=boq_element, item_type_configs=item_type_configs
            )

            if "item_type_id" in updated_fields or "uom" in updated_fields:
                data = UpdateElementDataEntity(
                    id=boq_element.id,
                    progress_percentage=decimal.Decimal(0),
                    progress_percentage_input=None,
                    progress_quantity_input=None,
                    milestone_input_id=None,
                    is_unlocked=True,
                    action=WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED,
                    boq_element_updated_fields=boq_updated_fields,
                    current_visible_updated_method=current_visible_updated_method,
                )
                if "item_type_id" in updated_fields:
                    data.update_method = None
                updated_data_entities.append(data)
            elif "quantity" in updated_fields:
                progress_percent = boq_element.work_progress_element.progress_percentage
                progress_quantity_input = boq_element.work_progress_element.progress_quantity_input
                is_progress_quantity_input_updated = False

                if current_visible_updated_method == ItemTypeUpdateMethodChoices.QUANTITY:
                    progress_quantity_input = (
                        boq_element.work_progress_element.progress_quantity_input or decimal.Decimal(0)
                    )
                    if boq_element.quantity == decimal.Decimal(0):
                        progress_percent = decimal.Decimal(0)
                        progress_quantity_input = decimal.Decimal(0)
                    elif boq_element.quantity < progress_quantity_input:
                        progress_percent = decimal.Decimal(100)
                        progress_quantity_input = boq_element.quantity
                        is_progress_quantity_input_updated = True
                    else:
                        progress_percent = progress_quantity_input / boq_element.quantity * decimal.Decimal(100)

                updated_data_entities.append(
                    UpdateElementDataEntity(
                        id=boq_element.id,
                        progress_percentage=progress_percent,
                        progress_quantity_input=progress_quantity_input,
                        is_unlocked=True,
                        action=WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED,
                        boq_element_updated_fields=boq_updated_fields,
                        is_progress_quantity_input_updated_by_boq_update=is_progress_quantity_input_updated,
                        current_visible_updated_method=current_visible_updated_method,
                    )
                )
        return updated_data_entities


class WorkProgressBulkUpdateInteractor:
    class Exception(BaseValidationError):
        pass

    class WorkProgressElementUpdateException(Exception):
        pass

    class WorkProgressVersionMismatch(Exception):
        pass

    def __init__(self, service: WorkProgressBulkUpdateService):
        self.service = service

    def bulk_update_elements_update_method(
        self,
        element_ids: list[int],
        incoming_update_method: ItemTypeUpdateMethodChoices,
        is_vendor: bool,
    ):
        try:
            return self.service.bulk_update_elements_update_method(
                element_ids=element_ids,
                incoming_update_method=incoming_update_method,
                is_vendor=is_vendor,
            )
        except WorkProgressBulkUpdateService.WorkProgressVersionMismatch as e:
            raise self.WorkProgressVersionMismatch(str(e))
        except WorkProgressBulkUpdateService.WorkProgressElementUpdateException as e:
            raise self.WorkProgressElementUpdateException(str(e))

    def bulk_update_work_progress_element(
        self,
        element_to_updated_fields_map: dict[BulkUpdateTimelineElementEntity, list[str]],
        action_histories: list[BulkUpdateTimelineActionHistoryEntity],
    ):
        """
        When BoqElementActionHistory is created, this method is invoked to create timeline and percentage history
        for the linked work progress elements.
        """
        try:
            self.service.bulk_update_work_progress_element(
                element_to_updated_fields_map=element_to_updated_fields_map,
                action_histories=action_histories,
            )
        except WorkProgressBulkUpdateService.WorkProgressElementTimelineException as e:
            raise self.WorkProgressElementUpdateException(str(e))
