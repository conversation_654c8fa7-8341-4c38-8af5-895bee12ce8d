from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import SectionListEntity
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService


class WorkProgressScopeDataInteractor:
    def __init__(self, service: WorkProgressScopeDataService, cache: WorkProgressCache):
        self.service = service
        self.cache = cache

    def get_scope_data(self):
        return self.cache.get_scope_data(scope_data_service=self.service, cache=False)

    def get_section_data(self):
        data = self.cache.get_section_data(scope_data_service=self.service, cache=False)

        return [
            SectionListEntity(
                id=section.id,
                progress_percentage=section.progress_percentage,
                element_count=section.element_count,
                name=section.name,
                total_amount=section.total_amount,
            )
            for section in data
        ]
