from authorization.domain.constants import Permissions
from authorization.enums import PermissionLevelEnum
from work_progress_v2.domain.caches import WorkProgressCache


class WorkProgressPermissionService:
    def __init__(self, cache: WorkProgressCache):
        self.cache = cache

    def is_action_permitted(self, permission: Permissions, permission_type: PermissionLevelEnum):
        if permission_type == PermissionLevelEnum.PROJECT:
            return permission in self.cache.get_user_project_permissions()
        elif permission_type == PermissionLevelEnum.ORG:
            return permission in self.cache.get_user_org_permissions()
        else:
            return (
                permission in self.cache.get_user_project_permissions()
                and permission in self.cache.get_user_org_permissions()
            )
