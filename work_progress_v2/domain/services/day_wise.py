from work_progress_v2.domain.abstract_repos import WorkProgressDayWiseAbstractRepo
from work_progress_v2.domain.entities import (
    ScopeProgressEntity,
    SectionNameTotalProgressEntity,
    UpdateElementOutputEntity,
)


class WorkProgressDayWiseService:
    def __init__(self, repo: WorkProgressDayWiseAbstractRepo):
        self.repo = repo

    def update_project_daywise_data(self, data: ScopeProgressEntity):
        self.repo.update_project_daywise_data(data=data)

    def update_section_daywise_data(self, data: list[SectionNameTotalProgressEntity]):
        self.repo.update_section_daywise_data(data=data)

    def update_element_daywise_data(self, data: list[UpdateElementOutputEntity]):
        self.repo.update_element_daywise_data(data=data)
