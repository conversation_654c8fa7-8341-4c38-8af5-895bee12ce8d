import decimal
from collections import defaultdict

import structlog
from django.utils import timezone

from boq.data.models import BoqSection
from common.utils import get_local_time
from work_progress_v2.domain.helper_entities import (
    SectionTodayProgressElementEntity,
    SectionTodayProgressEntity,
    SectionTotalProgressElementEntity,
    SectionTotalProgressEntity,
    TodayProgressDataEntity,
    TotalProgressDataEntity,
)

logger = structlog.get_logger(__name__)


class WorkProgressScopeDataHelper:
    @staticmethod
    def get_section_total_progress(
        elements: list[SectionTotalProgressElementEntity],
    ) -> list[SectionTotalProgressEntity]:
        """
        Calculate weighted average progress for each section based on element quantities and amounts.
        Algorithm:
        1. Groups elements by section_id (defaults to 0 for None)
        2. For each section:
           - If all quantities are 0: Progress = average of element percentages
           - Otherwise: Progress = weighted average based on amounts, excluding zero quantity items
           - If all amounts are 0: Progress = 0
        """

        if not elements:
            return []

        section_element_mapping: dict[int, list[SectionTotalProgressElementEntity]] = defaultdict(list)

        for element in elements:
            section_element_mapping[element.section_id or BoqSection.DEFAULT_SECTION_ID].append(element)

        section_total_progress: list[SectionTotalProgressEntity] = []

        for section_id, elements in section_element_mapping.items():
            total_quantity = decimal.Decimal(0)
            total_progress_percentage = decimal.Decimal(0)
            total_amount = decimal.Decimal(0)
            total_completion_amount = decimal.Decimal(0)

            for element in elements:
                total_quantity += element.quantity
                total_progress_percentage += element.progress_percentage

                if element.quantity != 0:
                    total_completion_amount += decimal.Decimal(element.amount / 100 * (element.progress_percentage))
                    total_amount += element.amount

            if total_quantity == 0:
                section_progress = (
                    decimal.Decimal(total_progress_percentage / len(elements)) if len(elements) else decimal.Decimal(0)
                )
                section_total_progress.append(
                    SectionTotalProgressEntity(
                        id=section_id,
                        progress_percentage=section_progress,
                        total_amount=decimal.Decimal(0),
                        total_completion_amount=decimal.Decimal(0),
                        is_quantity_zero=True,
                        element_count=len(elements),
                    )
                )

                continue

            if total_amount == 0:
                section_total_progress.append(
                    SectionTotalProgressEntity(
                        id=section_id,
                        progress_percentage=decimal.Decimal(0),
                        total_amount=decimal.Decimal(0),
                        total_completion_amount=decimal.Decimal(0),
                        is_quantity_zero=False,
                        element_count=len(elements),
                    )
                )
                continue

            section_progress = decimal.Decimal(total_completion_amount / total_amount * 100)
            section_total_progress.append(
                SectionTotalProgressEntity(
                    id=section_id,
                    progress_percentage=section_progress,
                    total_amount=total_amount,
                    total_completion_amount=total_completion_amount,
                    is_quantity_zero=False,
                    element_count=len(elements),
                )
            )

        return section_total_progress

    @staticmethod
    def get_total_progress(
        elements: list[SectionTotalProgressElementEntity],
    ) -> TotalProgressDataEntity:
        section_total_progress = WorkProgressScopeDataHelper.get_section_total_progress(elements=elements)

        total_completion_amount = decimal.Decimal(0)
        total_amount = decimal.Decimal(0)

        for section in section_total_progress:
            if section.is_quantity_zero:
                continue

            total_completion_amount += section.total_completion_amount
            total_amount += section.total_amount

        total_progress_data = TotalProgressDataEntity(
            progress_percentage=(
                decimal.Decimal(total_completion_amount / total_amount * 100) if total_amount else decimal.Decimal(0)
            ),
            completion_amount=total_completion_amount,
            total_amount=total_amount,
            total_element_count=len(elements),
        )
        logger.info(total_progress_data=total_progress_data)
        return total_progress_data

    @staticmethod
    def get_section_today_progress(
        elements: list[SectionTodayProgressElementEntity],
    ) -> list[SectionTodayProgressEntity]:
        """
        Calculate weighted average today's progress for each section based on element quantities and amounts.
        Algorithm:
        1. Groups elements by section_id (defaults to 0 for None)
        2. For each section:
           - If all quantities are 0: Progress = average of element percentages
           - Otherwise: Progress = weighted average based on amounts, excluding zero quantity items
           - If all amounts are 0: Progress = 0
        """
        current_date = get_local_time(timezone.now()).date()

        if not elements:
            return []

        section_element_mapping: dict[int, list[SectionTodayProgressElementEntity]] = defaultdict(list)

        for element in elements:
            section_element_mapping[element.section_id or BoqSection.DEFAULT_SECTION_ID].append(element)

        section_total_progress: list[SectionTodayProgressEntity] = []

        for section_id, elements in section_element_mapping.items():
            total_quantity = decimal.Decimal(0)
            total_progress_percentage = decimal.Decimal(0)
            total_amount = decimal.Decimal(0)
            total_completion_amount = decimal.Decimal(0)
            total_today_updated_element_count = 0

            for element in elements:
                total_quantity += element.quantity

                progress_updated_today = (
                    element.progress_updated_at and element.progress_updated_at.date() == current_date
                )
                attachment_uploaded_today = (
                    element.attachment_uploaded_at and element.attachment_uploaded_at.date() == current_date
                )

                if progress_updated_today:
                    total_progress_percentage += element.progress_percentage - element.previous_day_progress_percentage

                if progress_updated_today or attachment_uploaded_today:
                    total_today_updated_element_count += 1

                if element.quantity != 0:
                    if progress_updated_today:
                        total_completion_amount += decimal.Decimal(
                            element.amount
                            / 100
                            * (element.progress_percentage - element.previous_day_progress_percentage)
                        )
                    total_amount += element.amount

            if total_quantity == 0:
                section_progress = (
                    decimal.Decimal(total_progress_percentage / len(elements)) if len(elements) else decimal.Decimal(0)
                )
                section_total_progress.append(
                    SectionTodayProgressEntity(
                        id=section_id,
                        progress_percentage=section_progress,
                        total_amount=decimal.Decimal(0),
                        total_completion_amount=decimal.Decimal(0),
                        is_quantity_zero=True,
                        total_today_updated_element_count=total_today_updated_element_count,
                        element_count=len(elements),
                    )
                )
                continue

            if total_amount == 0:
                section_total_progress.append(
                    SectionTodayProgressEntity(
                        id=section_id,
                        progress_percentage=decimal.Decimal(0),
                        total_amount=decimal.Decimal(0),
                        total_completion_amount=decimal.Decimal(0),
                        is_quantity_zero=False,
                        total_today_updated_element_count=total_today_updated_element_count,
                        element_count=len(elements),
                    )
                )
                continue

            section_progress = (
                decimal.Decimal(total_completion_amount / total_amount * 100) if total_amount else decimal.Decimal(0)
            )

            section_total_progress.append(
                SectionTodayProgressEntity(
                    id=section_id,
                    progress_percentage=section_progress,
                    total_amount=total_amount,
                    total_completion_amount=total_completion_amount,
                    is_quantity_zero=False,
                    total_today_updated_element_count=total_today_updated_element_count,
                    element_count=len(elements),
                )
            )

        return section_total_progress

    @staticmethod
    def get_today_progress(
        elements: list[SectionTodayProgressElementEntity],
    ) -> TodayProgressDataEntity:
        section_today_progress = WorkProgressScopeDataHelper.get_section_today_progress(elements=elements)

        today_completion_amount = decimal.Decimal(0)
        total_amount = decimal.Decimal(0)
        total_today_updated_element_count = 0

        for section in section_today_progress:
            total_today_updated_element_count += section.total_today_updated_element_count

            if section.is_quantity_zero:
                continue

            today_completion_amount += section.total_completion_amount
            total_amount += section.total_amount

        today_progress_data = TodayProgressDataEntity(
            progress_percentage=(
                decimal.Decimal(today_completion_amount / total_amount * 100) if total_amount else decimal.Decimal(0)
            ),
            completion_amount=today_completion_amount,
            updated_element_count=total_today_updated_element_count,
        )
        logger.info(today_progress_data=today_progress_data)
        return today_progress_data
