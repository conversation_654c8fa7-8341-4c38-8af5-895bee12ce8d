import datetime
from functools import partial

from django.db.transaction import on_commit
from django.utils import timezone

from common.events import Events
from common.events.progress_report import (
    MarkExecutionCompletedEventData,
    WorkProgressExportReportEventData,
    WorkProgressExportReportPDFGeneratedEventData,
    WorkProgressGenerateReportEventData,
    WorkProgressGenerateReportPDFGeneratedEventData,
)
from common.events.services import trigger_event
from core.entities import ProjectUserEntity


def trigger_mark_execution_completed(user_entity: ProjectUserEntity):
    on_commit(
        partial(
            trigger_event,
            event=Events.WORK_PROGRESS_MARK_EXECUTION_COMPLETED,
            event_data=MarkExecutionCompletedEventData(
                created_at=str(timezone.now().date()),
                project_id=user_entity.project_id,
                user_id=user_entity.user_id,
                org_id=user_entity.org_id,
            ),
        )
    )


def trigger_work_progress_report_generated(
    report_id: int,
    user_entity: ProjectUserEntity,
    should_notify: bool = False,
):
    on_commit(
        partial(
            trigger_event,
            event=Events.WORK_PROGRESS_REPORT_GENERATED,
            event_data=WorkProgressGenerateReportEventData(
                project_id=user_entity.project_id,
                user_id=user_entity.user_id,
                created_at=str(timezone.now().date()),
                dpr_id=report_id,
                org_id=user_entity.org_id,
                should_notify=should_notify,
            ),
        )
    )


def trigger_work_progress_generate_report_pdf_generated(
    report_id: int,
    user_entity: ProjectUserEntity,
):
    on_commit(
        partial(
            trigger_event,
            event=Events.WORK_PROGRESS_REPORT_PDF_GENERATED,
            event_data=WorkProgressGenerateReportPDFGeneratedEventData(
                project_id=user_entity.project_id,
                user_id=user_entity.user_id,
                created_at=str(timezone.now().date()),
                dpr_id=report_id,
                org_id=user_entity.org_id,
            ),
        )
    )


def trigger_work_progress_export_report_generated(
    user_entity: ProjectUserEntity,
    start_date: datetime.date,
    end_date: datetime.date,
    download_id: int,
    exported_by_name: str,
    exported_by_photo: str | None,
    is_vendor_report: bool,
    vendor_id: int | None,
    sync: bool = False,
):
    on_commit(
        partial(
            trigger_event,
            event=Events.WORK_PROGRESS_EXPORT_REPORT_GENERATED,
            event_data=WorkProgressExportReportEventData(
                created_at=str(timezone.now().date()),
                project_id=user_entity.project_id,
                user_id=user_entity.user_id,
                org_id=user_entity.org_id,
                start_date=start_date,
                end_date=end_date,
                download_id=download_id,
                exported_by_name=exported_by_name,
                exported_by_photo=exported_by_photo,
                is_vendor_report=is_vendor_report,
                vendor_id=vendor_id,
                sync=sync,
            ),
            sync=sync,
        )
    )


def trigger_work_progress_export_report_pdf_generated(
    download_id: int,
    user_entity: ProjectUserEntity,
    sync: bool = False,
):
    on_commit(
        partial(
            trigger_event,
            event=Events.WORK_PROGRESS_EXPORT_REPORT_PDF_GENERATED,
            event_data=WorkProgressExportReportPDFGeneratedEventData(
                created_at=str(timezone.now().date()),
                project_id=user_entity.project_id,
                user_id=user_entity.user_id,
                org_id=user_entity.org_id,
                download_id=download_id,
            ),
            sync=sync,
        )
    )
