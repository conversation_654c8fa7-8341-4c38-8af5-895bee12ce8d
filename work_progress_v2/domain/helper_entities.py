import datetime
import decimal

from common.pydantic.base_model import BaseModelV2
from common.pydantic.custom_fields import HashIdInt
from element.data.choices import ItemTypeUpdateMethodChoices


class SectionTotalProgressElementEntity(BaseModelV2):
    id: HashIdInt
    section_id: HashIdInt | None = None
    quantity: decimal.Decimal
    amount: decimal.Decimal
    progress_percentage: decimal.Decimal
    update_method: ItemTypeUpdateMethodChoices | None = None


class SectionTodayProgressElementEntity(SectionTotalProgressElementEntity):
    previous_day_progress_percentage: decimal.Decimal
    progress_updated_at: datetime.datetime | None
    attachment_uploaded_at: datetime.datetime | None


class TotalProgressDataEntity(BaseModelV2):
    progress_percentage: decimal.Decimal
    completion_amount: decimal.Decimal
    total_amount: decimal.Decimal
    total_element_count: int


class TodayProgressDataEntity(BaseModelV2):
    progress_percentage: decimal.Decimal
    completion_amount: decimal.Decimal
    updated_element_count: int


class SectionTotalProgressEntity(BaseModelV2):
    id: HashIdInt
    progress_percentage: decimal.Decimal
    total_amount: decimal.Decimal
    total_completion_amount: decimal.Decimal
    is_quantity_zero: bool
    element_count: int


class SectionTodayProgressEntity(SectionTotalProgressEntity):
    total_today_updated_element_count: int
