# Generated by Django 3.2.15 on 2025-05-05 10:43

import common.mixins
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0164_auto_20250502_1116'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('project', '0118_auto_20250502_1116'),
        ('work_progress_v2', '0003_auto_20250502_1259'),
    ]

    operations = [
        migrations.CreateModel(
            name='WPReleaseQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('action_histories', models.JSONField(default=list)),
                ('element_to_updated_fields_map', models.JSONField(default=dict)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_wpreleasequeue_created', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_release_queue', to='core.organization')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_release_queue', to='project.project')),
            ],
            options={
                'verbose_name': 'Work Progress Release Queue',
                'verbose_name_plural': 'Work Progress Release Queues',
                'db_table': 'work_progress_release_queue',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
    ]
