# Generated by Django 3.2.15 on 2025-05-02 12:59

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('work_progress_v2', '0002_trigger_wp_element_create'),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name='projectreportconfig',
            name='config',
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name='projectreportconfighistory',
            name='config',
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name='reportconfig',
            name='config',
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name='reportconfighistory',
            name='config',
            field=models.JSONField(default=list),
        ),
        migrations.AlterField(
            model_name='workprogressinventorystockitem',
            name='stock_value',
            field=models.DecimalField(decimal_places=4, max_digits=30, validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]
