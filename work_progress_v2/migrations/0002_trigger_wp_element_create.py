# Generated by Django 3.2.25 on 2025-05-02 11:38

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("work_progress_v2", "0001_initial"),
    ]

    operations = [
        migrations.RunSQL(
            """
            CREATE OR REPLACE FUNCTION create_work_progress_element()
            RET<PERSON>NS TRIGGER AS $$
            BEGIN
                INSERT INTO work_progress_element (
                    boq_element_id,
                    progress_percentage
                )
                SELECT
                    NEW.id,
                    0.00;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """,
            reverse_sql="""
                DROP FUNCTION IF EXISTS create_work_progress_element();
            """,
        ),
        migrations.RunSQL(
            """
            CREATE TRIGGER tr_create_work_progress_element
            AFTER INSERT ON boq_elements
            FOR EACH ROW
            EXECUTE FUNCTION create_work_progress_element();
            """,
            reverse_sql="DROP TRIGGER IF EXISTS tr_create_work_progress_element ON boq_elements;",
        ),
    ]
