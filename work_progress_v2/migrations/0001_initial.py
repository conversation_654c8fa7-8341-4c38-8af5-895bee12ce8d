# Generated by Django 3.2.25 on 2025-05-02 11:36

import common.helpers
import common.mixins
from decimal import Decimal
from django.conf import settings
import django.contrib.postgres.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('element', '0050_auto_20250227_1211'),
        ('progressreport', '0016_auto_20250228_0812'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('project', '0118_auto_20250502_1116'),
        ('inventory', '0007_auto_20250502_0950'),
        ('project_schedule', '0007_auto_20250502_1116'),
        ('boq', '0069_auto_20250403_1027'),
        ('core', '0164_auto_20250502_1116'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemTypeMileStone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=50)),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('is_visible', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestone_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestone_deleted', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Item Type Milestone',
                'verbose_name_plural': 'Item Type Milestones',
                'db_table': 'item_type_milestone',
                'ordering': ['percentage'],
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressElement',
            fields=[
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('boq_element', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, primary_key=True, related_name='work_progress_element', serialize=False, to='boq.boqelement')),
                ('progress_percentage', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=5, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('progress_quantity_input', models.DecimalField(blank=True, decimal_places=4, default=None, max_digits=10, null=True)),
                ('progress_percentage_input', models.PositiveSmallIntegerField(blank=True, default=None, null=True, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('update_method', models.CharField(blank=True, choices=[('percentage', 'Percentage'), ('quantity', 'Quantity'), ('milestone', 'Milestone')], default=None, max_length=50, null=True)),
                ('unlocked_at', models.DateTimeField(blank=True, default=None, null=True)),
                ('progress_updated_at', models.DateTimeField(blank=True, default=None, null=True)),
                ('input_progress_updated_at', models.DateTimeField(blank=True, default=None, null=True)),
                ('attachment_uploaded_at', models.DateTimeField(blank=True, default=None, null=True)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresselement_deleted', to=settings.AUTH_USER_MODEL)),
                ('milestone_input', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.RESTRICT, to='work_progress_v2.itemtypemilestone')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresselement_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Work Progress Element',
                'verbose_name_plural': 'Work Progress Elements',
                'db_table': 'work_progress_element',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressElementTimeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('action', models.CharField(choices=[('input_quantity_updated', 'Input Quantity Updated'), ('input_percentage_updated', 'Input Percentage Updated'), ('input_milestone_updated', 'Input Milestone Updated'), ('attachment_updated', 'Attachment Updated'), ('update_method_updated', 'Update Method Updated'), ('boq_element_updated', 'BOQ Element Updated')], max_length=100)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresselementtimeline_created', to=settings.AUTH_USER_MODEL)),
                ('element', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='timeline', to='work_progress_v2.workprogresselement')),
            ],
            options={
                'verbose_name': 'Work Progress Element Timeline',
                'verbose_name_plural': 'Work Progress Element Timelines',
                'db_table': 'work_progress_element_timeline',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('today_progress', models.DecimalField(blank=True, decimal_places=4, max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('total_progress', models.DecimalField(blank=True, decimal_places=4, default=Decimal('0'), max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('status_type', models.CharField(blank=True, choices=[('on_time', 'On Time'), ('delayed', 'Delayed'), ('overdue', 'Overdue'), ('not_set', 'Not Set')], max_length=50, null=True)),
                ('delay_days', models.IntegerField(blank=True, null=True)),
                ('planned_end_date', models.DateField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogressschedule_created', to=settings.AUTH_USER_MODEL)),
                ('progress_report', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='schedule', to='progressreport.progressreport')),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_schedules', to='project_schedule.projectschedule')),
            ],
            options={
                'verbose_name': 'Work Progress Schedule',
                'verbose_name_plural': 'Work Progress Schedules',
                'db_table': 'work_progress_schedules',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressScheduleActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=500)),
                ('wbs', models.CharField(max_length=50)),
                ('status', models.CharField(blank=True, choices=[('on_time', 'On Time'), ('delayed', 'Delayed'), ('overdue', 'Overdue'), ('not_set', 'Not Set')], max_length=50, null=True)),
                ('delay_days', models.IntegerField(blank=True, null=True)),
                ('today_progress', models.DecimalField(decimal_places=4, default=Decimal('0'), max_digits=7, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('total_progress', models.DecimalField(decimal_places=4, default=Decimal('0'), max_digits=7, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('activity', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_schedule_activities', to='project_schedule.projectscheduleactivity')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogressscheduleactivity_created', to=settings.AUTH_USER_MODEL)),
                ('wp_schedule', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='activities', to='work_progress_v2.workprogressschedule')),
            ],
            options={
                'verbose_name': 'Work Progress Schedule Activity',
                'verbose_name_plural': 'Work Progress Schedule Activities',
                'db_table': 'work_progress_schedule_activities',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressTimelineAndBoqElementActionHistoryMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_fields', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=50), blank=True, default=list, size=None)),
                ('boq_element_action_history', models.OneToOneField(null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='timeline_mappings', to='boq.boqelementactionhistory')),
                ('boq_element_history', models.ForeignKey(null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='element_history_timeline_mappings', to='boq.boqelementhistory')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresstimelineandboqelementactionhistorymapping_created', to=settings.AUTH_USER_MODEL)),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='boq_element_action_history_mappings', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Timeline And Boq Element Action History Mapping',
                'verbose_name_plural': 'Timeline And Boq Element Action History Mappings',
                'db_table': 'work_progress_element_timeline_and_boq_element_action_history_mapping',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressScheduleActivityAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('file', models.FileField(blank=True, max_length=250, null=True, upload_to=common.helpers.get_upload_path)),
                ('thumbnail', models.FileField(blank=True, max_length=250, null=True, upload_to=common.helpers.get_upload_path)),
                ('name', models.CharField(max_length=500)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogressscheduleactivityattachment_uploaded', to=settings.AUTH_USER_MODEL)),
                ('wp_schedule', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='attachments', to='work_progress_v2.workprogressschedule')),
                ('wp_schedule_activity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='attachments', to='work_progress_v2.workprogressscheduleactivity')),
            ],
            options={
                'verbose_name': 'Work Progress Schedule Activity Attachment',
                'verbose_name_plural': 'Work Progress Schedule Activity Attachments',
                'db_table': 'work_progress_schedule_activity_attachments',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressInventoryStockItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('stock_value', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('item_name', models.CharField(max_length=300)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('uom_name', models.CharField(max_length=300)),
                ('type', models.CharField(choices=[('consumed', 'Consumed'), ('transferred_out', 'Transferred Out'), ('received', 'Received')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogressinventorystockitem_created', to=settings.AUTH_USER_MODEL)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_inventory_stock_items', to='inventory.inventorystockitem')),
                ('progress_report', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='inventory_stock_items', to='progressreport.progressreport')),
            ],
            options={
                'verbose_name': 'Inventory Stock Item',
                'verbose_name_plural': 'Inventory Stock Items',
                'db_table': 'work_progress_inventory_stock_items',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='UpdateMethodHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('update_method', models.CharField(choices=[('percentage', 'Percentage'), ('quantity', 'Quantity'), ('milestone', 'Milestone')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_updatemethodhistory_created', to=settings.AUTH_USER_MODEL)),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='update_method_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Update Method History',
                'verbose_name_plural': 'Update Method Histories',
                'db_table': 'work_progress_element_update_method_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='ReportConfigHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('config', models.JSONField(default=[])),
                ('config_type', models.CharField(choices=[('generate_report', 'Generate Report'), ('export_single_day_report', 'Export Single Day Report'), ('export_multi_day_report', 'Export Multi Day Report')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_reportconfighistory_created', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_report_config_histories', to='core.organization')),
            ],
            options={
                'verbose_name': 'Report Config History',
                'verbose_name_plural': 'Report Config Histories',
                'db_table': 'work_progress_report_config_histories',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='ReportConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('config', models.JSONField(default=[])),
                ('config_type', models.CharField(choices=[('generate_report', 'Generate Report'), ('export_single_day_report', 'Export Single Day Report'), ('export_multi_day_report', 'Export Multi Day Report')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_reportconfig_created', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_report_configs', to='core.organization')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_reportconfig_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Report Config',
                'verbose_name_plural': 'Report Configs',
                'db_table': 'work_progress_report_configs',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='ProjectReportConfigHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('config', models.JSONField(default=[])),
                ('config_type', models.CharField(choices=[('generate_report', 'Generate Report'), ('export_single_day_report', 'Export Single Day Report'), ('export_multi_day_report', 'Export Multi Day Report')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_projectreportconfighistory_created', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_project_report_config_histories', to='core.organization')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_project_report_config_histories', to='project.project')),
            ],
            options={
                'verbose_name': 'Project Report Config History',
                'verbose_name_plural': 'Project Report Config Histories',
                'db_table': 'work_progress_project_report_config_histories',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='ProjectReportConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('config', models.JSONField(default=[])),
                ('config_type', models.CharField(choices=[('generate_report', 'Generate Report'), ('export_single_day_report', 'Export Single Day Report'), ('export_multi_day_report', 'Export Multi Day Report')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_projectreportconfig_created', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_project_report_configs', to='core.organization')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_project_report_configs', to='project.project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_projectreportconfig_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Project Report Config',
                'verbose_name_plural': 'Project Report Configs',
                'db_table': 'work_progress_project_report_configs',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='PercentageHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('percentage', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=5, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('update_method', models.CharField(blank=True, choices=[('percentage', 'Percentage'), ('quantity', 'Quantity'), ('milestone', 'Milestone')], max_length=50, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_percentagehistory_created', to=settings.AUTH_USER_MODEL)),
                ('item_type', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='percentage_history', to='element.elementitemtype')),
                ('previous_day_percentage', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.RESTRICT, to='work_progress_v2.percentagehistory')),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='percentage_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Percentage History',
                'verbose_name_plural': 'Percentage Histories',
                'db_table': 'work_progress_element_percentage_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='MileStoneHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('name', models.CharField(max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_milestonehistory_created', to=settings.AUTH_USER_MODEL)),
                ('milestone', models.ForeignKey(null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='history', to='work_progress_v2.itemtypemilestone')),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='milestone_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Item Type Milestone History',
                'verbose_name_plural': 'Item Type Milestone Histories',
                'db_table': 'work_progress_element_milestone_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='ItemTypeMileStoneConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestoneconfig_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestoneconfig_deleted', to=settings.AUTH_USER_MODEL)),
                ('item_type', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='milestone_config', to='element.elementitemtype')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestoneconfig_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Item Type Milestone Configuration',
                'verbose_name_plural': 'Item Type Milestone Configurations',
                'db_table': 'item_type_milestone_config',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.AddField(
            model_name='itemtypemilestone',
            name='milestone_config',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='milestones', to='work_progress_v2.itemtypemilestoneconfig'),
        ),
        migrations.AddField(
            model_name='itemtypemilestone',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_itemtypemilestone_updated', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='InputProgressQuantityHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15, null=True)),
                ('uom', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('uom_name', models.CharField(blank=True, max_length=50, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_inputprogressquantityhistory_created', to=settings.AUTH_USER_MODEL)),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='input_progress_quantity_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Input Progress Quantity History',
                'verbose_name_plural': 'Input Progress Quantity Histories',
                'db_table': 'work_progress_element_input_progress_quantity_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='InputProgressPercentageHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('percentage', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=5, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_inputprogresspercentagehistory_created', to=settings.AUTH_USER_MODEL)),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='input_progress_percentage_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Input Progress Percentage History',
                'verbose_name_plural': 'Input Progress Percentage Histories',
                'db_table': 'work_progress_element_input_progress_percentage_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='AttachmentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('action', models.CharField(choices=[('attachment_uploaded', 'Attachment Uploaded'), ('attachment_deleted', 'Attachment Deleted')], max_length=50)),
                ('attachment', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='history', to='progressreport.progressreportelementattachment')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_attachmenthistory_created', to=settings.AUTH_USER_MODEL)),
                ('timeline', models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='attachment_history', to='work_progress_v2.workprogresselementtimeline')),
            ],
            options={
                'verbose_name': 'Attachment History',
                'verbose_name_plural': 'Attachment Histories',
                'db_table': 'work_progress_element_attachment_history',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressSectionDayWiseData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('day_end_data', models.JSONField()),
                ('date', models.DateField()),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='wp_section_daywise_data', to='core.organization')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='wp_section_daywise_data', to='project.project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresssectiondaywisedata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'work_progress_section_daywise_data',
                'unique_together': {('project', 'organization', 'date')},
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressProjectDayWiseData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('day_end_data', models.JSONField()),
                ('date', models.DateField()),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='wp_project_daywise_data', to='core.organization')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='wp_project_daywise_data', to='project.project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogressprojectdaywisedata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'work_progress_project_daywise_data',
                'unique_together': {('project', 'organization', 'date')},
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.CreateModel(
            name='WorkProgressElementDayWiseData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('date', models.DateField()),
                ('day_end_progress_percentage', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=5, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('element', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='element_daywise_data', to='work_progress_v2.workprogresselement')),
                ('latest_timeline', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='element_daywise_data', to='work_progress_v2.workprogresselementtimeline')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='work_progress_v2_workprogresselementdaywisedata_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'work_progress_element_daywise_data',
                'unique_together': {('element', 'date')},
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin),
        ),
        migrations.AlterUniqueTogether(
            name='itemtypemilestone',
            unique_together={('milestone_config', 'percentage')},
        ),
    ]
