from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import ReportConfigInputEntity
from work_progress_v2.domain.factories import WorkProgressOrgServiceFactory
from work_progress_v2.interface.base_api import WorkProgressOrgBase<PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class UpdateGenerateReportConfigApi(WorkProgressOrgBaseApi):
    """
    Update generate report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_generate_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_generate_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.ORG_GENERATE_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)


class UpdateSingleDayReportConfigApi(WorkProgressOrgBaseApi):
    """
    Update single day report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_single_day_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_export_single_day_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.ORG_SINGLE_DAY_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)


class UpdateMultiDayReportConfigApi(WorkProgressOrgBaseApi):
    """
    Update multi day report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_multi_day_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_export_multi_day_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.ORG_MULTI_DAY_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)
