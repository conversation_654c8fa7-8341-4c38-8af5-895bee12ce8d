from drf_yasg.utils import swagger_auto_schema
from pydantic import Field
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.report_entities import CreateProjectManpowerCategoryDataEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.services.report import WorkProgressReportInteractor
from work_progress_v2.interface.base_api import Work<PERSON>rogress<PERSON>ase<PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class CreateManpowerCategoryApi(WorkProgressBaseApi):
    """
    Create Manpower Category
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    class InputModel(PydanticInputBaseModel):
        name: str = Field(max_length=100)

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={
            HTTP_200_OK: pydantic_schema(CreateProjectManpowerCategoryDataEntity),
        },
        operation_id="work_progress_create_manpower_category",
    )
    def post(self, request, project_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()

        try:
            data = interactor.create_project_manpower_category(name=data.name)
        except WorkProgressReportInteractor.Exception as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(WorkProgressErrorMessages.MANPOWER_CREATE_SUCCESS)
        return Response(pydantic_dump(data), status=HTTP_200_OK)
