from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.report_entities import ReportDetailCreatedByEntity
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ReportCreatedByListApi(WorkProgressBaseApi):
    """
    Gives list of users who have created reports.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportDetailCreatedByEntity)},
        operation_id="work_progress_report_created_by_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()
        data = interactor.get_report_created_by_list()

        self.set_response_message("Report created by list fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
