from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.services.report import WorkProgressReportInteractor
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class DeleteReportApi(WorkProgressBaseApi):
    """
    Delete report API
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_DELETE_PROGRESS_REPORT,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_delete_report",
    )
    def delete(self, request, project_id, report_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()

        try:
            interactor.delete_report(report_id)
        except WorkProgressReportInteractor.Exception as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Report deleted successfully")
        return Response(status=HTTP_200_OK)
