from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from common.serializers import BaseSerializer
from element.data.selectors import org_item_type_config_fetch
from element.domain.services import org_item_type_config_update
from element.interface.serializers.data_serializers import (
    ItemTypeConfigDataSerializer,
    ItemTypeConfigUpdateDataSerializer,
)
from work_progress_v2.domain.entities import ItemTypeConfigEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi, WorkProgressOrgBaseApi


class ItemTypeConfigApi(WorkProgressBaseApi):
    """
    API to get milestone configuration, default update method for each item-type
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ItemTypeConfigEntity)},
        operation_id="work_progress_item_type_config",
    )
    def get(self, request, project_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_interactor()
        data = service.get_item_type_config()

        self.set_response_message("Item type config data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


# TODO: Move this to pydantic style
class OrgItemTypeConfigApi(WorkProgressOrgBaseApi):
    """
    Organization level API to update item type config
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: ItemTypeConfigDataSerializer(many=True)},
        operation_id="org_item_type_config_fetch",
        operation_summary="Organization item type config fetch.",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        response = org_item_type_config_fetch(org_id=user_entity.org_id)
        return Response(ItemTypeConfigDataSerializer(response, many=True).data, status=HTTP_200_OK)


# TODO: Move this to pydantic style
class OrgItemTypeConfigUpdateApi(WorkProgressOrgBaseApi):
    class InputSerializer(BaseSerializer):
        config = ItemTypeConfigUpdateDataSerializer(many=True)

        class Meta:
            ref_name = "OrgItemTypeConfigUpdateApiInput"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ItemTypeConfigDataSerializer(many=True)},
        operation_id="org_item_type_config_update",
        operation_summary="Organization item type config update.",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        user_entity = self.get_org_user_entity()

        org_item_type_config_update(org_id=user_entity.org_id, data=data["config"], user_id=user_entity.user_id)
        response = org_item_type_config_fetch(org_id=user_entity.org_id)
        return Response(ItemTypeConfigDataSerializer(response, many=True).data, status=HTTP_200_OK)
