from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import ScopeProgressEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ScopeDataApi(WorkProgressBaseApi):
    """
    Gives scope data of WorkProgress.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ScopeProgressEntity)},
        operation_id="work_progress_scope_data",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        interactor = factory.get_scope_data_interactor()
        data = interactor.get_scope_data()

        self.set_response_message("Scope progress data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
