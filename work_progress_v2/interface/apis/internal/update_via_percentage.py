import decimal

from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from authorization.domain.constants import Permissions
from common.constants import RequestHeaders
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import UpdateProgressElementDataEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.update import WorkProgressUpdateInteractor
from work_progress_v2.interface.base_api import Work<PERSON>rogressBaseA<PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class UpdateViaPercentageApi(WorkProgressBaseApi):
    """
    Update work progress element by percentage.
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_UPDATE_WORK_PROGRESS,
    ]

    class InputModel(PydanticInputBaseModel):
        percentage: decimal.Decimal

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: pydantic_schema(UpdateProgressElementDataEntity)},
        operation_id="work_progress_update_via_percentage",
    )
    @transaction.atomic
    def put(self, request, project_id: int, element_id: int, *args, **kwargs):
        self.validate_work_progress_version()

        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()
        version = self.get_work_progress_version()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity, version=version)

        try:
            update_interactor = factory.get_update_interactor(element_id=element_id, is_vendor=is_vendor)
        except WorkProgressServiceFactory.ElementNotFoundException:
            self.set_response_message(WorkProgressErrorMessages.ITEM_NOT_FOUND)
            return Response(status=HTTP_404_NOT_FOUND)

        try:
            result = update_interactor.update_via_percentage(incoming_percentage=data.percentage)
        except WorkProgressUpdateInteractor.WorkProgressElementUpdateException:
            transaction.set_rollback(True)
            self.set_response_message(WorkProgressErrorMessages.FAILED_TO_UPDATE_PERCENTAGE)
            return Response(status=HTTP_400_BAD_REQUEST)
        except WorkProgressUpdateInteractor.WorkProgressVersionMismatch:
            transaction.set_rollback(True)
            return self.get_version_mismatch_response()
        except WorkProgressUpdateInteractor.Exception as e:
            transaction.set_rollback(True)
            self.set_response_message(str(e.message))
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(WorkProgressErrorMessages.PERCENTAGE_UPDATE_SUCCESS)
        return Response(
            pydantic_dump(result.data),
            status=HTTP_200_OK,
            headers={RequestHeaders.WORK_PROGRESS_VERSION.value: result.version},
        )
