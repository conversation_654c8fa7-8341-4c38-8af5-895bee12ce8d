from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.report_entities import PrefillDailyLogEntity
from work_progress_v2.interface.base_api import WorkProgress<PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class PrefillDailyLogApi(WorkProgressBaseApi):
    """
    Get prefill daily log data
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PrefillDailyLogEntity)},
        operation_id="work_progress_prefill_daily_log",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        config_service = factory.get_config_service()

        if not config_service.is_module_enabled(module_key=WorkProgressReportConfigIdEnum.DAILY_LOG):
            self.set_response_message(WorkProgressErrorMessages.PREFILL_DAILY_LOG)
            return Response(status=HTTP_400_BAD_REQUEST)

        interactor = factory.get_interactor()

        data = interactor.get_prefill_daily_log_data()

        self.set_response_message("Prefill daily log data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
