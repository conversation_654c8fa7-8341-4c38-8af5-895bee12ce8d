from drf_yasg.utils import swagger_auto_schema
from rest_framework.status import HTTP_200_OK

from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.report_entities import ReportListPaginatedEntity
from work_progress_v2.domain.entities import VendorReportListFilterDataEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class VendorReportListApi(WorkProgressBaseApi):
    """
    Gives vendor progress reports list.
    """

    filter_pydantic_class = VendorReportListFilterDataEntity

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportListPaginatedEntity)},
        operation_id="work_progress_vendor_report_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data: VendorReportListFilterDataEntity = self.validate_pydantic_filter_data()

        if not filter_data.vendor_id:
            filter_data.vendor_org_ids = self.get_vendor_organization_ids()
        else:
            filter_data.vendor_org_ids = [filter_data.vendor_id]

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()
        data = interactor.get_vendor_report_list(filter_data=filter_data)

        self.set_response_message("Vendor reports fetched successfully")
        return self.get_pydantic_paginated_data(data=data)
