from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ExecutionDueDateHistoryApi(WorkProgressBaseApi):
    """
    Get the execution due date history of a project.
    """

    @swagger_auto_schema(
        operation_id="work_progress_get_execution_due_date_history",
    )
    def get(self, request, project_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        project_service = factory.get_project_service()
        order_service = factory.get_order_service()
        interactor = factory.get_scope_timeline_interactor()

        data = interactor.get_execution_due_date_history(
            is_vendor=is_vendor,
            project_service=project_service,
            order_service=order_service,
        )

        self.set_response_message("Project execution due date history retrieved successfully")
        return Response(pydantic_dump(data.history_entities), status=HTTP_200_OK)


class ExpectedStartDateHistoryApi(WorkProgressBaseApi):
    """
    Get the expected start date history of a project.
    """

    @swagger_auto_schema(
        operation_id="work_progress_get_expected_start_date_history",
    )
    def get(self, request, project_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        project_service = factory.get_project_service()
        order_service = factory.get_order_service()
        interactor = factory.get_scope_timeline_interactor()

        data = interactor.get_expected_start_date_history(
            is_vendor=is_vendor,
            project_service=project_service,
            order_service=order_service,
        )

        self.set_response_message("Project expected due date history retrieved successfully")
        return Response(pydantic_dump(data.history_entities), status=HTTP_200_OK)
