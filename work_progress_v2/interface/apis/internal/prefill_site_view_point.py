from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.report_entities import PrefillSiteViewPointEntity
from work_progress_v2.interface.base_api import WorkProgress<PERSON>ase<PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class PrefillSiteViewPointApi(WorkProgressBaseApi):
    """
    Get prefill site view point data
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PrefillSiteViewPointEntity)},
        operation_id="work_progress_prefill_scope_update",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        config_service = factory.get_config_service()

        if not config_service.is_module_enabled(module_key=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT):
            self.set_response_message(WorkProgressErrorMessages.PREFILL_SITE_VIEW_POINT)
            return Response(status=HTTP_400_BAD_REQUEST)

        interactor = factory.get_interactor()

        data = interactor.get_prefill_site_view_point_data()

        self.set_response_message("Prefill site view point data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
