from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from work_progress_v2.domain.entities import ExportReportInputEntity
from work_progress_v2.domain.factories import WorkProgressReportPDFServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class ExportReportApi(WorkProgressBaseApi):
    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EXPORT_PROGRESS_REPORT,
    ]

    filter_pydantic_class = ExportReportInputEntity

    @swagger_auto_schema(
        # query_serializer=pydantic_schema(ExportReportInputEntity),
        operation_id="work_progress_export_report",
    )
    def get(self, request, project_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_filter_data()
        user = self.get_user()

        service_factory = WorkProgressReportPDFServiceFactory(user_entity=user_entity)
        interactor = service_factory.get_interactor()

        data = interactor.get_export_report_pdf_data(
            data=data,
            exported_by_name=user.name,
            exported_by_photo=user.photo.url if user.photo else None,
        )

        self.set_response_message(WorkProgressErrorMessages.EXPORT_SUCCESS)
        return Response(status=HTTP_200_OK)
