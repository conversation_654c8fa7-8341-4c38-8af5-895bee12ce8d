from drf_yasg.utils import swagger_auto_schema
from rest_framework.status import HTTP_200_OK

from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.report_entities import ReportListPaginatedEntity
from work_progress_v2.domain.entities import ReportList<PERSON>ilterDataEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ReportListApi(WorkProgressBaseApi):
    """
    Gives work progress reports list.
    """

    filter_pydantic_class = ReportListFilterDataEntity

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportListPaginatedEntity)},
        operation_id="work_progress_report_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data = self.validate_pydantic_filter_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()
        data = interactor.get_report_list(filter_data=filter_data)

        self.set_response_message("Reports fetched successfully")
        return self.get_pydantic_paginated_data(data=data)
