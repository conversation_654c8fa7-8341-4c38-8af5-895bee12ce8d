from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from pydantic import Field
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_304_NOT_MODIFIED, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.constants import RequestHeaders
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.custom_fields import HashIdInt
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.domain.entities import BulkUpdateElementUpdateMethodDataEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.bulk_update import WorkProgressBulkUpdateInteractor
from work_progress_v2.interface.base_api import WorkProgress<PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class BulkUpdateMethod<PERSON><PERSON>(WorkProgressBaseApi):
    """
    Bulk update work progress update method.
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD,
    ]

    class InputModel(PydanticInputBaseModel):
        update_method: ItemTypeUpdateMethodChoices
        element_ids: list[HashIdInt] = Field(
            default_factory=list,
            min_length=1,
            description="Minimum 1 item is required",
        )

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={
            HTTP_200_OK: pydantic_schema(BulkUpdateElementUpdateMethodDataEntity),
        },
        operation_id="work_progress_bulk_update_method",
    )
    @transaction.atomic
    def put(self, request, project_id: int, *args, **kwargs):
        self.validate_work_progress_version()

        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()
        version = self.get_work_progress_version()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity, version=version)

        bulk_update_interactor = factory.get_bulk_update_interactor()

        try:
            data = bulk_update_interactor.bulk_update_elements_update_method(
                element_ids=data.element_ids,
                incoming_update_method=data.update_method,
                is_vendor=is_vendor,
            )
        except WorkProgressBulkUpdateInteractor.WorkProgressElementUpdateException:
            transaction.set_rollback(True)
            self.set_response_message(WorkProgressErrorMessages.FAILED_TO_UPDATE_METHOD)
            return Response(status=HTTP_400_BAD_REQUEST)
        except WorkProgressBulkUpdateInteractor.WorkProgressVersionMismatch:
            transaction.set_rollback(True)
            return self.get_version_mismatch_response()
        except WorkProgressBulkUpdateInteractor.Exception as e:
            transaction.set_rollback(True)
            self.set_response_message(str(e))
            return Response(status=HTTP_400_BAD_REQUEST)

        if data.error_list:
            self.set_response_message(WorkProgressErrorMessages.FAILED_TO_UPDATE_METHOD_FOR_SOME_ITEMS)
            return Response(data=data.error_list, status=HTTP_400_BAD_REQUEST)

        if data.data is None:
            self.set_response_message(WorkProgressErrorMessages.UPDATE_METHOD_ALREADY_SET)
            return Response(status=HTTP_304_NOT_MODIFIED)

        self.set_response_message(WorkProgressErrorMessages.BULK_UPDATE_METHOD_SUCCESS)
        return Response(
            pydantic_dump(data.data),
            status=HTTP_200_OK,
            headers={RequestHeaders.WORK_PROGRESS_VERSION.value: data.version},
        )
