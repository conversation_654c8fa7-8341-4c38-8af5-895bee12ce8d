from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.report_entities import PrefillScheduleUpdateEntity
from work_progress_v2.interface.base_api import WorkProgress<PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class PrefillScheduleUpdateApi(WorkProgressBaseApi):
    """
    Get prefill schedule update data
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PrefillScheduleUpdateEntity)},
        operation_id="work_progress_prefill_schedule_update",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        config_service = factory.get_config_service()

        if not config_service.is_module_enabled(module_key=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE):
            self.set_response_message(WorkProgressErrorMessages.PREFILL_SCHEDULE_UPDATE)
            return Response(status=HTTP_400_BAD_REQUEST)

        interactor = factory.get_interactor()
        schedule_service = factory.get_schedule_service()

        data = interactor.get_prefill_schedule_update_data(schedule_service=schedule_service)

        self.set_response_message("Prefill schedule update data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
