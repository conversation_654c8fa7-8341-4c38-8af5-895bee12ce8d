from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import ElementHistoryTimelineEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ElementHistoryTimelineApi(WorkProgressBaseApi):
    """
    API to get history timeline for work progress element
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementHistoryTimelineEntity)},
        operation_id="work_progress_hashtag_list",
    )
    def get(self, request, project_id, element_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        interactor = factory.get_element_interactor()
        data = interactor.get_element_history(element_id)

        self.set_response_message("Item history timeline fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
