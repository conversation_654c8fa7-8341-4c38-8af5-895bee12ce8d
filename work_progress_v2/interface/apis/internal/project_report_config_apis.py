from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.repositories import WorkProgressConfigReportTypeEnum
from work_progress_v2.domain.entities import ReportConfigEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.services.config import WorkProgressProjectConfigService
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ProjectGenerateReportConfigApi(WorkProgressBaseApi):
    """
    Get project generate report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_project_generate_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)
        service = factory.get_config_service()

        try:
            data = service.get_config(type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT)
        except WorkProgressProjectConfigService.ConfigDoesNotExistException:
            self.set_response_message("Project generate report config does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Project generate report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ProjectExportSingleDayReportConfigApi(WorkProgressBaseApi):
    """
    Get project export single day report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_project_export_single_day_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)
        service = factory.get_config_service()

        try:
            data = service.get_config(type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT)
        except WorkProgressProjectConfigService.ConfigDoesNotExistException:
            self.set_response_message("Project export single day report config does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Project export single day report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ProjectExportMultiDayReportConfigApi(WorkProgressBaseApi):
    """
    Get project export multi day report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_project_export_multi_day_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)
        service = factory.get_config_service()

        try:
            data = service.get_config(type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT)
        except WorkProgressProjectConfigService.ConfigDoesNotExistException:
            self.set_response_message("Project export multi day report config does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Project export multi day report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ProjectCheckedGenerateReportConfigIdApi(WorkProgressBaseApi):
    """
    Get project generate report config checked ids
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_project_generate_report_config_checked_ids",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)
        service = factory.get_config_service()

        try:
            data = service.get_config_checked_ids(type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT)
        except WorkProgressProjectConfigService.ConfigDoesNotExistException:
            self.set_response_message("Project generate report config does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Project generate report config checked ids fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
