from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.custom_fields import HashIdInt
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.update import WorkProgressUpdateInteractor
from work_progress_v2.interface.base_api import WorkProgress<PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class BulkDeleteAttachmentApi(WorkProgressBaseApi):
    """
    Bulk delete element attachments.
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_DELETE_FILE_IN_DPR,
    ]

    class InputModel(PydanticInputBaseModel):
        attachment_ids: list[HashIdInt]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        operation_id="work_progress_bulk_delete_attachment",
    )
    @transaction.atomic
    def delete(self, request, project_id: int, element_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        try:
            update_interactor = factory.get_update_interactor(
                element_id=element_id,
                is_vendor=is_vendor,
            )
        except WorkProgressServiceFactory.ElementNotFoundException:
            self.set_response_message(WorkProgressErrorMessages.ITEM_NOT_FOUND)
            return Response(status=HTTP_404_NOT_FOUND)

        try:
            update_interactor.delete_attachments(attachment_ids=data.attachment_ids)
        except WorkProgressUpdateInteractor.WorkProgressElementUpdateException:
            self.set_response_message(WorkProgressErrorMessages.FAILED_TO_REMOVE_IMAGE)
            return Response(status=HTTP_400_BAD_REQUEST)
        except WorkProgressUpdateInteractor.Exception as e:
            self.set_response_message(str(e.message))
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(WorkProgressErrorMessages.BULK_DELETE_SUCCESS)
        return Response(status=HTTP_200_OK)
