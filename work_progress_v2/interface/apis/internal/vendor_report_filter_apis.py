from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.report_entities import VendorReportCreatedByDataEntity
from work_progress_v2.domain.entities import VendorReportCreatedByListFilterDataEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class VendorReportCreatedByListApi(WorkProgressBaseApi):
    """
    Gives list of users who have created vendor reports.
    """

    filter_pydantic_class = VendorReportCreatedByListFilterDataEntity

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(VendorReportCreatedByDataEntity)},
        operation_id="work_progress_vendor_report_created_by_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data = self.validate_pydantic_filter_data()

        if not filter_data.vendor_id:
            filter_data.vendor_org_ids = self.get_vendor_organization_ids()
        else:
            filter_data.vendor_org_ids = filter_data.vendor_id

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()
        data = interactor.get_vendor_report_created_by_list(filter_data=filter_data)

        self.set_response_message("Vendor report created by list fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
