from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import ReportConfigInputEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.interface.base_api import Work<PERSON>rogressBase<PERSON>pi
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class UpdateProjectGenerateReportConfigApi(WorkProgressBaseApi):
    """
    Update project generate report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS,
    ]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_project_generate_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_generate_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.PROJECT_GENERATE_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)


class UpdateProjectSingleDayReportConfigApi(WorkProgressBaseApi):
    """
    Update project single day report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    ]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_project_single_day_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_export_single_day_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.PROJECT_SINGLE_DAY_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)


class UpdateProjectMultiDayReportConfigApi(WorkProgressBaseApi):
    """
    Update project multi day report config
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    ]

    class InputModel(PydanticInputBaseModel):
        config: list[ReportConfigInputEntity]

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_update_project_multi_day_report_config",
    )
    def put(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        service.update_export_multi_day_report(config=data.config)

        self.set_response_message(WorkProgressErrorMessages.PROJECT_MULTI_DAY_REPORT_CONFIG_SUCCESS)
        return Response(status=HTTP_200_OK)
