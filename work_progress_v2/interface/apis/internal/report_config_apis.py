from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.repositories import WorkProgressConfigReportTypeEnum
from work_progress_v2.domain.entities import ReportConfigEntity
from work_progress_v2.domain.factories import WorkProgressOrgServiceFactory
from work_progress_v2.interface.base_api import WorkProgressOrgBaseApi


class GenerateReportConfigApi(WorkProgressOrgBaseApi):
    """
    Get generate report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_generate_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        data = service.get_config(type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT)

        self.set_response_message("Generate report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ExportSingleDayReportConfigApi(WorkProgressOrgBaseApi):
    """
    Get export single day report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_export_single_day_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        data = service.get_config(type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT)

        self.set_response_message("Export single day report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ExportMultiDayReportConfigApi(WorkProgressOrgBaseApi):
    """
    Get export multi day report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ReportConfigEntity)},
        operation_id="work_progress_export_multi_day_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = WorkProgressOrgServiceFactory(user_entity=user_entity)

        service = factory.get_config_service()
        data = service.get_config(type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT)

        self.set_response_message("Export multi day report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
