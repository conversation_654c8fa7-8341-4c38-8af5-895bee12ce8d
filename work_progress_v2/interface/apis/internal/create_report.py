from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory, WorkProgressServiceFactory
from work_progress_v2.domain.report_entities import CreateReportInputEntity
from work_progress_v2.domain.services.report import WorkProgressReportInteractor
from work_progress_v2.interface.base_api import WorkProg<PERSON><PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class CreateReportApi(WorkProgressBaseApi):
    """
    Create a new report.
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    input_pydantic_class = CreateReportInputEntity

    @swagger_auto_schema(
        request_body=pydantic_schema(CreateReportInputEntity),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_create_report",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        config_service = factory.get_config_service()

        if not config_service.can_update_config(data):
            self.set_response_message(WorkProgressErrorMessages.REPORT_CONFIG_CHANGED)
            return Response(status=HTTP_400_BAD_REQUEST)

        inventory_service = factory.get_inventory_service()
        schedule_service = factory.get_schedule_service()

        scope_service = WorkProgressServiceFactory(user_entity=user_entity).get_scope_data_service()

        interactor = factory.get_interactor()
        try:
            interactor.create_report(
                data,
                scope_service=scope_service,
                inventory_service=inventory_service,
                schedule_service=schedule_service,
            )
        except WorkProgressReportInteractor.FailedToCreateReport as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(WorkProgressErrorMessages.REPORT_CREATED_SUCCESS)
        return Response(status=HTTP_200_OK)
