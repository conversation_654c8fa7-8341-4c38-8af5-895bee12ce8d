from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ManpowerCategoryListApi(WorkProgressBaseApi):
    """
    Gives manpower dropdown options for dpr form
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_manpower_category_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        interactor = factory.get_interactor()
        data = interactor.get_manpower_category_list_data()

        self.set_response_message("Manpower category list fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
