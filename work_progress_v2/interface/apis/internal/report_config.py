from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import GenerateReportConfigEntity
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory
from work_progress_v2.domain.services.config import WorkProgressProjectConfigService
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ReportConfigApi(WorkProgressBaseApi):
    """
    Get report config
    """

    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_WORK_PROGRESS]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(GenerateReportConfigEntity)},
        operation_id="work_progress_report_config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)
        interactor = factory.get_interactor()

        try:
            data = interactor.get_report_config()
        except WorkProgressProjectConfigService.ConfigDoesNotExistException:
            self.set_response_message("Report config does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Report config fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
