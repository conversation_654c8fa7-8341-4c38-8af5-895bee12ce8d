from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from work_progress_v2.domain.factories import WorkProgressReportPDFServiceFactory
from work_progress_v2.interface.base_api import Work<PERSON>rogress<PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class GenerateReportApi(WorkProgressBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_generate_report",
    )
    def get(self, request, project_id, report_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        interactor = WorkProgressReportPDFServiceFactory(user_entity=user_entity).get_interactor()
        pdf_url = interactor.get_report_pdf_url(report_id=report_id)

        if not pdf_url:
            self.set_response_message(WorkProgressErrorMessages.GENERATE_IN_PROGRESS)
            return Response(status=HTTP_200_OK)

        self.set_response_message(WorkProgressErrorMessages.GENERATE_SUCCESS)
        return Response(data={"url": pdf_url}, status=HTTP_200_OK)
