from drf_yasg.utils import swagger_auto_schema
from rest_framework.status import HTTP_200_OK

from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.entities import ElementListFilterEntity, PaginatedElementListEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ElementListApi(WorkProgressBaseApi):
    """
    Gives work progress elements list.
    """

    filter_pydantic_class = ElementListFilterEntity

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PaginatedElementListEntity)},
        operation_id="work_progress_element_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data = self.validate_pydantic_filter_data()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_service()
        data = service.get_elements(filter_data=filter_data)

        self.set_response_message("Items fetched successfully")
        return self.get_pydantic_paginated_data(data)
