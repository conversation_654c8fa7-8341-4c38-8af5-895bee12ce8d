from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.entities import (
    ElementCategoryDataEntity,
    ElementItemTypeDataEntity,
    ElementStatusDataEntity,
    ElementUomDataEntity,
)
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.interface.base_api import WorkProgressBaseApi


class ElementCategoryListApi(WorkProgressBaseApi):
    """
    Gives category dropdown options for filtration of workProgress elements.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementCategoryDataEntity)},
        operation_id="work_progress_element_category_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_service()
        data = service.get_categories()

        self.set_response_message("Categories fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementStatusListApi(WorkProgressBaseApi):
    """
    Gives status dropdown options for filtration of workProgress elements.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementStatusDataEntity)},
        operation_id="work_progress_element_status_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_service()
        data = service.get_statuses()

        self.set_response_message("Statuses fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementUomListApi(WorkProgressBaseApi):
    """
    Gives uom dropdown options for filtration of workProgress elements.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementUomDataEntity)},
        operation_id="work_progress_element_uom_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_service()
        data = service.get_uoms()

        self.set_response_message("UOMs fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementItemTypeListApi(WorkProgressBaseApi):
    """
    Gives item type dropdown options for filtration of workProgress elements.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementItemTypeDataEntity)},
        operation_id="work_progress_element_item_type_list",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)

        service = factory.get_element_service()
        data = service.get_item_types()

        self.set_response_message("Item types fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
