from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.status import WorkProgressStatusService
from work_progress_v2.interface.base_api import WorkProgressBase<PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class MarkCompleteApi(WorkProgressBaseApi):
    """
    Mark complete api
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE,
    ]

    class InputModel(PydanticInputBaseModel):
        update_project_status: bool = False

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_mark_complete",
    )
    @transaction.atomic
    def put(self, request, project_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        data = self.validate_pydantic_input_data()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity)
        service = factory.get_status_service()

        try:
            service.update_work_progress_to_mark_complete(
                update_project_status=data.update_project_status,
                is_vendor=is_vendor,
            )
        except WorkProgressStatusService.AllElementNot100PercentException:
            self.set_response_message("All elements are not 100 percent")
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(WorkProgressErrorMessages.MARK_COMPLETE_SUCCESS)
        return Response(status=HTTP_200_OK)


class MarkInCompleteApi(WorkProgressBaseApi):
    """
    Mark incomplete api
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="work_progress_mark_incomplete",
    )
    @transaction.atomic
    def put(self, request, project_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        is_vendor = self.is_vendor()

        factory = WorkProgressServiceFactory(user_entity=user_entity)
        service = factory.get_status_service()

        service.update_work_progress_to_mark_incomplete(is_vendor=is_vendor)

        self.set_response_message(WorkProgressErrorMessages.MARK_INCOMPLETE_SUCCESS)
        return Response(status=HTTP_200_OK)
