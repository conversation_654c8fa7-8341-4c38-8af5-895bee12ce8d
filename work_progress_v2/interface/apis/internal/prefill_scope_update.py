from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum
from work_progress_v2.domain.factories import WorkProgressReportServiceFactory, WorkProgressServiceFactory
from work_progress_v2.domain.report_entities import PrefillScopeUpdateEntity
from work_progress_v2.interface.base_api import Work<PERSON>rogress<PERSON>ase<PERSON><PERSON>
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class PrefillScopeUpdateApi(WorkProgressBaseApi):
    """
    Get prefill scope update data
    """

    REQUIRED_PERMISSIONS = [
        Permissions.CAN_ACCESS_WORK_PROGRESS,
        Permissions.CAN_GENERATE_PROGRESS_REPORT,
    ]

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PrefillScopeUpdateEntity)},
        operation_id="work_progress_prefill_scope_update",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressReportServiceFactory(user_entity=user_entity)

        config_service = factory.get_config_service()

        if not config_service.is_module_enabled(module_key=WorkProgressReportConfigIdEnum.SCOPE_UPDATE):
            self.set_response_message(WorkProgressErrorMessages.PREFILL_SCOPE_UPDATE)
            return Response(status=HTTP_400_BAD_REQUEST)

        scope_service = WorkProgressServiceFactory(user_entity=user_entity).get_scope_data_service()

        interactor = factory.get_interactor()

        data = interactor.get_prefill_scope_update_data(scope_service=scope_service)

        self.set_response_message("Prefill scope update data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
