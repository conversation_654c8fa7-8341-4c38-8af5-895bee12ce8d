from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from work_progress_v2.data.entities import ElementPreviewFileDataEntity
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.element import WorkProgressElementInteractor
from work_progress_v2.interface.base_api import WorkProgressBase<PERSON>pi
from work_progress_v2.interface.error_messages import WorkProgressErrorMessages


class ElementPreviewFileApi(WorkProgressBaseApi):
    """
    Gives element preview files.
    """

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(ElementPreviewFileDataEntity)},
        operation_id="work_progress_element_preview_files",
    )
    def get(self, request, element_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()

        factory = WorkProgressServiceFactory(user_entity=user_entity)
        interactor = factory.get_element_interactor()

        try:
            data = interactor.get_element_preview_files(element_id)
        except WorkProgressElementInteractor.ElementNotFoundException:
            self.set_response_message(WorkProgressErrorMessages.ITEM_NOT_FOUND)
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Item preview files fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
