import abc
from typing import TYPE_CHECKING

from common.exceptions import BaseValidationError

if TYPE_CHECKING:
    from work_progress_v2.data.entities import (
        ElementCategoryDataEntity,
        ElementItemTypeDataEntity,
        ElementStatusDataEntity,
        ElementUomDataEntity,
    )
    from work_progress_v2.domain.entities import (
        ItemTypeConfigEntity,
        PaginatedVendorScopeElementListEntity,
        ScopeProgressEntity,
        VendorScopeElementDetailEntity,
        VendorScopeElementListFilterEntity,
    )


class ProjectToWorkProgressAbstractFactory(abc.ABC):
    @abc.abstractmethod
    def get_service(self) -> "ProjectToWorkProgressAbstractService":
        pass


class ProjectToWorkProgressAbstractService(abc.ABC):
    @abc.abstractmethod
    def create_project_configs(self, project_id: int):
        pass


class OrderToWorkProgressAbstractFactory(abc.ABC):
    @abc.abstractmethod
    def get_service(self) -> "OrderToWorkProgressAbstractService":
        pass


class OrderToWorkProgressAbstractService(abc.ABC):
    class Exception(BaseValidationError):
        pass

    class ElementNotFoundException(Exception):
        pass

    @abc.abstractmethod
    def get_elements(
        self, filter_data: "VendorScopeElementListFilterEntity"
    ) -> "PaginatedVendorScopeElementListEntity":
        pass

    @abc.abstractmethod
    def get_scope_data(self, element_ids: list[int] | None = None) -> "ScopeProgressEntity":
        pass

    @abc.abstractmethod
    def get_categories(self) -> list["ElementCategoryDataEntity"]:
        pass

    @abc.abstractmethod
    def get_statuses(self) -> list["ElementStatusDataEntity"]:
        pass

    @abc.abstractmethod
    def get_uoms(self) -> list["ElementUomDataEntity"]:
        pass

    @abc.abstractmethod
    def get_item_types(self) -> list["ElementItemTypeDataEntity"]:
        pass

    @abc.abstractmethod
    def get_element_detail(self, element_id: int) -> "VendorScopeElementDetailEntity":
        pass

    @abc.abstractmethod
    def get_item_type_config(self) -> "ItemTypeConfigEntity":
        pass
