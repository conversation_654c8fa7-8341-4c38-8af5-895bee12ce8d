from core.entities import ProjectUserEntity
from work_progress_v2.domain.entities import (
    ItemTypeConfigEntity,
    VendorScopeElementDetailEntity,
    VendorScopeElementListFilterEntity,
)
from work_progress_v2.domain.factories import WorkProgressServiceFactory
from work_progress_v2.domain.services.element import (
    WorkProgressElementInteractor,
    WorkProgressElementService,
    WorkProgressVendorScopeElementService,
)
from work_progress_v2.domain.services.scope_data import WorkProgressVendorScopeDataService
from work_progress_v2.interface.external.abstract_factories import (
    OrderToWorkProgressAbstractFactory,
    OrderToWorkProgressAbstractService,
)


class OrderToWorkProgressFactory(OrderToWorkProgressAbstractFactory):
    def __init__(self, user_entity: ProjectUserEntity, vendor_ids: list[int]):
        self.user_entity = user_entity
        self.vendor_ids = vendor_ids

    def get_service(self):
        factory = WorkProgressServiceFactory(user_entity=self.user_entity)

        element_interactor = factory.get_vendor_scope_element_interactor(vendor_ids=self.vendor_ids)
        scope_data_service = factory.get_vendor_scope_data_service(vendor_ids=self.vendor_ids)
        element_service = factory.get_vendor_scope_element_service(vendor_ids=self.vendor_ids)

        return OrderToWorkProgressService(
            vendor_ids=self.vendor_ids,
            element_service=element_service,
            scope_data_service=scope_data_service,
            element_interactor=element_interactor,
        )


class OrderToWorkProgressService(OrderToWorkProgressAbstractService):
    def __init__(
        self,
        vendor_ids: list[int],
        element_service: WorkProgressVendorScopeElementService,
        scope_data_service: WorkProgressVendorScopeDataService,
        element_interactor: WorkProgressElementInteractor,
    ):
        self.vendor_ids = vendor_ids
        self.element_service = element_service
        self.scope_data_service = scope_data_service
        self.element_interactor = element_interactor

    def get_elements(self, filter_data: VendorScopeElementListFilterEntity):
        return self.element_service.get_element_paginated_list(filter_data=filter_data)

    def get_scope_data(self, element_ids: list[int] | None = None):
        return self.scope_data_service.get_scope_data(element_ids=element_ids)

    def get_item_types(self):
        return self.element_service.get_item_types()

    def get_categories(self):
        return self.element_service.get_categories()

    def get_statuses(self):
        return self.element_service.get_statuses()

    def get_uoms(self):
        return self.element_service.get_uoms()

    def get_element_detail(self, element_id: int):
        try:
            return VendorScopeElementDetailEntity(**self.element_service.get_element_detail(element_id).model_dump())
        except WorkProgressElementService.ElementNotFoundException:
            raise self.ElementNotFoundException("Item does not exist")

    def get_item_type_config(self) -> ItemTypeConfigEntity:
        return self.element_interactor.get_item_type_config()
