from core.entities import OrgUserEntity, ProjectUserEntity
from work_progress_v2.domain.factories import (
    WorkProgressOrgServiceFactory,
)
from work_progress_v2.domain.services.config import WorkProgressOrgConfigService
from work_progress_v2.interface.external.abstract_factories import (
    ProjectToWorkProgressAbstractFactory,
    ProjectToWorkProgressAbstractService,
)


class ProjectToWorkProgressFactory(ProjectToWorkProgressAbstractFactory):
    def __init__(self, user_entity: OrgUserEntity | ProjectUserEntity):
        self.user_entity = user_entity

    def get_service(self):
        org_config_factory = WorkProgressOrgServiceFactory(user_entity=self.user_entity)

        return ProjectToWorkProgressService(
            org_config_service=org_config_factory.get_config_service(),
        )


class ProjectToWorkProgressService(ProjectToWorkProgressAbstractService):
    def __init__(self, org_config_service: WorkProgressOrgConfigService):
        self.org_config_service = org_config_service

    def create_project_configs(self, project_id: int):
        self.org_config_service.create_project_configs(project_id=project_id)
