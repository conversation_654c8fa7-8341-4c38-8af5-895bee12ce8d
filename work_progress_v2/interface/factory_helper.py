import structlog

from boq.data.models import Boq<PERSON>lement, BoqElementActionHistory
from core.entities import ProjectUserEntity
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.domain.entities import (
    BulkUpdateTimelineActionHistoryEntity,
    BulkUpdateTimelineElementEntity,
    BulkUpdateTimelineWorkProgressElementEntity,
)
from work_progress_v2.domain.factories import WorkProgressServiceFactory

logger = structlog.get_logger(__name__)


class WorkProgressTimelineSync:
    @classmethod
    def get_work_progress_update_interactor(
        cls,
        user_id: int,
        project_id: int,
        org_id: int,
    ):
        user_entity = ProjectUserEntity(user_id=user_id, project_id=project_id, org_id=org_id)
        factory = WorkProgressServiceFactory(user_entity=user_entity)
        return factory.get_bulk_update_interactor()

    @classmethod
    def _prepare_action_history(cls, data: BoqElementActionHistory) -> BulkUpdateTimelineActionHistoryEntity:
        return BulkUpdateTimelineActionHistoryEntity(id=data.pk, boq_element_id=data.boq_element_id)

    @classmethod
    def _prepare_element(cls, data: BoqElement) -> BulkUpdateTimelineElementEntity:
        return BulkUpdateTimelineElementEntity(
            id=data.pk,
            quantity=data.quantity,
            item_type_id=data.item_type_id,
            work_progress_element=BulkUpdateTimelineWorkProgressElementEntity(
                update_method=(
                    ItemTypeUpdateMethodChoices(data.work_progress_element.update_method)
                    if data.work_progress_element.update_method
                    else None
                ),
                progress_percentage=data.work_progress_element.progress_percentage,
                progress_quantity_input=data.work_progress_element.progress_quantity_input,
            ),
        )

    @classmethod
    def bulk_update_wp_element_and_create_timeline(
        cls,
        element_to_updated_fields_map: dict[BoqElement, list[str]],
        action_histories: list[BoqElementActionHistory],
        user_id: int,
        project_id: int,
        org_id: int,
    ):
        new_action_histories = [cls._prepare_action_history(data) for data in action_histories]
        new_element_to_updated_fields_map = {
            cls._prepare_element(data): updated_fields for data, updated_fields in element_to_updated_fields_map.items()
        }

        wp_element_update_interactor = cls.get_work_progress_update_interactor(
            user_id=user_id,
            project_id=project_id,
            org_id=org_id,
        )

        wp_element_update_interactor.bulk_update_work_progress_element(
            element_to_updated_fields_map=new_element_to_updated_fields_map,
            action_histories=new_action_histories,
        )
