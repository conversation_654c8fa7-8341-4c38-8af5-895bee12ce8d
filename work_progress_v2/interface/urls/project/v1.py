from django.urls import include, path

from work_progress_v2.interface.apis.internal.bulk_create_attachment import BulkCreate<PERSON>ttachment<PERSON>pi
from work_progress_v2.interface.apis.internal.bulk_delete_attachment import BulkDelete<PERSON>ttachment<PERSON>pi
from work_progress_v2.interface.apis.internal.bulk_update_method import BulkUpdate<PERSON>ethod<PERSON><PERSON>
from work_progress_v2.interface.apis.internal.create_manpower_category import CreateManpowerCategoryApi
from work_progress_v2.interface.apis.internal.create_report import CreateReport<PERSON>pi
from work_progress_v2.interface.apis.internal.date_history import (
    ExecutionDueDateHistoryApi,
    ExpectedStartDateHistoryApi,
)
from work_progress_v2.interface.apis.internal.delete_report import Delete<PERSON>eport<PERSON><PERSON>
from work_progress_v2.interface.apis.internal.element_detail import ElementDetail<PERSON>pi
from work_progress_v2.interface.apis.internal.element_list import ElementListApi
from work_progress_v2.interface.apis.internal.element_preview_files import ElementPreviewFileApi
from work_progress_v2.interface.apis.internal.export_report import Export<PERSON><PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.interface.apis.internal.filter_apis import (
    ElementCategoryListApi,
    ElementItemTypeListApi,
    ElementStatusListApi,
    ElementUomListApi,
)
from work_progress_v2.interface.apis.internal.generate_report import GenerateReportApi
from work_progress_v2.interface.apis.internal.hashtag_list import HashTagListApi
from work_progress_v2.interface.apis.internal.history_timeline import ElementHistoryTimelineApi
from work_progress_v2.interface.apis.internal.item_type_config import ItemTypeConfigApi
from work_progress_v2.interface.apis.internal.manpower_list import ManpowerCategoryListApi
from work_progress_v2.interface.apis.internal.prefill_daily_log import PrefillDailyLogApi
from work_progress_v2.interface.apis.internal.prefill_manpower import PrefillManpowerApi
from work_progress_v2.interface.apis.internal.prefill_material_update import PrefillMaterialUpdateApi
from work_progress_v2.interface.apis.internal.prefill_reported_project_progress import PrefillReportedProjectProgressApi
from work_progress_v2.interface.apis.internal.prefill_schedule_update import PrefillScheduleUpdateApi
from work_progress_v2.interface.apis.internal.prefill_scope_update import PrefillScopeUpdateApi
from work_progress_v2.interface.apis.internal.prefill_site_view_point import PrefillSiteViewPointApi
from work_progress_v2.interface.apis.internal.project_report_config_apis import (
    ProjectCheckedGenerateReportConfigIdApi,
    ProjectExportMultiDayReportConfigApi,
    ProjectExportSingleDayReportConfigApi,
    ProjectGenerateReportConfigApi,
)
from work_progress_v2.interface.apis.internal.report_config import ReportConfigApi
from work_progress_v2.interface.apis.internal.report_filter_apis import ReportCreatedByListApi
from work_progress_v2.interface.apis.internal.report_list import ReportListApi
from work_progress_v2.interface.apis.internal.scope_data import ScopeDataApi
from work_progress_v2.interface.apis.internal.scope_timeline import ScopeTimelineApi
from work_progress_v2.interface.apis.internal.sections import ElementSectionListApi
from work_progress_v2.interface.apis.internal.status_apis import MarkCompleteApi, MarkInCompleteApi
from work_progress_v2.interface.apis.internal.update_project_report_config_apis import (
    UpdateProjectGenerateReportConfigApi,
    UpdateProjectMultiDayReportConfigApi,
    UpdateProjectSingleDayReportConfigApi,
)
from work_progress_v2.interface.apis.internal.update_via_milestone import UpdateViaMilestoneApi
from work_progress_v2.interface.apis.internal.update_via_percentage import UpdateViaPercentageApi
from work_progress_v2.interface.apis.internal.update_via_quantity import UpdateViaQuantityApi
from work_progress_v2.interface.apis.internal.vendor_report_filter_apis import VendorReportCreatedByListApi
from work_progress_v2.interface.apis.internal.vendor_report_list import VendorReportListApi

REPORT = [
    path("config/", ReportConfigApi.as_view(), name="report-config"),
    path("create/", CreateReportApi.as_view(), name="report-create"),
    path("list/", ReportListApi.as_view(), name="report-list"),
    path(
        "hashtag/list/",
        HashTagListApi.as_view(),
        name="hashtag-list",
    ),
    path(
        "manpower-category/list/",
        ManpowerCategoryListApi.as_view(),
        name="manpower-category-list",
    ),
    path("prefill/site-view-point/", PrefillSiteViewPointApi.as_view(), name="prefill-site-view-point"),
    path(
        "prefill/reported-project-progress/",
        PrefillReportedProjectProgressApi.as_view(),
        name="prefill-reported-project-progress",
    ),
    path("prefill/scope-update/", PrefillScopeUpdateApi.as_view(), name="prefill-scope-update"),
    path("prefill/daily-log/", PrefillDailyLogApi.as_view(), name="prefill-daily-log"),
    path("prefill/material-update/", PrefillMaterialUpdateApi.as_view(), name="prefill-material-update"),
    path("prefill/manpower-category/", PrefillManpowerApi.as_view(), name="prefill-manpower-category"),
    path("prefill/schedule-update/", PrefillScheduleUpdateApi.as_view(), name="prefill-schedule-update"),
    path("manpower-category/create/", CreateManpowerCategoryApi.as_view(), name="create-manpower-category"),
    path("created-by/list/", ReportCreatedByListApi.as_view(), name="report-created-by-list"),
    path(
        "<hash_id:report_id>/export-pdf/",
        GenerateReportApi.as_view(),
        name="generate-report-pdf",
    ),
    path("export-pdf/", ExportReportApi.as_view(), name="export-report-pdf"),
    path("<hash_id:report_id>/delete/", DeleteReportApi.as_view(), name="delete-report"),
]

ELEMENT = [
    path(
        "<hash_id:element_id>/update-via-percentage/",
        UpdateViaPercentageApi.as_view(),
        name="update-via-percentage",
    ),
    path(
        "<hash_id:element_id>/update-via-milestone/",
        UpdateViaMilestoneApi.as_view(),
        name="update-via-milestone",
    ),
    path(
        "<hash_id:element_id>/update-via-quantity/",
        UpdateViaQuantityApi.as_view(),
        name="update-via-quantity",
    ),
    path("list/", ElementListApi.as_view(), name="element-list"),
    path(
        "update-method/bulk-update/",
        BulkUpdateMethodApi.as_view(),
        name="bulk-update-method",
    ),
    path(
        "<hash_id:element_id>/attachment/bulk-create/",
        BulkCreateAttachmentApi.as_view(),
        name="bulk-create-attachment",
    ),
    path(
        "<hash_id:element_id>/attachment/bulk-delete/",
        BulkDeleteAttachmentApi.as_view(),
        name="bulk-delete-attachment",
    ),
    path("<hash_id:element_id>/details/", ElementDetailApi.as_view(), name="element-details"),
    path("<hash_id:element_id>/history/", ElementHistoryTimelineApi.as_view(), name="element-history-timeline"),
    path("<hash_id:element_id>/preview-files/", ElementPreviewFileApi.as_view(), name="element-preview-files"),
]

CONFIG = [
    path("generate-report/", ProjectGenerateReportConfigApi.as_view(), name="project-generate-report"),
    path("generate-report/update/", UpdateProjectGenerateReportConfigApi.as_view(), name="project-generate-report"),
    path(
        "export-single-day-report/",
        ProjectExportSingleDayReportConfigApi.as_view(),
        name="project-export-single-day-report",
    ),
    path(
        "export-single-day-report/update/",
        UpdateProjectSingleDayReportConfigApi.as_view(),
        name="project-export-single-day-report",
    ),
    path(
        "export-multi-day-report/",
        ProjectExportMultiDayReportConfigApi.as_view(),
        name="project-export-multi-day-report",
    ),
    path(
        "export-multi-day-report/update/",
        UpdateProjectMultiDayReportConfigApi.as_view(),
        name="project-export-multi-day-report",
    ),
    path(
        "generate-report/checked/list/",
        ProjectCheckedGenerateReportConfigIdApi.as_view(),
        name="project-generate-report-checked-list",
    ),
]

VENDOR_REPORT = [
    path("list/", VendorReportListApi.as_view(), name="vendor-report-list"),
    path("created-by/list/", VendorReportCreatedByListApi.as_view(), name="report-created-by-list"),
]

urlpatterns = [
    path("sections/", ElementSectionListApi.as_view(), name="section-list"),
    path("scope/progress/", ScopeDataApi.as_view(), name="header-api"),
    path("scope/timeline/", ScopeTimelineApi.as_view(), name="scope-api"),
    path("element-category/list/", ElementCategoryListApi.as_view(), name="category-list"),
    path("element-status/list/", ElementStatusListApi.as_view(), name="element-status-list"),
    path("element-uom/list/", ElementUomListApi.as_view(), name="element-uom-list"),
    path("element-item-type/list/", ElementItemTypeListApi.as_view(), name="element-item-type-list"),
    path("item-type-config/", ItemTypeConfigApi.as_view(), name="item-type-config"),
    path("execution-due-date/history/", ExecutionDueDateHistoryApi.as_view(), name="execution-due-date-history-list"),
    path("expected-start-date/history/", ExpectedStartDateHistoryApi.as_view(), name="expected-due-date-history-list"),
    path("mark-complete/", MarkCompleteApi.as_view(), name="mark-complete"),
    path("mark-incomplete/", MarkInCompleteApi.as_view(), name="mark-incomplete"),
    path("vendor-report/list/", VendorReportListApi.as_view(), name="vendor-report-list"),
    path("report/", include(REPORT)),
    path("element/", include(ELEMENT)),
    path("config/", include(CONFIG)),
    path("vendor-report/", include(VENDOR_REPORT)),
]
