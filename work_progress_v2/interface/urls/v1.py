from django.urls import include, path

from work_progress_v2.interface.apis.internal.item_type_config import OrgItemTypeConfigApi, OrgItemTypeConfigUpdateApi
from work_progress_v2.interface.apis.internal.report_config_apis import (
    ExportMultiDayReportConfigApi,
    ExportSingleDayReportConfigApi,
    GenerateReportConfigApi,
)
from work_progress_v2.interface.apis.internal.update_report_config_apis import (
    UpdateGenerateReportConfigApi,
    UpdateMultiDayReportConfigApi,
    UpdateSingleDayReportConfigApi,
)

CONFIG = [
    path("generate-report/", GenerateReportConfigApi.as_view(), name="generate-report"),
    path("generate-report/update/", UpdateGenerateReportConfigApi.as_view(), name="generate-report"),
    path("export-single-day-report/", ExportSingleDayReportConfigApi.as_view(), name="export-single-day-report"),
    path("export-single-day-report/update/", UpdateSingleDayReportConfigApi.as_view(), name="export-single-day-report"),
    path("export-multi-day-report/", ExportMultiDayReportConfigApi.as_view(), name="export-multi-day-report"),
    path("export-multi-day-report/update/", UpdateMultiDayReportConfigApi.as_view(), name="export-multi-day-report"),
    path("item-type/", OrgItemTypeConfigApi.as_view(), name="item-type-config"),
    path("item-type/update/", OrgItemTypeConfigUpdateApi.as_view(), name="item-type-config-update"),
]

urlpatterns = [
    path("config/", include(CONFIG)),
]
