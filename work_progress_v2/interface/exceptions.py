from rest_framework import status
from rest_framework.exceptions import APIException


class WorkProgressVersionMismatch(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Work progress data has been updated by someone else. Please refresh and try again."
    default_code = "work_progress_version_mismatch"

    def __init__(self, detail=None, code=None, extra_data=None):
        super().__init__(detail, code)
        self.extra_data = extra_data or {}

        # Include is_refresh_required in the detail
        if isinstance(self.detail, dict):
            self.detail.update({"is_refresh_required": True, **self.extra_data})
        else:
            self.detail = {"detail": self.detail, "is_refresh_required": True, **self.extra_data}
