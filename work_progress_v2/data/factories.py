from core.entities import OrgUserEntity, ProjectUserEntity
from inventory.data.respositories import InventoryWorkProgressRepository
from project_schedule.data.repositories import WorkProgressToScheduleRepo
from report.download.data.repositories import DownloadRepo
from work_progress_v2.data.repositories import (
    WorkProgressDayWiseRepo,
    WorkProgressOrgRepo,
    WorkProgressRepo,
    WorkProgressReportPDFRepo,
    WorkProgressReportRepo,
    WorkProgressVendorScopeElementRepo,
)


class WorkProgressRepoFactory:
    def __init__(self, user_entity: ProjectUserEntity):
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id

    def get_repo(self):
        return WorkProgressRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_vendor_scope_repo(self, vendor_ids: list[int]):
        return WorkProgressVendorScopeElementRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
            vendor_ids=vendor_ids,
        )

    def get_day_wise_repo(self):
        return WorkProgressDayWiseRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )


class WorkProgressReportRepoFactory:
    def __init__(self, user_entity: ProjectUserEntity):
        self.user_entity = user_entity
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id

    def get_repo(self):
        return WorkProgressReportRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_inventory_repo(self):
        return InventoryWorkProgressRepository(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_scope_repo(self):
        return WorkProgressRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_schedule_repo(self):
        return WorkProgressToScheduleRepo(
            user_entity=self.user_entity,
        )


class WorkProgressReportPDFRepoFactory:
    def __init__(self, user_entity: ProjectUserEntity):
        self.user_entity = user_entity
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.org_id = user_entity.org_id

    def get_repo(self):
        return WorkProgressReportPDFRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_inventory_repo(self):
        return InventoryWorkProgressRepository(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_schedule_repo(self):
        return WorkProgressToScheduleRepo(
            user_entity=self.user_entity,
        )

    def get_report_repo(self):
        return WorkProgressReportRepo(
            user_id=self.user_id,
            project_id=self.project_id,
            org_id=self.org_id,
        )

    def get_download_repo(self):
        return DownloadRepo(
            user_id=self.user_id,
            org_id=self.org_id,
            project_id=self.project_id,
        )


class WorkProgressOrgRepoFactory:
    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id

    def get_repo(self):
        return WorkProgressOrgRepo(
            user_id=self.user_id,
            org_id=self.org_id,
        )
