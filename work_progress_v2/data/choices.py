from django.db import models
from django.utils.translation import gettext_lazy as _

from work_progress_v2.domain.enums import (
    ElementUpdatedAttributeEnum,
    WorkProgressConfigReportTypeEnum,
    WorkProgressElementActionEnum,
    WorkProgressElementAttachmentActionEnum,
    WorkProgressInventoryStockItemTypeEnum,
    WorkProgressScheduleActivityStatusEnum,
)


class WorkProgressElementActionChoices(models.TextChoices):
    INPUT_QUANTITY_UPDATED = WorkProgressElementActionEnum.INPUT_QUANTITY_UPDATED.value, _("Input Quantity Updated")
    INPUT_PERCENTAGE_UPDATED = (
        WorkProgressElementActionEnum.INPUT_PERCENTAGE_UPDATED.value,
        _("Input Percentage Updated"),
    )
    INPUT_MILESTONE_UPDATED = WorkProgressElementActionEnum.INPUT_MILESTONE_UPDATED.value, _("Input Milestone Updated")
    ATTACHMENT_UPDATED = WorkProgressElementActionEnum.ATTACHMENT_UPDATED.value, _("Attachment Updated")
    UPDATE_METHOD_UPDATED = WorkProgressElementActionEnum.UPDATE_METHOD_UPDATED.value, _("Update Method Updated")
    BOQ_ELEMENT_UPDATED = WorkProgressElementActionEnum.BOQ_ELEMENT_UPDATED.value, _("BOQ Element Updated")


class WorkProgressElementAttachmentActionChoices(models.TextChoices):
    UPLOADED = WorkProgressElementAttachmentActionEnum.UPLOADED.value, _("Attachment Uploaded")
    DELETED = WorkProgressElementAttachmentActionEnum.DELETED.value, _("Attachment Deleted")


class ElementUpdatedAttributeChoices(models.TextChoices):
    QUANTITY = ElementUpdatedAttributeEnum.QUANTITY.value, _("Quantity")
    ITEM_TYPE = ElementUpdatedAttributeEnum.ITEM_TYPE.value, _("Item Type")
    UOM = ElementUpdatedAttributeEnum.UOM.value, _("UOM")
    METHOD_OF_UPDATE = ElementUpdatedAttributeEnum.METHOD_OF_UPDATE.value, _("Method of update")


class WorkProgressConfigReportTypeChoices(models.TextChoices):
    GENERATE_REPORT = WorkProgressConfigReportTypeEnum.GENERATE_REPORT.value, _("Generate Report")
    EXPORT_SINGLE_DAY_REPORT = (
        WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT.value,
        _("Export Single Day Report"),
    )
    EXPORT_MULTI_DAY_REPORT = (
        WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT.value,
        _("Export Multi Day Report"),
    )


class WorkProgressInventoryStockItemTypeChoices(models.TextChoices):
    CONSUMED = WorkProgressInventoryStockItemTypeEnum.CONSUMED.value, _("Consumed")
    TRANSFERRED_OUT = WorkProgressInventoryStockItemTypeEnum.TRANSFERRED_OUT.value, _("Transferred Out")
    RECEIVED = WorkProgressInventoryStockItemTypeEnum.RECEIVED.value, _("Received")


class WorkProgressScheduleActivityStatusChoices(models.TextChoices):
    ON_TIME = WorkProgressScheduleActivityStatusEnum.ON_TIME.value, _("On Time")
    DELAYED = WorkProgressScheduleActivityStatusEnum.DELAYED.value, _("Delayed")
    OVERDUE = WorkProgressScheduleActivityStatusEnum.OVERDUE.value, _("Overdue")
    NOT_SET = WorkProgressScheduleActivityStatusEnum.NOT_SET.value, _("Not Set")
