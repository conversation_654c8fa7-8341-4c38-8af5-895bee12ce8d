import datetime
import decimal
from typing import Union

import structlog

from common.exceptions import BaseValidationError
from common.utils import get_local_time
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.data.choices import WorkProgressElementActionChoices, WorkProgressElementAttachmentActionChoices
from work_progress_v2.data.entities import (
    ElementIdTimelineIdDataEntity,
    ElementUomDataEntity,
    TimelineHistoryOutputEntity,
)
from work_progress_v2.data.models.milestone import ItemTypeMileStone
from work_progress_v2.data.models.timeline import (
    AttachmentHistory,
    InputProgressPercentageHistory,
    InputProgressQuantityHistory,
    MileStoneHistory,
    PercentageHistory,
    UpdateMethodHistory,
    WorkProgressElementTimeline,
    WorkProgressTimelineAndBoqElementActionHistoryMapping,
)
from work_progress_v2.domain.entities import UpdateElementDataEntity

logger = structlog.get_logger(__name__)


class ElementHistoryTimelineRepoMixin:
    current_time: datetime.datetime
    user_id: int

    class InvalidActionOnElementException(BaseValidationError):
        pass

    def _create_timeline_percentage_history(
        self,
        element_id: int,
        timeline_obj,
        item_type_id: int | None,
        percentage: decimal.Decimal,
        update_method: ItemTypeUpdateMethodChoices,
    ) -> PercentageHistory:
        percentage_history_obj = PercentageHistory(
            timeline=timeline_obj,
            percentage=percentage,
            item_type_id=item_type_id,
            previous_day_percentage_id=None,
            update_method=update_method,
            created_at=self.current_time,
            created_by_id=self.user_id,
        )
        previous_history_obj = (
            PercentageHistory.objects.filter(timeline__element_id=element_id).order_by("-created_at").first()
        )
        if previous_history_obj:
            if get_local_time(previous_history_obj.created_at).date() == self.current_time.date():
                percentage_history_obj.previous_day_percentage_id = previous_history_obj.previous_day_percentage_id
            else:
                percentage_history_obj.previous_day_percentage_id = previous_history_obj.pk

        return percentage_history_obj

    def _create_input_quantity_history_object(
        self, timeline_obj, quantity: decimal.Decimal, uom: int, uom_name: str
    ) -> InputProgressQuantityHistory:
        return InputProgressQuantityHistory(
            timeline=timeline_obj,
            quantity=quantity,
            uom=uom,
            uom_name=uom_name,
            created_at=self.current_time,
            created_by_id=self.user_id,
        )

    def _create_input_progress_percentage_history_object(
        self, timeline_obj, percentage: decimal.Decimal
    ) -> InputProgressPercentageHistory:
        return InputProgressPercentageHistory(
            timeline=timeline_obj,
            percentage=percentage,
            created_at=self.current_time,
            created_by_id=self.user_id,
        )

    def _create_milestone_history_object(self, timeline_obj, milestone_id: int) -> MileStoneHistory:
        milestone = ItemTypeMileStone.objects.available().get(id=milestone_id)
        return MileStoneHistory(
            timeline=timeline_obj,
            milestone_id=milestone_id,
            name=milestone.name,
            created_at=self.current_time,
            created_by_id=self.user_id,
        )

    def _create_update_method_history_object(self, timeline_obj, update_method: str) -> UpdateMethodHistory:
        return UpdateMethodHistory(
            timeline=timeline_obj,
            update_method=update_method,
            created_at=self.current_time,
            created_by_id=self.user_id,
        )

    def _create_timeline_and_action_history_mapping_object(
        self, timeline_obj, boq_element_action_history_id: int, updated_fields: list[str]
    ) -> WorkProgressTimelineAndBoqElementActionHistoryMapping:
        return WorkProgressTimelineAndBoqElementActionHistoryMapping(
            timeline=timeline_obj,
            boq_element_action_history_id=boq_element_action_history_id,
            updated_fields=updated_fields,
            created_by_id=self.user_id,
        )

    def create_attachment_added_deleted_history(
        self,
        attachment_id_to_timeline_id_map: dict[int, int],
        attachment_action: WorkProgressElementAttachmentActionChoices,
    ):
        attachment_history_objects = []
        for attachment_id, timeline_id in attachment_id_to_timeline_id_map.items():
            attachment_history_objects.append(
                AttachmentHistory(
                    timeline_id=timeline_id,
                    attachment_id=attachment_id,
                    action=attachment_action,
                    created_at=self.current_time,
                    created_by_id=self.user_id,
                )
            )
        AttachmentHistory.objects.bulk_create(attachment_history_objects)
        logger.info("Attachment history added objects created successfully.")

    def _create_timeline_object(
        self, element_id: int, action: WorkProgressElementActionChoices
    ) -> WorkProgressElementTimeline:
        return WorkProgressElementTimeline(
            element_id=element_id,
            action=action,
            created_by_id=self.user_id,
            created_at=self.current_time,
        )

    def create_history_timeline(
        self,
        element_ids: list[int],
        element_id_to_updated_data_map: dict[int, UpdateElementDataEntity],
        element_id_to_item_type_id_map: dict[int, Union[int, None]],
        element_id_to_uom_map: dict[int, ElementUomDataEntity],
    ) -> TimelineHistoryOutputEntity:
        logger.info(f"Creating history timeline for element_ids: {element_ids}")
        timeline_objects: list[WorkProgressElementTimeline] = []

        input_quantity_history_objects: list[InputProgressQuantityHistory] = []
        input_progress_percentage_history_objects: list[InputProgressPercentageHistory] = []
        milestone_history_objects: list[MileStoneHistory] = []
        update_method_history_objects: list[UpdateMethodHistory] = []
        # attachment_history_objects: list[AttachmentHistory] = []
        timeline_and_action_history_objects: list[WorkProgressTimelineAndBoqElementActionHistoryMapping] = []

        percentage_history_objects: list[PercentageHistory] = []

        for element_id in element_ids:
            updated_data = element_id_to_updated_data_map[element_id]
            action = updated_data.action
            timeline_obj: WorkProgressElementTimeline = self._create_timeline_object(
                element_id=element_id, action=action
            )
            if action == WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED:
                element_uom_entity = element_id_to_uom_map[element_id]
                assert updated_data.progress_quantity_input is not None
                assert element_uom_entity is not None
                input_quantity_history_objects.append(
                    self._create_input_quantity_history_object(
                        timeline_obj=timeline_obj,
                        quantity=updated_data.progress_quantity_input,
                        uom=element_uom_entity.value,
                        uom_name=element_uom_entity.name,
                    )
                )
            elif action == WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED:
                assert updated_data.progress_percentage_input is not None
                input_progress_percentage_history_objects.append(
                    self._create_input_progress_percentage_history_object(
                        timeline_obj=timeline_obj, percentage=updated_data.progress_percentage_input
                    )
                )
            elif action == WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED:
                assert updated_data.milestone_input_id is not None
                milestone_history_objects.append(
                    self._create_milestone_history_object(
                        timeline_obj=timeline_obj, milestone_id=updated_data.milestone_input_id
                    )
                )
            elif action == WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED:
                assert updated_data.update_method is not None
                update_method_history_objects.append(
                    self._create_update_method_history_object(
                        timeline_obj=timeline_obj, update_method=updated_data.update_method
                    )
                )
            elif action == WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED:
                assert updated_data.progress_percentage is not None
                assert updated_data.boq_element_action_history_id is not None
                assert updated_data.boq_element_updated_fields is not None

                timeline_and_action_history_objects.append(
                    self._create_timeline_and_action_history_mapping_object(
                        timeline_obj=timeline_obj,
                        boq_element_action_history_id=updated_data.boq_element_action_history_id,
                        updated_fields=updated_data.boq_element_updated_fields,
                    )
                )

                # to create timeline for progress_input_quantity change by update in boq_element
                if updated_data.is_progress_quantity_input_updated_by_boq_update:
                    element_uom_entity = element_id_to_uom_map[element_id]
                    assert updated_data.progress_quantity_input is not None
                    assert element_uom_entity is not None
                    input_quantity_history_objects.append(
                        self._create_input_quantity_history_object(
                            timeline_obj=timeline_obj,
                            quantity=updated_data.progress_quantity_input,
                            uom=element_uom_entity.value,
                            uom_name=element_uom_entity.name,
                        )
                    )

            elif action == WorkProgressElementActionChoices.ATTACHMENT_UPDATED:
                """
                Attachment added and deleted history is handled in attachment added api
                """
                continue
            else:
                logger.info("Unknown action for creating history timeline", action=action, element_id=element_id)
                raise self.InvalidActionOnElementException(f"Unknown action {action} on element.")

            assert updated_data.progress_percentage is not None, "progress_percentage is required for all actions"
            assert updated_data.current_visible_updated_method is not None, (
                "current_visible_updated_method is required for all actions"
            )

            timeline_objects.append(timeline_obj)
            percentage_history_objects.append(
                self._create_timeline_percentage_history(
                    element_id=element_id,
                    timeline_obj=timeline_obj,
                    item_type_id=element_id_to_item_type_id_map.get(element_id),
                    percentage=updated_data.progress_percentage,
                    update_method=updated_data.current_visible_updated_method,
                )
            )

        # bulk create history objects
        """
        With timeline object, only one type of history object will be created at a time,
        """
        created_timeline_objects = []
        created_input_quantity_history_objects = []
        created_input_progress_percentage_history_objects = []
        created_milestone_history_objects = []
        created_update_method_history_objects = []
        # created_attachment_history_objects = []
        created_timeline_and_action_history_objects = []
        created_percentage_history_objects = []

        logger.info("Creating history timeline objects.")
        if timeline_objects:
            created_timeline_objects = WorkProgressElementTimeline.objects.bulk_create(timeline_objects)

        # create history data objects
        if input_quantity_history_objects:
            created_input_quantity_history_objects = InputProgressQuantityHistory.objects.bulk_create(
                input_quantity_history_objects
            )
            logger.info("Input quantity history objects created successfully.")
        if input_progress_percentage_history_objects:
            created_input_progress_percentage_history_objects = InputProgressPercentageHistory.objects.bulk_create(
                input_progress_percentage_history_objects
            )
            logger.info("Input progress percentage history objects created successfully.")
        if milestone_history_objects:
            created_milestone_history_objects = MileStoneHistory.objects.bulk_create(milestone_history_objects)
            logger.info("Milestone history objects created successfully.")
        if update_method_history_objects:
            created_update_method_history_objects = UpdateMethodHistory.objects.bulk_create(
                update_method_history_objects
            )
            logger.info("Update method history objects created successfully.")
        # if attachment_history_objects:
        #     AttachmentHistory.objects.bulk_create(attachment_history_objects)
        #     logger.info("Attachment history objects created successfully.")
        if timeline_and_action_history_objects:
            created_timeline_and_action_history_objects = (
                WorkProgressTimelineAndBoqElementActionHistoryMapping.objects.bulk_create(
                    timeline_and_action_history_objects
                )
            )

        # create percentage history objects, if any
        if percentage_history_objects:
            created_percentage_history_objects = PercentageHistory.objects.bulk_create(percentage_history_objects)
            logger.info("Percentage history objects created successfully.")

        logger.info("History timeline objects created successfully.")
        return TimelineHistoryOutputEntity(
            created_timeline_objects=created_timeline_objects,
            created_input_quantity_history_objects=created_input_quantity_history_objects,
            created_input_progress_percentage_history_objects=created_input_progress_percentage_history_objects,
            created_milestone_history_objects=created_milestone_history_objects,
            created_update_method_history_objects=created_update_method_history_objects,
            # created_attachment_history_objects=created_attachment_history_objects,
            created_timeline_and_action_history_objects=created_timeline_and_action_history_objects,
            created_percentage_history_objects=created_percentage_history_objects,
        )

    def _prepare_element_timeline_list(
        self, timeline_objects: list[WorkProgressElementTimeline]
    ) -> list[ElementIdTimelineIdDataEntity]:
        element_timeline_list: list[ElementIdTimelineIdDataEntity] = []
        for timeline_obj in timeline_objects:
            element_timeline_list.append(
                ElementIdTimelineIdDataEntity(
                    element_id=timeline_obj.element_id,
                    timeline_id=timeline_obj.pk,
                )
            )
        return element_timeline_list
