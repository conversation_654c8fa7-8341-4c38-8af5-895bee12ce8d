import decimal

import structlog
from django.db.models import QuerySet

from boq.data.choices import BoqElementStatus
from boq.data.models import BoqSection
from common.element_base.services import ElementCodeService
from common.timeline.enum import TimelineStatusEnum
from common.timeline.helper import TimelineStatusHelper
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementCategory, ElementItemType
from progressreport.constants import Constants as PROGRESS_REPORT_CONSTANTS
from progressreport.models import (
    ProgressReport,
    ProgressReportAttachment,
    ProgressReportItem,
    ProgressReportManPower,
    ProgressReportSiteViewPointMapping,
    ProgressReportSiteViewPointMappingAttachment,
)
from report.download.domain.enums import DownloadProgressStatusEnum
from rollingbanners.storage_backends import PublicMediaFileStorage
from work_progress_v2.data.choices import WorkProgressInventoryStockItemTypeChoices
from work_progress_v2.data.entities import (
    ElementAttachmentDataEntity,
    ElementCategoryDataEntity,
    ElementDetailDataEntity,
    ElementItemTypeDataEntity,
    ElementListDataEntity,
    ElementPreviewFileDataEntity,
    ElementSectionDataEntity,
    ElementStatusDataEntity,
    ElementUomDataEntity,
    ScopeElementBaseEntity,
    ScopeTimelineDatesDataEntity,
)
from work_progress_v2.data.models import inventory
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.schedule import WorkProgressScheduleActivity, WorkProgressScheduleActivityAttachment
from work_progress_v2.data.report_entities import ReportPDFDataEntity
from work_progress_v2.domain.entities import (
    ElementCategoryEntity,
    ElementItemTypeEntity,
    ElementStatusEntity,
    ElementUomEntity,
    ReportConfigEntity,
    SectionScopeDataEntity,
    VendorScopeElementListEntity,
    VendorScopeElementOrganizationEntity,
)
from work_progress_v2.domain.enums import WorkProgressCreationTypeEnum, WorkProgressReportConfigIdEnum
from work_progress_v2.domain.report_entities import (
    ReportBaseDetailEntity,
    ReportDetailAttachmentEntity,
    ReportDetailCreatedByEntity,
    ReportDetailDailyLogEntity,
    ReportDetailDailyLogUpdateEntity,
    ReportDetailEntity,
    ReportDetailManpowerCategoryEntity,
    ReportDetailMaterialUpdateElementEntity,
    ReportDetailMaterialUpdateEntity,
    ReportDetailPdfEntity,
    ReportDetailReportedProjectProgressEntity,
    ReportDetailScheduleUpdateActivityEntity,
    ReportDetailScheduleUpdateActivityStatusEntity,
    ReportDetailScheduleUpdateEntity,
    ReportDetailScopeUpdateEntity,
    ReportDetailScopeUpdateSectionEntity,
    ReportDetailSiteViewPointEntity,
    WorkProgressBlockBaseModel,
)
from work_progress_v2.domain.utils import WorkProgressReportItemBlockHelper

logger = structlog.get_logger(__name__)


class PrepareRepo:
    """
    Args requiresd:
    - self.uom_cache: dict[str, str]
    - self.status_dict : dict[BoqElementStatus.choices]
    These args must be initialized in __init__() of child repo.
    """

    def _get_section(self, element: WorkProgressElement) -> ElementSectionDataEntity:
        if element.boq_element.section:
            return ElementSectionDataEntity(id=element.boq_element.section.pk, name=element.boq_element.section.name)

        section = ElementSectionDataEntity(id=BoqSection.DEFAULT_SECTION_ID, name=BoqSection.DEFAULT_SECTION)
        return section

    def _prepare_item_type_entities(self, item_types: QuerySet[ElementItemType]) -> list[ElementItemTypeDataEntity]:
        entities = []
        for item_type in item_types:
            entities.append(
                ElementItemTypeDataEntity(
                    id=item_type.pk,
                    name=item_type.name,
                    color_code=item_type.color_code,
                )
            )
        return entities

    def _prepare_uom_entities(self, uoms) -> list[ElementUomDataEntity]:
        entities = []
        for uom in uoms:
            entities.append(
                ElementUomDataEntity(
                    name=self.uom_cache.get(str(uom), ""),
                    value=uom,
                )
            )
        return entities

    def _prepare_status_entities(self, statuses) -> list[ElementStatusDataEntity]:
        entities = []
        for status in statuses:
            entities.append(
                ElementStatusDataEntity(
                    name=str(self.status_dict.get(status)),
                    value=status,
                )
            )
        return entities

    def _prepare_category_entities(self, categories: QuerySet[ElementCategory]) -> list[ElementCategoryDataEntity]:
        entities = []
        for category in categories:
            entities.append(
                ElementCategoryDataEntity(
                    id=category.pk,
                    name=category.name,
                    code=category.code,
                )
            )
        return entities

    def _prepare_scope_timeline_data_entity(self, timeline_dates) -> ScopeTimelineDatesDataEntity:
        return ScopeTimelineDatesDataEntity(
            expected_start_date=(
                timeline_dates.expected_start_date.date() if timeline_dates.expected_start_date else None
            ),
            expected_due_date=timeline_dates.execution_due_date.date() if timeline_dates.execution_due_date else None,
            scope_start_date=timeline_dates.actual_start_date.date() if timeline_dates.actual_start_date else None,
            scope_completion_date=(
                timeline_dates.actual_completion_date.date() if timeline_dates.actual_completion_date else None
            ),
        )

    def _get_preview_file(self, element: WorkProgressElement) -> ElementPreviewFileDataEntity | None:
        if element.boq_element.main_preview_file is None:
            return None

        return ElementPreviewFileDataEntity(
            id=element.boq_element.main_preview_file.pk,
            file=element.boq_element.main_preview_file.file.url,
            name=element.boq_element.main_preview_file.name,
            is_main=True,
            type=element.boq_element.main_preview_file.type,
        )

    def _get_element_code(self, element: WorkProgressElement) -> str:
        return ElementCodeService.get_code(
            serial_number=element.boq_element.serial_number,
            custom_type=element.boq_element.custom_type,
            code=element.boq_element.code,
            version=element.boq_element.version,
        )

    def _prepare_element_list_data_entity(self, element: WorkProgressElement) -> ElementListDataEntity:
        return ElementListDataEntity(
            id=element.boq_element.pk,
            name=element.boq_element.name,
            code=self._get_element_code(element=element),
            description=element.boq_element.description,
            quantity=element.current_element_data.get("quantity", 0),
            item_type=(
                ElementItemTypeDataEntity(
                    id=element.boq_element.item_type.pk,
                    name=element.boq_element.item_type.name,
                    color_code=element.boq_element.item_type.color_code,
                )
                if element.boq_element.item_type
                else None
            ),
            status=ElementStatusDataEntity(
                name=str(self.status_dict.get(BoqElementStatus(element.boq_element.element_status))),
                value=element.boq_element.element_status,
            ),
            uom=ElementUomDataEntity(
                name=self.uom_cache.get(str(element.boq_element.uom), ""),
                value=element.boq_element.uom,
            ),
            category=(
                ElementCategoryDataEntity(
                    id=element.boq_element.category.pk,
                    name=element.boq_element.category.name,
                    code=element.boq_element.category.code,
                )
                if element.boq_element.category
                else None
            ),
            section=self._get_section(element=element),
            rate=element.current_element_data.get("client_rate", 0),
            current_input_quantity=element.progress_quantity_input or decimal.Decimal(0),
            current_input_progress=(
                decimal.Decimal(element.progress_percentage_input)
                if element.progress_percentage_input
                else decimal.Decimal(0)
            ),
            current_input_milestone_id=element.milestone_input_id,
            actual_progress_percentage=element.progress_percentage,
            update_method=element.update_method,
            previous_day_input_progress=(
                element.previous_day_progress_percentage_input
                if hasattr(element, "previous_day_progress_percentage_input")
                else None
            ),
            previous_day_input_quantity=(
                element.previous_day_progress_quantity_input
                if hasattr(element, "previous_day_progress_quantity_input")
                else None
            ),
            previous_day_input_milestone_id=(
                element.previous_day_progress_milestone_id_input
                if hasattr(element, "previous_day_progress_milestone_id_input")
                else None
            ),
            comment_count=element.comment_count if hasattr(element, "comment_count") else 0,  # type: ignore
            progress_updated_at=element.progress_updated_at,
            preview_file=self._get_preview_file(element=element),
            created_by_org_id=element.boq_element.created_by.org_id,
            last_day_progress_updated_at=(
                element.last_day_progress_updated_at if hasattr(element, "last_day_progress_updated_at") else None
            ),
            unlocked_at=element.unlocked_at,
        )

    def _prepare_element_preview_file(self, element: WorkProgressElement) -> list[ElementPreviewFileDataEntity]:
        entities: list[ElementPreviewFileDataEntity] = []

        for preview_file in element.boq_element.preview_files.all():  # type: ignore
            entities.append(
                ElementPreviewFileDataEntity(
                    id=preview_file.pk,
                    file=preview_file.file.url,
                    name=preview_file.name,
                    is_main=preview_file.is_main,
                    type=preview_file.type,
                )
            )

        return entities

    def _prepare_element_detail(
        self, element: WorkProgressElement, attachments: list[ElementAttachmentDataEntity]
    ) -> ElementDetailDataEntity:
        entity = self._prepare_element_list_data_entity(element)
        preview_files_entity = self._prepare_element_preview_file(element)

        entity = ElementDetailDataEntity(
            **entity.model_dump(),
            preview_files=preview_files_entity,
            attachments=attachments,
        )

        return entity

    def _prepare_scope_data(self, elements) -> list[ScopeElementBaseEntity]:
        entities = []
        for element in elements:
            entities.append(
                ScopeElementBaseEntity(
                    id=element["boq_element_id"],
                    section_id=(element["boq_element__section_id"] if element.get("boq_element__section_id") else None),
                    progress_percentage=element["progress_percentage"],
                    quantity=element["current_element_data"].get("quantity", 0),
                    amount=element["current_element_data"].get("final_amount", 0),
                    previous_day_progress_percentage=element["previous_day_progress_percentage"],
                    attachment_uploaded_at=element["attachment_uploaded_at"],
                    progress_updated_at=element["progress_updated_at"],
                    update_method=(
                        ItemTypeUpdateMethodChoices(element["update_method"]) if element.get("update_method") else None
                    ),
                    item_type_id=element["boq_element__item_type_id"],
                )
            )
        return entities

    def _prepare_section_data(self, sections) -> list[SectionScopeDataEntity]:
        entities = [
            SectionScopeDataEntity(
                id=BoqSection.DEFAULT_SECTION_ID,
                name=BoqSection.DEFAULT_SECTION,
            )
        ]

        for section in sections:
            entities.append(
                SectionScopeDataEntity(
                    id=section["id"],
                    name=section["name"],
                )
            )
        return entities

    def _prepare_vendor_scope_element_list_data_entity(
        self,
        element: WorkProgressElement,
    ) -> VendorScopeElementListEntity:
        assert element.boq_element.organization is not None, "Organization can not be None"

        return VendorScopeElementListEntity(
            id=element.boq_element.pk,
            name=element.boq_element.name,
            code=element.boq_element.code,
            description=element.boq_element.description,
            quantity=element.boq_element.quantity,
            item_type=(
                ElementItemTypeEntity(
                    id=element.boq_element.item_type.pk,
                    name=element.boq_element.item_type.name,
                    color_code=element.boq_element.item_type.color_code,
                )
                if element.boq_element.item_type
                else None
            ),
            status=ElementStatusEntity(
                name=str(self.status_dict.get(BoqElementStatus(element.boq_element.element_status))),
                value=element.boq_element.element_status,
            ),
            uom=ElementUomEntity(
                name=self.uom_cache.get(str(element.boq_element.uom), ""),
                value=element.boq_element.uom,
            ),
            category=(
                ElementCategoryEntity(
                    id=element.boq_element.category.pk,
                    name=element.boq_element.category.name,
                    code=element.boq_element.category.code,
                )
                if element.boq_element.category
                else None
            ),
            order_rate=element.boq_element.client_rate,
            progress_percentage=element.progress_percentage,
            amount=element.element_final_amount_with_tax,
            organization=VendorScopeElementOrganizationEntity(
                id=element.boq_element.organization.pk,
                name=element.boq_element.organization.name,
            ),
        )


class ReportPrepareRepo:
    def _prepare_report_data(self, report: ProgressReport) -> ReportBaseDetailEntity:
        return ReportBaseDetailEntity(
            id=report.pk,
            created_at=report.created_at,
            created_by_id=report.created_by_id,
            deleted_at=report.deleted_at,
        )

    def _prepare_report_manpower_data_list_entities(
        self,
        report: ProgressReport,
    ) -> list[ReportDetailManpowerCategoryEntity]:
        manpower_list: list[ReportDetailManpowerCategoryEntity] = []

        for manpower in report.pr_manpower.all():
            manpower: ProgressReportManPower
            manpower_list.append(
                ReportDetailManpowerCategoryEntity(
                    id=manpower.pk,
                    name=manpower.category_name,
                    value=manpower.count,
                )
            )

        return manpower_list

    def _prepare_report_site_view_point_data_list_entities(
        self,
        report: ProgressReport,
    ) -> list[ReportDetailSiteViewPointEntity]:
        site_view_point_list: dict[int, ReportDetailSiteViewPointEntity] = {}

        for view_point_mapping in report.mappings.all():
            view_point_mapping: ProgressReportSiteViewPointMapping

            site_view_point_list[view_point_mapping.site_view_point_id] = ReportDetailSiteViewPointEntity(
                id=view_point_mapping.site_view_point_id,
                name=view_point_mapping.site_view_point.name,
                previous_attachments=[],
                todays_attachments=[],
            )

            for attachment in view_point_mapping.attachments.all():
                attachment: ProgressReportSiteViewPointMappingAttachment

                attachment_entity = ReportDetailAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name if attachment.name else attachment.file.url.split("/")[-1],
                    url=attachment.file.url,
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                )

                if attachment.is_previous:
                    site_view_point_list[view_point_mapping.site_view_point_id].previous_attachments.append(
                        attachment_entity
                    )
                else:
                    site_view_point_list[view_point_mapping.site_view_point_id].todays_attachments.append(
                        attachment_entity
                    )

            site_view_point_list[view_point_mapping.site_view_point_id].previous_attachments.reverse()

        return list(site_view_point_list.values())

    def _prepare_report_inventory_data_list_entities(self, report: ProgressReport) -> ReportDetailMaterialUpdateEntity:
        material_update = ReportDetailMaterialUpdateEntity(
            total_stock_value=decimal.Decimal(0),
            consumed=[],
            received=[],
            transferred_out=[],
        )

        for item in report.inventory_stock_items.all():
            item: inventory.WorkProgressInventoryStockItem

            material_update.total_stock_value += item.stock_value

            if item.type == WorkProgressInventoryStockItemTypeChoices.CONSUMED:
                material_update.consumed.append(
                    ReportDetailMaterialUpdateElementEntity(
                        id=item.item_id,
                        name=item.item_name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                )

            if item.type == WorkProgressInventoryStockItemTypeChoices.RECEIVED:
                material_update.received.append(
                    ReportDetailMaterialUpdateElementEntity(
                        id=item.item_id,
                        name=item.item_name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                )

            if item.type == WorkProgressInventoryStockItemTypeChoices.TRANSFERRED_OUT:
                material_update.transferred_out.append(
                    ReportDetailMaterialUpdateElementEntity(
                        id=item.item_id,
                        name=item.item_name,
                        quantity=item.quantity,
                        uom_name=item.uom_name,
                    )
                )

        return material_update

    def _prepare_report_config_ids(self, report: ProgressReport) -> list[WorkProgressReportConfigIdEnum]:
        report_config = [ReportConfigEntity(**section) for section in report.config.config]

        checked_config_ids: list[WorkProgressReportConfigIdEnum] = []

        def get_checked_config_ids(config: list[ReportConfigEntity]):
            for section in config:
                if section.checked:
                    checked_config_ids.append(section.id)
                if section.children:
                    get_checked_config_ids(section.children)

        get_checked_config_ids(config=report_config)

        return checked_config_ids

    def _prepare_report_schedule_data_list_entities(
        self, report: ProgressReport
    ) -> ReportDetailScheduleUpdateEntity | None:
        if not hasattr(report, "schedule"):
            return None

        activities = []
        attachments = []

        for attachment in report.schedule.attachments.all():
            attachment: WorkProgressScheduleActivityAttachment

            attachments.append(
                ReportDetailAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    url=attachment.file.url,
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                )
            )

        for activity in report.schedule.activities.all():
            activity: WorkProgressScheduleActivity

            activities.append(
                ReportDetailScheduleUpdateActivityEntity(
                    id=activity.pk,
                    name=activity.name,
                    wbs=activity.wbs,
                    status=(
                        ReportDetailScheduleUpdateActivityStatusEntity(
                            **TimelineStatusHelper.get_status(
                                status_type=TimelineStatusEnum(activity.status),
                                days=activity.delay_days if activity.delay_days else None,
                            ).model_dump()
                        )
                        if activity.status
                        else None
                    ),
                    day_progress=activity.today_progress,
                    total_progress=activity.total_progress,
                )
            )

            for attachment in activity.attachments.all():
                attachment: WorkProgressScheduleActivityAttachment

                attachments.append(
                    ReportDetailAttachmentEntity(
                        id=attachment.pk,
                        name=attachment.name,
                        url=attachment.file.url,
                        thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                        uploaded_at=attachment.uploaded_at,
                        activity_name=activity.name,
                    )
                )

        return ReportDetailScheduleUpdateEntity(
            id=report.schedule.pk,
            total_progress=report.schedule.total_progress,
            today_progress=report.schedule.today_progress,
            status=(
                ReportDetailScheduleUpdateActivityStatusEntity(
                    **TimelineStatusHelper.get_status(
                        status_type=TimelineStatusEnum(report.schedule.status_type),
                        days=report.schedule.delay_days if report.schedule.delay_days else None,
                    ).model_dump()
                )
                if report.schedule.status_type
                else None
            ),
            planned_end_date=report.schedule.planned_end_date,
            activities=activities,
            attachments=attachments,
        )

    def _prepare_report_data_list_entities(
        self,
        report: ProgressReport,
        is_vendor_report: bool = False,
    ) -> ReportDetailEntity:
        manpower_list = self._prepare_report_manpower_data_list_entities(report)
        site_view_point_list = self._prepare_report_site_view_point_data_list_entities(report)
        material_update = self._prepare_report_inventory_data_list_entities(report)
        checked_config_ids = self._prepare_report_config_ids(report)
        schedule_update = self._prepare_report_schedule_data_list_entities(report)

        daily_log = ReportDetailDailyLogEntity(
            today_update=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
            blocker=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
            tomorrow_plan=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
        )

        scope_update = ReportDetailScopeUpdateEntity(
            total_element_count=report.total_element_count,
            today_progress=report.today_progress_percentage,
            total_progress=report.derived_project_progress_percent,
            section_updates=[],
        )

        section_updates_mapping: dict[int, ReportDetailScopeUpdateSectionEntity] = {}

        for item_attachment in report.attachments.all():
            item_attachment: ProgressReportAttachment

            # if creation_type is manual and section_id is None and element_id is None
            # then it is daily log attachment and currently we can only add attachments in today update
            if (
                item_attachment.creation_type == PROGRESS_REPORT_CONSTANTS.MANUAL
                and item_attachment.section_id is None
                and item_attachment.element_id is None
            ):
                daily_log.today_update.attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=item_attachment.file.url,
                        uploaded_at=item_attachment.uploaded_at,
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                    )
                )

            else:
                if item_attachment.section_id is None or item_attachment.section is None:
                    # TODO: some project has section_id as None, need to check
                    #  why it is happening check project 'PEPPER Fry' offset: 40 limit: 10
                    # assert item_attachment.section_id is not None, "Section id can not be None"
                    continue

                if item_attachment.section_id not in section_updates_mapping:
                    section_updates_mapping[item_attachment.section_id] = ReportDetailScopeUpdateSectionEntity(
                        id=item_attachment.section_id,
                        name=item_attachment.section.name,
                        progress_percentage=item_attachment.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                section_updates_mapping[item_attachment.section_id].attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=item_attachment.file.url,
                        uploaded_at=item_attachment.uploaded_at,
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                        element_name=item_attachment.element_name,
                    )
                )

        for item in report.items.all():
            item: ProgressReportItem
            creation_type = WorkProgressCreationTypeEnum(item.creation_type)

            if creation_type == WorkProgressCreationTypeEnum.MANUAL:
                blocks: list[WorkProgressBlockBaseModel] = []

                for block in item.blocks:
                    if item.version == 1:
                        blocks.append(WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=block))
                    else:
                        blocks.append(WorkProgressBlockBaseModel(**block))

                if item.type == ProgressReportItem.UPDATE or item.type == ProgressReportItem.TODAY:
                    daily_log.today_update.updates.append(blocks)
                elif item.type == ProgressReportItem.BLOCKER:
                    daily_log.blocker.updates.append(blocks)
                elif item.type == ProgressReportItem.TOMORROW_PLAN:
                    daily_log.tomorrow_plan.updates.append(blocks)

            if creation_type == WorkProgressCreationTypeEnum.DERIVED:
                if item.section_id is None or item.section is None:
                    # TODO: some project has section_id as None,
                    #  need to check why it is happening check project 'PEPPER Fry'
                    # assert item.section_id is not None, "Section id can not be None"
                    continue

                if item.section_id not in section_updates_mapping:
                    section_updates_mapping[item.section_id] = ReportDetailScopeUpdateSectionEntity(
                        id=item.section_id,
                        name=item.section.name,
                        progress_percentage=item.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                blocks: list[WorkProgressBlockBaseModel] = []

                for block in item.blocks:
                    if item.version == 1:
                        blocks.append(WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=block))
                    else:
                        blocks.append(WorkProgressBlockBaseModel(**block))

                section_updates_mapping[item.section_id].updates.append(blocks)

        scope_update.section_updates = list(section_updates_mapping.values())

        return ReportDetailEntity(
            id=report.pk,
            created_at=report.created_at,
            reported_project_progress=ReportDetailReportedProjectProgressEntity(
                reported_progress_percentage=report.project_progress_percent,
                projected_end_date=report.projected_end_date,
            ),
            manpower_category_list=manpower_list,
            site_view_point_list=site_view_point_list,
            comment_count=report.comment_count if hasattr(report, "comment_count") and report.comment_count else 0,
            total_manpower_count=report.total_manpower_count,
            daily_log=daily_log,
            created_by=(
                ReportDetailCreatedByEntity(
                    id=report.created_by.id,
                    name=report.created_by.name,
                    photo=report.created_by.photo.url if report.created_by.photo else None,
                )
                if not is_vendor_report
                else ReportDetailCreatedByEntity(
                    id=report.created_by.org.id,
                    name=report.created_by.org.name,
                    photo=report.created_by.org.logo.url if report.created_by.org.logo else None,
                )
            ),
            scope_update=scope_update,
            checked_config_ids=checked_config_ids,
            material_update=material_update,
            schedule_update=schedule_update,
            pdf=ReportDetailPdfEntity(
                url=PublicMediaFileStorage.url(report.pdf.url) if report.pdf else None,
                status=DownloadProgressStatusEnum(report.pdf_status),
            ),
        )

    def _prepare_daywise_scope_update_data(self, report: ProgressReport) -> ReportDetailScopeUpdateEntity:
        scope_update = ReportDetailScopeUpdateEntity(
            total_element_count=report.total_element_count,
            today_progress=report.today_progress_percentage,
            total_progress=report.derived_project_progress_percent,
            section_updates=[],
        )

        section_updates_mapping: dict[int, ReportDetailScopeUpdateSectionEntity] = {}

        for item_attachment in report.attachments.all():
            item_attachment: ProgressReportAttachment

            # if creation_type is manual and section_id is None and element_id is None
            # then it is daily log attachment and currently we can only add attachments in today update
            if (
                item_attachment.creation_type == PROGRESS_REPORT_CONSTANTS.MANUAL
                and item_attachment.section_id is None
                and item_attachment.element_id is None
            ):
                pass
            else:
                if item_attachment.section_id is None or item_attachment.section is None:
                    # TODO: some project has section_id as None, need to check
                    #  why it is happening check project 'PEPPER Fry' offset: 40 limit: 10
                    # assert item_attachment.section_id is not None, "Section id can not be None"
                    continue

                if item_attachment.section_id not in section_updates_mapping:
                    section_updates_mapping[item_attachment.section_id] = ReportDetailScopeUpdateSectionEntity(
                        id=item_attachment.section_id,
                        name=item_attachment.section.name,
                        progress_percentage=item_attachment.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                section_updates_mapping[item_attachment.section_id].attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=item_attachment.file.url,
                        uploaded_at=item_attachment.uploaded_at,
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                        element_name=item_attachment.element_name,
                    )
                )

        for item in report.items.all():
            item: ProgressReportItem
            creation_type = WorkProgressCreationTypeEnum(item.creation_type)

            if creation_type == WorkProgressCreationTypeEnum.DERIVED:
                if item.section_id is None or item.section is None:
                    # TODO: some project has section_id as None,
                    #  need to check why it is happening check project 'PEPPER Fry'
                    # assert item.section_id is not None, "Section id can not be None"
                    continue

                if item.section_id not in section_updates_mapping:
                    section_updates_mapping[item.section_id] = ReportDetailScopeUpdateSectionEntity(
                        id=item.section_id,
                        name=item.section.name,
                        progress_percentage=item.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                blocks: list[WorkProgressBlockBaseModel] = []

                for block in item.blocks:
                    if item.version == 1:
                        blocks.append(WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=block))
                    else:
                        blocks.append(WorkProgressBlockBaseModel(**block))

                section_updates_mapping[item.section_id].updates.append(blocks)

        scope_update.section_updates = list(section_updates_mapping.values())
        return scope_update

    def _prepare_reports_list_data_for_export(self, report: ProgressReport) -> ReportPDFDataEntity:
        logger.info("Preparing report list data for export.", report_id=report.pk)
        reported_project_progress = ReportDetailReportedProjectProgressEntity(
            reported_progress_percentage=report.project_progress_percent,
            projected_end_date=report.projected_end_date,
        )
        site_view_point_list = self._prepare_daywise_site_view_point_data(report=report)
        manpower_data = self._prepare_daywise_manpower_data(report=report)
        daily_log = self._prepare_daywise_daily_log_data(report=report)
        # will be used for backward compatibility of older reports data
        scope_update = self._prepare_daywise_scope_update_data(report=report)
        logger.info("report list data prepared.")

        return ReportPDFDataEntity(
            created_at=report.created_at,
            created_by=ReportDetailCreatedByEntity(
                id=report.created_by.pk,
                name=report.created_by.name,
                photo=report.created_by.photo.url if report.created_by.photo else None,
            ),
            creator_org=ReportDetailCreatedByEntity(
                id=report.created_by.org.pk,
                name=report.created_by.org.name,
                photo=report.created_by.org.logo.url if report.created_by.org.logo else None,
            ),
            reported_project_progress=reported_project_progress,
            site_view_point_list=site_view_point_list,
            manpower_category_list=manpower_data,
            total_manpower_count=report.total_manpower_count,
            daily_log=daily_log,
            scope_update=scope_update,
        )

    def _prepare_daywise_manpower_data(self, report) -> list[ReportDetailManpowerCategoryEntity]:
        manpower_category_list: list[ReportDetailManpowerCategoryEntity] = []
        for manpower in report.pr_manpower.all():
            manpower: ProgressReportManPower
            manpower_category_list.append(
                ReportDetailManpowerCategoryEntity(
                    id=manpower.pk,
                    name=manpower.category_name,
                    value=manpower.count,
                )
            )
        logger.info("Manpower data prepared.")
        return manpower_category_list

    def _prepare_daywise_site_view_point_data(self, report: ProgressReport) -> list[ReportDetailSiteViewPointEntity]:
        site_view_point_data: dict[int, ReportDetailSiteViewPointEntity] = {}
        # for report in reports:
        for view_point_mapping in report.mappings.all():
            view_point_mapping: ProgressReportSiteViewPointMapping

            site_view_point_data[view_point_mapping.site_view_point_id] = ReportDetailSiteViewPointEntity(
                id=view_point_mapping.site_view_point_id,
                name=view_point_mapping.site_view_point.name,
                previous_attachments=[],
                todays_attachments=[],
            )

            for attachment in view_point_mapping.attachments.all():
                attachment: ProgressReportSiteViewPointMappingAttachment

                url = attachment.file.url
                attachment_entity = ReportDetailAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name if attachment.name else url.split("/")[-1],
                    url=PublicMediaFileStorage.url(url),
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                )
                if attachment.is_previous:
                    site_view_point_data[view_point_mapping.site_view_point_id].previous_attachments.append(
                        attachment_entity
                    )
                else:
                    site_view_point_data[view_point_mapping.site_view_point_id].todays_attachments.append(
                        attachment_entity
                    )

        logger.info("Site view point data prepared.")
        return list(site_view_point_data.values())

    def _prepare_daywise_daily_log_data(self, report: ProgressReport) -> ReportDetailDailyLogEntity:
        daily_log_data = ReportDetailDailyLogEntity(
            today_update=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
            blocker=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
            tomorrow_plan=ReportDetailDailyLogUpdateEntity(updates=[], attachments=[]),
        )

        for item_attachment in report.attachments.all():
            item_attachment: ProgressReportAttachment

            """
            Currently adding attachments in today's updates only
            If creation_type = manual & section_id is None & element_id is None, then it is daily log attachment
            """
            if (
                item_attachment.creation_type == PROGRESS_REPORT_CONSTANTS.MANUAL
                and not item_attachment.section
                and not item_attachment.element
            ):
                daily_log_data.today_update.attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=PublicMediaFileStorage.url(item_attachment.file.url),
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                        uploaded_at=item_attachment.uploaded_at,
                    )
                )

        for item in report.items.all():
            item: ProgressReportItem
            creation_type = WorkProgressCreationTypeEnum(item.creation_type)

            if creation_type == WorkProgressCreationTypeEnum.MANUAL:
                blocks: list[WorkProgressBlockBaseModel] = []
                for item_block in item.blocks:
                    if item.version == 1:
                        block = WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=item_block)
                    else:
                        block = WorkProgressBlockBaseModel(**item_block)
                    blocks.append(block)

                if item.type == ProgressReportItem.UPDATE or item.type == ProgressReportItem.TODAY:
                    daily_log_data.today_update.updates.append(blocks)
                elif item.type == ProgressReportItem.BLOCKER:
                    daily_log_data.blocker.updates.append(blocks)
                elif item.type == ProgressReportItem.TOMORROW_PLAN:
                    daily_log_data.tomorrow_plan.updates.append(blocks)

        logger.info("Daily log data prepared.")
        return daily_log_data
