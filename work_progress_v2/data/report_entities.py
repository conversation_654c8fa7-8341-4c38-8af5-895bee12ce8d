import datetime
import decimal

from common.pydantic.base_model import BaseModelV2, PydanticPaginatedBaseModel, WorkProgressBlockBaseModel
from common.pydantic.custom_fields import HashIdInt
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.domain.report_entities import (
    ExportREportPDFManpowerDateWiseEntity,
    ExportReportPDFSiteViewPointEntity,
    ReportDetailAttachmentEntity,
    ReportDetailCreatedByEntity,
    ReportDetailDailyLogEntity,
    ReportDetailEntity,
    ReportDetailManpowerCategoryEntity,
    ReportDetailReportedProjectProgressEntity,
    ReportDetailScopeUpdateEntity,
    ReportDetailSiteViewPointEntity,
    ReportExportPDFHeaderEntity,
    ReportPDFFooterEntity,
)


class SiteViewPointDataEntity(BaseModelV2):
    id: HashIdInt
    name: str


class SiteViewPointAttachmentDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: str
    thumbnail_url: str | None
    uploaded_at: datetime.datetime
    site_view_point_id: HashIdInt
    is_attachment_of_current_day: bool


class LastReportReportedProjectProgressDataEntity(BaseModelV2):
    reported_progress_percentage: decimal.Decimal | None = None
    projected_end_date: datetime.date | None = None


class PrefillScopeUpdateElementAttachmentDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: str
    thumbnail_url: str | None
    uploaded_at: datetime.datetime


class PrefillScopeUpdateDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    section_id: HashIdInt
    section_name: str
    attachments: list[PrefillScopeUpdateElementAttachmentDataEntity] = []
    uom_name: str
    item_type_id: HashIdInt | None = None
    progress_percentage: decimal.Decimal
    progress_percentage_input: decimal.Decimal | None = None
    progress_quantity_input: decimal.Decimal | None = None
    milestone_input_id: HashIdInt | None = None
    update_method: ItemTypeUpdateMethodChoices | None = None
    previous_day_progress_percentage: decimal.Decimal | None = None
    previous_day_progress_percentage_input: decimal.Decimal | None = None
    previous_day_progress_quantity_input: decimal.Decimal | None = None
    previous_day_progress_milestone_input_id: HashIdInt | None = None


class LastReportForDailyLogUpdateAttachmentDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    url: str
    thumbnail_url: str | None
    uploaded_at: datetime.datetime


class LastReportForDailyLogUpdateDataEntity(BaseModelV2):
    updates: list[list[WorkProgressBlockBaseModel]]
    attachments: list[LastReportForDailyLogUpdateAttachmentDataEntity]


class LastReportForDailyLogDataEntity(BaseModelV2):
    blocker: LastReportForDailyLogUpdateDataEntity
    today_update: LastReportForDailyLogUpdateDataEntity
    tomorrow_plan: LastReportForDailyLogUpdateDataEntity


class CreateSiteViewPointDataEntity(BaseModelV2):
    id: HashIdInt
    name: str


class CreateSiteViewPointMappingDataEntity(BaseModelV2):
    id: HashIdInt
    site_view_point_id: HashIdInt


class VendorReportCreatedByDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: str | None
    organization_name: str | None


class CreateProjectManpowerCategoryDataEntity(BaseModelV2):
    name: str


class ReportListPaginatedEntity(PydanticPaginatedBaseModel):
    data: list[ReportDetailEntity]


"""
SINGLE REPORT PDF DATA ENTITIES
"""


class ReportPDFBlockDataEntity(BaseModelV2):
    blocks: list[WorkProgressBlockBaseModel]


class ReportPDFStatusDataEntity(BaseModelV2):
    name: str
    color: str


# Scope Update Data Entities
class ReportPDFScopeUpdateSectionDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    updates: list[ReportPDFBlockDataEntity]
    progress_percentage: decimal.Decimal
    attachments: list[ReportDetailAttachmentEntity]


class ReportPDFScopeUpdateDataEntity(BaseModelV2):
    total_items: int
    day_progress: decimal.Decimal
    scope_progress: decimal.Decimal
    sections: list[ReportPDFScopeUpdateSectionDataEntity]


# Daily Log Data Entities
class ReportPDFDailyLogUpdateDataEntity(BaseModelV2):
    updates: list[list[WorkProgressBlockBaseModel]] = []
    attachments: list[ReportDetailAttachmentEntity]


class ReportPDFDailyLogDataEntity(BaseModelV2):
    today_update: ReportPDFDailyLogUpdateDataEntity
    blocker: ReportPDFDailyLogUpdateDataEntity
    tomorrow_plan: ReportPDFDailyLogUpdateDataEntity


# Schedule Data Entities
class ReportPDFScheduleActivityDataEntity(BaseModelV2):
    wbs: str
    name: str
    status: ReportPDFStatusDataEntity | None
    days_progress: decimal.Decimal
    total_progress: decimal.Decimal


class ReportPDFScheduleDataEntity(BaseModelV2):
    id: HashIdInt
    schedule_progress: decimal.Decimal | None
    days_progress: decimal.Decimal | None
    status: ReportPDFStatusDataEntity | None
    planned_end_date: datetime.date | None
    activities: list[ReportPDFScheduleActivityDataEntity] = []


# Manpower Data Entities
class ReportPDFManpowerCategoryDataEntity(BaseModelV2):
    name: str
    value: int


class ReportPDFManpowerDataEntity(BaseModelV2):
    total_manpower: int
    updates: list[ReportPDFManpowerCategoryDataEntity]


# Material Data Entities
class ReportPDFStockDataEntity(BaseModelV2):
    name: str
    quantity: decimal.Decimal
    uom: str


class ReportPDFMaterialDataEntity(BaseModelV2):
    total_stock_value: decimal.Decimal
    received: list[ReportPDFStockDataEntity]
    consumed: list[ReportPDFStockDataEntity]
    transferred_out: list[ReportPDFStockDataEntity]


# Site View Point Data Entities
class ReportPDFSiteViewPointDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    todays_attachments: list[ReportDetailAttachmentEntity]
    previous_attachments: list[ReportDetailAttachmentEntity]


# Reported Project Progress Data Entities
class ReportPDFReportedProjectProgressDataEntity(BaseModelV2):
    reported_progress: decimal.Decimal | None = None
    projected_end_date: datetime.date | None = None


# Report Data Entities
class ReportPDFSectionDataEntity(BaseModelV2):
    scope_update: ReportPDFScopeUpdateDataEntity
    daily_log: ReportPDFDailyLogDataEntity
    manpower: ReportPDFManpowerDataEntity
    site_view_point: list[ReportPDFSiteViewPointDataEntity]
    reported_project_progress: ReportPDFReportedProjectProgressDataEntity
    material: ReportPDFMaterialDataEntity | None = None  # Remove None after implementing material data
    schedule: ReportPDFScheduleDataEntity | None = None  # Remove None after implementing schedule data


"""
CUMULATIVE REPORT PDF DATA ENTITIES
"""


class SectionDayWiseDataEntity(BaseModelV2):
    date: datetime.date
    id: HashIdInt
    name: str
    progress_percentage: decimal.Decimal


class ElementDayWiseDataEntity(BaseModelV2):
    element_id: HashIdInt
    element_name: str
    section_id: HashIdInt
    section_name: str
    date: datetime.date
    latest_timeline_id: HashIdInt
    day_end_progress_percentage: decimal.Decimal
    day_end_update_method: str
    day_end_input_quantity: decimal.Decimal | None
    day_end_uom_name: str | None
    day_end_milestone_name: str | None
    previous_day_end_progress_percentage: decimal.Decimal | None
    pervious_day_end_update_method: str | None
    previous_day_end_input_quantity: decimal.Decimal | None
    previous_day_end_uom_name: str | None
    previous_day_milestone_name: str | None
    attachments: list[ReportDetailAttachmentEntity]


class ProjectOrgDaywiseDataEntity(BaseModelV2):
    date: datetime.date
    total_items: int
    day_progress: decimal.Decimal
    total_progress: decimal.Decimal


# Manpower Data Entities
class ReportPDFManpowerDateWiseDataEntity(BaseModelV2):
    date: datetime.date
    manpower_data: ReportPDFManpowerDataEntity


# Site View Point Data Entities
class CumulativeReportPDFSiteViewPointDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    attachments: list[ReportDetailAttachmentEntity]


class CumulativeReportsData(BaseModelV2):
    header: ReportExportPDFHeaderEntity
    manpower: list[ExportREportPDFManpowerDateWiseEntity]
    site_view_point_list: list[ExportReportPDFSiteViewPointEntity]
    footer: ReportPDFFooterEntity


class ReportPDFDataEntity(BaseModelV2):
    created_at: datetime.datetime
    created_by: ReportDetailCreatedByEntity
    creator_org: ReportDetailCreatedByEntity
    reported_project_progress: ReportDetailReportedProjectProgressEntity
    site_view_point_list: list[ReportDetailSiteViewPointEntity] = []
    manpower_category_list: list[ReportDetailManpowerCategoryEntity] = []
    total_manpower_count: int
    daily_log: ReportDetailDailyLogEntity
    scope_update: ReportDetailScopeUpdateEntity


class ReportPDFDataWithProjectDataEntity(BaseModelV2):
    report_data: list[ReportPDFDataEntity]
    project_name: str
    project_job_id: str
    org: ReportDetailCreatedByEntity


class ReportProjectDataEntity(BaseModelV2):
    name: str
    job_id: str
