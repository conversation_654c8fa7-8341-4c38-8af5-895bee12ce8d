from django.core.validators import MinValueValidator
from django.db import models

from common.constants import (
    ElementNameConfig,
    QuantityDecimalConfig,
    StockValueDecimalConfig,
)
from common.models import CreateModel
from inventory.data.models import InventoryStockItem
from progressreport.models import ProgressReport
from work_progress_v2.data.choices import WorkProgressInventoryStockItemTypeChoices


class WorkProgressInventoryStockItem(CreateModel):
    item_id: int
    progress_report_id: int

    progress_report = models.ForeignKey(
        ProgressReport,
        on_delete=models.RESTRICT,
        related_name="inventory_stock_items",
    )

    item = models.ForeignKey(
        InventoryStockItem,
        on_delete=models.RESTRICT,
        related_name="work_progress_inventory_stock_items",
    )
    stock_value = models.DecimalField(
        max_digits=StockValueDecimalConfig.MAX_DIGITS,
        decimal_places=StockValueDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )
    item_name = models.CharField(max_length=ElementNameConfig.MAX_LENGTH)
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )
    uom_name = models.CharField(max_length=ElementNameConfig.MAX_LENGTH)
    type = models.CharField(choices=WorkProgressInventoryStockItemTypeChoices.choices, max_length=50)

    class Meta:
        db_table = "work_progress_inventory_stock_items"
        verbose_name = "Inventory Stock Item"
        verbose_name_plural = "Inventory Stock Items"
