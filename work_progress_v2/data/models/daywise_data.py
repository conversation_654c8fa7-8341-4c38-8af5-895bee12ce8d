import datetime
import decimal
from typing import <PERSON>V<PERSON>

from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MinV<PERSON>ueValidator
from django.db import models
from django.db.models import Prefetch

from common.db.manager.base import BaseManager
from common.db.manager.base_queryset import AvailableQuerySet
from common.db.manager.types import M
from common.models import UpdateModel
from core.models import Organization
from project.data.models import Project
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.timeline import WorkProgressElementTimeline


class WorkProgressElementDayWiseDataQueryset(AvailableQuerySet[M]):
    def prefetch_element_attachments_in_date_range(self, start_date: datetime.date, end_date: datetime.date):
        from progressreport.models import ProgressReportElementAttachment

        prefetch = Prefetch(
            "element__boq_element__pr_attachments",
            queryset=ProgressReportElementAttachment.objects.select_related("element")
            .filter(uploaded_at__date__range=(start_date, end_date))
            .filter(deleted_at__isnull=True)
            .order_by("uploaded_at"),
        )
        return self.prefetch_related(prefetch)


class WorkProgressElementDayWiseDataManager(BaseManager[M]):
    def get_queryset(self) -> WorkProgressElementDayWiseDataQueryset[M]:
        return WorkProgressElementDayWiseDataQueryset(self.model, using=self._db)


class WorkProgressElementDayWiseData(UpdateModel):
    element_id: int
    latest_timeline_id: int

    element = models.ForeignKey(WorkProgressElement, on_delete=models.RESTRICT, related_name="element_daywise_data")
    date = models.DateField()
    day_end_progress_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=decimal.Decimal(0),
        validators=[MaxValueValidator(100), MinValueValidator(0)],
    )
    latest_timeline = models.ForeignKey(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="element_daywise_data"
    )
    day_end_uom = models.PositiveSmallIntegerField(
        null=True, blank=True
    )  # TODO: Remove null after running backfilling previous data
    day_end_uom_name = models.CharField(
        max_length=100, null=True, blank=True
    )  # TODO: Remove null after running backfilling previous data
    day_end_milestone_name = models.CharField(max_length=100, null=True, blank=True)

    objects: ClassVar[WorkProgressElementDayWiseDataManager] = WorkProgressElementDayWiseDataManager()

    class Meta:
        db_table = "work_progress_element_daywise_data"
        unique_together = ("element", "date")


class WorkProgressSectionDayWiseDataQueryset(AvailableQuerySet[M]):
    pass


class WorkProgressSectionDayWiseDataManager(BaseManager[M]):
    def get_queryset(self) -> WorkProgressSectionDayWiseDataQueryset[M]:
        return WorkProgressSectionDayWiseDataQueryset(self.model, using=self._db)


class WorkProgressSectionDayWiseData(UpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="wp_section_daywise_data")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="wp_section_daywise_data")
    day_end_data = models.JSONField()
    date = models.DateField()

    objects: ClassVar[WorkProgressSectionDayWiseDataManager] = WorkProgressSectionDayWiseDataManager()

    class Meta:
        db_table = "work_progress_section_daywise_data"
        unique_together = ("project", "organization", "date")


class WorkProgressProjectDayWiseDataQueryset(AvailableQuerySet[M]):
    pass


class WorkProgressProjectDayWiseDataManager(BaseManager[M]):
    def get_queryset(self) -> WorkProgressProjectDayWiseDataQueryset[M]:
        return WorkProgressProjectDayWiseDataQueryset(self.model, using=self._db)


class WorkProgressProjectDayWiseData(UpdateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="wp_project_daywise_data")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="wp_project_daywise_data")
    day_end_data = models.JSONField()
    date = models.DateField()

    objects: ClassVar[WorkProgressProjectDayWiseDataManager] = WorkProgressProjectDayWiseDataManager()

    class Meta:
        db_table = "work_progress_project_daywise_data"
        unique_together = ("project", "organization", "date")
