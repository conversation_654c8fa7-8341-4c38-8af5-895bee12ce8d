import decimal
from typing import <PERSON>V<PERSON>

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Min<PERSON><PERSON>ueValidator
from django.db import models

from boq.data.models import BoqElementActionHistory, BoqElementHistory
from common.constants import QuantityDecimalConfig
from common.db.manager.base import BaseManager
from common.db.manager.base_queryset import AvailableQuerySet
from common.db.manager.types import M
from common.models import CreateModelWithDateOverride
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementItemType
from progressreport.models import ProgressReportElementAttachment
from work_progress_v2.data.choices import WorkProgressElementActionChoices, WorkProgressElementAttachmentActionChoices
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.milestone import ItemTypeMileStone


class WorkProgressElementTimelineQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class WorkProgressElementTimelineManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> WorkProgressElementTimelineQueryset[M]:  # type: ignore
        return WorkProgressElementTimelineQueryset(self.model, using=self._db)


class WorkProgressElementTimeline(CreateModelWithDateOverride):
    element_id: int
    boq_element_action_history_id: int | None

    element = models.ForeignKey(WorkProgressElement, on_delete=models.RESTRICT, related_name="timeline")
    action = models.CharField(choices=WorkProgressElementActionChoices.choices, max_length=100)

    objects: ClassVar[WorkProgressElementTimelineManager] = WorkProgressElementTimelineManager()

    class Meta:
        db_table = "work_progress_element_timeline"
        verbose_name = "Work Progress Element Timeline"
        verbose_name_plural = "Work Progress Element Timelines"


class PercentageHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class PercentageHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> PercentageHistoryQueryset[M]:  # type: ignore
        return PercentageHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class PercentageHistory(CreateModelWithDateOverride):
    timeline_id: int
    item_type_id: int | None
    previous_day_percentage_id: int | None

    timeline = models.OneToOneField(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="percentage_history"
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=decimal.Decimal(0),
        validators=[MaxValueValidator(100), MinValueValidator(0)],
    )
    item_type = models.ForeignKey(
        ElementItemType,
        on_delete=models.RESTRICT,
        related_name="percentage_history",
        null=True,
        blank=True,
        default=None,
    )
    """
    update_method:
        -This field is used to store the update method of the element at that point of time
        -If update_method is not available on wp_element, then it will store the default update
            method of the item_type
        (i.e. storing type of update method shown at front end UI for that element)
    """
    update_method = models.CharField(max_length=50, choices=ItemTypeUpdateMethodChoices.choices, null=True, blank=True)
    previous_day_percentage = models.ForeignKey(
        "PercentageHistory", on_delete=models.RESTRICT, default=None, null=True, blank=True
    )

    objects: ClassVar[PercentageHistoryManager] = PercentageHistoryManager()

    class Meta:
        db_table = "work_progress_element_percentage_history"
        verbose_name = "Percentage History"
        verbose_name_plural = "Percentage Histories"


class UpdateMethodHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class UpdateMethodHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> UpdateMethodHistoryQueryset[M]:  # type: ignore
        return UpdateMethodHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class UpdateMethodHistory(CreateModelWithDateOverride):
    timeline_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="update_method_history"
    )
    update_method = models.CharField(max_length=50, choices=ItemTypeUpdateMethodChoices.choices)

    objects: ClassVar[UpdateMethodHistoryManager] = UpdateMethodHistoryManager()

    class Meta:
        db_table = "work_progress_element_update_method_history"
        verbose_name = "Update Method History"
        verbose_name_plural = "Update Method Histories"


class AttachmentHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class AttachmentHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> AttachmentHistoryQueryset[M]:  # type: ignore
        return AttachmentHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class AttachmentHistory(CreateModelWithDateOverride):
    timeline_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="attachment_history"
    )
    attachment = models.ForeignKey(ProgressReportElementAttachment, on_delete=models.RESTRICT, related_name="history")
    action = models.CharField(choices=WorkProgressElementAttachmentActionChoices.choices, max_length=50)

    objects: ClassVar[AttachmentHistoryManager] = AttachmentHistoryManager()

    class Meta:
        db_table = "work_progress_element_attachment_history"
        verbose_name = "Attachment History"
        verbose_name_plural = "Attachment Histories"


class InputProgressQuantityHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class InputProgressQuantityHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> InputProgressQuantityHistoryQueryset[M]:  # type: ignore
        return InputProgressQuantityHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class InputProgressQuantityHistory(CreateModelWithDateOverride):
    timeline_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline,
        on_delete=models.RESTRICT,
        related_name="input_progress_quantity_history",
    )
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        null=True,
    )
    # TODO: remove null and blank after migration
    uom = models.PositiveSmallIntegerField(null=True, blank=True)
    uom_name = models.CharField(max_length=50, null=True, blank=True)

    objects: ClassVar[InputProgressQuantityHistoryManager] = InputProgressQuantityHistoryManager()

    class Meta:
        db_table = "work_progress_element_input_progress_quantity_history"
        verbose_name = "Input Progress Quantity History"
        verbose_name_plural = "Input Progress Quantity Histories"


class InputProgressPercentageHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class InputProgressPercentageHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> InputProgressPercentageHistoryQueryset[M]:  # type: ignore
        return InputProgressPercentageHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class InputProgressPercentageHistory(CreateModelWithDateOverride):
    timeline_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline,
        on_delete=models.RESTRICT,
        related_name="input_progress_percentage_history",
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=decimal.Decimal(0),
        validators=[MaxValueValidator(100), MinValueValidator(0)],
    )

    objects: ClassVar[InputProgressPercentageHistoryManager] = InputProgressPercentageHistoryManager()

    class Meta:
        db_table = "work_progress_element_input_progress_percentage_history"
        verbose_name = "Input Progress Percentage History"
        verbose_name_plural = "Input Progress Percentage Histories"


class MileStoneHistoryQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class MileStoneHistoryManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> MileStoneHistoryQueryset[M]:  # type: ignore
        return MileStoneHistoryQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class MileStoneHistory(CreateModelWithDateOverride):
    timeline_id: int
    milestone_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="milestone_history"
    )
    # TODO: remove null and blank after running backward compatibility script
    milestone = models.ForeignKey(ItemTypeMileStone, on_delete=models.RESTRICT, related_name="history", null=True)
    name = models.CharField(max_length=50)

    objects: ClassVar[MileStoneHistoryManager] = MileStoneHistoryManager()

    class Meta:
        db_table = "work_progress_element_milestone_history"
        verbose_name = "Item Type Milestone History"
        verbose_name_plural = "Item Type Milestone Histories"


class WorkProgressTimelineAndBoqElementActionHistoryMapping(CreateModelWithDateOverride):
    timeline_id: int
    boq_element_action_history_id: int

    timeline = models.OneToOneField(
        WorkProgressElementTimeline, on_delete=models.RESTRICT, related_name="boq_element_action_history_mappings"
    )
    # NOTE: Nullable field to allow for backward compatibility
    boq_element_action_history = models.OneToOneField(
        BoqElementActionHistory, on_delete=models.RESTRICT, null=True, related_name="timeline_mappings"
    )
    boq_element_history = models.ForeignKey(
        BoqElementHistory, on_delete=models.RESTRICT, related_name="element_history_timeline_mappings", null=True
    )
    updated_fields = ArrayField(models.CharField(max_length=50), default=list, blank=True)

    class Meta:
        db_table = "work_progress_element_timeline_and_boq_element_action_history_mapping"
        verbose_name = "Timeline And Boq Element Action History Mapping"
        verbose_name_plural = "Timeline And Boq Element Action History Mappings"
        # constraints = [
        #     models.UniqueConstraint(
        #         fields=["timeline", "boq_element_action_history"],
        #         name="unique_timeline_boq_element_action_history_mapping",
        #     )
        # ]
