import decimal
from typing import <PERSON>V<PERSON>

from django.conf import settings
from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MinV<PERSON>ueValidator
from django.db import models
from django.db.models import <PERSON>oleanField, Case, Char<PERSON>ield, F, Func, OuterRef, Prefetch, Subquery, Value, When
from django.db.models.functions import <PERSON>, <PERSON>esce, JSONObject
from django.utils.module_loading import import_string

from boq.data.models import BoqElement
from common.constants import STANDARD_DECIMAL_CONFIG
from common.db.manager.base import BaseManager
from common.db.manager.base_queryset import AvailableQuerySet
from common.db.manager.types import M
from common.models import UpdateDeleteModel
from common.utils import get_current_local_time
from element.data.choices import ItemTypeUpdateMethodChoices
from proposal.data.models import ProposalElementMapping
from proposal.domain.constants import ProposalStatus
from rollingbanners.comment_base_service import CommentBaseService
from work_progress_v2.data.models.milestone import ItemTypeMileStone

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class WorkProgressElementQueryset(AvailableQuerySet[M]):  # type: ignore
    def prefetch_all_preview_files(self):
        from boq.data.models import BoqElementPreviewFile

        prefetch = Prefetch(
            "boq_element__preview_files",
            queryset=BoqElementPreviewFile.objects.available().order_by("-is_main", "uploaded_at"),
        )
        return self.prefetch_related(prefetch)

    def annotate_work_progress_comment_count(self, org_id: int):
        return self.annotate(
            comment_count=Coalesce(
                CommentHelperService.get_count(
                    context_group=CommentHelperService.GROUPED_CONTEXT.PROGRESS_REPORT_ITEM.name,
                    org_id=org_id,
                ),
                Value(0),
            )
        )

    def annotate_is_proposal_linked_element(self):
        return self.annotate(
            is_proposal_linked_element=Case(
                When(boq_element__proposal_element__isnull=False, then=True), default=False, output_field=BooleanField()
            )
        )

    def annotate_saved_final_amount_with_tax(self):
        return self.annotate_is_proposal_linked_element().annotate(
            element_final_amount_with_tax=Case(
                When(
                    is_proposal_linked_element=True,
                    then=F("boq_element__proposal_element__saved_final_amount_with_tax"),
                ),
                default=F("boq_element__saved_final_amount_with_tax"),
            ),
        )

    def annotate_current_element_data(self):
        """
        Currently we only need final_amount, client_rate, quantity
        """
        return self.annotate_is_proposal_linked_element().annotate(
            current_element_data=Case(
                When(
                    is_proposal_linked_element=True,
                    then=JSONObject(
                        client_rate=F("boq_element__proposal_element__client_rate"),
                        quantity=F("boq_element__proposal_element__quantity"),
                        # discount_percent=F("proposal_element__discount_percent"),
                        # service_charge_percent=F("proposal_element__service_charge_percent"),
                        # brand_name=F("proposal_element__brand_name"),
                        # tax_percent=F("proposal_element__tax_percent"),
                        # hsn_code=F("proposal_element__hsn_code"),
                        # is_service_charge_with_base_amount=F("proposal_element__is_service_charge_with_base_amount"),
                        # base_amount=F("proposal_element__saved_base_amount"),
                        # gross_amount=F("proposal_element__saved_gross_amount"),
                        # service_charge_amount=F("proposal_element__saved_service_charge"),
                        # amount_without_tax=F("proposal_element__saved_final_amount_without_tax"),
                        final_amount=F("boq_element__proposal_element__saved_final_amount_with_tax"),
                    ),
                ),
                default=JSONObject(
                    client_rate=F("boq_element__client_rate"),
                    quantity=F("boq_element__quantity"),
                    # discount_percent=F("discount_percent"),
                    # service_charge_percent=F("service_charge_percent"),
                    # brand_name=F("brand_name"),
                    # tax_percent=F("tax_percent"),
                    # hsn_code=F("hsn_code"),
                    # is_service_charge_with_base_amount=F("is_service_charge_with_base_amount"),
                    # base_amount=F("saved_base_amount"),
                    # gross_amount=F("saved_gross_amount"),
                    # service_charge_amount=F("saved_service_charge"),
                    # amount_without_tax=F("saved_final_amount_without_tax"),
                    final_amount=F("boq_element__saved_final_amount_with_tax"),
                ),
            ),
        )

    def annotate_preview_day_progress(self):
        from work_progress_v2.data.models.timeline import (
            InputProgressQuantityHistory,
            MileStoneHistory,
            PercentageHistory,
        )

        current_local_date = get_current_local_time().date()

        previous_day_percentage_history = PercentageHistory.objects.filter(
            timeline__element_id=OuterRef("pk"),
            created_at__date__lt=current_local_date,
        ).order_by("-created_at")

        previous_day_percentage_subquery = Subquery(previous_day_percentage_history.values("percentage")[:1])

        previous_day_progress_updated_at_subquery = Subquery(previous_day_percentage_history.values("created_at")[:1])

        previous_day_input_quantity_subquery = Subquery(
            InputProgressQuantityHistory.objects.filter(
                timeline__element_id=OuterRef("pk"),
                created_at__date__lt=current_local_date,
                created_at__gt=OuterRef("unlocked_at"),
            )
            .order_by("-created_at")
            .values("quantity")[:1]
        )

        previous_day_milestone_subquery = Subquery(
            MileStoneHistory.objects.filter(
                timeline__element_id=OuterRef("pk"),
                created_at__date__lt=current_local_date,
                created_at__gt=OuterRef("unlocked_at"),
            )
            .order_by("-created_at")
            .values("milestone_id")[:1]
        )

        return self.annotate(
            previous_day_progress_percentage=previous_day_percentage_subquery,
            previous_day_progress_percentage_input=previous_day_percentage_subquery,
            previous_day_progress_quantity_input=previous_day_input_quantity_subquery,
            previous_day_progress_milestone_id_input=previous_day_milestone_subquery,
            last_day_progress_updated_at=previous_day_progress_updated_at_subquery,
        )

    def prefetch_today_uploaded_attachments(self):
        from progressreport.models import ProgressReportElementAttachment

        current_local_date = get_current_local_time().date()

        prefetch = Prefetch(
            "boq_element__pr_attachments",
            queryset=ProgressReportElementAttachment.objects.filter(
                uploaded_at__date=current_local_date,
                deleted_at__isnull=True,
            ).order_by("-uploaded_at"),
        )

        return self.prefetch_related(prefetch)

    def annotate_order_number(self):
        return self.annotate(
            order_number=Func(
                F("boq_element__boq__project__job_id"),
                Value("/"),
                Cast(F("boq_element__order_element__vendor_order__order_number"), CharField()),
                function="CONCAT",
            )
        )

    def annotate_proposal_hidden_fields(self):
        # This subquery will assign meta_data.hidden_fields of
        # last approved proposal linked to a particular boq element
        hidden_fields_subquery = (
            ProposalElementMapping.objects.filter(element_id=OuterRef("boq_element_id"))
            .select_related("proposal", "proposal__meta_data")
            .filter(proposal__status=ProposalStatus.APPROVED.value)
            .order_by("-id")
        ).values("proposal__meta_data__hidden_fields")[:1]

        return self.annotate(hidden_fields=Coalesce(Subquery(hidden_fields_subquery), Value([])))


class WorkProgressElementManager(BaseManager[M]):
    def get_queryset(self) -> WorkProgressElementQueryset[M]:
        return WorkProgressElementQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class WorkProgressElement(UpdateDeleteModel):
    boq_element_id: int
    milestone_input_id: int | None
    current_element_data: dict

    boq_element: "models.OneToOneField[BoqElement]"

    boq_element = models.OneToOneField(
        BoqElement,
        on_delete=models.RESTRICT,
        related_name="work_progress_element",
        primary_key=True,
    )
    progress_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=decimal.Decimal(0),
        validators=[MaxValueValidator(100), MinValueValidator(0)],
    )
    progress_quantity_input = models.DecimalField(
        max_digits=STANDARD_DECIMAL_CONFIG.get("max_digits"),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
        default=None,
        null=True,
        blank=True,
    )
    progress_percentage_input = models.PositiveSmallIntegerField(
        default=None,
        validators=[MaxValueValidator(100), MinValueValidator(0)],
        null=True,
        blank=True,
    )
    milestone_input = models.ForeignKey(
        ItemTypeMileStone,
        on_delete=models.RESTRICT,
        default=None,
        null=True,
        blank=True,
    )
    update_method = models.CharField(
        max_length=50, choices=ItemTypeUpdateMethodChoices.choices, default=None, null=True, blank=True
    )

    unlocked_at = models.DateTimeField(default=None, null=True, blank=True)

    # This field is used to track the last time the progress was updated by resetting, unlocking, or updating the progress  # noqa
    # Use of this field is to show the last time the progress was updated
    progress_updated_at = models.DateTimeField(default=None, null=True, blank=True)

    # This field is used to track the last time the progress was updated via quantity, percentage or milestone
    # Use of this field is for locking the progress
    input_progress_updated_at = models.DateTimeField(default=None, null=True, blank=True)

    # This field is used to track the last time any attachment was uploaded
    attachment_uploaded_at = models.DateTimeField(default=None, null=True, blank=True)

    objects: ClassVar[WorkProgressElementManager] = WorkProgressElementManager()

    class Meta:
        db_table = "work_progress_element"
        verbose_name = "Work Progress Element"
        verbose_name_plural = "Work Progress Elements"
