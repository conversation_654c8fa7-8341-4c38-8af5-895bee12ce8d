from django.db import models

from common.models import CreateModel, CreateUpdateModel
from core.models import Organization
from project.data.models import Project
from work_progress_v2.data.choices import WorkProgressConfigReportTypeChoices


class ReportConfig(CreateUpdateModel):
    organization_id: int

    organization = models.ForeignKey(
        Organization,
        on_delete=models.RESTRICT,
        related_name="work_progress_report_configs",
    )
    config = models.JSONField(default=list)
    config_type = models.CharField(choices=WorkProgressConfigReportTypeChoices.choices, max_length=50)

    class Meta:
        db_table = "work_progress_report_configs"
        verbose_name = "Report Config"
        verbose_name_plural = "Report Configs"


class ProjectReportConfig(CreateUpdateModel):
    organization_id: int
    project_id: int

    organization = models.ForeignKey(
        Organization,
        on_delete=models.RESTRICT,
        related_name="work_progress_project_report_configs",
    )
    project = models.ForeignKey(
        Project,
        on_delete=models.RESTRICT,
        related_name="work_progress_project_report_configs",
    )
    config = models.JSONField(default=list)
    config_type = models.Char<PERSON>ield(choices=WorkProgressConfigReportTypeChoices.choices, max_length=50)

    class Meta:
        db_table = "work_progress_project_report_configs"
        verbose_name = "Project Report Config"
        verbose_name_plural = "Project Report Configs"


class ReportConfigHistory(CreateModel):
    organization_id: int

    organization = models.ForeignKey(
        Organization,
        on_delete=models.RESTRICT,
        related_name="work_progress_report_config_histories",
    )
    config = models.JSONField(default=list)
    config_type = models.CharField(choices=WorkProgressConfigReportTypeChoices.choices, max_length=50)

    class Meta:
        db_table = "work_progress_report_config_histories"
        verbose_name = "Report Config History"
        verbose_name_plural = "Report Config Histories"


class ProjectReportConfigHistory(CreateModel):
    organization_id: int
    project_id: int

    organization = models.ForeignKey(
        Organization,
        on_delete=models.RESTRICT,
        related_name="work_progress_project_report_config_histories",
    )
    project = models.ForeignKey(
        Project,
        on_delete=models.RESTRICT,
        related_name="work_progress_project_report_config_histories",
    )
    config = models.JSONField(default=list)
    config_type = models.CharField(choices=WorkProgressConfigReportTypeChoices.choices, max_length=50)

    class Meta:
        db_table = "work_progress_project_report_config_histories"
        verbose_name = "Project Report Config History"
        verbose_name_plural = "Project Report Config Histories"
