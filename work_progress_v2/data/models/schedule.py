import decimal

from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models

from common.constants import (
    FILE_FIELD_MAX_LENGTH,
    STANDARD_DECIMAL_CONFIG,
)
from common.helpers import get_upload_path
from common.models import CreateModel, UploadModel
from progressreport.models import ProgressReport
from project_schedule.data.models import ProjectSchedule, ProjectScheduleActivity
from work_progress_v2.data.choices import WorkProgressScheduleActivityStatusChoices


class WorkProgressSchedule(CreateModel):
    """
    delay_days: This field is used to store delayed or overdue days
    """

    progress_report_id: int
    schedule_id: int

    progress_report = models.OneToOneField(
        ProgressReport,
        on_delete=models.RESTRICT,
        related_name="schedule",
    )
    schedule = models.ForeignKey(
        ProjectSchedule,
        on_delete=models.RESTRICT,
        related_name="work_progress_schedules",
    )
    today_progress = models.DecimalField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
        max_digits=7,
        null=True,
        blank=True,
    )
    total_progress = models.DecimalField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=decimal.Decimal(0),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
        max_digits=7,
        null=True,
        blank=True,
    )
    status_type = models.CharField(
        choices=WorkProgressScheduleActivityStatusChoices.choices,
        max_length=50,
        blank=True,
        null=True,
    )
    delay_days = models.IntegerField(blank=True, null=True)
    planned_end_date = models.DateField(blank=True, null=True)

    class Meta:
        db_table = "work_progress_schedules"
        verbose_name = "Work Progress Schedule"
        verbose_name_plural = "Work Progress Schedules"


class WorkProgressScheduleActivity(CreateModel):
    """
    delay_days: This field is used to store delayed or overdue days
    """

    activity_id: int
    wp_schedule_id: int

    wp_schedule = models.ForeignKey(
        WorkProgressSchedule,
        on_delete=models.RESTRICT,
        related_name="activities",
    )
    activity = models.ForeignKey(
        ProjectScheduleActivity,
        on_delete=models.RESTRICT,
        related_name="work_progress_schedule_activities",
    )
    name = models.CharField(max_length=500)
    wbs = models.CharField(max_length=50)
    status = models.CharField(
        choices=WorkProgressScheduleActivityStatusChoices.choices,
        max_length=50,
        blank=True,
        null=True,
    )
    delay_days = models.IntegerField(blank=True, null=True)

    today_progress = models.DecimalField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=decimal.Decimal(0),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
        max_digits=7,
    )
    total_progress = models.DecimalField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=decimal.Decimal(0),
        decimal_places=STANDARD_DECIMAL_CONFIG.get("decimal_places"),
        max_digits=7,
    )

    class Meta:
        db_table = "work_progress_schedule_activities"
        verbose_name = "Work Progress Schedule Activity"
        verbose_name_plural = "Work Progress Schedule Activities"


class WorkProgressScheduleActivityAttachment(UploadModel):
    wp_schedule_id: int
    wp_schedule_activity_id: int | None

    wp_schedule = models.ForeignKey(
        WorkProgressSchedule,
        on_delete=models.RESTRICT,
        related_name="attachments",
    )

    wp_schedule_activity = models.ForeignKey(
        WorkProgressScheduleActivity,
        on_delete=models.RESTRICT,
        related_name="attachments",
        blank=True,
        null=True,
    )
    file = models.FileField(upload_to=get_upload_path, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH)
    thumbnail = models.FileField(
        upload_to=get_upload_path,
        null=True,
        blank=True,
        max_length=FILE_FIELD_MAX_LENGTH,
    )
    name = models.CharField(max_length=500)

    class Meta:
        db_table = "work_progress_schedule_activity_attachments"
        verbose_name = "Work Progress Schedule Activity Attachment"
        verbose_name_plural = "Work Progress Schedule Activity Attachments"
