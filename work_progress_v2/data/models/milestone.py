from typing import ClassVar

from django.db import models

from common.db.manager.base import BaseManager
from common.db.manager.base_queryset import AvailableQuerySet
from common.db.manager.types import M
from common.models import CreateUpdateDeleteModel
from element.data.models import ElementItemType


class ItemTypeMileStoneConfigQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class ItemTypeMileStoneConfigManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> ItemTypeMileStoneConfigQueryset[M]:  # type: ignore
        return ItemTypeMileStoneConfigQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class ItemTypeMileStoneConfig(CreateUpdateDeleteModel):
    item_type_id: int

    item_type = models.OneToOneField(ElementItemType, on_delete=models.RESTRICT, related_name="milestone_config")

    objects: ClassVar[ItemTypeMileStoneConfigManager] = ItemTypeMileStoneConfigManager()

    class Meta:
        db_table = "item_type_milestone_config"
        verbose_name = "Item Type Milestone Configuration"
        verbose_name_plural = "Item Type Milestone Configurations"


class ItemTypeMileStoneQueryset(AvailableQuerySet[M]):  # type: ignore
    pass


class ItemTypeMileStoneManager(BaseManager[M]):  # type: ignore
    def get_queryset(self) -> ItemTypeMileStoneQueryset[M]:  # type: ignore
        return ItemTypeMileStoneQueryset(self.model, using=self._db)

    def available(self):
        return self.get_queryset().available()


class ItemTypeMileStone(CreateUpdateDeleteModel):
    milestone_config_id: int | None

    name = models.CharField(max_length=50)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)
    milestone_config = models.ForeignKey(
        ItemTypeMileStoneConfig,
        on_delete=models.RESTRICT,
        related_name="milestones",
        null=True,
        blank=True,
        default=None,
    )
    is_visible = models.BooleanField(default=True)

    objects: ClassVar[ItemTypeMileStoneManager] = ItemTypeMileStoneManager()

    class Meta:
        db_table = "item_type_milestone"
        ordering = ["percentage"]
        unique_together = ["milestone_config", "percentage"]
        verbose_name = "Item Type Milestone"
        verbose_name_plural = "Item Type Milestones"
