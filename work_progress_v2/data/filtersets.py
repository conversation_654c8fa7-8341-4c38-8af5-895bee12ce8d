from django.db.models import Q, QuerySet
from django.utils import timezone
from django_filters import BooleanFilter, Filter, FilterSet

from boq.data.models import BoqSection
from client.domain.constants import ClientFields
from common.utils import get_local_time
from work_progress_v2.domain.enums import WorkProgressElementListOrderingEnum, WorkProgressElementListProgressStatusEnum


class ElementFilterSet(FilterSet):
    search_text = Filter(method="filter_search_text")
    search_quantity = Filter(method="filter_search_quantity")
    category_ids = Filter(field_name="boq_element__category_id", lookup_expr="in")
    section_ids = Filter(method="filter_section_ids")
    item_type_ids = Filter(field_name="boq_element__item_type_id", lookup_expr="in")
    uom_ids = Filter(field_name="boq_element__uom", lookup_expr="in")
    element_status = Filter(field_name="boq_element__element_status", lookup_expr="in")
    progress_status = Filter(method="filter_progress_status")
    ordering = Filter(method="filter_ordering")
    is_updated_recently = BooleanFilter(method="filter_is_updated_recently")
    is_updated_earliest = BooleanFilter(method="filter_is_updated_earliest")
    is_not_updated_today = BooleanFilter(method="filter_is_not_updated_today")
    is_updated_in_last_7_days = BooleanFilter(method="filter_is_updated_in_last_7_days")
    is_not_updated_in_last_7_days = BooleanFilter(method="filter_is_not_updated_in_last_7_days")
    is_not_updated_yet = BooleanFilter(method="filter_is_not_updated_yet")
    is_updated_today = BooleanFilter(method="filter_is_updated_today")

    def filter_search_text(self, queryset: QuerySet, name: str, value: str | None) -> QuerySet:
        """Filter elements by search text."""
        if value:
            return queryset.filter(boq_element__name__icontains=value)
        return queryset

    def filter_search_quantity(self, queryset: QuerySet, name: str, value: str | None) -> QuerySet:
        """Filter elements by search quantity."""
        if value:
            return queryset.filter(boq_element__quantity__icontains=value)
        return queryset

    def filter_section_ids(self, queryset: QuerySet, name: str, value: list[int] | None) -> QuerySet:
        """Filter elements by section ids."""
        if value:
            if BoqSection.DEFAULT_SECTION_ID in value:
                return queryset.filter(Q(boq_element__section_id__isnull=True) | Q(boq_element__section_id__in=value))

            return queryset.filter(boq_element__section_id__in=value)
        return queryset

    def filter_progress_status(
        self, queryset: QuerySet, name: str, value: list[WorkProgressElementListProgressStatusEnum] | None
    ) -> QuerySet:
        """Filter elements by progress status."""
        if value:
            not_started = WorkProgressElementListProgressStatusEnum.NOT_STARTED.value in value
            in_progress = WorkProgressElementListProgressStatusEnum.IN_PROGRESS.value in value
            completed = WorkProgressElementListProgressStatusEnum.COMPLETED.value in value

            not_started_filter = Q(progress_percentage__isnull=True) | Q(progress_percentage=0)

            if not_started and in_progress and completed:
                return queryset

            if not_started and in_progress:
                return queryset.filter(not_started_filter | Q(progress_percentage__lt=100))

            if not_started and completed:
                return queryset.filter(not_started_filter | Q(progress_percentage=100))

            if in_progress and completed:
                return queryset.filter(Q(progress_percentage__gt=0))

            if not_started:
                return queryset.filter(not_started_filter)

            if in_progress:
                return queryset.filter(progress_percentage__gt=0, progress_percentage__lt=100)

            if completed:
                return queryset.filter(progress_percentage=100)

            return queryset
        return queryset

    def filter_ordering(
        self, queryset: QuerySet, name: str, value: list[WorkProgressElementListOrderingEnum] | None
    ) -> QuerySet:
        """Order elements."""
        if value:
            progress_asc = WorkProgressElementListOrderingEnum.PROGRESS_ASC.value in value
            progress_desc = WorkProgressElementListOrderingEnum.PROGRESS_DESC.value in value
            quantity_desc = WorkProgressElementListOrderingEnum.QUANTITY_DESC.value in value
            quantity_asc = WorkProgressElementListOrderingEnum.QUANTITY_ASC.value in value
            rate_desc = WorkProgressElementListOrderingEnum.RATE_DESC.value in value
            rate_asc = WorkProgressElementListOrderingEnum.RATE_ASC.value in value
            amount_desc = WorkProgressElementListOrderingEnum.AMOUNT_DESC.value in value
            amount_asc = WorkProgressElementListOrderingEnum.AMOUNT_ASC.value in value
            created_at_asc = WorkProgressElementListOrderingEnum.CREATED_AT_ASC.value in value
            created_at_desc = WorkProgressElementListOrderingEnum.CREATED_AT_DESC.value in value

            if progress_asc:
                return queryset.order_by("progress_percentage")
            if progress_desc:
                return queryset.order_by("-progress_percentage")
            if quantity_asc:
                return queryset.order_by("quantity")
            if quantity_desc:
                return queryset.order_by("-quantity")
            if rate_asc:
                return queryset.order_by("order_rate")
            if rate_desc:
                return queryset.order_by("-order_rate")
            if amount_asc:
                return queryset.order_by("amount")
            if amount_desc:
                return queryset.order_by("-amount")
            if created_at_asc:
                return queryset.order_by("created_at")
            if created_at_desc:
                return queryset.order_by("-created_at")

            return queryset
        return queryset

    def filter_is_updated_recently(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements updated in last 3 days."""
        if value:
            return queryset.order_by("-progress_updated_at")
        return queryset

    def filter_is_updated_earliest(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """
        Filter elements updated earliest.
        """
        if value:
            return queryset.order_by("progress_updated_at")
        return queryset

    def filter_is_not_updated_today(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements not updated today."""
        if value:
            today_date = get_local_time(timezone.now()).date()
            return queryset.exclude(progress_updated_at__date=today_date)
        return queryset

    def filter_is_updated_in_last_7_days(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements updated in last 7 days."""
        if value:
            today_date = get_local_time(timezone.now()).date()
            return queryset.filter(progress_updated_at__date__gte=today_date - timezone.timedelta(days=7))
        return queryset

    def filter_is_not_updated_in_last_7_days(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements not updated in last 7 days."""
        if value:
            today_date = get_local_time(timezone.now()).date()
            return queryset.exclude(progress_updated_at__date__gte=today_date - timezone.timedelta(days=7))
        return queryset

    def filter_is_not_updated_yet(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements not updated yet."""
        if value:
            return queryset.filter(progress_updated_at__isnull=True)
        return queryset

    def filter_is_updated_today(self, queryset: QuerySet, name: str, value: bool | None) -> QuerySet:
        """Filter elements updated today."""
        if value:
            today_date = get_local_time(timezone.now()).date()
            return queryset.filter(progress_updated_at__date=today_date)
        return queryset


class ReportFilterSet(FilterSet):
    created_by_ids = Filter(field_name="created_by_id", lookup_expr="in")
    start_date = Filter(method="filter_start_date")
    end_date = Filter(method="filter_end_date")

    def filter_start_date(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        """Filter reports by start date."""
        if value:
            return queryset.filter(created_at__date__gte=value)
        return queryset

    def filter_end_date(self, queryset: QuerySet, name: str, value: str) -> QuerySet:
        """Filter reports by end date."""
        if value:
            return queryset.filter(created_at__date__lte=value)
        return queryset


class VendorReportFilterSet(ReportFilterSet):
    vendor_org_ids = Filter(field_name="organization_id", lookup_expr="in")


class VendorScopeElementFilterSet(ElementFilterSet):
    uom_ids = Filter(method="filter_uom_ids")
    amount = Filter(method="filter_amount")
    order_rate = Filter(method="filter_order_rate")

    def filter_uom_ids(self, queryset: QuerySet, name: str, value: list[int] | None) -> QuerySet:
        """Filter elements by uom ids."""
        if value:
            return queryset.filter(boq_element__uom__in=value).filter(~Q(hidden_fields__contains=[ClientFields.UOM]))
        return queryset

    def filter_search_quantity(self, queryset: QuerySet, name: str, value: str | None) -> QuerySet:
        """Filter elements by search quantity."""
        if value:
            return queryset.filter(boq_element__quantity__icontains=value).filter(
                ~Q(hidden_fields__contains=[ClientFields.QUANTITY])
            )
        return queryset

    def filter_amount(self, queryset: QuerySet, name: str, value: str | None) -> QuerySet:
        """Filter elements by amount."""
        if value:
            return queryset.filter(amount__icontains=value).filter(
                ~Q(hidden_fields__contains=[ClientFields.FINAL_AMOUNT])
            )
        return queryset

    def filter_order_rate(self, queryset: QuerySet, name: str, value: str | None) -> QuerySet:
        """Filter elements by order rate."""
        if value:
            return queryset.filter(order_rate__icontains=value).filter(~Q(hidden_fields__contains=[ClientFields.RATE]))
        return queryset
