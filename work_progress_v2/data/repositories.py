import datetime
import decimal
import math
from io import BytesIO

import structlog
from django.core.files.base import ContentFile
from django.db.models import <PERSON>, CharField, DecimalField, F, Max, OuterRef, Prefetch, Q, QuerySet, Subquery, When
from django.db.models.functions import <PERSON><PERSON><PERSON>Object
from django.utils import timezone

from boq.data.choices import BoqElementStatus
from boq.data.models import BoqElementActionHistory, BoqElementHistory, BoqSection, StatusChoices
from common.constants import TWO_DECIMAL_FACTOR
from common.entities import (
    ProjectDataEntity,
    ProjectOrganizationEntity,
)
from common.exceptions import BaseValidationError
from common.pydantic.paginator import get_filter_pydantic_queryset_data
from common.services import PublicMediaFileStorage, model_update
from common.timeline.enum import TimelineStatusEnum
from common.timeline.helper import TimelineStatusHelper
from common.utils import format_trailing_zeros, get_local_time
from core.caches import OrganizationUOMCache
from core.exceptions import ResourceDoesNotExistException
from core.models import Organization
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementCategory, ElementItemType
from element.data.selectors import element_category_list
from progressreport.constants import Constants as PROGRESS_REPORT_CONSTANTS
from progressreport.models import (
    ManpowerCategory,
    ProgressReport,
    ProgressReportAttachment,
    ProgressReportElementAttachment,
    ProgressReportItem,
    ProgressReportManPower,
    ProgressReportSection,
    ProgressReportSiteViewPointMapping,
    ProgressReportSiteViewPointMappingAttachment,
    ProjectManpowerCategory,
    ProjectOrgSiteViewPoint,
    ProjectProgressReport,
)
from project.data.models import Project
from project.data.selectors import get_project_organization_create_data
from project.domain.caches import ProjectDataCache
from project.domain.entities import ProjectOrgCreatedData
from report.download.domain.enums import DownloadProgressStatusEnum
from work_progress_v2.data.choices import (
    ElementUpdatedAttributeChoices,
    WorkProgressElementActionChoices,
    WorkProgressElementAttachmentActionChoices,
    WorkProgressInventoryStockItemTypeChoices,
)
from work_progress_v2.data.entities import (
    ElementAttachmentCreateDataEntity,
    ElementAttachmentDataEntity,
    ElementCategoryDataEntity,
    ElementDetailDataEntity,
    ElementItemTypeDataEntity,
    ElementListDataEntity,
    ElementPreviewFileDataEntity,
    ElementStatusDataEntity,
    ElementUomDataEntity,
    HistoryTimelineDataEntity,
    PaginatedElementListDataEntity,
    ScopeElementBaseEntity,
    ScopeTimelineDatesDataEntity,
    UpdatedDataEntity,
    UserDataEntity,
    WPElementBaseEntity,
)
from work_progress_v2.data.filtersets import (
    ElementFilterSet,
    ReportFilterSet,
    VendorReportFilterSet,
    VendorScopeElementFilterSet,
)
from work_progress_v2.data.models.config import (
    ProjectReportConfig,
    ProjectReportConfigHistory,
    ReportConfig,
    ReportConfigHistory,
)
from work_progress_v2.data.models.daywise_data import (
    WorkProgressElementDayWiseData,
    WorkProgressProjectDayWiseData,
    WorkProgressSectionDayWiseData,
)
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.inventory import WorkProgressInventoryStockItem
from work_progress_v2.data.models.milestone import ItemTypeMileStone
from work_progress_v2.data.models.schedule import (
    WorkProgressSchedule,
    WorkProgressScheduleActivity,
    WorkProgressScheduleActivityAttachment,
)
from work_progress_v2.data.models.timeline import (
    UpdateMethodHistory,
    WorkProgressElementTimeline,
    WorkProgressElementTimelineQueryset,
)
from work_progress_v2.data.repo_mixins.history_timeline import ElementHistoryTimelineRepoMixin
from work_progress_v2.data.repo_mixins.prepare import PrepareRepo, ReportPrepareRepo
from work_progress_v2.data.report_entities import (
    CreateProjectManpowerCategoryDataEntity,
    CreateSiteViewPointDataEntity,
    CreateSiteViewPointMappingDataEntity,
    ElementDayWiseDataEntity,
    LastReportForDailyLogDataEntity,
    LastReportForDailyLogUpdateAttachmentDataEntity,
    LastReportForDailyLogUpdateDataEntity,
    LastReportReportedProjectProgressDataEntity,
    PrefillScopeUpdateDataEntity,
    PrefillScopeUpdateElementAttachmentDataEntity,
    ProjectOrgDaywiseDataEntity,
    ReportListPaginatedEntity,
    ReportPDFBlockDataEntity,
    ReportPDFDailyLogDataEntity,
    ReportPDFDailyLogUpdateDataEntity,
    ReportPDFDataWithProjectDataEntity,
    ReportPDFManpowerCategoryDataEntity,
    ReportPDFManpowerDataEntity,
    ReportPDFMaterialDataEntity,
    ReportPDFReportedProjectProgressDataEntity,
    ReportPDFScheduleActivityDataEntity,
    ReportPDFScheduleDataEntity,
    ReportPDFScopeUpdateDataEntity,
    ReportPDFScopeUpdateSectionDataEntity,
    ReportPDFSectionDataEntity,
    ReportPDFSiteViewPointDataEntity,
    ReportPDFStatusDataEntity,
    ReportPDFStockDataEntity,
    ReportProjectDataEntity,
    SectionDayWiseDataEntity,
    SiteViewPointAttachmentDataEntity,
    SiteViewPointDataEntity,
    VendorReportCreatedByDataEntity,
)
from work_progress_v2.domain.abstract_repos import (
    WorkProgressAbstractRepo,
    WorkProgressDayWiseAbstractRepo,
    WorkProgressOrgAbstractRepo,
    WorkProgressReportAbstractRepo,
    WorkProgressReportPDFAbstractRepo,
    WorkProgressVendorScopeElementAbstractRepo,
)
from work_progress_v2.domain.entities import (
    ElementListFilterEntity,
    GetReportConfigEntity,
    PaginatedVendorScopeElementListEntity,
    ReportConfigEntity,
    ReportListFilterDataEntity,
    ScopeProgressEntity,
    SectionNameTotalProgressEntity,
    SectionScopeDataEntity,
    UpdateElementDataEntity,
    UpdateElementOutputDataEntity,
    UpdateElementOutputEntity,
    UpdateMethodElementEntity,
    VendorReportCreatedByListFilterDataEntity,
    VendorReportListFilterDataEntity,
    VendorScopeElementListFilterEntity,
)
from work_progress_v2.domain.enums import (
    WorkProgressConfigReportTypeEnum,
    WorkProgressCreationTypeEnum,
    WorkProgressElementAttachmentPermissionActionEnum,
)
from work_progress_v2.domain.report_entities import (
    CreateReportAttachmentEntity,
    CreateReportItemEntity,
    CreateReportManPowerDataEntity,
    CreateReportScheduleUpdateAttachmentInputEntity,
    CreateReportSectionEntity,
    CreateScheduleActivityEntity,
    CreateSiteViewPointAttachmentEntity,
    HashtagElementEntity,
    MaterialItemEntity,
    PrefillScheduleUpdateActivityEntity,
    PrefillScheduleUpdateEntity,
    ReportDetailAttachmentEntity,
    ReportDetailCreatedByEntity,
    ReportGeneratedPdfDataEntity,
    ReportGeneratePDFHeaderEntity,
    ReportPDFCreatorEntity,
    ReportPdfEntity,
    ReportPDFFooterEntity,
    ReportPDFProjectEntity,
    ReportSectionEntity,
    WorkProgressBlockBaseModel,
)
from work_progress_v2.domain.utils import WorkProgressReportItemBlockHelper

logger = structlog.get_logger(__name__)


class WorkProgressElementAttachmentRepo:
    user_id: int
    current_time: datetime.datetime

    def can_delete_attachment(self, uploaded_at: datetime.datetime) -> bool:
        return self.current_time.date() == uploaded_at.date()

    def get_element_all_attachments(self, element_id: int) -> QuerySet[ProgressReportElementAttachment]:
        return ProgressReportElementAttachment.objects.filter(element_id=element_id, deleted_at=None).order_by(
            "-uploaded_at"
        )

    def get_element_all_attachments_entity(self, element_id: int) -> list[ElementAttachmentDataEntity]:
        attachments = self.get_element_all_attachments(element_id=element_id)

        entities: list[ElementAttachmentDataEntity] = []

        for attachment in attachments:
            entities.append(
                ElementAttachmentDataEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    file=attachment.file.url,
                    uploaded_at=attachment.uploaded_at,
                    actions=(
                        [WorkProgressElementAttachmentPermissionActionEnum.CAN_DELETE]
                        if self.can_delete_attachment(attachment.uploaded_at)
                        else []
                    ),
                    thumbnail_file=attachment.thumbnail.url if attachment.thumbnail else None,
                )
            )

        return entities

    def get_element_attachments_entity(
        self, element_id: int, attachment_ids: list[int]
    ) -> list[ElementAttachmentDataEntity]:
        attachments = self.get_element_all_attachments(element_id=element_id).filter(id__in=attachment_ids)

        entities: list[ElementAttachmentDataEntity] = []

        for attachment in attachments:
            entities.append(
                ElementAttachmentDataEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    file=attachment.file.url,
                    uploaded_at=attachment.uploaded_at,
                    actions=(
                        [WorkProgressElementAttachmentPermissionActionEnum.CAN_DELETE]
                        if self.can_delete_attachment(attachment.uploaded_at)
                        else []
                    ),
                    thumbnail_file=attachment.thumbnail.url if attachment.thumbnail else None,
                )
            )

        return entities

    def create_attachments(
        self, element_id: int, attachments: list[ElementAttachmentCreateDataEntity]
    ) -> list[ElementAttachmentDataEntity]:
        data = [
            ProgressReportElementAttachment(
                element_id=element_id,
                name=attachment.name,
                file=attachment.file,
                uploaded_by_id=self.user_id,
                uploaded_at=self.current_time,
                thumbnail=attachment.thumbnail_file,
            )
            for attachment in attachments
        ]

        created_attachments = ProgressReportElementAttachment.objects.bulk_create(data)

        entities: list[ElementAttachmentDataEntity] = []
        timeline_objects: list[WorkProgressElementTimeline] = []

        for attachment in created_attachments:
            entities.append(
                ElementAttachmentDataEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    file=attachment.file.url,
                    thumbnail_file=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                    actions=[],
                )
            )
            timeline_objects.append(
                self._create_timeline_object(
                    element_id=element_id, action=WorkProgressElementActionChoices.ATTACHMENT_UPDATED
                )
            )

        # update element attachment uploaded at
        self.update_element(
            element_id=element_id,
            updated_data=UpdateElementDataEntity(
                id=element_id,
                action=WorkProgressElementActionChoices.ATTACHMENT_UPDATED,
            ),
        )

        # create attachment added history timeline
        logger.info("Creating attachment added history timeline")
        created_timelines = WorkProgressElementTimeline.objects.bulk_create(timeline_objects)

        attachment_id_to_timeline_id_map: dict[int, int] = {
            attachment.pk: timeline.pk for attachment, timeline in zip(created_attachments, created_timelines)
        }
        self.create_attachment_added_deleted_history(
            attachment_id_to_timeline_id_map=attachment_id_to_timeline_id_map,
            attachment_action=WorkProgressElementAttachmentActionChoices.UPLOADED,
        )
        logger.info("Attachment added history timeline created.")

        return entities

    def delete_attachments(self, element_id: int, attachment_ids: list[int]) -> None:
        attachments = ProgressReportElementAttachment.objects.filter(element_id=element_id, id__in=attachment_ids)

        timeline_objects: list[WorkProgressElementTimeline] = []
        for attachment in attachments:
            attachment.deleted_at = self.current_time
            attachment.deleted_by_id = self.user_id
            timeline_objects.append(
                self._create_timeline_object(  # type: ignore
                    element_id=element_id, action=WorkProgressElementActionChoices.ATTACHMENT_UPDATED
                )
            )

        ProgressReportElementAttachment.objects.bulk_update(attachments, ["deleted_at", "deleted_by_id"])

        # create attachment deleted history timeline
        logger.info("Creating attachment deleted history timeline")
        created_timelines = WorkProgressElementTimeline.objects.bulk_create(timeline_objects)

        attachment_id_to_timeline_id_map: dict[int, int] = {
            attachment.pk: timeline.pk for attachment, timeline in zip(attachments, created_timelines)
        }
        self.create_attachment_added_deleted_history(  # type: ignore
            attachment_id_to_timeline_id_map=attachment_id_to_timeline_id_map,
            attachment_action=WorkProgressElementAttachmentActionChoices.DELETED,
        )
        logger.info("Attachment deleted history timeline created.")


class WorkProgressRepo(
    PrepareRepo,
    WorkProgressElementAttachmentRepo,
    ElementHistoryTimelineRepoMixin,
    WorkProgressAbstractRepo,
):
    element_model: WorkProgressElement | None

    class WorkProgressRepoException(BaseValidationError):
        pass

    def __init__(self, user_id: int, project_id: int, org_id: int):
        self.user_id = user_id
        self.project_id = project_id
        self.org_id = org_id
        self.element_model = None
        self.current_time = get_local_time(timezone.now())
        self.status_dict = dict(BoqElementStatus.choices)
        self.uom_cache: dict[str, str] = OrganizationUOMCache.get(instance_id=self.org_id)

    def get_element_fast_base_selector(self):
        return WorkProgressElement.objects.get_queryset().filter(
            boq_element__organization_id=self.org_id,
            boq_element__boq_id=self.project_id,
            boq_element__deleted_at=None,
            boq_element__element_status__in=[
                BoqElementStatus.DRAFT.value,
                BoqElementStatus.APPROVED.value,
                BoqElementStatus.CHANGE_REQUESTED.value,
                BoqElementStatus.REQUESTED.value,
                BoqElementStatus.CANCELLATION_REQUESTED.value,
            ],
        )

    def get_elements_base_selector(self):
        return (
            self.get_element_fast_base_selector()
            .select_related("boq_element", "boq_element__proposal_element")
            .annotate_current_element_data()
        )

    def get_wp_element(self, element_id: int) -> WorkProgressElement:
        if self.element_model:
            return self.element_model

        try:
            result: WorkProgressElement = (
                self.get_elements_base_selector()
                .select_related(
                    "boq_element__item_type",
                    "boq_element__section",
                    "boq_element__category",
                    "boq_element__created_by",
                )
                .annotate_preview_day_progress()
                .get(boq_element_id=element_id)
            )

            self.element_model = result
            return self.element_model
        except WorkProgressElement.DoesNotExist:
            raise ResourceDoesNotExistException()

    def get_wp_element_entity(self, element_id: int) -> WPElementBaseEntity:
        element = self.get_wp_element(element_id=element_id)

        return WPElementBaseEntity(
            id=element.pk,
            section_id=element.boq_element.section_id,
            quantity=element.current_element_data.get("quantity", 0),
            amount=element.current_element_data.get("final_amount", 0),
            progress_percentage=element.progress_percentage,
            input_progress_percentage=element.progress_percentage_input,
            input_progress_quantity=element.progress_quantity_input,
            previous_day_progress_percentage=(
                element.previous_day_progress_percentage
                if element.previous_day_progress_percentage
                else element.progress_percentage
            ),
            previous_day_input_progress_percentage=(
                element.previous_day_progress_percentage_input
                if element.previous_day_progress_percentage_input
                else None
            ),
            previous_day_input_progress_quantity=(
                element.previous_day_progress_quantity_input if element.previous_day_progress_quantity_input else None
            ),
            previous_day_input_progress_milestone_id=(
                element.previous_day_progress_milestone_id_input
                if element.previous_day_progress_milestone_id_input
                else None
            ),
            element_status=BoqElementStatus(element.boq_element.element_status),
            update_method=(ItemTypeUpdateMethodChoices(element.update_method) if element.update_method else None),
            last_day_progress_updated_at=element.last_day_progress_updated_at,
            progress_updated_at=element.progress_updated_at,
            unlocked_at=element.unlocked_at,
            uom_id=element.boq_element.uom,
            item_type_id=element.boq_element.item_type_id,
        )

    def get_bulk_wp_elements(self, element_ids: list[int]) -> list[ElementListDataEntity]:
        elements = (
            self.get_elements_base_selector()
            .select_related(
                "boq_element__section",
                "boq_element__category",
                "boq_element__item_type",
                "boq_element__status_updated_by",
                "boq_element__created_by",
                "boq_element__proposal_element",
                "boq_element__main_preview_file",
            )
            .annotate_preview_day_progress()
            .annotate_work_progress_comment_count(org_id=self.org_id)
            .annotate_current_element_data()
            .filter(boq_element_id__in=element_ids)
        )

        element_entities: list[ElementListDataEntity] = []

        for element in elements:
            element: WorkProgressElement

            element_entities.append(self._prepare_element_list_data_entity(element))

        return element_entities

    def get_elements_for_update_method(self, element_ids: list[int]):
        return (
            self.get_elements_base_selector()
            .filter(boq_element_id__in=element_ids)
            .values("boq_element_id", "update_method", "boq_element__element_status")
        )

    def get_elements_for_update_method_entity(self, element_ids: list[int]) -> list[UpdateMethodElementEntity]:
        elements = self.get_elements_for_update_method(element_ids=element_ids)

        entities: list[UpdateMethodElementEntity] = []

        for element in elements:
            entities.append(
                UpdateMethodElementEntity(
                    id=element.get("boq_element_id", 0),
                    element_status=BoqElementStatus(element.get("boq_element__element_status", None)),
                    update_method=(
                        ItemTypeUpdateMethodChoices(element.get("update_method"))
                        if element.get("update_method")
                        else None
                    ),
                )
            )

        return entities

    def get_elements_for_scope_data(self, element_ids: list[int] | None = None) -> list[ScopeElementBaseEntity]:
        if element_ids:
            # This is for order header details
            elements = (
                WorkProgressElement.objects.get_queryset()
                .select_related("boq_element", "boq_element__proposal_element", "boq_element__section")
                .annotate_current_element_data()
                .annotate_preview_day_progress()
            )
            elements = elements.filter(boq_element_id__in=element_ids)
        else:
            elements = (
                self.get_elements_base_selector().select_related("boq_element__section").annotate_preview_day_progress()
            )

        elements = elements.values(
            "boq_element_id",
            "boq_element__section_id",
            "progress_percentage",
            "current_element_data",
            "attachment_uploaded_at",
            "previous_day_progress_percentage",
            "progress_updated_at",
            "update_method",
            "boq_element__item_type_id",
        )

        return self._prepare_scope_data(elements)

    def get_sections_for_scope_data(self) -> list[SectionScopeDataEntity]:
        sections = (
            BoqSection.objects.filter(boq_id=self.project_id).values("id", "name").order_by("position").available()
        )

        return self._prepare_section_data(sections)

    def get_scope_timeline_data(self) -> ScopeTimelineDatesDataEntity:
        project_details = ProjectOrganizationEntity(project_id=self.project_id, organization_id=self.org_id)
        timeline_data: ProjectDataEntity = ProjectDataCache.get(project_details=project_details)
        return self._prepare_scope_timeline_data_entity(timeline_dates=timeline_data.dates)

    def get_element_categories(self) -> list[ElementCategoryDataEntity]:
        boq_elements_category_ids = (
            self.get_elements_base_selector().values_list("boq_element__category", flat=True).distinct()
        )
        categories = ElementCategory.objects.filter(is_active=True, id__in=boq_elements_category_ids)
        return self._prepare_category_entities(categories)

    def get_element_statuses(self) -> list[ElementStatusDataEntity]:
        element_statuses = (
            self.get_elements_base_selector().values_list("boq_element__element_status", flat=True).distinct()
        )
        return self._prepare_status_entities(element_statuses)

    def get_element_uoms(self) -> list[ElementUomDataEntity]:
        uoms = self.get_elements_base_selector().values_list("boq_element__uom", flat=True).distinct()
        return self._prepare_uom_entities(uoms)

    def get_element_item_types(self) -> list[ElementItemTypeDataEntity]:
        item_type_ids = self.get_elements_base_selector().values_list("boq_element__item_type", flat=True).distinct()
        item_types = ElementItemType.objects.filter(id__in=item_type_ids)
        return self._prepare_item_type_entities(item_types)

    def get_work_progress_elements_paginated_data(
        self,
        filter_data: ElementListFilterEntity,
    ) -> PaginatedElementListDataEntity:
        elements = (
            self.get_elements_base_selector()
            .select_related(
                "boq_element__section",
                "boq_element__category",
                "boq_element__item_type",
                "boq_element__created_by",
                "boq_element__proposal_element",
                "boq_element__main_preview_file",
            )
            .order_by(F("boq_element__section_id").asc(nulls_first=True), "boq_element__position")
            .annotate_preview_day_progress()
            .annotate_work_progress_comment_count(org_id=self.org_id)
        )

        total_count, filtered_elements = get_filter_pydantic_queryset_data(
            queryset=elements, filter_data=filter_data, filterset=ElementFilterSet
        )
        entities = [self._prepare_element_list_data_entity(element) for element in filtered_elements]
        return PaginatedElementListDataEntity(count=total_count, data=entities)

    def get_element_detail_data(self, element_id: int) -> ElementDetailDataEntity:
        element: WorkProgressElement = (
            self.get_elements_base_selector()
            .select_related(
                "boq_element__section",
                "boq_element__category",
                "boq_element__item_type",
                "boq_element__created_by",
            )
            .annotate_preview_day_progress()
            .prefetch_all_preview_files()
            .annotate_work_progress_comment_count(org_id=self.org_id)
            .get(boq_element_id=element_id)
        )

        return self._prepare_element_detail(element, attachments=self.get_element_all_attachments_entity(element_id))

    def update_element(
        self,
        element_id: int,
        updated_data: UpdateElementDataEntity,
        element: WorkProgressElement | None = None,
        save: bool = True,
    ) -> tuple[UpdateElementOutputDataEntity, WorkProgressElement]:
        if element is None:
            self.get_wp_element(element_id=element_id)
            element = self.element_model

        assert element is not None

        fields = [
            "progress_percentage",
            "progress_quantity_input",
            "progress_percentage_input",
            "update_method",
            "unlocked_at",
            "milestone_input_id",
        ]

        current_time = timezone.now()

        progress_percentage_input = updated_data.progress_percentage_input
        progress_quantity_input = updated_data.progress_quantity_input
        is_unlocked = updated_data.is_unlocked
        # old_update_method = element.update_method

        if progress_percentage_input and progress_percentage_input > 100:
            raise BaseValidationError("Progress percentage can not be greater than 100")

        if progress_percentage_input and progress_percentage_input < 0:
            raise BaseValidationError("Progress percentage can not be less than 0")

        if progress_quantity_input and progress_quantity_input < 0:
            raise BaseValidationError("Progress quantity can not be less than 0")

        element_data = updated_data.model_dump(exclude_unset=True)

        if element_data.get("progress_percentage"):
            element_data["progress_percentage"] = decimal.Decimal(
                str(math.floor(float(element_data["progress_percentage"]) * TWO_DECIMAL_FACTOR) / TWO_DECIMAL_FACTOR)
            ).quantize(decimal.Decimal(".01"))

        if is_unlocked:
            element_data["unlocked_at"] = current_time

        # if element_data.get("milestone_id", None):
        #     element_data["milestone_input_id"] = element_data.pop("milestone_id")

        progress_can_update_today = (
            element.progress_updated_at is None or element.progress_updated_at.date() != current_time.date()
        )

        old_progress_percentage = element.progress_percentage

        updated_element, _, updated_field = model_update(
            instance=element,
            fields=fields,
            data=element_data,
            updated_by_id=self.user_id,
            clean=False,
            save=False,
        )

        completion_amount_diff = decimal.Decimal(0)
        is_updated_today = False

        if "update_method" in updated_field:
            updated_element.progress_updated_at = current_time
            updated_element.progress_percentage = decimal.Decimal(0)
            updated_element.progress_percentage_input = None
            updated_element.progress_quantity_input = None
            updated_element.milestone_input_id = None

            if progress_can_update_today:
                is_updated_today = True

        if element_data.get("is_progress_manually_updated"):
            updated_element.input_progress_updated_at = current_time

        if "progress_percentage" in updated_field:
            updated_element.progress_updated_at = current_time
            completion_amount_diff = (
                (updated_element.boq_element.saved_final_amount_with_tax or decimal.Decimal(0))
                * (updated_element.progress_percentage - old_progress_percentage)
                / 100
            )

            if progress_can_update_today:
                is_updated_today = True

        if element_data.get("action") == WorkProgressElementActionChoices.ATTACHMENT_UPDATED:
            updated_element.attachment_uploaded_at = current_time

        progress_percentage_diff = updated_element.progress_percentage - old_progress_percentage

        element_timeline_map: dict[int, int] = {}
        if save:
            updated_element.save()
            # create history timeline
            item_type_map = {element_id: updated_element.boq_element.item_type_id}
            updated_data_map = {element_id: updated_data}
            element_uom_map = {
                element_id: ElementUomDataEntity(
                    value=updated_element.boq_element.uom,
                    name=self.uom_cache.get(str(updated_element.boq_element.uom), ""),
                )
            }
            timeline_output_entities = self.create_history_timeline(
                element_ids=[element_id],
                element_id_to_updated_data_map=updated_data_map,
                element_id_to_item_type_id_map=item_type_map,
                element_id_to_uom_map=element_uom_map,
            )
            element_timeline_list = self._prepare_element_timeline_list(
                timeline_objects=timeline_output_entities.created_timeline_objects
            )
            element_timeline_map = {entity.element_id: entity.timeline_id for entity in element_timeline_list}

        return (
            UpdateElementOutputDataEntity(
                updated_element_entity=self._prepare_element_list_data_entity(updated_element),
                updated_field=updated_field,
                completion_amount_diff=completion_amount_diff,
                is_updated_today=is_updated_today,
                section_id=updated_element.boq_element.section_id or BoqSection.DEFAULT_SECTION_ID,
                progress_percentage_diff=progress_percentage_diff,
                timeline_id=element_timeline_map.get(updated_element.boq_element_id),
            ),
            updated_element,
        )

    def bulk_update_elements(self, updated_data: list[UpdateElementDataEntity]) -> list[UpdateElementOutputDataEntity]:
        fields = [
            "progress_percentage",
            "progress_quantity_input",
            "progress_percentage_input",
            "input_progress_updated_at",
            "attachment_uploaded_at",
            "progress_updated_at",
            "update_method",
            "unlocked_at",
            "milestone_input_id",
        ]

        element_ids_to_updated: list[int] = []
        element_id_to_updated_data_map: dict[int, UpdateElementDataEntity] = {}
        for data in updated_data:
            element_ids_to_updated.append(data.id)
            element_id_to_updated_data_map[data.id] = data

        elements = (
            self.get_elements_base_selector()
            .select_related(
                "boq_element__item_type",
                "boq_element__section",
                "boq_element__category",
                "boq_element__created_by",
            )
            .annotate_preview_day_progress()
            .filter(boq_element_id__in=element_ids_to_updated)
        )
        updated_elements: list[WorkProgressElement] = []
        updated_element_entities: list[UpdateElementOutputDataEntity] = []
        for element in elements:
            updated_element_entity, element = self.update_element(
                element_id=element.pk,
                element=element,
                updated_data=element_id_to_updated_data_map[element.pk],
                save=False,
            )
            updated_elements.append(element)
            updated_element_entities.append(updated_element_entity)

        WorkProgressElement.objects.bulk_update(updated_elements, fields)

        element_id_to_item_type_id_map = {element.pk: element.boq_element.item_type_id for element in updated_elements}
        element_id_to_uom_map = {
            element.pk: ElementUomDataEntity(
                name=self.uom_cache.get(str(element.boq_element.uom), ""), value=element.boq_element.uom
            )
            for element in updated_elements
        }

        timeline_output_entities = self.create_history_timeline(
            element_ids=[element.pk for element in updated_elements],
            element_id_to_updated_data_map=element_id_to_updated_data_map,
            element_id_to_item_type_id_map=element_id_to_item_type_id_map,
            element_id_to_uom_map=element_id_to_uom_map,
        )
        element_timeline_list = self._prepare_element_timeline_list(
            timeline_objects=timeline_output_entities.created_timeline_objects
        )
        element_timeline_map: dict[int, int] = {
            entity.element_id: entity.timeline_id for entity in element_timeline_list
        }

        return self._update_timeline_id_updated_element_entities(
            updated_element_entities=updated_element_entities, element_timeline_map=element_timeline_map
        )

    def _update_timeline_id_updated_element_entities(
        self, updated_element_entities: list[UpdateElementOutputDataEntity], element_timeline_map: dict[int, int]
    ) -> list[UpdateElementOutputDataEntity]:
        for element_entity in updated_element_entities:
            try:
                assert (
                    element_timeline_map.get(element_entity.updated_element_entity.id) is not None
                ), "Timeline ID not found for updated element"
            except AssertionError:
                logger.info(
                    "Timeline ID not found for updated element", element_id=element_entity.updated_element_entity.id
                )
                raise self.WorkProgressRepoException("Element ID not found for updated element")

            element_entity.timeline_id = element_timeline_map[element_entity.updated_element_entity.id]
        return updated_element_entities

    def get_milestone_progress_percentage(self, milestone_id: int) -> decimal.Decimal:
        return ItemTypeMileStone.objects.get(id=milestone_id).percentage

    def get_element_history_data(self, element_id: int):
        # Subquery to get previous action history for change comparison
        previous_action_history_data_subquery = (
            BoqElementActionHistory.objects.filter(
                boq_element_id=OuterRef("element__boq_element_id"),
                created_at__lt=OuterRef("boq_element_action_history_mappings__boq_element_action_history__created_at"),
            )
            .order_by("-created_at")
            .annotate(previous_data=JSONObject(quantity="quantity", item_type_name="item_type__name", uom="uom"))
            .values("previous_data")[:1]
        )

        # Subquery tot get previous element history for change comparison
        previous_element_history_data_subquery = (
            BoqElementHistory.objects.filter(
                element_id=OuterRef("element__boq_element_id"),
                created_at__lt=OuterRef("boq_element_action_history_mappings__boq_element_history__created_at"),
            )
            .order_by("-created_at")
            .annotate(previous_data=JSONObject(quantity="quantity", item_type_name="item_type__name", uom="uom"))
            .values("previous_data")[:1]
        )

        previous_update_method_data_subquery = (
            UpdateMethodHistory.objects.filter(
                timeline__element_id=OuterRef("element_id"),
                created_at__lt=OuterRef("created_at"),
            )
            .order_by("-created_at")
            .values("update_method")[:1]
        )

        histories = (
            WorkProgressElementTimeline.objects.get_queryset()
            .select_related(
                "element",
                "boq_element_action_history_mappings__boq_element_action_history",
                "boq_element_action_history_mappings__boq_element_history",
                "percentage_history",
                "update_method_history",
                "attachment_history",
                "input_progress_quantity_history",
                "input_progress_percentage_history",
                "milestone_history",
                "created_by",
            )
            .annotate(
                previous_action_history_data=Subquery(previous_action_history_data_subquery),
                previous_element_history_data=Subquery(previous_element_history_data_subquery),
                previous_update_method=Subquery(previous_update_method_data_subquery),
            )
            .filter(element__boq_element_id=element_id)
            .order_by("-created_at")
        )
        history_data = self._prepare_history_data(histories)
        return history_data

    def _prepare_history_data(self, histories: WorkProgressElementTimelineQueryset) -> list[HistoryTimelineDataEntity]:
        entities = []
        if not histories:
            return entities
        for history in histories:
            updated_data = None
            is_dependent_history = False
            if history.action == WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED:
                updated_data = UpdatedDataEntity(
                    updated_data=f"{format_trailing_zeros(history.input_progress_quantity_history.quantity)} {history.input_progress_quantity_history.uom_name}",  # noqa
                    previous_data=None,
                    updated_attribute=None,
                )
            elif history.action == WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED:
                updated_data = UpdatedDataEntity(
                    updated_data=history.input_progress_percentage_history.percentage,
                    previous_data=None,
                    updated_attribute=None,
                )
            elif history.action == WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED:
                updated_data = UpdatedDataEntity(
                    updated_data=history.milestone_history.name,
                    previous_data=None,
                    updated_attribute=None,
                )
            elif history.action == WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED:
                updated_data = UpdatedDataEntity(
                    updated_data=history.update_method_history.update_method,
                    previous_data=history.previous_update_method,
                    updated_attribute=ElementUpdatedAttributeChoices.METHOD_OF_UPDATE,
                )
                is_dependent_history = True
            elif history.action == WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED:
                updated_data, is_dependent_history = self._get_boq_element_previous_and_updated_data(history)
                if updated_data is None:
                    continue

            elif history.action == WorkProgressElementActionChoices.ATTACHMENT_UPDATED:
                # Not showing attachment history
                continue
            else:
                # Unknown action
                continue

            assert updated_data is not None

            # Add dependent runtime generated history data first if any
            if is_dependent_history:
                entities.append(self._get_dependent_history_data(history))

            entities.append(
                HistoryTimelineDataEntity(
                    id=history.id,
                    created_at=history.created_at,
                    created_by=UserDataEntity(
                        id=history.created_by.id,
                        name=history.created_by.name,
                        photo=history.created_by.photo.url if history.created_by.photo else None,
                    ),
                    action=history.action,
                    data=updated_data,
                )
            )
        return entities

    def _get_dependent_history_data(self, history: WorkProgressElementTimeline) -> HistoryTimelineDataEntity:
        return HistoryTimelineDataEntity(
            id=history.id,
            created_at=history.created_at,
            created_by=UserDataEntity(
                id=history.created_by.id,
                name=history.created_by.name,
                photo=history.created_by.photo.url if history.created_by.photo else None,
            ),
            action=WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED,
            data=UpdatedDataEntity(
                updated_data=decimal.Decimal(0),
                previous_data=None,
                updated_attribute=None,
            ),
        )

    def _get_boq_element_previous_and_updated_data(
        self, history: WorkProgressElementTimeline
    ) -> tuple[UpdatedDataEntity | None, bool]:
        org_uoms = OrganizationUOMCache.get(instance_id=history.created_by.org_id)
        current_action_history = history.boq_element_action_history_mappings.boq_element_action_history
        previous_action_history = history.previous_action_history_data

        if not current_action_history:
            current_element_history = history.boq_element_action_history_mappings.boq_element_history
            previous_element_history = history.previous_element_history_data
            update_data, is_dependent_history = self.get_update_data_by_element_history(
                current_element_history=current_element_history,
                previous_element_history=previous_element_history,
                org_uoms=org_uoms,
            )
        else:
            update_data, is_dependent_history = self.get_update_data_by_action_history(
                current_action_history=current_action_history,
                previous_action_history=previous_action_history,
                org_uoms=org_uoms,
            )

        return update_data, is_dependent_history

    def get_update_data_by_element_history(
        self, current_element_history, previous_element_history, org_uoms
    ) -> tuple[UpdatedDataEntity | None, bool]:
        is_dependent_history = False
        if current_element_history.quantity != previous_element_history.get("quantity", None):
            return (
                UpdatedDataEntity(
                    updated_data=current_element_history.quantity,
                    previous_data=previous_element_history.get("quantity", None),
                    updated_attribute=ElementUpdatedAttributeChoices.QUANTITY,
                ),
                is_dependent_history,
            )
        elif (
            current_element_history.item_type
            and current_element_history.item_type.name != previous_element_history.get("item_type_name", None)
        ) or (
            not current_element_history.item_type and previous_element_history.get("item_type_name", None) is not None
        ):
            is_dependent_history = True
            return (
                UpdatedDataEntity(
                    updated_data=current_element_history.item_type.name,
                    previous_data=previous_element_history.get("item_type_name", None),
                    updated_attribute=ElementUpdatedAttributeChoices.ITEM_TYPE,
                ),
                is_dependent_history,
            )
        elif current_element_history.uom != previous_element_history.get("uom", None):
            current_uom_name = org_uoms.get(current_element_history.uom, None)
            previous_uom_name = (
                org_uoms.get(previous_element_history.get("uom"), None) if previous_element_history.get("uom") else None
            )
            is_dependent_history = True
            return (
                UpdatedDataEntity(
                    updated_data=current_uom_name,
                    previous_data=previous_uom_name,
                    updated_attribute=ElementUpdatedAttributeChoices.UOM,
                ),
                is_dependent_history,
            )
        else:
            logger.info(
                "No updated attribute found for boq element.", current_element_history_id=current_element_history.id
            )
            # Skipping this history for backward compatibility as no updated attribute found in boq current version
            # or no recorded change found for backward data
            return None, False

    def get_update_data_by_action_history(
        self, current_action_history, previous_action_history, org_uoms
    ) -> tuple[UpdatedDataEntity | None, bool]:
        is_dependent_history = False
        if current_action_history.quantity != previous_action_history.get("quantity", None):
            return (
                UpdatedDataEntity(
                    updated_data=current_action_history.quantity,
                    previous_data=previous_action_history.get("quantity", None),
                    updated_attribute=ElementUpdatedAttributeChoices.QUANTITY,
                ),
                is_dependent_history,
            )
        elif (
            current_action_history.item_type
            and current_action_history.item_type.name != previous_action_history.get("item_type_name", None)
        ) or (not current_action_history.item_type and previous_action_history.get("item_type_name", None) is not None):
            is_dependent_history = True
            return (
                UpdatedDataEntity(
                    updated_data=current_action_history.item_type.name,
                    previous_data=previous_action_history.get("item_type_name", None),
                    updated_attribute=ElementUpdatedAttributeChoices.ITEM_TYPE,
                ),
                is_dependent_history,
            )
        elif current_action_history.uom != previous_action_history.get("uom", None):
            current_uom_name = org_uoms.get(current_action_history.uom, None)
            previous_uom_name = (
                org_uoms.get(previous_action_history.get("uom"), None) if previous_action_history.get("uom") else None
            )
            is_dependent_history = True
            return (
                UpdatedDataEntity(
                    updated_data=current_uom_name,
                    previous_data=previous_uom_name,
                    updated_attribute=ElementUpdatedAttributeChoices.UOM,
                ),
                is_dependent_history,
            )
        else:
            logger.info(
                "No updated attribute found for boq element.", current_action_history_id=current_action_history.id
            )
            # Skipping this history for backward compatibility as no updated attribute found in boq current version
            # or no recorded change found for backward data
            return None, False

    def check_all_elements_are_100_percent(self) -> bool:
        return not self.get_element_fast_base_selector().filter(progress_percentage__lt=100).exists()

    def update_work_report_status(self, status: StatusChoices):
        ProjectProgressReport.objects.filter(pk=self.project_id).update(
            status=status,
            updated_by_id=self.user_id,
            updated_at=self.current_time,
        )

    def get_element_preview_files(self, element_id: int) -> list[ElementPreviewFileDataEntity]:
        element = (
            self.get_element_fast_base_selector()
            .select_related("boq_element")
            .prefetch_all_preview_files()
            .get(boq_element_id=element_id)
        )

        return self._prepare_element_preview_file(element)

    def get_project_created_at(self) -> datetime.datetime:
        project = Project.objects.values("created_at").get(pk=self.project_id)

        return project["created_at"]


class WorkProgressReportRepo(WorkProgressReportAbstractRepo, ReportPrepareRepo):
    def __init__(self, user_id: int, project_id: int, org_id: int):
        self.user_id = user_id
        self.project_id = project_id
        self.org_id = org_id
        self.current_date = get_local_time(timezone.now()).date()
        self.uom_cache: dict[str, str] = OrganizationUOMCache.get(instance_id=self.org_id)

    def get_elements_base_selector(self):
        return (
            WorkProgressElement.objects.get_queryset()
            .select_related("boq_element")
            .filter(
                boq_element__organization_id=self.org_id,
                boq_element__boq_id=self.project_id,
                boq_element__deleted_at=None,
                boq_element__element_status__in=[
                    BoqElementStatus.DRAFT.value,
                    BoqElementStatus.APPROVED.value,
                    BoqElementStatus.CHANGE_REQUESTED.value,
                    BoqElementStatus.REQUESTED.value,
                    BoqElementStatus.CANCELLATION_REQUESTED.value,
                ],
            )
            .order_by("boq_element__position_ts")
        )

    def get_base_report_selector(self) -> QuerySet[ProgressReport]:
        return (
            ProgressReport.objects.select_related("ppr__project__organization")
            .filter(
                ppr_id=self.project_id,
                organization_id=self.org_id,
            )
            .available()
        )

    def get_base_vendor_report_selector(self) -> QuerySet[ProgressReport]:
        return (
            ProgressReport.objects.select_related("ppr__project__organization")
            .filter(
                ppr_id=self.project_id,
            )
            .available()
        )

    def get_report_data(self, report_id: int):
        try:
            report = self.get_base_report_selector().get(pk=report_id)

            return self._prepare_report_data(report)
        except ProgressReport.DoesNotExist:
            raise self.ReportDoesNotExistException("Report does not exist")

    def delete_report(self, report_id: int):
        try:
            report = self.get_base_report_selector().get(pk=report_id)

            report.soft_delete(user_id=self.user_id)
        except ProgressReport.DoesNotExist:
            raise self.ReportDoesNotExistException("Report does not exist")

    def get_hashtag_element_list(self) -> list[HashtagElementEntity]:
        elements = self.get_elements_base_selector().select_related("boq_element__main_preview_file").all()

        entities: list[HashtagElementEntity] = []

        for element in elements:
            element: WorkProgressElement

            entity = HashtagElementEntity(
                id=element.boq_element_id,
                name=element.boq_element.name,
                photo=element.boq_element.main_preview_file.file.url if element.boq_element.main_preview_file else None,
            )

            entities.append(entity)

        return entities

    def get_hashtag_category_list(self) -> list[HashtagElementEntity]:
        categories = element_category_list(organization_id=self.org_id).values("id", "name")

        entities: list[HashtagElementEntity] = []
        for category in categories:
            entities.append(HashtagElementEntity(id=category["id"], name=category["name"]))

        return entities

    def get_global_manpower_category_list(self):
        return ManpowerCategory.objects.filter(is_active=True)

    def get_project_manpower_category_list(self):
        return ProjectManpowerCategory.objects.filter(project_id=self.project_id)

    def get_manpower_category_list(self) -> list[str]:
        global_category_list: list[str] = list(self.get_global_manpower_category_list().values_list("name", flat=True))
        project_category_list: list[str] = list(
            self.get_project_manpower_category_list().values_list("name", flat=True)
        )

        return list(set(global_category_list + project_category_list))

    def get_project_site_view_point_data(self) -> list[SiteViewPointDataEntity]:
        view_points = (
            ProjectOrgSiteViewPoint.objects.filter(project_id=self.project_id, organization_id=self.org_id)
            .available()  # type: ignore
            .all()
            .order_by("id")
            .values("id", "name")
        )

        entities: list[SiteViewPointDataEntity] = []

        for view_point in view_points:
            entities.append(SiteViewPointDataEntity(id=view_point["id"], name=view_point["name"]))

        return entities

    def get_site_view_attachment_data(
        self,
        number_of_previous_day_data_in_site_view_point: int,
        max_number_of_attachments_per_day: int,
    ) -> list[SiteViewPointAttachmentDataEntity]:
        current_day_site_view_point_mapping_ids = self.get_current_day_site_view_point_mapping_ids()
        previous_day_site_view_point_mapping_ids = self.get_previous_day_site_view_point_mapping_ids(
            number_of_previous_day_data_in_site_view_point=number_of_previous_day_data_in_site_view_point
        )

        attachments = (
            ProgressReportSiteViewPointMappingAttachment.objects.select_related("mapping")
            .filter(
                mapping_id__in=current_day_site_view_point_mapping_ids + previous_day_site_view_point_mapping_ids,
                is_previous=False,
            )
            .all()
            .order_by("-uploaded_at")
        )

        entities: list[SiteViewPointAttachmentDataEntity] = []

        for attachment in attachments:
            entities.append(
                SiteViewPointAttachmentDataEntity(
                    id=attachment.pk,
                    name=attachment.name if attachment.name else attachment.file.url.split("/")[-1],
                    url=attachment.file.url,
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                    site_view_point_id=attachment.mapping.site_view_point_id,
                    is_attachment_of_current_day=attachment.mapping_id in current_day_site_view_point_mapping_ids,
                )
            )

        return entities

    def get_current_day_site_view_point_mapping_ids(self) -> list[int]:
        todays_latest_dpr_subquery = (
            ProgressReport.objects.select_related("ppr")
            .filter(ppr__project_id=self.project_id, organization_id=self.org_id, created_at__date=self.current_date)
            .order_by("-created_at")
            .values("id")[:1]
        )

        view_points = ProgressReportSiteViewPointMapping.objects.filter(
            progress_report_id=Subquery(todays_latest_dpr_subquery)
        ).values_list("id", flat=True)

        return list(view_points)

    def get_previous_day_site_view_point_mapping_ids(
        self, number_of_previous_day_data_in_site_view_point: int
    ) -> list[int]:
        raw_sql = f"""
            WITH RankedDates AS (
                SELECT
                    psv.site_view_point_id,
                    max(psv.id) as id,
                    MAX(psv.progress_report_id) AS progress_report_id,
                    DATE(psv.created_at) AS created_date,
                    ROW_NUMBER() OVER (PARTITION BY psv.site_view_point_id ORDER 
                    BY DATE(psv.created_at) DESC) AS rank
                FROM
                    progress_report_site_view_point_mapping psv
                INNER JOIN 
                    project_org_site_view_points 
                    ON (psv.site_view_point_id = project_org_site_view_points.id)
                where
                    project_org_site_view_points.project_id='{self.project_id}' 
                    and project_org_site_view_points.organization_id='{self.org_id}'
                    and DATE(psv.created_at) < CURRENT_DATE
                GROUP BY
                    psv.site_view_point_id, DATE(psv.created_at)
            )
            SELECT
                rd.site_view_point_id,
                rd.created_date,
                rd.id
            FROM
                RankedDates rd
            WHERE
                rd.rank <= '{number_of_previous_day_data_in_site_view_point}'
            ORDER BY
                rd.site_view_point_id, rd.created_date DESC;
        """

        view_points = ProgressReportSiteViewPointMapping.objects.raw(raw_sql)

        view_point_ids: list[int] = []

        for view_point in view_points:
            view_point_ids.append(view_point.id)

        return view_point_ids

    def get_last_report_reported_project_progress_data(self):
        last_report = (
            self.get_base_report_selector()
            .order_by("-id")
            .values("project_progress_percent", "projected_end_date")
            .first()
        )

        return LastReportReportedProjectProgressDataEntity(
            reported_progress_percentage=last_report["project_progress_percent"] if last_report else None,
            projected_end_date=last_report["projected_end_date"] if last_report else None,
        )

    def get_elements_for_prefill_scope_update_data(self) -> list[PrefillScopeUpdateDataEntity]:
        elements = (
            self.get_elements_base_selector()
            .select_related("boq_element__section")
            .filter(Q(progress_updated_at__date=self.current_date) | Q(attachment_uploaded_at__date=self.current_date))
            .prefetch_today_uploaded_attachments()
            .annotate_preview_day_progress()
        )

        entities: list[PrefillScopeUpdateDataEntity] = []

        for element in elements:
            element: WorkProgressElement

            attachments: list[PrefillScopeUpdateElementAttachmentDataEntity] = []

            for attachment in element.boq_element.pr_attachments.all():  # type: ignore
                attachment: ProgressReportElementAttachment
                attachments.append(
                    PrefillScopeUpdateElementAttachmentDataEntity(
                        id=attachment.pk,
                        name=attachment.name,
                        url=attachment.file.url,
                        uploaded_at=attachment.uploaded_at,
                        thumbnail_url=None,
                    )
                )

            entities.append(
                PrefillScopeUpdateDataEntity(
                    id=element.boq_element_id,
                    name=element.boq_element.name,
                    section_name=(
                        element.boq_element.section.name if element.boq_element.section else BoqSection.DEFAULT_SECTION
                    ),
                    section_id=(
                        element.boq_element.section.pk if element.boq_element.section else BoqSection.DEFAULT_SECTION_ID
                    ),
                    item_type_id=element.boq_element.item_type_id,
                    attachments=attachments,
                    progress_percentage=element.progress_percentage,
                    progress_percentage_input=element.progress_percentage,
                    progress_quantity_input=element.progress_quantity_input,
                    milestone_input_id=element.milestone_input_id,
                    update_method=(
                        ItemTypeUpdateMethodChoices(element.update_method) if element.update_method else None
                    ),
                    previous_day_progress_percentage_input=element.previous_day_progress_percentage_input,
                    previous_day_progress_milestone_input_id=element.previous_day_progress_milestone_id_input,
                    previous_day_progress_quantity_input=element.previous_day_progress_quantity_input,
                    previous_day_progress_percentage=element.previous_day_progress_percentage,
                    uom_name=self.uom_cache.get(str(element.boq_element.uom), ""),
                )
            )

        return entities

    def get_today_latest_report_for_daily_log_data(self) -> LastReportForDailyLogDataEntity:
        prefetch_attachments = Prefetch(
            "attachments",
            ProgressReportAttachment.objects.filter(
                creation_type=PROGRESS_REPORT_CONSTANTS.MANUAL, boq_section__isnull=True
            ),
        )
        prefetch_items = Prefetch(
            "items", ProgressReportItem.objects.filter(creation_type=PROGRESS_REPORT_CONSTANTS.MANUAL)
        )

        today_latest_report = (
            self.get_base_report_selector()
            .prefetch_related(prefetch_attachments, prefetch_items)
            .order_by("-id")
            .filter(created_at__date=self.current_date)
            .first()
        )

        if not today_latest_report:
            return LastReportForDailyLogDataEntity(
                today_update=LastReportForDailyLogUpdateDataEntity(updates=[], attachments=[]),
                blocker=LastReportForDailyLogUpdateDataEntity(updates=[], attachments=[]),
                tomorrow_plan=LastReportForDailyLogUpdateDataEntity(updates=[], attachments=[]),
            )

        updates: dict[str, list[list[WorkProgressBlockBaseModel]]] = {
            ProgressReportItem.TODAY: [],
            ProgressReportItem.UPDATE: [],
            ProgressReportItem.BLOCKER: [],
            ProgressReportItem.TOMORROW_PLAN: [],
        }

        for update in today_latest_report.items.all():  # type: ignore
            update: ProgressReportItem

            blocks: list[WorkProgressBlockBaseModel] = []

            for block in update.blocks:
                if update.version == 1:
                    blocks.append(WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=block))
                else:
                    blocks.append(WorkProgressBlockBaseModel(**block))

            updates[update.type].append(blocks)

        attachments: list[LastReportForDailyLogUpdateAttachmentDataEntity] = []

        for attachment in today_latest_report.attachments.all():  # type: ignore
            attachment: ProgressReportAttachment

            attachments.append(
                LastReportForDailyLogUpdateAttachmentDataEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    url=attachment.file.url,
                    uploaded_at=attachment.uploaded_at,
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                )
            )

        return LastReportForDailyLogDataEntity(
            today_update=LastReportForDailyLogUpdateDataEntity(
                updates=updates[ProgressReportItem.UPDATE] + updates[ProgressReportItem.TODAY], attachments=attachments
            ),
            blocker=LastReportForDailyLogUpdateDataEntity(updates=updates[ProgressReportItem.BLOCKER], attachments=[]),
            tomorrow_plan=LastReportForDailyLogUpdateDataEntity(
                updates=updates[ProgressReportItem.TOMORROW_PLAN], attachments=[]
            ),
        )

    def get_latest_generate_report_config_id(self) -> int:
        latest_report_config = (
            ProjectReportConfigHistory.objects.filter(
                project_id=self.project_id,
                organization_id=self.org_id,
                config_type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT.value,
            )
            .order_by("-id")
            .values("id")
            .first()
        )

        assert latest_report_config is not None, "Report config not found"

        return latest_report_config["id"]

    def create_report(
        self,
        input_project_progress_percentage: decimal.Decimal,
        input_projected_end_date: datetime.datetime | None,
        actual_progress_percentage: decimal.Decimal,
        total_manpower_count: int,
        total_element_count: int,
        today_progress_percentage: decimal.Decimal,
        config_id: int,
        save: bool = True,
    ) -> ProgressReport:
        report = ProgressReport()

        report.ppr_id = self.project_id
        report.created_by_id = self.user_id
        report.organization_id = self.org_id
        report.project_progress_percent = input_project_progress_percentage.quantize(
            decimal.Decimal(".01"), rounding=decimal.ROUND_DOWN
        )
        report.projected_end_date = input_projected_end_date
        report.derived_project_progress_percent = actual_progress_percentage.quantize(
            decimal.Decimal(".01"), rounding=decimal.ROUND_DOWN
        )
        report.created_at = timezone.now()
        report.total_manpower_count = total_manpower_count
        report.total_element_count = total_element_count
        report.today_progress_percentage = today_progress_percentage.quantize(
            decimal.Decimal(".01"), rounding=decimal.ROUND_DOWN
        )
        report.config_id = config_id
        report.dpr_version = 3
        report.pdf_status = DownloadProgressStatusEnum.IN_PROGRESS.value

        report.clean()

        if save:
            report.save()

        return report

    def create_manpower(
        self, report_id: int, data: CreateReportManPowerDataEntity, save=True
    ) -> ProgressReportManPower:
        manpower = ProgressReportManPower()

        manpower.progress_report_id = report_id
        manpower.category_name = data.name
        manpower.count = data.value
        manpower.created_by_id = self.user_id
        manpower.created_at = timezone.now()

        manpower.clean()

        if save:
            manpower.save()

        return manpower

    def bulk_create_manpower(self, report_id: int, data: list[CreateReportManPowerDataEntity]) -> None:
        if not data:
            return

        manpowers = [
            self.create_manpower(
                report_id=report_id,
                data=manpower,
                save=False,
            )
            for manpower in data
        ]

        ProgressReportManPower.objects.bulk_create(manpowers)

    def bulk_modify_view_points(
        self,
        rename_view_point_data: dict[int, str],
        delete_view_point_data: list[int],
    ):
        site_view_points: list[ProjectOrgSiteViewPoint] = []

        for view_point_id, new_name in rename_view_point_data.items():
            site_view_points.append(
                ProjectOrgSiteViewPoint(
                    id=view_point_id,
                    name=new_name,
                    updated_by_id=self.user_id,
                    updated_at=timezone.now(),
                )
            )

        for view_point_id in delete_view_point_data:
            site_view_points.append(
                ProjectOrgSiteViewPoint(
                    id=view_point_id,
                    deleted_by_id=self.user_id,
                    deleted_at=timezone.now(),
                )
            )

        ProjectOrgSiteViewPoint.objects.bulk_update(
            objs=site_view_points, fields=["name", "updated_by_id", "updated_at", "deleted_at", "deleted_by_id"]
        )

    def bulk_create_view_points(self, data: list[str]) -> list[CreateSiteViewPointDataEntity]:
        if not data:
            return []

        site_view_points: list[ProjectOrgSiteViewPoint] = []

        for view_point_name in data:
            site_view_points.append(
                ProjectOrgSiteViewPoint(
                    name=view_point_name,
                    project_id=self.project_id,
                    created_by_id=self.user_id,
                    organization_id=self.org_id,
                    created_at=timezone.now(),
                )
            )

        create_site_view_points = ProjectOrgSiteViewPoint.objects.bulk_create(site_view_points)

        return [
            CreateSiteViewPointDataEntity(id=site_view_point.pk, name=site_view_point.name)
            for site_view_point in create_site_view_points
        ]

    def bulk_create_view_points_mapping(
        self, report_id: int, data: list[int]
    ) -> list[CreateSiteViewPointMappingDataEntity]:
        if not data:
            return []

        site_view_point_mapping: list[ProgressReportSiteViewPointMapping] = []

        for view_point_id in data:
            mapping = ProgressReportSiteViewPointMapping()

            mapping.progress_report_id = report_id
            mapping.site_view_point_id = view_point_id
            mapping.created_at = timezone.now()

            site_view_point_mapping.append(mapping)

        created_site_view_point_mappings = ProgressReportSiteViewPointMapping.objects.bulk_create(
            site_view_point_mapping
        )

        return [
            CreateSiteViewPointMappingDataEntity(
                id=site_view_point_mapping.pk,
                site_view_point_id=site_view_point_mapping.site_view_point_id,
            )
            for site_view_point_mapping in created_site_view_point_mappings
        ]

    def bulk_create_view_point_attachments(self, data: list[CreateSiteViewPointAttachmentEntity]):
        if not data:
            return

        attachments: list[ProgressReportSiteViewPointMappingAttachment] = []

        current_time = timezone.now()

        for attachment in data:
            instance = ProgressReportSiteViewPointMappingAttachment()

            instance.mapping_id = attachment.mapping_id
            instance.name = attachment.name
            instance.file = attachment.url
            instance.thumbnail = attachment.thumbnail_url
            instance.uploaded_at = current_time
            instance.uploaded_by_id = self.user_id
            instance.is_previous = attachment.is_previous

            if attachment.is_previous:
                instance.uploaded_at = attachment.uploaded_at or current_time

            attachments.append(instance)

        ProgressReportSiteViewPointMappingAttachment.objects.bulk_create(attachments, batch_size=100)

    def get_report_list(self) -> QuerySet[ProgressReport]:
        mappings_prefetch = Prefetch(
            "mappings",
            ProgressReportSiteViewPointMapping.objects.order_by("created_at").select_related("site_view_point"),
        )
        mapping_attachments_prefetch = Prefetch(
            "mappings__attachments",
            ProgressReportSiteViewPointMappingAttachment.objects.order_by("uploaded_at"),
        )

        attachments_prefetch = Prefetch(
            "attachments",
            ProgressReportAttachment.objects.select_related("section").order_by("uploaded_at"),
        )

        schedule_attachment_prefetch = Prefetch(
            "attachments",
            WorkProgressScheduleActivityAttachment.objects.filter(wp_schedule_activity__isnull=True).order_by(
                "uploaded_at"
            ),
        )

        schedule_prefetch = Prefetch(
            "schedule",
            WorkProgressSchedule.objects.prefetch_related(
                "activities", "activities__attachments", schedule_attachment_prefetch
            ),
        )

        inventory_stock_items_prefetch = Prefetch(
            "inventory_stock_items",
            queryset=WorkProgressInventoryStockItem.objects.select_related("item").order_by("created_at"),
        )

        return (
            self.get_base_report_selector()
            .select_related("created_by", "created_by__org", "config")
            .prefetch_related(
                "pr_manpower",
                mappings_prefetch,
                mapping_attachments_prefetch,
                attachments_prefetch,
                "items",
                "items__section",
                inventory_stock_items_prefetch,
                schedule_prefetch,
            )
            .order_by("-id")
        )

    def get_vendor_report_list(self, vendor_ids: list[int]) -> QuerySet[ProgressReport]:
        mappings_prefetch = Prefetch(
            "mappings",
            ProgressReportSiteViewPointMapping.objects.order_by("created_at").select_related("site_view_point"),
        )
        mapping_attachments_prefetch = Prefetch(
            "mappings__attachments",
            ProgressReportSiteViewPointMappingAttachment.objects.order_by("uploaded_at"),
        )

        attachments_prefetch = Prefetch(
            "attachments",
            ProgressReportAttachment.objects.select_related("section").order_by("uploaded_at"),
        )

        schedule_attachment_prefetch = Prefetch(
            "attachments",
            WorkProgressScheduleActivityAttachment.objects.filter(wp_schedule_activity__isnull=True).order_by(
                "uploaded_at"
            ),
        )

        schedule_prefetch = Prefetch(
            "schedule",
            WorkProgressSchedule.objects.prefetch_related(
                "activities", "activities__attachments", schedule_attachment_prefetch
            ),
        )

        return (
            self.get_base_vendor_report_selector()
            .filter(organization_id__in=vendor_ids)
            .select_related("created_by", "created_by__org", "config", "organization")
            .prefetch_related(
                "pr_manpower",
                mappings_prefetch,
                mapping_attachments_prefetch,
                attachments_prefetch,
                "items",
                "items__section",
                "inventory_stock_items",
                schedule_prefetch,
            )
            .order_by("organization__name", "-id")
        )

    def get_report_list_data(self, filter_data: ReportListFilterDataEntity) -> ReportListPaginatedEntity:
        queryset = self.get_report_list().annotate_comment_count(org_id=self.org_id)

        count, queryset = get_filter_pydantic_queryset_data(
            queryset=queryset,
            filter_data=filter_data,
            filterset=ReportFilterSet,
        )

        return ReportListPaginatedEntity(
            data=[self._prepare_report_data_list_entities(report) for report in queryset],
            count=count,
        )

    def get_vendor_report_list_paginated_data(
        self, filter_data: VendorReportListFilterDataEntity
    ) -> ReportListPaginatedEntity:
        queryset = self.get_vendor_report_list(vendor_ids=filter_data.vendor_org_ids)

        count, queryset = get_filter_pydantic_queryset_data(
            queryset=queryset,
            filter_data=filter_data,
            filterset=VendorReportFilterSet,
        )

        return ReportListPaginatedEntity(
            data=[self._prepare_report_data_list_entities(report, is_vendor_report=True) for report in queryset],
            count=count,
        )

    def get_prefill_manpower_data(self):
        latest_report = self.get_base_report_selector().prefetch_related("pr_manpower").order_by("-id").first()

        if not latest_report:
            return []

        entities: list[CreateReportManPowerDataEntity] = []

        for manpower in latest_report.pr_manpower.all():  # type: ignore
            manpower: ProgressReportManPower

            entities.append(
                CreateReportManPowerDataEntity(
                    name=manpower.category_name,
                    value=manpower.count,
                )
            )

        return entities

    def create_attachment(
        self,
        report_id: int,
        data: CreateReportAttachmentEntity,
        save: bool = True,
    ) -> ProgressReportAttachment:
        instance = ProgressReportAttachment()

        instance.file = data.url
        instance.name = data.name
        instance.uploaded_by_id = self.user_id
        instance.progress_report_id = report_id
        instance.creation_type = data.creation_type.value
        instance.thumbnail = data.thumbnail_url

        instance.element_name = data.element_name
        instance.element_id = data.element_id
        instance.section_id = data.section_id
        instance.boq_section_id = data.boq_section_id

        instance.clean()

        if save:
            instance.save()

        return instance

    def bulk_create_attachments(self, report_id: int, data: list[CreateReportAttachmentEntity]):
        if not data:
            return

        attachments: list[ProgressReportAttachment] = []

        for attachment in data:
            attachments.append(self.create_attachment(report_id=report_id, data=attachment, save=False))

        ProgressReportAttachment.objects.bulk_create(objs=attachments)

    def create_item(
        self,
        report_id: int,
        data: CreateReportItemEntity,
        save: bool = True,
    ) -> ProgressReportItem:
        instance = ProgressReportItem()

        instance.progress_report_id = report_id
        instance.type = data.daily_log_type.value
        instance.blocks = [block.__dict__ for block in data.blocks]
        instance.creation_type = data.creation_type.value
        instance.boq_section_id = data.boq_section_id
        instance.section_id = data.section_id
        instance.version = 2

        instance.clean()

        if save:
            instance.save()
            instance.category_hashtags.set(data.category_ids)
            instance.element_hashtags.set(data.boq_element_ids)

        return instance

    def bulk_create_items(
        self,
        report_id: int,
        data: list[CreateReportItemEntity],
    ) -> None:
        if not data:
            return

        items: list[ProgressReportItem] = []

        for item in data:
            items.append(self.create_item(report_id=report_id, data=item, save=False))

        created_items = ProgressReportItem.objects.bulk_create(items)

        for created_item, incoming_data in zip(created_items, data):
            created_item.category_hashtags.set(incoming_data.category_ids)
            created_item.element_hashtags.set(incoming_data.boq_element_ids)

    def create_project_manpower_category(self, name: str, save: bool = True) -> CreateProjectManpowerCategoryDataEntity:
        try:
            instance = ProjectManpowerCategory()

            instance.name = name
            instance.project_id = self.project_id
            instance.created_by_id = self.user_id

            instance.clean()

            if save:
                instance.save()

            return CreateProjectManpowerCategoryDataEntity(name=instance.name)
        except ProjectManpowerCategory.ModelFieldValidationException as e:
            raise self.ProjectManpowerCategoryFieldValidationException(e)

    def get_report_created_by_list(self) -> list[ReportDetailCreatedByEntity]:
        created_by_list = (
            self.get_base_report_selector()
            .select_related("created_by")
            .order_by("created_by__first_name", "created_by__last_name")
            .values("created_by_id", "created_by__first_name", "created_by__last_name", "created_by__photo")
            .distinct()
        )

        entities: list[ReportDetailCreatedByEntity] = []

        for created_by in created_by_list:
            entities.append(
                ReportDetailCreatedByEntity(
                    id=created_by["created_by_id"],
                    name=f"{created_by['created_by__first_name']} {created_by['created_by__last_name']}",
                    photo=(
                        PublicMediaFileStorage.url(created_by["created_by__photo"])
                        if created_by["created_by__photo"]
                        else None
                    ),
                )
            )

        return entities

    def get_vendor_report_created_by_list(
        self, filter_data: VendorReportCreatedByListFilterDataEntity
    ) -> list[VendorReportCreatedByDataEntity]:
        created_by_list = (
            self.get_base_report_selector()
            .select_related("created_by", "created_by__org")
            .filter(organization_id__in=filter_data.vendor_org_ids)
            .order_by("created_by__first_name", "created_by__last_name")
            .values(
                "created_by_id",
                "created_by__first_name",
                "created_by__last_name",
                "created_by__photo",
                "created_by__org__name",
            )
            .distinct()
        )

        entities: list[VendorReportCreatedByDataEntity] = []

        for created_by in created_by_list:
            entities.append(
                VendorReportCreatedByDataEntity(
                    id=created_by["created_by_id"],
                    name=f"{created_by['created_by__first_name']} {created_by['created_by__last_name']}",
                    photo=created_by["created_by__photo"] if created_by["created_by__photo"] else None,
                    organization_name=created_by["created_by__org__name"],
                )
            )

        return entities

    def create_section(
        self,
        report_id: int,
        data: CreateReportSectionEntity,
        save: bool = True,
    ) -> ProgressReportSection:
        instance = ProgressReportSection()

        instance.progress_report_id = report_id
        instance.name = data.name
        instance.progress_percentage = data.progress_percentage

        instance.clean()

        if save:
            instance.save()

        return instance

    def bulk_create_sections(self, report_id: int, data: list[CreateReportSectionEntity]) -> list[ReportSectionEntity]:
        if not data:
            return []

        sections: list[ProgressReportSection] = []

        for section in data:
            sections.append(self.create_section(report_id=report_id, data=section, save=False))

        created_sections = ProgressReportSection.objects.bulk_create(sections)

        return [
            ReportSectionEntity(
                id=section.pk,
                name=section.name,
                progress_percentage=section.progress_percentage,
            )
            for section in created_sections
        ]

    def get_report_configs(self, types: list[WorkProgressConfigReportTypeEnum]) -> GetReportConfigEntity:
        config_types = [config_type.value for config_type in types]
        configs = ProjectReportConfig.objects.filter(
            organization_id=self.org_id,
            project=self.project_id,
            config_type__in=config_types,
        ).values("config", "config_type")

        return GetReportConfigEntity(
            config={
                WorkProgressConfigReportTypeEnum(config["config_type"]): [
                    ReportConfigEntity(**section) for section in config["config"]
                ]
                for config in configs
            }
        )

    def create_report_config_history(self, type: WorkProgressConfigReportTypeEnum, config: list[dict]):
        instance = ProjectReportConfigHistory()

        instance.organization_id = self.org_id
        instance.project_id = self.project_id
        instance.config_type = type.value
        instance.config = config
        instance.created_by_id = self.user_id

        instance.clean()
        instance.save()

    def update_report_config(self, type: WorkProgressConfigReportTypeEnum, config: list[ReportConfigEntity]):
        report_config = ProjectReportConfig.objects.get(
            organization_id=self.org_id,
            project=self.project_id,
            config_type=type.value,
        )

        config_dump = [data.model_dump() for data in config]

        report_config.config = config_dump
        report_config.updated_by_id = self.user_id
        report_config.updated_at = timezone.now()

        report_config.clean()
        report_config.save()

        self.create_report_config_history(type=type, config=config_dump)

    def create_material_item(self, report_id: int, data: MaterialItemEntity, save: bool = True):
        instance = WorkProgressInventoryStockItem()

        instance.progress_report_id = report_id
        instance.created_by_id = self.user_id
        instance.item_name = data.name
        instance.quantity = data.quantity
        instance.type = data.type.value
        instance.uom_name = data.uom_name
        instance.item_id = data.id
        instance.stock_value = data.stock_value

        instance.clean()

        if save:
            instance.save()

        return instance

    def bulk_create_material_items(self, report_id: int, data: list[MaterialItemEntity]):
        if not data:
            return

        items: list[WorkProgressInventoryStockItem] = []

        for item in data:
            items.append(self.create_material_item(report_id=report_id, data=item, save=False))

        WorkProgressInventoryStockItem.objects.bulk_create(items)

    def create_schedule_activity(self, schedule_id: int, data: PrefillScheduleUpdateActivityEntity, save: bool = True):
        instance = WorkProgressScheduleActivity()

        instance.wp_schedule_id = schedule_id
        instance.activity_id = data.id
        instance.name = data.name
        instance.wbs = data.wbs
        instance.status = data.status.status.value if data.status else None
        instance.delay_days = data.status.days if data.status else None
        instance.today_progress = data.day_progress
        instance.total_progress = data.total_progress
        instance.created_by_id = self.user_id

        instance.clean()

        if save:
            instance.save()

        return instance

    def bulk_create_schedule_activities(
        self, schedule_id: int, data: list[PrefillScheduleUpdateActivityEntity] | None
    ) -> list[CreateScheduleActivityEntity]:
        if not data:
            return []

        activities: list[WorkProgressScheduleActivity] = []

        for activity in data:
            activities.append(self.create_schedule_activity(schedule_id=schedule_id, data=activity, save=False))

        created_activities = WorkProgressScheduleActivity.objects.bulk_create(activities)

        return [
            CreateScheduleActivityEntity(
                id=activity.pk,
                activity_id=activity.activity_id,
            )
            for activity in created_activities
        ]

    def create_schedule(
        self, report_id: int, data: PrefillScheduleUpdateEntity
    ) -> tuple[int, list[CreateScheduleActivityEntity]]:
        instance = WorkProgressSchedule()

        instance.progress_report_id = report_id
        instance.schedule_id = data.id
        instance.today_progress = data.today_progress
        instance.total_progress = data.total_progress
        instance.status_type = data.status.status.value if data.status else None
        instance.delay_days = data.status.days if data.status else None
        instance.planned_end_date = data.planned_end_date
        instance.created_by_id = self.user_id

        instance.clean()
        instance.save()

        return instance.pk, self.bulk_create_schedule_activities(schedule_id=instance.pk, data=data.activities)

    def create_schedule_attachment(
        self, schedule_id: int, data: CreateReportScheduleUpdateAttachmentInputEntity, save: bool = True
    ):
        instance = WorkProgressScheduleActivityAttachment()

        instance.wp_schedule_activity_id = data.activity_id
        instance.name = data.name
        instance.file = data.file
        instance.thumbnail = data.thumbnail_url
        instance.uploaded_by_id = self.user_id
        instance.wp_schedule_id = schedule_id
        instance.uploaded_at = timezone.now()

        instance.clean()

        if save:
            instance.save()

        return instance

    def bulk_create_schedule_attachments(
        self, schedule_id: int, data: list[CreateReportScheduleUpdateAttachmentInputEntity]
    ):
        if not data:
            return

        attachments: list[WorkProgressScheduleActivityAttachment] = []

        for attachment in data:
            attachments.append(self.create_schedule_attachment(schedule_id=schedule_id, data=attachment, save=False))

        WorkProgressScheduleActivityAttachment.objects.bulk_create(attachments)


class WorkProgressOrgRepo(WorkProgressOrgAbstractRepo):
    def __init__(self, user_id: int, org_id: int):
        self.user_id = user_id
        self.org_id = org_id
        self.current_time = get_local_time(timezone.now())

    def create_report_config_history(self, type: WorkProgressConfigReportTypeEnum, config: list[dict]):
        instance = ReportConfigHistory()

        instance.organization_id = self.org_id
        instance.config_type = type.value
        instance.config = config
        instance.created_by_id = self.user_id

        instance.clean()
        instance.save()

    def update_or_create_report_config(
        self,
        type: WorkProgressConfigReportTypeEnum,
        config: list[ReportConfigEntity],
    ):
        config_dump = [data.model_dump() for data in config]
        report_config, is_created = ReportConfig.objects.get_or_create(
            organization_id=self.org_id,
            config_type=type.value,
            defaults={
                "organization_id": self.org_id,
                "config_type": type.value,
                "created_by_id": self.user_id,
                "config": config_dump,
            },
        )

        if not is_created:
            report_config.updated_by_id = self.user_id
            report_config.updated_at = self.current_time
            report_config.config = config_dump

            report_config.clean()
            report_config.save()

        self.create_report_config_history(type=type, config=config_dump)

    def get_configs(self, types: list[WorkProgressConfigReportTypeEnum]) -> GetReportConfigEntity:
        config_types = [config_type.value for config_type in types]
        configs = ReportConfig.objects.filter(
            organization_id=self.org_id,
            config_type__in=config_types,
        ).values("config", "config_type")

        return GetReportConfigEntity(
            config={
                WorkProgressConfigReportTypeEnum(config["config_type"]): [
                    ReportConfigEntity(**section) for section in config["config"]
                ]
                for config in configs
            }
        )

    def create_project_configs(
        self,
        project_id: int,
        configs: dict[WorkProgressConfigReportTypeEnum, list[ReportConfigEntity]],
    ) -> None:
        project_report_configs = []
        project_report_config_histories = []

        for config_type, config in configs.items():
            report_config = ProjectReportConfig()

            report_config.organization_id = self.org_id
            report_config.project_id = project_id
            report_config.config_type = config_type.value
            report_config.config = [data.model_dump() for data in config]
            report_config.created_by_id = self.user_id

            project_report_configs.append(report_config)

            report_config_history = ProjectReportConfigHistory()

            report_config_history.organization_id = self.org_id
            report_config_history.project_id = project_id
            report_config_history.config_type = config_type.value
            report_config_history.config = [data.model_dump() for data in config]
            report_config_history.created_by_id = self.user_id

            project_report_config_histories.append(report_config_history)

        ProjectReportConfig.objects.bulk_create(project_report_configs)
        ProjectReportConfigHistory.objects.bulk_create(project_report_config_histories)


class WorkProgressReportPDFRepo(WorkProgressReportPDFAbstractRepo, ReportPrepareRepo):
    DEFAULT_GENERATED_BY_NAME = "RDash"
    DEFAULT_GENERATED_BY_PHOTO = "https://cdn.rdash.io/rdash-assets/67ad2824c5df407384e2731193b9353e.png"

    class WorkProgressReportPDFRepoException(
        WorkProgressReportPDFAbstractRepo.WorkProgressReportPDFAbstractRepoException
    ):
        pass

    def __init__(self, user_id: int, project_id: int, org_id: int):
        self.user_id = user_id
        self.project_id = project_id
        self.org_id = org_id
        self.current_time = get_local_time(timezone.now())
        self.current_date = get_local_time(timezone.now()).date()

    def save_work_progress_report_pdf(self, report_id: int, pdf: BytesIO):
        content_file = ContentFile(pdf.getvalue())

        report = ProgressReport.objects.get(pk=report_id)
        filename = f"DPR_{self.current_date}.pdf"

        report.pdf_status = DownloadProgressStatusEnum.UPLOADED.value
        report.pdf.save(filename, content_file, save=True)

        report.save()

    def get_work_progress_report_pdf(self, report_id: int) -> ReportPdfEntity:
        report = ProgressReport.objects.values("pdf", "pdf_status").get(pk=report_id)

        return ReportPdfEntity(
            url=PublicMediaFileStorage.url(report["pdf"]) if report["pdf"] else None,
            status=DownloadProgressStatusEnum(report["pdf_status"]),
        )

    def update_single_report_pdf_status(self, report_id: int, status: DownloadProgressStatusEnum):
        instance = ProgressReport.objects.get(pk=report_id)

        instance.pdf_status = status.value

        instance.clean()
        instance.save()

    def _prepare_updated_element_daywise_data(
        self, elements: list[WorkProgressElementDayWiseData], start_date: datetime.date, end_date: datetime.date
    ) -> list[ElementDayWiseDataEntity]:
        daywise_updated_elements: list[ElementDayWiseDataEntity] = []
        for updated_element in elements:
            updated_element: WorkProgressElementDayWiseData

            attachments: list[ReportDetailAttachmentEntity] = []
            if updated_element.element.boq_element.pr_attachments.all():  # type: ignore
                for attachment in updated_element.element.boq_element.pr_attachments.all():  # type: ignore
                    attachment: ProgressReportElementAttachment

                    if attachment.uploaded_at.date() < start_date or attachment.uploaded_at.date() > end_date:
                        continue
                    attachments.append(
                        ReportDetailAttachmentEntity(
                            id=attachment.id,
                            name=attachment.name,
                            url=attachment.file.url,
                            thumbnail_url=None,
                            uploaded_at=attachment.uploaded_at,
                            element_name=attachment.element.name,
                        )
                    )

            section_id = (
                updated_element.element.boq_element.section_id
                if updated_element.element.boq_element.section
                else BoqSection.DEFAULT_SECTION_ID
            )
            section_name = (
                updated_element.element.boq_element.section.name
                if updated_element.element.boq_element.section
                else BoqSection.DEFAULT_SECTION
            )
            is_previous_data_available = True if updated_element.previous_day_data is not None else False
            daywise_updated_elements.append(
                ElementDayWiseDataEntity(
                    element_id=updated_element.element.pk,
                    element_name=updated_element.element.boq_element.name,
                    section_id=section_id,
                    section_name=section_name,
                    day_end_progress_percentage=updated_element.day_end_progress_percentage,
                    day_end_update_method=updated_element.latest_timeline.percentage_history.update_method,
                    day_end_input_quantity=(
                        updated_element.latest_timeline.input_progress_quantity_history.quantity
                        if hasattr(updated_element.latest_timeline, "input_progress_quantity_history")
                        else decimal.Decimal(0)
                    ),
                    day_end_uom_name=(
                        updated_element.latest_timeline.input_progress_quantity_history.uom_name
                        if hasattr(updated_element.latest_timeline, "input_progress_quantity_history")
                        else updated_element.day_end_uom_name
                    ),
                    day_end_milestone_name=(
                        updated_element.latest_timeline.milestone_history.name
                        if hasattr(updated_element.latest_timeline, "milestone_history")
                        else updated_element.day_end_milestone_name
                    ),
                    previous_day_end_progress_percentage=(
                        updated_element.previous_day_data.get("progress_percentage")
                        if is_previous_data_available
                        else None
                    ),
                    pervious_day_end_update_method=(
                        updated_element.previous_day_data.get("update_method") if is_previous_data_available else None
                    ),
                    previous_day_end_input_quantity=(
                        updated_element.previous_day_data.get("input_quantity") if is_previous_data_available else None
                    ),
                    previous_day_end_uom_name=(
                        updated_element.previous_day_data.get("uom_name") if is_previous_data_available else None
                    ),
                    previous_day_milestone_name=(
                        updated_element.previous_day_data.get("milestone_name") if is_previous_data_available else None
                    ),
                    latest_timeline_id=updated_element.latest_timeline_id,
                    date=updated_element.date,
                    attachments=attachments,
                )
            )

        return daywise_updated_elements

    def get_updated_elements_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[ElementDayWiseDataEntity]:
        previous_day_data_subquery = Subquery(
            WorkProgressElementDayWiseData.objects.filter(
                element_id=OuterRef("element_id"),
                date__lt=OuterRef("date"),
            )
            .annotate(
                previous_day_data=JSONObject(
                    update_method=F("latest_timeline__percentage_history__update_method"),
                    progress_percentage=F("day_end_progress_percentage"),
                    input_quantity=Case(
                        When(
                            Q(latest_timeline__input_progress_quantity_history__isnull=False),
                            then=F("latest_timeline__input_progress_quantity_history__quantity"),
                        ),
                        default=None,
                        output_field=DecimalField(),
                    ),
                    uom_name=Case(
                        When(
                            Q(latest_timeline__input_progress_quantity_history__isnull=False),
                            then=F("latest_timeline__input_progress_quantity_history__uom_name"),
                        ),
                        default=None,
                        output_field=CharField(),
                    ),
                    milestone_name=Case(
                        When(
                            Q(latest_timeline__milestone_history__isnull=False),
                            then=F("latest_timeline__milestone_history__name"),
                        ),
                        default=None,
                        output_field=CharField(),
                    ),
                )
            )
            .order_by("-date")
            .values("previous_day_data")[:1]
        )
        element_ids = (
            WorkProgressElement.objects.get_queryset()
            .filter(
                boq_element__organization_id=self.org_id,
                boq_element__boq_id=self.project_id,
                boq_element__deleted_at=None,
                boq_element__element_status__in=[
                    BoqElementStatus.DRAFT.value,
                    BoqElementStatus.APPROVED.value,
                    BoqElementStatus.CHANGE_REQUESTED.value,
                    BoqElementStatus.REQUESTED.value,
                    BoqElementStatus.CANCELLATION_REQUESTED.value,
                ],
            )
            .values_list("boq_element_id", flat=True)
        )
        elements = (
            WorkProgressElementDayWiseData.objects.get_queryset()
            .filter(element_id__in=element_ids)
            .filter(Q(date__gte=start_date) | Q(date__lte=end_date))
            .select_related(
                "element",
                "element__boq_element__section",
                "latest_timeline",
                "latest_timeline__milestone_history",
                "latest_timeline__percentage_history",
                "latest_timeline__input_progress_quantity_history",
                # "latest_timeline__input_progress_percentage_history",
                # "latest_timeline__update_method_history",
            )
            .prefetch_element_attachments_in_date_range(start_date=start_date, end_date=end_date)
            .annotate(previous_day_data=previous_day_data_subquery)
            .order_by("date")
        )

        return self._prepare_updated_element_daywise_data(
            elements=list(elements), start_date=start_date, end_date=end_date
        )

    def get_updated_sections_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[SectionDayWiseDataEntity]:
        day_wise_sections = (
            WorkProgressSectionDayWiseData.objects.get_queryset()
            .filter(project_id=self.project_id, organization_id=self.org_id)
            .filter(Q(date__gte=start_date) | Q(date__lte=end_date))
            .order_by("date")
        )

        daywise_updated_section: list[SectionDayWiseDataEntity] = []

        # if no data is found in the date range, get the last updated section
        if day_wise_sections.count() == 0:
            last_updated_sections = (
                WorkProgressSectionDayWiseData.objects.get_queryset()
                .filter(project_id=self.project_id, organization_id=self.org_id)
                .filter(date__lte=start_date)
                .order_by("-date")
                .first()
            )
            if not last_updated_sections:
                return []

            section_data_at_day_end = last_updated_sections.day_end_data
            for section_data in section_data_at_day_end:
                section = SectionNameTotalProgressEntity.model_validate(section_data)
                daywise_updated_section.append(
                    SectionDayWiseDataEntity(
                        id=section.id,
                        name=section.name,
                        progress_percentage=section.progress_percentage,
                        date=last_updated_sections.date,
                    )
                )
            return daywise_updated_section

        for day_data in list(day_wise_sections):
            day_data: WorkProgressSectionDayWiseData
            section_data_at_day_end = day_data.day_end_data
            for section_data in section_data_at_day_end:
                section = SectionNameTotalProgressEntity.model_validate(section_data)
                daywise_updated_section.append(
                    SectionDayWiseDataEntity(
                        id=section.id,
                        name=section.name,
                        progress_percentage=section.progress_percentage,
                        date=day_data.date,
                    )
                )

        return daywise_updated_section

    def get_updated_project_org_data_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[ProjectOrgDaywiseDataEntity]:
        day_wise_project_org_data = (
            WorkProgressProjectDayWiseData.objects.get_queryset()
            .filter(project_id=self.project_id, organization_id=self.org_id)
            .filter(Q(date__gte=start_date) | Q(date__lte=end_date))
            .order_by("date")
        )

        # if no data is found in the date range, get the last updated project org data
        if day_wise_project_org_data.count() == 0:
            last_updated_project_org_data = (
                WorkProgressProjectDayWiseData.objects.get_queryset()
                .filter(project_id=self.project_id, organization_id=self.org_id)
                .filter(date__lte=start_date)
                .order_by("-date")
                .first()
            )
            if not last_updated_project_org_data:
                return []

            scope_data_at_day_end = ScopeProgressEntity.model_validate(last_updated_project_org_data.day_end_data)
            return [
                ProjectOrgDaywiseDataEntity(
                    date=last_updated_project_org_data.date,
                    total_items=scope_data_at_day_end.total_item_count,
                    day_progress=scope_data_at_day_end.today_progress,
                    total_progress=scope_data_at_day_end.total_progress,
                )
            ]

        return self._prepare_project_org_daywise_data(list(day_wise_project_org_data))

    def _prepare_project_org_daywise_data(self, day_wise_project_org_data: list[WorkProgressProjectDayWiseData]):
        daywise_updated_project_org_data: list[ProjectOrgDaywiseDataEntity] = []
        for data in day_wise_project_org_data:
            data: WorkProgressProjectDayWiseData
            scope_data_at_day_end = ScopeProgressEntity.model_validate(data.day_end_data)
            daywise_updated_project_org_data.append(
                ProjectOrgDaywiseDataEntity(
                    date=data.date,
                    total_items=scope_data_at_day_end.total_item_count,
                    day_progress=scope_data_at_day_end.today_progress,
                    total_progress=scope_data_at_day_end.total_progress,
                )
            )

        return daywise_updated_project_org_data

    def get_reports_base_selector(self):
        mappings_prefetch = Prefetch(
            "mappings",
            ProgressReportSiteViewPointMapping.objects.order_by("created_at").select_related("site_view_point"),
        )
        mapping_attachments_prefetch = Prefetch(
            "mappings__attachments",
            ProgressReportSiteViewPointMappingAttachment.objects.order_by("uploaded_at"),
        )

        pr_attachments_prefetch = Prefetch(
            "attachments",
            queryset=ProgressReportAttachment.objects.select_related("section").order_by("uploaded_at"),
        )

        schedule_attachment_prefetch = Prefetch(
            "attachments",
            WorkProgressScheduleActivityAttachment.objects.filter(wp_schedule_activity__isnull=True).order_by(
                "uploaded_at"
            ),
        )

        schedule_prefetch = Prefetch(
            "schedule",
            WorkProgressSchedule.objects.prefetch_related(
                "activities", "activities__attachments", schedule_attachment_prefetch
            ),
        )

        inventory_stock_items_prefetch = Prefetch(
            "inventory_stock_items",
            queryset=WorkProgressInventoryStockItem.objects.select_related("item").order_by("created_at"),
        )

        return ProgressReport.objects.select_related(
            "ppr__project",
            "created_by",
            "created_by__org",
            "config",
        ).prefetch_related(
            "pr_manpower",
            mappings_prefetch,
            mapping_attachments_prefetch,
            pr_attachments_prefetch,
            "items",
            "items__section",
            inventory_stock_items_prefetch,
            schedule_prefetch,
        )

    def get_report_by_id(self, report_id: int) -> ProgressReport:
        return self.get_reports_base_selector().get(pk=report_id)

    def _prepare_report_pdf_material_data(self, report: ProgressReport) -> ReportPDFMaterialDataEntity | None:
        if len(report.inventory_stock_items.all()) == 0:
            return None

        total_stock_value = decimal.Decimal(0)
        stock_consumed: list[ReportPDFStockDataEntity] = []
        stock_received: list[ReportPDFStockDataEntity] = []
        stock_transferred_out: list[ReportPDFStockDataEntity] = []

        for item in report.inventory_stock_items.all():
            item: WorkProgressInventoryStockItem

            total_stock_value += item.item.stock_value

            if item.type == WorkProgressInventoryStockItemTypeChoices.CONSUMED:
                stock_consumed.append(
                    ReportPDFStockDataEntity(
                        name=item.item_name,
                        quantity=item.quantity,
                        uom=item.uom_name,
                    )
                )

            if item.type == WorkProgressInventoryStockItemTypeChoices.RECEIVED:
                stock_received.append(
                    ReportPDFStockDataEntity(
                        name=item.item_name,
                        quantity=item.quantity,
                        uom=item.uom_name,
                    )
                )

            if item.type == WorkProgressInventoryStockItemTypeChoices.TRANSFERRED_OUT:
                stock_transferred_out.append(
                    ReportPDFStockDataEntity(
                        name=item.item_name,
                        quantity=item.quantity,
                        uom=item.uom_name,
                    )
                )

        return ReportPDFMaterialDataEntity(
            total_stock_value=total_stock_value,
            consumed=stock_consumed,
            received=stock_received,
            transferred_out=stock_transferred_out,
        )

    def _prepare_report_pdf_schedule_data(self, report: ProgressReport) -> ReportPDFScheduleDataEntity | None:
        if not hasattr(report, "schedule"):
            return None

        report_schedule: WorkProgressSchedule = report.schedule

        activities: list[ReportPDFScheduleActivityDataEntity] = []

        for activity in report_schedule.activities.all():
            activity: WorkProgressScheduleActivity

            activity_status = None
            if activity.status:
                status = TimelineStatusHelper.get_status(
                    status_type=TimelineStatusEnum(activity.status),
                    days=activity.delay_days if activity.delay_days else None,
                )
                activity_status = ReportPDFStatusDataEntity(
                    name=status.name,
                    color=status.color_code,
                )

            activities.append(
                ReportPDFScheduleActivityDataEntity(
                    name=activity.name,
                    wbs=activity.wbs,
                    status=activity_status,
                    days_progress=activity.today_progress,
                    total_progress=activity.total_progress,
                )
            )

        schedule_status = None
        if report_schedule.status_type:
            status = TimelineStatusHelper.get_status(
                status_type=TimelineStatusEnum(report_schedule.status_type),
                days=report_schedule.delay_days if report_schedule.delay_days else None,
            )
            schedule_status = ReportPDFStatusDataEntity(
                name=status.name,
                color=status.color_code,
            )

        return ReportPDFScheduleDataEntity(
            id=report_schedule.pk,
            schedule_progress=report_schedule.total_progress,
            days_progress=report_schedule.today_progress,
            status=schedule_status,
            planned_end_date=report_schedule.planned_end_date,
            activities=activities,
        )

    def _prepare_report_pdf_section_data_entity(self, report: ProgressReport) -> ReportPDFSectionDataEntity:
        # prepare manpower data
        manpower_category_list: list[ReportPDFManpowerCategoryDataEntity] = []
        total_manpower = report.total_manpower_count
        for manpower in report.pr_manpower.all():
            manpower: ProgressReportManPower
            manpower_category_list.append(
                ReportPDFManpowerCategoryDataEntity(
                    name=manpower.category_name,
                    value=manpower.count,
                )
            )
        MANPOWER_DATA = ReportPDFManpowerDataEntity(total_manpower=total_manpower, updates=manpower_category_list)

        # prepare site view point data
        site_view_point_data: dict[int, ReportPDFSiteViewPointDataEntity] = {}
        for view_point_mapping in report.mappings.all():
            view_point_mapping: ProgressReportSiteViewPointMapping

            site_view_point_data[view_point_mapping.site_view_point_id] = ReportPDFSiteViewPointDataEntity(
                id=view_point_mapping.site_view_point_id,
                name=view_point_mapping.site_view_point.name,
                todays_attachments=[],
                previous_attachments=[],
            )

            for attachment in view_point_mapping.attachments.all():
                attachment: ProgressReportSiteViewPointMappingAttachment

                url = attachment.file.url
                attachment_entity = ReportDetailAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name if attachment.name else url.split("/")[-1],
                    url=url,
                    thumbnail_url=attachment.thumbnail.url if attachment.thumbnail else None,
                    uploaded_at=attachment.uploaded_at,
                )

                if attachment.is_previous:
                    site_view_point_data[view_point_mapping.site_view_point_id].previous_attachments.append(
                        attachment_entity
                    )
                else:
                    site_view_point_data[view_point_mapping.site_view_point_id].todays_attachments.append(
                        attachment_entity
                    )
        SITE_VIEW_POINT_DATA = list(site_view_point_data.values())

        # prepare daily log data
        DAILY_LOG_DATA = ReportPDFDailyLogDataEntity(
            today_update=ReportPDFDailyLogUpdateDataEntity(updates=[], attachments=[]),
            blocker=ReportPDFDailyLogUpdateDataEntity(updates=[], attachments=[]),
            tomorrow_plan=ReportPDFDailyLogUpdateDataEntity(updates=[], attachments=[]),
        )

        # prepare scope update data
        SCOPE_UPDATE_DATA = ReportPDFScopeUpdateDataEntity(
            total_items=report.total_element_count,
            day_progress=report.today_progress_percentage,
            scope_progress=report.derived_project_progress_percent,
            sections=[],
        )

        section_updates_mapping: dict[int, ReportPDFScopeUpdateSectionDataEntity] = {}

        for item_attachment in report.attachments.all():
            item_attachment: ProgressReportAttachment

            """
            Currently adding attachments in today's updates only
            If creation_type = manual & section_id is None & element_id is None, then it is daily log attachment
            """
            if (
                item_attachment.creation_type == PROGRESS_REPORT_CONSTANTS.MANUAL
                and not item_attachment.section
                and not item_attachment.element
            ):
                DAILY_LOG_DATA.today_update.attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=item_attachment.file.url,
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                        uploaded_at=item_attachment.uploaded_at,
                    )
                )
            else:
                assert item_attachment.section is not None, "Section can not be None"

                if item_attachment.section_id not in section_updates_mapping:
                    section_updates_mapping[item_attachment.section.pk] = ReportPDFScopeUpdateSectionDataEntity(
                        id=item_attachment.section.pk,
                        name=item_attachment.section.name,
                        progress_percentage=item_attachment.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                section_updates_mapping[item_attachment.section.pk].attachments.append(
                    ReportDetailAttachmentEntity(
                        id=item_attachment.pk,
                        name=item_attachment.name,
                        url=item_attachment.file.url,
                        thumbnail_url=item_attachment.thumbnail.url if item_attachment.thumbnail else None,
                        uploaded_at=item_attachment.uploaded_at,
                    )
                )

        for item in report.items.all():
            item: ProgressReportItem
            creation_type = WorkProgressCreationTypeEnum(item.creation_type)

            if creation_type == WorkProgressCreationTypeEnum.MANUAL:
                block_data = ReportPDFBlockDataEntity(blocks=[])
                for item_block in item.blocks:
                    if item.version == 1:
                        block = WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=item_block)
                    else:
                        block = WorkProgressBlockBaseModel(**item_block)
                    block_data.blocks.append(block)

                if item.type == ProgressReportItem.UPDATE or item.type == ProgressReportItem.TODAY:
                    DAILY_LOG_DATA.today_update.updates.append(block_data)
                elif item.type == ProgressReportItem.BLOCKER:
                    DAILY_LOG_DATA.blocker.updates.append(block_data)
                elif item.type == ProgressReportItem.TOMORROW_PLAN:
                    DAILY_LOG_DATA.tomorrow_plan.updates.append(block_data)

            if creation_type == WorkProgressCreationTypeEnum.DERIVED:
                assert item.section is not None, "Section can not be None"
                if item.section_id not in section_updates_mapping:
                    section_updates_mapping[item.section.pk] = ReportPDFScopeUpdateSectionDataEntity(
                        id=item.section.pk,
                        name=item.section.name,
                        progress_percentage=item.section.progress_percentage,
                        updates=[],
                        attachments=[],
                    )

                block_data = ReportPDFBlockDataEntity(blocks=[])
                for item_block in item.blocks:
                    if item.version == 1:
                        block = WorkProgressReportItemBlockHelper.convert_v1_block_data_into_v2(block=item_block)
                    else:
                        block = WorkProgressBlockBaseModel(**item_block)
                    block_data.blocks.append(block)

                section_updates_mapping[item.section.pk].updates.append(block_data)

        # fill scope update sections data
        SCOPE_UPDATE_DATA.sections = list(section_updates_mapping.values())

        # prepare materials data
        MATERIAL_DATA = self._prepare_report_pdf_material_data(report=report)

        # prepare schedule data
        SCHEDULE_DATA = self._prepare_report_pdf_schedule_data(report=report)

        # fill reported project progress data
        REPORTED_PROJECT_PROGRESS_DATA = ReportPDFReportedProjectProgressDataEntity(
            reported_progress=report.project_progress_percent,
            projected_end_date=report.projected_end_date,
        )

        return ReportPDFSectionDataEntity(
            scope_update=SCOPE_UPDATE_DATA,
            daily_log=DAILY_LOG_DATA,
            manpower=MANPOWER_DATA,
            site_view_point=SITE_VIEW_POINT_DATA,
            reported_project_progress=REPORTED_PROJECT_PROGRESS_DATA,
            material=MATERIAL_DATA,
            schedule=SCHEDULE_DATA,
        )

    def get_pdf_footer_data(self) -> ReportPDFFooterEntity:
        return ReportPDFFooterEntity(
            generated_by=ReportPDFCreatorEntity(
                name=self.DEFAULT_GENERATED_BY_NAME,
                photo=self.DEFAULT_GENERATED_BY_PHOTO,
            ),
            generated_at=self.current_date,
        )

    def get_single_report_pdf_data(self, report_id: int) -> ReportGeneratedPdfDataEntity:
        report = self.get_report_by_id(report_id=report_id)

        return ReportGeneratedPdfDataEntity(
            data=self._prepare_report_data_list_entities(report),
            header=ReportGeneratePDFHeaderEntity(
                project=ReportPDFProjectEntity(
                    name=report.ppr.project.name,
                    job_id=report.ppr.project.job_id if report.ppr.project.job_id else "",
                ),
                user=ReportPDFCreatorEntity(
                    name=report.created_by.name,
                    photo=report.created_by.photo.url if report.created_by.photo else None,
                ),
                generated_at=report.created_at,
                org=ReportPDFCreatorEntity(
                    name=report.organization.name if report.organization else self.DEFAULT_GENERATED_BY_NAME,
                    photo=report.organization.logo_url if report.organization else self.DEFAULT_GENERATED_BY_PHOTO,
                ),
            ),
            footer=self.get_pdf_footer_data(),
        )

    def _get_report_project_data(self) -> ReportProjectDataEntity:
        project = Project.objects.get(id=self.project_id)
        return ReportProjectDataEntity(name=project.name, job_id=project.job_id if project.job_id else "")

    def _get_exporter_org(self, org_id: int) -> ReportDetailCreatedByEntity:
        org = Organization.objects.get(id=org_id)
        return ReportDetailCreatedByEntity(
            id=org.pk,
            name=org.name,
            photo=org.logo.url if org.logo else None,
        )

    def get_reports_between_date_range(
        self, start_date: datetime.date, end_date: datetime.date, org_id: int
    ) -> ReportPDFDataWithProjectDataEntity:
        logger.info("Fetching reports in date range.", start_date=start_date, end_date=end_date, org_id=org_id)
        latest_report_ids_per_day = (
            ProgressReport.objects.filter(
                ppr__project_id=self.project_id,
                organization_id=org_id,
                created_at__date__gte=start_date,
                created_at__date__lte=end_date,
            )
            .values("created_at__date")
            .annotate(latest_id=Max("id"))
            .values_list("latest_id", flat=True)
        )

        report_queryset = (
            self.get_reports_base_selector()
            .filter(id__in=Subquery(latest_report_ids_per_day), created_by__org_id=org_id)
            .order_by("-created_at__date", "created_by__org_id")
        )

        reports = list(report_queryset)
        project_data = self._get_report_project_data()

        logger.info("Reports in date range fetched.", report_count=len(reports))
        if len(reports) == 0:
            logger.info("No report found between the given date range.", start_date=start_date, end_date=end_date)
            return ReportPDFDataWithProjectDataEntity(
                report_data=[],
                project_name=project_data.name,
                project_job_id=project_data.job_id,
                org=self._get_exporter_org(org_id=org_id),
            )

        reports_data = [self._prepare_reports_list_data_for_export(report) for report in list(reports)]
        return ReportPDFDataWithProjectDataEntity(
            report_data=reports_data,
            project_name=project_data.name,
            project_job_id=project_data.job_id,
            org=self._get_exporter_org(org_id=org_id),
        )

    def get_project_organization_created_data(self) -> ProjectOrgCreatedData:
        return get_project_organization_create_data(project_id=self.project_id, org_id=self.org_id)


class WorkProgressVendorScopeElementRepo(
    WorkProgressRepo,
    WorkProgressVendorScopeElementAbstractRepo,
):
    def __init__(self, user_id: int, project_id: int, org_id: int, vendor_ids: list[int]):
        super().__init__(
            user_id=user_id,
            project_id=project_id,
            org_id=org_id,
        )
        self.vendor_ids = vendor_ids

    def get_elements_base_selector(self):
        return (
            WorkProgressElement.objects.get_queryset()
            .select_related(
                "boq_element",
                "boq_element__order_element__vendor_order__org_from",
                "boq_element__organization",
            )
            .annotate_current_element_data()
            .filter(
                boq_element__boq_id=self.project_id,
                boq_element__deleted_at=None,
                boq_element__element_status__in=[
                    BoqElementStatus.DRAFT.value,
                    BoqElementStatus.APPROVED.value,
                    BoqElementStatus.CHANGE_REQUESTED.value,
                    BoqElementStatus.REQUESTED.value,
                    BoqElementStatus.CANCELLATION_REQUESTED.value,
                ],
                boq_element__organization_id__in=self.vendor_ids,
                boq_element__order_element__vendor_order__org_from_id=self.org_id,
            )
        )

    def get_element_paginated_list(
        self,
        filter_data: VendorScopeElementListFilterEntity,
    ) -> PaginatedVendorScopeElementListEntity:
        elements = (
            self.get_elements_base_selector()
            .select_related(
                "boq_element__organization",
                "boq_element__category",
                "boq_element__item_type",
                "boq_element__proposal_element",
                "boq_element__main_preview_file",
            )
            .annotate_saved_final_amount_with_tax()
            .annotate_order_number()
            .annotate_proposal_hidden_fields()
            .annotate(
                amount=F("element_final_amount_with_tax"),
                order_rate=F("boq_element__client_rate"),
            )
            .order_by("boq_element__organization__name")
        )

        total_count, filtered_elements = get_filter_pydantic_queryset_data(
            queryset=elements, filter_data=filter_data, filterset=VendorScopeElementFilterSet
        )
        entities = [self._prepare_vendor_scope_element_list_data_entity(element) for element in filtered_elements]
        return PaginatedVendorScopeElementListEntity(count=total_count, data=entities)


class WorkProgressDayWiseRepo(WorkProgressDayWiseAbstractRepo):
    def __init__(self, user_id: int, project_id: int, org_id: int):
        self.user_id = user_id
        self.project_id = project_id
        self.org_id = org_id
        self.current_time = get_local_time(timezone.now())
        self.current_date = get_local_time(timezone.now()).date()

    def update_project_daywise_data(
        self,
        data: ScopeProgressEntity,
    ):
        logger.info("Updating project daywise data.", project_id=self.project_id, org_id=self.org_id)
        WorkProgressProjectDayWiseData.objects.update_or_create(
            project_id=self.project_id,
            organization_id=self.org_id,
            date=self.current_date,
            defaults={
                "day_end_data": data.model_dump(mode="json"),
                "date": self.current_date,
                "project_id": self.project_id,
                "organization_id": self.org_id,
                "updated_by_id": self.user_id,
                "updated_at": self.current_time,
            },
        )

    def update_section_daywise_data(
        self,
        data: list[SectionNameTotalProgressEntity],
    ):
        logger.info("Updating section daywise data.", project_id=self.project_id, org_id=self.org_id)
        WorkProgressSectionDayWiseData.objects.update_or_create(
            project_id=self.project_id,
            organization_id=self.org_id,
            date=self.current_date,
            defaults={
                "day_end_data": [d.model_dump(mode="json") for d in data],
                "date": self.current_date,
                "project_id": self.project_id,
                "organization_id": self.org_id,
                "updated_by_id": self.user_id,
                "updated_at": self.current_time,
            },
        )

    def update_element_daywise_data(self, data: list[UpdateElementOutputEntity]):
        updated_element_ids = [element.updated_element_entity.id for element in data]
        logger.info("Updating element daywise data.", element_ids=updated_element_ids)

        existing_elements = WorkProgressElementDayWiseData.objects.filter(
            element_id__in=updated_element_ids,
            date=self.current_date,
        ).values("id", "element_id")

        existing_element_ids = [element["element_id"] for element in existing_elements]
        element_id_to_object_id_map = {element["element_id"]: element["id"] for element in existing_elements}

        to_create: list[UpdateElementOutputEntity] = []
        to_update: list[UpdateElementOutputEntity] = []
        for element in data:
            if element.updated_element_entity.id in existing_element_ids:
                to_update.append(element)
            else:
                to_create.append(element)
        if to_create:
            WorkProgressElementDayWiseData.objects.bulk_create(
                [
                    WorkProgressElementDayWiseData(
                        element_id=element.updated_element_entity.id,
                        date=self.current_date,
                        day_end_progress_percentage=element.updated_element_entity.actual_progress_percentage,
                        latest_timeline_id=element.timeline_id,
                        day_end_uom=element.updated_element_entity.uom.value,
                        day_end_uom_name=element.updated_element_entity.uom.name,
                        day_end_milestone_name=element.updated_element_entity.current_input_milestone_name,
                        updated_by_id=self.user_id,
                        updated_at=self.current_time,
                    )
                    for element in to_create
                ]
            )

        if to_update:
            WorkProgressElementDayWiseData.objects.bulk_update(
                [
                    WorkProgressElementDayWiseData(
                        id=element_id_to_object_id_map.get(element.updated_element_entity.id),
                        element_id=element.updated_element_entity.id,
                        date=self.current_date,
                        day_end_progress_percentage=element.updated_element_entity.actual_progress_percentage,
                        latest_timeline_id=element.timeline_id,
                        day_end_uom=element.updated_element_entity.uom.value,
                        day_end_uom_name=element.updated_element_entity.uom.name,
                        day_end_milestone_name=element.updated_element_entity.current_input_milestone_name,
                        updated_by_id=self.user_id,
                        updated_at=self.current_time,
                    )
                    for element in to_update
                ],
                fields=[
                    "day_end_progress_percentage",
                    "latest_timeline_id",
                    "day_end_uom",
                    "day_end_uom_name",
                    "day_end_milestone_name",
                    "updated_by_id",
                    "updated_at",
                ],
            )
