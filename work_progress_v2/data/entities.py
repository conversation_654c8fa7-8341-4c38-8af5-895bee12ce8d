import datetime
import decimal
from typing import Literal, Optional

from boq.data.choices import BoqElementStatus
from common.pydantic.base_model import BaseModelV2, PydanticPaginatedBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.data.choices import (
    ElementUpdatedAttributeChoices,
    WorkProgressElementActionChoices,
)
from work_progress_v2.domain.enums import WorkProgressElementAttachmentPermissionActionEnum


class ScopeElementBaseEntity(BaseModelV2):
    id: HashIdInt
    section_id: HashIdInt | None
    quantity: decimal.Decimal
    amount: decimal.Decimal
    progress_percentage: decimal.Decimal
    previous_day_progress_percentage: decimal.Decimal | None
    progress_updated_at: datetime.datetime | None
    attachment_uploaded_at: datetime.datetime | None
    update_method: ItemTypeUpdateMethodChoices | None
    item_type_id: HashIdInt | None


class ScopeTimelineDatesDataEntity(BaseModelV2):
    expected_start_date: datetime.date | None
    expected_due_date: datetime.date | None
    scope_start_date: datetime.date | None
    scope_completion_date: datetime.date | None


class ElementCategoryDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str


class ElementStatusDataEntity(BaseModelV2):
    name: str
    value: str


class ElementUomDataEntity(BaseModelV2):
    name: str
    value: int


class ElementItemTypeDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    color_code: str | None


class WPElementBaseEntity(BaseModelV2):
    id: int
    section_id: int | None
    quantity: decimal.Decimal
    amount: decimal.Decimal
    progress_percentage: decimal.Decimal
    input_progress_percentage: decimal.Decimal | None
    input_progress_quantity: decimal.Decimal | None
    previous_day_progress_percentage: decimal.Decimal | None  # last updated day progress percentage
    previous_day_input_progress_percentage: decimal.Decimal | None  # last updated day INPUT progress percentage
    previous_day_input_progress_quantity: decimal.Decimal | None  # last updated day INPUT progress quantity
    previous_day_input_progress_milestone_id: HashIdInt | None  # last updated day INPUT milestone id
    element_status: BoqElementStatus
    last_day_progress_updated_at: datetime.datetime | None
    progress_updated_at: datetime.datetime | None
    unlocked_at: datetime.datetime | None
    update_method: ItemTypeUpdateMethodChoices | None
    uom_id: int
    item_type_id: int | None


class ElementSectionDataEntity(BaseModelV2):
    id: Optional[HashIdInt] = None
    name: str


class ElementPreviewFileDataEntity(BaseModelV2):
    id: HashIdInt
    file: str
    name: str
    is_main: bool
    type: str


class ElementListDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    code: str
    description: str
    quantity: decimal.Decimal
    item_type: ElementItemTypeDataEntity | None = None
    status: ElementStatusDataEntity
    uom: ElementUomDataEntity
    category: ElementCategoryDataEntity | None = None
    section: ElementSectionDataEntity
    rate: decimal.Decimal
    actual_progress_percentage: decimal.Decimal
    current_input_quantity: decimal.Decimal | None = None
    current_input_progress: decimal.Decimal | None = None
    current_input_milestone_id: HashIdInt | None = None
    previous_day_input_quantity: decimal.Decimal | None = None
    previous_day_input_progress: decimal.Decimal | None = None
    previous_day_input_milestone_id: HashIdInt | None = None
    update_method: str | None = None
    comment_count: int
    preview_file: ElementPreviewFileDataEntity | None = None
    progress_updated_at: Optional[datetime.datetime] = None
    created_by_org_id: HashIdInt
    unlocked_at: datetime.datetime | None = None
    last_day_progress_updated_at: datetime.datetime | None = None


class ElementAttachmentDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    file: CustomFileUrlStr
    thumbnail_file: CustomFileUrlStr | None = None
    uploaded_at: datetime.datetime
    actions: list[WorkProgressElementAttachmentPermissionActionEnum]


class ElementAttachmentCreateDataEntity(BaseModelV2):
    name: str
    file: CustomFileUrlStr
    thumbnail_file: CustomFileUrlStr | None = None


class ElementDetailDataEntity(ElementListDataEntity):
    preview_files: list[ElementPreviewFileDataEntity] = []
    attachments: list[ElementAttachmentDataEntity] = []


class PaginatedElementListDataEntity(PydanticPaginatedBaseModel):
    data: list[ElementListDataEntity]


class MilestoneDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    percentage: decimal.Decimal
    is_visible: bool


class ItemTypeConfigWithMilestonesDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    milestones: list[MilestoneDataEntity]
    update_methods: list[ItemTypeUpdateMethodChoices]
    default_update_method: ItemTypeUpdateMethodChoices


class UserDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: str | None


class QuantityProgressUpdateData(BaseModelV2):
    action: Literal[
        WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED,
        WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED,
    ]
    data: decimal.Decimal


class ElementAttributeUpdateData(BaseModelV2):
    action: Literal[
        WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED,
        WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED,
        WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED,
        WorkProgressElementActionChoices.ATTACHMENT_UPDATED,
    ]
    data: str


class UpdatedDataEntity(BaseModelV2):
    updated_data: str | int | decimal.Decimal | None
    previous_data: str | int | decimal.Decimal | None
    updated_attribute: ElementUpdatedAttributeChoices | None


class HistoryTimelineDataEntity(BaseModelV2):
    id: HashIdInt
    action: WorkProgressElementActionChoices
    data: UpdatedDataEntity
    created_at: datetime.datetime
    created_by: UserDataEntity


class TimelineHistoryOutputEntity(BaseModelV2):
    created_timeline_objects: list
    created_input_quantity_history_objects: list
    created_input_progress_percentage_history_objects: list
    created_milestone_history_objects: list
    created_update_method_history_objects: list
    # created_attachment_history_objects: list
    created_timeline_and_action_history_objects: list
    created_percentage_history_objects: list


class ElementIdTimelineIdDataEntity(BaseModelV2):
    element_id: HashIdInt
    timeline_id: HashIdInt
