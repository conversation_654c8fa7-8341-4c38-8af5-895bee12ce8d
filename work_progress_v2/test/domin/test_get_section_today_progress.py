import datetime
import decimal
import unittest
from unittest.mock import MagicMock, patch

from django.utils import timezone

from work_progress_v2.domain.helper import WorkProgressScopeDataHelper
from work_progress_v2.domain.helper_entities import SectionTodayProgressElementEntity


class TestCases(unittest.TestCase):
    def setUp(self):
        self.repo = MagicMock()
        self.user_entity = MagicMock()
        self.service = WorkProgressScopeDataHelper()
        self.today = timezone.now().date()
        self.yesterday = self.today - datetime.timedelta(days=1)

    def test_empty_elements_list(self):
        """Test with an empty elements list should return an empty list."""
        result = self.service.get_section_today_progress([])
        assert result == []

    def test_single_section_zero_quantity(self):
        """Test with elements that have zero quantity - should use average progress."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal(100),
                progress_percentage=decimal.Decimal(40),
                previous_day_progress_percentage=decimal.Decimal(30),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            SectionTodayProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal(200),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].id == 1
        self.assertTrue(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal(10)  # (40-30 + 60-50) / 2
        assert result[0].total_today_updated_element_count == 2

    def test_single_section_zero_amount(self):
        """Test with elements that have zero amount - should return zero progress."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(0),
                progress_percentage=decimal.Decimal(40),
                previous_day_progress_percentage=decimal.Decimal(30),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].id == 1
        self.assertFalse(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal(0)
        assert result[0].total_amount == decimal.Decimal(0)

    def test_single_section_normal_elements(self):
        """Test with normal elements (quantity > 0, amount > 0)."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            SectionTodayProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(5),
                amount=decimal.Decimal(2000),
                progress_percentage=decimal.Decimal(40),
                previous_day_progress_percentage=decimal.Decimal(30),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].id == 1
        self.assertFalse(result[0].is_quantity_zero)
        # Weighted average: (1000*(60-50) + 2000*(40-30)) / 3000 * 100 = 100*10 + 200*10 / 3000 = 10
        assert result[0].progress_percentage == decimal.Decimal(10)
        assert result[0].total_completion_amount == decimal.Decimal(300)  # 100 + 200
        assert result[0].total_amount == decimal.Decimal(3000)  # 1000 + 2000

    def test_multiple_sections(self):
        """Test with elements in multiple sections."""
        elements = [
            # Section 1
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            # Section 2
            SectionTodayProgressElementEntity(
                id=2,
                section_id=2,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal(2000),
                progress_percentage=decimal.Decimal(40),
                previous_day_progress_percentage=decimal.Decimal(30),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            # Section 3
            SectionTodayProgressElementEntity(
                id=3,
                section_id=3,
                quantity=decimal.Decimal(5),
                amount=decimal.Decimal(0),
                progress_percentage=decimal.Decimal(80),
                previous_day_progress_percentage=decimal.Decimal(70),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 3
        # Check section 1 - normal case
        section1 = next(s for s in result if s.id == 1)
        self.assertFalse(section1.is_quantity_zero)
        assert section1.progress_percentage == decimal.Decimal(10)  # (60-50) = 10

        # Check section 2 - quantity zero case
        section2 = next(s for s in result if s.id == 2)
        self.assertTrue(section2.is_quantity_zero)
        assert section2.progress_percentage == decimal.Decimal(10)  # (40-30) = 10

        # Check section 3 - amount zero case
        section3 = next(s for s in result if s.id == 3)
        self.assertFalse(section3.is_quantity_zero)
        assert section3.progress_percentage == decimal.Decimal(0)  # Amount = 0

    def test_progress_not_updated_today(self):
        """Test with elements that weren't updated self.today."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.yesterday, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].progress_percentage == decimal.Decimal(0)  # Not updated self.today
        assert result[0].total_today_updated_element_count == 0

    def test_attachment_updated_today(self):
        """Test with elements where only attachment was updated self.today."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=None,
                attachment_uploaded_at=datetime.datetime.combine(self.today, datetime.time()),
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].progress_percentage == decimal.Decimal(0)  # Progress wasn't updated
        assert result[0].total_today_updated_element_count == 1  # But element is counted as updated

    def test_null_section_id(self):
        """Test handling of elements with None section_id."""
        elements = [
            SectionTodayProgressElementEntity(
                id=1,
                section_id=None,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        with patch("boq.data.models.BoqSection.DEFAULT_SECTION_ID", 0):
            result = self.service.get_section_today_progress(elements)

        assert len(result) == 1
        assert result[0].id == 0  # Should use DEFAULT_SECTION_ID

    def test_complex_mixed_case(self):
        """Test with a complex mix of various element types and conditions."""
        elements = [
            # Section 1 - mix of updated and not updated elements
            SectionTodayProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal(1000),
                progress_percentage=decimal.Decimal(60),
                previous_day_progress_percentage=decimal.Decimal(50),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            SectionTodayProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(5),
                amount=decimal.Decimal(2000),
                progress_percentage=decimal.Decimal(40),
                previous_day_progress_percentage=decimal.Decimal(30),
                progress_updated_at=datetime.datetime.combine(self.yesterday, datetime.time()),
                attachment_uploaded_at=datetime.datetime.combine(self.today, datetime.time()),
            ),
            # Section 2 - mix of zero and non-zero quantity elements
            SectionTodayProgressElementEntity(
                id=3,
                section_id=2,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal(3000),
                progress_percentage=decimal.Decimal(80),
                previous_day_progress_percentage=decimal.Decimal(70),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
            SectionTodayProgressElementEntity(
                id=4,
                section_id=2,
                quantity=decimal.Decimal(15),
                amount=decimal.Decimal(4000),
                progress_percentage=decimal.Decimal(30),
                previous_day_progress_percentage=decimal.Decimal(20),
                progress_updated_at=datetime.datetime.combine(self.today, datetime.time()),
                attachment_uploaded_at=None,
            ),
        ]

        result = self.service.get_section_today_progress(elements)

        assert len(result) == 2

        # Check section 1
        section1 = next(s for s in result if s.id == 1)
        assert section1.total_today_updated_element_count == 2  # Both elements count (one progress, one attachment)
        assert section1.total_completion_amount == decimal.Decimal(100)  # Only from element 1

        # Check section 2
        section2 = next(s for s in result if s.id == 2)
        assert section2.total_today_updated_element_count == 2  # Both elements had progress updates
        assert section2.total_completion_amount == decimal.Decimal(400)  # Only from element 4
