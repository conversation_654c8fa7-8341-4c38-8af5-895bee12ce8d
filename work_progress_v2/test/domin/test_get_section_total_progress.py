import decimal
import unittest
from unittest.mock import MagicMock

from work_progress_v2.domain.helper import WorkProgressScopeDataHelper
from work_progress_v2.domain.helper_entities import SectionTotalProgressElementEntity


class TestCases(unittest.TestCase):
    def setUp(self) -> None:
        self.repo = MagicMock()
        self.user_entity = MagicMock()
        self.service = WorkProgressScopeDataHelper()

    def test_empty_list(self):
        """Test with empty elements list"""
        result = self.service.get_section_total_progress([])
        assert result == []

    def test_single_section(self):
        """Test with single section elements"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("50.00"),
            ),
            SectionTotalProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(5),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 1
        assert result[0].id == 1
        self.assertFalse(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal(
            "58.33333333333333333333333333"
        )  # (500*75 + 1000*50)/(1500)

    def test_multiple_sections(self):
        """Test with multiple sections"""
        elements = [
            # Section 1
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("50.00"),
            ),
            # Section 2
            SectionTotalProgressElementEntity(
                id=2,
                section_id=2,
                quantity=decimal.Decimal(5),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 2
        assert {r.id for r in result} == {1, 2}
        self.assertFalse(result[0].is_quantity_zero)
        self.assertFalse(result[1].is_quantity_zero)
        assert next(r.progress_percentage for r in result if r.id == 1) == decimal.Decimal("50.00")
        assert next(r.progress_percentage for r in result if r.id == 2) == decimal.Decimal("75.00")

    def test_zero_quantity(self):
        """Test when quantity is zero"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("50.00"),
            ),
            SectionTotalProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
            SectionTotalProgressElementEntity(
                id=3,
                section_id=2,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 2
        self.assertTrue(result[0].is_quantity_zero)
        self.assertTrue(result[1].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal("62.50")  # (50 + 75)/2
        assert result[1].progress_percentage == decimal.Decimal("75.00")  # (75)/1

    def test_zero_amount(self):
        """Test when amount is zero"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("0.00"),
                progress_percentage=decimal.Decimal("50.00"),
            ),
            SectionTotalProgressElementEntity(
                id=2,
                section_id=2,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("0.00"),
                progress_percentage=decimal.Decimal("70.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 2
        self.assertFalse(result[0].is_quantity_zero)
        self.assertTrue(result[1].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal("0.00")
        assert result[1].progress_percentage == decimal.Decimal("70.00")

    def test_null_section(self):
        """Test with null section ID"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=None,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("50.00"),
            )
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 1
        assert result[0].id == 0
        self.assertFalse(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal("50.00")

    def test_single_non_zero_quantity(self):
        """Test when quantity is not zero in one of the element"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("10.00"),
            ),
            SectionTotalProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
            SectionTotalProgressElementEntity(
                id=3,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 1
        self.assertFalse(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal("75.00")  # (500*75)/(500)

    def test_multi_non_zero_quantity(self):
        """Test when quantity is not zero in one of the element"""
        elements = [
            SectionTotalProgressElementEntity(
                id=1,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("1000.00"),
                progress_percentage=decimal.Decimal("10.00"),
            ),
            SectionTotalProgressElementEntity(
                id=2,
                section_id=1,
                quantity=decimal.Decimal(10),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
            SectionTotalProgressElementEntity(
                id=3,
                section_id=1,
                quantity=decimal.Decimal(0),
                amount=decimal.Decimal("500.00"),
                progress_percentage=decimal.Decimal("75.00"),
            ),
        ]

        result = self.service.get_section_total_progress(elements)

        assert len(result) == 1
        self.assertFalse(result[0].is_quantity_zero)
        assert result[0].progress_percentage == decimal.Decimal(
            "31.66666666666666666666666667"
        )  # (500*75 + 1000*10)/(1500)
