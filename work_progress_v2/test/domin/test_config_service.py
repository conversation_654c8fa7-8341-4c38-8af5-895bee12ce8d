import unittest
from unittest.mock import MagicMock

from work_progress_v2.domain.entities import ReportConfigEntity
from work_progress_v2.domain.enums import WorkProgressReportConfigIdEnum
from work_progress_v2.domain.services.config import WorkProgressBaseConfigService


def create_sample_config():
    return [
        ReportConfigEntity(
            id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
            name="Scope Update",
            mandatory=False,
            checked=True,
            children=[
                ReportConfigEntity(
                    id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                    name="Scope Summary",
                    mandatory=False,
                    checked=True,
                    children=[],
                ),
                ReportConfigEntity(
                    id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                    name="Item wise Update",
                    mandatory=False,
                    checked=True,
                    children=[
                        ReportConfigEntity(
                            id=WorkProgressReportConfigIdEnum.DAILY_LOG,
                            name="Daily Log",
                            mandatory=False,
                            checked=True,
                            children=[],
                        ),
                        ReportConfigEntity(
                            id=WorkProgressReportConfigIdEnum.TODAY_UPDATE,
                            name="Daily Log",
                            mandatory=False,
                            checked=True,
                            children=[
                                ReportConfigEntity(
                                    id=WorkProgressReportConfigIdEnum.DAILY_LOG,
                                    name="Daily Log",
                                    mandatory=False,
                                    checked=True,
                                    children=[],
                                ),
                            ],
                        ),
                    ],
                ),
            ],
        )
    ]


class TestCases(unittest.TestCase):
    def setUp(self) -> None:
        self.permission_service = MagicMock()
        self.service = WorkProgressBaseConfigService(org_id=1, permission_service=self.permission_service)

    def test_base_root_level_validate_config_with_empty_config(self):
        result = self.service.base_root_level_validate_config([])

        self.assertTrue(result)

    def test_base_root_level_validate_config_with_valid_config(self):
        config = create_sample_config()

        result = self.service.base_root_level_validate_config(config)

        self.assertTrue(result)

    def test_base_root_level_validate_config_parent_unchecked(self):
        config = create_sample_config()
        config[0].checked = False

        result = self.service.base_root_level_validate_config(config)

        self.assertFalse(result)

    def test_base_root_level_validate_config_children_unchecked(self):
        config = create_sample_config()
        config[0].children[0].checked = False
        config[0].children[1].checked = False

        result = self.service.base_root_level_validate_config(config)

        self.assertFalse(result)

    def _validate_second_level_config(self, config: list[ReportConfigEntity]):
        result = True
        for section in config:
            for child in section.children:
                result = self.service.base_second_level_validate_config(child)

                if not result:
                    return False

        return result

    def test_base_second_level_validate_config_with_empty_config(self):
        result = self._validate_second_level_config([])

        self.assertTrue(result)

    def test_base_second_level_validate_config_with_valid_config_1(self):
        config = create_sample_config()

        result = self._validate_second_level_config(config)

        self.assertTrue(result)

    def test_base_second_level_validate_config_with_valid_config_2(self):
        config = create_sample_config()
        config[0].children[0].checked = False

        result = self._validate_second_level_config(config)

        self.assertTrue(result)

    def test_base_second_level_validate_config_with_valid_config_3(self):
        config = create_sample_config()
        config[0].children[0].checked = False
        config[0].children[1].children[0].checked = False

        result = self._validate_second_level_config(config)

        self.assertTrue(result)

    def test_base_second_level_validate_config_with_invalid_config_1(self):
        config = create_sample_config()
        config[0].children[1].checked = False

        result = self._validate_second_level_config(config)

        self.assertFalse(result)

    def test_base_second_level_validate_config_with_invalid_config_2(self):
        config = create_sample_config()
        config[0].children[1].children[1].checked = False

        result = self._validate_second_level_config(config)

        self.assertFalse(result)

    def test_base_second_level_validate_config_with_invalid_config_3(self):
        config = create_sample_config()
        config[0].children[1].children[1].children[0].checked = False

        result = self._validate_second_level_config(config)

        self.assertTrue(result)

    def test_base_validate_config_with_empty_config(self):
        result = self.service.base_validate_config([])

        self.assertTrue(result)

    def test_base_validate_config_with_valid_config(self):
        config = create_sample_config()

        result = self.service.base_validate_config(config)

        self.assertTrue(result)

    def test_base_validate_config_with_invalid_config(self):
        config = create_sample_config()
        config[0].children[0].checked = False
        config[0].children[1].checked = False

        result = self.service.base_validate_config(config)

        self.assertFalse(result)

    def test_base_validate_config_with_invalid_config_2(self):
        config = create_sample_config()
        config[0].children[0].checked = False
        config[0].children[1].children[1].checked = False

        result = self.service.base_validate_config(config)

        self.assertFalse(result)
