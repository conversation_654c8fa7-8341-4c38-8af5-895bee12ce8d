import structlog

from common.excel.constants import SheetType<PERSON>num
from common.excel.data_builder import ExcelDataBuilder
from common.excel.entities import <PERSON>er<PERSON>ell, SheetCell, SheetRowData
from project_schedule.domain.utils import get_delay_days
from project_schedule.excel.constants import ActivityExcelColumnEnum
from project_schedule.excel.entities import (
    ProjectScheduleActivityExcelEntity,
    ScheduleExcelExportInputData,
    ScheduleExcelSheetData,
)

logger = structlog.get_logger(__name__)


class ProjectScheduleActivityExcelDataBuilder(ExcelDataBuilder):
    _activity_cols: list[ActivityExcelColumnEnum]
    _activity_col_display_text_mapping = {
        ActivityExcelColumnEnum.WBS: "WBS",
        ActivityExcelColumnEnum.ACTIVITY_NAME: "Activity Name",
        ActivityExcelColumnEnum.PLANNED_START_DATE: "Planned Start Date",
        ActivityExcelColumnEnum.PLANNED_END_DATE: "Planned End Date",
        ActivityExcelColumnEnum.DURATION: "Duration",
        ActivityExcelColumnEnum.ACTUAL_START_DATE: "Actual Start Date",
        ActivityExcelColumnEnum.ACTUAL_END_DATE: "Actual End Date",
        ActivityExcelColumnEnum.PROJECT_START_DATE: "Projected Start Date",
        ActivityExcelColumnEnum.PROJECT_END_DATE: "Projected End Date",
        ActivityExcelColumnEnum.PROGRESS: "Progress %",
        ActivityExcelColumnEnum.STATUS: "Status",
        ActivityExcelColumnEnum.DEPENDENCY: "Dependency",
        ActivityExcelColumnEnum.SLACK: "Slack",
        ActivityExcelColumnEnum.CRITICAL_PATH: "Critical Path",
    }
    _root_uuid: str = None
    _activities: list[ProjectScheduleActivityExcelEntity] = []
    _activity_uuid_wbs_mapping: dict = {}

    def __init__(self, data: ScheduleExcelExportInputData):
        super().__init__()
        self._activity_cols = []
        self._root_uuid = data.root_uuid
        self._activities = data.activities
        self._activity_uuid_wbs_mapping = {activity.uuid: activity.wbs for activity in self._activities}
        logger.info("Schedule excel export data", data=data)

    def get_file_name(self) -> str:
        return "Project_Schedule_Activities.xlsx"

    def add_activity_cols(self, *cols: ActivityExcelColumnEnum):
        self._activity_cols.extend(cols)
        return self

    def validate(self):
        return super().validate()

    def get_sheets_data(self) -> list[ScheduleExcelSheetData]:
        sheets: list[ScheduleExcelSheetData] = []
        for sheet in self._sheets:
            if sheet == SheetTypeEnum.ACTIVITY:
                sheets.append(self.get_activity_sheet())
        return sheets

    def get_activity_sheet(self) -> ScheduleExcelSheetData:
        return ScheduleExcelSheetData(
            title="Activity Schedule",
            headers=self.get_activity_sheet_headers(),
            rows=self.get_activity_sheet_rows(),
        )

    def get_activity_sheet_headers(self):
        all_headers = self.get_activity_sheet_all_headers()
        headers = {}
        for key, value in all_headers.items():
            if key in self._activity_cols:
                headers[key] = value
        return headers

    def get_activity_sheet_all_headers(self) -> dict[ActivityExcelColumnEnum, HeaderCell]:
        all_headers = {}
        for key, value in self._activity_col_display_text_mapping.items():
            all_headers[key] = HeaderCell(value)
        return all_headers

    def get_activity_sheet_rows(self) -> list[SheetRowData]:
        rows: list[SheetRowData] = []
        for activity in self._activities:
            if activity.uuid == self._root_uuid:
                continue
            row_data = self.get_activity_row_data(activity=activity)
            rows.append(row_data)
        return rows

    def get_activity_row_data(self, activity: ProjectScheduleActivityExcelEntity) -> SheetRowData:
        row_data = {}
        for col in self._activity_cols:
            row_data[col] = self.get_activity_cell_data(activity=activity, col=col)
        return SheetRowData(data=row_data)

    def get_activity_cell_data(
        self, activity: ProjectScheduleActivityExcelEntity, col: ActivityExcelColumnEnum
    ) -> SheetCell:
        if col == ActivityExcelColumnEnum.WBS:
            return SheetCell(activity.wbs)
        elif col == ActivityExcelColumnEnum.ACTIVITY_NAME:
            return SheetCell(activity.name)
        elif col == ActivityExcelColumnEnum.PLANNED_START_DATE:
            return SheetCell(activity.planned_start_date.strftime("%Y/%m/%d") if activity.planned_start_date else "")
        elif col == ActivityExcelColumnEnum.PLANNED_END_DATE:
            return SheetCell(activity.planned_end_date.strftime("%Y/%m/%d") if activity.planned_end_date else "")
        elif col == ActivityExcelColumnEnum.DURATION:
            return SheetCell(f"{activity.duration}d")
        elif col == ActivityExcelColumnEnum.ACTUAL_START_DATE:
            return SheetCell(activity.actual_start_date.strftime("%Y/%m/%d") if activity.actual_start_date else "")
        elif col == ActivityExcelColumnEnum.ACTUAL_END_DATE:
            return SheetCell(activity.actual_end_date.strftime("%Y/%m/%d") if activity.actual_end_date else "")
        elif col == ActivityExcelColumnEnum.PROJECT_START_DATE:
            return SheetCell(
                activity.projected_start_date.strftime("%Y/%m/%d") if activity.projected_start_date else ""
            )
        elif col == ActivityExcelColumnEnum.PROJECT_END_DATE:
            return SheetCell(activity.projected_end_date.strftime("%Y/%m/%d") if activity.projected_end_date else "")
        elif col == ActivityExcelColumnEnum.PROGRESS:
            return SheetCell(str(int(activity.completion_percent)) + "%")
        elif col == ActivityExcelColumnEnum.STATUS:
            delay_days = get_delay_days(
                activity_status=activity.status,
                planned_end_date=activity.planned_end_date,
                projected_end_date=activity.projected_end_date,
                projected_start_date=activity.projected_start_date,
            )
            if delay_days > 0:
                return SheetCell(f"{activity.status.get_visible_text()} by {delay_days}d")
            return SheetCell(activity.status.get_visible_text())
        elif col == ActivityExcelColumnEnum.DEPENDENCY:
            dependency_strs = []
            for dep in activity.dependencies:
                dependency_strs.append(
                    f"{self._activity_uuid_wbs_mapping.get(dep.dependency_uuid)} "
                    f"{dep.type.value} {'+' if dep.lag>=0 else ''} {dep.lag}d"
                )
            return SheetCell(
                ", ".join(dependency_strs) if dependency_strs else "",
            )
        elif col == ActivityExcelColumnEnum.SLACK:
            return SheetCell(f"{activity.slack}d")
        elif col == ActivityExcelColumnEnum.CRITICAL_PATH:
            return SheetCell("Yes" if activity.is_critical else "No")
        else:
            raise NotImplementedError(f"Activity column {col} not implemented")
