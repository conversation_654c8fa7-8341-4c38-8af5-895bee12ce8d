import collections
from functools import partial
from typing import List, Optional, Tuple

import structlog
from django.db.models import F
from django.db.transaction import on_commit
from django.utils import timezone

from common.entities import ObjectStatus
from common.events.constants import Events
from common.events.project_schedule import (
    ProjectScheduleActivitiesAssignedEventData,
    ProjectScheduleActivitiesCreateEventData,
    ProjectScheduleActivitiesDeletedEventData,
    ProjectScheduleActivitiesEditEventData,
    ProjectScheduleActivitiesUpdateEventData,
    ProjectScheduleCompletedEventData,
)
from common.events.services import trigger_event
from common.exceptions import BaseValidationError
from common.injector import Injectable, inject
from project_schedule.data.caches import ProjectScheduleCache
from project_schedule.data.models import (
    ProjectSchedule,
    ProjectScheduleActivity,
    ProjectScheduleActivityAssignee,
    ProjectScheduleActivityAttachment,
    ProjectScheduleActivityAttachmentsTagMapping,
    ProjectScheduleActivityDependencyMapping,
    ProjectScheduleActivityOrganization,
    ProjectScheduleActivityProgressAttachment,
)
from project_schedule.data.repositories import ProjectScheduleRepo
from project_schedule.domain.constants import SCHEDULE_ROOT_ID, ProjectScheduleActivityDetailEnum
from project_schedule.domain.entities import (
    CreateAndDeleteProgressAttachmentEntity,
    ProjectScheduleActivityAssigneeUpdateEntity,
    ProjectScheduleActivityAttachmentEntity,
    ProjectScheduleActivityAttachmentTagMappingUpdateEntity,
    ProjectScheduleActivityAttachmentUpdateEntity,
    ProjectScheduleActivityDataEntity,
    ProjectScheduleActivityDependencyEntity,
    ProjectScheduleActivityDependencyUpdateEntity,
    ProjectScheduleActivityEntity,
    ProjectScheduleActivityOrganizationUpdateEntity,
    ProjectScheduleActivityProgressAttachmentUpdateEntity,
    ProjectScheduleActivityUpdateEntity,
    ProjectScheduleUpdateEntity,
    ScheduleLogObjBuilderUpdateEntity,
)
from project_schedule.domain.exceptions import (
    ProjectScheduleActivityAssigneeException,
    ProjectScheduleActivityDataException,
    ProjectScheduleActivityDependencyMappingException,
    ProjectScheduleActivityOrganizationException,
)
from project_schedule.domain.log_builder import ActivityLogObjBuilder, ScheduleLogObjBuilder
from project_schedule.domain.logger import (
    ProjectScheduleLogger,
)

logger = structlog.get_logger(__name__)


class ProjectScheduleDBService(Injectable):
    class ProjectScheduleDBServiceException(BaseValidationError):
        pass

    def _create_and_update_assignees(self, assignees_to_be_created: list, assignees_to_be_updated: list):
        try:
            ProjectScheduleActivityAssignee.objects.bulk_create(objs=assignees_to_be_created)
            ProjectScheduleActivityAssignee.objects.bulk_update(
                objs=assignees_to_be_updated, fields=["deleted_at", "deleted_by_id"]
            )
        except ProjectScheduleActivityAssigneeException as e:
            logger.info("Error in creating or updating assignees", error=e.message)
            raise self.ProjectScheduleDBServiceException("Error in creating or updating assignees") from e

    def _create_and_update_organizations(self, organizations_to_be_created: list, organizations_to_be_updated: list):
        try:
            ProjectScheduleActivityOrganization.objects.bulk_create(objs=organizations_to_be_created)
            ProjectScheduleActivityOrganization.objects.bulk_update(
                objs=organizations_to_be_updated, fields=["deleted_at", "deleted_by_id"]
            )
        except ProjectScheduleActivityOrganizationException as e:
            logger.info("Error in creating or updating organizations", error=e.message)
            raise self.ProjectScheduleDBServiceException("Error in creating or updating organizations") from e

    def _create_and_update_dependencies(self, dependencies_to_be_created: list, dependencies_to_be_updated: list):
        try:
            ProjectScheduleActivityDependencyMapping.objects.bulk_create(objs=dependencies_to_be_created)
            ProjectScheduleActivityDependencyMapping.objects.bulk_update(
                objs=dependencies_to_be_updated,
                fields=["lag", "type", "deleted_at", "deleted_by_id", "updated_at", "updated_by_id"],
            )
        except ProjectScheduleActivityDependencyMappingException as e:
            logger.info("Error in creating or updating dependencies", error=e.message)
            raise self.ProjectScheduleDBServiceException("Error in creating or updating dependencies") from e

    def _create_and_update_attachments(self, attachments_to_be_created: list, attachments_to_be_updated: list):
        ProjectScheduleActivityAttachment.objects.bulk_create(objs=attachments_to_be_created)
        ProjectScheduleActivityAttachment.objects.bulk_update(
            objs=attachments_to_be_updated, fields=["name", "file", "deleted_at", "deleted_by_id"]
        )

    def _create_and_update_tag_mappings(self, tag_mappings_to_be_created: list, tag_mappings_to_be_updated: list):
        ProjectScheduleActivityAttachmentsTagMapping.objects.bulk_create(objs=tag_mappings_to_be_created)
        ProjectScheduleActivityAttachmentsTagMapping.objects.bulk_update(
            objs=tag_mappings_to_be_updated, fields=["deleted_at", "deleted_by_id"]
        )

    def _create_and_delete_progress_attachments(
        self,
        progress_attachments_to_be_created: list[ProjectScheduleActivityProgressAttachment],
        progress_attachments_to_be_deletion: list[ProjectScheduleActivityProgressAttachment],
    ) -> dict[int, CreateAndDeleteProgressAttachmentEntity]:
        created_progress_attachments = ProjectScheduleActivityProgressAttachment.objects.bulk_create(
            objs=progress_attachments_to_be_created
        )
        ProjectScheduleActivityProgressAttachment.objects.bulk_update(
            objs=progress_attachments_to_be_deletion, fields=["deleted_at", "deleted_by_id"]
        )

        attachments = collections.defaultdict(
            partial(CreateAndDeleteProgressAttachmentEntity, created_ids=[], deleted_ids=[])
        )

        for attachment in created_progress_attachments:
            attachments[attachment.activity_id].created_ids.append(attachment.pk)
        for attachment in progress_attachments_to_be_deletion:
            attachments[attachment.activity_id].deleted_ids.append(attachment.pk)

        return attachments

    def create_activities(
        self, input_activities: list[ProjectScheduleActivityUpdateEntity], project_schedule_id: int, user_id: int
    ) -> dict:
        activities_to_be_created = []
        for activity_entity in input_activities:
            activities_to_be_created.append(
                ProjectScheduleActivity(
                    uuid=activity_entity.uuid,
                    name=activity_entity.name,
                    wbs=activity_entity.wbs,
                    color=activity_entity.color,
                    planned_start_date=activity_entity.planned_start_date,
                    planned_end_date=activity_entity.planned_end_date,
                    duration=activity_entity.duration,
                    actual_start_date=activity_entity.actual_start_date,
                    actual_end_date=activity_entity.actual_end_date,
                    projected_start_date=activity_entity.projected_start_date,
                    projected_end_date=activity_entity.projected_end_date,
                    completion_percentage=activity_entity.completion_percent,
                    slack=activity_entity.slack,
                    is_critical=activity_entity.is_critical,
                    project_schedule_id=project_schedule_id,
                    created_by_id=user_id,
                    type=activity_entity.type.value,
                )
            )
        activities = ProjectScheduleActivity.objects.bulk_create(objs=activities_to_be_created, batch_size=50)
        activities_uuid_to_id_mapping = {str(activity.uuid): activity.id for activity in activities}

        return activities_uuid_to_id_mapping

    def _prepare_assignees_objs(
        self, input_assignees: list[ProjectScheduleActivityAssigneeUpdateEntity], user_id: int, input_activity_id: int
    ) -> Tuple[List[ProjectScheduleActivityAssignee], List[ProjectScheduleActivityAssignee], dict]:
        assignees_to_be_created = []
        assignees_to_be_updated = []

        user_id_to_assignee_obj_mapping = {}

        for input_assignee in input_assignees:
            if input_assignee.object_status == ObjectStatus.ADD:
                assignees_to_be_created.append(
                    ProjectScheduleActivityAssignee(
                        user_id=input_assignee.user_id,
                        activity_id=input_activity_id,
                        created_by_id=user_id,
                    )
                )
                user_id_to_assignee_obj_mapping[input_assignee.user_id] = assignees_to_be_created[-1]
            elif input_assignee.object_status == ObjectStatus.DELETE:
                assignees_to_be_updated.append(
                    ProjectScheduleActivityAssignee(
                        id=input_assignee.id,
                        deleted_at=timezone.now(),
                        deleted_by_id=user_id,
                    )
                )
        return assignees_to_be_created, assignees_to_be_updated, user_id_to_assignee_obj_mapping

    def _prepare_attachments_tags_mapping_data(
        self,
        input_tag_mappings: list[ProjectScheduleActivityAttachmentTagMappingUpdateEntity],
        user_id: int,
        input_attachment: ProjectScheduleActivityAttachment,
    ) -> Tuple[List[ProjectScheduleActivityAttachmentsTagMapping], List[ProjectScheduleActivityAttachmentsTagMapping]]:
        attachment_tag_mappings_to_be_created = []
        attachment_tag_mappings_to_be_updated = []

        for input_tag_mapping in input_tag_mappings:
            if input_tag_mapping.object_status == ObjectStatus.ADD:
                attachment_tag_mappings_to_be_created.append(
                    ProjectScheduleActivityAttachmentsTagMapping(
                        attachment=input_attachment,
                        tag_id=input_tag_mapping.tag_id,
                        created_by_id=user_id,
                    )
                )
            elif input_tag_mapping.object_status == ObjectStatus.DELETE:
                attachment_tag_mappings_to_be_updated.append(
                    ProjectScheduleActivityAttachmentsTagMapping(
                        id=input_tag_mapping.id, deleted_at=timezone.now(), deleted_by_id=user_id
                    )
                )
        return attachment_tag_mappings_to_be_created, attachment_tag_mappings_to_be_updated

    def _prepare_attachments_objs(
        self,
        input_attachments: list[ProjectScheduleActivityAttachmentUpdateEntity],
        saved_attachments: Optional[list[ProjectScheduleActivityAttachmentEntity]],
        user_id: int,
        input_activity_id: int,
    ) -> Tuple[
        List[ProjectScheduleActivityAttachment],
        List[ProjectScheduleActivityAttachment],
        List[ProjectScheduleActivityAttachmentsTagMapping],
        List[ProjectScheduleActivityAttachmentsTagMapping],
    ]:
        attachments_to_be_created = []
        attachments_to_be_updated = []

        tag_mappings_to_be_created = []
        tag_mappings_to_be_updated = []

        # Needed in case of deleting the entity
        saved_attachment_id_to_entity_mapping = {attachment.id: attachment for attachment in saved_attachments}

        for input_attachment in input_attachments:
            if input_attachment.object_status == ObjectStatus.ADD:
                attachments_to_be_created.append(
                    ProjectScheduleActivityAttachment(
                        name=input_attachment.name,
                        file=input_attachment.file,
                        activity_id=input_activity_id,
                        uuid=input_attachment.uuid,
                        uploaded_at=timezone.now(),
                        uploaded_by_id=user_id,
                    )
                )
                (
                    tmp_tag_mappings_to_be_created,
                    tmp_tag_mappings_to_be_updated,
                ) = self._prepare_attachments_tags_mapping_data(
                    input_tag_mappings=input_attachment.tag_mappings,
                    user_id=user_id,
                    input_attachment=attachments_to_be_created[-1],
                )
                tag_mappings_to_be_created.extend(tmp_tag_mappings_to_be_created)
                tag_mappings_to_be_updated.extend(tmp_tag_mappings_to_be_updated)
            elif input_attachment.object_status == ObjectStatus.UPDATE:
                attachments_to_be_updated.append(
                    ProjectScheduleActivityAttachment(
                        id=input_attachment.id,
                        name=input_attachment.name,
                        file=input_attachment.file,
                        updated_at=timezone.now(),
                        updated_by_id=user_id,
                    )
                )
                (
                    tmp_tag_mappings_to_be_created,
                    tmp_tag_mappings_to_be_updated,
                ) = self._prepare_attachments_tags_mapping_data(
                    input_tag_mappings=input_attachment.tag_mappings,
                    user_id=user_id,
                    input_attachment=attachments_to_be_updated[-1],
                )
                tag_mappings_to_be_created.extend(tmp_tag_mappings_to_be_created)
                tag_mappings_to_be_updated.extend(tmp_tag_mappings_to_be_updated)

            elif input_attachment.object_status == ObjectStatus.DELETE:
                attachments_to_be_updated.append(
                    ProjectScheduleActivityAttachment(
                        id=input_attachment.id,
                        name=input_attachment.name,
                        file=input_attachment.file,
                        updated_at=saved_attachment_id_to_entity_mapping.get(input_attachment.id).updated_at,
                        updated_by_id=saved_attachment_id_to_entity_mapping.get(input_attachment.id).updated_by_id,
                        deleted_at=timezone.now(),
                        deleted_by_id=user_id,
                    )
                )
                (
                    tmp_tag_mappings_to_be_created,
                    tmp_tag_mappings_to_be_updated,
                ) = self._prepare_attachments_tags_mapping_data(
                    input_tag_mappings=input_attachment.tag_mappings,
                    user_id=user_id,
                    input_attachment=attachments_to_be_updated[-1],
                )
                tag_mappings_to_be_created.extend(tmp_tag_mappings_to_be_created)
                tag_mappings_to_be_updated.extend(tmp_tag_mappings_to_be_updated)

        return (
            attachments_to_be_created,
            attachments_to_be_updated,
            tag_mappings_to_be_created,
            tag_mappings_to_be_updated,
        )

    def _prepare_dependencies_objs(
        self,
        input_dependencies: list[ProjectScheduleActivityDependencyUpdateEntity],
        saved_dependencies: Optional[list[ProjectScheduleActivityDependencyEntity]],
        user_id: int,
        input_activity_id: int,
        activities_uuid_to_id_mapping: dict,
    ) -> Tuple[List[ProjectScheduleActivityDependencyMapping], List[ProjectScheduleActivityDependencyMapping], dict]:
        dependencies_to_be_created = []
        dependencies_to_be_updated = []

        # Needed in case of deleting the entity
        saved_dependency_id_to_entity_mapping = {dependency.id: dependency for dependency in saved_dependencies}

        dependency_uuid_to_dependency_mapping_obj_mapping = {}

        for input_dependency in input_dependencies:
            if input_dependency.object_status == ObjectStatus.ADD:
                dependencies_to_be_created.append(
                    ProjectScheduleActivityDependencyMapping(
                        dependency_activity_id=activities_uuid_to_id_mapping.get(
                            str(input_dependency.dependency_uuid)
                        ),  # Use uuid to id mapping
                        lag=input_dependency.lag,
                        type=input_dependency.type.value,
                        uuid=input_dependency.uuid,
                        activity_id=input_activity_id,
                        created_at=timezone.now(),
                        created_by_id=user_id,
                    )
                )
                dependency_uuid_to_dependency_mapping_obj_mapping[input_dependency.dependency_uuid] = (
                    dependencies_to_be_created[-1]
                )
            elif input_dependency.object_status == ObjectStatus.UPDATE:
                dependencies_to_be_updated.append(
                    ProjectScheduleActivityDependencyMapping(
                        id=input_dependency.id,
                        dependency_activity_id=activities_uuid_to_id_mapping.get(str(input_dependency.dependency_uuid)),
                        uuid=input_dependency.uuid,
                        lag=input_dependency.lag,
                        type=input_dependency.type.value,
                        updated_at=timezone.now(),
                        updated_by_id=user_id,
                    )
                )
            elif input_dependency.object_status == ObjectStatus.DELETE:
                dependencies_to_be_updated.append(
                    ProjectScheduleActivityDependencyMapping(
                        id=input_dependency.id,
                        lag=input_dependency.lag,
                        type=input_dependency.type.value,
                        updated_at=saved_dependency_id_to_entity_mapping.get(input_dependency.id).updated_at,
                        updated_by_id=saved_dependency_id_to_entity_mapping.get(input_dependency.id).updated_by_id,
                        deleted_at=timezone.now(),
                        deleted_by_id=user_id,
                    )
                )
        return dependencies_to_be_created, dependencies_to_be_updated, dependency_uuid_to_dependency_mapping_obj_mapping

    def _prepare_activity_organizations_objs(
        self,
        input_organizations: list[ProjectScheduleActivityOrganizationUpdateEntity],
        user_id: int,
        input_activity_id: int,
    ) -> Tuple[List[ProjectScheduleActivityOrganization], List[ProjectScheduleActivityOrganization], dict]:
        organizations_to_be_created = []
        organizations_to_be_updated = []

        org_id_to_activity_org_obj_mapping = {}

        for input_organization in input_organizations:
            if input_organization.object_status == ObjectStatus.ADD:
                organizations_to_be_created.append(
                    ProjectScheduleActivityOrganization(
                        organization_id=input_organization.org_id,
                        activity_id=input_activity_id,
                        created_by_id=user_id,
                    )
                )
                org_id_to_activity_org_obj_mapping[input_organization.org_id] = organizations_to_be_created[-1]
            elif input_organization.object_status == ObjectStatus.DELETE:
                organizations_to_be_updated.append(
                    ProjectScheduleActivityOrganization(
                        id=input_organization.id,
                        deleted_at=timezone.now(),
                        deleted_by_id=user_id,
                    )
                )
        return organizations_to_be_created, organizations_to_be_updated, org_id_to_activity_org_obj_mapping

    def _prepare_progress_attachments_objs(
        self,
        input_progress_attachments: list[ProjectScheduleActivityProgressAttachmentUpdateEntity],
        user_id: int,
        input_activity_id: int,
        all_activities_id_to_progress_attachments_uuid_mapping: dict,
    ):
        attachment_uuids = all_activities_id_to_progress_attachments_uuid_mapping.get(input_activity_id, [])
        progress_attachments_to_be_created: list[ProjectScheduleActivityProgressAttachment] = []
        progress_attachments_to_be_deleted: list[ProjectScheduleActivityProgressAttachment] = []

        for input_progress_attachment in input_progress_attachments:
            if input_progress_attachment.object_status == ObjectStatus.ADD:
                if input_progress_attachment.uuid in attachment_uuids:
                    raise self.ProjectScheduleDBServiceException("Duplicate attachment found in the activity")
                else:
                    attachment_uuids.append(input_progress_attachment.uuid)

                instance = ProjectScheduleActivityProgressAttachment()

                instance.name = input_progress_attachment.name
                instance.file = input_progress_attachment.file
                instance.uuid = input_progress_attachment.uuid
                instance.activity_id = input_activity_id
                instance.uploaded_by_id = user_id
                instance.uploaded_at = timezone.now()

                progress_attachments_to_be_created.append(instance)

            elif input_progress_attachment.object_status == ObjectStatus.DELETE:
                assert input_progress_attachment.id is not None, "Id should be present"
                instance = ProjectScheduleActivityProgressAttachment()

                instance.pk = input_progress_attachment.id
                instance.deleted_at = timezone.now()
                instance.activity_id = input_activity_id
                instance.deleted_by_id = user_id

                progress_attachments_to_be_deleted.append(instance)

        return progress_attachments_to_be_created, progress_attachments_to_be_deleted

    def update_schedule(
        self,
        input_activities: list[ProjectScheduleActivityUpdateEntity],
        user_id: int,
        project_schedule_id: int,
        project_schedule_uuid: str,
        saved_activities: list[ProjectScheduleActivityEntity],
        is_auto_updated: bool,
    ) -> tuple[
        list[ProjectScheduleActivityUpdateEntity],
        ProjectScheduleActivityAssignee,
        dict[int, CreateAndDeleteProgressAttachmentEntity],
    ]:
        """
        This Function does the following
        * Maintain a global level mapping of uuid to id for all activities
        * Maintain a mapping of activity_id to multiple mappings of assignees,
        organizations, dependencies to get ids (PK) of these entities

        1. Create new activities that are not present in DB and get the
            mapping of uuid to id for new activities
        2. Traverse all the input activities (filling ids in those that are none)
            and prepare data for assignees, organizations, dependencies and attachments
        3. Update the activities
        4. Create and update assignees, organizations, dependencies and attachments
        5. Bulk update the activities and assignees, organizations, dependencies and attachments
        6. Update the input_activities with the ids of assignees, organizations,
            dependencies (Not Attachments as their ids are not required)
        """
        activities_to_be_created = []
        activities_to_be_updated = []
        activity_ids_to_be_deleted = []

        assignees_to_be_created = []
        assignees_to_be_updated = []

        attachments_to_be_created = []
        attachments_to_be_updated = []

        dependencies_to_be_created = []
        dependencies_to_be_updated = []

        organizations_to_be_created = []
        organizations_to_be_updated = []
        tag_mappings_to_be_created = []
        tag_mappings_to_be_updated = []

        progress_attachments_to_be_created = []
        progress_attachments_to_be_deleted = []

        all_activities_uuid_to_id_mapping = {}
        all_activities_id_to_dependencies_and_attachments_mapping = {}

        all_activities_id_to_progress_attachments_uuid_mapping = {}

        """
        Following is the structure of the activities_id_to_multiple_mappings dictionary ->
        { "activity_id": {"user_id_assignee_obj_mapping": {}, "org_id_org_obj_mapping": {},
            "dependency_uuid_dependency_mapping_obj_mapping": {}} }
        """
        activities_id_to_multiple_mappings = collections.defaultdict(
            lambda: {
                "user_id_assignee_obj_mapping": {},
                "org_id_org_obj_mapping": {},
                "dependency_uuid_dependency_mapping_obj_mapping": {},
            }
        )

        """
        To ensure each WBS is unique within a schedule:
        1. Use a set (activities_wbs) to store unique WBS values and a 
        list (seen_activity_ids) to track processed activity IDs.
        2. Handle three activity types:
            New Activities: Add their WBS to activities_wbs.
            Updated Activities: Add their WBS to activities_wbs if they are part of the input payload.
            Saved Activities: Add WBS for unchanged activities not in the input payload.
        3. Prevent duplicates by ensuring:
            WBS from updated activities doesn’t overlap with others using seen_activity_ids.
            New and saved activities do not conflict with input payload WBS.
        """
        seen_activities_wbs = list()
        seen_activity_ids = set()

        for input_activity in input_activities:
            if input_activity.uuid == project_schedule_uuid:
                continue
            if input_activity.object_status == ObjectStatus.ADD:
                activities_to_be_created.append(input_activity)
                seen_activities_wbs.append(input_activity.wbs)
            elif input_activity.object_status == ObjectStatus.DELETE:
                activity_ids_to_be_deleted.append(input_activity.id)
                seen_activity_ids.add(input_activity.id)
            else:
                seen_activity_ids.add(input_activity.id)
                seen_activities_wbs.append(input_activity.wbs)

        for saved_activity in saved_activities:
            all_activities_uuid_to_id_mapping[str(saved_activity.uuid)] = saved_activity.id
            all_activities_id_to_dependencies_and_attachments_mapping[saved_activity.id] = (
                ProjectScheduleActivityDataEntity(**saved_activity.model_dump(include={"dependencies", "attachments"}))
            )
            if saved_activity.id not in seen_activity_ids:
                if saved_activity.uuid == project_schedule_uuid:
                    continue
                seen_activity_ids.add(saved_activity.id)
                seen_activities_wbs.append(saved_activity.wbs)

            all_activities_id_to_progress_attachments_uuid_mapping[saved_activity.id] = [
                attachment.uuid for attachment in saved_activity.progress_attachments
            ]

        if len(seen_activities_wbs) != len(set(seen_activities_wbs)):
            raise self.ProjectScheduleDBServiceException("Duplicate WBS found in the schedule")

        # First deleted activities so that constraints will not break in create and update
        if activity_ids_to_be_deleted:
            ProjectScheduleActivity.objects.filter(id__in=activity_ids_to_be_deleted).update(
                deleted_at=timezone.now(), deleted_by_id=user_id
            )
        # Create new activities and get the mapping of uuid to id
        try:
            all_activities_uuid_to_id_mapping.update(
                self.create_activities(
                    input_activities=activities_to_be_created, user_id=user_id, project_schedule_id=project_schedule_id
                )
            )
        except ProjectScheduleActivityDataException as e:
            logger.info("Error in creating activities", error=e.message)
            raise self.ProjectScheduleDBServiceException("Error in creating activities") from e

        logger.info("New Activities Created")

        is_root_activity_present = False

        # Prepare data for assignees, organizations, dependencies and attachments
        for input_activity in input_activities:
            if input_activity.uuid == project_schedule_uuid:  # Ignore the Root activity
                is_root_activity_present = True
                is_schedule_completed = input_activity.completion_percent == 100
                schedule_planned_start_date = input_activity.planned_start_date
                schedule_planned_end_date = input_activity.planned_end_date
                schedule_actual_start_date = input_activity.actual_start_date
                schedule_actual_end_date = input_activity.actual_end_date
                schedule_projected_end_date = input_activity.projected_end_date
                schedule_projected_start_date = input_activity.projected_start_date
                schedule_completion_percentage = input_activity.completion_percent
                continue
            if input_activity.id is None:
                input_activity.id = all_activities_uuid_to_id_mapping.get(str(input_activity.uuid))

            input_activity.parent_id = all_activities_uuid_to_id_mapping.get(str(input_activity.parent_uuid), None)

            (
                activity_assignees_for_create,
                activity_assignees_for_update,
                activity_user_id_assignee_obj_mapping,
            ) = self._prepare_assignees_objs(
                input_assignees=input_activity.assignees, user_id=user_id, input_activity_id=input_activity.id
            )
            assignees_to_be_created.extend(activity_assignees_for_create)
            assignees_to_be_updated.extend(activity_assignees_for_update)
            activities_id_to_multiple_mappings[input_activity.id]["user_id_assignee_obj_mapping"].update(
                activity_user_id_assignee_obj_mapping
            )

            (
                activity_attachments_for_create,
                activity_attachments_for_update,
                activity_tag_mappings_to_be_created,
                activity_tag_mappings_to_be_updated,
            ) = self._prepare_attachments_objs(
                input_attachments=input_activity.attachments,
                user_id=user_id,
                input_activity_id=input_activity.id,
                saved_attachments=(
                    all_activities_id_to_dependencies_and_attachments_mapping.get(input_activity.id).attachments
                    if input_activity.id in all_activities_id_to_dependencies_and_attachments_mapping
                    else []
                ),
            )
            attachments_to_be_created.extend(activity_attachments_for_create)
            attachments_to_be_updated.extend(activity_attachments_for_update)

            tag_mappings_to_be_created.extend(activity_tag_mappings_to_be_created)
            tag_mappings_to_be_updated.extend(activity_tag_mappings_to_be_updated)

            (
                activity_progress_attachments_for_create,
                activity_progress_attachments_for_deletion,
            ) = self._prepare_progress_attachments_objs(
                input_progress_attachments=input_activity.progress_attachments,
                user_id=user_id,
                input_activity_id=input_activity.id,
                all_activities_id_to_progress_attachments_uuid_mapping=all_activities_id_to_progress_attachments_uuid_mapping,
            )
            progress_attachments_to_be_created.extend(activity_progress_attachments_for_create)
            progress_attachments_to_be_deleted.extend(activity_progress_attachments_for_deletion)

            (
                activity_dependencies_for_create,
                activity_dependencies_for_update,
                activity_dependency_uuid_dependency_mapping_obj_mapping,
            ) = self._prepare_dependencies_objs(
                input_dependencies=input_activity.dependencies,
                saved_dependencies=(
                    all_activities_id_to_dependencies_and_attachments_mapping.get(input_activity.id).dependencies
                    if input_activity.id in all_activities_id_to_dependencies_and_attachments_mapping
                    else []
                ),
                user_id=user_id,
                input_activity_id=input_activity.id,
                activities_uuid_to_id_mapping=all_activities_uuid_to_id_mapping,
            )
            dependencies_to_be_created.extend(activity_dependencies_for_create)
            dependencies_to_be_updated.extend(activity_dependencies_for_update)
            activities_id_to_multiple_mappings[input_activity.id][
                "dependency_uuid_dependency_mapping_obj_mapping"
            ].update(activity_dependency_uuid_dependency_mapping_obj_mapping)

            (
                activity_organizations_for_create,
                activity_organizations_for_update,
                activity_org_id_org_obj_mapping,
            ) = self._prepare_activity_organizations_objs(
                input_organizations=input_activity.organizations, user_id=user_id, input_activity_id=input_activity.id
            )
            organizations_to_be_created.extend(activity_organizations_for_create)
            organizations_to_be_updated.extend(activity_organizations_for_update)
            activities_id_to_multiple_mappings[input_activity.id]["org_id_org_obj_mapping"].update(
                activity_org_id_org_obj_mapping
            )

            activities_to_be_updated.append(
                ProjectScheduleActivity(
                    id=input_activity.id,
                    name=input_activity.name,
                    wbs=input_activity.wbs,
                    color=input_activity.color,
                    is_parent=input_activity.is_parent,
                    parent_id=(
                        all_activities_uuid_to_id_mapping.get(str(input_activity.parent_uuid))
                        if input_activity.parent_uuid != project_schedule_uuid
                        else None
                    ),
                    prev_sibling_id=(
                        all_activities_uuid_to_id_mapping.get(str(input_activity.prev_sibling_uuid))
                        if input_activity.prev_sibling_uuid
                        else None
                    ),
                    planned_start_date=input_activity.planned_start_date,
                    planned_end_date=input_activity.planned_end_date,
                    duration=input_activity.duration,
                    actual_start_date=input_activity.actual_start_date,
                    actual_end_date=input_activity.actual_end_date,
                    projected_start_date=input_activity.projected_start_date,
                    projected_end_date=input_activity.projected_end_date,
                    completion_percentage=input_activity.completion_percent,
                    slack=input_activity.slack,
                    is_critical=input_activity.is_critical,
                    type=input_activity.type.value,
                    updated_by_id=user_id if input_activity.object_status == ObjectStatus.UPDATE else None,
                    updated_at=timezone.now() if input_activity.object_status == ObjectStatus.UPDATE else None,
                    deleted_at=timezone.now() if input_activity.object_status == ObjectStatus.DELETE else None,
                    deleted_by_id=user_id if input_activity.object_status == ObjectStatus.DELETE else None,
                )
            )

        fields_to_update = [
            "name",
            "wbs",
            "color",
            "is_parent",
            "parent_id",
            "prev_sibling_id",
            "planned_start_date",
            "planned_end_date",
            "duration",
            "actual_start_date",
            "actual_end_date",
            "projected_start_date",
            "projected_end_date",
            "completion_percentage",
            "slack",
            "is_critical",
            "deleted_at",
            "deleted_by_id",
            "type",
        ]

        if not is_auto_updated:
            fields_to_update.append("updated_at")
            fields_to_update.append("updated_by_id")

        logger.info("Updating Data in Project Schedule Models")
        try:
            ProjectScheduleActivity.objects.bulk_update(
                objs=activities_to_be_updated,
                fields=fields_to_update,
            )
        except ProjectScheduleActivityDataException as e:
            logger.info("Error in updating activities", error=e.message, schedule_id=project_schedule_id)
            raise self.ProjectScheduleDBServiceException(
                "Error in updating activities",
            ) from e

        self._create_and_update_assignees(assignees_to_be_created, assignees_to_be_updated)
        self._create_and_update_organizations(organizations_to_be_created, organizations_to_be_updated)
        self._create_and_update_dependencies(dependencies_to_be_created, dependencies_to_be_updated)
        self._create_and_update_attachments(attachments_to_be_created, attachments_to_be_updated)
        self._create_and_update_tag_mappings(tag_mappings_to_be_created, tag_mappings_to_be_updated)
        updated_progress_attachments = self._create_and_delete_progress_attachments(
            progress_attachments_to_be_created,
            progress_attachments_to_be_deleted,
        )

        if is_root_activity_present:
            ProjectSchedule.objects.filter(id=project_schedule_id).update(
                actual_start_date=schedule_actual_start_date,
                actual_end_date=schedule_actual_end_date,
                planned_start_date=schedule_planned_start_date,
                planned_end_date=schedule_planned_end_date,
                projected_start_date=schedule_projected_start_date,
                completion_percentage=schedule_completion_percentage,
                is_completed=is_schedule_completed,
                projected_end_date=schedule_projected_end_date,
            )

        logger.info("Project Schedule Data Updated")

        # Update the input_activities with the ids of assignees, organizations, dependencies
        for input_activity in input_activities:
            activity_id = input_activity.id
            user_id_assignee_obj_mapping = activities_id_to_multiple_mappings[activity_id][
                "user_id_assignee_obj_mapping"
            ]
            org_id_org_obj_mapping = activities_id_to_multiple_mappings[activity_id]["org_id_org_obj_mapping"]
            dependency_uuid_dependency_mapping_obj_mapping = activities_id_to_multiple_mappings[activity_id][
                "dependency_uuid_dependency_mapping_obj_mapping"
            ]

            for input_assignee in input_activity.assignees:
                if input_assignee.object_status == ObjectStatus.ADD:
                    input_assignee.id = user_id_assignee_obj_mapping.get(input_assignee.user_id).id

            for input_organization in input_activity.organizations:
                if input_organization.object_status == ObjectStatus.ADD:
                    input_organization.id = org_id_org_obj_mapping.get(input_organization.org_id).id

            for input_dependency in input_activity.dependencies:
                if input_dependency.object_status == ObjectStatus.ADD:
                    input_dependency.id = dependency_uuid_dependency_mapping_obj_mapping.get(
                        input_dependency.dependency_uuid
                    ).id

        return input_activities, assignees_to_be_created, updated_progress_attachments

    def increment_schedule_version(
        self, project_schedule_id: int, schedule_version: int, is_auto_updated: bool, user_id: int
    ):
        update_fields = {
            "version": F("version") + 1,
        }

        if not is_auto_updated:
            update_fields["updated_at"] = timezone.now()
            update_fields["updated_by"] = user_id

        return ProjectSchedule.objects.filter(id=project_schedule_id, version=schedule_version).update(**update_fields)


class ProjectScheduleService:
    class ProjectScheduleVersionMismatchException(BaseValidationError):
        pass

    class ProjectScheduleDBException(BaseValidationError):
        pass

    @inject(
        params={
            "project_schedule_repo": ProjectScheduleRepo,
            "project_schedule_db_service": ProjectScheduleDBService,
            "project_schedule_logger": ProjectScheduleLogger,
            "activity_log_builder": ActivityLogObjBuilder,
            "schedule_log_builder": ScheduleLogObjBuilder,
        }
    )
    def __init__(
        self,
        project_schedule_repo: ProjectScheduleRepo,
        project_schedule_db_service: ProjectScheduleDBService,
        project_schedule_logger: ProjectScheduleLogger,
        activity_log_builder: ActivityLogObjBuilder,
        schedule_log_builder: ScheduleLogObjBuilder,
    ):
        self.project_schedule_repo = project_schedule_repo
        self.project_schedule_db_service = project_schedule_db_service
        self.project_schedule_logger = project_schedule_logger
        self.activity_log_builder = activity_log_builder
        self.schedule_log_builder = schedule_log_builder

    def get_project_schedule_id(self, project_id: int, org_id: int, user_id: int) -> int:
        return self.project_schedule_repo.get_project_schedule_id(project_id=project_id, org_id=org_id, user_id=user_id)

    def update(
        self,
        project_schedule_update_entity: ProjectScheduleUpdateEntity,
        user_id: int,
        project_id: int,
    ) -> dict:
        project_schedule_cache_data = ProjectScheduleCache.get(instance_id=project_schedule_update_entity.id)

        # Version mismatch to be checked here as well as in the end

        if project_schedule_update_entity.version != project_schedule_cache_data.version:
            raise self.ProjectScheduleVersionMismatchException("Version mismatch")

        logger.info("Schedule Update Started", data=project_schedule_update_entity.activities)

        # Update the schedule
        try:
            (
                input_activities_with_ids,
                new_assignee_entities,
                updated_progress_attachments,
            ) = self.project_schedule_db_service.update_schedule(
                input_activities=project_schedule_update_entity.activities,
                user_id=user_id,
                project_schedule_id=project_schedule_update_entity.id,
                project_schedule_uuid=project_schedule_cache_data.uuid,
                saved_activities=list(project_schedule_cache_data.activities.values()),
                is_auto_updated=project_schedule_update_entity.is_auto_updated,
            )
        except ProjectScheduleDBService.ProjectScheduleDBServiceException as e:
            logger.info("Unable to update schedule", error=e.message)
            raise self.ProjectScheduleDBException("Error in updating schedule due to Invalid data") from e
        logger.info("Schedule Update Completed")

        deleted_activity_ids = []
        deleted_activity_assignee_user_ids = []

        created_activity_ids = []
        updated_activity_ids = []
        activities_edited_trigger_data = {}

        logger.info("Creating Activity Logs")
        is_schedule_completed = False
        # Create project schedule Activity Log Data (Per Activity)
        for input_activity in input_activities_with_ids:
            if input_activity.uuid == project_schedule_cache_data.uuid:  # Ignore the Root activity
                is_schedule_completed = input_activity.completion_percent == 100

                saved_root_activity = None

                for activity in project_schedule_cache_data.activities.values():
                    if activity.uuid == project_schedule_cache_data.uuid:
                        saved_root_activity = activity
                        break

                assert saved_root_activity is not None, "Root Activity not found in cache"

                schedule_event_logs = self.schedule_log_builder.build_log_entities_for_schedule(
                    input_schedule=ScheduleLogObjBuilderUpdateEntity(
                        completion_percent=input_activity.completion_percent,
                        planned_end_date=input_activity.planned_end_date,
                        planned_start_date=input_activity.planned_start_date,
                        actual_start_date=input_activity.actual_start_date,
                        actual_end_date=input_activity.actual_end_date,
                    ),
                    saved_schedule=ScheduleLogObjBuilderUpdateEntity(
                        completion_percent=saved_root_activity.completion_percent,
                        planned_end_date=saved_root_activity.planned_end_date,
                        planned_start_date=saved_root_activity.planned_start_date,
                        actual_start_date=saved_root_activity.actual_start_date,
                        actual_end_date=saved_root_activity.actual_end_date,
                    ),
                )

                self.project_schedule_logger.log_schedule_changes(
                    user_id=user_id,
                    schedule_id=project_schedule_update_entity.id,
                    schedule_event_log=schedule_event_logs,
                )
                continue

            if input_activity.object_status == ObjectStatus.ADD:
                created_activity_ids.append(input_activity.id)

            if input_activity.object_status == ObjectStatus.DELETE and input_activity.completion_percent > 0:
                deleted_activity_ids.append(input_activity.id)
                deleted_activity_assignee_user_ids.extend([assignee.user_id for assignee in input_activity.assignees])

            # Create project schedule Activity Log Data (Per Activity)
            (
                activity_event_log_entity,
                activity_events_list,
            ) = self.activity_log_builder.build_log_entities_for_activity(
                input_activity=input_activity,
                saved_activity=project_schedule_cache_data.activities.get(input_activity.id, None),
                updated_progress_attachments=updated_progress_attachments.get(input_activity.id, None),
            )
            if activity_events_list:
                if ProjectScheduleActivityDetailEnum.COMPLETION_PERCENTAGE.value in activity_events_list:
                    updated_activity_ids.append(input_activity.id)
                if {
                    ProjectScheduleActivityDetailEnum.PLANNED_START_DATE.value,
                    ProjectScheduleActivityDetailEnum.PLANNED_END_DATE.value,
                    ProjectScheduleActivityDetailEnum.ACTUAL_START_DATE.value,
                    ProjectScheduleActivityDetailEnum.ACTUAL_END_DATE.value,
                    ProjectScheduleActivityDetailEnum.ASSIGNEES.value,
                    ProjectScheduleActivityDetailEnum.NAME.value,
                    ProjectScheduleActivityDetailEnum.ORGANIZATIONS.value,
                    ProjectScheduleActivityDetailEnum.DEPENDENCIES.value,
                    ProjectScheduleActivityDetailEnum.PARENT.value,
                    ProjectScheduleActivityDetailEnum.TYPE.value,
                }.intersection(activity_events_list):
                    activities_edited_trigger_data[input_activity.id] = list(activity_events_list)

            # If there are no changes in the activity, then no need to log
            if activity_event_log_entity.events_entities:
                self.project_schedule_logger.log_activity_changes(
                    activity_event_log=activity_event_log_entity,
                    user_id=user_id,
                    schedule_id=project_schedule_update_entity.id,
                )

            if input_activity.parent_uuid != project_schedule_cache_data.uuid:
                saved_activity = project_schedule_cache_data.activities.get(input_activity.id, None)
                old_parent_id = saved_activity.parent_id if saved_activity else None

                """
                It could return multiple event log entities like when a parent 
                is changed of a child activity , resulting in 2 events 
                (1-> Child deleted even and 2-> Child created event)
                Both of which are separate events
                """
                activity_event_log_entities = self.activity_log_builder.build_log_entities_for_parent_activity(
                    input_activity=input_activity, new_parent_id=input_activity.parent_id, old_parent_id=old_parent_id
                )

                for activity_event_log_entity in activity_event_log_entities:
                    if activity_event_log_entity.events_entities:
                        self.project_schedule_logger.log_activity_changes(
                            activity_event_log=activity_event_log_entity,
                            user_id=user_id,
                            schedule_id=project_schedule_update_entity.id,
                        )

        logger.info("Committing Activity Logs")
        # Commit the logs
        self.project_schedule_logger.commit_logs()

        logger.info(
            "Webhook Triggers Started",
            created_activity_ids=created_activity_ids,
            updated_activity_ids=updated_activity_ids,
            activities_edited_trigger_data=activities_edited_trigger_data,
        )

        # ProjectScheduleActivitiesEventData
        if created_activity_ids:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_ACTIVITY_CREATED,
                    event_data=ProjectScheduleActivitiesCreateEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                        activity_ids=created_activity_ids,
                        user_id=user_id,
                    ),
                )
            )

        if updated_activity_ids:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_ACTIVITY_UPDATED,
                    event_data=ProjectScheduleActivitiesUpdateEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                        activity_ids=updated_activity_ids,
                        user_id=user_id,
                    ),
                )
            )

        if activities_edited_trigger_data:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_ACTIVITY_EDITED,
                    event_data=ProjectScheduleActivitiesEditEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                        activities_data=activities_edited_trigger_data,
                        user_id=user_id,
                    ),
                )
            )

        logger.info(
            "Notification Triggers Started",
            is_schedule_completed=is_schedule_completed,
            new_assignee_entities=new_assignee_entities,
            deleted_activity_ids=deleted_activity_ids,
            deleted_activity_assignee_user_ids=deleted_activity_assignee_user_ids,
        )

        # Trigger the Notifications
        if is_schedule_completed:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_COMPLETED,
                    event_data=ProjectScheduleCompletedEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                    ),
                )
            )

        if new_assignee_entities:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED,
                    event_data=ProjectScheduleActivitiesAssignedEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                        assigned_user_ids=[assignee.user_id for assignee in new_assignee_entities],
                    ),
                )
            )

        if deleted_activity_ids:
            on_commit(
                partial(
                    trigger_event,
                    event=Events.PROJECT_SCHEDULE_ACTIVITIES_DELETED,
                    event_data=ProjectScheduleActivitiesDeletedEventData(
                        project_id=project_id,
                        schedule_id=project_schedule_update_entity.id,
                        user_id=user_id,
                        activity_ids=deleted_activity_ids,
                        assigned_user_ids=deleted_activity_assignee_user_ids,
                    ),
                )
            )

        # Version mismatch Check
        updated_row = self.project_schedule_db_service.increment_schedule_version(
            project_schedule_update_entity.id,
            project_schedule_update_entity.version,
            is_auto_updated=project_schedule_update_entity.is_auto_updated,
            user_id=user_id,
        )

        logger.info("Schedule Version Check", updated_row=updated_row, version=project_schedule_update_entity.version)
        if not updated_row:
            # Delete Cache here for Race Condition
            ProjectScheduleCache.delete(instance_id=project_schedule_update_entity.id)
            raise self.ProjectScheduleVersionMismatchException("Version mismatch")

        logger.info("Schedule Cache Delete", version=project_schedule_update_entity.version)
        ProjectScheduleCache.delete(instance_id=project_schedule_update_entity.id)

        logger.info(
            "Schedule Cache Update and Cache data",
            cache_data=ProjectScheduleCache.get(instance_id=project_schedule_update_entity.id),
        )
        schedule = ProjectScheduleCache.get_raw_data(instance_id=project_schedule_update_entity.id)
        logger.info("Schedule Cache New version", version=schedule.get("version"))
        activities = []
        for input_activity in input_activities_with_ids:
            if str(input_activity.uuid) == schedule["uuid"]:
                activities.append(schedule["activities"].get(str(SCHEDULE_ROOT_ID)))
            else:
                activities.append(schedule["activities"].get(str(input_activity.id)))
        schedule["activities"] = activities
        return schedule
