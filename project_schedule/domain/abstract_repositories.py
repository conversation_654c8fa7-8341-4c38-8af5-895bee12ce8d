import abc
import datetime

from common.timeline.service import TimelineStatusService
from project_schedule.domain.entities import (
    DayWiseScheduleDataEntity,
    TodayScheduleDataEntity,
)
from work_progress_v2.domain.report_entities import HashtagElementEntity


class WorkProgressToScheduleAbstractRepository:
    @abc.abstractmethod
    def get_today_schedule_update_data(self, timeline_helper: TimelineStatusService) -> TodayScheduleDataEntity | None:
        pass

    @abc.abstractmethod
    def get_schedule_update_date_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date, timeline_helper: TimelineStatusService
    ) -> list[DayWiseScheduleDataEntity]:
        ...

    def get_hashtag_activity_list(self) -> list[HashtagElementEntity]:
        ...
