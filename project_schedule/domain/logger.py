import abc
import datetime
from dataclasses import dataclass, field
from typing import Optional

from django.utils import timezone

from common.injector import Injectable
from common.pydantic.base_model import BaseModel
from project_schedule.data.choices import ProjectScheduleActivityTypeEnum
from project_schedule.data.models import (
    ProjectScheduleActivityAssigneeUpdateEventData,
    ProjectScheduleActivityBulkRemovalEventData,
    ProjectScheduleActivityChildCreatedEventData,
    ProjectScheduleActivityChildDeletedEventData,
    ProjectScheduleActivityCompletionUpdateEventData,
    ProjectScheduleActivityCreatedEventData,
    ProjectScheduleActivityDateUpdateEventData,
    ProjectScheduleActivityDeletedEventData,
    ProjectScheduleActivityDependencyUpdateEventData,
    ProjectScheduleActivityEvent,
    ProjectScheduleActivityNameUpdateEventData,
    ProjectScheduleActivityOrgUpdateEventData,
    ProjectScheduleActivityParentUpdateEventData,
    ProjectScheduleActivityProgressAttachmentCreatedEventData,
    ProjectScheduleActivityProgressAttachmentDeletedEventData,
    ProjectScheduleActivityTypeUpdateEventData,
    ProjectScheduleActivityWBSUpdateEventData,
    ProjectScheduleCompletionProgressUpdateEventData,
    ProjectScheduleDateUpdateEventData,
    ProjectScheduleEvent,
    ScheduleActivityEventMapping,
)
from project_schedule.domain.constants import ProjectScheduleActivityDependencyEnum


@dataclass
class ActivityLogData:
    schedule_event_obj: Optional[ProjectScheduleEvent] = None
    schedule_activity_event_mappings: list[ScheduleActivityEventMapping] = field(default_factory=list)
    schedule_completion_progress_updated_event_data_objs: list[ProjectScheduleCompletionProgressUpdateEventData] = (
        field(default_factory=list)
    )
    schedule_date_updated_event_data_objs: list[ProjectScheduleDateUpdateEventData] = field(default_factory=list)

    event_objs: list[ProjectScheduleActivityEvent] = field(default_factory=list)
    created_event_data_objs: list[ProjectScheduleActivityCreatedEventData] = field(default_factory=list)
    assignee_updated_event_data_objs: list[ProjectScheduleActivityAssigneeUpdateEventData] = field(default_factory=list)
    org_updated_event_data_objs: list[ProjectScheduleActivityOrgUpdateEventData] = field(default_factory=list)
    dates_updated_event_data_objs: list[ProjectScheduleActivityDateUpdateEventData] = field(default_factory=list)
    dependency_updated_event_data_objs: list[ProjectScheduleActivityDependencyUpdateEventData] = field(
        default_factory=list
    )
    completion_updated_event_data_objs: list[ProjectScheduleActivityCompletionUpdateEventData] = field(
        default_factory=list
    )
    child_created_event_data_objs: list[ProjectScheduleActivityChildCreatedEventData] = field(default_factory=list)
    child_deleted_event_data_objs: list[ProjectScheduleActivityChildDeletedEventData] = field(default_factory=list)
    name_updated_event_data_objs: list[ProjectScheduleActivityNameUpdateEventData] = field(default_factory=list)
    parent_updated_event_data_objs: list[ProjectScheduleActivityParentUpdateEventData] = field(default_factory=list)
    deleted_event_data_objs: list[ProjectScheduleActivityDeletedEventData] = field(default_factory=list)
    bulk_removal_event_data_objs: list[ProjectScheduleActivityBulkRemovalEventData] = field(default_factory=list)
    wbs_updated_event_data_objs: list[ProjectScheduleActivityWBSUpdateEventData] = field(default_factory=list)
    type_updated_event_data_objs: list[ProjectScheduleActivityTypeUpdateEventData] = field(default_factory=list)
    progress_attachment_created_event_data_objs: list[ProjectScheduleActivityProgressAttachmentCreatedEventData] = (
        field(default_factory=list)
    )
    progress_attachment_deleted_event_data_objs: list[ProjectScheduleActivityProgressAttachmentDeletedEventData] = (
        field(default_factory=list)
    )


class ProjectScheduleActivityEventDataBaseEntity(BaseModel):
    @abc.abstractmethod
    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        pass


class ProjectScheduleEventDataBaseEntity(BaseModel):
    @abc.abstractmethod
    def log_event_data(self, schedule_event: ProjectScheduleEvent, log_obj: ActivityLogData) -> ActivityLogData:
        pass


class ProjectScheduleCompletionProgressUpdatedLogEntity(ProjectScheduleEventDataBaseEntity):
    from_completion_percent: float
    to_completion_percent: float

    def log_event_data(self, schedule_event: ProjectScheduleEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.schedule_completion_progress_updated_event_data_objs.append(
            ProjectScheduleCompletionProgressUpdateEventData(
                schedule_event=schedule_event,
                from_completion_percentage=self.from_completion_percent,
                to_completion_percentage=self.to_completion_percent,
            )
        )
        return log_obj


class ProjectScheduleDateUpdatedLogEntity(ProjectScheduleEventDataBaseEntity):
    from_planned_start_date: Optional[datetime.date]
    from_planned_end_date: Optional[datetime.date]
    from_actual_start_date: Optional[datetime.date]
    from_actual_end_date: Optional[datetime.date]
    to_planned_start_date: Optional[datetime.date]
    to_planned_end_date: Optional[datetime.date]
    to_actual_start_date: Optional[datetime.date]
    to_actual_end_date: Optional[datetime.date]

    def log_event_data(self, schedule_event: ProjectScheduleEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.schedule_date_updated_event_data_objs.append(
            ProjectScheduleDateUpdateEventData(
                schedule_event=schedule_event,
                from_planned_start_date=self.from_planned_start_date,
                from_planned_end_date=self.from_planned_end_date,
                from_actual_start_date=self.from_actual_start_date,
                from_actual_end_date=self.from_actual_end_date,
                to_planned_start_date=self.to_planned_start_date,
                to_planned_end_date=self.to_planned_end_date,
                to_actual_start_date=self.to_actual_start_date,
                to_actual_end_date=self.to_actual_end_date,
            )
        )
        return log_obj


class ProjectScheduleActivityCreatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    name: str
    created_at: timezone.datetime

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.created_event_data_objs.append(ProjectScheduleActivityCreatedEventData(name=self.name, event=event))
        return log_obj


class ProjectScheduleActivityNameUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_name: str
    to_name: str

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.name_updated_event_data_objs.append(
            ProjectScheduleActivityNameUpdateEventData(from_name=self.from_name, to_name=self.to_name, event=event)
        )
        return log_obj


class ProjectScheduleActivityWBSUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_wbs: str
    to_wbs: str

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.wbs_updated_event_data_objs.append(
            ProjectScheduleActivityWBSUpdateEventData(from_wbs=self.from_wbs, to_wbs=self.to_wbs, event=event)
        )
        return log_obj


class ProjectScheduleActivityTypeUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_type: ProjectScheduleActivityTypeEnum
    to_type: ProjectScheduleActivityTypeEnum

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.type_updated_event_data_objs.append(
            ProjectScheduleActivityTypeUpdateEventData(from_type=self.from_type, to_type=self.to_type, event=event)
        )
        return log_obj


class ProjectScheduleActivityParentUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_parent_id: Optional[int]
    to_parent_id: Optional[int]

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.parent_updated_event_data_objs.append(
            ProjectScheduleActivityParentUpdateEventData(
                event=event,
                from_parent_id=self.from_parent_id,
                to_parent_id=self.to_parent_id,
            )
        )
        return log_obj


class ProjectScheduleActivityAssigneeUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    activity_assignee_id: int

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.assignee_updated_event_data_objs.append(
            ProjectScheduleActivityAssigneeUpdateEventData(
                event=event,
                assignee_id=self.activity_assignee_id,
            )
        )
        return log_obj


class ProjectScheduleActivityOrgUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    activity_organization_id: int

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.org_updated_event_data_objs.append(
            ProjectScheduleActivityOrgUpdateEventData(
                event=event,
                activity_organization_id=self.activity_organization_id,
            )
        )
        return log_obj


class ProjectScheduleActivityDateUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_planned_start_date: Optional[datetime.date]
    from_planned_end_date: Optional[datetime.date]
    from_actual_start_date: Optional[datetime.date]
    from_actual_end_date: Optional[datetime.date]
    to_planned_start_date: Optional[datetime.date]
    to_planned_end_date: Optional[datetime.date]
    to_actual_start_date: Optional[datetime.date]
    to_actual_end_date: Optional[datetime.date]

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.dates_updated_event_data_objs.append(
            ProjectScheduleActivityDateUpdateEventData(
                event=event,
                from_planned_start_date=self.from_planned_start_date,
                from_planned_end_date=self.from_planned_end_date,
                from_actual_start_date=self.from_actual_start_date,
                from_actual_end_date=self.from_actual_end_date,
                to_planned_start_date=self.to_planned_start_date,
                to_planned_end_date=self.to_planned_end_date,
                to_actual_start_date=self.to_actual_start_date,
                to_actual_end_date=self.to_actual_end_date,
            )
        )
        return log_obj


class ProjectScheduleActivityDependencyUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    dependency_mapping_id: int
    type: ProjectScheduleActivityDependencyEnum
    lag: int

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.dependency_updated_event_data_objs.append(
            ProjectScheduleActivityDependencyUpdateEventData(
                event=event,
                dependency_mapping_id=self.dependency_mapping_id,
                type=self.type.value,
                lag=self.lag,
            )
        )
        return log_obj


class ProjectScheduleActivityCompletionUpdatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    from_completion_percent: float
    to_completion_percent: float

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.completion_updated_event_data_objs.append(
            ProjectScheduleActivityCompletionUpdateEventData(
                event=event,
                from_completion_percentage=self.from_completion_percent,
                to_completion_percentage=self.to_completion_percent,
            )
        )
        return log_obj


class ProjectScheduleActivityChildCreatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    child_activity_id: int

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.child_created_event_data_objs.append(
            ProjectScheduleActivityChildCreatedEventData(
                event=event,
                child_activity_id=self.child_activity_id,
            )
        )
        return log_obj


class ProjectScheduleActivityChildDeletedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    child_activity_id: int

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.child_deleted_event_data_objs.append(
            ProjectScheduleActivityChildDeletedEventData(
                event=event,
                child_activity_id=self.child_activity_id,
            )
        )
        return log_obj


class ProjectScheduleActivityDeletedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    name: str

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.deleted_event_data_objs.append(
            ProjectScheduleActivityDeletedEventData(
                event=event,
                name=self.name,
            )
        )
        return log_obj


class ProjectScheduleActivityBulkRemovalLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    assignees_removed: bool
    organizations_removed: bool
    dependencies_removed: bool

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        log_obj.bulk_removal_event_data_objs.append(
            ProjectScheduleActivityBulkRemovalEventData(
                event=event,
                assignees_removed=self.assignees_removed,
                organizations_removed=self.organizations_removed,
                dependencies_removed=self.dependencies_removed,
            )
        )
        return log_obj


class ProjectScheduleActivityProgressAttachmentCreatedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    created_ids: list[int] = []

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        instance = ProjectScheduleActivityProgressAttachmentCreatedEventData()

        instance.event = event
        # Store the created IDs for later
        instance.temp_created_ids = self.created_ids
        log_obj.progress_attachment_created_event_data_objs.append(instance)
        return log_obj


class ProjectScheduleActivityProgressAttachmentDeletedLogEntity(ProjectScheduleActivityEventDataBaseEntity):
    deleted_ids: list[int] = []

    def log_event_data(self, event: ProjectScheduleActivityEvent, log_obj: ActivityLogData) -> ActivityLogData:
        instance = ProjectScheduleActivityProgressAttachmentDeletedEventData()

        instance.event = event
        # Store the deleted IDs for later
        instance.temp_deleted_ids = self.deleted_ids
        log_obj.progress_attachment_deleted_event_data_objs.append(instance)
        return log_obj


class ProjectScheduleActivityEventLogEntity(BaseModel):
    activity_id: int
    is_derived: bool
    events_entities: list[ProjectScheduleActivityEventDataBaseEntity]

    def get_event_obj(self, user_id: int) -> ProjectScheduleActivityEvent:
        return ProjectScheduleActivityEvent(
            activity_id=self.activity_id, is_derived=self.is_derived, created_by_id=user_id
        )


class ProjectScheduleLogger(Injectable):
    log_obj = None

    def __init__(self):
        self.log_obj = ActivityLogData()

    def log_activity_changes(
        self,
        activity_event_log: ProjectScheduleActivityEventLogEntity,
        user_id: int,
        schedule_id: int,
    ):
        if not activity_event_log.events_entities:
            return

        if not self.log_obj:
            assert False, "Log object is not initialized"

        event = activity_event_log.get_event_obj(user_id)
        self.log_obj.event_objs.append(event)
        for event_entity in activity_event_log.events_entities:
            if issubclass(type(event_entity), ProjectScheduleActivityEventDataBaseEntity):
                self.log_obj = event_entity.log_event_data(event, self.log_obj)

            # Schedule Level History
            if isinstance(event_entity, ProjectScheduleActivityCreatedLogEntity) or isinstance(
                event_entity, ProjectScheduleActivityDeletedLogEntity
            ):
                if not self.log_obj.schedule_event_obj:
                    self.log_obj.schedule_event_obj = ProjectScheduleEvent(
                        schedule_id=schedule_id, created_by_id=user_id
                    )
                self.log_obj.schedule_activity_event_mappings.append(
                    ScheduleActivityEventMapping(
                        schedule_event=self.log_obj.schedule_event_obj,
                        activity_event=event,
                    )
                )

    def log_schedule_changes(
        self,
        schedule_event_log: list[ProjectScheduleEventDataBaseEntity],
        user_id: int,
        schedule_id: int,
    ):
        if not schedule_event_log:
            return

        if not self.log_obj:
            assert False, "Log object is not initialized"

        for event in schedule_event_log:
            if not self.log_obj.schedule_event_obj:
                self.log_obj.schedule_event_obj = ProjectScheduleEvent(schedule_id=schedule_id, created_by_id=user_id)

            if issubclass(type(event), ProjectScheduleEventDataBaseEntity):
                self.log_obj = event.log_event_data(
                    schedule_event=self.log_obj.schedule_event_obj,
                    log_obj=self.log_obj,
                )

    def commit_logs(self):
        if self.log_obj is None or not self.log_obj.event_objs:
            return

        ProjectScheduleActivityEvent.objects.bulk_create(self.log_obj.event_objs)

        if self.log_obj.schedule_event_obj:
            ProjectScheduleEvent.objects.bulk_create([self.log_obj.schedule_event_obj])

        if self.log_obj.schedule_activity_event_mappings:
            ScheduleActivityEventMapping.objects.bulk_create(self.log_obj.schedule_activity_event_mappings)

        if self.log_obj.schedule_completion_progress_updated_event_data_objs:
            ProjectScheduleCompletionProgressUpdateEventData.objects.bulk_create(
                self.log_obj.schedule_completion_progress_updated_event_data_objs
            )

        if self.log_obj.schedule_date_updated_event_data_objs:
            ProjectScheduleDateUpdateEventData.objects.bulk_create(self.log_obj.schedule_date_updated_event_data_objs)

        if self.log_obj.created_event_data_objs:
            ProjectScheduleActivityCreatedEventData.objects.bulk_create(self.log_obj.created_event_data_objs)
        if self.log_obj.assignee_updated_event_data_objs:
            ProjectScheduleActivityAssigneeUpdateEventData.objects.bulk_create(
                self.log_obj.assignee_updated_event_data_objs
            )
        if self.log_obj.org_updated_event_data_objs:
            ProjectScheduleActivityOrgUpdateEventData.objects.bulk_create(self.log_obj.org_updated_event_data_objs)
        if self.log_obj.dates_updated_event_data_objs:
            ProjectScheduleActivityDateUpdateEventData.objects.bulk_create(self.log_obj.dates_updated_event_data_objs)
        if self.log_obj.dependency_updated_event_data_objs:
            ProjectScheduleActivityDependencyUpdateEventData.objects.bulk_create(
                self.log_obj.dependency_updated_event_data_objs
            )
        if self.log_obj.completion_updated_event_data_objs:
            ProjectScheduleActivityCompletionUpdateEventData.objects.bulk_create(
                self.log_obj.completion_updated_event_data_objs
            )
        if self.log_obj.child_created_event_data_objs:
            ProjectScheduleActivityChildCreatedEventData.objects.bulk_create(self.log_obj.child_created_event_data_objs)
        if self.log_obj.child_deleted_event_data_objs:
            ProjectScheduleActivityChildDeletedEventData.objects.bulk_create(self.log_obj.child_deleted_event_data_objs)
        if self.log_obj.name_updated_event_data_objs:
            ProjectScheduleActivityNameUpdateEventData.objects.bulk_create(self.log_obj.name_updated_event_data_objs)
        if self.log_obj.parent_updated_event_data_objs:
            ProjectScheduleActivityParentUpdateEventData.objects.bulk_create(
                self.log_obj.parent_updated_event_data_objs
            )
        if self.log_obj.deleted_event_data_objs:
            ProjectScheduleActivityDeletedEventData.objects.bulk_create(self.log_obj.deleted_event_data_objs)
        if self.log_obj.bulk_removal_event_data_objs:
            ProjectScheduleActivityBulkRemovalEventData.objects.bulk_create(self.log_obj.bulk_removal_event_data_objs)
        if self.log_obj.wbs_updated_event_data_objs:
            ProjectScheduleActivityWBSUpdateEventData.objects.bulk_create(self.log_obj.wbs_updated_event_data_objs)
        if self.log_obj.type_updated_event_data_objs:
            ProjectScheduleActivityTypeUpdateEventData.objects.bulk_create(self.log_obj.type_updated_event_data_objs)
        if self.log_obj.progress_attachment_deleted_event_data_objs:
            created_instance = ProjectScheduleActivityProgressAttachmentDeletedEventData.objects.bulk_create(
                self.log_obj.progress_attachment_deleted_event_data_objs
            )

            # set the M2M relationship
            for instance in created_instance:
                instance.progress_attachments.set(instance.temp_deleted_ids)
                instance.save()

        if self.log_obj.progress_attachment_created_event_data_objs:
            created_instance = ProjectScheduleActivityProgressAttachmentCreatedEventData.objects.bulk_create(
                self.log_obj.progress_attachment_created_event_data_objs
            )

            # set the M2M relationship
            for instance in created_instance:
                instance.progress_attachments.set(instance.temp_created_ids)
                instance.save()

        self.log_obj = None
