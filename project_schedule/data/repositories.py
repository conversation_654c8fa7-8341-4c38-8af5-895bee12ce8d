import datetime
import decimal

import structlog
from django.db.models import <PERSON><PERSON>tch, Q
from django.utils import timezone

from common.exceptions import BaseValidationError
from common.injector import Injectable
from common.timeline.service import TimelineStatusService
from common.utils import get_local_time
from core.entities import ProjectUserEntity
from project_schedule.data.choices import ProjectScheduleActivityTypeEnum
from project_schedule.data.models import (
    ProjectSchedule,
    ProjectScheduleActivity,
    ProjectScheduleActivityAssigneeUpdateEventData,
    ProjectScheduleActivityDependencyUpdateEventData,
    ProjectScheduleActivityEvent,
    ProjectScheduleActivityOrgUpdateEventData,
    ProjectScheduleActivityProgressAttachment,
    ProjectScheduleEvent,
    ProjectScheduleTag,
    ScheduleActivityEventMapping,
)
from project_schedule.domain.abstract_repositories import WorkProgressToScheduleAbstractRepository
from project_schedule.domain.constants import DATES_NOT_SET_MESSAGES, SCHEDULE_ROOT_ID
from project_schedule.domain.entities import (
    DayWiseScheduleDataEntity,
    ProjectScheduleActivityAssigneeEntity,
    ProjectScheduleActivityAttachmentEntity,
    ProjectScheduleActivityAttachmentTagMappingEntity,
    ProjectScheduleActivityDependencyEntity,
    ProjectScheduleActivityEntity,
    ProjectScheduleActivityOrganizationEntity,
    ProjectScheduleActivityProgressAttachmentEntity,
    ProjectScheduleEntity,
    TodayScheduleActivityDataEntity,
    TodayScheduleActivityStatusEntity,
    TodayScheduleAttachmentActivityEntity,
    TodayScheduleAttachmentEntity,
    TodayScheduleDataEntity,
)
from work_progress_v2.domain.report_entities import HashtagElementEntity

logger = structlog.get_logger(__name__)


class ProjectScheduleRepo(Injectable):
    class ProjectScheduleRepoException(BaseValidationError):
        pass

    class ScheduleNotFoundException(ProjectScheduleRepoException):
        pass

    def get_project_schedule_id(self, project_id: int, org_id: int, user_id: int) -> int:
        schedule_entity, _ = ProjectSchedule.objects.get_or_create(
            project_id=project_id, defaults={"version": 1, "created_by_id": user_id, "organization_id": org_id}
        )
        return schedule_entity.pk

    def get_project_schedule(self, project_schedule_id: int) -> ProjectScheduleEntity:
        project_schedule = (
            ProjectSchedule.objects.filter(id=project_schedule_id)
            .select_related("organization")
            .prefetch_activities()
            .first()
        )
        if not project_schedule:
            logger.info("Schedule not found", project_schedule_id=project_schedule_id)
            raise self.ScheduleNotFoundException("Schedule not found")
        activities = self.get_activities(project_schedule=project_schedule)
        activities[SCHEDULE_ROOT_ID] = self._get_root_activity(project_schedule=project_schedule)
        schedule = ProjectScheduleEntity(
            id=project_schedule.pk,
            project_id=project_schedule.project_id,
            org_id=project_schedule.organization_id,
            org_logo=project_schedule.organization.logo.url if project_schedule.organization.logo.name else None,
            version=project_schedule.version,
            uuid=project_schedule.uuid,
            activities=activities,
        )
        return schedule

    def get_activity_attachments(
        self, activity: ProjectScheduleActivity
    ) -> list[ProjectScheduleActivityAttachmentEntity]:
        activity_attachment_entities = []
        for attachment in activity.attachments.all():
            attachment_tag_entities = []
            for tag_mapping in attachment.tag_mappings.all():
                attachment_tag_entities.append(
                    ProjectScheduleActivityAttachmentTagMappingEntity(
                        id=tag_mapping.pk,
                        tag_id=tag_mapping.tag_id,
                        name=tag_mapping.tag.name,
                    )
                )
            activity_attachment_entities.append(
                ProjectScheduleActivityAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    file=attachment.file.url,
                    uuid=attachment.uuid,
                    tags=attachment_tag_entities,
                    uploaded_at=attachment.uploaded_at,
                    updated_at=attachment.updated_at,
                    updated_by_id=attachment.updated_by_id,
                )
            )
        return activity_attachment_entities

    def get_activity_assignees(self, activity: ProjectScheduleActivity) -> list[ProjectScheduleActivityAssigneeEntity]:
        assignees = activity.assigned_assignee_mappings.all()
        activity_assignee_entities = []
        for assignee in assignees:
            activity_assignee_entities.append(
                ProjectScheduleActivityAssigneeEntity(
                    id=assignee.pk,
                    user_id=assignee.user_id,
                    name=assignee.user.name,
                    photo=assignee.user.photo.url if assignee.user.photo.name else None,
                )
            )
        return activity_assignee_entities

    def get_activity_dependencies(
        self, activity: ProjectScheduleActivity
    ) -> list[ProjectScheduleActivityDependencyEntity]:
        activity_dependency_entities = []
        for dependency in activity.dependency_mappings.all():
            activity_dependency_entities.append(
                ProjectScheduleActivityDependencyEntity(
                    id=dependency.pk,
                    dependency_uuid=dependency.dependency_activity.uuid,
                    uuid=dependency.uuid,
                    lag=dependency.lag,
                    type=dependency.type,
                    updated_at=dependency.updated_at,
                    updated_by_id=dependency.updated_by_id,
                )
            )
        return activity_dependency_entities

    def get_activity_organizations(
        self, activity: ProjectScheduleActivity
    ) -> list[ProjectScheduleActivityOrganizationEntity]:
        activity_organization_entities = []
        for organization_mapping in activity.assigned_organization_mappings.all():
            activity_organization_entities.append(
                ProjectScheduleActivityOrganizationEntity(
                    id=organization_mapping.pk,
                    org_id=organization_mapping.organization_id,
                    name=organization_mapping.organization.name,
                    logo=(
                        organization_mapping.organization.logo.url
                        if organization_mapping.organization.logo.name
                        else None
                    ),
                )
            )
        return activity_organization_entities

    def get_activity_progress_attachments(
        self,
        activity: ProjectScheduleActivity,
    ) -> list[ProjectScheduleActivityProgressAttachmentEntity]:
        activity_attachment_entities = []
        for attachment in activity.progress_attachments.all():
            attachment: ProjectScheduleActivityProgressAttachment

            activity_attachment_entities.append(
                ProjectScheduleActivityProgressAttachmentEntity(
                    id=attachment.pk,
                    name=attachment.name,
                    file=attachment.file.url,
                    uploaded_at=attachment.uploaded_at,
                    uuid=attachment.uuid,
                )
            )
        return activity_attachment_entities

    def get_activities(self, project_schedule: ProjectSchedule) -> dict[int, ProjectScheduleActivityEntity]:
        project_schedule_activity_entities: dict[int, ProjectScheduleActivityEntity] = {}
        for activity in project_schedule.activities.all():
            activity_entity = self._get_activity_entity(schedule_activity=activity, project_schedule=project_schedule)
            project_schedule_activity_entities[activity_entity.id] = activity_entity
        return project_schedule_activity_entities

    def get_activity(self, activity_id: int) -> ProjectScheduleActivityEntity:
        schedule_activity = (
            ProjectScheduleActivity.objects.filter(id=activity_id)
            .select_related("parent", "prev_sibling")
            .prefetch_dependencies()
            .prefetch_assignees()
            .prefetch_organizations()
            .prefetch_attachments()
            .prefetch_progress_attachments()
        ).first()

        return self._get_activity_entity(schedule_activity)

    def _get_activity_entity(
        self,
        schedule_activity: ProjectScheduleActivity,
        project_schedule: ProjectSchedule,
    ) -> ProjectScheduleActivityEntity:
        activity_entity = ProjectScheduleActivityEntity(
            id=schedule_activity.pk,
            uuid=schedule_activity.uuid,
            name=schedule_activity.name,
            wbs=schedule_activity.wbs,
            color=schedule_activity.color,
            is_parent=schedule_activity.is_parent,
            parent_uuid=schedule_activity.parent.uuid if schedule_activity.parent else project_schedule.uuid,
            prev_sibling_uuid=schedule_activity.prev_sibling.uuid if schedule_activity.prev_sibling else None,
            parent_id=schedule_activity.parent_id if schedule_activity.parent else SCHEDULE_ROOT_ID,
            prev_sibling_id=schedule_activity.prev_sibling_id,
            is_critical=schedule_activity.is_critical,
            planned_start_date=schedule_activity.planned_start_date,
            planned_end_date=schedule_activity.planned_end_date,
            duration=schedule_activity.duration,
            actual_start_date=schedule_activity.actual_start_date,
            actual_end_date=schedule_activity.actual_end_date,
            projected_start_date=schedule_activity.projected_start_date,
            projected_end_date=schedule_activity.projected_end_date,
            completion_percent=schedule_activity.completion_percentage,
            slack=schedule_activity.slack,
            assignees=self.get_activity_assignees(schedule_activity),
            organizations=self.get_activity_organizations(schedule_activity),
            attachments=self.get_activity_attachments(schedule_activity),
            dependencies=self.get_activity_dependencies(schedule_activity),
            progress_attachments=self.get_activity_progress_attachments(schedule_activity),
            type=ProjectScheduleActivityTypeEnum.to_enum(schedule_activity.type),
        )
        return activity_entity

    def _get_root_activity(self, project_schedule: ProjectSchedule) -> ProjectScheduleActivityEntity:
        return ProjectScheduleActivityEntity(
            id=SCHEDULE_ROOT_ID,
            uuid=project_schedule.uuid,
            name="Root Activity",
            wbs="0",
            color="#000000",
            is_parent=True,
            parent_uuid=None,
            prev_sibling_uuid=None,
            parent_id=None,
            prev_sibling_id=None,
            is_critical=False,
            planned_start_date=project_schedule.planned_start_date,
            planned_end_date=project_schedule.planned_end_date,
            duration=0,
            actual_start_date=project_schedule.actual_start_date,
            actual_end_date=project_schedule.actual_end_date,
            projected_start_date=project_schedule.projected_start_date,
            projected_end_date=project_schedule.projected_end_date,
            completion_percent=project_schedule.completion_percentage,
            slack=0,
            assignees=[],
            organizations=[],
            attachments=[],
            dependencies=[],
            type=ProjectScheduleActivityTypeEnum.PROJECT,
        )

    def get_activity_history(self, activity_id: int) -> list[dict]:
        activity_logs = (
            ProjectScheduleActivityEvent.objects.filter(activity_id=activity_id, is_derived=False)
            .select_related(
                "created_by",
                "created_by__org",
                "name_update_event_data",
                "parent_update_event_data",
                "parent_update_event_data__from_parent",
                "parent_update_event_data__to_parent",
                "completion_update_event_data",
                "date_update_event_data",
                "created_event_data",
                "deleted_event_data",
                "bulk_removal_event_data",
                "progress_attachment_created_event_data",
                "progress_attachment_deleted_event_data",
            )
            .prefetch_related(
                Prefetch(
                    "assignee_update_event_data",
                    queryset=ProjectScheduleActivityAssigneeUpdateEventData.objects.select_related("assignee__user"),
                ),
                Prefetch(
                    "org_update_event_data",
                    queryset=ProjectScheduleActivityOrgUpdateEventData.objects.select_related(
                        "activity_organization__organization"
                    ),
                ),
                Prefetch(
                    "dependency_update_event_data",
                    queryset=ProjectScheduleActivityDependencyUpdateEventData.objects.select_related(
                        "dependency_mapping__dependency_activity"
                    ),
                ),
            )
        ).order_by("-id")
        return activity_logs

    def get_schedule_history(self, schedule_id: int):
        schedule_logs = (
            ProjectScheduleEvent.objects.filter(schedule_id=schedule_id)
            .prefetch_related(
                Prefetch(
                    "schedule_event_mappings",
                    queryset=ScheduleActivityEventMapping.objects.select_related(
                        "activity_event__created_event_data",
                        "activity_event__deleted_event_data",
                    ),
                )
            )
            .order_by("-id")
        )
        return schedule_logs

    def create_schedule_tags(self, schedule_id: int, tags: list[dict], user_id: int):
        tags_to_be_created: list[ProjectScheduleTag] = []
        for tag in tags:
            tags_to_be_created.append(
                ProjectScheduleTag(
                    schedule_id=schedule_id,
                    name=tag.get("name"),
                    uuid=tag.get("uuid"),
                    created_by_id=user_id,
                )
            )

        created_tags = ProjectScheduleTag.objects.bulk_create(tags_to_be_created)
        return created_tags


class WorkProgressToScheduleRepo(WorkProgressToScheduleAbstractRepository):
    def __init__(self, user_entity: ProjectUserEntity):
        self.org_id = user_entity.org_id
        self.user_id = user_entity.user_id
        self.project_id = user_entity.project_id
        self.current_date = get_local_time(timezone.now()).date()

    def get_today_schedule_update_data(self, timeline_helper: TimelineStatusService) -> TodayScheduleDataEntity | None:
        prefetch_activity_events = Prefetch(
            "events",
            queryset=ProjectScheduleActivityEvent.objects.filter(created_at__date=self.current_date)
            .select_related("completion_update_event_data")
            .order_by("id"),
        )

        prefetch_activity_attachments = Prefetch(
            "progress_attachments",
            queryset=ProjectScheduleActivityProgressAttachment.objects.filter(uploaded_at__date=self.current_date)
            .available()
            .order_by("-uploaded_at"),
        )

        prefetch_activities = Prefetch(
            "activities",
            queryset=ProjectScheduleActivity.objects.prefetch_related(
                prefetch_activity_events,
                prefetch_activity_attachments,
            )
            .order_by("wbs")
            .available(),
        )

        # TODO: Optimize this query, i only want to get the first event of the day
        prefetch_schedule_events = Prefetch(
            "events",
            queryset=ProjectScheduleEvent.objects.filter(created_at__date=self.current_date)
            .select_related("completion_progress_update_event_data")
            .order_by("id"),
        )

        project_schedule = (
            ProjectSchedule.objects.filter(project_id=self.project_id)
            .prefetch_related(prefetch_activities, prefetch_schedule_events)
            .first()
        )

        if not project_schedule:
            return None

        if len(project_schedule.activities.all()) == 0:
            return None

        activities: list[TodayScheduleActivityDataEntity] = []
        attachments: list[TodayScheduleAttachmentEntity] = []

        for activity in project_schedule.activities.all():
            activity: ProjectScheduleActivity

            found_first_completion_event = False
            attachment_uploaded_today = False
            last_day_progress: decimal.Decimal = decimal.Decimal(0)
            for event in activity.events.all():
                if hasattr(event, "completion_update_event_data") and not found_first_completion_event:
                    found_first_completion_event = True
                    last_day_progress = event.completion_update_event_data.from_completion_percentage

            for attachment in activity.progress_attachments.all():
                attachment: ProjectScheduleActivityProgressAttachment
                attachment_uploaded_today = True

                attachments.append(
                    TodayScheduleAttachmentEntity(
                        id=attachment.pk,
                        name=attachment.name,
                        url=attachment.file.url,
                        uploaded_at=attachment.uploaded_at,
                        activity=TodayScheduleAttachmentActivityEntity(
                            id=activity.pk,
                            name=activity.name,
                        ),
                    )
                )

            if not found_first_completion_event and not attachment_uploaded_today:
                continue

            day_progress = activity.completion_percentage - last_day_progress
            status = timeline_helper.get_status_message(
                expected_start_date=activity.planned_start_date,
                expected_due_date=activity.planned_end_date,
                actual_start_date=activity.actual_start_date,
                progress_percentage=activity.completion_percentage,
            )

            activities.append(
                TodayScheduleActivityDataEntity(
                    id=activity.pk,
                    wbs=activity.wbs,
                    name=activity.name,
                    status=TodayScheduleActivityStatusEntity(
                        name=status.name,
                        color_code=status.color_code,
                        status=status.status,
                        days=status.days,
                    ),
                    day_progress=day_progress,
                    total_progress=activity.completion_percentage,
                )
            )

        schedule_status = timeline_helper.get_status_message(
            expected_start_date=project_schedule.planned_start_date,
            expected_due_date=project_schedule.planned_end_date,
            actual_start_date=project_schedule.actual_start_date,
            progress_percentage=project_schedule.completion_percentage,
        )

        for schedule_event in project_schedule.events.all():
            if hasattr(schedule_event, "completion_progress_update_event_data"):
                return TodayScheduleDataEntity(
                    id=project_schedule.pk,
                    today_progress=(
                        project_schedule.completion_percentage
                        - schedule_event.completion_progress_update_event_data.from_completion_percentage
                    ),
                    total_progress=project_schedule.completion_percentage,
                    status=TodayScheduleActivityStatusEntity(
                        name=schedule_status.name,
                        color_code=schedule_status.color_code,
                        status=schedule_status.status,
                        days=schedule_status.days,
                    ),
                    planned_end_date=project_schedule.planned_end_date,
                    activities=activities,
                    attachments=attachments,
                )

        return TodayScheduleDataEntity(
            id=project_schedule.pk,
            today_progress=decimal.Decimal(0),
            total_progress=project_schedule.completion_percentage,
            status=TodayScheduleActivityStatusEntity(
                name=schedule_status.name,
                color_code=schedule_status.color_code,
                status=schedule_status.status,
                days=schedule_status.days,
            ),
            planned_end_date=project_schedule.planned_end_date,
            activities=activities,
            attachments=attachments,
        )

    def get_schedule_update_date_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date, timeline_helper: TimelineStatusService
    ) -> list[DayWiseScheduleDataEntity]:
        """
        Prefetch schedule activity events
        """
        # # Get latest completion update event before start_date for each activity
        # latest_completion_events_subquery = (
        #     ProjectScheduleActivityEvent.objects.filter(
        #         activity_id=OuterRef("activity_id"),
        #         created_at__date__lt=start_date,
        #         completion_update_event_data__isnull=False,
        #     )
        #     .order_by("-created_at")
        #     .values("id")[:1]
        # )

        # # Get latest name update event before start_date for each activity
        # latest_name_events_subquery = (
        #     ProjectScheduleActivityEvent.objects.filter(
        #         activity_id=OuterRef("activity_id"),
        #         created_at__date__lt=start_date,
        #         name_update_event_data__isnull=False,
        #     )
        #     .order_by("-created_at")
        #     .values("id")[:1]
        # )

        # # Get latest date update event before start_date for each activity
        # latest_date_events_subquery = (
        #     ProjectScheduleActivityEvent.objects.filter(
        #         activity_id=OuterRef("activity_id"),
        #         created_at__date__lt=start_date,
        #         date_update_event_data__isnull=False,
        #     )
        #     .order_by("-created_at")
        #     .values("id")[:1]
        # )

        # # Get latest WBS update event before start_date for each activity
        # latest_wbs_events_subquery = (
        #     ProjectScheduleActivityEvent.objects.filter(
        #         activity_id=OuterRef("activity_id"),
        #         created_at__date__lt=start_date,
        #         wbs_update_event_data__isnull=False,
        #     )
        #     .order_by("-created_at")
        #     .values("id")[:1]
        # )

        # # Combine all previous events
        # all_previous_events_subquery = ProjectScheduleActivityEvent.objects.filter(
        #     Q(id__in=latest_completion_events_subquery)
        #     | Q(id__in=latest_name_events_subquery)
        #     | Q(id__in=latest_date_events_subquery)
        #     | Q(id__in=latest_wbs_events_subquery)
        # )

        # # Prefetch the previous events for each activity
        # prefetch_activity_previous_events = Prefetch(
        #     "events",
        #     queryset=all_previous_events_subquery.select_related(
        #         "created_event_data",
        #         "date_update_event_data",
        #         "completion_update_event_data",
        #         "name_update_event_data",
        #         "wbs_update_event_data",
        #     ),
        #     to_attr="previous_events",
        # )

        prefetch_activity_events = Prefetch(
            "events",
            queryset=ProjectScheduleActivityEvent.objects.filter(created_at__date__range=(start_date, end_date))
            .select_related(
                "created_event_data",
                "date_update_event_data",
                "completion_update_event_data",
                "name_update_event_data",
                "wbs_update_event_data",
            )
            .order_by("created_at"),
        )

        prefetch_activity_attachments = Prefetch(
            "progress_attachments",
            queryset=ProjectScheduleActivityProgressAttachment.objects.filter(
                uploaded_at__date__range=(start_date, end_date)
            )
            .order_by("uploaded_at")
            .available(),
        )

        activity_filter = Q(
            Q(events__created_at__date__range=(start_date, end_date))
            | Q(progress_attachments__uploaded_at__date__range=(start_date, end_date))
        )
        prefetch_activities = Prefetch(
            "activities",
            queryset=ProjectScheduleActivity.objects.filter(activity_filter)
            .prefetch_related(
                prefetch_activity_events,
                # prefetch_activity_previous_events,
                prefetch_activity_attachments,
            )
            .order_by("wbs")
            .available(),
        )

        """
        Prefetch schedule events
        """
        prefetch_schedule_events = Prefetch(
            "events",
            queryset=ProjectScheduleEvent.objects.filter(created_at__date__range=(start_date, end_date))
            .select_related("completion_progress_update_event_data", "date_update_event")
            .order_by("id", "created_at"),
        )

        """
        Main Query
        """
        project_schedule = (
            ProjectSchedule.objects.filter(project_id=self.project_id)
            .prefetch_related(
                prefetch_activities,
                prefetch_schedule_events,
            )
            .first()
        )

        if not project_schedule:
            return []

        return self._prepare_daywise_schedule_data(project_schedule=project_schedule, timeline_helper=timeline_helper)

    def _prepare_daywise_schedule_data(
        self, project_schedule: ProjectSchedule, timeline_helper: TimelineStatusService
    ) -> list[DayWiseScheduleDataEntity]:
        datewise_schedule_data: dict[datetime.date, TodayScheduleDataEntity] = {}

        datewise_activity_data_map = self._prepare_datewise_activity_data(
            schedule=project_schedule, timeline_helper=timeline_helper
        )
        datewise_schedule_attachments_map = self._prepare_datewise_attachment_data(schedule=project_schedule)

        schedule_planned_start_date = project_schedule.planned_start_date
        schedule_planned_end_date = project_schedule.planned_end_date
        schedule_actual_start_date = project_schedule.actual_start_date
        schedule_start_percentage = project_schedule.completion_percentage
        schedule_completion_percentage = project_schedule.completion_percentage

        for schedule_event in project_schedule.events.all():
            schedule_event: ProjectScheduleEvent

            event_date = schedule_event.created_at.date()

            if hasattr(schedule_event, "completion_progress_update_event_data"):
                schedule_start_percentage = (
                    schedule_event.completion_progress_update_event_data.from_completion_percentage
                )
                schedule_completion_percentage = (
                    schedule_event.completion_progress_update_event_data.to_completion_percentage
                )

            if hasattr(schedule_event, "date_update_event"):
                schedule_planned_start_date = schedule_event.date_update_event.to_planned_start_date
                schedule_planned_end_date = schedule_event.date_update_event.to_planned_end_date

            schedule_status = timeline_helper.get_status_message(
                expected_start_date=schedule_planned_start_date,
                expected_due_date=schedule_planned_end_date,
                actual_start_date=schedule_actual_start_date,
                progress_percentage=schedule_completion_percentage,
            )

            schedule_activities = []
            if datewise_activity_data_map.get(event_date) is not None:
                schedule_activities = list(datewise_activity_data_map[event_date].values())

            schedule_attachments = []
            if datewise_schedule_attachments_map.get(event_date) is not None:
                schedule_attachments = datewise_schedule_attachments_map[event_date]

            datewise_schedule_data[event_date] = TodayScheduleDataEntity(
                id=project_schedule.pk,
                today_progress=(schedule_completion_percentage - schedule_start_percentage),
                total_progress=schedule_completion_percentage,
                status=TodayScheduleActivityStatusEntity(
                    name=schedule_status.name if schedule_status.name not in DATES_NOT_SET_MESSAGES else "-",
                    color_code=schedule_status.color_code,
                    status=schedule_status.status,
                    days=schedule_status.days,
                ),
                planned_end_date=project_schedule.planned_end_date,
                activities=schedule_activities,
                attachments=schedule_attachments,
            )

        daywise_schedule_data: list[DayWiseScheduleDataEntity] = [
            DayWiseScheduleDataEntity(
                date=date,
                schedule_data=schedule_data,
            )
            for date, schedule_data in datewise_schedule_data.items()
        ]

        return daywise_schedule_data

    def _prepare_datewise_activity_data(
        self, schedule: ProjectSchedule, timeline_helper: TimelineStatusService
    ) -> dict[datetime.date, dict[int, TodayScheduleActivityDataEntity]]:
        datewise_activity_data_map: dict[datetime.date, dict[int, TodayScheduleActivityDataEntity]] = {}

        visited_activity_ids = set()
        visited_event_ids = set()

        for activity in schedule.activities.all():
            activity: ProjectScheduleActivity

            if activity.pk in visited_activity_ids:
                continue
            visited_activity_ids.add(activity.pk)

            activity_start_percentage = activity.completion_percentage
            activity_completion_percentage = activity.completion_percentage
            activity_name = activity.name
            activity_wbs = activity.wbs
            activity_status = timeline_helper.get_status_message(
                expected_start_date=activity.planned_start_date,
                expected_due_date=activity.planned_end_date,
                actual_start_date=activity.actual_start_date,
                progress_percentage=activity.completion_percentage,
            )

            for event in activity.events.all():  # type: ignore
                event: ProjectScheduleActivityEvent

                if event.pk in visited_event_ids:
                    continue
                visited_event_ids.add(event.pk)

                event_date = event.created_at.date()

                if hasattr(event, "date_update_event_data"):
                    activity_status = timeline_helper.get_status_message(
                        expected_start_date=event.date_update_event_data.to_planned_start_date,
                        expected_due_date=event.date_update_event_data.to_planned_end_date,
                        actual_start_date=event.date_update_event_data.to_actual_start_date,
                        progress_percentage=activity_completion_percentage,
                    )

                """
                if 'completion_update_event_data' is not present in any date range,
                then no percentage change in activity progress
                hence 'activity_start_percentage' = 'activity_completion_percentage' = 'activity.completion_percentage'
                """
                if hasattr(event, "completion_update_event_data"):
                    activity_start_percentage = event.completion_update_event_data.from_completion_percentage
                    activity_completion_percentage = event.completion_update_event_data.to_completion_percentage

                if hasattr(event, "name_update_event_data"):
                    activity_name = event.name_update_event_data.to_name

                if hasattr(event, "wbs_update_event_data"):
                    activity_wbs = event.wbs_update_event_data.to_wbs

                status = TodayScheduleActivityStatusEntity(
                    name=activity_status.name if activity_status.name not in DATES_NOT_SET_MESSAGES else "-",
                    color_code=activity_status.color_code,
                    status=activity_status.status,
                    days=activity_status.days,
                )

                if event_date not in datewise_activity_data_map:
                    datewise_activity_data_map[event_date] = {}

                if activity.pk not in datewise_activity_data_map[event_date]:
                    activity_data = TodayScheduleActivityDataEntity(
                        id=activity.pk,
                        wbs=activity_wbs,
                        name=activity_name,
                        status=status,
                        day_progress=activity_completion_percentage - activity_start_percentage,
                        total_progress=activity_completion_percentage,
                    )
                    datewise_activity_data_map[event_date][activity.pk] = activity_data
                else:
                    activity_data = datewise_activity_data_map[event_date][activity.pk]
                    # update activity data with latest updated event data
                    activity_data.status = status
                    activity_data.name = activity_name
                    activity_data.wbs = activity_wbs
                    activity_data.day_progress += activity_completion_percentage - activity_start_percentage
                    activity_data.total_progress = activity_completion_percentage

                # update activity starting percent for next iteration
                activity_start_percentage = activity_completion_percentage

        return datewise_activity_data_map

    def _prepare_datewise_attachment_data(
        self, schedule: ProjectSchedule
    ) -> dict[datetime.date, list[TodayScheduleAttachmentEntity]]:
        datewise_attachments_map: dict[datetime.date, list[TodayScheduleAttachmentEntity]] = {}

        visited_activity_ids = set()
        visited_attachment_ids = set()

        for activity in schedule.activities.all():
            activity: ProjectScheduleActivity

            if activity.pk in visited_activity_ids:
                continue
            visited_activity_ids.add(activity.pk)

            for attachment in activity.progress_attachments.all():
                attachment: ProjectScheduleActivityProgressAttachment

                if attachment.pk in visited_attachment_ids:
                    continue
                visited_attachment_ids.add(attachment.pk)

                attachment_date = attachment.uploaded_at.date()

                if attachment_date not in datewise_attachments_map:
                    datewise_attachments_map[attachment_date] = []

                datewise_attachments_map[attachment_date].append(
                    TodayScheduleAttachmentEntity(
                        id=attachment.pk,
                        name=attachment.name,
                        url=attachment.file.url,
                        uploaded_at=attachment.uploaded_at,
                        activity=TodayScheduleAttachmentActivityEntity(id=activity.pk, name=activity.name),
                    )
                )

        return datewise_attachments_map

    def get_hashtag_activity_list(self) -> list[HashtagElementEntity]:
        project_schedule = ProjectSchedule.objects.filter(project_id=self.project_id).values("id").first()

        if not project_schedule:
            return []

        project_schedule_activities = (
            ProjectScheduleActivity.objects.filter(project_schedule_id=project_schedule["id"])
            .order_by("name")
            .available()
            .values("id", "name")
        )

        entities: list[HashtagElementEntity] = []
        for activity in project_schedule_activities:
            entities.append(HashtagElementEntity(id=activity["id"], name=activity["name"]))

        return entities
