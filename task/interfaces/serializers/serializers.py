from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from approval_request.domain.constants import RequestError<PERSON>odeEnum
from authorization.domain.constants import Permissions
from boq.data.models import BoqElement
from commentv2.domain.constants import BREADCRUMB_CONTEXT_MAPPINGS
from commentv2.interface.serializers import CommentReplyModelSerializer as _CommentReplyModelSerializer
from common.choices import OrderType
from common.element_base.services import ElementCodeService
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer, TypeSerializer
from common.serializers import BaseModelSerializer, CustomDateField, HashId<PERSON>ield
from common.utils import formatINR
from core.caches import UnitOfMeasurementCache
from core.helpers import OrgPermissionHelper
from core.serializers import BaseSerializer, UserProfileSerializer, UserSerializer
from crm.data.models import Lead
from design.data.models import DesignFile
from inventory.data.models import InventoryBatch
from inventory.domain.enums import StockBatchType
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import <PERSON>Context
from order.data.models import VendorOrder, VendorOrderElement
from progressreport.models import ProgressReport
from project.data.models import Project
from project.domain.helpers import ProjectPermissionHelper
from project_schedule.data.models import ProjectScheduleActivity
from proposal.data.models import Proposal, ProposalElementMapping
from recce.data.models import RecceFile
from rollingbanners.hash_id_converter import HashIdConverter
from rollingbanners.storage_backends import PublicMediaFileStorage
from snags.data.models import Snag
from task.data.choices import TaskTypeChoices
from task.data.models import Task, TaskAssignment, TaskComment


class AttachmentSerializer(BaseSerializer):
    file_name = serializers.CharField()
    url = serializers.URLField()
    type = serializers.CharField()

    class Meta:
        ref_name = "TaskAssignmentAttachmentSerializer"


class TaskAssignementModelSerializer(BaseModelSerializer):
    task_id = HashIdField(source="task.id")
    description = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()
    due_at = CustomDateField(source="task.due_at")
    assignee_id = HashIdField(source="task_assignee_id")
    assignee = serializers.SerializerMethodField()
    project_id = HashIdField(source="task.project_id")
    context = serializers.SerializerMethodField()
    context_id = HashIdField(source="task.context_id")
    comment_id = HashIdField(source="task.comment_id")
    request_id = HashIdField(source="task.request_id")
    snapshot_id = HashIdField(source="task.snapshot_id", allow_null=True)
    created_by = UserProfileSerializer(source="task.created_by")
    created_at = serializers.DateTimeField(source="task.created_at")
    status = serializers.CharField(source="task.status")
    error_code = serializers.ChoiceField(source="task.error_code", choices=RequestErrorCodeEnum.choices)

    @swagger_serializer_method(
        serializer_or_field=serializers.ChoiceField(source="task.context", choices=MicroContextChoices.choices)
    )
    def get_context(self, obj):
        if obj.task.context == MicroContext.INSTA_ORDER.value:
            return MicroContext.ORDER.value
        return obj.task.context

    @swagger_serializer_method(serializer_or_field=UserSerializer())
    def get_assignee(self, obj):
        if obj.assignee_data is None:
            return None
        return {
            "id": HashIdConverter.encode(obj.assignee_data.get("id")),
            "name": obj.assignee_data.get("name"),
            "photo": obj.assignee_data.get("photo"),
        }

    @swagger_serializer_method(
        serializer_or_field=serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
    )
    def get_description(self, obj):
        serializer = TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
        attachments = []
        description = []
        for block in obj.task.description:
            data = serializer.to_representation(block)
            if block.get("type") in (const.ATTACHMENT, const.IMAGE):
                attachments.append(
                    {
                        "type": block.get("type"),
                        "file_name": block.get("meta").get("file_name"),
                        "url": PublicMediaFileStorage.url(block.get("meta").get("url")),
                    }
                )
            else:
                description.append(data)

        self.context["attachments"] = attachments
        return description

    @swagger_serializer_method(serializer_or_field=AttachmentSerializer(many=True))
    def get_attachments(self, obj):
        return AttachmentSerializer(self.context.get("attachments"), many=True).data

    class Meta:
        model = TaskAssignment
        fields = "__all__"
        ref_name = "TaskAssignmentModelSerializer"


class TaskAssignmentDetailSerializer(TaskAssignementModelSerializer):
    snapshot_id = HashIdField(allow_null=True)
    last_activity_timestamp = serializers.SerializerMethodField()
    type = serializers.ChoiceField(source="task.type", choices=TaskTypeChoices.choices)

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_last_activity_timestamp(self, obj):
        if hasattr(obj, "last_activity_timestamp") and obj.last_activity_timestamp:
            return obj.last_activity_timestamp

    class Meta(TaskAssignementModelSerializer.Meta):
        fields = (
            "id",
            "task_id",
            "assignee",
            "created_by",
            "created_at",
            "latest_title",
            "title",
            "title_color",
            "title_background",
            "description",
            "attachments",
            "preview",
            "due_at",
            "is_unread",
            "actions",
            "remind_at",
            "buckets",
            "comment_id",
            "context",
            "context_id",
            "request_id",
            "project_id",
            "is_delay",
            "status",
            "error_code",
            "snapshot_id",
            "last_activity_timestamp",
            "type",
        )
        ref_name = "TaskAssignmentDetailSerializer"


class TaskCreateUpdateInputSerializer(BaseSerializer):
    description = serializers.ListField(
        child=TypeSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.LINE_BREAK: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
    )
    assignee_id = HashIdField(required=False, default=None, allow_null=True)
    due_at = CustomDateField(required=False, default=None, allow_null=True)
    context_id = HashIdField(required=False, default=None, allow_null=True)
    context = serializers.ChoiceField(
        required=False, default=None, allow_null=True, choices=MicroContextChoices.choices
    )

    def validate(self, attrs):
        data = super().validate(attrs)
        if data.get("context") and not data.get("context_id"):
            raise serializers.ValidationError({"context_id": "This field is required."})
        return data

    class Meta:
        ref_name = "TaskCreateUpdateInputSerializer"


class TaskReplyCreateInputSerializer(BaseSerializer):
    description = serializers.ListField(
        child=TypeSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.LINE_BREAK: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
    )

    class Meta:
        ref_name = "TaskReplyCreateInputSerializer"


class TaskCommentModelSerializer(BaseModelSerializer):
    user = UserProfileSerializer()
    description = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()

    @swagger_serializer_method(
        serializer_or_field=serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
    )
    def get_description(self, obj):
        serializer = TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
        attachments = []
        description = []
        for block in obj.blocks:
            data = serializer.to_representation(block)
            if block.get("type") in (const.ATTACHMENT, const.IMAGE):
                attachments.append(
                    {
                        "type": block.get("type"),
                        "file_name": block.get("meta").get("file_name"),
                        "url": PublicMediaFileStorage.url(block.get("meta").get("url")),
                    }
                )
            else:
                description.append(data)

        self.context["attachments"] = attachments
        return description

    @swagger_serializer_method(serializer_or_field=AttachmentSerializer(many=True))
    def get_attachments(self, obj):
        return AttachmentSerializer(self.context.get("attachments"), many=True).data

    class Meta:
        model = TaskComment
        fields = ("id", "user", "description", "commented_at", "type", "attachments")
        ref_name = "TaskCommentModelSerializer"


class CommentReplyModelSerializer(_CommentReplyModelSerializer):
    description = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()

    @swagger_serializer_method(
        serializer_or_field=serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
    )
    def get_description(self, obj):
        serializer = TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
        attachments = []
        description = []
        for block in obj.blocks:
            data = serializer.to_representation(block)
            if block.get("type") in (const.ATTACHMENT, const.IMAGE):
                attachments.append(
                    {
                        "type": block.get("type"),
                        "file_name": block.get("meta").get("file_name"),
                        "url": PublicMediaFileStorage.url(block.get("meta").get("url")),
                    }
                )
            else:
                description.append(data)

        self.context["attachments"] = attachments
        return description

    @swagger_serializer_method(serializer_or_field=AttachmentSerializer(many=True))
    def get_attachments(self, obj):
        return AttachmentSerializer(self.context.get("attachments"), many=True).data

    class Meta(_CommentReplyModelSerializer.Meta):
        fields = (
            "id",
            "user",
            "commented_at",
            "description",
            "attachments",
        )
        ref_name = "CommentReplyModelSerializer"


class TaskContextDetailsSerializer(BaseSerializer):
    breadcrumb = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    meta_data = serializers.SerializerMethodField()
    show_tags = serializers.SerializerMethodField()
    project_id = serializers.SerializerMethodField()
    context = serializers.SerializerMethodField()
    context_id = serializers.SerializerMethodField()

    def get_project_id(self, obj):
        return self.context.get("task").project_id

    def get_context(self, obj):
        if self.context.get("task").context == MicroContext.INSTA_ORDER.value:
            return MicroContext.ORDER.value
        return self.context.get("task").context

    def get_context_id(self, obj):
        return self.context.get("task").context_id

    @staticmethod
    def get_file_name(file_path):
        return file_path.split("/")[-1]

    @staticmethod
    def format_datetime(dt, with_time=True):
        return dt.strftime("%d %b %Y, %I:%M %p" if with_time else "%d %b %Y")

    @staticmethod
    def get_meta_data(obj):
        return {}

    @staticmethod
    def get_thumbnail(obj):
        return None

    @staticmethod
    def get_show_tags(obj):
        return False

    @staticmethod
    def get_description_obj(text: str, color_code: str = "#3d3d52"):
        return {"text": text, "color": color_code}

    class Meta:
        ref_name = "TaskContextDetailsSerializer"
        fields = (
            "breadcrumb",
            "name",
            "description",
            "thumbnail",
            "meta_data",
            "show_tags",
            "project_id",
            "context",
            "context_id",
        )
        output_hash_id_fields = ("project_id", "context_id")


class TaskProjectContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    @staticmethod
    def get_breadcrumb(obj):
        return f"{obj.job_id} . {obj.name}"

    @staticmethod
    def get_name(obj):
        return obj.name

    @classmethod
    def get_description(cls, obj):
        return [cls.get_description_obj(obj.store.full_address)]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = Project
        ref_name = "TaskProjectContextDetailsSerializer"


class TaskRecceContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} > Recce > {BREADCRUMB_CONTEXT_MAPPINGS.get(self.context.get('task').context)} > {self.get_name(obj)}"  # noqa

    def get_name(self, obj):
        return obj.name if obj.name else self.get_file_name(obj.file.url)

    def get_description(self, obj):
        return [
            self.get_description_obj(
                f"Uploaded By : {obj.uploaded_by.name if obj.uploaded_by else 'Guest User'}, On : {self.format_datetime(obj.uploaded_at)}"  # noqa
            )
        ]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = RecceFile
        ref_name = "TaskRecceFileContextDetailsSerializer"


class TaskDesignContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Design > V{obj.version} > {BREADCRUMB_CONTEXT_MAPPINGS.get(self.context.get('task').context)} > {self.get_name(obj)}"  # noqa

    def get_name(self, obj):
        return obj.design_file.name if obj.design_file.name else self.get_file_name(obj.file.url)

    def get_description(self, obj):
        return [
            self.get_description_obj(
                f"Uploaded By : {obj.uploaded_by.name if obj.uploaded_by else 'Guest User'}, On : {self.format_datetime(obj.uploaded_at)}"  # noqa
            )
        ]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = DesignFile
        ref_name = "TaskDesignFileContextDetailsSerializer"


class ScopeItemContextDetailsMixin:
    def get_description(self, obj):
        code = ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.version
        )
        desc = []
        hidden_fields: list[str] = self.context.get("hidden_fields", [])
        if obj.item_type:
            desc.append(self.get_description_obj(obj.item_type.name.title(), color_code=obj.item_type.color_code))
        desc.extend(
            [
                self.get_description_obj(obj.category.name.title()),
                self.get_description_obj(code),
            ]
        )
        if "uom" not in hidden_fields:
            desc.append(self.get_description_obj(UnitOfMeasurementCache.get().get(str(obj.uom))))
        if "quantity" not in hidden_fields:
            desc.append(self.get_description_obj(str(obj.quantity)))
        if (
            OrgPermissionHelper.has_permission(
                user=self.context.get("user"), permission=Permissions.CAN_VIEW_CLIENT_RATE
            )
            and "client_rate" not in hidden_fields
        ):
            desc.append(self.get_description_obj(f"Rate: {obj.client_rate}"))
        return desc

    @staticmethod
    def get_show_tags(obj):
        return True


class TaskScopeItemContextDetailsSerializer(
    ScopeItemContextDetailsMixin, TaskContextDetailsSerializer, BaseModelSerializer
):
    def get_breadcrumb(self, obj):
        code = ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.version
        )
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > BOQ > My Active Scope > {code}"  # noqa

    def get_name(self, obj):
        return obj.name

    def get_thumbnail(self, obj):
        if obj.preview_files.count() == 0:
            return None
        return obj.preview_files.all()[0].file.url if obj.preview_files.all()[0].file else None

    class Meta(TaskContextDetailsSerializer.Meta):
        model = BoqElement
        ref_name = "TaskScopeItemContextDesignSerializer"


class TaskOrderContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        user = self.context.get("user")
        order_type = OrderType.OUTGOING if user.org_id == obj.org_from_id else OrderType.INCOMING
        self.context["order_type"] = order_type

        if order_type == OrderType.OUTGOING:
            return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Orders > Outgoing Order > {self.context.get('task').project.job_id}/{obj.order_number}"  # noqa
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > BOQ > Incoming Order > {self.context.get('task').project.job_id}/{obj.order_number}"  # noqa

    def get_name(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.order_number}"  # noqa

    def get_description(self, obj):
        if self.context.get("order_type") == OrderType.OUTGOING:
            return (
                [
                    self.get_description_obj(
                        f"Order to: {obj.org_to.name} ({obj.org_to.vendor.code}) , Created_by: {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}"  # noqa
                    )
                ]
                if obj.org_to
                else [
                    self.get_description_obj(
                        f"Order to: - , Created_by: {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}"  # noqa
                    )
                ]
            )
        return [
            self.get_description_obj(
                f"Order from: {obj.org_from.name} ({obj.org_from.client.code}), Created_by: {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}"  # noqa
            )
        ]

    def get_meta_data(self, obj):
        return {
            "order_type": self.context.get("order_type"),
            "order_number": f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.order_number}",  # noqa
        }

    class Meta(TaskContextDetailsSerializer.Meta):
        model = VendorOrder
        ref_name = "TaskOrderContextDesignSerializer"


class TaskOrderItemContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        user = self.context.get("user")
        order_type = OrderType.OUTGOING if user.org_id == obj.vendor_order.org_from_id else OrderType.INCOMING
        self.context["order_type"] = order_type
        if order_type == OrderType.OUTGOING:
            return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Orders > Outgoing Order > {self.context.get('task').project.job_id}/{obj.vendor_order.order_number} > {obj.name}"  # noqa
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > BOQ > Incoming Order > {self.context.get('task').project.job_id}/{obj.vendor_order.order_number} > {obj.name}"  # noqa

    def get_name(self, obj):
        return obj.name

    def get_description(self, obj):
        code = ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.boq_element_version
        )
        desc = []
        hidden_fields: list[str] = self.context.get("hidden_fields", [])
        if obj.item_type:
            desc.append(self.get_description_obj(obj.item_type.name.title(), color_code=obj.item_type.color_code))

        desc.extend(
            [
                self.get_description_obj(obj.category.name.title()),
                self.get_description_obj(code),
            ]
        )
        if "uom" not in hidden_fields:
            desc.append(self.get_description_obj(UnitOfMeasurementCache.get().get(str(obj.uom))))
        if "quantity" not in hidden_fields:
            desc.append(self.get_description_obj(str(obj.quantity)))
        if (
            ProjectPermissionHelper.has_permission(
                project_id=self.context.get("task").project.id,
                user=self.context.get("user"),
                permission=Permissions.CAN_VIEW_ORDER_RATE,
            )
            and "vendor_rate" not in hidden_fields
        ):
            desc.append(self.get_description_obj(f"Rate: {obj.vendor_rate}"))

        return desc

    @staticmethod
    def get_show_tags(obj):
        return True

    def get_meta_data(self, obj):
        return {
            "order_type": self.context.get("order_type"),
            "order_number": f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.vendor_order.order_number}",  # noqa
            "name": obj.name,
            "order_id": HashIdConverter.encode(obj.vendor_order_id),
        }

    def get_thumbnail(self, obj):
        if obj.preview_files.count() == 0:
            return None
        return obj.preview_files.all()[0].file.url if obj.preview_files.all()[0].file else None

    class Meta(TaskContextDetailsSerializer.Meta):
        model = VendorOrderElement
        ref_name = "TaskOrderItemContextDesignSerializer"


class TaskProgressReportContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Work Progress > DPR "  # noqa

    def get_name(self, obj):
        return "Work Progress Report"

    def get_description(self, obj):
        return [
            self.get_description_obj(f"Created By : {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}")
        ]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = ProgressReport
        ref_name = "TaskProgressReportContextDesignSerializer"


class TaskSnagContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return (
            f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Snags > {obj.code}"
        )

    def get_name(self, obj):
        return obj.title

    def get_description(self, obj):
        if obj.snag_category:
            return [self.get_description_obj(f"Snag Category: {obj.snag_category.name}")]
        return []

    class Meta(TaskContextDetailsSerializer.Meta):
        model = Snag
        ref_name = "TaskSnagContextDesignSerializer"


class TaskProposalContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        user = self.context.get("user")
        proposal_type = "outgoing" if user.org_id == obj.proposal_from_id else "incoming"
        self.context["proposal_type"] = proposal_type
        if proposal_type == "outgoing":
            return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > BOQ > Proposal For Client > Proposal Ref. No: {self.context.get('task').project.job_id}/{obj.ref_number}"  # noqa
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Order > Proposal From Vendor > Proposal Ref. No: {self.context.get('task').project.job_id}/{obj.ref_number}"  # noqa

    def get_name(self, obj):
        return f"Proposal Ref. No: {self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.ref_number}"  # noqa

    def get_description(self, obj):
        if self.context.get("proposal_type") == "outgoing":
            return [
                self.get_description_obj(
                    f"Proposal For: {obj.proposal_for.name} ({obj.proposal_for.client.code}), Created By : {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}"  # noqa
                )
            ]
        return [
            self.get_description_obj(
                f"Proposal From: {obj.proposal_from.name} ({obj.proposal_from.vendor.code}), Created By : {obj.created_by.name}, On : {self.format_datetime(obj.created_at)}"  # noqa
            )
        ]

    def get_meta_data(self, obj):
        return {
            "proposal_code": f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.ref_number}",  # noqa
            "proposal_type": self.context.get("proposal_type"),
        }

    class Meta(TaskContextDetailsSerializer.Meta):
        model = Proposal
        ref_name = "TaskProposalContextDesignSerializer"


class TaskProposalItemContextDetailsSerializer(
    ScopeItemContextDetailsMixin, TaskContextDetailsSerializer, BaseModelSerializer
):
    def get_breadcrumb(self, obj):
        user = self.context.get("user")
        proposal_type = "outgoing" if user.org_id == obj.proposal.proposal_from_id else "incoming"
        self.context["proposal_type"] = proposal_type
        if proposal_type == "outgoing":
            return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > BOQ > Proposal For Client Item > Proposal Ref. No: {obj.proposal.ref_number} > {obj.name}"  # noqa
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Order > Proposal From Vendor Item > Proposal Ref. No: {obj.proposal.ref_number} > {obj.name}"  # noqa

    def get_name(self, obj):
        return obj.name

    def get_meta_data(self, obj):
        return {
            "proposal_type": self.context.get("proposal_type"),
            "proposal_code": f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name}/{obj.proposal.ref_number}",  # noqa
            "proposal_id": HashIdConverter.encode(obj.proposal_id),
            "item_code": ElementCodeService.get_code(
                serial_number=obj.serial_number, code=obj.code, custom_type=obj.custom_type, version=obj.version
            ),
        }

    def get_thumbnail(self, obj):
        if obj.preview_files.count() == 0:
            return None
        return obj.preview_files.all()[0].file.url if obj.preview_files.all()[0].file else None

    class Meta(TaskContextDetailsSerializer.Meta):
        model = ProposalElementMapping
        ref_name = "TaskProposalItemContextDesignSerializer"


class TaskInvoiceContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Order > Vendor Invoice > Invoice No: {obj.invoice_number}"  # noqa

    def get_name(self, obj):
        return obj.invoice_number

    def get_thumbnail(self, obj):
        return obj.file.url if obj.file else None

    def get_description(self, obj):
        return [
            self.get_description_obj(
                f"Amount: {obj.amount}, Uploaded By : {obj.uploaded_by.name if obj.uploaded_by else 'Guest User'}, On : {self.format_datetime(obj.uploaded_at)}"  # noqa
            )
        ]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = BoqElement
        ref_name = "TaskScopeItemContextDesignSerializer"


class TaskLeadContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"Leads > {obj.board.name} > {obj.name}"

    def get_name(self, obj):
        return obj.name

    def get_description(self, obj):
        return [self.get_description_obj(f"Company: {obj.company.name if obj.company_id else '-'}")]  # noqa

    def get_meta_data(self, obj):
        return {"board_id": HashIdConverter.encode(obj.board_id), "stage_id": HashIdConverter.encode(obj.stage_id)}

    class Meta(TaskContextDetailsSerializer.Meta):
        model = Lead
        ref_name = "TaskLeadContextDesignSerializer"


class TaskInventoryBatchContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Work Progess > Materials > Stock Received > {obj.full_grn_number}"  # noqa

    def get_name(self, obj):
        return obj.full_grn_number

    def get_description(self, obj):
        created_by_name = obj.created_by.name.title() if obj.created_by else ""
        if obj.source == StockBatchType.ORDER.value and obj.order_id:
            return [
                self.get_description_obj(
                    f"Order - {obj.project.job_id}/{obj.order.order_number}, {created_by_name}, {self.format_datetime(obj.created_at)}, Stock Value :  {formatINR(round(obj.stock_value, 2))}"  # noqa
                )
            ]
        elif obj.source == StockBatchType.ADHOC.value:
            return [
                self.get_description_obj(
                    f"Ad-hoc, {created_by_name}, {self.format_datetime(obj.created_at)}, Stock Value :  {formatINR(round(obj.stock_value, 2))}"  # noqa
                )
            ]
        else:
            return [
                self.get_description_obj(
                    f"Stock Transfer, {created_by_name}, {self.format_datetime(obj.created_at)}, Stock Value :  {formatINR(round(obj.stock_value, 2))}"  # noqa
                )
            ]

    class Meta(TaskContextDetailsSerializer.Meta):
        model = InventoryBatch
        ref_name = "TaskScopeItemContextDesignSerializer"


class TaskProgressReportItemContextDetailsSerializer(
    ScopeItemContextDetailsMixin, TaskContextDetailsSerializer, BaseModelSerializer
):
    def get_breadcrumb(self, obj):
        code = ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.version
        )
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Work Progress > Progress Update > {code} "  # noqa

    def get_name(self, obj):
        return obj.name

    def get_thumbnail(self, obj):
        if obj.preview_files.count() == 0:
            return None
        return obj.preview_files.all()[0].file.url if obj.preview_files.all()[0].file else None

    class Meta(TaskContextDetailsSerializer.Meta):
        model = BoqElement
        ref_name = "TaskScopeItemContextDesignSerializer"


class TaskProjectScheduleActivityContextDetailsSerializer(TaskContextDetailsSerializer, BaseModelSerializer):
    def get_breadcrumb(self, obj):
        return f"{self.context.get('task').project.job_id} . {self.context.get('task').project.name} > Activity Schedule > {obj.name}"  # noqa

    def get_name(self, obj):
        return obj.name

    def get_description(self, obj: ProjectScheduleActivity):
        return [
            self.get_description_obj(
                f"{obj.wbs} . Planned Duration : "
                f"{obj.planned_start_date.strftime('%d %b %Y')} - {obj.planned_end_date.strftime('%d %b %Y')}"
            )
        ]

    @staticmethod
    def get_meta_data(obj: ProjectScheduleActivity):
        return {
            "uuid": obj.uuid,
        }

    class Meta(TaskContextDetailsSerializer.Meta):
        model = ProjectScheduleActivity
        ref_name = "TaskProjectScheduleActivityContextDetailsSerializer"


class TaskModelSerializer(BaseModelSerializer):
    task_id = HashIdField(source="id")
    description = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()
    created_by = UserProfileSerializer()
    assignee = serializers.SerializerMethodField()
    assignee_id = HashIdField(source="task_assignee_id")
    comment_id = HashIdField()
    project_id = HashIdField()
    context_id = HashIdField()
    request_id = HashIdField()
    context = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()
    is_unread = serializers.BooleanField(default=False, required=False)
    remind_at = serializers.DateTimeField(default=None, required=False, allow_null=True)
    snapshot_id = HashIdField(allow_null=True)
    due_at = CustomDateField()

    @swagger_serializer_method(
        serializer_or_field=serializers.ChoiceField(source="task.context", choices=MicroContextChoices.choices)
    )
    def get_context(self, obj):
        if obj.context == MicroContext.INSTA_ORDER.value:
            return MicroContext.ORDER.value
        return obj.context

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        user = self.context.get("user")
        is_admin = self.context.get("is_admin")
        return obj.actions(user=user, is_admin=is_admin)

    @swagger_serializer_method(serializer_or_field=UserSerializer())
    def get_assignee(self, obj):
        if not hasattr(obj, "assignee_data") or obj.assignee_data is None:
            return None
        return {
            "id": HashIdConverter.encode(obj.assignee_data.get("id")),
            "name": obj.assignee_data.get("name"),
            "photo": (
                PublicMediaFileStorage.url(obj.assignee_data.get("photo")) if obj.assignee_data.get("photo") else ""
            ),
        }

    @swagger_serializer_method(
        serializer_or_field=serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    }
                }
            )
        )
    )
    def get_description(self, obj):
        serializer = TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.MENTION: None,
                    const.TEXT: TEXT_TYPE_LIST,
                    const.LINE_BREAK: None,
                    const.ATTACHMENT: None,
                    const.IMAGE: None,
                    const.HYPERLINK: TEXT_TYPE_LIST,
                }
            }
        )
        attachments = []
        description = []
        for block in obj.description:
            data = serializer.to_representation(block)
            if block.get("type") in (const.ATTACHMENT, const.IMAGE):
                attachments.append(
                    {
                        "type": block.get("type"),
                        "file_name": block.get("meta").get("file_name"),
                        "url": PublicMediaFileStorage.url(block.get("meta").get("url")),
                    }
                )
            else:
                description.append(data)

        self.context["attachments"] = attachments
        return description

    @swagger_serializer_method(serializer_or_field=AttachmentSerializer(many=True))
    def get_attachments(self, obj):
        return AttachmentSerializer(self.context.get("attachments"), many=True).data

    class Meta:
        model = Task
        fields = "__all__"
        ref_name = "TaskModelSerializer"


class TaskDetailSerializer(TaskModelSerializer):
    snapshot_id = HashIdField(allow_null=True)
    last_activity_timestamp = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_last_activity_timestamp(self, obj):
        if hasattr(obj, "last_activity_timestamp") and obj.last_activity_timestamp:
            return obj.last_activity_timestamp

    class Meta(TaskModelSerializer.Meta):
        fields = (
            "id",
            "task_id",
            "assignee",
            "created_by",
            "created_at",
            "latest_title",
            "title",
            "title_color",
            "title_background",
            "description",
            "attachments",
            "preview",
            "due_at",
            "actions",
            "comment_id",
            "context",
            "context_id",
            "request_id",
            "status",
            "is_unread",
            "buckets",
            "remind_at",
            "is_delay",
            "project_id",
            "error_code",
            "snapshot_id",
            "last_activity_timestamp",
            "type",
        )
        ref_name = "TaskDetailSerializer"
