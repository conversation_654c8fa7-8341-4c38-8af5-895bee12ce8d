from django.urls import path

from task.interfaces.apis import (
    AllRequestsApi,
    AllTaskApi,
    JobIdListApi,
    ProjectListApi,
    TaskApprovalRequiresApi,
    TaskArchiveForAllApi,
    TaskAssigneeListApi,
    TaskBucketListApi,
    TaskContextDetailsApi,
    TaskCreateApi,
    TaskCreatorListApi,
    TaskDetailsApi,
    TaskDoneApi,
    TaskImmediatelyPendingApi,
    TaskLandingDataApi,
    TaskListApi,
    TaskReadApi,
    TaskRemindApi,
    TaskReplyCreateApi,
    TaskReplyListApi,
    TaskRequesteesApi,
    TaskSelfArchiveApi,
    TaskUndoApi,
    TaskUnDoneApi,
    TaskUpdateApi,
)

urlpatterns = [
    path("bucket/list/", TaskBucketListApi.as_view(), name="bucket-list"),
    path("list/", TaskListApi.as_view(), name="task-list"),
    path("create/", TaskCreateApi.as_view(), name="task-create"),
    path("<hash_id:task_id>/details/", TaskDetailsApi.as_view(), name="task-details"),
    path("<hash_id:task_id>/update/", TaskUpdateApi.as_view(), name="task-update"),
    path("<hash_id:task_id>/read/", TaskReadApi.as_view(), name="task-read"),
    path("<hash_id:task_id>/done/", TaskDoneApi.as_view(), name="task-done"),
    path("<hash_id:task_id>/undo/", TaskUndoApi.as_view(), name="task-undo"),
    path("<hash_id:task_id>/undone/", TaskUnDoneApi.as_view(), name="task-undone"),
    path("<hash_id:task_id>/remind/", TaskRemindApi.as_view(), name="task-remind"),
    path("<hash_id:task_id>/reply/", TaskReplyCreateApi.as_view(), name="task-reply"),
    path("<hash_id:task_id>/reply/list/", TaskReplyListApi.as_view(), name="task-reply-list"),
    path("<hash_id:task_id>/scope/", TaskContextDetailsApi.as_view(), name="task-context-details"),
    path("<hash_id:task_id>/archive/", TaskArchiveForAllApi.as_view(), name="task-archive"),
    path("<hash_id:task_id>/self-archive/", TaskSelfArchiveApi.as_view(), name="task-self-archive"),
    path("requestees/", TaskRequesteesApi.as_view(), name="task-requestees"),
    path("immediately-pending/", TaskImmediatelyPendingApi.as_view(), name="task-immediately-pending"),
    path("requires-approval/", TaskApprovalRequiresApi.as_view(), name="task-approval-requires"),
    path("project-list/", ProjectListApi.as_view(), name="project-list"),
    path("job-id-list/", JobIdListApi.as_view(), name="job-id-list"),
    path("assignee-list/", TaskAssigneeListApi.as_view(), name="task-assignee-list"),
    path("creator-list/", TaskCreatorListApi.as_view(), name="task-creator-list"),
    path("all-requests/", AllRequestsApi.as_view(), name="all-requests"),
    path("all-tasks/", AllTaskApi.as_view(), name="all-tasks"),
    path("<hash_id:task_id>/landing-data/", TaskLandingDataApi.as_view(), name="task-landing-data"),
]
