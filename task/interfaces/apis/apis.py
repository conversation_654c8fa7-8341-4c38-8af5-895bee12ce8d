import structlog
from django.conf import settings
from django.db import transaction
from django.db.models import Case, F, Prefetch, QuerySet, When
from django.utils import timezone
from django.utils.module_loading import import_string
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.mixins import ListModelMixin
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_202_ACCEPTED,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
)

from approval_request.domain.constants import RequestStatusEnum
from boq.data.models import BoqElement, BoqElementPreviewFile
from commentv2.data.selectors import get_comment_replies
from commentv2.domain.constants import CommentTypeEnum
from commentv2.domain.entities import CommentReplyData
from commentv2.domain.services.services import comment_reply_create
from common.apis import Base<PERSON><PERSON>
from common.serializers import (
    BaseSerial<PERSON>,
    CustomDate<PERSON>ield,
    <PERSON><PERSON>nd<PERSON>ate<PERSON>ield,
    SearchTextFilterSerializer,
    SplitChar<PERSON>ash<PERSON>d<PERSON>ist<PERSON>ield,
    SplitCharListField,
)
from core.apis import Org<PERSON><PERSON><PERSON><PERSON>
from core.caches import BaseCache
from core.models import User
from core.serializers import UserSerializer
from crm.data.models import Lead
from crm.interface.factories.factories import LeadEventsServiceFactory
from design.data.models import DesignFileVersion
from inventory.data.models import InventoryBatch
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import MicroContext
from order.data.models import VendorOrder, VendorOrderElement, VendorOrderElementPreviewFile
from order.domain.mappings import ORDER_FIELDS_MAPPING
from order.invoice.data.selectors import invoice_fetch_all
from progressreport.models import ProgressReport
from project.selectors import project_fetch_all, project_fetch_all_using_immediate_client_ids
from project.serializers import ProjectListSerializer
from project_schedule.data.models import ProjectScheduleActivity
from proposal.data.models import Proposal, ProposalElementMapping, ProposalElementPreviewFile
from proposal.domain.services import get_hidden_fields
from proposal.interface.mappings import PROPOSAL_FIELDS_MAPPING
from recce.data.models import RecceFile
from snags.data.models import Snag
from task.data.models import Task, TaskAssignment
from task.data.querysets import TaskAssignmentQuerySet, TaskQuerySet
from task.data.repositories import TaskRepo, TaskTitleRepo
from task.data.selectors import (
    approvers_fetch_using_user,
    get_task_page_and_bucket,
    immediate_approvers_fetch_using_user,
    org_all_request_task_fetch,
    org_all_task_fetch,
    project_fetch_using_user,
    requestee_fetch_using_user,
    task_assignees_fetch_using_user,
    task_assignment_fetch_using_user,
    task_bucket_data_get,
    task_comment_fetch_using_task,
    task_creator_fetch_using_user,
    task_fetch_all,
)
from task.domain.constants import (
    RequestTypeEnum,
    TaskAssignedAsEnum,
    TaskBucketsEnum,
    TaskOrderingEnum,
)
from task.domain.entities import (
    PureTaskCreateServiceData,
    PureTaskUpdateServiceData,
    TaskReplyCreateServiceData,
    TaskRepoData,
)
from task.domain.factories import TaskServiceFactory
from task.domain.services.base import TaskBaseService
from task.domain.services.pure_task import PureTaskService, PureTaskTitleService
from task.domain.utils import mentioned_user_ids_get_by_description
from task.interfaces.filters import OrgTaskListFilter, ProjectListFilter, TaskListFilter
from task.interfaces.paginator import TaskListPaginator
from task.interfaces.serializers import (
    CommentReplyModelSerializer,
    TaskAssignmentDetailSerializer,
    TaskCommentModelSerializer,
    TaskContextDetailsSerializer,
    TaskCreateUpdateInputSerializer,
    TaskDesignContextDetailsSerializer,
    TaskDetailSerializer,
    TaskInventoryBatchContextDetailsSerializer,
    TaskInvoiceContextDetailsSerializer,
    TaskLeadContextDetailsSerializer,
    TaskOrderContextDetailsSerializer,
    TaskOrderItemContextDetailsSerializer,
    TaskProgressReportContextDetailsSerializer,
    TaskProgressReportItemContextDetailsSerializer,
    TaskProjectContextDetailsSerializer,
    TaskProjectScheduleActivityContextDetailsSerializer,
    TaskProposalContextDetailsSerializer,
    TaskProposalItemContextDetailsSerializer,
    TaskRecceContextDetailsSerializer,
    TaskReplyCreateInputSerializer,
    TaskScopeItemContextDetailsSerializer,
    TaskSnagContextDetailsSerializer,
)

CACHE: BaseCache = import_string(settings.CUSTOM_CACHE)

logger = structlog.get_logger(__name__)


class TaskBucketListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="task_bucket_list_api",
        operation_summary="Task Bucket List API",
    )
    def get(self, request, *args, **kwargs):
        data = task_bucket_data_get(
            user=self.get_user(), is_admin=self.user_is_admin(), org_id=self.get_organization_id()
        )
        return Response(data, status=HTTP_200_OK)


class TaskListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(SearchTextFilterSerializer):
        bucket = serializers.ChoiceField(choices=TaskBucketsEnum.get_list_buckets)
        cid = serializers.CharField(required=True)
        ordering = serializers.ChoiceField(choices=TaskOrderingEnum.choices, required=False)
        request_types = SplitCharListField(
            child=serializers.ChoiceField(choices=RequestTypeEnum.choices), allow_empty=True, required=False
        )
        project_ids = SplitCharHashIdListField(allow_empty=True, required=False)
        status = SplitCharListField(
            child=serializers.ChoiceField(choices=RequestStatusEnum.get_task_filter_status()),
            allow_empty=True,
            required=False,
        )
        requestees = SplitCharHashIdListField(allow_empty=True, required=False)
        immediately_pending = SplitCharHashIdListField(allow_empty=True, required=False)
        requires_approval = SplitCharHashIdListField(allow_empty=True, required=False)
        assignees = SplitCharHashIdListField(allow_empty=True, required=False)
        created_by = SplitCharHashIdListField(allow_empty=True, required=False)
        creation_start_date = CustomDateField(required=False)
        creation_end_date = CustomEndDateField(required=False)

        class Meta:
            ref_name = "TaskListInput"

    pagination_class = TaskListPaginator
    filter_serializer_class = FilterSerializer
    filterset_class = TaskListFilter
    serializer_class = TaskAssignmentDetailSerializer

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset: TaskAssignmentQuerySet = (
            task_assignment_fetch_using_user(user_id=self.get_user_id())
            .filter(task__org_id=self.get_organization_id())
            .annotate_last_activity_timestamp()
        )
        if data.get("project_ids"):
            queryset = queryset.filter(task__project_id__in=data.get("project_ids"))
        if data.get("request_types"):
            if MicroContext.COMMENT.value in data.get("request_types"):
                queryset = queryset.filter(task__request_id__isnull=True, task__comment_id__isnull=False)
            else:
                queryset = queryset.filter(task__request__context__in=data.get("request_types"))
        if data.get("status"):
            if "delayed" in data.get("status"):
                queryset = queryset.delayed(timezone.now())
            else:
                queryset = queryset.filter(task__status__in=data.get("status"))
        if data.get("requestees"):
            queryset = queryset.filter(
                assigned_as__in=[
                    TaskAssignedAsEnum.IMMEDIATE_APPROVER.value,
                    TaskAssignedAsEnum.UPCOMING_APPROVER.value,
                    TaskAssignedAsEnum.OLD_APPROVER.value,
                ],
                created_by_id__in=data.get("requestees"),
            )
        if data.get("immediately_pending"):
            queryset = queryset.immediately_pending_on(data.get("immediately_pending"))
        if data.get("requires_approval"):
            queryset = queryset.requires_approval_of(data.get("requires_approval"))
        if data.get("created_by"):
            queryset = queryset.filter(task__created_by_id__in=data.get("created_by"))
        if data.get("assignees"):
            queryset = queryset.filter(task_assignee_id__in=data.get("assignees"))
        if data.get("creation_start_date") and data.get("creation_end_date"):
            queryset = queryset.filter(
                created_at__range=(data.get("creation_start_date"), data.get("creation_end_date"))
            )
        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["timezone"] = self.get_user_timezone()
        return context

    @swagger_auto_schema(
        responses={HTTP_200_OK: TaskAssignmentDetailSerializer(many=True)},
        operation_id="task_list_api",
        operation_summary="Task List API",
    )
    def get(self, request, *args, **kwargs):
        filters = self.validate_filter_data()
        cached_page_count = 5  # number of pages to cache
        cache_ttl = 60 * 10  # cache ttl in seconds
        imp_cache_key = f"task_list_imp_{request.user.pk}_{filters.get('cid')}"
        reminder_cache_key = f"task_list_rem_{request.user.pk}_{filters.get('cid')}"
        mention_cache_key = f"task_list_mention_{request.user.pk}_{filters.get('cid')}"
        tasks_for_me_cache_key = f"task_list_tasks_for_me_{request.user.pk}_{filters.get('cid')}"
        offset = self.paginator.get_offset(request)  # request offset
        limit = self.paginator.get_limit(request)  # request limit
        if not offset:
            # reset cache
            if filters.get("bucket") == TaskBucketsEnum.IMPORTANT.value:
                CACHE.delete(imp_cache_key)
            elif filters.get("bucket") == TaskBucketsEnum.REMINDERS.value:
                CACHE.delete(reminder_cache_key)
            elif filters.get("bucket") == TaskBucketsEnum.MENTIONS.value:
                CACHE.delete(mention_cache_key)
            elif filters.get("bucket") == TaskBucketsEnum.TASK_FOR_ME.value:
                CACHE.delete(tasks_for_me_cache_key)
        queryset = self.get_queryset()
        current_time = timezone.now()
        if (
            filters.get("bucket")
            in [
                TaskBucketsEnum.IMPORTANT.value,
                TaskBucketsEnum.REMINDERS.value,
                TaskBucketsEnum.MENTIONS.value,
                TaskBucketsEnum.TASK_FOR_ME.value,
            ]
            and not filters.get("search_text", "")
            and not filters.get("ordering", "")
            and not filters.get("request_types", [])
            and not filters.get("job_ids", [])
            and not filters.get("approval_status", [])
            and not filters.get("requestees", [])
            and not filters.get("immediately_pending_on", [])
            and not filters.get("requires_approval_of", [])
        ):
            # important task list without search text
            if filters.get("bucket") == TaskBucketsEnum.IMPORTANT.value:
                queryset = queryset.imps(current_time)
                cache_key = imp_cache_key
                order_by = "-activated_at"
            elif filters.get("bucket") == TaskBucketsEnum.MENTIONS.value:
                queryset = queryset.mentions()
                cache_key = mention_cache_key
                order_by = "-last_activity_timestamp"
            elif filters.get("bucket") == TaskBucketsEnum.TASK_FOR_ME.value:
                queryset = queryset.task_for_me(current_time)
                cache_key = tasks_for_me_cache_key
                order_by = "-last_activity_timestamp"
            else:
                queryset = queryset.reminders(current_time)
                cache_key = reminder_cache_key
                order_by = "remind_at"
            self.paginator.configure(request, queryset)
            cache_data = CACHE.get(cache_key, decode_json=True)
            if cache_data is not None:
                # previous cache is available
                total_cache_length = len(cache_data)
                if total_cache_length < cached_page_count * limit or (
                    total_cache_length == cached_page_count * limit and offset <= total_cache_length - limit
                ):
                    # user is scrolling down using cache
                    # need to select next page ids from cache
                    ids = [cache_data[offset:][i : i + limit] for i in range(0, len(cache_data[1:]), limit)][0]
                    # extend cache ttl for next request
                    CACHE.set_with_ttl(cache_key, cache_data, cache_ttl, encode_json=True)
                    ids_order = list(
                        queryset.order_by(order_by).values_list("id", flat=True)[
                            offset + len(ids) - 1 : offset + len(ids)
                        ]
                    )  # order for last id in current page
                    if len(ids):
                        if not len(ids_order) or ids_order[0] != ids[-1]:
                            # user cache is not valid now because of task shuffling need to refresh from client side
                            self.paginator.set_refresh(True)
                        elif cached_page_count * limit > total_cache_length != queryset.count():
                            # user cache is not valid now as tasks are added or removed,
                            # need to refresh from client side
                            self.paginator.set_refresh(True)
                else:
                    # user is scrolling after cache length
                    ids = list(
                        queryset.exclude(pk__in=cache_data)
                        .order_by(order_by)
                        .values_list("id", flat=True)[
                            offset - limit * cached_page_count : limit + (offset - limit * cached_page_count)
                        ]
                    )
                    # TODO: refresh flag needs to be set
            elif cache_data is None and offset:
                # user came back after cache ttl, need to reset task list from client side
                self.paginator.set_reset(True)
                ids = []
            else:
                # user came first time
                ids = list(queryset.order_by(order_by).values_list("id", flat=True)[0 : limit * cached_page_count])
                CACHE.set_with_ttl(cache_key, ids, cache_ttl, encode_json=True)
                ids = ids[:limit]
            order = Case(*[When(id=pk, then=pos) for pos, pk in enumerate(ids)])

            page = queryset.filter(id__in=ids).order_by(order)
        else:
            queryset = self.filter_queryset(queryset=queryset)
            page = self.paginate_queryset(queryset=queryset)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)


class TaskDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        is_admin = serializers.BooleanField(required=False, default=False)

        class Meta:
            ref_name = "TaskDetailsApiFilter"

    serializer_class = TaskAssignmentDetailSerializer

    def get_serializer_class(self, is_user_assignee: bool = True):
        """
        When user is not involved in task, i.e user can access task detail
        via task created in leads
        """
        is_admin = self.validate_filter_data().get("is_admin")
        if (is_admin and self.user_is_admin()) or not is_user_assignee:
            return TaskDetailSerializer
        return TaskAssignmentDetailSerializer

    def get_queryset(self, task_id: int):
        is_admin = self.validate_filter_data().get("is_admin")
        if is_admin and self.user_is_admin():
            return (
                task_fetch_all()
                .filter(id=task_id)
                .annotate_order_snapshot_id()
                .select_related("admin_title")
                .annotate(last_activity_timestamp=F("admin_title__created_at"))
            )
        return (
            task_assignment_fetch_using_user(user_id=self.get_user_id())
            .filter(task_id=task_id)
            .annotate_order_snapshot_id()
            .annotate_last_activity_timestamp()
        )

    def get_output_context(self):
        context = super().get_output_context()
        context["user"] = self.get_user()
        context["is_admin"] = self.user_is_admin()
        return context

    @swagger_auto_schema(
        responses={HTTP_200_OK: TaskAssignmentDetailSerializer},
        operation_id="task_details_api",
        operation_summary="Task Details API",
    )
    def get(self, request, task_id, *args, **kwargs):
        instance = self.get_queryset(task_id=task_id).first()
        is_user_assignee = True
        if instance is None:
            """
            When user is not involved in task, i.e user can access task detail
            via task created in leads
            """
            is_user_assignee = False
            instance = task_fetch_all().filter(id=task_id).annotate_order_snapshot_id().first()
        serializer_class = self.get_serializer_class(is_user_assignee=is_user_assignee)
        response = serializer_class(instance, context=self.get_output_context()).data
        return Response(response, status=HTTP_200_OK)


class TaskCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = TaskCreateUpdateInputSerializer
    serializer_class = TaskAssignmentDetailSerializer

    @swagger_auto_schema(
        request_body=TaskCreateUpdateInputSerializer,
        responses={HTTP_201_CREATED: TaskAssignmentDetailSerializer},
        operation_id="task_create_api",
        operation_summary="Task Create API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        title_service = PureTaskTitleService(repo=TaskTitleRepo())
        repo = TaskRepo()
        service = PureTaskService(
            repo=repo, title_service=title_service, lead_event_service=LeadEventsServiceFactory.get_service()
        )
        service_data = PureTaskCreateServiceData(
            description=data.get("description"),
            created_by_id=self.get_user_id(),
            org_id=self.get_organization_id(),
            due_at=data.get("due_at"),
            assignee_id=data.get("assignee_id"),
            context=MicroContext.to_enum(data.get("context")) if data.get("context") else None,
            context_id=data.get("context_id") if data.get("context_id") else None,
        )
        try:
            task_id = service.create_pure_task(data=service_data)
        except PureTaskService.TaskCreateException:
            self.set_response_message("Task creation failed")
            return Response(status=HTTP_400_BAD_REQUEST)

        task_assignment: TaskAssignment = (
            task_assignment_fetch_using_user(user_id=self.get_user_id()).filter(task_id=task_id).first()
        )
        return Response(
            TaskAssignmentDetailSerializer(task_assignment, context=self.get_output_context()).data,
            status=HTTP_201_CREATED,
        )


class TaskUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = TaskCreateUpdateInputSerializer

    @swagger_auto_schema(
        request_body=TaskCreateUpdateInputSerializer,
        responses={HTTP_200_OK: TaskAssignmentDetailSerializer},
        operation_id="task_update_api",
        operation_summary="Task Update API",
    )
    @transaction.atomic
    def put(self, request, task_id, *args, **kwargs):
        data = self.validate_input_data()
        title_service = PureTaskTitleService(repo=TaskTitleRepo())
        repo = TaskRepo()
        service = PureTaskService(
            repo=repo,
            title_service=title_service,
            lead_event_service=LeadEventsServiceFactory.get_service(),
        )
        service_data = PureTaskUpdateServiceData(
            id=task_id,
            description=data.get("description"),
            updated_by_id=self.get_user_id(),
            due_at=data.get("due_at"),
            assignee_id=data.get("assignee_id"),
        )
        try:
            service.update(data=service_data)
        except PureTaskService.WrongInputDataException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except PureTaskService.TaskUpdateException:
            self.set_response_message("Task updation failed")
            return Response(status=HTTP_400_BAD_REQUEST)

        response = task_assignment_fetch_using_user(user_id=self.get_user_id()).filter(task_id=task_id).first()
        if response is None:
            self.set_response_message("Something went wrong")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            TaskAssignmentDetailSerializer(response, context={"timezone": self.get_organization_timezone()}).data,
            status=HTTP_200_OK,
        )


class TaskReadApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_read_api",
        operation_summary="Task Read API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        task_service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            task_service.read(task_id=task_id, user_id=self.get_user_id())
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task read failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskDoneApi(OrgBaseApi):
    class OutputSerializer(BaseSerializer):
        undo_token = serializers.CharField()

        class Meta:
            ref_name = "TaskDoneApiOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_done_api",
        operation_summary="Task Done API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        task_service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            undo_token = task_service.done(task_id=task_id, done_by_id=self.get_user_id())
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task done failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(data={"undo_token": undo_token}, status=HTTP_202_ACCEPTED)


class TaskUnDoneApi(OrgBaseApi):
    class OutputSerializer(BaseSerializer):
        undo_token = serializers.CharField()

        class Meta:
            ref_name = "TaskDoneApiOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_undone_api",
        operation_summary="Task Undone API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        task_service: PureTaskService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            task_service.undone(task_id=task_id, undone_by_id=self.get_user_id())
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task undone failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskUndoApi(OrgBaseApi):
    class InputSerializer(BaseSerializer):
        undo_token = serializers.CharField()

        class Meta:
            ref_name = "TaskDoneApiOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_done_api",
        operation_summary="Task Done API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        undo_token = self.validate_input_data().get("undo_token")
        task_service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            task_service.undo(undo_token=undo_token, task_id=task_id)
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task undo failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        except TaskBaseService.TaskUndoException as e:
            logger.info(e.message)
            self.set_response_message("Undo token expired")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskRemindApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        remind_at = serializers.DateTimeField()

        class Meta:
            ref_name = "TaskRemindApiInput"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_remind_api",
        operation_summary="Task Remind API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        data = self.validate_input_data()
        task_service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            task_service.set_reminder(task_id=task_id, user_id=self.get_user_id(), time=data.get("remind_at"))
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task remind failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskReplyCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = TaskReplyCreateInputSerializer

    @swagger_auto_schema(
        request_body=TaskReplyCreateInputSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="task_reply_create_api",
        operation_summary="Task Reply Create API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        data = self.validate_input_data()
        repo = TaskRepo()
        task_data: TaskRepoData = repo.get_task(task_id=task_id)
        if task_data.comment_id:
            logger.info("Task have comment id, calling comment module to create reply")
            type = (
                CommentTypeEnum.NORMAL
                if len(mentioned_user_ids_get_by_description(data.get("description"))) == 0
                else CommentTypeEnum.MENTION
            )
            comment_reply_create(
                data=CommentReplyData(type=type.value, blocks=data.get("description")),
                comment_id=task_data.comment_id,
                user_id=self.get_user_id(),
                project_id=task_data.project_id,
                org_id=self.get_organization_id(),
            )
            return Response(status=HTTP_201_CREATED)
        title_service = PureTaskTitleService(repo=TaskTitleRepo())
        service = PureTaskService(
            repo=repo,
            title_service=title_service,
            lead_event_service=LeadEventsServiceFactory.get_service(),
        )
        service_data = TaskReplyCreateServiceData(
            task_id=task_id,
            description=data.get("description"),
            reply_by_id=self.get_user_id(),
        )
        try:
            service.create_reply(data=service_data)
        except TaskBaseService.TaskReplyException as e:
            logger.info(e.message)
            self.set_response_message("Task reply creation failed")
        except TaskBaseService.WrongInputDataException as e:
            logger.info(e.message)
            self.set_response_message("Task reply creation failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_201_CREATED)


class TaskReplyListApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []

    is_comment_replies = False

    def get_serializer_class(self):
        if self.is_comment_replies:
            return CommentReplyModelSerializer
        return TaskCommentModelSerializer

    def get_queryset(self, *args, **kwargs):
        task = Task.objects.filter(id=self.kwargs.get("task_id")).first()
        if not task:
            raise serializers.ValidationError("Task not found")
        if task.comment_id:
            self.is_comment_replies = True
            return get_comment_replies(comment_id=task.comment_id)
        return task_comment_fetch_using_task(task_id=task.pk)

    @swagger_auto_schema(
        responses={HTTP_200_OK: CommentReplyModelSerializer(many=True)},
        operation_id="task_reply_list_api",
        operation_summary="Task Reply List API",
    )
    def get(self, request, task_id, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class TaskArchiveForAllApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_archive_for_all_api",
        operation_summary="Task Archive For All API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            service.archive_for_all(task_id=task_id, archive_by_id=self.get_user_id())
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task archive for all failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskSelfArchiveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="task_self_archive_api",
        operation_summary="Task Self Archive API",
    )
    @transaction.atomic
    def post(self, request, task_id, *args, **kwargs):
        service: TaskBaseService = TaskServiceFactory().get_service(task_id=task_id)
        try:
            service.self_archive(task_id=task_id, archive_by_id=self.get_user_id())
        except TaskBaseService.TaskActionException as e:
            logger.info(e.message)
            self.set_response_message("Task archive failed")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class TaskFiltersBaseApi(OrgBaseApi):
    class FilterSerializer(BaseSerializer):
        is_admin = serializers.BooleanField(required=False, default=False)

        class Meta:
            ref_name = "TaskFiltersBaseApiFilter"


class TaskRequesteesApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: UserSerializer(many=True)},
        operation_id="requestee_list_api",
        operation_summary="Requestee List API",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        if self.check_if_system_user():
            is_admin = True
        requestees: QuerySet[User] = requestee_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        self.set_response_message("Requestees list fetch sucessfully.")
        return Response(
            UserSerializer(instance=requestees, many=True).data,
            status=HTTP_200_OK,
        )


class TaskImmediatelyPendingApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: UserSerializer(many=True)},
        operation_id="immediate_approver_list_api",
        operation_summary="Immediate Approver List API",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        immediate_approvers: QuerySet[User] = immediate_approvers_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        self.set_response_message("Immediate approver list fetch sucessfully.")
        return Response(
            UserSerializer(instance=immediate_approvers, many=True).data,
            status=HTTP_200_OK,
        )


class TaskApprovalRequiresApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: UserSerializer(many=True)},
        operation_id="approver_list_api",
        operation_summary="Approver List API",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        approvers: QuerySet[User] = approvers_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        self.set_response_message("Approver list fetch sucessfully.")
        return Response(
            UserSerializer(instance=approvers, many=True).data,
            status=HTTP_200_OK,
        )


class TaskContextDetailsApi(BaseApi):
    task = None

    def __init__(self, *args, **kwargs):
        self.task = None
        self.instance = None
        super().__init__(*args, **kwargs)

    def get_serializer_class(self, *args, **kwargs):
        if self.task.context in [
            MicroContextChoices.RECCE_LAYOUT_FILE,
            MicroContextChoices.RECCE_SPACE_FILE,
            MicroContextChoices.RECCE_EXTERNAL_FILE,
            MicroContextChoices.RECCE_INTERNAL_FILE,
            MicroContextChoices.RECCE_FILE,
        ]:
            return TaskRecceContextDetailsSerializer
        elif self.task.context == MicroContextChoices.DESIGN_FILE:
            return TaskDesignContextDetailsSerializer
        elif self.task.context == MicroContextChoices.SCOPE_ITEM:
            return TaskScopeItemContextDetailsSerializer
        elif self.task.context == MicroContextChoices.ORDER:
            return TaskOrderContextDetailsSerializer
        elif self.task.context == MicroContextChoices.ORDER_ITEM:
            return TaskOrderItemContextDetailsSerializer
        elif self.task.context == MicroContextChoices.PROGRESS_REPORT:
            return TaskProgressReportContextDetailsSerializer
        elif self.task.context == MicroContextChoices.SNAG:
            return TaskSnagContextDetailsSerializer
        elif self.task.context == MicroContextChoices.PROPOSAL:
            return TaskProposalContextDetailsSerializer
        elif self.task.context == MicroContextChoices.PROPOSAL_ITEM:
            return TaskProposalItemContextDetailsSerializer
        elif self.task.context == MicroContextChoices.INVOICE:
            return TaskInvoiceContextDetailsSerializer
        elif self.task.context == MicroContextChoices.LEAD:
            return TaskLeadContextDetailsSerializer
        elif self.task.context == MicroContextChoices.PROGRESS_REPORT_ITEM:
            return TaskProgressReportItemContextDetailsSerializer
        elif self.task.context == MicroContextChoices.INVENTORY_BATCH:
            return TaskInventoryBatchContextDetailsSerializer
        elif self.task.context == MicroContextChoices.PROJECT_SCHEDULE_ACTIVITY:
            return TaskProjectScheduleActivityContextDetailsSerializer
        return TaskProjectContextDetailsSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["task"] = self.task
        context["user"] = self.request.user
        if self.task.context == MicroContextChoices.ORDER_ITEM:
            context["hidden_fields"] = get_hidden_fields(
                order_id=self.instance.vendor_order_id,
                org_id=self.get_organization_id(),
                client_field_mapping=ORDER_FIELDS_MAPPING,
            )
        elif self.task.context == MicroContextChoices.PROPOSAL_ITEM:
            context["hidden_fields"] = get_hidden_fields(
                proposal_id=self.instance.proposal_id,
                org_id=self.get_organization_id(),
                client_field_mapping=PROPOSAL_FIELDS_MAPPING,
            )
        return context

    def get_queryset(self, *args, **kwargs) -> QuerySet:
        assert self.task is not None, "Task instance is not set"

        if self.task.context in [
            MicroContextChoices.RECCE_LAYOUT_FILE,
            MicroContextChoices.RECCE_SPACE_FILE,
            MicroContextChoices.RECCE_EXTERNAL_FILE,
            MicroContextChoices.RECCE_INTERNAL_FILE,
            MicroContextChoices.RECCE_FILE,
        ]:
            return RecceFile.objects.filter(pk=self.task.context_id).select_related("uploaded_by")
        elif self.task.context == MicroContextChoices.DESIGN_FILE:
            return DesignFileVersion.objects.filter(pk=self.task.context_id).select_related(
                "uploaded_by", "design_file", "design_file__design_section"
            )
        elif self.task.context == MicroContextChoices.SCOPE_ITEM:
            return (
                BoqElement.objects.filter(pk=self.task.context_id)
                .select_related("item_type", "category")
                .prefetch_related(
                    Prefetch("preview_files", BoqElementPreviewFile.objects.available().filter(is_main=True))
                )
            )
        elif self.task.context == MicroContextChoices.ORDER:
            return VendorOrder.objects.filter(pk=self.task.context_id).select_related(
                "created_by", "org_to__vendor", "org_from__client"
            )
        elif self.task.context == MicroContextChoices.ORDER_ITEM:
            return (
                VendorOrderElement.objects.filter(pk=self.task.context_id)
                .select_related("vendor_order", "item_type", "category")
                .prefetch_related(
                    Prefetch("preview_files", VendorOrderElementPreviewFile.objects.available().filter(is_main=True))
                )
            )
        elif self.task.context == MicroContextChoices.PROGRESS_REPORT:
            return ProgressReport.objects.filter(pk=self.task.context_id).select_related("created_by")
        elif self.task.context == MicroContextChoices.SNAG:
            return (
                Snag.objects.filter(pk=self.task.context_id)
                .select_related("created_by", "snag_category")
                .annotate_snag_code()
            )
        elif self.task.context == MicroContextChoices.PROPOSAL:
            return Proposal.objects.filter(pk=self.task.context_id).select_related(
                "created_by", "proposal_from__vendor", "proposal_for__client"
            )
        elif self.task.context == MicroContextChoices.PROPOSAL_ITEM:
            return (
                ProposalElementMapping.objects.filter(pk=self.task.context_id)
                .select_related(
                    "proposal",
                    "proposal__proposal_from__vendor",
                    "proposal__proposal_for__client",
                    "item_type",
                    "category",
                )
                .prefetch_related(
                    Prefetch("preview_files", ProposalElementPreviewFile.objects.available().filter(is_main=True))
                )
            )
        elif self.task.context == MicroContextChoices.INVOICE:
            return invoice_fetch_all().filter(pk=self.task.context_id)
        elif self.task.context == MicroContextChoices.LEAD:
            return Lead.objects.filter(pk=self.task.context_id).select_related("board", "company")
        elif self.task.context == MicroContextChoices.PROGRESS_REPORT_ITEM:
            return (
                BoqElement.objects.filter(pk=self.task.context_id)
                .select_related("item_type", "category")
                .prefetch_related(
                    Prefetch("preview_files", BoqElementPreviewFile.objects.available().filter(is_main=True))
                )
            )
        elif self.task.context == MicroContextChoices.INVENTORY_BATCH:
            return (
                InventoryBatch.objects.filter(pk=self.task.context_id)
                .select_related("created_by", "order", "project")
                .annotate_grn_number()
                .annotate_stock_value()
            )
        elif self.task.context == MicroContextChoices.PROJECT_SCHEDULE_ACTIVITY:
            return ProjectScheduleActivity.objects.available().filter(id=self.task.context_id)
        raise NotImplementedError("Task context not implemented")

    @swagger_auto_schema(
        responses={HTTP_200_OK: TaskContextDetailsSerializer()},
        operation_id="task_context_details_api",
        operation_summary="Task Context Details API",
    )
    def get(self, request, task_id, *args, **kwargs):
        self.task = Task.objects.filter(id=task_id).select_related("project").first()
        if not self.task:
            return Response(status=HTTP_404_NOT_FOUND)

        if not self.task.context:
            self.set_response_message("This resource is pure task.")
            return Response(status=HTTP_404_NOT_FOUND)

        if self.task.context not in [MicroContextChoices.PROJECT_DETAILS, MicroContextChoices.PROJECT]:
            self.instance = self.get_queryset().first()
            if not self.instance:
                self.set_response_message("This resource has been deleted and is no longer available.")
                return Response(status=HTTP_400_BAD_REQUEST)
        else:
            self.instance = self.task.project
        serializer = self.get_serializer(instance=self.instance)
        return Response(serializer.data, status=HTTP_200_OK)


class ProjectListApi(OrgBaseApi, ListModelMixin):
    class FilterSerializer(SearchTextFilterSerializer):
        client_ids = SplitCharHashIdListField(required=False)
        exclude_project_ids = SplitCharHashIdListField(required=False)
        show_all_projects = serializers.BooleanField(required=False, default=False)

    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectListSerializer):
        class Meta(ProjectListSerializer.Meta):
            ref_name = "ProjectListOutputSerializer"
            fields = ("id", "name", "client", "job_id", "city", "state")

    def get_queryset(self):
        filter_data = self.validate_filter_data()
        if len(filter_data.get("client_ids", [])):
            queryset = project_fetch_all_using_immediate_client_ids(
                user=self.request.user, client_ids=filter_data.get("client_ids")
            )
        else:
            queryset = project_fetch_all(user=self.request.user, show_all_projects=filter_data.get("show_all_projects"))

        queryset = (
            queryset.select_related("client", "store", "store__city", "store__state")
            .annotate_project_is_archived(organization_id=self.get_organization_id())
            .filter(is_archived=False)
        )

        if filter_data.get("exclude_project_ids"):
            queryset = queryset.exclude(id__in=filter_data.get("exclude_project_ids"))

        return queryset

    serializer_class = OutputSerializer
    filter_serializer_class = FilterSerializer
    filterset_class = ProjectListFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="project_list_api",
        operation_summary="Project List API",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        response = self.list(request, *args, **kwargs)
        self.set_response_message("Project list fetch sucessfully")
        return response


class JobIdListApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(ProjectListSerializer):
        class Meta(ProjectListSerializer.Meta):
            ref_name = "JobIdListOutputSerializer"
            fields = ("id", "name", "job_id")

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="job_id_list_api",
        operation_summary="Job Id List API",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        queryset = project_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        queryset = queryset.order_by("name")
        self.set_response_message("Job id list fetch sucessfully")
        return Response(data=self.OutputSerializer(queryset, many=True).data, status=HTTP_202_ACCEPTED)


class TaskAssigneeListApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: UserSerializer(many=True)},
        operation_id="task_assingee_list_api",
        operation_summary="Task assignee list api",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        task_assignees: QuerySet[User] = task_assignees_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        self.set_response_message("Task assignee list fetch sucessfully.")
        return Response(
            UserSerializer(instance=task_assignees, many=True).data,
            status=HTTP_200_OK,
        )


class TaskCreatorListApi(TaskFiltersBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=TaskFiltersBaseApi.FilterSerializer(),
        responses={HTTP_200_OK: UserSerializer(many=True)},
        operation_id="task_creator_list_api",
        operation_summary="Task creator list api",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        is_admin = self.validate_filter_data().get("is_admin")
        task_creators: QuerySet[User] = task_creator_fetch_using_user(
            user_id=self.get_user_id(), is_admin=is_admin, org_id=self.get_organization_id()
        )
        self.set_response_message("Task creator list fetch sucessfully.")
        return Response(
            UserSerializer(instance=task_creators, many=True).data,
            status=HTTP_200_OK,
        )


class AllRequestsApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(SearchTextFilterSerializer):
        project_ids = SplitCharHashIdListField(child=serializers.CharField(), allow_empty=True, required=False)
        status = SplitCharListField(
            child=serializers.ChoiceField(choices=RequestStatusEnum.get_task_filter_status()),
            allow_empty=True,
            required=False,
        )
        requestees = SplitCharHashIdListField(allow_empty=True, required=False)
        immediately_pending = SplitCharHashIdListField(allow_empty=True, required=False)
        requires_approval = SplitCharHashIdListField(allow_empty=True, required=False)
        request_types = SplitCharListField(
            child=serializers.ChoiceField(choices=RequestTypeEnum.choices), allow_empty=True, required=False
        )
        creation_start_date = CustomDateField(required=False)
        creation_end_date = CustomEndDateField(required=False)

        class Meta:
            ref_name = "AllRequestsFilter"

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = org_all_request_task_fetch(org_id=self.get_organization()).annotate(
            last_activity_timestamp=F("admin_title__created_at")
        )
        if data.get("project_ids"):
            queryset = queryset.filter(project_id__in=data.get("project_ids"))
        if data.get("status"):
            queryset = queryset.filter(status__in=data.get("status"))
        if data.get("immediately_pending"):
            queryset = queryset.immediately_pending_on(user_ids=data.get("immediately_pending"))
        if data.get("requires_approval"):
            queryset = queryset.requires_approval_of(user_ids=data.get("requires_approval"))
        if data.get("requestees"):
            queryset = queryset.filter(created_by__in=data.get("requestees"))
        if data.get("request_types"):
            if MicroContext.COMMENT.value in data.get("request_types"):
                queryset = queryset.filter(request_id__isnull=True, comment_id__isnull=False)
            else:
                queryset = queryset.filter(request__context__in=data.get("request_types"))
        if data.get("creation_start_date") and data.get("creation_end_date"):
            queryset = queryset.filter(
                created_at__range=(data.get("creation_start_date"), data.get("creation_end_date"))
            )
        return queryset

    serializer_class = TaskDetailSerializer
    filter_serializer_class = FilterSerializer
    filterset_class = OrgTaskListFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: TaskDetailSerializer(many=True)},
        operation_id="all_requests_api",
        operation_summary="All Requests api",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        if not self.user_is_admin():
            self.set_response_message("User is not Admin, Can't task this action")
            return Response(status=HTTP_400_BAD_REQUEST)
        response = self.list(request, *args, **kwargs)
        self.set_response_message("All requests are fetch sucessfully.")
        return response


class AllTaskApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(SearchTextFilterSerializer):
        project_ids = SplitCharHashIdListField(child=serializers.CharField(), allow_empty=True, required=False)
        assignees = SplitCharHashIdListField(allow_empty=True, required=False)
        created_by = SplitCharHashIdListField(allow_empty=True, required=False)
        status = SplitCharListField(
            child=serializers.ChoiceField(choices=RequestStatusEnum.get_task_filter_status()),
            allow_empty=True,
            required=False,
        )
        creation_start_date = CustomDateField(required=False)
        creation_end_date = CustomEndDateField(required=False)

        class Meta:
            ref_name = "AllTasksFilter"

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset: TaskQuerySet = org_all_task_fetch(org_id=self.get_organization()).annotate(
            last_activity_timestamp=F("admin_title__created_at")
        )
        if data.get("project_ids"):
            queryset = queryset.filter(project_id__in=data.get("project_ids"))
        if data.get("assignees"):
            queryset = queryset.filter(task_assignee_id__in=data.get("assignees"))
        if data.get("created_by"):
            queryset = queryset.filter(created_by_id__in=data.get("created_by"))
        if data.get("status"):
            if "delayed" in data.get("status"):
                queryset = queryset.delayed()
        if data.get("creation_start_date") and data.get("creation_end_date"):
            queryset = queryset.filter(
                created_at__range=(data.get("creation_start_date"), data.get("creation_end_date"))
            )

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update(
            {"user": self.get_user(), "is_admin": self.user_is_admin(), "timezone": self.get_user_timezone()}
        )
        return context

    serializer_class = TaskDetailSerializer
    filter_serializer_class = FilterSerializer
    filterset_class = OrgTaskListFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: TaskDetailSerializer(many=True)},
        operation_id="all_tasks_api",
        operation_summary="All Tasks api",
    )
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        response = self.list(request, *args, **kwargs)
        self.set_response_message("All tasks are fetch sucessfully.")
        return response


class TaskLandingDataApi(OrgBaseApi):
    class OutputSerializer(BaseSerializer):
        bucket = serializers.ChoiceField(choices=TaskBucketsEnum.choices)
        page = serializers.IntegerField()

        class Meta:
            ref_name = "TaskRedirectDataOutput"

    REQUIRED_PERMISSIONS = []

    def get(self, request, task_id, *args, **kwargs):
        response = get_task_page_and_bucket(
            task_id=task_id, user_id=self.get_user_id(), org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(response).data, status=HTTP_200_OK)
