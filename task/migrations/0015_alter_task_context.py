# Generated by Django 3.2.15 on 2025-01-27 06:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('task', '0014_alter_task_context'),
    ]

    operations = [
        migrations.AlterField(
            model_name='task',
            name='context',
            field=models.CharField(blank=True, choices=[('comment', 'Comment'), ('recce_internal_environment_file', 'Recce Internal Environment File'), ('recce_external_environment_file', 'Recce External Environment File'), ('recce_layout_and_sketches_file', 'Recce Layout & Sketches File'), ('recce_space_file', 'Recce Space File'), ('recce_file', 'Recce File'), ('design_2d_file', 'Design 2D File'), ('design_3d_file', 'Design 3D File'), ('design_prod_file', 'Design Production File'), ('boq_element', 'BOQ Element'), ('order', 'Order'), ('insta_order', 'Insta Order'), ('order_item', 'Order Item'), ('project', 'Project'), ('project_date', 'Project Date'), ('recce', 'Recce'), ('project_recce', 'Project Recce'), ('project_design', 'Project Design'), ('order_po', 'Order PO'), ('order_invoice', 'Order Invoice'), ('project_details', 'Project Details'), ('progress_report', 'Progress Report'), ('progress-report-item', 'Progress Report Item'), ('proposal', 'Proposal'), ('proposal_item', 'Proposal Item'), ('scope_item', 'Scope Item'), ('snag', 'Snag'), ('project-snag', 'Project Snag'), ('rate_contract', 'Rate Contract'), ('element_library', 'Element Library'), ('boq', 'Boq'), ('task', 'Task'), ('expense', 'Expense'), ('design_file', 'Design File'), ('payment-request', 'Payment Request'), ('invoice', 'Invoice'), ('inventory_batch', 'Inventory Batch'), ('inventory_transfer_batch', 'Inventory Transfer Batch'), ('lead', 'Lead'), ('lead-quotation', 'Lead Quotation'), ('board', 'Board'), ('user', 'User'), ('project_schedule', 'Project Schedule'), ('project_schedule_activity', 'Project Schedule Activity')], default=None, max_length=50, null=True),
        ),
    ]
