# This are exceptions specific for task app, raised by task services and model layers for error occure in data or business logic. # noqa
from django.core.exceptions import ValidationError

from common.exceptions import RollingBannersException


class TaskManagerException(RollingBannersException, ValidationError):
    pass


class InvalidBucketException(TaskManagerException):
    pass


class InvalidDataException(TaskManagerException):
    pass


class AttributeNotFoundException(TaskManagerException):
    pass


class AssignmentNotFound(TaskManagerException):
    pass


class UserIsNotCreatorException(TaskManagerException):
    pass


class WrongUserException(TaskManagerException):
    pass


class TaskNotFoundException(TaskManagerException):
    pass


class TaskUpdationNotAllowedException(TaskManagerException):
    pass


class InvalidReadAtException(InvalidDataException):
    pass


class InvalidDoneAtException(InvalidDataException):
    pass


class InvalidArchivedByException(InvalidDataException):
    pass


class InvalidArchivedAtException(InvalidDataException):
    pass


class InvalidRemindAtException(InvalidDataException):
    pass


class InvalidIsImpException(InvalidDataException):
    pass


class InvalidActivatedAtException(InvalidDataException):
    pass


class InvalidProjectException(InvalidDataException):
    pass


class TaskExceptionMixin:
    class InsufficientDataException(TaskManagerException):
        pass
