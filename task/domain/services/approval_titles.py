import structlog

from common.json_parser.constants import TextEditorJsonConstants
from microcontext.domain.constants import MicroContext
from task.domain.entities import UserIdMappingData
from task.domain.services.base import TaskTitleBaseService

logger = structlog.get_logger(__name__)


class ApprovalTaskTitleService(TaskTitleBaseService):
    microcontext_text = {
        MicroContext.EXPENSE: "Other Expense Payment",
        MicroContext.INVOICE: "Vendor Invoice",
        MicroContext.PAYMENT_REQUEST: "Vendor Payment",
        MicroContext.ORDER: "Vendor Order",
        MicroContext.INSTA_ORDER: "Insta Order",
    }

    def get_draft_request_initial_titles(self, creator_id: int, context: MicroContext) -> dict[int, list[dict]]:
        titles = {}
        titles[creator_id] = [
            {
                "text": "Your ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": "request submission is pending.",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        return titles

    def get_auto_approve_request_initial_titles(
        self, creator_id: int, context: MicroContext, org_id: int
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        titles[creator_id] = self._get_auto_approve_creator_title(context=context)
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id])
        creator_name = user_id_data_mapping.get(creator_id).name
        if org_id != user_id_data_mapping.get(creator_id).org_id:
            creator_name = user_id_data_mapping.get(creator_id).org_name
        return titles, self._get_auto_approve_admin_title(
            creator_name=creator_name,
            context=context,
        )

    def get_request_approval_initial_titles(
        self,
        creator_id: int,
        immediate_approver_ids: list[int],
        upcoming_approver_ids: list[int],
        context: MicroContext,
        pending_level_count,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        return self._get_request_approval_initial_or_submit_titles(
            creator_id=creator_id,
            immediate_approver_ids=immediate_approver_ids,
            upcoming_approver_ids=upcoming_approver_ids,
            context=context,
            pending_level_count=pending_level_count,
            org_id=org_id,
        )

    def get_request_approval_auto_approve_reset_titles(
        self, creator_id: int, assignee_ids: list[int], context: MicroContext, org_id: int
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        return self._get_request_final_approve_titles(
            creator_id=creator_id,
            assignee_ids=assignee_ids,
            context=context,
            org_id=org_id,
        )

    def get_request_reset_titles(
        self,
        creator_id: int,
        approver_ids: list[int],
        old_approver_ids: list[int],
        new_approver_ids: list[int],
        context: MicroContext,
        assignee_ids: list[int],
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id])
        if user_id_data_mapping.get(creator_id).org_id != org_id:
            old_approver_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} has edited ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]

            new_approver_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} has requested ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            old_approver_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} has edited ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]

            new_approver_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} has requested ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        for user_id in old_approver_ids + approver_ids + assignee_ids:
            titles[user_id] = old_approver_title

        for user_id in new_approver_ids:
            titles[user_id] = new_approver_title
        titles[creator_id] = [
            {
                "text": "You have edited the ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": "request",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        return titles, old_approver_title

    def get_request_approval_auto_approve_submit_titles(
        self,
        creator_id: int,
        context: MicroContext,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        return self._get_request_final_approve_titles(
            creator_id=creator_id, assignee_ids=[], context=context, org_id=org_id
        )

    def get_request_submit_titles(
        self,
        creator_id: int,
        immediate_approver_ids: list[int],
        upcoming_approver_ids: list[int],
        context: MicroContext,
        pending_level_count: int,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        return self._get_request_approval_initial_or_submit_titles(
            creator_id=creator_id,
            immediate_approver_ids=immediate_approver_ids,
            upcoming_approver_ids=upcoming_approver_ids,
            context=context,
            pending_level_count=pending_level_count,
            org_id=org_id,
        )

    def get_request_hold_titles(
        self,
        creator_id: int,
        holder_id: int,
        assignee_ids: list[int],
        context: MicroContext,
        is_admin: bool,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        # user_id_name_mapping = self.get_user_id_name_mapping(user_ids=[creator_id, holder_id])
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id, holder_id])

        if org_id != user_id_data_mapping.get(creator_id).org_id:
            holder_name = f"{user_id_data_mapping.get(holder_id).org_name}"
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is put on hold by {user_id_data_mapping.get(holder_id).name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for assignee_id in assignee_ids:
                titles[assignee_id] = others_title
            titles[holder_id] = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request is put on hold by You",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is put on hold by {holder_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            holder_name = f"{user_id_data_mapping.get(holder_id).name}"
            if is_admin:
                holder_name = f"Admin({user_id_data_mapping.get(holder_id).name}) "
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is put on hold by {holder_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for assignee_id in assignee_ids:
                titles[assignee_id] = others_title
            titles[holder_id] = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request is put on hold by You",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is put on hold by {holder_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        return titles, others_title

    def get_request_cancel_titles(
        self,
        creator_id: int,
        canceller_id: int,
        assignee_ids: list[int],
        context: MicroContext,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        user_id_data_mapping: dict[int, UserIdMappingData] = self.get_user_id_data_mapping(
            user_ids=[creator_id, canceller_id] + assignee_ids
        )
        others_titles = [
            {
                "text": f"{user_id_data_mapping.get(creator_id).name} has cancelled the request for ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
        ]
        for user_id in assignee_ids:
            titles[user_id] = others_titles

        if org_id != user_id_data_mapping.get(creator_id).org_id:
            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is cancelled by {user_id_data_mapping.get(assignee_ids[0]).org_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]

            titles[canceller_id] = [
                {
                    "text": "You have cancelled the request for ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name}'s",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)}",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
            ]
        elif canceller_id == creator_id:
            titles[creator_id] = [
                {
                    "text": "You have cancelled the request for  ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
            ]
        else:
            titles[canceller_id] = [
                {
                    "text": "You have cancelled the request for  ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
            ]
            if user_id_data_mapping.get(canceller_id).org_id == user_id_data_mapping.get(creator_id).org_id:
                titles[canceller_id].append(
                    {
                        "text": f"created by {user_id_data_mapping.get(creator_id).name}",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                )
            else:
                titles[canceller_id].append(
                    {
                        "text": f"created by {user_id_data_mapping.get(creator_id).org_name}",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                )
        return titles, others_titles

    def get_request_reject_titles(
        self,
        creator_id: int,
        rejector_id: int,
        assignee_ids: list[int],
        context: MicroContext,
        is_admin: bool,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        # user_id_name_mapping = self.get_user_id_name_mapping(user_ids=[creator_id, rejector_id])
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id, rejector_id])
        if org_id != user_id_data_mapping.get(creator_id).org_id:
            rejector_name = f"{user_id_data_mapping.get(rejector_id).org_name}"

            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name}'s ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is rejected by {user_id_data_mapping.get(rejector_id).name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for assignee_id in assignee_ids:
                titles[assignee_id] = others_title
            titles[rejector_id] = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name}'s ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request is rejected by You",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is rejected by {rejector_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            rejector_name = f"{user_id_data_mapping.get(rejector_id).name}"

            if is_admin:
                rejector_name = f"Admin({user_id_data_mapping.get(rejector_id).name}) "
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name}'s ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is rejected by {rejector_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for assignee_id in assignee_ids:
                titles[assignee_id] = others_title
            titles[rejector_id] = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name}'s ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request is rejected by You",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"request is rejected by {rejector_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        return titles, others_title

    def get_request_final_approve_titles(
        self,
        creator_id: int,
        assignee_ids: list[int],
        context: MicroContext,
        org_id: int,
    ) -> dict[int, list[dict]]:
        return self._get_request_final_approve_titles(
            creator_id=creator_id,
            assignee_ids=assignee_ids,
            context=context,
            org_id=org_id,
        )

    def get_request_approve_titles(
        self,
        creator_id: int,
        immediate_approver_ids: list[int],
        assignee_ids: list[int],
        context: MicroContext,
        is_admin: bool,
        pending_level_count: int,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        if is_admin:
            return self._get_request_final_approve_titles(
                creator_id=creator_id, assignee_ids=assignee_ids, context=context, org_id=org_id
            )
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id] + immediate_approver_ids)
        immediate_approver_names = "/".join(
            [user_id_data_mapping.get(user_id).name for user_id in immediate_approver_ids]
        )

        if org_id != user_id_data_mapping.get(creator_id).org_id:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"is pending on {immediate_approver_names} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"is pending on {immediate_approver_names} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        if pending_level_count > 1:
            others_title.append(
                {
                    "text": f"+{pending_level_count - 1} More",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            )

        if org_id != user_id_data_mapping.get(creator_id).org_id:
            for user_id in assignee_ids:
                if user_id in immediate_approver_ids:
                    titles[user_id] = [
                        {
                            "text": f"{user_id_data_mapping.get(creator_id).org_name} ",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                        {
                            "text": f"{self.microcontext_text.get(context)} ",
                            "text_type": TextEditorJsonConstants.ITALICS,
                            "type": "text",
                        },
                        {
                            "text": "is pending on you",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                    ]
                else:
                    titles[user_id] = others_title

            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"is pending on {user_id_data_mapping.get(immediate_approver_ids[0]).org_name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            for user_id in assignee_ids:
                if user_id in immediate_approver_ids:
                    titles[user_id] = [
                        {
                            "text": f"{user_id_data_mapping.get(creator_id).name} ",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                        {
                            "text": f"{self.microcontext_text.get(context)} ",
                            "text_type": TextEditorJsonConstants.ITALICS,
                            "type": "text",
                        },
                        {
                            "text": "is pending on you",
                            "text_type": TextEditorJsonConstants.NORMAL,
                            "type": "text",
                        },
                    ]
                else:
                    titles[user_id] = others_title

            titles[creator_id] = [
                {
                    "text": "Your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"is pending on {immediate_approver_names} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            if pending_level_count > 1:
                titles[creator_id].append(
                    {
                        "text": f"+{pending_level_count - 1} More",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                )
        return titles, others_title

    def get_comment_approval_initial_titles(
        self, creator_id: int, approver_id: int, mentioned_user_ids: list[int]
    ) -> dict[int, list[dict]]:
        titles = {}
        user_id_name_mapping = self.get_user_id_name_mapping(user_ids=[creator_id, approver_id])
        if creator_id == approver_id:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} has requested an approval from themself",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        else:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} has requested an approval from {user_id_name_mapping.get(approver_id)}",  # noqa
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        for user_id in mentioned_user_ids:
            titles[user_id] = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} has requested an approval from {user_id_name_mapping.get(approver_id)} where you were mentioned",  # noqa
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        titles[approver_id] = [
            {
                "text": f"{user_id_name_mapping.get(creator_id)} has requested an approval from you",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            }
        ]
        titles[creator_id] = [
            {
                "text": f"You requested for an approval from {user_id_name_mapping.get(approver_id)}",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            }
        ]
        return titles, others_title

    def get_comment_approve_titles(
        self,
        creator_id: int,
        assignee_ids: list[int],
        approver_id: int,
    ) -> tuple[dict[int, list[dict]]]:
        titles = {}
        user_id_name_mapping = self.get_user_id_name_mapping(user_ids=[creator_id, approver_id])
        if creator_id == approver_id:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} request is approved by themself",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} request is approved by {user_id_name_mapping.get(approver_id)}",  # noqa
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        for user_id in assignee_ids:
            titles[user_id] = others_title
        titles[approver_id] = [
            {
                "text": f"You have approved request by {user_id_name_mapping.get(creator_id)}",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        titles[creator_id] = [
            {
                "text": f"Your request has been approved by {user_id_name_mapping.get(approver_id)}",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        return titles, others_title

    def get_comment_reject_titles(self, creator_id: int, assignee_ids: list[int], rejector_id: int):
        titles = {}
        user_id_name_mapping = self.get_user_id_name_mapping(user_ids=[creator_id, rejector_id])
        if creator_id == rejector_id:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} request is rejected by themself",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            others_title = [
                {
                    "text": f"{user_id_name_mapping.get(creator_id)} request is rejected by {user_id_name_mapping.get(rejector_id)}",  # noqa
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        for user_id in assignee_ids:
            titles[user_id] = others_title
        titles[rejector_id] = [
            {
                "text": f"You have rejected request by {user_id_name_mapping.get(creator_id)}",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        titles[creator_id] = [
            {
                "text": f"Your request has been rejected by {user_id_name_mapping.get(rejector_id)}",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        return titles, others_title

    def get_reply_titles(
        self,
        creator_id: int,
        replier_id: int,
        new_mentioned_user_ids: list[int],
        old_mentiones_user_ids: list[int],
        approver_ids: list[int],
        commented_user_ids: list[int],
        context: MicroContext,
    ) -> dict[int, list[dict]]:
        titles = {}
        user_id_name_mapping: dict[int, str] = self.get_user_id_name_mapping(user_ids=[creator_id, replier_id])
        for user_id in new_mentioned_user_ids:
            titles[user_id] = [
                {
                    "text": f"{user_id_name_mapping.get(replier_id)} has mentioned you in a comment thread",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        for user_id in old_mentiones_user_ids:
            titles[user_id] = [
                {
                    "text": f"{user_id_name_mapping.get(replier_id)} has commented on thread where you were mentioned",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        for user_id in approver_ids:
            if user_id == replier_id:
                titles[user_id] = [
                    {
                        "text": "You have commented on ",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                    {
                        "text": f"{self.microcontext_text.get(context)} ",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                    {
                        "text": "where you were approver",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ]
            else:
                titles[user_id] = [
                    {
                        "text": f"{user_id_name_mapping.get(replier_id)} commented on ",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                    {
                        "text": f"{self.microcontext_text.get(context)} ",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                    {
                        "text": "where you were approver",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ]
        for user_id in commented_user_ids:
            if user_id == replier_id:
                titles[user_id] = [
                    {
                        "text": "You have commented on ",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                    {
                        "text": f"{self.microcontext_text.get(context)} ",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                    {
                        "text": "where you replied",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ]
            else:
                titles[user_id] = [
                    {
                        "text": f"{user_id_name_mapping.get(replier_id)} commented on ",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                    {
                        "text": f"{self.microcontext_text.get(context)} ",
                        "text_type": TextEditorJsonConstants.ITALICS,
                        "type": "text",
                    },
                    {
                        "text": "where you replied",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ]

        if creator_id == replier_id:
            titles[creator_id] = [
                {
                    "text": f"You have commented on a {self.microcontext_text.get(context)} request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ]
        else:
            titles[creator_id] = [
                {
                    "text": f"{user_id_name_mapping.get(replier_id)} has commented on your ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "request",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            titles[replier_id] = [
                {
                    "text": "You have commented on ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": f"requested by {user_id_name_mapping.get(creator_id)}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        return titles

    def _get_auto_approve_creator_title(self, context: MicroContext) -> list[dict]:
        return [
            {
                "text": "Your ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": " request is auto approved.",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]

    def _get_auto_approve_admin_title(self, creator_name: str, context: MicroContext) -> list[dict]:
        return [
            {
                "text": f"{creator_name} ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": "request is auto approved.",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]

    def _get_request_approval_initial_or_submit_titles(
        self,
        creator_id: int,
        immediate_approver_ids: list[int],
        upcoming_approver_ids: list[int],
        context: MicroContext,
        pending_level_count: int,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id] + immediate_approver_ids)

        if user_id_data_mapping.get(creator_id).org_id != org_id:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} has requested ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "approval",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for user_id in immediate_approver_ids + upcoming_approver_ids:
                titles[user_id] = others_title
        else:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} has requested ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "approval",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
            for user_id in immediate_approver_ids + upcoming_approver_ids:
                titles[user_id] = others_title
        titles[creator_id] = [
            {
                "text": "Your ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": "request is pending ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        if user_id_data_mapping.get(creator_id).org_id != org_id:
            titles[creator_id].append(
                {
                    "text": f"with {user_id_data_mapping.get(creator_id).org_name}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            )
        else:
            if immediate_approver_ids:
                immediate_approver_names = "/".join(
                    [user_id_data_mapping.get(user_id).name for user_id in immediate_approver_ids]
                )

                titles[creator_id].append(
                    {
                        "text": f"with {immediate_approver_names}",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                )
            if pending_level_count > 1:
                titles[creator_id].append(
                    {
                        "text": f"+{pending_level_count - 1} More",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                )
        return titles, others_title

    def _get_request_final_approve_titles(
        self,
        creator_id: int,
        assignee_ids: int,
        context: MicroContext,
        org_id: int,
    ) -> tuple[dict[int, list[dict]], list[dict]]:
        titles = {}
        user_id_data_mapping = self.get_user_id_data_mapping(user_ids=[creator_id])
        if user_id_data_mapping.get(creator_id).org_id != org_id:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).org_name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "is approved",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        else:
            others_title = [
                {
                    "text": f"{user_id_data_mapping.get(creator_id).name} ",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
                {
                    "text": f"{self.microcontext_text.get(context)} ",
                    "text_type": TextEditorJsonConstants.ITALICS,
                    "type": "text",
                },
                {
                    "text": "is approved",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                },
            ]
        for user_id in assignee_ids:
            titles[user_id] = others_title
        titles[creator_id] = [
            {
                "text": "Your ",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
            {
                "text": f"{self.microcontext_text.get(context)} ",
                "text_type": TextEditorJsonConstants.ITALICS,
                "type": "text",
            },
            {
                "text": "is approved",
                "text_type": TextEditorJsonConstants.NORMAL,
                "type": "text",
            },
        ]
        admin_title = others_title
        return titles, admin_title
