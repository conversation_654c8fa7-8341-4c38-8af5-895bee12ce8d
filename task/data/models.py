from typing import List

import pytz
from django.conf import settings
from django.db import models
from django.utils import timezone

from approval_request.data.choices import ApprovalRequestStatusChoices, RequestErrorCodeChoices
from approval_request.data.models import ApprovalRequest
from approval_request.domain.constants import Request<PERSON>tatus<PERSON>num, RequestTypesEnum
from authorization.domain.constants import Permissions
from commentv2.data.choices import CommentReplyTypeChoices
from commentv2.data.models import Comment
from common.json_parser.constants import const
from common.models import (
    BaseModel,
    CreateDeleteModel,
    CreateModel,
    CreateUpdateDeleteModel,
    CreateUpdateModel,
)
from core.helpers import OrgPermissionHelper
from core.models import Organization, User
from microcontext.choices import MicroContextChoices
from project.data.models import Project
from task.data.choices import TaskAssignedAs, TaskBuckets, TaskTypeChoices
from task.data.querysets import TaskAssignmentQuerySet, TaskQuerySet
from task.data.utils import get_description_preview
from task.domain.constants import (
    Task<PERSON><PERSON><PERSON><PERSON>,
    TaskAssigned<PERSON>Enum,
    TaskBucketsEnum,
    TaskTypesEnum,
)
from task.domain.entities import TaskAssignmentRepoData
from task.domain.exceptions import AttributeNotFoundException as _AttributeNotFoundException
from task.domain.exceptions import TaskExceptionMixin
from task.domain.utils import get_archived_time


def check_is_delay(due_at: timezone.datetime) -> bool:
    ist_timezone = pytz.timezone("Asia/Kolkata")
    if due_at.astimezone(ist_timezone).date() < timezone.now().astimezone(ist_timezone).date():
        return True
    return False


class Task(CreateUpdateDeleteModel, TaskExceptionMixin):
    description = models.JSONField(default=list)
    searchable_text = models.TextField(null=True, blank=True)
    due_at = models.DateTimeField(null=True, blank=True)
    context = models.CharField(max_length=50, choices=MicroContextChoices.choices, null=True, blank=True, default=None)
    context_id = models.IntegerField(null=True, blank=True, default=None)
    project = models.ForeignKey(
        Project, on_delete=models.RESTRICT, related_name="tasks", null=True, blank=True, default=None
    )
    comment = models.OneToOneField(
        Comment, on_delete=models.RESTRICT, related_name="task", null=True, blank=True, default=None
    )
    request = models.OneToOneField(ApprovalRequest, on_delete=models.RESTRICT, related_name="+", null=True, blank=True)
    status = models.CharField(
        max_length=50, choices=ApprovalRequestStatusChoices.choices, default=None, null=True, blank=True
    )
    org = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="+", default=None, null=True, blank=True
    )
    type = models.CharField(max_length=50, choices=TaskTypeChoices.choices, null=True, blank=True, default=None)
    error_code = models.PositiveSmallIntegerField(choices=RequestErrorCodeChoices.choices, null=True, blank=True)

    objects = TaskQuerySet.as_manager()

    @property
    def description_and_assignee_text(self) -> str:
        description_text = []
        for block in self.description:
            if block["type"] == const.TEXT:
                description_text.append(block["text"])
        if self.pk:
            for assignment in self.assignments.all():
                description_text.append(assignment.assignee.name)
        return " ".join(description_text)

    @property
    def title(self) -> list[dict]:
        if hasattr(self, "admin_title") and self.admin_title.first_title:
            return self.admin_title.first_title
        return []

    @property
    def latest_title(self) -> list[dict]:
        if hasattr(self, "admin_title") and self.admin_title.latest_title:
            return self.admin_title.latest_title
        return []

    @property
    def title_color(self):
        if self.type == TaskTypesEnum.APPROVAL_TASK.value:
            if self.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                return "#ea9245"
            else:
                return "#344054"
        return "#61C7CD"

    @property
    def title_background(self):
        if self.type == TaskTypesEnum.APPROVAL_TASK.value:
            if self.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                return "#edeff2"
            else:
                return "#fceddf"
        return "#E4F6F7"

    @property
    def is_delay(self) -> bool:
        if self.due_at is not None and check_is_delay(due_at=self.due_at):
            return True
        return False

    @property
    def preview(self):
        return get_description_preview(self.description)

    @property
    def buckets(self):
        if self.type == TaskTypesEnum.PURE_TASK.value:
            return [TaskBucketsEnum.ALL_TASKS.value]
        elif self.type == TaskTypesEnum.APPROVAL_TASK.value:
            return [TaskBucketsEnum.ALL_REQUESTS.value]
        return []

    def actions(self, user: User, is_admin: bool):
        if not is_admin:
            return []
        if self.request_id is None:
            return []
        if self.created_by_id == user.id:
            return []
        if self.org_id != user.org_id:
            return []
        if not OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_APPROVE_ALL_APPROVAL_REQUESTS.value
        ):
            return []
        if self.error_code == RequestErrorCodeChoices.WRONG_CONFIG.value:
            if OrgPermissionHelper.has_permission(
                user=user, permission=Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS.value
            ):
                return [TaskActionsEnum.CANCEL_AS_ADMIN.value]
            return []
        if self.status in [RequestStatusEnum.REJECTED.value, RequestStatusEnum.CANCELLED.value]:
            return []
        if self.status in [RequestStatusEnum.APPROVED.value] and OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS.value
        ):
            if self.context in [
                MicroContextChoices.ORDER.value,
                MicroContextChoices.INSTA_ORDER.value,
                MicroContextChoices.EXPENSE.value,
            ]:
                return []
            return [TaskActionsEnum.CANCEL_AS_ADMIN.value]
        if self.status == RequestStatusEnum.HOLD.value:
            return [TaskActionsEnum.APPROVE_AS_ADMIN.value, TaskActionsEnum.REJECT_AS_ADMIN.value]
        return [
            TaskActionsEnum.APPROVE_AS_ADMIN.value,
            TaskActionsEnum.REJECT_AS_ADMIN.value,
            TaskActionsEnum.HOLD_AS_ADMIN.value,
        ]

    def save(
        self,
        *args,
        **kwargs,
    ) -> None:
        self.searchable_text = self.description_and_assignee_text
        super().save(*args, **kwargs)

    def _validate_pure_task(self) -> dict:
        errors = {}
        if self.comment_id is None:
            if self.project_id is not None:
                errors["project_id"] = "Project should be null for pure task created from task manager"
            # if self.context_id is not None:
            #     errors["context_id"] = "Context should be null for pure task created from task manager"
            # if self.context is not None:
            #     errors["context"] = "Context should be null for pure task created from task manager"
            if self.request_id is not None:
                errors["request_id"] = "Request should be null for pure task created from task manager"
            if self.status is not None:
                errors["status"] = "Status should be null for pure task"
        else:
            # if self.project_id is None:
            #     errors["project_id"] = "Project is required for pure task created from comment"
            if self.context_id is None:
                errors["context_id"] = "Context is required for pure task created from comment"
            if self.context is None:
                errors["context"] = "Context is required for pure task created from comment"
            if self.request_id:
                errors["request_id"] = "Request is not required pure for task created from comment"
            if self.status is not None:
                errors["status"] = "Status should be null for pure task"
        return errors

    def _validate_approval_task(self) -> dict:
        errors = {}
        if self.request_id and self.comment_id:
            if self.project_id is None:
                errors["project_id"] = "Project is required for request approval task creation"
            if self.context_id is None:
                errors["context_id"] = "Context is required for request approval task creation"
            if self.context is None:
                errors["context"] = "Context is required for request approval task creation"
            if self.status is None:
                errors["status"] = "Status is required for request approval task creation"
        elif self.request_id is None and self.comment_id:
            if self.project_id is None:
                errors["project_id"] = "Project is required for comment approval task creation"
            if self.context_id is None:
                errors["context_id"] = "Context is required for comment approval task creation"
            if self.context is None:
                errors["context"] = "Context is required for comment approval task creation"
            if self.status is None:
                errors["status"] = "Status is required for comment approval task creation"
        return errors

    def _validate_comment_task(self) -> dict:
        errors = {}
        if self.request_id:
            errors["request_id"] = "Request id is not required for comment approval task creation"
        if self.comment_id is None:
            errors["comment_id"] = "Comment id is required for comment task creation"
        # if self.project_id is None:
        #     errors["project_id"] = "Project is required for comment task creation"
        if self.context_id is None:
            errors["context_id"] = "Context is required for comment task creation"
        if self.context is None:
            errors["context"] = "Context is required for comment task creation"
        if self.status is not None:
            errors["status"] = "Status should be null for comment task"
        return errors

    def clean(self) -> None:
        errors = {}
        if self.org_id is None:
            errors["org_id"] = f"Org is required for task. (org_id: {self.org_id})"
        if self.type == TaskTypesEnum.PURE_TASK.value:
            errors.update(self._validate_pure_task())
        elif self.type == TaskTypesEnum.APPROVAL_TASK.value:
            errors.update(self._validate_approval_task())
        elif self.type == TaskTypesEnum.COMMENT_TASK.value:
            errors.update(self._validate_comment_task())
        elif self.type is None:
            errors["type"] = f"Task type is required for task. (type: {self.type}) "
        else:
            errors["type"] = f"Invalid task type provided. (type: {self.type}) "
        if len(errors) > 0:
            raise self.ModelFieldValidationException(errors)

    class Meta:
        db_table = "task_tasks"


class TaskAssignment(CreateDeleteModel, TaskExceptionMixin):
    class AttributeNotFoundException(_AttributeNotFoundException):
        pass

    task = models.ForeignKey(Task, on_delete=models.RESTRICT, related_name="assignments")
    assignee = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="task_assignments")
    read_at = models.DateTimeField(null=True, blank=True)
    done_at = models.DateTimeField(null=True, blank=True)
    archived_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="+", null=True, blank=True
    )
    archived_at = models.DateTimeField()
    remind_at = models.DateTimeField(null=True, blank=True)
    bucket = models.CharField(max_length=50, choices=TaskBuckets.choices)
    is_imp = models.BooleanField(default=False)
    assigned_as = models.CharField(max_length=50, choices=TaskAssignedAs.choices)
    activated_at = models.DateTimeField(null=True, blank=True)
    is_self_archived = models.BooleanField(default=False)

    objects = TaskAssignmentQuerySet.as_manager()

    def clean(self) -> None:
        errors = {}
        allow_bucket = [
            TaskBucketsEnum.MY_COMMENTS.value,
            TaskBucketsEnum.MY_APPROVALS.value,
            TaskBucketsEnum.MY_REQUESTS.value,
            TaskBucketsEnum.MENTIONS.value,
            TaskBucketsEnum.TASK_FOR_ME.value,
            TaskBucketsEnum.TASK_BY_ME.value,
            TaskBucketsEnum.TASK_BY_ME.value,
            TaskBucketsEnum.DRAFT_REQUESTS.value,
        ]
        if self.bucket not in allow_bucket:
            errors["bucket"] = "Invalid task assignment bucket provided"
        if not self.pk:
            errors.update(self.validate_create_data())
        else:
            errors.update(self.validate_update_data())

        if len(errors) > 0:
            raise self.ModelFieldValidationException(errors)

    @property
    def title(self) -> list[dict]:
        """
        Returns the first title of the task for specific task assignee.
        Will use assignee_id, bucket, created_by and assigned_as to get the title.
        """
        if hasattr(self, "first_title"):
            return self.first_title
        raise self.AttributeNotFoundException("First title not found. Please annotate first_title in queryset.")

    @property
    def latest_title(self) -> list[dict]:
        """
        Returns the latest title of the task.
        Will use last title from title history for assignee if found else return title.
        """
        if hasattr(self, "last_title"):
            return self.last_title
        raise self.AttributeNotFoundException("Last title not found. Please annotate last_title in queryset.")

    @property
    def actions(self) -> List[TaskActionsEnum]:
        """
        Returns the list of actions that can be performed on the task.
        """
        if self.task.type == TaskTypesEnum.PURE_TASK.value:
            return self.get_pure_task_actions()
        elif self.task.type == TaskTypesEnum.COMMENT_TASK.value:
            return self.get_comment_task_actions()
        elif self.task.type == TaskTypesEnum.APPROVAL_TASK.value:
            return self.get_approval_task_actions()
        raise self.WrongDataSavedException(f"Task type is not valid, type: {self.task.type}")

    @property
    def is_delay(self) -> bool:
        if self.task.due_at is not None and check_is_delay(due_at=self.task.due_at):
            return True
        return False

    @property
    def buckets(self) -> list[TaskBucketsEnum]:
        current_time = timezone.now()
        buckets = []
        if self.is_imp:
            buckets.append(TaskBucketsEnum.IMPORTANT.value)
        if self.remind_at and self.remind_at > current_time:
            buckets.append(TaskBucketsEnum.REMINDERS.value)
        if self.assigned_as == TaskAssignedAs.MENTIONS.value:
            buckets.append(TaskBucketsEnum.MENTIONS.value)
        if self.task.type == TaskTypesEnum.PURE_TASK.value:
            if self.archived_at > current_time:
                if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                    buckets.append(TaskBucketsEnum.TASK_BY_ME.value)
                elif self.assigned_as == TaskAssignedAsEnum.ASSIGNEE.value:
                    buckets.append(TaskBucketsEnum.TASK_FOR_ME.value)
            else:
                if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                    buckets.append(TaskBucketsEnum.TASK_BY_ME_ARCHIVED.value)
                elif self.assigned_as == TaskAssignedAsEnum.ASSIGNEE.value:
                    buckets.append(TaskBucketsEnum.TASK_FOR_ME_ARCHIVED.value)
        elif self.task.type == TaskTypesEnum.APPROVAL_TASK.value:
            if self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                    buckets.append(TaskBucketsEnum.MY_REQUESTS_ARCHIVED.value)
                elif self.assigned_as == TaskAssignedAsEnum.OLD_APPROVER.value:
                    buckets.append(TaskBucketsEnum.ALL_APPROVALS.value)
            else:
                if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                    if self.task.status == RequestStatusEnum.DRAFT.value:
                        buckets.append(TaskBucketsEnum.DRAFT_REQUESTS.value)
                    elif self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                        buckets.append(TaskBucketsEnum.MY_REQUESTS_ARCHIVED.value)
                    else:
                        buckets.append(TaskBucketsEnum.MY_REQUESTS.value)
                elif self.assigned_as == TaskAssignedAsEnum.IMMEDIATE_APPROVER.value:
                    buckets.append(TaskBucketsEnum.IMMEDIATE_APPROVALS.value)
                elif self.assigned_as == TaskAssignedAsEnum.UPCOMING_APPROVER.value:
                    buckets.append(TaskBucketsEnum.UPCOMMING_APPROVALS.value)
        elif self.task.type == TaskTypesEnum.COMMENT_TASK.value:
            if self.assigned_as in [TaskAssignedAsEnum.CREATOR.value, TaskAssignedAsEnum.REPLIER.value]:
                buckets.append(TaskBucketsEnum.MY_COMMENTS.value)
            elif self.assigned_as == TaskAssignedAsEnum.MENTIONS.value:
                buckets.append(TaskBucketsEnum.MENTIONS.value)
        else:
            raise self.WrongDataSavedException(f"Task type is not valid, type: {self.task.type}")
        return buckets

    @property
    def title_color(self) -> str:
        if self.assigned_as in [TaskAssignedAsEnum.MENTIONS.value, TaskAssignedAsEnum.REPLIER.value]:
            return "#5D81D2"
        if self.task.type == TaskTypesEnum.PURE_TASK.value:
            if self.done_at:
                return "#344054"
        elif self.task.type == TaskTypesEnum.APPROVAL_TASK.value:
            if self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                return "#344054"
            else:
                return "#ea9245"
        return "#61C7CD"

    @property
    def title_background(self) -> str:
        if self.assigned_as in [TaskAssignedAsEnum.MENTIONS.value, TaskAssignedAsEnum.REPLIER.value]:
            return "#E3EAF8"
        if self.task.type == TaskTypesEnum.PURE_TASK.value:
            if self.done_at:
                return "#EDEFF2"
        elif self.task.type == TaskTypesEnum.APPROVAL_TASK.value:
            if self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
                return "#edeff2"
            else:
                return "#fceddf"
        return "#E4F6F7"

    @property
    def is_unread(self):
        return bool(self.read_at is None)

    @property
    def preview(self):
        return get_description_preview(self.task.description)

    # TODO: have to remove
    @property
    def get_action_data(self):
        return TaskAssignmentRepoData(
            id=self.id,
            assignee_id=self.assignee_id,
            read_at=self.read_at,
            done_at=self.done_at,
            archived_at=self.archived_at,
            archived_by_id=self.archived_by_id,
            remind_at=self.remind_at,
            bucket=self.bucket,
            is_imp=self.is_imp,
            assigned_as=self.assigned_as,
            activated_at=self.activated_at,
            title=self.latest_title,
            created_at=self.created_at,
            created_by_id=self.created_by_id,
            is_self_archived=self.is_self_archived,
        )

    def validate_create_data(self) -> dict:
        errors = {}
        if self.assigned_as == TaskAssignedAsEnum.OLD_APPROVER.value:
            errors["assigned_as"] = "assigned_as should not be old_approver."
        if self.read_at and self.read_at != self.created_at:
            errors["read_at"] = "read_at should be same as created_at creator's assignment create."
        if self.done_at:
            errors["done_at"] = "done_at should be null at the time of creation."
        if self.archived_by:
            errors["archived_by"] = "archived_by should be null at the time of creation."
        if not self.archived_at:
            errors["archived_at"] = "archived_at should be provided at the time of creation."
        if self.remind_at:
            errors["remind_at"] = "remind_at should be null at the time of creation."
        if self.archived_at != get_archived_time(self.created_at):
            errors["archived_at"] = "archived_at should be 30 days ahead of created_at."
        if self.bucket == TaskBucketsEnum.MY_COMMENTS.value:
            if self.is_imp:
                errors["is_imp"] = "is_imp should be false for bucket my_comments."
            if self.assigned_as not in [TaskAssignedAsEnum.REPLIER.value, TaskAssignedAsEnum.CREATOR.value]:
                errors["assigned_as"] = "assigned_as should be replier or creator for bucket my_comments."
        if self.bucket == TaskBucketsEnum.MENTIONS.value:
            if self.is_imp and self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                errors["is_imp"] = "is_imp should be false for bucket mentions and assigned as creator."
            if self.assigned_as not in [
                TaskAssignedAsEnum.MENTIONS.value,
                TaskAssignedAsEnum.CREATOR.value,
                TaskAssignedAsEnum.ASSIGNEE.value,
            ]:
                errors["assigned_as"] = "assigned_as should be mentions, creator or assignee for bucket mentions."
        if self.bucket == TaskBucketsEnum.TASK_FOR_ME.value:
            if not self.is_imp:
                errors["is_imp"] = "is_imp should be true for bucket task_for_me."
            if self.assigned_as != TaskAssignedAsEnum.ASSIGNEE.value:
                errors["assigned_as"] = "assigned_as should be assignee for bucket task_for_me."
        if self.bucket == TaskBucketsEnum.TASK_BY_ME.value:
            if self.is_imp:
                errors["is_imp"] = "is_imp should be false for bucket task_by_me."
            if self.assigned_as != TaskAssignedAsEnum.CREATOR.value:
                errors["assigned_as"] = "assigned_as should be creator for bucket task_by_me."
        if self.bucket == TaskBucketsEnum.MY_REQUESTS.value:
            if self.is_imp:
                errors["is_imp"] = "is_imp should be false for bucket my_requests."
            if self.assigned_as != TaskAssignedAsEnum.CREATOR.value:
                errors["assigned_as"] = "assigned_as should be creator for bucket my_requests."
        if self.bucket == TaskBucketsEnum.MY_APPROVALS.value:
            if self.assigned_as not in [
                TaskAssignedAsEnum.IMMEDIATE_APPROVER.value,
                TaskAssignedAsEnum.UPCOMING_APPROVER.value,
            ]:
                errors["assigned_as"] = (
                    "assigned_as should be immediate_approver or upcoming_approver for bucket my_approvals."
                )
        return errors

    def validate_update_data(self) -> dict:
        errors = {}
        # archived at change on read, done, remind, activate and archive for all action.
        if (
            self.read_at
            and self.archived_at != get_archived_time(self.read_at)
            and self.archived_at != self.done_at
            and self.remind_at
            and self.archived_at != get_archived_time(self.remind_at)
            and self.archived_at != get_archived_time(self.activated_at)
            and self.is_imp is True
        ):
            errors["archived_by"] = "archived_at should be 30 days a head of updated_at."
        return errors

    def get_pure_task_actions(self) -> list[TaskActionsEnum]:
        current_time = timezone.now()
        actions = []
        actions.append(TaskActionsEnum.REMIND.value)
        if self.archived_at <= current_time:
            actions.append(TaskActionsEnum.UNDONE.value)
        else:
            if self.assigned_as in [TaskAssignedAsEnum.CREATOR.value, TaskAssignedAsEnum.ASSIGNEE.value]:
                actions.append(TaskActionsEnum.DONE.value)
                actions.append(TaskActionsEnum.SELF_ARCHIVE.value)
                if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                    actions.append(TaskActionsEnum.ARCHIVE.value)
                    actions.append(TaskActionsEnum.EDIT.value)
            elif self.is_imp:
                actions.append(TaskActionsEnum.DONE.value)
            if self.task.comment_id is None and self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                actions.append(TaskActionsEnum.EDIT.value)

            if self.task.comment_id and TaskActionsEnum.EDIT.value in actions:
                actions.remove(TaskActionsEnum.EDIT.value)
        return actions

    def get_comment_task_actions(self) -> list[TaskActionsEnum]:
        actions = []
        actions.append(TaskActionsEnum.REMIND.value)
        if self.is_imp:
            actions.append(TaskActionsEnum.DONE.value)
        return actions

    def get_approval_task_actions(self) -> list[TaskActionsEnum]:
        actions = []
        actions.append(TaskActionsEnum.REMIND.value)
        if self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]:
            if self.is_imp:
                actions.append(TaskActionsEnum.DONE.value)
        else:
            if self.assigned_as == TaskAssignedAsEnum.CREATOR.value:
                if self.task.request_id:
                    if self.task.status == RequestStatusEnum.DRAFT.value:
                        actions.append(TaskActionsEnum.EDIT.value)
                        actions.append(TaskActionsEnum.DELETE.value)
                    elif self.task.status == RequestStatusEnum.PENDING.value:
                        actions.append(TaskActionsEnum.EDIT.value)
                        actions.append(TaskActionsEnum.CANCEL.value)
            elif self.assigned_as == TaskAssignedAsEnum.IMMEDIATE_APPROVER.value:
                actions.append(TaskActionsEnum.APPROVE.value)
                actions.append(TaskActionsEnum.REJECT.value)
                if self.task.status != RequestStatusEnum.HOLD.value:
                    if self.task.request_id:
                        actions.append(TaskActionsEnum.HOLD.value)
            elif self.assigned_as == TaskAssignedAsEnum.UPCOMING_APPROVER.value:
                if self.task.request and self.task.request.type == RequestTypesEnum.SKIPPED.value:
                    actions.append(TaskActionsEnum.APPROVE.value)
                    actions.append(TaskActionsEnum.REJECT.value)
                    if self.task.status != RequestStatusEnum.HOLD.value and self.task.request_id:
                        actions.append(TaskActionsEnum.HOLD.value)
        if self.task.context in [MicroContextChoices.ORDER.value, MicroContextChoices.INSTA_ORDER.value]:
            if TaskActionsEnum.CANCEL.value in actions:
                actions.remove(TaskActionsEnum.CANCEL.value)
            if (
                self.task.status in [RequestStatusEnum.APPROVED.value, RequestStatusEnum.REJECTED.value]
                and TaskActionsEnum.EDIT.value in actions
            ):
                actions.remove(TaskActionsEnum.EDIT.value)
        return actions

    class Meta:
        db_table = "task_assignments"


class TaskComment(BaseModel):
    task = models.ForeignKey(Task, on_delete=models.RESTRICT, related_name="reply_comments")
    blocks = models.JSONField(default=list)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="+")
    commented_at = models.DateTimeField(auto_now_add=True)
    type = models.CharField(max_length=20, choices=CommentReplyTypeChoices.choices)

    class Meta:
        db_table = "task_reply_comments"


class TaskTitleHistory(CreateModel):
    title = models.CharField(max_length=250, null=True, blank=True, default=None)
    title_json = models.JSONField(default=list)
    assignment = models.ForeignKey(TaskAssignment, on_delete=models.RESTRICT, related_name="title_histories")

    class Meta:
        db_table = "task_title_histories"


class TaskAdminTitle(CreateUpdateModel):
    latest_title = models.JSONField(default=list)
    first_title = models.JSONField(default=list)
    task = models.OneToOneField(Task, on_delete=models.RESTRICT, related_name="admin_title")


class TaskBucketHistory(CreateModel):
    bucket = models.CharField(max_length=50, choices=TaskBuckets.choices)
    assignment = models.ForeignKey(TaskAssignment, on_delete=models.RESTRICT, related_name="bucket_histories")
    is_imp = models.BooleanField(default=True)

    class Meta:
        db_table = "task_bucket_histories"
