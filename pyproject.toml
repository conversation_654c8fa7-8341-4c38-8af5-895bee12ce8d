[tool.poetry]
name = "rdash-api"
version = "1.0.0"
description = ""
authors = ["<PERSON><PERSON><PERSON><PERSON><PERSON> <ab<PERSON><PERSON><PERSON>.<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
Django = "^3.2.7"
djangorestframework = "^3.12.4"
dj-rest-auth = "^2.1.11"
coreapi = "^2.3.3"
drf-yasg = "^1.20.0"
django-extensions = "^3.1.3"
django-environ = "^0.7.0"
django-storages = "^1.11.1"
redis = "^3.5.3"
Pillow = "^8.3.2"
django-phonenumber-field = "^5.2.0"
phonenumbers = "^8.12.33"
google-cloud-storage = "^1.42.3"
psycopg2 = { version = "^2.9.1", optional = true }
gdal = { version = "3.1.4", optional = true }
gunicorn = "23.0.0"
django-cors-headers = "^3.10.0"
requests = "^2.26.0"
django-filter = "^21.1"
hashids = "^1.3.1"
PyJWT = "^2.3.0"
dictdiffer = "^0.9.0"
django-cte = "^1.1.5"
beautifulsoup4 = "^4.10.0"
django-multidb-router = "^0.10"
bitrix24-rest = "^1.1.1"
openpyxl = "^3.0.9"
Deprecated = "^1.2.13"
django-admin-rangefilter = "^0.8.4"
firebase-admin = "4.0.0"
grpcio = "1.46.3"
celery = "^5.2.7"
flower = "^1.0.0"
pytest-dotenv = "^0.5.2"
sentry-sdk = "1.44.0"
django-structlog = "^2.2.0"

django-wkhtmltopdf = "^3.4.0"
gspread-dataframe = "^3.3.0"
gspread-formatting = "^1.0.6"
gspread = "5.4.0"
djangorestframework-dataclasses = "^1.1.1"
django-jsonform = "^2.13.0"
django-easy-select2 = "^1.5.8"
django-debug-toolbar = "4.2.0"
pytype = {version = "^2023.1.17", python = "3.10"}
newrelic = "^8.8.0"
django-thread = "^0.0.1"
mixpanel = "^4.10.0"
zipfly = "^6.0.5"
python-pptx = "^0.6.22"
ruff = "0.11.13"
jsonschema = "4.17.3"
django-timezone-field = "^6.1.0"
boto3 = "^1.34.87"
pydantic = "^2.11.1"
twilio = "^9.2.1"
posthog = "^3.5.0"
django-celery-beat = "^2.6.0"
email-validator = "^2.2.0"
django-autocomplete-light = "3.9.7"
cython = "^3.0.11"
[tool.poetry.dev-dependencies]
psycopg2-binary = "^2.9.1"
pytest = "^7.1.2"
pytest-django = "^4.5.2"
grpcio = "1.46.3"
pylint = "^2.15.10"

[tool.poetry.extras]
psycopg2 = ["psycopg2"]
gdal = ["gdal"]


[build-system]
requires = ["poetry-core>=1.0.0"]


[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "venv",
    "migrations",
    "__init__.py",
    "script",
    "bitrix"
]

# Same as Black.
line-length = 120
indent-width = 4

# Assume Python 3.7
target-version = "py37"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.ruff.lint]
select = ["F", "E", "I"]
ignore = ["E501"]