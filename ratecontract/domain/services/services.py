import structlog
from django.db.models import QuerySet
from django.utils import timezone

from common.element_base.services import (
    ElementRelatedCommonServiceNew,
    ElementRelatedServiceNew,
)
from common.entities import ObjectStatus
from common.section.entities import SectionData
from common.section.services import SectionBasePersistenceService
from common.services import model_update, nested_object_segregation
from core.utils import get_relative_path
from element.data.models import SectionBase
from element.data.selectors import elements_fetch_by_ids
from ratecontract.data.choices import (
    RateContractActionType,
    RateContractHistoryAction,
    RateContractStatusType,
)
from ratecontract.data.models import (
    RateContract,
    RateContractDocument,
    RateContractElement,
    RateContractElementGuideline,
    RateContractElementGuidelineAttachment,
    RateContractElementPreviewFile,
    RateContractElementProductionDrawing,
    RateContractElementProductionDrawingTag,
    RateContractHistory,
    RateContractSection,
    RateContractTermsAndConditionAttachment,
)
from ratecontract.data.selectors import (
    rate_contract_data_fetch,
    rate_contract_fetch,
    rate_contract_status_fetch,
)
from ratecontract.domain.entities import (
    RateContractBaseData,
    RateContractCreateData,
    RateContractDocumentBaseData,
    RateContractDocumentCreateData,
    RateContractDocumentUpdateData,
    RateContractElementBaseData,
    RateContractElementCreateData,
    RateContractElementUpdateData,
    RateContractReviseData,
    RateContractTermsAndConditionAttachmentBaseData,
    RateContractUpdateData,
)
from ratecontract.interface.exceptions import (
    RateContractActionError,
    RateContractActiveStatusError,
    RateContractCancelledStatusError,
    RateContractDocumentsNotFountError,
    RateContractDraftStatusError,
    RateContractElementsNotFountError,
    RateContractHoldStatusError,
    RateContractNotFoundError,
    RateContractRevisionDifferentBuyerError,
    RateContractRevisionDifferentVendorError,
    RateContractWrongStatusError,
    VendorNotFoundError,
)
from vendor.data.models import Vendor

logger = structlog.get_logger(__name__)


def rate_contract_history_create(rate_contract_id: int, action: RateContractHistoryAction, created_by_id):
    RateContractHistory.objects.create(rate_contract_id=rate_contract_id, action=action, created_by_id=created_by_id)


class RateContractSectionService(SectionBasePersistenceService):
    model = RateContractSection

    def process_sections(self, rate_contract_id: int, user_id: int, section_name_list: list[str]):
        section_data = SectionData(
            section_model_foreign_key=rate_contract_id,
            created_by_id=user_id,
        )
        available_sections = self.model.objects.filter(rate_contract_id=rate_contract_id).available()
        return self.bulk_import_sections(
            section_names_list=section_name_list, sections=available_sections, section_data=section_data
        )


class RateContractElementRelatedService(ElementRelatedServiceNew):
    preview_file = RateContractElementPreviewFile
    guideline = RateContractElementGuideline
    guideline_attachment = RateContractElementGuidelineAttachment
    production_drawing = RateContractElementProductionDrawing
    production_drawing_tag = RateContractElementProductionDrawingTag


class RateContractElementService:
    def __init__(self) -> None:
        self.model = RateContractElement


class RateContractElementCreateService(RateContractElementService):
    def __init__(self) -> None:
        super().__init__()
        self.section_service = RateContractSectionService()
        self.element_related_common_service = ElementRelatedCommonServiceNew()

    def get_source_element_dict(self, el_element_ids: list[int]):
        elements_dict = elements_fetch_by_ids(element_ids=el_element_ids).in_bulk()

        ordered_elements = {}
        for element_id in el_element_ids:
            if element_id in elements_dict:
                ordered_elements[element_id] = elements_dict[element_id]
            else:
                logger.debug(f"Element with id {element_id} not found.")

        return ordered_elements

    def process_related_elements(self, rc_elements: list, user_id: int, source_elements: list):
        entity_list = self.element_related_common_service.prepare_data_entities(
            created_element_objects=rc_elements,
            element_list=source_elements,
        )
        self.element_related_common_service.process_bulk_create(
            service=RateContractElementRelatedService,
            data=entity_list,
            elements=rc_elements,
            created_by_id=user_id,
        )

    def create(
        self, data: list[RateContractElementCreateData], rate_contract_id: int, user_id: int, source_element_dict: dict
    ):
        section_name_id_dict = self.section_service.process_sections(
            rate_contract_id=rate_contract_id,
            user_id=user_id,
            section_name_list=[element.section_name for element in data],
        )
        el_element_id_rate_dict = {element.element_id: element.rate for element in data}
        el_element_id_secion_name_dict = {element.element_id: element.section_name.lower() for element in data}
        rate_contract_elements = []

        for el_element_id, source_element in source_element_dict.items():
            rate_contract_element = self.model(
                name=source_element.name,
                uom=source_element.uom,
                description=source_element.description,
                category=source_element.category,
                code=source_element.code,
                serial_number=source_element.serial_number,
                element_id=el_element_id,
                rate=el_element_id_rate_dict.get(el_element_id),
                section_id=section_name_id_dict.get(el_element_id_secion_name_dict.get(el_element_id)),
                item_type_id=source_element.item_type_id,
                client_id=source_element.client_id,
                rate_contract_id=rate_contract_id,
                created_by_id=user_id,
            )
            rate_contract_elements.append(rate_contract_element)

        return self.model.objects.bulk_create(rate_contract_elements)

    def process_create(self, data: list[RateContractElementCreateData], rate_contract_id: int, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id}, user_id: {user_id}")
        assert all(
            [issubclass(type(obj), RateContractElementBaseData) for obj in data]
        ), "Invalid dataclass, data needs to be list of RateContractElementBaseData or its subclass"
        source_element_dict = self.get_source_element_dict(el_element_ids=[element.element_id for element in data])
        created_rc_elements = self.create(
            data=data, rate_contract_id=rate_contract_id, user_id=user_id, source_element_dict=source_element_dict
        )
        self.process_related_elements(
            created_rc_elements, user_id=user_id, source_elements=list(source_element_dict.values())
        )
        logger.info("Rate Contract Elements created successfully.")


class RateContractElementDeleteService(RateContractElementService):
    def process_delete(self, rc_element_ids: list[int], user_id: int):
        self.model.objects.filter(id__in=rc_element_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())
        logger.info("Rate Contract Elements deleted successfully.")


class RateContractElementUpdateService(RateContractElementService):
    def __init__(self) -> None:
        super().__init__()
        self.rc_element_create_service = RateContractElementCreateService()
        self.rc_element_delete_service = RateContractElementDeleteService()

    def update(self, data: list[RateContractElementUpdateData], user_id: int):
        rc_element_list = []
        for rc_element_data in data:
            rc_element = self.model(id=rc_element_data.id)
            rc_element.rate = rc_element_data.rate
            rc_element.updated_by_id = user_id
            rc_element.updated_at = timezone.now()
            rc_element_list.append(rc_element)
        self.model.objects.bulk_update(rc_element_list, fields=["updated_by_id", "updated_at", "rate"])

    def process_update(self, data: list[RateContractElementUpdateData], rate_contract_id: int, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id}, user_id: {user_id}")
        assert all(
            [issubclass(type(obj), RateContractElementBaseData) for obj in data]
        ), "Invalid dataclass, data needs to be list of RateContractElementBaseData or its subclass"
        to_create_elements, to_update_elements, to_delete_elements = nested_object_segregation(data)
        self.rc_element_delete_service.process_delete(
            rc_element_ids=[rc_element_data.id for rc_element_data in to_delete_elements], user_id=user_id
        )
        self.rc_element_create_service.process_create(
            data=to_create_elements, rate_contract_id=rate_contract_id, user_id=user_id
        )
        self.update(data=to_update_elements, user_id=user_id)


class RateContractDocumentService:
    def __init__(self) -> None:
        self.model = RateContractDocument


class RateContractDocumentCreateService(RateContractDocumentService):
    def process_create(self, data: list[RateContractDocumentCreateData], rate_contract_id: int, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id}, user_id: {user_id}")
        assert all(
            [issubclass(type(obj), RateContractDocumentBaseData) for obj in data]
        ), "Invalid dataclass, data needs to be list of RateContractDocumentBaseData or its subclass"

        rate_contract_documents = []
        for document in data:
            rate_contract_document = self.model(
                rate_contract_id=rate_contract_id,
                name=document.name,
                reference_number=document.reference_number,
                file=document.file,
                uploaded_by_id=user_id,
            )
            rate_contract_documents.append(rate_contract_document)

        self.model.objects.bulk_create(rate_contract_documents)
        logger.info("Rate Contract Documents created successfully.")


class RateContractDocumentDeleteService(RateContractDocumentService):
    def process_delete(self, rc_document_ids: list[int], user_id: int):
        self.model.objects.filter(id__in=rc_document_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())
        logger.info("Rate Contract Documents deleted successfully.")


class RateContractDocumentUpdateService(RateContractDocumentService):
    def __init__(self) -> None:
        super().__init__()
        self.rc_document_create_service = RateContractDocumentCreateService()
        self.rc_document_delete_service = RateContractDocumentDeleteService()

    def update(self, data: list[RateContractDocumentUpdateData], user_id: int):
        rate_contract_documents: list[RateContractDocument] = []
        for document in data:
            rate_contract_document = RateContractDocument(
                id=document.id,
                name=document.name,
                reference_number=document.reference_number,
                updated_by_id=user_id,
                updated_at=timezone.now(),
            )
            rate_contract_documents.append(rate_contract_document)

        RateContractDocument.objects.bulk_update(
            rate_contract_documents, fields=["name", "reference_number", "updated_by_id", "updated_at"]
        )

    def process_update(self, data: list[RateContractDocumentUpdateData], rate_contract_id: int, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id} and user_id: {user_id}")
        assert all(
            [issubclass(type(obj), RateContractDocumentBaseData) for obj in data]
        ), "Invalid dataclass, data needs to be list of RateContractDocumentBaseData or its subclass"
        to_create_documents, to_update_documents, to_delete_documents = nested_object_segregation(data)
        self.rc_document_delete_service.process_delete(
            rc_document_ids=[rc_document_data.id for rc_document_data in to_delete_documents], user_id=user_id
        )
        self.rc_document_create_service.process_create(
            data=to_create_documents, rate_contract_id=rate_contract_id, user_id=user_id
        )
        self.update(data=to_update_documents, user_id=user_id)
        logger.info("Rate Contract Documents updated successfully.")


class RateContractTermsAndConditionAttachmentService:
    def __init__(self) -> None:
        self.model = RateContractTermsAndConditionAttachment


class RateContractTermsAndConditionAttachementCreateService(RateContractTermsAndConditionAttachmentService):
    def process_create(
        self, data: list[RateContractTermsAndConditionAttachmentBaseData], rate_contract_id: int, user_id: int
    ):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id}, user_id: {user_id}")
        assert all(
            [isinstance(obj, RateContractTermsAndConditionAttachmentBaseData) for obj in data]
        ), "Invalid dataclass, data needs to be list of RateContractTermsAndConditionAttachmentBaseData"

        rate_contract_terms_and_condition_attachments = []
        for attachement in data:
            rate_contract_terms_and_condition_attachment = self.model(
                rate_contract_id=rate_contract_id,
                file=attachement.file,
                name=attachement.name,
                type=attachement.type,
                uploaded_by_id=user_id,
            )
            rate_contract_terms_and_condition_attachments.append(rate_contract_terms_and_condition_attachment)

        self.model.objects.bulk_create(rate_contract_terms_and_condition_attachments, ignore_conflicts=True)
        logger.info("Rate Contract terms and condition attachments created successfully.")


class RateContractTermsAndConditionAttachementDeleteService(RateContractTermsAndConditionAttachmentService):
    def process_delete(self, rc_terms_and_condition_attachement_ids: list[int], user_id: int):
        self.model.objects.filter(id__in=rc_terms_and_condition_attachement_ids).update(
            deleted_by_id=user_id, deleted_at=timezone.now()
        )
        logger.info("Rate Contract terms and condition attachments deleted successfully.")


class RateContractTermsAndConditionAttachementUpdateService(RateContractTermsAndConditionAttachmentService):
    def __init__(self) -> None:
        super().__init__()
        self.rc_terms_and_condition_attachement_create_service = RateContractTermsAndConditionAttachementCreateService()
        self.rc_terms_and_condition_attachement_delete_service = RateContractTermsAndConditionAttachementDeleteService()

    def process_update(
        self, data: list[RateContractTermsAndConditionAttachmentBaseData], rate_contract_id: int, user_id: int
    ):
        (
            to_create_terms_condition_attachments,
            _,
            to_delete_terms_condition_attachments,
        ) = nested_object_segregation(data)
        self.rc_terms_and_condition_attachement_delete_service.process_delete(
            rc_terms_and_condition_attachement_ids=[
                rc_terms_and_condition_attachement_data.id
                for rc_terms_and_condition_attachement_data in to_delete_terms_condition_attachments
            ],
            user_id=user_id,
        )
        self.rc_terms_and_condition_attachement_create_service.process_create(
            data=to_create_terms_condition_attachments, rate_contract_id=rate_contract_id, user_id=user_id
        )


class RateContractService:
    def __init__(self):
        self.model = RateContract

    def process_dates(self, data: RateContractBaseData):
        started_at = (
            timezone.datetime(data.started_at.year, data.started_at.month, data.started_at.day, 0, 0, 0)
            if data.started_at
            else None
        )
        ended_at = (
            timezone.datetime(data.ended_at.year, data.ended_at.month, data.ended_at.day, 23, 59, 59)
            if data.ended_at
            else None
        )
        sign_date = (
            timezone.datetime(data.sign_date.year, data.sign_date.month, data.sign_date.day, 0, 0, 0)
            if data.sign_date
            else None
        )
        return (started_at, ended_at, sign_date)


class RateContractCreateService(RateContractService):
    def __init__(self):
        super().__init__()
        self.rc_element_service = RateContractElementCreateService()
        self.rc_document_service = RateContractDocumentCreateService()
        self.rc_terms_and_condition_attachement_service = RateContractTermsAndConditionAttachementCreateService()

    def create(self, data: RateContractCreateData, organization_id: int, client_id: int, user_id: int):
        logger.debug(f"data: {data}, organization_id: {organization_id}, client_id: {client_id}, user_id: {user_id}")
        assert issubclass(
            type(data), RateContractBaseData
        ), "Invalid dataclass, data needs to be of RateContractBaseData or its subclass"

        if data.vendor_id and not Vendor.objects.filter(organization_id=data.vendor_id).exists():
            logger.debug(f"Vendor Object does not exists for organization (id: {organization_id})")
            raise VendorNotFoundError("Vendor object does not exists for selected vendor organization.")
        if data.is_activate:
            if len(data.elements) == 0:
                raise RateContractElementsNotFountError("At least one element is required to activate rate contract.")
            if len(data.documents) == 0:
                raise RateContractDocumentsNotFountError("At least one document is required to activate rate contract.")

        started_at, ended_at, sign_date = self.process_dates(data=data)
        rate_contract = self.model(
            name=data.name,
            organization_id=organization_id,
            client_id=client_id,
            vendor_id=data.vendor_id,
            started_at=started_at,
            ended_at=ended_at,
            sign_date=sign_date,
            terms_conditions=data.terms_conditions,
            status=RateContractStatusType.ACTIVE if data.is_activate else RateContractStatusType.DRAFT,
            created_by_id=user_id,
            payment_term_id=data.payment_term_id,
            payment_term_text=data.payment_term_text,
            buyer_id=data.buyer_id,
        )
        if data.is_activate:
            rate_contract.updated_by_id = user_id
            rate_contract.updated_at = timezone.now()
        rate_contract.save()
        rate_contract_history_create(
            rate_contract_id=rate_contract.pk, action=RateContractHistoryAction.CREATED, created_by_id=user_id
        )
        if data.is_activate:
            rate_contract_history_create(
                rate_contract_id=rate_contract.pk, action=RateContractHistoryAction.ACTIVATED, created_by_id=user_id
            )
        logger.debug(f"Rate Contract object with id: {rate_contract.id} created and returned")
        return rate_contract

    def process_create(self, data: RateContractCreateData, organization_id: int, client_id: int, user_id: int):
        rate_contract = self.create(data=data, organization_id=organization_id, client_id=client_id, user_id=user_id)
        self.rc_element_service.process_create(data=data.elements, rate_contract_id=rate_contract.id, user_id=user_id)
        self.rc_document_service.process_create(data=data.documents, rate_contract_id=rate_contract.id, user_id=user_id)
        self.rc_terms_and_condition_attachement_service.process_create(
            data=data.terms_conditions_attachments, rate_contract_id=rate_contract.id, user_id=user_id
        )
        return rate_contract


class RateContractUpdateService(RateContractService):
    def __init__(self):
        super().__init__()
        self.rc_element_service = RateContractElementUpdateService()
        self.rc_document_service = RateContractDocumentUpdateService()
        self.rc_terms_and_condition_attachement_service = RateContractTermsAndConditionAttachementUpdateService()

    def update(self, data: RateContractUpdateData, rate_contract_id: int, user_id: int):
        instance = rate_contract_fetch(rate_contract_id=rate_contract_id)
        if not instance:
            logger.debug(f"Rate Contract not found for id {rate_contract_id}.")
            raise RateContractNotFoundError("Rate contract not exits.")

        if instance.status != RateContractStatusType.DRAFT:
            logger.debug(
                f"Rate Contract (id: {rate_contract_id}) in {instance.status} status, No modification possible"
            )
            raise RateContractWrongStatusError("Rate contract not in draft status")

        data.started_at, data.ended_at, data.sign_date = self.process_dates(data=data)
        allowed_fields = [
            "name",
            "vendor_id",
            "started_at",
            "ended_at",
            "sign_date",
            "terms_conditions",
            "payment_term_id",
            "payment_term_text",
            "buyer_id",
        ]
        instance, _, _ = model_update(
            instance=instance, data=data, fields=allowed_fields, updated_by_id=user_id, save=False
        )
        if data.is_activate:
            if instance.element_count == 0:
                raise RateContractElementsNotFountError("At least one element is required to activate rate contract.")
            if instance.document_count == 0:
                raise RateContractDocumentsNotFountError("At least one document is required to activate rate contract.")
            instance.status = RateContractStatusType.ACTIVE

        instance.save()
        rate_contract_history_create(
            rate_contract_id=instance.pk, action=RateContractHistoryAction.UPDATED, created_by_id=user_id
        )
        return instance

    def process_update(self, data: RateContractUpdateData, rate_contract_id: int, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id} and user_id: {user_id}")
        self.rc_element_service.process_update(data=data.elements, rate_contract_id=rate_contract_id, user_id=user_id)
        self.rc_document_service.process_update(data=data.documents, rate_contract_id=rate_contract_id, user_id=user_id)
        self.rc_terms_and_condition_attachement_service.process_update(
            data=data.terms_conditions_attachments, rate_contract_id=rate_contract_id, user_id=user_id
        )
        self.update(data=data, rate_contract_id=rate_contract_id, user_id=user_id)
        logger.debug(f"Rate Contract (id: {rate_contract_id}) updated successfully.")


class RateContractReviseElementService(RateContractElementCreateService):
    def get_element_data(
        self, elements_data: list[RateContractElementUpdateData], old_rc_elements: QuerySet[RateContractElement]
    ):
        to_create_elements_data, to_update_elements_data, to_delete_elements_data = nested_object_segregation(
            elements_data
        )
        update_rc_element_ids = [e.id for e in to_update_elements_data]
        self.create_element_el_ids = [e.element_id for e in to_create_elements_data]
        self.old_rc_elements = old_rc_elements.exclude(id__in=[e.id for e in to_delete_elements_data])
        revise_rc_element_data: list[RateContractElementUpdateData] = []
        for element in self.old_rc_elements:
            if element.id in update_rc_element_ids:
                continue
            element_data = RateContractElementUpdateData(
                id=None,
                object_status=ObjectStatus.ADD.value,
                element_id=element.element_id,
                rate=element.rate,
                section_name=element.section.name if element.section else SectionBase.DEFAULT_SECTION,
            )
            revise_rc_element_data.append(element_data)
        revise_rc_element_data.extend(to_create_elements_data)
        revise_rc_element_data.extend(to_update_elements_data)
        return revise_rc_element_data

    def get_source_element_dict(self, el_element_ids: list[int]):
        element_id_rc_element_dict = {element.element_id: element for element in self.old_rc_elements}
        return elements_fetch_by_ids(element_ids=self.create_element_el_ids).in_bulk() | element_id_rc_element_dict


class RateContractReviseDocumentService(RateContractDocumentCreateService):
    def get_document_data(
        self, documents_data: list[RateContractDocumentUpdateData], old_rc_documents: QuerySet[RateContractDocument]
    ):
        to_create_documents_data, to_update_documents_data, to_delete_documents_data = nested_object_segregation(
            documents_data
        )
        update_rc_document_ids = [d.id for d in to_update_documents_data]
        old_rc_documents = old_rc_documents.available().exclude(id__in=[d.id for d in to_delete_documents_data])
        revise_rc_document_data: list[RateContractDocumentUpdateData] = []
        for document in old_rc_documents:
            if document.id not in update_rc_document_ids:
                continue
            document_data = RateContractDocumentUpdateData(
                id=None,
                object_status=ObjectStatus.ADD.value,
                name=document.name,
                reference_number=document.reference_number,
                file=get_relative_path(document.file.url) if document.file else None,
                is_cancelled=document.is_cancelled,
            )
            revise_rc_document_data.append(document_data)
        revise_rc_document_data.extend(to_create_documents_data)
        revise_rc_document_data.extend(to_update_documents_data)
        return revise_rc_document_data


class RateContractReviseTermsAndConditionAttachementService(RateContractTermsAndConditionAttachementCreateService):
    def get_term_condition_attachment_data(
        self,
        attachment_data: list[RateContractTermsAndConditionAttachmentBaseData],
        old_rc_attachments: QuerySet[RateContractTermsAndConditionAttachment],
    ):
        (
            to_create_terms_condition_attachments,
            _,
            to_delete_terms_condition_attachments,
        ) = nested_object_segregation(attachment_data)

        old_attachments = old_rc_attachments.exclude(id__in=[d.id for d in to_delete_terms_condition_attachments])
        revise_terms_condition_attachments: list[RateContractTermsAndConditionAttachmentBaseData] = []
        for attachment in old_attachments:
            attachment_data = RateContractTermsAndConditionAttachmentBaseData(
                id=None,
                type=attachment.type,
                object_status=ObjectStatus.ADD.value,
                name=attachment.name,
                file=get_relative_path(attachment.file.url) if attachment.file else None,
            )
            revise_terms_condition_attachments.append(attachment_data)
        revise_terms_condition_attachments.extend(to_create_terms_condition_attachments)
        return revise_terms_condition_attachments


class RateContractReviseService(RateContractCreateService):
    def __init__(self, rate_contract_id: int):
        super().__init__()
        self.rate_contract_id = rate_contract_id
        self.instance = rate_contract_data_fetch(rate_contract_id=rate_contract_id)
        self.rc_element_service = RateContractReviseElementService()
        self.rc_document_service = RateContractReviseDocumentService()
        self.rc_terms_and_condition_attachement_service = RateContractReviseTermsAndConditionAttachementService()

    def update_old_rcs(self, revised_rc_id, user_id: int):
        logger.debug(f"Update parent Rate Contract (id: {self.rate_contract_id}) status")
        if self.instance.status == RateContractStatusType.ACTIVE:
            self.instance.status = RateContractStatusType.CANCELLED
        self.instance.parent_id = revised_rc_id
        self.instance.updated_by_id = user_id
        self.instance.save()
        RateContract.objects.filter(parent_id=self.instance.pk).update(parent_id=revised_rc_id)

    def process_revise(self, data: RateContractUpdateData, user_id: int):
        logger.debug(f"data: {data}, rate_contract_id: {self.rate_contract_id}, user_id: {user_id}")
        logger.debug(f"Rate Contract: {self.instance}")
        if self.instance.status == RateContractStatusType.DRAFT:
            logger.debug(f"Rate Contract (id: {self.rate_contract_id}) in draft states so it can be modified.")
            raise RateContractDraftStatusError("This rate contract in draft status, it is editable not revisable")
        if data.vendor_id != self.instance.vendor_id:
            logger.debug(
                f"Revised Rate Contract's vendor must be same as vendor of Rate Contact (id: {self.rate_contract_id})"
            )
            raise RateContractRevisionDifferentVendorError("Vendor is different then parent Rate Contract")
        if data.buyer_id != self.instance.buyer_id:
            logger.debug(
                f"Revised Rate Contract's buyer must be same as buyer of Rate Contact (id: {self.rate_contract_id})"
            )
            raise RateContractRevisionDifferentBuyerError("Buyer is different then parent Rate Contract")

        revised_rc_element_data = self.rc_element_service.get_element_data(
            elements_data=data.elements, old_rc_elements=self.instance.elements.available()
        )
        revised_rc_documents_data = self.rc_document_service.get_document_data(
            documents_data=data.documents, old_rc_documents=self.instance.documents.available()
        )
        revised_rc_terms_condition_attachments_data = (
            self.rc_terms_and_condition_attachement_service.get_term_condition_attachment_data(
                attachment_data=data.terms_conditions_attachments,
                old_rc_attachments=self.instance.terms_condition_attachments.available(),
            )
        )
        revised_rate_contract_data = RateContractReviseData(
            name=data.name,
            vendor_id=self.instance.vendor_id,
            started_at=data.started_at,
            ended_at=data.ended_at,
            sign_date=data.sign_date,
            terms_conditions=data.terms_conditions,
            elements=revised_rc_element_data,
            documents=revised_rc_documents_data,
            terms_conditions_attachments=revised_rc_terms_condition_attachments_data,
            buyer_id=self.instance.buyer_id,
            payment_term_id=data.payment_term_id,
            payment_term_text=data.payment_term_text,
            is_activate=data.is_activate,
        )
        revised_rate_contract = self.process_create(
            data=revised_rate_contract_data,
            organization_id=self.instance.organization_id,
            client_id=self.instance.client_id,
            user_id=user_id,
        )
        self.update_old_rcs(revised_rc_id=revised_rate_contract.id, user_id=user_id)
        rate_contract_history_create(
            rate_contract_id=self.instance.pk, action=RateContractHistoryAction.REVISED, created_by_id=user_id
        )
        logger.info("Rate Contract revised successfully")


def rate_contract_activate(rate_contract_id: int, user_id: int) -> None:
    logger.debug(f"rate_contract_id: {rate_contract_id}")
    instance = rate_contract_data_fetch(rate_contract_id=rate_contract_id)
    logger.debug(f"Rate contract instance: {instance}")
    if not instance:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) not found")
        raise RateContractNotFoundError("Rate contract not exits")

    if instance.status == RateContractStatusType.ACTIVE:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) already in active state")
        raise RateContractActiveStatusError("Rate Contract are already in Active state")

    if instance.status not in [RateContractStatusType.DRAFT, RateContractStatusType.ONHOLD]:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) must be in Draft or OnHold state for activation")
        raise RateContractWrongStatusError("Rate Contract must be in draft or hold state for Activation")

    if instance.elements.count() == 0:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) must have at least one element to activate")
        raise RateContractElementsNotFountError("Rate Contract does not have any Elements")

    if instance.documents.count() == 0:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) must have at least one document to activate")
        raise RateContractDocumentsNotFountError("Rate Contract does not have any Documents")

    instance.status = RateContractStatusType.ACTIVE
    instance.updated_by_id = user_id
    instance.updated_at = timezone.now()
    instance.save()
    rate_contract_history_create(
        rate_contract_id=instance.pk, action=RateContractHistoryAction.ACTIVATED, created_by_id=user_id
    )
    logger.info("Rate Contract Activate Successfully")


def rate_contract_cancel(rate_contract_id: int, user_id: int) -> None:
    logger.debug(f"rate_contract_id: {rate_contract_id}")
    instance = rate_contract_fetch(rate_contract_id=rate_contract_id)
    logger.debug(f"Rate contract instance: {instance}")
    if not instance:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) not found")
        raise RateContractNotFoundError("Rate contract not exits")

    if instance.status == RateContractStatusType.CANCELLED:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) is already in cancelled state")
        raise RateContractCancelledStatusError("Rate Contract are already Cancelled")

    if instance.status not in [RateContractStatusType.ACTIVE, RateContractStatusType.ONHOLD]:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) must be active or onhold for cancel")
        raise RateContractWrongStatusError("Rate Contract must be in active or OnHold state for Cancel")

    instance.status = RateContractStatusType.CANCELLED
    instance.updated_by_id = user_id
    instance.updated_at = timezone.now()
    instance.save()
    rate_contract_history_create(
        rate_contract_id=instance.pk, action=RateContractHistoryAction.CANCELLED, created_by_id=user_id
    )
    logger.info("Rate Contract Cancelled successfully")


def rate_contract_hold(rate_contract_id: int, user_id: int) -> None:
    logger.debug(f"rate_contract_id: {rate_contract_id}")
    instance = rate_contract_fetch(rate_contract_id=rate_contract_id)
    logger.debug(f"Rate contract : {instance}")
    if not instance:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) not found")
        raise RateContractNotFoundError("Rate contract not exits")

    if instance.status == RateContractStatusType.ONHOLD:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) are already on hold")
        raise RateContractHoldStatusError("Rate Contract are already on Hold")

    if instance.status != RateContractStatusType.ACTIVE:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) is not activated")
        raise RateContractWrongStatusError("Rate Contract must be in active state for Hold action")

    instance.status = RateContractStatusType.ONHOLD
    instance.updated_by_id = user_id
    instance.updated_at = timezone.now()
    instance.save()
    rate_contract_history_create(
        rate_contract_id=instance.pk, action=RateContractHistoryAction.HELD, created_by_id=user_id
    )
    logger.info("Rate Contract Holded successfully")


def rate_contract_status_update(action: RateContractActionType, rate_contract_id: int, user_id: int) -> None:
    logger.debug(f"action: {action} and rate_contract_id: {rate_contract_id}")
    if action == RateContractActionType.ACTIVATE:
        try:
            rate_contract_activate(rate_contract_id=rate_contract_id, user_id=user_id)
        except (RateContractNotFoundError, RateContractActionError) as e:
            raise RateContractActionError(e.message)
    elif action == RateContractActionType.CANCEL:
        try:
            rate_contract_cancel(rate_contract_id=rate_contract_id, user_id=user_id)
        except (RateContractNotFoundError, RateContractActionError) as e:
            raise RateContractActionError(e.message)
    elif action == RateContractActionType.HOLD:
        try:
            rate_contract_hold(rate_contract_id=rate_contract_id, user_id=user_id)
        except (RateContractNotFoundError, RateContractActionError) as e:
            raise RateContractActionError(e.message)


def rate_contract_document_create(
    data: RateContractDocumentBaseData, rate_contract_id: int, user_id: int
) -> RateContractDocument:
    logger.debug(f"data: {data}, rate_contract_id: {rate_contract_id}, user_id: {user_id}")
    instance = RateContractDocument(
        rate_contract_id=rate_contract_id,
        name=data.name,
        reference_number=data.reference_number,
        file=data.file,
        uploaded_by_id=user_id,
    )
    instance.save()
    logger.info("Rate Contract Document created successfully")
    return instance


def rate_contract_document_update(document_id: int, name: str, reference_number: str, user_id: int) -> None:
    logger.debug(f"document_id: {document_id}, name: {name}, reference_number: {reference_number}, user_id: {user_id}")
    RateContractDocument.objects.filter(id=document_id).update(
        name=name, reference_number=reference_number, updated_by_id=user_id, updated_at=timezone.now()
    )
    logger.info("Rate Contract Document updated successfully.")


def rate_contract_document_cancel_delete(rate_contract_id: int, document_id: int, user_id: int) -> None:
    logger.debug(f"rate_contract_id: {rate_contract_id}, document_id: {document_id}, user_id: {user_id}")
    rate_contract_status: RateContractStatusType = rate_contract_status_fetch(rate_contract_id=rate_contract_id)
    logger.debug(f"rate_contract_status: {rate_contract_status}")
    if rate_contract_status is None:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) not found")
        raise RateContractNotFoundError("Rate contract not exits")

    if rate_contract_status == RateContractStatusType.CANCELLED:
        logger.debug(f"Rate Contract (id: {rate_contract_id}) is cancelled so it can not be modified")
        raise RateContractCancelledStatusError("Rate Contract is Cancelled, So it can not be modified")
    # delete document
    if rate_contract_status == RateContractStatusType.DRAFT:
        RateContractDocument.objects.filter(id=document_id).update(deleted_by=user_id, deleted_at=timezone.now())
        logger.debug(f"Rate Contract Document id: {document_id} deleted successfully")
    # cancel document
    else:
        RateContractDocument.objects.filter(id=document_id).update(
            cancelled_by_id=user_id, cancelled_at=timezone.now(), updated_by_id=user_id, updated_at=timezone.now()
        )
        logger.debug(f"Rate Contract Document id: {document_id} cancelled successfully")
