from rest_framework import serializers


class EmailSerializer(serializers.Serializer):
    to = serializers.ListField(child=serializers.EmailField(), required=True)
    cc = serializers.ListField(child=serializers.EmailField(), required=False)
    bcc = serializers.ListField(child=serializers.EmailField(), required=False)
    subject = serializers.CharField(min_length=1, max_length=50, allow_null=False, required=True)
    body = serializers.CharField(min_length=1, max_length=250, allow_null=False, required=True)
    attachments = serializers.ListField(child=serializers.URLField(), required=False)
