from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED

from common.apis import BaseApi, BaseOpenApi
from common.serializers import BaseSerializer
from smtp_email.domain.services import DjangoEmailService
from smtp_email.interface.serializers import EmailSerializer


class EmailView(BaseApi):
    permission_classes = [IsAuthenticated]
    input_serializer_class = EmailSerializer

    @swagger_auto_schema(request_body=EmailSerializer, operation_summary="Send Email Content")
    @transaction.atomic
    def post(self, request):
        data = self.validate_input_data()
        DjangoEmailService().send_email(
            subject=data.get("subject"),
            body=data.get("body"),
            from_email=settings.EMAIL_HOST_USER,
            to=data.get("to"),
            cc=data.get("cc"),
            bcc=data.get("bcc"),
            attachments=data.get("attachments"),
            context=data.get("context"),
            html_email_template_name=data.get("html_email_template_name"),
        )
        return Response(
            {
                "message": _("Email has been sent successfully"),
            },
            status=status.HTTP_200_OK,
        )


class ContactUsEmailApi(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        user_email = serializers.EmailField()
        message = serializers.CharField()
        subject = serializers.CharField()

        class Meta:
            ref_name = "ContactUsEmailApiInput"

    input_serializer_class = InputSerializer

    pagination_class = None

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: "Successfully"},
        operation_id="contact_us_send_email",
        operation_summary="Contact Us email send",
    )
    @transaction.atomic
    def post(self, *args, **kwargs):
        data = self.validate_input_data()
        DjangoEmailService().send_email(
            subject=data.get("subject"),
            body=data.get("message"),
            from_email=settings.EMAIL_HOST_USER,
            to=[settings.SUPPORT_EMAIL],
            reply_to=[data.get("user_email")],
        )

        return Response(
            status=HTTP_201_CREATED,
        )
