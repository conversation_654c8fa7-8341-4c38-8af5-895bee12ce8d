from abc import ABC, abstractmethod
from typing import Any, Optional

import requests
import structlog
from django.conf import settings
from django.core.mail import EmailMultiAlternatives, get_connection
from django.template import loader

from common.helpers import get_file_name
from smtp_email.data.models import Email

logger = structlog.get_logger(__name__)


class AbstractMailClass(ABC):
    @abstractmethod
    def send_email(self, *args, **kwargs):
        pass


class DjangoEmailService(AbstractMailClass):
    @staticmethod
    def attach_attachments(attachments, email_message):
        if attachments:
            for path in attachments:
                response = requests.get(path)
                email_message.attach(get_file_name(path), response.content, "application/vnd.ms-excel")

        return email_message

    def send_email(
        self,
        subject,
        body,
        to,
        from_name: str = "Rdash",
        cc=None,
        bcc=None,
        from_email=None,
        context=None,
        attachments=None,
        html_email_template_name=None,
        created_by_id: Optional[int] = None,
        reply_to=None,
        log_email: bool = True,
    ):
        """
        Send a django.core.mail.EmailMultiAlternatives to `to_email`.
        """
        logger.info("Email data received", subject=subject, body=body, to=to, cc=cc, bcc=bcc)

        # Email subject *must not* contain newlines
        subject = "".join(subject.splitlines())
        email_message = EmailMultiAlternatives(
            subject, body, f'"{from_name}" <{settings.EMAIL_HOST_USER}>', to, bcc=bcc, cc=cc, reply_to=reply_to
        )

        if html_email_template_name is not None:
            html_email = loader.render_to_string(html_email_template_name, context)
            email_message.attach_alternative(html_email, "text/html")
        if attachments:
            email_message = self.attach_attachments(attachments=attachments, email_message=email_message)
        email_message.send()
        logger.info("email sent successfully!")
        # Save the info to DB!
        if log_email:
            Email.objects.create(
                sender=from_email,
                to_receiver=to,
                cc_receiver=cc,
                bcc_receiver=bcc,
                subject=subject,
                body=body,
                files=attachments,
                created_by_id=created_by_id,
            )
        logger.info("email data saved successfully!")


class CustomEmailMultiAlternatives(EmailMultiAlternatives):
    def get_connection(self, fail_silently: bool = ...) -> Any:
        if not self.connection:
            self.connection = get_connection(backend=settings.SES_EMAIL_BACKEND, fail_silently=fail_silently)
        return self.connection
