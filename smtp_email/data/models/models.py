from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db import models


class EmailBase(models.Model):
    to_receiver = Array<PERSON>ield(models.EmailField(), blank=False, null=False)
    cc_receiver = Array<PERSON>ield(models.EmailField(null=True, blank=True), blank=True, null=True)
    bcc_receiver = ArrayField(models.EmailField(null=True, blank=True), blank=True, null=True)
    subject = models.CharField(max_length=500, null=False)
    files = ArrayField(models.URLField(null=True, blank=True), null=True, blank=True)

    class Meta:
        abstract = True


class Email(EmailBase):
    body = models.CharField(max_length=1000, null=True, blank=True)
    sender = models.EmailField(null=False)
    sent_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)

    class Meta:
        db_table = "email"
