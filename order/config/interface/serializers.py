from rest_framework import serializers

from common.serializers import BaseModelSerializer
from core.serializers import UserModelSerializer
from order.data.models import (
    OrganizationIncomingOrderNotificationPoc,
    OrganizationOutgoingOrderNotificationPoc,
    OrganizationPurchaseOrderType,
)


class OrganizationIncomingOrderNotificationPOCModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrganizationIncomingOrderNotificationPoc
        fields = "__all__"


class OrganizationOutgoingOrderNotificationPOCModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrganizationOutgoingOrderNotificationPoc
        fields = "__all__"


class POCUserSerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "VendorPOCUserSerializer"
        fields = ("id", "name", "email", "photo")


class OrganizationPurchaseOrderTypeModelSerializer(BaseModelSerializer):
    name = serializers.CharField()

    class Meta:
        model = OrganizationPurchaseOrderType
        fields = ("id", "name")
