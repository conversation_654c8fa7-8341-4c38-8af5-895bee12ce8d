from django.urls import path

from order.config.interface.apis import (
    DefaultIncomingOrderNotificationReceiverAddApi,
    DefaultIncomingOrderNotificationReceiverRemoveApi,
    DefaultOutgoingOrderNotificationReceiverAddApi,
    DefaultOutgoingOrderNotificationReceiverRemoveApi,
    IncomingOrderNotificationReceiverListApi,
    OrgPurchaseOrderTypeCreateApi,
    OrgPurchaseOrderTypeDeleteApi,
    OrgPurchaseOrderTypeListApi,
    OrgPurchaseOrderTypeUpdateApi,
    OutgoingOrderNotificationReceiverListApi,
)

urlpatterns = [
    path("default-order-cc/", OutgoingOrderNotificationReceiverListApi.as_view(), name="default-order-cc-users"),
    path(
        "default-order-cc/add/", DefaultOutgoingOrderNotificationReceiverAddApi.as_view(), name="default-order-cc-add"
    ),
    path(
        "default-order-cc/<hash_id:poc_id>/delete/",
        DefaultOutgoingOrderNotificationReceiverRemoveApi.as_view(),
        name="default-order-cc-delete",
    ),
    path(
        "default-order-receivers/", IncomingOrderNotificationReceiverListApi.as_view(), name="default-order-bcc-users"
    ),
    path(
        "default-order-receivers/add/",
        DefaultIncomingOrderNotificationReceiverAddApi.as_view(),
        name="default-order-bcc-add",
    ),
    path(
        "default-order-receivers/<hash_id:poc_id>/delete/",
        DefaultIncomingOrderNotificationReceiverRemoveApi.as_view(),
        name="default-order-bcc-delete",
    ),
    path(
        "purchase-order-types/list/",
        OrgPurchaseOrderTypeListApi.as_view(),
        name="purchase-order-type-org-list",
    ),
    path(
        "purchase-order-types/create/",
        OrgPurchaseOrderTypeCreateApi.as_view(),
        name="purchase-order-type-create",
    ),
    path(
        "purchase-order-types/<hash_id:purchase_order_type_id>/update/",
        OrgPurchaseOrderTypeUpdateApi.as_view(),
        name="purchase-order-type-update",
    ),
    path(
        "purchase-order-types/<hash_id:purchase_order_type_id>/delete/",
        OrgPurchaseOrderTypeDeleteApi.as_view(),
        name="purchase-order-type-delete",
    ),
]
