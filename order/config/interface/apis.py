from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from pydantic import Field
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from common.serializers import BaseSerializer, HashIdField
from core.apis import OrgBaseApi
from order.config.data.entities import PurchaseOrderTypeDataEntity, PurchaseOrderTypeListEntity
from order.config.data.factories import OrganizationPurchaseOrderTypeRepoFactory
from order.config.data.repositories import OrganizationPurchaseOrderTypeRepo
from order.config.domain.exceptions import OrderConfigNotificationException
from order.config.domain.services import (
    IncomingOrderNotificationReceiverService,
    OutgoingOrderNotificationReceiverService,
)
from order.config.interface.serializers import (
    POCUserSerializer,
)


class OutgoingOrderNotificationReceiverListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: POCUserSerializer()},
        operation_id="outgoing_order_notification_receiver_list_api",
        operation_summary="outgoing order receiver list API",
    )
    def get(self, request, *args, **kwargs):
        receivers = OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).get_all_receivers()
        self.set_response_message("user list fetched succesfully.")
        return Response(data=self.serializer_class(receivers, many=True).data, status=HTTP_200_OK)


class DefaultOutgoingOrderNotificationReceiverAddApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        receiver_id = HashIdField()

        class Meta:
            ref_name = "AddReceiverInputSerializer"

    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: POCUserSerializer()},
        operation_id="add_default_vo_notification_receiver_api",
        operation_summary="add default outgoing order receiver API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            receiver = OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).add(
                receiver_id=data.get("receiver_id"), user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user added successfully.")
        return Response(data=self.serializer_class(receiver).data, status=HTTP_201_CREATED)


class DefaultOutgoingOrderNotificationReceiverRemoveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="remove_default_vo_notification_receiver_api",
        operation_summary="remove default outgoing order receiver API",
    )
    @transaction.atomic
    def delete(self, request, poc_id, *args, **kwargs):
        try:
            OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).remove(
                poc_id=poc_id, user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user deleted successfully.")
        return Response(status=HTTP_200_OK)


class IncomingOrderNotificationReceiverListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: POCUserSerializer()},
        operation_id="incoming_order_notification_receiver_list_api",
        operation_summary="incoming order receiver list API",
    )
    def get(self, request, *args, **kwargs):
        receivers = IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).get_all_receivers()
        self.set_response_message("user list fetched succesfully.")
        return Response(data=self.serializer_class(receivers, many=True).data, status=HTTP_200_OK)


class DefaultIncomingOrderNotificationReceiverAddApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        receiver_id = HashIdField()

        class Meta:
            ref_name = "AddReceiverInputSerializer"

    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: POCUserSerializer()},
        operation_id="add_default_co_notification_receiver_api",
        operation_summary="add default incoming order receiver API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            receiver = IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).add(
                receiver_id=data.get("receiver_id"), user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user added successfully.")
        return Response(data=self.serializer_class(receiver).data, status=HTTP_201_CREATED)


class DefaultIncomingOrderNotificationReceiverRemoveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="remove_default_co_notification_receiver_api",
        operation_summary="remove default incoming order receiver API",
    )
    @transaction.atomic
    def delete(self, request, poc_id, *args, **kwargs):
        try:
            IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).remove(
                poc_id=poc_id, user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user deleted successfully.")
        return Response(status=HTTP_200_OK)


class OrgPurchaseOrderTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(PurchaseOrderTypeListEntity)},
        operation_id="order_org_purchase_order_type_list",
        operation_summary="Get Organization Purchase Order Type List",
    )
    def get(self, request, *args, **kwargs):
        org_purchase_order_type_repo = OrganizationPurchaseOrderTypeRepoFactory(
            org_id=self.get_organization_id()
        ).get_repo()
        purchase_order_types = org_purchase_order_type_repo.get_org_purchase_order_types()
        return Response(pydantic_dump(purchase_order_types), status=HTTP_200_OK)


class OrgPurchaseOrderTypeCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputModel(PydanticInputBaseModel):
        name: str = Field(max_length=100)

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(InputModel),
        responses={HTTP_200_OK: pydantic_schema(PurchaseOrderTypeDataEntity)},
        operation_id="order_org_purchase_order_type_create",
        operation_summary="Create Organization Purchase Order Type",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_pydantic_input_data()
        org_purchase_order_type_repo = OrganizationPurchaseOrderTypeRepoFactory(
            org_id=self.get_organization_id()
        ).get_repo()
        try:
            purchase_order_type_data = org_purchase_order_type_repo.create_org_purchase_order_type(
                name=data.name, user_id=self.get_user_id()
            )
        except OrganizationPurchaseOrderTypeRepo.PurchaseOrderTypeException as e:
            self.set_response_message(e.message)
            raise e
        return Response(pydantic_dump(purchase_order_type_data), status=HTTP_200_OK)


class OrgPurchaseOrderTypeUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputModel(PydanticInputBaseModel):
        is_active: bool
        is_default: bool

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        request_body=pydantic_schema(PurchaseOrderTypeDataEntity),
        responses={HTTP_200_OK: ""},
        operation_id="order_org_purchase_order_type_update",
        operation_summary="Update Organization Purchase Order Type",
    )
    def put(self, request, purchase_order_type_id, *args, **kwargs):
        data = self.validate_pydantic_input_data()
        org_purchase_order_type_repo = OrganizationPurchaseOrderTypeRepoFactory(
            org_id=self.get_organization_id()
        ).get_repo()
        try:
            purchase_order_type_data = org_purchase_order_type_repo.update_org_purchase_order_type(
                purchase_order_type_id=purchase_order_type_id,
                is_active=data.is_active,
                is_default=data.is_default,
                user_id=self.get_user_id(),
            )
        except OrganizationPurchaseOrderTypeRepo.PurchaseOrderTypeException as e:
            self.set_response_message(e.message)
            raise e
        return Response(pydantic_dump(purchase_order_type_data), status=HTTP_200_OK)


class OrgPurchaseOrderTypeDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="order_org_purchase_order_type_delete",
        operation_summary="Delete Organization Purchase Order Type",
    )
    def delete(self, request, purchase_order_type_id, *args, **kwargs):
        org_purchase_order_type_repo = OrganizationPurchaseOrderTypeRepoFactory(
            org_id=self.get_organization_id()
        ).get_repo()
        org_purchase_order_type_repo.delete_org_purchase_order_type(
            user_id=self.get_user_id(), purchase_order_type_id=purchase_order_type_id
        )
        return Response(status=HTTP_200_OK)
