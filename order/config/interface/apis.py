from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from common.serializers import BaseSerializer, HashIdField
from core.apis import Org<PERSON><PERSON><PERSON><PERSON>
from order.config.domain.exceptions import OrderConfigNotificationException
from order.config.domain.services import (
    IncomingOrderNotificationReceiverService,
    OutgoingOrderNotificationReceiverService,
)
from order.config.interface.serializers import (
    POCUserSerializer,
)


class OutgoingOrderNotificationReceiverListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: POCUserSerializer()},
        operation_id="outgoing_order_notification_receiver_list_api",
        operation_summary="outgoing order receiver list API",
    )
    def get(self, request, *args, **kwargs):
        receivers = OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).get_all_receivers()
        self.set_response_message("user list fetched succesfully.")
        return Response(data=self.serializer_class(receivers, many=True).data, status=HTTP_200_OK)


class DefaultOutgoingOrderNotificationReceiverAddApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        receiver_id = HashIdField()

        class Meta:
            ref_name = "AddReceiverInputSerializer"

    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: POCUserSerializer()},
        operation_id="add_default_vo_notification_receiver_api",
        operation_summary="add default outgoing order receiver API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            receiver = OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).add(
                receiver_id=data.get("receiver_id"), user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user added successfully.")
        return Response(data=self.serializer_class(receiver).data, status=HTTP_201_CREATED)


class DefaultOutgoingOrderNotificationReceiverRemoveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="remove_default_vo_notification_receiver_api",
        operation_summary="remove default outgoing order receiver API",
    )
    @transaction.atomic
    def delete(self, request, poc_id, *args, **kwargs):
        try:
            OutgoingOrderNotificationReceiverService(org_id=self.get_organization_id()).remove(
                poc_id=poc_id, user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user deleted successfully.")
        return Response(status=HTTP_200_OK)


class IncomingOrderNotificationReceiverListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: POCUserSerializer()},
        operation_id="incoming_order_notification_receiver_list_api",
        operation_summary="incoming order receiver list API",
    )
    def get(self, request, *args, **kwargs):
        receivers = IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).get_all_receivers()
        self.set_response_message("user list fetched succesfully.")
        return Response(data=self.serializer_class(receivers, many=True).data, status=HTTP_200_OK)


class DefaultIncomingOrderNotificationReceiverAddApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        receiver_id = HashIdField()

        class Meta:
            ref_name = "AddReceiverInputSerializer"

    serializer_class = POCUserSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: POCUserSerializer()},
        operation_id="add_default_co_notification_receiver_api",
        operation_summary="add default incoming order receiver API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            receiver = IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).add(
                receiver_id=data.get("receiver_id"), user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user added successfully.")
        return Response(data=self.serializer_class(receiver).data, status=HTTP_201_CREATED)


class DefaultIncomingOrderNotificationReceiverRemoveApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="remove_default_co_notification_receiver_api",
        operation_summary="remove default incoming order receiver API",
    )
    @transaction.atomic
    def delete(self, request, poc_id, *args, **kwargs):
        try:
            IncomingOrderNotificationReceiverService(org_id=self.get_organization_id()).remove(
                poc_id=poc_id, user_id=request.user.pk
            )
        except OrderConfigNotificationException as e:
            self.set_response_message(e.message)
            raise OrderConfigNotificationException(e)
        self.set_response_message("user deleted successfully.")
        return Response(status=HTTP_200_OK)
