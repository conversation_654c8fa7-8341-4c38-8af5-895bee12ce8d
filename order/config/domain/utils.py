from microcontext.domain.constants import MicroContext
from order.data.models import OrganizationPurchaseOrderType
from order.domain.constants import OrderType<PERSON>num


def get_order_context_for_approval_request(org_purchase_order_type: OrganizationPurchaseOrderType):
    if org_purchase_order_type.name == OrderTypeEnum.INSTA_ORDER.value:
        return MicroContext.INSTA_ORDER.value
    else:
        return MicroContext.ORDER.value
