import abc

from order.config.data.entities import PurchaseOrderTypeDataEntity


class OrganizationPurchaseOrderTypeAbstractRepo:
    @abc.abstractmethod
    def get_org_purchase_order_types(self) -> list[PurchaseOrderTypeDataEntity]: ...

    @abc.abstractmethod
    def get_active_purchase_order_types(self) -> list[PurchaseOrderTypeDataEntity]: ...

    @abc.abstractmethod
    def create_purchase_order_type(self, name: str, user_id: int) -> PurchaseOrderTypeDataEntity: ...

    @abc.abstractmethod
    def update_purchase_order_type(
        self, purchase_order_type_id: int, is_active: bool, is_default: bool, user_id: int
    ) -> None: ...

    @abc.abstractmethod
    def delete_purchase_order_type(self, purchase_order_type_id: int, user_id: int) -> None: ...
