from django.utils import timezone

from order.config.data.entities import PurchaseOrderTypeDataEntity
from order.config.data.selectors import get_active_purchase_order_types_qs, get_org_purchase_order_types_qs
from order.config.domain.abstract_repos import (
    OrganizationPurchaseOrderTypeAbstractRepo,
)
from order.data.models import (
    OrganizationPurchaseOrderType,
)


class OrganizationPurchaseOrderTypeRepo(OrganizationPurchaseOrderTypeAbstractRepo):
    def __init__(self, org_id: int) -> None:
        self.org_id = org_id

    def get_org_purchase_order_types(self) -> list[PurchaseOrderTypeDataEntity]:
        org_purchase_order_types_qs = get_org_purchase_order_types_qs(org_id=self.org_id)
        org_purchase_order_types = []
        for purchase_order_type in org_purchase_order_types_qs:
            org_purchase_order_types.append(
                PurchaseOrderTypeDataEntity(
                    id=purchase_order_type.id,
                    name=purchase_order_type.name,
                    is_default=purchase_order_type.is_default,
                    is_active=purchase_order_type.is_active,
                    is_editable=purchase_order_type.is_editable,
                )
            )
        return org_purchase_order_types

    def get_active_purchase_order_types(self) -> list[PurchaseOrderTypeDataEntity]:
        active_purchase_order_type_qs = get_active_purchase_order_types_qs(org_id=self.org_id)
        active_purchase_order_type = []
        for purchase_order_type in active_purchase_order_type_qs:
            active_purchase_order_type.append(
                PurchaseOrderTypeDataEntity(
                    id=purchase_order_type.id,
                    name=purchase_order_type.name,
                    is_default=purchase_order_type.is_default,
                    is_active=purchase_order_type.is_active,
                    is_editable=purchase_order_type.is_editable,
                )
            )
        return active_purchase_order_type

    def create_org_purchase_order_type(self, name: str, user_id: int) -> list[PurchaseOrderTypeDataEntity]:
        purchase_order_type = OrganizationPurchaseOrderType.objects.create(
            organization_id=self.org_id,
            name=name,
            is_default=False,
            is_active=False,
            created_by_id=user_id,
        )
        return PurchaseOrderTypeDataEntity(
            id=purchase_order_type.id,
            name=purchase_order_type.name,
            is_default=purchase_order_type.is_default,
            is_active=purchase_order_type.is_active,
            is_editable=purchase_order_type.is_editable,
        )

    def update_org_purchase_order_type(
        self, is_active: bool, is_default: bool, purchase_order_type_id: int, user_id: int
    ):
        if is_default:
            OrganizationPurchaseOrderType.objects.filter(organization_id=self.org_id, is_default=True).update(
                is_default=False, updated_by_id=user_id, updated_at=timezone.now()
            )
        OrganizationPurchaseOrderType.objects.filter(id=purchase_order_type_id).update(
            is_default=is_default, is_active=is_active, updated_by_id=user_id, updated_at=timezone.now()
        )

    def delete_org_purchase_order_type(self, purchase_order_type_id: int, user_id: int):
        OrganizationPurchaseOrderType.objects.filter(id=purchase_order_type_id).soft_delete(user_id=user_id)
