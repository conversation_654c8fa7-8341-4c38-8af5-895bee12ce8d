from typing import Union

from core.models import User
from order.data.models import OrganizationIncomingOrderNotificationPoc, OrganizationOutgoingOrderNotificationPoc


def check_for_config_model(config_model):
    if config_model not in [OrganizationIncomingOrderNotificationPoc, OrganizationOutgoingOrderNotificationPoc]:
        raise Exception("Incorrect model defined")


def order_receivers_fetch(
    org_id: int,
    config_model: Union[OrganizationIncomingOrderNotificationPoc, OrganizationOutgoingOrderNotificationPoc],
):
    check_for_config_model(config_model)
    return User.objects.filter(
        id__in=config_model.objects.filter(organization_id=org_id).available().values_list("poc_id", flat=True),
        deleted_at__isnull=True,
    )


def receiver_poc_fetch_by_config_id(
    config_id: int,
    config_model: Union[OrganizationIncomingOrderNotificationPoc, OrganizationOutgoingOrderNotificationPoc],
):
    check_for_config_model(config_model)
    return User.objects.filter(id=config_model.objects.filter(id=config_id).available().first().poc_id).first()


def receiver_poc_config_fetch_by_poc_id(
    poc_id: int,
    config_model: Union[OrganizationIncomingOrderNotificationPoc, OrganizationOutgoingOrderNotificationPoc],
):
    check_for_config_model(config_model)
    return config_model.objects.filter(poc_id=poc_id).available().first()
