# Generated by Django 3.2.15 on 2025-07-02 06:07

from django.db import migrations
from order.data.models import OrganizationPurchaseOrderType


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0078_auto_20250708_1027'),
    ]

    operations = [
        migrations.RunSQL(
            f'CREATE UNIQUE INDEX "{OrganizationPurchaseOrderType.CUSTOM_INDEX}" '
            f'ON "organization_po_types" ("organization_id", (LOWER("name"))) '
            f'WHERE "deleted_at" IS NULL;',
            reverse_sql=f'DROP INDEX "{OrganizationPurchaseOrderType.CUSTOM_INDEX}";',
        ),
    ]
