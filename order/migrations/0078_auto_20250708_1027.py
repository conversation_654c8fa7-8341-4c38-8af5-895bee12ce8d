# Generated by Django 3.2.15 on 2025-07-08 10:27

import common.mixins
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0175_auto_20250708_1027'),
        ('order', '0077_auto_20250528_1801'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationPurchaseOrderType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('is_default', models.<PERSON>oleanField(default=False)),
                ('is_editable', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='order_organizationpurchaseordertype_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='order_organizationpurchaseordertype_deleted', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='purchase_order_types', to='core.organization')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='order_organizationpurchaseordertype_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'organization_po_types',
            },
            bases=(models.Model, common.mixins.BaseModelMixin, common.mixins.TableNameMixin, common.mixins.SoftDeleteMixin),
        ),
        migrations.AddField(
            model_name='vendororder',
            name='purchase_order_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to='order.organizationpurchaseordertype'),
        ),
        migrations.AddConstraint(
            model_name='organizationpurchaseordertype',
            constraint=models.UniqueConstraint(condition=models.Q(('deleted_at__isnull', True), ('is_editable', False)), fields=('organization', 'is_editable'), name='unique_org_non_editable'),
        ),
    ]
