from approval_request.approval_hierarchy.domain.enums import OrderTypeConditionEnum, PaymentTermConditionEnum
from common.intefaces import ContextValueProviderAbstractInterface
from core.order_type_config.data.selectors import get_active_purchase_order_types_qs
from order.data.models import Vendor<PERSON>rder
from order.domain.constants import OrderTypeEnum
from order.domain.entities.domain_entities import OrderPaymentTermValueProviderData
from payment_request.data.selectors import payment_request_get


class OrderPaymentTermsValueProvider(ContextValueProviderAbstractInterface):
    @staticmethod
    def get_value(data: OrderPaymentTermValueProviderData) -> int:
        order = VendorOrder.objects.select_related("payment_term").filter(id=data.order_id).first()
        if not order:
            return PaymentTermConditionEnum.NO_PAYMENT_TERM.value[0]
        elif order.payment_term_id and order.payment_term.is_active:
            return order.payment_term_id
        elif order.payment_term_id and not order.payment_term.is_active:
            return PaymentTermConditionEnum.INACTIVE_PAYMENT_TERM.value[0]
        elif order.payment_term_text:
            return PaymentTermConditionEnum.CUSTOM_PAYMENT_TERM.value[0]
        return PaymentTermConditionEnum.NO_PAYMENT_TERM.value[0]


class OrderTypeValueProvider(ContextValueProviderAbstractInterface):
    @staticmethod
    def get_value(data: OrderPaymentTermValueProviderData) -> int:
        if hasattr(data, "order_id"):
            order = VendorOrder.objects.filter(id=data.order_id).first()
            active_purchase_order_types = get_active_purchase_order_types_qs(org_id=order.origin_org_id)
        else:
            # resource is payment_request
            payment_request = payment_request_get(payment_request_id=data.context_id)
            if payment_request.purchase_order:
                order = payment_request.purchase_order.vendor_order
            else:
                order = payment_request.invoice.order if payment_request.invoice.order else None

        if not order:
            return OrderTypeConditionEnum.NO_ORDER.value[0]
        # TODO: Order Type
        for purchase_order_type in active_purchase_order_types:
            if purchase_order_type.id == order.purchase_order_type_id:
                return purchase_order_type.id
        # Default Case, which will be represeted by 0 primary key
        return 0
        # if order.order_type == OrderTypeEnum.INSTA_ORDER.value:
        #     return OrderTypeConditionEnum.INSTA_ORDER.value[0]
        # return OrderTypeConditionEnum.REGULAR.value[0]


class OrderAmountValueProvider(ContextValueProviderAbstractInterface):
    @staticmethod
    def get_value(data: OrderPaymentTermValueProviderData) -> int:
        order = (
            VendorOrder.objects.annotate_elements_final_amount()
            .annotate_deduction_amount()
            .annotate_deduction_tax_amount()
            .filter(id=data.order_id)
            .first()
        )
        return (
            order.elements_final_amount
            - order.deduction_amount
            + order.elements_tax_amount
            - order.deduction_tax_amount
        )
