import abc
from typing import List

from order.domain.entities import PurchaseOrderTypeDataEntity


class PurchaseOrderTypeAbstractService(abc.ABC):
    """Abstract service for purchase order type operations."""

    @abc.abstractmethod
    def get_org_purchase_order_types(self, org_id: int) -> List[PurchaseOrderTypeDataEntity]:
        """Get purchase order types for an organization."""
        pass

    @abc.abstractmethod
    def get_purchase_order_types(self, org_id: int) -> List[PurchaseOrderTypeDataEntity]:
        """Get active purchase order types for an organization."""
        pass

    @abc.abstractmethod
    def create_purchase_order_type(
        self, org_id: int, name: str, is_default: bool, is_active: bool, user_id: int
    ) -> PurchaseOrderTypeDataEntity:
        """Create a new purchase order type."""
        pass

    @abc.abstractmethod
    def update_purchase_order_type(self, purchase_order_type_id: int, is_default: bool, is_active: bool) -> None:
        """Update an existing purchase order type."""
        pass

    @abc.abstractmethod
    def delete_purchase_order_type(self, purchase_order_type_id: int) -> None:
        """Delete a purchase order type."""
        pass
