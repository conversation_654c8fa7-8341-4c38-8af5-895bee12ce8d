from typing import Dict, List, Optional, Tuple

import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>ime<PERSON><PERSON>,
    Decimal<PERSON>ield,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    IntegerField,
    Max,
    <PERSON>,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, JSONObject, Lower
from django.utils import timezone
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _

from approval_request.data.choices import ApprovalRequestStatusChoices
from boq.data.models import BoqElement, BoqElementOrderAmountView, BoqElementV2
from boq.data.querysets import BoqElementQuerySet
from boq.data.selectors import boq_elements_fetch
from common.choices import OrderType, VendorStatusChoices
from common.constants import CustomFieldTypeChoices
from common.timeline.service import TimelineStatusService
from common.types import DjangoModelType
from common.utils import get_today_starting_datetime
from core.models import (
    Organization,
    OrganizationAddress,
    OrganizationGSTNumber,
    OrganizationLegalEntity,
    OrganizationUser,
    State,
    User,
)
from core.organization.domain.entities import (
    OrganizationCountryConfigData,
    OrganizationDocumentData,
    OrganizationSectionData,
    StateData,
)
from core.organization.domain.services import prepare_nested_data_for_section_config
from core.organization.models import OrganizationDocumentFieldContextConfig
from element.data.models import ElementCategory, ElementItemType
from element.data.selectors import element_categories_fetch_all, element_item_type_fetch_all
from microcontext.choices import MicroContextChoices
from order.data.choices import OrderStatusChoices
from order.data.entities import OrderCompletionPercentageEntity, OrderHeaderDetailEntity
from order.data.models import (
    Deduction,
    DeductionAttachment,
    DeductionRemark,
    DeductionType,
    OrderElementGuidelineAttachmentSnapshot,
    OrderElementGuidelineSnapshot,
    OrderElementPreviewFileSnapshot,
    OrderElementProductionDrawingSnapshot,
    OrderElementSnapshot,
    OrderRequestMapping,
    OrderSnapshot,
    OrderTextFieldData,
    VendorOrder,
    VendorOrderElement,
    VendorOrderElementGuideline,
    VendorOrderElementGuidelineAttachment,
    VendorOrderElementPreviewFile,
    VendorOrderElementProductionDrawing,
    VendorOrderEmail,
    VendorOrderFieldHistory,
    VendorPurchaseOrder,
)
from order.domain.constants import OrderEventEnum, OrderStatusEnum, VendorListFormChoices
from order.domain.exceptions import OrderNotFoundException
from order.domain.status_choices import OrderStatus, POStatus
from order.invoice.data.models import Invoice
from order.invoice.data.selectors import invoice_fetch_all
from progressreport.services.progress_percentage import get_today_and_total_progress
from project.data.models import Project
from project.domain.services import get_project_proposal_client
from ratecontract.data.choices import RateContractStatusType
from ratecontract.data.models import RateContract, RateContractElement
from ratecontract.data.querysets import RateContractQuerySet
from ratecontract.data.selectors import order_rate_contract_list_fetch
from rollingbanners.comment_base_service import CommentBaseService
from vendor.data.models import Vendor
from vendorv2.data.models import ClientVendorMapping
from vendorv2.domain.services.vendor import get_vendor_onboard_config
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractService

logger = structlog.get_logger(__name__)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


def purchase_order_max_version(*, vendor_order_id: int) -> List[VendorPurchaseOrder]:
    purchase_orders = (
        VendorPurchaseOrder.objects.filter(vendor_order_id=vendor_order_id)
        .all()
        .order_by("-version", "-uploaded_at")
        .annotate_pending_and_approved_payment_request_amount()
    )
    latest_purchase_order_subquery = Subquery(
        VendorPurchaseOrder.objects.filter(vendor_order_id=OuterRef("vendor_order_id"), po_number=OuterRef("po_number"))
        .order_by("-uploaded_at")
        .values("id")[:1]
    )

    return (
        purchase_orders.annotate(latest_purchase_order_id=latest_purchase_order_subquery)
        .filter(id=F("latest_purchase_order_id"))
        .order_by("id")
    )


def get_shipping_address(store_address: str, org_id_list: List[int]):
    addresses = OrganizationAddress.objects.filter(organization_id__in=org_id_list).select_related(
        "state", "country", "city"
    )
    return [
        {"header": "Project Location", "address": store_address},
    ] + [
        {"header": address.header if address.header else "Location", "address": address.full_address}
        for address in addresses
    ]


def get_organization_legal_entities(organizations: List[int]) -> List[Dict]:
    orgs: List[Dict] = (
        Organization.objects.filter(id__in=organizations)
        .annotate(org_id=F("legal_entities__organization_id"), legal_name=F("legal_entities__name"))
        .values("legal_name", "org_id", "id", "name")
    )
    return [
        (
            {"name": org["legal_name"], "organization_id": org["org_id"]}
            if org["legal_name"]
            else {"name": org["name"], "organization_id": org["id"]}
        )
        for org in orgs
    ]


def incoming_order_create_prepare_data(*, project_id: int, user_id: int, self_organization_id: int):
    project: Project = Project.objects.filter(id=project_id).select_related("store").first()
    if not project:
        raise ValidationError("Project not found")
    project_organizations = list(
        project.organizations.all().exclude(id=self_organization_id).values_list("id", flat=True)
    )
    user: User = User.objects.filter(id=user_id).first()
    if not user:
        raise ValidationError("User not found")

    custom_address = list(
        VendorOrder.objects.filter(project_id=project_id, shipping_address_header="Custom")
        .distinct()
        .annotate(header=F("shipping_address_header"), address=F("shipping_address"))
        .values("header", "address")
    )
    client = get_project_proposal_client(project_id=project_id, organization_id=self_organization_id)
    organization_legal_entities = OrganizationLegalEntity.objects.filter(organization_id=client.id).values(
        "organization_id", "name"
    )
    if len(organization_legal_entities) == 0:
        organization_legal_entities = [{"organization_id": client.id, "name": client.name}]
    return {
        "user": user,
        "work_order_from": organization_legal_entities,
        "job_id": project.job_id,
        "shipping_address": get_shipping_address(
            store_address=project.store.address_line_1, org_id_list=project_organizations
        )
        + custom_address,
        "order_created_on": timezone.now(),
        "vendor": Vendor.objects.filter(organization_id=self_organization_id).first(),
    }


def outgoing_order_create_prepare_order_data(*, project_id: int, user_id: int, self_organization_id: int):
    project: Project = Project.objects.filter(id=project_id).select_related("store").first()
    user: User = User.objects.filter(id=user_id).first()
    if not user:
        raise ValidationError("User not found")

    if not project:
        raise ValidationError("Project not found")

    organization_legal_entities = OrganizationLegalEntity.objects.filter(organization_id=self_organization_id).values(
        "organization_id", "name"
    )

    custom_address = list(
        VendorOrder.objects.filter(project_id=project_id, shipping_address_header="Custom")
        .distinct()
        .annotate(header=F("shipping_address_header"), address=F("shipping_address"))
        .values("header", "address")
    )

    if len(organization_legal_entities) == 0:
        organization_legal_entities = (
            Organization.objects.filter(
                id__in=[
                    self_organization_id,
                ]
            )
            .annotate(organization_id=F("id"))
            .values("organization_id", "name")
        )

    return {
        "user": user,
        "work_order_from": organization_legal_entities,
        "job_id": project.job_id,
        "shipping_address": get_shipping_address(
            store_address=project.store.full_address, org_id_list=[project.client_id, self_organization_id]
        )
        + custom_address,
        "order_created_on": timezone.now(),
    }


def fetch_basic_order_list(*, project_id: int, org_id: int):
    return (
        VendorOrder.objects.filter(project_id=project_id)
        .order_by("created_at")
        .filter(~Q(org_to_id=org_id), origin_org_id=org_id)
        .all()
    )


def order_list(*, project_id: int, org_id: int):
    orders = (
        fetch_basic_order_list(project_id=project_id, org_id=org_id)
        .annotate_elements_final_amount()
        .annotate_full_order_number()
        .annotate_deduction_amount()
        .annotate_deduction_tax_amount()
        .annotate_payment_request_pending_and_approved_amount()
        .annotate_is_snapshot_available_for_vendor()
        .select_related(
            "updated_by", "issued_by", "org_to", "org_from", "origin_org", "project", "org_to__vendor", "created_by"
        )
        .prefetch_related("org_to__vendor__category")
        .prefetch_related(
            Prefetch(
                "order_elements",
                queryset=VendorOrderElement.available_objects.all()
                .select_related("linked_element", "boq_element", "linked_element__work_progress_element")
                .annotate(progress_percentage=Coalesce(F("linked_element__progress_percentage"), None))
                .annotate(
                    total_amount=Sum(
                        F("linked_element__quantity") * F("linked_element__client_rate")
                        - F("linked_element__quantity")
                        * F("linked_element__client_rate")
                        * F("linked_element__discount_percent")
                        / 100,
                        filter=Q(linked_element__isnull=False),
                    )
                ),  # TODO: remove this query from here and add another selector
            ),
            "order_elements__item_type",
            "order_elements__category",
            Prefetch(
                "purchase_orders",
                queryset=VendorPurchaseOrder.objects.select_related("cancelled_by", "uploaded_by")
                .annotate_total_payment_request_amount()
                .exclude(status=POStatus.PO_CANCELLED)
                .all(),
            ),
        )
        .annotate(
            cancelled_elements_count=Count(
                "order_elements",
                distinct=True,
                filter=Q(order_elements__status=OrderStatus.CANCELLED, order_elements__deleted_at__isnull=True),
            ),
            not_cancelled_elements_count=Count(
                "order_elements",
                distinct=True,
                filter=Q(
                    order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                    order_elements__deleted_at__isnull=True,
                ),
            ),
            closed_at=Subquery(
                VendorOrder.objects.filter(id=OuterRef("id"))
                .annotate(
                    closed_at=Max(
                        "status_history__created_at",
                        filter=Q(status_history__outgoing_status=VendorOrder.OutgoingStatus.CLOSED),
                    )
                )
                .values("closed_at")[:1]
            ),
            order_amount=Coalesce(
                F("elements_final_amount") - F("deduction_amount"),
                Value(0),
                output_field=DecimalField(),
            ),
            order_tax_amount=Coalesce(
                F("elements_tax_amount") - F("deduction_tax_amount"),
                Value(0),
                output_field=DecimalField(),
            ),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER.name, org_id=org_id
            )
        )
        .annotate_is_vendor_active()
        .all()
    )
    return orders


def external_order_list_fetch(project_id: int, org_id: int):
    return fetch_basic_order_list(project_id=project_id, org_id=org_id)


def order_by_job_id_and_order_number_fetch(job_id: str, order_number: int):
    return VendorOrder.objects.filter(project__job_id=job_id, order_number=order_number).first()


def vendor_order_element_item_type_fetch_all():
    return ElementItemType.objects.all()


def vendor_order_precreate_order_number_fetch(project_id: int):
    project = Project.objects.filter(id=project_id).first()
    if not project:
        raise ValidationError({"project_id": "Project does not exist"})
    last_order = VendorOrder.all_objects.filter(project_id=project_id).order_by("-order_number").first()
    if not last_order:
        return {"order_number": f"{project.job_id}/{1}"}
    return {"order_number": f"{project.job_id}/{last_order.order_number + 1}"}


def order_element_list(elements: List):
    return VendorOrderElement.available_objects.filter(id__in=elements)


def order_element_list_using_vendor_order(vendor_order_id: int, project_id: int):
    return (
        VendorOrderElement.available_objects.filter(project_id=project_id, vendor_order_id=vendor_order_id)
        .all()
        .order_by("created_at")
    )


def vendor_order_fetch(vendor_order_id: int) -> QuerySet:
    return VendorOrder.objects.filter(id=vendor_order_id).annotate(
        elements_count=Count(
            "order_elements",
            filter=Q(
                order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                order_elements__deleted_at__isnull=True,
            ),
        )
    )


def vendor_order_fetch_via_snapshot(*, snapshot_id: int) -> VendorOrder:
    return OrderSnapshot.objects.filter(id=snapshot_id).first().order


def vendor_order_element_details(element_id: int, org_id: int):
    return (
        VendorOrderElement.available_objects.filter(id=element_id)
        .annotate(preview_file_count=Count("preview_files"))
        .prefetch_related(
            Prefetch(
                "preview_files",
                queryset=VendorOrderElementPreviewFile.objects.available(),
            ),
            Prefetch(
                "production_drawings",
                queryset=VendorOrderElementProductionDrawing.objects.prefetch_related("tags").available(),
            ),
            Prefetch("guidelines", queryset=VendorOrderElementGuideline.objects.available()),
            Prefetch("guidelines__attachments", queryset=VendorOrderElementGuidelineAttachment.objects.available()),
        )
        .annotate_comment_count(org_id=org_id)
        .first()
    )


def order_element_snapshot_details(element_id: int, org_id: int):
    return (
        OrderElementSnapshot.available_objects.filter(id=element_id)
        .annotate(preview_file_count=Count("preview_files"))
        .prefetch_related(
            Prefetch(
                "preview_files",
                queryset=OrderElementPreviewFileSnapshot.objects.available(),
            ),
            Prefetch(
                "production_drawings",
                queryset=OrderElementProductionDrawingSnapshot.objects.prefetch_related("tags").available(),
            ),
            Prefetch("guidelines", queryset=OrderElementGuidelineSnapshot.objects.available()),
            Prefetch("guidelines__attachments", queryset=OrderElementGuidelineAttachmentSnapshot.objects.available()),
        )
        .annotate_comment_count(org_id=org_id)
        .first()
    )


def client_order_element_details(order_id: int, element_id: int, org_id: int):
    vendor_order = VendorOrder.objects.filter(id=order_id).annotate_last_approved_snapshot()

    if (
        vendor_order
        and vendor_order[0].outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order[0].last_approved_snapshot
    ):
        return order_element_snapshot_details(
            element_id=element_id,
            org_id=org_id,
        )
    else:
        return vendor_order_element_details(element_id=element_id, org_id=org_id)


def order_element_guidelines(element_id: int):
    return VendorOrderElementGuideline.objects.filter(element_id=element_id).all()


def get_boq_element_version(project_id: int, boq_element_id_list: List):
    element_with_version = {}
    for element_id in boq_element_id_list:
        boq_element_version = VendorOrderElement.objects.filter(
            boq_element_id=element_id, project_id=project_id
        ).values_list("boq_element_version", flat=True)
        if boq_element_version:
            element_with_version[element_id] = max(list(boq_element_version)) + 1
        else:
            element_with_version[element_id] = 1
    return element_with_version


def get_client(project_id: int):
    project = Project.objects.filter(id=project_id).first()
    if not project:
        raise ValidationError({"project_id": _("Project Doesn't exist")})
    return project.client_id


def vendor_order_element_list_with_single_preview_file(
    elements: List,
):
    return (
        VendorOrderElement.available_objects.filter(
            id__in=elements,
        )
        .select_related("item_type")
        .prefetch_related(
            Prefetch(
                "preview_files",
                queryset=VendorOrderElementPreviewFile.objects.filter(
                    is_main=True, deleted_at__isnull=True, type="IMAGE"
                ).order_by("-is_main"),
            )
        )
        .all()
    )


def get_order_number(project_id: int, vendor_order: DjangoModelType) -> str:
    project = Project.objects.filter(id=project_id).first()
    if not project:
        raise ValidationError({"project_id": "Project Doesn't exists"})
    return f"{project.job_id}/{vendor_order.order_number}"


def get_vendor_name(vendor_id: int) -> str:
    return Organization.objects.filter(id=vendor_id).values_list("name", flat=True)[0]


def get_organizations_users_emails(organization_id: int) -> List[str]:
    return OrganizationUser.objects.filter(organization_id=organization_id, is_active=True).values_list(
        "user__email", flat=True
    )


def order_sent_info_fetch(vendor_order_id: int, project_id: int):
    vendor_order: VendorOrder = VendorOrder.objects.filter(id=vendor_order_id).first()
    if not vendor_order:
        raise ValidationError({"vendor_order_id": _("Vendor Order doesn't exists")})
    if vendor_order.org_to_id:
        vendor_id = vendor_order.org_to_id

    vendor_name = get_vendor_name(vendor_id=vendor_id)
    order_number = get_order_number(project_id=project_id, vendor_order=vendor_order)

    return order_number, vendor_name, vendor_order.work_order_from, vendor_order


def order_details_for_email(vendor_order_id: int) -> VendorOrder:
    vendor_order = (
        VendorOrder.objects.annotate_elements_final_amount()
        .filter(id=vendor_order_id)
        .select_related("created_by", "org_from")
        .annotate(
            elements_count=Count("order_elements", filter=Q(order_elements__deleted_at__isnull=True)),
            deduction_amount=Coalesce(
                Sum("deductions__amount", distinct=True, filter=Q(deductions__deleted_at__isnull=True)),
                Value(0),
                output_field=DecimalField(),
            ),
            request_id=Subquery(
                OrderRequestMapping.objects.filter(resource_id=OuterRef("id"), event=OrderEventEnum.DRAFT.value)
                .order_by("-created_at")
                .values("request_id")[:1]
            ),
        )
        .first()
    )
    amount = vendor_order.elements_final_amount
    # amount = sum(
    #     [
    #         rate * quantity
    #         for rate, quantity in VendorOrderElement.available_objects.filter(
    #             vendor_order_id=vendor_order_id
    #         ).values_list("vendor_rate", "quantity")
    #     ]
    # )
    if vendor_order.deduction_amount:
        amount = vendor_order.elements_final_amount - vendor_order.deduction_amount
    setattr(vendor_order, "amount", amount)
    return vendor_order


def get_email_data(vendor_order_id: int) -> VendorOrderEmail:
    return VendorOrderEmail.objects.filter(vendor_order_id=vendor_order_id).first()


def order_elements_fetch(*, vendor_order_id: int, org_id: int):
    return (
        VendorOrderElement.available_objects.annotate_elements_final_amount()
        .filter(vendor_order_id=vendor_order_id)
        .all()
        .select_related("item_type", "category", "linked_element")
        .annotate(
            progress_percentage=Coalesce(F("linked_element__progress_percentage"), 0, output_field=DecimalField())
        )
        .annotate_preview_file_url()
        .annotate_comment_count(org_id=org_id)
    )


def order_purchase_orders_fetch(*, vendor_order_id: int) -> QuerySet:
    return (
        VendorPurchaseOrder.objects.filter(vendor_order_id=vendor_order_id)
        .exclude(cancelled_at__isnull=False)
        .all()
        .order_by("uploaded_at")
    )


# def get_outgoing_org_list(*, org_from_id: int):
#     org_mappings_queryset = FromToOrgMapping.objects.filter(org_from_id=org_from_id)
#     .values_list("org_to_id", flat=True)
#     return Vendor.objects.filter(organization_id__in=org_mappings_queryset, is_active=True).all()


def get_outgoing_org_list(*, org_from_id: int):
    org_mappings_queryset = ClientVendorMapping.objects.filter(org_from_id=org_from_id).values_list(
        "org_to_id", flat=True
    )
    return Vendor.objects.filter(organization_id__in=org_mappings_queryset).all()


def get_grouped_elements(*, vendor_order_id: int):
    return (
        VendorOrderElement.available_objects.filter(
            vendor_order_id=vendor_order_id,
            status__in=[OrderStatus.COMPLETED, OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
        )
        .values("category__name")
        .annotate(Amount=Sum((F("quantity") * F("vendor_rate"))), Category=F("category__name"))
        .values("Category", "Amount")
    )


def get_grouped_elements_v2(*, order_elements: List[VendorOrderElement]) -> List[Dict]:
    category_wise_amount_grouping = {}
    category_mapping = ElementCategory.objects.in_bulk()
    for order_element in order_elements:
        category_name = category_mapping[order_element.category_id].name
        if category_name in category_wise_amount_grouping:
            category_wise_amount_grouping[category_name] += order_element.quantity * order_element.vendor_rate
        else:
            category_wise_amount_grouping[category_name] = order_element.quantity * order_element.vendor_rate
    category_wise_amount_group_list = [
        {"Category": category, "Amount": amount} for category, amount in category_wise_amount_grouping.items()
    ]
    return category_wise_amount_group_list


def get_orders_from_vendor_id(vendor_id: int):
    return VendorOrder.objects.filter(org_to_id=vendor_id)


def get_last_snapshot_created_at(vendor_order_id: int):
    order_snapshot = OrderSnapshot.objects.filter(order_id=vendor_order_id).order_by("-created_at").first()
    if order_snapshot:
        return order_snapshot.created_at
    return None


def get_order_detail(*, order_id: int, org_id: int) -> VendorOrder:
    preview_file_subquery = (
        VendorOrderElementPreviewFile.objects.filter(element_id=OuterRef("pk"), is_main=True)
        .available()
        .order_by("-is_main", "uploaded_at")
    )

    """
    order_request_mappings_subquery ->  This subquery is used to get the latest request_id and task_id for the order.
    order_request_mappings_subquery_excluding_rejected_status
        -> In case of Reject, we are updating the order status to the last approved state ,
        -> Similarly for the order request mapping we need to get the last approved state request_id and task_id.

    """

    order_request_mappings_subquery = (
        OrderRequestMapping.objects.filter(resource_id=OuterRef("pk"))
        .annotate(request_id_and_task_id=JSONObject(request_id="request_id", task_id="task_id"))
        .order_by("-created_at")
    )
    order_request_mappings_subquery_excluding_rejected_status = (
        OrderRequestMapping.objects.filter(resource_id=OuterRef("pk"))
        .exclude(request__status=ApprovalRequestStatusChoices.REJECTED.value)
        .annotate(request_id_and_task_id=JSONObject(request_id="request_id", task_id="task_id"))
        .order_by("-created_at")
    )
    vendor_order_amount_subquery = BoqElementOrderAmountView.objects.filter(
        id=OuterRef("boq_element_id"), org_from_id=OuterRef("boq_element__organization_id")
    )

    boq_element_final_amount_subquery = BoqElementV2.objects.filter(
        id=OuterRef("boq_element_id")
    ).annotate_final_amount_v2()

    boq_final_element_amount_subquery = Subquery(boq_element_final_amount_subquery.values("final_element_amount")[:1])
    total_element_amount_subquery = Subquery(vendor_order_amount_subquery.values("total_amount")[:1])
    draft_amount_subquery = Subquery(vendor_order_amount_subquery.values("draft_amount")[:1])
    total_quantity_subquery = Subquery(vendor_order_amount_subquery.values("total_quantity")[:1])
    max_sent_order_rate_subquery = Subquery(vendor_order_amount_subquery.values("max_sent_order_rate")[:1])
    max_draft_order_rate_subquery = Subquery(vendor_order_amount_subquery.values("max_draft_order_rate")[:1])

    vendor_order: VendorOrder = (
        VendorOrder.objects.annotate_elements_final_amount()
        .filter(id=order_id)
        .annotate_elements_final_amount()
        .annotate_deduction_amount()
        .annotate_deduction_tax_amount()
        .select_related(
            "project",
            "project__store",
            "updated_by",
            "issued_by",
            "org_to",
            "org_from",
            "created_by",
            "origin_org",
            # "gst_details",
        )
        .prefetch_related(Prefetch("rate_contract", RateContract.objects.available().annotate_element_count()))
        .annotate_is_snapshot_available_for_vendor()
        .annotate(
            order_request_mapping=Case(
                When(
                    is_snapshot_available=True,
                    then=Subquery(
                        order_request_mappings_subquery_excluding_rejected_status.values("request_id_and_task_id")[:1]
                    ),
                ),
                default=Subquery(order_request_mappings_subquery.values("request_id_and_task_id")[:1]),
            ),
            current_event=Subquery(
                OrderRequestMapping.objects.filter(
                    resource_id=OuterRef("id"),
                    event__in=[
                        OrderEventEnum.SENT_FOR_APPROVAL.value,
                        OrderEventEnum.MODIFICATION_FOR_APPROVAL.value,
                    ],
                )
                .order_by("-created_at")
                .values("event")[:1]
            ),
            request_id=Subquery(
                OrderRequestMapping.objects.filter(
                    resource_id=OuterRef("id"),
                    event__in=[
                        OrderEventEnum.SENT_FOR_APPROVAL.value,
                        OrderEventEnum.MODIFICATION_FOR_APPROVAL.value,
                    ],
                )
                .order_by("-created_at")
                .values("request_id")[:1]
            ),
            elements_amount_value=Coalesce(
                Sum(
                    F("elements_final_amount"),
                    filter=Q(
                        order_elements__deleted_at__isnull=True,
                        order_elements__status__in=[
                            OrderStatus.PENDING,
                            OrderStatus.SENT,
                            OrderStatus.MODIFIED,
                            OrderStatus.COMPLETED,
                        ],
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
        )
        .annotate(request_id=Cast(F("order_request_mapping__request_id"), output_field=IntegerField()))
        .annotate(task_id=Cast(F("order_request_mapping__task_id"), output_field=IntegerField()))
        .annotate(
            is_invoice_visible=Case(
                When(
                    Q(is_snapshot_available=True)
                    | Q(
                        outgoing_status__in=[
                            OrderStatusEnum.APPROVED.value,
                            OrderStatusEnum.CANCELLED.value,
                            OrderStatusEnum.SENT.value,
                            OrderStatusEnum.REJECTED.value,
                            OrderStatusEnum.CLOSED.value,
                            OrderStatusEnum.COMPLETED.value,
                        ]
                    ),
                    then=Value(True),
                ),
                default=Value(False),
            )
        )
        .annotate_payment_request_pending_and_approved_amount()
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER.name, org_id=org_id
            ),
        )
        .annotate_request_data()
        .annotate(
            order_amount=Coalesce(
                F("elements_final_amount") - F("deduction_amount"),
                Value(0),
                output_field=DecimalField(),
            ),
            order_tax_amount=Coalesce(
                F("elements_tax_amount") - F("deduction_tax_amount"),
                Value(0),
                output_field=DecimalField(),
            ),
        )
        .annotate_last_approved_snapshot()
        .prefetch_related(
            Prefetch(
                "order_elements",
                VendorOrderElement.available_objects.annotate_elements_final_amount()
                .all()
                .select_related(
                    "linked_element",
                    "el_element",
                    "item_type",
                    "category",
                    "boq_element",
                    "linked_element__work_progress_element",
                )
                .annotate(
                    progress_percentage=Coalesce(
                        F("linked_element__work_progress_element__progress_percentage"),
                        Value(0),
                        output_field=DecimalField(),
                    )
                )
                .annotate(
                    comment_count=CommentHelperService.get_count(
                        context_group=CommentHelperService.GROUPED_CONTEXT.ORDER_ITEM.name, org_id=org_id
                    ),
                    preview_file=Subquery(preview_file_subquery.values("file")[:1]),
                )
                .annotate(rate_contract_id=F("vendor_order__rate_contract_id"))
                .annotate(
                    rc_rate=Subquery(
                        RateContractElement.objects.filter(
                            rate_contract_id=OuterRef("rate_contract_id"), element_id=OuterRef("el_element_id")
                        ).values("rate")[:1],
                        output_field=DecimalField(max_digits=11, decimal_places=2),
                    )
                )
                .annotate_is_order_amount_greater_than_boq_amount(
                    total_element_amount_subquery=total_element_amount_subquery,
                    draft_amount_subquery=draft_amount_subquery,
                    boq_final_element_amount_subquery=boq_final_element_amount_subquery,
                )
                .annotate_is_error_found_in_order_element(
                    total_element_amount_subquery=total_element_amount_subquery,
                    total_quantity_subquery=total_quantity_subquery,
                    max_sent_order_rate_subquery=max_sent_order_rate_subquery,
                    max_draft_order_rate_subquery=max_draft_order_rate_subquery,
                    boq_final_element_amount_subquery=boq_final_element_amount_subquery,
                )
                .order_by("created_at"),
            ),
            "attachments",
            Prefetch("deductions", Deduction.objects.select_related("created_by", "updated_by").all()),
            Prefetch("deductions__attachments", DeductionAttachment.objects.select_related("uploaded_by").all()),
            Prefetch("invoices", Invoice.objects.all()),
        )
        .annotate_is_vendor_active()
        .first()
    )
    rc_count = (
        RateContract.objects.active()
        .filter(
            organization_id=org_id,
            client_id__in=[vendor_order.project.client_id, org_id],
        )
        .count()
    )

    sorted_purchase_orders = purchase_order_max_version(vendor_order_id=vendor_order.pk)
    setattr(vendor_order, "sorted_purchase_orders", sorted_purchase_orders)
    setattr(vendor_order, "rc_count", rc_count)
    setattr(vendor_order, "document_config", [])
    if vendor_order.org_to:
        document_config = fetch_order_document_config_with_data(
            country_id=vendor_order.org_to.country_id, order_id=vendor_order.pk
        )
        setattr(vendor_order, "document_config", document_config)

    if vendor_order.org_to and vendor_order.vendor_info_v2:
        vendor_order.vendor_info_v2 = prepare_vendor_info_data_for_order_detail(
            country_id=vendor_order.org_to.country_id, vendor_info=vendor_order.vendor_info_v2, org_id=org_id
        )
    return vendor_order


def prepare_vendor_info_data_for_order_detail(
    country_id: int, vendor_info: dict, org_id: int
) -> list[OrganizationSectionData]:
    logger.info("Preparing vendor info data for order detail", vendor_info=vendor_info)
    vendor_config: OrganizationCountryConfigData = get_vendor_onboard_config(country_id=country_id, org_id=org_id)
    stored_data = vendor_info.get("data", {})
    # stored_sections = stored_data.get("sections", {})

    sections_data = []

    # Traverse through each section in vendor_info
    for section in vendor_config.sections:
        section_id = section.id

        # Create a new OrganizationSectionData object for this section
        section_data = OrganizationSectionData(
            id=section_id, name=section.name, position=section.position, type=section.type, documents=[]
        )

        # Traverse through each document in the section
        for document in section.documents:
            document_id = document.id

            # Create a new OrganizationDocumentData object for this document
            document_data = OrganizationDocumentData(
                id=document_id,
                name=document.name,
                position=document.position,
                fields_data=[],
                fields=document.fields,
                is_required=document.is_required,
                multiple_allowed=document.multiple_allowed,
                is_visible_on_app=document.is_visible_on_app,
            )

            field_id_field_data_mapping = {str(field.id): field for field in document.fields}

            # Check if there is fields_data available for this document in stored_sections
            if str(section_id) in stored_data and str(document_id) in stored_data[str(section_id)]:
                document_fields_data = stored_data[str(section_id)][str(document_id)]

                # Prepare fields_data in the required format
                converted_fields_data = []
                for field_data in document_fields_data:
                    field_representation = {}
                    for field_id, field_info in field_data.items():
                        if field_id == "id":
                            field_representation[field_id] = field_info
                        else:
                            if field_id_field_data_mapping[field_id].type == CustomFieldTypeChoices.STATE:
                                state_id = field_info["data"].get("id")
                                state = State.objects.filter(id=state_id).first()
                                logger.info("State data fetched for order detail vendor info", state_id=state_id)
                                field_representation[field_id] = {
                                    "id": field_info["id"],
                                    "data": StateData(
                                        id=state.pk if state else None, name=state.name if state else None
                                    ),
                                }
                            else:
                                field_representation[field_id] = {"id": field_info["id"], "data": field_info["data"]}
                    converted_fields_data.append(field_representation)

                # Add the converted fields_data to the document_data
                document_data.fields_data = converted_fields_data

            # Append the document_data to the section_data's documents list
            section_data.documents.append(document_data)

        # Append the section_data to the final list of sections
        sections_data.append(section_data)

    logger.info("Vendor info data prepared for order detail", sections_data=sections_data)
    return sections_data


def fetch_order_document_config_base_selector(country_id: int) -> QuerySet[OrganizationDocumentFieldContextConfig]:
    order_config_field = OrganizationDocumentFieldContextConfig.objects.filter(
        context=MicroContextChoices.ORDER, country_id=country_id, is_client_data=False
    ).select_related("field_config", "field_config__document_config", "field_config__document_config__section")
    return order_config_field


def fetch_order_document_config(country_id: int) -> QuerySet[OrganizationDocumentFieldContextConfig]:
    order_config_field = fetch_order_document_config_base_selector(country_id=country_id)
    config: list[OrganizationSectionData] = prepare_nested_data_for_section_config(
        context_config_objects=order_config_field, context=MicroContextChoices.ORDER
    )
    return config


def fetch_order_document_config_with_data(country_id: int, order_id: int) -> list[OrganizationSectionData]:
    order_config_field = fetch_order_document_config_base_selector(country_id=country_id).prefetch_related(
        Prefetch(
            "vendor_order_text_field_data", queryset=OrderTextFieldData.objects.available().filter(order_id=order_id)
        )
    )
    config: list[OrganizationSectionData] = prepare_nested_data_for_section_config(
        context_config_objects=order_config_field, with_data=True, context=MicroContextChoices.ORDER
    )
    return config


def external_order_detail_fetch(org_id: int, project_id: int, order_id: int):
    vendor_order: VendorOrder = (
        external_order_list_fetch(project_id=project_id, org_id=org_id)
        .filter(id=order_id)
        .select_related("poc", "org_to", "org_from", "created_by")
        .prefetch_related(
            Prefetch(
                "org_to__organization_gst", queryset=OrganizationGSTNumber.objects.filter(gst_number__isnull=False)
            )
        )
        .first()
    )
    return vendor_order


def orders_sent_to_fetch_all_using_org_from_id(
    *, org_from_id: int, project_id: int, is_order_element_required: bool = False
) -> list[VendorOrder]:
    prefetch_order_elements = Prefetch(
        "order_elements",
        VendorOrderElement.available_objects.filter(
            status__in=[OrderStatus.COMPLETED, OrderStatus.PENDING, OrderStatus.MODIFIED, OrderStatus.SENT]
        ).select_related("linked_element"),
    )

    orders = (
        VendorOrder.objects.all()
        .annotate_is_snapshot_available_for_client()
        .filter(
            (
                Q(
                    outgoing_status__in=[
                        OrderStatusChoices.SENT.value,
                        OrderStatusChoices.COMPLETED.value,
                        OrderStatusChoices.APPROVED.value,
                    ]
                )
                | Q(
                    outgoing_status__in=[
                        OrderStatusChoices.PENDING.value,
                    ],
                    is_snapshot_available=True,
                )
            ),
            org_from_id=org_from_id,
            project_id=project_id,
            org_to__isnull=False,
        )
        .exclude(org_to_id=org_from_id)
    )

    if is_order_element_required:
        orders.prefetch_related(prefetch_order_elements)
    return orders


def vendors_given_order_fetch_all(*, org_from_id: int, project_id: int) -> list[Organization]:
    org_id_list = (
        orders_sent_to_fetch_all_using_org_from_id(org_from_id=org_from_id, project_id=project_id)
        .distinct("org_to")
        .values_list("org_to", flat=True)
    )
    return Organization.objects.filter(id__in=org_id_list)


def scope_header_details_fetch(
    *, project_id: int, org_from_id: int, vendor_id_list: list[int]
) -> Tuple[dict, list[int]]:
    order_count: int = (
        orders_sent_to_fetch_all_using_org_from_id(
            org_from_id=org_from_id, project_id=project_id, is_order_element_required=False
        )
        .filter(org_to_id__in=vendor_id_list)
        .count()
    )

    return order_count


def item_type_distinct_fetch_using_boq_elements(boq_elements: BoqElementQuerySet) -> list[ElementItemType]:
    item_type = boq_elements.values_list("item_type", flat=True).distinct()
    return element_item_type_fetch_all().filter(id__in=item_type)


def element_categories_distinct_fetch_using_boq_elements(boq_elements: BoqElementQuerySet) -> list[ElementCategory]:
    categories = boq_elements.values_list("category", flat=True).distinct()
    return element_categories_fetch_all().filter(id__in=categories)


def uom_distinct_fetch_using_boq_elements(boq_elements: BoqElementQuerySet) -> list[int]:
    return boq_elements.values_list("uom", flat=True).distinct()


def vendor_order_fetch_all_with_order_elements(*, order_ids: list[int], project_id: int) -> list[VendorOrder]:
    prefetch_order_elements = Prefetch(
        "order_elements",
        VendorOrderElement.available_objects.filter(
            status__in=[OrderStatus.COMPLETED, OrderStatus.PENDING, OrderStatus.MODIFIED, OrderStatus.SENT]
        )
        .select_related("linked_element")
        .annotate(progress_percentage=Coalesce(F("linked_element__progress_percentage"), None))
        .annotate(
            total_amount=Sum(
                F("linked_element__quantity") * F("linked_element__client_rate"), filter=Q(linked_element__isnull=False)
            )
        ),
    )

    return VendorOrder.objects.filter(id__in=order_ids, project_id=project_id).prefetch_related(prefetch_order_elements)


def annotate_progress_percentage_details(*, vendor_order: VendorOrder, project_id: int) -> None:
    today_starting_date_time = get_today_starting_datetime()
    excepted_start_date = vendor_order.started_at.date()
    actual_completion_date = vendor_order.completed_at.date() if vendor_order.completed_at else None

    order_details = vendor_order.order_elements.all().aggregate(
        today_updated_items_count=Count(
            "linked_element",
            filter=Q(
                linked_element__status_updated_at__gte=today_starting_date_time,
                linked_element__deleted_at__isnull=True,
            ),
        ),
        actual_start_date=Min("linked_element__boq_element_history__created_at__date"),
    )

    linked_boq_id_list = vendor_order.order_elements.all().values_list("linked_element", flat=True)

    boq_elements = boq_elements_fetch(project_id=project_id).filter(id__in=linked_boq_id_list)
    today_progress, total_progress, _, _, _, _ = get_today_and_total_progress(boq_elements=boq_elements)

    setattr(vendor_order, "todays_progress_percentage", today_progress)
    setattr(vendor_order, "total_progress_percentage", total_progress)
    setattr(vendor_order, "today_updated_items_count", order_details.get("today_updated_items_count"))

    if vendor_order.project.version == 1 and vendor_order.version in (1, 2):
        # setting actual start date for projects (and orders) started before rewiring

        if order_details["actual_start_date"] is None:
            order_details["actual_start_date"] = excepted_start_date

        elif actual_completion_date and order_details["actual_start_date"] > actual_completion_date:
            order_details["actual_start_date"] = excepted_start_date

        else:
            order_details["actual_start_date"] = min(order_details["actual_start_date"], excepted_start_date)

    setattr(vendor_order, "actual_start_date", order_details.get("actual_start_date"))


def outgoing_org_list_fetch_v2(
    org_from_id: int, client_id: int, element_ids: list[int], form: VendorListFormChoices, project_id: int
) -> QuerySet:
    rc_subquery: RateContractQuerySet = order_rate_contract_list_fetch(
        client_id=client_id, organization_id=org_from_id, element_ids=element_ids
    ).filter(vendor_id=OuterRef("organization_id"))

    organization_gst_number_subquery = Subquery(
        OrganizationGSTNumber.objects.filter(organization_id=OuterRef("organization_id"))
        .order_by("created_at")
        .values("gst_number")[:1]
    )
    organization_gst_state_subquery = Subquery(
        OrganizationGSTNumber.objects.filter(organization_id=OuterRef("organization_id"))
        .order_by("created_at")
        .values("gst_state")[:1]
    )
    order_vendor_ids = order_vendor_ids = (
        VendorOrder.objects.filter(project_id=project_id, org_from_id=org_from_id)
        .available()
        .values_list("org_to_id", flat=True)
    )
    vendor_ids = (
        ClientVendorMapping.objects.filter(
            org_from_id=org_from_id,
            deleted_at__isnull=True,
        )
        .exclude(org_from_id=F("org_to_id"))
        .values_list("org_to_id", flat=True)
    )
    if form == VendorListFormChoices.REGULAR_ORDER.value:
        vendor_ids = vendor_ids.filter(
            vendor_status=VendorStatusChoices.ACTIVE.value,
        )
    elif form == VendorListFormChoices.INSTA_ORDER.value:
        vendor_ids = vendor_ids.filter(
            vendor_status__in=[VendorStatusChoices.ACTIVE.value, VendorStatusChoices.ONBOARDED.value],
        )
    elif form == VendorListFormChoices.INVOICE_UPLOAD.value:
        vendor_ids = order_vendor_ids.union(vendor_ids.filter(vendor_status=VendorStatusChoices.ACTIVE.value))
    elif form == VendorListFormChoices.PAYMENT_REQUEST_INVOICE.value:
        invoice_vendor_ids = (
            Invoice.objects.filter(project_id=project_id, client_id=org_from_id)
            .available()
            .values_list("vendor_id", flat=True)
        )
        vendor_ids = order_vendor_ids.union(invoice_vendor_ids)
    elif form == VendorListFormChoices.PAYMENT_REQUEST_PO.value:
        vendor_ids = order_vendor_ids
    else:
        # non-order payments
        vendor_ids = vendor_ids.filter(
            vendor_status=VendorStatusChoices.ACTIVE.value,
        )

    outgoing_org_list: QuerySet = (
        Vendor.objects.filter(organization_id__in=vendor_ids, code__isnull=False)
        .all()
        .select_related("organization")
        .prefetch_related("category")
        .annotate(
            organization_gst_number=organization_gst_number_subquery,
            organization_gst_state=organization_gst_state_subquery,
        )
        .annotate(
            rc_count=Count(
                "organization__vendor_rate_contract",
                distinct=True,
                filter=(
                    Q(organization__vendor_rate_contract__status=RateContractStatusType.ACTIVE)
                    & Q(
                        organization__vendor_rate_contract__ended_at__gt=timezone.now(),
                    )
                    & Q(organization__vendor_rate_contract__client_id__in=[client_id, org_from_id])
                    & Q(organization__vendor_rate_contract__organization_id=org_from_id)
                ),
            )
        )
        # .prefetch_related("organization__organization_gst")
        .annotate(
            element_count=Subquery(rc_subquery.order_by("-matched_element_count").values("matched_element_count")[:1])
        )
    ).order_by("-rc_count", "-element_count")
    return outgoing_org_list


def order_fetch_with_elements(vendor_order_id: int) -> VendorOrder:
    prefetch_order_elements = Prefetch(
        "order_elements", VendorOrderElement.available_objects.all().select_related("linked_element")
    )
    return (
        VendorOrder.objects.filter(id=vendor_order_id)
        .select_related("project")
        .prefetch_related(prefetch_order_elements)
        .annotate(
            elements_count=Count(
                "order_elements",
                filter=Q(
                    order_elements__deleted_at__isnull=True,
                ),
            )
        )
    )


def order_wise_vendor_timeline_fetch(*, organization_id: int, project_id: int) -> dict:
    order = (
        VendorOrder.objects.filter(
            Q(status__in=[OrderStatus.SENT, OrderStatus.COMPLETED])
            | Q(
                outgoing_status__in=[
                    VendorOrder.OutgoingStatus.SENT,
                    VendorOrder.OutgoingStatus.COMPLETED,
                    OrderStatusEnum.APPROVED.value,
                    OrderStatusEnum.PENDING_APPROVAL.value,
                ]
            ),
            org_to=organization_id,
            project_id=project_id,
            org_to__isnull=False,
        )
        .exclude(
            Q(status=OrderStatus.CANCELLED)
            | Q(
                outgoing_status__in=[
                    VendorOrder.OutgoingStatus.CANCELLED,
                    OrderStatusEnum.REJECTED.value,
                ]
            )
        )
        .select_related("project")
        .prefetch_related("order_elements")
        .annotate_last_approved_snapshot()
        .annotate(
            order_start_date=Case(
                When(
                    Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value) & Q(last_approved_snapshot__isnull=False),
                    then=Cast(
                        Cast(F("last_approved_snapshot__started_at"), output_field=CharField()),
                        output_field=DateTimeField(),
                    ),
                ),
                default=Cast(F("started_at"), output_field=DateTimeField()),
            )
        )
        .annotate(
            order_due_date=Case(
                When(
                    Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value) & Q(last_approved_snapshot__isnull=False),
                    then=Cast(
                        Cast(F("last_approved_snapshot__due_at"), output_field=CharField()),
                        output_field=DateTimeField(),
                    ),
                ),
                default=Cast(F("due_at"), output_field=DateTimeField()),
            )
        )
        .aggregate(
            expected_start_date=Min("order_start_date"),
            execution_due_date=Max("order_due_date"),
            actual_start_date=Min("order_elements__linked_element__boq_element_history__created_at"),
            project_version=Min("project__version"),
        )
    )
    if order.get("project_version") == 1:
        if order["actual_start_date"] is None:
            order["actual_start_date"] = order["expected_start_date"]
        else:
            order["actual_start_date"] = min(order["actual_start_date"], order["expected_start_date"])
    return order


def get_subject(work_order_from: str, order_number: str):
    return f"Purchase Intent from {work_order_from} | Purchase Intent No. : {order_number}"


def get_deduction_types() -> List[Dict]:
    return DeductionType.objects.all().values("id", "name", "color_code")


def get_deduction_type_remarks(*, deduction_type_name: str) -> List[Dict]:
    return (
        DeductionRemark.objects.annotate(type_name=Lower("deduction_type__name"))
        .filter(type_name=deduction_type_name.lower())
        .all()
        .values("id", "remark")
    )


def order_review_list(*, vendor_code: str) -> QuerySet:
    return (
        VendorOrder.objects.filter(
            Q(org_to__id__in=Vendor.objects.filter(code=vendor_code).values_list("organization", flat=True))
            | Q(org_to__vendor__code=vendor_code)
        )
        .select_related("review", "review__created_by")
        .filter(review__isnull=False)
        .prefetch_related("review__review_documents")
        .order_by("-review__created_at")
        .all()
    )


def get_order_from_id(vendor_order_id: int, project_id: int):
    return VendorOrder.objects.filter(id=vendor_order_id, project_id=project_id).first()


def vendor_order_field_history_fetch_all(*, vendor_order_id: int, field: str):
    return (
        VendorOrderFieldHistory.objects.filter(field=field, order_id=vendor_order_id)
        .select_related("created_by")
        .order_by("created_at")
    )


def order_status_tooltip_data(
    *, project_id: int, element_id: int, org_id: int
) -> Tuple[List[VendorOrderElement], List[BoqElement]]:
    order_elements = (
        VendorOrderElement.available_objects.annotate_elements_final_amount()
        .filter(boq_element_id=element_id, vendor_order__org_from_id=org_id, project_id=project_id)
        .exclude(status=OrderStatus.CANCELLED)
        .select_related("vendor_order__org_to", "item_type")
        .annotate(vendor_name=F("vendor_order__org_to__name"), element_amount=F("amount_without_tax"))
    )
    related_elements = (
        BoqElement.objects.filter(
            Q(id=element_id)  # self element
            | Q(
                id__in=BoqElement.objects.filter(id=element_id, parent_element_id__isnull=False)
                .values_list("parent_element_id", flat=True)
                .available()  # when element is a sub element
            )
            | Q(
                parent_element_id=element_id,  # when element is the parent_element_id
            )
            | Q(
                element_id__in=BoqElement.objects.filter(id=element_id, element_id__isnull=False)
                .values_list("element_id", flat=True)
                .available()
            ),
            boq_id=project_id,
            organization=org_id,
        )
        .select_related("category", "item_type")
        .distinct()
        .annotate_final_amount()
        .annotate(element_amount=F("amount_without_gst"))
        .available()
    )
    return order_elements, related_elements


def get_order_outgoing_status_filter_for_invoice() -> Q:
    return Q(
        outgoing_status__in=[
            VendorOrder.OutgoingStatus.SENT.value,
            VendorOrder.OutgoingStatus.COMPLETED.value,
            OrderStatusEnum.APPROVED.value,
        ]
    ) | Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value, last_approved_snapshot__isnull=False)


def invoice_order_list(
    project_id: Optional[int], order_type: OrderType, organization_id: int, vendor_ids: list[int], client_ids: list[int]
):
    if order_type == OrderType.INCOMING.value:
        if not project_id and not client_ids:
            raise ValidationError("Project or client at least one are required.")
    else:
        if not project_id and not vendor_ids:
            raise ValidationError("Project or vendor at least one are required.")
    if order_type == OrderType.INCOMING.value:
        queryset = (
            VendorOrder.objects.all()
            .annotate_full_order_number()
            .annotate_last_approved_snapshot()
            .annotate(
                order_amount=Case(
                    When(
                        Q(last_approved_snapshot__isnull=False)
                        & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                        then=Cast(
                            Cast(F("last_approved_snapshot__amount"), FloatField())
                            + Cast(F("last_approved_snapshot__tax_amount"), FloatField()),
                            FloatField(),
                        ),
                    ),
                    default=Coalesce(
                        Cast(F("saved_total_amount"), FloatField()),
                        Value(0.0),
                        output_field=FloatField(),
                    ),
                    output_field=FloatField(),  # Ensure the whole Case expression returns a FloatField
                )
            )
            .annotate(
                not_cancelled_elements_count=Case(
                    When(
                        Q(last_approved_snapshot__isnull=False)
                        & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                        then=Subquery(
                            OrderSnapshot.objects.filter(
                                id=Cast(OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField()),
                                order_elements__status__in=[
                                    OrderStatus.PENDING,
                                    OrderStatus.SENT,
                                    OrderStatus.MODIFIED,
                                ],
                                order_elements__deleted_at__isnull=True,
                            )
                            .annotate(count=Count("order_elements"))
                            .values("count")[:1],
                        ),
                    ),
                    default=Count(
                        "order_elements",
                        distinct=True,
                        filter=Q(
                            order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                            order_elements__deleted_at__isnull=True,
                        ),
                    ),
                )
            )
            .all()
            .select_related("org_from")
            .filter(get_order_outgoing_status_filter_for_invoice())
        )
    else:
        queryset = (
            VendorOrder.objects.all()
            .annotate_not_cancelled_elements_count()
            .annotate_full_order_number()
            .annotate_last_approved_snapshot()
            .annotate(
                order_amount=Case(
                    When(
                        Q(last_approved_snapshot__isnull=False)
                        & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                        then=Cast(
                            Cast(F("last_approved_snapshot__amount"), FloatField())
                            + Cast(F("last_approved_snapshot__tax_amount"), FloatField()),
                            FloatField(),
                        ),
                    ),
                    default=Coalesce(
                        Cast(F("saved_total_amount"), FloatField()),
                        Value(0.0),
                        output_field=FloatField(),
                    ),
                    output_field=FloatField(),  # Ensure the whole Case expression returns a FloatField
                )
            )
            .all()
            .select_related("org_to")
            .filter(get_order_outgoing_status_filter_for_invoice())
        )
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    if order_type == OrderType.INCOMING.value:
        queryset = queryset.filter(org_to=organization_id)
    else:
        queryset = queryset.filter(org_from=organization_id)
    if client_ids:
        queryset = queryset.filter(org_from__in=client_ids)
    if vendor_ids:
        queryset = queryset.filter(org_to__in=vendor_ids)
    return queryset


def invoice_filter_order_list(
    order_type: OrderType,
    organization_id: int,
    project_id: Optional[int],
    client_id: Optional[int],
    vendor_id: Optional[int],
):
    invoices = invoice_fetch_all()
    if client_id:
        invoices = invoices.filter(client_id=client_id)
    if vendor_id:
        invoices = invoices.filter(vendor_id=vendor_id)
    if project_id:
        invoices = invoices.filter(project_id=project_id)
    if order_type == OrderType.INCOMING.value:
        order_ids = invoices.filter(vendor_id=organization_id).values_list("order_id", flat=True)
    else:
        order_ids = invoices.filter(client_id=organization_id).values_list("order_id", flat=True)
    queryset = VendorOrder.objects.filter(id__in=order_ids).select_related("project").all()
    return queryset


def invoice_filter_po_list(order_type: OrderType, organization_id: int, project_id: Optional[int]):
    queryset = invoice_fetch_all()
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    if order_type == OrderType.INCOMING.value:
        po_ids = queryset.filter(vendor_id=organization_id).values_list("vendor_po_id", flat=True)
    else:
        po_ids = queryset.filter(client_id=organization_id).values_list("vendor_po_id", flat=True)
    queryset = VendorPurchaseOrder.objects.filter(id__in=po_ids)
    return queryset


def order_elements_qty_dimensions_exists(*, vendor_order_id: int) -> bool:
    return VendorOrderElement.objects.filter(
        vendor_order_id=vendor_order_id, quantity_dimensions__isnull=False
    ).exists()


def order_request_comment_id_fetch(*, resource_id: int, request_id: int, org_id: int, is_snapshot: bool = False) -> int:
    if is_snapshot:
        mapping = (
            OrderRequestMapping.objects.filter(snapshot_id=resource_id, request_id=request_id)
            .select_related("comment__organization")
            .first()
        )
    else:
        mapping = (
            OrderRequestMapping.objects.filter(resource_id=resource_id, request_id=request_id)
            .select_related("comment__organization")
            .first()
        )
    if mapping and mapping.comment_id and org_id == mapping.comment.organization_id:
        return mapping.comment_id
    # if not mapping.secondary_comment:
    #     return None
    # return mapping.secondary_comment_id
    return None


# def snapshot_request_comment_id_fetch(*, snapshot_id: int, request_id: int, org_id: int) -> int:
#     mapping = (
#         OrderRequestMapping.objects.filter(snapshot_id=snapshot_id, request_id=request_id)
#         .select_related("comment__organization")
#         .first()
#     )
#     if org_id == mapping.comment.organization_id:
#         return mapping.comment_id
#     if not mapping.secondary_comment:
#         return None
#     return mapping.secondary_comment_id


def order_request_secondary_comment_update(*, order_id: int, request_id: int, comment_id: int):
    OrderRequestMapping.objects.filter(resource_id=order_id, request_id=request_id).update(
        secondary_comment_id=comment_id
    )


def are_all_orders_in_final_state(project_id: int, organization_id: int) -> bool:
    order_aggregate = fetch_basic_order_list(project_id=project_id, org_id=organization_id).aggregate(
        count=Count("id", distinct=True),
        orders_in_final_state=Count(
            "id",
            filter=Q(
                outgoing_status__in=[
                    OrderStatusEnum.CLOSED.value,
                    OrderStatusEnum.REJECTED.value,
                    OrderStatusEnum.CANCELLED.value,
                ]
            ),
        ),
    )
    return order_aggregate["count"] == order_aggregate["orders_in_final_state"]


def get_order_header_detail(
    order_id: int,
    wp_service: OrderToWorkProgressAbstractService,
):
    prefetch_order_elements = Prefetch(
        "order_elements", VendorOrderElement.available_objects.all().select_related("linked_element")
    )

    vendor_order = (
        VendorOrder.objects.filter(id=order_id).select_related("project").prefetch_related(prefetch_order_elements)
    ).first()

    if not vendor_order:
        raise OrderNotFoundException("Vendor order not found")

    excepted_start_date = vendor_order.started_at.date() if vendor_order.started_at else None
    actual_completion_date = vendor_order.completed_at.date() if vendor_order.completed_at else None

    order_details = vendor_order.order_elements.all().aggregate(
        actual_start_date=Min("linked_element__boq_element_history__created_at__date"),
    )

    linked_boq_id_list = vendor_order.order_elements.all().values_list("linked_element", flat=True)
    scope_data = wp_service.get_scope_data(element_ids=linked_boq_id_list)

    if vendor_order.project.version == 1 and vendor_order.version in (1, 2):
        # setting actual start date for projects (and orders) started before rewiring

        if order_details["actual_start_date"] is None:
            order_details["actual_start_date"] = excepted_start_date

        elif actual_completion_date and order_details["actual_start_date"] > actual_completion_date:
            order_details["actual_start_date"] = excepted_start_date

        else:
            order_details["actual_start_date"] = min(order_details["actual_start_date"], excepted_start_date)

    expected_due_date = vendor_order.due_at.date() if vendor_order.due_at else None
    expected_start_date = vendor_order.started_at.date() if vendor_order.started_at else None
    order_start_date = order_details.get("actual_start_date")
    order_completion_date = vendor_order.completed_at.date() if vendor_order.completed_at else None

    time_line_status = TimelineStatusService().get_status_message(
        expected_due_date=expected_due_date,
        expected_start_date=expected_start_date,
        actual_start_date=order_start_date,
        progress_percentage=scope_data.total_progress,
    )

    return OrderHeaderDetailEntity(
        expected_due_date=expected_due_date,
        expected_start_date=expected_start_date,
        order_start_date=order_start_date,
        order_completion_date=order_completion_date,
        order_status=time_line_status,
        order_completion=OrderCompletionPercentageEntity(
            percentage=scope_data.total_progress,
            color_code=time_line_status.color_code,
        ),
        total_item_count=scope_data.total_item_count,
        today_updated_item_count=scope_data.today_updated_item_count,
        today_progress=scope_data.today_progress,
        total_progress=scope_data.total_progress,
        total_amount=scope_data.total_amount,
        today_progress_amount=scope_data.today_progress_amount,
        total_progress_amount=scope_data.total_progress_amount,
    )
