from order.domain.abstract_repos import PurchaseOrderTypeAbstractRepo
from order.domain.abstract_services import PurchaseOrderTypeAbstractService
from order.domain.entities import PurchaseOrderTypeDataEntity


class PurchaseOrderTypeService(PurchaseOrderTypeAbstractService):
    def __init__(self, purchase_order_type_repo: PurchaseOrderTypeAbstractRepo):
        self.purchase_order_type_repo = purchase_order_type_repo

    def get_org_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        return self.purchase_order_type_repo.get_org_purchase_order_types(org_id=org_id)

    def get_purchase_order_types(self, org_id: int) -> list[PurchaseOrderTypeDataEntity]:
        return self.purchase_order_type_repo.get_purchase_order_types(org_id=org_id)

    def create_purchase_order_type(
        self, org_id: int, name: str, is_default: bool, is_active: bool, user_id: int
    ) -> PurchaseOrderTypeDataEntity:
        return self.purchase_order_type_repo.create_purchase_order_type(
            org_id=org_id, name=name, is_default=is_default, is_active=is_active, user_id=user_id
        )

    def update_purchase_order_type(self, purchase_order_type_id: int, is_default: bool, is_active: bool) -> None:
        return self.purchase_order_type_repo.update_purchase_order_type(
            purchase_order_type_id=purchase_order_type_id, is_default=is_default, is_active=is_active
        )

    def delete_purchase_order_type(self, purchase_order_type_id: int) -> None:
        return self.purchase_order_type_repo.delete_purchase_order_type(purchase_order_type_id=purchase_order_type_id)
