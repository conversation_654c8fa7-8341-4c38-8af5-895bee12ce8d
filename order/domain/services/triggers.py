from functools import partial

from django.db.transaction import on_commit

from common.events.constants import Events
from order.domain.entities.entities import OrderData, PurchaseOrderTriggerData
from order.services.trigger import (
    draft_order_modified_trigger,
    order_modified_trigger_v2,
    order_modify_email_trigger_v2,
    order_sent_email_trigger_v2,
    order_sent_trigger,
    po_upload_trigger,
)


class OrderTriggerService:
    @classmethod
    def _approve_sent_order(cls, data: OrderData):
        on_commit(
            partial(
                order_sent_trigger,
                vendor_order_id=data.id,
                org_to_id=data.vendor_id,
                org_from_id=data.origin_org_id,
                order_number=data.order_number,
                project_id=data.project_id,
            )
        )
        on_commit(partial(order_sent_email_trigger_v2, order=data, event=Events.ORDER_SENT_WITHOUT_PROPOSAL))

    @classmethod
    def _approve_modified_order(cls, data: OrderData):
        on_commit(partial(order_modified_trigger_v2, order=data))
        on_commit(partial(order_modify_email_trigger_v2, order=data, event=Events.ORDER_MODIFY_WITHOUT_PROPOSAL))

    @classmethod
    def approve_trigger(cls, data: OrderData):
        if data.snapshot_id is None:
            cls._approve_sent_order(data)
        else:
            cls._approve_modified_order(data)

    @classmethod
    def draft_order_modified_trigger(cls, data: OrderData):
        on_commit(partial(draft_order_modified_trigger, order=data))

    @classmethod
    def trigger_po_uploaded(self, data: OrderData):
        for purchase_orders_data in data.purchase_orders_data:
            po_trigger_data = PurchaseOrderTriggerData(
                order_id=data.id,
                project_id=data.project_id,
                order_number=f"{data.job_id}/{data.order_number}",
                order_org_from_id=data.org_from_id,
                order_org_to_id=data.org_to_id,
                uploaded_by_id=purchase_orders_data.uploaded_by_id,
                project_job_id=data.job_id,
                po_id=purchase_orders_data.id,
                po_number=purchase_orders_data.po_number,
            )
            on_commit(
                partial(
                    po_upload_trigger,
                    po_trigger_data=po_trigger_data,
                )
            )
