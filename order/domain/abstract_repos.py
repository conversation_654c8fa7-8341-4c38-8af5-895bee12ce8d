import abc
from typing import List

from boq.domain.entities import BoqElementInputSyncData
from order.domain.constants import OrderEventEnum, OrderStatusEnum
from order.domain.entities.entities import (
    OrderData,
    OrderRequestMappingRepoData,
    OrderSnapshotData,
    VendorInfoData,
)


class OrderRequestMappingAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def create_mapping(self, data: OrderRequestMappingRepoData):
        ...

    @abc.abstractmethod
    def update_mapping_event(self, request_id: int, order_id: int, event: OrderEventEnum):
        ...

    @abc.abstractmethod
    def update_snaphot_id(self, order_id: int, snapshot_id: int):
        ...


class OrderAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def update_order_status(self, order_id: int, status: str, user_id: int):
        ...

    @abc.abstractmethod
    def delete_order(self, order_id: int, user_id: int):
        ...

    @abc.abstractmethod
    def get_order(self, order_id: int) -> OrderData:
        ...

    @abc.abstractmethod
    def update_order(self, order_data: OrderData, order_snapshot_data: OrderSnapshotData, user_id: int):
        ...

    @abc.abstractmethod
    def get_order_data_for_snapshot(self, order_id: int) -> OrderSnapshotData:
        ...

    @abc.abstractmethod
    def create_order_status_history(self, order_id: int, status: OrderStatusEnum, user_id: int):
        ...


class OrderElementSyncAbstractRepo(abc.ABC):
    @abc.abstractmethod
    def get_elements_data(self, order_id: int) -> List[BoqElementInputSyncData]:
        ...

    @abc.abstractmethod
    def update_elements_data(self, elements: List[BoqElementInputSyncData]):
        ...


class OrderSnapshotAbstractRepo:
    @abc.abstractmethod
    def get_last_approved_order_snapshot_data(self, order_id: int):
        pass

    @abc.abstractmethod
    def create_order_snapshot(self, order_snapshot_data: OrderSnapshotData) -> int:
        pass

    @abc.abstractmethod
    def get_elements_data(self, order_id: int) -> List[BoqElementInputSyncData]:
        pass


class OrganizationPOConfigAbstractRepo:
    @abc.abstractmethod
    def update_po_config(self, upload_po_anytime: bool, auto_attach_po: bool, user_id: int, org_id: int):
        ...


class VendorAbstractRepo:
    @abc.abstractmethod
    def update_vendor_info(self, order_id: int, vendor_info_data: VendorInfoData) -> int:
        ...
