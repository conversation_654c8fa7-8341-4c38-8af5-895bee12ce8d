from typing import Optional

import structlog

from approval_request.domain.callbacks import RequestToResourceBaseCallback
from approval_request.domain.constants import ResourceEventsEnum
from boq.domain.abstract_repos import BoqElementSyncAbstractRepo
from boq.services.boq import update_boq_status
from common.exceptions import BaseValidationError
from order.domain.abstract_repos import (
    OrderAbstractRepo,
    OrderElementSyncAbstractRepo,
    OrderRequestMappingAbstractRepo,
    OrderSnapshotAbstractRepo,
)
from order.domain.constants import OrderEventEnum, OrderStatusEnum
from order.domain.entities.entities import OrderData, OrderRequestMappingRepoData
from order.domain.services.snapshot import OrderSnapshotService
from order.domain.services.sync import OrderToBoqSyncService
from order.domain.services.triggers import OrderTriggerService

logger = structlog.get_logger(__name__)


class RequestToOrderCallback(RequestToResourceBaseCallback):
    class RequestToOrderCallbackException(BaseValidationError):
        pass

    def __init__(
        self,
        resource_id: int,
        user_id: int,
        mapping_repo: OrderRequestMappingAbstractRepo,
        order_repo: OrderAbstractRepo,
        order_sync_repo: Optional[OrderElementSyncAbstractRepo] = None,
        boq_sync_repo: BoqElementSyncAbstractRepo = None,
        snapshot_id: Optional[int] = None,
        order_snapshot_repo: Optional[OrderSnapshotAbstractRepo] = None,
    ):
        super().__init__(resource_id, user_id)
        self.mapping_repo = mapping_repo
        self.order_repo = order_repo
        self.snapshot_id = snapshot_id
        self.boq_sync_repo = boq_sync_repo
        self.order_sync_repo = order_sync_repo
        self.order_snapshot_repo = order_snapshot_repo

    def _is_order_sent_first_time(self, order_data: OrderData) -> bool:
        if order_data.event == OrderEventEnum.SENT_FOR_APPROVAL.value:
            return True
        if order_data.event == OrderEventEnum.MODIFICATION_FOR_APPROVAL.value:
            return False
        if order_data.event is None and order_data.snapshot_id:
            return False
        if order_data.event is None and not order_data.snapshot_id:
            return True

    def boq_sync_trigger_and_permission_assign(self, order_data: OrderData):
        from order.services.email import assign_role_and_permissions

        assign_role_and_permissions(
            vendor_users_email=order_data.email_data.get("to_receiver") if order_data.email_data else [],
            to_assign_org=order_data.vendor_id,
            project_id=order_data.project_id,
            assigned_by_org=order_data.origin_org_id,
            created_by_id=self.user_id,
        )
        sync_service = OrderToBoqSyncService(
            order_sync_repo=self.order_sync_repo,
            boq_sync_repo=self.boq_sync_repo,
            order_snapshot_repo=self.order_snapshot_repo,
        )
        sync_service.apply_sync(order_id=self.resource_id, user_id=self.user_id)
        OrderTriggerService.approve_trigger(data=order_data)
        update_boq_status(boq_id=order_data.project_id, organization_id=order_data.vendor_id, user_id=self.user_id)

    def on_request_not_required(self):
        order_data = self.order_repo.get_order(order_id=self.resource_id)
        self.boq_sync_trigger_and_permission_assign(order_data=order_data)
        self.order_repo.update_order_status(
            order_id=self.resource_id, status=OrderStatusEnum.SENT.value, user_id=self.user_id
        )
        self.order_repo.create_order_status_history(
            order_id=self.resource_id, status=OrderStatusEnum.SENT.value, user_id=self.user_id
        )
        if not order_data.snapshot_id:
            """
            (If Order has POs)
            This trigger will be initiated when the order is approved/sent for the FIRST time.
            In other cases, the trigger is initiated from PO Upload/Revise API directly
            """
            logger.info("Triggering PO Uploaded", order_data=order_data.purchase_orders_data)
            OrderTriggerService.trigger_po_uploaded(data=order_data)

    def on_create(self, request_id: int, event: OrderEventEnum, task_id: int, comment_id: int):
        if self.snapshot_id:
            # If order request mapping request exist the update the snapshot-id to previous order request mapping
            self.mapping_repo.update_snaphot_id(order_id=self.resource_id, snapshot_id=self.snapshot_id)

        self.mapping_repo.create_mapping(
            data=OrderRequestMappingRepoData(
                order_id=self.resource_id,
                request_id=request_id,
                task_id=task_id,
                comment_id=comment_id,
                event=event,
                user_id=self.user_id,
            )
        )
        self.order_repo.update_order_status(
            status=OrderStatusEnum.PENDING_APPROVAL.value, order_id=self.resource_id, user_id=self.user_id
        )
        self.order_repo.create_order_status_history(
            order_id=self.resource_id, status=OrderStatusEnum.PENDING_APPROVAL.value, user_id=self.user_id
        )

    def on_submit(self, request_id: int, event: ResourceEventsEnum):
        self.order_repo.update_order_status(
            status=OrderStatusEnum.PENDING_APPROVAL.value, order_id=self.resource_id, user_id=self.user_id
        )
        self.mapping_repo.update_mapping_event(request_id=request_id, order_id=self.resource_id, event=event)

    def on_delete(self):
        pass

    def on_reset(self):
        pass

    def on_cancel(self):
        pass

    def on_approve(self):
        """
        1. Update order status to Approved
        2. Update mapping event to Approved
        3. Boq Order sync or Boq Proposal Sync
        4. Provide Permissions
        5. Add Event Triggers
        6. Boq Status Update
        """
        order_data = self.order_repo.get_order(order_id=self.resource_id)
        self.boq_sync_trigger_and_permission_assign(order_data=order_data)
        self.order_repo.update_order_status(
            status=OrderStatusEnum.APPROVED.value, order_id=self.resource_id, user_id=self.user_id
        )
        self.order_repo.create_order_status_history(
            order_id=self.resource_id, status=OrderStatusEnum.APPROVED.value, user_id=self.user_id
        )
        self.mapping_repo.update_mapping_event(
            request_id=order_data.request_id,
            order_id=self.resource_id,
            event=OrderEventEnum.APPROVED.value,
        )
        if not order_data.snapshot_id:
            """
            (If Order has POs)
            This trigger will be initiated when the order is approved/sent for the FIRST time.
            In other cases, the trigger is initiated from PO Upload/Revise API directly
            """
            logger.info("Triggering PO Uploaded", order_data=order_data.purchase_orders_data)
            OrderTriggerService.trigger_po_uploaded(data=order_data)

    def _create_order_snapshot(self, order_id: int, order_snapshot_status: OrderStatusEnum) -> int:
        order_snapshot_data = OrderSnapshotService(
            order_repo=self.order_repo, order_snapshot_repo=self.order_snapshot_repo
        ).create_order_snapshot_data(order_id=order_id)
        order_snapshot_data.outgoing_status = order_snapshot_status
        return self.order_snapshot_repo.create_order_snapshot(order_snapshot_data=order_snapshot_data)

    def on_reject(self):
        """
        1. Update order status to Rejected
            if its the first order else revert order state to previous approved state
        2. Update mapping event to Rejected

        * First Update order status to 'REJECT', then create snapshot of the order
        * Second, if snapshot is available then revert order state to previous approved state
        """
        # self.order_repo.update_order_status(
        #     status=OrderStatusEnum.REJECTED.value, order_id=self.resource_id, user_id=self.user_id
        # )
        order_data = self.order_repo.get_order(order_id=self.resource_id)
        order_snapshot = None

        if order_data.snapshot_id:
            rejected_snapshot_id = self._create_order_snapshot(
                order_id=self.resource_id, order_snapshot_status=OrderStatusEnum.REJECTED.value
            )

            # If order request mapping request exist the update the snapshot-id to previous order request mapping
            self.mapping_repo.update_snaphot_id(order_id=self.resource_id, snapshot_id=rejected_snapshot_id)

            order_snapshot = self.order_snapshot_repo.get_last_approved_order_snapshot_data(order_id=self.resource_id)
            if order_snapshot:
                self.order_repo.create_order_status_history(
                    order_id=self.resource_id, status=order_snapshot.outgoing_status, user_id=self.user_id
                )
            order_data.outgoing_status = order_snapshot.outgoing_status
            self.order_repo.update_order(
                order_data=order_data, order_snapshot_data=order_snapshot, user_id=self.user_id
            )
        else:
            order_data.outgoing_status = OrderStatusEnum.REJECTED.value
            self.order_repo.update_order_status(
                status=OrderStatusEnum.REJECTED.value, order_id=self.resource_id, user_id=self.user_id
            )
        self.order_repo.create_order_status_history(
            order_id=self.resource_id, status=OrderStatusEnum.REJECTED.value, user_id=self.user_id
        )
        if order_data.snapshot_id and order_snapshot:
            self.order_repo.create_order_status_history(
                order_id=self.resource_id, status=order_snapshot.outgoing_status, user_id=self.user_id
            )

        self.mapping_repo.update_mapping_event(
            request_id=order_data.request_id,
            order_id=self.resource_id,
            event=OrderEventEnum.REJECTED.value,
        )

    def on_hold(self):
        pass

    def on_pending(self):
        pass
