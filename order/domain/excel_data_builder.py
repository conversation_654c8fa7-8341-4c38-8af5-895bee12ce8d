from typing import Optional

from django.db.models import Inte<PERSON><PERSON><PERSON>, Sum, Value
from django.db.models.functions import Coalesce

from authorization.domain.constants import Permissions
from client.domain.constants import ClientFields
from common.element_base_serializer import ElementUomInitializer
from common.excel.constants import (
    ElementExcelColumnsEnum,
    GuidelineExcelColumnsEnum,
    HorizontalAlignmentEnum,
    SheetTypeEnum,
    SummaryExcelColumnsEnum,
    TermsAndConditionsExcelColumnsEnum,
)
from common.excel.data_builder import ElementExcelDataBaseBuilder
from common.excel.entities import (
    FontEnum,
    SheetCell,
    SheetCellBorderEnum,
    SheetCellTypeEnum,
    SheetRowData,
)
from common.excel.generator import ExcelGenerator
from core.models import User
from order.data.models import Deduction, VendorOrder, VendorOrderElement, VendorOrderElementGuideline
from order.data.selectors.selector_v1 import (
    order_element_list_using_vendor_order,
    order_elements_qty_dimensions_exists,
)
from order.domain.excel_utils import get_grouped_elements_v2, prepare_payment_term_list, prepare_term_condition_list
from order.domain.mappings import ORDER_FIELDS_MAPPING
from project.domain.helpers import ProjectPermissionHelper
from proposal.domain.services import get_hidden_fields


class OrderExcelDataBuilder(ElementExcelDataBaseBuilder):
    _element_col_display_text_mapping = {
        ElementExcelColumnsEnum.S_NO: "S No",
        ElementExcelColumnsEnum.ITEM_NAME: "Item Name",
        ElementExcelColumnsEnum.DESCRIPTION: "Description",
        ElementExcelColumnsEnum.BRAND_NAME: "Brand/Make",
        ElementExcelColumnsEnum.ITEM_TYPE: "Item Type",
        ElementExcelColumnsEnum.QUANTITY: "Quantity",
        ElementExcelColumnsEnum.LENGTH: "Length",
        ElementExcelColumnsEnum.LENGTH_UOM: "Length Uom",
        ElementExcelColumnsEnum.BREADTH: "Breadth",
        ElementExcelColumnsEnum.BREADTH_UOM: "Breadth Uom",
        ElementExcelColumnsEnum.UOM: "Uom",
        ElementExcelColumnsEnum.CLIENT_RATE: "Client Rate",
        ElementExcelColumnsEnum.BUDGET_RATE: "Budget Rate",
        ElementExcelColumnsEnum.ORDER_RATE: "Order Rate",
        ElementExcelColumnsEnum.HSN_CODE: "HSN",
        ElementExcelColumnsEnum.TAX_PERCENT: "Tax %",
        ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX: "Amount (w/o Tax)",
        ElementExcelColumnsEnum.FINAL_AMOUNT: "Final Amount",
    }
    _guideline_cols_display_text_mapping = {
        GuidelineExcelColumnsEnum.S_NO: "S No",
        GuidelineExcelColumnsEnum.GUIDELINE_NAME: "Name",
        GuidelineExcelColumnsEnum.GUIDELINE_DESCRIPTION: "Description",
    }
    _summary_cols_display_text_mapping = {
        SummaryExcelColumnsEnum.S_NO: "S No",
        SummaryExcelColumnsEnum.CATEGORY: "Category",
        SummaryExcelColumnsEnum.AMOUNT: "Amount",
    }
    _term_and_condition_cols_display_text_mapping = {
        TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS: "Payment Terms:",
    }
    _project_id: int = None
    _order_id: int = None
    _order: VendorOrder = None
    _element_ids: list[int] = []
    _org_id: int = None
    _uom_helper: ElementUomInitializer = None

    def __init__(
        self,
        org_id: int,
        project_id: Optional[int] = None,
        order_id: Optional[int] = None,
        element_ids: list[int] = [],
    ):
        super().__init__()
        self._org_id = org_id
        self._project_id = project_id
        self._order_id = order_id
        self._element_ids = element_ids
        if project_id and order_id:
            self._elements = order_element_list_using_vendor_order(
                vendor_order_id=self._order_id, project_id=self._project_id
            )
        else:
            self._elements = (
                VendorOrderElement.available_objects.filter(id__in=self._element_ids)
                .select_related("item_type", "vendor_order", "vendor_order__payment_term")
                .order_by("created_at")
            )
        self._order_id = self._elements[0].vendor_order_id
        self._order = self._elements[0].vendor_order
        self._project_id = self._elements[0].vendor_order.project_id
        self._guidelines = VendorOrderElementGuideline.objects.filter(element_id__in=self._element_ids).all()
        self._summary, self.tax_amount = get_grouped_elements_v2(order_elements=self._elements)
        # If We check this logic api then we also have to query element and order in api level
        if self._order and self._org_id == self._order.org_to_id:
            if ElementExcelColumnsEnum.CLIENT_RATE in self._element_cols:
                self._element_cols.remove(ElementExcelColumnsEnum.CLIENT_RATE)
        self._uom_helper = ElementUomInitializer()

    def get_file_name(self) -> str:
        return f"{self._order.project.job_id}/{self._order.order_number}_element"

    def get_element_cell_data(self, element: VendorOrderElement, col: ElementExcelColumnsEnum) -> SheetCell:
        excel_entity = element.get_excel_entity()
        if col == ElementExcelColumnsEnum.ITEM_NAME:
            return SheetCell(
                value=element.name,
                type=SheetCellTypeEnum.HYPERLINK,
                hyperlink=element.public_url,
            )
        elif col == ElementExcelColumnsEnum.DESCRIPTION:
            return SheetCell(value=excel_entity.get_parsed_description())
        elif col == ElementExcelColumnsEnum.ITEM_CODE:
            return SheetCell(value=element.get_item_code())
        elif col == ElementExcelColumnsEnum.ITEM_TYPE:
            return SheetCell(value=element.item_type.name if element.item_type else "")
        elif col == ElementExcelColumnsEnum.QUANTITY:
            return SheetCell(value=round(element.quantity, 2))
        elif col == ElementExcelColumnsEnum.LENGTH:
            return SheetCell(value=excel_entity.length if excel_entity.length else "")
        elif col == ElementExcelColumnsEnum.LENGTH_UOM:
            return SheetCell(value=excel_entity.length_uom if excel_entity.length_uom else "")
        elif col == ElementExcelColumnsEnum.BREADTH:
            return SheetCell(value=excel_entity.breadth if excel_entity.breadth else "")
        elif col == ElementExcelColumnsEnum.BREADTH_UOM:
            return SheetCell(value=excel_entity.breadth_uom if excel_entity.breadth_uom else "")
        elif col == ElementExcelColumnsEnum.UOM:
            return SheetCell(value=self._uom_helper.uom_name_get(element.uom))
        elif col == ElementExcelColumnsEnum.CLIENT_RATE:
            return SheetCell(value=element.client_rate if element.client_rate else "")
        elif col == ElementExcelColumnsEnum.ORDER_RATE:
            return SheetCell(value=round(element.vendor_rate, 2))
        elif col == ElementExcelColumnsEnum.FINAL_AMOUNT:
            return SheetCell(value=round(element.get_final_amount(), 2))
        elif col == ElementExcelColumnsEnum.BRAND_NAME:
            return SheetCell(value=element.brand_name)
        elif col == ElementExcelColumnsEnum.TAX_PERCENT:
            return SheetCell(value=element.tax_percent)
        elif col == ElementExcelColumnsEnum.HSN_CODE:
            return SheetCell(value=element.hsn_code)
        elif col == ElementExcelColumnsEnum.BUDGET_RATE:
            return SheetCell(value=element.budget_rate)
        elif col == ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX:
            return SheetCell(value=round(element.get_amount_without_tax(), 2))
        else:
            raise ValueError(f"Unknown column {col}")

    def get_summary_sheet_rows(self) -> list[SheetRowData]:
        rows = [SheetRowData(data={key: SheetCell(value="") for key in self._summary_cols})]
        count = 1
        total_amount = 0
        tax_value = self.tax_amount
        for data in self._summary:
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value=str(count))
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(value=data.get("Category"))
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(value=round(data.get("Amount"), 2))
            total_amount += data.get("Amount")
            count += 1
            rows.append(SheetRowData(data=row_data))

        deduction_amount = Deduction.objects.filter(order_id=self._order_id).aggregate(
            amount_sum=Coalesce(Sum("amount", output_field=IntegerField()), Value(0))
        )["amount_sum"]
        row_data = {}
        if tax_value:
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(
                        value="Sub Total(w/o Tax)",
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=round(total_amount - tax_value, 2),
                        type=SheetCellTypeEnum.DECIMAL,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))

            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(value="Tax Amount", horizontal_alignment=HorizontalAlignmentEnum.RIGHT)
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=round(tax_value, 2),
                        type=SheetCellTypeEnum.DECIMAL,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
        if deduction_amount:
            row_data = {}
            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    text = "Sub Total (With Tax)" if tax_value else "Sub Total"
                    row_data[key] = SheetCell(
                        value=text,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=round(total_amount, 2),
                        type=SheetCellTypeEnum.DECIMAL,
                        border=SheetCellBorderEnum.TOP,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )
            rows.append(SheetRowData(data=row_data))
            row_data = {}

            for key in self._summary_cols:
                if key == SummaryExcelColumnsEnum.S_NO:
                    row_data[key] = SheetCell(value="")
                elif key == SummaryExcelColumnsEnum.CATEGORY:
                    row_data[key] = SheetCell(value="Deductions", horizontal_alignment=HorizontalAlignmentEnum.RIGHT)
                elif key == SummaryExcelColumnsEnum.AMOUNT:
                    row_data[key] = SheetCell(
                        value=-round(deduction_amount, 2),
                        type=SheetCellTypeEnum.DECIMAL,
                        horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                    )

            rows.append(SheetRowData(data=row_data))
        row_data = {}
        for key in self._summary_cols:
            if key == SummaryExcelColumnsEnum.S_NO:
                row_data[key] = SheetCell(value="")
            elif key == SummaryExcelColumnsEnum.CATEGORY:
                text = "Final Amount (with Tax)" if tax_value else "Final Amount"
                row_data[key] = SheetCell(
                    value=text,
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
            elif key == SummaryExcelColumnsEnum.AMOUNT:
                row_data[key] = SheetCell(
                    value=round(total_amount - deduction_amount, 2),
                    font=FontEnum.BOLD,
                    border=SheetCellBorderEnum.TOP,
                    horizontal_alignment=HorizontalAlignmentEnum.RIGHT,
                )
        rows.append(SheetRowData(data=row_data))
        return rows

    def get_guideline_cell_data(
        self, guideline: VendorOrderElementGuideline, col: GuidelineExcelColumnsEnum
    ) -> SheetCell:
        if col == GuidelineExcelColumnsEnum.GUIDELINE_NAME:
            return SheetCell(value=guideline.name)
        elif col == GuidelineExcelColumnsEnum.GUIDELINE_DESCRIPTION:
            return SheetCell(value=guideline.get_description())
        else:
            raise ValueError(f"Unknown column {col}")

    def get_terms_and_conditions_sheet_rows(self) -> list[SheetRowData]:
        rows = []
        payment_term_list = prepare_payment_term_list(vendor_order_obj=self._order)
        term_and_conditions_list = prepare_term_condition_list(vendor_order_obj=self._order)

        for payment_term in payment_term_list:
            rows_data = {}
            for key in self._term_and_condition_cols:
                if key == TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS:
                    rows_data[key] = SheetCell(value=payment_term)
            rows.append(SheetRowData(data=rows_data))

        rows_data = {}
        for key in self._term_and_condition_cols:
            if key == TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS:
                rows_data[key] = SheetCell(value="")
        rows.append(SheetRowData(data=rows_data))
        rows_data = {}

        if term_and_conditions_list:
            for key in self._term_and_condition_cols:
                if key == TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS:
                    rows_data[key] = SheetCell(value="Other Terms and Conditions:")
            rows.append(SheetRowData(data=rows_data))

        for term in term_and_conditions_list:
            rows_data = {}
            for key in self._term_and_condition_cols:
                if key == TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS:
                    rows_data[key] = SheetCell(value=term)
            rows.append(SheetRowData(data=rows_data))

        return rows


class OrderExcelDirectorService:
    visible_columns_name_mapping = {
        "tax_percent": [
            ElementExcelColumnsEnum.TAX_PERCENT,
            ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX,
        ],
        "budget_rate": [ElementExcelColumnsEnum.BUDGET_RATE],
        "hsn": [ElementExcelColumnsEnum.HSN_CODE],
        "brand": [ElementExcelColumnsEnum.BRAND_NAME],
    }

    def __init__(
        self,
        user: User,
        project_id: int,
        order_id: int,
        org_id: int,
        order_number: str,
        element_ids: list[int],
        with_client_rate: bool = False,
        with_guidelines: bool = False,
        visible_columns: list[str] = [],
    ):
        self.project_id = project_id
        self.order_id = order_id
        self.org_id = org_id
        self.element_ids = element_ids
        self.with_client_rate = with_client_rate
        self.with_guidelines = with_guidelines
        self.user = user
        self.visible_columns = visible_columns
        self.order_number = order_number

    def build_excel(self):
        hidden_fields = get_hidden_fields(
            client_field_mapping=ORDER_FIELDS_MAPPING, order_id=self.order_id, org_id=self.org_id
        )

        cols = [
            ElementExcelColumnsEnum.S_NO,
            ElementExcelColumnsEnum.ITEM_NAME,
            ElementExcelColumnsEnum.DESCRIPTION,
            ElementExcelColumnsEnum.ITEM_CODE,
            ElementExcelColumnsEnum.ITEM_TYPE,
            ElementExcelColumnsEnum.QUANTITY if (ClientFields.QUANTITY not in hidden_fields) else None,
            ElementExcelColumnsEnum.UOM if (ClientFields.UOM not in hidden_fields) else None,
        ]

        data_builder = (
            OrderExcelDataBuilder(
                project_id=self.project_id,
                order_id=self.order_id,
                org_id=self.org_id,
                element_ids=self.element_ids,
            )
            .add_sheet(SheetTypeEnum.ELEMENT)
            .add_sheet(SheetTypeEnum.SUMMARY)
            .add_sheet(SheetTypeEnum.TERMS_AND_CONDITIONS)
            .add_element_cols(*[col for col in cols if col is not None])
            .add_summary_cols(
                SummaryExcelColumnsEnum.S_NO,
                SummaryExcelColumnsEnum.CATEGORY,
                SummaryExcelColumnsEnum.AMOUNT,
            )
            .add_term_and_condition_cols(
                TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS,
            )
        )

        if order_elements_qty_dimensions_exists(vendor_order_id=self.order_id) and (
            ClientFields.UOM not in hidden_fields
        ):
            data_builder = data_builder.add_element_cols(
                *[
                    ElementExcelColumnsEnum.LENGTH,
                    ElementExcelColumnsEnum.LENGTH_UOM,
                    ElementExcelColumnsEnum.BREADTH,
                    ElementExcelColumnsEnum.BREADTH_UOM,
                ]
            )
        # TODO: Quick Fix for hiding Budget Rate Column
        order = VendorOrder.objects.filter(project_id=self.project_id, order_number=self.order_number).first()
        if self.visible_columns:
            for column in self.visible_columns:
                if column == ElementExcelColumnsEnum.BUDGET_RATE.value:
                    # If current org is vendor org (Client Order Tab)
                    if self.org_id == order.org_to_id:
                        continue
                if column in self.visible_columns_name_mapping:
                    data_builder = data_builder.add_element_cols(*self.visible_columns_name_mapping[column])

        if self.with_client_rate and Permissions.CAN_VIEW_CLIENT_RATE in ProjectPermissionHelper.get_permissions(
            project_id=self.project_id, user=self.user
        ):
            data_builder = data_builder.add_element_cols(
                ElementExcelColumnsEnum.CLIENT_RATE,
            )

        amount_cols = [
            ElementExcelColumnsEnum.ORDER_RATE if (ClientFields.RATE not in hidden_fields) else None,
            ElementExcelColumnsEnum.FINAL_AMOUNT if (ClientFields.FINAL_AMOUNT not in hidden_fields) else None,
        ]
        if ProjectPermissionHelper.has_permission(
            project_id=self.project_id, user=self.user, permission=Permissions.CAN_VIEW_ORDER_RATE
        ):
            data_builder = data_builder.add_element_cols(*[col for col in amount_cols if col is not None])
        excel_file, filename = ExcelGenerator(data_builder=data_builder).generate()
        return excel_file, filename


class OrderExcelEmailDirectorService:
    def __init__(
        self,
        order_id: int,
        org_id: int,
        element_ids: list[int],
    ):
        self.order_id = order_id
        self.org_id = org_id
        self.element_ids = element_ids

    def build_excel(self):
        hidden_fields = get_hidden_fields(
            client_field_mapping=ORDER_FIELDS_MAPPING, order_id=self.order_id, org_id=self.org_id
        )

        cols = [
            ElementExcelColumnsEnum.S_NO,
            ElementExcelColumnsEnum.ITEM_NAME,
            ElementExcelColumnsEnum.DESCRIPTION,
            ElementExcelColumnsEnum.ITEM_CODE,
            ElementExcelColumnsEnum.ITEM_TYPE,
            ElementExcelColumnsEnum.QUANTITY if (ClientFields.QUANTITY not in hidden_fields) else None,
            ElementExcelColumnsEnum.UOM if (ClientFields.UOM not in hidden_fields) else None,
            ElementExcelColumnsEnum.ORDER_RATE if (ClientFields.RATE not in hidden_fields) else None,
            ElementExcelColumnsEnum.TAX_PERCENT if (ClientFields.TAX not in hidden_fields) else None,
            ElementExcelColumnsEnum.HSN_CODE if (ClientFields.HSN not in hidden_fields) else None,
            ElementExcelColumnsEnum.BRAND_NAME,
            (
                ElementExcelColumnsEnum.AMOUNT_WITHOUT_TAX
                if (ClientFields.AMOUNT_WITHOUT_TAX not in hidden_fields)
                else None
            ),
            ElementExcelColumnsEnum.FINAL_AMOUNT if (ClientFields.FINAL_AMOUNT not in hidden_fields) else None,
        ]

        data_builder = (
            OrderExcelDataBuilder(
                order_id=self.order_id,
                org_id=self.org_id,
                element_ids=self.element_ids,
            )
            .add_sheet(SheetTypeEnum.ELEMENT)
            .add_sheet(SheetTypeEnum.SUMMARY)
            .add_sheet(SheetTypeEnum.TERMS_AND_CONDITIONS)
            .add_element_cols(*[col for col in cols if col is not None])
            .add_summary_cols(
                SummaryExcelColumnsEnum.S_NO,
                SummaryExcelColumnsEnum.CATEGORY,
                SummaryExcelColumnsEnum.AMOUNT,
            )
            .add_term_and_condition_cols(
                TermsAndConditionsExcelColumnsEnum.PAYMENT_TERMS,
            )
        )
        if order_elements_qty_dimensions_exists(vendor_order_id=self.order_id) and (
            ClientFields.UOM not in hidden_fields
        ):
            data_builder = data_builder.add_element_cols(
                *[
                    ElementExcelColumnsEnum.LENGTH,
                    ElementExcelColumnsEnum.LENGTH_UOM,
                    ElementExcelColumnsEnum.BREADTH,
                    ElementExcelColumnsEnum.BREADTH_UOM,
                ]
            )
        data_builder = (
            data_builder.add_element_cols(ElementExcelColumnsEnum.ORDER_RATE)
            if (ClientFields.RATE.value not in hidden_fields)
            else data_builder
        )
        excel_file, filename = ExcelGenerator(data_builder=data_builder).generate()
        return excel_file, filename
