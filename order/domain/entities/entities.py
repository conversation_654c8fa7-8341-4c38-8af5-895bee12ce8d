import decimal
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Union

from django.db.models import QuerySet

from boq.data.choices import BoqElementStatus
from common.pydantic.base_model import BaseModel, BaseModelV2
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt
from core.tnc_config.domain.entities import OtherTnCDataEntity, PaymentTnCDataEntity
from microcontext.domain.constants import MicroContext
from order.domain.constants import OrderEventEnum, OrderTypeEnum
from order.domain.entities.domain_entities import DeductionData
from proposal.data.entities import OrderElementData


@dataclass(frozen=True)
class OrderRequestMappingRepoData:
    order_id: int
    request_id: int
    comment_id: int
    task_id: int
    event: OrderEventEnum
    user_id: int


@dataclass(frozen=True)
class OrderRequestCreateData:
    order_id: int
    user_id: int
    context: MicroContext
    project_id: int
    order_amount: float
    is_draft: bool
    org_id: int
    event: OrderEventEnum
    request_id: Optional[int] = None
    snapshot_id: Optional[int] = None
    hierarchy_context: Optional[MicroContext] = None


@dataclass(frozen=True)
class OrderRequestCancelData:
    request_id: int
    order_id: int
    order_type: OrderTypeEnum
    user_id: int
    org_id: int


@dataclass
class PurchaseOrderData:
    id: int
    po_number: str
    uploaded_by_id: int


@dataclass
class PurchaseOrderTriggerData:
    order_id: int
    project_id: int
    order_number: str
    order_org_from_id: int
    order_org_to_id: int
    uploaded_by_id: int
    project_job_id: str
    po_id: int
    po_number: str


@dataclass
class OrderData:
    id: int
    email_data: Dict
    vendor_id: int
    origin_org_id: int
    project_id: int
    event: OrderEventEnum
    org_from_id: int
    shipping_address: str
    issued_by_id: int
    org_from_name: str
    order_number: str
    job_id: str
    vendor_name: str
    outgoing_status: str
    old_order_value: float = 0
    org_to_id: Optional[int] = None
    proposal_id: Optional[int] = None
    snapshot_id: Optional[int] = None
    request_id: Optional[int] = None
    order_elements: List[OrderElementData] = field(default_factory=list)
    order_deductions: List[DeductionData] = field(default_factory=list)
    purchase_orders_data: List[PurchaseOrderData] = field(default_factory=list)
    started_at: Optional[datetime] = None
    due_at: Optional[datetime] = None
    payment_term_text: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    is_service_charged: bool = False
    is_discounted: bool = False
    is_taxed: bool = False
    payment_tnc: Union[PaymentTnCDataEntity, None] = None
    other_tnc: Union[OtherTnCDataEntity, None] = None


@dataclass
class OrderElementPreviewFileSnapshotData:
    file: str
    name: str
    type: str
    object_status: str
    is_main: bool


@dataclass
class OrderElementProductionDrawingTagSnapshotData:
    id: int


@dataclass
class OrderElementProductionDrawingSnapshotData:
    file: str
    name: str
    object_status: str
    tags: List[OrderElementProductionDrawingTagSnapshotData]


@dataclass
class OrderElementGuidelineAttachmentSnapshotData:
    file: str
    name: str
    type: str
    object_status: str


@dataclass
class OrderElementGuidelineSnapshotData:
    description: str
    name: str
    object_status: str
    attachments: List[OrderElementGuidelineAttachmentSnapshotData]


@dataclass
class OrderElementSnapshotData:
    item_type_id: int
    serial_number: int
    client_rate: decimal.Decimal
    budget_rate: decimal.Decimal
    vendor_rate: decimal.Decimal
    quantity: decimal.Decimal
    status: str
    cancelled_at: datetime
    modified_at: datetime
    modified_by_id: int
    custom_type: str
    boq_element_version: int
    discount_percent: decimal.Decimal
    service_charge_percent: decimal.Decimal
    is_service_charge_with_base_amount: bool
    quantity_dimensions: dict
    name: str
    uom: str
    description: str
    category_id: int
    code: str
    is_custom: bool
    created_by_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    updated_by_id: Optional[int] = None
    boq_element_id: Optional[int] = None
    el_element_id: Optional[int] = None
    cancelled_by_id: Optional[int] = None
    tax_percent: Optional[decimal.Decimal] = 0
    preview_files: list[OrderElementPreviewFileSnapshotData] = field(default_factory=list)
    production_drawings: list[OrderElementProductionDrawingSnapshotData] = field(default_factory=list)
    guidelines: list[OrderElementGuidelineSnapshotData] = field(default_factory=list)
    linked_element_id: Optional[int] = None
    linked_element_deleted_at: Optional[datetime] = None
    linked_element_element_status: Optional[BoqElementStatus] = None
    linked_element_progress_percentage: Optional[int] = None
    progress_percentage: Optional[int] = None
    brand_name: Optional[str] = ""
    hsn_code: Optional[str] = ""


@dataclass
class OrderElementDataForUpdation:
    id: int
    item_type_id: int
    serial_number: int
    custom_type: str
    code: str
    status: str
    boq_element_version: int
    vendor_rate: decimal.Decimal
    quantity: decimal.Decimal
    quantity_dimensions: dict
    updated_at: datetime
    updated_by_id: int
    cancelled_at: datetime
    cancelled_by_id: int
    budget_rate: decimal.Decimal
    tax_percent: decimal.Decimal


@dataclass
class DeductionAttachmentSnapshotData:
    file: str
    name: str
    uploaded_by_id: int
    uploaded_at: datetime


@dataclass
class DeductionSnapshotData:
    name: str
    amount: decimal.Decimal
    tax_amount: decimal.Decimal
    code: int
    type: str
    remark: str
    type_color_code: str
    item_reference: str
    created_by_id: int
    created_at: datetime
    attachments: list[DeductionAttachmentSnapshotData]


@dataclass
class OrderSnapshotData:
    order_id: int
    started_at: datetime
    due_at: datetime
    work_order_from: str
    shipping_address: str
    shipping_address_header: str
    rate_contract_id: Union[int, None]
    payment_term_id: Optional[int]
    payment_term_text: Optional[str]
    created_at: datetime
    created_by_id: int
    progress_percentage: int
    elements: list[OrderElementSnapshotData]
    deductions: list[DeductionSnapshotData]
    amount: decimal.Decimal
    tax_amount: decimal.Decimal
    is_discounted: bool
    is_service_charged: bool
    issued_at: Optional[datetime]
    invoice_status: Optional[str]
    outgoing_status: Optional[str]
    id: Optional[int] = None
    is_taxed: bool = False
    gst_number: Optional[str] = None
    gst_state: Optional[int] = None
    payment_tnc: Union[PaymentTnCDataEntity, None] = None
    other_tnc: Union[OtherTnCDataEntity, None] = None
    config_text_data: Optional[QuerySet] = field(default_factory=list)


@dataclass(frozen=True)
class VendorOrderElementData:
    id: int
    vendor_rate: decimal.Decimal
    budget_rate: decimal.Decimal
    item_type_id: int
    quantity: decimal.Decimal
    quantity_dimensions: dict
    updated_at: datetime
    updated_by_id: int
    status: str
    cancelled_at: datetime
    cancelled_by_id: int
    deleted_at: Optional[datetime] = None
    deleted_by_id: Optional[int] = None
    tax_percent: Optional[decimal.Decimal] = 0


@dataclass(frozen=True)
class OrganizationPOConfigEntity:
    can_upload_po_anytime: bool
    auto_attach_po: bool


@dataclass(frozen=True)
class VendorInfoData:
    pan_number: str
    gst_number: str
    account_holder_name: str
    account_number: str
    ifsc_code: str
    bank_name: str
    cancelled_cheque_name: str
    cancelled_cheque: str
    pan_attachment: Optional[str] = None
    gst_attachment: Optional[str] = None


class VendorInfoOrganizationCountryConfigData(BaseModel):
    class VendorInfoOrganizationSectionConfigData(BaseModel):
        class VendorInfoOrganizationDocumentConfigData(BaseModel):
            class VendorInfoOrganizationFieldData(BaseModel):
                id: Optional[int]
                name: str
                type: str
                position: int
                is_required: bool
                regex: Optional[str]
                is_visible_on_app: bool

            id: Optional[int]
            name: str
            multiple_allowed: bool
            position: int
            is_required: bool
            fields: list[VendorInfoOrganizationFieldData]
            is_visible_on_app: bool

        id: Optional[int]
        name: str
        position: int
        type: str
        documents: list[VendorInfoOrganizationDocumentConfigData]

    class VendorInfoOrganizationFieldData(BaseModel):
        id: Optional[int]
        name: str
        type: str
        position: int
        is_required: bool
        regex: Optional[str]
        is_visible_on_app: bool

    sections: list[VendorInfoOrganizationSectionConfigData]
    uid_field: Optional[VendorInfoOrganizationFieldData]


class OrderFieldHistoryUpdatedByOrgDataEntity(BaseModelV2):
    id: HashIdInt
    name: str


class OrderFieldHistoryUpdatedByDataEntity(BaseModelV2):
    id: HashIdInt
    name: str
    photo: CustomFileUrlStr | None = None
    org: OrderFieldHistoryUpdatedByOrgDataEntity


class OrderFieldHistoryDataEntity(BaseModelV2):
    updated_at: datetime
    reason: str | None = None
    value: datetime | None
    updated_by: OrderFieldHistoryUpdatedByDataEntity | None = None
    prev_value: datetime | None = None


class OrderFieldHistoryUsingDatesEntity(BaseModelV2):
    history_entities: List[OrderFieldHistoryDataEntity]
    prev_date: datetime | None = None
    prev_reason: str | None = None
    curr_date: datetime | None = None
    curr_reason: str | None = None


class VendorScopeHeaderDetailModel(BaseModelV2):
    order_count: int
    total_item_count: int
    total_amount: decimal.Decimal
    today_updated_item_count: int
    total_progress: decimal.Decimal
    today_progress: decimal.Decimal
