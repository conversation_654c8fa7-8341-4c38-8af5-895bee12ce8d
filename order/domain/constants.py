from common.constants import BaseEnum


class OrderTypeEnum(BaseEnum):
    REGULAR = "regular"
    INSTA_ORDER = "insta_order"


class OrderActionEnum(BaseEnum):
    VIEW = "view"
    EDIT = "edit"
    DELETE = "delete"
    SUBMIT = "submit"
    CANCEL = "cancel"
    APPROVE = "approve"
    REJECT = "reject"
    HOLD = "hold"
    SEND = "send"
    COMPLETE = "complete"
    CLOSE = "close"
    INCOMPLETE = "incomplete"
    UNCLOSE = "unclose"
    MARK_ALL_INVOICE_UPLOADED = "mark_all_invoice_uploaded"
    DUPLICATE = "duplicate"


class OrderStatusEnum(BaseEnum):
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    COMPLETED = "completed"
    CLOSED = "closed"
    SENT = "sent"
    DRAFT = "notsent"
    MODIFIED = "modified"
    PENDING = "pending"


class ClientOrderStatusEnum(BaseEnum):
    CONFIRMED = "confirmed"
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class OrderEventEnum(BaseEnum):
    DRAFT = "notsent"
    SENT_FOR_APPROVAL = "sent_for_approval"
    MODIFICATION_FOR_APPROVAL = "modification_for_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class VendorListFormChoices(BaseEnum):
    REGULAR_ORDER = "regular_order"
    INSTA_ORDER = "insta_order"
    PURCHASE_ORDER = "purchase_order_type"
    INVOICE_UPLOAD = "invoice_upload"
    PAYMENT_REQUEST_PO = "payment_request_po"
    PAYMENT_REQUEST_INVOICE = "payment_request_invoice"
