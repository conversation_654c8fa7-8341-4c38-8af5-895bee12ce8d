from django.db import models
from django.utils.translation import gettext_lazy as _

from order.domain.constants import ClientOrderStatusEnum, OrderEventEnum, OrderStatusEnum, OrderTypeEnum


class OrderTypeChoices(models.TextChoices):
    REGULAR = OrderTypeEnum.REGULAR.value, _("Regular")
    INSTA_ORDER = OrderTypeEnum.INSTA_ORDER.value, _("Insta Order")


class OrderEventChoices(models.TextChoices):
    DRAFT = OrderEventEnum.DRAFT.value, _("DRAFT")
    SENT_FOR_APPROVAL = OrderEventEnum.SENT_FOR_APPROVAL.value, _("PENDING_APPROVAL")
    MODIFICATION_FOR_APPROVAL = OrderEventEnum.MODIFICATION_FOR_APPROVAL.value, _("MODIFICATION_FOR_APPROVAL")
    APPROVED = OrderEventEnum.APPROVED.value, _("APPROVED")
    REJECTED = OrderEventEnum.REJECTED.value, _("REJECTED")
    CANCELLED = OrderEventEnum.CANCELLED.value, _("CANCELLED")


class OrderStatusChoices(models.TextChoices):
    DRAFT = OrderStatusEnum.DRAFT.value, _("Not Sent")
    PENDING_APPROVAL = OrderStatusEnum.PENDING_APPROVAL.value, _("Pending Approval")
    PENDING = OrderStatusEnum.PENDING.value, _("Pending")
    APPROVED = OrderStatusEnum.APPROVED.value, _("Approved")
    REJECTED = OrderStatusEnum.REJECTED.value, _("Rejected")
    CANCELLED = OrderStatusEnum.CANCELLED.value, _("Cancelled")
    COMPLETED = OrderStatusEnum.COMPLETED.value, _("Completed")
    CLOSED = OrderStatusEnum.CLOSED.value, _("Closed")
    # When order is sent from client side, then confirmed status will be visible on vendor side.
    CONFIRMED = ClientOrderStatusEnum.CONFIRMED.value, _("Confirmed")
    # Order Status for old orders
    SENT = OrderStatusEnum.SENT.value, _("Sent")
    MODIFIED = OrderStatusEnum.MODIFIED.value, _("Modified")
