import decimal
from dataclasses import dataclass, field
from datetime import date, datetime
from typing import Dict, List, Optional, Union

from boq.data.choices import CustomElementType
from common.choices import OrderType
from common.context_values import BaseContextValues, OrderContextValueMixin, ProjectContextValueMixin
from common.element_base.entities import (
    GuidelineData,
    GuidelineUpdateData,
    PreviewFileData,
    PreviewFileUpdateData,
    ProductionDrawingData,
    ProductionDrawingUpdateData,
)
from common.entities import BaseNestedObject, BaseObjectData
from common.pydantic.base_model import BaseModelV2
from common.timeline.entities import TimelineStatusMessageEntity
from core.organization.domain.entities import OrganizationSectionUpdateData
from core.tnc_config.domain.entities import OtherTnCDataEntity, PaymentTnCDataEntity
from element.data.entities import ElementBaseData
from order.domain.constants import OrderTypeEnum


@dataclass
class DeductionAttachmentData(BaseNestedObject):
    file: str
    name: str
    uploaded_at: Optional[datetime] = None
    uploaded_by_id: Optional[int] = None
    deduction_id: Optional[int] = None


@dataclass
class DeductionData(BaseObjectData):
    id: Union[int, None]
    name: str
    amount: decimal.Decimal
    type: str
    type_color_code: Union[str, None]
    remark: str
    item_reference: Union[str, None]
    deleted_at: Union[datetime, None] = None
    deleted_by_id: Union[int, None] = None
    attachments: Union[List[DeductionAttachmentData], None] = field(default_factory=list)
    code: Optional[int] = None
    tax_amount: Optional[decimal.Decimal] = 0
    created_at: Union[datetime, None] = None
    created_by_id: Union[int, None] = None
    tax_amount: decimal.Decimal = 0


@dataclass
class OrderElementProductionDrawingTag:
    id: Union[int, None]
    name: str


@dataclass
class OrderElementCategoryData:
    id: int
    name: str
    code: str


@dataclass
class OrderElementItemTypeData:
    id: int
    name: str
    color_code: str


@dataclass
class OrderElementBaseData(ElementBaseData):
    id: Union[int, None]
    vendor_rate: decimal.Decimal
    quantity: decimal.Decimal
    serial_number: Union[int, None]
    boq_element_id: Union[int, None]
    el_element_id: Union[int, None]
    client_id: int
    custom_type: CustomElementType
    code: Union[str, None]
    cancelled_element_id: Union[int, None]
    linked_element_id: Union[int, None]
    brand_name: Optional[str]
    hsn_code: Optional[str]


@dataclass
class OrderElementCreateData(OrderElementBaseData):
    preview_files: Optional[list[PreviewFileData]]
    guidelines: Optional[list[GuidelineData]]
    production_drawings: Optional[list[ProductionDrawingData]]
    boq_element_version: Union[int, None]


@dataclass
class OrderElementCreateDataV2(OrderElementCreateData):
    budget_rate: decimal.Decimal
    quantity_dimensions: Union[dict, None] = None
    tax_percent: decimal.Decimal = 0
    discounted_client_rate: Optional[decimal.Decimal] = None


@dataclass
class IncomingOrderElementCreateData(OrderElementCreateData):
    boq_element_version: int


@dataclass
class OrderElementImportOutputData(OrderElementCreateData, BaseObjectData):
    category: OrderElementCategoryData
    item_type: OrderElementItemTypeData
    discount_percent: decimal.Decimal
    base_amount: decimal.Decimal
    gross_amount: decimal.Decimal
    final_amount: decimal.Decimal
    budget_rate: decimal.Decimal = 0
    discounted_value: decimal.Decimal = 0
    service_charge_percent: decimal.Decimal = 0
    is_service_charge_with_base_amount: Optional[bool] = None
    quantity_dimensions: Union[dict, None] = None
    discounted_client_rate: Optional[decimal.Decimal] = None
    tax_percent: decimal.Decimal = 0


@dataclass
class OrderToBoqSyncElementData(OrderElementCreateData):
    quantity_dimensions: Union[dict, None] = None


@dataclass
class OrderElementExcelImportData(OrderElementCreateData, BaseObjectData):
    category: OrderElementCategoryData
    item_type: OrderElementItemTypeData
    boq_element_version: int
    budget_rate: decimal.Decimal = 0
    tax_percent: decimal.Decimal = 0
    quantity_dimensions: Union[dict, None] = None


@dataclass
class IncomingOrderElementImportOutputData(IncomingOrderElementCreateData, BaseObjectData):
    category: OrderElementCategoryData
    item_type: OrderElementItemTypeData


@dataclass
class OrderElementUpdateData(BaseNestedObject, OrderElementBaseData):
    preview_files: Optional[list[PreviewFileUpdateData]]
    guidelines: Optional[list[GuidelineUpdateData]]
    production_drawings: Optional[list[ProductionDrawingUpdateData]]
    budget_rate: decimal.Decimal = 0
    discount_percent: decimal.Decimal = 0
    service_charge_percent: decimal.Decimal = 0
    is_service_charge_with_base_amount: Optional[bool] = None
    discounted_client_rate: Optional[decimal.Decimal] = None
    quantity_dimensions: Union[dict, None] = None
    tax_percent: decimal.Decimal = 0


@dataclass
class TermsAndConditionsAttachmentsBaseData(BaseObjectData):
    type: str
    name: str
    file: str


@dataclass
class TermsAndConditionsAttachmentsData(TermsAndConditionsAttachmentsBaseData):
    pass


@dataclass
class TermsAndConditionsAttachmentsUpdateData(TermsAndConditionsAttachmentsBaseData, BaseNestedObject):
    id: Union[None, int]


@dataclass
class OrderBaseData:
    started_at: datetime
    due_at: datetime
    order_type: OrderType
    org_to_id: int
    work_order_from: str
    shipping_address: str
    shipping_address_header: str
    rate_contract_id: Union[int, None]
    deductions: Union[List[DeductionData], None]
    payment_tnc: Union[PaymentTnCDataEntity, None]
    other_tnc: Union[OtherTnCDataEntity, None]
    poc_id: Optional[int]
    type_of_order: OrderTypeEnum
    document_config_data: OrganizationSectionUpdateData


@dataclass
class OrderCreateData(OrderBaseData):
    terms_and_conditions_attachments: Union[List[TermsAndConditionsAttachmentsData], None]
    org_from_id: int
    elements: List[OrderElementCreateDataV2]
    purchase_orders: List[int]
    body: Optional[str] = None
    subject: str = None
    attachments: List = field(default_factory=list)
    bcc: List[str] = field(default_factory=list)
    cc: List[str] = field(default_factory=list)
    to: List[str] = field(default_factory=list)
    origin_org_id: Union[int, None] = None
    is_discounted: bool = False
    is_service_charged: bool = False
    is_new_vendor: bool = False
    is_taxed: bool = False


@dataclass
class IncomingOrderCreateData(OrderBaseData):
    terms_and_conditions_attachments: Union[List[TermsAndConditionsAttachmentsData], None]
    org_from_id: Union[int, None]
    elements: List[IncomingOrderElementCreateData]
    purchase_orders: List[int]


@dataclass
class OrderUpdateData(OrderBaseData):
    terms_and_conditions_attachments: Union[List[TermsAndConditionsAttachmentsUpdateData], None]
    elements: List[OrderElementUpdateData]
    org_from_id: Union[int, None]
    final_amount: decimal.Decimal
    origin_org_id: Union[int, None] = None
    is_discounted: Optional[bool] = None
    is_service_charged: Optional[bool] = None
    email_data: Optional[Dict] = None
    is_new_vendor: Optional[bool] = None
    is_taxed: bool = False


@dataclass
class ExcelAttachmentData:
    name: str
    url: str


@dataclass
class OrderSentData:
    to: List
    cc: List
    bcc: List
    subject: str
    body: str
    attachments: List[ExcelAttachmentData]


@dataclass
class OrderCreateAndSentData(OrderSentData, OrderBaseData):
    terms_and_conditions_attachments: Union[List[TermsAndConditionsAttachmentsData], None]
    org_from_id: Union[int, None]
    elements: List[OrderElementCreateData]
    purchase_orders: List[int]
    final_amount: decimal.Decimal
    proposal_id: Union[int, None]
    origin_org_id: Union[int, None] = None
    is_discounted: bool = False
    is_service_charged: bool = False
    quantity_dimensions: Union[dict, None] = None
    is_new_vendor: bool = False
    is_taxed: bool = False


@dataclass
class OrderUpdateAndSentData(OrderSentData, OrderBaseData):
    final_amount: decimal.Decimal
    terms_and_conditions_attachments: Union[List[TermsAndConditionsAttachmentsUpdateData], None]
    elements: List[OrderElementUpdateData]
    org_from_id: Union[int, None]
    final_amount: decimal.Decimal
    origin_org_id: Union[int, None] = None
    is_new_vendor: Optional[bool] = False
    is_taxed: bool = False


@dataclass
class OrderSentInitializeData:
    org_to_id: int
    order_number: str
    work_order_from: str


@dataclass
class ProposalOrderElementImportOutputData(OrderElementCreateData, BaseObjectData):
    category: OrderElementCategoryData
    item_type: OrderElementItemTypeData
    boq_element_version: int
    quantity_dimensions: Union[dict, None] = None


@dataclass
class WorkOrderFrom:
    organization_id: int
    name: str


@dataclass
class OrgTo:
    organization_id: int
    name: str
    code: str
    gst_number: str


@dataclass
class OrderOuputData:
    order_number: str
    shipping_address: str
    started_at: str
    due_at: str
    terms_and_conditions: str
    org_from_id: int
    org_to: OrgTo
    org_to_id: int
    origin_org_id: int
    work_order_from: List[WorkOrderFrom]
    order_elements: List[ProposalOrderElementImportOutputData]
    proposal_id: int
    shipping_address_header = None
    order_type = OrderType.OUTGOING
    rate_contract_id = None
    attachments: List[TermsAndConditionsAttachmentsData]


@dataclass
class VendorOrderDataForExcelSheet:
    vendor_order_element_id: int
    vendor_rate: decimal.Decimal
    tax_percent: decimal.Decimal
    brand_name: str
    hsn_code: str
    quantity: decimal.Decimal


@dataclass
class MarkOrderCompleteData:
    vendor_order_id: int
    user_id: int
    project_id: int
    started_at: datetime
    due_at: datetime
    completed_at: datetime


@dataclass(frozen=True)
class OrderPaymentTermValueProviderData(BaseContextValues, ProjectContextValueMixin, OrderContextValueMixin):
    pass


@dataclass
class POEmailData:
    to: List[str]
    created_by_id: int
    purchase_order_id: int


@dataclass
class POUploadEmailEventData(POEmailData):
    vendor_order_id: int


class OrderCompletionPercentageEntity(BaseModelV2):
    percentage: decimal.Decimal
    color_code: str


class OrderHeaderDetailEntity(BaseModelV2):
    order_status: TimelineStatusMessageEntity
    order_completion: OrderCompletionPercentageEntity
    expected_start_date: date | None
    expected_due_date: date | None
    order_start_date: date | None
    order_completion_date: date | None
    total_item_count: int
    today_updated_item_count: int
    total_amount: decimal.Decimal
    today_progress: decimal.Decimal
    total_progress: decimal.Decimal
    today_progress_amount: decimal.Decimal
    total_progress_amount: decimal.Decimal
