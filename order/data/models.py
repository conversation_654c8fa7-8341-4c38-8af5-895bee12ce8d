import textwrap

import structlog
from bs4 import BeautifulSoup
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models
from django.db.models import Q, TextChoices, UniqueConstraint
from django.db.utils import IntegrityError
from django.utils.translation import gettext_lazy as _
from rest_framework.settings import api_settings

from approval_request.data.models import ResourceRequestMappingBaseModel
from boq.data.entities import OrderElementExcelEntity
from boq.data.models import BoqElement
from boq.domain.validators import discount_percent_validator
from commentv2.data.models import Comment
from common.choices import CustomElementType, StateChoices
from common.constants import (
    FILE_FIELD_MAX_LENGTH,
    BudgetRateDecimalConfig,
    HsnCodeFieldConfig,
    TaxPercentageFieldConfig,
)
from common.element_base.services import ElementCodeService
from common.helpers import get_upload_path
from common.mixins import FieldHistoryMixin
from common.models import (
    BaseFieldHistory,
    BaseModel,
    CreateDeleteModel,
    CreateModel,
    CreateUpdateDeleteModel,
    UpdateModel,
    UploadDeleteModel,
)
from core.models import (
    Organization,
    OrganizationOrderPaymentTerm,
    ProductionDrawingTag,
    User,
)
from core.organization.data.models import OrganizationDocumentFieldContextConfig
from core.organization.data.querysets import (
    OrganizationDocumentFieldContextConfigDataBaseQuerySet,
)
from core.querysets import (
    OrganizationIncomingOrderNotificationPocQuerySet,
    OrganizationOutgoingOrderNotificationPocQuerySet,
)
from element.data.models import (
    Element,
    ElementBase,
    ElementItemType,
    GuidelineAttachmentBase,
    GuidelineBase,
    PreviewFileBase,
    ProductionDrawingBase,
    ProductionDrawingTagBase,
    QuantityDimensionsModel,
    ServiceChargeModel,
)
from microcontext.domain.constants import MicroContext
from order.config.data.exceptions import DuplicateNonEditablePOTypeException, DuplicatePOTypeNameException
from order.data.choices import OrderEventChoices, OrderStatusChoices, OrderTypeChoices
from order.data.managers import (
    CommonQuerysetManager,
    OrderElementSnapshotManager,
    OrderSnapshotManager,
    VendorOrderAllElementManager,
    VendorOrderElementManager,
    VendorOrderManager,
    VendorPurchaseOrderManager,
)
from order.data.querysets import OrganizationPurchaseOrderTypeQuerySet
from order.domain.constants import OrderTypeEnum
from order.domain.exceptions import POwithVersionOrgAlreadyExists
from order.domain.status_choices import InvoiceStatus, OrderStatus, POStatus
from project.data.models import Project
from ratecontract.data.models import RateContract
from smtp_email.data.models import EmailBase
from task.data.models import Task

logger = structlog.get_logger(__name__)


class BaseOrder(BaseModel):
    class IncomingStatus(TextChoices):
        PENDING = "pending"
        CANCELLED = "cancelled"
        COMPLETED = "completed"
        CLOSED = "closed"

    class OutgoingStatus(TextChoices):
        NOT_SENT = "notsent"
        SENT = "sent"
        CANCELLED = "cancelled"
        COMPLETED = "completed"
        MODIFIED = "modified"
        CLOSED = "closed"

    shipping_address = models.TextField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    due_at = models.DateTimeField(null=True, blank=True)
    payment_term = models.ForeignKey(
        OrganizationOrderPaymentTerm, on_delete=models.RESTRICT, null=True, blank=True, related_name="+"
    )  # payment term for the order when selected from the dropdown , in this case  payment_term_text will be null
    payment_term_title = models.CharField(max_length=200, null=True, blank=True)
    payment_term_text = models.TextField(
        null=True, blank=True
    )  # custom payment term for the order when entered manually , in this case  payment_term will be null
    payment_term_block = models.JSONField(default=list, null=True, blank=True)
    other_term = models.ForeignKey(
        OrganizationOrderPaymentTerm, on_delete=models.RESTRICT, null=True, blank=True, related_name="+"
    )
    other_term_title = models.CharField(max_length=200, null=True, blank=True)
    other_term_block = models.JSONField(default=list, null=True, blank=True)
    terms_and_conditions = models.TextField(null=True, blank=True)
    shipping_address_header = models.TextField(
        null=True, blank=True, default=None
    )  # text used in the shipping address dropdwon as a header data
    invoice_status = models.CharField(max_length=8, choices=InvoiceStatus.choices, default=InvoiceStatus.PENDING)
    work_order_from = models.CharField(max_length=250, null=True, blank=True)  # organization legal entity name
    incoming_status = models.CharField(
        max_length=50, default=None, choices=IncomingStatus.choices, null=True, blank=True
    )  # status of the order received by the org
    outgoing_status = models.CharField(
        max_length=50, default=None, choices=OrderStatusChoices.choices, null=True
    )  # status of the order sent by the org
    issued_at = models.DateTimeField(null=True, blank=True)
    rate_contract = models.ForeignKey(
        RateContract, on_delete=models.RESTRICT, related_name="+", blank=True, null=True, default=None
    )
    is_discounted = models.BooleanField(default=False)
    is_service_charged = models.BooleanField(default=False)
    is_taxed = models.BooleanField(default=False)

    class Meta:
        abstract = True


class VendorOrder(BaseOrder, CreateUpdateDeleteModel, FieldHistoryMixin):
    """
    Represents a vendor order within the system.

    This model stores information related to vendor orders, including details such as order numbers,
    associated vendors, project information, shipping addresses, payment terms, statuses, timestamps,
    and other relevant attributes.

    Parameters:
    -----------
    order_number (IntegerField): The order number used for identification.
    vendor (ForeignKey, optional): The vendor associated with the order.
    project (ForeignKey): The project associated with the order.
    shipping_address (TextField, optional): The shipping address for the order.
    started_at (DateTimeField, optional): The timestamp when the order was started.
    due_at (DateTimeField, optional): The timestamp indicating the due date of the order.
    terms_and_conditions (TextField, optional): Terms and conditions associated with the order.
    payment_term (ForeignKey, optional): The payment term selected from a dropdown.
    payment_term_text (TextField, optional): Custom payment term for the order.
    amount (DecimalField): The total amount of the order.
    po_status (CharField, optional): The status of the Purchase Order (PO).
    po_attached_at (DateTimeField, optional): The timestamp when the PO was attached.
    po_cancelled_at (DateTimeField, optional): The timestamp when the PO was cancelled.
    status (CharField): The status of the order.
    sent_at (DateTimeField, optional): The timestamp when the order was sent.
    cancelled_at (DateTimeField, optional): The timestamp when the order was cancelled.
    sent_by (ForeignKey, optional): The user who sent the order.
    cancelled_by (ForeignKey, optional): The user who cancelled the order.
    shipping_address_header (TextField, optional): Text used as header data in the shipping address dropdown.
    invoice_status (CharField): The status of the associated invoice.
    work_order_from (CharField, optional): Legal entity name of the organization.
    org_from (ForeignKey, optional): The organization sending the order.
    org_to (ForeignKey, optional): The organization receiving the order.
    origin_org (ForeignKey, optional): The organization that created the order.
    incoming_status (CharField, optional): The status of the order received by the organization.
    outgoing_status (CharField, optional): The status of the order sent by the organization.
    parent_order (ForeignKey, optional): The parent order, if applicable.
    excel_sheet (CharField, optional): Name of the associated Excel sheet.
    excel_meta (JSONField, optional): Metadata associated with the Excel sheet.
    issued_at (DateTimeField, optional): The timestamp when the order was issued.
    issued_by (ForeignKey, optional): The user who issued the order.
    completed_at (DateTimeField, optional): The timestamp when the order was completed.
    completed_by (ForeignKey, optional): The user who completed the order.
    version (IntegerField): Version 3 after the rewiring, v=2 just before rewiring, v=1 first version of order.
    rate_contract (ForeignKey, optional): The associated rate contract.

    Attributes:
    ------------
    objects (CommonQuerysetManager): Custom manager for querying common queryset.
    all_objects (Manager): Default manager for querying all objects.

    Properties:
    -----------
    actual_vendor_id (int): The actual vendor ID, derived from either org_to_id or vendor_id.

    Meta:
    ------
    unique_together (tuple): Ensures uniqueness based on project and order number.
    db_table (str): The name of the database table for this model.
    get_latest_by (str): The field used to retrieve the latest record.

    Methods:
    --------
    clean(): Validates the fields of the model and raises validation errors if needed.

    Note:
    -----
    This class extends from CreateUpdateDeleteModel and FieldHistoryMixin and is a key part of
    the vendor order management system.

    """

    project_id: int
    org_from_id: int | None
    org_to_id: int | None
    origin_org_id: int | None
    issued_by_id: int | None
    completed_by_id: int | None

    version = models.IntegerField(
        default=1
    )  # version 3 after the rewiring, v=2 just before rewiring, v=1 first version of order
    excel_sheet = models.CharField(max_length=200, null=True, blank=True)
    excel_meta = models.JSONField(default=dict, null=True, blank=True)
    email_data = models.JSONField(default=None, null=True, blank=True)
    order_type = models.CharField(choices=OrderTypeChoices.choices, max_length=50, default=OrderTypeChoices.REGULAR)
    status = models.CharField(max_length=50, default=OrderStatus.PENDING)
    po_status = models.CharField(max_length=50, null=True, blank=True, default=POStatus.PO_PENDING)
    po_attached_at = models.DateTimeField(null=True, blank=True)
    po_cancelled_at = models.DateTimeField(null=True, blank=True)
    order_number = models.IntegerField(default=0)  # Used in the order number
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="orders")
    org_from = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="orders_from", default=None, null=True, blank=True
    )  # org that sends the order
    org_to = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="orders_to", default=None, null=True, blank=True
    )  # org that receives the order
    origin_org = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="orders_created_by", default=None, null=True
    )  # org that created the order
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="orders_issued",
        null=True,
        blank=True,
    )
    completed_at = models.DateTimeField(null=True, blank=True)
    completed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    incompleted_at = models.DateTimeField(blank=True, null=True)
    incompleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    cancelled_at = models.DateTimeField(null=True, blank=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="orders_cancelled", null=True, blank=True
    )
    poc = models.ForeignKey(User, on_delete=models.RESTRICT, blank=True, null=True, default=None)
    purchase_order_preview = models.FileField(
        null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH
    )
    vendor_info = models.JSONField(null=True, blank=True)
    vendor_info_v2 = models.JSONField(null=True, blank=True)
    saved_total_amount = models.DecimalField(
        max_digits=28, decimal_places=4, default=0
    )  # TODO: remove the default value after release
    to_send_email = models.BooleanField(default=True)
    purchase_order_type = models.ForeignKey(
        "OrganizationPurchaseOrderType",
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )

    objects = VendorOrderManager()
    all_objects = models.Manager()

    @property
    def context(self):
        if self.purchase_order_type.name.lower().replace(" ", "_") == OrderTypeEnum.INSTA_ORDER.value:
            return MicroContext.INSTA_ORDER.value
        else:
            return MicroContext.ORDER.value

    @property
    def actual_vendor_id(self):
        return self.org_to_id

    class Meta:
        constraints = [
            models.UniqueConstraint(
                name="order_project_unique",
                fields=["project_id", "order_number"],
                condition=Q(deleted_at__isnull=True),
            )
        ]
        db_table = "vendor_orders"
        get_latest_by = "created_at"

    def clean(self):
        if self.org_to_id and self.org_to_id == self.org_from_id:
            raise ValidationError("Vendor's organization is same as the organization you are logged in")

        if self.payment_term and self.payment_term.organization.id != self.org_from_id:
            # Same org payment terms should be there
            raise ValidationError({"status": _("Payment term must belong to the client organization")})

        if self.po_status == POStatus.PO_ATTACHED and not self.po_attached_at:
            raise ValidationError({"po_attached_at": _("PO attached date must be set")})

        if self.po_status == POStatus.PO_CANCELLED and not self.po_cancelled_at:
            raise ValidationError({"po_cancelled_at": _("PO cancelled date must be set")})

        if self.outgoing_status == self.OutgoingStatus.SENT:
            # When sending the order , vendor org must be therer
            if not self.org_to_id:
                raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Order to is required")})
            if (
                not all(
                    [
                        self.shipping_address,
                        self.started_at,
                        self.due_at,
                    ]
                )
                and not any([self.issued_at])
                and not any([self.issued_by_id])
            ):
                raise ValidationError(
                    {api_settings.NON_FIELD_ERRORS_KEY: _("Check if all the required fields are filled")}
                )

        if (
            self.outgoing_status == self.OutgoingStatus.CANCELLED
            or self.incoming_status == self.IncomingStatus.CANCELLED
        ) and not self.cancelled_at:
            raise ValidationError({"status": _("Cancelled date must be set")})
        return super().clean()


class VendorOrderGSTDetail(BaseModel):
    vendor_order = models.OneToOneField(VendorOrder, on_delete=models.RESTRICT, related_name="gst_details")
    gst_number = models.CharField(max_length=20, null=True, blank=True)
    gst_state = models.PositiveSmallIntegerField(
        max_length=20, choices=StateChoices.choices, null=True, blank=True, default=None
    )

    class Meta:
        db_table = "vendor_order_gst_details"


class BaseOrderElement(ElementBase, ServiceChargeModel, QuantityDimensionsModel):
    serial_number = models.PositiveIntegerField(null=True, blank=True)
    quantity = models.DecimalField(max_digits=15, default=0, decimal_places=4)
    boq_element_version = models.PositiveBigIntegerField(null=True, blank=True)
    vendor_rate = models.DecimalField(max_digits=20, decimal_places=2)
    status = models.CharField(max_length=50, default=OrderStatus.PENDING)
    client_rate = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    budget_rate = models.DecimalField(
        max_digits=BudgetRateDecimalConfig.MAX_DIGITS, decimal_places=BudgetRateDecimalConfig.DECIMAL_PLACES, default=0
    )
    cancelled_at = models.DateTimeField(null=True, blank=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="orders_elements", null=True, blank=True
    )
    modified_at = models.DateTimeField(null=True, blank=True)
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    # to decide whether custom element is created in order or boq custom element copied through proposal flow
    custom_type = models.CharField(choices=CustomElementType.choices, max_length=5, null=True, blank=True)
    discount_percent = models.DecimalField(
        max_digits=15, default=0, decimal_places=12, validators=[discount_percent_validator]
    )
    tax_percent = models.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        default=0,
    )
    item_type = models.ForeignKey(
        ElementItemType, on_delete=models.RESTRICT, related_name="order_elements", null=True, blank=True
    )
    brand_name = models.CharField(max_length=200, null=True, blank=True, default="")
    hsn_code = models.CharField(max_length=HsnCodeFieldConfig.MAX_LENGTH, null=True, blank=True, default="")
    available_objects = VendorOrderElementManager()
    objects = VendorOrderAllElementManager()

    class Meta:
        abstract = True

    @property
    def base_amount(self):
        return self.vendor_rate * self.quantity

    @property
    def discounted_value(self):
        return self.gross_amount * self.discount_percent / 100

    @property
    def tax_amount(self):
        return self.get_amount_without_tax() * self.tax_percent / 100

    @property
    def final_amount(self):
        return self.get_amount_without_tax() + self.tax_amount

    def get_amount_without_tax(self):
        return self.gross_amount - self.discounted_value

    @property
    def gross_amount(self):
        gross_amt = self.base_amount
        if self.is_service_charge_with_base_amount:
            gross_amt += gross_amt * (self.service_charge_percent / 100)
        elif self.is_service_charge_with_base_amount is False:
            gross_amt = self.service_charge_percent * gross_amt
        else:
            gross_amt = self.base_amount
        return gross_amt

    @property
    def amount(self):
        return self.vendor_rate * self.quantity


class VendorOrderElement(BaseOrderElement):
    PUBLIC_URL_BASE = "order-element"
    """
    Represents an order element within a vendor order.

    This model is used to store information about individual elements within a vendor order,
    including details such as serial number, quantity, associated vendor order, rates, status,
    and other relevant attributes.

    Parameters:
    -----------
    serial_number (PositiveIntegerField, optional): The serial number of the element. Copied directly
        if imported from Bill of Quantity (BOQ) or element library, generated new for custom elements.
    quantity (DecimalField): The quantity of the element.
    vendor_order (ForeignKey, optional): The vendor order to which the element belongs.
    boq_element_version (PositiveBigIntegerField, optional): The version of the BOQ element.
    client (ForeignKey): The client organization associated with the element.
    vendor_rate (DecimalField): The rate of the element from the vendor.
    item_type (ForeignKey, optional): The type of the element, if applicable.
    status (CharField): The status of the element, such as 'MODIFIED', 'CANCELLED', etc.
    boq_element (ForeignKey, optional): The BOQ element linked to this order element.
    el_element (ForeignKey, optional): The EL (Element Library) element linked to this order element.
    project (ForeignKey): The project to which the element belongs.
    client_rate (DecimalField, optional): The rate of the element from the client's perspective.
    cancelled_at (DateTimeField, optional): The timestamp when the element was cancelled.
    cancelled_by (ForeignKey, optional): The user who cancelled the element, if applicable.
    modified_at (DateTimeField, optional): The timestamp when the element was last modified.
    modified_by (ForeignKey, optional): The user who last modified the element.
    custom_type (CharField, optional): Indicates whether the element is custom and its origin type.
    linked_element (OneToOneField, optional): The BOQ element linked to this order element.

    Attributes:
    ------------
    available_objects (VendorOrderElementManager): Custom manager for querying available elements.
    objects (VendorOrderAllElementManager): Custom manager for querying all elements.

    Properties:
    -----------
    amount (DecimalField): The calculated amount for this element (vendor_rate * quantity).

    Meta:
    ------
    db_table (str): The name of the database table for this model.
    get_latest_by (str): The field used to retrieve the latest record.
    constraints (list): Database constraints to ensure uniqueness of certain fields.

    Note:
    -----
    This class extends from ElementBase and is part of the vendor order management system.

    """
    client = models.ForeignKey(Organization, default=0, on_delete=models.RESTRICT, related_name="order_elements")
    boq_element = models.ForeignKey(
        BoqElement, blank=True, null=True, on_delete=models.RESTRICT, related_name="order_elements"
    )
    el_element = models.ForeignKey(
        Element, blank=True, null=True, on_delete=models.RESTRICT, related_name="order_elements"
    )
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="order_elements")
    linked_element = models.OneToOneField(
        BoqElement, blank=True, null=True, on_delete=models.RESTRICT, related_name="order_element"
    )
    vendor_order = models.ForeignKey(
        VendorOrder, blank=True, null=True, on_delete=models.RESTRICT, related_name="order_elements"
    )
    discounted_client_rate = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    def get_description(self) -> str:
        wrapper = textwrap.TextWrapper(width=100)
        word_list = wrapper.wrap(text=BeautifulSoup(self.description, "html.parser").get_text())
        description = "\n".join(word_list)
        return description

    def get_item_code(self) -> str:
        value = ElementCodeService.get_code(
            serial_number=self.serial_number,
            custom_type=self.custom_type,
            code=self.code,
            version=self.boq_element_version,
        )
        return value

    def get_final_amount(self) -> float:
        if self.status != OrderStatus.CANCELLED:
            return self.final_amount
        return 0

    def get_excel_entity(self) -> OrderElementExcelEntity:
        return OrderElementExcelEntity(
            description=self.description,
            item_code=self.get_item_code(),
            length=self.get_length(),
            breadth=self.get_breadth(),
            length_uom=self.get_length_uom(),
            breadth_uom=self.get_breadth_uom(),
        )

    class Meta:
        db_table = "vendor_order_elements"
        get_latest_by = "created_at"
        constraints = [
            models.UniqueConstraint(
                name="element_version_unique",
                fields=[
                    "project_id",
                    "client_id",
                    "serial_number",
                    "boq_element_version",
                    "custom_type",
                ],
                condition=Q(deleted_at__isnull=True),
            )
        ]


class VendorPurchaseOrder(UploadDeleteModel):
    IMAGE = "IMAGE"
    DOC = "DOC"

    TYPE_CHOICES = [(IMAGE, "Image"), (DOC, "Document")]
    vendor_order = models.ForeignKey(
        VendorOrder, on_delete=models.CASCADE, related_name="purchase_orders", null=True, blank=True
    )
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    type = models.CharField(max_length=10, default=DOC, choices=TYPE_CHOICES)
    po_number = models.CharField(max_length=50)
    status = models.CharField(max_length=50, default=POStatus.PO_PENDING)
    name = models.CharField(max_length=100)
    amount = models.DecimalField(max_digits=50, decimal_places=2, default=0)
    po_date = models.DateTimeField(null=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="purchase_orders", null=True, blank=True
    )
    org = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, related_name="purchase_orders", null=True, blank=True
    )
    version = models.PositiveSmallIntegerField(default=1)
    tax_amount = models.DecimalField(max_digits=50, decimal_places=2, default=0)

    objects = VendorPurchaseOrderManager()

    def save(self, *args, **kwargs):
        if kwargs.get("update_fields"):
            if "deleted_at" in kwargs.get("update_fields"):
                is_status_sent = VendorOrder.objects.filter(id=self.id, status=OrderStatus.SENT).exists()
                if is_status_sent:
                    raise ValidationError(
                        {"is_status_sent": _("Purchase Order with order status Sent cannot be deleted")}
                    )
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "vendor_purchase_orders_unique" in str(e):
                logger.info(
                    f"purchase_order org_id {self.org_id}, po_number {self.po_number} , version {self.version} already exists"  # noqa
                )
                raise POwithVersionOrgAlreadyExists(
                    message=f"purchase_order org_id {self.org_id}, po_number {self.po_number} , version {self.version} already exists"  # noqa
                ) from e
            raise e

    class Meta:
        constraints = [
            UniqueConstraint(
                name="vendor_purchase_orders_unique",
                fields=["po_number", "org_id", "version"],
                condition=Q(
                    vendor_order__isnull=False,
                    deleted_at__isnull=True,
                    status__in=[POStatus.PO_ACTIVE, POStatus.PO_ATTACHED],
                ),
            )
        ]
        db_table = "vendor_purchase_orders"
        get_latest_by = "uploaded_at"


class TermsAndConditionAttachment(UploadDeleteModel):
    IMAGE = "IMAGE"
    DOC = "DOC"

    TYPE_CHOICES = [(IMAGE, "Image"), (DOC, "Document")]
    vendor_order = models.ForeignKey(
        VendorOrder, on_delete=models.RESTRICT, related_name="attachments", null=True, blank=True
    )
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=10, default=IMAGE, choices=TYPE_CHOICES)

    objects = CommonQuerysetManager()

    class Meta:
        db_table = "terms_and_conditions_attachments"


class VendorOrderElementPreviewFile(PreviewFileBase):
    element = models.ForeignKey(VendorOrderElement, on_delete=models.CASCADE, related_name="preview_files")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "vendor_order_element_preview_files"


class VendorOrderElementProductionDrawing(ProductionDrawingBase):
    tags = models.ManyToManyField(ProductionDrawingTag, through="VendorOrderElementProductionDrawingTag")
    element = models.ForeignKey(VendorOrderElement, on_delete=models.CASCADE, related_name="production_drawings")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "vendor_order_element_production_drawings"


class VendorOrderElementProductionDrawingTag(ProductionDrawingTagBase):
    element_production_drawing = models.ForeignKey(VendorOrderElementProductionDrawing, on_delete=models.CASCADE)

    @property
    def get_production_drawing_foreign_key(self):
        return "element_production_drawing_id"

    class Meta:
        db_table = "vendor_order_element_production_drawing_tags"


class VendorOrderElementGuideline(GuidelineBase):
    element = models.ForeignKey(VendorOrderElement, on_delete=models.CASCADE, related_name="guidelines")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "vendor_order_element_guidelines"


class VendorOrderElementGuidelineAttachment(GuidelineAttachmentBase):
    guideline = models.ForeignKey(
        VendorOrderElementGuideline,
        null=True,
        blank=True,
        related_name="attachments",
        on_delete=models.CASCADE,
    )

    @property
    def get_guideline_foriegn_key(self):
        return "guideline_id"

    class Meta:
        db_table = "vendor_order_element_guideline_attachments"


class VendorOrderEmail(EmailBase):
    vendor_order = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT)
    created_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "vendor_order_emails"


class OrderReview(CreateModel):
    order_review = models.OneToOneField(VendorOrder, on_delete=models.CASCADE, primary_key=True, related_name="review")
    on_time_delivery = models.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
    quality_of_work = models.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
    cost_effectiveness = models.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
    review = models.TextField()

    class Meta:
        db_table = "order_reviews"


class OrderReviewDocument(CreateModel):
    class FileChoices(TextChoices):
        DOC = "document"
        IMAGE = "image"

    order_review = models.ForeignKey(OrderReview, on_delete=models.RESTRICT, related_name="review_documents")
    url = models.URLField()
    type = models.CharField(max_length=8, choices=FileChoices.choices)

    class Meta:
        db_table = "order_review_docs"


class OrderSnapshot(BaseOrder, CreateDeleteModel):
    order = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT, related_name="snapshots")
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    amount = models.DecimalField(max_digits=50, decimal_places=4, default=0)
    tax_amount = models.DecimalField(max_digits=50, decimal_places=4, default=0)
    gst_number = models.CharField(max_length=20, null=True, blank=True)
    gst_state = models.PositiveSmallIntegerField(
        max_length=20, choices=StateChoices.choices, null=True, blank=True, default=None
    )

    objects = OrderSnapshotManager()

    class Meta:
        db_table = "order_snapshots"


class BaseDeduction(BaseModel):
    name = models.CharField(max_length=100)
    code = models.IntegerField()
    amount = models.DecimalField(max_digits=11, decimal_places=2)
    type = models.CharField(max_length=50)
    type_color_code = models.CharField(max_length=50, null=True, blank=True)
    remark = models.CharField(max_length=100)
    item_reference = models.CharField(max_length=50, null=True, blank=True)
    tax_amount = models.DecimalField(max_digits=11, decimal_places=2, default=0)

    objects = CommonQuerysetManager()
    all_objects = models.Manager()

    class Meta:
        abstract = True


class Deduction(BaseDeduction, CreateUpdateDeleteModel):
    order = models.ForeignKey(VendorOrder, on_delete=models.CASCADE, related_name="deductions")
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="deductions")

    class Meta:
        db_table = "deductions"
        constraints = [
            UniqueConstraint(
                name="deduction_code_unique",
                fields=["project_id", "code"],
            )
        ]

        get_latest_by = "created_at"


class DeductionSnapshot(BaseDeduction, CreateDeleteModel):
    order = models.ForeignKey(OrderSnapshot, on_delete=models.CASCADE, related_name="deduction_snapshots")

    class Meta:
        db_table = "deduction_snapshots"


class BaseDeductionAttachment(BaseModel):
    deduction = models.ForeignKey(Deduction, on_delete=models.CASCADE, related_name="attachments")
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100)

    objects = CommonQuerysetManager()

    class Meta:
        abstract = True


class DeductionAttachment(BaseDeductionAttachment, UploadDeleteModel):
    class Meta:
        db_table = "deduction_attachments"


class DeductionAttachmentSnapshot(BaseDeductionAttachment, UploadDeleteModel):
    deduction = models.ForeignKey(DeductionSnapshot, on_delete=models.CASCADE, related_name="attachments")

    class Meta:
        db_table = "deduction_attachment_snapshots"


class DeductionType(BaseModel):
    name = models.CharField(max_length=50, unique=True)
    color_code = models.CharField(max_length=50, null=True, blank=True)

    def __str__(self, **args):
        return str(self.name)

    class Meta:
        db_table = "deduction_types"


class DeductionRemark(BaseModel):
    remark = models.CharField(max_length=50)
    deduction_type = models.ForeignKey(DeductionType, on_delete=models.RESTRICT, related_name="remarks")

    class Meta:
        db_table = "deduction_remarks"
        constraints = [
            UniqueConstraint(
                name="deduction_type_remark_unique",
                fields=["deduction_type_id", "remark"],
            )
        ]


class OrderStatusHistory(CreateModel):
    order = models.ForeignKey(VendorOrder, on_delete=models.CASCADE, related_name="status_history")
    incoming_status = models.CharField(max_length=50, choices=OrderStatusChoices.choices, null=True, blank=True)
    outgoing_status = models.CharField(max_length=50, choices=OrderStatusChoices.choices)

    class Meta:
        db_table = "order_status_history"


class VendorOrderFieldHistory(BaseFieldHistory):
    order = models.ForeignKey(VendorOrder, on_delete=models.CASCADE, related_name="field_histories")

    class Meta:
        db_table = "vendor_order_field_histories"


class OrganizationIncomingOrderNotificationPoc(CreateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    poc = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="+")

    objects = OrganizationIncomingOrderNotificationPocQuerySet.as_manager()

    class Meta:
        db_table = "organization_incoming_order_notification_poc"


class OrganizationOutgoingOrderNotificationPoc(CreateDeleteModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    poc = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="+")

    objects = OrganizationOutgoingOrderNotificationPocQuerySet.as_manager()

    class Meta:
        db_table = "organization_outgoing_order_notification_poc"


class OrderElementSnapshot(BaseOrderElement):
    order_snapshot = models.ForeignKey(OrderSnapshot, on_delete=models.CASCADE, related_name="order_elements")
    boq_element = models.ForeignKey(BoqElement, blank=True, null=True, on_delete=models.RESTRICT)
    el_element = models.ForeignKey(Element, blank=True, null=True, on_delete=models.RESTRICT)
    item_type = models.ForeignKey(ElementItemType, on_delete=models.RESTRICT, null=True, blank=True)
    cancelled_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, null=True, blank=True)

    available_objects = OrderElementSnapshotManager()

    class Meta:
        db_table = "order_element_snapshots"


class OrderElementPreviewFileSnapshot(PreviewFileBase):
    element = models.ForeignKey(OrderElementSnapshot, on_delete=models.CASCADE, related_name="preview_files")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "order_element_preview_files_snapshots"


class OrderElementProductionDrawingSnapshot(ProductionDrawingBase):
    tags = models.ManyToManyField(ProductionDrawingTag, through="OrderElementProductionDrawingTagSnapshot")
    element = models.ForeignKey(OrderElementSnapshot, on_delete=models.CASCADE, related_name="production_drawings")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "order_element_production_drawings_snapshots"


class OrderElementProductionDrawingTagSnapshot(ProductionDrawingTagBase):
    element_production_drawing = models.ForeignKey(OrderElementProductionDrawingSnapshot, on_delete=models.CASCADE)

    @property
    def get_production_drawing_foreign_key(self):
        return "element_production_drawing_id"

    class Meta:
        db_table = "order_element_production_drawing_tags_snapshots"


class OrderElementGuidelineSnapshot(GuidelineBase):
    element = models.ForeignKey(OrderElementSnapshot, on_delete=models.CASCADE, related_name="guidelines")

    @property
    def get_element_foriegn_key(self):
        return "element_id"

    class Meta:
        db_table = "order_element_guidelines_snapshots"


class OrderElementGuidelineAttachmentSnapshot(GuidelineAttachmentBase):
    guideline = models.ForeignKey(
        OrderElementGuidelineSnapshot,
        null=True,
        blank=True,
        related_name="attachments",
        on_delete=models.CASCADE,
    )

    @property
    def get_guideline_foriegn_key(self):
        return "guideline_id"

    class Meta:
        db_table = "order_element_guideline_attachments_snapshots"


class OrderRequestMapping(ResourceRequestMappingBaseModel):
    resource = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT, related_name="request_mappings")
    event = models.CharField(max_length=50, choices=OrderEventChoices.choices)
    task = models.ForeignKey(Task, on_delete=models.RESTRICT)
    comment = models.ForeignKey(Comment, on_delete=models.RESTRICT)
    snapshot = models.ForeignKey(
        OrderSnapshot, null=True, blank=True, default=None, on_delete=models.RESTRICT, related_name="request_mappings"
    )

    class Meta:
        db_table = "order_request_mappings"


class OrganizationPOConfig(UpdateModel):
    can_upload_po_anytime = models.BooleanField(default=False)
    auto_attach_po = models.BooleanField(default=False)
    organization = models.OneToOneField(Organization, on_delete=models.RESTRICT)


class OrderTextFieldData(CreateUpdateDeleteModel):
    order = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT, related_name="vendor_order_text_field_data")
    data = models.TextField()
    context_config = models.ForeignKey(
        OrganizationDocumentFieldContextConfig, on_delete=models.RESTRICT, related_name="vendor_order_text_field_data"
    )

    objects = OrganizationDocumentFieldContextConfigDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "order_config_text_field_data"
        unique_together = ("order", "context_config")


class SnapshotTextFieldData(CreateUpdateDeleteModel):
    snapshot = models.ForeignKey(
        OrderSnapshot, on_delete=models.RESTRICT, related_name="order_snapshot_text_field_data"
    )
    data = models.TextField()
    context_config = models.ForeignKey(
        OrganizationDocumentFieldContextConfig, on_delete=models.RESTRICT, related_name="order_snapshot_text_field_data"
    )

    objects = OrganizationDocumentFieldContextConfigDataBaseQuerySet.as_manager()

    class Meta:
        db_table = "order_snapshot_config_text_field_data"
        unique_together = ("snapshot", "context_config")


class OrganizationPurchaseOrderType(CreateUpdateDeleteModel):
    CUSTOM_INDEX = "unique_org_lowercase_name"

    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="purchase_order_types")
    name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    is_editable = models.BooleanField(default=True)

    objects = OrganizationPurchaseOrderTypeQuerySet.as_manager()

    def save(self, *args, **kwargs):
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "unique_org_lowercase_name" in str(e):
                raise DuplicatePOTypeNameException("Purchase order type with this name already exists")
            if "unique_org_non_editable" in str(e):
                raise DuplicateNonEditablePOTypeException("Organization already has a non-editable purchase order type")
            raise e

    class Meta:
        db_table = "organization_po_types"
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "is_editable"],
                condition=Q(is_editable=False, deleted_at__isnull=True),
                name="unique_org_non_editable",
            ),
        ]
