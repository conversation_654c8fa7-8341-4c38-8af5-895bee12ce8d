from dataclasses import asdict
from typing import List

import structlog
from django.conf import settings
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Max,
    OuterRef,
    Prefetch,
    Q,
    Subquery,
    Sum,
    Value,
)
from django.db.models.functions import Cast, JSONObject
from django.utils import timezone
from django.utils.module_loading import import_string

from boq.data.choices import BoqElementStatus
from boq.data.models import BoqElement, BoqElementOrderAmountView
from boq.domain.entities import BoqElementInputSyncData
from common.element_base.entities import (
    ElementImportEntity,
    GuidelineAttachmentData,
    GuidelineData,
    PreviewFileData,
    ProductionDrawingData,
)
from common.element_base.services import ElementRelatedCommonServiceNew
from common.entities import ObjectStatus
from common.exceptions import BaseValidationError
from common.signals import order_updated_signal
from core.tnc_config.domain.entities import OtherTnCDataEntity, PaymentTnCDataEntity
from order.data.models import (
    Deduction,
    DeductionAttachment,
    DeductionAttachmentSnapshot,
    DeductionSnapshot,
    OrderElementGuidelineAttachmentSnapshot,
    OrderElementGuidelineSnapshot,
    OrderElementPreviewFileSnapshot,
    OrderElementProductionDrawingSnapshot,
    OrderElementSnapshot,
    OrderRequestMapping,
    OrderSnapshot,
    OrderStatusHistory,
    OrderTextFieldData,
    OrganizationPOConfig,
    SnapshotTextFieldData,
    VendorOrder,
    VendorOrderElement,
    VendorPurchaseOrder,
)
from order.data.selectors.selector_v1 import fetch_order_snapshot_document_config_with_data, purchase_order_max_version
from order.data.selectors.selectors import fetch_vendor_order_with_linked_elements
from order.domain.abstract_repos import (
    OrderAbstractRepo,
    OrderElementSyncAbstractRepo,
    OrderRequestMappingAbstractRepo,
    OrderSnapshotAbstractRepo,
    OrganizationPOConfigAbstractRepo,
    VendorAbstractRepo,
)
from order.domain.constants import OrderEventEnum, OrderStatusEnum
from order.domain.entities.domain_entities import DeductionAttachmentData, DeductionData
from order.domain.entities.entities import (
    DeductionAttachmentSnapshotData,
    DeductionSnapshotData,
    OrderData,
    OrderElementDataForUpdation,
    OrderElementSnapshotData,
    OrderRequestMappingRepoData,
    OrderSnapshotData,
    OrganizationPOConfigEntity,
    PurchaseOrderData,
    VendorInfoData,
)
from order.domain.status_choices import OrderStatus
from order.services.element import OrderElementSnapshotRelatedService, get_element_action
from proposal.data.models import Proposal
from ratecontract.data.models import RateContract, RateContractElement
from rollingbanners.comment_base_service import CommentBaseService

logger = structlog.get_logger(__name__)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class OrderRequestMappingRepo(OrderRequestMappingAbstractRepo):
    def create_mapping(self, data: OrderRequestMappingRepoData):
        OrderRequestMapping.objects.create(
            resource_id=data.order_id,
            request_id=data.request_id,
            task_id=data.task_id,
            comment_id=data.comment_id,
            event=data.event,
            created_by_id=data.user_id,
        )

    def update_mapping_event(self, request_id: int, order_id: int, event: OrderEventEnum):
        OrderRequestMapping.objects.filter(resource_id=order_id, request_id=request_id).update(event=event)

    def update_snaphot_id(self, order_id: int, snapshot_id: int):
        mapping = OrderRequestMapping.objects.filter(resource_id=order_id).order_by("-created_at").first()
        if mapping:
            mapping.snapshot_id = snapshot_id
            mapping.save()


class OrderRepo(OrderAbstractRepo):
    class OrderNotFoundError(BaseValidationError):
        pass

    def update_order_status(self, order_id: int, status: str, user_id: int):
        VendorOrder.objects.filter(id=order_id).update(
            outgoing_status=status, updated_by_id=user_id, updated_at=timezone.now()
        )

    def delete_order(self, order_id: int, user_id: int):
        VendorOrder.objects.filter(id=order_id, outgoing_status=OrderStatusEnum.DRAFT.value).first().soft_delete(
            user_id
        )

    def _create_order_elements_and_deductions(self, order: VendorOrder):
        order_elements = []
        for order_element in order.order_elements.all():
            order_element_entity = OrderElementDataForUpdation(
                id=order_element.pk,
                quantity=order_element.quantity,
                vendor_rate=order_element.client_rate,
                serial_number=order_element.serial_number,
                custom_type=order_element.custom_type,
                boq_element_version=order_element.boq_element_version,
                code=order_element.code,
                status=order_element.status,
                item_type_id=order_element.item_type_id,
                quantity_dimensions=order_element.quantity_dimensions,
                updated_at=order_element.updated_at,
                updated_by_id=order_element.updated_by_id,
                cancelled_at=order_element.cancelled_at,
                cancelled_by_id=order_element.cancelled_by_id,
                budget_rate=order_element.budget_rate,
                tax_percent=order_element.tax_percent,
            )
            order_elements.append(order_element_entity)

        order_deductions = []
        for deduction in order.deductions.all():
            order_deduction_attachments = []
            for attachment in deduction.attachments.all():
                order_deduction_attachments.append(
                    DeductionAttachmentData(
                        id=attachment.id,
                        object_status=ObjectStatus.ADD,
                        file=attachment.file,
                        name=attachment.name,
                    )
                )
            order_deductions.append(
                DeductionData(
                    id=deduction.id,
                    name=deduction.name,
                    amount=deduction.amount,
                    code=deduction.code,
                    type=deduction.type,
                    remark=deduction.remark,
                    type_color_code=deduction.type_color_code,
                    item_reference=deduction.item_reference,
                    object_status=ObjectStatus.ADD,
                    attachments=order_deduction_attachments,
                )
            )
        return order_elements, order_deductions

    def _create_purchase_orders_data(self, order: VendorOrder) -> list[PurchaseOrderData]:
        purchase_orders = order.purchase_orders.all()
        po_data_list = []
        for purchase_order in purchase_orders:
            po_data_list.append(
                PurchaseOrderData(
                    id=purchase_order.id,
                    po_number=purchase_order.po_number,
                    uploaded_by_id=purchase_order.uploaded_by_id,
                )
            )
        return po_data_list

    def get_order(self, order_id: int) -> OrderData:
        # TODO: change old_order_value to order_amount of snapshot
        order = (
            VendorOrder.objects.filter(id=order_id)
            .select_related("project")
            .prefetch_related("order_elements", "deductions")
            .prefetch_related(Prefetch("purchase_orders", queryset=VendorPurchaseOrder.objects.all().available()))
            .annotate(
                request_id=Subquery(
                    OrderRequestMapping.objects.filter(resource_id=OuterRef("id"))
                    .order_by("-created_at")
                    .values("request_id")[:1]
                ),
                event=Subquery(
                    OrderRequestMapping.objects.filter(resource_id=OuterRef("id"))
                    .order_by("-created_at")
                    .values("event")[:1]
                ),
                snapshot_id=Subquery(
                    OrderSnapshot.objects.filter(order_id=OuterRef("id")).order_by("-created_at").values("id")[:1]
                ),
                proposal_id=Subquery(
                    Proposal.objects.filter(order_id=OuterRef("id")).order_by("-created_at").values("id")[:1]
                ),
                old_order_value=Value(0, output_field=DecimalField()),
            )
            .first()
        )

        ################
        order_elements, order_deductions = self._create_order_elements_and_deductions(order)
        purchase_orders_data = self._create_purchase_orders_data(order)
        ################

        return OrderData(
            id=order.id,
            email_data=order.email_data,
            outgoing_status=order.outgoing_status,
            vendor_id=order.actual_vendor_id,
            origin_org_id=order.origin_org_id,
            project_id=order.project_id,
            request_id=order.request_id,
            event=order.event,
            org_from_id=order.org_from_id,
            org_to_id=order.org_to_id,
            shipping_address=order.shipping_address,
            issued_by_id=order.issued_by_id,
            org_from_name=order.org_from.name.title(),
            snapshot_id=order.snapshot_id,
            order_number=f"{order.project.job_id}/{order.order_number}",
            job_id=order.project.job_id,
            proposal_id=order.proposal_id,
            old_order_value=order.old_order_value,
            vendor_name=order.org_to.name.title(),
            order_elements=order_elements,
            order_deductions=order_deductions,
            started_at=order.started_at,
            due_at=order.due_at,
            payment_tnc=PaymentTnCDataEntity(
                id=order.payment_term_id, title=order.payment_term_title, block=order.payment_term_block
            ),
            other_tnc=OtherTnCDataEntity(
                id=order.other_term_id, title=order.other_term_title, block=order.other_term_block
            ),
            purchase_orders_data=purchase_orders_data,
        )

    def trigger_order_updated_signal(self, order_id: int):
        order_updated_signal.send(sender=VendorOrder, order_id=order_id)

    def update_order(self, order_data: OrderData, order_snapshot_data: OrderSnapshotData, user_id: int):
        from order.services.order import OrderBaseService

        # Update order data fields from last approved snapshot order data
        final_order_data = OrderBaseService.get_final_order_data(
            order_data=order_data, order_snapshot_data=order_snapshot_data, user_id=user_id
        )

        # Soft Delete Deduction Attachments
        DeductionAttachment.objects.filter(deduction__order_id=final_order_data.id).update(
            deleted_at=timezone.now(), deleted_by_id=order_snapshot_data.created_by_id
        )

        # Update Order

        VendorOrder.objects.filter(id=final_order_data.id).update(
            outgoing_status=final_order_data.outgoing_status,
            started_at=final_order_data.started_at,
            due_at=final_order_data.due_at,
            shipping_address=final_order_data.shipping_address,
            payment_term_id=final_order_data.payment_tnc.id if final_order_data.payment_tnc else None,
            other_term_id=final_order_data.other_tnc.id if final_order_data.other_tnc else None,
            payment_term_title=final_order_data.payment_tnc.title if final_order_data.payment_tnc else None,
            payment_term_block=final_order_data.payment_tnc.block if final_order_data.payment_tnc else None,
            other_term_title=final_order_data.other_tnc.title if final_order_data.other_tnc else None,
            other_term_block=final_order_data.other_tnc.block if final_order_data.other_tnc else None,
            # terms_and_conditions=final_order_data.terms_and_conditions,
            # payment_term_text=final_order_data.payment_term_text,
            is_service_charged=final_order_data.is_service_charged,
            is_discounted=final_order_data.is_discounted,
            is_taxed=final_order_data.is_taxed,
        )

        # VendorOrderGSTDetail.objects.update_or_create(
        #     vendor_order_id=final_order_data.id,
        #     defaults={"gst_number": order_snapshot_data.gst_number, "gst_state": order_snapshot_data.gst_state},
        # )

        # update order config data
        snapshot_text_data_object_mapping = {}
        for obj in order_snapshot_data.config_text_data:
            snapshot_text_data_object_mapping[obj.context_config_id] = obj

        order_text_data_objects = OrderTextFieldData.objects.filter(order_id=final_order_data.id).available()
        updated_objs = []
        if order_snapshot_data.config_text_data:
            # if data is stored in last approved snapshot then order must be having data
            for obj in order_text_data_objects:
                obj.data = snapshot_text_data_object_mapping[obj.context_config_id].data
                obj.updated_by_id = user_id
                updated_objs.append(obj)
            OrderTextFieldData.objects.bulk_update(objs=updated_objs, fields=["data", "updated_by_id"])
        else:
            # if data is not stored in last approved snapshot then delete the order data
            order_text_data_objects.soft_delete(user_id)

        ######

        # Bulk Create Order Elements

        order_elements = []
        for order_element in final_order_data.order_elements:
            order_element_entity = VendorOrderElement(
                pk=order_element.id,
                vendor_rate=order_element.vendor_rate,
                budget_rate=order_element.budget_rate,
                item_type_id=order_element.item_type_id,
                quantity=order_element.quantity,
                quantity_dimensions=order_element.quantity_dimensions,
                updated_at=order_element.updated_at,
                updated_by_id=order_element.updated_by_id,
                status=order_element.status,
                cancelled_by_id=order_element.cancelled_by_id,
                cancelled_at=order_element.cancelled_at,
                deleted_at=order_element.deleted_at,
                deleted_by_id=order_element.deleted_by_id,
                tax_percent=order_element.tax_percent,
            )
            order_elements.append(order_element_entity)

        VendorOrderElement.objects.bulk_update(
            objs=order_elements,
            fields=[
                "vendor_rate",
                "client_rate",
                "item_type",
                "quantity",
                "quantity_dimensions",
                "updated_at",
                "updated_by",
                "status",
                "cancelled_by_id",
                "cancelled_at",
                "deleted_at",
                "deleted_by_id",
                "tax_percent",
            ],
        )

        ############

        # Bulk Create Deductions and Attachments

        order_deductions = []
        deduction_attachments = []
        for deduction in final_order_data.order_deductions:
            for attachment in deduction.attachments:
                attachment_data = DeductionAttachment(
                    file=attachment.file,
                    name=attachment.name,
                    uploaded_at=attachment.uploaded_at,
                    uploaded_by_id=attachment.uploaded_by_id,
                    deduction_id=deduction.id,
                )
                deduction_attachments.append(attachment_data)
            order_deductions.append(
                Deduction(
                    pk=deduction.id,
                    name=deduction.name,
                    amount=deduction.amount,
                    type=deduction.type,
                    remark=deduction.remark,
                    type_color_code=deduction.type_color_code,
                    item_reference=deduction.item_reference,
                    deleted_at=deduction.deleted_at,
                    deleted_by_id=deduction.deleted_by_id,
                    tax_amount=deduction.tax_amount,
                )
            )

        DeductionAttachment.objects.bulk_create(objs=deduction_attachments)

        Deduction.objects.bulk_update(
            objs=order_deductions,
            fields=[
                "amount",
                "type",
                "remark",
                "type_color_code",
                "item_reference",
                "name",
                "deleted_at",
                "deleted_by_id",
                "tax_amount",
            ],
        )

        self.trigger_order_updated_signal(order_id=final_order_data.id)
        #############

    ###########################

    def _create_order_elements_snapshot_data(cls, order_copy: VendorOrder):
        order_elements_snapshot_data = []
        for order_element in order_copy.order_elements.all():
            preview_files = []
            guidelines = []
            production_drawings = []

            for preview_file in order_element.preview_files.all():
                preview_files.append(
                    PreviewFileData(
                        object_status=ObjectStatus.ADD,
                        name=preview_file.name,
                        file=preview_file.file,
                        type=preview_file.type,
                        is_main=preview_file.is_main,
                    )
                )

            for guideline in order_element.guidelines.all():
                guideline_attachments = []
                for attachment in guideline.attachments.all():
                    guideline_attachments.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            name=attachment.name,
                            file=attachment.file,
                            type=attachment.type,
                        )
                    )
                guidelines.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        name=guideline.name,
                        description=guideline.description,
                        attachments=guideline_attachments,
                    )
                )

            for production_drawing in order_element.production_drawings.all():
                production_drawings.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        name=production_drawing.name,
                        file=production_drawing.file,
                        tags=[tag.id for tag in production_drawing.tags.all()],
                    )
                )

            order_elements_snapshot_data.append(
                OrderElementSnapshotData(
                    boq_element_id=order_element.boq_element_id,
                    el_element_id=order_element.el_element_id,
                    item_type_id=order_element.item_type_id,
                    serial_number=order_element.serial_number,
                    client_rate=order_element.client_rate,
                    budget_rate=order_element.budget_rate,
                    vendor_rate=order_element.vendor_rate,
                    quantity=order_element.quantity,
                    status=order_element.status,
                    cancelled_at=order_element.cancelled_at,
                    modified_at=order_element.modified_at,
                    modified_by_id=order_element.modified_by_id,
                    custom_type=order_element.custom_type,
                    boq_element_version=order_element.boq_element_version,
                    discount_percent=order_element.discount_percent,
                    service_charge_percent=order_element.service_charge_percent,
                    is_service_charge_with_base_amount=order_element.is_service_charge_with_base_amount,
                    quantity_dimensions=order_element.quantity_dimensions,
                    name=order_element.name,
                    uom=order_element.uom,
                    description=order_element.description,
                    category_id=order_element.category_id,
                    code=order_element.code,
                    is_custom=order_element.is_custom,
                    created_by_id=order_element.created_by_id,
                    created_at=order_element.created_at,
                    tax_percent=order_element.tax_percent,
                    preview_files=preview_files,
                    guidelines=guidelines,
                    production_drawings=production_drawings,
                    linked_element_id=order_element.linked_element_id,
                    linked_element_deleted_at=(
                        order_element.linked_element.deleted_at if order_element.linked_element else None
                    ),
                    linked_element_element_status=(
                        order_element.linked_element.element_status if order_element.linked_element else None
                    ),
                    linked_element_progress_percentage=(
                        order_element.linked_element.progress_percentage if order_element.linked_element else None
                    ),
                    progress_percentage=order_element.progress_percentage,
                    brand_name=order_element.brand_name,
                    hsn_code=order_element.hsn_code,
                )
            )

        return order_elements_snapshot_data

    def _create_order_deductions_snapshot_data(cls, order_copy: VendorOrder) -> List[DeductionSnapshotData]:
        order_deductions = []
        for deduction in order_copy.deductions.all():
            order_deduction_attachments = []
            for attachment in deduction.attachments.all():
                order_deduction_attachments.append(
                    DeductionAttachmentSnapshotData(
                        file=attachment.file,
                        name=attachment.name,
                        uploaded_by_id=attachment.uploaded_by_id,
                        uploaded_at=attachment.uploaded_at,
                    )
                )
            order_deductions.append(
                DeductionSnapshotData(
                    name=deduction.name,
                    amount=deduction.amount,
                    code=deduction.code,
                    type=deduction.type,
                    remark=deduction.remark,
                    type_color_code=deduction.type_color_code,
                    item_reference=deduction.item_reference,
                    created_by_id=deduction.created_by_id,
                    created_at=deduction.created_at,
                    attachments=order_deduction_attachments,
                    tax_amount=deduction.tax_amount,
                )
            )

        return order_deductions

    def get_order_data_for_snapshot(self, order_id: int) -> OrderSnapshotData:
        order: VendorOrder = fetch_vendor_order_with_linked_elements(order_id=order_id).first()
        order_deductions = self._create_order_deductions_snapshot_data(order_copy=order)
        order_elements_snapshot_data = self._create_order_elements_snapshot_data(order_copy=order)

        order_snapshot_data = OrderSnapshotData(
            order_id=order.pk,
            started_at=order.started_at,
            due_at=order.due_at,
            payment_tnc=PaymentTnCDataEntity(
                id=order.payment_term_id, title=order.payment_term_title, block=order.payment_term_block
            ),
            other_tnc=OtherTnCDataEntity(
                id=order.other_term_id, title=order.other_term_title, block=order.other_term_block
            ),
            work_order_from=order.work_order_from,
            shipping_address=order.shipping_address,
            shipping_address_header=order.shipping_address_header,
            rate_contract_id=order.rate_contract_id,
            payment_term_id=order.payment_term_id,
            payment_term_text=order.payment_term_text,
            created_by_id=order.created_by_id,
            created_at=order.created_at,
            elements=order_elements_snapshot_data,
            is_discounted=order.is_discounted,
            is_service_charged=order.is_service_charged,
            issued_at=order.issued_at,
            invoice_status=order.invoice_status,
            outgoing_status=order.outgoing_status,
            deductions=order_deductions,
            progress_percentage=0,
            amount=order.elements_final_amount,
            tax_amount=order.elements_tax_amount,
            is_taxed=order.is_taxed,
            # gst_number=order.gst_details.gst_number if hasattr(order, "gst_details") else None,
            # gst_state=order.gst_details.gst_state if hasattr(order, "gst_details") else None,
            config_text_data=order.text_data,
        )

        return order_snapshot_data

    def create_order_status_history(self, order_id: int, status: str, user_id: int):
        status_history = OrderStatusHistory()
        status_history.order_id = order_id
        status_history.outgoing_status = status
        status_history.created_by_id = user_id
        status_history.full_clean()
        return status_history.save()

    def upload_po_preview(self, order_id: int, file: str):
        order = VendorOrder.objects.get(id=order_id)
        if not order:
            raise self.OrderNotFoundError("Order not found")
        order.purchase_order_preview = file
        return order.save(update_fields=["purchase_order_preview"])


class OrderElementSyncRepo(OrderElementSyncAbstractRepo):
    def get_elements_data(self, order_id: int) -> List[BoqElementInputSyncData]:
        order_elements: List[VendorOrderElement] = (
            VendorOrderElement.available_objects.filter(vendor_order_id=order_id)
            .select_related("vendor_order", "project", "linked_element")
            .prefetch_related("preview_files", "guidelines", "guidelines__attachments", "production_drawings")
            .all()
            .order_by("id")
        )
        elements = []

        for count, element in enumerate(order_elements):
            preview_files = element.preview_files.all()
            guidelines = element.guidelines.all()
            production_drawings = element.production_drawings.all()
            preview_file_objs = []
            guideline_objs = []
            production_drawing_objs = []
            old_element_status = element.linked_element.element_status if element.linked_element else None

            if old_element_status == BoqElementStatus.CANCELLED and element.status == old_element_status:
                continue

            for preview_file in preview_files:
                preview_file_data = PreviewFileData(
                    object_status=ObjectStatus.ADD.value,
                    file=preview_file.file,
                    name=preview_file.name,
                    type=preview_file.type,
                    is_main=preview_file.is_main,
                )
                preview_file_objs.append(preview_file_data)

            for guideline in guidelines:
                attachment_list = []
                for attachment in guideline.attachments.all():
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )
                guideline_data = GuidelineData(
                    object_status=ObjectStatus.ADD.value,
                    name=guideline.name,
                    description=guideline.description,
                    attachments=attachment_list,
                )
                guideline_objs.append(guideline_data)

            for production_drawing in production_drawings:
                production_drawing_data = ProductionDrawingData(
                    object_status=ObjectStatus.ADD.value,
                    file=production_drawing.file,
                    name=production_drawing.name,
                    tags=[tag.id for tag in production_drawing.tags.all()],
                )
                production_drawing_objs.append(production_drawing_data)

            elements.append(
                BoqElementInputSyncData(
                    name=element.name,
                    uom=element.uom,
                    description=element.description,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.vendor_rate,
                    el_element_id=element.el_element_id,
                    custom_type=element.custom_type,
                    element_status=(
                        BoqElementStatus.CANCELLED.value
                        if element.status == OrderStatus.CANCELLED
                        else BoqElementStatus.APPROVED.value
                    ),
                    old_element_status=old_element_status,
                    reported_quantity=element.quantity,
                    quantity=element.quantity,
                    position=count + 1,
                    version=element.boq_element_version if element.boq_element_version else 0,
                    boq_id=element.project_id,
                    quantity_dimensions=element.quantity_dimensions,
                    org_id=element.vendor_order.org_to_id,
                    linked_element_id=element.linked_element_id,
                    preview_files=preview_file_objs,
                    guidelines=guideline_objs,
                    production_drawings=production_drawing_objs,
                    client_id=element.client_id,
                    serial_number=element.serial_number,
                    code=element.code,
                    order_element_id=element.pk,
                    order_number=element.vendor_order.order_number,
                    job_id=element.project.job_id,
                    action=get_element_action(
                        linked_element_id=element.linked_element_id,
                        old_element_status=old_element_status,
                        element_status=element.status,
                    ),
                    budget_rate=element.budget_rate,
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )
        return elements

    def update_elements_data(self, elements: List[BoqElementInputSyncData]):
        element_objs = []
        for element in elements:
            element_obj = VendorOrderElement()
            element_obj.id = element.order_element_id
            element_obj.linked_element_id = element.linked_element_id
            element_objs.append(element_obj)
        VendorOrderElement.objects.bulk_update(objs=element_objs, fields=["linked_element_id"])


class OrderSnapshotRepo(OrderSnapshotAbstractRepo):
    def _create_snapshot_config_data(self, order_snapshot: OrderSnapshot, order_snapshot_data: OrderSnapshotData):
        logger.info("Creating snapshot config data", order_snapshot_data=order_snapshot_data)
        created_text_objects = []
        for obj in order_snapshot_data.config_text_data:
            created_text_objects.append(
                SnapshotTextFieldData(
                    snapshot_id=order_snapshot.pk,
                    context_config_id=obj.context_config_id,
                    data=obj.data,
                    created_by_id=order_snapshot_data.created_by_id,
                )
            )
        if created_text_objects:
            SnapshotTextFieldData.objects.bulk_create(objs=created_text_objects)
        logger.info("Snapshot config data created", order_snapshot_data=order_snapshot_data)

    def create_order_snapshot(self, order_snapshot_data: OrderSnapshotData) -> int:
        order_elements = []
        element_entities = []
        # preview_files = []
        # guidelines = []
        # production_drawings = []
        order_deduction_attachments = []

        order_snapshot = OrderSnapshot(
            order_id=order_snapshot_data.order_id,
            shipping_address=order_snapshot_data.shipping_address,
            started_at=order_snapshot_data.started_at,
            due_at=order_snapshot_data.due_at,
            payment_term_id=order_snapshot_data.payment_tnc.id if order_snapshot_data.payment_tnc else None,
            other_term_id=order_snapshot_data.other_tnc.id if order_snapshot_data.other_tnc else None,
            payment_term_title=order_snapshot_data.payment_tnc.title if order_snapshot_data.payment_tnc else None,
            payment_term_block=order_snapshot_data.payment_tnc.block if order_snapshot_data.payment_tnc else None,
            other_term_title=order_snapshot_data.other_tnc.title if order_snapshot_data.other_tnc else None,
            other_term_block=order_snapshot_data.other_tnc.block if order_snapshot_data.other_tnc else None,
            work_order_from=order_snapshot_data.work_order_from,
            rate_contract_id=order_snapshot_data.rate_contract_id,
            payment_term_text=order_snapshot_data.payment_term_text,
            shipping_address_header=order_snapshot_data.shipping_address_header,
            is_discounted=order_snapshot_data.is_discounted,
            is_service_charged=order_snapshot_data.is_service_charged,
            issued_at=order_snapshot_data.issued_at,
            invoice_status=order_snapshot_data.invoice_status,
            outgoing_status=order_snapshot_data.outgoing_status,
            created_by_id=order_snapshot_data.created_by_id,
            created_at=order_snapshot_data.created_at,
            progress_percentage=order_snapshot_data.progress_percentage,
            amount=order_snapshot_data.amount,
            tax_amount=order_snapshot_data.tax_amount,
            is_taxed=order_snapshot_data.is_taxed,
            # gst_number=order_snapshot_data.gst_number,
            # gst_state=order_snapshot_data.gst_state,
        )
        order_snapshot.save()

        self._create_snapshot_config_data(order_snapshot=order_snapshot, order_snapshot_data=order_snapshot_data)

        for deduction in order_snapshot_data.deductions:
            deduction_data = DeductionSnapshot(
                name=deduction.name,
                amount=deduction.amount,
                type=deduction.type,
                remark=deduction.remark,
                type_color_code=deduction.type_color_code,
                item_reference=deduction.item_reference,
                order_id=order_snapshot.id,
                created_by_id=deduction.created_by_id,
                created_at=deduction.created_at,
                code=deduction.code,
                tax_amount=deduction.tax_amount,
            )
            # order_deductions.append(deduction_data)
            # TODO: Create mapping for deduction attachments
            deduction_data.save()

            order_deduction_attachments = []
            for attachment in deduction.attachments:
                attachment_data = DeductionAttachmentSnapshot(
                    file=attachment.file,
                    name=attachment.name,
                    deduction_id=deduction_data.id,
                    uploaded_by_id=attachment.uploaded_by_id,
                    uploaded_at=attachment.uploaded_at,
                )
                order_deduction_attachments.append(attachment_data)

            # DeductionSnapshot.objects.bulk_create(objs=order_deductions)
            DeductionAttachmentSnapshot.objects.bulk_create(objs=order_deduction_attachments)

        for order_element in order_snapshot_data.elements:
            order_element_preview_files = order_element.preview_files
            preview_files = []
            guidelines = []
            production_drawings = []
            for element in order_element_preview_files:
                preview_file_data = PreviewFileData(
                    object_status=ObjectStatus.ADD.value,
                    file=element.file,
                    name=element.name,
                    type=element.type,
                    is_main=element.is_main,
                )
                preview_files.append(preview_file_data)

            order_element_guidelines = order_element.guidelines
            for element in order_element_guidelines:
                attachment_list = []
                for attachment in element.attachments:
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )
                guideline_data = GuidelineData(
                    object_status=ObjectStatus.ADD.value,
                    name=element.name,
                    description=element.description,
                    attachments=attachment_list,
                )
                guidelines.append(guideline_data)

            order_element_production_drawings = order_element.production_drawings
            for element in order_element_production_drawings:
                production_drawing_data = ProductionDrawingData(
                    object_status=ObjectStatus.ADD.value,
                    file=element.file,
                    name=element.name,
                    tags=element.tags,
                )
                production_drawings.append(production_drawing_data)

            element_data = ElementImportEntity(
                id=None,
                object_status=ObjectStatus.ADD.value,
                preview_files=preview_files,
                guidelines=guidelines,
                production_drawings=production_drawings,
            )
            element_entities.append(element_data)

            order_element_data = OrderElementSnapshot(
                boq_element_id=order_element.boq_element_id,
                el_element_id=order_element.el_element_id,
                item_type_id=order_element.item_type_id,
                order_snapshot_id=order_snapshot.pk,
                serial_number=order_element.serial_number,
                client_rate=order_element.client_rate,
                budget_rate=order_element.budget_rate,
                vendor_rate=order_element.vendor_rate,
                quantity=order_element.quantity,
                status=order_element.status,
                cancelled_at=order_element.cancelled_at,
                modified_at=order_element.modified_at,
                modified_by_id=order_element.modified_by_id,
                custom_type=order_element.custom_type,
                boq_element_version=order_element.boq_element_version,
                discount_percent=order_element.discount_percent,
                service_charge_percent=order_element.service_charge_percent,
                is_service_charge_with_base_amount=order_element.is_service_charge_with_base_amount,
                tax_percent=order_element.tax_percent,
                quantity_dimensions=order_element.quantity_dimensions,
                name=order_element.name,
                uom=order_element.uom,
                description=order_element.description,
                category_id=order_element.category_id,
                code=order_element.code,
                is_custom=order_element.is_custom,
                created_by_id=order_element.created_by_id,
                created_at=order_element.created_at,
                brand_name=order_element.brand_name,
                hsn_code=order_element.hsn_code,
            )
            order_elements.append(order_element_data)

        order_elements = OrderElementSnapshot.objects.bulk_create(objs=order_elements)
        ElementRelatedCommonServiceNew.process_bulk_create(
            service=OrderElementSnapshotRelatedService,
            elements=order_elements,
            data=element_entities,
            created_by_id=order_snapshot_data.created_by_id,
        )
        return order_snapshot.pk

    def get_vendor_order_snapshots(self, order_id: int):
        return self.get_order_snapshots(order_id=order_id)

    def get_client_order_snapshots(self, order_id: int):
        order_snapshots = (
            self.get_order_snapshots(order_id=order_id)
            .filter(outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value])
            .all()
        )
        # if Order Status is Pending Approval, then remove the Last Approved Snapshot Data
        if order_snapshots[0].order_outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value:
            return order_snapshots[1:]
        return order_snapshots

    def get_order_snapshots(self, order_id: int):
        order_snapshots = (
            OrderSnapshot.objects.filter(order_id=order_id)
            # .annotate_elements_final_amount()
            .annotate(order_number=F("order__order_number"))
            .annotate(job_id=F("order__project__job_id"))
            .annotate(po_status=F("order__po_status"))
            .annotate(
                cancelled_elements_count=Count(
                    "order_elements",
                    distinct=True,
                    filter=Q(order_elements__status=OrderStatus.CANCELLED, order_elements__deleted_at__isnull=True),
                ),
                not_cancelled_elements_count=Count(
                    "order_elements",
                    distinct=True,
                    filter=Q(
                        order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                        order_elements__deleted_at__isnull=True,
                    ),
                ),
                deduction_amount=Subquery(
                    OrderSnapshot.objects.filter(id=OuterRef("id"))
                    .annotate(deduction_amount=Sum("deduction_snapshots__amount"))
                    .values("deduction_amount")[:1]
                ),
                closed_at=Subquery(
                    VendorOrder.objects.filter(id=OuterRef("order_id"))
                    .annotate(
                        closed_at=Max(
                            "status_history__created_at",
                            filter=Q(status_history__outgoing_status=VendorOrder.OutgoingStatus.CLOSED),
                        )
                    )
                    .values("closed_at")[:1]
                ),
                # order_amount=Coalesce(
                #     F("elements_final_amount") - F("deduction_amount"),
                #     Value(0),
                #     output_field=DecimalField(),
                # ),
                # order_tax_amount=Coalesce(
                #     F("elements_tax_amount") - F("deduction_tax_amount"),
                #     Value(0),
                #     output_field=DecimalField(),
                # ),
                order_outgoing_status=F("order__outgoing_status"),
            )
            .order_by("-created_at")
            .all()
        )
        return order_snapshots

    def get_order_snapshot_details(self, order_snapshot_id: int, org_id: int):
        preview_file_subquery = (
            OrderElementPreviewFileSnapshot.objects.filter(element_id=OuterRef("pk"), is_main=True)
            .available()
            .order_by("-is_main", "uploaded_at")
        )
        order_request_mappings_subquery = OrderRequestMapping.objects.filter(snapshot_id=OuterRef("pk")).annotate(
            request_id_and_task_id=JSONObject(request_id="request_id", task_id="task_id")
        )
        vendor_order_amount_subquery = (
            BoqElementOrderAmountView.objects.filter(
                id=OuterRef("boq_element_id"), org_from_id=OuterRef("boq_element__organization_id")
            )
            .values("id")
            .annotate(view_draft_amount=Sum("draft_amount"), view_total_amount=Sum("total_amount"))
        )

        boq_element_final_amount_subquery = BoqElement.objects.filter(
            id=OuterRef("boq_element_id")
        ).annotate_final_amount()

        boq_final_element_amount_subquery = Subquery(
            boq_element_final_amount_subquery.values("final_element_amount")[:1]
        )
        total_element_amount_subquery = Subquery(vendor_order_amount_subquery.values("total_amount")[:1])
        draft_amount_subquery = Subquery(vendor_order_amount_subquery.values("draft_amount")[:1])
        total_quantity_subquery = Subquery(vendor_order_amount_subquery.values("total_quantity")[:1])
        max_sent_order_rate_subquery = Subquery(vendor_order_amount_subquery.values("max_sent_order_rate")[:1])
        max_draft_order_rate_subquery = Subquery(vendor_order_amount_subquery.values("max_draft_order_rate")[:1])

        order_snapshot: OrderSnapshot = (
            OrderSnapshot.objects.filter(id=order_snapshot_id)
            .select_related("created_by", "order", "order__project", "order__org_to")
            .annotate(job_id=F("order__project__job_id"))
            .annotate(
                order_request_mapping=Subquery(order_request_mappings_subquery.values("request_id_and_task_id")[:1])
            )
            .annotate(request_id=Cast(F("order_request_mapping__request_id"), output_field=IntegerField()))
            .annotate(task_id=Cast(F("order_request_mapping__task_id"), output_field=IntegerField()))
            .annotate(order_number=F("order__order_number"), job_id=F("order__project__job_id"))
            # .annotate_request_data()
            .prefetch_related(Prefetch("rate_contract", RateContract.objects.available().annotate_element_count()))
            # .annotate(
            #     order_amount=Coalesce(
            #         Sum(
            #             F("elements_final_amount"),
            #             filter=Q(
            #                 order_elements__deleted_at__isnull=True,
            #                 order_elements__status__in=[
            #                     OrderStatus.PENDING,
            #                     OrderStatus.SENT,
            #                     OrderStatus.MODIFIED,
            #                     OrderStatus.COMPLETED,
            #                 ],
            #             ),
            #         ),
            #         Value(0),
            #         output_field=DecimalField(),
            #     ),
            # )
            .annotate(po_status=Subquery(VendorOrder.objects.filter(id=OuterRef("order_id")).values("po_status")[:1]))
            .prefetch_related(
                Prefetch(
                    "order_elements",
                    OrderElementSnapshot.available_objects.annotate_elements_final_amount()
                    .all()
                    .annotate(rate_contract_id=F("order_snapshot__rate_contract_id"))
                    .annotate(progress_percentage=F("order_snapshot__progress_percentage"))
                    .annotate(
                        comment_count=CommentHelperService.get_count(
                            context_group=CommentHelperService.GROUPED_CONTEXT.ORDER_ITEM.name, org_id=org_id
                        ),
                        preview_file=Subquery(preview_file_subquery.values("file")[:1]),
                    )
                    .annotate(rate_contract_id=F("order_snapshot__rate_contract_id"))
                    .annotate(
                        rc_rate=Subquery(
                            RateContractElement.objects.filter(
                                rate_contract_id=OuterRef("rate_contract_id"), element_id=OuterRef("el_element_id")
                            ).values("rate")[:1],
                            output_field=DecimalField(max_digits=11, decimal_places=2),
                        )
                    )
                    .annotate_is_order_amount_greater_than_boq_amount(
                        total_element_amount_subquery=total_element_amount_subquery,
                        draft_amount_subquery=draft_amount_subquery,
                        boq_final_element_amount_subquery=boq_final_element_amount_subquery,
                    )
                    .annotate_is_error_found_in_order_element(
                        total_element_amount_subquery=total_element_amount_subquery,
                        total_quantity_subquery=total_quantity_subquery,
                        max_sent_order_rate_subquery=max_sent_order_rate_subquery,
                        max_draft_order_rate_subquery=max_draft_order_rate_subquery,
                        boq_final_element_amount_subquery=boq_final_element_amount_subquery,
                    )
                    .order_by("created_at"),
                ),
                "order__attachments",
                Prefetch("deduction_snapshots", DeductionSnapshot.objects.select_related("created_by").all()),
                Prefetch(
                    "deduction_snapshots__attachments",
                    DeductionAttachmentSnapshot.objects.select_related("uploaded_by").all(),
                ),
            )
            .first()
        )

        if not order_snapshot:
            raise self.OrderSnapshotNotFoundException("Order snapshot not found")

        rc_count = (
            RateContract.objects.active()
            .filter(
                organization_id=org_id,
                client_id__in=[order_snapshot.order.project.client_id, org_id],
            )
            .count()
        )

        sorted_purchase_orders = purchase_order_max_version(vendor_order_id=order_snapshot.order_id)
        setattr(order_snapshot, "document_config", [])
        if order_snapshot.order.org_to:
            document_config = fetch_order_snapshot_document_config_with_data(
                country_id=order_snapshot.order.org_to.country_id, snapshot_id=order_snapshot.pk
            )
            setattr(order_snapshot, "document_config", document_config)
        setattr(order_snapshot, "sorted_purchase_orders", sorted_purchase_orders)
        setattr(order_snapshot, "rc_count", rc_count)
        return order_snapshot

    def get_last_approved_order_snapshot_data(self, order_id: int):
        last_approved_order_snapshot = (
            OrderSnapshot.objects.filter(
                order_id=order_id, outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value]
            )
            .prefetch_related(
                Prefetch(
                    "order_snapshot_text_field_data",
                    queryset=SnapshotTextFieldData.objects.available(),
                    to_attr="text_data",
                )
            )
            .order_by("-created_at")
            .first()
        )
        order_snapshot_elements = []
        for snapshot_element in last_approved_order_snapshot.order_elements.all():
            order_snapshot_elements.append(
                OrderElementSnapshotData(
                    serial_number=snapshot_element.serial_number,
                    client_rate=snapshot_element.client_rate,
                    budget_rate=snapshot_element.budget_rate,
                    vendor_rate=snapshot_element.vendor_rate,
                    quantity=snapshot_element.quantity,
                    status=snapshot_element.status,
                    cancelled_at=snapshot_element.cancelled_at,
                    modified_at=snapshot_element.modified_at,
                    modified_by_id=snapshot_element.modified_by_id,
                    custom_type=snapshot_element.custom_type,
                    boq_element_version=snapshot_element.boq_element_version,
                    discount_percent=snapshot_element.discount_percent,
                    service_charge_percent=snapshot_element.service_charge_percent,
                    is_service_charge_with_base_amount=snapshot_element.is_service_charge_with_base_amount,
                    quantity_dimensions=snapshot_element.quantity_dimensions,
                    name=snapshot_element.name,
                    uom=snapshot_element.uom,
                    description=snapshot_element.description,
                    category_id=snapshot_element.category_id,
                    code=snapshot_element.code,
                    item_type_id=snapshot_element.item_type_id,
                    is_custom=snapshot_element.is_custom,
                    created_by_id=snapshot_element.created_by_id,
                    created_at=snapshot_element.created_at,
                    updated_at=snapshot_element.updated_at,
                    updated_by_id=snapshot_element.updated_by_id,
                    tax_percent=snapshot_element.tax_percent,
                    brand_name=snapshot_element.brand_name,
                    hsn_code=snapshot_element.hsn_code,
                )
            )

        order_deductions = []
        for deduction in last_approved_order_snapshot.deduction_snapshots.all():
            order_deduction_attachments = []
            for attachment in deduction.attachments.all():
                order_deduction_attachments.append(
                    DeductionAttachmentSnapshotData(
                        file=attachment.file,
                        name=attachment.name,
                        uploaded_by_id=attachment.uploaded_by_id,
                        uploaded_at=attachment.uploaded_at,
                    )
                )
            order_deductions.append(
                DeductionSnapshotData(
                    name=deduction.name,
                    amount=deduction.amount,
                    code=deduction.code,
                    type=deduction.type,
                    remark=deduction.remark,
                    type_color_code=deduction.type_color_code,
                    item_reference=deduction.item_reference,
                    created_by_id=deduction.created_by_id,
                    created_at=deduction.created_at,
                    attachments=order_deduction_attachments,
                    tax_amount=deduction.tax_amount,
                )
            )

        order_snapshot_data = OrderSnapshotData(
            order_id=last_approved_order_snapshot.order_id,
            started_at=last_approved_order_snapshot.started_at,
            due_at=last_approved_order_snapshot.due_at,
            work_order_from=last_approved_order_snapshot.work_order_from,
            shipping_address=last_approved_order_snapshot.shipping_address,
            shipping_address_header=last_approved_order_snapshot.shipping_address_header,
            elements=order_snapshot_elements,
            payment_term_text=last_approved_order_snapshot.payment_term_text,
            progress_percentage=last_approved_order_snapshot.progress_percentage,
            is_discounted=last_approved_order_snapshot.is_discounted,
            is_service_charged=last_approved_order_snapshot.is_service_charged,
            amount=last_approved_order_snapshot.amount,
            issued_at=last_approved_order_snapshot.issued_at,
            invoice_status=last_approved_order_snapshot.invoice_status,
            outgoing_status=last_approved_order_snapshot.outgoing_status,
            created_at=last_approved_order_snapshot.created_at,
            rate_contract_id=last_approved_order_snapshot.rate_contract_id,
            payment_term_id=last_approved_order_snapshot.payment_term_id,
            deductions=order_deductions,
            created_by_id=last_approved_order_snapshot.created_by_id,
            tax_amount=last_approved_order_snapshot.tax_amount,
            is_taxed=last_approved_order_snapshot.is_taxed,
            config_text_data=last_approved_order_snapshot.text_data,
        )
        return order_snapshot_data

    def get_order_element_snapshot_details(self, element_id: int, org_id: int):
        return (
            OrderElementSnapshot.available_objects.filter(id=element_id)
            .annotate(preview_file_count=Count("preview_files"))
            .prefetch_related(
                Prefetch(
                    "preview_files",
                    queryset=OrderElementPreviewFileSnapshot.objects.available(),
                ),
                Prefetch(
                    "production_drawings",
                    queryset=OrderElementProductionDrawingSnapshot.objects.prefetch_related("tags").available(),
                ),
                Prefetch("guidelines", queryset=OrderElementGuidelineSnapshot.objects.available()),
                Prefetch(
                    "guidelines__attachments", queryset=OrderElementGuidelineAttachmentSnapshot.objects.available()
                ),
            )
            .annotate_comment_count(org_id=org_id)
            .first()
        )

    def get_elements_data(self, order_id: int) -> List[BoqElementInputSyncData]:
        last_approved_order_snapshot = (
            OrderSnapshot.objects.filter(
                order_id=order_id, outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value]
            )
            .select_related("order")
            .prefetch_related("order_elements", "order_elements__preview_files", "order_elements__guidelines")
            .order_by("-created_at")
            .first()
        )
        if not last_approved_order_snapshot:
            return []
        elements = []
        for count, element in enumerate(last_approved_order_snapshot.order_elements.all()):
            preview_files = element.preview_files.all()
            guidelines = element.guidelines.all()
            production_drawings = element.production_drawings.all()
            preview_file_objs = []
            guideline_objs = []
            production_drawing_objs = []

            for preview_file in preview_files:
                preview_file_data = PreviewFileData(
                    object_status=ObjectStatus.ADD.value,
                    file=preview_file.file,
                    name=preview_file.name,
                    type=preview_file.type,
                    is_main=preview_file.is_main,
                )
                preview_file_objs.append(preview_file_data)

            for guideline in guidelines:
                attachment_list = []
                for attachment in guideline.attachments.all():
                    attachment_list.append(
                        GuidelineAttachmentData(
                            object_status=ObjectStatus.ADD,
                            file=attachment.file,
                            name=attachment.name,
                            type=attachment.type,
                        )
                    )
                guideline_data = GuidelineData(
                    object_status=ObjectStatus.ADD.value,
                    name=guideline.name,
                    description=guideline.description,
                    attachments=attachment_list,
                )
                guideline_objs.append(guideline_data)

            for production_drawing in production_drawings:
                production_drawing_data = ProductionDrawingData(
                    object_status=ObjectStatus.ADD.value,
                    file=production_drawing.file,
                    name=production_drawing.name,
                    tags=[tag.id for tag in production_drawing.tags.all()],
                )
                production_drawing_objs.append(production_drawing_data)

            # old_element_status = element.linked_element.element_status if element.linked_element_id else None
            elements.append(
                BoqElementInputSyncData(
                    name=element.name,
                    uom=element.uom,
                    description=element.description,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=element.vendor_rate,
                    el_element_id=element.el_element_id,
                    custom_type=element.custom_type,
                    element_status=(
                        BoqElementStatus.CANCELLED.value
                        if element.status == OrderStatus.CANCELLED
                        else BoqElementStatus.APPROVED.value
                    ),
                    old_element_status=None,
                    reported_quantity=element.quantity,
                    quantity=element.quantity,
                    position=count + 1,
                    version=element.boq_element_version if element.boq_element_version else 0,
                    boq_id=element.order_snapshot.order.project_id,
                    quantity_dimensions=element.quantity_dimensions,
                    org_id=element.order_snapshot.order.org_to_id,
                    linked_element_id=None,
                    preview_files=preview_file_objs,
                    guidelines=guideline_objs,
                    production_drawings=production_drawing_objs,
                    client_id=element.order_snapshot.order.org_from_id,
                    serial_number=element.serial_number,
                    code=element.code,
                    order_element_id=element.pk,
                    order_number=element.order_snapshot.order.order_number,
                    job_id=element.order_snapshot.order.project.job_id,
                    action=get_element_action(
                        linked_element_id=None,
                        old_element_status=None,
                        element_status=element.status,
                    ),
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )
        return elements


class OrganizationPOConfigRepo(OrganizationPOConfigAbstractRepo):
    class OrgPoConfigNotFoudError(BaseValidationError):
        pass

    def get_po_config(self, org_id: int) -> OrganizationPOConfig:
        org_po_config = OrganizationPOConfig.objects.filter(organization_id=org_id).first()
        if not org_po_config:
            return OrganizationPOConfigEntity(can_upload_po_anytime=False, auto_attach_po=False)
        return OrganizationPOConfigEntity(
            can_upload_po_anytime=org_po_config.can_upload_po_anytime, auto_attach_po=org_po_config.auto_attach_po
        )

    def update_po_config(self, upload_po_anytime: bool, auto_attach_po: bool, user_id: int, org_id: int) -> None:
        org_po_config = OrganizationPOConfig.objects.filter(organization_id=org_id).all()
        org_po_config.update_or_create(
            organization_id=org_id,
            defaults={
                "can_upload_po_anytime": upload_po_anytime,
                "auto_attach_po": auto_attach_po,
                "updated_by_id": user_id,
                "updated_at": timezone.now(),
            },
        )


class VendorRepo(VendorAbstractRepo):
    class VendorOrderNotFound(BaseValidationError):
        pass

    def update_vendor_info(self, order_id: int, vendor_info_data: VendorInfoData):
        vendor_order = VendorOrder.objects.filter(id=order_id).first()
        if not vendor_order:
            raise self.VendorOrderNotFound("Vendor Order not found")
        vendor_order.vendor_info = asdict(vendor_info_data)
        vendor_order.save()
