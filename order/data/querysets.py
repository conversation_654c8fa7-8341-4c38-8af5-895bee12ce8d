from django.conf import settings
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    IntegerField,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, Concat, JSONObject
from django.utils.module_loading import import_string

from common.choices import VendorStatusChoices
from common.querysets import AvailableQuerySetMixin
from core.models import FromToOrgMapping
from order.domain.constants import OrderStatusEnum
from order.domain.status_choices import OrderStatus
from payment_request.data.choices import PaymentRequestStatus
from rollingbanners.comment_base_service import CommentBaseService

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class OrderElementQuerySetMixin(QuerySet, AvailableQuerySetMixin):
    # TODO: Remove this after APP release
    def annotate_is_order_amount_greater_than_boq_amount(
        self, total_element_amount_subquery, draft_amount_subquery, boq_final_element_amount_subquery
    ):
        return self.annotate(
            boq_final_element_amount=boq_final_element_amount_subquery,
            total_element_amount=total_element_amount_subquery,
            draft_amount=draft_amount_subquery,
            is_draft_order_amount_greater_than_boq_amount=Case(
                When(
                    boq_final_element_amount__lt=F("draft_amount"),
                    then=Value(True),
                ),
                default=Value(False),
            ),
            is_remaining_order_amount_greater_than_boq_amount=Case(
                When(
                    boq_final_element_amount__lt=F("total_element_amount") - F("draft_amount"),
                    then=Value(True),
                ),
                default=Value(False),
            ),
        )

    def annotate_is_error_found_in_order_element(
        self,
        total_element_amount_subquery,
        total_quantity_subquery,
        max_sent_order_rate_subquery,
        max_draft_order_rate_subquery,
        boq_final_element_amount_subquery,
    ):
        return self.annotate(
            boq_final_element_amount=boq_final_element_amount_subquery,
            total_element_amount=total_element_amount_subquery,
            total_quantity=total_quantity_subquery,
            max_sent_order_rate=max_sent_order_rate_subquery,
            max_draft_order_rate=max_draft_order_rate_subquery,
            is_error_found=Case(
                When(
                    Q(boq_final_element_amount__lt=F("total_element_amount"))
                    | Q(boq_element__quantity__lt=F("total_quantity"))
                    | Q(boq_element__client_rate__lt=F("max_sent_order_rate"))
                    | Q(boq_element__client_rate__lt=F("max_draft_order_rate")),
                    then=Value(True),
                ),
                default=Value(False),
            ),
        )


class VendorOrderElementQuerySet(OrderElementQuerySetMixin):
    def annotate_preview_file_url(self) -> QuerySet:
        from order.data.models import VendorOrderElementPreviewFile

        preview_file_subquery = (
            VendorOrderElementPreviewFile.objects.filter(element_id=OuterRef("pk"), is_main=True)
            .available()
            .order_by("-is_main", "uploaded_at")
        )
        return self.annotate(preview_file_url=Subquery(preview_file_subquery.values("file")[:1]))

    def prefetch_all_preview_files(self):
        from order.data.models import VendorOrderElementPreviewFile

        prefetch = Prefetch(
            "preview_files",
            queryset=VendorOrderElementPreviewFile.objects.available().order_by("-is_main", "uploaded_at"),
        )
        return self.prefetch_related(prefetch)

    def prefetch_all_production_drawings(self):
        from order.data.models import VendorOrderElementProductionDrawing

        prefetch = Prefetch(
            "production_drawings",
            queryset=VendorOrderElementProductionDrawing.objects.available()
            .prefetch_related("tags")
            .order_by("-uploaded_at"),
        )
        return self.prefetch_related(prefetch)

    def prefetch_all_element_guidelines(self):
        from order.data.models import (
            VendorOrderElementGuideline,
            VendorOrderElementGuidelineAttachment,
        )

        prefetch = Prefetch(
            "guidelines",
            queryset=VendorOrderElementGuideline.objects.available()
            .prefetch_related(Prefetch("attachments", VendorOrderElementGuidelineAttachment.objects.available()))
            .order_by("-created_at"),
        )
        return self.prefetch_related(prefetch)

    def annotate_comment_count(self, org_id: int):
        return self.annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER_ITEM.name, org_id=org_id
            )
        )


class OrderElementSnapshotQueryset(OrderElementQuerySetMixin):
    def annotate_preview_file_url(self) -> QuerySet:
        from order.data.models import OrderElementPreviewFileSnapshot

        preview_file_subquery = (
            OrderElementPreviewFileSnapshot.objects.filter(element_id=OuterRef("pk"), is_main=True)
            .available()
            .order_by("uploaded_at")
        )
        return self.annotate(preview_file_url=Subquery(preview_file_subquery.values("file")[:1]))

    def prefetch_all_preview_files(self):
        from order.data.models import OrderElementPreviewFileSnapshot

        prefetch = Prefetch(
            "preview_files",
            queryset=OrderElementPreviewFileSnapshot.objects.available().order_by("-is_main", "uploaded_at"),
        )
        return self.prefetch_related(prefetch)

    def prefetch_all_production_drawings(self):
        from order.data.models import OrderElementProductionDrawingSnapshot

        prefetch = Prefetch(
            "production_drawings",
            queryset=OrderElementProductionDrawingSnapshot.objects.available()
            .prefetch_related("tags")
            .order_by("-uploaded_at"),
        )
        return self.prefetch_related(prefetch)

    def prefetch_all_element_guidelines(self):
        from order.data.models import (
            OrderElementGuidelineAttachmentSnapshot,
            OrderElementGuidelineSnapshot,
        )

        prefetch = Prefetch(
            "guidelines",
            queryset=OrderElementGuidelineSnapshot.objects.available()
            .prefetch_related(Prefetch("attachments", OrderElementGuidelineAttachmentSnapshot.objects.available()))
            .order_by("-created_at"),
        )
        return self.prefetch_related(prefetch)

    def annotate_comment_count(self, org_id: int):
        return self.annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER_ITEM.name, org_id=org_id
            )
        )


class VendorPurchaseOrderQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_pending_payment_request_amount(self):
        return self.annotate(
            pending_payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    payment_requests__status__in=[
                        PaymentRequestStatus.HOLD.value,
                        PaymentRequestStatus.PENDING.value,
                        PaymentRequestStatus.REQUEST_MISCONFIGURED.value,
                    ]
                ),
            )
        )

    def annotate_pending_payment_gst_amount(self):
        return self.annotate(
            pending_payment_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    payment_requests__status__in=[
                        PaymentRequestStatus.HOLD.value,
                        PaymentRequestStatus.PENDING.value,
                        PaymentRequestStatus.REQUEST_MISCONFIGURED.value,
                    ]
                ),
            )
        )

    def annotate_approved_payment_request_amount(self):
        return self.annotate(
            approved_payment_request_amount=Sum(
                "payment_requests__request_amount",
                filter=Q(
                    payment_requests__status__in=[
                        PaymentRequestStatus.APPROVED.value,
                        PaymentRequestStatus.SUBMITTED.value,
                    ]
                ),
            ),
        )

    def annotate_approved_payment_gst_amount(self):
        return self.annotate(
            approved_payment_gst_amount=Sum(
                "payment_requests__gst_amount",
                filter=Q(
                    payment_requests__status__in=[
                        PaymentRequestStatus.APPROVED.value,
                        PaymentRequestStatus.SUBMITTED.value,
                    ]
                ),
            ),
        )

    def annotate_total_payment_request_amount(self):
        return (
            self.annotate_pending_payment_request_amount()
            .annotate_approved_payment_request_amount()
            .annotate(
                payment_request_amount=Coalesce(
                    F("pending_payment_request_amount"), Value(0), output_field=DecimalField()
                )
                + Coalesce(F("approved_payment_request_amount"), Value(0), output_field=DecimalField())
            )
        )

    def annotate_total_payment_gst_amount(self):
        return (
            self.annotate_pending_payment_gst_amount()
            .annotate_approved_payment_gst_amount()
            .annotate(
                payment_gst_amount=Coalesce(F("pending_payment_gst_amount"), Value(0), output_field=DecimalField())
                + Coalesce(F("approved_payment_gst_amount"), Value(0), output_field=DecimalField())
            )
        )

    def annotate_pending_and_approved_payment_request_amount(self):
        return (
            self.annotate_pending_payment_request_amount()
            .annotate_approved_payment_request_amount()
            .annotate(
                purchase_order_payment_request=JSONObject(
                    pending_payment_request_amount=Coalesce("pending_payment_request_amount", Value(0)),
                    approved_payment_request_amount=Coalesce("approved_payment_request_amount", Value(0)),
                )
            )
        )

    def annotate_vendor_order_amount(self):
        from order.data.models import OrderSnapshot

        snapshots_subquery = (
            OrderSnapshot.objects.filter(
                order_id=OuterRef("vendor_order_id"),
                outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value],
            )
            .order_by("-created_at")
            .annotate(
                snapshot=JSONObject(
                    order_id="id",
                    outgoing_status="outgoing_status",
                    due_at="due_at",
                    started_at="started_at",
                    invoice_status="invoice_status",
                    work_order_from="work_order_from",
                    incoming_status="incoming_status",
                    issued_at="issued_at",
                    amount="amount",
                    progress_percentage="progress_percentage",
                    tax_amount="tax_amount",
                )
            )
        )

        return self.annotate(last_approved_snapshot=Subquery(snapshots_subquery.values("snapshot")[:1])).annotate(
            order_amount=Case(
                When(
                    Q(last_approved_snapshot__isnull=False)
                    & Q(vendor_order__outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Cast(F("last_approved_snapshot__amount"), FloatField())
                        + Cast(F("last_approved_snapshot__tax_amount"), FloatField()),
                        FloatField(),
                    ),
                ),
                default=Coalesce(
                    Cast(F("vendor_order__saved_total_amount"), FloatField()),
                    Value(0.0),
                    output_field=FloatField(),
                ),
                output_field=FloatField(),  # Ensure the whole Case expression returns a FloatField
            )
        )


class VendorOrderQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_is_vendor_active(self):
        subquery = (
            FromToOrgMapping.objects.filter(org_to_id=OuterRef("org_to_id"), org_from_id=OuterRef("org_from_id"))
            .filter(deleted_at__isnull=True)
            .values("vendor_status")
        )
        return self.annotate(vendor_status=Subquery(subquery[:1])).annotate(
            is_vendor_active=Case(
                When(
                    vendor_status__isnull=False,
                    vendor_status=VendorStatusChoices.ACTIVE.value,
                    then=Value(True),
                ),
                default=Value(False),
            )
        )

    def annotate_request_data(self):
        from order.data.models import OrderRequestMapping

        request_mapping = OrderRequestMapping.objects.filter(resource_id=OuterRef("pk")).annotate_request_data()
        return self.annotate(request_data=request_mapping.values("request_data")[:1])

    def annotate_payment_request_pending_and_approved_amount(self):
        from order.data.models import VendorOrder

        subquery_for_invoice_amount = Subquery(
            VendorOrder.objects.filter(id=OuterRef("pk"))
            .annotate(
                pending_payment_request_amount=Coalesce(
                    Sum(
                        "invoices__payment_requests__request_amount",
                        filter=Q(
                            invoices__payment_requests__status__in=[
                                PaymentRequestStatus.HOLD.value,
                                PaymentRequestStatus.PENDING.value,
                                PaymentRequestStatus.REQUEST_MISCONFIGURED.value,
                            ]
                        ),
                    ),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                approved_payment_request_amount=Coalesce(
                    Sum(
                        "invoices__payment_requests__request_amount",
                        filter=Q(
                            invoices__payment_requests__status__in=[
                                PaymentRequestStatus.APPROVED.value,
                                PaymentRequestStatus.SUBMITTED.value,
                            ]
                        ),
                    ),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                invoice_payment_request_amount=JSONObject(
                    pending_payment_request_amount=Coalesce("pending_payment_request_amount", Value(0)),
                    approved_payment_request_amount=Coalesce("approved_payment_request_amount", Value(0)),
                )
            )
            .values("invoice_payment_request_amount")[:1]
        )

        subquery_for_purchase_order = Subquery(
            VendorOrder.objects.filter(id=OuterRef("pk"))
            .annotate(
                pending_payment_request_amount=Coalesce(
                    Sum(
                        "purchase_orders__payment_requests__request_amount",
                        filter=Q(
                            purchase_orders__payment_requests__status__in=[
                                PaymentRequestStatus.HOLD.value,
                                PaymentRequestStatus.PENDING.value,
                                PaymentRequestStatus.REQUEST_MISCONFIGURED.value,
                            ]
                        ),
                    ),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                approved_payment_request_amount=Coalesce(
                    Sum(
                        "purchase_orders__payment_requests__request_amount",
                        filter=Q(
                            purchase_orders__payment_requests__status__in=[
                                PaymentRequestStatus.APPROVED.value,
                                PaymentRequestStatus.SUBMITTED.value,
                            ]
                        ),
                    ),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                purchase_order_payment_request=JSONObject(
                    pending_payment_request_amount=Coalesce("pending_payment_request_amount", Value(0)),
                    approved_payment_request_amount=Coalesce("approved_payment_request_amount", Value(0)),
                )
            )
            .values("purchase_order_payment_request")[:1]
        )

        return self.annotate(purchase_order_payment_request=Subquery(subquery_for_purchase_order)).annotate(
            invoice_payment_request_amount=Subquery(subquery_for_invoice_amount)
        )

    def annotate_is_snapshot_available_for_vendor(self):
        return self.annotate(
            is_snapshot_available=Case(When(snapshots__isnull=False, then=Value(True)), default=Value(False))
        )

    def annotate_is_snapshot_available_for_client(self):
        from order.data.models import VendorOrder

        subquery = VendorOrder.objects.filter(id=OuterRef("id")).annotate(
            snapshot_count=Count(
                "snapshots",
                filter=Q(snapshots__outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value]),
            )
        )

        return self.annotate(
            approved_snapshot_count=Subquery(subquery.values("snapshot_count")[:1]),
            is_snapshot_available=Case(
                When(
                    Q(approved_snapshot_count=1) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Value(False),
                ),
                When(
                    Q(approved_snapshot_count=1) & Q(outgoing_status=OrderStatusEnum.APPROVED.value), then=Value(True)
                ),
                When(snapshots__isnull=False, then=Value(True)),
                default=Value(False),
            ),
        )

    def annotate_last_approved_snapshot(self):
        from order.data.models import OrderSnapshot

        snapshots_subquery = (
            OrderSnapshot.objects.filter(
                order_id=OuterRef("id"),
                outgoing_status__in=[OrderStatusEnum.APPROVED.value, OrderStatusEnum.SENT.value],
            )
            .order_by("-created_at")
            .annotate(
                snapshot=JSONObject(
                    order_id="id",
                    outgoing_status="outgoing_status",
                    due_at="due_at",
                    started_at="started_at",
                    invoice_status="invoice_status",
                    work_order_from="work_order_from",
                    incoming_status="incoming_status",
                    issued_at="issued_at",
                    amount="amount",
                    progress_percentage="progress_percentage",
                    tax_amount="tax_amount",
                )
            )
        )

        return self.annotate(last_approved_snapshot=Subquery(snapshots_subquery.values("snapshot")[:1]))

    def annotate_not_cancelled_elements_count(self):
        return self.annotate(
            not_cancelled_elements_count=Count(
                "order_elements",
                distinct=True,
                filter=Q(
                    order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                    order_elements__deleted_at__isnull=True,
                ),
            ),
        )

    def annotate_full_order_number(self):
        return self.annotate(
            full_order_number=Concat(F("project__job_id"), Value("/"), F("order_number"), output_field=CharField())
        )

    def annotate_elements_final_amount(self):
        from order.data.models import VendorOrder

        subquery = (
            VendorOrder.objects.filter(id=OuterRef("id"))
            .annotate(
                total_gross_amount=Case(
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=None,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                            order_elements__deleted_at__isnull=True,
                        ),
                        then=F("order_elements__vendor_rate") * F("order_elements__quantity"),
                    ),
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=True,
                            order_elements__deleted_at__isnull=True,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                        ),
                        then=F("order_elements__vendor_rate") * F("order_elements__quantity")
                        + F("order_elements__vendor_rate")
                        * F("order_elements__quantity")
                        * F("order_elements__service_charge_percent")
                        / 100,
                    ),
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=False,
                            order_elements__deleted_at__isnull=True,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                        ),
                        then=F("order_elements__vendor_rate")
                        * F("order_elements__quantity")
                        * F("order_elements__service_charge_percent")
                        / 100,
                    ),
                    default=Value(0),
                    output_field=DecimalField(),
                ),
                # total_discounted_value=F("order_elements__discount_percent") * F("total_gross_amount") / 100,
                total_discounted_value=Case(
                    When(
                        Q(
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                            order_elements__deleted_at__isnull=True,
                        ),
                        then=F("order_elements__discount_percent") * F("total_gross_amount") / 100,
                    ),
                    default=Value(0),
                    output_field=DecimalField(),
                ),
                # tax_amount=F("order_elements__tax_percent")
                # * (F("total_gross_amount") - F("total_discounted_value"))
                # / 100,
                tax_amount=Case(
                    When(
                        Q(
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                            order_elements__deleted_at__isnull=True,
                        ),
                        then=F("order_elements__tax_percent")
                        * (F("total_gross_amount") - F("total_discounted_value"))
                        / 100,
                    ),
                    default=Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                total_order_amount=Sum(F("total_gross_amount") - F("total_discounted_value")),
                total_tax_amount=Sum(F("tax_amount")),
            )
            # .annotate(
            #     elements_final_and_tax_amount=JSONObject(
            #         total_order_amount="total_order_amount", total_tax_amount="total_tax_amount"
            #     )
            # )
        )

        # return self.annotate(
        #     elements_final_and_tax_amount=Subquery(subquery.values("elements_final_and_tax_amount")[:1]),
        #     elements_final_amount=Cast(
        #         F("elements_final_and_tax_amount__total_order_amount"), output_field=FloatField()
        #     ),
        #     elements_tax_amount=Cast(F("elements_final_and_tax_amount__total_tax_amount"), output_field=FloatField()),
        # )
        return self.annotate(
            elements_final_amount=Subquery(subquery.values("total_order_amount")[:1]),
            elements_tax_amount=Subquery(subquery.values("total_tax_amount")[:1]),
        )

    def annotate_deduction_amount(self):
        from order.data.models import VendorOrder

        return self.annotate(
            deduction_amount=Subquery(
                VendorOrder.objects.filter(id=OuterRef("id"))
                .annotate(
                    deduction_amount=Coalesce(
                        Sum(
                            F("deductions__amount"),
                            filter=Q(deductions__deleted_at__isnull=True),
                        ),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                .values("deduction_amount")[:1]
            )
        )

    def annotate_deduction_tax_amount(self):
        from order.data.models import VendorOrder

        return self.annotate(
            deduction_tax_amount=Subquery(
                VendorOrder.objects.filter(id=OuterRef("id"))
                .annotate(
                    deduction_tax_amount=Coalesce(
                        Sum(
                            F("deductions__tax_amount"),
                            filter=Q(deductions__deleted_at__isnull=True),
                        ),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                .values("deduction_tax_amount")[:1]
            )
        )

    def annotate_order_final_amount(self):
        return (
            self.annotate_elements_final_amount()
            .annotate_deduction_amount()
            .annotate_deduction_tax_amount()
            .annotate(
                final_amount=(F("elements_final_amount") - F("deduction_amount"))
                + (F("elements_tax_amount") - F("deduction_tax_amount")),
            )
        )

    def annotate_client_deduction_amount(self):
        from order.data.models import OrderSnapshot, VendorOrder

        return self.annotate(
            deduction_amount=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Subquery(
                            OrderSnapshot.objects.filter(
                                id=Cast(OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField())
                            )
                            .annotate(
                                deduction_amount=Sum(
                                    F("deduction_snapshots__amount"),
                                    filter=Q(deduction_snapshots__deleted_at__isnull=True),
                                )
                            )
                            .values("deduction_amount")[:1],
                        ),
                        output_field=FloatField(),
                    ),
                ),
                default=Coalesce(
                    Cast(
                        Subquery(
                            VendorOrder.objects.filter(id=OuterRef("id"))
                            .annotate(
                                deduction_amount=Sum(
                                    F("deductions__amount"),
                                    filter=Q(deductions__deleted_at__isnull=True),
                                )
                            )
                            .values("deduction_amount")[:1],
                        ),
                        output_field=FloatField(),
                    ),
                    Value(0),
                    output_field=FloatField(),
                ),
            )
        )

    def annotate_client_deduction_tax_amount(self):
        from order.data.models import OrderSnapshot, VendorOrder

        return self.annotate(
            deduction_tax_amount=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Subquery(
                            OrderSnapshot.objects.filter(
                                id=Cast(OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField())
                            )
                            .annotate(
                                deduction_tax_amount=Sum(
                                    F("deduction_snapshots__tax_amount"),
                                    filter=Q(deduction_snapshots__deleted_at__isnull=True),
                                )
                            )
                            .values("deduction_tax_amount")[:1],
                        ),
                        output_field=FloatField(),
                    ),
                ),
                default=Coalesce(
                    Cast(
                        Subquery(
                            VendorOrder.objects.filter(id=OuterRef("id"))
                            .annotate(
                                deduction_tax_amount=Sum(
                                    F("deductions__tax_amount"),
                                    filter=Q(deductions__deleted_at__isnull=True),
                                )
                            )
                            .values("deduction_tax_amount")[:1],
                        ),
                        output_field=FloatField(),
                    ),
                    Value(0),
                    output_field=FloatField(),
                ),
            )
        )

    def annotate_client_order_amount(self):
        return (
            self.annotate_last_approved_snapshot()
            .annotate_elements_final_amount()
            .annotate_client_deduction_amount()
            .annotate(
                order_amount=Case(
                    When(
                        Q(last_approved_snapshot__isnull=False)
                        & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                        then=Cast(F("last_approved_snapshot__amount"), output_field=FloatField()),
                    ),
                    default=Coalesce(
                        F("elements_final_amount") - F("deduction_amount"),
                        Value(0),
                        output_field=FloatField(),
                    ),
                )
            )
        )

    def annotate_client_order_tax_amount(self):
        return (
            self.annotate_last_approved_snapshot()
            .annotate_client_deduction_tax_amount()
            .annotate_elements_final_amount()
            .annotate(
                order_tax_amount=Case(
                    When(
                        Q(last_approved_snapshot__isnull=False)
                        & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                        then=Cast(F("last_approved_snapshot__tax_amount"), output_field=FloatField()),
                    ),
                    default=Coalesce(
                        F("elements_tax_amount") - F("deduction_tax_amount"),
                        Value(0),
                        output_field=FloatField(),
                    ),
                ),
            )
        )


class OrderSnapshotQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_request_data(self):
        from order.data.models import OrderRequestMapping

        request_mapping = OrderRequestMapping.objects.filter(resource_id=OuterRef("pk")).annotate_request_data()
        return self.annotate(request_data=request_mapping.values("request_data")[:1])

    def annotate_not_cancelled_elements_count(self):
        return self.annotate(
            not_cancelled_elements_count=Count(
                "order_elements",
                distinct=True,
                filter=Q(
                    order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                    order_elements__deleted_at__isnull=True,
                ),
            ),
        )

    def annotate_full_order_number(self):
        return self.annotate(
            full_order_number=Concat(F("project__job_id"), Value("/"), F("order_number"), output_field=CharField())
        )

    def annotate_elements_final_amount(self):
        from order.data.models import OrderSnapshot

        subquery = (
            OrderSnapshot.objects.filter(id=OuterRef("id"))
            .annotate(
                total_gross_amount=Case(
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=None,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                            order_elements__deleted_at__isnull=True,
                        ),
                        then=F("order_elements__vendor_rate") * F("order_elements__quantity"),
                    ),
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=True,
                            order_elements__deleted_at__isnull=True,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                        ),
                        then=F("order_elements__vendor_rate") * F("order_elements__quantity")
                        + F("order_elements__vendor_rate")
                        * F("order_elements__quantity")
                        * F("order_elements__service_charge_percent")
                        / 100,
                    ),
                    When(
                        Q(
                            order_elements__is_service_charge_with_base_amount=False,
                            order_elements__deleted_at__isnull=True,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                        ),
                        then=F("order_elements__vendor_rate")
                        * F("order_elements__quantity")
                        * F("order_elements__service_charge_percent")
                        / 100,
                    ),
                    default=Value(0),
                    output_field=DecimalField(),
                ),
                total_discounted_value=F("order_elements__discount_percent") * F("total_gross_amount") / 100,
                filter=Q(
                    order_elements__status__in=[
                        OrderStatus.PENDING,
                        OrderStatus.SENT,
                        OrderStatus.MODIFIED,
                        OrderStatus.COMPLETED,
                    ],
                    order_elements__deleted_at__isnull=True,
                ),
                tax_amount=F("order_elements__tax_percent")
                * (F("total_gross_amount") - F("total_discounted_value"))
                / 100,
            )
            .annotate(
                total_order_amount=Sum(F("total_gross_amount") - F("total_discounted_value")),
                total_tax_amount=Sum(F("tax_amount")),
            )
            # .annotate(
            #     elements_final_and_tax_amount=JSONObject(
            #         total_order_amount="total_order_amount", total_tax_amount="total_tax_amount"
            #     )
            # )
        )

        # return self.annotate(
        #     elements_final_and_tax_amount=Subquery(subquery.values("elements_final_and_tax_amount")[:1]),
        #     elements_final_amount=Cast(
        #         F("elements_final_and_tax_amount__total_order_amount"), output_field=FloatField()
        #     ),
        #     elements_tax_amount=Cast(F("elements_final_and_tax_amount__total_tax_amount"), output_field=FloatField()),
        # )
        return self.annotate(
            elements_final_amount=Subquery(subquery.values("total_order_amount")[:1]),
            elements_tax_amount=Subquery(subquery.values("total_tax_amount")[:1]),
        )


class OrganizationPurchaseOrderTypeQuerySet(QuerySet, AvailableQuerySetMixin):
    pass
