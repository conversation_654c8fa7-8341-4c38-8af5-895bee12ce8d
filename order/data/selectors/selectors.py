import datetime
import decimal
from typing import Tuple

import structlog
from django.conf import settings
from django.contrib.postgres.aggregates import A<PERSON>y<PERSON>gg
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>ime<PERSON>ield,
    DecimalField,
    F,
    Func,
    IntegerField,
    Max,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce, JSONObject
from django.utils.module_loading import import_string

from common.choices import VendorStatusChoices
from order.data.choices import OrderStatusChoices
from order.data.models import (
    BaseOrder,
    Deduction,
    DeductionAttachment,
    DeductionAttachmentSnapshot,
    DeductionSnapshot,
    OrderElementGuidelineAttachmentSnapshot,
    OrderElementGuidelineSnapshot,
    OrderElementPreviewFileSnapshot,
    OrderElementProductionDrawingSnapshot,
    OrderElementSnapshot,
    OrderSnapshot,
    OrderTextFieldData,
    VendorOrder,
    VendorOrderElement,
    VendorOrderElementPreviewFile,
    VendorOrderFieldHistory,
    VendorPurchaseOrder,
)
from order.data.selectors.selector_v1 import (
    fetch_basic_order_list,
    fetch_order_document_config_with_data,
    purchase_order_max_version,
)
from order.domain.abstract_repos import OrderSnapshotAbstractRepo
from order.domain.constants import OrderStatusEnum
from order.domain.status_choices import OrderStatus, POStatus
from ratecontract.data.models import RateContract, RateContractElement
from rollingbanners.comment_base_service import CommentBaseService
from vendor.data.models import Vendor
from vendorv2.data.models import ClientVendorMapping

logger = structlog.get_logger(__name__)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


def order_element_snapshot_details(element_id: int, org_id: int):
    return (
        OrderElementSnapshot.available_objects.filter(id=element_id)
        .annotate(preview_file_count=Count("preview_files"))
        .prefetch_related(
            Prefetch(
                "preview_files",
                queryset=OrderElementPreviewFileSnapshot.objects.available(),
            ),
            Prefetch(
                "production_drawings",
                queryset=OrderElementProductionDrawingSnapshot.objects.prefetch_related("tags").available(),
            ),
            Prefetch("guidelines", queryset=OrderElementGuidelineSnapshot.objects.available()),
            Prefetch("guidelines__attachments", queryset=OrderElementGuidelineAttachmentSnapshot.objects.available()),
        )
        .annotate_comment_count(org_id=org_id)
        .first()
    )


def fetch_vendor_order(*, vendor_order: QuerySet, org_id: int):
    preview_file_subquery = (
        VendorOrderElementPreviewFile.objects.filter(element_id=OuterRef("pk"), is_main=True)
        .available()
        .order_by("-is_main", "uploaded_at")
    )
    order: VendorOrder = (
        vendor_order.annotate_elements_final_amount()
        .annotate_deduction_amount()
        .annotate_deduction_tax_amount()
        .select_related(
            "project",
            "updated_by",
            "issued_by",
            "org_to",
            "org_from",
            "created_by",
            "origin_org",
        )
        .prefetch_related(Prefetch("rate_contract", RateContract.objects.available().annotate_element_count()))
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER.name, org_id=org_id
            ),
            order_amount=Coalesce(
                F("elements_final_amount") - F("deduction_amount"),
                Value(0),
                output_field=DecimalField(),
            ),
        )
        .annotate_request_data()
        .prefetch_related(
            Prefetch(
                "order_elements",
                VendorOrderElement.available_objects.annotate_elements_final_amount()
                .all()
                .select_related("linked_element", "el_element", "item_type", "category")
                .annotate(
                    progress_percentage=Coalesce(
                        F("linked_element__progress_percentage"), Value(0), output_field=DecimalField()
                    )
                )
                .annotate(
                    comment_count=CommentHelperService.get_count(
                        context_group=CommentHelperService.GROUPED_CONTEXT.ORDER_ITEM.name, org_id=org_id
                    ),
                    preview_file=Subquery(preview_file_subquery.values("file")[:1]),
                )
                .annotate(rate_contract_id=F("vendor_order__rate_contract_id"))
                .annotate(
                    rc_rate=Subquery(
                        RateContractElement.objects.filter(
                            rate_contract_id=OuterRef("rate_contract_id"), element_id=OuterRef("el_element_id")
                        ).values("rate")[:1],
                        output_field=DecimalField(max_digits=11, decimal_places=2),
                    )
                )
                .order_by("created_at"),
            ),
            "attachments",
            Prefetch("deductions", Deduction.objects.select_related("created_by", "updated_by").all()),
            Prefetch("deductions__attachments", DeductionAttachment.objects.select_related("uploaded_by").all()),
        )
        .first()
    )
    rc_count = (
        RateContract.objects.active()
        .filter(
            organization_id=org_id,
            client_id__in=[order.project.client_id, org_id],
        )
        .count()
    )

    sorted_purchase_orders = purchase_order_max_version(vendor_order_id=order.pk)
    setattr(order, "sorted_purchase_orders", sorted_purchase_orders)
    setattr(order, "rc_count", rc_count)
    document_config = fetch_order_document_config_with_data(country_id=order.org_to.country_id, order_id=order.pk)
    setattr(order, "document_config", document_config)
    return order, False


def get_client_order_detail(*, order_id: int, org_id: int, order_snapshot_repo: OrderSnapshotAbstractRepo) -> BaseOrder:
    vendor_order = VendorOrder.objects.filter(id=order_id).annotate_last_approved_snapshot()

    # If snapshot is available and order is approved, return snapshot
    if (
        vendor_order
        and vendor_order[0].outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order[0].last_approved_snapshot
    ):
        order_snapshot = order_snapshot_repo.get_order_snapshot_details(
            order_snapshot_id=vendor_order[0].last_approved_snapshot["order_id"], org_id=org_id
        )
        # order_snapshot = fetch_order_snapshot(
        #     order_snapshot_id=vendor_order[0].last_approved_snapshot["order_id"], org_id=org_id
        # )
        return order_snapshot, True

    return fetch_vendor_order(vendor_order=vendor_order, org_id=org_id)


def client_order_list(*, project_id: int, org_id: int):
    orders = (
        VendorOrder.objects.filter(project_id=project_id)
        .annotate_elements_final_amount()
        .annotate_client_order_amount()
        .annotate_client_order_tax_amount()
        .annotate_client_deduction_tax_amount()
        .annotate_client_deduction_amount()
        .annotate_payment_request_pending_and_approved_amount()
        .annotate_last_approved_snapshot()
        .annotate_is_snapshot_available_for_client()
        .select_related("updated_by", "issued_by", "org_to", "org_from", "origin_org", "project", "org_to__vendor")
        .prefetch_related("org_to__vendor__category")
        .prefetch_related(
            Prefetch(
                "order_elements",
                queryset=VendorOrderElement.available_objects.all()
                .select_related("linked_element", "boq_element", "linked_element__work_progress_element")
                .annotate(progress_percentage=Coalesce(F("linked_element__progress_percentage"), None))
                .annotate(
                    total_amount=Sum(
                        F("linked_element__quantity") * F("linked_element__client_rate")
                        - F("linked_element__quantity")
                        * F("linked_element__client_rate")
                        * F("linked_element__discount_percent")
                        / 100,
                        filter=Q(linked_element__isnull=False),
                    )
                ),  # TODO: remove this query from here and add another selector
            ),
            "order_elements__item_type",
            "order_elements__category",
            Prefetch(
                "purchase_orders",
                queryset=VendorPurchaseOrder.objects.select_related("cancelled_by", "uploaded_by")
                .annotate_total_payment_request_amount()
                .exclude(status=POStatus.PO_CANCELLED)
                .all(),
            ),
        )
        .annotate(
            order_due_at=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Cast(F("last_approved_snapshot__due_at"), output_field=CharField()),
                        output_field=DateTimeField(),
                    ),
                ),
                default=Cast(F("due_at"), output_field=DateTimeField()),
            )
        )
        .annotate(
            order_issued_at=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Cast(F("last_approved_snapshot__issued_at"), output_field=CharField()),
                        output_field=DateTimeField(),
                    ),
                ),
                default=Cast(F("issued_at"), output_field=DateTimeField()),
            )
        )
        .annotate(
            order_status=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Func(
                        Cast(F("last_approved_snapshot__outgoing_status"), output_field=CharField()),
                        Value('"'),
                        function="BTRIM",
                        output_field=CharField(),
                    ),
                ),
                default=F("outgoing_status"),
            )
        )
        .annotate(
            order_invoice_status=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(F("last_approved_snapshot__invoice_status"), output_field=CharField()),
                ),
                default=F("invoice_status"),
            )
        )
        .annotate(
            item_type=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Subquery(
                        OrderElementSnapshot.objects.filter(
                            order_snapshot_id=Cast(
                                OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField()
                            )
                        )
                        .annotate(
                            item_type_list=ArrayAgg(
                                JSONObject(
                                    id=F("item_type__id"),
                                    name=F("item_type__name"),
                                    color_code=F("item_type__color_code"),
                                )
                            )
                        )
                        .values("item_type_list")[:1]
                    ),
                ),
                default=Subquery(
                    VendorOrderElement.objects.filter(vendor_order_id=OuterRef("id"))
                    .values("item_type__id", "item_type__name", "item_type__color_code")
                    .annotate(
                        item_type_list=ArrayAgg(
                            JSONObject(
                                id=F("item_type__id"), name=F("item_type__name"), color_code=F("item_type__color_code")
                            )
                        )
                    )
                    .values("item_type_list")[:1]
                ),
            ),
        )
        .annotate(
            category=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Subquery(
                        OrderElementSnapshot.objects.filter(
                            order_snapshot_id=Cast(
                                OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField()
                            )
                        )
                        .annotate(
                            category_list=ArrayAgg(
                                JSONObject(id=F("category__id"), name=F("category__name"), code=F("category__code"))
                            )
                        )
                        .values("category_list")[:1]
                    ),
                ),
                default=Subquery(
                    VendorOrderElement.objects.filter(vendor_order_id=OuterRef("id"))
                    .values("category__id", "category__name", "category__code")
                    .annotate(
                        category_list=ArrayAgg(
                            JSONObject(id=F("category__id"), name=F("category__name"), code=F("category__code"))
                        )
                    )
                    .values("category_list")[:1]
                ),
            )
        )
        .annotate(
            cancelled_elements_count=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Subquery(
                        OrderSnapshot.objects.filter(
                            id=Cast(OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField()),
                            order_elements__status=OrderStatus.CANCELLED,
                            order_elements__deleted_at__isnull=True,
                        )
                        .annotate(count=Count("order_elements"))
                        .values("count")[:1],
                    ),
                ),
                default=Count(
                    "order_elements",
                    distinct=True,
                    filter=Q(order_elements__status=OrderStatus.CANCELLED, order_elements__deleted_at__isnull=True),
                ),
            )
        )
        .annotate(
            not_cancelled_elements_count=Case(
                When(
                    Q(last_approved_snapshot__isnull=False) & Q(outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Subquery(
                        OrderSnapshot.objects.filter(
                            id=Cast(OuterRef("last_approved_snapshot__order_id"), output_field=IntegerField()),
                            order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                            order_elements__deleted_at__isnull=True,
                        )
                        .annotate(count=Count("order_elements"))
                        .values("count")[:1],
                    ),
                ),
                default=Count(
                    "order_elements",
                    distinct=True,
                    filter=Q(
                        order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                        order_elements__deleted_at__isnull=True,
                    ),
                ),
            )
        )
        .annotate(
            closed_at=Subquery(
                VendorOrder.objects.filter(id=OuterRef("id"))
                .annotate(
                    closed_at=Max(
                        "status_history__created_at",
                        filter=Q(status_history__outgoing_status=VendorOrder.OutgoingStatus.CLOSED),
                    )
                )
                .values("closed_at")[:1],
            ),
        )
        .annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.ORDER.name, org_id=org_id
            )
        )
        .order_by("created_at")
        .filter(org_to_id=org_id)
        .filter(
            Q(last_approved_snapshot__isnull=False)
            | Q(
                outgoing_status__in=[
                    OrderStatusEnum.SENT.value,
                    OrderStatusEnum.APPROVED.value,
                    OrderStatusEnum.CLOSED.value,
                    OrderStatusEnum.CANCELLED.value,
                    OrderStatusEnum.COMPLETED.value,
                ]
            )
        )
        .exclude(outgoing_status=OrderStatusEnum.DRAFT.value)
        .all()
    )
    return orders


def fetch_vendor_order_with_linked_elements(*, order_id: int):
    order: VendorOrder = (
        VendorOrder.objects.annotate_elements_final_amount()
        .filter(id=order_id)
        .prefetch_related(
            Prefetch(
                "order_elements",
                queryset=VendorOrderElement.available_objects.all()
                .prefetch_related("production_drawings", "preview_files", "guidelines", "guidelines__attachments")
                .select_related("linked_element", "boq_element")
                .annotate(progress_percentage=Coalesce(F("linked_element__progress_percentage"), None))
                # .annotate(
                #     total_amount=Sum(
                #         F("linked_element__quantity") * F("linked_element__client_rate")
                #         - F("linked_element__quantity")
                #         * F("linked_element__client_rate")
                #         * F("linked_element__discount_percent")
                #         / 100,
                #         filter=Q(linked_element__isnull=False),
                #     )
                # )
                # .annotate(
                #     base_amount=Sum(
                #         F("linked_element__quantity") * F("linked_element__client_rate"),
                #         filter=Q(linked_element__isnull=False),
                #     )
                # )
                .annotate(
                    linked_element_base_amount=Case(
                        When(
                            Q(linked_element__isnull=False),
                            then=F("linked_element__quantity") * F("linked_element__client_rate"),
                        ),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                )
                .annotate(
                    linked_element_gross_amount=Case(
                        When(
                            Q(
                                linked_element__isnull=False,
                                linked_element__is_service_charge_with_base_amount=True,
                            ),
                            then=F("linked_element_base_amount")
                            + F("linked_element__quantity")
                            * F("linked_element__client_rate")
                            * F("linked_element__service_charge_percent")
                            / 100,
                        ),
                        When(
                            Q(
                                linked_element__isnull=False,
                                linked_element__is_service_charge_with_base_amount=False,
                            ),
                            then=F("linked_element_base_amount")
                            - F("linked_element__quantity") * F("linked_element__client_rate")
                            + F("linked_element__quantity")
                            * F("linked_element__client_rate")
                            * F("linked_element__service_charge_percent")
                            / 100,
                        ),
                        default=F("linked_element_base_amount"),
                        output_field=DecimalField(),
                    )
                )
                # .annotate(
                #     discounted_value=Sum(
                #         F("gross_amount") * F("linked_element__discount_percent") / 100,
                #         filter=Q(linked_element__isnull=False),
                #     )
                # )
                .annotate(
                    linked_element_discounted_value=Case(
                        When(
                            Q(linked_element__isnull=False),
                            then=F("linked_element_gross_amount") * F("linked_element__discount_percent") / 100,
                        ),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                )
                .annotate(
                    total_amount=Coalesce(
                        Sum(
                            F("linked_element_gross_amount") - F("linked_element_discounted_value"),
                            output_field=DecimalField(),
                        ),
                        Value(decimal.Decimal(0.0)),
                    ),
                ),  # TODO: remove this query from here and add another selector
            ),
            Prefetch(
                "vendor_order_text_field_data", queryset=OrderTextFieldData.objects.available(), to_attr="text_data"
            ),
        )
    ).select_related("gst_details")
    return order


def get_vendor_order_v2(order_id: int, project_id: int, org_id: int):
    return (
        VendorOrder.objects.filter(id=order_id, project_id=project_id)
        .filter(~Q(org_to_id=org_id), origin_org_id=org_id)
        .select_related("org_to", "created_by", "poc", "issued_by")
        .first()
    )


def get_vendor_order_elements_list_v2(order_id: int, project_id: int, org_id: int):
    return (
        VendorOrderElement.objects.filter(vendor_order_id=order_id, project_id=project_id)
        .select_related("vendor_order")
        .filter(~Q(vendor_order__org_to_id=org_id), vendor_order__origin_org_id=org_id)
        .all()
    )


def get_vendor_order_element_v2(project_id: int, order_id: int, element_id: int, org_id: int):
    preview_file_subquery = (
        VendorOrderElementPreviewFile.objects.filter(element_id=OuterRef("pk"), is_main=True)
        .available()
        .order_by("-is_main", "uploaded_at")
    )
    return (
        VendorOrderElement.objects.filter(id=element_id, vendor_order_id=order_id, project_id=project_id)
        .select_related("vendor_order")
        .filter(~Q(vendor_order__org_to_id=org_id), vendor_order__origin_org_id=org_id)
        .annotate(preview_file=Subquery(preview_file_subquery.values("file")[:1]))
        .select_related("category", "item_type")
        .first()
    )


def get_vendor_order_deductions_list_v2(project_id: int, order_id: int, org_id: int):
    return (
        Deduction.objects.filter(order_id=order_id, project_id=project_id)
        .select_related("order")
        .filter(~Q(order__org_to_id=org_id), order__origin_org_id=org_id)
        .all()
    )


def get_vendor_order_deduction_v2(project_id: int, order_id: int, deduction_id: int, org_id: int):
    return (
        Deduction.objects.filter(id=deduction_id, order_id=order_id, project_id=project_id)
        .select_related("order")
        .filter(~Q(order__org_to_id=org_id), order__origin_org_id=org_id)
        .prefetch_related(
            Prefetch("attachments", DeductionAttachment.objects.all()),
        )
    ).first()


def get_vendor_purchase_orders_list_v2(project_id: int, order_id: int, org_id: int):
    purchase_orders = (
        VendorPurchaseOrder.objects.filter(vendor_order_id=order_id)
        .select_related("vendor_order")
        .filter(
            ~Q(vendor_order__org_to_id=org_id), vendor_order__origin_org_id=org_id, vendor_order__project_id=project_id
        )
        .all()
        .order_by("-version", "-uploaded_at")
    )
    latest_purchase_order_subquery = Subquery(
        VendorPurchaseOrder.objects.filter(vendor_order_id=OuterRef("vendor_order_id"), po_number=OuterRef("po_number"))
        .order_by("-uploaded_at")
        .values("id")[:1]
    )

    return (
        purchase_orders.annotate(latest_purchase_order_id=latest_purchase_order_subquery)
        .filter(id=F("latest_purchase_order_id"))
        .order_by("id")
    )


def get_client_purchase_orders_list_v2(project_id: int, order_id: int, org_id: int):
    purchase_orders = (
        VendorPurchaseOrder.objects.filter(vendor_order_id=order_id)
        .select_related("vendor_order")
        .filter(vendor_order__org_to_id=org_id, vendor_order__project_id=project_id)
        .all()
        .order_by("-version", "-uploaded_at")
    )
    latest_purchase_order_subquery = Subquery(
        VendorPurchaseOrder.objects.filter(vendor_order_id=OuterRef("vendor_order_id"), po_number=OuterRef("po_number"))
        .order_by("-uploaded_at")
        .values("id")[:1]
    )

    return (
        purchase_orders.annotate(latest_purchase_order_id=latest_purchase_order_subquery)
        .filter(id=F("latest_purchase_order_id"))
        .order_by("id")
    )


def get_vendor_purchase_order_v2(project_id: int, order_id: int, purchase_order_id: int, org_id: int):
    return (
        VendorPurchaseOrder.objects.filter(id=purchase_order_id, vendor_order_id=order_id)
        .select_related("vendor_order")
        .filter(
            ~Q(vendor_order__org_to_id=org_id), vendor_order__origin_org_id=org_id, vendor_order__project_id=project_id
        )
        .first()
    )


def get_client_purchase_order_v2(project_id: int, order_id: int, purchase_order_id: int, org_id: int):
    return (
        VendorPurchaseOrder.objects.filter(id=purchase_order_id, vendor_order_id=order_id)
        .select_related("vendor_order")
        .filter(vendor_order__org_to_id=org_id, vendor_order__project_id=project_id)
        .first()
    )


def get_client_order_list_v2(project_id: int, org_id: int):
    return (
        VendorOrder.objects.filter(project_id=project_id, org_to_id=org_id)
        .select_related("project")
        .annotate_last_approved_snapshot()
        .order_by("created_at")
        .filter(
            Q(last_approved_snapshot__isnull=False)
            | Q(
                outgoing_status__in=[
                    OrderStatusEnum.SENT.value,
                    OrderStatusEnum.APPROVED.value,
                    OrderStatusEnum.CLOSED.value,
                    OrderStatusEnum.CANCELLED.value,
                    OrderStatusEnum.COMPLETED.value,
                ]
            )
        )
        .exclude(outgoing_status=OrderStatusEnum.DRAFT.value)
        .all()
    )


def get_client_order_details_v2(
    project_id: int, order_id: int, org_id: int, order_snapshot_repo: OrderSnapshotAbstractRepo
) -> Tuple[BaseOrder, bool]:
    vendor_order = (
        VendorOrder.objects.filter(id=order_id, project_id=project_id, org_to_id=org_id)
        .select_related(
            "project",
            "updated_by",
            "issued_by",
            "poc",
            "org_to",
            "org_from",
            "created_by",
            "origin_org",
        )
        .annotate_last_approved_snapshot()
    ).first()

    # If snapshot is available and order is approved, return snapshot
    if (
        vendor_order
        and vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order.last_approved_snapshot
    ):
        order_snapshot = order_snapshot_repo.get_order_snapshot_details(
            order_snapshot_id=vendor_order.last_approved_snapshot["order_id"], org_id=org_id
        )
        return order_snapshot, True

    return vendor_order, False


def get_client_order_elements_v2(order_id: int, project_id: int, org_id: int):
    vendor_order = (
        VendorOrder.objects.filter(id=order_id, project_id=project_id, org_to_id=org_id)
        .annotate_last_approved_snapshot()
        .first()
    )
    if not vendor_order:
        return None, None

    if (
        vendor_order
        and vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order.last_approved_snapshot
    ):
        order_elements = OrderElementSnapshot.objects.filter(
            order_snapshot_id=vendor_order.last_approved_snapshot["order_id"]
        ).all()
        return order_elements, True

    return VendorOrderElement.objects.filter(vendor_order_id=order_id, project_id=project_id).all(), False


def get_client_order_element_v2(project_id: int, order_id: int, element_id: int, org_id: int):
    vendor_order = (
        VendorOrder.objects.filter(id=order_id, project_id=project_id, org_to_id=org_id)
        .annotate_last_approved_snapshot()
        .first()
    )

    if not vendor_order:
        return None, None

    if (
        vendor_order
        and vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order.last_approved_snapshot
    ):
        preview_file_subquery = (
            OrderElementPreviewFileSnapshot.objects.filter(element_id=OuterRef("pk"), is_main=True)
            .available()
            .order_by("-is_main", "uploaded_at")
        )

        order_element = (
            OrderElementSnapshot.objects.filter(
                order_snapshot_id=vendor_order.last_approved_snapshot["order_id"], id=element_id
            )
            .annotate(preview_file=Subquery(preview_file_subquery.values("file")[:1]))
            .first()
        )

        return order_element, True

    return get_order_element_detail_v2(order_id=order_id, element_id=element_id, project_id=project_id), False


def get_order_element_detail_v2(order_id: int, element_id: int, project_id: int):
    preview_file_subquery = (
        VendorOrderElementPreviewFile.objects.filter(element_id=OuterRef("pk"), is_main=True)
        .available()
        .order_by("-is_main", "uploaded_at")
    )
    return (
        VendorOrderElement.objects.filter(id=element_id, vendor_order_id=order_id, project_id=project_id)
        .select_related("vendor_order")
        .annotate(preview_file=Subquery(preview_file_subquery.values("file")[:1]))
        .select_related("category", "item_type")
        .first()
    )


def get_client_order_deductions_list_v2(project_id: int, order_id: int, org_id: int):
    vendor_order = (
        VendorOrder.objects.filter(id=order_id, project_id=project_id, org_to_id=org_id)
        .annotate_last_approved_snapshot()
        .first()
    )

    if not vendor_order:
        return None, None

    if (
        vendor_order
        and vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order.last_approved_snapshot
    ):
        order_deductions = DeductionSnapshot.objects.filter(
            order_id=vendor_order.last_approved_snapshot["order_id"]
        ).all()
        return order_deductions, True

    return Deduction.objects.filter(order_id=order_id, project_id=project_id).all(), False


def get_client_order_deduction_v2(project_id: int, order_id: int, deduction_id: int, org_id: int):
    vendor_order = (
        VendorOrder.objects.filter(id=order_id, project_id=project_id, org_to_id=org_id)
        .annotate_last_approved_snapshot()
        .first()
    )

    if not vendor_order:
        return None, None

    if (
        vendor_order
        and vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
        and vendor_order.last_approved_snapshot
    ):
        order_deduction = (
            DeductionSnapshot.objects.filter(
                id=deduction_id, order_id=vendor_order.last_approved_snapshot["order_id"]
            ).prefetch_related(
                Prefetch("attachments", DeductionAttachmentSnapshot.objects.all()),
            )
        ).first()

        return order_deduction, True

    return get_order_deduction_details_v2(deduction_id=deduction_id, order_id=order_id, project_id=project_id), False


def get_order_deduction_details_v2(project_id: int, order_id: int, deduction_id: int):
    return (
        Deduction.objects.filter(id=deduction_id, order_id=order_id, project_id=project_id).prefetch_related(
            Prefetch("attachments", DeductionAttachment.objects.all()),
        )
    ).first()


def get_vendors_list_v2(project_id: int, org_id: int):
    order_vendor_ids = (
        VendorOrder.objects.filter(project_id=project_id, org_from_id=org_id)
        .available()
        .values_list("org_to_id", flat=True)
    )
    vendor_ids = (
        ClientVendorMapping.objects.filter(
            org_from_id=org_id,
            deleted_at__isnull=True,
        )
        .exclude(org_from_id=F("org_to_id"))
        .values_list("org_to_id", flat=True)
    )

    vendor_ids = order_vendor_ids.union(vendor_ids.filter(vendor_status=VendorStatusChoices.ACTIVE.value))

    return (
        Vendor.objects.filter(organization_id__in=vendor_ids, code__isnull=False).select_related("organization").all()
    )


def get_vendor_orders_for_vendor_due_date_history_get_using_dates(
    org_id: int,
    project_id: int,
    start_date: datetime.date,
    end_date: datetime.date,
) -> QuerySet[VendorOrder]:
    return (
        VendorOrder.objects.filter(
            project_id=project_id,
            org_to_id=org_id,
            created_at__date__lte=end_date,
            issued_at__isnull=False,
        )
        .filter(Q(cancelled_at__isnull=True) | Q(cancelled_at__gte=start_date))
        .order_by("issued_at")
    )


def get_vendor_order_history_data(
    vendor_orders: QuerySet[VendorOrder],
    end_date: datetime.date,
    field: str,
) -> QuerySet[VendorOrder]:
    queryset = (
        VendorOrderFieldHistory.objects.filter(
            order__in=vendor_orders,
            field=field,
            created_at__date__lte=end_date,
        )
        .values("created_at__date", "order_id")
        .annotate(max_id=Max("id"))
        .values_list("max_id", flat=True)
    )

    return vendor_orders.prefetch_related(
        Prefetch(
            "field_histories",
            VendorOrderFieldHistory.objects.filter(id__in=queryset).select_related("created_by").order_by("created_at"),
        ),
    )


def get_vendor_scope_order_count(project_id: int, org_id: int, vendor_ids: list[int]) -> int:
    vendor_orders = (
        VendorOrder.objects.all()
        .annotate_is_snapshot_available_for_client()
        .filter(
            (
                Q(
                    outgoing_status__in=[
                        OrderStatusChoices.SENT.value,
                        OrderStatusChoices.COMPLETED.value,
                        OrderStatusChoices.APPROVED.value,
                    ]
                )
                | Q(
                    outgoing_status__in=[
                        OrderStatusChoices.PENDING.value,
                    ],
                    is_snapshot_available=True,
                )
            ),
            org_from_id=org_id,
            project_id=project_id,
            org_to__isnull=False,
        )
        .filter(org_to_id__in=vendor_ids)
    )

    return vendor_orders.count()


def get_vendor_ids_from_orders(project_id: int, org_id: int) -> list[int]:
    return list(
        fetch_basic_order_list(project_id=project_id, org_id=org_id).values_list("org_to_id", flat=True).distinct()
    )
