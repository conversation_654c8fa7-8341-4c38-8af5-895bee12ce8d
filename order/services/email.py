import mimetypes
from functools import partial
from typing import Dict, List, Tuple, Union

import requests
import structlog
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.module_loading import import_string

from boq.data.models import Boq
from common import decorators
from common.choices import OrganizationType, ProjectUserRoleLevelChoices, ReservedRoleNames
from common.entities import ProjectOrganizationEntity
from common.helpers import get_file_extension
from common.services import model_update
from common.utils import formatINR
from core.models import (
    OrganizationConfigRole,
    Role,
    User,
)
from core.role.entities import RoleCreateData
from core.role.services import ProjectDynamicRoleCreateService
from core.selectors import org_config_bcc_emails_get
from order.config.data.selectors import order_receivers_fetch
from order.data.models import (
    OrganizationIncomingOrderNotificationPoc,
    OrganizationOutgoingOrderNotificationPoc,
    VendorOrder,
    VendorOrderElement,
    VendorOrderEmail,
    VendorPurchaseOrder,
)
from order.data.selectors.selector_v1 import (
    get_order_number,
    get_shipping_address,
    get_vendor_name,
    order_purchase_orders_fetch,
    order_sent_info_fetch,
)
from order.domain.entities.domain_entities import (
    ExcelAttachmentData,
    OrderCreateAndSentData,
    OrderElementCreateData,
    OrderSentData,
)
from order.domain.entities.entities import PurchaseOrderTriggerData
from order.domain.services.helper_service import get_email_dict
from order.domain.utils import order_element_qty_dimensions_exists
from project.data.models import Project, ProjectUser
from project.domain.caches import ProjectCountryConfigCache, ProjectDataCache
from project.domain.services import (
    check_or_assign_project_permission,
    project_org_item_type_config_assignment,
    project_user_assign_many,
    restrict_action_decorator,
)
from project.domain.status import RDStatus
from rollingbanners.comment_base_service import CommentBaseService
from smtp_email.domain.services import DjangoEmailService
from vendor.data.models import Vendor

from .excel import genrate_and_upload_excel_file, genrate_and_upload_excel_file_v2
from .sync_element import OrderElementToBoqElementSyncService
from .trigger import (
    order_cancel_email_trigger,
    order_sent_email_trigger,
    order_sent_trigger,
    order_sent_without_notification_trigger,
    po_upload_trigger,
)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)

logger = structlog.getLogger(__name__)


def assign_role_and_permissions(
    *, vendor_users_email: List[str], to_assign_org: int, project_id: int, assigned_by_org: int, created_by_id: int
):
    is_assigned = check_or_assign_project_permission(
        project_id=project_id, to_assign_org=to_assign_org, assigned_by_org=assigned_by_org, created_by_id=created_by_id
    )
    OrderReceiverService(
        project_id=project_id,
        organization_id=to_assign_org,
        created_by_id=created_by_id,
    ).process(vendor_users_email=vendor_users_email)
    Boq.objects.get_or_create(project_id=project_id)
    if is_assigned:
        project_org_item_type_config_assignment(
            project_id=project_id,
            assignee_org_id=to_assign_org,
            assigner_org_id=assigned_by_org,
            creator_id=created_by_id,
        )


class OrderToBoqElementSyncData:
    @classmethod
    def prepare_data(cls, elements: List[VendorOrderElement]) -> List[OrderElementCreateData]:
        from order.interface.serializers.sync_serializers import (
            OrderToBoqElementSyncDictToEntitySerializerV2,
            OrderToBoqElementSyncModelToDictSerializer,
        )

        elements_ordered_dict = OrderToBoqElementSyncModelToDictSerializer(elements, many=True).data
        serializer = OrderToBoqElementSyncDictToEntitySerializerV2(data=elements_ordered_dict, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data


class OrderReceiverService:
    def __init__(self, project_id: int, organization_id: int, created_by_id: int):
        self.project_id = project_id
        self.organization_id = organization_id
        self.created_by_id = created_by_id
        self.project = Project.objects.get(id=self.project_id)
        self.created_by = User.objects.get(id=created_by_id)

    def get_or_create_role(self, role_name: str, level):
        role = Role.objects.filter(organization_id=self.organization_id, name=role_name).first()
        if role:
            return role
        role_data = RoleCreateData(
            name=role_name,
            level=level,
            notifications=[],
        )

        role = ProjectDynamicRoleCreateService().create_project_user_role(
            data=role_data,
            organization_id=self.organization_id,
            org_type=OrganizationType.VENDOR,
            created_by_id=self.created_by_id,
        )
        return role

    def order_receivers_permission_process(self, vendor_users: List[str]):
        level = ProjectUserRoleLevelChoices.LEVEL_99
        # order viewer permission and project assign to user

        role = self.get_or_create_role(role_name=ReservedRoleNames.ORDER_RECEIVER.value, level=level)
        project_user_assign_many(
            role_id=role.id,
            user_ids=list(vendor_users.values_list("id", flat=True)),
            project=self.project,
            user=self.created_by,
            organization_id=self.organization_id,
        )

    def order_viewer_permission_process(self, vendor_users: List[str]):
        level = ProjectUserRoleLevelChoices.LEVEL_98
        # order viewer permission and project assign to user

        role = self.get_or_create_role(level=level, role_name=ReservedRoleNames.ORDER_VIEWER.value)

        project_user_assign_many(
            role_id=role.id,
            user_ids=list(vendor_users.values_list("id", flat=True)),
            project=self.project,
            user=self.created_by,
            organization_id=self.organization_id,
        )

    def process(self, vendor_users_email: list[str]):
        bcc_vendor_users = order_receivers_fetch(
            config_model=OrganizationIncomingOrderNotificationPoc, org_id=self.organization_id
        )
        order_receivers_users = User.objects.filter(
            email__in=vendor_users_email, org_id=self.organization_id, deleted_at__isnull=True
        )
        bcc_users = bcc_vendor_users.exclude(id__in=order_receivers_users.values_list("id", flat=True))
        if bcc_users:
            self.order_viewer_permission_process(vendor_users=bcc_users)
        if order_receivers_users:
            self.order_receivers_permission_process(vendor_users=order_receivers_users)


@decorators.deprecated_function()
@restrict_action_decorator(status_list=[RDStatus.LOST.value, RDStatus.HOLD.value])
def send_email_and_status_update_v2(
    *,
    vendor_order: VendorOrder,
    vendor_order_id: int,
    project_id: int,
    data: Dict,
    user: User,
    org_id: int,
    boq_sync: bool = True,
):
    vendor_id = vendor_order.actual_vendor_id
    elements: List[VendorOrderElement] = VendorOrderElement.available_objects.filter(
        vendor_order_id=vendor_order_id
    ).prefetch_related("guidelines", "guidelines__attachments", "preview_files", "production_drawings")
    order_number = get_order_number(vendor_order=vendor_order, project_id=project_id)
    email_data_dict = get_email_dict(order_entity=OrderSentData(**data))
    vendor_order, _, _ = model_update(
        instance=vendor_order,
        fields=["issued_at", "issued_by_id", "outgoing_status", "email_data"],
        data={
            "outgoing_status": VendorOrder.OutgoingStatus.SENT,
            "issued_at": timezone.now(),
            "issued_by_id": user.pk,
            "email_data": email_data_dict,
        },
        updated_by_id=user.id,
    )
    if boq_sync:
        OrderElementToBoqElementSyncService.process_create(
            order=vendor_order,
            organization_id=vendor_id,
            order_element_objs=elements,
            element_entities=OrderToBoqElementSyncData.prepare_data(elements=elements),
        )
        currency = ProjectCountryConfigCache.get(instance_id=project_id).currency
        assign_role_and_permissions(
            vendor_users_email=data.get("to"),
            to_assign_org=vendor_id,
            project_id=project_id,
            assigned_by_org=vendor_order.origin_org_id,
            created_by_id=user.id,
        )
        on_commit(
            partial(
                order_sent_trigger,
                vendor_order_id=vendor_order.pk,
                org_to_id=vendor_id,
                org_from_id=org_id,
                order_number=order_number,
                project_id=vendor_order.project_id,
            )
        )
        on_commit(
            partial(
                ProjectDataCache.delete,
                project_details=ProjectOrganizationEntity(
                    project_id=vendor_order.project_id, organization_id=vendor_id
                ),
            )
        )
        po_numbers, po_total_amount, po_attachments = po_trigger_event_and_get_po_related_data(
            vendor_order=vendor_order
        )
        data["attachments"].extend(po_attachments)
        data["bcc"].extend(bcc_recipient_emails_fetch(org_id=vendor_order.org_to_id))
        # TODO : patch need to be removed once, org config order receiver data is corrected in org
        data["bcc"].extend(org_config_bcc_emails_get(org_id=vendor_order.org_to_id))
        OrderEmailService().send_email(
            vendor_order_id=vendor_order_id,
            subject=data.get("subject"),
            body=data.get("body"),
            from_name=vendor_order.org_from.name,
            from_email=settings.EMAIL_HOST_USER,
            to=data.get("to"),
            cc=data.get("cc"),
            bcc=data.get("bcc"),
            attachments=data.get("attachments"),
            context=get_email_context(
                vendor_order=vendor_order,
                custom_message=data.get("body"),
                po_numbers=po_numbers,
                po_total_amount=po_total_amount,
                user=user,
                vendor_organization_name=get_vendor_name(vendor_id=vendor_id),
                org_id=org_id,
                order_number=order_number,
                currency_symbol=currency.symbol,
            ),
            html_email_template_name="order/create_email.html",
            created_by_id=user.pk,
        )
    else:
        # TODO: Temprary trigger for orders which are sent but not visible to vendor
        on_commit(
            partial(
                order_sent_without_notification_trigger,
                vendor_order_id=vendor_order.pk,
                org_to_id=vendor_id,
                org_from_id=org_id,
                order_number=order_number,
                project_id=vendor_order.project_id,
            )
        )
    return vendor_order


def decide_recipients_email_users_while_creation(
    *,
    cc_emails: List,
    bcc_emails: List,
) -> Tuple[List, List, List]:
    return [], cc_emails, bcc_emails


def decide_recipients_email_users_while_updations(
    *,
    vendor_order_id: int,
    cc_emails: List,
    bcc_emails: List,
) -> Tuple[List, List, List]:
    order_email_data: VendorOrderEmail = VendorOrderEmail.objects.filter(vendor_order_id=vendor_order_id).first()
    if order_email_data:
        final_user_emails = order_email_data.to_receiver if order_email_data.to_receiver else []
        cc_emails.extend(order_email_data.cc_receiver if order_email_data.cc_receiver else [])
    else:
        final_user_emails = []
    return final_user_emails, cc_emails, bcc_emails


def cc_recipient_emails_fetch(org_id: int):
    return list(
        set(
            order_receivers_fetch(org_id=org_id, config_model=OrganizationOutgoingOrderNotificationPoc).values_list(
                "email", flat=True
            )
        )
    )


def bcc_recipient_emails_fetch(org_id: int) -> list:
    return list(
        set(
            order_receivers_fetch(org_id=org_id, config_model=OrganizationIncomingOrderNotificationPoc).values_list(
                "email", flat=True
            )
        )
    )


def prepare_email_data(
    *,
    vendor_order_id: int,
    project_id: int,
    user_email: str,
    fields: list = None,
    with_attachments: bool = True,
    org_id: int,
    is_order_updated: bool = False,
) -> Dict:
    final_user_emails = []
    cc_emails = [user_email] if user_email else []
    bcc_emails = []

    order_number, vendor_name, work_order_from, vendor_order = order_sent_info_fetch(
        vendor_order_id=vendor_order_id, project_id=project_id
    )

    subject = f"Purchase Intent from {work_order_from} | Purchase Intent No. : {order_number}"

    cc_role_ids = OrganizationConfigRole.objects.filter(
        organization_config_id=org_id, is_order_cc_role=True
    ).values_list("role_id", flat=True)

    cc_role_user_emails = set(
        ProjectUser.objects.filter(project_id=project_id, role_id__in=cc_role_ids)
        .select_related("user")
        .values_list("user__email", flat=True)
    )
    cc_emails = set(cc_emails).union(cc_role_user_emails)
    cc_emails = list(cc_emails.union(set(cc_recipient_emails_fetch(org_id=org_id))))

    if is_order_updated:
        final_user_emails, cc_emails, bcc_emails = decide_recipients_email_users_while_updations(
            vendor_order_id=vendor_order_id,
            cc_emails=cc_emails,
            bcc_emails=bcc_emails,
        )
    else:
        final_user_emails, cc_emails, bcc_emails = decide_recipients_email_users_while_creation(
            cc_emails=cc_emails,
            bcc_emails=bcc_emails,
        )
    if with_attachments:
        if order_element_qty_dimensions_exists(vendor_order_id):
            fields.extend(["length", "length_uom", "breadth", "breadth_uom"])

        if vendor_order.is_discounted:
            fields.extend(["base_amount", "discount_percent", "discounted_value"])
            # fields.extend(["gross_amount", "discount_percent", "discounted_value"])

        if vendor_order.is_service_charged:
            fields.extend(["service_charge_percent"])
            # fields.extend(["base_amount", "service_charge_percent"])

        attachment = {
            "name": f"Purchase_Intent_{order_number}_{vendor_name}.xls",
            "url": genrate_and_upload_excel_file(
                vendor_order_id=vendor_order_id, order_number=order_number, fields=fields
            ),
        }
    else:
        attachment = {}

    return {
        "recipient_emails": list(set(final_user_emails)),
        "cc_emails": list(set(cc_emails)),
        "bcc_emails": list(set(bcc_emails)),
        "subject": subject,
        "attachment": attachment,
    }


def send_email_for_cancel_order(*, user: User, vendor_order: VendorOrder):
    email_data: Dict = prepare_email_data(
        vendor_order_id=vendor_order.id,
        project_id=vendor_order.project_id,
        with_attachments=False,
        user_email=user.email,
        org_id=user.token_data.org_id if user.token_data else None,
        is_order_updated=True,
    )
    on_commit(
        partial(
            order_cancel_email_trigger,
            order=vendor_order,
            email_data=email_data,
            user_id=user.id,
        )
    )


class OrderEmailService(DjangoEmailService):
    @staticmethod
    def attach_attachments(attachments: List, email_message: EmailMultiAlternatives) -> EmailMultiAlternatives:
        # TODO: instead of checking instance of attachment, receive attachment as ExcelAttachementData object
        for attachment in attachments:
            response = requests.get(attachment.get("url") if isinstance(attachment, dict) else attachment.url)
            mimetype, _ = mimetypes.guess_type(
                attachment.get("url") if isinstance(attachment, dict) else attachment.url
            )
            email_message.attach(
                attachment.get("name") if isinstance(attachment, dict) else attachment.name,
                response.content,
                mimetype=mimetype,
            )
        return email_message

    def send_email(
        self,
        subject: str,
        vendor_order_id: int,
        to: List,
        cc: List,
        bcc: List,
        from_email: str,
        body: str = None,
        context: Union[Dict, None] = {},
        attachments: Union[List, None] = [],
        html_email_template_name: str = None,
        created_by_id: int = None,
        from_name: str = "Rdash",
    ):
        # TODO: instead of checking instance of attachment, receive attachment as ExcelAttachementData object
        order_email = VendorOrderEmail()
        order_email.cc_receiver = cc
        order_email.subject = "".join(subject.splitlines())
        order_email.bcc_receiver = bcc
        order_email.vendor_order_id = vendor_order_id
        order_email.to_receiver = list(to)
        order_email.files = [
            attachment.get("url") if isinstance(attachment, dict) else attachment.url for attachment in attachments
        ]
        order_email.created_at = timezone.now()
        order_email.full_clean()
        order_email.save()

        return super().send_email(
            subject=subject,
            body=body,
            to=list(to),
            cc=cc,
            bcc=bcc,
            from_email=from_email,
            attachments=attachments,
            context=context,
            html_email_template_name=html_email_template_name,
            created_by_id=created_by_id,
            from_name=from_name,
        )


def get_email_shipping_address(vendor_order: VendorOrder, vendor_id: int, org_id: int) -> str:
    store_address = (
        Project.objects.filter(id=vendor_order.project_id)
        .select_related("store")
        .values_list("store__address", flat=True)
    )
    addresses = get_shipping_address(store_address=store_address[0], org_id_list=[org_id])
    shipping_address_list = [
        address.get("address") for address in addresses if address.get("header") == vendor_order.shipping_address
    ]
    if not shipping_address_list:
        shipping_address = vendor_order.shipping_address
    else:
        shipping_address = shipping_address_list[-1]
    return shipping_address


def get_email_context(
    *,
    vendor_order: VendorOrder,
    custom_message: str,
    po_numbers: str,
    po_total_amount: float,
    user: User,
    vendor_organization_name: str,
    order_number: str,
    org_id: int,
    currency_symbol: str,
) -> Dict:
    return {
        "order_number": order_number,
        "order_from": vendor_order.work_order_from,
        "company_name": vendor_organization_name,
        "created_at": vendor_order.created_at,
        "custom_message": custom_message if custom_message else "",
        "shipping_address": get_email_shipping_address(
            vendor_order=vendor_order, vendor_id=vendor_order.actual_vendor_id, org_id=org_id
        ),
        "sent_by": f"{user.first_name} {user.last_name}",
        "start_date": vendor_order.started_at.date().strftime("%d/%m/%Y"),
        "due_date": vendor_order.due_at.date().strftime("%d/%m/%Y"),
        "order_items": vendor_order.elements_count,
        "amount": formatINR(round(vendor_order.amount, 2)),
        "po_numbers": po_numbers,
        "po_total_amount": format(round(po_total_amount, 2)),
        "has_no_attachments": bool(po_numbers),
        "currency_symbol": currency_symbol,
    }


def get_vendor_org_name(*, vendor_id) -> str:
    vendor = Vendor.objects.filter(organization_id=vendor_id).select_related("organization").first()
    return vendor.organization.name


def po_trigger_event_and_get_po_related_data(
    *, vendor_order: VendorOrder
) -> Tuple[str, str, List[ExcelAttachmentData]]:
    purchase_orders: List[VendorPurchaseOrder] = order_purchase_orders_fetch(vendor_order_id=vendor_order.pk)
    po_numbers = ""
    po_total_amount = 0
    po_attachments = []
    for purchase_order in purchase_orders:
        po_trigger_data = PurchaseOrderTriggerData(
            order_id=vendor_order.id,
            project_id=vendor_order.project_id,
            order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
            order_org_from_id=vendor_order.org_from_id,
            order_org_to_id=vendor_order.org_to_id,
            uploaded_by_id=purchase_order.uploaded_by_id,
            project_job_id=vendor_order.project.job_id,
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
        )
        on_commit(
            partial(
                po_upload_trigger,
                po_trigger_data=po_trigger_data,
            )
        )
        po_attachments.append(
            ExcelAttachmentData(
                name=purchase_order.name + get_file_extension(filepath=purchase_order.file.url),
                url=purchase_order.file.url,
            )
        )
        po_numbers += f"{purchase_order.po_number},"
        po_total_amount += purchase_order.amount
    return po_numbers.rstrip(","), po_total_amount, po_attachments


def get_elements_excel_attachment(
    *, order_number: str, vendor_id: int, order_elements: List[VendorOrderElement]
) -> Dict:
    fields = [
        "item_name",
        "description",
        "item_code",
        "item_type",
        "Quantity",
        "UOM",
        "order_rate",
        "Amount",
    ]
    return {
        "name": f"Purchase_Intent_{order_number}_{get_vendor_name(vendor_id=vendor_id)}.xls",
        "url": genrate_and_upload_excel_file_v2(
            order_number=order_number, fields=fields, order_elements=order_elements
        ),
    }


def order_send_email_v2(
    *,
    vendor_order: VendorOrder,
    project_id: int,
    data: OrderCreateAndSentData,
    user: User,
    org_id: int,
    order_elements: List[VendorOrderElement] = None,
):
    order_number = get_order_number(vendor_order=vendor_order, project_id=project_id)
    vendor_id = vendor_order.actual_vendor_id
    data.bcc = list(set(data.bcc).union(set(bcc_recipient_emails_fetch(org_id=vendor_order.org_to_id))))
    # TODO : patch need to be removed once, org config order receiver data is corrected in org
    data.bcc = list(set(org_config_bcc_emails_get(org_id=vendor_order.org_to_id)).union(set(data.bcc)))
    assign_role_and_permissions(
        vendor_users_email=data.to,
        to_assign_org=vendor_id,
        project_id=project_id,
        assigned_by_org=vendor_order.origin_org_id,
        created_by_id=user.id,
    )
    on_commit(
        partial(
            order_sent_trigger,
            vendor_order_id=vendor_order.pk,
            org_to_id=vendor_id,
            org_from_id=org_id,
            order_number=order_number,
            project_id=vendor_order.project_id,
        )
    )
    on_commit(partial(order_sent_email_trigger, order=vendor_order, order_entity=data))
    return vendor_order
