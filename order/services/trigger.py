from typing import Dict

import structlog
from django.conf import settings

from common.events.actions.order import (
    OrderCancelEmailActionData,
    OrderModifyEmailActionData,
    OrderSentEmailActionData,
    POEmailActionData,
)
from common.events.constants import Events
from common.events.order import (
    OrderCancelledEventData,
    OrderCompleteEventData,
    OrderCreatedEventdata,
    OrderModifiedEventData,
    OrderSentEventData,
    OrderSentWithoutNotificationEventData,
    PurchaseOrderCancelledEventData,
    PurchaseOrderRevisedEventData,
    PurchaseOrderUploadEventData,
)
from common.events.services import trigger_event
from order.data.models import VendorOrder
from order.domain.entities.domain_entities import POEmailData
from order.domain.entities.entities import OrderData, PurchaseOrderTriggerData

logger = structlog.getLogger(__name__)


def order_cancel_trigger(vendor_order: VendorOrder):
    event_data = OrderCancelledEventData(
        project_id=vendor_order.project_id,
        order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
        order_id=vendor_order.id,
        org_from_id=vendor_order.org_from_id,
        org_to_id=vendor_order.org_to_id,
    )
    trigger_event(event=Events.ORDER_CANCELLED, event_data=event_data)


# def order_modified_trigger(vendor_order: VendorOrder):
#     event_data = OrderModifiedEventData(
#         project_id=vendor_order.project_id,
#         order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
#         order_id=vendor_order.id,
#         org_from_id=vendor_order.org_from_id,
#         org_to_id=vendor_order.org_to_id,
#     )
#     trigger_event(event=Events.ORDER_MODIFIED, event_data=event_data)


def order_sent_trigger(vendor_order_id: str, org_to_id: int, org_from_id: int, order_number: str, project_id: int):
    logger.info("order_sent_trigger", vendor_order_id=vendor_order_id)
    event_data = OrderSentEventData(
        order_id=vendor_order_id,
        org_from_id=org_from_id,
        org_to_id=org_to_id,
        order_number=order_number,
        project_id=project_id,
    )
    trigger_event(event=Events.ORDER_SENT, event_data=event_data)


def order_sent_without_notification_trigger(
    vendor_order_id: str, org_to_id: int, org_from_id: int, order_number: str, project_id: int
):
    logger.info("order_sent_trigger", vendor_order_id=vendor_order_id)
    event_data = OrderSentWithoutNotificationEventData(
        order_id=vendor_order_id,
        org_from_id=org_from_id,
        org_to_id=org_to_id,
        order_number=order_number,
        project_id=project_id,
    )
    trigger_event(event=Events.ORDER_SENT_WITHOUT_NOTFICATION, event_data=event_data)


def order_complete_trigger(vendor_order_id: int, project_id: int):
    event_data = OrderCompleteEventData(order_id=vendor_order_id, project_id=project_id)
    trigger_event(event=Events.ORDER_COMPLETED, event_data=event_data)


def po_cancelled_trigger(vendor_order: VendorOrder, cancelled_by_id: int):
    event_data = PurchaseOrderCancelledEventData(
        project_id=vendor_order.project_id,
        user_id=cancelled_by_id,
        order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
        order_id=vendor_order.id,
        org_from_id=vendor_order.org_from_id,
        org_to_id=vendor_order.org_to_id,
    )
    trigger_event(Events.PO_CANCELLED, event_data=event_data)


def po_upload_trigger(po_trigger_data: PurchaseOrderTriggerData):
    event_data = PurchaseOrderUploadEventData(
        order_id=po_trigger_data.order_id,
        project_id=po_trigger_data.project_id,
        order_number=f"{po_trigger_data.project_job_id}/{po_trigger_data.order_number}",
        po_id=po_trigger_data.po_id,
        org_from_id=po_trigger_data.order_org_from_id,
        org_to_id=po_trigger_data.order_org_to_id,
        user_id=po_trigger_data.uploaded_by_id,
        po_number=po_trigger_data.po_number,
    )
    trigger_event(event=Events.PO_UPLOADED, event_data=event_data)


def po_email_trigger(po_email_data: POEmailData, event: Events):
    event_data = POEmailActionData(
        receiver_emails=po_email_data.to,
        user_id=po_email_data.created_by_id,
        purchase_order_id=po_email_data.purchase_order_id,
    )
    trigger_event(event=event, event_data=event_data)


def po_revised_trigger(po_trigger_data: PurchaseOrderTriggerData):
    event_data = PurchaseOrderRevisedEventData(
        order_id=po_trigger_data.order_id,
        project_id=po_trigger_data.project_id,
        order_number=f"{po_trigger_data.project_job_id}/{po_trigger_data.order_number}",
        po_id=po_trigger_data.po_id,
        org_from_id=po_trigger_data.order_org_from_id,
        org_to_id=po_trigger_data.order_org_to_id,
        user_id=po_trigger_data.uploaded_by_id,
        po_number=po_trigger_data.po_number,
    )
    trigger_event(event=Events.PO_REVISED, event_data=event_data)


def order_cancel_email_trigger(order: VendorOrder, email_data: Dict, user_id: int):
    event_data = OrderCancelEmailActionData(
        from_name=order.org_from.name.title(),
        shipping_address=order.shipping_address,
        order_id=order.pk,
        user_id=user_id,
        subject=email_data["subject"],
        from_email=settings.EMAIL_HOST_USER,
        body=None,
        cc=email_data["cc_emails"],
        bcc=email_data["bcc_emails"],
        attachments=[],
        receiver_emails=email_data["recipient_emails"],
    )
    trigger_event(event=Events.ORDER_CANCEL_WITHOUT_PROPOSAL, event_data=event_data)


def order_created_trigger_event(order_id: int, project_id: int):
    trigger_event(
        event=Events.ORDER_CREATED, event_data=OrderCreatedEventdata(order_id=order_id, project_id=project_id)
    )


def order_sent_email_trigger_v2(order: OrderData, event: Events):
    event_data = OrderSentEmailActionData(
        from_name=order.org_from_name,
        shipping_address=order.shipping_address,
        order_id=order.id,
        user_id=order.issued_by_id,
        subject=order.email_data.get("subject"),
        from_email=settings.EMAIL_HOST_USER,
        body=order.email_data.get("body"),
        cc=order.email_data.get("cc_receiver"),
        bcc=order.email_data.get("bcc_receiver"),
        attachments=(
            [
                {"name": attachment.get("name"), "url": attachment.get("url")}
                for attachment in order.email_data.get("attachments")
            ]
            if order.email_data.get("attachments")
            else []
        ),
        receiver_emails=order.email_data.get("to_receiver"),
    )
    trigger_event(event=event, event_data=event_data)


def order_modified_trigger_v2(order: OrderData):
    event_data = OrderModifiedEventData(
        project_id=order.project_id,
        order_number=f"{order.job_id}/{order.order_number}",
        order_id=order.id,
        org_from_id=order.org_from_id,
        org_to_id=order.vendor_id,
        status=order.outgoing_status,
    )
    trigger_event(event=Events.ORDER_MODIFIED, event_data=event_data)


def draft_order_modified_trigger(order: OrderData):
    event_data = OrderModifiedEventData(
        project_id=order.project_id,
        order_number=f"{order.job_id}/{order.order_number}",
        order_id=order.id,
        org_from_id=order.org_from_id,
        org_to_id=order.vendor_id,
        status=order.outgoing_status,
    )
    trigger_event(event=Events.DRAFT_ORDER_MODIFIED, event_data=event_data)


def order_modify_email_trigger_v2(order: OrderData, event: Events):
    event_data = OrderModifyEmailActionData(
        from_name=order.org_from_name,
        shipping_address=order.shipping_address,
        order_id=order.id,
        user_id=order.issued_by_id,
        subject=order.email_data["subject"],
        from_email=settings.EMAIL_HOST_USER,
        body=order.email_data["body"],
        cc=order.email_data["cc_receiver"],
        bcc=order.email_data["bcc_receiver"],
        attachments=[],
        receiver_emails=order.email_data["to_receiver"],
        elements=[],
        old_order_value=order.old_order_value,
        snapshot_id=order.snapshot_id,
    )
    trigger_event(event=event, event_data=event_data)
