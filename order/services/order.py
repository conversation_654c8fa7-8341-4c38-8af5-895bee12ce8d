import abc
import decimal
import json
import mimetypes
from collections import defaultdict
from datetime import date, datetime
from functools import partial
from typing import Any, Dict, List, Optional, Tuple, Union

import structlog
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Count, DecimalField, F, IntegerField, OuterRef, Prefetch, Q, Subquery, Sum, Value
from django.db.models.functions import Coalesce
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import quote_sheetname
from openpyxl.writer.excel import save_virtual_workbook
from rest_framework.fields import FileField
from rest_framework.settings import api_settings

from approval_request.domain.constants import RequestStatusEnum
from authorization.domain.constants import Permissions
from boq.data.choices import BoqElementAction, BoqElementStatus
from boq.data.models import BoqElement
from boq.data.selectors import boq_elements_fetch_for_vendor_wise_scope
from boq.domain.abstract_repos import BoqElementSyncAbstractRepo
from boq.services.boq import update_boq_status
from boq.services.status_history import StatusService
from common.choices import OrderType, RoleType, VendorStatusChoices
from common.element_base.entities import (
    GuidelineAttachmentData,
    GuidelineData,
    ProductionDrawingData,
)
from common.element_base.services import ElementCodeService
from common.entities import ObjectStatus, ProjectOrganizationEntity
from common.events.constants import Events
from common.excel_file_generation import (
    create_excel_file,
    excel_data_file_with_no_record,
    get_excel_data_validation,
)
from common.helpers import calculate_element_final_amount
from common.services import model_update, nested_object_segregation
from common.signals import order_updated_signal
from controlroom.data.querysets import QuerySet
from core.caches import OrganizationUOMCache, RolePermissionCache
from core.helpers import OrgPermissionHelper
from core.models import (
    FromToOrgMapping,
    OrganizationConfig,
    OrganizationConfigRole,
    OrganizationUser,
    RolePermission,
    User,
)
from core.organization.data.selectors import get_context_config_by_country
from core.organization.domain.entities import OrganizationSectionData
from core.organization.models import OrganizationDocumentFieldContextConfig
from core.selectors import get_tax_slab_list, org_config_bcc_emails_get, organization_get
from core.services import check_if_organization_po_flow_enabled
from element.data.models import ElementItemType
from element.data.selectors import element_category_list
from external_services.request.request import RequestService
from external_services.request.schema import RetryOption
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import MicroContext
from order.data.models import (
    Deduction,
    OrderRequestMapping,
    OrderReview,
    OrderReviewDocument,
    OrderSnapshot,
    OrderStatusHistory,
    OrderTextFieldData,
    TermsAndConditionAttachment,
    VendorOrder,
    VendorOrderElement,
    VendorOrderElementGuideline,
    VendorOrderEmail,
    VendorOrderFieldHistory,
    VendorPurchaseOrder,
)
from order.data.selectors.selector_v1 import (
    get_grouped_elements,
    get_grouped_elements_v2,
    get_last_snapshot_created_at,
    get_order_from_id,
    get_orders_from_vendor_id,
    get_organizations_users_emails,
    order_details_for_email,
    order_element_list,
    order_element_list_using_vendor_order,
    order_elements_qty_dimensions_exists,
    vendor_order_fetch,
    vendor_order_field_history_fetch_all,
)
from order.data.selectors.selectors import fetch_vendor_order_with_linked_elements
from order.domain.abstract_repos import (
    OrderAbstractRepo,
    OrderElementSyncAbstractRepo,
    OrderRequestMappingAbstractRepo,
    OrderSnapshotAbstractRepo,
)
from order.domain.constants import OrderEventEnum, OrderStatusEnum, OrderTypeEnum
from order.domain.entities.domain_entities import (
    DeductionAttachmentData,
    DeductionData,
    ExcelAttachmentData,
    MarkOrderCompleteData,
    OrderCreateAndSentData,
    OrderCreateData,
    OrderElementBaseData,
    OrderElementCreateData,
    OrderElementCreateDataV2,
    OrderElementUpdateData,
    OrderSentInitializeData,
    OrderUpdateData,
    POEmailData,
    TermsAndConditionsAttachmentsBaseData,
    TermsAndConditionsAttachmentsData,
    TermsAndConditionsAttachmentsUpdateData,
)
from order.domain.entities.entities import (
    OrderData,
    OrderRequestCancelData,
    OrderRequestCreateData,
    OrderSnapshotData,
    PurchaseOrderTriggerData,
    VendorInfoOrganizationCountryConfigData,
    VendorOrderElementData,
)
from order.domain.excel_utils import prepare_excel_payment_term_and_conditions_data
from order.domain.exceptions import (
    OrderCancelledItemException,
    OrderElementNotFoundException,
    OrderNotFoundException,
    OrderStatusException,
    OrderWithoutElementException,
    VendorNotFoundException,
    VendorStatusNotActiveException,
    VendorStatusNotActiveOrOnboardedException,
)
from order.domain.factories import OrderRequestServiceFactory
from order.domain.services.helper_service import (
    get_email_dict,
)
from order.domain.services.triggers import OrderTriggerService
from order.domain.status_choices import OrderStatus, POStatus
from order.interface.constants import PROMPT_MESSAGE
from order.interface.vendor_serializers import VendorOrderElementGuidelineExportSerializer
from order.invoice.data.models import Invoice
from order.invoice.domain.services.invoice import InvoiceService
from order.services.deduction import DeductionAndAttachmentService
from order.services.excel import (
    prepare_excel_summary_data_v2,
    serializer_element_data,
)
from order.services.proposal import generate_proposal_and_order_number
from order.services.purchase_order import vendor_po_and_invoice_cancel_many
from order.services.trigger import (
    order_cancel_trigger,
    order_complete_trigger,
    order_modify_email_trigger,
    po_email_trigger,
    po_revised_trigger,
    po_upload_trigger,
)
from payment_request.data.choices import PaymentRequestStatus
from payment_request.data.models import PaymentRequest
from project.data.models import Project, ProjectOrganization, ProjectUser
from project.domain.caches import (
    ProjectAssignmentCache,
    ProjectCountryConfigCache,
    ProjectDataCache,
    WorkProgressChartCache,
)
from project.domain.entities import ProjectCountryConfigData
from project.domain.helpers import ProjectPermissionHelper
from project.domain.status import RDStatus
from project.selectors import project_status_history_for_work_report_fetch
from project.share.domain.constants import PERMISSION_SET_MAPPING, PermissionGroup
from proposal.data.models import Proposal
from proposal.domain.constants import ProposalStatus
from rollingbanners.comment_base_service import CommentBaseService
from rollingbanners.hash_id_converter import HashIdConverter
from rollingbanners.settings import VMS_HOST, VMS_TOKEN
from vendor.data.models import Vendor
from vendorv2.data.models import ClientVendorMapping
from vendorv2.domain.services.vendor import VendorOrganizationService, get_vendor_onboard_config
from vms.constants import ORDER_COUNT_OF_MONTHS
from work_progress_v2.domain.helper import WorkProgressScopeDataHelper
from work_progress_v2.domain.helper_entities import SectionTotalProgressElementEntity

logger = structlog.getLogger(__name__)
CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


def get_order_version(*, org_from_id: int, proposal_number: Optional[int]) -> int:
    if proposal_number:
        return 3
    return 5

    # org_config = OrganizationConfig.objects.filter(organization_id=org_from_id).first()
    # return 5 if org_config and org_config.order_flow == OrganizationConfig.OrderFlowChoices.PO_FLOW else 3


def order_update_after_operation(user_id: int, vendor_order_id: int):
    VendorOrder.objects.filter(id=vendor_order_id).update(updated_at=timezone.now(), updated_by_id=user_id)


def generate_item_type_and_category_list(data: dict):
    item_type_list = []
    category_list = []
    item_type_id_set = set()
    category_id_set = set()
    if data.get("item_type_list", None) or data.get("category_list", None):
        item_type_list = data["item_type_list"]
        category_list = data["category_list"]
        return item_type_list, category_list
    if data["order_elements"]:
        for element in data["order_elements"]:
            item_type = element.get("item_type")
            category = element.get("category")
            if item_type:
                if item_type.get("id") not in item_type_id_set:
                    item_type_list.append(item_type)
                    item_type_id_set.add(item_type.get("id"))
            if category:
                if category.get("id") not in category_id_set:
                    category_list.append(category)
                    category_id_set.add(category.get("id"))
    return item_type_list, category_list


def purchase_order_number_validation(po_number, org_id):
    if (
        VendorPurchaseOrder.objects.exclude(status=POStatus.PO_CANCELLED)
        .select_related("vendor_order")
        .filter(
            po_number=po_number, org_id=org_id, vendor_order_id__isnull=False, vendor_order__deleted_at__isnull=True
        )
        .exists()
    ):
        raise ValidationError({"po_number": "PO Number Already exist"})


def purchase_order_tax_amount_validation(tax_amount, amount, project_id):
    project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=project_id)
    max_slab_percent = project_config.tax_type.max_slab_percent
    if round(decimal.Decimal(tax_amount), 2) > round(
        decimal.Decimal((decimal.Decimal(amount) * decimal.Decimal(max_slab_percent) / 100)), 2
    ):
        raise ValidationError(f"Tax amount must not be greater than {max_slab_percent}% of total amount.")


def order_status_check_for_po_upload(vendor_order_id: int, organization_id: int):
    order_instance = VendorOrder.objects.filter(id=vendor_order_id).first()
    if (order_instance.origin_org_id == organization_id and order_instance.org_to == organization_id) or (
        order_instance.outgoing_status
        in [
            VendorOrder.OutgoingStatus.CANCELLED,
            VendorOrder.OutgoingStatus.COMPLETED,
            VendorOrder.OutgoingStatus.CLOSED,
        ]
    ):
        raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Cannot Upload PO for this Order"})


# TODO: Remove this function
# def create_order_entity(*, vendor_order: VendorOrder, attachments: List[ExcelAttachmentData], final_amount: float):
#     return OrderCreateAndSentData(
#         order_type=OrderType.OUTGOING,
#         started_at=vendor_order.started_at,
#         due_at=vendor_order.due_at,
#         terms_and_conditions=vendor_order.terms_and_conditions,
#         org_to_id=vendor_order.org_to_id,
#         work_order_from=vendor_order.work_order_from,
#         shipping_address=vendor_order.shipping_address,
#         shipping_address_header=vendor_order.shipping_address_header,
#         to=vendor_order.email_data.get("to_receiver"),
#         cc=vendor_order.email_data.get("cc_receiver"),
#         bcc=vendor_order.email_data.get("bcc_receiver"),
#         subject=vendor_order.email_data.get("subject"),
#         body=vendor_order.email_data.get("body"),
#         attachments=attachments,
#         final_amount=final_amount,
#         origin_org_id=vendor_order.origin_org_id,
#         org_from_id=vendor_order.org_from_id,
#         elements=[],
#         rate_contract_id=vendor_order.rate_contract_id,
#         deductions=[],
#         payment_term_id=vendor_order.payment_term_id,
#         payment_term_text=vendor_order.payment_term_text,
#         terms_and_conditions_attachments=[],
#         purchase_orders=[],
#         proposal_id=None,
#         poc_id=vendor_order.poc_id,
#         gst_number=vendor_order.gst_number if hasattr(vendor_order, "gst_number") else None,
#         gst_state=vendor_order.gst_state if hasattr(vendor_order, "gst_state") else None,
#         type_of_order=vendor_order.order_type,
#     )

# TODO: Remove this function
# def create_element_entity_data(*, order_elements: List[VendorOrderElement]):
#     element_entities = []
#     for element in order_elements:
#         production_drawing_entities = []
#         guideline_entities = []

#         for production_drawing in element.production_drawings.all():
#             production_drawing_entities.append(
#                 ProductionDrawingData(
#                     object_status=ObjectStatus.ADD,
#                     name=production_drawing.name,
#                     file=production_drawing.file,
#                     tags=[tag.id for tag in production_drawing.tags.all()],
#                 )
#             )
#         for guidelines in element.guidelines.all():
#             guideline_entities.append(
#                 GuidelineData(
#                     object_status=ObjectStatus.ADD,
#                     name=guidelines.name,
#                     description=guidelines.description,
#                     attachments=[
#                         GuidelineAttachmentData(
#                             object_status=ObjectStatus.ADD,
#                             file=attachment.file,
#                             name=attachment.name,
#                             type=attachment.type,
#                         )
#                         for attachment in guidelines.attachments.all()
#                     ],
#                 )
#             )
#         element_entities.append(
#             OrderElementCreateDataV2(
#                 id=element.pk,
#                 name=element.name,
#                 description=element.description,
#                 uom=element.uom,
#                 client_id=element.client_id,
#                 serial_number=element.serial_number,
#                 boq_element_id=element.boq_element_id,
#                 el_element_id=element.el_element_id,
#                 boq_element_version=element.boq_element_version,
#                 custom_type=element.custom_type,
#                 category_id=element.category_id,
#                 item_type_id=element.item_type_id,
#                 code=element.code,
#                 preview_files=element.preview_files.all(),
#                 guidelines=guideline_entities,
#                 production_drawings=production_drawing_entities,
#                 cancelled_element_id=None,
#                 linked_element_id=None,
#                 quantity=element.quantity,
#                 quantity_dimensions=element.quantity_dimensions,
#                 vendor_rate=element.vendor_rate,
#                 client_rate=0,
#             )
#         )
#     return element_entities


def vendor_purchase_order_create(
    *,
    file: InMemoryUploadedFile,
    uploaded_by: User,
    vendor_order_id: int,
    po_number: str,
    po_date: datetime,
    name: str,
    amount: float,
    type: str,
    org_id: int,
    project_id: int,
    tax_amount: float = 0,
):
    purchase_order_number_validation(po_number=po_number, org_id=org_id)
    purchase_order_tax_amount_validation(tax_amount=tax_amount, amount=amount, project_id=project_id)
    purchase_order = VendorPurchaseOrder(
        file=file,
        name=name,
        uploaded_by_id=uploaded_by.pk,
        vendor_order_id=vendor_order_id,
        status=POStatus.PO_ACTIVE,
        po_number=po_number,
        po_date=po_date,
        amount=amount,
        type=type,
        org_id=org_id,
        tax_amount=tax_amount,
    )
    purchase_order.full_clean()
    purchase_order.save()
    if vendor_order_id:
        vendor_order: VendorOrder = (
            VendorOrder.objects.select_related("org_from", "project").filter(id=vendor_order_id).get()
        )
        fields = ["po_status", "po_attached_at"]
        to_update_data = {"po_status": POStatus.PO_ATTACHED, "po_attached_at": timezone.now()}
        vendor_order, _, _ = model_update(
            instance=vendor_order, fields=fields, data=to_update_data, updated_by_id=uploaded_by.pk
        )
        if (
            vendor_order.outgoing_status in [VendorOrder.OutgoingStatus.SENT, OrderStatusEnum.APPROVED.value]
            or (vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value and vendor_order.snapshots.all())
            or vendor_order.status == OrderStatus.SENT
        ):
            if vendor_order.org_from_id == vendor_order.origin_org_id:
                if vendor_order.to_send_email:
                    if not vendor_order.email_data:
                        raise ValidationError("Email Data not found")
                    else:
                        PoUploadEmail.send_email(
                            purchase_order=purchase_order,
                            vendor_order=vendor_order,
                            user=uploaded_by,
                            event=Events.PO_UPLOADED_EMAIL,
                        )
                po_trigger_data = PurchaseOrderTriggerData(
                    order_id=vendor_order_id,
                    project_id=project_id,
                    order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
                    order_org_from_id=vendor_order.org_from_id,
                    order_org_to_id=vendor_order.org_to_id,
                    uploaded_by_id=uploaded_by.pk,
                    project_job_id=vendor_order.project.job_id,
                    po_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
                on_commit(
                    partial(
                        po_upload_trigger,
                        po_trigger_data=po_trigger_data,
                    )
                )
    return purchase_order


# TODO: Move this function to helpers/utils
def get_file_type_from_url(file_url: str):
    file_type, _ = mimetypes.guess_type(file_url)

    image_types = ["image/png", "image/jpeg", "image/jpg"]
    document_types = [
        "text/plain",  # TXT
        "application/pdf",  # PDF
        "application/msword",  # DOC
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # DOCX
        "application/vnd.ms-excel",  # XLS
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # XLSX
        "application/vnd.ms-powerpoint",  # PPT
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",  # PPTX
    ]

    if file_type:
        if file_type in image_types:
            return VendorPurchaseOrder.IMAGE
        elif file_type in document_types:
            return VendorPurchaseOrder.DOC

    raise ValidationError(
        {
            "file_url": "Invalid file type provided. Supported file"
            " formats: PNG, JPG, JPEG, TXT, XLS, XLSX, DOC, DOCX, PDF, PPT, PPTX."
        }
    )


def create_client_purchase_order_v2(
    *,
    file: FileField,
    uploaded_by: User,
    vendor_order_id: int,
    po_number: str,
    po_date: datetime,
    name: str,
    amount: float,
    type: str,
    org_id: int,
    project_id: int,
    tax_amount: float = 0,
):
    if not VendorOrder.objects.filter(id=vendor_order_id, project_id=project_id, org_to_id=org_id).exists():
        raise ValidationError({"order_id": "Client Order not found for this project"})

    return create_purchase_order_v2(
        file=file,
        uploaded_by=uploaded_by,
        vendor_order_id=vendor_order_id,
        po_number=po_number,
        name=name,
        amount=amount,
        type=type,
        po_date=po_date,
        org_id=org_id,
        tax_amount=tax_amount,
        project_id=project_id,
    )


def create_vendor_purchase_order_v2(
    *,
    file: FileField,
    uploaded_by: User,
    vendor_order_id: int,
    po_number: str,
    po_date: datetime,
    name: str,
    amount: float,
    type: str,
    org_id: int,
    project_id: int,
    tax_amount: float = 0,
):
    if (
        not VendorOrder.objects.filter(id=vendor_order_id, project_id=project_id)
        .filter(~Q(org_to_id=org_id), origin_org_id=org_id)
        .exists()
    ):
        raise ValidationError({"order_id": "Vendor Order not found for this project"})

    return create_purchase_order_v2(
        file=file,
        uploaded_by=uploaded_by,
        vendor_order_id=vendor_order_id,
        po_number=po_number,
        name=name,
        amount=amount,
        type=type,
        po_date=po_date,
        org_id=org_id,
        tax_amount=tax_amount,
        project_id=project_id,
    )


def create_purchase_order_v2(
    *,
    file: FileField,
    uploaded_by: User,
    vendor_order_id: int,
    po_number: str,
    po_date: datetime,
    name: str,
    amount: float,
    type: str,
    org_id: int,
    project_id: int,
    tax_amount: float = 0,
):
    purchase_order_number_validation(po_number=po_number, org_id=org_id)
    purchase_order_tax_amount_validation(tax_amount=tax_amount, amount=amount, project_id=project_id)
    purchase_order = VendorPurchaseOrder(
        file=file,
        name=name,
        uploaded_by_id=uploaded_by.pk,
        vendor_order_id=vendor_order_id,
        status=POStatus.PO_ACTIVE,
        po_number=po_number,
        po_date=po_date,
        amount=amount,
        type=type,
        org_id=org_id,
        tax_amount=tax_amount,
    )
    purchase_order.full_clean()
    purchase_order.save()
    if vendor_order_id:
        vendor_order: VendorOrder = (
            VendorOrder.objects.select_related("org_from", "project").filter(id=vendor_order_id).get()
        )
        fields = ["po_status", "po_attached_at"]
        to_update_data = {"po_status": POStatus.PO_ATTACHED, "po_attached_at": timezone.now()}
        vendor_order, _, _ = model_update(
            instance=vendor_order, fields=fields, data=to_update_data, updated_by_id=uploaded_by.pk
        )
        if (
            vendor_order.outgoing_status in [VendorOrder.OutgoingStatus.SENT, OrderStatusEnum.APPROVED.value]
            or (vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value and vendor_order.snapshots.all())
            or vendor_order.status == OrderStatus.SENT
        ):
            if vendor_order.org_from_id == vendor_order.origin_org_id:
                if not vendor_order.email_data:
                    raise ValidationError("Email Data not found")
                else:
                    PoUploadEmail.send_email(
                        purchase_order=purchase_order,
                        vendor_order=vendor_order,
                        user=uploaded_by,
                        event=Events.PO_UPLOADED_EMAIL,
                    )
                po_trigger_data = PurchaseOrderTriggerData(
                    order_id=vendor_order_id,
                    project_id=project_id,
                    order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
                    order_org_from_id=vendor_order.org_from_id,
                    order_org_to_id=vendor_order.org_to_id,
                    uploaded_by_id=uploaded_by.pk,
                    project_job_id=vendor_order.project.job_id,
                    po_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
                on_commit(
                    partial(
                        po_upload_trigger,
                        po_trigger_data=po_trigger_data,
                    )
                )
    return purchase_order


def vendor_purchase_order_update(
    *, updated_by_id, purchase_order_id: int, vendor_order_id: int, data: Dict, org_id: int, project_id: int
):
    fields = ["name", "po_number", "amount", "file", "type", "po_date", "tax_amount"]
    purchase_order = VendorPurchaseOrder.objects.filter(id=purchase_order_id).first()
    if vendor_order_id:
        vendor_order: VendorOrder = VendorOrder.objects.filter(id=vendor_order_id).first()
        if not vendor_order:
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Invalid Vendor Order"})
        if vendor_order.status in [OrderStatus.SENT, OrderStatus.COMPLETED] or vendor_order.outgoing_status in [
            VendorOrder.OutgoingStatus.COMPLETED,
            VendorOrder.OutgoingStatus.SENT,
        ]:
            raise ValidationError(
                {api_settings.NON_FIELD_ERRORS_KEY: "Purchase Order with order status sent cannot be updated"}
            )
        vendor_order.updated_by_id = updated_by_id
        vendor_order.updated_at = timezone.now()
        vendor_order.save(update_fields=["updated_by_id", "updated_at"])
    purchase_order, _, updated_fields = model_update(
        instance=purchase_order, data=data, updated_by_id=updated_by_id, fields=fields, save=False
    )
    if "po_number" in updated_fields:
        purchase_order_number_validation(po_number=purchase_order.po_number, org_id=org_id)
    if "tax_amount" in updated_fields or "amount" in updated_fields:
        purchase_order_tax_amount_validation(
            tax_amount=purchase_order.tax_amount,
            amount=purchase_order.amount,
            project_id=project_id,
        )
    purchase_order.save(update_fields=updated_fields)
    return purchase_order


def vendor_element_delete_many(
    *,
    vendor_order: VendorOrder,
    order_elements: VendorOrderElement,
    user: User,
    html_message: str = None,
    updated_fields: Union[List, None] = None,
    modified_elements_count: int = 0,
    order_type: str = OrderType.OUTGOING,
):
    from .element import set_changed_order_status

    if not vendor_order:
        raise ValidationError({"vendor_order_id", "Vendor Order not found"})

    new_elements_count = vendor_order.elements_count - len(order_elements)
    if vendor_order.status in [OrderStatus.SENT, OrderStatus.COMPLETED] or vendor_order.outgoing_status in [
        VendorOrder.OutgoingStatus.COMPLETED,
        VendorOrder.OutgoingStatus.SENT,
        OrderStatusEnum.SENT.value,
        OrderStatusEnum.PENDING_APPROVAL.value,
        OrderStatusEnum.APPROVED.value,
    ]:
        order_elements.update(
            status=OrderStatus.CANCELLED,
            cancelled_at=timezone.now(),
            cancelled_by_id=user.id,
        )
        on_commit(partial(order_cancel_trigger, vendor_order=vendor_order))
        if order_type == OrderType.OUTGOING:
            prepare_and_cancel_order_email(vendor_order=vendor_order, user=user)
    else:
        order_elements.update(deleted_at=timezone.now(), deleted_by_id=user.id)

    vendor_order, _ = set_changed_order_status(
        vendor_order=vendor_order, user=user, total_element_count=new_elements_count, order_type=order_type
    )


def map_purchase_order_with_vendor_order(purchase_orders: List[int], vendor_order_id: int):
    VendorPurchaseOrder.objects.filter(id__in=purchase_orders).update(vendor_order_id=vendor_order_id)


def process_order_permissions(project_id: int, org_to_id: int, org_from_id: int) -> None:
    if VendorOrder.objects.filter(
        project_id=project_id,
        org_to_id=org_to_id,
        org_from_id=org_from_id,
        outgoing_status__in=[
            VendorOrder.OutgoingStatus.SENT,
            VendorOrder.OutgoingStatus.COMPLETED,
            VendorOrder.OutgoingStatus.MODIFIED,
        ],
    ).exists():
        return

    ProjectOrganization.objects.select_related("role").filter(
        project_id=project_id, organization_id=org_to_id, assigned_by_id=org_from_id, role__role_type=RoleType.GENERIC
    ).delete()

    role_id = (
        ProjectOrganization.objects.select_related("role")
        .filter(
            project_id=project_id,
            organization_id=org_to_id,
            assigned_by_id=org_from_id,
            role__role_type=RoleType.DYNAMIC,
            role__updated_at__isnull=True,
        )
        .values_list("role_id", flat=True)
        .first()
    )

    if role_id:
        RolePermission.objects.filter(role_id=role_id).exclude(
            permission__in=PERMISSION_SET_MAPPING.get(PermissionGroup.PROJECT_LEVEL_1)
        ).delete()
        RolePermissionCache.delete(instance_id=role_id)

    ProjectAssignmentCache.delete(instance_id=project_id)


def vendor_order_cancel_delete(
    *,
    vendor_order_id: int,
    user: User,
    order_repo: OrderAbstractRepo,
    mapping_repo: OrderRequestMappingAbstractRepo,
    boq_sync_repo: BoqElementSyncAbstractRepo,
    order_sync_repo: OrderElementSyncAbstractRepo,
    order_snapshot_repo: OrderSnapshotAbstractRepo,
):
    from .email import send_email_for_cancel_order

    vendor_order: VendorOrder = (
        VendorOrder.objects.select_related("project")
        .filter(id=vendor_order_id)
        .annotate(
            request_id=Subquery(
                OrderRequestMapping.objects.filter(
                    resource_id=OuterRef("id"),
                    request__status=RequestStatusEnum.PENDING.value,
                )
                .order_by("-created_at")
                .values("request_id")[:1]
            )
        )
        .annotate_last_approved_snapshot()
        .first()
    )
    if not vendor_order:
        raise ValidationError({"vendor_order_id": "Invalid Vendor Order"})
    if vendor_order.outgoing_status in [
        VendorOrder.OutgoingStatus.SENT,
        VendorOrder.OutgoingStatus.COMPLETED,
        OrderStatusEnum.SENT.value,
        OrderStatusEnum.PENDING_APPROVAL.value,
        OrderStatusEnum.APPROVED.value,
    ]:
        elements_to_cancel: List[VendorOrderElement] = VendorOrderElement.objects.filter(
            vendor_order_id=vendor_order_id
        ).all()
        linked_elements = [element.linked_element_id for element in elements_to_cancel if element.linked_element]
        boq_elements = BoqElement.objects.filter(id__in=linked_elements).all()
        for element in boq_elements:
            setattr(element, "quantity", 0)
            setattr(element, "element_status", BoqElementStatus.CANCELLED)
        history_data_list = StatusService.create_history_entity(
            elements=boq_elements,
            action=BoqElementAction.CANCELLED,
            source=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
        )
        StatusService.create_bulk(history_data_list=history_data_list, user_id=user.pk)
        BoqElement.objects.filter(id__in=linked_elements).update(
            quantity=0, element_status=BoqElementStatus.CANCELLED, updated_by_id=user.pk
        )
        elements_to_cancel.update(
            quantity=0, status=OrderStatus.CANCELLED, cancelled_at=timezone.now(), cancelled_by_id=user.id
        )

        Deduction.objects.filter(order_id=vendor_order.pk).update(amount=0, tax_amount=0)

        data_to_update = {
            "cancelled_at": timezone.now(),
            "cancelled_by_id": user.id,
            "po_status": POStatus.PO_CANCELLED,
            "po_cancelled_at": timezone.now(),
            "outgoing_status": VendorOrder.OutgoingStatus.CANCELLED,
            "incoming_status": VendorOrder.IncomingStatus.CANCELLED,
        }
        vendor_order, _, _ = model_update(
            instance=vendor_order,
            updated_by_id=user.id,
            fields=[
                "cancelled_at",
                "cancelled_by_id",
                "po_status",
                "po_cancelled_at",
                "outgoing_status",
                "incoming_status",
            ],
            data=data_to_update,
            clean=False,
        )
        if (
            vendor_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value
            and vendor_order.last_approved_snapshot
        ):
            update_boq_status(
                boq_id=vendor_order.project_id, user_id=user.id, organization_id=vendor_order.actual_vendor_id
            )
        po_list = (
            VendorPurchaseOrder.objects.filter(vendor_order_id=vendor_order.id)
            .exclude(status=POStatus.PO_CANCELLED)
            .values_list("id", flat=True)
        )
        if po_list:
            vendor_po_and_invoice_cancel_many(
                po_list=po_list,
                user_id=user.id,
                vendor_order=vendor_order,
                cancel_order_invoice_flag=True,
            )
            purchase_orders = VendorPurchaseOrder.objects.filter(id__in=list(po_list)).all()
            logger.info(f"Sending email for cancelled POs: {po_list} {purchase_orders}")
            PoCancelEmail.send_multiple_emails(
                purchase_orders=purchase_orders, vendor_order=vendor_order, user=user, event=Events.PO_CANCELLED_EMAIL
            )

        on_commit(
            partial(
                ProjectDataCache.delete,
                project_details=ProjectOrganizationEntity(
                    project_id=vendor_order.project_id, organization_id=vendor_order.org_to_id
                ),
            )
        )
        on_commit(
            partial(
                WorkProgressChartCache.delete,
                project_details=ProjectOrganizationEntity(
                    project_id=vendor_order.project_id, organization_id=vendor_order.org_to_id
                ),
            )
        )
        if hasattr(vendor_order, "request_id") and vendor_order.request_id:
            """
            If order cancel is done and if there is in request pending state
            then cancel the request and if not then do nothing
            """
            request_service = OrderRequestServiceFactory.get_service(
                order_repo=order_repo,
                mapping_repo=mapping_repo,
                boq_sync_repo=boq_sync_repo,
                order_sync_repo=order_sync_repo,
                order_snapshot_repo=order_snapshot_repo,
            )
            request_service.cancel_request(
                data=OrderRequestCancelData(
                    order_id=vendor_order_id,
                    request_id=vendor_order.request_id,
                    order_type=vendor_order.order_type,
                    user_id=user.pk,
                    org_id=vendor_order.origin_org_id,
                )
            )
        on_commit(partial(order_cancel_trigger, vendor_order=vendor_order))
        send_email_for_cancel_order(user=user, vendor_order=vendor_order)
        process_order_permissions(
            project_id=vendor_order.project_id,
            org_from_id=vendor_order.org_from_id,
            org_to_id=vendor_order.org_to_id,
        )
        OrderBaseService.create_status_history(
            order_id=vendor_order.id,
            incoming_status=VendorOrder.IncomingStatus.CANCELLED,
            outgoing_status=VendorOrder.OutgoingStatus.CANCELLED,
            user_id=user.pk,
        )
    elif vendor_order.outgoing_status == VendorOrder.OutgoingStatus.NOT_SENT:
        vendor_order.soft_delete(user.id)
        VendorPurchaseOrder.objects.filter(vendor_order_id=vendor_order_id).update(
            deleted_at=timezone.now(), deleted_by_id=user.id
        )
        CommentHelperService.archive(
            context=MicroContextChoices.ORDER.value,
            context_id=vendor_order_id,
            user_id=user.pk,
        )

        order_elements = VendorOrderElement.objects.filter(vendor_order_id=vendor_order_id)

        CommentHelperService.archive_many(
            data={MicroContextChoices.ORDER_ITEM.value: order_elements.values_list("id", flat=True)}, user_id=user.pk
        )
        order_elements.update(deleted_at=timezone.now(), deleted_by_id=user.id)
    else:
        logger.error("Unknown case for order cancel/delete", vendor_order_id=vendor_order_id)
        raise ValidationError("Order cancellation failed.")


def vendor_purchase_order_cancel_delete_many(*, vendor_order_id: int, po_list: list, user: User):
    vendor_order: VendorOrder = (
        VendorOrder.objects.filter(id=vendor_order_id)
        .select_related("org_from")
        .prefetch_related(
            Prefetch(
                "purchase_orders",
                VendorPurchaseOrder.objects.exclude(status=POStatus.PO_CANCELLED),
            )
        )
        .first()
    )
    old_po_list = vendor_order.purchase_orders.all().values_list("id", flat=True)
    po_exists = set(old_po_list) - set(po_list)

    if not vendor_order:
        raise ValidationError({"vendor_order_id": "Invalid Vendor Order id"})

    if vendor_order.status in [OrderStatus.SENT, OrderStatus.COMPLETED] or vendor_order.outgoing_status in [
        VendorOrder.OutgoingStatus.COMPLETED,
        VendorOrder.OutgoingStatus.SENT,
        OrderStatusEnum.APPROVED.value,
    ]:
        po_status = POStatus.PO_CANCELLED
        po_attached_at = vendor_order.po_attached_at
        vendor_po_and_invoice_cancel_many(po_list=po_list, user_id=user.pk, vendor_order=vendor_order)
        purchase_order = VendorPurchaseOrder.objects.filter(id=po_list[-1]).first()
        PoCancelEmail.send_email(
            purchase_order=purchase_order, vendor_order=vendor_order, user=user, event=Events.PO_CANCELLED_EMAIL
        )
    else:
        po_status = POStatus.PO_PENDING
        po_attached_at = None
        VendorPurchaseOrder.objects.filter(id__in=po_list).soft_delete(user_id=user.pk)

    if not po_exists:
        data_to_update = {"po_status": po_status, "po_cancelled_at": timezone.now(), "po_attached_at": po_attached_at}
        vendor_order, _, _ = model_update(
            instance=vendor_order,
            fields=["po_status", "po_cancelled_at", "po_attached_at"],
            data=data_to_update,
            updated_by_id=user.pk,
        )
    else:
        order_update_after_operation(user_id=user.pk, vendor_order_id=vendor_order_id)


def get_order_detail_custom_message():
    return "Purchase Intent Details are updated, "


def get_cancelled_elements_custom_message(cancelled_elements_count: int, custom_message: str):
    cancelled_elements_custom_message = "of this Purchase Intent have been cancelled,"
    if custom_message:
        and_text = "and"
    else:
        and_text = ""
    custom_message = (
        custom_message
        + f"{and_text} {cancelled_elements_count} {'item' if cancelled_elements_count == 1 else 'items'} "
        + cancelled_elements_custom_message
    )
    return custom_message


def get_to_map_elements_custom_message(to_map_elements_count: int, custom_message: str):
    added_elements_custom_message = "of this Purchase Intent have been added, "
    if custom_message:
        and_text = "and"
    else:
        and_text = ""
    custom_message = (
        custom_message
        + f"{and_text} {to_map_elements_count} {'item' if to_map_elements_count == 1 else 'items'} "
        + added_elements_custom_message
    )
    return custom_message


def get_updated_elements_custom_message(updated_elements_count: int, custom_message: str):
    updated_elements_custom_message = "of this Purchase Intent have been modified, "
    if custom_message:
        and_text = "and"
    else:
        and_text = ""
    custom_message = (
        custom_message
        + f"{and_text} {updated_elements_count} {'item' if updated_elements_count == 1 else 'items'} "
        + updated_elements_custom_message
    )
    return custom_message


def prepare_custom_message(
    is_updated: bool, cancelled_elements_count: int, to_map_elements_count: int, updated_elements_count: int
) -> str:
    final_message = "Please find the updated details and scope of this Purchase Intent."
    custom_message = ""
    if is_updated:
        custom_message = get_order_detail_custom_message()
    if cancelled_elements_count:
        custom_message = get_cancelled_elements_custom_message(cancelled_elements_count, custom_message)
    if to_map_elements_count:
        custom_message = get_to_map_elements_custom_message(to_map_elements_count, custom_message)
    if updated_elements_count:
        custom_message = get_updated_elements_custom_message(updated_elements_count, custom_message)
    custom_message = custom_message + final_message
    return custom_message


# TODO: Not Used Anymore
# def prepare_and_send_modified_order_email(
#     vendor_order: VendorOrder,
#     user: User,
#     for_create: List,
#     for_update: List,
#     for_delete: List,
#     old_order_value: decimal.Decimal,
# ):
#     from .email import bcc_recipient_emails_fetch, prepare_email_data

#     email_data: Dict = prepare_email_data(
#         vendor_order_id=vendor_order.id,
#         project_id=vendor_order.project_id,
#         with_attachments=False,
#         user_email=user.email,
#         org_id=user.token_data.org_id if user.token_data else None,
#         is_order_updated=True,
#     )

#     if not email_data["recipient_emails"]:
#         raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Please set email for the selected vendor"})

#     org_incoming_emails = bcc_recipient_emails_fetch(org_id=vendor_order.org_to.id)
#     # TODO : patch need to be removed once, org config order receiver data is corrected in org
#     configured_bcc_email = org_config_bcc_emails_get(org_id=vendor_order.org_to_id)
#     email_data["bcc_emails"] = email_data["bcc_emails"] + org_incoming_emails + configured_bcc_email
#     on_commit(
#         partial(
#             order_modify_email_trigger,
#             order=vendor_order,
#             element_entities=for_create + for_update + for_delete,
#             email_data=email_data,
#             user_id=user.id,
#             old_order_value=old_order_value,
#         )
#     )


def prepare_and_cancel_order_email(vendor_order: VendorOrder, user: User):
    from .email import prepare_email_data
    from .trigger import order_cancel_email_trigger

    email_data: Dict = prepare_email_data(
        vendor_order_id=vendor_order.id,
        project_id=vendor_order.project_id,
        with_attachments=False,
        user_email=user.email,
        org_id=user.token_data.org_id if user.token_data else None,
        is_order_updated=True,
    )
    if email_data["recipient_emails"]:
        on_commit(
            partial(
                order_cancel_email_trigger,
                order=vendor_order,
                email_data=email_data,
                user_id=user.id,
            )
        )


def vendor_order_element_excel_template_generate(excel_columns: Dict, organization_id: int, project_id: int):
    tax_type = ProjectCountryConfigCache.get(project_id).tax_type
    uom_choices = OrganizationUOMCache.get(instance_id=organization_id)
    uom_title_list = list(uom_choices.values())
    uom_title_list.sort()
    category_code_name_list = (
        element_category_list(organization_id=organization_id).order_by("name", "code").values_list("name", "code")
    )
    item_type_list = ElementItemType.objects.order_by("name").values_list("name", flat=True)
    gst_percentage_list = list(get_tax_slab_list(tax_id=tax_type.id))
    category_code_list = [f"{name} ({code})" for name, code in category_code_name_list]
    workbook = Workbook()
    dropdowns = workbook.create_sheet("Dropdown Options")
    for index, uom_title in enumerate(uom_title_list, start=1):
        dropdowns[f"A{index}"] = uom_title
    for index, category_code in enumerate(category_code_list, start=1):
        dropdowns[f"B{index}"] = category_code
    for index, item_type in enumerate(item_type_list, start=1):
        dropdowns[f"C{index}"] = item_type

    for index, gst_percentage in enumerate(gst_percentage_list, start=1):
        dropdowns[f"D{index}"] = gst_percentage

    worksheet = workbook.active
    worksheet.title = "Element Upload Sheet"
    worksheet["A1"] = "Mandatory Fields"
    worksheet["E1"] = "Optional Fields"
    worksheet["A1"].alignment = Alignment(horizontal="center")
    worksheet["E1"].alignment = Alignment(horizontal="center")
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=4)
    worksheet.merge_cells(start_row=1, start_column=5, end_row=1, end_column=11)
    worksheet["A1"].fill = PatternFill(start_color="99ccff", end_color="99ccff", fill_type="solid")
    worksheet["E1"].fill = PatternFill(start_color="ffcc99", end_color="ffcc99", fill_type="solid")
    for key, value in excel_columns.items():
        worksheet[key] = value

    uom_validation = get_excel_data_validation(
        formula=f"{quote_sheetname('Dropdown Options')}!$A$1:$A${len(uom_title_list)}",
        error_title="Invalid UOM",
        error_message="Please enter a valid UOM.",
        prompt_title="UOM List",
        prompt_message=PROMPT_MESSAGE,
    )
    uom_validation.add("D3:D1048576")

    category_validation = get_excel_data_validation(
        formula=f"{quote_sheetname('Dropdown Options')}!$B$1:$B${len(category_code_list)}",
        error_title="Invalid Category",
        error_message="Please enter a valid element category.",
        prompt_title="Category List",
        prompt_message="Please select from the list",
    )
    category_validation.add("C3:C1048576")

    itemtype_validation = get_excel_data_validation(
        formula=f"{quote_sheetname('Dropdown Options')}!$C$1:$C${len(item_type_list)}",
        error_title="Invalid ItemType",
        error_message="Please enter a valid ItemType.",
        prompt_title="ItemType List",
        prompt_message="Please select from the list",
    )
    itemtype_validation.add("F3:F1048576")

    tax_percent_validation = get_excel_data_validation(
        formula=f"{quote_sheetname('Dropdown Options')}!$D$1:$D${len(gst_percentage_list)}",
        error_title="Invalid Tax %",
        error_message="Please enter a valid Tax%.",
        prompt_title="Tax% List",
        prompt_message="Please select from the list",
    )
    tax_percent_validation.add("K3:K1048576")

    worksheet.add_data_validation(uom_validation)
    worksheet.add_data_validation(category_validation)
    worksheet.add_data_validation(itemtype_validation)
    worksheet.add_data_validation(tax_percent_validation)
    workbook.properties.creator = "rdash_order_v1"
    workbook.properties.title = "order"
    workbook.properties.version = "v1"
    return save_virtual_workbook(workbook)


def vendor_order_element_excel_file(
    data: dict,
    project_id: int = None,
    vendor_order_id: int = None,
    create: bool = True,
    with_client_rate: bool = True,
    is_client_rate_visible: bool = True,
    is_order_rate_visible: bool = True,
    show_qunatity_dimensions: bool = False,
):
    excel_data = []
    filename = f"{data.get('order_number')}_element"

    if project_id and vendor_order_id:
        order_elements = order_element_list_using_vendor_order(vendor_order_id=vendor_order_id, project_id=project_id)
    else:
        order_elements = (
            order_element_list(elements=data.get("order_elements")).select_related("item_type").order_by("created_at")
        )

    if not vendor_order_id and order_elements:
        vendor_order_id = order_elements[0].vendor_order_id

    excel_field_list = [
        "item_name",
        "description",
        "brand_make",
        "item_code",
        "item_type",
        "Quantity",
        "UOM",
        "client_rate",
        "order_rate",
        "HSN",
        "tax_percent",
        "final_amount",
        "public_url",
    ]

    if order_elements_qty_dimensions_exists(vendor_order_id=vendor_order_id) or show_qunatity_dimensions:
        excel_field_list.extend(["length", "length_uom", "breadth", "breadth_uom"])

    if data.get("is_service_charge_col_visible", False):
        excel_field_list.append("base_amount")
        excel_field_list.append("service_charge_percent")

    if data.get("is_discount_col_visible", False):
        excel_field_list.append("gross_amount")
        excel_field_list.append("discounted_value")
        excel_field_list.append("discount_percent")

    if not with_client_rate:
        excel_field_list.remove("client_rate")
    if not is_order_rate_visible:
        excel_field_list.remove("order_rate")
        if "Amount" in excel_field_list:
            excel_field_list.remove("Amount")

    if with_client_rate and (not data.get("client_rates")):
        excel_field_list.remove("client_rate")

    order = get_order_from_id(vendor_order_id=vendor_order_id, project_id=project_id)
    if order and data.get("org_id") == order.org_to_id:
        is_client_rate_visible = False

    if order_elements:
        excel_data.append(
            serializer_element_data(
                fields=excel_field_list, order_elements=order_elements, is_client_rate_visible=is_client_rate_visible
            )
        )
        deduction_amount = Deduction.objects.filter(order_id=order_elements.first().vendor_order_id).aggregate(
            amount_sum=Coalesce(Sum("amount", output_field=IntegerField()), Value(0))
        )["amount_sum"]
        # summary sheet data preparation
        excel_data.append(
            prepare_excel_summary_data_v2(
                grouped_order_elements=(
                    get_grouped_elements(vendor_order_id=vendor_order_id)
                    if vendor_order_id
                    else get_grouped_elements_v2(order_elements=order_elements)
                ),
                deduction_amount=deduction_amount,
            )
        )
        # excel data for payment terms
        if vendor_order_id:
            excel_data.append(prepare_excel_payment_term_and_conditions_data(vendor_order_id=vendor_order_id))

    if data.get("guidelines"):
        guidelines = VendorOrderElementGuideline.objects.filter(element_id__in=data.get("order_elements")).all()
        excel_data.append(
            {
                "sheet_title": "Element Guidelines",
                "sheet_data": (
                    VendorOrderElementGuidelineExportSerializer(guidelines, many=True).data
                    if guidelines
                    else excel_data_file_with_no_record(sheet_name="Element_Guidelines")
                ),
                "with_serial_number": bool(guidelines),
            }
        )
    if not create:
        return excel_data, filename

    if order_elements:
        return create_excel_file(data=excel_data), filename
    raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Vendor Order has no elements/guidelines"})


def get_vendor_order_last_six_months_count(vendor_id: int):
    vendor_order_last_six_months_count = (
        get_orders_from_vendor_id(vendor_id=vendor_id)
        .filter(created_at__gte=(date.today() + relativedelta(months=-ORDER_COUNT_OF_MONTHS)))
        .count()
    )
    return vendor_order_last_six_months_count


def get_vendor_order_total_count(vendor_id: int):
    vendor_order_total_count = get_orders_from_vendor_id(vendor_id=vendor_id).count()
    return vendor_order_total_count


def get_vendor_id_and_code(order: VendorOrder):
    vendor_code = None
    vendor_id = None
    if hasattr(order, "org_to") and hasattr(order.org_to, "vendor"):
        vendor_code = order.org_to.vendor.code
    else:
        raise ValidationError("Vendor Code should not be None")
    vendor_id = order.org_to.pk
    return vendor_id, vendor_code


def get_order_detailed_object_using_id(vendor_order_id: int):
    return VendorOrder.objects.filter(pk=vendor_order_id).select_related("org_to__vendor").first()


def get_vendor_rdash_turnover(vendor_id: int):
    rdash_turnover = (
        get_orders_from_vendor_id(vendor_id=vendor_id)
        .prefetch_related("order_elements")
        .annotate(order_value_sum=Sum(F("order_elements__quantity") * F("order_elements__vendor_rate")))
        .aggregate(vendor_order_value_sum=Sum(F("order_value_sum")))["vendor_order_value_sum"]
    )
    if rdash_turnover is None:
        rdash_turnover = 0
    else:
        rdash_turnover = float(rdash_turnover)
    return rdash_turnover


def send_aggregated_vendor_order_data_to_vms(vendor_order_id: int):
    request_session = RequestService(VMS_HOST, retry_option=RetryOption)
    order = get_order_detailed_object_using_id(vendor_order_id=vendor_order_id)
    vendor_id, vendor_code = get_vendor_id_and_code(order=order)
    vendor_order_total_count = get_vendor_order_total_count(vendor_id=vendor_id)
    vendor_order_last_six_months_count = get_vendor_order_last_six_months_count(vendor_id=vendor_id)
    vendor_rdash_turnover = get_vendor_rdash_turnover(vendor_id=vendor_id)
    url = f"{VMS_HOST}/api/v1/vendor/{vendor_code}/update-vendor-orders-data"
    payload = {
        "vendor_order_total_count": vendor_order_total_count,
        "vendor_order_last_six_months_count": vendor_order_last_six_months_count,
        "rdash_turnover": vendor_rdash_turnover,
    }
    headers = {"Authorization": f"Bearer {VMS_TOKEN}", "Content-Type": "application/json"}
    try:
        request_session.put(url, headers=headers, data=json.dumps(payload))
    except Exception as e:
        # TODO: Handle the case when vendor doesn't exist in VMS
        # TODO: Once handled, remove exception catching
        # TODO: Make it async
        logger.error(
            "Exception while sending vendor order data ratings to VMS",
            vendor_code=vendor_code,
            ratings=payload,
            error=e,
        )


def mark_order_complete(order_complete_data: MarkOrderCompleteData):
    incoming_status = VendorOrder.IncomingStatus.COMPLETED
    outgoing_status = VendorOrder.OutgoingStatus.COMPLETED
    order = VendorOrder.objects.filter(id=order_complete_data.vendor_order_id).first()
    is_initial_history = True if order.completed_at is None else False
    model_update(
        instance=order,
        data={
            "outgoing_status": outgoing_status,
            "incoming_status": incoming_status,
            "started_at": order_complete_data.started_at,
            "due_at": order_complete_data.due_at,
            "completed_at": order_complete_data.completed_at,
            "completed_by_id": order_complete_data.user_id,
        },
        updated_by_id=order_complete_data.user_id,
        fields=["outgoing_status", "incoming_status", "started_at", "due_at", "completed_at", "completed_by_id"],
    )
    OrderBaseService.create_status_history(
        order_id=order_complete_data.vendor_order_id,
        user_id=order_complete_data.user_id,
        incoming_status=incoming_status,
        outgoing_status=outgoing_status,
    )
    create_vendor_order_fields_history(
        order=order,
        fields=["started_at", "due_at", "completed_at", "completed_by"],
        created_by_id=order_complete_data.user_id,
        is_initial=is_initial_history,
    )
    on_commit(
        partial(
            order_complete_trigger,
            vendor_order_id=order_complete_data.vendor_order_id,
            project_id=order_complete_data.project_id,
        )
    )


def mark_order_incomplete(*, vendor_order_id: int, user_id: int):
    last_status: OrderStatusHistory = (
        OrderStatusHistory.objects.filter(order_id=vendor_order_id)
        .exclude(
            outgoing_status__in=[
                VendorOrder.OutgoingStatus.COMPLETED,
                OrderStatusEnum.CLOSED.value,
                VendorOrder.OutgoingStatus.NOT_SENT,
            ]
        )
        .order_by("-created_at")
        .first()
    )
    if not last_status:
        incoming_status = VendorOrder.IncomingStatus.PENDING
        outgoing_status = VendorOrder.OutgoingStatus.SENT
    else:
        incoming_status = last_status.incoming_status
        outgoing_status = last_status.outgoing_status
    order: VendorOrder = VendorOrder.objects.filter(id=vendor_order_id).first()
    if order.outgoing_status == VendorOrder.OutgoingStatus.CLOSED:
        raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Order is closed, cannot be marked incomplete"})
    is_initial_history = True if order.incompleted_at is None else False
    model_update(
        instance=order,
        data={
            "incoming_status": incoming_status,
            "outgoing_status": outgoing_status,
            "incompleted_at": timezone.now(),
            "incompleted_by_id": user_id,
        },
        updated_by_id=user_id,
        fields=["incoming_status", "outgoing_status", "incompleted_at", "incompleted_by_id"],
    )
    OrderBaseService.create_status_history(
        order_id=vendor_order_id, user_id=user_id, incoming_status=incoming_status, outgoing_status=outgoing_status
    )
    create_vendor_order_fields_history(
        order=order, fields=["incompleted_at", "incompleted_by"], created_by_id=user_id, is_initial=is_initial_history
    )


def mark_and_unmark_order_closed(*, order_id: int, user: User, org_id: int, is_closed: bool):
    if is_closed:
        incoming_status = VendorOrder.IncomingStatus.CLOSED
        outgoing_status = VendorOrder.OutgoingStatus.CLOSED
        close_prompt = "mark-close"
    else:
        last_status: OrderStatusHistory = (
            OrderStatusHistory.objects.filter(order_id=order_id)
            .exclude(outgoing_status__in=[VendorOrder.OutgoingStatus.CLOSED, VendorOrder.OutgoingStatus.NOT_SENT])
            .order_by("-created_at")
            .first()
        )
        """
        This is Patch for Bug in Order Status History, where we were not logginghistory for on_request_not_required case
        """
        if not last_status:
            incoming_status = VendorOrder.IncomingStatus.PENDING
            outgoing_status = VendorOrder.OutgoingStatus.SENT
        else:
            incoming_status = last_status.incoming_status
            outgoing_status = last_status.outgoing_status
        close_prompt = "unmark-close"
    order: VendorOrder = VendorOrder.objects.filter(id=order_id).first()
    org_user_is_permitted = OrgPermissionHelper.has_permission(user=user, permission=Permissions.CAN_CLOSE_ORDER)
    project_user_is_permitted = ProjectPermissionHelper.has_permission(
        user=user, permission=Permissions.CAN_CLOSE_ORDER, project_id=order.project_id
    )
    if org_id == order.origin_org_id and org_user_is_permitted and project_user_is_permitted:
        model_update(
            instance=order,
            data={
                "incoming_status": incoming_status,
                "outgoing_status": outgoing_status,
            },
            updated_by_id=user.pk,
            fields=["outgoing_status", "incoming_status"],
        )
        if is_closed:
            InvoiceService().cancel_orders_pending_hold_invoices(order_id=order_id, user_id=user.pk)
        return OrderBaseService.create_status_history(
            order_id=order_id, user_id=user.pk, incoming_status=incoming_status, outgoing_status=outgoing_status
        )
    if is_closed:
        InvoiceService().cancel_orders_pending_hold_invoices(order_id=order_id, user_id=user.pk)

    raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: f"You are not authorized to {close_prompt} this order"})


def send_latest_order_ratings_to_vms(
    *,
    vendor_order_id: int,
    on_time_delivery: int,
    quality_of_work: int,
    cost_effectiveness: int,
):
    request_session = RequestService(VMS_HOST, retry_option=RetryOption)
    order = VendorOrder.objects.filter(pk=vendor_order_id).select_related("org_to__vendor").first()
    if hasattr(order, "org_to") and hasattr(order.org_to, "vendor"):
        vendor_code = order.org_to.vendor.code
    else:
        raise ValidationError("Vendor Code should not be None")
    url = f"{VMS_HOST}/api/v1/vendor/{vendor_code}/update-rating"
    payload = {
        "on_time_delivery": on_time_delivery,
        "quality_of_work": quality_of_work,
        "cost_effectiveness": cost_effectiveness,
    }

    headers = {"Authorization": f"Bearer {VMS_TOKEN}", "Content-Type": "application/json"}

    try:
        request_session.put(url, headers=headers, data=json.dumps(payload))
    except Exception as e:
        # TODO: Handle the case when vendor doesn't exist in VMS
        # TODO: Once handled, remove exception catching
        # TODO: Make it async
        logger.info(
            "Exception while sending latest order ratings to VMS", vendor_code=vendor_code, ratings=payload, error=e
        )


def submit_order_review(
    *,
    vendor_order_id: int,
    user_id: int,
    on_time_delivery: int,
    quality_of_work: int,
    cost_effectiveness: int,
    review: str,
    review_documents: List[OrderReviewDocument] = None,
):
    rating_and_review, _ = OrderReview.objects.update_or_create(
        order_review_id=vendor_order_id,
        defaults={
            "created_by_id": user_id,
            "on_time_delivery": on_time_delivery,
            "quality_of_work": quality_of_work,
            "cost_effectiveness": cost_effectiveness,
            "review": review,
        },
    )
    if review_documents:
        documents = []
        for document in review_documents:
            doc_obj = OrderReviewDocument()
            doc_obj.created_by_id = user_id
            doc_obj.order_review_id = rating_and_review.pk
            doc_obj.url = document["url"]
            doc_obj.type = document["type"]
            documents.append(doc_obj)
        OrderReviewDocument.objects.bulk_create(documents)


def update_payment_requests_latched_to_po(old_purchase_order_id: int, new_purchase_order_id: int):
    PaymentRequest.objects.filter(
        purchase_order_id=old_purchase_order_id,
    ).exclude(status__in=[PaymentRequestStatus.REJECTED, PaymentRequestStatus.CANCELLED]).update(
        purchase_order_id=new_purchase_order_id
    )


def update_payment_requests_latched_to_invoices(latched_invoice_ids: list, new_purchase_order_id: int):
    PaymentRequest.objects.filter(invoice_id__in=latched_invoice_ids).exclude(
        status__in=[PaymentRequestStatus.REJECTED, PaymentRequestStatus.CANCELLED]
    ).update(purchase_order_id=new_purchase_order_id)


def update_latched_invoices_and_payment_requests(
    *,
    old_purchase_order_id: int,
    new_purchase_order_id: int,
):
    latched_invoice_ids = Invoice.objects.filter(vendor_po_id=old_purchase_order_id).values_list("id", flat=True)
    update_payment_requests_latched_to_po(
        old_purchase_order_id=old_purchase_order_id, new_purchase_order_id=new_purchase_order_id
    )
    update_payment_requests_latched_to_invoices(
        latched_invoice_ids=latched_invoice_ids, new_purchase_order_id=new_purchase_order_id
    )
    Invoice.objects.filter(vendor_po_id=old_purchase_order_id).update(vendor_po_id=new_purchase_order_id)


def vendor_purchase_order_version_create(
    *,
    purchase_order_id: int,
    vendor_order_id: int,
    po_file: InMemoryUploadedFile,
    name: str,
    amount: float,
    po_date: datetime,
    type: str,
    user: User,
    tax_amount: float = 0,
):
    old_purchase_order: VendorPurchaseOrder = (
        VendorPurchaseOrder.objects.filter(id=purchase_order_id, vendor_order_id=vendor_order_id)
        .annotate_total_payment_request_amount()
        .annotate_total_payment_gst_amount()
        .order_by("-version")
        .first()
    )
    if old_purchase_order.status == POStatus.PO_CANCELLED:
        raise ValidationError(
            {api_settings.NON_FIELD_ERRORS_KEY: "Cannot create a new version of a cancelled purchase order"}
        )

    payment_request_amount = old_purchase_order.payment_request_amount
    payment_gst_amount = old_purchase_order.payment_gst_amount
    if (amount + tax_amount) < payment_request_amount:
        raise ValidationError(
            {
                api_settings.NON_FIELD_ERRORS_KEY: "Revised amount cannot be less than the sum "
                "of payment requests already raised."
            }
        )
    elif tax_amount < payment_gst_amount:
        raise ValidationError(
            {
                api_settings.NON_FIELD_ERRORS_KEY: "Revised tax amount cannot be less than the sum "
                "of taxes of all payment requests already raised."
            }
        )

    # update status of old purchase to cancelled , so thats its amount won't reflect on Invoice po amount
    model_update(
        instance=old_purchase_order, fields=["status"], data={"status": POStatus.PO_CANCELLED}, updated_by_id=user.pk
    )

    if not po_file:
        # If file is not been to post to the server,
        # we fetch it from the database(that means user didn't change the file)
        po_file = old_purchase_order.file
    purchase_order = VendorPurchaseOrder()
    purchase_order.file = po_file
    purchase_order.name = name
    purchase_order.amount = amount
    purchase_order.tax_amount = tax_amount
    purchase_order.status = POStatus.PO_ACTIVE
    purchase_order.po_number = old_purchase_order.po_number
    purchase_order.po_date = po_date
    purchase_order.type = type
    purchase_order.uploaded_by_id = user.pk
    purchase_order.vendor_order_id = vendor_order_id
    purchase_order.version = old_purchase_order.version + 1
    purchase_order.org_id = old_purchase_order.org_id
    purchase_order.full_clean()
    purchase_order.save()

    update_latched_invoices_and_payment_requests(
        old_purchase_order_id=old_purchase_order.id, new_purchase_order_id=purchase_order.id
    )

    vendor_order = VendorOrder.objects.select_related("org_from", "project").filter(id=vendor_order_id).first()
    PoRevisedEmail.send_email(
        purchase_order=purchase_order, vendor_order=vendor_order, user=user, event=Events.PO_REVISED_EMAIL
    )
    po_trigger_data = PurchaseOrderTriggerData(
        order_id=vendor_order_id,
        project_id=vendor_order.project_id,
        order_number=f"{vendor_order.project.job_id}/{vendor_order.order_number}",
        order_org_from_id=vendor_order.org_from_id,
        order_org_to_id=vendor_order.org_to_id,
        uploaded_by_id=user.pk,
        project_job_id=vendor_order.project.job_id,
        po_id=purchase_order.id,
        po_number=purchase_order.po_number,
    )
    on_commit(
        partial(
            po_revised_trigger,
            po_trigger_data=po_trigger_data,
        )
    )
    return purchase_order


def vendor_order_sync_update(*, data: dict[str, Any], instance: VendorOrder, updated_by_id: int):
    fields = ["excel_sheet", "excel_meta"]
    instance, _, _ = model_update(instance=instance, fields=fields, data=data, updated_by_id=updated_by_id)
    return instance


def order_filtering(
    *, orders: List[VendorOrder], status: str, order_type: str, self_organization_id: int
) -> List[VendorOrder]:
    if order_type == OrderType.INCOMING:
        if status:
            orders = orders.filter(Q(incoming_status=status) | Q(status=status)).all()
        return (
            orders.filter(org_to_id=self_organization_id)
            .filter(Q(last_approved_snapshot__isnull=False) | Q(outgoing_status=OrderStatusEnum.SENT))
            .exclude(outgoing_status=VendorOrder.OutgoingStatus.NOT_SENT)
            .exclude(
                org_from__config__order_flow=OrganizationConfig.OrderFlowChoices.PO_FLOW,
                po_status=POStatus.PO_PENDING,
                version=4,
            )
            .all()
        )
    if status:
        orders = orders.filter(Q(outgoing_status=status) | Q(status=status)).all()
    return orders.filter(~Q(org_to_id=self_organization_id), origin_org_id=self_organization_id).all()


def annotate_order_progress_percentage(*, orders: List[VendorOrder]) -> None:
    for order in orders:
        element_data: list[SectionTotalProgressElementEntity] = []
        for element in order.order_elements.all():
            element: VendorOrderElement

            if (
                getattr(element, "linked_element") is not None
                and element.linked_element.deleted_at is None
                and element.linked_element.element_status in (BoqElementStatus.DRAFT, BoqElementStatus.APPROVED)
            ):
                element_data.append(
                    SectionTotalProgressElementEntity(
                        id=element.pk,
                        quantity=element.quantity,
                        progress_percentage=element.linked_element.work_progress_element.progress_percentage,
                        amount=getattr(element, "total_amount", 0),
                    )
                )
        total_progress = WorkProgressScopeDataHelper.get_total_progress(elements=element_data)

        setattr(order, "progress_percentage", total_progress.progress_percentage)


def annotate_amount_and_element_count(
    *, vendors: list[Vendor], project_id: int, vendor_id_list: list[int], org_id: int
) -> None:
    order_element_details_group_by_org = (
        boq_elements_fetch_for_vendor_wise_scope(project_id=project_id, vendor_id_list=vendor_id_list, org_id=org_id)
        .annotate_final_amount()
        .values("organization_id")
        .annotate(
            order_element_count=Count("id"),
            order_amount=Sum(F("final_element_amount")),
        )
    )
    order_element_details_group_by_org = dict(
        (single_detail.get("organization_id"), single_detail) for single_detail in order_element_details_group_by_org
    )

    for vendor in vendors:
        if vendor.id in order_element_details_group_by_org:
            setattr(vendor, "order_element_count", order_element_details_group_by_org[vendor.id]["order_element_count"])
            setattr(vendor, "order_amount", order_element_details_group_by_org[vendor.id]["order_amount"])


def vendors_project_completion_date_get(*, organization_id: int, project_id: int) -> Optional[str]:
    current_project_status = (
        project_status_history_for_work_report_fetch(organization_id=organization_id, project_id=project_id)
        .values("status", "created_at")
        .order_by("-created_at")
        .first()
    )

    if current_project_status and current_project_status.get("status") == RDStatus.EXECUTION_COMPLETED:
        actual_completion_date = current_project_status.get("created_at")
    else:
        actual_completion_date = None
    return actual_completion_date


class PoEmailService:
    @classmethod
    @abc.abstractmethod
    def get_template_name(cls) -> str: ...

    @classmethod
    def get_email_data(cls, vendor_order_id: int) -> Optional[VendorOrderEmail]:
        return VendorOrderEmail.objects.filter(vendor_order_id=vendor_order_id).first()

    @classmethod
    def prepare_data(cls, vendor_order: VendorOrder):
        email_data: Optional[VendorOrderEmail] = cls.get_email_data(vendor_order_id=vendor_order.id)
        if not email_data:
            if vendor_order.email_data:
                email_data = VendorOrderEmail(
                    to_receiver=vendor_order.email_data.get("to_receiver"),
                    cc_receiver=vendor_order.email_data.get("cc_receiver"),
                    bcc_receiver=vendor_order.email_data.get("bcc_receiver"),
                    subject=vendor_order.email_data.get("subject"),
                )
        return email_data

    @classmethod
    def get_to_receiver_data(
        cls,
        vendor_order: VendorOrder,
    ):
        if vendor_order.org_to_id:
            vendor_id = vendor_order.org_to_id
        else:
            vendor_id = vendor_order.vendor_id
        org_emails: Dict = (
            OrganizationConfig.objects.filter(organization_id=vendor_id)
            .values("order_receiver_emails", "order_cc_emails")
            .first()
        )
        if not org_emails or not org_emails.get("order_receiver_emails"):
            user_emails: OrganizationUser = get_organizations_users_emails(organization_id=vendor_id).filter(
                is_admin=True
            )
        else:
            user_emails = org_emails["order_receiver_emails"]
        email_data = cls.prepare_data(vendor_order=vendor_order)
        if not email_data:
            if not user_emails:
                raise ValidationError({"vendor_order_id": "No email found for this vendor"})
            to_receiver = user_emails
        else:
            to_receiver = email_data.to_receiver
        return to_receiver

    @classmethod
    def send_multiple_emails(
        cls,
        purchase_orders: QuerySet,
        vendor_order: VendorOrder,
        user: User,
        event: str,
    ):
        to_receiver = cls.get_to_receiver_data(vendor_order=vendor_order)
        for purchase_order in purchase_orders:
            po_email_data = POEmailData(
                purchase_order_id=purchase_order.pk,
                to=[receiver.strip() for receiver in to_receiver],
                created_by_id=user.pk,
            )
            on_commit(partial(po_email_trigger, po_email_data, event))

    @classmethod
    def send_email(
        cls,
        purchase_order: VendorPurchaseOrder,
        vendor_order: VendorOrder,
        user: User,
        event: str,
    ):
        to_receiver = cls.get_to_receiver_data(vendor_order=vendor_order)

        po_email_data = POEmailData(
            purchase_order_id=purchase_order.pk,
            to=[receiver.strip() for receiver in to_receiver],
            created_by_id=user.pk,
        )

        on_commit(partial(po_email_trigger, po_email_data, event))


class PoUploadEmail(PoEmailService):
    @classmethod
    def get_template_name(cls):
        return "order/po_received.html"


class PoCancelEmail(PoEmailService):
    @classmethod
    def get_template_name(cls):
        return "order/po_cancelled.html"

    @classmethod
    def get_email_context(
        cls,
        order_number: str,
        vendor_order: VendorOrder,
        vendor: Vendor,
        purchase_order: VendorPurchaseOrder,
        user: User,
    ) -> Dict:
        context = super().get_email_context(order_number, vendor_order, vendor, purchase_order, user)
        del context["sent_by"]
        return context


class PoRevisedEmail(PoEmailService):
    @classmethod
    def get_template_name(cls):
        return "order/po_revised.html"


class OrderAttachmentPersistanceService:
    @classmethod
    def create_bulk(
        cls, attachment_entities: List[TermsAndConditionsAttachmentsData], uploaded_by_id: int, order_id: int
    ):
        attachment_objs = []
        for attachment in attachment_entities:
            attachment_obj = TermsAndConditionAttachment()
            attachment_obj.file = attachment.file
            attachment_obj.type = attachment.type
            attachment_obj.name = attachment.name
            attachment_obj.vendor_order_id = order_id
            attachment_obj.uploaded_by_id = uploaded_by_id
            attachment_objs.append(attachment_obj)
        return TermsAndConditionAttachment.objects.bulk_create(objs=attachment_objs)

    @classmethod
    def delete_bulk(cls, attachment_entities: List[TermsAndConditionsAttachmentsUpdateData], deleted_by_id: int):
        TermsAndConditionAttachment.objects.filter(id__in=[attachment.id for attachment in attachment_entities]).update(
            deleted_by_id=deleted_by_id, deleted_at=timezone.now()
        )


class OrderAttachmentService:
    @classmethod
    def process_create(cls, attachments: List[TermsAndConditionsAttachmentsData], user_id: int, order_id: int):
        OrderAttachmentPersistanceService.create_bulk(
            attachment_entities=attachments,
            uploaded_by_id=user_id,
            order_id=order_id,
        )

    @classmethod
    def process_delete(cls, attachments: List[TermsAndConditionsAttachmentsUpdateData], user_id: int):
        OrderAttachmentPersistanceService.delete_bulk(attachment_entities=attachments, deleted_by_id=user_id)


class OrderBaseService:
    @classmethod
    def get_final_order_data(cls, order_data: OrderData, order_snapshot_data: OrderSnapshotData, user_id: int):
        if not order_snapshot_data:
            return order_data
        order_element_code_to_obj_mapping = {}
        order_elements = []
        for snapshot_element in order_snapshot_data.elements:
            code = ElementCodeService.get_code(
                serial_number=snapshot_element.serial_number,
                custom_type=snapshot_element.custom_type,
                code=snapshot_element.code,
                version=snapshot_element.boq_element_version,
            )
            order_element_code_to_obj_mapping[code] = snapshot_element

        for order_element in order_data.order_elements:
            code = ElementCodeService.get_code(
                serial_number=order_element.serial_number,
                custom_type=order_element.custom_type,
                code=order_element.code,
                version=order_element.boq_element_version,
            )
            snapshot_element = order_element_code_to_obj_mapping.get(code, None)
            if not snapshot_element:
                vendor_order_element = VendorOrderElementData(
                    id=order_element.id,
                    vendor_rate=order_element.vendor_rate,
                    budget_rate=order_element.budget_rate,
                    item_type_id=order_element.item_type_id,
                    quantity=order_element.quantity,
                    quantity_dimensions=order_element.quantity_dimensions,
                    updated_at=order_element.updated_at,
                    updated_by_id=order_element.updated_by_id,
                    status=order_element.status,
                    cancelled_by_id=order_element.cancelled_by_id,
                    cancelled_at=order_element.cancelled_at,
                    deleted_at=timezone.now(),
                    deleted_by_id=user_id,
                    tax_percent=order_element.tax_percent,
                )
            else:
                vendor_order_element = VendorOrderElementData(
                    id=order_element.id,
                    vendor_rate=snapshot_element.vendor_rate,
                    budget_rate=snapshot_element.budget_rate,
                    item_type_id=snapshot_element.item_type_id,
                    quantity=snapshot_element.quantity,
                    quantity_dimensions=snapshot_element.quantity_dimensions,
                    updated_at=snapshot_element.updated_at,
                    updated_by_id=snapshot_element.updated_by_id,
                    status=snapshot_element.status,
                    cancelled_by_id=snapshot_element.cancelled_by_id,
                    cancelled_at=snapshot_element.cancelled_at,
                    tax_percent=snapshot_element.tax_percent,
                )
            order_elements.append(vendor_order_element)

        order_deductions = []
        order_deduction_attachments = []
        order_deduction_code_to_obj_mapping = {}
        for deduction in order_snapshot_data.deductions:
            order_deduction_code_to_obj_mapping[deduction.code] = deduction

        for deduction in order_data.order_deductions:
            snapshot_deduction = order_deduction_code_to_obj_mapping.get(deduction.code, None)
            if not snapshot_deduction:
                order_deduction = DeductionData(
                    id=deduction.id,
                    amount=deduction.amount,
                    type=deduction.type,
                    remark=deduction.remark,
                    type_color_code=deduction.type_color_code,
                    item_reference=deduction.item_reference,
                    name=deduction.name,
                    object_status=ObjectStatus.DELETE,
                    deleted_at=timezone.now(),
                    deleted_by_id=user_id,
                    tax_amount=deduction.tax_amount,
                )
                for attachment in deduction.attachments:
                    deduction_attachment = DeductionAttachmentData(
                        id=None,
                        file=attachment.file,
                        name=attachment.name,
                        uploaded_by_id=attachment.uploaded_by_id,
                        uploaded_at=attachment.uploaded_at,
                        deduction_id=deduction.id,
                        object_status=ObjectStatus.UPDATE,
                    )
                    order_deduction_attachments.append(deduction_attachment)
            else:
                order_deduction = DeductionData(
                    id=deduction.id,
                    amount=snapshot_deduction.amount,
                    type=snapshot_deduction.type,
                    remark=snapshot_deduction.remark,
                    type_color_code=snapshot_deduction.type_color_code,
                    item_reference=snapshot_deduction.item_reference,
                    name=snapshot_deduction.name,
                    object_status=ObjectStatus.UPDATE,
                    tax_amount=snapshot_deduction.tax_amount,
                )
                for attachment in snapshot_deduction.attachments:
                    deduction_attachment = DeductionAttachmentData(
                        id=None,
                        file=attachment.file,
                        name=attachment.name,
                        uploaded_by_id=attachment.uploaded_by_id,
                        uploaded_at=attachment.uploaded_at,
                        deduction_id=deduction.id,
                        object_status=ObjectStatus.UPDATE,
                    )
                    order_deduction_attachments.append(deduction_attachment)
            order_deductions.append(order_deduction)

        order_data.order_elements = order_elements
        order_data.order_deductions = order_deductions
        order_data.started_at = order_snapshot_data.started_at
        order_data.due_at = order_snapshot_data.due_at
        order_data.shipping_address = order_snapshot_data.shipping_address
        order_data.payment_tnc = order_snapshot_data.payment_tnc
        order_data.other_tnc = order_snapshot_data.other_tnc
        # order_data.payment_term_text = order_snapshot_data.payment_term_text

        # order_data.terms_and_conditions = order_snapshot_data.terms_and_conditions
        order_data.is_discounted = order_snapshot_data.is_discounted
        order_data.is_service_charged = order_snapshot_data.is_service_charged
        order_data.is_taxed = order_snapshot_data.is_taxed
        return order_data

    @classmethod
    def fetch_order_number(cls, project_id: int, proposal_number: Optional[int] = None) -> Tuple[int, Project]:
        project = Project.objects.get(id=project_id)
        if project.version > 2:
            if proposal_number:
                return proposal_number, project
            else:
                return generate_proposal_and_order_number(project_id=project_id), project
        return generate_proposal_and_order_number(project_id=project_id, proposal_number=proposal_number), project

    @classmethod
    def create_status_history(
        cls,
        order_id: int,
        incoming_status: OrderStatusEnum,
        outgoing_status: OrderStatusEnum,
        user_id: int,
    ) -> OrderStatusHistory:
        status_history = OrderStatusHistory()
        status_history.order_id = order_id
        status_history.incoming_status = incoming_status
        status_history.outgoing_status = outgoing_status
        status_history.created_by_id = user_id
        status_history.full_clean()
        return status_history.save()

    @classmethod
    def process_attachments(
        cls, order: VendorOrder, attachments: List[TermsAndConditionsAttachmentsBaseData], user_id: int
    ):
        for_create, _, for_delete = nested_object_segregation(docs_list=attachments)
        if for_create:
            OrderAttachmentService.process_create(attachments=for_create, user_id=user_id, order_id=order.id)
        if for_delete:
            OrderAttachmentService.process_delete(attachments=for_delete, user_id=user_id)

    @classmethod
    def process_deductions(cls, order: VendorOrder, deductions: List[DeductionData], user_id: int):
        logger.info("process_deductions started", order_id=order.pk, user_id=user_id)
        for_create_deductions, for_update_deductions, for_delete_deductions = nested_object_segregation(
            docs_list=deductions
        )
        if for_create_deductions:
            DeductionAndAttachmentService.process_create(
                deduction_entities=for_create_deductions,
                order_id=order.pk,
                user_id=user_id,
                project_id=order.project_id,
            )
        if for_update_deductions:
            DeductionAndAttachmentService.process_update(
                deduction_entities=for_update_deductions,
                user_id=user_id,
            )
        if for_delete_deductions:
            DeductionAndAttachmentService.process_delete(deduction_entities=for_delete_deductions, user_id=user_id)

        logger.info("process_deductions finished", order_id=order.pk)

    @classmethod
    def cancel_old_order_elements(cls, element_entities: List[OrderElementBaseData], user: User, order_type: OrderType):
        logger.info("cancel_old_order_elements started")
        to_cancel_elements_of_old_orders = []
        for element in element_entities:
            if element.cancelled_element_id:
                to_cancel_elements_of_old_orders.append(element.cancelled_element_id)
        if to_cancel_elements_of_old_orders:
            to_move_order_elements: List[VendorOrderElement] = order_element_list(
                elements=to_cancel_elements_of_old_orders
            )
            if not to_move_order_elements:
                raise OrderCancelledItemException({"cancelled_element_id": "Incorrect Cancelled Element"})
            old_vendor_order: VendorOrder = vendor_order_fetch(
                vendor_order_id=to_move_order_elements[0].vendor_order_id
            ).first()
            vendor_element_delete_many(
                order_elements=to_move_order_elements, user=user, vendor_order=old_vendor_order, order_type=order_type
            )
            logger.info("cancel_old_order_elements deleted")
        logger.info("cancel_old_order_elements finished")

    @classmethod
    def get_incoming_status(cls):
        return VendorOrder.IncomingStatus.PENDING

    @classmethod
    def get_outgoing_status(cls, is_sent: bool, order_type: OrderType):
        if is_sent or order_type == OrderType.INCOMING:
            return VendorOrder.OutgoingStatus.SENT
        return VendorOrder.OutgoingStatus.NOT_SENT

    @classmethod
    def get_issued_at(cls, is_sent: bool):
        if is_sent:
            return timezone.now()
        return None

    @classmethod
    def get_issued_by_id(cls, is_sent: bool, user_id: int):
        if is_sent:
            return user_id
        return None

    @classmethod
    def get_po_status(cls, order_entity: OrderCreateData):
        if order_entity.purchase_orders:
            return POStatus.PO_ATTACHED
        return POStatus.PO_PENDING

    @classmethod
    def get_po_attached_at(cls, order_entity: OrderCreateData):
        if order_entity.purchase_orders:
            return timezone.now()
        return None

    @classmethod
    def _prepare_vendor_info_data(cls, config_data: list[OrganizationSectionData]) -> dict:
        logger.info("Preparing Vendor Info Data From Config Data", config_data=config_data)
        sections = defaultdict(dict)

        # Iterate over each section
        for section in config_data:
            sections[section.id] = {}

            # Iterate over documents in each section
            for document in section.documents:
                sections[section.id][document.id] = []

                # Iterate over fields data in each document
                for field_data in document.fields_data:
                    field_representation = {}
                    field_representation["id"] = document.id

                    # Iterate over each field in field_data and prepare nested structure
                    for field_id, value in field_data.items():
                        if field_id == "id":
                            continue
                        field_representation[field_id] = type(value).drf_serializer(value).data
                        if field_representation[field_id].get("id"):
                            field_representation[field_id]["id"] = HashIdConverter.decode(
                                field_representation[field_id]["id"]
                            )
                        if isinstance(field_representation[field_id]["data"], dict) and field_representation[field_id][
                            "data"
                        ].get("id"):
                            data_obj = field_representation[field_id]["data"]
                            field_representation[field_id]["data"]["id"] = HashIdConverter.decode(data_obj["id"])

                    sections[section.id][document.id].append(field_representation)
        logger.info("Vendor Info Data Prepared", sections=sections)
        return sections

    @classmethod
    def _create_vendor_info_data_v2(cls, org_to_id: int, org_from_id: int) -> dict:
        logger.info("Preparing Vendor Info Data", org_to_id=org_to_id, org_from_id=org_from_id)
        vendor = organization_get(org_id=org_to_id)
        vendor_config: VendorInfoOrganizationCountryConfigData = get_vendor_onboard_config(
            country_id=vendor.country_id, org_id=org_from_id
        )
        vendor_config_data = VendorInfoOrganizationCountryConfigData.drf_serializer(vendor_config).data
        logger.info("Vendor Config Data", vendor_config_data=vendor_config_data)

        vendor_service = VendorOrganizationService(vendor_id=org_to_id, org_id=org_from_id)
        config_with_fields_data: list[OrganizationSectionData] = []
        config_with_fields_data.append(vendor_service.get_kyc_details())
        config_with_fields_data.append(vendor_service.get_bank_details())
        prepared_data = cls._prepare_vendor_info_data(config_with_fields_data)

        vendor_info = {"data": prepared_data, "config": vendor_config_data}
        logger.info("Vendor Info Data Prepared", vendor_info=vendor_info)
        return vendor_info

    @classmethod
    def _validate_vendor_status(cls, org_from_id: int, org_to_id, type_of_order: OrderTypeEnum):
        mapping = ClientVendorMapping.objects.filter(org_from_id=org_from_id, org_to_id=org_to_id).available().first()
        if not mapping:
            logger.info("Client Vendor Mapping not found", org_from_id=org_from_id, org_to_id=org_to_id)
            raise VendorNotFoundException("Vendor not found")
        if type_of_order == OrderTypeEnum.REGULAR.value and mapping.vendor_status != VendorStatusChoices.ACTIVE.value:
            logger.info("Vendor is not active", org_from_id=org_from_id, org_to_id=org_to_id)
            raise VendorStatusNotActiveException({org_to_id: "Vendor is not active"})
        if type_of_order == OrderTypeEnum.INSTA_ORDER.value and mapping.vendor_status not in [
            VendorStatusChoices.ACTIVE.value,
            VendorStatusChoices.ONBOARDED.value,
        ]:
            logger.info("Vendor is not active or onboarded", org_from_id=org_from_id, org_to_id=org_to_id)
            raise VendorStatusNotActiveOrOnboardedException({org_to_id: "Vendor is not active or onboarded"})

    @classmethod
    def _save_order_document_config_data(cls, data: dict, order_id: int, vendor_country_id: int, created_by_id: int):
        logger.info("Order Document Config Data saving", order_id=order_id, data=data)
        order_context_config: OrganizationDocumentFieldContextConfig = get_context_config_by_country(
            country_id=vendor_country_id, context=MicroContextChoices.ORDER
        )
        if not order_context_config:
            return
        created_text_data_objects = []
        if data and data.get("sections"):
            for section_id, section_values in data.get("sections").items():
                for document_id, document_values in section_values.items():
                    for document in document_values:
                        for context_config_id, data_value in document.items():
                            if context_config_id == "id":  # Skip the document id
                                continue
                            created_text_data_objects.append(
                                OrderTextFieldData(
                                    order_id=order_id,
                                    data=data_value.get("data"),
                                    context_config_id=order_context_config.pk,
                                    created_by_id=created_by_id,
                                )
                            )
            OrderTextFieldData.objects.bulk_create(objs=created_text_data_objects)
        logger.info("Order Document Config Data saved", order_id=order_id)

    @classmethod
    def _update_order_document_config_data(cls, data: dict, order_id: int, updated_by_id: int):
        logger.info("Order Document Config Data saving", order_id=order_id, data=data)
        incoming_context_config_id_data_object_mapping = {}
        updated_text_data_objects = []
        if data and data.get("sections"):
            for section_id, section_values in data.get("sections").items():
                for document_id, document_values in section_values.items():
                    for document in document_values:
                        for context_config_id, data_value in document.items():
                            if context_config_id == "id":  # Skip the document id
                                continue
                            incoming_context_config_id_data_object_mapping[context_config_id] = data_value.get("data")

            order_text_data_objs = OrderTextFieldData.objects.available().filter(order_id=order_id)
            updated_object_config_ids = incoming_context_config_id_data_object_mapping.keys()
            deleted_object_ids = []
            logger.info(
                "Config object mapping prepared.",
                incoming_context_config_id_data_object_mapping=incoming_context_config_id_data_object_mapping,
            )
            for obj in order_text_data_objs:
                if obj.context_config_id in updated_object_config_ids:
                    obj.data = incoming_context_config_id_data_object_mapping[obj.context_config_id]
                    obj.updated_by_id = updated_by_id
                    updated_text_data_objects.append(obj)
                else:
                    deleted_object_ids.append(obj.id)

            if updated_text_data_objects:
                OrderTextFieldData.objects.bulk_update(objs=updated_text_data_objects, fields=["data"])
            if deleted_object_ids:
                OrderTextFieldData.objects.filter(id__in=deleted_object_ids, order_id=order_id).soft_delete(
                    user_id=updated_by_id
                )
        logger.info("Order Document Config Data Updated", order_id=order_id)

    @classmethod
    def order_create(
        cls,
        order_entity: OrderCreateData,
        created_by_id: int,
        project_id: int,
        po_attached_at: datetime,
        origin_org_id: int,
        incoming_status: VendorOrder.IncomingStatus,
        outgoing_status: OrderStatusEnum,
        issued_by_id: int,
        issued_at: datetime,
        po_status: POStatus,
        proposal_number: Optional[int] = None,
        is_discounted: bool = False,
        is_service_charged: bool = False,
    ):
        logger.info(
            "OrderBaseService order_create started",
            origin_org_id=origin_org_id,
            incoming_status=incoming_status,
            outgoing_status=outgoing_status,
            proposal_number=proposal_number,
        )
        if outgoing_status != OrderStatusEnum.DRAFT.value and order_entity.org_to_id is not None:
            cls._validate_vendor_status(
                org_from_id=order_entity.org_from_id,
                org_to_id=order_entity.org_to_id,
                type_of_order=order_entity.type_of_order,
            )
        vendor_info = None
        if order_entity.is_new_vendor:
            vendor_info = cls._create_vendor_info_data_v2(
                org_to_id=order_entity.org_to_id, org_from_id=order_entity.org_from_id
            )

        if order_entity.payment_tnc:
            payment_tnc_id = order_entity.payment_tnc.id
            payment_tnc_title = order_entity.payment_tnc.title
            payment_tnc_block = order_entity.payment_tnc.block
        else:
            payment_tnc_id = None
            payment_tnc_title = None
            payment_tnc_block = None

        if order_entity.other_tnc:
            other_tnc_id = order_entity.other_tnc.id
            other_tnc_title = order_entity.other_tnc.title
            other_tnc_block = order_entity.other_tnc.block
        else:
            other_tnc_id = None
            other_tnc_title = None
            other_tnc_block = None

        order = VendorOrder(
            payment_term_id=payment_tnc_id,
            payment_term_title=payment_tnc_title,
            payment_term_block=payment_tnc_block,
            other_term_id=other_tnc_id,
            other_term_title=other_tnc_title,
            other_term_block=other_tnc_block,
            created_by_id=created_by_id,
            started_at=order_entity.started_at,
            due_at=order_entity.due_at,
            project_id=project_id,
            po_status=po_status,
            po_attached_at=po_attached_at,
            shipping_address=order_entity.shipping_address,
            work_order_from=order_entity.work_order_from,
            shipping_address_header=order_entity.shipping_address_header,
            org_from_id=order_entity.org_from_id,
            org_to_id=order_entity.org_to_id,
            origin_org_id=origin_org_id,
            incoming_status=incoming_status,
            outgoing_status=outgoing_status,
            issued_by_id=issued_by_id,
            issued_at=issued_at,
            rate_contract_id=order_entity.rate_contract_id,
            version=get_order_version(org_from_id=order_entity.org_from_id, proposal_number=proposal_number),
            order_number=cls.fetch_order_number(project_id=project_id, proposal_number=proposal_number)[0],
            email_data=get_email_dict(order_entity=order_entity) if order_entity.to_send_email else None,
            poc_id=order_entity.poc_id,
            is_discounted=is_discounted,
            order_type=order_entity.type_of_order,
            is_service_charged=is_service_charged,
            vendor_info_v2=vendor_info,
            is_taxed=order_entity.is_taxed,
            to_send_email=order_entity.to_send_email,
        )
        order.full_clean()
        order.save()

        # save order config dynamic field data
        # if order is in draft state then we don't need to create document config data
        if order.org_to:
            cls._save_order_document_config_data(
                data=order_entity.document_config_data,
                order_id=order.pk,
                vendor_country_id=order.org_to.country_id,
                created_by_id=created_by_id,
            )

        logger.info("vendor order object created", order_id=order.pk)
        if order_entity.started_at is not None:
            create_vendor_order_field_history(
                order=order, field="started_at", created_by_id=created_by_id, is_initial=True
            )
            logger.info("vendor_order_field_history created, started_at is None")
        if order_entity.due_at is not None:
            create_vendor_order_field_history(order=order, field="due_at", created_by_id=created_by_id, is_initial=True)
            logger.info("vendor_order_field_history created, due_at is None")

        return order

    @classmethod
    def order_update(cls, order_entity: OrderUpdateData, user: User, is_sent: bool, save: bool = True):
        order = (
            fetch_vendor_order_with_linked_elements(order_id=order_entity.id)
            .annotate(
                proposal_id=Subquery(
                    Proposal.objects.filter(order_id=OuterRef("id"), status=ProposalStatus.APPROVED.value)
                    .order_by("-created_at")
                    .values("id")[:1]
                ),
                request_id=Subquery(
                    OrderRequestMapping.objects.filter(
                        resource_id=OuterRef("id"),
                        event__in=[
                            OrderEventEnum.SENT_FOR_APPROVAL.value,
                            OrderEventEnum.MODIFICATION_FOR_APPROVAL.value,
                        ],
                    )
                    .order_by("-created_at")
                    .values("request_id")[:1]
                ),
                current_event=Subquery(
                    OrderRequestMapping.objects.filter(
                        resource_id=OuterRef("id"),
                        event__in=[
                            OrderEventEnum.SENT_FOR_APPROVAL.value,
                            OrderEventEnum.MODIFICATION_FOR_APPROVAL.value,
                        ],
                    )
                    .order_by("-created_at")
                    .values("event")[:1]
                ),
                cancelled_elements_count=Count(
                    "order_elements",
                    filter=Q(order_elements__status=OrderStatus.CANCELLED, order_elements__deleted_at__isnull=True),
                ),
                not_cancelled_elements_count=Count(
                    "order_elements",
                    filter=Q(
                        order_elements__status__in=[
                            OrderStatus.PENDING,
                            OrderStatus.SENT,
                            OrderStatus.MODIFIED,
                            OrderStatus.COMPLETED,
                        ],
                        order_elements__deleted_at__isnull=True,
                    ),
                ),
                elements_amount_value=Coalesce(
                    Sum(
                        F("elements_final_amount"),
                        filter=Q(
                            order_elements__deleted_at__isnull=True,
                            order_elements__status__in=[
                                OrderStatus.PENDING,
                                OrderStatus.SENT,
                                OrderStatus.MODIFIED,
                                OrderStatus.COMPLETED,
                            ],
                        ),
                    ),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .first()
        )
        if order:
            deduction_amount_value = Deduction.objects.filter(order_id=order.id, deleted_at__isnull=True).aggregate(
                deduction_amount=Sum("amount")
            )["deduction_amount"]
            setattr(
                order,
                "old_order_value",
                float(order.elements_amount_value) - float(deduction_amount_value if deduction_amount_value else 0),
            )
        # order_snapshot_data = cls.create_order_snapshot_data(order_id=order.id)
        if order.outgoing_status == VendorOrder.OutgoingStatus.CLOSED:
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Order is closed, cannot be updated"})

        if not order.not_cancelled_elements_count + len(order_entity.elements):
            raise OrderWithoutElementException("At least one Order Elements need to added to perform update operation")

        fields = [
            "shipping_address",
            "started_at",
            "due_at",
            "po_status",
            "org_to_id",
            "work_order_from",
            "shipping_address_header",
            "rate_contract_id",
            "origin_org_id",
            "poc_id",
            "is_taxed",
        ]
        if hasattr(order_entity, "is_discounted") and order_entity.is_discounted is not None:
            fields.append("is_discounted")
        if hasattr(order_entity, "email_data") and order_entity.email_data is not None:
            fields.append("email_data")

        # We cannot change order's vendor once it leaves draft state
        if order.outgoing_status in [
            OrderStatusEnum.PENDING_APPROVAL.value,
            OrderStatusEnum.APPROVED.value,
            OrderStatusEnum.SENT.value,
        ]:
            fields.remove("org_to_id")

        old_due_at = order.due_at
        old_started_at = order.started_at
        if order_entity.origin_org_id is None:
            fields.remove("origin_org_id")

        updated_vendor_order, is_updated, updated_fields = model_update(
            instance=order, fields=fields, data=order_entity.__dict__, updated_by_id=user.id, save=False
        )
        updated_fields.extend(
            [
                "payment_term_id",
                "payment_term_title",
                "payment_term_block",
                "other_term_id",
                "other_term_title",
                "other_term_block",
            ]
        )
        if not order_entity.payment_tnc:
            payment_term_id = None
            payment_term_title = None
            payment_term_block = None
        else:
            payment_term_id = order_entity.payment_tnc.id
            payment_term_title = order_entity.payment_tnc.title
            payment_term_block = order_entity.payment_tnc.block

        if not order_entity.other_tnc:
            other_term_id = None
            other_term_title = None
            other_term_block = None
        else:
            other_term_id = order_entity.other_tnc.id
            other_term_title = order_entity.other_tnc.title
            other_term_block = order_entity.other_tnc.block

        updated_vendor_order.other_term_id = other_term_id
        updated_vendor_order.other_term_title = other_term_title
        updated_vendor_order.other_term_block = other_term_block
        updated_vendor_order.payment_term_id = payment_term_id
        updated_vendor_order.payment_term_title = payment_term_title
        updated_vendor_order.payment_term_block = payment_term_block

        if (
            hasattr(order_entity, "is_new_vendor")
            and order_entity.is_new_vendor
            and "org_to_id" in updated_fields
            and hasattr(updated_vendor_order, "org_from_id")
            and updated_vendor_order.org_from_id
        ):
            vendor_info = cls._create_vendor_info_data_v2(
                org_to_id=order_entity.org_to_id, org_from_id=updated_vendor_order.org_from_id
            )
            updated_vendor_order.vendor_info_v2 = vendor_info
        else:
            updated_vendor_order.vendor_info_v2 = None

        # update order config dynamic field data
        cls._update_order_document_config_data(
            data=order_entity.document_config_data, order_id=order.pk, updated_by_id=user.id
        )

        if (
            hasattr(order_entity, "type_of_order")
            and order_entity.type_of_order
            and updated_vendor_order.outgoing_status == OrderStatusEnum.DRAFT.value
        ):
            updated_vendor_order.order_type = order_entity.type_of_order
            updated_fields.append("order_type")

        if "started_at" in updated_fields:
            if old_started_at is None:
                create_vendor_order_field_history(
                    order=order, field="started_at", created_by_id=user.id, is_initial=True
                )
            elif order_entity.started_at is None:
                """
                Currently the user cannot remove started at after order is sent_for_approval/approved/sent
                so we ignore started at field if it is None
                """
                updated_fields.remove("started_at")
            else:
                if not VendorOrderFieldHistory.objects.filter(order=order, field="started_at").count():
                    VendorOrderFieldHistory.objects.create(
                        order=order,
                        field="started_at",
                        value=order.to_field_history_datetime(old_started_at),
                        is_initial=True,
                        created_by_id=1,  # admin user
                    )
                create_vendor_order_field_history(
                    order=order, field="started_at", created_by_id=user.id, is_initial=False
                )
        if "due_at" in updated_fields:
            if old_due_at is None:
                create_vendor_order_field_history(order=order, field="due_at", created_by_id=user.id, is_initial=True)
            elif order_entity.due_at is None:
                """
                Currently the user cannot remove due at after order is sent_for_approval/approved/sent
                so we ignore due at field if it is None
                """
                updated_fields.remove("due_at")
            else:
                if not VendorOrderFieldHistory.objects.filter(order=order, field="due_at").count():
                    VendorOrderFieldHistory.objects.create(
                        order=order,
                        field="due_at",
                        value=order.to_field_history_datetime(old_due_at),
                        is_initial=True,
                        created_by_id=1,  # admin user
                    )
                create_vendor_order_field_history(order=order, field="due_at", created_by_id=user.id, is_initial=False)
            on_commit(
                partial(
                    WorkProgressChartCache.delete,
                    project_details=ProjectOrganizationEntity(
                        project_id=order.project_id, organization_id=order.org_to_id
                    ),
                )
            )
        if "started_at" in updated_fields or "due_at" in updated_fields:
            on_commit(
                partial(
                    ProjectDataCache.delete,
                    project_details=ProjectOrganizationEntity(
                        project_id=order.project_id, organization_id=order.org_to_id
                    ),
                )
            )
        if hasattr(order_entity, "to") and order_entity.to is not None:
            updated_vendor_order.email_data = get_email_dict(order_entity=order_entity)
            updated_fields.extend("email_data")
        if hasattr(order_entity, "to_send_email") and order_entity.to_send_email is not None:
            updated_vendor_order.to_send_email = order_entity.to_send_email
            updated_fields.extend("to_send_email")
        if save:
            updated_vendor_order.save(update_fields=updated_fields)
        if hasattr(order, "request_id") and order.request_id:
            setattr(updated_vendor_order, "request_id", order.request_id)
        if hasattr(order, "current_event") and order.current_event:
            setattr(updated_vendor_order, "current_event", order.current_event)
        return updated_vendor_order, is_updated, updated_fields, order.old_order_value


class OrderCreateProcessService(OrderBaseService):
    @classmethod
    def get_context(cls, order_type: OrderTypeEnum):
        if order_type == OrderTypeEnum.INSTA_ORDER.value:
            return MicroContext.INSTA_ORDER.value
        return MicroContext.ORDER.value

    @classmethod
    def process_create(
        cls,
        order_entity: OrderCreateData,
        user: User,
        project_id: int,
        origin_org_id: int,
        order_type: OrderType,
        is_sent: bool,
        proposal_id: Union[int, None] = None,
        direct_order: bool = True,
        boq_sync_on: bool = True,
        proposal_number: Optional[int] = None,
    ) -> Tuple[VendorOrder, List[VendorOrderElement]]:
        from .element import OrderElementService, incoming_order_elements_bulk_create

        logger.info("OrderCreateProcessService process_create started")
        if not order_entity.elements:
            raise OrderWithoutElementException(
                {"elements": _("Atleast one element needs to be present while creating order")}
            )
        order: VendorOrder = cls.order_create(
            order_entity=order_entity,
            created_by_id=user.pk,
            project_id=project_id,
            po_attached_at=cls.get_po_attached_at(order_entity=order_entity),
            origin_org_id=order_entity.origin_org_id if order_entity.origin_org_id else origin_org_id,
            issued_by_id=cls.get_issued_by_id(user_id=user.pk, is_sent=is_sent),
            issued_at=cls.get_issued_at(is_sent=is_sent),
            incoming_status=cls.get_incoming_status(),
            outgoing_status=cls.get_outgoing_status(is_sent=is_sent, order_type=order_type),
            po_status=cls.get_po_status(order_entity=order_entity),
            proposal_number=proposal_number,
            is_discounted=order_entity.is_discounted,
            is_service_charged=order_entity.is_service_charged,
        )
        logger.info("order_created")
        logger.info("status history created")
        if order_entity.purchase_orders:
            map_purchase_order_with_vendor_order(purchase_orders=order_entity.purchase_orders, vendor_order_id=order.id)
        if order_entity.terms_and_conditions_attachments:
            OrderAttachmentPersistanceService.create_bulk(
                attachment_entities=order_entity.terms_and_conditions_attachments,
                uploaded_by_id=user.pk,
                order_id=order.id,
            )
            logger.info("terms_and_conditions_attachments created")
        order_amount = sum(
            calculate_element_final_amount(
                rate=element.vendor_rate,
                quantity=element.quantity,
                service_charge_percent=0,
                discount_percent=0,
                is_service_charge_with_base_amount=None,
            )
            for element in order_entity.elements
        )

        if proposal_id:
            elements = incoming_order_elements_bulk_create(
                element_entities=order_entity.elements, created_by_id=user.id, project_id=project_id, order_id=order.id
            )
            logger.info("incoming_order_elements_bulk created")
            setattr(order, "amount", order_amount)
            setattr(order, "elements_count", len(elements))
            logger.info("proposal incoming order", elements_count=len(elements), amount=order_amount)
        else:
            permissions = OrgPermissionHelper.get_permissions(user=user)
            logger.info("fetched permissions")
            elements: List[VendorOrderElement] = OrderElementService.process_create(
                element_entities=order_entity.elements,
                project_id=project_id,
                order=order,
                user_id=user.id,
                permissions=permissions,
                order_type=order_type,
                direct_order=direct_order,
                boq_sync_on=boq_sync_on,
            )
            cls.process_deductions(order=order, deductions=order_entity.deductions, user_id=user.pk)
            if is_sent:
                setattr(order, "amount", order_amount)
                setattr(order, "elements_count", len(elements))
            cls.cancel_old_order_elements(element_entities=order_entity.elements, user=user, order_type=order_type)
        setattr(order, "order_amount", order_amount)
        logger.info("OrderCreateProcessService process_create finished")
        order_updated_signal.send(sender=VendorOrder, order_id=order.id)
        return order, elements, boq_sync_on


class OrderSentProcessorService:
    @staticmethod
    def send_email(
        order: VendorOrder,
        order_create_sent_entity: OrderCreateAndSentData,
        user: User,
        order_elements: List[VendorOrderElement],
    ):
        from .email import order_send_email_v2

        order_send_email_v2(
            vendor_order=order,
            project_id=order.project_id,
            data=order_create_sent_entity,
            user=user,
            org_id=order.origin_org_id,
            order_elements=order_elements,
        )

    @staticmethod
    def prepare_email_data(
        sent_initializer_data: OrderSentInitializeData, user_email: str, org_id: int, project_id: int
    ):
        from .email import cc_recipient_emails_fetch, decide_recipients_email_users_while_creation

        cc_emails = [user_email] if user_email else []
        bcc_emails = []
        final_user_emails = []

        subject = (
            f"Purchase Intent from {sent_initializer_data.work_order_from} | "
            f"Purchase Intent No. : {sent_initializer_data.order_number}"
        )

        cc_role_ids = OrganizationConfigRole.objects.filter(
            organization_config_id=org_id, is_order_cc_role=True
        ).values_list("role_id", flat=True)

        cc_role_user_emails = set(
            ProjectUser.objects.filter(project_id=project_id, role_id__in=cc_role_ids)
            .select_related("user")
            .values_list("user__email", flat=True)
        )
        cc_emails = set(cc_emails).union(cc_role_user_emails)
        cc_emails = list(cc_emails.union(set(cc_recipient_emails_fetch(org_id=org_id))))

        final_user_emails, cc_emails, bcc_emails = decide_recipients_email_users_while_creation(
            cc_emails=cc_emails, bcc_emails=bcc_emails
        )
        return {
            "recipient_emails": list(set(final_user_emails)),
            "cc_emails": list(set(cc_emails)),
            "bcc_emails": bcc_emails,
            "subject": subject,
            "po_flow": check_if_organization_po_flow_enabled(organization_id=org_id),
        }


class OrderCreateAndSentProcessService:
    @staticmethod
    def create_and_send(
        order_entity: OrderCreateAndSentData,
        user: User,
        project_id: int,
        order_type: OrderType,
        is_sent: bool,
        origin_org_id: int,
        direct_order: bool = True,
    ):
        order, elements, boq_sync = OrderCreateProcessService.process_create(
            order_entity=order_entity,
            user=user,
            project_id=project_id,
            order_type=order_type,
            is_sent=is_sent,
            origin_org_id=origin_org_id,
            proposal_id=order_entity.proposal_id,
            direct_order=direct_order,
        )
        on_commit(
            partial(
                ProjectDataCache.delete,
                project_details=ProjectOrganizationEntity(project_id=project_id, organization_id=order.org_to_id),
            )
        )
        # if order and boq_sync_on:
        #     order.amount = order_entity.final_amount

        #     po_list: List[VendorPurchaseOrder] = VendorPurchaseOrder.objects.filter(
        #         id__in=order_entity.purchase_orders
        #     ).all()

        #     attachments = []
        #     if po_list:
        #         for po in po_list:
        #             attachments.append(
        #                 ExcelAttachmentData(
        #                     name=po.name + get_file_extension(filepath=po.file.url),
        #                     url=po.file.url,
        #                 )
        #             )
        #     if attachments:
        #         order_entity.attachments.extend(attachments)
        # OrderSentProcessorService.send_email(
        #     order=order, order_create_sent_entity=order_entity, user=user, order_elements=elements
        # )
        # else:
        #     # TODO: Temprary trigger for orders which are sent but not visible to vendor
        #     on_commit(
        #         partial(
        #             order_sent_without_notification_trigger,
        #             vendor_order_id=order.pk,
        #             org_to_id=order.org_to_id,
        #             org_from_id=order.org_from_id,
        #             order_number=order.order_number,
        #             project_id=order.project_id,
        #         )
        #     )
        return order


class OrderUpdateAndSentProcessService:
    @classmethod
    def create_order_elements_entities(cls, order_elements: List[VendorOrderElement]) -> List[OrderElementCreateData]:
        element_objs = []
        for element in order_elements:
            production_drawing_entities = []
            guideline_entities = []
            for production_drawing in element.production_drawings.all():
                production_drawing_entities.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        name=production_drawing.name,
                        file=production_drawing.file,
                        tags=[tag.id for tag in production_drawing.tags.all()],
                    )
                )
            for guidelines in element.guidelines.all():
                guideline_entities.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        name=guidelines.name,
                        description=guidelines.description,
                        attachments=[
                            GuidelineAttachmentData(
                                object_status=ObjectStatus.ADD,
                                file=attachment.file,
                                name=attachment.name,
                                type=attachment.type,
                            )
                            for attachment in guidelines.attachments.all()
                        ],
                    )
                )
            element_objs.append(
                OrderElementUpdateData(
                    object_status=ObjectStatus.ADD,
                    name=element.name,
                    description=element.description,
                    uom=element.uom,
                    client_id=element.client_id,
                    serial_number=element.serial_number,
                    boq_element_id=element.boq_element_id,
                    el_element_id=element.el_element_id,
                    custom_type=element.custom_type,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    code=element.code,
                    preview_files=element.preview_files.all(),
                    guidelines=guideline_entities,
                    production_drawings=production_drawing_entities,
                    cancelled_element_id=None,
                    linked_element_id=None,
                    quantity=element.quantity,
                    vendor_rate=element.client_rate,
                    client_rate=0,
                    budget_rate=element.budget_rate,
                    id=element.pk,
                    brand_name=element.brand_name,
                    hsn_code=element.hsn_code,
                )
            )
        return element_objs

    @classmethod
    def update_and_send(
        cls,
        order_entity: OrderCreateAndSentData,
        user: User,
        project_id: int,
        order_type: OrderType,
        is_sent: bool,
        order_repo: OrderAbstractRepo,
        mapping_repo: OrderRequestMappingAbstractRepo,
        boq_sync_repo: BoqElementSyncAbstractRepo,
        order_sync_repo: OrderElementSyncAbstractRepo,
        order_snapshot_repo: OrderSnapshotAbstractRepo,
    ):
        order, _, _ = OrderUpdateService.process_update(
            order_entity=order_entity, user=user, project_id=project_id, order_type=order_type, is_sent=is_sent
        )
        on_commit(
            partial(
                ProjectDataCache.delete,
                project_details=ProjectOrganizationEntity(project_id=project_id, organization_id=order.org_to_id),
            )
        )
        request_service = OrderRequestServiceFactory.get_service(
            order_repo=order_repo,
            mapping_repo=mapping_repo,
            boq_sync_repo=boq_sync_repo,
            order_sync_repo=order_sync_repo,
            order_snapshot_repo=order_snapshot_repo,
        )
        request_service.create_request(
            data=OrderRequestCreateData(
                event=OrderEventEnum.SENT_FOR_APPROVAL.value,
                order_id=order.pk,
                user_id=user.pk,
                context=order.context,
                project_id=order.project_id,
                is_draft=False,
                org_id=order.origin_org_id,
                order_amount=order.amount,
                request_id=order.request_id,
                hierarchy_context=MicroContext.ORDER.value,
            )
        )
        order_updated_signal.send(sender=VendorOrder, order_id=order.id)
        return order, False


class OrderUpdateService(OrderBaseService):
    @classmethod
    def get_amount_and_element_count(
        cls,
        updated_elements: List[VendorOrderElement],
        for_delete: List[OrderElementUpdateData],
        created_elements: List[VendorOrderElement],
        db_elements: List[VendorOrderElement],
        for_create: List[OrderElementCreateData],
    ):
        all_changed_elements = []
        updated_elements_mappings = {element.id: element for element in updated_elements}
        deleted_elements_mappings = {element.id: element for element in for_delete}
        if updated_elements_mappings:
            for db_element in db_elements:
                if db_element.id in updated_elements_mappings:
                    all_changed_elements.append(updated_elements_mappings[db_element.id])
                elif db_element.id in deleted_elements_mappings:
                    all_changed_elements.append(deleted_elements_mappings[db_element.id])

        if created_elements:
            all_changed_elements.extend(created_elements)
        amount = sum(
            calculate_element_final_amount(
                rate=element.vendor_rate,
                quantity=element.quantity,
                discount_percent=element.discount_percent,
                service_charge_percent=element.service_charge_percent,
                is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
            )
            for element in all_changed_elements
        )
        return amount, all_changed_elements, len(db_elements) + len(for_create)

    @classmethod
    def process_update(
        cls,
        order_entity: OrderUpdateData,
        user: User,
        order_type: OrderType,
        project_id: int,
        is_sent: bool,
        direct_order: bool = True,
        boq_sync_on: bool = True,
    ) -> Tuple[VendorOrder, List, List[VendorOrderElement]]:
        from .element import OrderElementService

        order, _, updated_fields, old_order_value = cls.order_update(
            order_entity=order_entity, user=user, is_sent=is_sent, save=False
        )
        for_create, for_updates, for_delete = nested_object_segregation(docs_list=order_entity.elements)
        created_elements: List[VendorOrderElement] = []
        updated_elements: List[VendorOrderElement] = []
        permissions = OrgPermissionHelper.get_permissions(user=user)
        db_elements: List[VendorOrderElement] = list(
            VendorOrderElement.objects.filter(vendor_order_id=order.id, cancelled_by_id__isnull=True).all()
        )
        if order_entity.terms_and_conditions_attachments:
            cls.process_attachments(
                order=order, attachments=order_entity.terms_and_conditions_attachments, user_id=user.pk
            )
        if order_entity.deductions:
            cls.process_deductions(order=order, deductions=order_entity.deductions, user_id=user.pk)
        # if order.version < 5:
        #     if boq_sync_on and not can_send_email(order=order):
        #         boq_sync_on = False
        if for_create:
            created_elements.extend(
                OrderElementService.process_create(
                    element_entities=for_create,
                    order=order,
                    project_id=project_id,
                    user_id=user.id,
                    permissions=permissions,
                    order_type=order_type,
                    direct_order=direct_order,
                    boq_sync_on=boq_sync_on,
                )
            )
        if for_updates:
            updated_elements.extend(
                OrderElementService.process_update(
                    order=order,
                    element_entities=for_updates,
                    project_id=project_id,
                    user=user,
                    order_type=order_type,
                    permissions=permissions,
                    direct_order=direct_order,
                    boq_sync_on=boq_sync_on,
                )
            )
        _, all_changed_elements, elements_count = cls.get_amount_and_element_count(
            updated_elements=updated_elements,
            for_delete=for_delete,
            created_elements=created_elements,
            db_elements=db_elements,
            for_create=for_create,
        )
        setattr(order, "amount", order_entity.final_amount)
        setattr(order, "order_amount", order_entity.final_amount)
        setattr(order, "elements_count", elements_count)
        setattr(
            order,
            "gst_number",
            order.gst_details.gst_number if hasattr(order, "gst_details") else None,
        )
        setattr(
            order,
            "gst_state",
            order.gst_details.gst_state if hasattr(order, "gst_details") else None,
        )
        # TODO: Temporary Patch for Android/IOS App
        from_to_org_mapping = (
            FromToOrgMapping.objects.filter(org_to_id=order.org_to_id, org_from_id=order.org_from_id)
            .filter(deleted_at__isnull=True)
            .first()
        )
        if from_to_org_mapping:
            vendor_status = True if from_to_org_mapping.vendor_status == VendorStatusChoices.ACTIVE.value else False
        else:
            vendor_status = False
        setattr(order, "is_vendor_active", vendor_status)
        ##

        if for_delete:
            OrderElementService.process_delete(
                order=order,
                order_type=order_type,
                element_entities=for_delete,
                user=user,
                to_create_elements_count=len(for_create),
                permissions=permissions,
                direct_order=direct_order,
                boq_sync_on=boq_sync_on,
            )

        cls.cancel_old_order_elements(element_entities=order_entity.elements, user=user, order_type=order_type)
        if is_sent:
            updated_fields.extend(["issued_by_id", "issued_at"])
            order.issued_at = timezone.now()
            order.issued_by_id = user.id
        if "updated_at" not in updated_fields:
            updated_fields.append("updated_at")
            updated_fields.append("updated_by_id")
            order.updated_by_id = user.pk
            order.updated_at = timezone.now()
        order.save(update_fields=updated_fields)
        order_updated_signal.send(sender=VendorOrder, order_id=order.id)
        return order, all_changed_elements, created_elements


def send_order_and_vms_data(
    *,
    project_id: int,
    vendor_order_id: int,
    data: dict,
    user: User,
    org_id: int,
    order_repo: OrderAbstractRepo,
    mapping_repo: OrderRequestMappingAbstractRepo,
    boq_sync_repo: BoqElementSyncAbstractRepo,
    order_sync_repo: OrderElementSyncAbstractRepo,
    order_snapshot_repo: OrderSnapshotAbstractRepo,
):
    order: VendorOrder = order_details_for_email(vendor_order_id=vendor_order_id)
    email_data = {
        "to_receiver": data["to"],
        "cc_receiver": data["cc"],
        "bcc_receiver": data["bcc"],
        "subject": data["subject"],
        "body": data["body"],
        "attachments": [{"name": attachment.name, "url": attachment.url} for attachment in data["attachments"]],
    }
    model_update(
        instance=order,
        fields=["email_data", "issued_at", "issued_by_id"],
        data={"email_data": email_data, "issued_at": timezone.now(), "issued_by_id": user.pk},
        updated_by_id=user.id,
        save=True,
    )
    request_data = OrderRequestCreateData(
        order_id=order.pk,
        user_id=user.pk,
        context=order.context,
        project_id=order.project_id,
        org_id=order.origin_org_id,
        is_draft=False,
        order_amount=order.amount,
        event=OrderEventEnum.SENT_FOR_APPROVAL.value,
        request_id=order.request_id,
        hierarchy_context=MicroContext.ORDER.value,
    )
    request_service = OrderRequestServiceFactory.get_service(
        order_repo=order_repo,
        mapping_repo=mapping_repo,
        boq_sync_repo=boq_sync_repo,
        order_sync_repo=order_sync_repo,
        order_snapshot_repo=order_snapshot_repo,
    )
    request_service.create_request(data=request_data)


def create_vendor_order_field_history(order: VendorOrder, field: str, is_initial: bool, created_by_id: int):
    VendorOrderFieldHistory.objects.create(
        order=order,
        value=order.to_field_history(field),
        field=field,
        is_initial=is_initial,
        created_by_id=created_by_id,
    )


def create_vendor_order_fields_history(order: VendorOrder, fields: List[str], is_initial: bool, created_by_id: int):
    for field in fields:
        if field == "started_at" or field == "due_at":
            create_vendor_order_field_history(order=order, field=field, created_by_id=created_by_id, is_initial=False)
        else:
            create_vendor_order_field_history(
                order=order, field=field, created_by_id=created_by_id, is_initial=is_initial
            )


def vendor_order_element_update(
    project_id: int,
    order_id: int,
    element_id: int,
    org_from_id: int,
    element_data: dict,
    user_id: int,
):
    order = VendorOrder.objects.filter(id=order_id, project_id=project_id, org_from_id=org_from_id).first()
    if order is None:
        raise OrderNotFoundException("Order not found.")
    if order.outgoing_status != VendorOrder.OutgoingStatus.NOT_SENT:
        raise OrderStatusException("Order is not draft.")

    element = VendorOrderElement.available_objects.filter(
        id=element_id,
        vendor_order_id=order_id,
        project_id=project_id,
    ).first()
    if element is None:
        raise OrderElementNotFoundException("Element not found.")

    allowed_fields = ["category_id", "item_type_id", "uom", "quantity", "vendor_rate"]
    fields_to_update = list(set(allowed_fields).intersection(set(element_data.keys())))

    instance, is_updated, _ = model_update(
        instance=element,
        data=element_data,
        fields=fields_to_update,
        updated_by_id=user_id,
        clean=False,
        save=False,
    )
    if is_updated:
        instance.save(update_fields=fields_to_update)
    return instance


def order_request_and_snapshot_create(
    updated_order: VendorOrder,
    order_snapshot_data: OrderSnapshotData,
    user_id: int,
    order_repo: OrderAbstractRepo,
    mapping_repo: OrderRequestMappingAbstractRepo,
    boq_sync_repo: BoqElementSyncAbstractRepo,
    order_sync_repo: OrderElementSyncAbstractRepo,
    order_snapshot_repo: OrderSnapshotAbstractRepo,
):
    if updated_order.outgoing_status == OrderStatusEnum.DRAFT.value:
        OrderTriggerService.draft_order_modified_trigger(
            data=OrderData(
                id=updated_order.id,
                email_data=updated_order.email_data,
                outgoing_status=updated_order.outgoing_status,
                vendor_id=updated_order.actual_vendor_id,
                origin_org_id=updated_order.origin_org_id,
                project_id=updated_order.project_id,
                request_id=updated_order.request_id,
                event=OrderEventEnum.DRAFT.value,
                org_from_id=updated_order.org_from_id,
                shipping_address=updated_order.shipping_address,
                issued_by_id=updated_order.issued_by_id,
                org_from_name=updated_order.org_from.name.title(),
                snapshot_id=None,
                order_number=f"{updated_order.project.job_id}/{updated_order.order_number}",
                job_id=updated_order.project.job_id,
                proposal_id=None,
                old_order_value=updated_order.old_order_value,
                vendor_name=updated_order.org_to.name.title() if updated_order.org_to else None,
                order_elements=[],
                order_deductions=[],
                started_at=updated_order.started_at,
                due_at=updated_order.due_at,
                # terms_and_conditions=updated_order.terms_and_conditions,
                payment_term_text=updated_order.payment_term_text,
            )
        )

    elif updated_order.outgoing_status in [
        OrderStatusEnum.SENT.value,
        OrderStatusEnum.APPROVED.value,
    ]:
        snapshot_id = order_snapshot_repo.create_order_snapshot(order_snapshot_data=order_snapshot_data)
        request_service = OrderRequestServiceFactory.get_service(
            order_repo=order_repo,
            mapping_repo=mapping_repo,
            boq_sync_repo=boq_sync_repo,
            order_sync_repo=order_sync_repo,
            order_snapshot_repo=order_snapshot_repo,
        )
        request_service.create_request(
            data=OrderRequestCreateData(
                event=OrderEventEnum.MODIFICATION_FOR_APPROVAL.value,
                order_id=updated_order.pk,
                user_id=user_id,
                context=updated_order.context,
                project_id=updated_order.project_id,
                org_id=updated_order.origin_org_id,
                is_draft=False,
                order_amount=updated_order.amount,
                request_id=updated_order.request_id,
                snapshot_id=snapshot_id,
                hierarchy_context=MicroContext.ORDER.value,
            )
        )
    elif updated_order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value:
        request_service = OrderRequestServiceFactory.get_service(
            order_repo=order_repo,
            mapping_repo=mapping_repo,
            boq_sync_repo=boq_sync_repo,
            order_sync_repo=order_sync_repo,
            order_snapshot_repo=order_snapshot_repo,
        )
        request_service.reset_request(
            data=OrderRequestCreateData(
                event=updated_order.current_event,
                order_id=updated_order.pk,
                user_id=user_id,
                context=updated_order.context,
                project_id=updated_order.project_id,
                org_id=updated_order.origin_org_id,
                is_draft=False,
                order_amount=updated_order.amount,
                request_id=updated_order.request_id,
                hierarchy_context=MicroContext.ORDER.value,
            )
        )


def get_email_data(order: VendorOrder):
    order_email = VendorOrderEmail.objects.filter(vendor_order_id=order.pk).order_by("-id").first()
    if order.email_data:
        return {
            "to_receiver": order.email_data.get("to_receiver"),
            "cc_receiver": order.email_data.get("cc_receiver"),
            "bcc_receiver": order.email_data.get("bcc_receiver"),
            "subject": order.email_data.get("subject"),
            "body": order.email_data.get("body"),
            "attachments": order.email_data.get("attachments"),
        }
    if order_email:
        return {
            "to_receiver": order_email.to_receiver,
            "cc_receiver": order_email.cc_receiver,
            "bcc_receiver": order_email.bcc_receiver,
            "subject": order_email.subject,
            "body": None,
            "attachments": [],
        }
    return {}


def get_vendor_order_field_histories(field: str, vendor_order_id: int, is_client: bool = False):
    vendor_order_field_histories = vendor_order_field_history_fetch_all(field=field, vendor_order_id=vendor_order_id)
    # using list here to support negative indexing in django
    if not is_client:
        # subquery = OrderSnapshot.objects.filter(
        #     order_id=OuterRef("order_id"), outgoing_status=OrderStatusEnum.APPROVED.value
        # ).order_by("-created_at")
        order_snapshot = (
            OrderSnapshot.objects.filter(order_id=vendor_order_id, outgoing_status=OrderStatusEnum.APPROVED.value)
            .select_related("order")
            .order_by("-created_at")
            .first()
        )
        if order_snapshot and order_snapshot.order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value:
            vendor_order_field_histories = vendor_order_field_histories.filter(
                created_at__lte=order_snapshot.created_at
            )

            return list(vendor_order_field_histories)[:-1]
        else:
            return list(vendor_order_field_histories)
    else:
        return list(vendor_order_field_histories)


def filter_purchase_orders_for_po_preview(order_data: VendorOrder):
    last_snapshot_created_at = get_last_snapshot_created_at(vendor_order_id=order_data.id)
    final_purchase_orders = []
    sorted_purchase_orders = sorted(order_data.sorted_purchase_orders, key=lambda x: x.id, reverse=True)
    if not last_snapshot_created_at:
        order_data.sorted_purchase_orders = sorted_purchase_orders
        return order_data

    for purchase_order in sorted_purchase_orders:
        if purchase_order.cancelled_at is None and purchase_order.uploaded_at >= last_snapshot_created_at:
            final_purchase_orders.append(purchase_order)
    order_data.sorted_purchase_orders = final_purchase_orders
    return order_data


def update_vendor_info_v2(order_id: int, vendor_info_data: dict, user_id: int, org_id: int):
    logger.info("Updating Vendor Info V2", order_id=order_id, vendor_info_data=vendor_info_data, user_id=user_id)
    updated_vendor_info_data = vendor_info_data.get("sections", {})
    vendor_order = VendorOrder.objects.select_related("org_to").filter(id=order_id).first()
    vendor_config: VendorInfoOrganizationCountryConfigData = get_vendor_onboard_config(
        country_id=vendor_order.org_to.country_id, org_id=org_id
    )
    vendor_config_data = VendorInfoOrganizationCountryConfigData.drf_serializer(vendor_config).data
    logger.info("Vendor Config Data", vendor_config_data=vendor_config_data)
    vendor_info_v2 = {"data": updated_vendor_info_data, "config": vendor_config_data}
    vendor_order.vendor_info_v2 = vendor_info_v2
    vendor_order.updated_by_id = user_id
    vendor_order.save()
    logger.info("Vendor Info V2 Updated", order_id=order_id, vendor_info_v2=vendor_info_v2)
