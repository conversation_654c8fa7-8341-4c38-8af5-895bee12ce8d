import structlog
from django.db import transaction
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.mixins import ListModelMixin
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_202_ACCEPTED,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
)

from approval_request.domain.constants import RequestActionEnum
from approval_request.domain.entities import (
    ResourceToRequestApproveServiceData,
    ResourceToRequestCancelServiceData,
    ResourceToRequestHoldServiceData,
    ResourceToRequestRejectServiceData,
)
from approval_request.domain.services.request import ResourceToRequestActionService, request_action_data_get
from approval_request.interface.apis import ResourceAction<PERSON>ase<PERSON><PERSON>
from approval_request.interface.serializers import RequestActionInputSerializer
from commentv2.data.selectors import get_comment_replies
from commentv2.domain.constants import CommentTypeEnum
from commentv2.domain.entities import CommentReplyData
from commentv2.domain.exceptions import CommentReplyException
from commentv2.domain.services.services import comment_reply_create
from commentv2.domain.services.task_services import create_invisible_comment
from commentv2.interface.serializers import CommentReplyDataSerializer
from common.choices import OrderType
from common.constants import USER_NOT_PERMITTED_ERROR
from common.serializers import (
    BaseSerializer,
    HashIdField,
    SearchTextFilterSerializer,
    SplitCharHashIdListField,
    SplitCharListField,
)
from core.apis import OrgBaseApi
from core.models import Organization, User
from core.serializers import OrganizationModelSerializer, OrganizationSerializer, UserModelSerializer
from microcontext.choices import MicroContextChoices
from microcontext.domain.constants import MicroContext
from order.data.models import VendorPurchaseOrder
from order.data.selectors.selector_v1 import invoice_filter_order_list, invoice_filter_po_list, invoice_order_list
from order.domain.status_choices import POStatus
from order.interface.vendor_serializers import VendorOrderModelSerializer, VendorPurchaseOrderModelSerializer
from order.invoice.data.choices import InvoiceCreditNoteStatus, InvoiceStatus
from order.invoice.data.models import InvoiceType, SupportingDocumentType
from order.invoice.data.selectors import (
    client_invoice_header_data_get,
    credit_note_fetch,
    credit_note_fetch_all,
    invoice_fetch_all,
    invoice_fetch_by_id,
    invoice_request_comment_id_get,
    organization_invoice_config_get,
    vendor_invoice_header_data_get,
)
from order.invoice.domain.entities import CreditNoteData, InvoiceCreateUpdateData
from order.invoice.domain.exceptions import InvoiceConfigException, InvoiceException
from order.invoice.domain.factories import InvoiceResourceActionServiceFactory
from order.invoice.domain.services import (
    CreditNoteService,
    InvoiceService,
    RequestToInvoiceCallback,
    invoice_config_order_mandatory_update,
    invoice_config_po_mandatory_update,
    invoice_config_tds_update,
    invoice_request_secondary_comment_update,
)
from order.invoice.interface.filters import InvoiceOrderListFilter
from order.invoice.interface.serializers import (
    CreditNoteDataSerializer,
    CreditNoteDetailsSerializer,
    CreditNoteModelSerializer,
    InvoiceCreateUpdateInputDataSerializer,
    InvoiceDetailsSerializer,
    InvoiceModelSerializer,
    InvoiceNumberSerializer,
    InvoiceTypeModelSerializer,
    OrganizationInvoiceConfigModelSerialzer,
    SupportingDocumentTypeModelSerializer,
)
from payment_request.domain.services import PaymentRequestService
from project.data.models import Project
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData
from project.serializers import ProjectModelSerializer
from task.domain.utils import mentioned_user_ids_get_by_description
from task.interfaces.serializers import (
    CommentReplyModelSerializer,  # TODO: need to move thin in comment V2 from task
    TaskReplyCreateInputSerializer,
)

logger = structlog.get_logger(__name__)


class InvoiceConfigApi(OrgBaseApi):
    class FilterSerializer(BaseSerializer):
        organization_id = HashIdField()

        class Meta:
            ref_name = "InvoiceConfigInput"

    class OutputSerializer(OrganizationInvoiceConfigModelSerialzer):
        class Meta(OrganizationInvoiceConfigModelSerialzer.Meta):
            fields = ("is_order_mandatory", "is_po_mandatory", "is_tds_enabled")
            ref_name = "InvoiceConfigApiOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="organization_invoice_config_fetch_api",
        operation_summary=" Organization Invoice Config Fetch Api",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        config = organization_invoice_config_get(data.get("organization_id"))
        return Response(self.OutputSerializer(config).data, status=HTTP_200_OK)


class InvoiceListBaseApi(OrgBaseApi, ListModelMixin):
    class FilterSerializer(SearchTextFilterSerializer):
        project_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        order_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        invoice_number = serializers.CharField(required=False, allow_null=True, default=None)
        type_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        po_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        invoice_dates = serializers.ListField(child=serializers.DateField(), allow_empty=True, default=list)
        uploader_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        status = SplitCharListField(
            child=serializers.ChoiceField(choices=InvoiceStatus.choices),
            allow_empty=True,
            default=list,
        )
        amount = serializers.DecimalField(max_digits=15, decimal_places=2, allow_null=True, default=None)
        credit_note_amount = serializers.DecimalField(max_digits=15, decimal_places=2, allow_null=True, default=None)
        payable_amount = serializers.DecimalField(max_digits=15, decimal_places=2, allow_null=True, default=None)
        currency_id = HashIdField(required=False, allow_null=True, default=None)
        order_amount = serializers.DecimalField(max_digits=15, decimal_places=2, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceListBaseApiFilter"

    class OutputSerializer(InvoiceModelSerializer):
        project_config = serializers.SerializerMethodField()
        order_amount = serializers.SerializerMethodField()

        def get_order_amount(self, obj):
            if obj.order:
                return serializers.DecimalField(max_digits=15, decimal_places=2).to_representation(obj.order_amount)

        @swagger_serializer_method(serializer_or_field=ProjectCountryConfigData.drf_serializer)
        def get_project_config(self, obj):
            # TODO: Check if cache is hit every-time on passing
            return self.get_local_serializer_cache(
                "project_config",
                obj.project_id,
                ProjectCountryConfigData.drf_serializer(ProjectCountryConfigCache.get(instance_id=obj.project_id)).data,
            )

        class Meta(InvoiceModelSerializer.Meta):
            ref_name = "InvoiceListBaseApiOutput"
            fields = (
                "id",
                "invoice_number",
                "po_number",
                "invoice_date",
                "uploaded_by",
                "uploaded_at",
                "invoice_amount",
                "invoice_gst_amount",
                "payable_amount",
                "credit_note_amount",
                "credit_note_gst_amount",
                "updated_by",
                "updated_at",
                "doc_count",
                "doc_types",
                "file",
                "status",
                "vendor",
                "actions",
                "project",
                "project_config",
                "order",
                "order_amount",
                "type",
                "request_id",
                "comment_count",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
            )

    serializer_class = OutputSerializer

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = (
            invoice_fetch_all().annotate_comment_count(org_id=self.get_organization_id()).select_related("vendor_po")
        )
        if data.get("project_ids"):
            queryset = queryset.filter(project_id__in=data.get("project_ids"))
        if data.get("order_ids"):
            queryset = queryset.filter(order_id__in=data.get("order_ids"))
        if data.get("invoice_number"):
            queryset = queryset.filter(invoice_number__icontains=data.get("invoice_number"))
        if data.get("type_ids"):
            queryset = queryset.filter(type_id__in=data.get("type_ids"))
        if data.get("po_ids"):
            queryset = queryset.filter(vendor_po_id__in=data.get("po_ids"))
        if data.get("invoice_dates"):
            queryset = queryset.filter(invoice_date__in=data.get("invoice_dates"))
        if data.get("uploader_ids"):
            queryset = queryset.filter(uploaded_by_id__in=data.get("uploader_ids"))
        if data.get("status"):
            queryset = queryset.filter(status__in=data.get("status"))
        if data.get("search_text"):
            queryset = queryset.filter(
                Q(invoice_number__icontains=data.get("search_text"))
                | Q(project__job_id__icontains=data.get("search_text"))
                | Q(type__name__icontains=data.get("search_text"))
                | Q(vendor__name__icontains=data.get("search_text"))
                | Q(uploaded_by__first_name__icontains=data.get("search_text"))
                | Q(uploaded_by__last_name__icontains=data.get("search_text"))
            )
        if data.get("amount") is not None:
            queryset = queryset.filter(amount=data.get("amount"))
        if data.get("credit_note_amount"):
            queryset = queryset.filter(credit_note_amount=data.get("credit_note_amount"))
        if data.get("payable_amount"):
            queryset = queryset.filter(payable_amount=data.get("payable_amount"))
        if data.get("currency_id"):
            queryset = queryset.filter(project__config__currency_id=data.get("currency_id"))
        if data.get("order_amount") is not None:
            queryset = queryset.filter(order_amount=data.get("order_amount"))

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["org_id"] = self.get_organization_id()
        context["user_id"] = self.get_user_id()
        context["is_admin"] = self.user_is_admin()
        return context


class ClientInvoiceListApi(InvoiceListBaseApi):
    class FilterSerializer(InvoiceListBaseApi.FilterSerializer):
        client_ids = SplitCharHashIdListField(required=False, default=None)

        def validate(self, attrs):
            if not attrs.get("client_ids") and not attrs.get("project_ids") and not attrs.get("order_ids"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

    REQUIRED_PERMISSIONS = []

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = super().get_queryset().filter(vendor_id=self.get_organization_id())
        if data.get("client_ids"):
            queryset = queryset.filter(client_id__in=self.validate_filter_data().get("client_ids"))
        return queryset

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        operation_id="client_invoice_list_api",
        operation_summary="Invoice list",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class VendorInvoiceListApi(InvoiceListBaseApi):
    class FilterSerializer(InvoiceListBaseApi.FilterSerializer):
        vendor_ids = SplitCharHashIdListField(required=False, default=list)

        def validate(self, attrs):
            if not attrs.get("vendor_ids") and not attrs.get("project_ids") and not attrs.get("order_ids"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

    REQUIRED_PERMISSIONS = []

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = super().get_queryset().filter(client_id=self.get_organization_id())
        if data.get("vendor_ids"):
            queryset = queryset.filter(vendor_id__in=self.validate_filter_data().get("vendor_ids"))
        return queryset

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        operation_id="vendor_invoice_list_api",
        operation_summary="Invoice list",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class SupportingDocTagList(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(SupportingDocumentTypeModelSerializer):
        class Meta(SupportingDocumentTypeModelSerializer.Meta):
            ref_name = "SupportingDocTagOutput"
            fields = ("id", "name")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="supporting_docs_tags_list",
        operation_summary="List of all supporting doc tags",
    )
    def get(self, request, *args, **kwargs):
        tags: list[SupportingDocumentType] = SupportingDocumentType.objects.all()
        return Response(self.OutputSerializer(tags, many=True).data, status=HTTP_200_OK)


class InvoiceCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = InvoiceCreateUpdateInputDataSerializer
    serializer_class = InvoiceDetailsSerializer

    @swagger_auto_schema(
        request_body=InvoiceCreateUpdateInputDataSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer()},
        operation_id="invoice_create_api",
        operation_summary="Invoice Create API",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data: InvoiceCreateUpdateData = self.validate_input_data()
        service = InvoiceService()
        try:
            invoice_id = service.create(data=data, user_id=self.get_user_id(), org_id=self.get_organization_id())
        except InvoiceService.InvoiceCreateException as e:
            logger.info("Invoice creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except InvoiceService.InvoiceAmountException as e:
            logger.info("Invoice creation failed due to amount validation", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = InvoiceCreateUpdateInputDataSerializer
    serializer_class = InvoiceDetailsSerializer

    @swagger_auto_schema(
        request_body=InvoiceCreateUpdateInputDataSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer()},
        operation_id="invoice_update_api",
        operation_summary="Invoice Update API",
    )
    @transaction.atomic
    def put(self, request, invoice_id, *args, **kwargs):
        data: InvoiceCreateUpdateData = self.validate_input_data()
        service = InvoiceService()
        try:
            service.update(
                invoice_id=invoice_id,
                data=data,
                user_id=self.get_user_id(),
                org_id=self.get_organization_id(),
                is_admin=self.user_is_admin(),
            )
        except InvoiceService.InvoiceUpdateException as e:
            logger.info("Invoice updation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except InvoiceService.InvoiceNotFoundException as e:
            logger.info("Invoice updation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceUpdateApiV2(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = InvoiceCreateUpdateInputDataSerializer
    serializer_class = InvoiceDetailsSerializer

    @swagger_auto_schema(
        request_body=InvoiceCreateUpdateInputDataSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer()},
        operation_id="invoice_update_api_v2",
        operation_summary="Invoice Update API V2",
    )
    @transaction.atomic
    def put(self, request, invoice_id, *args, **kwargs):
        data: InvoiceCreateUpdateData = self.validate_input_data()
        service = InvoiceService()
        try:
            service.update_v2(
                invoice_id=invoice_id,
                data=data,
                user_id=self.get_user_id(),
                org_id=self.get_organization_id(),
                is_admin=self.user_is_admin(),
            )
            invoice = invoice_fetch_all().filter(id=invoice_id).first()
        except InvoiceService.InvoiceUpdateException as e:
            logger.info("Invoice updation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except InvoiceService.InvoiceNotFoundException as e:
            logger.info("Invoice updation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: InvoiceDetailsSerializer()},
        operation_id="invoice_details",
        operation_summary="Invoice details",
    )
    def get(self, request, invoice_id, *args, **kwargs):
        invoice = (
            invoice_fetch_all()
            .filter(Q(vendor_id=self.get_organization_id()) | Q(client_id=self.get_organization_id()))
            .filter(id=invoice_id)
            .select_related("uploaded_by__org__invoice_config")
            .annotate_available_payment_request_count()
        ).first()
        if invoice is None:
            self.set_response_message("Invoice not found")
            return Response(status=HTTP_400_BAD_REQUEST)

        is_tds_enabled_on_org_level = (
            invoice.uploaded_by.org.invoice_config.is_tds_enabled
            if hasattr(invoice.uploaded_by.org, "invoice_config")
            else False
        )

        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                    "has_payment_request": invoice.payment_request_count > 0,
                    "is_tds_enabled_on_org_level": is_tds_enabled_on_org_level,
                    "invoice_uploaded_by_org_id": invoice.uploaded_by.org_id,
                },
            ).data,
            status=HTTP_200_OK,
        )


class CreditNoteListBaseApi(OrgBaseApi, ListModelMixin):
    class FilterSerializer(SearchTextFilterSerializer):
        project_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        order_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        remarks = serializers.CharField(required=False, allow_null=True, default=None)
        status = serializers.ChoiceField(
            choices=InvoiceCreditNoteStatus.choices, required=False, allow_null=True, default=None
        )
        credit_note_number = serializers.CharField(required=False, allow_null=True, default=None)
        vendor_name = serializers.CharField(required=False, allow_null=True, default=None)
        invoice_number = serializers.CharField(required=False, allow_null=True, default=None)
        reference_invoice_ids = SplitCharHashIdListField(required=False, allow_null=True, default=None)
        created_by_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        is_verified = serializers.BooleanField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "CreditNoteListBaseApiFilter"

    class OutputSerializer(CreditNoteModelSerializer):
        project_config = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=ProjectCountryConfigData.drf_serializer)
        def get_project_config(self, obj):
            return ProjectCountryConfigData.drf_serializer(
                ProjectCountryConfigCache.get(instance_id=obj.invoice.project_id)
            ).data

        class Meta(CreditNoteModelSerializer.Meta):
            ref_name = "CreditNoteListBaseApiOutput"
            fields = (
                "id",
                "invoice",
                "created_by",
                "created_at",
                "amount",
                "gst_amount",
                "debit_note_doc_name",
                "debit_note_doc",
                "credit_note_doc_name",
                "credit_note_doc",
                "is_verified",
                "verified_by",
                "verified_at",
                "remarks",
                "status",
                "credit_note_number",
                "actions",
                "project_config",
            )

    serializer_class = OutputSerializer

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = credit_note_fetch_all()
        if data.get("client_ids"):
            queryset = queryset.filter(invoice__client_id__in=data.get("client_ids"))
        if data.get("project_ids"):
            queryset = queryset.filter(invoice__project_id__in=data.get("project_ids"))
        if data.get("order_ids"):
            queryset = queryset.filter(invoice__order_id__in=data.get("order_ids"))
        if data.get("remarks"):
            queryset = queryset.filter(remarks__icontains=data.get("remarks"))
        if data.get("status"):
            queryset = queryset.filter(status=data.get("status"))
        if data.get("credit_note_number"):
            queryset = queryset.filter(credit_note_number__icontains=data.get("credit_note_number"))
        if data.get("vendor_name"):
            queryset = queryset.filter(invoice__vendor__name__icontains=data.get("vendor_name"))
        if data.get("invoice_number"):
            queryset = queryset.filter(invoice__invoice_number__icontains=data.get("invoice_number"))
        is_verified = data.get("is_verified")
        if is_verified is not None:
            queryset = queryset.filter(is_verified=is_verified)
        if data.get("search_text"):
            queryset = queryset.filter(
                Q(remarks__icontains=data.get("search_text"))
                | Q(status=data.get("search_text"))
                | Q(credit_note_number__icontains=data.get("search_text"))
                | Q(invoice__vendor__name__icontains=data.get("search_text"))
                | Q(invoice__invoice_number__icontains=data.get("search_text"))
            )
        if data.get("reference_invoice_ids"):
            queryset = queryset.filter(invoice_id__in=data.get("reference_invoice_ids"))
        if data.get("created_by_ids"):
            queryset = queryset.filter(created_by_id__in=data.get("created_by_ids"))
        return queryset


class ClientCreditNoteListApi(CreditNoteListBaseApi):
    class FilterSerializer(CreditNoteListBaseApi.FilterSerializer):
        client_ids = SplitCharHashIdListField(required=False, default=None)

        def validate(self, attrs):
            if not attrs.get("client_ids") and not attrs.get("project_ids") and not attrs.get("order_ids"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

    REQUIRED_PERMISSIONS = []

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = super().get_queryset().filter(invoice__vendor_id=self.get_organization_id())
        if data.get("client_ids"):
            queryset = queryset.filter(invoice__client_id__in=self.validate_filter_data().get("client_ids"))
        return queryset

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        operation_id="client_credit_note_list_api",
        operation_summary="CreditNote list",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class VendorCreditNoteListApi(CreditNoteListBaseApi):
    class FilterSerializer(CreditNoteListBaseApi.FilterSerializer):
        vendor_ids = SplitCharHashIdListField(required=False, default=None)

        def validate(self, attrs):
            if not attrs.get("vendor_ids") and not attrs.get("project_ids") and not attrs.get("order_ids"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

    REQUIRED_PERMISSIONS = []

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = super().get_queryset().filter(invoice__client_id=self.get_organization_id())
        if data.get("vendor_ids"):
            queryset = queryset.filter(invoice__vendor_id__in=data.get("vendor_ids"))
        return queryset

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        operation_id="Vendor_credit_note_list_api",
        operation_summary="CreditNote list",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class InvoiceOrderConfigUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        is_order_mandatory = serializers.BooleanField()

        class Meta:
            ref_name = "InvoiceOrderConfigUpdateInputDataSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="invoice_order_config_update",
        operation_summary="Invoice Order Config Update",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        invoice_config_order_mandatory_update(
            user_id=self.get_user_id(), org_id=self.get_organization_id(), is_order_mandatory=data["is_order_mandatory"]
        )
        self.set_response_message("Order Mandatory Config Updated Succesfully")
        return Response(status=HTTP_200_OK)


class InvoicePoConfigUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        is_po_mandatory = serializers.BooleanField()

        class Meta:
            ref_name = "InvoicePoConfigUpdateInputDataSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="invoice_po_config_update",
        operation_summary="Invoice Po Config Update",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            invoice_config_po_mandatory_update(
                user_id=self.get_user_id(), org_id=self.get_organization_id(), is_po_mandatory=data["is_po_mandatory"]
            )
            self.set_response_message("Po Mandatory Config Updated Succesfully")
            return Response(status=HTTP_200_OK)
        except InvoiceConfigException:
            self.set_response_message("Po Mandatory Update Required")
            return Response(status=HTTP_400_BAD_REQUEST)


class InvoiceTDSConfigUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        is_tds_enabled = serializers.BooleanField()

        class Meta:
            ref_name = "InvoiceTDSConfigUpdateInputDataSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="invoice_tds_config_update",
        operation_summary="Invoice TDS Config Update",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            invoice_config_tds_update(org_id=self.get_organization_id(), is_tds_enabled=data["is_tds_enabled"])
            self.set_response_message("TDS Mandatory Config Updated Succesfully")
            return Response(status=HTTP_200_OK)
        except InvoiceConfigException:
            self.set_response_message("TDS Mandatory Update Required")
            return Response(status=HTTP_400_BAD_REQUEST)


class InvoiceHeaderBaseApi(OrgBaseApi):
    class FilterSerializer(BaseSerializer):
        project_id = HashIdField(allow_null=True, default=None)
        order_id = HashIdField(allow_null=True, default=None)
        currency_id = HashIdField(allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceHeaderBaseApiFilter"

    class OutputSerializer(BaseSerializer):
        total_invoice_count = serializers.IntegerField()
        total_invoice_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        approved_invoice_count = serializers.IntegerField()
        approved_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        pending_invoice_count = serializers.IntegerField()
        pending_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        total_credit_note_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        approved_credit_note_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        pending_credit_note_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        remaining_total_invoice_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        remaining_approved_invoice_amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        remaining_pending_invoice_amount = serializers.DecimalField(max_digits=20, decimal_places=2)

        class Meta:
            ref_name = "InvoiceHeaderBaseApiOutput"

    serializer_class = OutputSerializer


class ClientInvoiceHeaderApi(InvoiceHeaderBaseApi):
    class FilterSerializer(InvoiceHeaderBaseApi.FilterSerializer):
        client_ids = SplitCharHashIdListField(allow_empty=True, default=list)

        def validate(self, attrs):
            if not attrs.get("client_ids") and not attrs.get("project_id") and not attrs.get("order_id"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

        class Meta:
            ref_name = "ClientInvoiceHeaderApiOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: InvoiceHeaderBaseApi.OutputSerializer},
        operation_id="client_invoice_header_api",
        operation_summary="Client Invoice Header",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        response = client_invoice_header_data_get(
            project_id=data.get("project_id"),
            order_id=data.get("order_id"),
            client_ids=data.get("client_ids"),
            org_id=self.get_organization_id(),
            currency_id=data.get("currency_id"),
        )
        return Response(InvoiceHeaderBaseApi.OutputSerializer(response).data, status=HTTP_200_OK)


class VendorInvoiceHeaderApi(InvoiceHeaderBaseApi):
    class FilterSerializer(InvoiceHeaderBaseApi.FilterSerializer):
        vendor_ids = SplitCharHashIdListField(allow_empty=True, default=list)

        def validate(self, attrs):
            if not attrs.get("vendor_ids") and not attrs.get("project_id") and not attrs.get("order_id"):
                raise ValidationError("At least one filter field is required")
            return super().validate(attrs)

        class Meta:
            ref_name = "VendorInvoiceHeaderApiFilter"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: InvoiceHeaderBaseApi.OutputSerializer},
        operation_id="vendor_invoice_header_api",
        operation_summary="vendor Invoice Header",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        response = vendor_invoice_header_data_get(
            project_id=data.get("project_id"),
            order_id=data.get("order_id"),
            vendor_ids=data.get("vendor_ids"),
            org_id=self.get_organization_id(),
            currency_id=data.get("currency_id"),
        )
        return Response(InvoiceHeaderBaseApi.OutputSerializer(response).data, status=HTTP_200_OK)


class InvoiceOrderListApi(OrgBaseApi, ListModelMixin):
    class FilterSerializer(SearchTextFilterSerializer):
        project_id = HashIdField(required=False, default=None, allow_null=True)
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        vendor_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        client_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)

        def validate(self, attrs):
            if attrs.get("order_type") == OrderType.INCOMING.value:
                if not attrs.get("project_id") and not attrs.get("client_ids"):
                    raise ValidationError("Project or client at least one are required.")
            else:
                if not attrs.get("project_id") and not attrs.get("vendor_ids"):
                    raise ValidationError("Project or vendor at least one are required.")
            return super().validate(attrs)

        class Meta:
            ref_name = "InvoiceOrderListApiFilter"

    class OutputSerializer(VendorOrderModelSerializer):
        project_id = HashIdField()
        element_count = serializers.IntegerField(source="not_cancelled_elements_count")
        vendor = OrganizationSerializer(source="org_to")
        order_number = serializers.CharField(source="full_order_number")
        amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="order_amount")

        class Meta(VendorOrderModelSerializer.Meta):
            fields = ("id", "order_number", "vendor", "amount", "element_count", "project_id")
            ref_name = "InvoiceOrderListApiOutput"

    REQUIRED_PERMISSIONS = []
    filter_serializer_class = FilterSerializer
    filterset_class = InvoiceOrderListFilter
    serializer_class = OutputSerializer

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = invoice_order_list(
            project_id=data.get("project_id"),
            order_type=data.get("order_type"),
            vendor_ids=data.get("vendor_ids"),
            client_ids=data.get("client_ids"),
            organization_id=self.get_organization_id(),
        )
        return queryset

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="invoice_order_list",
        operation_summary="Invoice Order List",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class InvoiceTypeListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(InvoiceTypeModelSerializer):
        class Meta(InvoiceTypeModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "InvoiceTypeApiOutput"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_type_api",
        operation_summary=" Invoice Type Api",
    )
    def get(self, request, *args, **kwargs):
        return Response(
            status=HTTP_200_OK,
            data=self.OutputSerializer(InvoiceType.objects.filter(is_active=True), many=True).data,
        )


class InvoicePoListApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(SearchTextFilterSerializer):
        order_ids = SplitCharHashIdListField(required=False, allow_empty=True, default=list)
        vendor_id = HashIdField(required=False, allow_null=True, default=None)
        project_id = HashIdField()

        class Meta:
            ref_name = "InvoicePoListApiFilter"

    class OutputSerializer(VendorPurchaseOrderModelSerializer):
        class OrderSerializer(VendorOrderModelSerializer):
            vendor = OrganizationSerializer(source="org_to")
            amount = serializers.SerializerMethodField()

            def get_amount(self, obj):
                return self.context.get("order_amount")

            class Meta(VendorOrderModelSerializer.Meta):
                fields = ("id", "order_number", "vendor", "amount")

        order = serializers.SerializerMethodField()

        def get_order(self, obj):
            return self.OrderSerializer(obj.vendor_order, context={"order_amount": obj.order_amount}).data

        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = ("id", "po_number", "order")
            ref_name = "InvoicePoListApiOutput"

    filter_serializer_class = FilterSerializer
    serializer_class = OutputSerializer

    def get_queryset(self):
        data = self.validate_filter_data()
        queryset = (
            VendorPurchaseOrder.objects.select_related("vendor_order__org_to")
            .filter(vendor_order__project_id=data.get("project_id"))
            .annotate_vendor_order_amount()
            .exclude(Q(cancelled_at__isnull=False) | Q(status=POStatus.PO_CANCELLED))
            .distinct("po_number")
        )
        if data.get("vendor_id"):
            queryset = queryset.filter(vendor_order__org_to_id=data.get("vendor_id"))
        if data.get("order_ids"):
            queryset = queryset.filter(vendor_order_id__in=data.get("order_ids"))
        if data.get("search_text"):
            queryset = queryset.filter(Q(po_number__icontains=data.get("search_text")))
        return queryset

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_type_api",
        operation_summary=" Invoice Type Api",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class InvoiceDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: InvoiceDetailsSerializer()},
        operation_id="invoice_delete_api",
        operation_summary="Invoice Delete Api",
    )
    @transaction.atomic
    def delete(self, request, invoice_id, *args, **kwargs):
        invoice_service = InvoiceService()
        invoice_service.delete(invoice_id=invoice_id, user_id=self.get_user_id())
        payment_request_service = PaymentRequestService(user=self.get_user())
        payment_request_service.cancel_on_parent_resource_cancellation(invoice_ids=[invoice_id])
        return Response(
            status=HTTP_200_OK,
        )


class InvoiceCancelApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: InvoiceDetailsSerializer},
        operation_id="invoice_cancel_api",
        operation_summary="Invoice Cancel Api",
    )
    @transaction.atomic
    def put(self, request, invoice_id, *args, **kwargs):
        invoice_service = InvoiceService()
        invoice_service.cancel(invoice_id=invoice_id, user_id=self.get_user_id())
        PaymentRequestService(user=self.get_user()).cancel_on_parent_resource_cancellation(invoice_ids=[invoice_id])
        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceFilterProjectListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        client_id = HashIdField(required=False, allow_null=True, default=None)
        vendor_id = HashIdField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceFilterProjectListApiFilter"

    class OutputSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "InvoiceFilterProjectListApiOutput"

    filter_serializer_class = FilterSerializer
    serializer_class = OutputSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_filter_project_list",
        operation_summary="Invoice Filter Project List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        invoices = invoice_fetch_all()
        if data.get("client_id"):
            invoices = invoices.filter(client_id=data.get("client_id"))
        if data.get("vendor_id"):
            invoices = invoices.filter(vendor_id=data.get("vendor_id"))
        if data.get("order_type") == OrderType.INCOMING.value:
            project_ids = invoices.filter(vendor_id=self.get_organization_id()).values_list("project_id", flat=True)
        else:
            project_ids = invoices.filter(client_id=self.get_organization_id()).values_list("project_id", flat=True)
        queryset = Project.objects.filter(id__in=project_ids)
        return Response(self.OutputSerializer(queryset, many=True).data, status=HTTP_200_OK)


class InvoiceFilterOrderListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        project_id = HashIdField(required=False, allow_null=True, default=None)
        client_id = HashIdField(required=False, allow_null=True, default=None)
        vendor_id = HashIdField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceFilterOrderListApiFilter"

    class OutputSerializer(VendorOrderModelSerializer):
        class Meta(VendorOrderModelSerializer.Meta):
            fields = ("id", "order_number")
            ref_name = "InvoiceFilterOrderListApiOutput"

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_filter_order_list",
        operation_summary="Invoice Filter Order List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        queryset = invoice_filter_order_list(
            data.get("order_type"),
            self.get_organization_id(),
            project_id=data.get("project_id"),
            client_id=data.get("client_id"),
            vendor_id=data.get("vendor_id"),
        )
        return Response(self.OutputSerializer(queryset, many=True).data, status=HTTP_200_OK)


class InvoiceFilterPoListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        project_id = HashIdField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceFilterPoListApiFilter"

    class OutputSerializer(VendorPurchaseOrderModelSerializer):
        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = ("id", "po_number")
            ref_name = "InvoiceFilterPoListApiOutput"

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_filter_po_list",
        operation_summary="Invoice Filter Po List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        queryset = invoice_filter_po_list(
            order_type=data.get("order_type"),
            organization_id=self.get_organization_id(),
            project_id=data.get("project_id"),
        )
        return Response(self.OutputSerializer(queryset, many=True).data, status=HTTP_200_OK)


class InvoiceFilterUploaderListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        project_id = HashIdField(required=False, allow_null=True, default=None)
        client_id = HashIdField(required=False, allow_null=True, default=None)
        vendor_id = HashIdField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceFilterUploaderListApiFilter"

    class OutputSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name", "photo")
            ref_name = "InvoiceFilterUploaderListApiOutput"

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_filter_uploader_list",
        operation_summary="Invoice Filter Uploader List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        invoices = invoice_fetch_all()
        if data.get("client_id"):
            invoices = invoices.filter(client_id=data.get("client_id"))
        if data.get("vendor_id"):
            invoices = invoices.filter(vendor_id=data.get("vendor_id"))
        if data.get("project_id"):
            invoices = invoices.filter(project_id=data.get("project_id"))
        if data.get("order_type") == OrderType.INCOMING.value:
            user_ids = invoices.filter(vendor_id=self.get_organization_id()).values_list("uploaded_by_id", flat=True)
        else:
            user_ids = invoices.filter(client_id=self.get_organization_id()).values_list("uploaded_by_id", flat=True)
        queryset = User.objects.filter(id__in=user_ids, org=self.get_organization_id())
        return Response(self.OutputSerializer(queryset, many=True).data, status=HTTP_200_OK)


class InvoiceFilterVendorListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)
        project_id = HashIdField(required=False, allow_null=True, default=None)

        class Meta:
            ref_name = "InvoiceFilterVendorListApiFilter"

    class OutputSerializer(OrganizationModelSerializer):
        class Meta(OrganizationModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "InvoiceFilterVendorListApiOutput"

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="invoice_filter_vendor_list",
        operation_summary="Invoice Filter Vendor List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        queryset = invoice_fetch_all()
        if data.get("project_id"):
            queryset = queryset.filter(project_id=data.get("project_id"))
        if data.get("order_type") == OrderType.INCOMING.value:
            vendor_ids = queryset.filter(vendor_id=self.get_organization_id()).values_list("vendor_id", flat=True)
        else:
            vendor_ids = queryset.filter(client_id=self.get_organization_id()).values_list("vendor_id", flat=True)
        queryset = Organization.objects.filter(id__in=vendor_ids)
        return Response(self.OutputSerializer(queryset, many=True).data, status=HTTP_200_OK)


class InvoiceRequestReplyListApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []
    serializer_class = CommentReplyModelSerializer

    def get_queryset(self, *args, **kwargs):
        comment_id = invoice_request_comment_id_get(
            invoice_id=self.kwargs.get("invoice_id"),
            request_id=self.kwargs.get("request_id"),
            org_id=self.get_organization_id(),
        )
        if not comment_id:
            return []
        return get_comment_replies(comment_id=comment_id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: CommentReplyModelSerializer(many=True)},
        operation_id="invoice_request_reply_list_api",
        operation_summary="Invoice Request Reply List API",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class InvoiceRequestReplyCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = TaskReplyCreateInputSerializer

    @swagger_auto_schema(
        request_body=CommentReplyDataSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="invoice_request_reply_create_api",
        operation_summary="Invoice Request Reply Create API",
    )
    @transaction.atomic
    def post(self, request, invoice_id, request_id, *args, **kwargs):
        data = self.validate_input_data()
        comment_id = invoice_request_comment_id_get(
            invoice_id=invoice_id, request_id=request_id, org_id=self.get_organization_id()
        )
        invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        if not invoice.project_id:
            return Response(status=HTTP_404_NOT_FOUND)
        is_task_callback_allowed = True
        if comment_id is None and self.get_organization_id() == invoice.vendor_id:
            comment_id = create_invisible_comment(
                context=MicroContext.INVOICE,
                org_id=self.get_organization_id(),
                project_id=invoice.project_id,
                user_id=self.get_user_id(),
                context_id=invoice_id,
            )
            invoice_request_secondary_comment_update(
                invoice_id=invoice_id, request_id=request_id, comment_id=comment_id
            )
            is_task_callback_allowed = False
        if not comment_id:
            return Response(status=HTTP_404_NOT_FOUND)
        try:
            type = (
                CommentTypeEnum.NORMAL
                if len(mentioned_user_ids_get_by_description(data.get("description"))) == 0
                else CommentTypeEnum.MENTION
            )
            instance = comment_reply_create(
                data=CommentReplyData(type=type.value, blocks=data.get("description")),
                comment_id=comment_id,
                user_id=self.get_user_id(),
                project_id=invoice.project_id,
                org_id=self.get_organization_id(),
                is_task_callback_allowed=is_task_callback_allowed,
            )
        except CommentReplyException as e:
            logger.info(e.message)
            self.set_response_message("Error occurred while posting reply, Please try again.")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            CommentReplyModelSerializer(instance, context=self.get_serializer_context()).data,
            status=HTTP_201_CREATED,
        )


class CreditNoteCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = CreditNoteDataSerializer

    @swagger_auto_schema(
        request_body=CreditNoteDataSerializer(),
        responses={HTTP_201_CREATED: CreditNoteDetailsSerializer()},
        operation_id="vendor_order_invoice_credit_note_create",
        operation_summary="Create new credit note.",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        credit_note: CreditNoteData = self.validate_input_data()
        service = CreditNoteService()
        try:
            instance = service.create(data=credit_note, user_id=self.get_user_id())
        except CreditNoteService.CreditNoteCreateException as e:
            logger.info("Credit note creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except CreditNoteService.GSTValidationException as e:
            logger.info("Credit note creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except CreditNoteService.AmountException as e:
            logger.info("Credit note creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        return Response(CreditNoteDetailsSerializer(instance).data, status=HTTP_201_CREATED)


class CreditNoteDetailsApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: CreditNoteDetailsSerializer()},
        operation_id="vendor_order_invoice_credit_note_details",
        operation_summary="Get order invoice credit note detail.",
    )
    def get(self, request, credit_note_id):
        data = credit_note_fetch(credit_note_id=credit_note_id)
        return Response(CreditNoteDetailsSerializer(data).data, status=HTTP_200_OK)


class CreditNoteUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = CreditNoteDataSerializer

    @swagger_auto_schema(
        request_body=CreditNoteDataSerializer,
        responses={HTTP_202_ACCEPTED: CreditNoteDetailsSerializer()},
        operation_id="vendor_order_invoice_credit_note_update",
        operation_summary="Update order invoice credit note.",
    )
    @transaction.atomic
    def put(self, request, credit_note_id, *args, **kwargs):
        credit_note: CreditNoteDataSerializer = self.validate_input_data()
        service = CreditNoteService()
        try:
            credit_note_instance = service.update(
                credit_note_id=credit_note_id, data=credit_note, user_id=self.get_user_id()
            )
        except CreditNoteService.CreditNoteCreateException as e:
            logger.info("Credit note creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except CreditNoteService.GSTValidationException as e:
            logger.info("Credit note creation failed", error=e.message)
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(CreditNoteDetailsSerializer(credit_note_instance).data, status=HTTP_202_ACCEPTED)


class CreditNoteDeleteApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        operation_id="vendor_order_invoice_credit_note_delete",
        operation_summary="Delete order invoice credit note",
    )
    @transaction.atomic
    def delete(self, request, credit_note_id, *args, **kwargs):
        credit_note_service = CreditNoteService()
        credit_note_service.delete(credit_note_id=credit_note_id, user_id=self.get_user_id())
        return Response(status=HTTP_202_ACCEPTED)


class CreditNoteCancelApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: CreditNoteDetailsSerializer},
        operation_id="vendor_order_invoice_credit_note_cancel",
        operation_summary="Cancel order invoice credit note",
    )
    @transaction.atomic
    def put(self, request, credit_note_id, *args, **kwargs):
        credit_note_service = CreditNoteService()
        credit_note_service.cancel(credit_note_id=credit_note_id, user_id=self.get_user_id())
        data = credit_note_fetch(credit_note_id=credit_note_id)
        return Response(CreditNoteDetailsSerializer(data).data, status=HTTP_200_OK)


class InvoiceRequestApproveApi(ResourceActionBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer},
        operation_id="invoice_request_approve_api",
        operation_description="Api for invoice approve request",
    )
    @transaction.atomic
    def post(self, request, request_id, invoice_id, *args, **kwargs):
        data = self.validate_input_data()
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.APPROVE_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.APPROVE.value

        try:
            invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        except InvoiceException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_404_NOT_FOUND)
        self.resource_action_service = InvoiceResourceActionServiceFactory.get_service(
            data=request_action_data_get(request_data=invoice.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )
        self.resource_callback = RequestToInvoiceCallback(resource_id=invoice_id, user_id=request.user.pk)
        self.request_action_data = ResourceToRequestApproveServiceData(
            request_id=request_id,
            approve_by_id=request.user.pk,
            remark=data.get("remark"),
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request approver service input data ",
            ResourceToRequestApproveServiceData=self.request_action_data,
        )
        self.set_context(context=MicroContextChoices.INVOICE.value)
        self.set_context_id(context_id=invoice_id)
        logger.info("Request approval started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        except ResourceToRequestActionService.RequestApproveException:
            self.set_response_message("Request is not approved.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not approved.")})
        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        self.set_response_message("Invoice request is successfully approved.")
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceRequestRejectApi(ResourceActionBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer},
        operation_id="invoice_request_reject_api",
        operation_description="Api for invoice reject request",
    )
    @transaction.atomic
    def post(self, request, request_id, invoice_id, *args, **kwargs):
        data = self.validate_input_data()
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.REJECT_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.REJECT.value

        try:
            invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        except InvoiceException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_404_NOT_FOUND)
        self.resource_action_service = InvoiceResourceActionServiceFactory.get_service(
            data=request_action_data_get(request_data=invoice.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )
        self.resource_callback = RequestToInvoiceCallback(resource_id=invoice.pk, user_id=request.user.pk)
        self.request_action_data = ResourceToRequestRejectServiceData(
            context=MicroContextChoices.INVOICE.value,
            context_id=invoice_id,
            org_id=self.get_organization_id(),
            request_id=request_id,
            reject_by_id=request.user.pk,
            remark=data.get("remark"),
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request reject service input data ",
            ResourceToRequestRejectServiceData=self.request_action_data,
        )
        self.set_context(context=MicroContextChoices.INVOICE.value)
        self.set_context_id(context_id=invoice_id)
        logger.info("Request rejection started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        except ResourceToRequestActionService.RequestRejectException:
            self.set_response_message("Request is not rejected.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not rejected.")})
        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        self.set_response_message("Invoice request is successfully rejected.")
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceRequestHoldApi(ResourceActionBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer},
        operation_id="invoice_request_hold_api",
        operation_description="Api for invoice hold request",
    )
    @transaction.atomic
    def post(self, request, request_id, invoice_id, *args, **kwargs):
        data = self.validate_input_data()
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.HOLD_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.HOLD.value

        try:
            invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        except InvoiceException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_404_NOT_FOUND)
        self.resource_action_service = InvoiceResourceActionServiceFactory.get_service(
            data=request_action_data_get(request_data=invoice.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )
        self.resource_callback = RequestToInvoiceCallback(resource_id=invoice_id, user_id=request.user.pk)
        self.request_action_data = ResourceToRequestHoldServiceData(
            context=MicroContextChoices.INVOICE.value,
            context_id=invoice_id,
            org_id=self.get_organization_id(),
            request_id=request_id,
            hold_by_id=request.user.pk,
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request hold service input data ", ResourceToRequestHoldServiceData=self.request_action_data
        )
        self.set_context(context=MicroContextChoices.INVOICE.value)
        self.set_context_id(context_id=invoice_id)
        logger.info("Request hold started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceToRequestActionService.RequestHoldException:
            self.set_response_message("Request is not put on hold.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not put on hold.")})
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        invoice = invoice_fetch_all().filter(id=invoice_id).first()
        self.set_response_message("Invoice request is successfully put on hold.")
        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class InvoiceRequestCancelApi(ResourceActionBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        responses={HTTP_200_OK: InvoiceDetailsSerializer},
        operation_id="invoice_request_cancel_api",
        operation_description="Api for cancel invoice request",
    )
    @transaction.atomic
    def post(self, request, request_id, invoice_id, *args, **kwargs):
        data = self.validate_input_data()
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.CANCEL_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.CANCEL.value
        try:
            invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        except InvoiceException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_404_NOT_FOUND)

        self.resource_action_service = InvoiceResourceActionServiceFactory().get_service(
            data=request_action_data_get(request_data=invoice.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )
        self.resource_callback = RequestToInvoiceCallback(resource_id=invoice_id, user_id=request.user.pk)
        self.request_action_data = ResourceToRequestCancelServiceData(
            context=MicroContextChoices.INVOICE.value,
            context_id=invoice_id,
            org_id=self.get_organization_id(),
            request_id=request_id,
            cancel_by_id=request.user.pk,
            is_admin=self.is_admin_with_cancel_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Invoice to request cancel service input data ",
            ResourceToRequestCancelServiceData=self.request_action_data,
        )
        self.set_context(context=MicroContextChoices.INVOICE.value)
        self.set_context_id(context_id=invoice_id)
        logger.info("Invoice Request cancelation started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceToRequestActionService.CancelException:
            self.set_response_message("Request is not cancelled.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not cancelled.")})
        self.set_response_message("Request is cancelled  successfully.")
        payment_request_service = PaymentRequestService(user=self.get_user())
        payment_request_service.cancel_on_parent_resource_cancellation(invoice_ids=[invoice_id])
        invoice = invoice_fetch_all().filter(id=invoice_id).first()

        return Response(
            InvoiceDetailsSerializer(
                invoice,
                context={
                    "org_id": self.get_organization_id(),
                    "user_id": self.get_user_id(),
                    "is_admin": self.user_is_admin(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class CreditNoteFilterCreatorListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)

        class Meta:
            ref_name = "CreditNoteFilterCtreatorListApiFilter"

    class OutputSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "CreditNoteFilterCtreatorListApiOutput"

    filter_serializer_class = FilterSerializer
    serializer_class = OutputSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="credit_note_filter_creator_list",
        operation_summary="Credit Note Filter Creator List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        if data.get("order_type") == OrderType.INCOMING.value:
            created_by_ids = (
                credit_note_fetch_all()
                .filter(invoice__vendor_id=self.get_organization_id())
                .values_list("created_by_id", flat=True)
            )
        else:
            created_by_ids = (
                credit_note_fetch_all()
                .filter(invoice__client_id=self.get_organization_id())
                .values_list("created_by_id", flat=True)
            )
        query = User.objects.filter(id__in=created_by_ids)
        return Response(self.OutputSerializer(query, many=True).data, status=HTTP_200_OK)


class CreditNoteFilterRefInvoiceListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(choices=OrderType.choices)

        class Meta:
            ref_name = "CreditNoteFilterRefInvoiceListApiFilter"

    class OutputSerializer(InvoiceModelSerializer):
        class Meta(InvoiceNumberSerializer.Meta):
            fields = ("id", "invoice_number")
            ref_name = "CreditNoteFilterRefInvoiceListApiOutput"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer},
        operation_id="credit_note_filter_ref_invoice_list",
        operation_summary="Credit Note Filter Ref Invoice List",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        if data.get("order_type") == OrderType.INCOMING.value:
            ref_invoice_ids = (
                credit_note_fetch_all()
                .filter(invoice__vendor_id=self.get_organization_id())
                .values_list("invoice_id", flat=True)
            )
        else:
            ref_invoice_ids = (
                credit_note_fetch_all()
                .filter(invoice__client_id=self.get_organization_id())
                .values_list("invoice_id", flat=True)
            )
        query = invoice_fetch_all().filter(id__in=ref_invoice_ids)
        return Response(self.OutputSerializer(query, many=True).data, status=HTTP_200_OK)
