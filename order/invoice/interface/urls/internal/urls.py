from django.urls import path

from order.invoice.interface.apis.internal.apis import (
    ClientCreditNoteListApi,
    ClientInvoiceHeaderApi,
    ClientInvoiceListApi,
    CreditNoteCancelApi,
    CreditNoteCreateApi,
    CreditNoteDeleteApi,
    CreditNoteDetailsApi,
    CreditNoteFilterCreatorListApi,
    CreditNoteFilterRefInvoiceListApi,
    CreditNoteUpdateApi,
    InvoiceCancelApi,
    InvoiceConfigApi,
    InvoiceCreateApi,
    InvoiceDeleteApi,
    InvoiceDetailsApi,
    InvoiceFilterOrderListApi,
    InvoiceFilterPoListApi,
    InvoiceFilterProjectListApi,
    InvoiceFilterUploaderList<PERSON><PERSON>,
    InvoiceFilterVendorListApi,
    InvoiceOrderConfigUpdateApi,
    InvoiceOrderListApi,
    InvoicePoConfigUpdateApi,
    InvoicePoListApi,
    InvoiceRequestApproveApi,
    InvoiceRequestCancel<PERSON>pi,
    InvoiceRequestHoldApi,
    InvoiceRequestReject<PERSON>pi,
    InvoiceRequestReplyCreateApi,
    InvoiceRequestReplyListApi,
    InvoiceTDSConfigUpdateApi,
    InvoiceTypeListApi,
    InvoiceUpdateApi,
    InvoiceUpdateApiV2,
    SupportingDocTagList,
    VendorCreditNoteListApi,
    VendorInvoiceHeaderApi,
    VendorInvoiceListApi,
)

urlpatterns = [
    path("config/", InvoiceConfigApi.as_view(), name="invoice-config"),
    path("client-invoice/list/", ClientInvoiceListApi.as_view(), name="client-invoice-list"),
    path("vendor-invoice/list/", VendorInvoiceListApi.as_view(), name="vendor-invoice-list"),
    path("<hash_id:invoice_id>/details/", InvoiceDetailsApi.as_view(), name="invoice-details"),
    path("client-credit-note/list/", ClientCreditNoteListApi.as_view(), name="client-credit-notes-list"),
    path("vendor-credit-note/list/", VendorCreditNoteListApi.as_view(), name="vendor-credit-notes-list"),
    path(
        "update-order-mandatory/",
        InvoiceOrderConfigUpdateApi.as_view(),
        name="invoice-update-order-mandatory",
    ),
    path("update-po-mandatory/", InvoicePoConfigUpdateApi.as_view(), name="invoice-update-po-mandatory"),
    path("update-tds-config/", InvoiceTDSConfigUpdateApi.as_view(), name="invoice-update-tds"),
    path("vendor-invoice-header/", VendorInvoiceHeaderApi.as_view(), name="vendor-invoice-header"),
    path("client-invoice-header/", ClientInvoiceHeaderApi.as_view(), name="client-invoice-header"),
    path("order-list/", InvoiceOrderListApi.as_view(), name="invoice-order-list"),
    path("invoice-type/list/", InvoiceTypeListApi.as_view(), name="invoice-type-list"),
    path("create/", InvoiceCreateApi.as_view(), name="invoice-create"),
    path("<hash_id:invoice_id>/update/", InvoiceUpdateApi.as_view(), name="invoice-update"),
    path("<hash_id:invoice_id>/update-v2/", InvoiceUpdateApiV2.as_view(), name="invoice-update-v2"),
    path("po-list/", InvoicePoListApi.as_view(), name="po-list"),
    path("<hash_id:invoice_id>/delete/", InvoiceDeleteApi.as_view(), name="invoice-delete"),
    path("supporting-docs/tags/", SupportingDocTagList.as_view(), name="supporting-docs-tags"),
    path("<hash_id:invoice_id>/cancel/", InvoiceCancelApi.as_view(), name="invoice-cancel"),
    path("list/filter/project-list/", InvoiceFilterProjectListApi.as_view(), name="invoice-list-filter-project-list"),
    path("list/filter/order-list/", InvoiceFilterOrderListApi.as_view(), name="invoice-list-filter-order-list"),
    path("list/filter/po-list/", InvoiceFilterPoListApi.as_view(), name="invoice-list-filter-po-list"),
    path(
        "list/filter/uploader-list/", InvoiceFilterUploaderListApi.as_view(), name="invoices-list-filter-uploader-list"
    ),
    path("list/filter/vendor-list/", InvoiceFilterVendorListApi.as_view(), name="invoices-list-filter-vendor-list"),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/reply/list/",
        InvoiceRequestReplyListApi.as_view(),
        name="invoice-request-reply-list",
    ),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/reply/",
        InvoiceRequestReplyCreateApi.as_view(),
        name="invoice-request-reply-create",
    ),
    path("credit-note/create/", CreditNoteCreateApi.as_view(), name="credit-note-create"),
    path("credit-note/<hash_id:credit_note_id>/details/", CreditNoteDetailsApi.as_view(), name="credit-note-details"),
    path("credit-note/<hash_id:credit_note_id>/update/", CreditNoteUpdateApi.as_view(), name="credit-note-update"),
    path("credit-note/<hash_id:credit_note_id>/delete/", CreditNoteDeleteApi.as_view(), name="credit-note-delete"),
    path("credit-note/<hash_id:credit_note_id>/cancel/", CreditNoteCancelApi.as_view(), name="credit-note-cancel"),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/approve/",
        InvoiceRequestApproveApi.as_view(),
        name="invoice-approve",
    ),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/reject/",
        InvoiceRequestRejectApi.as_view(),
        name="invoice-reject",
    ),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/hold/", InvoiceRequestHoldApi.as_view(), name="invoice-hold"
    ),
    path(
        "<hash_id:invoice_id>/request/<hash_id:request_id>/cancel/",
        InvoiceRequestCancelApi.as_view(),
        name="invoice-request-cancel",
    ),
    path(
        "credit-note/list/filter/creator-list/",
        CreditNoteFilterCreatorListApi.as_view(),
        name="credit-note-filter-creator-list",
    ),
    path(
        "credit-note/list/filter/ref-invoice-list/",
        CreditNoteFilterRefInvoiceListApi.as_view(),
        name="credit-note-filter-ref-invoice-list",
    ),
]
