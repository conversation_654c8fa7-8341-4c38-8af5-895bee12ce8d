from typing import Any, Dict

from rest_framework import serializers

from common.entities import ObjectStatus
from common.serializers import BaseDataclassSerializer, CustomFileField, HashIdField, IdNameSerializer
from order.domain.exceptions import FileNameException
from order.invoice.data.choices import InvoiceSupportingDocumentStatus
from order.invoice.domain.entities import (
    InvoiceCreateUpdateData,
    InvoiceDocsCreateUpdateData,
    InvoiceTaxDetails,
    InvoiceTDSDetails,
)
from order.invoice.domain.entities.credit_note import CreditNoteDataV2
from order.invoice.domain.entities.invoice import (
    ClientInvoiceCreateUpdateV2Data,
    InvoiceCreateUpdateData,
    InvoiceDocsCreateUpdateData,
    InvoiceDocsCreateUpdateV2Data,
    InvoiceTaxDetails,
    InvoiceTDSDetails,
    VendorInvoiceCreateUpdateV2Data,
)
from order.invoice.interface.serializers.models import (
    CreditNoteModelSerializer,
    InvoiceModelSerializer,
    SupportingDocModelSerializer,
)


class InvoiceDocsCreateUpdateDataSerializer(BaseDataclassSerializer):
    id = HashIdField(required=False, allow_null=True, default=None)
    file = CustomFileField()
    tag_ids = serializers.ListField(child=HashIdField(), allow_empty=True)

    class Meta:
        dataclass = InvoiceDocsCreateUpdateData
        ref_name = "InvoiceDocsCreateUpdateDataSerializer"


class InvoiceTDSDetailsSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = InvoiceTDSDetails


class InvoiceTaxDetailsSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = InvoiceTaxDetails


class InvoiceCreateUpdateInputDataSerializer(BaseDataclassSerializer):
    vendor_id = HashIdField()
    client_id = HashIdField()
    project_id = HashIdField()
    order_id = HashIdField(required=False, allow_null=True, default=None)
    po_id = HashIdField(required=False, allow_null=True, default=None)
    type_id = HashIdField()
    docs = InvoiceDocsCreateUpdateDataSerializer(many=True)
    invoice_date = serializers.DateField()
    file = CustomFileField()
    vendor_po_id = HashIdField(required=False, allow_null=True, default=None)
    tds_details = InvoiceTDSDetailsSerializer(
        required=False, allow_null=True
    )  # required False for backward compatibility
    tax_details = InvoiceTaxDetailsSerializer(required=False)  # required False for backward compatibility

    def to_internal_value(self, data: Dict[str, Any]) -> Any:
        # Frontend sends po_id in payload, we need vendor_po_id for model_update
        data["vendor_po_id"] = data.get("po_id", None)

        tds_details = data.get("tds_details") or {}
        tax_details = data.get("tax_details") or {}

        data["tds_amount"] = tds_details.get("amount", None)
        data["tds_amount_percent"] = tds_details.get("amount_percent", 0)
        data["tds_amount_percent_toggle"] = tds_details.get("amount_percent_toggle", False)
        data["tax_amount_percent"] = tax_details.get("amount_percent", 0)
        data["tax_amount_percent_toggle"] = tax_details.get("amount_percent_toggle", False)
        return super().to_internal_value(data)

    def validate(self, attrs):
        if attrs.po_id and not attrs.order_id:
            raise serializers.ValidationError("Order is mandatory with po")
        if attrs.order_id and not attrs.project_id:
            raise serializers.ValidationError("Project is mandatory with order")
        return super().validate(attrs)

    class Meta:
        dataclass = InvoiceCreateUpdateData
        ref_name = "InvoiceCreateUpdateInputDataSerializer"


class InvoiceOrderConfigUpdateInputDataSerializer(BaseDataclassSerializer):
    is_order_mandatory = serializers.BooleanField(default=False)

    class Meta:
        ref_name = "InvoiceOrderConfigUpdateInputDataSerializer"


class InvoiceListOutputV2Serializer(InvoiceModelSerializer):
    number = serializers.CharField(source="invoice_number")
    invoice_date = serializers.DateField()

    class Meta(InvoiceModelSerializer.Meta):
        ref_name = "InvoiceListOutputV2Serializer"
        fields = ("id", "number", "invoice_date", "status")


class InvoiceDetailsOutputV2Serializer(InvoiceModelSerializer):
    number = serializers.CharField(source="invoice_number")
    po_id = HashIdField(source="vendor_po_id")
    amount_without_tax = serializers.CharField(source="amount")
    tax_amount = serializers.CharField(source="gst_amount")
    final_amount = serializers.SerializerMethodField()
    invoice_file_url = serializers.FileField(source="file")
    uploaded_by = IdNameSerializer()

    class Meta(InvoiceModelSerializer.Meta):
        ref_name = "InvoiceDetailsOutputV2Serializer"
        fields = (
            "id",
            "uploaded_by",
            "uploaded_at",
            "number",
            "type",
            "po_id",
            "order_id",
            "invoice_date",
            "amount_without_tax",
            "tax_amount",
            "tax_amount_percent",
            "tds_amount_percent",
            "tds_amount",
            "final_amount",
            "invoice_file_url",
            "status",
        )
        output_hash_id_fields = ("id", "order_id")


class InvoiceSupportingDocumentsListOutputV2Serializer(SupportingDocModelSerializer):
    name = serializers.CharField(source="file_name")

    class Meta(SupportingDocModelSerializer.Meta):
        ref_name = "InvoiceSupportingDocumentsListOutputV2Serializer"
        fields = ("id", "name")


class InvoiceSupportingDocumentDetailsOutputV2Serializer(SupportingDocModelSerializer):
    name = serializers.CharField(source="file_name")
    file_url = serializers.FileField(source="file")
    type = IdNameSerializer(source="tags", many=True)

    class Meta(SupportingDocModelSerializer.Meta):
        ref_name = "InvoiceSupportingDocumentDetailsOutputV2Serializer"
        fields = ("id", "name", "file_url", "type")


class InvoiceCreditNoteListOutputV2Serializer(CreditNoteModelSerializer):
    number = serializers.CharField(source="credit_note_number")

    class Meta(CreditNoteModelSerializer.Meta):
        ref_name = "InvoiceCreditNoteListOutputV2Serializer"
        fields = ("id", "number")


class InvoiceCreditNoteDetailsOutputV2Serializer(CreditNoteModelSerializer):
    number = serializers.CharField(source="credit_note_number")
    tax_amount = serializers.CharField(source="gst_amount")
    credit_note_document_url = serializers.FileField(source="credit_note_doc")
    debit_note_document_url = serializers.FileField(source="debit_note_doc")
    verified_status = serializers.SerializerMethodField()
    invoice_id = HashIdField()

    class Meta(CreditNoteModelSerializer.Meta):
        ref_name = "InvoiceCreditNoteDetailsOutputV2Serializer"
        fields = (
            "id",
            "number",
            "invoice_id",
            "amount",
            "tax_amount",
            "credit_note_document_url",
            "debit_note_document_url",
            "remarks",
            "verified_status",
            "verified_at",
            "verified_by",
            "created_by",
            "created_at",
            "status",
        )


class InvoiceDocsCreateInputV2Serializer(BaseDataclassSerializer):
    file = CustomFileField()
    tag_ids = serializers.ListField(child=HashIdField(), allow_empty=True)

    class Meta:
        dataclass = InvoiceDocsCreateUpdateV2Data
        ref_name = "InvoiceDocsCreateInputV2Serializer"


class VendorInvoiceCreateInputV2Serializer(BaseDataclassSerializer):
    vendor_id = HashIdField()
    order_id = HashIdField(required=False, allow_null=True, default=None)
    po_id = HashIdField(required=False, allow_null=True, default=None)
    type_id = HashIdField()
    docs = InvoiceDocsCreateInputV2Serializer(many=True)
    invoice_date = serializers.DateField()

    def validate(self, attrs):
        if attrs.po_id and not attrs.order_id:
            raise serializers.ValidationError("Order is mandatory with po")

        client_id = self.context.get("client_id")
        project_id = self.context.get("project_id")

        if client_id is None:
            raise serializers.ValidationError("Client id is missing in context")
        if project_id is None:
            raise serializers.ValidationError("Project id is missing in context")

        docs = [
            InvoiceDocsCreateUpdateData(
                id=None,
                name=doc.name,
                file=doc.file,
                tag_ids=doc.tag_ids,
                status=InvoiceSupportingDocumentStatus.UPLOADED,
                object_status=ObjectStatus.ADD,
            )
            for doc in attrs.docs
        ]

        return InvoiceCreateUpdateData(
            vendor_id=attrs.vendor_id,
            client_id=client_id,
            project_id=project_id,
            order_id=attrs.order_id,
            po_id=attrs.po_id,
            invoice_number=attrs.invoice_number,
            invoice_date=attrs.invoice_date,
            amount=attrs.amount,
            gst_amount=attrs.gst_amount,
            file=attrs.file,
            file_name=attrs.file_name,
            type_id=attrs.type_id,
            docs=docs,
        )

    class Meta:
        dataclass = VendorInvoiceCreateUpdateV2Data
        ref_name = "VendorInvoiceCreateInputV2Serializer"


class ClientInvoiceCreateInputV2Serializer(BaseDataclassSerializer):
    order_id = HashIdField(required=False, allow_null=True, default=None)
    po_id = HashIdField(required=False, allow_null=True, default=None)
    type_id = HashIdField()
    docs = InvoiceDocsCreateInputV2Serializer(many=True)
    invoice_date = serializers.DateField()

    def validate(self, attrs):
        if attrs.po_id and not attrs.order_id:
            raise serializers.ValidationError("Order is mandatory with po")

        client_id = self.context.get("client_id")
        vendor_id = self.context.get("vendor_id")
        project_id = self.context.get("project_id")

        if client_id is None:
            raise serializers.ValidationError("Client id is missing in context")
        if vendor_id is None:
            raise serializers.ValidationError("Vendor id is missing in context")
        if project_id is None:
            raise serializers.ValidationError("Project id is missing in context")

        docs = [
            InvoiceDocsCreateUpdateData(
                id=None,
                name=doc.name,
                file=doc.file,
                tag_ids=doc.tag_ids,
                status=InvoiceSupportingDocumentStatus.UPLOADED,
                object_status=ObjectStatus.ADD,
            )
            for doc in attrs.docs
        ]

        return InvoiceCreateUpdateData(
            vendor_id=vendor_id,
            client_id=client_id,
            project_id=project_id,
            order_id=attrs.order_id,
            po_id=attrs.po_id,
            invoice_number=attrs.invoice_number,
            invoice_date=attrs.invoice_date,
            amount=attrs.amount,
            gst_amount=attrs.gst_amount,
            file=attrs.file,
            file_name=attrs.file_name,
            type_id=attrs.type_id,
            docs=docs,
        )

    class Meta:
        dataclass = ClientInvoiceCreateUpdateV2Data
        ref_name = "ClientInvoiceCreateInputV2Serializer"


class InvoiceCreditNoteCreateInputV2Serializer(BaseDataclassSerializer):
    amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit_note_doc_url = CustomFileField(allow_null=True, required=False)
    debit_note_doc_url = CustomFileField(allow_null=True, required=False)
    credit_note_doc_name = serializers.CharField(allow_null=True, required=False)
    debit_note_doc_name = serializers.CharField(allow_null=True, required=False)
    remarks = serializers.CharField(allow_blank=True, allow_null=True, required=False)

    def validate(self, value):
        if value.credit_note_doc_url and not value.credit_note_doc_name:
            raise FileNameException("credit doc file name not available")
        if value.debit_note_doc_url and not value.debit_note_doc_name:
            raise FileNameException("debit doc file name not available")

        return value

    class Meta:
        dataclass = CreditNoteDataV2
