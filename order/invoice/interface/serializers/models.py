import decimal

from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    CustomFileField,
    HashIdField,
)
from core.serializers import OrganizationModelSerializer, UserModelSerializer
from order.domain.exceptions import FileNameException
from order.interface.serializers import VendorOrderModelSerializer, VendorPurchaseOrderModelSerializer
from order.interface.vendor_serializers import VendorOrderModelSerializer, VendorPurchaseOrderModelSerializer
from order.invoice.data.choices import CreditNoteVerificationStatusChoices, InvoiceStatus
from order.invoice.data.models import (
    Invoice,
    InvoiceCreditNote,
    InvoiceType,
    OrganizationInvoiceConfig,
    SupportingDocument,
    SupportingDocumentType,
)
from order.invoice.domain.entities import (
    CreditNoteData,
    SupportingDocumentCreateEntity,
    SupportingDocumentTagEntity,
    SupportingDocumentUpdateEntity,
)
from order.invoice.domain.factories import InvoiceActionServiceFactory
from order.invoice.domain.services import get_invoice_hidden_fields, get_invoice_readonly_fields
from project.serializers import ProjectModelSerializer
from rollingbanners.hash_id_converter import HashIdConverter


class UserSerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "UserOutput"
        fields = ("id", "name", "photo")


class OrganizationInvoiceConfigModelSerialzer(BaseModelSerializer):
    class Meta:
        fields = "__all__"
        model = OrganizationInvoiceConfig
        ref_name = "OrganizationInvoiceConfigModelSerialzer"


class InvoiceTypeModelSerializer(BaseModelSerializer):
    class Meta:
        fields = "__all__"
        model = InvoiceType
        ref_name = "InvoiceTypeModelSerializer"


class CreditNoteDataSerializer(BaseDataclassSerializer):
    invoice_id = HashIdField()
    amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit_note_doc = CustomFileField(allow_null=True, required=False)
    debit_note_doc = CustomFileField(allow_null=True, required=False)
    credit_note_doc_name = serializers.CharField(allow_null=True, required=False)
    debit_note_doc_name = serializers.CharField(allow_null=True, required=False)
    remarks = serializers.CharField(allow_blank=True, allow_null=True, required=False)

    def validate(self, value):
        if value.credit_note_doc and not value.credit_note_doc_name:
            raise FileNameException("credit doc file name not available")
        if value.debit_note_doc and not value.debit_note_doc_name:
            raise FileNameException("debit doc file name not available")

        return value

    class Meta:
        dataclass = CreditNoteData


class SupportingDocumentTagDataSerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = SupportingDocumentTagEntity


class SupportingDocumentBaseDataSerializer(BaseDataclassSerializer):
    file = CustomFileField(allow_null=True, default=None)
    tags = serializers.ListField(child=HashIdField(), required=False, allow_null=True, default=[])

    class Meta:
        pass


class SupportingDocumenCreateDataSerializer(SupportingDocumentBaseDataSerializer):
    id = HashIdField(required=False, allow_null=True, default=None)

    class Meta(SupportingDocumentBaseDataSerializer.Meta):
        dataclass = SupportingDocumentCreateEntity


class SupportingDocumentUpdateDataSerializer(SupportingDocumentBaseDataSerializer):
    id = HashIdField()

    class Meta(SupportingDocumentBaseDataSerializer.Meta):
        dataclass = SupportingDocumentUpdateEntity


class InvoiceBaseDataSerializer(BaseDataclassSerializer):
    file = CustomFileField(allow_null=True, default=None)
    vendor_po_id = HashIdField()
    amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    docs = SupportingDocumentUpdateDataSerializer(many=True)

    class Meta:
        pass


class SupportingDocumentTypeModelSerializer(BaseModelSerializer):
    class Meta:
        fields = "__all__"
        model = SupportingDocumentType


class SupportingDocModelSerializer(BaseModelSerializer):
    class SupportingDocumentTagSerializer(SupportingDocumentTypeModelSerializer):
        class Meta(SupportingDocumentTypeModelSerializer.Meta):
            fields = ("id", "name")

    tags = SupportingDocumentTagSerializer(many=True)

    class Meta:
        fields = "__all__"
        model = SupportingDocument


class InvoiceModelSerializer(BaseModelSerializer):
    class PurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
        number = serializers.CharField(source="po_number")

        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = ("id", "number")

    class SupportingDocSerializer(SupportingDocModelSerializer):
        uploaded_at = serializers.DateTimeField(source="created_at")
        uploaded_by = UserSerializer(source="created_by")
        actions = serializers.SerializerMethodField()

        def get_actions(self, obj):
            action_service = InvoiceActionServiceFactory().get_service(
                user_id=self.context.get("user_id"),
                is_admin=self.context.get("is_admin"),
                org_id=self.context.get("org_id"),
                invoice=self.context.get("invoice_obj"),
            )

            actions_list = action_service.get_supporting_docs_actions(
                current_invoice_status=self.context.get("invoice_status"), doc_invoice_status=obj.invoice_status
            )
            return actions_list

        class Meta(SupportingDocModelSerializer.Meta):
            fields = ("id", "file_name", "file", "tags", "uploaded_at", "uploaded_by", "actions", "status")

    class ProjectSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectOutput"
            fields = ("id", "name", "job_id")

    class VendorSerializer(OrganizationModelSerializer):
        code = serializers.CharField(source="vendor.code")

        class Meta(OrganizationModelSerializer.Meta):
            fields = ("id", "name", "code")
            ref_name = "InvoiceVendor"

    class OrderSerializer(VendorOrderModelSerializer):
        class Meta(VendorOrderModelSerializer.Meta):
            fields = ("id", "order_number")
            ref_name = "InvoiceOrder"

    class InvoiceTypeSerializer(InvoiceTypeModelSerializer):
        class Meta(InvoiceTypeModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "InvoiceType"

    doc_types = serializers.SerializerMethodField()
    docs = serializers.SerializerMethodField()
    purchase_order = PurchaseOrderSerializer(source="vendor_po")
    po_number = serializers.SerializerMethodField()
    uploaded_by = serializers.SerializerMethodField()
    uploaded_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()
    updated_by = UserSerializer()
    invoice_amount = serializers.DecimalField(max_digits=15, decimal_places=2, source="amount")
    invoice_gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2, source="gst_amount")
    payable_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    credit_note_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    credit_note_gst_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    doc_count = serializers.IntegerField()
    vendor = VendorSerializer()
    actions = serializers.SerializerMethodField()
    project = ProjectSerializer()
    order = OrderSerializer()
    type = InvoiceTypeSerializer()
    request_id = serializers.SerializerMethodField()
    task_id = serializers.SerializerMethodField()
    comment_count = serializers.IntegerField()
    status = serializers.SerializerMethodField()
    pending_payment_request_amount = serializers.SerializerMethodField()
    approved_payment_request_amount = serializers.SerializerMethodField()

    def get_docs(self, obj):
        docs = obj.supporting_documents.all()
        self.context.update({"invoice_status": obj.status, "invoice_obj": obj})
        return self.SupportingDocSerializer(docs, many=True, context=self.context).data

    def get_pending_payment_request_amount(self, obj):
        pending_amount = decimal.Decimal(0)
        if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
            pending_amount += decimal.Decimal(obj.invoice_payment_request_amount.get("pending_payment_request_amount"))

        return pending_amount

    def get_approved_payment_request_amount(self, obj):
        approved_amount = decimal.Decimal(0)
        if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
            approved_amount += decimal.Decimal(
                obj.invoice_payment_request_amount.get("approved_payment_request_amount")
            )
        return approved_amount

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_final_amount(self, obj):
        if hasattr(obj, "amount") and obj.amount and hasattr(obj, "gst_amount") and obj.gst_amount:
            return str(obj.amount + obj.gst_amount)

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_po_number(self, obj):
        if obj.vendor_po and obj.vendor_po.po_number:
            return obj.vendor_po.po_number
        return None

    @swagger_serializer_method(serializer_or_field=serializers.ListField())
    def get_doc_types(self, obj):
        doc_types = []
        docs = obj.supporting_documents.all()
        if docs:
            for doc in docs:
                if doc.tags.all():
                    for tag in doc.tags.all():
                        if tag.name not in doc_types:
                            doc_types.append(tag.name)
        return doc_types

    @swagger_serializer_method(serializer_or_field=serializers.ListField())
    def get_actions(self, obj):
        action_service = InvoiceActionServiceFactory().get_service(
            user_id=self.context.get("user_id"),
            is_admin=self.context.get("is_admin"),
            org_id=self.context.get("org_id"),
            invoice=obj,
        )
        user_actions = action_service.get_user_actions()
        return user_actions

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_request_id(self, obj):
        if not getattr(obj, "request_data"):
            return None
        if obj.status == InvoiceStatus.ATTACHED.value:
            return None
        return HashIdConverter.encode(obj.request_data["request_id"])

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_task_id(self, obj):
        if not getattr(obj, "request_data"):
            return None
        if obj.status == InvoiceStatus.ATTACHED.value:
            return None
        return HashIdConverter.encode(obj.request_data["task_id"])

    @swagger_serializer_method(serializer_or_field=serializers.DictField())
    def get_uploaded_by(self, obj):
        if obj.uploaded_by.org_id != self.context.get("org_id"):
            return {
                "id": obj.uploaded_by.org_id,
                "name": obj.uploaded_by.org.name,
                "photo": obj.uploaded_by.org.logo.url if obj.uploaded_by.org.logo else None,
            }
        else:
            return UserSerializer(obj.uploaded_by).data

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        if obj.vendor_id == self.context.get("org_id") and obj.status not in [
            InvoiceStatus.ATTACHED,
            InvoiceStatus.APPROVED,
            InvoiceStatus.REJECTED,
            InvoiceStatus.CANCELLED,
        ]:
            return InvoiceStatus.PENDING.value
        else:
            return obj.status

    class Meta:
        model = Invoice
        fields = "__all__"


class InvoiceNumberSerializer(InvoiceModelSerializer):
    number = serializers.CharField(source="invoice_number")

    class Meta(InvoiceModelSerializer.Meta):
        fields = ("id", "number")


class InvoiceDetailsSerializer(InvoiceModelSerializer):
    order_amount = serializers.SerializerMethodField()
    readonly_fields = serializers.SerializerMethodField()
    hidden_fields = serializers.SerializerMethodField()
    tax_details = serializers.SerializerMethodField()
    tds_details = serializers.SerializerMethodField()

    def get_tax_details(self, obj):
        return {
            "tax_amount_percent": serializers.DecimalField(
                max_digits=20, decimal_places=2, allow_null=True
            ).to_representation(obj.tax_amount_percent)
            if obj.tax_amount_percent
            else None,
            "tax_amount_percent_toggle": serializers.BooleanField().to_representation(obj.tax_amount_percent_toggle),
            "tax_amount": serializers.DecimalField(max_digits=20, decimal_places=2).to_representation(obj.gst_amount),
        }

    def get_tds_details(self, obj):
        max_tds_allowed = min(obj.amount + obj.gst_amount - obj.already_paid_amount, obj.amount)
        return {
            "amount": serializers.DecimalField(max_digits=20, decimal_places=2, allow_null=True).to_representation(
                obj.tds_amount
            )
            if obj.tds_amount
            else None,
            "amount_percent": serializers.DecimalField(
                max_digits=20, decimal_places=2, allow_null=True
            ).to_representation(obj.tds_amount_percent)
            if obj.tds_amount_percent
            else None,
            "amount_percent_toggle": serializers.BooleanField().to_representation(obj.tds_amount_percent_toggle),
            "max_tds_allowed": serializers.DecimalField(
                max_digits=20, decimal_places=2, allow_null=True
            ).to_representation(max_tds_allowed),
        }

    def get_readonly_fields(self, obj):
        if self.context.get("invoice_uploaded_by_org_id") != self.context.get("org_id"):
            return [
                "po_id",
                "order_id",
                "invoice_number",
                "invoice_date",
                "file",
                "vendor_id",
                "amount",
                "gst_amount",
                "type_id",
                "tds_amount",
            ]
        return get_invoice_readonly_fields(
            is_payment_request_available=self.context.get("has_payment_request"), invoice_status=obj.status
        )

    def get_hidden_fields(self, obj):
        return get_invoice_hidden_fields(
            is_tds_enabled_on_org_level=self.context.get("is_tds_enabled_on_org_level"),
            tds_amount=obj.tds_amount,
        )

    def get_order_amount(self, obj):
        if obj.order:
            return serializers.DecimalField(max_digits=20, decimal_places=2).to_representation(obj.order_amount)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for field in data["hidden_fields"]:
            data[field] = None

        return data

    class Meta(InvoiceModelSerializer.Meta):
        fields = (
            "id",
            "invoice_number",
            "purchase_order",
            "project",
            "invoice_date",
            "amount",
            "file",
            "file_name",
            "docs",
            "status",
            "actions",
            "type",
            "gst_amount",
            "vendor",
            "order",
            "order_amount",
            "request_id",
            "task_id",
            "request_id",
            "pending_payment_request_amount",
            "approved_payment_request_amount",
            "readonly_fields",
            "hidden_fields",
            "tds_details",
            "tax_details",
        )
        ref_name = "InvoiceDetailsSerializer"


class CreditNoteModelSerializer(BaseModelSerializer):
    class InvoiceSerializer(InvoiceModelSerializer):
        class Meta(InvoiceModelSerializer.Meta):
            fields = ("id", "invoice_number", "vendor")
            ref_name = "InvoiceOutput"

    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "UserOutput"

    invoice = InvoiceSerializer()
    verified_by = UserSerializer()
    created_by = UserSerializer()
    created_at = serializers.DateTimeField()
    remarks = serializers.CharField()
    credit_note_number = serializers.CharField()

    def get_verified_status(self, obj):
        if hasattr(obj, "is_verified"):
            if obj.is_verified:
                return CreditNoteVerificationStatusChoices.VERIFIED

        return CreditNoteVerificationStatusChoices.UNVERIFIED

    class Meta:
        model = InvoiceCreditNote
        fields = "__all__"
        ref_name = "CreditNoteSerializr"


class CreditNoteDetailsSerializer(CreditNoteModelSerializer):
    class PurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
        number = serializers.CharField(source="po_number")

        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = ("id", "number")
            ref_name = "CreditNoteDetailsPurchaseOrderSerializer"

    class OrderSerializer(VendorOrderModelSerializer):
        number = serializers.CharField(source="order_number")

        class Meta(VendorOrderModelSerializer.Meta):
            fields = ("id", "number")
            ref_name = "CreditNoteDetailsOrderSerializer"

    invoice = InvoiceNumberSerializer()
    created_by = UserSerializer()
    verified_by = UserSerializer()
    invoice_file_name = serializers.CharField(source="invoice.file_name", read_only=True)
    invoice_file = CustomFileField(source="invoice.file", read_only=True)
    purchase_order = PurchaseOrderSerializer(source="invoice.vendor_po")
    po_number = serializers.SerializerMethodField()
    order = OrderSerializer(source="invoice.order")
    order_number = serializers.SerializerMethodField()
    invoice_amount = serializers.CharField(source="invoice.amount", read_only=True)

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_po_number(self, obj):
        return {
            f"{obj.invoice.vendor_po.po_number}" if obj.invoice.vendor_po and obj.invoice.vendor_po.po_number else None
        }

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_order_number(self, obj):
        return {
            (
                f"{obj.invoice.project.job_id}/{obj.invoice.order.order_number}"
                if obj.invoice.order and obj.invoice.order.order_number
                else None
            )
        }

    class Meta(CreditNoteModelSerializer.Meta):
        ref_name = "CreditNoteListApiOutputSerializer"
        fields = (
            "id",
            "invoice",
            "invoice_file_name",
            "invoice_file",
            "invoice_amount",
            "purchase_order",
            "po_number",
            "order",
            "order_number",
            "created_by",
            "created_at",
            "amount",
            "gst_amount",
            "debit_note_doc_name",
            "debit_note_doc",
            "credit_note_doc_name",
            "credit_note_doc",
            "is_verified",
            "verified_by",
            "verified_at",
            "remarks",
            "status",
            "credit_note_number",
            "actions",
        )
