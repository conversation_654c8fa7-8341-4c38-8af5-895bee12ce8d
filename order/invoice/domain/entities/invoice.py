import decimal
from dataclasses import dataclass
from typing import Optional

from django.utils import timezone

from common.context_values import BaseContextValues, ProjectContextValueMixin
from common.entities import ObjectStatus
from order.invoice.data.choices import InvoiceSupportingDocumentStatus


@dataclass
class InvoiceDocsCreateUpdateData:
    id: Optional[int]
    name: str
    file: str
    tag_ids: list[int]
    status: InvoiceSupportingDocumentStatus = InvoiceSupportingDocumentStatus.UPLOADED
    object_status: ObjectStatus = ObjectStatus.ADD


@dataclass
class InvoiceDocsCreateUpdateV2Data:
    name: str
    file: str
    tag_ids: list[int]


# @dataclass
# class BaseInvoiceTaxDetails:
#     amount: decimal.Decimal
#     amount_percent: decimal.Decimal = 0
#     amount_percent_toggle: bool = False


@dataclass
class InvoiceTDSDetails:
    amount: Optional[decimal.Decimal] = None
    amount_percent: Optional[decimal.Decimal] = None
    amount_percent_toggle: bool = False


@dataclass
class InvoiceTaxDetails:
    amount: decimal.Decimal
    amount_percent: Optional[decimal.Decimal] = None
    amount_percent_toggle: bool = False


@dataclass(frozen=True)
class InvoiceCreateUpdateData:
    vendor_id: int
    client_id: int
    project_id: int
    order_id: Optional[int]
    po_id: Optional[int]
    invoice_number: str
    invoice_date: timezone.datetime
    amount: decimal.Decimal
    gst_amount: decimal.Decimal
    file: str
    file_name: str
    type_id: int
    docs: list[InvoiceDocsCreateUpdateData]
    vendor_po_id: Optional[int]
    tax_details: Optional[InvoiceTaxDetails] = None  # None required for backward compatibility
    tds_details: Optional[InvoiceTDSDetails] = None  # None required for backward compatibility
    tds_amount: Optional[decimal.Decimal] = None
    tax_amount_percent: Optional[decimal.Decimal] = None
    tds_amount_percent: Optional[decimal.Decimal] = None
    tax_amount_percent_toggle: bool = False
    tds_amount_percent_toggle: bool = False


@dataclass(frozen=True)
class VendorInvoiceCreateUpdateV2Data:
    vendor_id: int
    order_id: Optional[int]
    po_id: Optional[int]
    invoice_number: str
    invoice_date: timezone.datetime
    amount: decimal.Decimal
    gst_amount: decimal.Decimal
    file: str
    file_name: str
    type_id: int
    docs: list[InvoiceDocsCreateUpdateV2Data]


@dataclass(frozen=True)
class ClientInvoiceCreateUpdateV2Data:
    order_id: Optional[int]
    po_id: Optional[int]
    invoice_number: str
    invoice_date: timezone.datetime
    amount: decimal.Decimal
    gst_amount: decimal.Decimal
    file: str
    file_name: str
    type_id: int
    docs: list[InvoiceDocsCreateUpdateV2Data]


@dataclass(frozen=True)
class InvoiceAmountValueProviderData(BaseContextValues, ProjectContextValueMixin): ...


@dataclass(frozen=True)
class InvoiceTypeValueProviderData(BaseContextValues, ProjectContextValueMixin): ...
