import collections
import decimal
from functools import reduce
from operator import or_
from typing import Optional

import structlog
from django.db.models import Q
from django.utils import timezone

from approval_request.domain.callbacks import RequestToResourceBaseCallback
from approval_request.domain.constants import RequestHierarchyCasesActions<PERSON>num, RequestStatusEnum
from approval_request.domain.entities import (
    RequestActionData,
    RequestCreateServiceData,
    ResourceToRequestResetServiceData,
)
from approval_request.domain.services.request import RequestActionService, ResourceToRequestService
from approval_request.interface.factories import ResourceToRequestServiceFactory
from authorization.domain.constants import Permissions
from common.entities import ObjectStatus
from common.events.constants import Events
from common.events.requests import ResourceRequestCancelEventData
from common.events.services import trigger_event
from common.exceptions import BaseValidationError
from common.json_parser.constants import TextEditorJsonConstants
from common.services import AllowedActions, model_update
from controlroom.data.models import InvoiceType
from core.helpers import OrgPermissionHelper
from core.models import User
from core.selectors import fetch_client_org_ids, fetch_vendor_org_ids
from core.services import get_runtime_user_with_token_data
from microcontext.domain.constants import MicroContext
from order.data.models import VendorOrder
from order.domain.status_choices import InvoiceStatus as OrderInvoiceStatus
from order.invoice.data.choices import (
    InvoiceCreditNoteStatus,
    InvoiceEventChoices,
    InvoiceStatus,
    InvoiceSupportingDocumentStatus,
)
from order.invoice.data.models import (
    Invoice,
    InvoiceCreditNote,
    InvoiceRequestMapping,
    OrganizationInvoiceConfig,
    SupportingDocument,
    SupportingDocumentTypeTag,
)
from order.invoice.data.selectors import invoice_fetch_by_id, organization_invoice_config_get
from order.invoice.domain.constants import InvoiceActionEnum, InvoiceSupportingDocumentActionEnum
from order.invoice.domain.entities import InvoiceCreateUpdateData, InvoiceDocsCreateUpdateData
from order.invoice.domain.exceptions import InvoiceConfigException, InvoiceException
from order.invoice.domain.factories import InvoiceActionServiceFactory
from order.invoice.domain.triggers import invoice_uploaded_event_trigger
from payment_request.domain.services import PaymentRequestService
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData
from project.domain.services import check_or_assign_project_permission

logger = structlog.get_logger(__name__)


def invoice_config_order_mandatory_update(user_id: int, org_id: int, is_order_mandatory: bool):
    config: OrganizationInvoiceConfig = organization_invoice_config_get(org_id)
    if config.pk is None:
        config.is_order_mandatory = True
    else:
        config.is_order_mandatory = is_order_mandatory
        config.is_po_mandatory = False
    config.save()


def invoice_config_po_mandatory_update(user_id: int, org_id: int, is_po_mandatory: bool):
    config: OrganizationInvoiceConfig = organization_invoice_config_get(org_id)
    if config.pk is None:
        raise InvoiceConfigException("Organization Invoice Config does not exist")
    if is_po_mandatory and not config.is_order_mandatory:
        raise InvoiceConfigException("Invalid Configuration: is_order_mandatory must be True")
    config.is_po_mandatory = is_po_mandatory
    config.save()


def invoice_config_tds_update(org_id: int, is_tds_enabled: bool):
    config: OrganizationInvoiceConfig = organization_invoice_config_get(org_id)
    if config.pk is None:
        raise InvoiceConfigException("Organization Invoice Config does not exist")
    config.is_tds_enabled = is_tds_enabled
    config.save()


class InvoiceService:
    class InvoiceServiceException(InvoiceException):
        pass

    class InvoiceConfigException(InvoiceServiceException):
        pass

    class GstAmountException(InvoiceConfigException):
        pass

    class TDSAmountException(InvoiceConfigException):
        pass

    class InvoiceNotFoundException(InvoiceConfigException):
        pass

    class InvoiceNumberExistsException(InvoiceServiceException):
        pass

    class InvoiceCreateException(InvoiceServiceException):
        pass

    class InvoiceUpdateException(InvoiceServiceException):
        pass

    class InvoiceUpdatePermissionException(InvoiceServiceException):
        pass

    class InvoiceAmountException(InvoiceServiceException):
        pass

    @classmethod
    def validate_config(cls, data: InvoiceCreateUpdateData):
        config: OrganizationInvoiceConfig = organization_invoice_config_get(org_id=data.client_id)
        if config.is_order_mandatory is True and data.project_id is None and data.order_id is None:
            raise cls.InvoiceConfigException("Project and Order is mandatory")
        if config.is_po_mandatory is True and data.project_id is None and data.order_id is None and data.po_id is None:
            raise cls.InvoiceConfigException("Project, Order and PO is mandatory")

    @classmethod
    def validate_gst_amount(cls, data: InvoiceCreateUpdateData, max_tax_percent: float = float(28)):
        if data.gst_amount > (data.amount * decimal.Decimal(max_tax_percent) / 100):
            raise cls.GstAmountException("Tax amount must be between 0 to 28 percent")

    @classmethod
    def validate_tds_amount(cls, data: InvoiceCreateUpdateData):
        if (data.tds_amount or 0) > data.amount:
            raise cls.TDSAmountException("TDS amount should not be more than 100 percent of amount")

    @classmethod
    def validate_invoice_number(cls, client_id: int, vendor_id: int, invoice_number: str, org_id: int):
        message = "Invoice number already exists for this vendor."
        if org_id == vendor_id:
            message = "Invoice number already exists for this client."
        if (
            Invoice.objects.available()
            .filter(client_id=client_id, vendor_id=vendor_id, invoice_number__iexact=invoice_number)
            .exclude(status__in=[InvoiceStatus.CANCELLED, InvoiceStatus.REJECTED])
            .exists()
        ):
            raise cls.InvoiceNumberExistsException(message)

    @classmethod
    def validate_percentage_and_amount(
        cls, amount: decimal.Decimal, amount_percent: decimal.Decimal, final_amount: decimal.Decimal
    ):
        if round((amount * amount_percent) / 100, 2) != final_amount:
            raise cls.InvoiceAmountException("Invalid amount and percentage")

    def create(self, data: InvoiceCreateUpdateData, user_id: int, org_id: int) -> int:
        logger.info("Invoice creation started")
        project_config = ProjectCountryConfigCache.get(instance_id=data.project_id)
        try:
            self.validate_config(data=data)
        except self.InvoiceConfigException as e:
            logger.info("Invoice create failed", data=data, error=e)
            raise self.InvoiceCreateException(e.message)
        try:
            self.validate_gst_amount(data, max_tax_percent=project_config.tax_type.max_slab_percent)
        except self.GstAmountException as e:
            logger.info("Invoice create failed", data=data, error=e)
            raise self.InvoiceCreateException(e.message)

        try:
            self.validate_tds_amount(data)
        except self.TDSAmountException as e:
            logger.info("Invoice create failed", data=data, error=e)
            raise self.InvoiceCreateException(e.message)

        if data.tax_amount_percent_toggle:
            self.validate_percentage_and_amount(
                amount=data.amount,
                amount_percent=data.tax_amount_percent,
                final_amount=data.gst_amount,
            )

        if data.tds_amount_percent_toggle:
            self.validate_percentage_and_amount(
                amount=data.amount or 0,
                amount_percent=data.tds_amount_percent or 0,
                final_amount=data.tds_amount or 0,
            )

        try:
            self.validate_invoice_number(
                client_id=data.client_id,
                vendor_id=data.vendor_id,
                invoice_number=data.invoice_number,
                org_id=org_id,
            )
        except self.InvoiceNumberExistsException as e:
            logger.info("Invoice creation failed.", data=data, error=e)
            raise self.InvoiceCreateException(e.message)
        if data.vendor_id != org_id and data.client_id != org_id:
            raise self.InvoiceCreateException("Invalid vendor_id or client_id")
        if data.vendor_id == org_id and data.client_id not in fetch_client_org_ids(org_id=org_id):
            raise self.InvoiceCreateException("Invalid client_id")
        if data.client_id == org_id and data.vendor_id not in fetch_vendor_org_ids(org_id=org_id):
            raise self.InvoiceCreateException("Invalid vendor_id")
        invoice = Invoice(
            vendor_po_id=data.po_id,
            invoice_number=data.invoice_number,
            invoice_date=data.invoice_date,
            amount=data.amount,
            gst_amount=data.gst_amount,
            file=data.file,
            file_name=data.file_name,
            status=InvoiceStatus.PENDING.value,
            type_id=data.type_id,
            project_id=data.project_id,
            client_id=data.client_id,
            vendor_id=data.vendor_id,
            order_id=data.order_id,
            uploaded_by_id=user_id,
            tax_amount_percent=data.tax_amount_percent if data.tax_amount_percent_toggle else None,
            tax_amount_percent_toggle=data.tax_amount_percent_toggle,
            tds_amount_percent=data.tds_amount_percent if data.tds_amount_percent_toggle else None,
            tds_amount_percent_toggle=data.tds_amount_percent_toggle,
            tds_amount=data.tds_amount,
        )
        invoice.clean()
        invoice.save()
        self._update_supporting_docs(invoice=invoice, docs_data=data.docs, user_id=user_id)
        logger.info("Invoice created successfully")

        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=data.project_id)
        logger.info("Invoice request creation started")
        request_data = RequestCreateServiceData(
            created_by_id=user_id,
            event=InvoiceEventChoices.SUBMIT,
            context=MicroContext.INVOICE.value,
            context_id=invoice.pk,
            org_id=data.client_id,
            project_id=data.project_id,
            order_id=data.order_id,
            description=[
                {
                    "text": f"{project_config.currency.symbol} {invoice.amount}",
                    "text_type": TextEditorJsonConstants.NORMAL,
                    "type": "text",
                }
            ],
        )
        resource_request_service = ResourceToRequestServiceFactory.get_service(
            self, data=request_data, resource_callback=RequestToInvoiceCallback(resource_id=invoice.pk, user_id=user_id)
        )
        try:
            logger.info("Invoice request creation input", RequestCreateServiceData=request_data.__dict__)
            resource_request_service.create_request(
                data=request_data,
                on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
            )
        except ResourceToRequestService.CreateThroughAppTokenException as e:
            raise self.InvoiceServiceException(e.message) from e
        except ResourceToRequestService.CreateException as e:
            raise self.InvoiceServiceException("Invoice request not created") from e
        logger.info("Invoice request created", request_data=request_data)
        check_or_assign_project_permission(
            project_id=data.project_id,
            to_assign_org=data.vendor_id,
            assigned_by_org=data.client_id,
            created_by_id=user_id,
        )
        invoice_uploaded_event_trigger(project_id=data.project_id, order_id=data.order_id, invoice=invoice)
        return invoice.pk

    def _update_invoice(
        self, invoice_id: int, data: InvoiceCreateUpdateData, user_id: int, org_id: int, is_admin: bool
    ):
        """
        Get invoice update data
        check invoice config, Raises InvoiceUpdateException
        check gst amount is less then 28%, Raises InvoiceUpdateException
        fetch invoice and if not found, Raise InvoiceNotFoundException
        check credit note total amount is must not be greater then invoice updated amount, Raises InvoiceUpdateException
        update invoice
        update invoice supporting docs
        """
        logger.info("Invoice updation started")
        project_config = ProjectCountryConfigCache.get(instance_id=data.project_id)
        try:
            self.validate_config(data=data)
        except self.InvoiceConfigException as e:
            logger.info("Invoice update failed", data=data, error=e)
            raise self.InvoiceUpdateException(e.message)
        try:
            self.validate_gst_amount(data, max_tax_percent=project_config.tax_type.max_slab_percent)
        except self.GstAmountException as e:
            logger.info("Invoice update failed", data=data, error=e)
            raise self.InvoiceUpdateException(e.message)
        try:
            self.validate_tds_amount(data)
        except self.TDSAmountException as e:
            logger.info("Invoice update failed", data=data, error=e)
            raise self.InvoiceUpdateException(e.message)

        if data.tax_amount_percent_toggle:
            self.validate_percentage_and_amount(
                amount=data.amount,
                amount_percent=data.tax_amount_percent,
                final_amount=data.gst_amount,
            )

        if data.tds_amount_percent_toggle:
            self.validate_percentage_and_amount(
                amount=data.amount,
                amount_percent=data.tds_amount_percent,
                final_amount=data.tds_amount,
            )

        if data.vendor_id != org_id and data.client_id != org_id:
            raise self.InvoiceUpdateException("Invalid vendor_id or client_id")
        if data.vendor_id == org_id and data.client_id not in fetch_client_org_ids(org_id=org_id):
            raise self.InvoiceUpdateException("Invalid client_id")
        if data.client_id == org_id and data.vendor_id not in fetch_vendor_org_ids(org_id=org_id):
            raise self.InvoiceUpdateException("Invalid vendor_id")
        invoice: Invoice = (
            Invoice.objects.available()
            .filter(id=invoice_id)
            .select_related("type")
            .prefetch_related("supporting_documents", "supporting_documents__tags")
            .annotate_credit_total_amount()
            .annotate_request_id()
            .annotate_request_data()
            .annotate_total_payment_request_amount()
            .annotate_available_payment_request_count()
            .annotate_already_paid_amount()
        ).first()
        if invoice is None:
            logger.info("Invoice not found", data=data)
            return self.InvoiceNotFoundException("Invoice not found")

        if data.amount + data.gst_amount < invoice.payment_request_amount:
            logger.info("Payment request amount is greater than invoice amount.")
            raise self.InvoiceUpdateException("Payment request amount is greater than invoice amount.")

        if data.amount + data.gst_amount - (data.tds_amount or 0) < invoice.already_paid_amount:
            logger.info("TDS amount is less than already paid amount.")
            raise self.InvoiceUpdateException("TDS amount is less than already paid amount.")

        action_service = InvoiceActionServiceFactory.get_service(
            invoice=invoice, user_id=user_id, org_id=org_id, is_admin=is_admin
        )
        if not action_service.can_edit():
            raise self.InvoiceUpdatePermissionException("Invoice can be updated by creator only.")
        if data.amount + data.gst_amount < invoice.credit_total_amount:
            logger.info("Invoice amount is less then credit note amount.")
            raise self.InvoiceUpdateException("Invoice Amount Cannot be less than credit note amount.", data=data)

        invoice_type = InvoiceType.objects.filter(id=data.type_id).first()
        if invoice.type.is_active is True and invoice_type.is_active is False:
            logger.error("Invoice can't be updated from active to inactive type.", data=data)
            raise self.InvoiceUpdateException("Invoice can't be updated from active to inactive type.")

        fields = [
            "vendor_po_id",
            "order_id",
            "invoice_number",
            "invoice_date",
            "amount",
            "gst_amount",
            "file",
            "file_name",
            "vendor_id",
            "type_id",
            "tds_amount",
            "tax_amount_percent",
            "tax_amount_percent_toggle",
            "tds_amount_percent",
            "tds_amount_percent_toggle",
        ]
        invoice, is_updated, updated_fields = model_update(
            instance=invoice, data=data, fields=fields, updated_by_id=user_id, save=False, clean=False
        )
        readonly_fields = set()
        if invoice.status in [InvoiceStatus.APPROVED, InvoiceStatus.CANCELLED]:
            readonly_fields = readonly_fields.union(
                {
                    "po_id",
                    "order_id",
                    "invoice_number",
                    "invoice_date",
                    "file",
                    "vendor_id",
                    "amount",
                    "gst_amount",
                    "type_id",
                }
            )
        if invoice.status in [InvoiceStatus.REJECTED, InvoiceStatus.CANCELLED]:
            readonly_fields = readonly_fields.union({"tds_amount"})
        if invoice.payment_request_count > 0:
            readonly_fields = readonly_fields.union(
                {"po_id", "order_id", "invoice_number", "invoice_date", "file", "vendor_id"}
            )
        readonly_fields = list(readonly_fields)

        if set(readonly_fields).intersection(set(updated_fields)):
            raise self.InvoiceUpdateException("Invoice Data can't be updated because it has valid payment requests.")

        if is_updated:
            if "invoice_number" in updated_fields or "client_id" in updated_fields or "vendor_id" in updated_fields:
                try:
                    self.validate_invoice_number(
                        client_id=data.client_id,
                        vendor_id=data.vendor_id,
                        invoice_number=data.invoice_number,
                        org_id=org_id,
                    )
                except self.InvoiceNumberExistsException as e:
                    logger.info("Invoice updation failed.", data=data, error=e)
                    raise self.InvoiceUpdateException(e.message)
            invoice.clean()
            invoice.save(update_fields=updated_fields)

        logger.info("Invoice updated successfully")
        return invoice

    def _create_invoice_request(
        self, invoice: Invoice, invoice_type: InvoiceType, user_id: int, data: InvoiceCreateUpdateData
    ):
        if not invoice_type.is_active:
            return
        if invoice.status == InvoiceStatus.ATTACHED.value and invoice.request_data:
            return
        if invoice.status == InvoiceStatus.APPROVED.value:
            """DON'T RESET REQUEST AFTER INVOICE IS APPROVED"""
            return
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=data.project_id)
        if invoice.status == InvoiceStatus.ATTACHED.value:
            logger.info("Invoice request creation started")
            request_data = RequestCreateServiceData(
                created_by_id=user_id,  # request creator is user who updates invoice after hierarchy activation
                event=InvoiceEventChoices.UPDATE,
                context=MicroContext.INVOICE.value,
                context_id=invoice.pk,
                org_id=data.client_id,
                project_id=data.project_id,
                order_id=data.order_id,
                description=[
                    {
                        "text": f"{project_config.currency.symbol} {invoice.amount}",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ],
            )
            resource_request_service = ResourceToRequestServiceFactory.get_service(
                self,
                data=request_data,
                resource_callback=RequestToInvoiceCallback(resource_id=invoice.pk, user_id=user_id),
            )
            try:
                logger.info("Invoice request creation input", RequestCreateServiceData=request_data.__dict__)
                resource_request_service.create_request(
                    data=request_data,
                    on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                    on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                )
            except ResourceToRequestService.CreateException as e:
                raise self.InvoiceServiceException("Invoice request not created") from e
            logger.info("Invoice request created", request_data=request_data)
        else:
            logger.info("Invoice request reset started")
            request_data = ResourceToRequestResetServiceData(
                created_by_id=invoice.request_data.get("created_by_id"),
                event=InvoiceEventChoices.UPDATE,
                context=MicroContext.INVOICE.value,
                context_id=invoice.pk,
                org_id=invoice.client_id,
                request_id=invoice.request_id,
                project_id=data.project_id,
                order_id=data.order_id,
                description=[
                    {
                        "text": f"{project_config.currency.symbol} {invoice.amount}",
                        "text_type": TextEditorJsonConstants.NORMAL,
                        "type": "text",
                    },
                ],
                user_id=user_id,
            )
            resource_request_service = ResourceToRequestServiceFactory.get_service(
                self,
                data=request_data,
                resource_callback=RequestToInvoiceCallback(resource_id=invoice.pk, user_id=user_id),
            )
            try:
                logger.info("Invoice request reset input", ResourceToRequestResetServiceData=request_data.__dict__)
                resource_request_service.reset_request(
                    data=request_data,
                    on_hierarchy_not_found=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                    on_hierarchy_inactive=RequestHierarchyCasesActionsEnum.DO_NOTHING,
                )
            except ResourceToRequestService.UpdateException as e:
                raise self.InvoiceServiceException("Invoice request not resetted") from e
            logger.info("Invoice request resetted", request_id=invoice.request_id, request_data=request_data)

    # TODO: Deprecate after Release-28-march-2025
    def update(self, invoice_id: int, data: InvoiceCreateUpdateData, user_id: int, org_id: int, is_admin: bool):
        invoice = self._update_invoice(
            invoice_id=invoice_id, data=data, user_id=user_id, org_id=org_id, is_admin=is_admin
        )
        self._update_supporting_docs(invoice=invoice, docs_data=data.docs, user_id=user_id)
        self._create_invoice_request(
            invoice=invoice,
            invoice_type=invoice.type,
            user_id=user_id,
            data=data,
        )

    def update_v2(self, invoice_id: int, data: InvoiceCreateUpdateData, user_id: int, org_id: int, is_admin: bool):
        invoice = self._update_invoice(
            invoice_id=invoice_id, data=data, user_id=user_id, org_id=org_id, is_admin=is_admin
        )
        self._update_supporting_docs_v2(invoice=invoice, docs_data=data.docs, user_id=user_id)
        self._create_invoice_request(invoice=invoice, invoice_type=invoice.type, user_id=user_id, data=data)

    def _update_supporting_docs(self, invoice: Invoice, docs_data: list[InvoiceDocsCreateUpdateData], user_id: int):
        current_time = timezone.now()
        current_doc_ids = [doc.id for doc in invoice.supporting_documents.all()]
        updated_docs_data = []
        updated_doc_objs_mapping: dict[int, SupportingDocument] = {}
        updated_doc_ids = []
        new_docs_data = []
        for doc_data in docs_data:
            if doc_data.id:
                updated_docs_data.append(doc_data)
                updated_doc_ids.append(doc_data.id)
            else:
                new_docs_data.append(doc_data)
        deleted_doc_ids = list(set(current_doc_ids) - set([doc.id for doc in updated_docs_data]))
        # delete old documents
        SupportingDocument.objects.filter(id__in=deleted_doc_ids).update(deleted_by_id=user_id, deleted_at=current_time)
        logger.info("Deleted old documents")
        # create new documents
        self._create_supporting_docs(
            invoice_id=invoice.pk, docs_data=new_docs_data, user_id=user_id, invoice_status=invoice.status
        )
        logger.info("Created new documents")
        # update existing documents
        for doc in invoice.supporting_documents.available():
            if doc.id in updated_doc_ids:
                updated_doc_objs_mapping[doc.id] = doc
        for doc_data in updated_docs_data:
            doc = updated_doc_objs_mapping[doc_data.id]
            doc.id = doc_data.id
            doc.file = doc_data.file
            doc.file_name = doc_data.name
            doc.updated_at = current_time
            doc.updated_by_id = user_id
            doc.tags.set(doc_data.tag_ids)
        logger.info("Updated documents")

    def _update_supporting_docs_v2(self, invoice: Invoice, docs_data: list[InvoiceDocsCreateUpdateData], user_id: int):
        """
        Updates supporting documents and their tag mappings for a given invoice.
        """
        new_docs, updated_docs, deleted_docs, doc_data_to_tag_ids_mapping = self._process_documents(
            docs_data, invoice, user_id
        )

        # Bulk update existing documents
        self._bulk_update_docs(updated_docs, deleted_docs)

        # Create new documents
        self._create_supporting_docs(invoice.pk, new_docs, user_id, invoice.status)

        # Update document tags
        self._update_document_tags(invoice, doc_data_to_tag_ids_mapping)

    def _process_documents(self, docs_data, invoice, user_id):
        """
        Categorizes documents into new, updated, and deleted based on their object_status.
        """
        new_docs = []
        updated_docs = []
        deleted_docs = []
        doc_data_to_tag_ids_mapping = collections.defaultdict(list)

        for doc_data in docs_data:
            if doc_data.object_status == ObjectStatus.ADD:
                new_docs.append(doc_data)
            elif doc_data.object_status == ObjectStatus.UPDATE:
                doc_data_to_tag_ids_mapping[doc_data.id] = doc_data.tag_ids
                updated_docs.append(
                    SupportingDocument(
                        id=doc_data.id,
                        file=doc_data.file,
                        file_name=doc_data.name,
                        status=doc_data.status,
                        invoice_status=invoice.status,
                        updated_at=timezone.now(),
                        updated_by_id=user_id,
                    )
                )
            else:  # ObjectStatus.DELETE
                deleted_docs.append(
                    SupportingDocument(
                        id=doc_data.id,
                        file=doc_data.file,
                        file_name=doc_data.name,
                        status=doc_data.status,
                        invoice_status=invoice.status,
                        deleted_at=timezone.now(),
                        deleted_by_id=user_id,
                    )
                )

        return new_docs, updated_docs, deleted_docs, doc_data_to_tag_ids_mapping

    def _bulk_update_docs(self, updated_docs, deleted_docs):
        """
        Bulk updates and marks documents as deleted.
        """
        if updated_docs:
            SupportingDocument.objects.bulk_update(
                updated_docs, fields=["file", "file_name", "status", "updated_at", "updated_by_id"]
            )

        if deleted_docs:
            SupportingDocument.objects.bulk_update(
                deleted_docs, fields=["file", "file_name", "status", "deleted_at", "deleted_by_id"]
            )

    def _update_document_tags(self, invoice, doc_data_to_tag_ids_mapping):
        """
        Updates tag associations for documents.
        """
        new_tag_mappings, tags_to_delete = [], []

        for doc in invoice.supporting_documents.available():
            if doc.id not in doc_data_to_tag_ids_mapping:
                continue  # Skip docs that are not being updated

            input_tag_ids = set(doc_data_to_tag_ids_mapping[doc.id])
            saved_tag_ids = set(doc.tags.values_list("id", flat=True))

            # Find new tags to add
            new_tag_mappings.extend(
                [
                    SupportingDocumentTypeTag(supporting_document_id=doc.id, supporting_document_type_id=tag_id)
                    for tag_id in input_tag_ids - saved_tag_ids
                ]
            )

            # Find old tags to remove
            tags_to_delete.extend([(doc.id, tag_id) for tag_id in saved_tag_ids - input_tag_ids])

        self._bulk_update_tags(new_tag_mappings, tags_to_delete)

    def _bulk_update_tags(self, new_tag_mappings, tags_to_delete):
        """
        Bulk creates new tag mappings and removes deleted ones.
        """
        if new_tag_mappings:
            SupportingDocumentTypeTag.objects.bulk_create(new_tag_mappings)

        if tags_to_delete:
            query = reduce(
                or_,
                (
                    Q(supporting_document_id=doc_id, supporting_document_type_id=tag_id)
                    for doc_id, tag_id in tags_to_delete
                ),
            )
            SupportingDocumentTypeTag.objects.filter(query).delete()

    def _create_supporting_docs(
        self,
        invoice_id: int,
        docs_data: list[InvoiceDocsCreateUpdateData],
        user_id: int,
        invoice_status: str,
    ):
        logger.info("Invoice document creation started")
        docs: list[SupportingDocument] = []
        for doc_data in docs_data:
            doc = SupportingDocument(
                invoice_id=invoice_id,
                file=doc_data.file,
                file_name=doc_data.name,
                created_by_id=user_id,
                invoice_status=invoice_status,
                status=InvoiceSupportingDocumentStatus.UPLOADED.value,
            )
            docs.append(doc)
        docs: list[SupportingDocument] = SupportingDocument.objects.bulk_create(docs)
        doc_type_tags: list[SupportingDocumentTypeTag] = []
        for doc, doc_data in zip(docs, docs_data):
            for tag_id in doc_data.tag_ids:
                doc_type_tags.append(
                    SupportingDocumentTypeTag(
                        supporting_document_id=doc.pk,
                        supporting_document_type_id=tag_id,
                    )
                )
        SupportingDocumentTypeTag.objects.bulk_create(doc_type_tags)

    def delete(self, invoice_id: int, user_id: int):
        Invoice.objects.filter(id=invoice_id).soft_delete(user_id=user_id)
        InvoiceCreditNote.objects.filter(invoice_id=invoice_id).soft_delete(user_id=user_id)

    def cancel(self, invoice_id: int, user_id: int):
        Invoice.objects.filter(id=invoice_id).update(
            status=InvoiceStatus.CANCELLED.value, updated_at=timezone.now(), updated_by_id=user_id
        )
        InvoiceCreditNote.objects.filter(invoice_id=invoice_id).update(
            status=InvoiceCreditNoteStatus.CANCELLED, cancelled_by_id=user_id, cancelled_at=timezone.now()
        )
        SupportingDocument.objects.filter(invoice_id=invoice_id).update(
            status=InvoiceSupportingDocumentStatus.CANCELLED, updated_at=timezone.now(), updated_by_id=user_id
        )

    def cancel_orders_pending_hold_invoices(self, order_id: int, user_id: int):
        invoice_ids = Invoice.objects.filter(
            order_id=order_id, status__in=[InvoiceStatus.PENDING.value, InvoiceStatus.HOLD.value]
        ).values_list("id", flat=True)
        InvoiceCreditNote.objects.filter(invoice_id__in=invoice_ids).update(
            status=InvoiceCreditNoteStatus.CANCELLED, cancelled_by_id=user_id, cancelled_at=timezone.now()
        )
        for invoice_id in invoice_ids:
            trigger_event(
                event=Events.RESOURCE_CANCEL,
                event_data=ResourceRequestCancelEventData(
                    context=MicroContext.INVOICE.value, context_id=invoice_id, user_id=user_id
                ),
            )
        PaymentRequestService(user=User.objects.get(id=user_id)).cancel_on_parent_resource_cancellation(
            invoice_ids=list(invoice_ids)
        )

        Invoice.objects.filter(id__in=invoice_ids).update(
            status=InvoiceStatus.CANCELLED.value, updated_at=timezone.now(), updated_by_id=user_id
        )

    @staticmethod
    def update_invoice_status(invoice_id: int, status: InvoiceStatus):
        logger.info("Invoice status updation started")
        invoice = invoice_fetch_by_id(invoice_id=invoice_id)
        if not invoice:
            logger.info("Invoice not found", invoice_id=invoice_id)
            raise InvoiceService.InvoiceNotFoundException("Invoice not found.")
        if invoice.status == InvoiceStatus.CANCELLED or invoice.status == InvoiceStatus.REJECTED:
            raise InvoiceService.InvoiceServiceException("Invoice request is already in final state.")
        if status == InvoiceStatus.APPROVED and invoice.status not in [
            InvoiceStatus.HOLD,
            InvoiceStatus.REQUEST_MISCONFIGURED,
            InvoiceStatus.PENDING,
        ]:
            raise InvoiceService.InvoiceServiceException("Invoice request cannot be approved.")
        if status == InvoiceStatus.HOLD and invoice.status != InvoiceStatus.PENDING:
            raise InvoiceService.InvoiceServiceException("Expense request cannot be put on hold.")
        if status == InvoiceStatus.REJECTED and (
            invoice.status
            not in [
                InvoiceStatus.HOLD,
                InvoiceStatus.REQUEST_MISCONFIGURED,
                InvoiceStatus.PENDING,
            ]
        ):
            raise InvoiceService.InvoiceServiceException("Expense request cannot be rejected.")

        invoice.status = status
        invoice.save(update_fields=["status"])
        logger.info("Invoice status updated")


class RequestToInvoiceCallback(RequestToResourceBaseCallback):
    class RequestToInvoiceCallbackException(BaseValidationError):
        pass

    def on_create(self, request_id: int, task_id: int, event: str, comment_id: int):
        try:
            InvoiceRequestMapping.objects.create(
                request_id=request_id,
                resource_id=self.resource_id,
                event=event,
                task_id=task_id,
                created_by_id=self.user_id,
                comment_id=comment_id,
            )
        except InvoiceRequestMapping.ModelDataIntegrityException as e:
            raise self.RequestToInvoiceCallbackException(e) from e
        InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.PENDING.value)

    def on_request_not_required(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.ATTACHED.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_cancel(self):
        try:
            InvoiceService().cancel(invoice_id=self.resource_id, user_id=self.user_id)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_reset(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.PENDING.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_approve(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.APPROVED.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_pending(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.PENDING.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_hold(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.HOLD.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e

    def on_reject(self):
        try:
            InvoiceService.update_invoice_status(invoice_id=self.resource_id, status=InvoiceStatus.REJECTED.value)
        except InvoiceService.InvoiceServiceException as e:
            raise self.RequestToInvoiceCallbackException(e) from e


class InvoiceRequestActionService(RequestActionService):
    def __init__(self, data: RequestActionData, is_admin: bool, user_id: int, org_id: int):
        super().__init__(data=data, is_admin=is_admin, user_id=user_id)
        self.org_id = org_id

    def can_cancel(self):
        if self.data.status in [
            RequestStatusEnum.APPROVED.value,
            RequestStatusEnum.REJECTED.value,
            RequestStatusEnum.CANCELLED.value,
        ]:
            return False
        return self.org_id == self.data.creator_org_id

    def can_cancel_as_admin(self):
        if self.data.status in [RequestStatusEnum.CANCELLED.value, RequestStatusEnum.REJECTED.value]:
            return False
        if self.data.org_id == self.org_id and self.is_admin:
            user = get_runtime_user_with_token_data(user_id=self.user_id, org_id=self.data.org_id, is_admin=True)
            return OrgPermissionHelper.has_permission(
                user=user, permission=Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS.value
            )
        return False

    def get_user_actions(self):
        actions = []
        if self.org_id == self.data.org_id:
            actions = super().get_user_actions()
        if self.can_cancel_as_admin():
            actions.append(self.action_choices.CANCEL_AS_ADMIN.value)
        return actions


class InvoiceActionService(AllowedActions):
    invoice: Optional[Invoice] = None
    is_admin: bool = False
    action_choices = InvoiceActionEnum
    request_action_service = InvoiceRequestActionService

    def __init__(
        self,
        user_id: int,
        org_id: int,
        invoice: Invoice,
        request_action_service: InvoiceRequestActionService,
        is_admin: bool = None,
    ) -> None:
        self.user_id = user_id
        self.org_id = org_id
        self.invoice = invoice
        self.is_admin = is_admin
        self.request_action_service = request_action_service

    def can_edit(self):
        if self.invoice.status in [
            InvoiceStatus.REJECTED.value,
            InvoiceStatus.CANCELLED.value,
        ]:
            return False
        return self.invoice.uploaded_by.org_id == self.org_id

    def can_delete(self):
        if self.invoice.uploaded_by.org_id != self.org_id:
            return False
        if self.invoice.status != InvoiceStatus.DELETED.value and not self.is_request_created():
            return True
        return False

    def can_cancel(self):
        if not self.is_request_created():
            return False
        if self.invoice.status in [InvoiceStatus.ATTACHED.value, InvoiceStatus.CANCELLED.value]:
            return False
        else:
            return self.request_action_service.can_cancel()

    def can_resource_cancel(self):
        if not self.is_request_created():
            if self.invoice.status == InvoiceStatus.CANCELLED.value:
                return False
            return self.invoice.uploaded_by.org_id == self.org_id
        return False

    def is_request_created(self):
        return True if self.invoice.request_data else False

    def get_user_actions(self):
        logger.info("Getting user actions", user_id=self.user_id, invoice_id=self.invoice.pk)
        action_list = []
        if self.can_edit():
            action_list.append(self.action_choices.EDIT.value)
        if self.can_delete():
            action_list.append(self.action_choices.DELETE.value)
        if self.can_cancel():
            action_list.append(self.action_choices.CANCEL.value)
        if self.can_resource_cancel():
            action_list.append(self.action_choices.RESOURCE_CANCEL.value)
        if self.invoice.status == InvoiceStatus.ATTACHED.value and self.is_request_created():
            return action_list
        if self.request_action_service:
            action_list.extend(self.request_action_service.get_user_actions())
        return action_list

    def get_supporting_docs_actions(self, current_invoice_status: str, doc_invoice_status: str):
        actions_list = [InvoiceSupportingDocumentActionEnum.DOWNLOAD.value]
        if current_invoice_status == InvoiceStatus.APPROVED.value:
            if doc_invoice_status not in [InvoiceStatus.APPROVED.value, InvoiceStatus.CANCELLED.value]:  # noqa
                actions_list.append(InvoiceSupportingDocumentActionEnum.CANCEL.value)
            else:
                actions_list.append(InvoiceSupportingDocumentActionEnum.EDIT.value)
                actions_list.append(InvoiceSupportingDocumentActionEnum.DELETE.value)
        else:
            actions_list.append(InvoiceSupportingDocumentActionEnum.EDIT.value)
            actions_list.append(InvoiceSupportingDocumentActionEnum.DELETE.value)

        return actions_list


def invoice_request_secondary_comment_update(*, invoice_id: int, request_id: int, comment_id: int):
    InvoiceRequestMapping.objects.filter(resource_id=invoice_id, request_id=request_id).update(
        secondary_comment_id=comment_id
    )


def vendor_order_invoice_status_update(*, vendor_order_id: int, user_id: int) -> int:
    order = VendorOrder.objects.filter(id=vendor_order_id)
    order.update(invoice_status=OrderInvoiceStatus.ALL_UPLOADED, updated_by_id=user_id, updated_at=timezone.now())
    return order.first()


def get_invoice_readonly_fields(is_payment_request_available: bool, invoice_status: str):
    readonly_fields = set()
    if invoice_status in [InvoiceStatus.APPROVED, InvoiceStatus.CANCELLED]:
        readonly_fields = readonly_fields.union(
            {
                "po_id",
                "order_id",
                "invoice_number",
                "invoice_date",
                "file",
                "vendor_id",
                "amount",
                "gst_amount",
                "type_id",
            }
        )
    if invoice_status in [InvoiceStatus.REJECTED, InvoiceStatus.CANCELLED]:
        readonly_fields = readonly_fields.union({"tds_amount"})
    if is_payment_request_available:
        readonly_fields = readonly_fields.union(
            {"po_id", "order_id", "invoice_number", "invoice_date", "file", "vendor_id"}
        )
    return list(readonly_fields)


def get_invoice_hidden_fields(is_tds_enabled_on_org_level: bool, tds_amount: Optional[decimal.Decimal]):
    hidden_fields = []
    if not is_tds_enabled_on_org_level:
        if tds_amount is None:
            hidden_fields.append("tds_amount")
    return hidden_fields
