import decimal

import structlog
from django.utils import timezone

from common.services import model_update
from order.invoice.data.choices import InvoiceCreditNoteStatus, InvoiceStatus
from order.invoice.data.models import (
    Invoice,
    InvoiceCreditNote,
)
from order.invoice.domain.entities.credit_note import CreditNoteBaseData, CreditNoteData
from order.invoice.domain.exceptions import CreditNoteException
from project.domain.caches import ProjectCountryConfigCache
from project.domain.entities import ProjectCountryConfigData

logger = structlog.get_logger(__name__)


class CreditNoteService:
    class CreditNoteServiceException(CreditNoteException):
        pass

    class CreditNoteCreateException(CreditNoteServiceException):
        pass

    class GSTValidationException(CreditNoteServiceException):
        pass

    class AmountException(CreditNoteServiceException):
        pass

    class CreditNoteUpdateException(CreditNoteServiceException):
        pass

    def validate_gst_amount(self, obj, max_tax_percentage: float = float(28)):
        if not obj.gst_amount <= (obj.amount * decimal.Decimal(max_tax_percentage) / 100):
            raise self.GSTValidationException("invalid Tax amount")

    def validate_update_amount(self, obj: InvoiceCreditNote, old_amount: float, old_gst_amount: float):
        invoice_amount_data: Invoice = (
            Invoice.objects.available()
            .annotate_credit_note_amount()
            .annotate_credit_note_gst_amount()
            .filter(id=obj.invoice_id)
            .values("credit_note_amount", "credit_note_gst_amount", "amount", "gst_amount", "id")
        ).first()

        total_amount_invoice = invoice_amount_data["amount"] + invoice_amount_data["gst_amount"]
        total_credit_notes_amount = (
            invoice_amount_data["credit_note_amount"] + invoice_amount_data["credit_note_gst_amount"]
        )
        total_credit_notes_amount -= old_amount
        total_credit_notes_amount -= old_gst_amount

        if not (obj.amount + obj.gst_amount) <= (total_amount_invoice - total_credit_notes_amount):
            raise self.AmountException("Total Credit Notes amount cannot be greater than Invoice amount.")

    def validate_amount(self, obj: InvoiceCreditNote) -> None:
        invoice_amount_data: dict = (
            Invoice.objects.available()
            .annotate_credit_note_amount()
            .annotate_credit_note_gst_amount()
            .filter(id=obj.invoice_id)
            .values("credit_note_amount", "credit_note_gst_amount", "amount", "gst_amount")
        ).first()
        if invoice_amount_data is None:
            raise self.AmountException("Invoice amount data is not available")
        total_amount_invoice = invoice_amount_data["amount"] + invoice_amount_data["gst_amount"]
        total_credit_notes_amount = (
            invoice_amount_data["credit_note_amount"] + invoice_amount_data["credit_note_gst_amount"]
        )

        if not (obj.amount + obj.gst_amount) <= (total_amount_invoice - total_credit_notes_amount):
            raise self.AmountException("Total Credit Notes amount cannot be greater than Invoice amount.")

    def data_validate_is_verified(self, obj: InvoiceCreditNote, is_verified: bool, user_id: int):
        if obj.is_verified:
            return obj, False

        if is_verified:
            if obj.credit_note_doc or obj.debit_note_doc:
                obj.is_verified = True
                obj.verified_at = timezone.now()
                obj.verified_by_id = user_id
                return obj, True
            else:
                raise self.CreditNoteCreateException("Credit Note File not available ")
        return obj, False

    def is_invoice_approved_or_attached(self, invoice_id: int) -> bool:
        from order.invoice.data.selectors import invoice_fetch_all

        status = invoice_fetch_all().filter(id=invoice_id).values_list("status", flat=True).first()
        if status in [InvoiceStatus.APPROVED.value, InvoiceStatus.ATTACHED.value]:
            return True
        return False

    def create(self, data: CreditNoteData, user_id: int):
        from order.invoice.data.selectors import invoice_fetch_by_id

        assert data is not None, "Credit Note data is not available"
        invoice: Invoice = invoice_fetch_by_id(invoice_id=data.invoice_id)
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=invoice.project_id)
        try:
            self.validate_gst_amount(data, max_tax_percentage=project_config.tax_type.max_slab_percent)
        except self.GSTValidationException as e:
            logger.info("CreditNote create failed", data=data, error=e)
            raise self.CreditNoteCreateException(e.message)
        try:
            self.validate_amount(data)
        except self.AmountException as e:
            logger.info("CreditNote create failed", data=data, error=e)
            raise self.CreditNoteCreateException(e.message)

        if not self.is_invoice_approved_or_attached(data.invoice_id):
            raise self.CreditNoteCreateException("Invoice is not approved or attached")
        credit_note = InvoiceCreditNote()

        credit_note.invoice_id = data.invoice_id
        credit_note.credit_note_number = data.credit_note_number
        credit_note.amount = data.amount
        credit_note.gst_amount = data.gst_amount
        credit_note.credit_note_doc_name = data.credit_note_doc_name
        credit_note.credit_note_doc = data.credit_note_doc
        credit_note.debit_note_doc_name = data.debit_note_doc_name
        credit_note.debit_note_doc = data.debit_note_doc
        credit_note.remarks = data.remarks
        credit_note.created_at = timezone.now()
        credit_note.created_by_id = user_id
        credit_note, _verified = self.data_validate_is_verified(
            obj=credit_note, is_verified=data.is_verified, user_id=user_id
        )
        self.validate_amount(obj=credit_note)
        self.validate_gst_amount(obj=credit_note, max_tax_percentage=project_config.tax_type.max_slab_percent)
        credit_note.save()
        return credit_note

    def update(self, credit_note_id: int, data: CreditNoteData, user_id: int):
        from order.invoice.data.selectors import credit_note_fetch, invoice_fetch_by_id

        invoice: Invoice = invoice_fetch_by_id(invoice_id=data.invoice_id)
        project_config: ProjectCountryConfigData = ProjectCountryConfigCache.get(instance_id=invoice.project_id)
        try:
            self.validate_gst_amount(data, max_tax_percentage=project_config.tax_type.max_slab_percent)
        except self.GSTValidationException as e:
            raise self.CreditNoteCreateException(e.message)

        assert issubclass(type(data), CreditNoteBaseData), (
            "Invalid dataclass, data needs to be of CreditNoteBaseData or its subclass"
        )

        obj = credit_note_fetch(credit_note_id=credit_note_id)
        old_amount = obj.amount
        old_gst_amount = obj.gst_amount

        if not obj:
            return
        allowed_fields = [
            "invoice_id",
            "amount",
            "gst_amount",
            "credit_note_doc_name",
            "credit_note_doc",
            "debit_note_doc_name",
            "debit_note_doc",
            "remarks",
        ]
        if obj.is_verified:
            if obj.credit_note_doc and not obj.debit_note_doc:
                allowed_fields = ["debit_note_doc_name", "debit_note_doc"]
            elif obj.debit_note_doc and not obj.credit_note_doc:
                allowed_fields = ["credit_note_doc_name", "credit_note_doc"]
            else:
                return obj

        obj, _, updated_fields = model_update(
            instance=obj, fields=allowed_fields, data=data, save=False, updated_by_id=user_id, clean=False
        )
        self.validate_update_amount(obj=obj, old_amount=old_amount, old_gst_amount=old_gst_amount)
        self.validate_gst_amount(obj=obj, max_tax_percentage=project_config.tax_type.max_slab_percent)
        obj, _verified = self.data_validate_is_verified(obj=obj, is_verified=data.is_verified, user_id=user_id)
        if _verified:
            updated_fields.extend(["is_verified", "verified_by_id", "verified_at"])

        obj.full_clean()
        obj.save(update_fields=updated_fields)
        return obj

    def delete(self, credit_note_id: int, user_id: int) -> None:
        from order.invoice.data.selectors import credit_note_fetch

        credit_note_fetch(credit_note_id=credit_note_id).soft_delete(user_id=user_id)

    def cancel(self, credit_note_id: int, user_id: int) -> None:
        InvoiceCreditNote.objects.filter(id=credit_note_id).update(
            status=InvoiceCreditNoteStatus.CANCELLED, cancelled_at=timezone.now(), cancelled_by_id=user_id
        )
