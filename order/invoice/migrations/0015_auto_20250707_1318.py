from django.db import migrations

from order.invoice.data.choices import InvoiceStatus
from order.invoice.data.models import Invoice


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0014_auto_20250707_1318'),
    ]

    operations = [
        migrations.RunSQL(
            f'DROP INDEX "{Invoice.CUSTOM_INDEX}";',
            reverse_sql=f'CREATE UNIQUE INDEX "{Invoice.CUSTOM_INDEX}"'
            f'ON vendor_order_invoices (client_id, vendor_id, LOWER("invoice_number"))'
            f'WHERE version = 2 AND deleted_at IS NULL;',
        ),
        migrations.RunSQL(
            f'CREATE UNIQUE INDEX "{Invoice.CUSTOM_INDEX}" '
            f'ON vendor_order_invoices (client_id, vendor_id, LOWER("invoice_number")) '
            f'WHERE version = 2 '
            f'AND deleted_at IS NULL '
            f'AND status NOT IN (\'{InvoiceStatus.CANCELLED}\', \'{InvoiceStatus.REJECTED}\');',
            reverse_sql=f'DROP INDEX "{Invoice.CUSTOM_INDEX}";',
        )
    ]
