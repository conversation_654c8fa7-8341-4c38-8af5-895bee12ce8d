# Generated by Django 3.2.15 on 2025-06-14 18:58

import common.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0173_auto_20250614_1858'),
        ('invoice', '0013_alter_invoice_unique_constraint'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='tax_amount_percent',
            field=models.DecimalField(decimal_places=2, default=None, max_digits=5, null=True, validators=[common.validators.tax_percent_validator]),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tax_amount_percent_toggle',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tds_amount',
            field=models.DecimalField(decimal_places=2, default=None, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tds_amount_percent',
            field=models.DecimalField(decimal_places=2, default=None, max_digits=5, null=True, validators=[common.validators.tax_percent_validator]),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tds_amount_percent_toggle',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='organizationinvoiceconfig',
            name='is_tds_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='organizationinvoiceconfig',
            name='organization',
            field=models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, related_name='invoice_config', to='core.organization'),
        ),
    ]
