from typing import Optional

from django.db.models import (
    <PERSON>,
    <PERSON>,
    Decimal<PERSON>ield,
    F,
    <PERSON>loatField,
    IntegerField,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Cast, Coalesce
from rest_framework.exceptions import ValidationError

from order.domain.constants import OrderStatusEnum
from order.invoice.data.choices import InvoiceStatus
from order.invoice.data.models import (
    Invoice,
    InvoiceCreditNote,
    InvoiceRequestMapping,
    InvoiceType,
    OrganizationInvoiceConfig,
    SupportingDocument,
    SupportingDocumentType,
    VendorOrder,
)
from order.invoice.domain.entities.credit_note import CreditNoteData
from order.invoice.domain.exceptions import InvoiceNotFoundException


def organization_invoice_config_get(org_id: int) -> OrganizationInvoiceConfig:
    config = OrganizationInvoiceConfig.objects.filter(organization_id=org_id).first()
    if config is None:
        return OrganizationInvoiceConfig(
            organization_id=org_id,
            is_order_mandatory=False,
            is_po_mandatory=False,
            is_tds_enabled=False,
        )
    return config


def invoice_header_data_get(
    project_id: Optional[int], order_id: Optional[int], queryset: QuerySet[Invoice], currency_id: Optional[int]
) -> dict:
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    if order_id:
        queryset = queryset.filter(order_id=order_id)
    if currency_id:
        queryset = queryset.filter(project__config__currency_id=currency_id)

    data = queryset.annotate_invoice_total_amount().aggregate(
        total_count=Count(
            "id",
            filter=Q(
                status__in=[
                    InvoiceStatus.ATTACHED.value,
                    InvoiceStatus.APPROVED.value,
                    InvoiceStatus.PENDING.value,
                    InvoiceStatus.HOLD.value,
                    InvoiceStatus.REQUEST_MISCONFIGURED.value,
                ]
            ),
        ),
        total_amount=Coalesce(
            Sum(
                "invoice_total_amount",
                filter=Q(
                    status__in=[
                        InvoiceStatus.ATTACHED.value,
                        InvoiceStatus.APPROVED.value,
                        InvoiceStatus.PENDING.value,
                        InvoiceStatus.HOLD.value,
                        InvoiceStatus.REQUEST_MISCONFIGURED.value,
                    ],
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        approved_count=Count(
            "id",
            filter=Q(
                status__in=[
                    InvoiceStatus.APPROVED.value,
                ]
            ),
            output_field=IntegerField(),
        ),
        approved_amount=Coalesce(
            Sum(
                "invoice_total_amount",
                filter=Q(
                    status__in=[
                        InvoiceStatus.APPROVED.value,
                    ]
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
        pending_count=Count(
            "id",
            filter=Q(
                status__in=[
                    InvoiceStatus.PENDING.value,
                    InvoiceStatus.HOLD.value,
                    InvoiceStatus.REQUEST_MISCONFIGURED.value,
                ]
            ),
            output_field=IntegerField(),
        ),
        pending_amount=Coalesce(
            Sum(
                "invoice_total_amount",
                filter=Q(
                    status__in=[
                        InvoiceStatus.PENDING.value,
                        InvoiceStatus.HOLD.value,
                        InvoiceStatus.REQUEST_MISCONFIGURED.value,
                    ]
                ),
            ),
            Value(0),
            output_field=DecimalField(),
        ),
    )

    credit_note_data = (
        InvoiceCreditNote.objects.select_related("invoice")
        .filter(
            invoice_id__in=queryset.values_list("id", flat=True),
            is_verified=True,
            deleted_at__isnull=True,
            status__in=[InvoiceCreditNote.Status.ATTACHED.value],
        )
        .aggregate(
            total_credit_note_amount=Coalesce(Sum("amount") + Sum("gst_amount"), Value(0), output_field=DecimalField()),
            approved_credit_note_amount=Coalesce(
                Sum("amount", filter=Q(invoice__status__in=[InvoiceStatus.APPROVED.value]))
                + Sum("gst_amount", filter=Q(invoice__status__in=[InvoiceStatus.APPROVED.value])),
                Value(0),
                output_field=DecimalField(),
            ),
            pending_credit_note_amount=Coalesce(
                Sum(
                    F("amount") + F("gst_amount"),
                    filter=Q(
                        invoice__status__in=[
                            InvoiceStatus.PENDING.value,
                            InvoiceStatus.HOLD.value,
                            InvoiceStatus.REQUEST_MISCONFIGURED.value,
                        ]
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            ),
        )
    )

    return {
        "total_invoice_count": data.get("total_count"),
        "total_invoice_amount": data.get("total_amount"),
        "approved_invoice_count": data.get("approved_count"),
        "approved_amount": data.get("approved_amount"),
        "pending_invoice_count": data.get("pending_count"),
        "pending_amount": data.get("pending_amount"),
        "total_credit_note_amount": credit_note_data.get("total_credit_note_amount"),
        "approved_credit_note_amount": credit_note_data.get("approved_credit_note_amount"),
        "pending_credit_note_amount": credit_note_data.get("pending_credit_note_amount"),
        "remaining_total_invoice_amount": data.get("total_amount") - credit_note_data.get("total_credit_note_amount"),
        "remaining_approved_invoice_amount": data.get("approved_amount")
        - credit_note_data.get("approved_credit_note_amount"),
        "remaining_pending_invoice_amount": data.get("pending_amount")
        - credit_note_data.get("pending_credit_note_amount"),
    }


def client_invoice_header_data_get(
    org_id: int,
    project_id: Optional[int] = None,
    order_id: Optional[int] = None,
    client_ids: list[int] = [],
    currency_id: Optional[int] = None,
) -> dict:
    if project_id is None and len(client_ids) == 0:
        raise Exception("project_id or client_ids must be provided in order to prevent client level data access.")
    queryset = Invoice.objects.available().select_related("project__config").filter(vendor_id=org_id)
    if client_ids:
        queryset = queryset.filter(client_id__in=client_ids)
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    return invoice_header_data_get(project_id=project_id, order_id=order_id, queryset=queryset, currency_id=currency_id)


def vendor_invoice_header_data_get(
    org_id: int,
    project_id: Optional[int] = None,
    order_id: Optional[int] = None,
    vendor_ids: list[int] = [],
    currency_id: Optional[int] = None,
) -> dict:
    if project_id is None and len(vendor_ids) == 0:
        raise Exception("project_id or vendor_ids must be provided in order to prevent vendor level data access.")
    queryset = Invoice.objects.available().select_related("project__config").filter(client_id=org_id)
    if vendor_ids:
        queryset = queryset.filter(vendor_id__in=vendor_ids)
    return invoice_header_data_get(project_id=project_id, order_id=order_id, queryset=queryset, currency_id=currency_id)


def invoice_fetch_all() -> QuerySet[Invoice]:
    return (
        Invoice.objects.available()
        .select_related(
            "vendor",
            "vendor__vendor",
            "uploaded_by",
            "updated_by",
            "project",
            "project__config",
            "order",
            "type",
            "order__project",
        )
        .annotate(
            last_approved_snapshot=Case(
                When(
                    order__isnull=False,
                    then=Subquery(
                        VendorOrder.objects.filter(id=OuterRef("order_id"))
                        .annotate_last_approved_snapshot()
                        .values("last_approved_snapshot")[:1]
                    ),
                )
            )
        )
        .annotate(
            order_amount=Case(
                When(
                    Q(last_approved_snapshot__isnull=False)
                    & Q(order__outgoing_status=OrderStatusEnum.PENDING_APPROVAL.value),
                    then=Cast(
                        Cast(F("last_approved_snapshot__amount"), FloatField())
                        + Cast(F("last_approved_snapshot__tax_amount"), FloatField()),
                        FloatField(),
                    ),
                ),
                When(
                    Q(order__isnull=False),
                    then=Coalesce(
                        Cast(F("order__saved_total_amount"), FloatField()),
                        Value(0.0),
                        output_field=FloatField(),
                    ),
                ),
                default=Value(0.0),
                output_field=FloatField(),
            )
        )
        .prefetch_related(
            Prefetch("supporting_documents", SupportingDocument.objects.available()), "supporting_documents__tags"
        )
        .annotate_doc_count()
        .annotate_credit_note_amount()
        .annotate_payable_amount()
        .annotate_request_data()
        .annotate_payment_request_pending_and_approved_amount()
        .annotate_already_paid_amount()
    )


# def get_invoice_details(invoice_id: int) -> Optional[Invoice]:
#     invoice = (
#         invoice_fetch_all()
#         .filter(Q(vendor_id=self.get_organization_id()) | Q(client_id=self.get_organization_id()))
#         .filter(id=invoice_id)
#         .select_related("uploaded_by__org__invoice_config")
#         .annotate_available_payment_request_count()
#         .annotate(
#             already_paid_amount=Coalesce(invoice_already_paid_amount_subquery(), Value(0), output_field=DecimalField()),
#         )
#     ).first()
#     return invoice


def client_invoice_fetch_all(client_ids: list[int], org_id: int) -> QuerySet[Invoice]:
    return invoice_fetch_all().filter(client_id__in=client_ids, vendor_id=org_id)


def vendor_invoice_fetch_all(vendor_ids: list[int], org_id: int) -> QuerySet[Invoice]:
    return invoice_fetch_all().filter(vendor_id__in=vendor_ids, client_id=org_id)


def credit_note_fetch_all() -> QuerySet[InvoiceCreditNote]:
    return (
        InvoiceCreditNote.objects.available()
        .select_related("invoice", "verified_by", "created_by", "invoice__vendor")
        .order_by("id")
    )


def invoice_fetch_by_id(invoice_id: int):
    invoice = invoice_fetch_all().filter(pk=invoice_id).first()
    if not invoice:
        raise InvoiceNotFoundException("Invoice not found")
    return invoice


def invoice_request_comment_id_get(*, invoice_id: int, request_id: int, org_id: int) -> Optional[int]:
    irm: InvoiceRequestMapping = (
        InvoiceRequestMapping.objects.filter(resource_id=invoice_id, request_id=request_id)
        .select_related("comment__organization")
        .first()
    )
    if org_id == irm.comment.organization_id:
        return irm.comment_id
    if not irm.secondary_comment:
        return None
    return irm.secondary_comment_id


def credit_note_fetch(credit_note_id: int):
    return InvoiceCreditNote.objects.available().filter(id=credit_note_id).first()


def order_pending_invoice_data_fetch(project_id: int, order_id: int) -> dict:
    data = (
        invoice_fetch_all()
        .filter(
            project_id=project_id,
            order_id=order_id,
            status__in=[InvoiceStatus.PENDING.value, InvoiceStatus.HOLD.value],
        )
        .annotate_invoice_total_amount()
        .values("id", "invoice_total_amount")
        .aggregate(
            pending_count=Count("id"),
            amount=Coalesce(Sum("invoice_total_amount"), Value(0), output_field=DecimalField()),
        )
    )
    return data


def get_invoice_list_v2(project_id: int):
    return Invoice.objects.filter(project_id=project_id)


def get_invoice_type_list_v2():
    return InvoiceType.objects.filter(is_active=True).all()


# TODO -> Should have filter of is_active=true but not applied in prod, so not using that filter here too
def get_invoice_supporting_doc_tags_list_v2():
    return SupportingDocumentType.objects.all()


def get_invoice_details_v2(project_id: int, invoice_id: int):
    return Invoice.objects.filter(id=invoice_id, project_id=project_id).select_related("uploaded_by")


def get_invoice_supporting_docs_list_v2(project_id: int, invoice_id: int):
    return (
        SupportingDocument.objects.filter(invoice_id=invoice_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id)
    )


def get_invoice_supporting_doc_details_v2(project_id: int, invoice_id: int, document_id: int):
    return (
        SupportingDocument.objects.filter(invoice_id=invoice_id, id=document_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id)
        .prefetch_related("tags")
    )


def get_vendor_invoice_credit_notes_list_v2(project_id: int, invoice_id: int, org_id: int):
    return (
        InvoiceCreditNote.objects.filter(invoice_id=invoice_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id, invoice__client_id=org_id)
        .available()
    )


def get_client_invoice_credit_notes_list_v2(project_id: int, invoice_id: int, org_id: int):
    return (
        InvoiceCreditNote.objects.filter(invoice_id=invoice_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id, invoice__vendor_id=org_id)
        .available()
    )


def get_vendor_invoice_credit_note_details_v2(project_id: int, invoice_id: int, credit_note_id: int, org_id: int):
    return (
        InvoiceCreditNote.objects.filter(id=credit_note_id, invoice_id=invoice_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id, invoice__client_id=org_id)
        .select_related("created_by", "verified_by")
        .first()
    )


def get_client_invoice_credit_note_details_v2(project_id: int, invoice_id: int, credit_note_id: int, org_id: int):
    return (
        InvoiceCreditNote.objects.filter(id=credit_note_id, invoice_id=invoice_id)
        .select_related("invoice")
        .filter(invoice__project_id=project_id, invoice__vendor_id=org_id)
        .select_related("created_by", "verified_by")
        .first()
    )


def create_vendor_invoice_credit_note_v2(project_id: int, invoice_id: int, org_id: int, credit_note, user_id: int):
    from order.invoice.domain.services.credit_note import CreditNoteService

    if not Invoice.objects.filter(id=invoice_id).filter(project_id=project_id, client_id=org_id).exists():
        raise ValidationError({"invoice_id": "Vendor Invoice not found for this project"})

    credit_note_data: CreditNoteData = CreditNoteData(
        is_verified=credit_note.is_verified,
        invoice_id=invoice_id,
        amount=credit_note.amount,
        credit_note_number=credit_note.credit_note_number,
        gst_amount=credit_note.tax_amount,
        remarks=credit_note.remarks,
        credit_note_doc_name=credit_note.credit_note_doc_name,
        credit_note_doc=credit_note.credit_note_doc_url,
        debit_note_doc_name=credit_note.debit_note_doc_name,
        debit_note_doc=credit_note.debit_note_doc_url,
    )

    service = CreditNoteService()
    return service.create(data=credit_note_data, user_id=user_id)


def create_client_invoice_credit_note_v2(project_id: int, invoice_id: int, org_id: int, credit_note, user_id: int):
    from order.invoice.domain.services.credit_note import CreditNoteService

    if not Invoice.objects.filter(id=invoice_id).filter(project_id=project_id, vendor_id=org_id).exists():
        raise ValidationError({"invoice_id": "Client Invoice not found for this project"})

    credit_note_data: CreditNoteData = CreditNoteData(
        is_verified=credit_note.is_verified,
        invoice_id=invoice_id,
        amount=credit_note.amount,
        credit_note_number=credit_note.credit_note_number,
        gst_amount=credit_note.tax_amount,
        remarks=credit_note.remarks,
        credit_note_doc_name=credit_note.credit_note_doc_name,
        credit_note_doc=credit_note.credit_note_doc_url,
        debit_note_doc_name=credit_note.debit_note_doc_name,
        debit_note_doc=credit_note.debit_note_doc_url,
    )

    service = CreditNoteService()
    return service.create(data=credit_note_data, user_id=user_id)
