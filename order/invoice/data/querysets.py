from django.conf import settings
from django.db.models import Count, F, OuterRef, Q, QuerySet, Subquery, Sum, Value
from django.db.models.fields import DecimalField
from django.db.models.functions import Coalesce, JSONObject
from django.utils import timezone
from django.utils.module_loading import import_string

from common.querysets import AvailableQuerySetMixin
from order.invoice.data.choices import InvoiceCreditNoteStatus
from payment_request.data.choices import PaymentRequestStatus
from rollingbanners.comment_base_service import CommentBaseService

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class InvoiceQuerySet(QuerySet, AvailableQuerySetMixin):
    def annotate_doc_count(self):
        return self.annotate(
            doc_count=Count("supporting_documents", filter=Q(supporting_documents__deleted_at__isnull=True)),
        )

    def annotate_invoice_total_amount(self):
        return self.annotate(invoice_total_amount=(F("amount") + F("gst_amount")))

    def annotate_credit_note_amount(self):
        return self.annotate(
            credit_note_amount=Coalesce(
                Sum(
                    "credit_notes__amount",
                    filter=Q(
                        credit_notes__deleted_at__isnull=True,
                        credit_notes__is_verified=True,
                        credit_notes__status__in=[InvoiceCreditNoteStatus.ATTACHED.value],
                    ),
                    distinct=True,
                ),
                Value(0),
                output_field=DecimalField(),
            ),
        )

    def annotate_credit_note_gst_amount(self):
        return self.annotate(
            credit_note_gst_amount=Coalesce(
                Sum(
                    "credit_notes__gst_amount",
                    filter=Q(
                        credit_notes__deleted_at__isnull=True,
                        credit_notes__is_verified=True,
                        credit_notes__status__in=[InvoiceCreditNoteStatus.ATTACHED.value],
                    ),
                    distinct=True,
                ),
                Value(0),
                output_field=DecimalField(),
            ),
        )

    def annotate_payable_amount(self):
        return (
            self.annotate_credit_note_amount()
            .annotate_credit_note_gst_amount()
            .annotate(
                payable_amount=(F("amount") + F("gst_amount"))
                - (F("credit_note_amount") + F("credit_note_gst_amount")),
            )
        )

    def annotate_credit_total_amount(self):
        return (
            self.annotate_credit_note_amount()
            .annotate_credit_note_gst_amount()
            .annotate(
                credit_total_amount=Coalesce(
                    F("credit_note_amount") + F("credit_note_gst_amount"), Value(0), output_field=DecimalField()
                ),
            )
        )

    def annotate_request_id(self):
        from order.invoice.data.models import InvoiceRequestMapping

        request_subquery = Subquery(
            InvoiceRequestMapping.objects.filter(resource_id=OuterRef("pk")).order_by("-id").values("request_id")[:1]
        )
        return self.annotate(request_id=request_subquery)

    def annotate_request_data(self):
        from order.invoice.data.models import InvoiceRequestMapping

        request_mapping = InvoiceRequestMapping.objects.filter(resource_id=OuterRef("pk")).annotate_request_data()
        return self.annotate(request_data=request_mapping.values("request_data")[:1])

    def annotate_comment_count(self, org_id: int):
        return self.annotate(
            comment_count=CommentHelperService.get_count(
                context_group=CommentHelperService.GROUPED_CONTEXT.INVOICE.name, org_id=org_id
            ),
        )

    def annotate_pending_payment_request_amount(self):
        return self.annotate(
            pending_payment_request_amount=Coalesce(
                Sum(
                    "payment_requests__request_amount",
                    filter=Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.HOLD.value,
                            PaymentRequestStatus.PENDING.value,
                            PaymentRequestStatus.REQUEST_MISCONFIGURED.value,
                        ],
                    ),
                ),
                0,
                output_field=DecimalField(),
            )
        )

    def annotate_approved_payment_request_amount(self):
        return self.annotate(
            approved_payment_request_amount=Coalesce(
                Sum(
                    "payment_requests__request_amount",
                    filter=Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.APPROVED.value,
                            PaymentRequestStatus.SUBMITTED.value,
                        ],
                    ),
                ),
                0,
                output_field=DecimalField(),
            )
        )

    def annotate_payment_request_pending_and_approved_amount(self):
        return (
            self.annotate_pending_payment_request_amount()
            .annotate_approved_payment_request_amount()
            .annotate(
                invoice_payment_request_amount=JSONObject(
                    pending_payment_request_amount=Coalesce("pending_payment_request_amount", Value(0)),
                    approved_payment_request_amount=Coalesce("approved_payment_request_amount", Value(0)),
                )
            )
        )

    def annotate_total_payment_request_amount(self):
        return (
            self.annotate_pending_payment_request_amount()
            .annotate_approved_payment_request_amount()
            .annotate(
                payment_request_amount=Coalesce(
                    F("pending_payment_request_amount"), Value(0), output_field=DecimalField()
                )
                + Coalesce(F("approved_payment_request_amount"), Value(0), output_field=DecimalField())
            )
        )

    def annotate_available_payment_request_count(self):
        return self.annotate(
            payment_request_count=Coalesce(
                Count(
                    "payment_requests",
                    filter=~Q(
                        payment_requests__status__in=[
                            PaymentRequestStatus.REJECTED.value,
                            PaymentRequestStatus.CANCELLED.value,
                        ],
                    ),
                ),
                Value(0),
            )
        )

    def annotate_already_paid_amount(self):
        return self.annotate(
            already_paid_amount=Coalesce(
                Sum(
                    "payment_requests__payment_entries__amount",
                    filter=Q(
                        Q(
                            payment_requests__cancelled_at__isnull=True,
                            payment_requests__payment_entries__cancelled_at__isnull=True,
                        ),
                        ~Q(
                            payment_requests__status__in=[
                                PaymentRequestStatus.CANCELLED.value,
                                PaymentRequestStatus.REJECTED.value,
                            ]
                        ),
                    ),
                ),
                Value(0),
                output_field=DecimalField(),
            )
        )


class SupportingDocumentQuerySet(QuerySet, AvailableQuerySetMixin):
    pass


class InvoiceCreditNoteQuerySet(QuerySet, AvailableQuerySetMixin):
    def soft_delete(self, user_id: int):
        return self.update(deleted_at=timezone.now(), deleted_by_id=user_id)
