from typing import List

from django.conf import settings
from django.db import models

from approval_request.data.models import ResourceRequestMappingBaseModel
from commentv2.data.models import Comment
from common.constants import FILE_FIELD_MAX_LENGTH, TaxPercentageFieldConfig
from common.helpers import get_upload_path
from common.models import BaseModel, CreateUpdateDeleteModel, DeleteModel, UpdateModel, UploadUpdateDeleteModel
from common.validators import tax_percent_validator
from core.models import Organization, User
from order.data.models import VendorOrder, VendorPurchaseOrder
from order.invoice.data.choices import InvoiceEventChoices, InvoiceStatus, InvoiceSupportingDocumentStatus
from order.invoice.data.querysets import InvoiceCreditNoteQuerySet, InvoiceQuerySet, SupportingDocumentQuerySet
from order.invoice.domain.constants import CreditNoteActionsEnum
from project.data.models import Project
from task.data.models import Task


class InvoiceType(DeleteModel):
    name = models.CharField(max_length=50, unique=True)
    is_active = models.BooleanField(default=False)

    class Meta:
        db_table = "invoice_invoice_type"


class Invoice(UploadUpdateDeleteModel):
    CUSTOM_INDEX = "unique_invoice_number_vendor_client"

    vendor_po = models.ForeignKey(
        VendorPurchaseOrder, on_delete=models.RESTRICT, default=None, null=True, related_name="invoices"
    )
    invoice_number = models.CharField(max_length=25)
    # (invoice_number, vendor_id, client_id) unique constraint for version = 2
    version = models.PositiveSmallIntegerField(default=2)
    invoice_date = models.DateField()
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    gst_amount = models.DecimalField(max_digits=15, decimal_places=2)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    file_name = models.CharField(max_length=250)
    status = models.CharField(max_length=100, choices=InvoiceStatus.choices, default=InvoiceStatus.ATTACHED)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="invoices", null=True, blank=True
    )
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, default=None, null=True, related_name="invoices")
    type = models.ForeignKey(InvoiceType, on_delete=models.RESTRICT, default=None, null=True, related_name="invoices")
    client = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, default=None, null=True, related_name="vendor_invoices"
    )
    vendor = models.ForeignKey(
        Organization, on_delete=models.RESTRICT, default=None, null=True, related_name="client_invoices"
    )
    order = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT, default=None, null=True, related_name="invoices")
    tax_amount_percent = models.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        default=None,
        null=True,
        validators=[tax_percent_validator],
    )
    tax_amount_percent_toggle = models.BooleanField(default=False)
    tds_amount = models.DecimalField(max_digits=15, decimal_places=2, default=None, null=True)
    tds_amount_percent = models.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        default=None,
        null=True,
        validators=[tax_percent_validator],
    )
    tds_amount_percent_toggle = models.BooleanField(default=False)
    objects = InvoiceQuerySet.as_manager()

    class Meta:
        db_table = "vendor_order_invoices"


class SupportingDocumentType(BaseModel):
    name = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "invoice_supporting_document_types"


class SupportingDocument(CreateUpdateDeleteModel):
    invoice = models.ForeignKey(
        Invoice, on_delete=models.RESTRICT, related_name="supporting_documents", null=True, blank=True
    )
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    file_name = models.CharField(max_length=250)
    tags = models.ManyToManyField(SupportingDocumentType, through="SupportingDocumentTypeTag")
    invoice_status = models.CharField(max_length=100, choices=InvoiceStatus.choices, default=InvoiceStatus.PENDING)
    status = models.CharField(
        max_length=100,
        choices=InvoiceSupportingDocumentStatus.choices,
        default=InvoiceSupportingDocumentStatus.UPLOADED,
    )

    objects = SupportingDocumentQuerySet.as_manager()

    class Meta:
        db_table = "invoice_supporting_documents"


class SupportingDocumentTypeTag(models.Model):
    supporting_document = models.ForeignKey(SupportingDocument, on_delete=models.CASCADE)
    supporting_document_type = models.ForeignKey(SupportingDocumentType, on_delete=models.CASCADE)
    tagged_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "invoice_supporting_document_type_tags"


class InvoiceCreditNote(CreateUpdateDeleteModel):
    # TODO: have to remove this
    class Status(models.TextChoices):
        ATTACHED = "attached"
        CANCELLED = "cancelled"

    invoice = models.ForeignKey(Invoice, on_delete=models.RESTRICT, related_name="credit_notes")
    credit_note_number = models.CharField(max_length=25, default="", blank=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    gst_amount = models.DecimalField(max_digits=15, decimal_places=2)
    credit_note_doc_name = models.CharField(max_length=250, null=True, blank=True)
    credit_note_doc = models.FileField(
        upload_to=get_upload_path, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH
    )
    debit_note_doc_name = models.CharField(max_length=250, null=True, blank=True)
    debit_note_doc = models.FileField(
        upload_to=get_upload_path, null=True, blank=True, max_length=FILE_FIELD_MAX_LENGTH
    )
    remarks = models.TextField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)

    verified_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, related_name="invoice_credit_notes_verified", null=True, blank=True
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=15, default=Status.ATTACHED)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="credit_notes", null=True, blank=True
    )
    objects = InvoiceCreditNoteQuerySet.as_manager()

    @property
    def actions(self) -> List[str]:
        actions = []
        if self.status == InvoiceCreditNote.Status.ATTACHED:
            if self.is_verified is False:
                actions.extend(
                    [
                        CreditNoteActionsEnum.EDIT.value,
                        CreditNoteActionsEnum.CANCEL.value,
                        CreditNoteActionsEnum.DELETE.value,
                    ]
                )
            else:
                actions.append(CreditNoteActionsEnum.CANCEL.value)
        return [str(action) for action in actions]

    class Meta:
        db_table = "invoice_credit_notes"


class OrganizationInvoiceConfig(UpdateModel):
    is_order_mandatory = models.BooleanField(default=False)
    is_po_mandatory = models.BooleanField(default=False)
    is_tds_enabled = models.BooleanField(default=False)
    organization = models.OneToOneField(Organization, on_delete=models.RESTRICT, related_name="invoice_config")

    def clean(self) -> None:
        errors = {}

        if self.is_po_mandatory and not self.is_order_mandatory:
            errors["is_order_mandatory"] = "If is_po_mandatory is true, then is_order_mandatory must be true."

        if errors:
            raise self.WrongDataSavedException(errors)

    class Meta:
        db_table = "invoice_configs"


class InvoiceRequestMapping(ResourceRequestMappingBaseModel):
    resource = models.ForeignKey(Invoice, on_delete=models.RESTRICT, related_name="request_mappings")
    event = models.CharField(max_length=50, choices=InvoiceEventChoices.choices)
    task = models.ForeignKey(Task, on_delete=models.RESTRICT)
    comment = models.ForeignKey(Comment, on_delete=models.RESTRICT, related_name="request_mappings")
    secondary_comment = models.ForeignKey(
        Comment, on_delete=models.RESTRICT, null=True, blank=True, default=None
    )  # for vendor org comment

    class Meta:
        db_table = "invoice_request_mappings"
        verbose_name = "Invoice Request Mapping"
        verbose_name_plural = "Invoice Request Mappings"
        app_label = "invoice"
