import decimal

from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from boq.domain.constants import (
    ORDER_DRAFT_AMOUNT_GREATER_THAN_BOQ_AMOUNT,
    ORDER_REMAINING_AMOUNT_GREATER_THAN_BOQ_AMOUNT,
)
from common.element_base.services import ElementCodeService
from common.element_base_serializer import OrderElementBaseModelSerializer
from common.regex import RegularExpression as Regex
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomDateField,
    CustomFileField,
    HashIdField,
    IdNameSerializer,
)
from common.utils import padding_for_serial_number
from core.serializers import (
    OrganizationModelSerializer,
    OrganizationOrderPaymentTermSerializer,
    ProductionDrawingTagSerializer,
    UserModelSerializer,
    UserSerializer,
)
from element.domain.services import BaseElementQuantityService
from element.interface.serializers.serializers import (
    ElementCategoryModelSerializer,
    ElementItemTypeModelSerializer,
)
from order.config.interface.serializers import OrganizationPurchaseOrderTypeModelSerializer
from order.data.choices import OrderStatusChoices
from order.data.models import (
    DeductionAttachmentSnapshot,
    DeductionSnapshot,
    OrderElementGuidelineAttachmentSnapshot,
    OrderElementGuidelineSnapshot,
    OrderElementPreviewFileSnapshot,
    OrderElementProductionDrawingSnapshot,
    OrderElementSnapshot,
    OrderSnapshot,
    VendorOrderElement,
)
from order.domain.entities.entities import (
    OrganizationPOConfigEntity,
    VendorInfoData,
)
from order.domain.mappings import ORDER_FIELDS_MAPPING
from order.domain.status_choices import OrderStatus, POStatus
from order.interface.constants import old_shipping_address_helper
from order.interface.vendor_serializers import (
    BaseTncSerializer,
    DeductionAttachmentModelSerializer,
    DeductionModelSerializer,
    TermsAndConditionsAttachmentModelSerializer,
    VendorOrderElementModelSerializer,
    VendorOrderModelSerializer,
    VendorPurchaseOrderModelSerializer,
)
from project.data.models import Store
from project.serializers import ProjectModelSerializer
from proposal.domain.services import get_hidden_fields
from ratecontract.serializers.model_serializers import RateContractModelSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage
from vendor.interface.serializers import VendorCategorySerializer, VendorModelSerializer


class DeductionSnapshotModelSerializer(BaseModelSerializer):
    code = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    @staticmethod
    def get_code(obj):
        code = padding_for_serial_number(serial_number=obj.code, padding=3)
        return f"DED{code}"

    class Meta:
        model = DeductionSnapshot
        fields = "__all__"


class DeductionAttachmentSnapshotModelSerializer(BaseModelSerializer):
    class Meta:
        model = DeductionAttachmentSnapshot
        fields = "__all__"


class OrderElementPreviewFileSnapshotModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrderElementPreviewFileSnapshot
        fields = "__all__"


class OrderElementProductionDrawingSnapshotModelSerializer(BaseModelSerializer):
    tags = ProductionDrawingTagSerializer(many=True)

    class Meta:
        model = OrderElementProductionDrawingSnapshot
        fields = "__all__"


class OrderElementGuidelineAttachmentSnapshotModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrderElementGuidelineAttachmentSnapshot
        fields = "__all__"


class OrderElementGuidelineSnapshotModelSerializer(BaseModelSerializer):
    class AttachmentSerializer(OrderElementGuidelineAttachmentSnapshotModelSerializer):
        id = serializers.CharField()

        class Meta(OrderElementGuidelineAttachmentSnapshotModelSerializer.Meta):
            ref_name = "custom-element-guideline-attachment"
            fields = ("id", "file", "type", "name")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    attachments = AttachmentSerializer(many=True)

    class Meta:
        model = OrderElementGuidelineSnapshot
        fields = "__all__"


class OrderElementSnapshotModelSerializer(OrderElementBaseModelSerializer):
    class CategorySerializer(ElementCategoryModelSerializer):
        class Meta(ElementCategoryModelSerializer.Meta):
            ref_name = "vendor-order-element-category"
            fields = ("id", "name", "code")
            output_hash_id_fields = ("id",)

    class ItemTypeSerializer(ElementItemTypeModelSerializer):
        class Meta(ElementItemTypeModelSerializer.Meta):
            ref_name = "item-type"
            fields = ("id", "name", "color_code")
            output_hash_id_fields = ("id",)

    class ProductionDrawingSerializer(OrderElementProductionDrawingSnapshotModelSerializer):
        id = serializers.CharField()

        class Meta(OrderElementProductionDrawingSnapshotModelSerializer.Meta):
            ref_name = "custom-element-drawing"
            fields = ("id", "file", "tags", "name", "uploaded_at")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    class PreviewFileSerializer(OrderElementPreviewFileSnapshotModelSerializer):
        class Meta(OrderElementPreviewFileSnapshotModelSerializer.Meta):
            ref_name = "vendor-order-element-preview-file"
            fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
            output_hash_id_fields = ("id",)

    class ElementGuidelineSerializer(OrderElementGuidelineSnapshotModelSerializer):
        class Meta(OrderElementGuidelineSnapshotModelSerializer.Meta):
            fields = ("id", "name", "description", "created_at", "updated_at", "attachments")
            ref_name = "OrderElementModelElementGuidelineSerializer"

    status = serializers.SerializerMethodField()
    code = serializers.SerializerMethodField()
    item_type = ItemTypeSerializer()
    production_drawings = ProductionDrawingSerializer(many=True)
    category = CategorySerializer()
    preview_files = PreviewFileSerializer(many=True)
    guidelines = ElementGuidelineSerializer(many=True)
    preview_file_url = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        return OrderStatus.get_display_status(obj.status)

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_code(self, obj: VendorOrderElement):
        return ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.boq_element_version
        )

    @swagger_serializer_method(serializer_or_field=serializers.URLField())
    def get_preview_file_url(self, obj):
        if hasattr(obj, "preview_file_url") and obj.preview_file_url:
            return PublicMediaFileStorage.url(obj.preview_file_url)

    class Meta:
        model = OrderElementSnapshot
        fields = "__all__"


class OrderSnapshotModelSerializer(BaseModelSerializer):
    class PaymentTermSerializer(OrganizationOrderPaymentTermSerializer):
        class Meta(OrganizationOrderPaymentTermSerializer.Meta):
            ref_name = "payment_term"
            fields = (
                "id",
                "title",
                "description",
                "is_active",
            )
            output_hash_id_fields = ("id",)

    class DeductionSnapshotSerializer(DeductionSnapshotModelSerializer):
        class DeductionAttachmentSnapshotSerializer(DeductionAttachmentSnapshotModelSerializer):
            uploaded_by = serializers.SerializerMethodField()

            @swagger_serializer_method(serializer_or_field=serializers.CharField())
            def get_uploaded_by(self, obj):
                return obj.uploaded_by.name

            class Meta(DeductionAttachmentSnapshotModelSerializer.Meta):
                ref_name = "deduction-attachment"
                fields = ("id", "file", "name", "uploaded_at", "uploaded_by")
                output_hash_id_fields = ("id",)

        attachments = DeductionAttachmentSnapshotSerializer(many=True)
        created_by = serializers.SerializerMethodField()
        updated_by = serializers.SerializerMethodField()
        updated_at = serializers.DateTimeField(source="created_at")

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_created_by(self, obj):
            return obj.created_by.name

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_updated_by(self, obj):
            if obj.created_by:
                return obj.created_by.name
            return None

        class Meta(DeductionSnapshotModelSerializer.Meta):
            ref_name = "deduction"
            fields = (
                "id",
                "name",
                "amount",
                "tax_amount",
                "type",
                "remark",
                "code",
                "item_reference",
                "type_color_code",
                "created_at",
                "updated_at",
                "created_by",
                "updated_by",
                "attachments",
            )
            output_hash_id_fields = ("id",)

    class OrderElementSnapshotSerializer(OrderElementSnapshotModelSerializer):
        comment_count = serializers.SerializerMethodField()
        # progress_percentage = serializers.IntegerField()
        preview_file = serializers.SerializerMethodField()
        rc_rate = serializers.DecimalField(max_digits=11, decimal_places=2, required=False, allow_null=True)
        el_element_id = HashIdField()
        boq_element_id = HashIdField()
        # in_progress = serializers.SerializerMethodField()
        quantity_dimensions = serializers.SerializerMethodField()
        gross_amount = serializers.SerializerMethodField()
        final_amount = serializers.SerializerMethodField()
        tax_percent = serializers.DecimalField(max_digits=11, decimal_places=2, required=False, allow_null=True)
        error = serializers.SerializerMethodField()

        def get_error(self, obj):
            if (
                hasattr(obj, "is_remaining_order_amount_greater_than_boq_amount")
                and obj.is_remaining_order_amount_greater_than_boq_amount
            ):
                return ORDER_REMAINING_AMOUNT_GREATER_THAN_BOQ_AMOUNT
            elif (
                hasattr(obj, "is_draft_order_amount_greater_than_boq_amount")
                and obj.is_draft_order_amount_greater_than_boq_amount
            ):
                return ORDER_DRAFT_AMOUNT_GREATER_THAN_BOQ_AMOUNT
            return None

        def get_gross_amount(self, obj):
            if hasattr(obj, "gross_amount_annotation") and obj.gross_amount_annotation:
                return obj.gross_amount_annotation
            return 0

        def get_final_amount(self, obj):
            if hasattr(obj, "element_final_amount") and obj.element_final_amount:
                return obj.element_final_amount
            return 0

        # @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        # def get_in_progress(self, obj):
        #     if obj.progress_percentage and 0 < obj.progress_percentage < 100:
        #         return True
        #     return False

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        @swagger_serializer_method(serializer_or_field=serializers.FileField())
        def get_preview_file(self, obj):
            return (
                PublicMediaFileStorage.url(obj.preview_file)
                if hasattr(obj, "preview_file") and obj.preview_file
                else None
            )

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        class Meta(OrderElementSnapshotModelSerializer.Meta):
            ref_name = "OrderElementOutput"
            fields = (
                "id",
                "name",
                "code",
                "category",
                "description",
                "uom",
                "quantity",
                "quantity_dimensions",
                "item_type",
                "vendor_rate",
                "amount",
                "tax_percent",
                "status",
                "client_rate",
                "budget_rate",
                "comment_count",
                # "progress_percentage",
                "preview_file",
                "custom_type",
                "rc_rate",
                "el_element_id",
                "boq_element_id",
                # "in_progress",
                "base_amount",
                "gross_amount",
                "service_charge_percent",
                "is_service_charge_with_base_amount",
                "discount_percent",
                "discounted_value",
                "final_amount",
                "brand_name",
                "hsn_code",
                "error",
            )

    class VendorSerializer(VendorModelSerializer):
        name = serializers.CharField()
        code = serializers.CharField(source="vendor.code")
        categories = VendorCategorySerializer(many=True, source="vendor.category")
        location = serializers.CharField(source="vendor.location")

        class Meta(VendorModelSerializer.Meta):
            fields = ("id", "name", "code", "categories", "location")
            output_hash_id_fields = ("id",)

    class UserSerailizer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "UserOutput"
            fields = ("id", "name", "photo")
            output_hash_id_fields = ("id",)

    class VendorPurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
        status = serializers.SerializerMethodField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_status(self, obj):
            return POStatus.get_display_status(obj.status)

        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            return approved_amount

        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            ref_name = "VendorPurchaseOrderSnapshotOutput"
            fields = (
                "id",
                "name",
                "po_number",
                "uploaded_at",
                "file",
                "amount",
                "tax_amount",
                "status",
                "type",
                "po_date",
                "version",
                "uploaded_by",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
            )
            output_hash_id_fields = ("id",)
            input_hash_id_fields = ("id",)

    class ProjectSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectInputOutput"
            fields = ("id", "name", "job_id")
            output_hash_id_fields = ("id",)

    class TermsAndConditionAttachmentSerializer(TermsAndConditionsAttachmentModelSerializer):
        class Meta(TermsAndConditionsAttachmentModelSerializer.Meta):
            fields = ("id", "file", "name", "type")
            output_hash_id_fields = ("id",)

    class OrganizationSerializer(OrganizationModelSerializer):
        organization_id = HashIdField(source="id")

        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "OrderOrganziationOutput"
            fields = ("organization_id", "name")

    class OutgoingOrgSerializer(OrganizationModelSerializer):
        organization_id = HashIdField(source="id")
        name = serializers.CharField()
        code = serializers.CharField(source="vendor.code")

        categories = VendorCategorySerializer(many=True, source="vendor.category")
        location = serializers.CharField(source="vendor.location")

        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "OutgoingOrganziationOutput"
            fields = ("organization_id", "name", "code", "categories", "location")

    class RateContractSerializer(RateContractModelSerializer):
        class Meta(RateContractModelSerializer.Meta):
            ref_name = "RateContractOutput"
            fields = ("id", "name", "element_count", "status")

    project = ProjectSerializer(source="order.project", required=False)
    org_from = OrganizationSerializer(source="order.org_from")
    org_to = OutgoingOrgSerializer(source="order.org_to")
    origin_org = OrganizationSerializer(source="order.origin_org")
    purchase_orders = VendorPurchaseOrderSerializer(source="order.purchase_orders", required=False, many=True)
    order_elements = OrderElementSnapshotSerializer(many=True)
    cancelled_elements_count = serializers.SerializerMethodField()
    not_cancelled_elements_count = serializers.SerializerMethodField()
    vendor = VendorSerializer(source="org_to")
    created_by = UserSerailizer()
    issued_by = serializers.SerializerMethodField()
    issued_at = serializers.SerializerMethodField()
    updated_by = UserSerailizer(source="order.updated_by", allow_null=True)
    sent_at = serializers.SerializerMethodField()
    po_attached_at = serializers.DateTimeField(source="order.po_attached_at", allow_null=True)
    payment_term = PaymentTermSerializer(allow_null=True)
    attachments = TermsAndConditionAttachmentSerializer(source="order.attachments", many=True)
    status = serializers.SerializerMethodField()
    invoice_status = serializers.SerializerMethodField()
    po_status = serializers.SerializerMethodField()
    order_number = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    shipping_address = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()
    rate_contract = RateContractSerializer()
    deductions = DeductionSnapshotSerializer(source="deduction_snapshots", many=True)
    type_of_order = serializers.CharField(source="order.order_type")
    payment_tnc = serializers.SerializerMethodField()
    other_tnc = serializers.SerializerMethodField()
    purchase_order_type = OrganizationPurchaseOrderTypeModelSerializer(source="order.purchase_order_type")

    @swagger_serializer_method(serializer_or_field=BaseTncSerializer())
    def get_payment_tnc(self, obj):
        return VendorOrderModelSerializer().get_payment_tnc(obj=obj)

    @swagger_serializer_method(serializer_or_field=BaseTncSerializer())
    def get_other_tnc(self, obj):
        return VendorOrderModelSerializer().get_other_tnc(obj=obj)

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_comment_count(self, obj):
        return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

    @swagger_serializer_method(serializer_or_field=UserSerailizer())
    def get_issued_by(self, obj):
        if getattr(obj.order, "issued_by"):
            return self.UserSerailizer(obj.order.issued_by).data
        else:
            return None

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_issued_at(self, obj):
        if getattr(obj, "issued_at"):
            return obj.issued_at
        else:
            return None

    @swagger_serializer_method(serializer_or_field=OutgoingOrgSerializer())
    def get_org_to(self, obj):
        return self.OutgoingOrgSerializer(obj.org_to).data

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_order_number(self, obj):
        return f"{obj.job_id}/{obj.order_number}"

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        return dict(OrderStatusChoices.choices)[obj.outgoing_status]

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_invoice_status(self, obj):
        return obj.invoice_status.title()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_po_status(self, obj):
        return POStatus.get_display_status(obj.po_status)

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_cancelled_elements_count(self, obj):
        return obj.cancelled_elements_count

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_not_cancelled_elements_count(self, obj):
        return obj.not_cancelled_elements_count

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_sent_at(self, obj):
        if obj.status in [None, OrderStatus.PENDING]:
            # default status is pending so return created_at
            sent_at = obj.created_at
            # TODO:
        elif obj.status == OrderStatus.SENT:
            sent_at = obj.issued_at
        elif obj.status == OrderStatus.CANCELLED:
            sent_at = obj.cancelled_at
        else:
            sent_at = obj.created_at
        return sent_at

    # @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    # def get_po_attached_at(self, obj):
    #     if obj.po_status in [None, POStatus.PO_PENDING]:
    #         po_attached_at = None
    #     if obj.po_status == POStatus.PO_ATTACHED:
    #         po_attached_at = obj.po_attached_at
    #     if obj.po_status == POStatus.PO_CANCELLED:
    #         po_attached_at = obj.po_cancelled_at
    #     return po_attached_at

    @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
    def get_amount(self, obj):
        deduction_amount = 0
        if hasattr(obj, "deduction_amount") and obj.deduction_amount:
            deduction_amount = obj.deduction_amount
        if hasattr(obj, "elements_final_amount") and obj.elements_final_amount:
            return obj.elements_final_amount - deduction_amount
        if hasattr(obj, "order_amount") and obj.order_amount:
            return obj.order_amount - deduction_amount
        return 0

    # def get_amount(self, obj):
    #     if hasattr(obj, "elements_final_amount") and obj.elements_final_amount:
    #         return obj.elements_final_amount
    #     return 0
    # if hasattr(obj, "order_amount") and obj.order_amount:
    #     if hasattr(obj, "deduction_amount") and obj.deduction_amount:
    #         return obj.order_amount - obj.deduction_amount
    #     return obj.order_amount
    # return 0

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_shipping_address(self, obj):
        if obj.shipping_address_header is None or obj.shipping_address_header == "":
            store_address = (
                Store.objects.filter(project_id=obj.order.project_id).values_list("address", flat=True).first()
            )
            shipping_addresses = old_shipping_address_helper(store_address=store_address)

            for shipping_address in shipping_addresses:
                if obj.shipping_address == shipping_address["header"]:
                    return shipping_address["address"]
            return obj.shipping_address
        else:
            return obj.shipping_address

    class Meta:
        model = OrderSnapshot
        fields = "__all__"


class OrganizationPOConfigEntitySerializer(BaseDataclassSerializer):
    is_order_mandatory: bool
    is_po_mandatory: bool

    class Meta:
        dataclass = OrganizationPOConfigEntity


class SnapshotDetailSerializer(OrderSnapshotModelSerializer):
    rc_count = serializers.IntegerField()
    purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
        source="sorted_purchase_orders", many=True
    )
    poc = UserSerializer(source="order.poc")
    request_id = HashIdField()
    task_id = HashIdField()
    updated_at = serializers.DateTimeField(source="order.updated_at")
    po_status = serializers.CharField(source="order.po_status")
    po_attached_at = serializers.DateTimeField(source="order.po_attached_at")
    is_discount_col_visible = serializers.BooleanField(source="is_discounted")
    creator_org_id = HashIdField(source="created_by.org_id")
    error_config = serializers.SerializerMethodField()
    hidden_fields = serializers.SerializerMethodField()
    status = serializers.CharField()

    def get_error_config(self, obj):
        return [ORDER_DRAFT_AMOUNT_GREATER_THAN_BOQ_AMOUNT, ORDER_REMAINING_AMOUNT_GREATER_THAN_BOQ_AMOUNT]

    def get_hidden_fields(self, obj):
        return get_hidden_fields(
            client_field_mapping=ORDER_FIELDS_MAPPING, order_id=obj.order_id, org_id=obj.order.org_from_id
        )

    class VendorSerializer(VendorModelSerializer):
        organization_id = HashIdField()

        class Meta(VendorModelSerializer.Meta):
            ref_name = "VendorOrderOutputList"
            fields = ("organization_id", "name", "code")

    @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
    def get_org_to(self, obj):
        return self.OutgoingOrgSerializer(obj.org_to).data

    class Meta(OrderSnapshotModelSerializer.Meta):
        ref_name = "VendorOrderOutput"
        fields = (
            "id",
            "request_id",
            "task_id",
            "amount",
            "tax_amount",
            "started_at",
            "updated_at",
            "due_at",
            "payment_tnc",
            "other_tnc",
            "terms_and_conditions",
            "po_status",
            "status",
            "order_elements",
            "po_attached_at",
            "updated_by",
            "purchase_orders",
            "project",
            "attachments",
            "order_number",
            "shipping_address",
            "org_to",
            "org_from",
            "origin_org",
            "issued_by",
            "created_by",
            "issued_at",
            "work_order_from",
            "shipping_address_header",
            "comment_count",
            "rate_contract",
            "rc_count",
            "deductions",
            "poc",
            "deduction_snapshots",
            "is_discount_col_visible",
            "creator_org_id",
            "error_config",
            "hidden_fields",
        )
        output_hash_id_fields = ("id",)


class OrderSerializer(VendorOrderModelSerializer):
    org_to = serializers.SerializerMethodField()
    rc_count = serializers.IntegerField()
    purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
        source="sorted_purchase_orders", many=True
    )
    poc = UserSerializer()
    is_discount_col_visible = serializers.BooleanField(source="is_discounted")
    is_service_charge_col_visible = serializers.BooleanField(source="is_service_charged")
    creator_org_id = HashIdField(source="created_by.org_id")
    request_id = HashIdField()
    task_id = HashIdField()
    tax_amount = serializers.DecimalField(source="order_tax_amount", max_digits=20, decimal_places=2, default=0)
    amount = serializers.DecimalField(source="order_amount", max_digits=20, decimal_places=2, default=0)
    error_config = serializers.SerializerMethodField()
    hidden_fields = serializers.SerializerMethodField()
    status = serializers.CharField()

    def get_error_config(self, obj):
        return [ORDER_DRAFT_AMOUNT_GREATER_THAN_BOQ_AMOUNT, ORDER_REMAINING_AMOUNT_GREATER_THAN_BOQ_AMOUNT]

    class VendorSerializer(VendorModelSerializer):
        organization_id = HashIdField()

        class Meta(VendorModelSerializer.Meta):
            ref_name = "VendorOrderOutputList"
            fields = ("organization_id", "name", "code")

    @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
    def get_org_to(self, obj):
        return self.OutgoingOrgSerializer(obj.org_to).data

    def get_hidden_fields(self, obj):
        return get_hidden_fields(client_field_mapping=ORDER_FIELDS_MAPPING, order_id=obj.id, org_id=obj.org_from_id)

    class Meta(VendorOrderModelSerializer.Meta):
        ref_name = "VendorOrderOutput"
        fields = (
            "id",
            "request_id",
            "task_id",
            "status",
            "amount",
            "tax_amount",
            "updated_at",
            "started_at",
            "due_at",
            "terms_and_conditions",
            "payment_term_id",
            "payment_term_text",
            "po_status",
            "order_elements",
            "po_attached_at",
            "updated_by",
            "purchase_orders",
            "project",
            "attachments",
            "order_number",
            "shipping_address",
            "org_to",
            "org_from",
            "origin_org",
            "issued_by",
            "created_by",
            "issued_at",
            "work_order_from",
            "shipping_address_header",
            "comment_count",
            "rate_contract",
            "rc_count",
            "deductions",
            "poc",
            "is_discount_col_visible",
            "is_service_charge_col_visible",
            "creator_org_id",
            "actions",
            "type_of_order",
            "purchase_order_type",
            "purchase_order_preview",
            "error_config",
            "hidden_fields",
        )
        output_hash_id_fields = (
            "id",
            # "request_id",
            # "task_id",
            "payment_term_id",
        )


class VendorInfoDataSerializer(BaseDataclassSerializer):
    gst_number = serializers.RegexField(
        regex=Regex.GST_NUMBER, error_messages={"invalid": "GST Number Invalid"}, allow_null=True
    )
    pan_number = serializers.RegexField(
        regex=Regex.PAN_NUMBER, error_messages={"invalid": "PAN Number Invalid"}, allow_null=True
    )
    ifsc_code = serializers.RegexField(
        regex=Regex.IFSC_CODE, error_messages={"invalid": "IFSC Code Invalid"}, allow_null=True
    )
    pan_attachment = CustomFileField(allow_null=True, required=False)
    gst_attachment = CustomFileField(allow_null=True, required=False)
    account_holder_name = serializers.CharField(allow_null=True, required=False)
    account_number = serializers.CharField(allow_null=True, required=False)
    bank_name = serializers.CharField(allow_null=True, required=False)
    cancelled_cheque_name = serializers.CharField(allow_null=True, required=False)
    cancelled_cheque = CustomFileField(allow_null=True, required=False)

    class Meta:
        dataclass = VendorInfoData


class VendorOrderListOutputV2Serializer(VendorOrderModelSerializer):
    number = serializers.SerializerMethodField()

    def get_number(self, obj):
        return self.get_order_number(obj)

    class Meta(VendorOrderModelSerializer.Meta):
        ref_name = "VendorOrderListOutputV2Serializer"
        fields = ("id", "number")


class VendorOrderDetailsOutputV2Serializer(VendorOrderModelSerializer):
    number = serializers.SerializerMethodField()
    type = serializers.CharField(source="order_type")
    order_to = IdNameSerializer(source="org_to")
    order_from = serializers.CharField(source="work_order_from")
    start_date = CustomDateField(allow_null=True, required=False, default=None, source="started_at")
    due_date = CustomDateField(allow_null=True, required=False, default=None, source="due_at")
    poc = IdNameSerializer()
    created_by = IdNameSerializer()
    issued_by = IdNameSerializer()
    amount = serializers.CharField(source="saved_total_amount")

    def get_number(self, obj):
        return self.get_order_number(obj)

    class Meta(VendorOrderModelSerializer.Meta):
        ref_name = "VendorOrderDetailsOutputV2Serializer"
        fields = (
            "id",
            "number",
            "type",
            "purchase_order_type",
            "order_to",
            "created_by",
            "created_at",
            "order_from",
            "poc",
            "shipping_address",
            "start_date",
            "due_date",
            # "vendor_tax_number",
            "status",
            "po_status",
            "issued_by",
            "issued_at",
            "amount",
            "payment_tnc",
            "other_tnc",
            "to_send_email",
        )
        output_hash_id_fields = "id"


class VendorOrderElementsListOutputV2Serializer(VendorOrderElementModelSerializer):
    class Meta(VendorOrderElementModelSerializer.Meta):
        ref_name = "VendorOrderElementsListOutputV2Serializer"
        fields = ("id", "code")


class VendorOrderElementDetailsOutputV2Serializer(VendorOrderElementModelSerializer):
    order_rate = serializers.CharField(source="vendor_rate")
    category = IdNameSerializer()
    item_type = IdNameSerializer()
    brand_make = serializers.CharField(source="brand_name")
    preview_file = serializers.FileField()

    class Meta(VendorOrderElementModelSerializer.Meta):
        ref_name = "VendorOrderElementDetailsOutputSerializer"
        fields = (
            "id",
            "name",
            "description",
            "code",
            "category",
            "item_type",
            "quantity",
            "uom",
            "client_rate",
            "order_rate",
            # "progress_update_percentage",
            "brand_make",
            "hsn_code",
            "tax_percent",
            "preview_file",
            "base_amount",
            "final_amount",
            "amount_without_tax",
        )


class VendorOrderDeductionsListOutputV2Serializer(DeductionModelSerializer):
    reference_item = serializers.CharField(source="item_reference")

    class Meta(DeductionModelSerializer.Meta):
        ref_name = "VendorOrderDeductionsListOutputV2Serializer"
        fields = ("id", "name", "type", "remark", "reference_item", "amount")


class VendorOrderDeductionDetailsOutputV2Serializer(DeductionModelSerializer):
    class AttachmentSerializer(DeductionAttachmentModelSerializer):
        class Meta(DeductionAttachmentModelSerializer.Meta):
            ref_name = "AttachmentSerializer"
            fields = ("name", "file")

    reference_item = serializers.CharField(source="item_reference")
    attachments = serializers.SerializerMethodField()

    def get_attachments(self, obj):
        return self.AttachmentSerializer(obj.attachments.all(), many=True).data

    class Meta(DeductionModelSerializer.Meta):
        ref_name = "VendorOrderDeductionDetailsOutputV2Serializer"
        fields = ("id", "name", "type", "remark", "reference_item", "amount", "attachments")


class VendorPurchaseOrderListOutputV2Serializer(VendorPurchaseOrderModelSerializer):
    number = serializers.CharField(source="po_number")

    class Meta(VendorPurchaseOrderModelSerializer.Meta):
        ref_name = "VendorPurchaseOrderListOutputV2Serializer"
        fields = ("id", "number", "uploaded_at")


class VendorPurchaseOrderDetailsOutputV2Serializer(VendorPurchaseOrderModelSerializer):
    number = serializers.CharField(source="po_number")
    file_url = serializers.FileField(source="file")
    amount_without_tax = serializers.CharField(source="amount")
    final_amount = serializers.SerializerMethodField()

    class Meta(VendorPurchaseOrderModelSerializer.Meta):
        ref_name = "VendorPurchaseOrderDetailsOutputV2Serializer"
        fields = (
            "id",
            "name",
            "number",
            "file_url",
            "uploaded_by",
            "uploaded_at",
            "po_date",
            "tax_amount",
            "amount_without_tax",
            "final_amount",
            "status",
        )


class VendorPurchaseOrderCreateInputV2Serializer(BaseSerializer):
    file_url = CustomFileField()
    number = serializers.CharField()
    name = serializers.CharField(max_length=100)
    base_amount = serializers.DecimalField(max_digits=50, decimal_places=2)
    po_date = CustomDateField()
    tax_amount = serializers.DecimalField(max_digits=50, decimal_places=2, default=0, required=False)

    class Meta:
        ref_name = "VendorPurchaseOrderCreateInputV2Serializer"
