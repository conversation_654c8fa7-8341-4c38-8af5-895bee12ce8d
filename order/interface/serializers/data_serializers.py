from rest_framework import serializers
from rest_framework.settings import api_settings

from boq.data.choices import CustomElementType
from common.choices import OrderType
from common.constants import QuantityDecimalConfig, TaxPercentageFieldConfig
from common.element_base.data_serializer import (
    ElementGuidelineDataSerializer,
    GuidelineAttachmentDataSerializer,
    PreviewFileDataSerializer,
    ProductionDrawingDataSerializer,
)
from common.element_base_serializer import ElementUomInitializer
from common.entities import ObjectStatus
from common.exceptions import QuantityDimensionsMismatchError, UOMConversionError
from common.serializers import (
    BaseDataclassSerializer,
    CustomDateField,
    CustomEndDateField,
    CustomFileField,
    HashIdField,
    HashIdListField,
)
from common.utils import validate_quantity_dimensions
from common.validators import tax_percent_validator
from core.organization.domain.entities import OrganizationSectionUpdateData
from core.tnc_config.interface.serializers import BaseTncDataSerializer
from element.domain.services import BaseElementQuantityService
from element.interface.serializers.data_serializers import (
    ElementBaseCreateDataSerializer,
    ElementRelatedBaseCreateDataSerializer,
    ElementRelatedBaseUpdateDataSerializer,
)
from order.data.choices import OrderTypeChoices
from order.data.entities import (
    DeductionAttachmentData,
    DeductionData,
    IncomingOrderCreateData,
    IncomingOrderElementCreateData,
    IncomingOrderElementImportOutputData,
    OrderCreateAndSentData,
    OrderCreateData,
    OrderElementCategoryData,
    OrderElementCreateDataV2,
    OrderElementExcelImportData,
    OrderElementImportOutputData,
    OrderElementItemTypeData,
    OrderElementProductionDrawingTag,
    OrderElementUpdateData,
    OrderOuputData,
    OrderSentInitializeData,
    OrderUpdateAndSentData,
    OrderUpdateData,
    OrgTo,
    OtherTnCDataEntity,
    PaymentTnCDataEntity,
    ProposalOrderElementImportOutputData,
    TermsAndConditionsAttachmentsData,
    TermsAndConditionsAttachmentsUpdateData,
    VendorOrderDataForExcelSheet,
    WorkOrderFrom,
)
from order.data.models import TermsAndConditionAttachment
from order.interface.vendor_serializers import ExcelAttachmentSerializer


class DeductionAttachmentDataSerializer(BaseDataclassSerializer):
    file = CustomFileField()
    id = HashIdField(required=False, allow_null=True)

    class Meta:
        dataclass = DeductionAttachmentData
        ref_name = "DeductionAttachmentDataSerializer"


class DeductionDataSerializer(BaseDataclassSerializer):
    id = HashIdField(required=False, allow_null=True)
    attachments = DeductionAttachmentDataSerializer(many=True, required=False, allow_null=True)

    class Meta:
        dataclass = DeductionData


class TermsAndConditonsAttachmentsDataSerializer(BaseDataclassSerializer):
    file = CustomFileField()
    name = serializers.CharField()
    type = serializers.ChoiceField(choices=TermsAndConditionAttachment.TYPE_CHOICES, default=None)

    class Meta:
        dataclass = TermsAndConditionsAttachmentsData
        ref_name = "TermsAndConditionsDataSerializer"


class TermsAndConditonsAttachmentsUpdateDataSerializer(TermsAndConditonsAttachmentsDataSerializer):
    id = HashIdField(required=False, allow_null=True)

    class Meta:
        dataclass = TermsAndConditionsAttachmentsUpdateData
        ref_name = "TermsAndConditionsAttachmentUpdateDataSerializer"


class OrderBaseElementDataSerializer(ElementBaseCreateDataSerializer):
    id = HashIdField(required=False, allow_null=True)
    quantity = serializers.DecimalField(max_digits=11, decimal_places=4, default=0)
    quantity_dimensions = serializers.JSONField(allow_null=True, required=False, default=None)
    vendor_rate = serializers.DecimalField(max_digits=20, decimal_places=4, default=0)
    boq_element_id = HashIdField(required=False, allow_null=True)
    el_element_id = HashIdField(required=False, allow_null=True)
    custom_type = serializers.ChoiceField(required=False, allow_null=True, choices=CustomElementType.choices)
    serial_number = serializers.IntegerField(required=False, allow_null=True)
    cancelled_element_id = HashIdField(required=False, allow_null=True)
    linked_element_id = HashIdField(required=False, allow_null=True)
    client_id = HashIdField()
    brand_name = serializers.CharField(allow_blank=True, allow_null=False, default="", required=False)
    hsn_code = serializers.CharField(allow_blank=True, allow_null=False, default="", required=False)


class OrderElementCreateDataSerializer(OrderBaseElementDataSerializer, ElementRelatedBaseCreateDataSerializer):
    def validate(self, attrs):
        try:
            validate_quantity_dimensions(
                quantity_dimensions=attrs.quantity_dimensions, quantity=attrs.quantity, quantity_uom=attrs.uom
            )
        except QuantityDimensionsMismatchError as e:
            raise serializers.ValidationError({"quantity_dimensions": e.message})
        except UOMConversionError as e:
            raise serializers.ValidationError({"quantity_dimensions": e.message})
        return super().validate(attrs)

    class Meta:
        dataclass = OrderElementCreateDataV2


class IncomingOrderElementCreateDataSerializer(OrderBaseElementDataSerializer, ElementRelatedBaseCreateDataSerializer):
    boq_element_version = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        dataclass = IncomingOrderElementCreateData


class OrderElementUpdateDataSerializer(OrderBaseElementDataSerializer, ElementRelatedBaseUpdateDataSerializer):
    def validate(self, attrs):
        try:
            validate_quantity_dimensions(
                quantity_dimensions=attrs.quantity_dimensions, quantity=attrs.quantity, quantity_uom=attrs.uom
            )
        except QuantityDimensionsMismatchError as e:
            raise serializers.ValidationError({"quantity_dimensions": e.message})
        except UOMConversionError as e:
            raise serializers.ValidationError({"quantity_dimensions": e.message})
        return super().validate(attrs)

    class Meta:
        dataclass = OrderElementUpdateData


class OtherTnCDataSerializer(BaseTncDataSerializer):
    id = HashIdField(allow_null=True)

    class Meta:
        dataclass = OtherTnCDataEntity


class PaymentTnCDataSerializer(BaseTncDataSerializer):
    id = HashIdField(allow_null=True)

    class Meta:
        dataclass = PaymentTnCDataEntity


class OrderBaseDataSerializer(BaseDataclassSerializer):
    org_to_id = HashIdField(required=False, allow_null=True)
    org_from_id = HashIdField()
    terms_and_conditions_attachments = TermsAndConditonsAttachmentsDataSerializer(
        many=True, required=False, allow_null=True
    )
    started_at = CustomDateField(required=False, allow_null=True)
    due_at = CustomEndDateField(required=False, allow_null=True)
    shipping_address_header = serializers.CharField(required=False, allow_null=True)
    shipping_address = serializers.CharField(required=False, allow_null=True)
    rate_contract_id = HashIdField(required=False, allow_null=True)
    deductions = DeductionDataSerializer(many=True, required=False, allow_null=True)
    payment_tnc = PaymentTnCDataSerializer(required=False, allow_null=True)
    other_tnc = OtherTnCDataSerializer(required=False, allow_null=True)
    poc_id = HashIdField(required=False, allow_null=True, default=None)
    type_of_order = serializers.CharField(required=False, allow_null=True, default=OrderTypeChoices.REGULAR.value)
    document_config_data = OrganizationSectionUpdateData.drf_serializer(allow_null=True, default={})

    def validate(self, data):
        org_to_id = data.org_to_id

        if org_to_id and org_to_id == self.context.get("org_from_id"):
            raise serializers.ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Client and Vendor can not be same"})

        return super().validate(data)


class OrderCreateDataSerializer(OrderBaseDataSerializer):
    org_from_id = HashIdField()
    order_type = serializers.ChoiceField(choices=OrderType.choices)
    elements = OrderElementCreateDataSerializer(many=True)
    purchase_orders = HashIdListField(required=False, allow_null=True)
    work_order_from = serializers.CharField(required=False, allow_null=True)
    origin_org_id = serializers.CharField(required=False, allow_null=True, default=None)
    type_of_order = serializers.CharField(required=False, allow_null=True, default=OrderTypeChoices.REGULAR.value)
    is_taxed = serializers.BooleanField(required=False, allow_null=True, default=False)

    # is_new_vendor = serializers.BooleanField(required=False, allow_null=True, default=False)

    class Meta:
        dataclass = OrderCreateData


class IncomingOrderCreateDataSerializer(OrderBaseDataSerializer):
    org_from_id = HashIdField(required=False, allow_null=True)
    order_type = serializers.ChoiceField(choices=OrderType.choices)
    elements = IncomingOrderElementCreateDataSerializer(many=True)
    purchase_orders = HashIdListField(required=False, allow_null=True)

    class Meta:
        dataclass = IncomingOrderCreateData


class OrderUpdateDataSerializer(OrderBaseDataSerializer):
    terms_and_conditions_attachments = TermsAndConditonsAttachmentsUpdateDataSerializer(
        many=True, required=False, allow_null=True
    )
    elements = OrderElementUpdateDataSerializer(many=True, required=False, allow_null=True)
    org_from_id = HashIdField(required=False, allow_null=True)
    work_order_from = serializers.CharField(required=False, allow_null=True)
    final_amount: serializers.DecimalField(max_digits=50, decimal_places=4)
    origin_org_id = serializers.CharField(required=False, allow_null=True, default=None)
    is_taxed = serializers.BooleanField(required=False, allow_null=True, default=False)

    class Meta:
        dataclass = OrderUpdateData


class OrderElementProductionDrawingTagsDataSerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = OrderElementProductionDrawingTag


class OrderElementGuidelineAttachmentOutputDataSerializer(GuidelineAttachmentDataSerializer):
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.FileField()


class OrderElementPreviewFileOutputDataSerializer(PreviewFileDataSerializer):
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.FileField()


class OrderElementPreviewFileModelToDictSerializer(OrderElementPreviewFileOutputDataSerializer):
    file = serializers.CharField()


class OrderElementGuidelineOutputDataSerializer(ElementGuidelineDataSerializer):
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    attachments = OrderElementGuidelineAttachmentOutputDataSerializer(many=True, allow_null=True)


class OrderElementGuidelineAttachmentModelToDictDataSerializer(GuidelineAttachmentDataSerializer):
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.CharField()


class OrderElementGuidelineModelToDictSerializer(OrderElementGuidelineOutputDataSerializer):
    attachments = OrderElementGuidelineAttachmentModelToDictDataSerializer(many=True)


class OrderElementProductionDrawingOutputDataSerializer(ProductionDrawingDataSerializer):
    tags = OrderElementProductionDrawingTagsDataSerializer(many=True, allow_null=True)
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.FileField()


class OrderElementProductionDrawingModelToDictDataSerializer(ProductionDrawingDataSerializer):
    tags = serializers.SerializerMethodField()
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.CharField()

    def get_tags(self, obj):
        return [tag.id for tag in obj.tags.all()]


class OrderElementProductionDrawingDictToEntityDataSerializer(ProductionDrawingDataSerializer):
    tags = serializers.ListField()
    object_status = serializers.CharField(default=ObjectStatus.ADD.value)
    file = serializers.CharField()


class OrderElementCategoryDataSerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = OrderElementCategoryData


class OrderElementItemTypeDataSerializer(BaseDataclassSerializer):
    id = HashIdField()

    class Meta:
        dataclass = OrderElementItemTypeData


class OrderElementElementRelatedBaseCreateDataSerializer(BaseDataclassSerializer, ElementUomInitializer):
    category_id = HashIdField()
    item_type_id = HashIdField()
    client_id = HashIdField()
    uom = serializers.SerializerMethodField()
    cancelled_element_id = HashIdField(allow_null=True, required=False)
    boq_element_id = HashIdField(required=False, allow_null=True)
    el_element_id = HashIdField(required=False, allow_null=True)
    preview_files = OrderElementPreviewFileOutputDataSerializer(many=True, allow_null=True)
    production_drawings = OrderElementProductionDrawingOutputDataSerializer(allow_null=True, many=True)
    guidelines = OrderElementGuidelineOutputDataSerializer(allow_null=True, many=True)
    category = OrderElementCategoryDataSerializer(required=False, allow_null=True)
    item_type = OrderElementItemTypeDataSerializer(required=False, allow_null=True)
    quantity_dimensions = serializers.SerializerMethodField()

    def get_quantity_dimensions(self, obj):
        return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

    def get_uom(self, obj):
        return {"value": obj.uom, "name": ElementUomInitializer().uom_name_get(uom_id=obj.uom)}


class OrderElementRelatedSyncModelToDictDataSerializer(BaseDataclassSerializer):
    category_id = HashIdField(required=False, allow_null=True)
    item_type_id = HashIdField(required=False, allow_null=True)
    client_id = HashIdField()
    cancelled_element_id = HashIdField(allow_null=True, required=False)
    boq_element_id = HashIdField(required=False, allow_null=True)
    el_element_id = HashIdField(required=False, allow_null=True)
    preview_files = OrderElementPreviewFileModelToDictSerializer(many=True, allow_null=True)
    production_drawings = OrderElementProductionDrawingModelToDictDataSerializer(allow_null=True, many=True)
    guidelines = OrderElementGuidelineModelToDictSerializer(allow_null=True, many=True)


class OrderElementRelatedSyncDictToEntityDataSerializerV2(BaseDataclassSerializer):
    category_id = HashIdField(required=False, allow_null=True)
    item_type_id = HashIdField(required=False, allow_null=True)
    client_id = HashIdField()
    cancelled_element_id = HashIdField(allow_null=True, required=False)
    boq_element_id = HashIdField(required=False, allow_null=True)
    el_element_id = HashIdField(required=False, allow_null=True)
    preview_files = OrderElementPreviewFileModelToDictSerializer(many=True, allow_null=True)
    production_drawings = OrderElementProductionDrawingDictToEntityDataSerializer(allow_null=True, many=True)
    guidelines = OrderElementGuidelineModelToDictSerializer(allow_null=True, many=True)


class OrderElementOutputDataSerializer(OrderElementElementRelatedBaseCreateDataSerializer):
    quantity = serializers.DecimalField(max_digits=11, decimal_places=4)
    discounted_client_rate = serializers.DecimalField(max_digits=15, decimal_places=2)

    class Meta:
        dataclass = OrderElementImportOutputData


class OrderElementExportOutputDataSerializer(OrderElementElementRelatedBaseCreateDataSerializer):
    quantity_dimensions = serializers.JSONField(allow_null=True, required=False, default=None)

    class Meta:
        dataclass = OrderElementExcelImportData


class IncomingOrderElementOutputDataSerializer(OrderElementElementRelatedBaseCreateDataSerializer):
    linked_element_id = HashIdField()

    class Meta:
        dataclass = IncomingOrderElementImportOutputData


class OrderCreateAndSentDataSerializer(OrderCreateDataSerializer):
    org_to_id = HashIdField()
    started_at = CustomDateField()
    due_at = CustomEndDateField()
    shipping_address = serializers.CharField()
    to = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True, default=[])
    cc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True, default=[])
    bcc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True, default=[])
    attachments = ExcelAttachmentSerializer(many=True, allow_null=True, required=False, default=[])
    body = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    rate_contract_id = HashIdField(required=False, allow_null=True)
    proposal_id = HashIdField(required=False, allow_null=True)
    final_amount = serializers.DecimalField(max_digits=50, decimal_places=4)
    quantity_dimensions = serializers.JSONField(allow_null=True, required=False, default=None)
    is_taxed = serializers.BooleanField(required=False, allow_null=True, default=False)

    class Meta:
        dataclass = OrderCreateAndSentData


class OrderUpdateAndSentDataSerializer(OrderUpdateDataSerializer):
    org_to_id = HashIdField()
    org_from_id = HashIdField()
    started_at = CustomDateField()
    due_at = CustomEndDateField()
    shipping_address_header = serializers.CharField()
    shipping_address = serializers.CharField()
    cc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True, default=[])
    bcc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True, default=[])
    attachments = ExcelAttachmentSerializer(many=True, allow_null=True, required=False, default=[])
    body = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    rate_contract_id = HashIdField(required=False, allow_null=True)
    final_amount = serializers.DecimalField(max_digits=50, decimal_places=4)
    origin_org_id = serializers.CharField(required=False, allow_null=True, default=None)

    class Meta:
        dataclass = OrderUpdateAndSentData


class OrderSentInitializerDataSerializer(BaseDataclassSerializer):
    org_to_id = HashIdField()

    class Meta:
        dataclass = OrderSentInitializeData


class ProposalElementOutputDataSerializer(BaseDataclassSerializer):
    client_id = HashIdField()
    category_id = HashIdField()
    item_type_id = HashIdField()
    boq_element_id = HashIdField()
    el_element_id = HashIdField()
    linked_element_id = HashIdField()
    cancelled_element_id = HashIdField()
    category = OrderElementCategoryDataSerializer()
    item_type = OrderElementItemTypeDataSerializer()
    preview_files = OrderElementPreviewFileOutputDataSerializer(many=True, allow_null=True)
    production_drawings = OrderElementProductionDrawingOutputDataSerializer(allow_null=True, many=True)
    guidelines = OrderElementGuidelineOutputDataSerializer(allow_null=True, many=True)
    quantity_dimensions = serializers.JSONField(allow_null=True, required=False, default=None)

    class Meta:
        dataclass = ProposalOrderElementImportOutputData


class WorkOrderFromDataSerializer(BaseDataclassSerializer):
    organization_id = HashIdField()

    class Meta:
        dataclass = WorkOrderFrom


class OrgToDataSerializer(BaseDataclassSerializer):
    organization_id = HashIdField()

    class Meta:
        dataclass = OrgTo


class OrderOutputDataSerializer(BaseDataclassSerializer):
    org_from_id = HashIdField()
    org_to_id = HashIdField()
    origin_org_id = HashIdField()
    started_at = serializers.DateTimeField()
    due_at = serializers.DateTimeField()
    order_elements = ProposalElementOutputDataSerializer(many=True)
    rate_contract_id = HashIdField(required=False, allow_null=True)
    work_order_from = WorkOrderFromDataSerializer(many=True)
    proposal_id = HashIdField()
    org_to = OrgToDataSerializer()

    class Meta:
        dataclass = OrderOuputData


class VendorOrderElementUpdateExcelDataSerializer(BaseDataclassSerializer):
    vendor_rate = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    brand_name = serializers.CharField(allow_null=False, allow_blank=True, required=False, default="")
    hsn_code = serializers.CharField(allow_null=False, allow_blank=True, required=False, default="")
    tax_percent = serializers.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        validators=[tax_percent_validator],
        required=True,
    )
    quantity = serializers.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS, decimal_places=QuantityDecimalConfig.DECIMAL_PLACES, required=True
    )

    class Meta:
        dataclass = VendorOrderDataForExcelSheet
