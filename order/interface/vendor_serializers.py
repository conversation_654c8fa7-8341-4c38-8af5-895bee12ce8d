import decimal
import textwrap

from bs4 import BeautifulSoup
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from boq.domain.constants import (
    ELEMENT_DRAFT_ORDER_AMOUNT_GREATER_THAN_BOQ_AMOUNT,
    ELEMENT_REMAINING_ORDER_AMOUNT_GREATER_THAN_BOQ_AMOUNT,
)
from boq.interface.serializers import BoqElementModelSerializer
from common.constants import BudgetRateDecimalConfig, HsnCodeFieldConfig, TaxPercentageFieldConfig
from common.element_base.services import ElementCodeService
from common.element_base_serializer import OrderElementBaseModelSerializer
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer
from common.pydantic.base_model import BaseModel
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    <PERSON><PERSON>ate<PERSON><PERSON>,
    CustomEnd<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ield,
)
from common.utils import padding_for_serial_number
from core.organization.domain.entities import OrganizationSectionUpdateData
from core.serializers import (
    OrganizationModelSerializer,
    OrganizationOrderPaymentTermSerializer,
    ProductionDrawingTagSerializer,
    UserModelSerializer,
)
from element.domain.services import BaseElementQuantityService
from element.interface.serializers.serializers import (
    ElementCategoryModelSerializer,
    ElementItemTypeModelSerializer,
)
from order.data.choices import OrderStatusChoices
from order.data.models import (
    Deduction,
    DeductionAttachment,
    OrderReview,
    OrderReviewDocument,
    TermsAndConditionAttachment,
    VendorOrder,
    VendorOrderElement,
    VendorOrderElementGuideline,
    VendorOrderElementGuidelineAttachment,
    VendorOrderElementPreviewFile,
    VendorOrderElementProductionDrawing,
    VendorOrderFieldHistory,
    VendorPurchaseOrder,
)
from order.domain.entities.domain_entities import ExcelAttachmentData
from order.domain.mappings import VENDOR_WISE_SCOPE_FIELDS_MAPPING
from order.domain.status_choices import OrderStatus, POStatus
from order.interface.constants import old_shipping_address_helper
from project.data.models import Store
from project.serializers import BaseFieldHistoryModelSerializer, ProjectModelSerializer
from ratecontract.serializers.model_serializers import RateContractModelSerializer
from rollingbanners.storage_backends import PublicMediaFileStorage
from vendor.interface.serializers import VendorCategorySerializer, VendorModelSerializer


class VendorOrderInitializeSerializer(BaseSerializer):
    class UserSerailizer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "UserOutput"
            fields = ["id", "name"]

    class WorkOrderFromSerializer(BaseSerializer):
        organization_id = HashIdField()
        name = serializers.CharField()

        class Meta:
            pass

    job_id = serializers.CharField()
    shipping_address = serializers.ListField()
    work_order_from = WorkOrderFromSerializer(many=True)
    order_created_on = serializers.DateTimeField()
    user = UserSerailizer()

    class Meta:
        pass


class IncomingOrderInitializeSerializer(VendorOrderInitializeSerializer):
    class VendorSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            fields = ("id", "name", "code")
            ref_name = "IncommingOrderVendorOutput"

    vendor = VendorSerializer()

    class Meta(VendorOrderInitializeSerializer.Meta):
        pass


class VendorOrderElementPreviewFileModelSerializer(BaseModelSerializer):
    class Meta:
        model = VendorOrderElementPreviewFile
        fields = "__all__"


class VendorOrderElementProductionDrawingModelSerializer(BaseModelSerializer):
    tags = ProductionDrawingTagSerializer(many=True)

    class Meta:
        model = VendorOrderElementProductionDrawing
        fields = "__all__"


class VendorOrderElementGuidelineAttachmentModelSerializer(BaseModelSerializer):
    class Meta:
        model = VendorOrderElementGuidelineAttachment
        fields = "__all__"


class VendorOrderElementGuidelineModelSerializer(BaseModelSerializer):
    class AttachmentSerializer(VendorOrderElementGuidelineAttachmentModelSerializer):
        id = serializers.CharField()

        class Meta(VendorOrderElementGuidelineAttachmentModelSerializer.Meta):
            ref_name = "custom-element-guideline-attachment"
            fields = ("id", "file", "type", "name")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    attachments = AttachmentSerializer(many=True)

    class Meta:
        model = VendorOrderElementGuideline
        fields = "__all__"


class VendorOrderElementModelSerializer(OrderElementBaseModelSerializer):
    class CategorySerializer(ElementCategoryModelSerializer):
        class Meta(ElementCategoryModelSerializer.Meta):
            ref_name = "vendor-order-element-category"
            fields = ("id", "name", "code")
            output_hash_id_fields = ("id",)

    class ItemTypeSerializer(ElementItemTypeModelSerializer):
        class Meta(ElementItemTypeModelSerializer.Meta):
            ref_name = "item-type"
            fields = ("id", "name", "color_code")
            output_hash_id_fields = ("id",)

    class ProductionDrawingSerializer(VendorOrderElementProductionDrawingModelSerializer):
        id = serializers.CharField()

        class Meta(VendorOrderElementProductionDrawingModelSerializer.Meta):
            ref_name = "custom-element-drawing"
            fields = ("id", "file", "tags", "name", "uploaded_at")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    class PreviewFileSerializer(VendorOrderElementPreviewFileModelSerializer):
        class Meta(VendorOrderElementPreviewFileModelSerializer.Meta):
            ref_name = "vendor-order-element-preview-file"
            fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
            output_hash_id_fields = ("id",)

    class ElementGuidelineSerializer(VendorOrderElementGuidelineModelSerializer):
        class Meta(VendorOrderElementGuidelineModelSerializer.Meta):
            fields = ("id", "name", "description", "created_at", "updated_at", "attachments")
            ref_name = "VendorOrderElementModelElementGuidelineSerializer"

    status = serializers.SerializerMethodField()
    code = serializers.SerializerMethodField()
    item_type = ItemTypeSerializer()
    production_drawings = ProductionDrawingSerializer(many=True)
    category = CategorySerializer()
    preview_files = PreviewFileSerializer(many=True)
    guidelines = ElementGuidelineSerializer(many=True)
    preview_file_url = serializers.SerializerMethodField()
    amount_without_tax = serializers.SerializerMethodField()

    def get_amount_without_tax(self, obj):
        return obj.get_amount_without_tax() if obj.get_amount_without_tax() else 0

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        return OrderStatus.get_display_status(obj.status)

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_code(self, obj: VendorOrderElement):
        return ElementCodeService.get_code(
            serial_number=obj.serial_number, custom_type=obj.custom_type, code=obj.code, version=obj.boq_element_version
        )

    @swagger_serializer_method(serializer_or_field=serializers.URLField())
    def get_preview_file_url(self, obj):
        if hasattr(obj, "preview_file_url") and obj.preview_file_url:
            return PublicMediaFileStorage.url(obj.preview_file_url)

    class Meta:
        model = VendorOrderElement
        fields = "__all__"


class CustomVendorOrderElementModelSerializer(OrderElementBaseModelSerializer):
    class CategorySerializer(ElementCategoryModelSerializer):
        class Meta(ElementCategoryModelSerializer.Meta):
            ref_name = "custom-vendor-order-element-category"
            fields = ("id", "name")
            output_hash_id_fields = ("id",)

    class PreviewFileSerializer(VendorOrderElementPreviewFileModelSerializer):
        id = serializers.CharField()

        class Meta(VendorOrderElementPreviewFileModelSerializer.Meta):
            ref_name = "custom-vendor-order-element-preview-file"
            fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
            output_hash_id_fields = ("id",)
            input_hash_id_fields = ("id",)

    class ItemTypeSerializer(ElementItemTypeModelSerializer):
        class Meta(ElementItemTypeModelSerializer.Meta):
            ref_name = "item-type"
            fields = ("id", "name", "color_code")
            output_hash_id_fields = ("id",)

    class ProductionDrawingSerializer(VendorOrderElementProductionDrawingModelSerializer):
        id = serializers.CharField()

        class Meta(VendorOrderElementProductionDrawingModelSerializer.Meta):
            ref_name = "custom-element-drawing"
            fields = ("id", "file", "tags", "name", "uploaded_at")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    class GuidelineSerializer(VendorOrderElementGuidelineModelSerializer):
        id = serializers.CharField()

        class Meta(VendorOrderElementGuidelineModelSerializer.Meta):
            ref_name = "custom-element-guideline"
            fields = ("id", "description", "attachments", "name")
            input_hash_id_fields = ("id",)
            output_hash_id_fields = ("id",)

    preview_files = PreviewFileSerializer(many=True)
    category = CategorySerializer()

    item_type = ItemTypeSerializer()
    guidelines = GuidelineSerializer(many=True)
    production_drawings = ProductionDrawingSerializer(many=True)

    class Meta:
        model = VendorOrderElement
        fields = "__all__"


class VendorPurchaseOrderModelSerializer(BaseModelSerializer):
    class UserSerailizer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "UserOutputPO"
            fields = ("id", "name")
            output_hash_id_fields = ("id",)

    uploaded_by = UserSerailizer()
    po_date = CustomDateField()

    def get_final_amount(self, obj):
        if hasattr(obj, "tax_amount") and obj.tax_amount and hasattr(obj, "amount") and obj.amount:
            return str(obj.amount + obj.tax_amount)
        return "0"

    class Meta:
        model = VendorPurchaseOrder
        fields = "__all__"


class TermsAndConditionsAttachmentModelSerializer(BaseModelSerializer):
    class Meta:
        model = TermsAndConditionAttachment
        fields = "__all__"


class DeductionModelSerializer(BaseModelSerializer):
    code = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    @staticmethod
    def get_code(obj):
        code = padding_for_serial_number(serial_number=obj.code, padding=3)
        return f"DED{code}"

    class Meta:
        model = Deduction
        fields = "__all__"


class DeductionAttachmentModelSerializer(BaseModelSerializer):
    class Meta:
        model = DeductionAttachment
        fields = "__all__"


class BaseTncSerializer(BaseSerializer):
    id = HashIdField()
    title = serializers.CharField()
    block = TypeOutputSerializer(many=True)

    class Meta:
        ref_name = "payment-tnc"


class VendorOrderModelSerializer(BaseModelSerializer):
    class PaymentTermSerializer(OrganizationOrderPaymentTermSerializer):
        class Meta(OrganizationOrderPaymentTermSerializer.Meta):
            ref_name = "payment_term"
            fields = (
                "id",
                "title",
                "description",
                "is_active",
            )
            output_hash_id_fields = ("id",)

    class DeductionSerializer(DeductionModelSerializer):
        class DeductionAttachmentSerializer(DeductionAttachmentModelSerializer):
            uploaded_by = serializers.SerializerMethodField()

            @swagger_serializer_method(serializer_or_field=serializers.CharField())
            def get_uploaded_by(self, obj):
                return obj.uploaded_by.name

            class Meta(DeductionAttachmentModelSerializer.Meta):
                ref_name = "deduction-attachment"
                fields = ("id", "file", "name", "uploaded_at", "uploaded_by")
                output_hash_id_fields = ("id",)

        attachments = DeductionAttachmentSerializer(many=True)
        created_by = serializers.SerializerMethodField()
        updated_by = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_created_by(self, obj):
            return obj.created_by.name

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_updated_by(self, obj):
            if obj.updated_by:
                return obj.updated_by.name
            return None

        class Meta(DeductionModelSerializer.Meta):
            ref_name = "deduction"
            fields = (
                "id",
                "name",
                "amount",
                "tax_amount",
                "type",
                "remark",
                "code",
                "item_reference",
                "type_color_code",
                "created_at",
                "updated_at",
                "created_by",
                "updated_by",
                "attachments",
            )
            output_hash_id_fields = ("id",)

    class VendorOrderElementSerializer(VendorOrderElementModelSerializer):
        comment_count = serializers.SerializerMethodField()
        progress_percentage = serializers.IntegerField()
        preview_file = serializers.SerializerMethodField()
        rc_rate = serializers.DecimalField(max_digits=11, decimal_places=2, required=False, allow_null=True)
        el_element_id = HashIdField()
        boq_element_id = HashIdField()
        in_progress = serializers.SerializerMethodField()
        quantity_dimensions = serializers.SerializerMethodField()
        gross_amount = serializers.SerializerMethodField()
        final_amount = serializers.SerializerMethodField()
        tax_percent = serializers.DecimalField(max_digits=11, decimal_places=2, required=False, allow_null=True)
        error = serializers.SerializerMethodField()
        is_error_found = serializers.SerializerMethodField()

        def get_is_error_found(self, obj):
            if hasattr(obj, "is_error_found"):
                return obj.is_error_found
            return False

        def get_error(self, obj):
            if (
                hasattr(obj, "is_remaining_order_amount_greater_than_boq_amount")
                and obj.is_remaining_order_amount_greater_than_boq_amount
            ):
                return ELEMENT_REMAINING_ORDER_AMOUNT_GREATER_THAN_BOQ_AMOUNT
            elif (
                hasattr(obj, "is_draft_order_amount_greater_than_boq_amount")
                and obj.is_draft_order_amount_greater_than_boq_amount
            ):
                return ELEMENT_DRAFT_ORDER_AMOUNT_GREATER_THAN_BOQ_AMOUNT
            return None

        def get_gross_amount(self, obj):
            if hasattr(obj, "gross_amount_annotation") and obj.gross_amount_annotation:
                return obj.gross_amount_annotation
            return 0

        def get_final_amount(self, obj):
            if hasattr(obj, "element_final_amount") and obj.element_final_amount:
                return obj.element_final_amount
            return 0

        @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
        def get_in_progress(self, obj):
            if obj.progress_percentage and 0 < obj.progress_percentage < 100:
                return True
            return False

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_comment_count(self, obj):
            return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

        @swagger_serializer_method(serializer_or_field=serializers.FileField())
        def get_preview_file(self, obj):
            return (
                PublicMediaFileStorage.url(obj.preview_file)
                if hasattr(obj, "preview_file") and obj.preview_file
                else None
            )

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        class Meta(VendorOrderElementModelSerializer.Meta):
            ref_name = "OrderElementOutput"
            fields = (
                "id",
                "name",
                "code",
                "category",
                "description",
                "uom",
                "quantity",
                "quantity_dimensions",
                "item_type",
                "vendor_rate",
                "amount",
                "tax_percent",
                "status",
                "client_rate",
                "budget_rate",
                "discounted_client_rate",
                "comment_count",
                "progress_percentage",
                "preview_file",
                "custom_type",
                "rc_rate",
                "el_element_id",
                "boq_element_id",
                "in_progress",
                "base_amount",
                "gross_amount",
                "service_charge_percent",
                "is_service_charge_with_base_amount",
                "discount_percent",
                "discounted_value",
                "final_amount",
                "brand_name",
                "hsn_code",
                "amount_without_tax",
                "serial_number",
                "error",
                "is_error_found",
                "boq_element_id",
                "boq_element_version",
            )

    class VendorSerializer(VendorModelSerializer):
        name = serializers.CharField()
        code = serializers.CharField(source="vendor.code")
        categories = VendorCategorySerializer(many=True, source="vendor.category")
        location = serializers.CharField(source="vendor.location")

        class Meta(VendorModelSerializer.Meta):
            fields = ("id", "name", "code", "categories", "location")
            output_hash_id_fields = ("id",)

    class UserSerailizer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "UserOutput"
            fields = ("id", "name", "photo")
            output_hash_id_fields = ("id",)

    class VendorPurchaseOrderSerializer(VendorPurchaseOrderModelSerializer):
        status = serializers.SerializerMethodField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_status(self, obj):
            return POStatus.get_display_status(obj.status)

        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            return approved_amount

        class Meta(VendorPurchaseOrderModelSerializer.Meta):
            fields = (
                "id",
                "name",
                "po_number",
                "uploaded_at",
                "file",
                "amount",
                "tax_amount",
                "status",
                "type",
                "po_date",
                "version",
                "uploaded_by",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
            )
            output_hash_id_fields = ("id",)
            input_hash_id_fields = ("id",)

    class ProjectSerializer(ProjectModelSerializer):
        class Meta(ProjectModelSerializer.Meta):
            ref_name = "ProjectInputOutput"
            fields = ("id", "name", "job_id")
            output_hash_id_fields = ("id",)

    class TermsAndConditionAttachmentSerializer(TermsAndConditionsAttachmentModelSerializer):
        class Meta(TermsAndConditionsAttachmentModelSerializer.Meta):
            fields = ("id", "file", "name", "type")
            output_hash_id_fields = ("id",)

    class OrganizationSerializer(OrganizationModelSerializer):
        organization_id = HashIdField(source="id")

        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "OrderOrganziationOutput"
            fields = ("organization_id", "name")

    class OutgoingOrgSerializer(OrganizationModelSerializer):
        organization_id = HashIdField(source="id")
        name = serializers.CharField()
        code = serializers.CharField(source="vendor.code")
        country_id = HashIdField()
        categories = VendorCategorySerializer(many=True, source="vendor.category")

        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "OutgoingOrganziationOutput"
            fields = ("organization_id", "name", "code", "country_id", "categories")

    class RateContractSerializer(RateContractModelSerializer):
        class Meta(RateContractModelSerializer.Meta):
            ref_name = "RateContractOutput"
            fields = ("id", "name", "element_count", "status")

    project = ProjectSerializer(required=False)
    org_from = OrganizationSerializer()
    org_to = OutgoingOrgSerializer()
    origin_org = OrganizationSerializer()
    purchase_orders = VendorPurchaseOrderSerializer(required=False, many=True)
    order_elements = VendorOrderElementSerializer(many=True)
    cancelled_elements_count = serializers.SerializerMethodField()
    not_cancelled_elements_count = serializers.SerializerMethodField()
    vendor = VendorSerializer(source="org_to")
    created_by = UserSerailizer()
    issued_by = serializers.SerializerMethodField()
    issued_at = serializers.SerializerMethodField()
    updated_by = UserSerailizer(allow_null=True)
    sent_at = serializers.SerializerMethodField()
    po_attached_at = serializers.SerializerMethodField(allow_null=True)
    attachments = TermsAndConditionAttachmentSerializer(many=True)
    status = serializers.SerializerMethodField()
    invoice_status = serializers.SerializerMethodField()
    po_status = serializers.SerializerMethodField()
    order_number = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    shipping_address = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()
    rate_contract = RateContractSerializer()
    deductions = DeductionSerializer(many=True)
    actions = serializers.SerializerMethodField()
    type_of_order = serializers.CharField(source="order_type")
    amount_without_tax = serializers.SerializerMethodField()
    payment_tnc = serializers.SerializerMethodField()
    other_tnc = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=BaseTncSerializer())
    def get_payment_tnc(self, obj):
        if obj.payment_term_id is None and obj.payment_term_title is None and not obj.payment_term_block:
            return None
        payment_term_title = obj.payment_term_title
        if not payment_term_title:
            payment_term_title = "Custom Payment Terms"
        return {
            "id": HashIdField().to_representation(obj.payment_term_id) if obj.payment_term_id else None,
            "title": payment_term_title,
            "block": TypeOutputSerializer(
                instance=obj.payment_term_block,
                many=True,
                context={
                    "allowed_type_choices": {
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.PIN: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    },
                    "request": self.context.get("request"),
                },
            ).data,
        }

    @swagger_serializer_method(serializer_or_field=BaseTncSerializer())
    def get_other_tnc(self, obj):
        if obj.other_term_id is None and obj.other_term_title is None and not obj.other_term_block:
            return None
        other_term_title = obj.other_term_title
        if not other_term_title:
            other_term_title = "Custom Other Terms and Conditions"
        return {
            "id": HashIdField().to_representation(obj.other_term_id) if obj.other_term_id else None,
            "title": other_term_title,
            "block": TypeOutputSerializer(
                instance=obj.other_term_block,
                many=True,
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.APPROVAL: None,
                        const.REJECTED: None,
                        const.PIN: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                    },
                    "request": self.context.get("request"),
                },
            ).data,
        }

    due_at = CustomEndDateField(allow_null=True, required=False, default=None)
    started_at = CustomDateField(allow_null=True, required=False, default=None)
    created_at = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_comment_count(self, obj):
        return obj.comment_count if hasattr(obj, "comment_count") and obj.comment_count else 0

    @swagger_serializer_method(serializer_or_field=UserSerailizer())
    def get_issued_by(self, obj):
        if getattr(obj, "issued_by"):
            return self.UserSerailizer(obj.issued_by).data
        else:
            return None

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_issued_at(self, obj):
        if getattr(obj, "issued_at"):
            return obj.issued_at
        else:
            return None

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_created_at(self, obj):
        if getattr(obj, "created_at"):
            return obj.created_at
        else:
            return None

    @swagger_serializer_method(serializer_or_field=OutgoingOrgSerializer())
    def get_org_to(self, obj):
        return self.OutgoingOrgSerializer(obj.org_to).data

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_order_number(self, obj):
        return f"{obj.project.job_id}/{obj.order_number}"

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_status(self, obj):
        return dict(OrderStatusChoices.choices)[obj.outgoing_status]

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_invoice_status(self, obj):
        return obj.invoice_status.title()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_po_status(self, obj):
        return POStatus.get_display_status(obj.po_status)

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_cancelled_elements_count(self, obj):
        return obj.cancelled_elements_count

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
    def get_not_cancelled_elements_count(self, obj):
        return obj.not_cancelled_elements_count

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_sent_at(self, obj):
        if obj.status in [None, OrderStatus.PENDING]:
            # default status is pending so return created_at
            sent_at = obj.created_at
            # TODO:
        elif obj.status == OrderStatus.SENT:
            sent_at = obj.issued_at
        elif obj.status == OrderStatus.CANCELLED:
            sent_at = obj.cancelled_at
        else:
            sent_at = obj.created_at
        return sent_at

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField())
    def get_po_attached_at(self, obj):
        if obj.po_status in [None, POStatus.PO_PENDING]:
            po_attached_at = None
        if obj.po_status == POStatus.PO_ATTACHED:
            po_attached_at = obj.po_attached_at
        if obj.po_status == POStatus.PO_CANCELLED:
            po_attached_at = obj.po_cancelled_at
        return po_attached_at

    @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
    def get_amount(self, obj):
        deduction_amount = 0
        tax_amount = obj.elements_tax_amount if hasattr(obj, "elements_tax_amount") else 0
        if hasattr(obj, "deduction_amount") and obj.deduction_amount:
            deduction_amount = obj.deduction_amount
        if hasattr(obj, "elements_final_amount") and obj.elements_final_amount:
            return obj.elements_final_amount - deduction_amount + tax_amount
        if hasattr(obj, "order_amount") and obj.order_amount:
            return obj.order_amount - deduction_amount + tax_amount

        return 0

    def get_amount_without_tax(self, obj):
        deduction_amount = 0
        if hasattr(obj, "deduction_amount") and obj.deduction_amount:
            deduction_amount = obj.deduction_amount
        if hasattr(obj, "elements_final_amount") and obj.elements_final_amount:
            return obj.elements_final_amount - deduction_amount
        if hasattr(obj, "order_amount") and obj.order_amount:
            return obj.order_amount - deduction_amount

        return 0
        # if hasattr(obj, "") and obj.order_amount:
        #     if hasattr(obj, "deduction_amount") and obj.deduction_amount:
        #         return obj.order_amount - obj.deduction_amount
        #     return obj.order_amount
        # return 0

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_shipping_address(self, obj):
        if obj.shipping_address_header is None or obj.shipping_address_header == "":
            store_address = Store.objects.filter(project_id=obj.project_id).values_list("address", flat=True).first()
            shipping_addresses = old_shipping_address_helper(store_address=store_address)

            for shipping_address in shipping_addresses:
                if obj.shipping_address == shipping_address["header"]:
                    return shipping_address["address"]
            return obj.shipping_address
        else:
            return obj.shipping_address

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_actions(self, obj):
        return self.context.get("actions", [])

    class Meta:
        model = VendorOrder
        fields = "__all__"


class VendorOrderElementCreatePreviewFileBaseSerializer(BaseSerializer):
    file = serializers.URLField()
    name = serializers.CharField()
    is_main = serializers.BooleanField()
    type = serializers.ChoiceField(choices=VendorOrderElementPreviewFile.TYPE_CHOICES, default=None)

    class Meta:
        fields = "__all__"


class VendorOrderElementProductionDrawingBaseSerializer(BaseSerializer):
    file = serializers.URLField()
    name = serializers.CharField()
    tags = ProductionDrawingTagSerializer(many=True)

    class Meta:
        fields = "__all__"


class VendorOrderElementGuidelineAttachmentBaseSerializer(BaseSerializer):
    name = serializers.CharField()
    file = serializers.URLField()
    type = serializers.ChoiceField(choices=VendorOrderElementGuidelineAttachment.TYPE_CHOICES, default=None)

    class Meta:
        fields = "__all__"


class VendorOrderElementCreateGuidelineSerializer(BaseSerializer):
    name = serializers.CharField()
    description = serializers.CharField()
    attachments = VendorOrderElementGuidelineAttachmentBaseSerializer(many=True)

    class Meta:
        fields = "__all__"


class VendorOrderElementExcelRow(BaseSerializer):
    name = NameField(max_length=300, min_length=1)
    description = serializers.CharField()
    category_code = serializers.CharField(min_length=2)
    item_type = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    quantity = serializers.DecimalField(default=0, required=False, max_digits=15, decimal_places=4, allow_null=True)
    uom_title = serializers.CharField(min_length=1)
    vendor_rate = serializers.DecimalField(
        default=0, required=False, max_digits=15, decimal_places=2, allow_null=True, source="order_rate"
    )
    brand_name = serializers.CharField(required=False, allow_blank=True, allow_null=True, default="", max_length=200)
    hsn_code = serializers.CharField(
        required=False, allow_blank=True, allow_null=True, default="", max_length=HsnCodeFieldConfig.MAX_LENGTH
    )
    budget_rate = serializers.DecimalField(
        max_digits=BudgetRateDecimalConfig.MAX_DIGITS,
        decimal_places=BudgetRateDecimalConfig.DECIMAL_PLACES,
        required=False,
        allow_null=True,
        default=0,
    )
    tax_percent = serializers.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        required=False,
        allow_null=True,
        default=0,
    )
    row_num = serializers.CharField()

    def validate_uom_title(self, value):
        value = value.strip()
        if "uom_list" in self.context:
            if value.lower() not in self.context.get("uom_list"):
                raise serializers.ValidationError([_("Invalid unit of measurement.")])
        return value

    def validate_category_code(self, value):
        value = value.strip()
        if "category_list" in self.context:
            if value.upper() not in self.context.get("category_list"):
                raise serializers.ValidationError([_("Invalid element category.")])
        return value

    def validate_item_type(self, value):
        if value:
            value = value.strip()
            if "item_type_list" in self.context:
                if value.upper() not in self.context.get("item_type_list"):
                    raise serializers.ValidationError([_("Invalid element item type.")])
        return value

    def validate_tax_percent(self, tax_percent):
        tax_slabs = self.context.get("tax_slabs")
        if not tax_percent:
            return 0
        if tax_percent not in tax_slabs:
            raise serializers.ValidationError("Invalid tax percent")
        return tax_percent

    class Meta:
        pass


class DynamicFieldsModelSerializer(VendorOrderElementModelSerializer):
    """
    A ModelSerializer that takes an additional `fields` argument that
    controls which fields should be displayed.
    """

    def __init__(self, *args, **kwargs):
        # Don't pass the 'fields' arg up to the superclass
        fields = kwargs.pop("fields", None)

        # Instantiate the superclass normally
        super().__init__(*args, **kwargs)

        if fields is not None:
            # Drop any fields that are not specified in the `fields` argument.
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)


class VendorOrderElementExportSerializer(VendorOrderElementModelSerializer):
    item_name = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    item_code = serializers.SerializerMethodField()
    item_type = serializers.SerializerMethodField(required=False, allow_null=True)
    UOM = serializers.SerializerMethodField()
    Quantity = serializers.SerializerMethodField()
    final_amount = serializers.SerializerMethodField()
    Status = serializers.SerializerMethodField()
    order_rate = serializers.SerializerMethodField()
    client_rate = serializers.SerializerMethodField()
    vendor_order_element_id = serializers.IntegerField(source="id")
    remark = serializers.SerializerMethodField()
    length = serializers.SerializerMethodField()
    length_uom = serializers.SerializerMethodField()
    breadth = serializers.SerializerMethodField()
    breadth_uom = serializers.SerializerMethodField()
    tax_percent = serializers.SerializerMethodField()
    HSN = serializers.CharField(source="hsn_code")
    brand_make = serializers.SerializerMethodField()

    def get_brand_make(self, obj):
        return obj.brand_name if obj.brand_name else ""

    def get_tax_percent(self, obj):
        return round(obj.tax_percent, 2)

    def get_remark(self, obj):
        if hasattr(obj, "remark") and obj.remark:
            color = "#000000"
            if obj.remark == "Added":
                # green
                color = "#61C7CD"
            elif "Modified" in obj.remark:
                # yellow
                color = "#EA9245"
            elif obj.remark == "Cancelled":
                # red
                color = "#EC2245"
            return {"color": color, "value": obj.remark}

    def get_item_code(self, obj):
        return self.get_code(obj)

    def get_client_rate(self, obj):
        if not obj.client_rate:
            return 0
        if self.context.get("is_client_rate_visible") is False:
            return 0
        return obj.client_rate

    def get_order_rate(self, obj):
        return round(obj.vendor_rate, 2)

    def get_item_name(self, obj):
        return obj.name

    def get_Quantity(self, obj):
        return round(obj.quantity, 2)

    def get_description(self, obj):
        return obj.get_description()

    def get_UOM(self, obj):
        return self.uom_name_get(uom_id=obj.uom)

    def get_Status(self, obj):
        if obj.status == OrderStatus.CANCELLED:
            return obj.status
        if obj.status == OrderStatus.MODIFIED:
            return obj.status
        return None

    def get_item_type(self, obj):
        return obj.item_type.name if getattr(obj, "item_type") else None

    def get_final_amount(self, obj):
        if obj.status == OrderStatus.CANCELLED:
            return 0
        return obj.final_amount

    def get_discounted_value(self, obj):
        if obj.discounted_value:
            return str(round(obj.discounted_value, 2))
        return 0

    def get_length(self, obj):
        return obj.get_length()

    def get_length_uom(self, obj):
        length_uom = obj.get_length_uom()
        if length_uom:
            return self.uom_name_get(uom_id=length_uom)
        return None

    def get_breadth(self, obj):
        return obj.get_breadth()

    def get_breadth_uom(self, obj):
        breadth_uom = obj.get_breadth_uom()
        if breadth_uom:
            return self.uom_name_get(uom_id=breadth_uom)
        return None
        # return obj.get_breadth_uom()

    class Meta(VendorOrderElementModelSerializer.Meta):
        fields = (
            "item_name",
            "description",
            "brand_make",
            "item_code",
            "item_type",
            "Quantity",
            "length",
            "length_uom",
            "breadth",
            "breadth_uom",
            "UOM",
            "client_rate",
            "order_rate",
            "Status",
            "public_url",
            "vendor_order_element_id",
            "remark",
            "HSN",
            "tax_percent",
            "final_amount",
            "gross_amount",
            "service_charge_percent",
            "base_amount",
            "discount_percent",
            "discounted_value",
        )


class VendorOrderElementGuidelineExportSerializer(VendorOrderElementGuidelineModelSerializer):
    name = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()

    def get_name(self, obj):
        wrapper = textwrap.TextWrapper(width=30)
        word_list = wrapper.wrap(text=obj.name)
        return "\n".join(word_list)

    def get_description(self, obj):
        wrapper = textwrap.TextWrapper(width=100)
        word_list = wrapper.wrap(text=BeautifulSoup(obj.description, "html.parser").get_text(separator="\n"))
        return "\n".join(word_list)

    class Meta(VendorOrderElementGuidelineModelSerializer.Meta):
        fields = ("name", "description")


class ExcelAttachmentSerializer(BaseDataclassSerializer):
    name = serializers.CharField()
    url = serializers.URLField()

    class Meta:
        ref_name = "ExcelAttachmentSerializer"
        dataclass = ExcelAttachmentData


class VendorOrderElementGroupbyCategorySerializer(BaseSerializer):
    Category = serializers.CharField()
    Amount = serializers.DecimalField(max_digits=100, decimal_places=2)

    class Meta:
        pass


class OrderReviewModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrderReview
        exclude = ("order_review",)


class OrderReviewDocumentsModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrderReviewDocument
        exclude = ("created_by", "order_review", "created_at")


class UserSerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "UserOrderReview"
        fields = ("name", "photo")


class VendorElementSerializer(BoqElementModelSerializer):
    amount = serializers.DecimalField(max_digits=20, decimal_places=2)
    order_rate = serializers.DecimalField(max_digits=20, decimal_places=2)
    source = serializers.CharField(source="element_order_number")
    progress_percentage = serializers.SerializerMethodField()
    quantity = serializers.SerializerMethodField()
    hidden_fields = serializers.ListField()

    def to_representation(self, instance):
        # Local import is made due to circular import error
        from proposal.domain.services import transform_field_names

        representation = super().to_representation(instance)

        # transformed_hidden_fields are hidden fields of same name as of serializers
        transformed_hidden_fields = transform_field_names(
            field_name_mapping=VENDOR_WISE_SCOPE_FIELDS_MAPPING, fields=representation.get("hidden_fields")
        )

        # Each hidden field will be assigned value as None
        for field in transformed_hidden_fields:
            if field in representation:
                representation[field] = None

        # At last popping "hidden_fields", as now there is no use of sending it in response
        representation.pop("hidden_fields", None)
        return representation

    def get_progress_percentage(self, obj):
        return decimal.Decimal(obj.progress_percentage)

    def get_quantity(self, obj):
        return (
            str(decimal.Decimal(obj.updated_data.get("quantity")))
            if hasattr(obj, "updated_data") and obj.updated_data and obj.updated_data.get("quantity") is not None
            else str(decimal.Decimal(obj.quantity))
        )

    class OrganizationSerializer(OrganizationModelSerializer):
        class Meta(OrganizationModelSerializer.Meta):
            fields = ("id", "name")
            ref_name = "OrganizationUserSerializer"

    organization = OrganizationSerializer()  # TODO: implement this using mixins

    class Meta(BoqElementModelSerializer.Meta):
        ref_name = "VendorElementsListOutput"
        fields = (
            "id",
            "code",
            "quantity",
            "name",
            "description",
            "item_type",
            "status",
            "status_updated_at",
            "status_updated_by",
            "preview_file",
            "uom",
            "category",
            "progress_percentage",
            "organization",
            "amount",
            "source",
            "order_rate",
            "element_status",
            "status_date",
            "hidden_fields",
        )
        output_hash_id_fields = ("id",)


class RateContractRateFetchInputSerializer(BaseSerializer):
    rate_contract_id = HashIdField()
    element_ids = HashIdListField()

    class Meta:
        ref_name = "RateContractRateFetchInputSerializer"


class VendorOrderFieldHistoryModelSerializer(BaseFieldHistoryModelSerializer):
    model = VendorOrder

    class Meta:
        model = VendorOrderFieldHistory
        fields = "__all__"


class VendorOrderVendorInfoUpdateSerializer(BaseModel):
    document_config_data: OrganizationSectionUpdateData
