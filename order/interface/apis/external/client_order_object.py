from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from common.enums import RequestMethod
from common.permission_utils import TabPermissions
from common.serializers import IdNameSerializer
from order.data.repositories import OrderSnapshotRepo
from order.data.selectors.selectors import get_client_order_details_v2
from order.interface.serializers import OrderSnapshotModelSerializer, VendorOrderDetailsOutputV2Serializer
from project.interface.apis.internal.apis import ExternalProjectBaseV2Api


class ClientOrderObjectV2Api(ExternalProjectBaseV2Api):
    class VendorOrderSnapshotOutputSerializer(OrderSnapshotModelSerializer):
        number = serializers.SerializerMethodField()
        type = serializers.CharField(source="order.order_type")
        order_to = IdNameSerializer(source="order.org_to")
        order_from = serializers.CharField(source="work_order_from")
        start_date = serializers.Char<PERSON>ield(source="started_at")
        due_date = serializers.Char<PERSON>ield(source="due_at")
        poc = IdNameSerializer(source="order.poc")
        created_by = IdNameSerializer()
        issued_by = serializers.SerializerMethodField()

        def get_number(self, obj):
            return self.get_order_number(obj)

        def get_issued_by(self, obj):
            if getattr(obj.order, "issued_by"):
                return IdNameSerializer(obj.order.issued_by).data
            else:
                return None

        class Meta(OrderSnapshotModelSerializer.Meta):
            ref_name = "VendorOrderSnapshotOutputSerializer"
            fields = (
                "id",
                "number",
                "type",
                "purchase_order_type",
                "order_to",
                "created_by",
                "created_at",
                "order_from",
                "poc",
                "shipping_address",
                "start_date",
                "due_date",
                # "vendor_tax_number",
                "status",
                "po_status",
                "issued_by",
                "issued_at",
                "amount",
                "payment_tnc",
                "other_tnc",
            )
            output_hash_id_fields = "id"

    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: TabPermissions.CLIENT_ORDERS}

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorOrderDetailsOutputV2Serializer()},
        operation_id="external-client-order-details-v2",
        operation_summary="Get Client Order Details",
    )
    def get(self, request, project_id, order_id, *args, **kwargs):
        order_snapshot_repo = OrderSnapshotRepo()
        order, is_snapshot = get_client_order_details_v2(
            project_id=project_id,
            order_id=order_id,
            org_id=self.get_organization_id(),
            order_snapshot_repo=order_snapshot_repo,
        )

        if not order:
            self.set_response_message(str("Client Order not found."))
            return Response(status=HTTP_404_NOT_FOUND)

        if is_snapshot:
            output_serializer = self.VendorOrderSnapshotOutputSerializer
        else:
            output_serializer = VendorOrderDetailsOutputV2Serializer

        return Response(
            output_serializer(
                order,
                context={
                    "org_id": self.get_organization_id(),
                    "source": self.get_source(),
                    "timezone": self.get_project_timezone(),
                },
            ).data,
            status=HTTP_200_OK,
        )
