import decimal
from functools import partial
from typing import Callable, Dict, List

import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import transaction
from django.db.models import Count, DecimalField, F, Q, Sum, Value
from django.db.models.functions import Coalesce
from django.db.transaction import on_commit
from django.http import HttpResponse
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from rest_framework import serializers
from rest_framework.mixins import ListModelMixin
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from boq.data.repos import BoqElementSyncRepo
from client.domain.constants import ClientFields
from common.apis import BaseApi
from common.choices import OrderType
from common.exceptions import QuantityValidationError, UOMValidationError
from common.pydantic.model_dump import pydantic_dump
from common.serializers import (
    BaseSerializer,
    CustomDateField,
    CustomEndDateField,
    HashIdField,
    HashIdListField,
    SearchTextFilterSerializer,
)
from common.services import is_empty
from core.entities import ProjectUserEntity
from core.models import Organization
from core.organization.domain.entities import OrganizationSectionData
from core.selectors import vendor_fetch_all
from core.serializers import OrganizationModelSerializer, UserSerializer
from microcontext.choices import MicroContextActions, MicroContextChoices
from microcontext.domain.constants import MicroContext
from order.data.choices import OrderStatusChoices
from order.data.entities import OrderCreateAndSentData
from order.data.models import OrderReviewDocument, VendorOrder, VendorOrderElement
from order.data.repositories import OrderElementSyncRepo, OrderRepo, OrderRequestMappingRepo, OrderSnapshotRepo
from order.data.selectors.selector_v1 import (
    annotate_progress_percentage_details,
    get_deduction_type_remarks,
    get_deduction_types,
    get_order_detail,
    get_order_header_detail,
    get_outgoing_org_list,
    incoming_order_create_prepare_data,
    order_fetch_with_elements,
    order_list,
    order_review_list,
    outgoing_order_create_prepare_order_data,
    outgoing_org_list_fetch_v2,
)
from order.data.selectors.selectors import get_client_order_detail, get_vendor_ids_from_orders
from order.domain.constants import OrderEventEnum, VendorListFormChoices
from order.domain.entities.domain_entities import (
    MarkOrderCompleteData,
    OrderCreateData,
    OrderSentInitializeData,
    OrderUpdateData,
)
from order.domain.entities.entities import OrderRequestCreateData
from order.domain.exceptions import OrderException, OrderNotFoundException
from order.domain.factories import OrderActionServiceFactory, OrderRequestServiceFactory, OrderServiceFactory
from order.domain.mappings import ORDER_FIELDS_MAPPING
from order.domain.services.snapshot import OrderSnapshotService
from order.domain.status_choices import OrderStatus
from order.interface.constants import EXCEL_SAMPLE_COLUMNS, RESPONSE_FOR_EMAIL_SENT
from order.interface.serializers import (
    OrderCreateAndSentDataSerializer,
    OrderCreateDataSerializer,
    OrderSentInitializerDataSerializer,
    OrderSnapshotModelSerializer,
    OrderUpdateAndSentDataSerializer,
    OrderUpdateDataSerializer,
)
from order.interface.vendor_serializers import (
    ExcelAttachmentSerializer,
    IncomingOrderInitializeSerializer,
    OrderReviewDocumentsModelSerializer,
    OrderReviewModelSerializer,
    VendorOrderFieldHistoryModelSerializer,
    VendorOrderInitializeSerializer,
    VendorOrderModelSerializer,
)
from order.invoice.data.selectors import order_pending_invoice_data_fetch
from order.services import (
    OrderCreateAndSentProcessService,
    OrderCreateProcessService,
    OrderSentProcessorService,
    OrderUpdateAndSentProcessService,
    OrderUpdateService,
    annotate_amount_and_element_count,
    annotate_order_progress_percentage,
    generate_item_type_and_category_list,
    mark_order_complete,
    prepare_email_data,
    send_latest_order_ratings_to_vms,
    submit_order_review,
    vendor_order_cancel_delete,
)
from order.services.order import (
    OrderBaseService,
    get_vendor_order_field_histories,
    mark_and_unmark_order_closed,
    mark_order_incomplete,
    order_request_and_snapshot_create,
    send_order_and_vms_data,
    vendor_order_element_excel_template_generate,
    vendor_order_element_update,
)
from order.services.trigger import order_created_trigger_event
from project.domain.services import restrict_action_decorator
from project.domain.status import RDStatus
from project.interface.apis.internal.apis import ProjectActionBaseApi, ProjectBaseApi
from proposal.domain.services import get_hidden_fields, get_hidden_fields_for_vendor_order_list
from rollingbanners.comment_base_service import CommentBaseService
from vendor.data.filters import VendorListFilter
from vendor.data.models import Vendor
from vendor.interface.serializers import VendorModelSerializer
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractFactory

logger = structlog.get_logger(__name__)

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)
OrderToWorkProgressFactory: Callable[[ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory] = (
    import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)
)


class OutgoingOrderInitializeOrderApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorOrderInitializeSerializer()},
        operation_id="outgoing_initialize_order_api",
        operation_summary="outgoing initialize order api",
    )
    def get(self, request, project_id):
        initialized_data = outgoing_order_create_prepare_order_data(
            project_id=project_id, user_id=request.user.pk, self_organization_id=self.get_organization_id()
        )
        return Response(VendorOrderInitializeSerializer(initialized_data).data, status=HTTP_200_OK)


class IncomingOrderInitializerApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: IncomingOrderInitializeSerializer()},
        operation_id="incoming_initialize_order_api",
        operation_summary="incoming_initialize order api",
    )
    def get(self, request, project_id):
        initialized_data = incoming_order_create_prepare_data(
            project_id=project_id, user_id=request.user.pk, self_organization_id=self.get_organization_id()
        )
        return Response(IncomingOrderInitializeSerializer(initialized_data).data, status=HTTP_200_OK)


class VendorOrderListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    parser_classes = (MultiPartParser,)
    pagination_class = None

    class OutputSerializer(VendorOrderModelSerializer):
        org_to = serializers.SerializerMethodField()
        progress_percentage = serializers.IntegerField()
        closed_at = serializers.DateTimeField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()
        is_snapshot_available = serializers.BooleanField()
        type_of_order = serializers.CharField(source="order_type")
        tax_amount = serializers.DecimalField(source="order_tax_amount", max_digits=20, decimal_places=2, default=0)
        amount_without_tax = serializers.SerializerMethodField()
        amount = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
        def get_amount_without_tax(self, obj):
            if self.context.get("order_id_to_hidden_fields_mapping"):
                order_id_to_hidden_fields_mapping = self.context.get("order_id_to_hidden_fields_mapping")
                if (
                    obj.pk in order_id_to_hidden_fields_mapping
                    and ClientFields.TOTAL_AMOUNT_WITHOUT_TAX.value in order_id_to_hidden_fields_mapping[obj.pk]
                ):
                    return None
            return round(obj.order_amount, 2)

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
        def get_amount(self, obj):
            return round(obj.order_amount + obj.order_tax_amount, 2)

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                pending_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("pending_payment_request_amount")
                )
            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                approved_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("approved_payment_request_amount")
                )

            return approved_amount

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            if obj.outgoing_status == OrderStatusChoices.DRAFT.value and not obj.is_vendor_active:
                return None
            return self.OutgoingOrgSerializer(obj.org_to).data

        def to_representation(self, instance):
            data = super().to_representation(instance)
            data["item_type"], data["category"] = generate_item_type_and_category_list(data)
            del data["order_elements"]
            return data

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "OrderListOutput"
            fields = (
                "id",
                "status",
                "amount",
                "tax_amount",
                "amount_without_tax",
                "invoice_status",
                "updated_at",
                "po_status",
                "status",
                "work_order_from",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "po_attached_at",
                "updated_by",
                "order_elements",
                "order_number",
                "purchase_orders",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "issued_at",
                "completed_at",
                "cancelled_at",
                "due_at",
                "comment_count",
                "progress_percentage",
                "closed_at",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
                "is_snapshot_available",
                "type_of_order",
                "created_by",
                "created_at",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_list_api",
        operation_summary="Vendor Order list api",
    )
    def get(self, request, **kwargs):
        orders: List[VendorOrder] = order_list(project_id=kwargs.get("project_id"), org_id=self.get_organization_id())
        annotate_order_progress_percentage(orders=orders)

        context = self.get_output_context()
        context.update(
            {
                "order_id_to_hidden_fields_mapping": get_hidden_fields_for_vendor_order_list(
                    client_field_mapping=ORDER_FIELDS_MAPPING,
                    order_ids=[order.id for order in orders],
                    org_id=self.get_organization_id(),
                )
            }
        )
        return Response(
            self.OutputSerializer(orders, many=True, context=context).data,
            status=HTTP_200_OK,
        )


# TODO: api can be removed/deprecated
class VendorListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            ref_name = "VendorListOutput"
            fields = ("id", "name", "code", "categories", "location", "gst_number")
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="vendor_list")
    def get(self, request, **kwargs):
        vendor_org_ids = vendor_fetch_all(user=request.user).values_list("id", flat=True)
        vendors = (
            Vendor.objects.filter(organization_id__in=vendor_org_ids, is_active=True)
            .select_related("organization")
            .prefetch_related("category")
        )
        return Response(self.OutputSerializer(vendors, many=True).data, status=HTTP_200_OK)


class OrderBaseActionApi(ProjectActionBaseApi):
    context = MicroContextChoices.ORDER.value


class VendorOrderCreateApiV2(OrderBaseActionApi):
    REQUIRED_PERMISSIONS = []
    context_action = MicroContextActions.CREATE.value

    input_serializer_class = OrderCreateDataSerializer

    class OutputSerializer(VendorOrderModelSerializer):
        cancelled_elements_count = serializers.SerializerMethodField()
        not_cancelled_elements_count = serializers.SerializerMethodField()
        poc = UserSerializer()
        vendor_info = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_cancelled_elements_count(self, obj):
            return self.context["cancelled_elements_count"] if self.context["cancelled_elements_count"] else 0

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_not_cancelled_elements_count(self, obj):
            return self.context["not_cancelled_elements_count"] if self.context["not_cancelled_elements_count"] else 0

        def get_vendor_info(self, obj):
            return obj.vendor_info_v2

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "VendorOrderCreateOutput"
            fields = (
                "id",
                "updated_at",
                "amount",
                "started_at",
                "due_at",
                "po_status",
                "status",
                "created_by",
                "sent_at",
                "po_attached_at",
                "updated_by",
                "vendor",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "issued_at",
                "work_order_from",
                "poc",
                "vendor_info",
                "payment_tnc",
                "other_tnc",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        request_body=OrderCreateDataSerializer,
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="vendor_order_create_v2",
        operation_summary="Vendor order create",
    )
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["org_from_id"] = self.get_organization_id()
        return context

    @restrict_action_decorator(status_list=[RDStatus.LOST.value, RDStatus.HOLD.value])
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data: OrderCreateData = self.validate_input_data(copy=False)
        order, elements, _ = OrderCreateProcessService.process_create(
            order_entity=data,
            user=request.user,
            project_id=kwargs.get("project_id"),
            origin_org_id=self.get_organization_id(),
            order_type=data.order_type,
            is_sent=False,
        )
        OrderCreateProcessService.create_status_history(
            order_id=order.pk,
            outgoing_status=order.outgoing_status,
            incoming_status=order.incoming_status,
            user_id=request.user.pk,
        )
        on_commit(partial(order_created_trigger_event, order_id=order.id, project_id=kwargs.get("project_id")))
        return Response(
            self.OutputSerializer(
                order,
                context={
                    "not_cancelled_elements_count": len(elements),
                    "cancelled_elements_count": 0,
                    "order_type": data.order_type,
                    "timezone": self.get_project_timezone(),
                },
            ).data,
            status=HTTP_201_CREATED,
        )


class VendorOrderFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    # class FilterSerializer(BaseSerializer):
    #     order_type = serializers.ChoiceField(choices=OrderType.choices)

    #     class Meta:
    #         pass

    class OutputSerializer(VendorOrderModelSerializer):
        org_to = serializers.SerializerMethodField()
        rc_count = serializers.IntegerField()
        purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
            source="sorted_purchase_orders", many=True
        )
        poc = UserSerializer()
        is_discount_col_visible = serializers.BooleanField(source="is_discounted")
        is_service_charge_col_visible = serializers.BooleanField(source="is_service_charged")
        creator_org_id = HashIdField(source="created_by.org_id")
        request_id = HashIdField()
        task_id = HashIdField()
        is_invoice_visible = serializers.BooleanField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()
        is_snapshot_available = serializers.BooleanField()
        is_taxed = serializers.BooleanField()
        vendor_info = serializers.SerializerMethodField()
        document_config = serializers.SerializerMethodField()
        hidden_fields = serializers.SerializerMethodField()

        def get_vendor_info(self, obj):
            return OrganizationSectionData.drf_serializer(obj.vendor_info_v2, many=True).data

        def get_document_config(self, obj):
            if is_empty(obj.document_config):
                return []
            return OrganizationSectionData.drf_serializer(obj.document_config, many=True).data

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                pending_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("pending_payment_request_amount")
                )
            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                approved_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("approved_payment_request_amount")
                )

            return approved_amount

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            if obj.outgoing_status == OrderStatusChoices.DRAFT.value and not obj.is_vendor_active:
                return None
            return self.OutgoingOrgSerializer(obj.org_to).data

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=ORDER_FIELDS_MAPPING, order_id=obj.pk, org_id=self.context.get("org_id")
            )

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "VendorOrderOutput"
            fields = (
                "id",
                "request_id",
                "task_id",
                "status",
                "amount",
                "updated_at",
                "started_at",
                "due_at",
                "payment_tnc",
                "other_tnc",
                "po_status",
                "status",
                "order_elements",
                "po_attached_at",
                "updated_by",
                "purchase_orders",
                "project",
                "attachments",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "created_by",
                "issued_at",
                "work_order_from",
                "shipping_address_header",
                "comment_count",
                "rate_contract",
                "rc_count",
                "deductions",
                "poc",
                "is_discount_col_visible",
                "is_service_charge_col_visible",
                "creator_org_id",
                "actions",
                "type_of_order",
                "purchase_order_preview",
                "vendor_info",
                "is_invoice_visible",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
                "is_taxed",
                "is_snapshot_available",
                "amount_without_tax",
                "document_config",
                "hidden_fields",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_fetch",
    )
    def get(self, request, project_id, vendor_order_id, *args, **kwargs):
        order = get_order_detail(order_id=vendor_order_id, org_id=self.get_organization_id())
        order_action_service = OrderActionServiceFactory.get_service(
            self,
            order=order,
            user=request.user,
            is_admin=request.user.token_data.is_admin,
        )
        user_actions = order_action_service.get_user_actions()
        return Response(
            self.OutputSerializer(
                order,
                context={
                    "org_id": self.get_organization_id(),
                    "actions": user_actions,
                    "source": self.get_source(),
                    "timezone": self.get_project_timezone(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class ClientOrderFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OrderOutputSerializer(VendorOrderModelSerializer):
        org_to = serializers.SerializerMethodField()
        rc_count = serializers.IntegerField()
        purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
            source="sorted_purchase_orders", many=True
        )
        poc = UserSerializer()
        is_discount_col_visible = serializers.BooleanField(source="is_discounted")
        is_service_charge_col_visible = serializers.BooleanField(source="is_service_charged")
        creator_org_id = HashIdField(source="created_by.org_id")
        type_of_order = serializers.CharField(source="order_type")
        status = serializers.CharField()
        document_config = serializers.SerializerMethodField()
        hidden_fields = serializers.SerializerMethodField()

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            return self.OutgoingOrgSerializer(obj.org_to).data

        def get_document_config(self, obj):
            return OrganizationSectionData.drf_serializer(obj.document_config, many=True).data

        @swagger_serializer_method(serializer_or_field=serializers.ListField())
        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=ORDER_FIELDS_MAPPING, order_id=obj.pk, org_id=self.context.get("org_id")
            )

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "VendorOrderOutput"
            fields = (
                "id",
                "status",
                "amount",
                "updated_at",
                "started_at",
                "due_at",
                "payment_tnc",
                "other_tnc",
                "po_status",
                "order_elements",
                "po_attached_at",
                "updated_by",
                "purchase_orders",
                "project",
                "attachments",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "created_by",
                "issued_at",
                "work_order_from",
                "shipping_address_header",
                "comment_count",
                "rate_contract",
                "rc_count",
                "deductions",
                "poc",
                "is_discount_col_visible",
                "is_service_charge_col_visible",
                "is_taxed",
                "creator_org_id",
                "type_of_order",
                "purchase_order_preview",
                "document_config",
                "hidden_fields",
            )
            output_hash_id_fields = ("id",)

    class OrderSnapshotOutputSerializer(OrderSnapshotModelSerializer):
        rc_count = serializers.IntegerField()
        purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
            source="sorted_purchase_orders", many=True
        )
        poc = UserSerializer(source="order.poc")
        updated_at = serializers.DateTimeField(source="order.updated_at")
        po_status = serializers.CharField(source="order.po_status")
        po_attached_at = serializers.DateTimeField(source="order.po_attached_at")
        is_discount_col_visible = serializers.BooleanField(source="is_discounted")
        is_service_charge_col_visible = serializers.BooleanField(source="is_service_charged")
        creator_org_id = HashIdField(source="created_by.org_id")
        type_of_order = serializers.CharField(source="order.order_type")
        status = serializers.CharField()
        document_config = serializers.SerializerMethodField()
        hidden_fields = serializers.SerializerMethodField()

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            return self.OutgoingOrgSerializer(obj.org_to).data

        def get_document_config(self, obj):
            return OrganizationSectionData.drf_serializer(obj.document_config, many=True).data

        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=ORDER_FIELDS_MAPPING, order_id=obj.pk, org_id=self.context.get("org_id")
            )

        class Meta(OrderSnapshotModelSerializer.Meta):
            ref_name = "VendorOrderOutput"
            fields = (
                "id",
                "status",
                "amount",
                "updated_at",
                "started_at",
                "due_at",
                "payment_tnc",
                "other_tnc",
                "po_status",
                "order_elements",
                "po_attached_at",
                "updated_by",
                "purchase_orders",
                "project",
                "attachments",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "created_by",
                "issued_at",
                "work_order_from",
                "shipping_address_header",
                "comment_count",
                "rate_contract",
                "rc_count",
                "deductions",
                "poc",
                "is_discount_col_visible",
                "is_service_charge_col_visible",
                "is_taxed",
                "creator_org_id",
                "type_of_order",
                "document_config",
                "hidden_fields",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrderOutputSerializer()},
        operation_id="client_order_fetch",
    )
    def get(self, request, project_id, vendor_order_id, *args, **kwargs):
        order_snapshot_repo = OrderSnapshotRepo()
        order, is_snapshot = get_client_order_detail(
            order_id=vendor_order_id, org_id=self.get_organization_id(), order_snapshot_repo=order_snapshot_repo
        )

        order_factory = OrderServiceFactory()
        status_interactor = order_factory.get_status_interactor()
        status = status_interactor.get_client_order_status(order.outgoing_status)
        setattr(order, "status", status)

        if is_snapshot:
            output_serializer = self.OrderSnapshotOutputSerializer
        else:
            output_serializer = self.OrderOutputSerializer
        return Response(
            output_serializer(
                order,
                context={
                    "org_id": self.get_organization_id(),
                    "source": self.get_source(),
                    "timezone": self.get_project_timezone(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class VendorOrderCancelDeleteApi(OrderBaseActionApi):
    context_action = MicroContextActions.CANCEL.value
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(responses={HTTP_200_OK: ""}, operation_id="vendor_order_cancel_delete")
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        vendor_order_cancel_delete(
            vendor_order_id=kwargs.get("vendor_order_id"),
            user=request.user,
            order_repo=OrderRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        return Response(status=HTTP_200_OK)


class VendorOrderUpdateApiV2(OrderBaseActionApi):
    context_action = MicroContextActions.UPDATE.value
    REQUIRED_PERMISSIONS = []
    input_serializer_class = OrderUpdateDataSerializer

    class OutputSerializer(VendorOrderModelSerializer):
        cancelled_elements_count = serializers.SerializerMethodField()
        not_cancelled_elements_count = serializers.SerializerMethodField()
        poc = UserSerializer()
        org_to = serializers.SerializerMethodField()
        vendor_info = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            if obj.outgoing_status == OrderStatusChoices.DRAFT.value and not obj.is_vendor_active:
                return None
            return self.OutgoingOrgSerializer(
                obj.org_to, context={"gst_number": obj.gst_number, "gst_state": obj.gst_state}
            ).data

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_cancelled_elements_count(self, obj):
            return self.context["cancelled_elements_count"] if self.context["cancelled_elements_count"] else 0

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_not_cancelled_elements_count(self, obj):
            return self.context["not_cancelled_elements_count"] if self.context["not_cancelled_elements_count"] else 0

        def get_vendor_info(self, obj):
            return OrganizationSectionData.drf_serializer(obj.vendor_info_v2, many=True).data

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "VendorOrderCreateOutput"
            fields = (
                "id",
                "status",
                "created_at",
                "updated_at",
                "amount",
                "started_at",
                "due_at",
                "po_status",
                "status",
                "created_by",
                "sent_at",
                "po_attached_at",
                "updated_by",
                "vendor",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_at",
                "issued_by",
                "shipping_address_header",
                "work_order_from",
                "poc",
                "vendor_info",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        request_body=OrderUpdateDataSerializer,
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="vendor_order_update",
    )
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["org_from_id"] = self.get_organization_id()
        return context

    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data: OrderUpdateData = self.validate_input_data()
        setattr(data, "id", kwargs.get("vendor_order_id"))
        try:
            order_snapshot_data = OrderSnapshotService(
                order_repo=OrderRepo(), order_snapshot_repo=OrderSnapshotRepo()
            ).create_order_snapshot_data(order_id=data.id)

            order, _, _ = OrderUpdateService.process_update(
                order_entity=data,
                user=request.user,
                order_type=data.order_type,
                project_id=kwargs.get("project_id"),
                is_sent=False,
                direct_order=True,
            )
        except UOMValidationError as e:
            self.set_response_message(str(e))
            return Response(status=HTTP_400_BAD_REQUEST)
        except QuantityValidationError as e:
            self.set_response_message(str(e))
            return Response(status=HTTP_400_BAD_REQUEST)
        order_request_and_snapshot_create(
            updated_order=order,
            order_snapshot_data=order_snapshot_data,
            user_id=request.user.pk,
            order_repo=OrderRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        # if kwargs.get("vendor_order_id"):
        #     vendor_order_sync.apply_async(
        #         queue="excel_sync_queue",
        #         kwargs={
        #             "data": {"client_rates": True, "org_id": self.get_organization_id()},
        #             "vendor_order_id": kwargs.get("vendor_order_id"),
        #         },
        #     )
        return Response(
            self.OutputSerializer(
                order,
                context={"order_type": data.order_type, "timezone": self.get_project_timezone()},
            ).data,
            status=HTTP_201_CREATED,
        )


class VendorOrderPreCreateOrderNumberInitializeApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        order_number = serializers.CharField()

        class Meta:
            fields = ("order_number",)
            ref_name = "VendorOrderPreCreateOrderNumberInitialize"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_precreate_order_number",
    )
    def get(self, request, *args, **kwargs):
        # order_number = vendor_order_precreate_order_number_fetch(project_id=kwargs.get("project_id"))
        order_number, project = OrderBaseService.fetch_order_number(project_id=kwargs.get("project_id"))
        serializer = self.OutputSerializer(data={"order_number": f"{project.job_id}/{order_number}"})
        if not serializer.is_valid():
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: "Invalid Order"})
        data = serializer.data
        return Response(data, status=HTTP_200_OK)


class VendorOrderSentInfoFetch(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        recipient_emails = serializers.ListField()
        cc_emails = serializers.ListField()
        bcc_emails = serializers.ListField()
        subject = serializers.CharField()
        attachment = ExcelAttachmentSerializer()

        class Meta:
            ref_name = "VendorOrderSentInfoOutput"

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="order-sent-info-fetch")
    @transaction.atomic
    def get(self, request, project_id, vendor_order_id, *args, **kwargs):
        fields = [
            "item_name",
            "description",
            "item_code",
            "item_type",
            "Quantity",
            "UOM",
            "order_rate",
            "final_amount",
        ]
        email_data = prepare_email_data(
            vendor_order_id=vendor_order_id,
            project_id=project_id,
            user_email=request.user.email,
            fields=fields,
            org_id=self.get_organization_id(),
        )
        serializer = self.OutputSerializer(data=email_data)
        serializer.is_valid(raise_exception=True)
        return Response(data=serializer.data, status=HTTP_200_OK)


class VendorOrderSentInfoFetchApiV2(ProjectBaseApi):
    """
    Will be called only while creating and sending order
    """

    REQUIRED_PERMISSIONS = []
    filter_serializer_class = OrderSentInitializerDataSerializer

    class OutputSerializer(BaseSerializer):
        recipient_emails = serializers.ListField()
        cc_emails = serializers.ListField()
        bcc_emails = serializers.ListField()
        subject = serializers.CharField()
        po_flow = serializers.BooleanField()  # Not Used anymore

        class Meta:
            ref_name = "VendorOrderSentInfoOutput"

    @swagger_auto_schema(
        query_serializer=OrderSentInitializerDataSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order-sent-info-fetch",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        data: OrderSentInitializeData = self.validate_filter_data()
        email_data = OrderSentProcessorService.prepare_email_data(
            sent_initializer_data=data,
            user_email=request.user.email,
            org_id=self.get_organization_id(),
            project_id=project_id,
        )
        serializer = self.OutputSerializer(data=email_data)
        serializer.is_valid(raise_exception=True)
        return Response(data=serializer.data, status=HTTP_200_OK)


class VendorOrderEmailSendApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(serializers.Serializer):
        to = serializers.ListField(child=serializers.EmailField())
        cc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True)
        bcc = serializers.ListField(child=serializers.EmailField(), required=False, allow_null=True)
        subject = serializers.CharField(min_length=1, max_length=500)
        body = serializers.CharField(min_length=1, max_length=1000, required=False, allow_null=True)
        attachments = ExcelAttachmentSerializer(many=True, allow_null=True, required=False)

        class Meta:
            ref_name = "VendorOrderEmailSendOutput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(request_body=InputSerializer, operation_summary="Send Email Content")
    @transaction.atomic
    def post(self, request, project_id, vendor_order_id, *args, **kwargs):
        data = self.validate_input_data()
        send_order_and_vms_data(
            project_id=project_id,
            vendor_order_id=vendor_order_id,
            data=data,
            user=request.user,
            org_id=self.get_organization_id(),
            order_repo=OrderRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        return Response(
            {
                "message": _(RESPONSE_FOR_EMAIL_SENT),
            },
            status=HTTP_200_OK,
        )


class VendorOrderCreateAndSendApi(OrderBaseActionApi):
    context_action = MicroContextActions.CREATE_AND_SEND.value
    input_serializer_class = OrderCreateAndSentDataSerializer

    class OutputSerializer(BaseSerializer):
        message = serializers.CharField()
        order_id = HashIdField()

        class Meta:
            ref_name = "VendorOrderCreateAndSendOutput"

    @swagger_auto_schema(
        request_body=OrderCreateAndSentDataSerializer,
        operation_id="order_create_sent_api",
        operation_summary="Send Email Content",
    )
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["org_from_id"] = self.get_organization_id()
        return context

    @transaction.atomic
    @restrict_action_decorator(status_list=[RDStatus.LOST.value, RDStatus.HOLD.value])
    def post(self, request, project_id, *args, **kwargs):
        data: OrderCreateAndSentData = self.validate_input_data()
        order = OrderCreateAndSentProcessService.create_and_send(
            order_entity=data,
            user=request.user,
            order_type=OrderType.OUTGOING,
            is_sent=True,
            project_id=project_id,
            origin_org_id=self.get_organization_id(),
            direct_order=True,
        )
        request_service = OrderRequestServiceFactory.get_service(
            order_repo=OrderRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        request_service.create_request(
            data=OrderRequestCreateData(
                event=OrderEventEnum.SENT_FOR_APPROVAL.value,
                order_id=order.pk,
                user_id=request.user.pk,
                context=order.context,
                project_id=order.project_id,
                is_draft=False,
                org_id=order.origin_org_id,
                order_amount=order.order_amount,
                hierarchy_context=MicroContext.ORDER.value,
            )
        )
        return Response(self.OutputSerializer({"message": _(RESPONSE_FOR_EMAIL_SENT), "order_id": order.pk}).data)
        # {
        #     "message": RESPONSE_FOR_EMAIL_SENT,
        # },
        #     status=HTTP_200_OK,
        # )


class VendorOrderUpdateAndSendApi(OrderBaseActionApi):
    context_action = MicroContextActions.UPDATE_AND_SEND.value
    REQUIRED_PERMISSIONS = []
    input_serializer_class = OrderUpdateAndSentDataSerializer

    @swagger_auto_schema(
        request_body=OrderUpdateAndSentDataSerializer(),
        operation_id="order_update_sent_api",
        operation_summary="Send Email Content",
    )
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["org_from_id"] = self.get_organization_id()
        return context

    @transaction.atomic
    @restrict_action_decorator(status_list=[RDStatus.LOST.value, RDStatus.HOLD.value])
    def put(self, request, project_id, *args, **kwargs):
        data: OrderCreateAndSentData = self.validate_input_data()
        setattr(data, "id", kwargs.get("vendor_order_id"))
        OrderUpdateAndSentProcessService.update_and_send(
            order_entity=data,
            user=request.user,
            order_type=OrderType.OUTGOING,
            is_sent=True,
            project_id=project_id,
            order_repo=OrderRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        return Response(
            {
                "message": _(RESPONSE_FOR_EMAIL_SENT),
            },
            status=HTTP_200_OK,
        )


class VendorOrderMarkCompleteApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        order_type = serializers.ChoiceField(
            choices=OrderType.choices, required=False, allow_null=True, default=OrderType.OUTGOING
        )

        class Meta:
            ref_name = "VendorOrderMarkComplete"

    @swagger_auto_schema(responses={HTTP_200_OK: ""}, operation_id="vendor_order_mark_update")
    @transaction.atomic
    def put(self, request, project_id, vendor_order_id):
        mark_order_complete(
            vendor_order_id=vendor_order_id,
            project_id=project_id,
            user_id=request.user.pk,
        )
        return Response(status=HTTP_200_OK)


class VendorOrderMarkInCompleteApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(responses={HTTP_200_OK: ""}, operation_id="vendor_order_mark_incomplete")
    @transaction.atomic
    def put(self, request, project_id, vendor_order_id):
        mark_order_incomplete(vendor_order_id=vendor_order_id, user_id=request.user.pk)
        return Response(status=HTTP_200_OK)


class VendorOrderMarkUnmarkClosedApi(OrderBaseActionApi):
    REQUIRED_PERMISSIONS = []
    context_action = MicroContextActions.MARK_CLOSE.value

    class InputSerializer(BaseSerializer):
        is_closed = serializers.BooleanField()

        class Meta:
            ref_name = "VendorOrderMarkUnmarkClosed"

    @swagger_auto_schema(
        request_body=InputSerializer(), responses={HTTP_200_OK: ""}, operation_id="vendor_order_mark_unmark_close"
    )
    @transaction.atomic
    def put(self, request, project_id, vendor_order_id):
        is_closed = self.validate_input_data().get("is_closed")
        mark_and_unmark_order_closed(
            order_id=vendor_order_id, user=request.user, org_id=self.get_organization_id(), is_closed=is_closed
        )
        return Response(status=HTTP_200_OK)


class VendorOrderMarkCompleteWithReviewApi(OrderBaseActionApi):
    context_action = MicroContextActions.MARK_COMPLETED.value
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        class OrderReviewDocument(BaseSerializer):
            url = serializers.URLField()
            type = serializers.ChoiceField(choices=OrderReviewDocument.FileChoices.choices)

            class Meta:
                pass

        order_type = serializers.ChoiceField(
            choices=OrderType.choices, required=False, allow_null=True, default=OrderType.OUTGOING
        )
        on_time_delivery = serializers.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
        quality_of_work = serializers.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
        cost_effectiveness = serializers.IntegerField(validators=[MaxValueValidator(5), MinValueValidator(1)])
        review = serializers.CharField()
        review_documents = OrderReviewDocument(many=True, required=False)
        started_at = CustomDateField()
        due_at = CustomEndDateField()
        completed_at = CustomEndDateField()

        def validate(self, attrs):
            super().validate(attrs)
            if attrs["due_at"].date() < attrs["started_at"].date():
                raise ValidationError("Start date must be earlier than due date")
            if attrs["completed_at"].date() < attrs["started_at"].date():
                raise ValidationError("Start date must be earlier than completion date")
            return attrs

        class Meta:
            ref_name = "VendorOrderMarkComplete"

    @swagger_auto_schema(
        request_body=InputSerializer, responses={HTTP_200_OK: ""}, operation_id="order_mark_update_with_review"
    )
    @transaction.atomic
    def put(self, request, project_id, vendor_order_id):
        data: Dict = self.validate_input_data()
        submit_order_review(
            vendor_order_id=vendor_order_id,
            user_id=request.user.pk,
            on_time_delivery=data["on_time_delivery"],
            quality_of_work=data["quality_of_work"],
            cost_effectiveness=data["cost_effectiveness"],
            review=data["review"],
            review_documents=data["review_documents"],
        )
        send_latest_order_ratings_to_vms(
            vendor_order_id=vendor_order_id,
            on_time_delivery=data["on_time_delivery"],
            quality_of_work=data["quality_of_work"],
            cost_effectiveness=data["cost_effectiveness"],
        )
        mark_order_complete(
            order_complete_data=MarkOrderCompleteData(
                vendor_order_id=vendor_order_id,
                user_id=request.user.pk,
                project_id=project_id,
                started_at=data["started_at"],
                due_at=data["due_at"],
                completed_at=data["completed_at"],
            )
        )
        return Response(status=HTTP_200_OK)


class VendorOrderDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    # class FilterSerializer(BaseSerializer):
    #     order_type = serializers.ChoiceField(choices=OrderType.choices, default=OrderType.OUTGOING)

    #     class Meta:
    #         pass

    class OutputSerializer(VendorOrderModelSerializer):
        amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        org_to = serializers.SerializerMethodField()
        vendor = serializers.SerializerMethodField()
        comment_count = serializers.IntegerField(default=0)
        poc = UserSerializer()
        is_discount_col_visible = serializers.BooleanField(source="is_discounted")
        type_of_order = serializers.CharField(source="order_type")

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code", "location", "categories")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            return self.OutgoingOrgSerializer(obj.org_to).data

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_vendor(self, obj):
            return self.get_org_to(obj=obj)

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "VendorOrderDetailAppOutput"
            fields = (
                "id",
                "status",
                "po_status",
                "invoice_status",
                "updated_at",
                "updated_by",
                "created_at",
                "amount",
                "shipping_address",
                "shipping_address_header",
                "created_by",
                "created_at",
                "due_at",
                "started_at",
                "org_to",
                "vendor",
                "sent_at",
                "project",
                "order_number",
                "payment_tnc",
                "other_tnc",
                "work_order_from",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "comment_count",
                "poc",
                "is_discount_col_visible",
                "type_of_order",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_fetch_for_app",
    )
    def get(self, request, project_id, vendor_order_id, *args, **kwargs):
        vendor_order = (
            VendorOrder.objects.filter(id=vendor_order_id)
            .select_related("project", "org_to", "org_from", "updated_by", "issued_by")
            .annotate(
                cancelled_elements_count=Count(
                    "order_elements",
                    filter=Q(order_elements__status=OrderStatus.CANCELLED, order_elements__deleted_at__isnull=True),
                ),
                not_cancelled_elements_count=Count(
                    "order_elements",
                    filter=Q(
                        order_elements__status__in=[OrderStatus.PENDING, OrderStatus.SENT, OrderStatus.MODIFIED],
                        order_elements__deleted_at__isnull=True,
                    ),
                ),
                deduction_amount=Coalesce(
                    Sum("deductions__amount", filter=Q(deductions__deleted_at__isnull=True)),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
            .annotate(
                comment_count=CommentHelperService.get_count(
                    context_group=CommentHelperService.GROUPED_CONTEXT.ORDER.name, org_id=self.get_organization_id()
                )
            )
            .first()
        )
        amount_dict: Dict = (
            VendorOrderElement.available_objects.annotate_elements_final_amount()
            .filter(
                vendor_order_id=vendor_order_id,
                status__in=[OrderStatus.COMPLETED, OrderStatus.PENDING, OrderStatus.MODIFIED, OrderStatus.SENT],
            )
            .aggregate(amount=Sum(F("element_final_amount")))
        )
        if amount_dict.get("amount"):
            amount = round(amount_dict.get("amount"), 2)
        else:
            amount = 0
        if hasattr(vendor_order, "deduction_amount"):
            amount = amount - vendor_order.deduction_amount
        setattr(vendor_order, "amount", amount)
        setattr(vendor_order, "order_amount", amount)
        return Response(
            self.OutputSerializer(
                vendor_order, context={"order_type": OrderType.OUTGOING, "timezone": self.get_project_timezone()}
            ).data,
            status=HTTP_200_OK,
        )


class OutgoingOrderOrgListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            ref_name = "OutgoingOrgListOutput"
            fields = ("id", "name", "code", "categories", "location")
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="order_outgoing_org_list")
    def get(self, request, project_id):
        org_from_id = self.get_organization_id()
        outgoing_org_list: List[Vendor] = get_outgoing_org_list(org_from_id=org_from_id)
        return Response(self.OutputSerializer(outgoing_org_list, many=True).data, status=HTTP_200_OK)


class OutGoingOrderVendorListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        is_vendor_details_required = serializers.BooleanField(default=False)

        class Meta:
            ref_name = "VendorDetailsFilter"

    class OutputSerializer(OrganizationModelSerializer):
        order_element_count = serializers.IntegerField(default=0)
        name = serializers.CharField()
        order_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)

        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "OutGoingOrderVendorsListOutput"
            fields = ("id", "name", "order_amount", "order_element_count")
            output_hash_id_fields = ("id",)

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="vendors_given_order_list",
    )
    def get(self, request, project_id):
        data = self.validate_filter_data()
        vendor_id_list = get_vendor_ids_from_orders(project_id=project_id, org_id=self.get_organization_id())
        vendors = Organization.objects.filter(id__in=vendor_id_list).order_by("name")
        if data.get("is_vendor_details_required"):
            annotate_amount_and_element_count(
                vendors=vendors, project_id=project_id, vendor_id_list=vendor_id_list, org_id=self.get_organization_id()
            )
        return Response(self.OutputSerializer(vendors, many=True).data, status=HTTP_200_OK)


class OutgoingOrderOrgListV2Api(ProjectBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        element_ids = HashIdListField()
        client_id = HashIdField()
        is_project_shared = serializers.BooleanField(default=False)
        form = serializers.ChoiceField(
            choices=VendorListFormChoices.choices, required=False, default=VendorListFormChoices.REGULAR_ORDER.value
        )

        class Meta:
            ref_name = "OutgoingOrderOrgListV2ApiInput"

    class OutputSerializer(VendorModelSerializer):
        rc_count = serializers.IntegerField(allow_null=True, default=0)
        element_count = serializers.IntegerField(allow_null=True, default=0)

        class Meta(VendorModelSerializer.Meta):
            ref_name = "OutgoingOrgListOutput"
            fields = (
                "id",
                "name",
                "code",
                "categories",
                "location",
                "rc_count",
                "element_count",
            )

    serializer_class = OutputSerializer
    filterset_class = VendorListFilter
    filter_serializer_class = SearchTextFilterSerializer

    def get_queryset(self):
        data = self.validate_input_data()
        element_ids = data.get("element_ids")
        client_id = data.get("client_id")
        org_from_id = self.get_organization_id()
        project_id = self.kwargs.get("project_id")
        organization_filter = Q()
        if data.get("is_project_shared"):
            organization_ids = self.get_vendor_organization_ids()
            organization_filter = Q(organization_id__in=organization_ids)
        return outgoing_org_list_fetch_v2(
            org_from_id=org_from_id,
            client_id=client_id,
            element_ids=element_ids,
            form=data.get("form"),
            project_id=project_id,
        ).filter(organization_filter)

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_outgoing_org_list_v2",
    )
    def post(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class OrderHeaderDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorOrderModelSerializer):
        elements_count = serializers.IntegerField()
        total_progress_percentage = serializers.IntegerField()
        today_updated_items_count = serializers.IntegerField()
        todays_progress_percentage = serializers.IntegerField()
        actual_start_date = serializers.DateField()
        actual_completion_date = serializers.DateTimeField(source="completed_at")
        expected_start_date = CustomDateField(source="started_at")
        expected_due_date = CustomEndDateField(source="due_at")

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "OrderHeaderOutput"
            fields = (
                "elements_count",
                "total_progress_percentage",
                "todays_progress_percentage",
                "today_updated_items_count",
                "actual_start_date",
                "actual_completion_date",
                "expected_start_date",
                "expected_due_date",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_header_detail",
    )
    def post(self, request, project_id, vendor_order_id, *args, **kwargs):
        order: VendorOrder = order_fetch_with_elements(vendor_order_id=vendor_order_id).first()
        annotate_progress_percentage_details(vendor_order=order, project_id=project_id)
        return Response(
            self.OutputSerializer(order, context=self.get_output_context()).data,
            status=HTTP_200_OK,
        )


class OrderReview(BaseApi, ListModelMixin):
    class OutputSerializer(BaseSerializer):
        class OrderReviewSerializer(OrderReviewModelSerializer):
            created_by = UserSerializer()
            review_documents = OrderReviewDocumentsModelSerializer(many=True)

            class Meta(OrderReviewModelSerializer.Meta):
                pass

        review = OrderReviewSerializer()

        class Meta:
            ref_name = "OrderReviews"

    serializer_class = OutputSerializer

    def get_queryset(self):
        return (
            VendorOrder.objects.filter(
                Q(
                    org_to__id__in=Vendor.objects.filter(code=self.kwargs["vendor_code"]).values_list(
                        "organization", flat=True
                    )
                )
                | Q(org_to__vendor__code=self.kwargs["vendor_code"])
            )
            .select_related("review", "review__created_by")
            .filter(review__isnull=False)
            .prefetch_related("review__review_documents")
            .order_by("-review__created_at")
            .all()
        )

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="vendor_order_reviews")
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class DeductionTypeListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        id = serializers.CharField()
        name = serializers.CharField()
        color_code = serializers.CharField()

        class Meta:
            ref_name = "DeductionTypeListOutput"
            ouput_hash_id_fields = ("id",)

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer(many=True)}, operation_id="deduction_type_list")
    def get(self, request, *args, **kwargs):
        return Response(
            self.OutputSerializer(get_deduction_types(), many=True).data,
            status=HTTP_200_OK,
        )


class DeductionTypeRemarkListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        deduction_type_name = serializers.CharField()

        class Meta:
            ref_name = "DeductionTypeRemarkListFilterInput"

    class OutputSerializer(BaseSerializer):
        id = serializers.CharField()
        remark = serializers.CharField()

        class Meta:
            ref_name = "DeductionTypeRemarkListOutput"
            ouput_hash_id_fields = ("id",)

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        query_serializer=FilterSerializer,
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="deduction_type_remark_list",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        return Response(
            self.OutputSerializer(
                get_deduction_type_remarks(deduction_type_name=data.get("deduction_type_name", "")), many=True
            ).data,
            status=HTTP_200_OK,
        )


class OrderReviewListApi(BaseApi, ListModelMixin):
    class OutputSerializer(BaseSerializer):
        class OrderReviewSerializer(OrderReviewModelSerializer):
            created_by = UserSerializer()
            review_documents = OrderReviewDocumentsModelSerializer(many=True)

            class Meta(OrderReviewModelSerializer.Meta):
                pass

        review = OrderReviewSerializer()

        class Meta:
            ref_name = "OrderReviews"

    serializer_class = OutputSerializer

    def get_queryset(self):
        return order_review_list(vendor_code=self.kwargs["vendor_code"])

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="vendor_order_reviews")
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class VendorOrderFieldHistoryFetchApi(BaseApi):
    pagination_class = None

    class FilterSerializer(BaseSerializer):
        is_client = serializers.BooleanField()

        class Meta:
            ref_name = "VendorOrderFieldHistoryFilterSerializer"

    class OutputSerializer(VendorOrderFieldHistoryModelSerializer):
        class Meta(VendorOrderFieldHistoryModelSerializer.Meta):
            fields = ("prev_value", "value", "updated_at", "updated_by", "is_initial")
            ref_name = "VendorOrderFieldHistoryFetchOutput"

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_201_CREATED: OutputSerializer()},
        operation_id="vendor-order_field_history",
        operation_summary="Get vendor order field history.",
    )
    def get(self, request, project_id, vendor_order_id, field, *args, **kwargs):
        data = self.validate_filter_data()
        vendor_order_field_histories = get_vendor_order_field_histories(
            field=field, vendor_order_id=vendor_order_id, is_client=data.get("is_client")
        )
        response = self.OutputSerializer(vendor_order_field_histories, many=True, context={"field": field}).data
        response.reverse()
        return Response(response, status=HTTP_200_OK)


class VendorOrderElementUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        category_id = HashIdField(allow_null=True, required=False)
        item_type_id = HashIdField(allow_null=True, required=False)
        uom = serializers.IntegerField(allow_null=True, required=False)
        quantity = serializers.DecimalField(max_digits=11, decimal_places=4, allow_null=True, required=False)
        vendor_rate = serializers.DecimalField(max_digits=11, decimal_places=4, allow_null=True, required=False)

        class Meta:
            ref_name = "InputSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="vendor_order_element_update",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        element_data = self.validate_input_data()
        try:
            vendor_order_element_update(
                project_id=kwargs.get("project_id"),
                order_id=kwargs.get("vendor_order_id"),
                element_id=kwargs.get("element_id"),
                org_from_id=self.get_organization_id(),
                element_data=element_data,
                user_id=request.user.pk,
            )
        except OrderException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("Order element updated successfully.")
        return Response(status=HTTP_200_OK)


class OrderPendingInvoiceDataApi(ProjectBaseApi):
    class OutputSerializer(BaseSerializer):
        amount = serializers.DecimalField(max_digits=20, decimal_places=2)
        pending_count = serializers.IntegerField()

        class Meta:
            ref_name = "OrderPendingInvoiceDataOutput"

    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_pending_invoice_data",
        operation_summary="Get pending invoice data.",
    )
    def get(self, request, project_id, vendor_order_id, *args, **kwarg):
        data = order_pending_invoice_data_fetch(project_id=project_id, order_id=vendor_order_id)
        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class VendorOrderElementExcelTemplateDownloadV2Api(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: "elements.xlsx"},
        operation_id="vendor_order_element_excel_template_download_v2",
        operation_summary="Download excel template for element upload.",
    )
    def get(self, request, project_id, *args, **kwargs):
        response = HttpResponse(
            content=vendor_order_element_excel_template_generate(
                excel_columns=EXCEL_SAMPLE_COLUMNS,
                organization_id=self.get_organization_id(),
                project_id=self.get_project_id(),
            ),
            content_type="application/ms-excel",
        )
        response["Content-Disposition"] = "attachment; filename=elements.xlsx"
        return response


class OrderHeaderDetailV2Api(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    def get(self, request, project_id, vendor_order_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        wp_service = factory.get_service()

        try:
            data = get_order_header_detail(order_id=vendor_order_id, wp_service=wp_service)
        except OrderNotFoundException:
            self.set_response_message("Order not found")
            return Response(status=HTTP_404_NOT_FOUND)

        return Response(pydantic_dump(data), status=HTTP_200_OK)
