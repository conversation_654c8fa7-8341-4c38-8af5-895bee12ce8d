import decimal
import json
from typing import List

import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from rest_framework import serializers
from rest_framework.mixins import ListModelMixin
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from approval_request.domain.constants import RequestActionEnum
from approval_request.domain.entities import (
    ResourceToRequestApproveServiceData,
    ResourceToRequestHoldServiceData,
    ResourceToRequestRejectServiceData,
)
from approval_request.domain.factories import RequestActionServiceFactory
from approval_request.domain.services.request import ResourceToRequestActionService, request_action_data_get
from approval_request.interface.apis import ResourceA<PERSON><PERSON><PERSON><PERSON><PERSON>
from approval_request.interface.serializers import RequestActionInputSerializer
from authorization.domain.constants import Permissions
from boq.data.repos import BoqElementSyncRepo
from commentv2.data.selectors import get_comment_replies
from commentv2.domain.constants import CommentTypeEnum
from commentv2.domain.entities import CommentReplyData
from commentv2.domain.exceptions import CommentReplyException
from commentv2.domain.services.services import comment_reply_create
from commentv2.domain.services.task_services import create_invisible_comment
from commentv2.interface.serializers import CommentReplyDataSerializer
from common.choices import OrderType
from common.constants import USER_NOT_PERMITTED_ERROR
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeSerializer
from common.serializers import (
    BaseSerializer,
    CustomEndDateField,
    HashIdField,
)
from common.services import TemplateApiInterface
from core.apis import OrgBaseApi
from core.constants import ExternalWebhookType
from core.organization.domain.entities import OrganizationConfigBaseData
from core.organization.domain.mixins import ExternalWebhookFileMixin
from core.selectors import organization_get
from core.serializers import UserSerializer
from element.domain.services import BaseElementQuantityService
from external_services.messengers.email.entities import TemplateModule
from microcontext.choices import MicroContextChoices
from order.data.choices import OrderTypeChoices
from order.data.models import OrderRequestMapping, VendorOrder
from order.data.repositories import (
    OrderElementSyncRepo,
    OrderRepo,
    OrderRequestMappingRepo,
    OrderSnapshotRepo,
    OrganizationPOConfigRepo,
    VendorRepo,
)
from order.data.selectors.selector_v1 import (
    fetch_order_document_config,
    get_order_detail,
    order_request_comment_id_fetch,
    order_request_secondary_comment_update,
    vendor_order_fetch,
    vendor_order_fetch_via_snapshot,
)
from order.data.selectors.selectors import client_order_list
from order.domain.entities.entities import OrganizationPOConfigEntity, VendorInfoData
from order.domain.factories import OrderServiceFactory, RequestToOrderCallbackFactory
from order.interface.serializers import (
    OrderElementGuidelineSnapshotModelSerializer,
    OrderElementPreviewFileSnapshotModelSerializer,
    OrderElementProductionDrawingSnapshotModelSerializer,
    OrderElementSnapshotModelSerializer,
    OrderSerializer,
    OrderSnapshotModelSerializer,
    OrganizationPOConfigEntitySerializer,
    SnapshotDetailSerializer,
    VendorInfoDataSerializer,
)
from order.interface.vendor_serializers import VendorOrderModelSerializer, VendorOrderVendorInfoUpdateSerializer
from order.services import (
    generate_item_type_and_category_list,
)
from order.services.order import (
    annotate_order_progress_percentage,
    filter_purchase_orders_for_po_preview,
    update_vendor_info_v2,
)
from project.interface.apis.internal.apis import ProjectBaseApi
from task.domain.utils import mentioned_user_ids_get_by_description
from task.interfaces.serializers import CommentReplyModelSerializer, TaskReplyCreateInputSerializer
from vendor.interface.serializers import VendorModelSerializer

logger = structlog.get_logger(__name__)


class OrderActionBaseApi(ResourceActionBaseApi):
    def validate_request_resource_mapping(self, request_id: int, order_id: int):
        if not OrderRequestMapping.objects.filter(request_id=request_id, resource_id=order_id).exists():
            logger.info("Request not found for Resource", request_id=request_id, order_id=order_id)
            self.set_response_message("Please reload and try again")
            raise ValidationError(_("Request not found for Resource"))


class OrderRequestApproveApi(OrderActionBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        operation_id="order_request_approve_api",
        operation_description="Order Api for Approve Request",
    )
    @transaction.atomic
    def post(self, request, request_id, order_id, *args, **kwargs):
        data = self.validate_input_data(request)
        self.validate_request_resource_mapping(request_id, order_id)
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.APPROVE_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.APPROVE.value

        order = vendor_order_fetch(vendor_order_id=order_id).annotate_request_data().first()
        self.resource_action_service = RequestActionServiceFactory.get_service(
            data=request_action_data_get(request_data=order.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
        )
        self.resource_callback = RequestToOrderCallbackFactory.get_callback(
            resource_id=order_id,
            user_id=self.get_user_id(),
            order_repo=OrderRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        self.request_action_data = ResourceToRequestApproveServiceData(
            request_id=request_id,
            approve_by_id=request.user.pk,
            remark=data.get("remark"),
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request approver service input data ",
            ResourceToRequestApproveServiceData=self.request_action_data,
        )
        self.set_context(context=order.context)
        self.set_context_id(context_id=order_id)
        logger.info("Request approval started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        except ResourceToRequestActionService.RequestApproveException:
            self.set_response_message("Request is not approved.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not approved.")})
        self.set_response_message("Request is successfully approved.")
        return Response(status=HTTP_200_OK)


class OrderRequestRejectApi(OrderActionBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        operation_id="order_request_reject_api",
        operation_description="Order Api for Reject Request",
    )
    @transaction.atomic
    def post(self, request, request_id, order_id, *args, **kwargs):
        data = self.validate_input_data(request)
        self.validate_request_resource_mapping(request_id, order_id)
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.REJECT_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.REJECT.value

        order = vendor_order_fetch(vendor_order_id=order_id).annotate_request_data().first()
        self.resource_action_service = RequestActionServiceFactory.get_service(
            data=request_action_data_get(request_data=order.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
        )
        self.resource_callback = RequestToOrderCallbackFactory.get_callback(
            resource_id=order_id,
            user_id=self.get_user_id(),
            order_repo=OrderRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        self.request_action_data = ResourceToRequestRejectServiceData(
            context=MicroContextChoices.ORDER.value,
            context_id=order_id,
            org_id=self.get_organization_id(),
            request_id=request_id,
            reject_by_id=request.user.pk,
            remark=data.get("remark"),
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request reject service input data ",
            ResourceToRequestApproveServiceData=self.request_action_data,
        )
        self.set_context(context=order.context)
        self.set_context_id(context_id=order_id)
        logger.info("Request rejection started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        except ResourceToRequestActionService.RequestApproveException:
            self.set_response_message("Request is not rejected.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not rejected.")})
        self.set_response_message("Request is successfully rejected.")
        return Response(status=HTTP_200_OK)


class OrderRequestHoldApi(OrderActionBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RequestActionInputSerializer

    @swagger_auto_schema(
        request_body=RequestActionInputSerializer,
        operation_id="order_request_hold_api",
        operation_description="Order Api for Hold Request",
    )
    @transaction.atomic
    def post(self, request, request_id, order_id, *args, **kwargs):
        data = self.validate_input_data(request)
        self.validate_request_resource_mapping(request_id, order_id)
        if data.get("is_admin") is True:
            self.request_action = RequestActionEnum.HOLD_AS_ADMIN.value
        else:
            self.request_action = RequestActionEnum.HOLD.value

        order = vendor_order_fetch(vendor_order_id=order_id).annotate_request_data().first()
        self.resource_action_service = RequestActionServiceFactory.get_service(
            data=request_action_data_get(request_data=order.request_data),
            is_admin=request.user.token_data.is_admin,
            user_id=self.get_user_id(),
        )
        self.resource_callback = RequestToOrderCallbackFactory.get_callback(
            resource_id=order_id,
            user_id=self.get_user_id(),
            order_repo=OrderRepo(),
            boq_sync_repo=BoqElementSyncRepo(),
            mapping_repo=OrderRequestMappingRepo(),
            order_sync_repo=OrderElementSyncRepo(),
            order_snapshot_repo=OrderSnapshotRepo(),
        )
        self.request_action_data = ResourceToRequestHoldServiceData(
            context=order.context,
            context_id=order_id,
            org_id=self.get_organization_id(),
            request_id=request_id,
            hold_by_id=request.user.pk,
            is_admin=self.is_admin_with_permission(is_admin_flag=data.get("is_admin", False)),
        )
        logger.info(
            "Resource to request hold service input data ",
            ResourceToRequestHoldServiceData=self.request_action_data,
        )
        self.set_context(context=order.context)
        self.set_context_id(context_id=order_id)
        logger.info("Request Hold started")
        try:
            super().post(request, request_id, *args, **kwargs)
        except ResourceActionBaseApi.UserNotPermittedError:
            self.set_response_message("Higher level approver has already taken an action.")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error_code": USER_NOT_PERMITTED_ERROR})
        except ResourceToRequestActionService.RequestApproveException:
            self.set_response_message("Request is not hold.")
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: _("Request is not Hold.")})
        self.set_response_message("Request is successfully Hold.")
        return Response(status=HTTP_200_OK)


class OrderSnapshotFetchApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        is_client_order = serializers.BooleanField(default=False)

        class Meta:
            ref_name = "OrderSnapshotFilter"

    class OutputSerializer(OrderSnapshotModelSerializer):
        rc_count = serializers.IntegerField()
        purchase_orders = VendorOrderModelSerializer.VendorPurchaseOrderSerializer(
            source="sorted_purchase_orders", many=True
        )
        poc = UserSerializer(source="order.poc")
        request_id = HashIdField()
        task_id = HashIdField()
        updated_at = serializers.DateTimeField(source="order.updated_at")
        po_status = serializers.CharField(source="order.po_status")
        po_attached_at = serializers.DateTimeField(source="order.po_attached_at")
        is_discount_col_visible = serializers.BooleanField(source="is_discounted")
        creator_org_id = HashIdField(source="created_by.org_id")
        type_of_order = serializers.CharField(source="order.order_type")
        org_to = serializers.SerializerMethodField()
        status = serializers.CharField()

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            return self.OutgoingOrgSerializer(obj.order.org_to, context={"gst_number": obj.gst_number}).data

        class Meta(OrderSnapshotModelSerializer.Meta):
            ref_name = "VendorOrderOutput"
            fields = (
                "id",
                "request_id",
                "task_id",
                "status",
                "started_at",
                "updated_at",
                "due_at",
                "terms_and_conditions",
                "po_status",
                "order_elements",
                "po_attached_at",
                "updated_by",
                "purchase_orders",
                "project",
                "attachments",
                "order_number",
                "shipping_address",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "created_by",
                "issued_at",
                "work_order_from",
                "shipping_address_header",
                "comment_count",
                "rate_contract",
                "rc_count",
                "deductions",
                "poc",
                "deduction_snapshots",
                "is_discount_col_visible",
                "creator_org_id",
                "type_of_order",
                "is_taxed",
                "payment_tnc",
                "other_tnc",
            )
            output_hash_id_fields = (
                "id",
                # "request_id",
                # "task_id",
                "payment_term_id",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_snapshot_fetch",
    )
    def get(self, request, order_id, order_snapshot_id, *args, **kwargs):
        data = self.validate_filter_data()
        order_snapshot_repo = OrderSnapshotRepo()
        order_snapshot = order_snapshot_repo.get_order_snapshot_details(
            order_snapshot_id=order_snapshot_id, org_id=self.get_organization_id()
        )

        order_factory = OrderServiceFactory()
        status_interactor = order_factory.get_status_interactor()
        if data.get("is_client_order"):
            status = status_interactor.get_client_order_status(order_snapshot.outgoing_status)
        else:
            status = status_interactor.get_vendor_order_status(order_snapshot.outgoing_status)
        setattr(order_snapshot, "status", status)

        return Response(
            self.OutputSerializer(
                order_snapshot,
                context={"is_client_order": data.get("is_client_order"), "timezone": self.get_organization_timezone()},
            ).data,
            status=HTTP_200_OK,
        )


class OrderElementSnapshotDetailsAPi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrderElementSnapshotModelSerializer):
        class ProductionDrawingSerializer(OrderElementProductionDrawingSnapshotModelSerializer):
            class Meta(OrderElementProductionDrawingSnapshotModelSerializer.Meta):
                ref_name = "vendor-order-element-drawing"
                fields = ("id", "file", "tags", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class PreviewFileSerializer(OrderElementPreviewFileSnapshotModelSerializer):
            class Meta(OrderElementPreviewFileSnapshotModelSerializer.Meta):
                ref_name = "vendor-order-element-preview-file"
                fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class GuidelinesSerializer(OrderElementGuidelineSnapshotModelSerializer):
            class Meta(OrderElementGuidelineSnapshotModelSerializer.Meta):
                ref_name = "vendor-order-element-guideline-serializer"
                fields = ("id", "name", "description", "attachments", "created_at")

        production_drawings = ProductionDrawingSerializer(many=True)
        preview_files = PreviewFileSerializer(many=True)
        preview_file_count = serializers.SerializerMethodField()

        guidelines = GuidelinesSerializer(many=True)
        comment_count = serializers.IntegerField(default=0)

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_preview_file_count(self, obj):
            return obj.preview_file_count if hasattr(obj, "preview_file_count") else 0

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        class Meta(OrderElementSnapshotModelSerializer.Meta):
            fields = (
                "id",
                "name",
                "description",
                "category",
                "code",
                "uom",
                "uom_name",
                "quantity",
                "quantity_dimensions",
                "preview_files",
                "item_type",
                "production_drawings",
                "guidelines",
                "client_rate",
                "vendor_rate",
                "preview_file_count",
                "vendor_rate",
                "status",
                "comment_count",
                "brand_name",
                "hsn_code",
            )
            ref_name = "OrderElementSnapshotViewOutput"
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_element_snapshot_details",
    )
    def get(self, request, *args, **kwargs):
        order_snapshot_repo = OrderSnapshotRepo()
        order_element_snapshot_detail = order_snapshot_repo.get_order_element_snapshot_details(
            element_id=kwargs.get("order_element_snapshot_id"), org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(order_element_snapshot_detail).data, status=HTTP_200_OK)


class VendorOrderSnapshotListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrderSnapshotModelSerializer):
        progress_percentage = serializers.IntegerField()
        closed_at = serializers.DateTimeField()
        cancelled_at = serializers.DateTimeField(source="order.cancelled_at")
        completed_at = serializers.DateTimeField(source="order.completed_at")
        issued_at = serializers.DateTimeField(source="order.issued_at")
        updated_at = serializers.DateTimeField(source="order.updated_at")
        po_attached_at = serializers.DateTimeField(source="order.po_attached_at")
        issued_by = serializers.SerializerMethodField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()
        amount = serializers.DecimalField(max_digits=20, decimal_places=2)

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                pending_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("pending_payment_request_amount")
                )
            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                approved_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("approved_payment_request_amount")
                )

            return approved_amount

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        def to_representation(self, instance):
            data = super().to_representation(instance)
            data["item_type"], data["category"] = generate_item_type_and_category_list(data)
            del data["order_elements"]
            return data

        class Meta(OrderSnapshotModelSerializer.Meta):
            ref_name = "OrderListOutput"
            fields = (
                "id",
                "status",
                "amount",
                "tax_amount",
                "invoice_status",
                "updated_at",
                "created_at",
                "po_status",
                "status",
                "work_order_from",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "po_attached_at",
                "updated_by",
                "order_elements",
                "order_number",
                "purchase_orders",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "issued_at",
                "completed_at",
                "cancelled_at",
                "due_at",
                "progress_percentage",
                "closed_at",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
                "type_of_order",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_snapshot_list_api",
        operation_summary="Vendor Order Snapshot list api",
    )
    def get(self, request, order_id, **kwargs):
        order_snapshot_repo = OrderSnapshotRepo()
        vendor_order_snapshots = order_snapshot_repo.get_vendor_order_snapshots(order_id=order_id)

        return Response(
            self.OutputSerializer(
                vendor_order_snapshots,
                many=True,
                context={"order_type": OrderType.INCOMING, "timezone": self.get_organization_timezone()},
            ).data,
            status=HTTP_200_OK,
        )


class ClientOrderSnapshotListApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrderSnapshotModelSerializer):
        progress_percentage = serializers.IntegerField()
        closed_at = serializers.DateTimeField()
        cancelled_at = serializers.DateTimeField(source="order.cancelled_at")
        completed_at = serializers.DateTimeField(source="order.completed_at")
        due_at = serializers.DateTimeField(source="order.due_at")
        issued_at = serializers.DateTimeField(source="order.issued_at")
        updated_at = serializers.DateTimeField(source="order.updated_at")
        issued_by = serializers.SerializerMethodField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()
        status = serializers.CharField()
        amount = serializers.DecimalField(max_digits=20, decimal_places=2)

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                pending_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("pending_payment_request_amount")
                )
            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                approved_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("approved_payment_request_amount")
                )

            return approved_amount

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        def to_representation(self, instance):
            data = super().to_representation(instance)
            data["item_type"], data["category"] = generate_item_type_and_category_list(data)
            del data["order_elements"]
            return data

        class Meta(OrderSnapshotModelSerializer.Meta):
            ref_name = "OrderListOutput"
            fields = (
                "id",
                "status",
                "amount",
                "tax_amount",
                "invoice_status",
                "updated_at",
                "created_at",
                "po_status",
                "work_order_from",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "po_attached_at",
                "updated_by",
                "order_elements",
                "order_number",
                "purchase_orders",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "issued_at",
                "completed_at",
                "cancelled_at",
                "due_at",
                "progress_percentage",
                "closed_at",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
                "type_of_order",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_order_snapshot_list_api",
        operation_summary="Client Order Snapshot list api",
    )
    def get(self, request, order_id, **kwargs):
        order_snapshot_repo = OrderSnapshotRepo()
        client_order_snapshots = order_snapshot_repo.get_client_order_snapshots(order_id=order_id)

        order_factory = OrderServiceFactory()
        status_interactor = order_factory.get_status_interactor()

        for snapshot in client_order_snapshots:
            status = status_interactor.get_client_order_status(snapshot.outgoing_status)
            setattr(snapshot, "status", status)

        return Response(
            self.OutputSerializer(client_order_snapshots, many=True, context={"order_type": OrderType.INCOMING}).data,
            status=HTTP_200_OK,
        )


class ClientOrderListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    parser_classes = (MultiPartParser,)
    pagination_class = None

    class OutputSerializer(VendorOrderModelSerializer):
        org_to = serializers.SerializerMethodField()
        progress_percentage = serializers.IntegerField()
        closed_at = serializers.DateTimeField()
        pending_payment_request_amount = serializers.SerializerMethodField()
        approved_payment_request_amount = serializers.SerializerMethodField()
        is_snapshot_available = serializers.BooleanField()
        item_type = serializers.SerializerMethodField()
        category = serializers.SerializerMethodField()
        due_at = CustomEndDateField(source="order_due_at")
        issued_at = serializers.DateTimeField(source="order_issued_at")
        invoice_status = serializers.SerializerMethodField()
        type_of_order = serializers.CharField(source="order_type")
        status = serializers.CharField()
        amount_without_tax = serializers.SerializerMethodField()
        tax_amount = serializers.DecimalField(source="order_tax_amount", max_digits=20, decimal_places=2, default=0)
        amount = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
        def get_amount_without_tax(self, obj):
            return round(obj.order_amount, 2)

        @swagger_serializer_method(serializer_or_field=serializers.DecimalField(max_digits=20, decimal_places=2))
        def get_amount(self, obj):
            return round(obj.order_amount + obj.order_tax_amount, 2)

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_invoice_status(self, obj):
            if obj.last_approved_snapshot:
                return obj.order_invoice_status[1:-1]
            return obj.order_invoice_status

        class CategorySerializer(serializers.Serializer):
            id = HashIdField()
            name = serializers.CharField()
            code = serializers.CharField()

            class Meta:
                ref_name = "CategorySerializer"
                fields = ("id", "name", "code")

        class ItemTypeSerializer(serializers.Serializer):
            id = HashIdField()
            name = serializers.CharField()
            color_code = serializers.CharField()

            class Meta:
                ref_name = "ItemTypeSerializer"
                fields = ("id", "name", "color_code")

        def get_category(self, obj):
            if hasattr(obj, "category") and obj.category:
                return self.CategorySerializer(obj.category, many=True).data

        def get_item_type(self, obj):
            if hasattr(obj, "item_type") and obj.item_type:
                return self.ItemTypeSerializer(obj.item_type, many=True).data

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_pending_payment_request_amount(self, obj):
            pending_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                pending_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("pending_payment_request_amount")
                )

            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                pending_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("pending_payment_request_amount")
                )
            return pending_amount

        def get_approved_payment_request_amount(self, obj):
            approved_amount = decimal.Decimal(0)
            if hasattr(obj, "purchase_order_payment_request") and obj.purchase_order_payment_request:
                approved_amount += decimal.Decimal(
                    obj.purchase_order_payment_request.get("approved_payment_request_amount")
                )
            if hasattr(obj, "invoice_payment_request_amount") and obj.invoice_payment_request_amount:
                approved_amount += decimal.Decimal(
                    obj.invoice_payment_request_amount.get("approved_payment_request_amount")
                )

            return approved_amount

        class VendorSerializer(VendorModelSerializer):
            organization_id = HashIdField()

            class Meta(VendorModelSerializer.Meta):
                ref_name = "VendorOrderOutputList"
                fields = ("organization_id", "name", "code")

        @swagger_serializer_method(serializer_or_field=VendorOrderModelSerializer.OutgoingOrgSerializer())
        def get_org_to(self, obj):
            return self.OutgoingOrgSerializer(obj.org_to).data

        def to_representation(self, instance):
            data = super().to_representation(instance)
            return data

        class Meta(VendorOrderModelSerializer.Meta):
            ref_name = "OrderListOutput"
            fields = (
                "id",
                "status",
                "amount",
                "amount_without_tax",
                "tax_amount",
                "invoice_status",
                "updated_at",
                "po_status",
                "work_order_from",
                "cancelled_elements_count",
                "not_cancelled_elements_count",
                "po_attached_at",
                "updated_by",
                # "order_elements",
                "order_number",
                "purchase_orders",
                "org_to",
                "org_from",
                "origin_org",
                "issued_by",
                "issued_at",
                "completed_at",
                "cancelled_at",
                "due_at",
                "comment_count",
                "progress_percentage",
                "closed_at",
                "pending_payment_request_amount",
                "approved_payment_request_amount",
                "is_snapshot_available",
                "item_type",
                "category",
                "type_of_order",
            )
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_order_list_api",
        operation_summary="Client Order list api",
    )
    def get(self, request, **kwargs):
        orders: List[VendorOrder] = client_order_list(
            project_id=kwargs.get("project_id"), org_id=self.get_organization_id()
        )
        annotate_order_progress_percentage(orders=orders)

        order_factory = OrderServiceFactory()
        status_interactor = order_factory.get_status_interactor()

        for order in orders:
            status = status_interactor.get_client_order_status(order.order_status)
            setattr(order, "status", status)

        return Response(
            self.OutputSerializer(orders, many=True, context=self.get_output_context()).data,
            status=HTTP_200_OK,
        )


class OrderRequestReplyListApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []
    serializer_class = CommentReplyModelSerializer

    def get_queryset(self, *args, **kwargs):
        comment_id = order_request_comment_id_fetch(
            resource_id=self.kwargs.get("order_id"),
            request_id=self.kwargs.get("request_id"),
            org_id=self.get_organization_id(),
            is_snapshot=False,
        )
        if not comment_id:
            return []
        return get_comment_replies(comment_id=comment_id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: CommentReplyModelSerializer(many=True)},
        operation_id="order_request_reply_list_api",
        operation_summary="Order Request Reply List API",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class OrderSnapshotRequestReplyListApi(OrgBaseApi, ListModelMixin):
    REQUIRED_PERMISSIONS = []
    serializer_class = CommentReplyModelSerializer

    def get_queryset(self, *args, **kwargs):
        comment_id = order_request_comment_id_fetch(
            resource_id=self.kwargs.get("snapshot_id"),
            request_id=self.kwargs.get("request_id"),
            org_id=self.get_organization_id(),
            is_snapshot=True,
        )
        if not comment_id:
            return []
        return get_comment_replies(comment_id=comment_id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: CommentReplyModelSerializer(many=True)},
        operation_id="order_snaphot_reply_list_api",
        operation_summary="Order Snapshot Request Reply List API",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class OrderRequestReplyCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = TaskReplyCreateInputSerializer

    @swagger_auto_schema(
        request_body=CommentReplyDataSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="order_request_reply_create_api",
        operation_summary="Order Request Reply Create API",
    )
    @transaction.atomic
    def post(self, request, order_id, request_id, *args, **kwargs):
        data = self.validate_input_data()
        comment_id = order_request_comment_id_fetch(
            resource_id=order_id, request_id=request_id, org_id=self.get_organization_id()
        )
        order = vendor_order_fetch(vendor_order_id=order_id).first()
        is_task_callback_allowed = True
        if comment_id is None and self.get_organization_id() == order.org_to_id:
            comment_id = create_invisible_comment(
                context=order.context,
                org_id=self.get_organization_id(),
                project_id=order.project_id,
                user_id=self.get_user_id(),
                context_id=order_id,
            )
            order_request_secondary_comment_update(order_id=order_id, request_id=request_id, comment_id=comment_id)
            is_task_callback_allowed = False
        if not comment_id:
            return Response(status=HTTP_404_NOT_FOUND)
        try:
            type = (
                CommentTypeEnum.NORMAL
                if len(mentioned_user_ids_get_by_description(data.get("description"))) == 0
                else CommentTypeEnum.MENTION
            )
            instance = comment_reply_create(
                data=CommentReplyData(type=type.value, blocks=data.get("description")),
                comment_id=comment_id,
                user_id=self.get_user_id(),
                project_id=order.project_id,
                org_id=self.get_organization_id(),
                is_task_callback_allowed=is_task_callback_allowed,
            )
        except CommentReplyException as e:
            logger.info(e.message)
            self.set_response_message("Error occurred while posting reply, Please try again.")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            CommentReplyModelSerializer(instance, context=self.get_serializer_context()).data,
            status=HTTP_201_CREATED,
        )


class OrderSnapshotRequestReplyCreateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = TaskReplyCreateInputSerializer

    @swagger_auto_schema(
        request_body=CommentReplyDataSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="order_snpashot_request_reply_create_api",
        operation_summary="Order Snapshot Request Reply Create API",
    )
    @transaction.atomic
    def post(self, request, snapshot_id, request_id, *args, **kwargs):
        data = self.validate_input_data()
        comment_id = order_request_comment_id_fetch(
            resource_id=snapshot_id,
            request_id=request_id,
            org_id=self.get_organization_id(),
            is_snapshot=True,
        )
        order = vendor_order_fetch_via_snapshot(snapshot_id=snapshot_id)
        is_task_callback_allowed = True
        if comment_id is None and self.get_organization_id() == order.org_to_id:
            comment_id = create_invisible_comment(
                context=order.context,
                org_id=self.get_organization_id(),
                project_id=order.project_id,
                user_id=self.get_user_id(),
                context_id=order.pk,
            )
            order_request_secondary_comment_update(order_id=order.pk, request_id=request_id, comment_id=comment_id)
            is_task_callback_allowed = False
        if not comment_id:
            return Response(status=HTTP_404_NOT_FOUND)
        try:
            type = (
                CommentTypeEnum.NORMAL
                if len(mentioned_user_ids_get_by_description(data.get("description"))) == 0
                else CommentTypeEnum.MENTION
            )
            instance = comment_reply_create(
                data=CommentReplyData(type=type.value, blocks=data.get("description")),
                comment_id=comment_id,
                user_id=self.get_user_id(),
                project_id=order.project_id,
                org_id=self.get_organization_id(),
                is_task_callback_allowed=is_task_callback_allowed,
            )
        except CommentReplyException as e:
            logger.info(e.message)
            self.set_response_message("Error occurred while posting reply, Please try again.")
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(
            CommentReplyModelSerializer(instance, context=self.get_serializer_context()).data,
            status=HTTP_201_CREATED,
        )


class OrderPoConfigUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        can_upload_po_anytime = serializers.BooleanField(default=False)
        is_po_auto_attach = serializers.BooleanField(default=False)

        class Meta:
            ref_name = "OrderPoConfigUpdateInputDataSerializer"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="order_po_config_update_api",
        operation_summary="Order PO Config Update API",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        org_po_config_repo = OrganizationPOConfigRepo()
        org_po_config_repo.update_po_config(
            upload_po_anytime=data.get("can_upload_po_anytime"),
            auto_attach_po=data.get("is_po_auto_attach"),
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )

        return Response(status=HTTP_200_OK)


class OrderPoConfigApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationPOConfigEntitySerializer):
        class Meta:
            ref_name = "OrderPoConfigOutputDataSerializer"
            dataclass = OrganizationPOConfigEntity

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="order_po_config_get_api",
        operation_summary="Order PO Config Get API",
    )
    def get(self, request, *args, **kwargs):
        org_po_config_repo = OrganizationPOConfigRepo()
        org_po_config = org_po_config_repo.get_po_config(org_id=self.get_organization_id())
        return Response(self.OutputSerializer(org_po_config).data, status=HTTP_200_OK)


class TaskOrderDetailApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        snapshot_id = HashIdField(allow_null=True, required=True)

        class Meta:
            ref_name = "TaskOrderDetailFilter"

    @swagger_auto_schema(
        responses={HTTP_200_OK: SnapshotDetailSerializer()},
        operation_id="task_order_detail_api",
        operation_summary="Task Order Detail API",
    )
    def get(self, request, request_id, order_id, *args, **kwargs):
        data = self.validate_filter_data()
        if not data.get("snapshot_id"):
            order_data = get_order_detail(order_id=order_id, org_id=self.get_organization_id())
            serializer = OrderSerializer
        else:
            order_snapshot_repo = OrderSnapshotRepo()
            order_data = order_snapshot_repo.get_order_snapshot_details(
                order_snapshot_id=data.get("snapshot_id"), org_id=self.get_organization_id()
            )
            serializer = SnapshotDetailSerializer

        order_factory = OrderServiceFactory()
        status_interactor = order_factory.get_status_interactor()
        status = status_interactor.get_vendor_order_status(order_data.outgoing_status)
        setattr(order_data, "status", status)

        order_data = filter_purchase_orders_for_po_preview(order_data=order_data)
        return Response(serializer(order_data, context=self.get_output_context()).data, status=HTTP_200_OK)


# NOTE: Deprecated
class VendorInfoUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(VendorInfoDataSerializer):
        class Meta:
            ref_name = "VendorInfoUpdateInputDataSerializer"
            dataclass = VendorInfoData

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="vendor_info_update_api",
        operation_summary="Vendor Info Update API",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        vendor_repo = VendorRepo()
        try:
            vendor_repo.update_vendor_info(
                order_id=kwargs.get("order_id"),
                vendor_info_data=data,
            )
        except vendor_repo.VendorOrderNotFound as e:
            logger.error(e.message)
            self.set_response_message("Vendor Order not found, Please try again.")
            return Response(status=HTTP_400_BAD_REQUEST)

        return Response(status=HTTP_200_OK)


class VendorInfoUpdateApiV2(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = VendorOrderVendorInfoUpdateSerializer.drf_serializer

    @swagger_auto_schema(
        request_body=VendorOrderVendorInfoUpdateSerializer.drf_serializer,
        responses={HTTP_200_OK: ""},
        operation_id="vendor_info_update_api_v2",
        operation_summary="Vendor Info Update API V2",
    )
    @transaction.atomic
    def put(self, request, order_id, *args, **kwargs):
        data: VendorOrderVendorInfoUpdateSerializer = self.validate_input_data()
        update_vendor_info_v2(
            order_id=order_id,
            vendor_info_data=dict(data.document_config_data),
            user_id=self.get_user_id(),
            org_id=self.get_organization_id(),
        )
        self.set_response_message("Vendor Info updated successfully.")
        return Response(status=HTTP_200_OK)


class VendorOrderPOPreviewUploadApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        file = serializers.FileField()

        class Meta:
            ref_name = "VendorOrderPOPreviewUploadInputDataSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="vendor_order_po_preview_upload_api",
        operation_summary="Vendor Order PO Preview Upload API",
    )
    @transaction.atomic
    def post(self, request, order_id, *args, **kwargs):
        data = self.validate_input_data()
        vendor_order_repo = OrderRepo()
        try:
            vendor_order_repo.upload_po_preview(
                order_id=order_id,
                file=data.get("file"),
            )
        except vendor_order_repo.OrderNotFoundError as e:
            logger.error(e.message)
            self.set_response_message("Vendor Order not found, Please try again.")
            return Response(status=HTTP_400_BAD_REQUEST)

        return Response(status=HTTP_201_CREATED)


class VendorOrderDocumentConfigApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        vendor_id = HashIdField()
        client_id = HashIdField(required=False, allow_null=True)

        class Meta:
            ref_name = "VendorOrderDocumentConfigApiFilter"

    serializer_class = OrganizationConfigBaseData.drf_serializer
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationConfigBaseData.drf_serializer()},
        operation_id="vendor_order_document_config_api",
        operation_summary="vendor order document config api",
    )
    def get(self, request, *args, **kwargs):
        vendor_id = self.validate_filter_data().get("vendor_id")
        vendor_country_id = organization_get(org_id=vendor_id).country_id
        config = fetch_order_document_config(country_id=vendor_country_id)
        self.set_response_message("Vendor Order Document Config fetched successfully.")
        return Response(self.serializer_class({"sections": config}).data, status=HTTP_200_OK)


class VendorOrderPoPreviewApi(ExternalWebhookFileMixin):
    REQUIRED_PERMISSIONS = [Permissions.CAN_PREVIEW_PURCHASE_ORDER]

    class InputSerializer(BaseSerializer):
        class OrderElementSerializer(BaseSerializer):
            class UomSerializer(BaseSerializer):
                name = serializers.CharField(allow_null=True)
                value = serializers.IntegerField(allow_null=True)

                class Meta:
                    ref_name = "po-preview-vendor-order-element-uom"

            class CategorySerializer(serializers.Serializer):
                id = HashIdField(allow_null=True)
                name = serializers.CharField(allow_null=True)
                code = serializers.CharField(allow_null=True)

                class Meta:
                    ref_name = "po-preview-vendor-order-element-category-"

            class ItemTypeSerializer(serializers.Serializer):
                id = HashIdField(allow_null=True)
                name = serializers.CharField(allow_null=True)
                color_code = serializers.CharField(allow_null=True)

                class Meta:
                    ref_name = "po-preview-item-type"

            name = serializers.CharField(allow_null=True)
            description = serializers.CharField(allow_null=True)
            uom = UomSerializer(allow_null=True)
            category = CategorySerializer(allow_null=True)
            item_type = ItemTypeSerializer(allow_null=True)
            quantity = serializers.CharField(allow_null=True)
            client_rate = serializers.CharField(allow_null=True)
            order_rate = serializers.CharField(allow_null=True)
            brand = serializers.CharField(allow_null=True, allow_blank=True)
            budget_rate = serializers.CharField(allow_null=True)
            hsn = serializers.CharField(allow_null=True, allow_blank=True)
            tax_percent = serializers.CharField(allow_null=True)
            final_amount = serializers.DecimalField(max_digits=20, decimal_places=2)

            class Meta:
                ref_name = "PoPreviewOrderElementInputDataSerializer"

        class POSerializer(serializers.Serializer):
            id = HashIdField(allow_null=True)
            file_name = serializers.CharField(allow_null=True)
            number = serializers.CharField(allow_null=True)
            file = serializers.URLField(allow_null=True)
            date = serializers.CharField(allow_null=True)
            amount = serializers.CharField(allow_null=True)
            tax_amount = serializers.CharField(allow_null=True)

            class Meta:
                ref_name = "PoPreviewPurchaseOrdersInputDataSerializer"

        elements = OrderElementSerializer(many=True, default=[])
        purchase_orders = POSerializer(many=True, default=[])
        order_id = HashIdField(allow_null=True)
        order_number = serializers.CharField(allow_null=True)
        order_type = serializers.ChoiceField(choices=OrderTypeChoices.choices, allow_null=True)
        vendor_name = serializers.CharField(allow_null=True)
        vendor_code = serializers.CharField(allow_null=True)
        job_id = serializers.CharField(allow_null=True)
        created_by_id = HashIdField(allow_null=True)
        order_from = serializers.CharField(allow_null=True)
        org_from_name = serializers.CharField(allow_blank=True, allow_null=True)
        org_to_name = serializers.CharField(allow_blank=True, allow_null=True)
        order_poc_id = HashIdField(allow_null=True)
        order_poc_name = serializers.CharField(allow_null=True)
        shipping_address = serializers.CharField(allow_null=True)
        start_date = serializers.DateField(format="%Y-%m-%d", allow_null=True)
        end_date = serializers.DateField(format="%Y-%m-%d", allow_null=True)
        order_status = serializers.CharField(allow_null=True)
        po_status = serializers.CharField(allow_null=True)
        issued_by_id = HashIdField(allow_null=True)
        issued_by_name = serializers.CharField(allow_null=True)
        issued_at = serializers.DateTimeField(format="%Y-%m-%dT%H:%M:%S", allow_null=True)
        total_amount = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
        other_tnc = serializers.ListField(
            child=TypeSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                        const.BULLET: None,
                        const.NUMBERED_LIST: None,
                    }
                }
            ),
            allow_null=True,
            default=[],
        )
        payment_tnc = serializers.ListField(
            child=TypeSerializer(
                context={
                    "allowed_type_choices": {
                        const.MENTION: None,
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.IMAGE: None,
                        const.LINE_BREAK: None,
                        const.HYPERLINK: TEXT_TYPE_LIST,
                        const.BULLET: None,
                        const.NUMBERED_LIST: None,
                    }
                }
            ),
            allow_null=True,
            default=[],
        )

        class Meta:
            ref_name = "VendorOrderPoPreviewInputDataSerializer"

    def get_webhook_data(self):
        data = self.validate_input_data()
        template_api_for_t_and_c = TemplateApiInterface(
            url=f"{settings.TEMPLATE_ENDPOINT}/{TemplateModule.COMMON.value}/rich-text-block/v1/html",
            data={"blocks": data.get("other_tnc") if data.get("other_tnc") else []},
        )
        template_api_for_payment_terms = TemplateApiInterface(
            url=f"{settings.TEMPLATE_ENDPOINT}/{TemplateModule.COMMON.value}/rich-text-block/v1/html",
            data={"blocks": data.get("payment_tnc") if data.get("payment_tnc") else []},
        )
        try:
            template_response_for_t_and_c = template_api_for_t_and_c.get_html()
            template_response_for_payment_terms = template_api_for_payment_terms.get_html()
        except TemplateApiInterface.TemplateApiException as e:
            logger.info("Request failed.", data=data, error=str(e))
            raise e

        data["terms_and_conditions"] = template_response_for_t_and_c
        data["payment_terms"] = template_response_for_payment_terms
        data["start_date"] = data.get("start_date").strftime("%Y-%m-%d") if data.get("start_date") else None
        data["end_date"] = data.get("end_date").strftime("%Y-%m-%d") if data.get("end_date") else None
        data["issued_at"] = data.get("issued_at").strftime("%Y-%m-%dT%H:%M:%S") if data.get("issued_at") else None
        return json.dumps(data, default=float)

    @swagger_auto_schema(
        responses={HTTP_200_OK: "preview-po"},
        operation_id="preview-po",
        operation_summary="Preview Purchase Order",
    )
    def post(self, request, *args, **kwargs):
        response = super().post(webhook_type=ExternalWebhookType.PREVIEW_PURCHASE_ORDER)
        return Response(data=response, status=HTTP_200_OK)
