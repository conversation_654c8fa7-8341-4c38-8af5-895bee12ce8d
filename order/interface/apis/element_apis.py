from typing import Dict, List

from django.conf import settings
from django.db import transaction
from django.http import HttpResponse
from django.utils.module_loading import import_string
from drf_yasg.utils import swagger_auto_schema, swagger_serializer_method
from rest_framework import serializers
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from boq.data.models import BoqElement
from boq.data.selectors import get_boq_element_error_message
from boq.interface.serializers import BoqElementModelSerializer
from client.domain.constants import C<PERSON><PERSON>ields
from common.apis import BaseApi
from common.exceptions import ExcelError, ExcelValidationError
from common.serializers import BaseSerializer, HashId<PERSON>ield, HashIdListField
from core.helpers import OrgPermissionHelper
from element.domain.services import BaseElementQuantityService
from order.data.choices import OrderStatusChoices
from order.data.models import <PERSON>endor<PERSON><PERSON><PERSON>, VendorOrderElement, VendorOrderElementPreviewFile
from order.data.selectors.selector_v1 import (
    client_order_element_details,
    order_elements_fetch,
    order_status_tooltip_data,
    vendor_order_element_details,
)
from order.domain.entities.domain_entities import OrderElementImportOutputData
from order.domain.excel_data_builder import OrderExcelDirectorService
from order.domain.mappings import ORDER_FIELDS_MAPPING
from order.interface.constants import EXCEL_SAMPLE_COLUMNS
from order.interface.serializers import OrderElementExportOutputDataSerializer, OrderElementOutputDataSerializer
from order.interface.sync_file import VendorOrderSyncExcel, vendor_order_sync
from order.interface.vendor_serializers import (
    VendorOrderElementGuidelineModelSerializer,
    VendorOrderElementModelSerializer,
    VendorOrderElementPreviewFileModelSerializer,
    VendorOrderElementProductionDrawingModelSerializer,
)
from order.services import (
    OrderElementService,
    vendor_order_element_excel_template_generate,
    vendor_order_sync_update,
)
from project.interface.apis.internal.apis import ProjectBaseApi
from proposal.domain.services import get_hidden_fields
from rollingbanners.comment_base_service import CommentBaseService

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class VendorOrderElementImportManyViaExcelApiV2(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        file = serializers.FileField()

        class Meta:
            ref_name = "VendorOrderElementImportManyViaExcelInputV2"

    parser_classes = (MultiPartParser,)
    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OrderElementExportOutputDataSerializer(many=True)},
        operation_id="vendor_order_element_import_many_via_excel",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        try:
            order_elements = OrderElementService.create_excel_import_data(
                project_id=kwargs.get("project_id"), file=data.get("file"), organization_id=self.get_organization_id()
            )
        except ExcelError as e:
            message = e.args[0]
            return Response(
                {
                    "error_code": message.get("error_code"),
                    "message": message.get("message"),
                    "description": message.get("description"),
                    "error_file": None,
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except ExcelValidationError as e:
            message: Dict = e.args[0]
            return Response(
                {
                    "error_code": message.get("error_code"),
                    "message": message.get("message"),
                    "description": message.get("description"),
                    "error_file": message.get("error_file"),
                },
                status=HTTP_400_BAD_REQUEST,
            )
        return Response(OrderElementExportOutputDataSerializer(order_elements, many=True).data, status=HTTP_201_CREATED)


class VendorOrderElementExcelTemplateDownloadApi(BaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: "elements.xlsx"},
        operation_id="vendor_order_element_excel_template_download",
        operation_summary="Download excel template for element upload.",
        deprecated=True,
    )
    def get(self, request, *args, **kwargs):
        response = HttpResponse(
            content=vendor_order_element_excel_template_generate(
                excel_columns=EXCEL_SAMPLE_COLUMNS, organization_id=self.get_organization_id()
            ),
            content_type="application/ms-excel",
        )
        response["Content-Disposition"] = "attachment; filename=elements.xlsx"
        return response


class VendorOrderElementExportExcelApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        # is_discount_col_visible = serializers.BooleanField(default=False)
        order_elements = HashIdListField()
        guidelines = serializers.BooleanField(default=False)
        client_rates = serializers.BooleanField(default=False)
        order_number = serializers.CharField()
        visible_columns = serializers.ListField(child=serializers.CharField(), default=[])

        class Meta:
            ref_name = "VendorOrderElementExcelExportInput"
            fields = ("order_elements", "guidelines")

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "excel-file"},
        operation_id="vendor_order_elements_export_excel",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        excel_file, filename = OrderExcelDirectorService(
            project_id=kwargs.get("project_id"),
            order_id=kwargs.get("order_id"),
            org_id=self.get_organization_id(),
            element_ids=data.get("order_elements"),
            with_guidelines=data.get("guidelines"),
            with_client_rate=data.get("client_rates"),
            visible_columns=data.get("visible_columns"),
            user=request.user,
        ).build_excel()
        response = HttpResponse(content=excel_file, content_type="application/ms-excel")
        response["Content-Disposition"] = f"attachment; filename={filename}.xlsx"
        return response


class VendorOrderElementImportViaBoqV2(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        boq_element_id_list = HashIdListField()

        class Meta:
            input_hash_id_fields = ("vendor_order_id",)
            ref_name = "VendorOrderElementImportBoqInputV2"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OrderElementOutputDataSerializer()},
        operation_id="vendor_order_element_via_boq",
    )
    @transaction.atomic()
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        element_entities: List[OrderElementImportOutputData] = OrderElementService.create_boq_import_data(
            boq_element_ids=data.get("boq_element_id_list")
        )
        return Response(OrderElementOutputDataSerializer(element_entities, many=True).data, status=HTTP_201_CREATED)


class VendorOrderElementMoveApiV2(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        element_id_list = HashIdListField()

        class Meta:
            ref_name = "VendorOrderElementMoveInputV2"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OrderElementOutputDataSerializer(many=True)},
        operation_id="vendor_order_elements_move",
    )
    @transaction.atomic()
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        moved_elements = OrderElementService.move_elements(element_ids=data.get("element_id_list"))
        return Response(OrderElementOutputDataSerializer(moved_elements, many=True).data, status=HTTP_201_CREATED)


class VendorOrderElementDetailsAPi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorOrderElementModelSerializer):
        class ProductionDrawingSerializer(VendorOrderElementProductionDrawingModelSerializer):
            class Meta(VendorOrderElementProductionDrawingModelSerializer.Meta):
                ref_name = "vendor-order-element-drawing"
                fields = ("id", "file", "tags", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class PreviewFileSerializer(VendorOrderElementPreviewFileModelSerializer):
            class Meta(VendorOrderElementPreviewFileModelSerializer.Meta):
                ref_name = "vendor-order-element-preview-file"
                fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class GuidelinesSerializer(VendorOrderElementGuidelineModelSerializer):
            class Meta(VendorOrderElementGuidelineModelSerializer.Meta):
                ref_name = "vendor-order-element-guideline-serializer"
                fields = ("id", "name", "description", "attachments", "created_at")

        production_drawings = ProductionDrawingSerializer(many=True)
        preview_files = PreviewFileSerializer(many=True)
        preview_file_count = serializers.SerializerMethodField()

        guidelines = GuidelinesSerializer(many=True)
        comment_count = serializers.IntegerField(default=0)
        hidden_fields = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_preview_file_count(self, obj):
            return obj.preview_file_count if hasattr(obj, "preview_file_count") else 0

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=ORDER_FIELDS_MAPPING,
                order_id=obj.vendor_order_id,
                org_id=self.context.get("org_id"),
            )

        class Meta(VendorOrderElementModelSerializer.Meta):
            fields = (
                "id",
                "name",
                "description",
                "category",
                "code",
                "uom",
                "uom_name",
                "quantity",
                "quantity_dimensions",
                "preview_files",
                "item_type",
                "production_drawings",
                "guidelines",
                "client_rate",
                "discounted_client_rate",
                "budget_rate",
                "vendor_rate",
                "preview_file_count",
                "vendor_rate",
                "hsn_code",
                "tax_percent",
                "brand_name",
                "status",
                "comment_count",
                "hidden_fields",
            )
            ref_name = "VendorOrderElementViewOutput"
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_order_element_details",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        order_element = vendor_order_element_details(element_id=kwargs.get("element_id"), org_id=org_id)
        return Response(
            self.OutputSerializer(order_element, context={"org_id": org_id}).data,
            status=HTTP_200_OK,
        )


class ClientOrderElementDetailsAPi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorOrderElementModelSerializer):
        class ProductionDrawingSerializer(VendorOrderElementProductionDrawingModelSerializer):
            class Meta(VendorOrderElementProductionDrawingModelSerializer.Meta):
                ref_name = "vendor-order-element-drawing"
                fields = ("id", "file", "tags", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class PreviewFileSerializer(VendorOrderElementPreviewFileModelSerializer):
            class Meta(VendorOrderElementPreviewFileModelSerializer.Meta):
                ref_name = "vendor-order-element-preview-file"
                fields = ("id", "type", "file", "is_main", "name", "uploaded_at")
                output_hash_id_fields = ("id",)

        class GuidelinesSerializer(VendorOrderElementGuidelineModelSerializer):
            class Meta(VendorOrderElementGuidelineModelSerializer.Meta):
                ref_name = "vendor-order-element-guideline-serializer"
                fields = ("id", "name", "description", "attachments", "created_at")

        production_drawings = ProductionDrawingSerializer(many=True)
        preview_files = PreviewFileSerializer(many=True)
        preview_file_count = serializers.SerializerMethodField()

        guidelines = GuidelinesSerializer(many=True)
        comment_count = serializers.IntegerField(default=0)
        hidden_fields = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.IntegerField())
        def get_preview_file_count(self, obj):
            return obj.preview_file_count if hasattr(obj, "preview_file_count") else 0

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        def get_hidden_fields(self, obj):
            return get_hidden_fields(
                client_field_mapping=ORDER_FIELDS_MAPPING,
                order_id=self.context.get("order_id"),
                org_id=self.context.get("org_id"),
            ) + [ClientFields.CLIENT_RATE.value, ClientFields.BUDGET_RATE.value]

        class Meta(VendorOrderElementModelSerializer.Meta):
            fields = (
                "id",
                "name",
                "description",
                "category",
                "code",
                "uom",
                "uom_name",
                "quantity",
                "quantity_dimensions",
                "preview_files",
                "item_type",
                "production_drawings",
                "guidelines",
                "client_rate",
                "vendor_rate",
                "preview_file_count",
                "vendor_rate",
                "hsn_code",
                "tax_percent",
                "status",
                "comment_count",
                "brand_name",
                "hidden_fields",
            )
            ref_name = "ClientOrderElementViewOutput"
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="client_order_element_details",
    )
    def get(self, request, *args, **kwargs):
        org_id = self.get_organization_id()
        order_id = kwargs.get("order_id")
        order_element = client_order_element_details(
            order_id=order_id, element_id=kwargs.get("element_id"), org_id=org_id
        )
        return Response(
            self.OutputSerializer(order_element, context={"org_id": org_id, "order_id": order_id}).data,
            status=HTTP_200_OK,
        )


class VendorOrderElementPreviewFileListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorOrderElementPreviewFileModelSerializer):
        class Meta(VendorOrderElementPreviewFileModelSerializer.Meta):
            ref_name = "VendorOrderElementPreviewFileOutput"
            fields = ("id", "name", "file", "uploaded_at", "is_main", "type")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer},
        operation_id="vendor-order-element-preview-file-list",
        operation_summary="vendor order element preview files list",
    )
    def get(self, request, *args, **kwargs):
        preview_files = (
            VendorOrderElementPreviewFile.objects.filter(element_id=kwargs.get("element_id")).order_by("is_main").all()
        )
        return Response(self.OutputSerializer(preview_files, many=True).data, status=HTTP_200_OK)


class VendorOrderElementExcelSyncSheet(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        client_rates = serializers.BooleanField(default=False)
        order_number = serializers.CharField()

        class Meta:
            ref_name = "VendorSyncExcelInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "link"},
        operation_id="Vendor_Order_sheet_sync",
        operation_summary="Vendor Order sheet sync",
    )
    def post(self, request, project_id, vendor_order_id):
        data = self.validate_input_data()

        instance: VendorOrder = VendorOrder.objects.get(id=vendor_order_id)
        filename = VendorOrderSyncExcel.export_file_name(name=data.get("order_number"))
        if not instance.excel_sheet:
            sheet = VendorOrderSyncExcel.create_blank_excel(
                file_name=filename, sheets=["Elements"], org_id=request.user.token_data.org_id, is_open_in_sheet=True
            )
            sheet_dict = {}
            for _sheet in sheet.worksheets():
                sheet_dict[_sheet.title] = _sheet.id

            _excel_sheet = VendorOrderSyncExcel.format_sheet_link_using_id(sheet_id=sheet.id)
            instance = vendor_order_sync_update(
                instance=instance,
                data={"excel_sheet": _excel_sheet, "excel_meta": sheet_dict},
                updated_by_id=request.user.id,
            )

        if instance.excel_sheet:
            vendor_order_sync.apply_async(
                queue="excel_sync_queue",
                kwargs={
                    "data": {"client_rates": True, "org_id": self.get_organization_id()},
                    "vendor_order_id": vendor_order_id,
                },
            )

        return Response({"link": instance.excel_sheet}, HTTP_200_OK)


class VendorOrderElementListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(VendorOrderElementModelSerializer):
        comment_count = serializers.IntegerField(default=0)
        quantity = serializers.DecimalField(max_digits=20, decimal_places=2)
        uom = serializers.SerializerMethodField()
        progress_percentage = serializers.IntegerField()
        quantity_dimensions = serializers.SerializerMethodField()

        @swagger_serializer_method(serializer_or_field=serializers.CharField())
        def get_uom(self, obj):
            return self.uom_name_get(uom_id=obj.uom)

        def get_quantity_dimensions(self, obj):
            return BaseElementQuantityService().get_quantity_dimensions(obj.quantity_dimensions)

        class Meta(VendorOrderElementModelSerializer.Meta):
            ref_name = "VendorOrderELementList"
            fields = (
                "id",
                "name",
                "code",
                "description",
                "uom",
                "quantity",
                "quantity_dimensions",
                "vendor_rate",
                "amount",
                "updated_at",
                "item_type",
                "status",
                "client_rate",
                "item_type",
                "preview_file_url",
                "category",
                "progress_percentage",
                "comment_count",
                "final_amount",
                "base_amount",
                "gross_amount",
                "service_charge_percent",
                "is_service_charge_with_base_amount",
                "discount_percent",
                "discounted_value",
            )

    @swagger_auto_schema(responses={HTTP_200_OK: OutputSerializer()}, operation_id="vendor_order_element_list_for_app")
    def get(self, request, vendor_order_id, project_id):
        order_elements: List[VendorOrderElement] = order_elements_fetch(
            vendor_order_id=vendor_order_id, org_id=self.get_organization_id()
        )
        return Response(self.OutputSerializer(order_elements, many=True).data, status=HTTP_200_OK)


class VendorOrderElementImportViaMyOrgApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        element_id_list = HashIdListField()

        class Meta:
            ref_name = "VendorOrderElementImportViaMyOrgLibrariesInput"

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: OrderElementOutputDataSerializer()},
        operation_id="vendor_order_element_via_my_org",
    )
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        element_entities = OrderElementService.create_my_org_element_import_data(
            element_ids=data.get("element_id_list"),
            is_client_rate_visible=OrgPermissionHelper.has_permission(
                user=request.user, permission=Permissions.CAN_VIEW_CLIENT_RATE
            ),
            organization_id=self.get_organization_id(),
        )
        return Response(OrderElementOutputDataSerializer(element_entities, many=True).data, status=HTTP_201_CREATED)


class OrderStatusToolTipDataApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        class OrderElementSerializer(VendorOrderElementModelSerializer):
            vendor_name = serializers.CharField()
            amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="element_amount")
            order_id = HashIdField(source="vendor_order_id")
            order_status = serializers.SerializerMethodField()

            @swagger_serializer_method(serializer_or_field=serializers.CharField())
            def get_order_status(self, obj):
                return dict(OrderStatusChoices.choices)[obj.vendor_order.outgoing_status]

            class Meta(VendorOrderElementModelSerializer.Meta):
                ref_name = "OrderStatusToolTipDataOutputOrderElementSerializer"
                fields = (
                    "id",
                    "code",
                    "vendor_name",
                    "uom_name",
                    "quantity",
                    "vendor_rate",
                    "amount",
                    "order_id",
                    "item_type",
                    "order_status",
                )

        class BoqRelatedElementSerializer(BoqElementModelSerializer):
            category = serializers.SerializerMethodField()
            amount = serializers.DecimalField(max_digits=20, decimal_places=2, source="element_amount")

            @swagger_serializer_method(serializer_or_field=serializers.CharField())
            def get_category(self, obj: BoqElement):
                return obj.category.name.title()

            class Meta(BoqElementModelSerializer.Meta):
                ref_name = "OrderStatusToolTipDataOutputBoqRelatedElementSerializer"
                fields = (
                    "id",
                    "code",
                    "uom_name",
                    "client_rate",
                    "quantity",
                    "category",
                    "amount",
                    "item_type",
                    "element_status",
                )

        order_elements = OrderElementSerializer(many=True)
        boq_related_elements = BoqRelatedElementSerializer(many=True)

        class Meta:
            ref_name = "OrderStatusToolTipDataOutput"
            fields = ("order_elements", "boq_related_elements")

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="order_status_tooltip_data",
    )
    def get(self, request, element_id, *args, **kwargs):
        order_element_queryset, boq_element_queryset = order_status_tooltip_data(
            element_id=element_id, org_id=self.get_organization_id(), project_id=kwargs.get("project_id")
        )
        error = get_boq_element_error_message(boq_element_id=element_id)
        order_elements = self.OutputSerializer.OrderElementSerializer(order_element_queryset, many=True).data
        boq_related_elements = self.OutputSerializer.BoqRelatedElementSerializer(boq_element_queryset, many=True).data
        return Response(
            {
                "order_elements": order_elements,
                "boq_related_elements": boq_related_elements,
                "error": error,
            },
            status=HTTP_200_OK,
        )
