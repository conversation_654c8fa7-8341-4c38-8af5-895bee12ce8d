from typing import Callable

from django.conf import settings
from django.utils.module_loading import import_string
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from core.entities import ProjectUserEntity
from order.interface.apis.scope_apis_v2.base_api import VendorWise<PERSON>copeBase<PERSON>pi
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractFactory

OrderToWorkProgressFactory: Callable[[ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory] = (
    import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)
)


class ItemTypeConfigApi(VendorWiseScopeBaseApi):
    """
    API to get milestone configuration, default update method for each item-type
    """

    def get(self, request, project_id, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_item_type_config()

        self.set_response_message("Item type config data fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
