from typing import Callable

from django.conf import settings
from django.utils.module_loading import import_string
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from core.entities import ProjectUserEntity
from order.interface.apis.scope_apis_v2.base_api import VendorWiseScopeBaseApi
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractFactory

OrderToWorkProgressFactory: Callable[[ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory] = (
    import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)
)


class ElementCategoryListApi(VendorWiseScopeBaseApi):
    """
    Gives category dropdown options for filtration of work progress elements.
    """

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_categories()

        self.set_response_message("Categories fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementStatusListApi(VendorWiseScopeBaseApi):
    """
    Gives status dropdown options for filtration of work progress elements.
    """

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_statuses()

        self.set_response_message("Statuses fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementUomListApi(VendorWiseScopeBaseApi):
    """
    Gives uom dropdown options for filtration of work progress elements.
    """

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_uoms()

        self.set_response_message("UOMs fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class ElementItemTypeListApi(VendorWiseScopeBaseApi):
    """
    Gives item type dropdown options for filtration of work progress elements.
    """

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_item_types()

        self.set_response_message("Item types fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
