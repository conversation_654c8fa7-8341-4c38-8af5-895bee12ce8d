from typing import Callable

from django.conf import settings
from django.utils.module_loading import import_string

from core.entities import ProjectUserEntity
from order.interface.apis.scope_apis_v2.base_api import VendorWiseScopeBase<PERSON>pi
from work_progress_v2.domain.entities import VendorScopeElementListFilterEntity
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractFactory

OrderToWorkProgressFactory: Callable[
    [ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory
] = import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)


class ElementListApi(VendorWiseScopeBaseApi):
    """
    Gives work progress elements list.
    """

    filter_pydantic_class = VendorScopeElementListFilterEntity

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data: VendorScopeElementListFilterEntity = self.validate_pydantic_filter_data()

        vendor_ids = [filter_data.vendor_id] if filter_data.vendor_id else self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        data = service.get_elements(filter_data=filter_data)

        self.set_response_message("Items fetched successfully")
        return self.get_pydantic_paginated_data(data)
