from typing import Callable

from django.conf import settings
from django.utils.module_loading import import_string
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from core.entities import ProjectUserEntity
from order.data.selectors.selectors import get_vendor_scope_order_count
from order.domain.entities import VendorScopeHeaderDetailModel
from order.interface.apis.scope_apis_v2.base_api import VendorWiseScopeBase<PERSON><PERSON>
from work_progress_v2.domain.entities import VendorScopeHeaderDetailFilterEntity
from work_progress_v2.interface.external.abstract_factories import OrderToWorkProgressAbstractFactory

OrderToWorkProgressFactory: Callable[
    [ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory
] = import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)


class VendorScopeHeaderDetailApi(VendorWiseScopeBaseApi):
    """
    Vendor Scope Header Detail API
    """

    filter_pydantic_class = VendorScopeHeaderDetailFilterEntity

    def get(self, request, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        filter_data: VendorScopeHeaderDetailFilterEntity = self.validate_pydantic_filter_data()

        vendor_ids = [filter_data.vendor_id] if filter_data.vendor_id else self.get_vendor_organization_ids()

        order_vendor_count = get_vendor_scope_order_count(
            project_id=user_entity.project_id,
            org_id=user_entity.org_id,
            vendor_ids=vendor_ids,
        )

        wp_service = OrderToWorkProgressFactory(user_entity, vendor_ids).get_service()

        scope_data = wp_service.get_scope_data()

        result = VendorScopeHeaderDetailModel(
            order_count=order_vendor_count,
            total_item_count=scope_data.total_item_count,
            total_amount=scope_data.total_amount,
            today_updated_item_count=scope_data.today_updated_item_count,
            total_progress=scope_data.total_progress,
            today_progress=scope_data.today_progress,
        )

        return Response(pydantic_dump(result), status=HTTP_200_OK)
