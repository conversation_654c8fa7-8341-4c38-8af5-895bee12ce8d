from typing import Callable

from django.conf import settings
from django.utils.module_loading import import_string
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from common.pydantic.model_dump import pydantic_dump
from core.entities import ProjectUserEntity
from order.interface.apis.scope_apis_v2.base_api import VendorWiseScopeBaseApi
from work_progress_v2.interface.external.abstract_factories import (
    OrderToWorkProgressAbstractFactory,
    OrderToWorkProgressAbstractService,
)

OrderToWorkProgressFactory: Callable[[ProjectUserEntity, list[int]], OrderToWorkProgressAbstractFactory] = (
    import_string(settings.ORDER_TO_WORK_PROGRESS_FACTORY)
)


class ElementDetailApi(VendorWiseScopeBaseApi):
    """
    Gives element details.
    """

    def get(self, request, element_id: int, *args, **kwargs):
        user_entity = self.get_project_user_entity()
        vendor_ids = self.get_vendor_organization_ids()

        factory = OrderToWorkProgressFactory(user_entity, vendor_ids)

        service = factory.get_service()
        try:
            data = service.get_element_detail(element_id)
        except OrderToWorkProgressAbstractService.ElementNotFoundException:
            self.set_response_message("Item does not exist")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Item details fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
