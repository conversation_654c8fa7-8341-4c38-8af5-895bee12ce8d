from django.urls import path

from order.interface.apis import PurchaseOrderType<PERSON>ist<PERSON><PERSON>
from order.interface.apis.internal.apis import (
    ClientOrderSnapshotListApi,
    OrderElementSnapshotDetailsAPi,
    OrderPoConfigApi,
    OrderPoConfigUpdateApi,
    OrderRequestApproveApi,
    OrderRequestHoldApi,
    OrderRequestRejectApi,
    OrderRequestReplyCreateApi,
    OrderRequestReplyListApi,
    OrderSnapshotFetchApi,
    TaskOrderDetailApi,
    VendorInfoUpdateApi,
    VendorInfoUpdateApiV2,
    VendorOrderDocumentConfigApi,
    VendorOrderPoPreviewApi,
    VendorOrderPOPreviewUploadApi,
    VendorOrderSnapshotListApi,
)

urlpatterns = [
    path(
        "<hash_id:order_id>/request/<hash_id:request_id>/approve/",
        OrderRequestApproveApi.as_view(),
        name="order_request_approve",
    ),
    path(
        "<hash_id:order_id>/request/<hash_id:request_id>/hold/",
        OrderRequestHoldApi.as_view(),
        name="order_request_hold",
    ),
    path(
        "<hash_id:order_id>/request/<hash_id:request_id>/reject/",
        OrderRequestRejectApi.as_view(),
        name="order_request_reject",
    ),
    path("<hash_id:order_id>/snapshot/<hash_id:order_snapshot_id>/", OrderSnapshotFetchApi.as_view(), name=""),
    path(
        "<hash_id:order_id>/snapshot/<hash_id:order_snapshot_id>/element/<hash_id:order_element_snapshot_id>/details/",
        OrderElementSnapshotDetailsAPi.as_view(),
        name="",
    ),
    path(
        "<hash_id:order_id>/request/<hash_id:request_id>/reply/list/",
        OrderRequestReplyListApi.as_view(),
        name="order-request-reply-list",
    ),
    path(
        "<hash_id:order_id>/request/<hash_id:request_id>/reply/",
        OrderRequestReplyCreateApi.as_view(),
        name="order-request-reply-create",
    ),
    path("vendor-order/<hash_id:order_id>/snapshot-list/", VendorOrderSnapshotListApi.as_view(), name=""),
    path("client-order/<hash_id:order_id>/snapshot-list/", ClientOrderSnapshotListApi.as_view(), name=""),
    path("update-po-config/", OrderPoConfigUpdateApi.as_view(), name="order-po-config-update"),
    path("config/", OrderPoConfigApi.as_view(), name="order-po-config"),
    path("<hash_id:order_id>/request/<hash_id:request_id>/order-data/", TaskOrderDetailApi.as_view(), name=""),
    path("<hash_id:order_id>/vendor-info-update/", VendorInfoUpdateApi.as_view(), name="order-vendor-info-update"),
    path(
        "<hash_id:order_id>/vendor-info-update-v2/", VendorInfoUpdateApiV2.as_view(), name="order-vendor-info-update-v2"
    ),
    path(
        "<hash_id:order_id>/upload-po-preview/", VendorOrderPOPreviewUploadApi.as_view(), name="order-po-preview-upload"
    ),
    path("document-config/", VendorOrderDocumentConfigApi.as_view(), name="vendor-order-doc-config"),
    path("preview-po/", VendorOrderPoPreviewApi.as_view(), name="vendor-order-po-preview"),
    path(
        "purchase-order-types/list/",
        PurchaseOrderTypeListApi.as_view(),
        name="purchase-order-type-list",
    ),
]
