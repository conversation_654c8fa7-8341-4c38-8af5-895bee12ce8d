from django.db.models import F
from order.data.models import VendorOrder
from django.db.models import F
from itertools import islice


def update_vendor_orders_total_amount(batch_size=500):
    """
    Updates the total_amount field in VendorOrder in batches using an iterator to avoid memory overload.
    """
    vendor_orders = (
        VendorOrder.objects.all()
        .annotate_elements_final_amount()
        .annotate_deduction_amount()
        .annotate_deduction_tax_amount()
        .annotate(
            order_amount=F("elements_final_amount") - F("deduction_amount"),
            order_tax_amount=F("elements_tax_amount") - F("deduction_tax_amount"),
            final_amount=F("order_amount") + F("order_tax_amount"),
        )
    )

    def batch_iterator(iterable, size):
        iterator = iter(iterable)
        while batch := list(islice(iterator, size)):
            yield batch

    for batch in batch_iterator(vendor_orders.iterator(), batch_size):
        print(batch)
        orders_for_updation = [VendorOrder(id=order.id, saved_total_amount=order.final_amount) for order in batch]
        print(f"Updating {len(orders_for_updation)} orders...")
        VendorOrder.objects.bulk_update(orders_for_updation, ["saved_total_amount"])