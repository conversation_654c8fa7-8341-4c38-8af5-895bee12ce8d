from typing import Annotated

from django.db import transaction
from pydantic import Field

from common.constants import SYSTEM_USER_ID, CustomFieldTypeChoices
from common.pydantic.base_model import BaseModel
from core.models import (
    City,
    Country,
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    Currency,
    State,
    TaxSlab,
    TaxType,
    Timezone,
)
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.models import (
    OrganizationDocumentConfig,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationSectionConfig,
)
from microcontext.choices import MicroContextChoices

INDIA = 1


class ConfigureNewCountry:
    class CountryConfig:
        name = "UAE"
        flag = "flag_url"
        code = "+971"
        timezone = "Asia/Dubai"
        phone_number_length = "9"  # without country code
        phone_number_regex = "\+971[5][0-9]{8}$"
        is_state_available = True
        tax_type = "VAT (Value Added Tax)"
        tax_slabs = [0, 5]  # must be increasing order
        currency_name = "UAE Dirham"
        currency_code = "AED"
        currency_symbol = "د.إ"
        locale = "en-AE"
        state_city_mapping = {
            "Abu Dhabi": [
                "Abu Dhabi City",
                "Al Ain",
                "Al Dhafra (Western Region)",
                "Khalifa City",
                "Mussafah",
                "Yas Island",
                "Saadiyat Island",
                "Bani Yas",
                "Al Shamkha",
                "Al Raha Beach",
            ],
            "Dubai": [
                "Dubai City",
                "Dubai Marina",
                "Downtown Dubai",
                "Jumeirah",
                "Deira",
                "Bur Dubai",
                "Business Bay",
                "Al Quoz",
                "Al Barsha",
                "Jebel Ali",
                "Silicon Oasis",
                "Arabian Ranches",
                "Dubai Sports City",
                "Mirdif",
                "Palm Jumeirah",
                "Al Sufouh",
                "Umm Suqeim",
                "Ras Al Khor",
                "Hatta",
            ],
            "Sharjah": ["Sharjah City", "Khor Fakkan", "Kalba", "Dibba Al-Hisn", "Al Dhaid", "Mleiha"],
            "Ajman": ["Ajman City", "Masfout", "Manama", "Al Hamidiya", "Al Jurf"],
            "Umm Al Quwain": ["Umm Al Quwain City", "Falaj Al Mualla", "Al Rafaah"],
            "Ras Al Khaimah": [
                "Ras Al Khaimah City",
                "Al Rams",
                "Al Jazirah Al Hamra",
                "Al Dhait",
                "Khatt",
                "Masafi",
                "Wadi Shah",
            ],
            "Fujairah": ["Fujairah City", "Dibba Al-Fujairah", "Masafi", "Mirbah", "Khor Fakkan", "Kalba"],
        }

    def create_timezone(self) -> Timezone:
        timezone, _ = Timezone.objects.get_or_create(locale=self.CountryConfig.locale, tz=self.CountryConfig.timezone)
        return timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.CountryConfig.tax_type, remark="VAT UAE")
        slabs = []
        for slab in self.CountryConfig.tax_slabs:
            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.CountryConfig.currency_name,
            code=self.CountryConfig.currency_code,
            symbol=self.CountryConfig.currency_symbol,
            locale=self.CountryConfig.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.CountryConfig.name,
            defaults={
                "code": self.CountryConfig.code,
                "timezone": self.CountryConfig.timezone,
                "phone_number_length": self.CountryConfig.phone_number_length,
                "phone_number_regex": self.CountryConfig.phone_number_regex,
                "is_state_available": self.CountryConfig.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country, tax_type=tax, is_default=True, max_slab_percent=self.CountryConfig.tax_slabs[-1]
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        sections = OrganizationSectionConfig.objects.bulk_create(
            objs=[
                OrganizationSectionConfig(
                    country=country,
                    name="KYC Details (Know Your Customer)",
                    position=0,
                    type=OrganizationSectionTypeChoices.KYC_DETAILS,
                    id=3,
                ),
                OrganizationSectionConfig(
                    country=country,
                    name="Bank Details",
                    position=0,
                    type=OrganizationSectionTypeChoices.BANK_DETAILS,
                    id=4,
                ),
            ],
        )
        docs = OrganizationDocumentConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Trade License",
                    position=0,
                    is_required=True,
                    is_visible_on_app=True,
                    id=7,
                ),
                OrganizationDocumentConfig(
                    country=country, section=sections[0], name="VAT", position=1, is_visible_on_app=True, id=8
                ),
                OrganizationDocumentConfig(
                    country=country, section=sections[0], name="Emirates ID", position=2, is_visible_on_app=True, id=9
                ),
                OrganizationDocumentConfig(
                    country=country, section=sections[1], name="Bank Details", position=0, is_visible_on_app=True, id=10
                ),
            ],
        )
        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Trade License Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^\d{5,10}$",
                    is_visible_on_app=True,
                    id=21,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Trade License Proof",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                    id=22,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="VAT Registration Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{15}$",
                    is_visible_on_app=True,
                    id=23,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="VAT Certificate",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                    id=24,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Emirates ID",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{15}$",
                    is_visible_on_app=True,
                    id=25,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Emirates ID Proof",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                    id=26,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Account Holder Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                    id=27,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="IBAN (International Bank Account Number)",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=True,
                    regex="^[A-Z0-9]{23}$",
                    is_visible_on_app=True,
                    id=28,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="SWIFT/BIC Code",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=True,
                    regex="^[A-Z0-9]{8,11}$",
                    is_visible_on_app=True,
                    id=29,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Branch Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                    id=30,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=4,
                    is_required=True,
                    regex="^\d{10,14}$",
                    is_visible_on_app=True,
                    id=31,
                ),
            ],
        )
        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        OrganizationDocumentFieldContextConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldContextConfig(
                    context=MicroContextChoices.ORDER,
                    country_id=country.pk,
                    field_config_id=fields[2].pk,
                    name="VAT Registration Number",
                ),
                OrganizationDocumentFieldContextConfig(
                    context=MicroContextChoices.PROPOSAL,
                    country_id=country.pk,
                    field_config_id=fields[2].pk,
                    name="VAT Registration Number",
                ),
            ]
        )  # for context VAT Registration Number
        print("Data Fields created successfully")

    @transaction.atomic
    def configure(self):
        # Country Creation
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)

    @transaction.atomic
    def create_cities(self):
        country = Country.objects.get(name=self.CountryConfig.name)
        print(country)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")


class ConfigureKuwait:
    class CountryConfig:
        name = "Kuwait"
        flag = "flag_url"
        code = "+965"
        timezone = "Asia/Riyadh"
        phone_number_length = "8"  # without country code
        phone_number_regex = "\+965[2-9][0-9]{7}$"
        is_state_available = True
        tax_type = "VAT (Value Added Tax)"
        tax_slabs = [0]  # must be increasing order
        currency_name = "Kuwaiti Dinar"
        currency_code = "KWD"
        currency_symbol = "د.ك"
        locale = "en-KW"
        state_city_mapping = {
            "Al Ahmadi": [
                "Abu Halifa ",
                "Ahmadi City",
                "Eqaila",
                "Funtas",
                "Forosyia",
                "Fahaheel",
                "Ali Sabah Al Salem",
                "Jawakher South",
                "Al Kayron Residential",
                "Mahboula",
                "Mangaf",
                "Magwa",
                "Al Nuwaiseeb",
                "Wafra",
                "New Wafra",
                "Riqqa",
                "Al Shadadyia Industrial Area",
                "Shuaiba",
                "Shuaiba Industrial Western",
                "Sabahiya",
                "Daher",
                "Al Zour",
                "Ahmadi Desert",
                "West Funtas",
                "Fahad Al Ahmad",
                "Hadiya",
                "South Sabahiya",
                "Kabad Agricultural",
                "Sabah Al Ahmad City",
                "Wafra Agricultural",
                "Mina Abdullah",
                "Rajm Khashman",
                "Sabah Al Ahmad 1",
                "Sabah Al Ahmad 2",
                "Sabah Al Ahmad 3",
                "Sabah Al Ahmad 4",
                "Sabah Al Ahmad 5",
                "Sabah Al Ahmad Marine",
                "Dubaeeiya Resort",
                "Jlaiaa Resort",
                "Al Kayron Resorts",
                "Nwaiseeb Resort",
                "Zoor Resort",
                "Bedier Resort",
                "Mina Abdulla Resort",
                "Wara",
            ],
            "Al Asimah": [
                "Daiya",
                "Dasma",
                "Doha",
                "Faiha",
                "Khalidiya",
                "Murqab",
                "Qibla",
                "Dasman",
                "Wall Garden",
                "Kuwait City East",
                "Mansouriya",
                "Shuwaikh Port, Free Trade Zone",
                "Health District",
                "Nuzha",
                "Qadisiya",
                "Adailiya",
                "Coastal Strip",
                "Yarmouk",
                "Rawda",
                "Shamiya",
                "Shuwaikh",
                "Shuwaikh Industrial Area",
                "Sulaibekhat",
                "Surra",
                "Bneid Al Qar",
                "Abdulla Al Salem",
                "Garnata",
                "Jaber Al Ahmad New City",
                "Failaka",
                "Umm Al Namil",
                "Kaifan",
                "Doha Port",
                "Mubarakiya Camp",
                "Qurtoba",
                "Doha Resort",
                "Northwest Sulaibekhat",
            ],
            "Farwaniya": [
                "Abdullah Al Mubarak Al Sabah",
                "Andalus",
                "Ardiya",
                "Ardiya Governmental Use",
                "Ardiya Stores",
                "Ardiya Al Herafia",
                "Farwaniya",
                "Firdous",
                "Omariya",
                "International Airport",
                "Rai",
                "Rabia",
                "Rahab",
                "Regay",
                "Ishbeelia",
                "Al Shadadyia",
                "Airplane Noise Region",
                "Kheetan",
                "Sabah Al Salem University City",
                "Jaleeb Al Shuyoukh",
                "Sabah Al Nasser",
            ],
            "Al Jahra": [
                "Abdilee",
                "Bhaith",
                "Jahra",
                "Jahra Industrial Area",
                "Mitlaa",
                "Quirawan",
                "Qasser",
                "Oyoun",
                "Waha",
                "Aum Al Ash",
                "Amgara Industrial Area",
                "Al Nahda",
                "Na'eem",
                "Naseem",
                "Neayem",
                "Al Rawadatain",
                "Shegaya",
                "Sabryia",
                "Salmy",
                "Subiya",
                "Sulaibiah Shaabia",
                "Sulaibiah Industrial Area 1",
                "Sulaibiah Industrial Area 2",
                "Sulaibiah Industrial Area 3",
                "Sulaibiya Agriculture",
                "Jahra Desert",
                "South Al Mutlaa",
                "South Amgara",
                "Jahraa Jawakher",
                "Bubiyan Island",
                "Warba Island",
                "Kabad",
                "Kathma",
                "South Jahra City",
                "Sulaibekhat Cemetery",
                "Jahra Camp",
                "Al Subbiya Resort",
                "Kazma Resort",
                "Northwestern Jahra",
                "Taima",
            ],
            "Hawalli": [
                "Bedi",
                "Jabriya",
                "Mubarakyia",
                "Zahraa",
                "Anjafa",
                "Rumaithiya",
                "Sha'ab",
                "Shuhada",
                "Salam",
                "Salmiya",
                "Sideek",
                "Bayan",
                "Hateen",
                "Hawalli",
                "Ministries Area",
                "Mishrif",
                "Mubarak Al Abdullah",
                "Salwa",
            ],
            "Mubarak Al Kabeer": [
                "Abu Hasaniya",
                "Adan",
                "Wista",
                "Masayel",
                "Messila",
                "Qosour",
                "Qurain",
                "Abu Fatira",
                "Fanatees",
                "West Abu Fatira Al Herafia",
                "Mubarak Al Kabeer",
                "Sabah Al Salem",
                "Sabhan Industrial Area",
            ],
        }

    def create_timezone(self) -> Timezone:
        timezone, _ = Timezone.objects.get_or_create(locale=self.CountryConfig.locale, tz=self.CountryConfig.timezone)
        return timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.CountryConfig.tax_type, remark="VAT Kuwait")
        slabs = []
        for slab in self.CountryConfig.tax_slabs:
            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.CountryConfig.currency_name,
            code=self.CountryConfig.currency_code,
            symbol=self.CountryConfig.currency_symbol,
            locale=self.CountryConfig.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.CountryConfig.name,
            defaults={
                "code": self.CountryConfig.code,
                "timezone": self.CountryConfig.timezone,
                "phone_number_length": self.CountryConfig.phone_number_length,
                "phone_number_regex": self.CountryConfig.phone_number_regex,
                "is_state_available": self.CountryConfig.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country, tax_type=tax, is_default=True, max_slab_percent=self.CountryConfig.tax_slabs[-1]
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        sections = OrganizationSectionConfig.objects.bulk_create(
            objs=[
                OrganizationSectionConfig(
                    country=country,
                    name="KYC Details (Know Your Customer)",
                    position=0,
                    type=OrganizationSectionTypeChoices.KYC_DETAILS,
                ),
                OrganizationSectionConfig(
                    country=country,
                    name="Bank Details",
                    position=0,
                    type=OrganizationSectionTypeChoices.BANK_DETAILS,
                ),
            ],
        )
        docs = OrganizationDocumentConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],  # "KYC Details
                    name="Civil ID",
                    position=0,
                    is_required=True,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Trade License",
                    position=1,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[1],
                    name="Bank Details",
                    position=0,
                    is_visible_on_app=True,
                ),
            ],
        )
        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Civil ID Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^\d{7,12}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Civil ID Card",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="License Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{1,5}\/\d{4}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Central Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=False,
                    regex="^\d{13}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Authority’s Civil Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=False,
                    regex="^\d{7,12}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Commercial Register Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=False,
                    regex="^\d{6}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Trade License Proof",
                    type=CustomFieldTypeChoices.FILE,
                    position=4,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Holder Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^[A-Z0-9]{23}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="IBAN (International Bank Account Number)",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=True,
                    regex="^[A-Z0-9]{30}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="SWIFT/BIC Code",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=True,
                    regex="^[A-Z0-9]{8,11}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Branch Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=4,
                    is_required=True,
                    regex="^\d{10,14}$",
                    is_visible_on_app=True,
                ),
            ],
        )
        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        # OrganizationDocumentFieldContextConfig.objects.bulk_create(
        #     objs=[
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.ORDER,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.PROPOSAL,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #     ]
        # )  # for context VAT Registration Number
        print("Data Fields created successfully")

    @transaction.atomic
    def configure(self):
        # Country Creation
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)


class ConfigureBahrain:
    """Use this class to configure bahrain"""

    class CountryConfig:
        name = "Bahrain"
        flag = "flag_url"
        code = "+973"
        timezone = "Asia/Bahrain"
        phone_number_length = "8"  # without country code
        phone_number_regex = "^\+973(?:3|6|7)\d{7}$"
        is_state_available = True
        tax_type = "VAT (Value Added Tax)"
        tax_slabs = [0, 10]  # must be increasing order
        currency_name = "Bahraini Dinar"
        currency_code = "BHD"
        currency_symbol = ".د.ب"
        locale = "en-BH"
        state_city_mapping = {
            "Capital Governorate": [
                "Manama",
                "Juffair",
                "Seef",
                "Hoora",
                "Gudaibiya",
                "Bilad Al Qadeem",
            ],
            "Muharraq Governorate": [
                "Muharraq",
                "Hidd",
                "Arad",
                "Busaiteen",
            ],
            "Northern Governorate": [
                "Budaiya",
                "Saar",
                "Janabiya",
                "Hamala",
                "Barbar",
                "Duraz",
            ],
            "Southern Governorate": [
                "Riffa East",
                "Riffa West",
                "Zallaq",
                "Awali",
                "Askar",
            ],
        }

    def create_timezone(self) -> Timezone:
        timezone, _ = Timezone.objects.get_or_create(locale=self.CountryConfig.locale, tz=self.CountryConfig.timezone)
        return timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.CountryConfig.tax_type, remark="VAT UAE")
        slabs = []
        for slab in self.CountryConfig.tax_slabs:
            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.CountryConfig.currency_name,
            code=self.CountryConfig.currency_code,
            symbol=self.CountryConfig.currency_symbol,
            locale=self.CountryConfig.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.CountryConfig.name,
            defaults={
                "code": self.CountryConfig.code,
                "timezone": self.CountryConfig.timezone,
                "phone_number_length": self.CountryConfig.phone_number_length,
                "phone_number_regex": self.CountryConfig.phone_number_regex,
                "is_state_available": self.CountryConfig.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country, tax_type=tax, is_default=True, max_slab_percent=self.CountryConfig.tax_slabs[-1]
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        sections = OrganizationSectionConfig.objects.bulk_create(
            objs=[
                OrganizationSectionConfig(
                    country=country,
                    name="KYC Details (Know Your Customer)",
                    position=0,
                    type=OrganizationSectionTypeChoices.KYC_DETAILS,
                ),
                OrganizationSectionConfig(
                    country=country,
                    name="Bank Details",
                    position=0,
                    type=OrganizationSectionTypeChoices.BANK_DETAILS,
                ),
            ],
        )
        docs = OrganizationDocumentConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Civil ID",
                    position=0,
                    is_required=True,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Trade License",
                    position=1,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="VAT Registration",
                    position=0,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[1],
                    name="Bank Details",
                    position=0,
                    is_visible_on_app=True,
                ),
            ],
        )
        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="National Identity Card Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^\d{9}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="National Identity Card",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Trade License Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{5,8}-\d{1,2}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Trade License Document",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="VAT Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{15}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="VAT Registration Certificate",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Account Holder Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="IBAN (International Bank Account Number)",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=True,
                    regex="^BH\d{2}[A-Z]{4}[0-9]{8,14}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="SWIFT/BIC Code",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=True,
                    regex="^[A-Za-z0-9]{8,11}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Branch Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[3],
                    name="Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=4,
                    is_required=True,
                    regex="^\d{10,14}$",
                    is_visible_on_app=True,
                ),
            ],
        )
        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        # OrganizationDocumentFieldContextConfig.objects.bulk_create(
        #     objs=[
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.ORDER,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.PROPOSAL,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #     ]
        # )  # for context VAT Registration Number
        print("Data Fields created successfully")

    @transaction.atomic
    def configure(self):
        # Country Creation
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)

    @transaction.atomic
    def create_cities(self):
        country = Country.objects.get(name=self.CountryConfig.name)
        print(country)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")


class ConfigureSaudiArabia:
    """Use this class to configure Saudi Arabia"""

    class CountryConfig:
        name = "Saudi Arabia"
        flag = "flag_url"
        code = "+966"
        timezone = "Asia/Riyadh"
        phone_number_length = "9"  # without country code
        phone_number_regex = "^\+966(?:1|2|3|5)\d{9}$"
        is_state_available = True
        tax_type = "VAT (Value Added Tax)"
        tax_slabs = [0, 15]  # must be increasing order
        currency_name = "Saudi Riyal"
        currency_code = "SAR"
        currency_symbol = "ر.س"
        locale = "ar-SA"
        state_city_mapping = {
            "Riyadh": [
                "Al Olaya",
                "Al Malaz",
                "Al Diriyah",
                "Al Suwaidi",
                "Al Munsiyah",
            ],
            "Jeddah": [
                "Al Balad",
                "Al Hamra",
                "Al Rawda",
                "Al Shati",
                "Al Tahlia",
            ],
            "Mecca": [
                "Al Haram",
                "Al Nuzha",
                "Al Aziziyah",
                "Al Safa",
                "Al Shubaikah",
            ],
            "Medina": [
                "Al Haram",
                "Quba",
                "Al Noor",
                "Al Fath",
                "Al Ula",
            ],
            "Dammam": [
                "Al Khobar",
                "Al Jubail",
                "Al Manama",
                "Al Asimah",
            ],
            "Khobar": [
                "Al Khobar Corniche",
                "Al Faysaliyah",
                "Al Yasmeen",
                "Al Waha",
            ],
            "Abha": [
                "Al Hajar",
                "Al Soudah",
                "Al Khamis",
                "Al Qattah",
            ],
            "Taif": [
                "Al Shifa",
                "Al Hada",
                "Al Numan",
                "Al Qarah",
            ],
        }

    def create_timezone(self) -> Timezone:
        timezone, _ = Timezone.objects.get_or_create(locale=self.CountryConfig.locale, tz=self.CountryConfig.timezone)
        return timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.CountryConfig.tax_type, remark="VAT UAE")
        slabs = []
        for slab in self.CountryConfig.tax_slabs:
            if TaxSlab.objects.filter(tax_type_id=tax.pk, tax_percent=slab).exists():
                continue

            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.CountryConfig.currency_name,
            code=self.CountryConfig.currency_code,
            symbol=self.CountryConfig.currency_symbol,
            locale=self.CountryConfig.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.CountryConfig.name,
            defaults={
                "code": self.CountryConfig.code,
                "timezone": self.CountryConfig.timezone,
                "phone_number_length": self.CountryConfig.phone_number_length,
                "phone_number_regex": self.CountryConfig.phone_number_regex,
                "is_state_available": self.CountryConfig.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country, tax_type=tax, is_default=True, max_slab_percent=self.CountryConfig.tax_slabs[-1]
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        sections = OrganizationSectionConfig.objects.bulk_create(
            objs=[
                OrganizationSectionConfig(
                    country=country,
                    name="KYC Details (Know Your Customer)",
                    position=0,
                    type=OrganizationSectionTypeChoices.KYC_DETAILS,
                ),
                OrganizationSectionConfig(
                    country=country,
                    name="Bank Details",
                    position=1,
                    type=OrganizationSectionTypeChoices.BANK_DETAILS,
                ),
            ],
        )
        docs = OrganizationDocumentConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="National Identity Card",
                    position=0,
                    is_required=True,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Trade License",
                    position=1,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[1],
                    name="Bank Details",
                    position=0,
                    is_visible_on_app=True,
                ),
            ],
        )
        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="National Identity Card Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^\d{10}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="National Identity Card",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="License Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{1,5}\/\d{4}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Central Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=False,
                    regex="^\d{13}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Authority’s Civil Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=False,
                    regex="^\d{7,12}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Commercial Register Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=False,
                    regex="^\d{6}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Trade License Proof",
                    type=CustomFieldTypeChoices.FILE,
                    position=4,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Holder Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="IBAN (International Bank Account Number)",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=True,
                    regex="^SA03\d[0-9]{20}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="SWIFT/BIC Code",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=True,
                    regex="^[A-Za-z0-9]{8,11}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Branch Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=4,
                    is_required=True,
                    regex="^\d{16,24}$",
                    is_visible_on_app=True,
                ),
            ],
        )

        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        # OrganizationDocumentFieldContextConfig.objects.bulk_create(
        #     objs=[
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.ORDER,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.PROPOSAL,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #     ]
        # )  # for context VAT Registration Number
        print("Data Fields created successfully")

    @transaction.atomic
    def configure(self):
        # Country Creation
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)

    @transaction.atomic
    def create_cities(self):
        country = Country.objects.get(name=self.CountryConfig.name)
        print(country)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")


class ConfigureQatar:
    """Use this class to configure Qatar"""

    class CountryConfig:
        name = "Qatar"
        flag = "flag_url"
        code = "+974"
        timezone = "Asia/Qatar"
        phone_number_length = "8"  # without country code
        phone_number_regex = "^\+974(?:3|4|5)\d{8}$"
        is_state_available = True
        tax_type = "VAT (Value Added Tax)"
        tax_slabs = [0]  # must be increasing order
        currency_name = "Qatari Riyal"
        currency_code = "QAR"
        currency_symbol = "ر.ق"
        locale = "ar-QA"
        state_city_mapping = {
            "Doha": [
                "West Bay",
                "Corniche",
                "Al Dafna",
                "Msheireb",
                "Souq Waqif",
                "Al Sadd",
                "Al Rayyan",
            ],
            "Al Daayen": [
                "Al Khor",
                "Al Thakhira",
                "Pearl Qatar",
            ],
            "Al Khor": [
                "Al Khor city",
                "Al Thakhira",
                "Ras Laffan",
            ],
            "Al Shamal": [
                "Al Zubarah",
                "Al Ruwais",
                "Madinat ash Shamal",
                "Fuwairit",
            ],
            "Al Wakrah": [
                "Al Wakrah",
                "Mesaieed",
                "Al Wukair",
                "Al Matar",
            ],
            "Umm Salal": [
                "Umm Salal Ali",
                "Umm Salal Mohamed",
                "Al Kharaitiyat",
            ],
            "Al Rayyan": [
                "Al Rayyan city",
                "Al Soudan",
                "Musherib",
                "Fereej Abdul Aziz",
                "Abu Hamour",
            ],
            "Madinat ash Shamal": [
                "Al Zubarah",
                "Al Ruwais",
                "Fuwairit",
            ],
        }

    def create_timezone(self) -> Timezone:
        timezone, _ = Timezone.objects.get_or_create(locale=self.CountryConfig.locale, tz=self.CountryConfig.timezone)
        return timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.CountryConfig.tax_type, remark="VAT UAE")
        slabs = []
        for slab in self.CountryConfig.tax_slabs:
            if TaxSlab.objects.filter(tax_type_id=tax.pk, tax_percent=slab).exists():
                continue

            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.CountryConfig.currency_name,
            code=self.CountryConfig.currency_code,
            symbol=self.CountryConfig.currency_symbol,
            locale=self.CountryConfig.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.CountryConfig.name,
            defaults={
                "code": self.CountryConfig.code,
                "timezone": self.CountryConfig.timezone,
                "phone_number_length": self.CountryConfig.phone_number_length,
                "phone_number_regex": self.CountryConfig.phone_number_regex,
                "is_state_available": self.CountryConfig.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country, tax_type=tax, is_default=True, max_slab_percent=self.CountryConfig.tax_slabs[-1]
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        sections = OrganizationSectionConfig.objects.bulk_create(
            objs=[
                OrganizationSectionConfig(
                    country=country,
                    name="KYC Details (Know Your Customer)",
                    position=0,
                    type=OrganizationSectionTypeChoices.KYC_DETAILS,
                ),
                OrganizationSectionConfig(
                    country=country,
                    name="Bank Details",
                    position=1,
                    type=OrganizationSectionTypeChoices.BANK_DETAILS,
                ),
            ],
        )
        docs = OrganizationDocumentConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Qatar ID",
                    position=0,
                    is_required=True,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[0],
                    name="Trade License",
                    position=1,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentConfig(
                    country=country,
                    section=sections[1],
                    name="Bank Details",
                    position=0,
                    is_visible_on_app=True,
                ),
            ],
        )
        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=[
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Qatar ID Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex="^\d{11}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[0],
                    name="Qatar ID Card",
                    type=CustomFieldTypeChoices.FILE,
                    position=1,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="License Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=False,
                    regex="^\d{1,5}\/\d{4}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Central Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=False,
                    regex="^\d{13}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Authority’s Civil Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=False,
                    regex="^\d{7,12}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Commercial Register Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=False,
                    regex="^\d{6}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[1],
                    name="Trade License Proof",
                    type=CustomFieldTypeChoices.FILE,
                    position=4,
                    is_required=False,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Holder Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=0,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="IBAN (International Bank Account Number)",
                    type=CustomFieldTypeChoices.TEXT,
                    position=1,
                    is_required=True,
                    regex="^QA58\d[A-Z0-9]{25}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="SWIFT/BIC Code",
                    type=CustomFieldTypeChoices.TEXT,
                    position=2,
                    is_required=True,
                    regex="^[A-Za-z0-9]{8,11}$",
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Branch Name",
                    type=CustomFieldTypeChoices.TEXT,
                    position=3,
                    is_required=True,
                    regex=None,
                    is_visible_on_app=True,
                ),
                OrganizationDocumentFieldConfig(
                    document_config=docs[2],
                    name="Account Number",
                    type=CustomFieldTypeChoices.TEXT,
                    position=4,
                    is_required=True,
                    regex="^\d{16,21}$",
                    is_visible_on_app=True,
                ),
            ],
        )

        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        # OrganizationDocumentFieldContextConfig.objects.bulk_create(
        #     objs=[
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.ORDER,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #         OrganizationDocumentFieldContextConfig(
        #             context=MicroContextChoices.PROPOSAL,
        #             country_id=country.pk,
        #             field_config_id=fields[2].pk,
        #             name="VAT Registration Number",
        #         ),
        #     ]
        # )  # for context VAT Registration Number
        print("Data Fields created successfully")

    @transaction.atomic
    def configure(self):
        # Country Creation
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)

    @transaction.atomic
    def create_cities(self):
        country = Country.objects.get(name=self.CountryConfig.name)
        print(country)
        states = []
        for state_name in self.CountryConfig.state_city_mapping.keys():
            states.append(
                State(
                    name=state_name,
                    country=country,
                )
            )
        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(
                f"Cities for state: {state.name} , cities: {self.CountryConfig.state_city_mapping.get(state.name, [])}"
            )
            for city_name in self.CountryConfig.state_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")


class ConfigureCountryEntity(BaseModel):
    class CurrencyEntity(BaseModel):
        name: str
        code: str
        symbol: str
        locale: str

    class PhoneNumberEntity(BaseModel):
        max_length: int
        regex: str

    class TaxEntity(BaseModel):
        type: str
        remark: str
        slabs: list[float]

    class StateCityMappingEntity(BaseModel):
        state: str
        cities: list[str]

    class OrganizationSectionConfigEntity(BaseModel):
        name: str
        position: int
        type: OrganizationSectionTypeChoices

    class OrganizationDocumentConfigEntity(BaseModel):
        section_idx: int
        name: str
        position: int
        is_required: bool = False
        is_visible_on_app: bool

    class OrganizationDocumentFieldConfigEntity(BaseModel):
        document_idx: int
        name: str
        type: CustomFieldTypeChoices
        position: int
        is_required: bool
        regex: str | None
        is_visible_on_app: bool

    name: str
    code: str
    timezone: Annotated[list[str], Field(min_length=1)]
    phone_number: PhoneNumberEntity
    is_state_available: bool = True
    tax: TaxEntity
    currency: CurrencyEntity
    state_city_mapping: list[StateCityMappingEntity]
    sections: list[OrganizationSectionConfigEntity]
    docs: list[OrganizationDocumentConfigEntity]
    fields: list[OrganizationDocumentFieldConfigEntity]


class ConfigureCountry:
    DemocraticRepublicOfCongo = ConfigureCountryEntity(
        name="Democratic Republic of Congo",
        code="+243",
        timezone=["Africa/Kinshasa"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\+243(?:8|9)\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 16],
            remark="VAT Congo",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Congolese Franc",
            code="CDF",
            symbol="CDF",
            locale="fr-CD",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Kinshasa",
                cities=["Kinshasa City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Katanga",
                cities=["Lubumbashi", "Kolwezi"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National Identity Card",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Identity Card Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Identity Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Ghana = ConfigureCountryEntity(
        name="Ghana",
        code="+233",
        timezone=["Africa/Accra"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\+233(?:2|5|6|7)\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 12.5],
            remark="VAT Ghana",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Ghanaian Cedi",
            code="GHS",
            symbol="₵",
            locale="en-GH",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Greater Accra Region",
                cities=["Accra", "Tema"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Ashanti Region",
                cities=["Kumasi", "Obuasi"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Ghana Card",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Ghana Card Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Ghana Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Kenya = ConfigureCountryEntity(
        name="Kenya",
        code="+254",
        timezone=["Africa/Nairobi"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\+254(?:7|8|9)\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 16],
            remark="VAT Kenya",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Kenyan Shilling",
            code="KES",
            symbol="KES",
            locale="en-KE",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Nairobi",
                cities=["Nairobi City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Mombasa",
                cities=["Mombasa City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Maisha Namba",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Maisha Namba Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Maisha Namba Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Mozambique = ConfigureCountryEntity(
        name="Mozambique",
        code="+258",
        timezone=["Africa/Maputo"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\+258(?:8|9)\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 17],
            remark="VAT Mozambique",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Mozambican Metical",
            code="MZN",
            symbol="MZN",
            locale="pt-MZ",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Maputo",
                cities=["Maputo City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Beira",
                cities=["Beira City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Nampula",
                cities=["Nampula City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID Number",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National ID Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Rwanda = ConfigureCountryEntity(
        name="Rwanda",
        code="+250",
        timezone=["Africa/Maputo"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\+250(?:7|8|9)\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 18],
            remark="VAT Rwanda",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Rwandan Franc",
            code="RWF",
            symbol="RWF",
            locale="rw-RW",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Kigali",
                cities=["Kigali City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Butare",
                cities=["Butare City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Gisenyi",
                cities=["Gisenyi City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National Identity Card",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Identity Card Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Identity Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Uganda = ConfigureCountryEntity(
        name="Uganda",
        code="+256",
        timezone=["Africa/Kampala"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\+256(?:7|8|9)\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 18],
            remark="VAT Uganda",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Ugandan Shilling",
            code="UGX",
            symbol="UGX",
            locale="en-UG",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Kampala",
                cities=["Kampala City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Jinja",
                cities=["Jinja City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Mbarara",
                cities=["Mbarara City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Taxpayer Identification Number (TIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Taxpayer Identification Number (TIN)",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10,16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Australia = ConfigureCountryEntity(
        name="Australia",
        code="+61",
        timezone=["Australia/Sydney"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\+6104\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="GST",
            slabs=[10],
            remark="GST Australia",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Australian Dollar",
            code="AUD",
            symbol="$",
            locale="en-AU",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="New South Wales",
                cities=["Sydney"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Victoria",
                cities=["Melbourne"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Queensland",
                cities=["Brisbane"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Tax File Number (TFN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TFN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^[0-9]{8,9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TFN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^[0-9]{6,9}$",
                is_visible_on_app=True,
            ),
        ],
    )

    US = ConfigureCountryEntity(
        name="United States",
        code="+1",
        timezone=["America/New_York"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\+1\d{10}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="Sales Tax",
            slabs=[0, 7.25],
            remark="Sales Tax USA",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="United States Dollar",
            code="USD",
            symbol="$",
            locale="en-US",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="California",
                cities=["Los Angeles"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="New York",
                cities=["New York City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Texas",
                cities=["Houston"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Social Security Number (SSN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="SSN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="SSN Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
        ],
    )

    UK = ConfigureCountryEntity(
        name="United Kingdom",
        code="+44",
        timezone=["Europe/London"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\+44\d{10}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 5, 20],
            remark="VAT UK",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="British Pound Sterling",
            code="GBP",
            symbol="£",
            locale="en-GB",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="England",
                cities=["London"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Scotland",
                cities=["Edinburgh"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Wales",
                cities=["Cardiff"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National Insurance Number (NIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^[A-Za-z]{2}\d{6}[A-Za-z]$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=False,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{8}$",
                is_visible_on_app=True,
            ),
        ],
    )

    China = ConfigureCountryEntity(
        name="China",
        code="+86",
        timezone=["Asia/Shanghai"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=11,
            regex="^1[3-9]\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 6, 9, 13],
            remark="VAT China",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Chinese Yuan",
            code="CNY",
            symbol="¥",
            locale="zh-CN",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Beijing",
                cities=["Beijing City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Guangdong",
                cities=["Guangzhou", "Shenzhen"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Resident Identity Card",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^[A-Za-z0-9]{18}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[A-Za-z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{16,19}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Portugal = ConfigureCountryEntity(
        name="Portugal",
        code="+351",
        timezone=["Europe/Lisbon"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^9\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 6, 13, 23],
            remark="VAT Portugal",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Euro",
            code="EUR",
            symbol="€",
            locale="pt-PT",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Lisbon",
                cities=["Lisbon City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Porto",
                cities=["Porto City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Número de Identificação Fiscal (NIF)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIF Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIF Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^PT\d{23}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{22}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Italy = ConfigureCountryEntity(
        name="Italy",
        code="+39",
        timezone=["Europe/Rome"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\d{10}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[4, 5, 10, 22],
            remark="VAT Italy",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Euro",
            code="EUR",
            symbol="€",
            locale="it-IT",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Lazio",
                cities=["Rome"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Lombardy",
                cities=["Milan"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Codice Fiscale",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="CF Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^[A-Z0-9]{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="CF Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^IT\d{22}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{22}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Thailand = ConfigureCountryEntity(
        name="Thailand",
        code="+66",
        timezone=["Asia/Bangkok"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 7],
            remark="VAT Thailand",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Thai Baht",
            code="THB",
            symbol="฿",
            locale="th-TH",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Bangkok",
                cities=["Bangkok City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Chiang Mai",
                cities=["Chiang Mai City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Personal Identification Number (PIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="PIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{13}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="PIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,  # Note: Account number regex was not provided for Thailand
                is_visible_on_app=True,
            ),
        ],
    )

    Austria = ConfigureCountryEntity(
        name="Austria",
        code="+43",
        timezone=["Europe/Vienna"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^\d{10}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 5, 10, 20],
            remark="VAT Austria",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Euro",
            code="EUR",
            symbol="€",
            locale="de-AT",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Vienna",
                cities=["Vienna City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Tyrol",
                cities=["Innsbruck"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Steuer-Identifikationsnummer (Tax ID)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Steuer-Identifikationsnummer (Tax ID) Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Steuer-Identifikationsnummer Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^AT\d{20}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Iran = ConfigureCountryEntity(
        name="Iran",
        code="+98",
        timezone=["Asia/Tehran"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^9\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 9],
            remark="VAT Iran",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Iranian Rial",
            code="IRR",
            symbol="﷼",
            locale="fa-IR",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Tehran",
                cities=["Tehran City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Isfahan",
                cities=["Isfahan City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{16,19}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Iraq = ConfigureCountryEntity(
        name="Iraq",
        code="+964",
        timezone=["Asia/Baghdad"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^7\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="Sales Tax",
            slabs=[0, 15],
            remark="Sales Tax Iraq",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Iraqi Dinar",
            code="IQD",
            symbol="ع.د",
            locale="ar-IQ",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Baghdad",
                cities=["Baghdad City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Basra",
                cities=["Basra City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Taxpayer Identification Number (TIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Oman = ConfigureCountryEntity(
        name="Oman",
        code="+968",
        timezone=["Asia/Muscat"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=8,
            regex="^9\d{7}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 5],
            remark="VAT Oman",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Omani Rial",
            code="OMR",
            symbol="ر.ع.",
            locale="ar-OM",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Muscat",
                cities=["Muscat City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Dhofar",
                cities=["Salalah"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Taxpayer Identification Number (TIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^OM\d{23}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{23}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Yemen = ConfigureCountryEntity(
        name="Yemen",
        code="+967",
        timezone=["Asia/Aden"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^71\d{7}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 5],
            remark="VAT Yemen",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Yemeni Rial",
            code="YER",
            symbol="﷼",
            locale="ar-YE",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Sana'a",
                cities=["Sana'a City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Aden",
                cities=["Aden City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Taxpayer Identification Number (TIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Turkey = ConfigureCountryEntity(
        name="Turkey",
        code="+90",
        timezone=["Europe/Istanbul"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^5\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 18],
            remark="VAT Turkey",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Turkish Lira",
            code="TRY",
            symbol="₺",
            locale="tr-TR",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Istanbul",
                cities=["Istanbul City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Ankara",
                cities=["Ankara City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="T.C. Kimlik No (Turkish Identity Number)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Kimlik No",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="Kimlik Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^TR\d{24}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{24}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Egypt = ConfigureCountryEntity(
        name="Egypt",
        code="+20",
        timezone=["Africa/Cairo"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^1\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 14],
            remark="VAT Egypt",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Egyptian Pound",
            code="EGP",
            symbol="ج.م",
            locale="ar-EG",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Cairo",
                cities=["Cairo City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Alexandria",
                cities=["Alexandria City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{14}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Syria = ConfigureCountryEntity(
        name="Syria",
        code="+963",
        timezone=["Asia/Damascus"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^93\d{7}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 10],
            remark="VAT Syria",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Syrian Pound",
            code="SYP",
            symbol="ل.س",
            locale="ar-SY",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Damascus",
                cities=["Damascus City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Aleppo",
                cities=["Aleppo City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Lebanon = ConfigureCountryEntity(
        name="Lebanon",
        code="+961",
        timezone=["Asia/Beirut"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=8,
            regex="^70\d{6}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 11],
            remark="VAT Lebanon",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Lebanese Pound",
            code="LBP",
            symbol="ل.ل",
            locale="ar-LB",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Beirut",
                cities=["Beirut City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Mount Lebanon",
                cities=["Jounieh", "Baabda"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{8}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{8}$",
                is_visible_on_app=True,
            ),
        ],
    )

    SriLanka = ConfigureCountryEntity(
        name="Sri Lanka",
        code="+94",
        timezone=["Asia/Colombo"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^7\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 8],
            remark="VAT Sri Lanka",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Sri Lankan Rupee",
            code="LKR",
            symbol="රු",
            locale="si-LK",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Western Province",
                cities=["Colombo"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Central Province",
                cities=["Kandy"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National Identity Card (NIC)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIC Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIC Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Bangladesh = ConfigureCountryEntity(
        name="Bangladesh",
        code="+880",
        timezone=["Asia/Dhaka"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^1\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 15],
            remark="VAT Bangladesh",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Bangladeshi Taka",
            code="BDT",
            symbol="৳",
            locale="bn-BD",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Dhaka",
                cities=["Dhaka City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Chittagong",
                cities=["Chittagong City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National ID",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{17}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="ID Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Singapore = ConfigureCountryEntity(
        name="Singapore",
        code="+65",
        timezone=["Asia/Singapore"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=8,
            regex="^[89]\d{7}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="GST",
            slabs=[0, 7],
            remark="GST Singapore",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Singapore Dollar",
            code="SGD",
            symbol="S$",
            locale="en-SG",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Central Region",
                cities=["Singapore City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="East Region",
                cities=["Changi"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="NRIC (National Registration Identity Card)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NRIC Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="[ST]\d{7}[A-Za-z]$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NRIC Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{8}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Indonesia = ConfigureCountryEntity(
        name="Indonesia",
        code="+62",
        timezone=["Asia/Jakarta", "Asia/Makassar", "Asia/Jayapura"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^8\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 10],
            remark="VAT Indonesia",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Indonesian Rupiah",
            code="IDR",
            symbol="Rp",
            locale="id-ID",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Jakarta",
                cities=["Jakarta City"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Bali",
                cities=["Denpasar"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Kartu Tanda Penduduk (KTP)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="KTP Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{16}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="KTP Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Philippines = ConfigureCountryEntity(
        name="Philippines",
        code="+63",
        timezone=["Asia/Manila"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=10,
            regex="^9\d{9}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT (Value Added Tax)",
            slabs=[0, 12],
            remark="VAT Philippines",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Philippine Peso",
            code="PHP",
            symbol="₱",
            locale="en-PH",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="National Capital Region",
                cities=["Manila"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Cebu",
                cities=["Cebu City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Tax Identification Number (TIN)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{9}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="TIN Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex="^\d{10}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Belgium = ConfigureCountryEntity(
        name="Belgium",
        code="+32",
        timezone=["Europe/Brussels"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^4\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT",
            slabs=[0, 6, 12, 21],
            remark="Belgium",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Euro",
            code="EUR",
            symbol="€",
            locale="nl-BE",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Flanders",
                cities=["Antwerp", "Ghent"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Wallonia",
                cities=["Liège", "Namur"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Brussels-Capital",
                cities=["Brussels City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="National Registration Number",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Registration Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^\d{11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="National Registration Card",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^BE\d{14}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{14}$",
                is_visible_on_app=True,
            ),
        ],
    )

    Spain = ConfigureCountryEntity(
        name="Spain",
        code="+34",
        timezone=["Europe/Madrid"],
        phone_number=ConfigureCountryEntity.PhoneNumberEntity(
            max_length=9,
            regex="^6\d{8}$",
        ),
        tax=ConfigureCountryEntity.TaxEntity(
            type="VAT",
            slabs=[0, 4, 10, 21],
            remark="Spain",
        ),
        currency=ConfigureCountryEntity.CurrencyEntity(
            name="Euro",
            code="EUR",
            symbol="€",
            locale="es-ES",
        ),
        state_city_mapping=[
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Andalusia",
                cities=["Seville", "Málaga"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Catalonia",
                cities=["Barcelona", "Girona"],
            ),
            ConfigureCountryEntity.StateCityMappingEntity(
                state="Madrid",
                cities=["Madrid City"],
            ),
        ],
        sections=[
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="KYC Details (Know Your Customer)",
                position=0,
                type=OrganizationSectionTypeChoices.KYC_DETAILS,
            ),
            ConfigureCountryEntity.OrganizationSectionConfigEntity(
                name="Bank Details",
                position=1,
                type=OrganizationSectionTypeChoices.BANK_DETAILS,
            ),
        ],
        docs=[
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=0,
                name="Número de Identificación Fiscal (NIF)",
                position=0,
                is_required=True,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentConfigEntity(
                section_idx=1,
                name="Bank Details",
                position=0,
                is_visible_on_app=True,
            ),
        ],
        fields=[
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIF Number",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex="^[A-Z]\d{8}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=0,
                name="NIF Document",
                type=CustomFieldTypeChoices.FILE,
                position=1,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Holder Name",
                type=CustomFieldTypeChoices.TEXT,
                position=0,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="IBAN",
                type=CustomFieldTypeChoices.TEXT,
                position=1,
                is_required=True,
                regex="^ES\d{20}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="SWIFT/BIC Code",
                type=CustomFieldTypeChoices.TEXT,
                position=2,
                is_required=True,
                regex="^[a-zA-Z0-9]{8,11}$",
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Branch Name",
                type=CustomFieldTypeChoices.TEXT,
                position=3,
                is_required=True,
                regex=None,
                is_visible_on_app=True,
            ),
            ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
                document_idx=1,
                name="Account Number",
                type=CustomFieldTypeChoices.TEXT,
                position=4,
                is_required=True,
                regex="^\d{20}$",
                is_visible_on_app=True,
            ),
        ],
    )


"""
How to onboard a new country:
1. Create a new instance of ConfigureCountryEntity with the required details.
2. Run Configure(country_config).process() to create the country, timezone, tax type, currency, and data fields.


eg: 
from script.feature_middle_east import ConfigureCountry, Configure

country_config = ConfigureCountry.Australia
Configure(country_config).process()

"""


class Configure:
    def __init__(self, country_config: ConfigureCountryEntity):
        self.country_config = country_config

    def create_timezone(self) -> Timezone:
        default_timezone = None
        for idx, timezone in enumerate(self.country_config.timezone):
            created_timezone, _ = Timezone.objects.get_or_create(
                locale=self.country_config.currency.locale, tz=timezone
            )
            if idx == 0:
                default_timezone = created_timezone

        assert default_timezone, "Default timezone not found"

        return default_timezone

    def create_text_type(self) -> TaxType:
        tax, _ = TaxType.objects.get_or_create(name=self.country_config.tax.type, remark=self.country_config.tax.remark)
        slabs = []
        for slab in self.country_config.tax.slabs:
            if TaxSlab.objects.filter(tax_type_id=tax.pk, tax_percent=slab).exists():
                continue

            slabs.append(TaxSlab(tax_type_id=tax.pk, tax_percent=slab))
        TaxSlab.objects.bulk_create(slabs)
        return tax

    def create_currency(self) -> Currency:
        currency = Currency(
            name=self.country_config.currency.name,
            code=self.country_config.currency.code,
            symbol=self.country_config.currency.symbol,
            locale=self.country_config.currency.locale,
        )
        currency.save()
        return currency

    def create_country(self, timezone: Timezone, tax: TaxType, currency: Currency) -> Country:
        country, _ = Country.objects.get_or_create(
            name=self.country_config.name,
            defaults={
                "code": self.country_config.code,
                "timezone": timezone.tz,
                "phone_number_length": self.country_config.phone_number.max_length,
                "phone_number_regex": self.country_config.phone_number.regex,
                "is_state_available": self.country_config.is_state_available,
                "created_by_id": SYSTEM_USER_ID,
            },
        )
        max_slabs = max(self.country_config.tax.slabs)

        CountryTimezoneMapping.objects.create(country=country, timezone=timezone, is_default=True)
        CountryTaxMapping.objects.create(
            country=country,
            tax_type=tax,
            is_default=True,
            max_slab_percent=max_slabs,
        )
        CountryCurrencyMapping.objects.create(country=country, currency=currency, is_default=True)
        states = []
        state_name_to_city_mapping: dict[str, list[str]] = {}
        for mapping in self.country_config.state_city_mapping:
            states.append(
                State(
                    name=mapping.state,
                    country=country,
                )
            )
            state_name_to_city_mapping[mapping.state] = mapping.cities

        states = State.objects.bulk_create(states)
        states = State.objects.filter(country_id=country.pk)
        cities = []
        for state in states:
            print(f"Cities for state: {state.name} , cities: {state_name_to_city_mapping.get(state.name, [])}")
            for city_name in state_name_to_city_mapping.get(state.name, []):
                cities.append(City(name=city_name, state=state, country_id=country.pk))

        City.objects.bulk_create(cities)
        print("Country, State, and City created successfully")
        return country

    def create_data_fields(self, country: Country):
        section_objs: list[OrganizationSectionConfig] = []

        for section in self.country_config.sections:
            instance = OrganizationSectionConfig()
            instance.name = section.name
            instance.type = section.type
            instance.position = section.position
            instance.country = country

            section_objs.append(instance)

        sections = OrganizationSectionConfig.objects.bulk_create(objs=section_objs)

        section_id_to_section_mapping: dict[int, OrganizationSectionConfig] = {}
        for idx, section in enumerate(sections):
            section_id_to_section_mapping[idx] = sections[idx]

        doc_objs: list[OrganizationDocumentConfig] = []
        for doc in self.country_config.docs:
            instance = OrganizationDocumentConfig()
            instance.name = doc.name
            instance.section = section_id_to_section_mapping[doc.section_idx]
            instance.position = doc.position
            instance.is_required = doc.is_required
            instance.is_visible_on_app = doc.is_visible_on_app
            instance.country = country

            doc_objs.append(instance)

        docs = OrganizationDocumentConfig.objects.bulk_create(objs=doc_objs)

        doc_id_to_doc_mapping: dict[int, OrganizationDocumentConfig] = {}
        for idx, doc in enumerate(docs):
            doc_id_to_doc_mapping[idx] = docs[idx]

        field_objs: list[OrganizationDocumentFieldConfig] = []

        for field in self.country_config.fields:
            instance = OrganizationDocumentFieldConfig()
            instance.name = field.name
            instance.document_config = doc_id_to_doc_mapping[field.document_idx]
            instance.type = field.type
            instance.position = field.position
            instance.is_required = field.is_required
            instance.regex = field.regex
            instance.is_visible_on_app = field.is_visible_on_app

            field_objs.append(instance)

        fields = OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=field_objs,
        )

        country.uid_field_id = fields[0].pk  # Country Unique Field (like PAN):
        country.save()

        print("Data Fields created successfully")

    @transaction.atomic
    def process(self):
        timezone = self.create_timezone()
        tax = self.create_text_type()
        currency = self.create_currency()
        country = self.create_country(timezone=timezone, tax=tax, currency=currency)
        self.create_data_fields(country=country)
