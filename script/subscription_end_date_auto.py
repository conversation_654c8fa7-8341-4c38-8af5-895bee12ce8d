import pandas as pd
import datetime
from core.models import OrganizationConfig
from django.db import transaction
import pytz

def parse_csv_date(date_val):
    DATE_FORMATS = ["%d-%b-%y", "%Y-%m-%d", "%d/%m/%Y"]
    if isinstance(date_val, datetime.date):
        return date_val
    if pd.isna(date_val):
        return None
    for fmt in DATE_FORMATS:
        try:
            return datetime.datetime.strptime(str(date_val), fmt).date()
        except Exception:
            continue
    try:
        return pd.to_datetime(date_val).date()
    except Exception:
        return None

@transaction.atomic
def bulk_update_subscription_end_dates(csv_path):
    print(f"\nReading CSV from: {csv_path}\n")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows.")
    updated, org_config_not_found, skipped, errors = [], [], [], []

    for idx, row in df.iterrows():
        try:
            org_id = int(str(row['RDash Org ID']).replace(",", "").strip())
            end_date = parse_csv_date(row['Subscription End Date'])

            org_config = OrganizationConfig.objects.filter(organization_id=org_id).first()
            if not org_config:
                print(f"  >> ORG CONFIG NOT FOUND: {org_id}")
                org_config_not_found.append(org_id)
                continue

            # If timezone not present, skip updating this org
            if not getattr(org_config, "timezone", None):
                print(f"  >> Skipping org_id={org_id}: No timezone set for this organization.")
                skipped.append((org_id, 'No timezone'))
                continue

            org_tz = pytz.timezone(str(org_config.timezone.tz))
            today_in_org_tz = datetime.datetime.now(tz=org_tz).date()

            # Convert end_date to org's timezone as a datetime
            if end_date:
                end_date_dt = datetime.datetime.combine(end_date, datetime.time.min)
                end_date_in_org_tz = org_tz.localize(end_date_dt).date()
            else:
                end_date_in_org_tz = None

            if end_date_in_org_tz and end_date_in_org_tz < today_in_org_tz:
                print(f"  >> Skipping org_id={org_id}: Subscription end date {end_date} is in the past (org's today={today_in_org_tz}).")
                skipped.append((org_id, end_date))
                continue

            print(f"\nProcessing org_id={org_id} for date={end_date}...")

            before = org_config.subscription_end_date
            org_config.subscription_end_date = end_date
            org_config.clean()
            org_config.save(update_fields=["subscription_end_date"])
            print(f"  >> Updated org_id={org_id}: {before} -> {end_date}")
            updated.append((org_id, before, end_date))
        except Exception as e:
            print(f"  >> ERROR for org_id={org_id}: {e}")
            errors.append((org_id, row.get('Subscription End Date'), str(e)))

    print("\n=== SUMMARY ===")
    print(f"Total updated: {len(updated)}")
    print(f"Skipped (date in past or missing timezone): {skipped}")
    print(f"Not found: {org_config_not_found}")
    print(f"Errors: {errors}")

    return {"updated": updated, "skipped": skipped, "org_config_not_found": org_config_not_found, "errors": errors}
