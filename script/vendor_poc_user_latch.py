from django.db.models import F

from vendorv2.data.models import VendorPoc


def no_of_vendor_poc_with_invited_true_user_id_null():
    vendor_pocs = VendorPoc.objects.filter(is_invited=True, user_id__isnull=True)
    print(f"Vendor POCs: {list(vendor_pocs.values())}\n\nTotal Count: {vendor_pocs.count()}")

def latch_user_to_vendor_poc():
    VendorPoc.objects.filter(is_invited=True, user_id__isnull=True).update(user_id=F("created_by_id"))
    print("User ids successfully latched")

