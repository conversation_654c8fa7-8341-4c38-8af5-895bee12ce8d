from boq.data.models import BoqElement, BoqSection

"""
To Verify:

select count(boq_elements) from boq_sections 
left join project_project on project_project.id = boq_sections.boq_id
left join core_organization on core_organization.id = project_project.organization_id
left join boq_elements on boq_elements.section_id = boq_sections.id
where boq_sections.organization_id isnull and boq_elements.section_id isnull

"""


def backfill_org_id():
    boq_elements = (
        BoqElement.objects.all().filter(organization_id__isnull=True).select_related("boq__project__organization")
    )

    updates = []
    for boq_element in boq_elements:
        boq = boq_element.boq

        boq_element.organization_id = boq.project.organization.pk
        updates.append(boq_element)

    BoqElement.objects.bulk_update(updates, ["organization_id"])

    boq_sections = (
        BoqSection.objects.all().filter(organization_id__isnull=True).select_related("boq__project__organization")
    )

    updates = []
    for boq_section in boq_sections:
        boq = boq_section.boq

        boq_section.organization_id = boq.project.organization.pk
        updates.append(boq_section)

    BoqSection.objects.bulk_update(updates, ["organization_id"])


if __name__ == "__main__":
    backfill_org_id()
