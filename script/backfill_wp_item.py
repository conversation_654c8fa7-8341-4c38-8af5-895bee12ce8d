from progressreport.models import ProgressReportItem, ProgressReport
from common.json_parser.constants import TextEditorJsonConstants as JSON_CONST
from report.download.data.choices import DownloadProgressStatusChoices
from django.db import transaction


@transaction.atomic
def backfill_wp_item():
    organization_id = 44278  # Ethos Constructions Solution LLP
    project_id = 172458

    items = ProgressReportItem.objects.select_related("progress_report").filter(
        progress_report__organization_id=organization_id,
        progress_report__ppr_id=project_id,
    )

    report_ids = set()

    updates = []
    for item in items:

        old_blocks = item.blocks

        new_blocks = []

        for block in old_blocks:
            if block["type"] in [JSON_CONST.TEXT, JSON_CONST.HASHTAG, JSON_CONST.HYPERLINK]:
                new_blocks.append(block)

        if len(new_blocks) == 0:
            report_ids.add(item.progress_report_id)
            item.delete()
            continue

        item.blocks = new_blocks

        updates.append(item)
        report_ids.add(item.progress_report_id)

    ProgressReportItem.objects.bulk_update(updates, ["blocks"])
    ProgressReport.objects.filter(id__in=report_ids).update(pdf=None, pdf_status=DownloadProgressStatusChoices.FAILED)
