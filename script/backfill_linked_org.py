from django.db.models import Prefetch
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.models import (
    OrganizationDocumentConfig,
    OrganizationBillingEntity,
    OrganizationDocumentFieldConfig,
    OrganizationSectionConfig,
    Organization,
    LinkedOrganizationBillingEntity,
    LinkedOrganizations,
    LinkedOrganizationBusinessCard,
    LinkedOrganizationAddress,
    LinkedOrganizationDocument,
    OrganizationDocumentV2,
    OrganizationDocumentTextFieldData,
)
from core.models import Country,OrganizationDocument, OrganizationAddress
from common.pydantic.base_model import BaseModel
from common.constants import CustomFieldTypeChoices
from django.db import transaction
from core.choices import OrganizationDocumentChoices
from django.core.paginator import Paginator
from django.utils import timezone
from common.constants  import SYSTEM_USER_ID


class ConfigureCountryEntity(BaseModel):
    class OrganizationSectionConfigEntity(BaseModel):
        name: str
        position: int
        type: OrganizationSectionTypeChoices

    class OrganizationDocumentConfigEntity(BaseModel):
        section_idx: int
        name: str
        position: int
        is_required: bool = False
        is_visible_on_app: bool

    class OrganizationDocumentFieldConfigEntity(BaseModel):
        document_idx: int
        name: str
        type: CustomFieldTypeChoices
        position: int
        is_required: bool
        regex: str | None
        is_visible_on_app: bool

    sections: list[OrganizationSectionConfigEntity]
    docs: list[OrganizationDocumentConfigEntity]
    fields: list[OrganizationDocumentFieldConfigEntity]

class Config:
    sections=[
        ConfigureCountryEntity.OrganizationSectionConfigEntity(
            name="Other Details",
            position=2,
            type=OrganizationSectionTypeChoices.OTHER_DETAILS,
        ),
    ]
    docs=[
        ConfigureCountryEntity.OrganizationDocumentConfigEntity(
            section_idx=0,
            name="Signing Authority",
            position=0,
            is_required=False,
            is_visible_on_app=False,
        ),
        ConfigureCountryEntity.OrganizationDocumentConfigEntity(
            section_idx=0,
            name="Key POC Details",
            position=1,
            is_visible_on_app=False,
        ),
    ]
    fields=[
        ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
            document_idx=0,
            name="Authority Name",
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            regex=None,
            is_visible_on_app=False,
        ),
        ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
            document_idx=0,
            name="Stamp",
            type=CustomFieldTypeChoices.FILE,
            position=1,
            is_required=False,
            regex=None,
            is_visible_on_app=False,
        ),
        ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
            document_idx=1,
            name="Key POC Name",
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            regex=None,
            is_visible_on_app=False,
        ),
        ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
            document_idx=1,
            name="POC Mobile Number",
            type=CustomFieldTypeChoices.PHONE_NUMBER,
            position=1,
            is_required=False,
            regex=None,
            is_visible_on_app=False,
        ),
        ConfigureCountryEntity.OrganizationDocumentFieldConfigEntity(
            document_idx=1,
            name="POC Email Address",
            type=CustomFieldTypeChoices.TEXT,
            position=2,
            is_required=False,
            regex=None,
            is_visible_on_app=False,
        ),
    ]

    document_country_mapping: dict[str, dict] = {
        # "China": {
        #     "document_name": "Resident Identity Card",
        #     "position": 2,
        #     "field_name": "Registered Business Address",
        # },
        "Italy": {
            "document_name": "Codice Fiscale",
            "position": 2,
            "field_name": "Sede Legale",
        },
        # "Bangladesh": {
        #     "document_name": "National ID",
        #     "position": 2,
        #     "field_name": "Business Trade License Address",
        # },
        # "Egypt": {
        #     "document_name": "National ID",
        #     "position": 2,
        #     "field_name": "Business Location",
        # },
        # "Indonesia": {
        #     "document_name": "Kartu Tanda Penduduk (KTP)",
        #     "position": 2,
        #     "field_name": "Business Address",
        # },
        # "Iran": {
        #     "document_name": "National ID",
        #     "position": 2,
        #     "field_name": "Business Premises Address",
        # },
        "Iraq": {
            "document_name": "Taxpayer Identification Number (TIN)",
            "position": 2,
            "field_name": "Business Address",
        },
        # "Lebanon": {
        #     "document_name": "National ID",
        #     "position": 2,
        #     "field_name": "Main Office Address",
        # },
        # "Sri Lanka": {
        #     "document_name": "National Identity Card (NIC)",
        #     "position": 2,
        #     "field_name": "Registered Business Address",
        # },
        # "Syria": {
        #     "document_name": "National ID",
        #     "position": 2,
        #     "field_name": "Business Activity Address",
        # },
        "Oman": {
            "document_name": "Taxpayer Identification Number (TIN)",
            "position": 2,
            "field_name": "Commercial Registration Address",
        },
        # "United States": {
        #     "document_name": "Social Security Number (SSN)",
        #     "position": 2,
        #     "field_name": "Principal Place of Business",
        # },
        "Philippines": {
            "document_name": "Tax Identification Number (TIN)",
            "position": 2,
            "field_name": "Registered Business Address",
        },
        "Portugal": {
            "document_name": "Número de Identificação Fiscal (NIF)",
            "position": 2,
            "field_name": "Morada da Sede",
        },
        "Kuwait": {
            "document_name": "Trade License",
            "position": 5,
            "field_name": "CR Address",
        },
        # "Thailand": {
        #     "document_name": "Personal Identification Number (PIN)",
        #     "position": 2,
        #     "field_name": "Business Establishment Address",
        # },
        # "Turkey": {
        #     "document_name": "T.C. Kimlik No (Turkish Identity Number)",
        #     "position": 2,
        #     "field_name": "Business Address",
        # },
        "Yemen": {
            "document_name": "Taxpayer Identification Number (TIN)",
            "position": 2,
            "field_name": "Registered Business Address",
        },
        "Austria": {
            "document_name": "Steuer-Identifikationsnummer (Tax ID)",
            "position": 2,
            "field_name": "Sitz der Gesellschaft",
        },
        "Spain": {
            "document_name": "Número de Identificación Fiscal (NIF)",
            "position": 2,
            "field_name": "Domicilio Fiscal",
        },
        "Bahrain": {
            "document_name": "VAT Registration",
            "position": 2,
            "field_name": "Commercial Registration Address",
        },
        "Saudi Arabia": {
            "document_name": "Trade License",
            "position": 5,
            "field_name": "Registered Business Address",
        },
        # "Belgium": {
        #     "document_name": "National Registration Number",
        #     "position": 2,
        #     "field_name": "Registered Office Address",
        # },
        "UAE": {
            "document_name": "VAT",
            "position": 2,
            "field_name": "Trade License Address",
        },
        "Qatar": {
            "document_name": "Trade License",
            "position": 5,
            "field_name": "Commercial Registration Address",
        },
        "India": {
            "document_name": "GST",
            "position": 3,
            "field_name": "Address of Principal Place of Business",
        },
        "Australia": {
            "document_name": "Tax File Number (TFN)",
            "position": 2,
            "field_name": "Principal Place of Business",
        },
        # "United Kingdom": {
        #     "document_name": "National Insurance Number (NIN)",
        #     "position": 2,
        #     "field_name": "Registered Office Address",
        # },
        # "Kenya": {
        #     "document_name": "Maisha Namba",
        #     "position": 2,
        #     "field_name": "PIN Registration Address",
        # },
        "Uganda": {
            "document_name": "Taxpayer Identification Number (TIN)",
            "position": 2,
            "field_name": "URA Registered Business Address",
        },
        # "Democratic Republic of Congo": {
        #     "document_name": "National Identity Card",
        #     "position": 2,
        #     "field_name": "Business Address",
        # },
        # "Mozambique": {
        #     "document_name": "National ID Number",
        #     "position": 2,
        #     "field_name": "Headquarters Address",
        # },
        # "Rwanda": {
        #     "document_name": "National Identity Card",
        #     "position": 2,
        #     "field_name": "RRA Registered Business Address",
        # },
        # "Ghana": {
        #     "document_name": "Ghana Card",
        #     "position": 2,
        #     "field_name": "GRA Registered Business Premises Address",
        # },
        # "Singapore": {
        #     "document_name": "NRIC (National Registration Identity Card)",
        #     "position": 2,
        #     "field_name": "Registered Office Address",
        # },
    }
    
    def add_other_detail_section_config(self, country: Country):
        section_objs: list[OrganizationSectionConfig] = []

        for section in self.sections:
            instance = OrganizationSectionConfig()
            instance.name = section.name
            instance.type = section.type
            instance.position = section.position
            instance.country = country

            section_objs.append(instance)

        sections = OrganizationSectionConfig.objects.bulk_create(objs=section_objs)

        section_id_to_section_mapping: dict[int, OrganizationSectionConfig] = {}
        for idx, section in enumerate(sections):
            section_id_to_section_mapping[idx] = sections[idx]

        doc_objs: list[OrganizationDocumentConfig] = []
        for doc in self.docs:
            instance = OrganizationDocumentConfig()
            instance.name = doc.name
            instance.section = section_id_to_section_mapping[doc.section_idx]
            instance.position = doc.position
            instance.is_required = doc.is_required
            instance.is_visible_on_app = doc.is_visible_on_app
            instance.country = country

            doc_objs.append(instance)

        docs = OrganizationDocumentConfig.objects.bulk_create(objs=doc_objs)

        doc_id_to_doc_mapping: dict[int, OrganizationDocumentConfig] = {}
        for idx, doc in enumerate(docs):
            doc_id_to_doc_mapping[idx] = docs[idx]

        field_objs: list[OrganizationDocumentFieldConfig] = []

        for field in self.fields:
            instance = OrganizationDocumentFieldConfig()
            instance.name = field.name
            instance.document_config = doc_id_to_doc_mapping[field.document_idx]
            instance.type = field.type
            instance.position = field.position
            instance.is_required = field.is_required
            instance.regex = field.regex
            instance.is_visible_on_app = field.is_visible_on_app

            field_objs.append(instance)

        OrganizationDocumentFieldConfig.objects.bulk_create(
            objs=field_objs
        )

    def add_gst_address_config(self, country_name: str):
        country = Country.objects.filter(name=country_name).first()
        if not country:
            print(f"Country not found: {country_name}")
            return
        
        section = OrganizationSectionConfig.objects.get(country=country, type=OrganizationSectionTypeChoices.KYC_DETAILS)
        document_name = self.document_country_mapping.get(country.name, {}).get("document_name", None)

        if not document_name:
            raise ValueError(f"No document mapping found for country: {country.name}")

        document_config = OrganizationDocumentConfig.objects.filter(
            country=country,
            section=section,
            name=document_name
        ).first()

        if not document_config:
            raise ValueError(f"Document config not found for country: {country.name}, document: {document_name}")
        
        field_name = self.document_country_mapping.get(country.name, {}).get("field_name", None)

        if not field_name:
            raise ValueError(f"No field mapping found for country: {country.name}")
        
        field_config = OrganizationDocumentFieldConfig()

        field_config.document_config = document_config
        field_config.name = field_name
        field_config.type = CustomFieldTypeChoices.LONG_TEXT
        field_config.position = self.document_country_mapping[country.name]["position"]
        field_config.is_required = False
        field_config.is_visible_on_app = False
        field_config.regex = None
        field_config.save()
        


@transaction.atomic
def new_section():
    countries = Country.objects.all()

    print(f"Total countries found: {countries.count()}")

    for country in countries:
        print(f"Processing country: {country.name}")

        config = Config()
        config.add_other_detail_section_config(country)

        print(f"Completed processing for country: {country.name}")

    print("All countries processed successfully.")


@transaction.atomic
def create_linked_billing_entity():
    prefetch_business_cards = Prefetch(
        "organization_document",
        queryset=OrganizationDocument.objects.filter(type=OrganizationDocumentChoices.BUSINESS_CARD),
        to_attr="business_cards",
    )
    organizations = Organization.objects.all().order_by("id").prefetch_related("documents", "addresses", prefetch_business_cards).select_related("country__uid_field")

    print(f"Total organizations found: {organizations.count()}")

    paginator = Paginator(organizations, 100)

    timezone_now = timezone.now()

    org_number = 1

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        orgs = []
        org_mapping = {}
        linked_mapping = {}

        for org in page.object_list:

            print(org_number, " -> org_id:", org.id)
            org_number+=1

            billing_entity = OrganizationBillingEntity(
                organization=org,
                name=org.name,
                logo=org.logo,
                is_default=True,
                is_primary=True,
                created_by_id=SYSTEM_USER_ID,
                created_at=timezone_now,
                country_id=org.country_id
            )

            org_mapping[org.id] = {
                "billing_entity": billing_entity,
            }


            for document in org.documents.all():
                document.billing_entity = billing_entity

                org_mapping[org.id]["org_documents"] = org_mapping[org.id].get("org_documents", [])
                org_mapping[org.id]["org_documents"].append(document)

                uid_field_value = OrganizationDocumentTextFieldData.objects.filter(
                    document=document,
                    field_config_id=org.country.uid_field.id
                ).first()

                if uid_field_value:
                    org.uid = uid_field_value.data
                    orgs.append(org)
            

            for address in org.addresses.all():
                address.billing_entity = billing_entity

                org_mapping[org.id]["addresses"] = org_mapping[org.id].get("addresses", [])
                org_mapping[org.id]["addresses"].append(address)

            for business_card in org.business_cards:
                business_card.billing_entity = billing_entity

                org_mapping[org.id]["business_cards"] = org_mapping[org.id].get("business_cards", [])
                org_mapping[org.id]["business_cards"].append(business_card)

            linked_organizations = LinkedOrganizations.objects.filter(org=org.id).select_related("client", "vendor").prefetch_related(
                "linked_documents", 
                "addresses", 
                "business_cards"
            ).all()

            for linked_org in linked_organizations:

                as_client = linked_org.vendor == org

                billing_entity = LinkedOrganizationBillingEntity(
                    mapping=linked_org,
                    name=linked_org.client.name if as_client else linked_org.vendor.name,
                    logo=linked_org.client.logo if as_client else linked_org.vendor.logo,
                    is_default=True,
                    is_primary=True,
                    created_by_id=SYSTEM_USER_ID,
                    created_at=timezone_now,
                    country_id=linked_org.client.country_id if as_client else linked_org.vendor.country_id
                )

                linked_mapping[linked_org.id] = {
                    "billing_entity": billing_entity,
                    "linked_org": linked_org,
                }

                for linked_document in linked_org.linked_documents.all():
                    linked_document.billing_entity=billing_entity

                    linked_mapping[linked_org.id]["org_documents"] = linked_mapping[linked_org.id].get("org_documents", [])
                    linked_mapping[linked_org.id]["org_documents"].append(linked_document)

                for address in linked_org.addresses.all():
                    address.billing_entity =billing_entity

                    linked_mapping[linked_org.id]["linked_addresses"] = linked_mapping[linked_org.id].get("linked_addresses", [])
                    linked_mapping[linked_org.id]["linked_addresses"].append(address)

                for business_card in linked_org.business_cards.all():
                    business_card.billing_entity=billing_entity

                    linked_mapping[linked_org.id]["linked_business_cards"] = linked_mapping[linked_org.id].get("linked_business_cards", [])
                    linked_mapping[linked_org.id]["linked_business_cards"].append(business_card)

        org_billing_entities = OrganizationBillingEntity.objects.bulk_create(
            [mapping["billing_entity"] for mapping in org_mapping.values()]
        )

        created_org_to_billing_entity_mapping = {
            org_billing_entity.organization_id: org_billing_entity
            for org_billing_entity in org_billing_entities
        }

        org_documents = []
        addresses = []
        business_cards = []
        for org_id, mapping in org_mapping.items():
            billing_entity = mapping["billing_entity"]
            for document in mapping.get("org_documents", []):
                document.billing_entity = created_org_to_billing_entity_mapping[org_id] 
                org_documents.append(document)

            for address in mapping.get("addresses", []):
                address.billing_entity = created_org_to_billing_entity_mapping[org_id]
                addresses.append(address)

            for business_card in mapping.get("business_cards", []):
                business_card.billing_entity =  created_org_to_billing_entity_mapping[org_id]
                business_cards.append(business_card)

        OrganizationDocumentV2.objects.bulk_update(org_documents, ['billing_entity'])
        OrganizationAddress.objects.bulk_update(addresses, ['billing_entity'])
        OrganizationDocument.objects.bulk_update(business_cards, ['billing_entity'])

        linked_billing_entities = LinkedOrganizationBillingEntity.objects.bulk_create(
            [mapping["billing_entity"] for mapping in linked_mapping.values()]
        )

        created_linked_to_billing_entity_mapping = {
            linked_billing_entity.mapping_id: linked_billing_entity
            for linked_billing_entity in linked_billing_entities
        }

        linked_org_documents = []
        linked_addresses = []
        linked_business_cards = []
        linked_organizations = []
        for linked_org_id, mapping in linked_mapping.items():
            billing_entity = mapping["billing_entity"]
            for document in mapping.get("org_documents", []):
                document.billing_entity = created_linked_to_billing_entity_mapping[linked_org_id]
                linked_org_documents.append(document)

            for address in mapping.get("linked_addresses", []):
                address.billing_entity = created_linked_to_billing_entity_mapping[linked_org_id]
                linked_addresses.append(address)

            for business_card in mapping.get("linked_business_cards", []):
                business_card.billing_entity = created_linked_to_billing_entity_mapping[linked_org_id]
                linked_business_cards.append(business_card)

            linked_org = mapping["linked_org"]
            linked_org.primary_billing_entity = created_linked_to_billing_entity_mapping[linked_org_id]

            linked_organizations.append(linked_org)

        LinkedOrganizationDocument.objects.bulk_update(linked_org_documents, ['billing_entity'])
        LinkedOrganizationAddress.objects.bulk_update(linked_addresses, ['billing_entity'])
        LinkedOrganizationBusinessCard.objects.bulk_update(linked_business_cards, ['billing_entity'])
        LinkedOrganizations.objects.bulk_update(
            linked_organizations, ['primary_billing_entity']
        )


        Organization.objects.bulk_update(orgs, ['uid'])



    print("Linked billing entities created successfully.")


@transaction.atomic
def add_gst_address_config():
    config = Config()

    for country_name in config.document_country_mapping.keys():
        print(f"Processing country: {country_name}")
        config.add_gst_address_config(country_name)
    
    print("GST address configurations added successfully.")
            
                    




