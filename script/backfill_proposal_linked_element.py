from boq.data.choices import BoqElementStatus
from boq.data.models import BoqElement
from django.db.models import Q
from proposal.domain.constants import ProposalStatus
from proposal.data.models import ProposalElementMapping
from django.core.paginator import Paginator


def proposal_linked_element():
    boq_elements = BoqElement.objects.filter(~Q(element_status=BoqElementStatus.DRAFT)).order_by("id")

    print(f"Total elements: {boq_elements.count()}")
    print("paginator method")
    paginator = Paginator(boq_elements, 10000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        for boq_element in page.object_list:
            proposal_element = (
                ProposalElementMapping.objects.filter(
                    element_id=boq_element.pk, proposal__status=ProposalStatus.PENDING_APPROVAL
                )
                .select_related("proposal")
                .available()
                .order_by("-proposal__created_at")
                .first()
            )

            if proposal_element:
                proposal_element.linked_element_id = boq_element.pk
                updates.append(proposal_element)

        ProposalElementMapping.objects.bulk_update(updates, ["linked_element_id"])
        print(f"Updated {len(updates)} elements")
