from collections import defaultdict
from progressreport.models import ProgressReportSiteViewPointMapping, ProgressReportSiteViewPointMappingAttachment, \
    ProgressReport
def remove_files(project_id, save=False):
    for progress_report in ProgressReport.objects.filter(ppr_id=project_id).order_by("-id"):
        mapping_ids = (
            ProgressReportSiteViewPointMapping.objects
            .filter(progress_report=progress_report)
            .values_list("id", flat=True)
        )
        for mapping_id in mapping_ids:
            print(f"Mapping ID - {mapping_id}")
            attachments = ProgressReportSiteViewPointMappingAttachment.objects.filter(mapping_id=mapping_id)
            print(f"Project id - {project_id}, Total_attachments {attachments.count()}")
            attachment_ids_to_be_deleted = []
            mapping_id_to_total_attachment_id_map = defaultdict(list)
            mapping_id_to_unique_attachment_id_map = defaultdict(set)
            mapping_id_to_duplicate_attachment_id_map = defaultdict(list)
            for attachment in attachments:
                # store total attachments
                mapping_id_to_total_attachment_id_map[attachment.mapping_id].append(attachment.id)
                if attachment.file.url in mapping_id_to_unique_attachment_id_map[attachment.mapping_id]:
                    mapping_id_to_duplicate_attachment_id_map[attachment.mapping_id].append(attachment.id)
                    continue
                # unique file url
                mapping_id_to_unique_attachment_id_map[attachment.mapping_id].add(attachment.file.url)
            for mapping_id, file_urls in mapping_id_to_unique_attachment_id_map.items():
                print(
                    f"Mapping ID - {mapping_id}, total_files - {len(mapping_id_to_total_attachment_id_map[mapping_id])}, Unique files - {len(file_urls)}" # noqa
                )
            for mapping_id, attachment_ids in mapping_id_to_duplicate_attachment_id_map.items():
                print(f"Mapping ID - {mapping_id}, Duplicate files - {len(attachment_ids)}")
                attachment_ids_to_be_deleted.extend(attachment_ids)
            # delete duplicate attachments
            if save:
                ProgressReportSiteViewPointMappingAttachment.objects.filter(id__in=attachment_ids_to_be_deleted).delete()
            print("--------------------------------------------------------------------- \n")