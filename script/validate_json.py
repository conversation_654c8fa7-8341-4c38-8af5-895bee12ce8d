import json
import subprocess
import threading
import time


token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJrZXkiOiIwYWY3MDRlODFhMzU5MjZiMThkN2IwNjE1ZDNiMGZkYmQ4NjBlMTJmIiwib3JnX2lkIjoiRFdwSmE0TVpXTnJSWm4yayIsImlzX2FkbWluIjp0cnVlLCJvcmdfdHlwZSI6IkNMSUVOVCIsIm9yZ19vYnNlcnZlciI6dHJ1ZSwiaXNfYXBwX3Rva2VuIjpmYWxzZSwidXNlcl9pZCI6ImxkYUUyeGpBUHdycTcxT0EiLCJleHAiOjE3NDAyOTEzMDJ9.LneTGm-XbRG6NboqilpBRdV4R5m0jcTUFrArAFTWwpM"

token_line = f"--header 'authorization: Bearer {token}'"

domain = "localhost:8000"

counter_dict = {
    "config": [],
    "header_details": [],
    "element_list": [],
    "chart": [],
    "boq_element_list": [],
}


def get_curls(project_id: int):

    curl_commands = {
        "config": {
            0: f"curl --location '{domain}/api/v2/project/{project_id}/progress-report/config/' {token_line}",
            1: f"curl --location '{domain}/api/v3/project/{project_id}/progress-report/config/' {token_line}",
        },
        "header_details": {
            0: f"curl --location '{domain}/api/v2/project/{project_id}/progress-report/header-details/' {token_line}",
            1: f"curl --location '{domain}/api/v3/project/{project_id}/progress-report/header-details/' {token_line}",
        },
        "element_list": {
            0: f"curl --location '{domain}/api/v2/project/{project_id}/progress-report/element/list/?limit=10000&offset=0' {token_line}",
            1: f"curl --location '{domain}/api/v3/project/{project_id}/progress-report/element/list/?limit=10000&offset=0' {token_line}",
        },
        "chart": {
            0: f"curl --location '{domain}/api/v1/project/{project_id}/chart/work-progress/data/fetch/?end_date=2024-12-29&start_date=2023-12-01' {token_line}",
            1: f"curl --location '{domain}/api/v2/project/{project_id}/chart/work-progress/data/fetch/?end_date=2024-12-29&start_date=2023-12-01' {token_line}",
        },
        "boq_element_list": {
            0: f"curl --location '{domain}/api/v2/project/{project_id}/boq/elements/?current_version=true' {token_line}",
            1: f"curl --location '{domain}/api/v3/project/{project_id}/boq/elements/?current_version=true' {token_line}",
        },
    }

    return curl_commands


def compare_json_objects(json1, json2):
    """
    Compares two JSON objects for equality.

    Args:
        json1: First JSON object (dict).
        json2: Second JSON object (dict).

    Returns:
        A tuple (is_equal, differences), where:
        - is_equal is True if the JSON objects are equal, otherwise False.
        - differences is a list of differences between the objects.
    """

    differences = []

    def compare_recursive(obj1, obj2, path="root"):
        if isinstance(obj1, dict) and isinstance(obj2, dict):
            keys1 = set(obj1.keys())
            keys2 = set(obj2.keys())
            for key in keys1.union(keys2):
                if key not in obj1:
                    differences.append(f"{path}.{key} missing in first JSON")
                elif key not in obj2:
                    differences.append(f"{path}.{key} missing in second JSON")
                else:
                    compare_recursive(obj1[key], obj2[key], f"{path}.{key}")
        elif isinstance(obj1, list) and isinstance(obj2, list):
            if len(obj1) != len(obj2):
                differences.append(f"{path}: List lengths differ ({len(obj1)} != {len(obj2)})")
            else:
                for i, (item1, item2) in enumerate(zip(obj1, obj2)):
                    compare_recursive(item1, item2, f"{path}[{i}]")
        else:
            if obj1 != obj2:
                differences.append(f"{path}: {obj1} != {obj2}")

    compare_recursive(json1, json2)
    return len(differences) == 0, differences


def load_test(name, command, project_id):
    # print(f"Starting load test for {name}...")

    # List to store the time taken for each request
    request_response = []
    request_times = []

    def send_request(curl_command):
        """Execute the curl command and measure the time taken."""
        start_time = time.time()
        try:
            result = subprocess.run(curl_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            end_time = time.time()
            request_time = end_time - start_time
            request_response.append(json.loads(result.stdout.decode("utf-8").strip()))
            request_times.append(request_time)

        except Exception as e:
            print(f"Error: {e}")

    # no. of api requests to be compared
    for i in range(2):
        send_request(command.get(i))

    is_equal, diff = compare_json_objects(request_response[0], request_response[1])
    if is_equal:
        print(f"{name} -> old req/s: {request_times[0]} , new req/s: {request_times[1]} EQUAL JSON")
    else:
        counter_dict[name].append(project_id)
        print("The JSON objects are not equal.")
        print("Differences:")
        for d in diff:
            print(f"- {d}")


curl_project_list = f"curl --location '{domain}/api/v1/project/project-list/?limit=30&offset=10' {token_line}"


def get_project_ids():
    try:
        # Use subprocess to run the curl command
        response = subprocess.run(curl_project_list, shell=True, check=True, text=True, capture_output=True)
        response = json.loads(response.stdout)
        results = response.get("data", {}).get("results", [])

        # parse project-ids from the response
        ids = [item.get("id") for item in results]
        return ids
    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e.stderr}")
        return []


if __name__ == "__main__":
    project_ids = get_project_ids()
    for project_id in project_ids:
        print(project_id)
        curl_commands = get_curls(project_id=project_id)
        # load_test("config", command=curl_commands.get("config"), project_id=project_id)
        # load_test("header_details", command=curl_commands.get("header_details"), project_id=project_id)
        # load_test("element_list", command=curl_commands.get("element_list"), project_id=project_id)
        # load_test("chart", command=curl_commands.get("chart"), project_id=project_id)
        load_test("boq_element_list", command=curl_commands.get("boq_element_list"), project_id=project_id)
        print("------------------tested-------------------------\n\n")

    print("Performance testing completed.")
    print(counter_dict)
