from project.domain.caches import ItemTypeConfigCache
from work_progress_v2.data.models import WorkProgressElement
from boq.data.models import BoqElement
from django.core.paginator import Paginator
from django.db import transaction
from datetime import datetime
from core.entities import OrgIdProjectIdEntity


def update_work_progress_elements_update_method():
    """
    This function updates the work progress elements that have a progress_updated_at date
    greater than or equal to May 7, 2025. It checks if the update method of the work progress
    element is allowed based on the item type configuration. If not allowed, it sets the
    update method to None.
    """
    global_item_type_config_cache = {}
    default_global_item_type_config_cache = {}
    boq_elements = (
        BoqElement.objects.filter(
            work_progress_element__progress_updated_at__gte=datetime(2025, 5, 7),
            work_progress_element__update_method__isnull=False,  # noqa
        )
        .select_related("boq", "boq__project")
        .order_by("id")
    )
    paginator = Paginator(boq_elements, 10000)
    for page_number in paginator.page_range:
        with transaction.atomic():
            work_progress_elements_for_updation = []
            boq_elements_page = paginator.page(page_number).object_list
            for boq_element in boq_elements_page:
                org_id = boq_element.boq.project.organization_id
                project_id = boq_element.boq.project_id
                key = f"item_type_config_v1_{org_id}_{project_id}"
                # print(f"Processing page {page_number}, BOQ Element ID: {boq_element.id}, Key: {key}")
                if key not in global_item_type_config_cache:
                    cache = ItemTypeConfigCache.get(key=OrgIdProjectIdEntity(org_id=org_id, project_id=project_id))
                    global_item_type_config_cache[key] = {
                        id: set(config_data_entity.allowed_update_methods)
                        for id, config_data_entity in cache.item_types.items()
                    }
                if key not in default_global_item_type_config_cache:
                    default_global_item_type_config_cache[key] = {
                        "allowed_update_methods": set(cache.default_config.allowed_update_methods),
                        "default_update_method": cache.default_config.default_update_method,
                    }
                item_type_id_to_update_methods_mapping = global_item_type_config_cache[key]
                if boq_element.item_type_id:
                    assert (
                        boq_element.item_type_id in item_type_id_to_update_methods_mapping
                    ), "Item type ID not found in item type config cache."
                    if (
                        boq_element.work_progress_element.update_method
                        not in item_type_id_to_update_methods_mapping[boq_element.item_type_id]
                    ):  # noqa
                        print(
                            f"Boq Element Current Update Method: {boq_element.work_progress_element.update_method}, Update Methods: {item_type_id_to_update_methods_mapping[boq_element.item_type_id]}"
                        )  # noqa
                        work_progress_elements_for_updation.append(
                            WorkProgressElement(boq_element_id=boq_element.id, update_method=None)
                        )
                elif boq_element.item_type_id is None:
                    # If item_type_id is not set, we use the default update method
                    if (
                        boq_element.work_progress_element.update_method
                        not in default_global_item_type_config_cache[key]["allowed_update_methods"]
                    ):
                        print(
                            f"{boq_element.id}, Update Method: {boq_element.work_progress_element.update_method}, Allowed update methods {default_global_item_type_config_cache[key]['allowed_update_methods']}"
                        )  # noqa
                    #     work_progress_elements_for_updation.append(
                    #         WorkProgressElement(
                    #             id=boq_element.work_progress_element.id,
                    #             update_method=default_global_item_type_config_cache[key]["default_update_method"]
                    #         )
                    #     )
            WorkProgressElement.objects.bulk_update(
                work_progress_elements_for_updation, ["update_method"], batch_size=10000
            )
    return work_progress_elements_for_updation
