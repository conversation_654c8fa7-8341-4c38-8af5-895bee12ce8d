from datetime import date

from django.db import transaction

from common.constants import SYSTEM_USER_ID
from project.data.models import ProjectOrgData, ProjectStatusHistory
from project.domain.status_color_code import Module, RDStatus

ORG_ID = 15108  # zepto org


def project_status_history_create(project_id: int, module: int, status: int, user_id: int, organization_id: int):
    status_history = ProjectStatusHistory()
    status_history.project_id = project_id
    status_history.status = status
    status_history.module = module
    status_history.created_by_id = user_id
    status_history.organization_id = organization_id

    status_history.clean()

    return status_history


@transaction.atomic
def archive_projects(org_id: int, is_archived: bool = True):
    org_projects_data = ProjectOrgData.objects.filter(
        organization_id=org_id, project__created_at__lt=date(2025, 6, 1)
    ).select_related("project")

    projects = []
    project_histories = []

    for project_data in org_projects_data:
        if project_data.is_archived == is_archived:
            continue

        status = RDStatus.PROJECT_ARCHIVED.value if is_archived else RDStatus.PROJECT_UNARCHIVED.value
        project_data.is_archived = is_archived

        project_histories.append(
            project_status_history_create(
                project_id=project_data.project_id,
                status=status,
                user_id=SYSTEM_USER_ID,
                organization_id=org_id,
                module=Module.PROJECT.value,
            )
        )
        projects.append(project_data)

    ProjectOrgData.objects.bulk_update(projects, ["is_archived"], batch_size=1000)
    ProjectStatusHistory.objects.bulk_create(project_histories, batch_size=1000)


def main():
    archive_projects(org_id=ORG_ID, is_archived=True)
