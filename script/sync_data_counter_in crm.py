from common.constants import CustomFieldTypeEnum
from crm.data.models import Board, BoardLeadField, BoardLeadDropDownFieldData
from django.db import transaction
from django.db.models import (
    Prefetch,
)
@transaction.atomic
def sync_data_counter_in_crm_fields():
    boards = Board.objects.all().prefetch_related("fields")
    board_lead_field_objs = []
    final_output_res = []
    final_output_available_leads_counter = []
    for board in boards:
        board_lead_fields = BoardLeadField.objects.filter(board_id=board.id).prefetch_related(
            "custom_text_data",
            "custom_email_data",
            "custom_number_data",
            "custom_date_data",
            "custom_boolean_data",
            "custom_rich_text_data",
            "custom_phone_number_data",
            "custom_file_data",
            Prefetch("custom_drop_down_data", queryset=BoardLeadDropDownFieldData.objects.all().select_related("field")),
        ).all()
        for board_lead_field in board_lead_fields:
            if board_lead_field.type in (CustomFieldTypeEnum.BOOLEAN.value, CustomFieldTypeEnum.DATE.value, CustomFieldTypeEnum.DECIMAL.value, CustomFieldTypeEnum.DROPDOWN.value, CustomFieldTypeEnum.EMAIL.value, CustomFieldTypeEnum.FILE.value, CustomFieldTypeEnum.PHONE_NUMBER.value, CustomFieldTypeEnum.RICH_TEXT.value, CustomFieldTypeEnum.TEXT.value, CustomFieldTypeEnum.MULTI_DROPDOWN.value):
                text_data = board_lead_field.custom_text_data.all()
                multi_dropdown_data = board_lead_field.custom_drop_down_data.all()
                drop_down_data = board_lead_field.custom_drop_down_data.all()
                email_data = board_lead_field.custom_email_data.all()
                number_data = board_lead_field.custom_number_data.all()
                date_data = board_lead_field.custom_date_data.all()
                boolean_data = board_lead_field.custom_boolean_data.all()
                rich_text_data = board_lead_field.custom_rich_text_data.all()
                phone_number_data = board_lead_field.custom_phone_number_data.all()
                file_data = board_lead_field.custom_file_data.all()
                res = {board_lead_field.id: 0} # Final data counter for a particular field
                seen = set()
                available_leads_counter = {board_lead_field.id: 0}
                deleted_field = {board_lead_field.id: 0}
                """
                We are keeping 3 maps here
                1) res: This map will keep the count of data counter for a board_lead_field (which should be there)
                2) available_leads_counter: This map -> Board_lead_field_data [Boolean, Decimal, Dropdown etc...] is used in how many leads
                    (The field is actively used in how many Fields)
                3) deleted_field: This map will keep the count such that a particular Board_lead_field_data [Boolean, Decimal, Dropdown etc...] was part of deleted leads
                """
                print(f"Existing data counter for {board_lead_field.type} {board_lead_field.id} field is {board_lead_field.data_counter}")
                for text in text_data:
                    if text.lead.deleted_at:
                        deleted_field[text.field.id] += 1
                    if text.data and text.lead.deleted_at is None:
                        available_leads_counter[text.field.id] += 1
                        res[text.field.id] += 1
                for multi_dropdown in multi_dropdown_data:
                    if multi_dropdown.lead.deleted_at and multi_dropdown.field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value:
                        deleted_field[multi_dropdown.field.id] += 1
                    if multi_dropdown.field.id not in seen and multi_dropdown.field.type == CustomFieldTypeEnum.MULTI_DROPDOWN.value and multi_dropdown.data and multi_dropdown.lead.deleted_at is None:
                        available_leads_counter[multi_dropdown.field.id] += 1
                        res[multi_dropdown.field.id] += 1
                        seen.add(multi_dropdown.field.id)
                for drop_down in drop_down_data:
                    if drop_down.lead.deleted_at and drop_down.field.type == CustomFieldTypeEnum.DROPDOWN.value:
                        deleted_field[drop_down.field.id] += 1
                    if drop_down.field.type == CustomFieldTypeEnum.DROPDOWN.value and drop_down.data and drop_down.lead.deleted_at is None:
                        available_leads_counter[drop_down.field.id] += 1
                        res[drop_down.field.id] += 1
                for email in email_data:
                    if email.lead.deleted_at:
                        deleted_field[email.field.id] += 1
                    if email.data and email.lead.deleted_at is None:
                        available_leads_counter[email.field.id] += 1
                        res[email.field.id] += 1
                for number in number_data:
                    if number.lead.deleted_at:
                        deleted_field[number.field.id] += 1
                    if number.data and number.lead.deleted_at is None:
                        available_leads_counter[number.field.id] += 1
                        res[number.field.id] += 1
                for date in date_data:
                    if date.lead.deleted_at:
                        deleted_field[date.field.id] += 1
                    if date.data and date.lead.deleted_at is None:
                        available_leads_counter[date.field.id] += 1
                        res[date.field.id] += 1
                for boolean in boolean_data:
                    if boolean.lead.deleted_at:
                        deleted_field[boolean.field.id] += 1
                    if boolean.data is not None and boolean.lead.deleted_at is None:
                        available_leads_counter[boolean.field.id] += 1
                        res[boolean.field.id] += 1
                for rich_text in rich_text_data:
                    if rich_text.lead.deleted_at:
                        deleted_field[rich_text.field.id] += 1
                    if rich_text.data and rich_text.lead.deleted_at is None:
                        available_leads_counter[rich_text.field.id] += 1
                        res[rich_text.field.id] += 1
                for phone_number in phone_number_data:
                    if phone_number.lead.deleted_at:
                        deleted_field[phone_number.field.id] += 1
                    if phone_number.data and phone_number.lead.deleted_at is None:
                        available_leads_counter[phone_number.field.id] += 1
                        res[phone_number.field.id] += 1
                for file in file_data:
                    if file.lead.deleted_at:
                        deleted_field[file.field.id] += 1
                    if file.data and file.lead.deleted_at is None:
                        available_leads_counter[file.field.id] += 1
                        res[file.field.id] += 1
                print(f"Deleted data counter for {board_lead_field.type} {board_lead_field.id} field is {deleted_field.items()}")
                print(f"Available leads counter for {board_lead_field.type} {board_lead_field.id} field is (The field is actively used in how many Fields) {available_leads_counter.items()} {res.items()}")
                print("\n")
                final_output_res.append(res.items())
                final_output_available_leads_counter.append(available_leads_counter.items())
                for key, value in res.items():
                    board_lead_field_objs.append(BoardLeadField(id=key, data_counter=value))
    print("Data Counter {} for {} field".format(len(board_lead_field_objs), "fields"))
    # print(board_lead_field_objs)
    BoardLeadField.objects.bulk_update(board_lead_field_objs, ["data_counter"], batch_size=500)
    return (final_output_res, final_output_available_leads_counter)