from django.db import transaction
from report.subscription.models import ReportSubscription


@transaction.atomic
def main():
    report_subscriptions = ReportSubscription.objects.all().order_by("id")

    count = report_subscriptions.count()

    print(f"Starting backfill for {count} report subscriptions...")

    for subscription in report_subscriptions:
        if (
            subscription.action == "notify_progress_report_creation"
            or subscription.action == "notify_work_progress_export_report_pdf_generated"
        ):
            subscription.action = "notify_work_progress_report_pdf_generated"

        if subscription.action == "notify_client_for_progress_report_creation":
            subscription.action = "notify_client_for_work_progress_report_pdf_generated"

        if subscription.event == "progress_report_created":
            subscription.event = "work_progress_report_pdf_generated"

        subscription.save()

    print("Backfill completed successfully.")
