import uuid

from core.models import Organization
from project.data.models import Project


def assign_org_storage_uuid():
    organizations = Organization.objects.filter().order_by("id")
    print(organizations.count(), "total orgs")

    for org in organizations:
        org.storage_uuid = uuid.uuid4()

    Organization.objects.bulk_update(organizations, ["storage_uuid"])
    print("finished")

def assign_project_storage_uuid():
    projects = Project.objects.filter().order_by("id")
    print(projects.count(), "total projects")

    for project in projects:
        project.storage_uuid = uuid.uuid4()

    Project.objects.bulk_update(projects, ["storage_uuid"])
    print("finished")



