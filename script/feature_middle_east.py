import pytz
from django.db import transaction

from common.constants import SYSTEM_USER_ID, CustomFieldTypeChoices
from core.choices import OrganizationDocumentChoices
from core.models import (
    City,
    Country,
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    Currency,
    FromToOrgMapping,
    Organization,
    OrganizationAddress,
    OrganizationConfig,
    OrganizationDocument,
    OrganizationGSTNumber,
    TaxSlab,
    TaxType,
    Timezone,
)
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationDocument,
    LinkedOrganizationDocumentFileFieldFieldData,
    LinkedOrganizationDocumentStateFieldData,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationDocumentConfig,
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationDocumentFileFieldFieldData,
    OrganizationDocumentStateFieldData,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
    OrganizationSectionConfig,
)
from crm.board.domain.constants import BASIC_DETAIL_SECTION_UUID as LEAD_BASIC_DETAIL_SECTION_UUID
from crm.board.domain.constants import (
    LEAD_ADDRESS_LINE_1_UUID,
    LEAD_ADDRESS_LINE_2_UUID,
    LEAD_CITY_UUID,
    LEAD_COUNTRY_UUID,
    LEAD_STATE_UUID,
    LEAD_ZIPCODE_UUID,
)
from crm.data.models import Board, BoardLeadField, BoardLeadSection, Company, LeadProjectData
from microcontext.choices import MicroContextChoices
from order.data.models import OrderSnapshot, OrderTextFieldData, SnapshotTextFieldData, VendorOrder, VendorOrderGSTDetail

from project.domain.constants import (
    ASSIGNED_PROJECT_USER_SECTION_UUID,
    BASIC_DETAIL_SECTION_UUID,
    CITY_UUID,
    CLIENT_POC_DETAILS_SECTION_UUID,
    COUNTRY_UUID,
    IMPORTANT_DATES_SECTION_UUID,
    PROJECT_ADDRESS_LINE_1_UUID,
    PROJECT_ADDRESS_LINE_2_UUID,
    PROJECT_CURRENCY_UUID,
    PROJECT_ESTIMATE_UUID,
    PROJECT_SCOPE_SECTION_UUID,
    PROJECT_SETTINGS_SECTION_UUID,
    PROJECT_TAX_TYPE_UUID,
    PROJECT_TIMEZONE_UUID,
    STATE_UUID,
    ZIPCODE_UUID,
    ProjectSectionTypeEnum,
)
from project.data.models import Project, ProjectConfig, ProjectCustomField, ProjectCustomSection, Store
from proposal.data.models import Proposal, ProposalTextFieldData
from vendorv2.data.models import VendorBankDetail

INDIA = 1


class DocumentNames:
    PAN = "PAN"
    AADHAR = "Aadhar"
    MSME = "MSME"
    GST = "GST"
    PRIMARY_BANK_ACCOUNT = "Primary Bank Account"
    OTHER_BANK_ACCOUNTS = "Other Bank Accounts"


class FieldNames:
    PAN_NUMBER = "PAN Number"
    PAN_CERTIFICATE = "PAN Certificate"
    AADHAAR_NUMBER = "Aadhaar Number"
    AADHAAR_FRONT = "Aadhaar (Front)"
    AADHAAR_REAR = "Aadhaar (Rear)"
    MSME_NUMBER = "MSME ID"
    MSME_CERTIFICATE = "MSME Certificate"
    GST_NUMBER = "GST Number"
    GST_CERTIFICATE = "GST Certificate"
    GST_STATE = "GST State"
    ACCOUNT_HOLDER_NAME = "Account Holder Name"
    BANK_NAME = "Bank Name"
    ACCOUNT_NUMBER = "Account Number"
    IFSC_CODE = "IFSC Code"
    CANCELLED_CHEQUE = "Cancelled Cheque"


# Config Scripts


@transaction.atomic
def create_config():
    country = Country.objects.get(name="India", is_active=True)
    timezone, _ = Timezone.objects.get_or_create(tz=pytz.timezone("Asia/Kolkata"), locale="en-IN")
    currency, _ = Currency.objects.get_or_create(symbol="₹", code="INR", name="Indian National Rupee", locale="en-IN")
    tax_type, _ = TaxType.objects.get_or_create(name="GST")
    TaxSlab.objects.bulk_create(
        [
            TaxSlab(tax_type=tax_type, tax_percent=0, is_active=True),
            TaxSlab(tax_type=tax_type, tax_percent=5, is_active=True),
            TaxSlab(tax_type=tax_type, tax_percent=12, is_active=True),
            TaxSlab(tax_type=tax_type, tax_percent=18, is_active=True),
            TaxSlab(tax_type=tax_type, tax_percent=28, is_active=True),
        ]
    )
    create_mapping(country_id=country.pk, currency_id=currency.pk, timezone_id=timezone.pk, tax_type_id=tax_type.pk)
    create_project_config(currency_id=currency.pk, timezone_id=timezone.pk, tax_type_id=tax_type.pk)


def create_mapping(country_id: int, currency_id, timezone_id: int, tax_type_id: int):
    country_timezone_mapping, _ = CountryTimezoneMapping.objects.get_or_create(
        timezone_id=timezone_id, country_id=country_id, is_default=True
    )
    country_tax_mapping, _ = CountryTaxMapping.objects.get_or_create(
        tax_type_id=tax_type_id, country_id=country_id, max_slab_percent=28, is_default=True
    )
    country_currency_mapping, _ = CountryCurrencyMapping.objects.get_or_create(
        currency_id=currency_id, country_id=country_id, is_default=True
    )
    print("Country mappings created successfully.")


def create_project_config(currency_id, timezone_id: int, tax_type_id: int):
    OrganizationConfig.objects.all().update(timezone_id=timezone_id, currency_id=currency_id, tax_type_id=tax_type_id)
    project_ids = Project.objects.available().values_list("pk", flat=True)
    project_config_objs = []
    for project_id in project_ids:
        project_config_objs.append(
            ProjectConfig(
                project_id=project_id, timezone_id=timezone_id, currency_id=currency_id, tax_type_id=tax_type_id
            )
        )
    ProjectConfig.objects.bulk_create(project_config_objs, ignore_conflicts=True)
    print(len(project_config_objs))
    print("Project Configs created successfully.")


# Organization Related Scripts
# Address scripts
@transaction.atomic
def fill_country_in_city():
    City.objects.all().update(country_id=1)
    print("Country Updated in City")


@transaction.atomic
def fill_organization_address():
    address_list: list[OrganizationAddress] = OrganizationAddress.objects.all()
    city_dict = {city.lower(): city for city in City.objects.all().values_list("name", flat=True)}
    address_city_need_to_create = [
        address for address in address_list if address.city_data and address.city_data.lower() not in city_dict
    ]
    print("Address City Need to Create count", len(address_city_need_to_create))
    print("Cities list:", address_city_need_to_create)
    city_create = []
    for address in address_city_need_to_create:
        print(f"city: {address.city_data}")
        city = City(
            name=address.city_data,
            country_id=INDIA,
            state_id=address.state_data if address.state_data else None,
            is_active=True,
            is_verified=False,
        )
        city_create.append(city)
    City.objects.bulk_create(city_create, batch_size=50, ignore_conflicts=True)
    print("New City created")
    city_mapping = {city.name.lower(): city for city in City.objects.all().select_related("state", "state__country")}
    print("City Mapping ", city_mapping)
    updated_address = []
    for address in address_list:
        if address.city_data and address.city_data.lower() in city_mapping:
            city = city_mapping[address.city_data.lower()]
            address.city_id = city.id
            address.state_id = city.state_id
            address.country_id = city.state.country_id if city.state else None
        if not address.state_id and address.state_data:
            address.state_id = address.state_data if address.state_data else None
        if not address.country_id:
            address.country_id = INDIA
        address.zip_code = address.pincode
        if address.address and address.address_line_one:
            address_line_1 = address.address + " " + address.address_line_one
        elif address.address:
            address_line_1 = address.address
        else:
            address_line_1 = address.address_line_one
        address.address_line_1 = address_line_1
        address.address_line_2 = address.address_line_two
        updated_address.append(address)
    OrganizationAddress.objects.bulk_update(
        updated_address,
        ["state_id", "city_id", "country_id", "zip_code", "address_line_1", "address_line_2"],
        batch_size=50,
    )
    print("Organization Address Updated")


@transaction.atomic
def fill_store_address():
    stores = Store.objects.all()
    city_list = [name.lower() for name in City.objects.all().values_list("name", flat=True)]
    print("store fetched", city_list)
    stores_city_need_to_create = [
        store for store in stores if store.city_data and store.city_data.lower() not in city_list
    ]

    print("Store City Need to Create", len(stores_city_need_to_create))
    city_create = []
    for store in stores_city_need_to_create:
        print(f"city: {store.city_data}, state: {store.state}")
        city = City(name=store.city_data, country_id=INDIA, state_id=store.state, is_active=True, is_verified=False)
        city_create.append(city)
    City.objects.bulk_create(city_create, batch_size=50, ignore_conflicts=True)

    city_mapping = {city.name.lower(): city for city in City.objects.all().select_related("state", "state__country")}
    updated_stores = []

    for store in stores:
        if store.city_data and store.city_data.lower() in city_mapping:
            city = city_mapping[store.city_data.lower()]
            store.city_id = city.id
            store.state_id = city.state_id
            store.country_id = city.state.country_id if city.state else None
        if not store.state_id and store.state:
            store.state_id = store.state
        if store.address:
            store.address_line_1 = store.address
        updated_stores.append(store)
    Store.objects.bulk_update(updated_stores, ["state_id", "city_id", "country_id", "address_line_1"], batch_size=50)
    print("Store Address Updated")


@transaction.atomic
def fill_company_address():
    companies = Company.objects.all()
    for company in companies:
        if company.address_data:
            company.address_line_1 = company.address_data
            company.zip_code = company.pincode_data
    Company.objects.bulk_update(companies, ["address_line_1", "zip_code"], batch_size=50)
    print("Company Address Updated")


@transaction.atomic
def fill_lead_project_data():
    lead_project_data = LeadProjectData.objects.all().select_related("lead__project__store")
    for data in lead_project_data:
        data.address_line_1 = data.address_data
        data.country_id = INDIA
    LeadProjectData.objects.bulk_update(lead_project_data, ["address_line_1", "country_id"], batch_size=50)
    print("Lead Project Data Updated")


# Organization Config Script


@transaction.atomic
def create_organization_data_config():
    # Fetch country
    country, _ = Country.objects.get_or_create(name="India")
    print("Country Created")

    # Process Organization Section config
    sections_config = [
        OrganizationSectionConfig(
            id=1, country=country, name="KYC Details", type=OrganizationSectionTypeChoices.KYC_DETAILS, position=0
        ),
        OrganizationSectionConfig(
            id=2, country=country, name="Bank Details", type=OrganizationSectionTypeChoices.BANK_DETAILS, position=1
        ),
    ]
    sections_config = OrganizationSectionConfig.objects.bulk_create(sections_config, batch_size=50)
    print("Organization Section Config Created")

    # Process Document Config
    documents_config = [
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[0],
            name=DocumentNames.PAN,
            multiple_allowed=False,
            position=0,
            is_required=True,
            is_visible_on_app=True,
            id=1,
        ),
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[0],
            name=DocumentNames.AADHAR,
            multiple_allowed=False,
            position=1,
            is_required=False,
            id=2,
        ),
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[0],
            name=DocumentNames.MSME,
            multiple_allowed=False,
            position=2,
            is_required=False,
            id=3,
        ),
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[0],
            name=DocumentNames.GST,
            multiple_allowed=True,
            position=3,
            is_required=False,
            is_visible_on_app=True,
            id=4,
        ),
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[1],
            name=DocumentNames.PRIMARY_BANK_ACCOUNT,
            multiple_allowed=False,
            position=0,
            is_required=False,
            is_visible_on_app=True,
            id=5,
        ),
        OrganizationDocumentConfig(
            country=country,
            section=sections_config[1],
            name=DocumentNames.OTHER_BANK_ACCOUNTS,
            multiple_allowed=True,
            position=1,
            is_required=False,
            id=6,
        ),
    ]
    documents_config = OrganizationDocumentConfig.objects.bulk_create(documents_config, batch_size=50)
    print("Organization Document Config Created")

    # Process Document Field Config
    fields_config = [
        OrganizationDocumentFieldConfig(
            document_config=documents_config[0],
            name=FieldNames.PAN_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=True,
            regex="^[A-Z]{5}[0-9]{4}[A-Z]{1}$",
            is_visible_on_app=True,
            is_capitalized=True,
            id=1,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[0],
            name=FieldNames.PAN_CERTIFICATE,
            type=CustomFieldTypeChoices.FILE,
            position=1,
            is_required=False,
            is_visible_on_app=True,
            id=2,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[1],
            name=FieldNames.AADHAAR_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            regex="^[0-9]{12}$",
            id=3,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[1],
            name=FieldNames.AADHAAR_FRONT,
            type=CustomFieldTypeChoices.FILE,
            position=1,
            is_required=False,
            id=4,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[1],
            name=FieldNames.AADHAAR_REAR,
            type=CustomFieldTypeChoices.FILE,
            position=2,
            is_required=False,
            id=5,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[2],
            name=FieldNames.MSME_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            id=6,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[2],
            name=FieldNames.MSME_CERTIFICATE,
            type=CustomFieldTypeChoices.FILE,
            position=1,
            is_required=False,
            id=7,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[3],
            name=FieldNames.GST_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            is_visible_on_app=True,
            regex="^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$",
            id=8,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[3],
            name=FieldNames.GST_CERTIFICATE,
            type=CustomFieldTypeChoices.FILE,
            position=1,
            is_required=False,
            id=9,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[3],
            name=FieldNames.GST_STATE,
            type=CustomFieldTypeChoices.STATE,
            position=2,
            is_required=False,
            id=10,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[4],
            name=FieldNames.ACCOUNT_HOLDER_NAME,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            regex="^[a-zA-Z\s'-]+$",
            is_required=False,
            is_visible_on_app=True,
            id=11,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[4],
            name=FieldNames.BANK_NAME,
            type=CustomFieldTypeChoices.TEXT,
            position=1,
            is_required=False,
            is_visible_on_app=True,
            id=12,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[4],
            name=FieldNames.ACCOUNT_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=2,
            is_required=False,
            is_visible_on_app=True,
            id=13,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[4],
            name=FieldNames.IFSC_CODE,
            type=CustomFieldTypeChoices.TEXT,
            position=3,
            is_required=False,
            regex="^[A-Z]{4}0[A-Z0-9]{6}$",
            is_visible_on_app=True,
            is_capitalized=True,
            id=14,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[4],
            name=FieldNames.CANCELLED_CHEQUE,
            type=CustomFieldTypeChoices.FILE,
            position=4,
            is_required=False,
            is_visible_on_app=True,
            id=15,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[5],
            name=FieldNames.ACCOUNT_HOLDER_NAME,
            type=CustomFieldTypeChoices.TEXT,
            position=0,
            is_required=False,
            regex="^[a-zA-Z\s'-]+$",
            id=16,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[5],
            name=FieldNames.BANK_NAME,
            type=CustomFieldTypeChoices.TEXT,
            position=1,
            is_required=False,
            id=17,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[5],
            name=FieldNames.ACCOUNT_NUMBER,
            type=CustomFieldTypeChoices.TEXT,
            position=2,
            is_required=False,
            regex="^[0-9]{9,18}$",
            id=18,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[5],
            name=FieldNames.IFSC_CODE,
            type=CustomFieldTypeChoices.TEXT,
            position=3,
            is_required=False,
            regex="^[A-Z]{4}0[A-Z0-9]{6}$",
            id=19,
        ),
        OrganizationDocumentFieldConfig(
            document_config=documents_config[5],
            name=FieldNames.CANCELLED_CHEQUE,
            type=CustomFieldTypeChoices.FILE,
            position=4,
            is_required=False,
            id=20,
        ),
    ]

    fields_configs = OrganizationDocumentFieldConfig.objects.bulk_create(fields_config, batch_size=50)
    pan_field = fields_configs[0]
    Country.objects.filter(id=INDIA).update(uid_field_id=pan_field.pk, uid_document_id=pan_field.document_config.pk)
    print("Organization Document Field Config Created")


@transaction.atomic
def migrate_org_data(org_id: int):
    print(f"Migrating organization data, org_id: {org_id}")
    organization = Organization.objects.filter(id=org_id).select_related("vendor").first()
    sections_config_ids = OrganizationSectionConfig.objects.filter(
        country_id=organization.country_id,
    ).values_list("id", flat=True)
    documents_config = OrganizationDocumentConfig.objects.filter(
        country_id=organization.country_id, section_id__in=sections_config_ids
    )
    documents_config_mapping = {doc_config.name: doc_config for doc_config in documents_config}
    field_config = OrganizationDocumentFieldConfig.objects.all()
    fields_config_mapping = {(field.document_config.pk, field.name): field for field in field_config}

    org_old_documents: list[OrganizationDocument] = (
        OrganizationDocument.objects.filter(organization_id=org_id)
        .select_related("organization", "organization__vendor")
        .available()
    )
    org_old_gst_numbers: list[OrganizationGSTNumber] = OrganizationGSTNumber.objects.filter(organization_id=org_id)
    all_bank_details = VendorBankDetail.objects.filter(vendor_id=org_id)
    org_bank_details = [bank_detail for bank_detail in all_bank_details if bank_detail.client_id is None]
    print(
        f"Org old documents fetched: {len(org_old_documents)}, gst_numbers: {len(org_old_gst_numbers)}, bank details: {len(org_bank_details)}"  # noqa
    )
    org_old_pan_docs = [old_doc for old_doc in org_old_documents if old_doc.type == OrganizationDocumentChoices.PAN]
    org_old_aadhar_docs = [
        old_doc for old_doc in org_old_documents if old_doc.type == OrganizationDocumentChoices.AADHAR
    ]
    org_old_msme_docs = [
        old_doc for old_doc in org_old_documents if old_doc.type == OrganizationDocumentChoices.MSME_DOC
    ]

    org_old_primary_bank_details = org_bank_details[:1]
    org_old_other_bank_details = org_bank_details[1:]
    org_documents = []
    if organization.pan_number and len(org_old_pan_docs) == 0:
        document_config = documents_config_mapping.get(DocumentNames.PAN)
        org_documents.append(
            OrganizationDocumentV2(
                document_config=document_config,
                organization_id=organization.pk,
                created_by_id=SYSTEM_USER_ID,
            )
        )
    for old_document in org_old_documents:
        if old_document.type == OrganizationDocumentChoices.PAN:
            document_config = documents_config_mapping.get(DocumentNames.PAN)
        elif old_document.type == OrganizationDocumentChoices.AADHAR:
            document_config = documents_config_mapping.get(DocumentNames.AADHAR)
        elif old_document.type == OrganizationDocumentChoices.MSME_DOC:
            document_config = documents_config_mapping.get(DocumentNames.MSME)
        else:
            continue
        org_documents.append(
            OrganizationDocumentV2(
                document_config=document_config,
                organization_id=organization.pk,
                created_by_id=old_document.uploaded_by_id,
            )
        )
    document_config = documents_config_mapping.get(DocumentNames.GST)
    for gst_number in org_old_gst_numbers:
        org_documents.append(
            OrganizationDocumentV2(
                document_config=document_config,
                organization_id=organization.pk,
                created_by_id=gst_number.created_by_id,
            )
        )
    document_config = documents_config_mapping.get(DocumentNames.PRIMARY_BANK_ACCOUNT)
    for bank_detail in org_old_primary_bank_details:
        org_documents.append(
            OrganizationDocumentV2(
                document_config=document_config,
                organization_id=organization.pk,
                created_by_id=bank_detail.created_by_id,
            )
        )
    document_config = documents_config_mapping.get(DocumentNames.OTHER_BANK_ACCOUNTS)
    for bank_detail in org_old_other_bank_details:
        org_documents.append(
            OrganizationDocumentV2(
                document_config=document_config,
                organization_id=organization.pk,
                created_by_id=bank_detail.created_by_id,
            )
        )
    org_documents = OrganizationDocumentV2.objects.bulk_create(org_documents, batch_size=50)
    print(f"Documents to be created: {len(org_documents)}, Fields creation started")

    org_pan_docs_v2 = [doc for doc in org_documents if doc.document_config.name == DocumentNames.PAN]
    org_aadhar_docs_v2 = [doc for doc in org_documents if doc.document_config.name == DocumentNames.AADHAR]
    org_msme_docs_v2 = [doc for doc in org_documents if doc.document_config.name == DocumentNames.MSME]
    org_gst_docs_v2 = [doc for doc in org_documents if doc.document_config.name == DocumentNames.GST]
    org_primary_banks_v2 = [
        doc for doc in org_documents if doc.document_config.name == DocumentNames.PRIMARY_BANK_ACCOUNT
    ]
    org_other_bank_docs_v2 = [
        doc for doc in org_documents if doc.document_config.name == DocumentNames.OTHER_BANK_ACCOUNTS
    ]
    org_text_field_data = []
    org_file_field_data = []
    org_state_field_data = []

    if org_old_pan_docs:
        for old_doc in org_old_pan_docs:
            if org_pan_docs_v2:
                document = org_pan_docs_v2.pop()
                if old_doc.organization.pan_number:
                    field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
                    org_text_field_data.append(
                        OrganizationDocumentTextFieldData(
                            field_config_id=field_config.pk,
                            document_id=document.pk,
                            data=old_doc.organization.pan_number,
                            created_by_id=old_doc.uploaded_by_id,
                        )
                    )
                if old_doc.file:
                    field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_CERTIFICATE))
                    org_file_field_data.append(
                        OrganizationDocumentFileFieldFieldData(
                            field_config_id=field_config.pk,
                            document_id=document.pk,
                            file=old_doc.file,
                            file_name=old_doc.name,
                            created_by_id=old_doc.uploaded_by_id,
                        )
                    )
    else:
        if org_pan_docs_v2:
            document = org_pan_docs_v2.pop()
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=organization.pan_number,
                    created_by_id=SYSTEM_USER_ID,
                )
            )

    index = 0
    while index < len(org_old_aadhar_docs):
        old_doc = org_old_aadhar_docs[index]
        document = org_aadhar_docs_v2.pop()
        if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.aadhar_number:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=old_doc.organization.vendor.aadhar_number,
                    created_by_id=old_doc.uploaded_by_id,
                )
            )
        if old_doc.file:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_FRONT))
            org_file_field_data.append(
                OrganizationDocumentFileFieldFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    file=old_doc.file,
                    file_name=old_doc.name,
                    created_by_id=old_doc.uploaded_by_id,
                )
            )
        if index + 1 < len(org_old_aadhar_docs):
            old_doc = org_old_aadhar_docs[index + 1]
            if old_doc.file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_REAR))
                org_file_field_data.append(
                    OrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=old_doc.file,
                        file_name=old_doc.name,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
        index += 2

    for old_doc in org_old_msme_docs:
        document = org_msme_docs_v2.pop()
        if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.msme_id:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=old_doc.organization.vendor.msme_id,
                    created_by_id=old_doc.uploaded_by_id,
                )
            )
        if old_doc.file:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_CERTIFICATE))
            org_file_field_data.append(
                OrganizationDocumentFileFieldFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    file=old_doc.file,
                    file_name=old_doc.name,
                    created_by_id=old_doc.uploaded_by_id,
                )
            )
    for gst_number in org_old_gst_numbers:
        document = org_gst_docs_v2.pop()
        if gst_number.gst_number:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=gst_number.gst_number,
                    created_by_id=gst_number.created_by_id,
                )
            )
        if gst_number.name:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_CERTIFICATE))
            org_file_field_data.append(
                OrganizationDocumentFileFieldFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    file=gst_number.file,
                    file_name=gst_number.name,
                    created_by_id=gst_number.created_by_id,
                )
            )
        if gst_number.gst_state:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_STATE))
            org_state_field_data.append(
                OrganizationDocumentStateFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    state_id=gst_number.gst_state,
                    created_by_id=gst_number.created_by_id,
                )
            )
    for bank_detail in org_old_primary_bank_details:
        document = org_primary_banks_v2.pop()
        if bank_detail.account_holder_name:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_HOLDER_NAME))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.account_holder_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.bank_name:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.BANK_NAME))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.bank_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.account_number:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.account_number,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.ifsc_code:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.IFSC_CODE))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.ifsc_code,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.cheque_file:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.CANCELLED_CHEQUE))
            org_file_field_data.append(
                OrganizationDocumentFileFieldFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    file=bank_detail.cheque_file,
                    file_name=bank_detail.cheque_file_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )
    for bank_detail in org_old_other_bank_details:
        document = org_other_bank_docs_v2.pop()
        if bank_detail.account_holder_name:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_HOLDER_NAME))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.account_holder_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.bank_name:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.BANK_NAME))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.bank_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.account_number:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_NUMBER))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.account_number,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.ifsc_code:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.IFSC_CODE))
            org_text_field_data.append(
                OrganizationDocumentTextFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    data=bank_detail.ifsc_code,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        if bank_detail.cheque_file:
            field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.CANCELLED_CHEQUE))
            org_file_field_data.append(
                OrganizationDocumentFileFieldFieldData(
                    field_config_id=field_config.pk,
                    document_id=document.pk,
                    file=bank_detail.cheque_file,
                    file_name=bank_detail.cheque_file_name,
                    created_by_id=bank_detail.created_by_id,
                )
            )

    OrganizationDocumentTextFieldData.objects.bulk_create(org_text_field_data, batch_size=50)
    OrganizationDocumentFileFieldFieldData.objects.bulk_create(org_file_field_data, batch_size=50)
    OrganizationDocumentStateFieldData.objects.bulk_create(org_state_field_data, batch_size=50)
    print(f"fields data created successfully for org_id: {org_id}")

    # Migrating data for org linked as vendor
    client_org_ids = FromToOrgMapping.objects.filter(org_to_id=org_id, deleted_at__isnull=True).values_list(
        "org_from_id", flat=True
    )
    mappings = [
        LinkedOrganizations(
            client_id=client_org_id,
            vendor_id=org_id,
            org_id=client_org_id,
            created_by_id=SYSTEM_USER_ID,
        )
        for client_org_id in client_org_ids
    ]
    mappings = LinkedOrganizations.objects.bulk_create(mappings, batch_size=50)
    print(f"Mappings created for org_id: {org_id}")

    address_list: list[OrganizationAddress] = OrganizationAddress.objects.filter(
        organization_id=org_id,
    )
    linked_address_list = []
    for mapping in mappings:
        for address in address_list:
            linked_address_list.append(
                LinkedOrganizationAddress(
                    mapping=mapping,
                    header=address.header,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city_id=address.city_id,
                    state_id=address.state_id,
                    country_id=address.country_id,
                    zip_code=address.zip_code,
                    created_by_id=address.created_by_id,
                )
            )
    LinkedOrganizationAddress.objects.bulk_create(linked_address_list, batch_size=50)
    print(f"Linked Address data created successfully for org_id: {org_id}")

    for mapping in mappings:
        linked_org_docs = []
        if organization.pan_number and len(org_old_pan_docs) == 0:
            document_config = documents_config_mapping.get(DocumentNames.PAN)
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=SYSTEM_USER_ID,
                )
            )
        for old_document in org_old_documents:
            if old_document.type == OrganizationDocumentChoices.PAN:
                document_config = documents_config_mapping.get(DocumentNames.PAN)
            elif old_document.type == OrganizationDocumentChoices.AADHAR:
                document_config = documents_config_mapping.get(DocumentNames.AADHAR)
            elif old_document.type == OrganizationDocumentChoices.MSME_DOC:
                document_config = documents_config_mapping.get(DocumentNames.MSME)
            else:
                continue
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=old_document.uploaded_by_id,
                )
            )
        document_config = documents_config_mapping.get(DocumentNames.GST)
        for gst_number in org_old_gst_numbers:
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=gst_number.created_by_id,
                )
            )
        linked_bank_accounts = [
            bank_detail for bank_detail in all_bank_details if bank_detail.client_id == mapping.client_id
        ]
        linked_old_primary_bank_details = linked_bank_accounts[:1]
        linked_old_other_bank_details = linked_bank_accounts[1:]
        document_config = documents_config_mapping.get(DocumentNames.PRIMARY_BANK_ACCOUNT)
        for bank_detail in linked_old_primary_bank_details:
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        document_config = documents_config_mapping.get(DocumentNames.OTHER_BANK_ACCOUNTS)
        for bank_detail in linked_old_other_bank_details:
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=bank_detail.created_by_id,
                )
            )
        linked_org_docs = LinkedOrganizationDocument.objects.bulk_create(linked_org_docs, batch_size=50)
        print(f"Linked Doc created for mapping: {mapping}")

        linked_org_pan_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.PAN]
        linked_org_aadhar_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.AADHAR]
        linked_org_msme_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.MSME]
        linked_org_gst_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.GST]
        linked_org_primary_banks_v2 = [
            doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.PRIMARY_BANK_ACCOUNT
        ]
        linked_org_other_bank_docs_v2 = [
            doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.OTHER_BANK_ACCOUNTS
        ]
        linked_org_text_field_data = []
        linked_org_file_field_data = []
        linked_org_state_field_data = []

        if org_old_pan_docs:
            for old_doc in org_old_pan_docs:
                if linked_org_pan_docs_v2:
                    document = linked_org_pan_docs_v2.pop()
                    if old_doc.organization.pan_number:
                        field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
                        linked_org_text_field_data.append(
                            LinkedOrganizationDocumentTextFieldData(
                                field_config_id=field_config.pk,
                                document_id=document.pk,
                                data=old_doc.organization.pan_number,
                                created_by_id=old_doc.uploaded_by_id,
                            )
                        )
                    if old_doc.file:
                        field_config = fields_config_mapping.get(
                            (document.document_config.pk, FieldNames.PAN_CERTIFICATE)
                        )
                        linked_org_file_field_data.append(
                            LinkedOrganizationDocumentFileFieldFieldData(
                                field_config_id=field_config.pk,
                                document_id=document.pk,
                                file=old_doc.file,
                                file_name=old_doc.name,
                                created_by_id=old_doc.uploaded_by_id,
                            )
                        )
        else:
            if linked_org_pan_docs_v2:
                document = linked_org_pan_docs_v2.pop()
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=organization.pan_number,
                        created_by_id=SYSTEM_USER_ID,
                    )
                )

        index = 0
        while index < len(org_old_aadhar_docs):
            old_doc = org_old_aadhar_docs[index]
            document = linked_org_aadhar_docs_v2.pop()
            if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.aadhar_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=old_doc.organization.vendor.aadhar_number,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
            if old_doc.file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_FRONT))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=old_doc.file,
                        file_name=old_doc.name,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
            if index + 1 < len(org_old_aadhar_docs):
                old_doc = org_old_aadhar_docs[index + 1]
                if old_doc.file:
                    field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_REAR))
                    linked_org_file_field_data.append(
                        LinkedOrganizationDocumentFileFieldFieldData(
                            field_config_id=field_config.pk,
                            document_id=document.pk,
                            file=old_doc.file,
                            file_name=old_doc.name,
                            created_by_id=old_doc.uploaded_by_id,
                        )
                    )
            index += 2

        for old_doc in org_old_msme_docs:
            document = linked_org_msme_docs_v2.pop()
            if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.msme_id:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=old_doc.organization.vendor.msme_id,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
            if old_doc.file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_CERTIFICATE))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=old_doc.file,
                        file_name=old_doc.name,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )

        for gst_number in org_old_gst_numbers:
            document = linked_org_gst_docs_v2.pop()
            if gst_number.gst_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=gst_number.gst_number,
                        created_by_id=gst_number.created_by_id,
                    )
                )
            if gst_number.name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_CERTIFICATE))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=gst_number.file,
                        file_name=gst_number.name,
                        created_by_id=gst_number.created_by_id,
                    )
                )
            if gst_number.gst_state:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_STATE))
                linked_org_state_field_data.append(
                    LinkedOrganizationDocumentStateFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        state_id=gst_number.gst_state,
                        created_by_id=gst_number.created_by_id,
                    )
                )

        for bank_detail in linked_old_primary_bank_details:
            if not linked_org_primary_banks_v2:
                continue
            document = linked_org_primary_banks_v2.pop()
            if bank_detail.account_holder_name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_HOLDER_NAME))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.account_holder_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.bank_name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.BANK_NAME))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.bank_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.account_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.account_number,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.ifsc_code:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.IFSC_CODE))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.ifsc_code,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.cheque_file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.CANCELLED_CHEQUE))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=bank_detail.cheque_file,
                        file_name=bank_detail.cheque_file_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )

        for bank_detail in linked_old_other_bank_details:
            if not linked_org_other_bank_docs_v2:
                continue
            document = linked_org_other_bank_docs_v2.pop()
            if bank_detail.account_holder_name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_HOLDER_NAME))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.account_holder_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.bank_name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.BANK_NAME))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.bank_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.account_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.ACCOUNT_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.account_number,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.ifsc_code:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.IFSC_CODE))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=bank_detail.ifsc_code,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
            if bank_detail.cheque_file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.CANCELLED_CHEQUE))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=bank_detail.cheque_file,
                        file_name=bank_detail.cheque_file_name,
                        created_by_id=bank_detail.created_by_id,
                    )
                )
        LinkedOrganizationDocumentTextFieldData.objects.bulk_create(linked_org_text_field_data, batch_size=50)
        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_create(linked_org_file_field_data, batch_size=50)
        LinkedOrganizationDocumentStateFieldData.objects.bulk_create(linked_org_state_field_data, batch_size=50)
        print(f"Linked field created for mapping: {mapping}")

    # Migrating data for org linked as client
    vendor_org_ids = FromToOrgMapping.objects.filter(org_from_id=org_id, deleted_at__isnull=True).values_list(
        "org_to_id", flat=True
    )
    mappings = [
        LinkedOrganizations(
            client_id=org_id,
            vendor_id=vendor_org_id,
            org_id=vendor_org_id,
            created_by_id=SYSTEM_USER_ID,
        )
        for vendor_org_id in vendor_org_ids
    ]
    mappings = LinkedOrganizations.objects.bulk_create(mappings, batch_size=50)
    print(f"Mappings created for org_id as client: {org_id}")

    linked_address_list = []
    for mapping in mappings:
        for address in address_list:
            linked_address_list.append(
                LinkedOrganizationAddress(
                    mapping=mapping,
                    header=address.header,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city_id=address.city_id,
                    state_id=address.state_id,
                    country_id=address.country_id,
                    zip_code=address.zip_code,
                    created_by_id=address.created_by_id,
                )
            )
    LinkedOrganizationAddress.objects.bulk_create(linked_address_list, batch_size=50)
    print(f"Linked Address data created successfully for org_id as client: {org_id}")

    for mapping in mappings:
        linked_org_docs = []
        if organization.pan_number and len(org_old_pan_docs) == 0:
            document_config = documents_config_mapping.get(DocumentNames.PAN)
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=SYSTEM_USER_ID,
                )
            )
        for old_document in org_old_documents:
            if old_document.type == OrganizationDocumentChoices.PAN:
                document_config = documents_config_mapping.get(DocumentNames.PAN)
            elif old_document.type == OrganizationDocumentChoices.AADHAR:
                document_config = documents_config_mapping.get(DocumentNames.AADHAR)
            # elif old_document.type == OrganizationDocumentChoices.MSME_DOC:
            #     document_config = documents_config_mapping.get(DocumentNames.MSME)
            else:
                continue
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=old_document.uploaded_by_id,
                )
            )
        document_config = documents_config_mapping.get(DocumentNames.GST)
        for gst_number in org_old_gst_numbers:
            linked_org_docs.append(
                LinkedOrganizationDocument(
                    mapping=mapping,
                    document_config=document_config,
                    created_by_id=gst_number.created_by_id,
                )
            )
        # document_config = documents_config_mapping.get(DocumentNames.PRIMARY_BANK_ACCOUNT)
        # for bank_detail in org_old_primary_bank_details:
        #     linked_org_docs.append(
        #         LinkedOrganizationDocument(
        #             mapping=mapping,
        #             document_config=document_config,
        #             created_by_id=bank_detail.created_by_id,
        #         )
        #     )
        linked_org_docs = LinkedOrganizationDocument.objects.bulk_create(linked_org_docs, batch_size=50)
        print(f"Linked Doc created for mapping: {mapping}")

        linked_org_pan_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.PAN]
        linked_org_aadhar_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.AADHAR]
        # linked_org_msme_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.MSME]
        linked_org_gst_docs_v2 = [doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.GST]
        # linked_org_primary_banks_v2 = [
        #     doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.PRIMARY_BANK_ACCOUNT
        # ]
        # linked_org_other_bank_docs_v2 = [
        #     doc for doc in linked_org_docs if doc.document_config.name == DocumentNames.OTHER_BANK_ACCOUNTS
        # ]
        linked_org_text_field_data = []
        linked_org_file_field_data = []
        linked_org_state_field_data = []

        if org_old_pan_docs:
            for old_doc in org_old_pan_docs:
                if linked_org_pan_docs_v2:
                    document = linked_org_pan_docs_v2.pop()
                    if old_doc.organization.pan_number:
                        field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
                        linked_org_text_field_data.append(
                            LinkedOrganizationDocumentTextFieldData(
                                field_config_id=field_config.pk,
                                document_id=document.pk,
                                data=old_doc.organization.pan_number,
                                created_by_id=old_doc.uploaded_by_id,
                            )
                        )
                    if old_doc.file:
                        field_config = fields_config_mapping.get(
                            (document.document_config.pk, FieldNames.PAN_CERTIFICATE)
                        )
                        linked_org_file_field_data.append(
                            LinkedOrganizationDocumentFileFieldFieldData(
                                field_config_id=field_config.pk,
                                document_id=document.pk,
                                file=old_doc.file,
                                file_name=old_doc.name,
                                created_by_id=old_doc.uploaded_by_id,
                            )
                        )
        else:
            if linked_org_pan_docs_v2:
                document = linked_org_pan_docs_v2.pop()
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.PAN_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=organization.pan_number,
                        created_by_id=SYSTEM_USER_ID,
                    )
                )

        index = 0
        while index < len(org_old_aadhar_docs):
            old_doc = org_old_aadhar_docs[index]
            document = linked_org_aadhar_docs_v2.pop()
            if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.aadhar_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=old_doc.organization.vendor.aadhar_number,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
            if old_doc.file:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_FRONT))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=old_doc.file,
                        file_name=old_doc.name,
                        created_by_id=old_doc.uploaded_by_id,
                    )
                )
            if index + 1 < len(org_old_aadhar_docs):
                old_doc = org_old_aadhar_docs[index + 1]
                if old_doc.file:
                    field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.AADHAAR_REAR))
                    linked_org_file_field_data.append(
                        LinkedOrganizationDocumentFileFieldFieldData(
                            field_config_id=field_config.pk,
                            document_id=document.pk,
                            file=old_doc.file,
                            file_name=old_doc.name,
                            created_by_id=old_doc.uploaded_by_id,
                        )
                    )
            index += 2

        # for old_doc in org_old_msme_docs:
        #     document = linked_org_msme_docs_v2.pop()
        #     if old_doc.organization and old_doc.organization.vendor and old_doc.organization.vendor.msme_id:
        #         field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_NUMBER))
        #         linked_org_text_field_data.append(
        #             LinkedOrganizationDocumentTextFieldData(
        #                 field_config_id=field_config.pk,
        #                 document_id=document.pk,
        #                 data=old_doc.organization.vendor.msme_id,
        #                 created_by_id=old_doc.uploaded_by_id,
        #             )
        #         )
        #     if old_doc.file:
        #         field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.MSME_CERTIFICATE))
        #         linked_org_file_field_data.append(
        #             LinkedOrganizationDocumentFileFieldFieldData(
        #                 field_config_id=field_config.pk,
        #                 document_id=document.pk,
        #                 file=old_doc.file,
        #                 file_name=old_doc.name,
        #                 created_by_id=old_doc.uploaded_by_id,
        #             )
        #         )

        for gst_number in org_old_gst_numbers:
            document = linked_org_gst_docs_v2.pop()
            if gst_number.gst_number:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_NUMBER))
                linked_org_text_field_data.append(
                    LinkedOrganizationDocumentTextFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        data=gst_number.gst_number,
                        created_by_id=gst_number.created_by_id,
                    )
                )
            if gst_number.name:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_CERTIFICATE))
                linked_org_file_field_data.append(
                    LinkedOrganizationDocumentFileFieldFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        file=gst_number.file,
                        file_name=gst_number.name,
                        created_by_id=gst_number.created_by_id,
                    )
                )
            if gst_number.gst_state:
                field_config = fields_config_mapping.get((document.document_config.pk, FieldNames.GST_STATE))
                linked_org_state_field_data.append(
                    LinkedOrganizationDocumentStateFieldData(
                        field_config_id=field_config.pk,
                        document_id=document.pk,
                        state_id=gst_number.gst_state,
                        created_by_id=gst_number.created_by_id,
                    )
                )
        LinkedOrganizationDocumentTextFieldData.objects.bulk_create(linked_org_text_field_data, batch_size=50)
        LinkedOrganizationDocumentFileFieldFieldData.objects.bulk_create(linked_org_file_field_data, batch_size=50)
        LinkedOrganizationDocumentStateFieldData.objects.bulk_create(linked_org_state_field_data, batch_size=50)
        print(f"Linked field created for mapping: {mapping}")


def migrate_organizations_data():
    org_ids = Organization.objects.all().order_by("id").values_list("id", flat=True)
    print(f"Total Organizations: {len(org_ids)}")
    for org_id in org_ids:
        migrate_org_data(org_id)
        print("----------------------------------------------------------------")
        print("----------------------------------------------------------------")
    print("Migration Completed")


# Project Dynamic fields and address


# For Module Setting -> Project Creation
class ProjectFields:
    @staticmethod
    @transaction.atomic
    def update_position():
        # update state position
        ProjectCustomField.objects.filter(name="State", uuid=STATE_UUID).update(position=7)
        # update city position
        ProjectCustomField.objects.filter(name="City", uuid=CITY_UUID).update(position=8)
        print("Updated position for state and city.")

        # update section position
        ProjectCustomSection.objects.filter(uuid=PROJECT_SCOPE_SECTION_UUID).update(position=3)
        ProjectCustomSection.objects.filter(uuid=CLIENT_POC_DETAILS_SECTION_UUID).update(position=4)
        ProjectCustomSection.objects.filter(uuid=IMPORTANT_DATES_SECTION_UUID).update(position=5)
        ProjectCustomSection.objects.filter(uuid=ASSIGNED_PROJECT_USER_SECTION_UUID).update(position=6)
        print("Updated position for sections.")

    @staticmethod
    @transaction.atomic
    def create_section():
        org_ids = ProjectCustomSection.objects.all().values_list("organization_id", flat=True).distinct()
        print("Organization IDs: ", org_ids)

        section_objects = []
        for org_id in org_ids:
            basic_section = ProjectCustomSection.objects.filter(
                organization_id=org_id, uuid=BASIC_DETAIL_SECTION_UUID
            ).first()
            section_objects.append(
                ProjectCustomSection(
                    organization_id=org_id,
                    uuid=PROJECT_SETTINGS_SECTION_UUID,
                    name="Project Settings",
                    position=2,
                    type=ProjectSectionTypeEnum.ADVANCE,
                    created_by_id=basic_section.created_by_id,
                )
            )
        ProjectCustomSection.objects.bulk_create(section_objects)
        print("Added new project setting section.")

    @staticmethod
    def update_is_required():
        # update state is_required
        ProjectCustomField.objects.filter(name="State", uuid=STATE_UUID).update(is_required=False)
        print("Updated is_required for state.")

    @staticmethod
    def update_name():
        ProjectCustomField.objects.filter(name="Project Address", uuid=PROJECT_ADDRESS_LINE_1_UUID).update(
            name="Project Address Line 1"
        )
        print("Updated name for Project Address.")
        ProjectCustomField.objects.filter(uuid=PROJECT_ESTIMATE_UUID).update(name="Project Estimate")
        print("Updated name for Project Estimate.")

    @staticmethod
    @transaction.atomic
    def add_new_fields():
        org_ids = ProjectCustomSection.objects.all().values_list("organization_id", flat=True).distinct()
        print("Organization IDs: ", org_ids)

        field_objects = []
        for org_id in org_ids:
            basic_section = ProjectCustomSection.objects.filter(
                organization_id=org_id, uuid=BASIC_DETAIL_SECTION_UUID
            ).first()
            setting_section = ProjectCustomSection.objects.filter(
                organization_id=org_id, uuid=PROJECT_SETTINGS_SECTION_UUID
            ).first()
            obj = [
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=basic_section.pk,
                    uuid=PROJECT_ADDRESS_LINE_2_UUID,
                    name="Project Address Line 2",
                    type=CustomFieldTypeChoices.TEXT,
                    position=5,
                    is_required=False,
                    data_counter=0,
                    created_by_id=basic_section.created_by_id,
                ),
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=basic_section.pk,
                    uuid=COUNTRY_UUID,
                    name="Country",
                    type=CustomFieldTypeChoices.DROPDOWN,
                    position=6,
                    is_required=True,
                    data_counter=0,
                    created_by_id=basic_section.created_by_id,
                ),
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=basic_section.pk,
                    uuid=ZIPCODE_UUID,
                    name="Zipcode",
                    type=CustomFieldTypeChoices.TEXT,
                    position=9,
                    is_required=False,
                    data_counter=0,
                    created_by_id=basic_section.created_by_id,
                ),
                # Project Settings Section Fields
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=setting_section.pk,
                    uuid=PROJECT_CURRENCY_UUID,
                    name="Currency",
                    type=CustomFieldTypeChoices.DROPDOWN,
                    position=1,
                    is_required=True,
                    data_counter=0,
                    created_by_id=setting_section.created_by_id,
                ),
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=setting_section.pk,
                    uuid=PROJECT_TAX_TYPE_UUID,
                    name="Tax Type",
                    type=CustomFieldTypeChoices.DROPDOWN,
                    position=2,
                    is_required=True,
                    data_counter=0,
                    created_by_id=setting_section.created_by_id,
                ),
                ProjectCustomField(
                    organization_id=org_id,
                    section_id=setting_section.pk,
                    uuid=PROJECT_TIMEZONE_UUID,
                    name="Timezone",
                    type=CustomFieldTypeChoices.DROPDOWN,
                    position=3,
                    is_required=True,
                    data_counter=0,
                    created_by_id=setting_section.created_by_id,
                ),
            ]
            print("Fields objects prepared for organization id: ", org_id)
            field_objects.extend(obj)

        ProjectCustomField.objects.bulk_create(field_objects)
        print("Added new fields.")


# For Board -> Card Settings
class LeadFields:
    @staticmethod
    @transaction.atomic
    def update_position():
        # update state position
        BoardLeadField.objects.filter(name="State", uuid=LEAD_STATE_UUID).update(position=5)
        # update city position
        BoardLeadField.objects.filter(name="City", uuid=LEAD_CITY_UUID).update(position=6)
        # update address line 1 position
        BoardLeadField.objects.filter(name="Address", uuid=LEAD_ADDRESS_LINE_1_UUID).update(position=7)
        print("Updated position for state, city & address.")

    @staticmethod
    def update_name():
        # update address name to address line 1
        BoardLeadField.objects.filter(name="Address", uuid=LEAD_ADDRESS_LINE_1_UUID).update(name="Address Line 1")
        print("Updated name for Address.")

    @staticmethod
    @transaction.atomic
    def add_new_fields():
        board_ids = Board.objects.all().values_list("id", flat=True).order_by("id")
        print("Board IDs: ", board_ids)

        field_objects = []
        for board_id in board_ids:
            section = BoardLeadSection.objects.filter(board_id=board_id, uuid=LEAD_BASIC_DETAIL_SECTION_UUID).first()
            first_created_field = (
                BoardLeadField.objects.filter(section__board_id=board_id).order_by("created_at").first()
            )
            obj = [
                BoardLeadField(
                    section_id=section.pk,
                    board_id=board_id,
                    uuid=LEAD_COUNTRY_UUID,
                    name="Country",
                    type=CustomFieldTypeChoices.LEAD_COUNTRY,
                    position=4,
                    is_required=False,
                    is_visible=True,
                    data_counter=0,
                    created_by_id=section.created_by_id,
                    created_at=first_created_field.created_at,
                ),
                BoardLeadField(
                    section_id=section.pk,
                    board_id=board_id,
                    uuid=LEAD_ADDRESS_LINE_2_UUID,
                    name="Address Line 2",
                    type=CustomFieldTypeChoices.LEAD_ADDRESS_LINE_2,
                    position=8,
                    is_required=False,
                    is_visible=True,
                    data_counter=0,
                    created_by_id=section.created_by_id,
                    created_at=first_created_field.created_at,
                ),
                BoardLeadField(
                    section_id=section.pk,
                    board_id=board_id,
                    uuid=LEAD_ZIPCODE_UUID,
                    name="Zipcode",
                    type=CustomFieldTypeChoices.LEAD_ZIPCODE,
                    position=9,
                    is_required=False,
                    is_visible=True,
                    data_counter=0,
                    created_by_id=section.created_by_id,
                    created_at=first_created_field.created_at,
                ),
            ]
            print("Fields objects prepared for board id: ", board_id)
            field_objects.extend(obj)

        BoardLeadField.objects.bulk_create(field_objects)
        print("Added new fields for board leads.")


def process_project_fields():
    ProjectFields.update_position()
    ProjectFields.create_section()
    ProjectFields.update_is_required()
    ProjectFields.update_name()
    ProjectFields.add_new_fields()


def process_lead_fields():
    LeadFields.update_position()
    LeadFields.update_name()
    LeadFields.add_new_fields()


# Script for order gst migrations


def order_gst_field_config_create():
    field_config = OrganizationDocumentFieldConfig.objects.filter(name=FieldNames.GST_NUMBER).first()
    OrganizationDocumentFieldContextConfig(
        context=MicroContextChoices.ORDER, country_id=INDIA, field_config=field_config, name="Vendor GST Number"
    ).save()


def migrate_order_gst_data(prev_order_id: int = 0):
    order_ids = VendorOrder.all_objects.filter(id__gt=prev_order_id).order_by("id").values_list("id", flat=True)

    gst_number_context_config = OrganizationDocumentFieldContextConfig.objects.filter(
        context=MicroContextChoices.ORDER, field_config__name="GST Number"
    ).first()

    for order_id in order_ids:
        create_data(
            order_id=order_id,
            gst_number_context_config_id=gst_number_context_config.pk,
        )


@transaction.atomic
def create_data(order_id: int, gst_number_context_config_id: int):
    create_order_gst_data(
        order_id=order_id,
        gst_number_context_config_id=gst_number_context_config_id,
    )
    create_snapshot_gst_data(
        order_id=order_id,
        gst_number_context_config_id=gst_number_context_config_id,
    )
    print("Order GST data & Snapshot GST data created successfully for -> ", order_id)


def create_order_gst_data(order_id: int, gst_number_context_config_id: int):
    gst_data = VendorOrderGSTDetail.objects.filter(vendor_order_id=order_id).select_related("vendor_order").first()
    if gst_data and gst_data.gst_number:
        created_by_id = (
            gst_data.vendor_order.updated_by_id
            if gst_data.vendor_order.updated_by_id
            else gst_data.vendor_order.created_by_id
        )
        text_obj = OrderTextFieldData(
            order_id=order_id,
            context_config_id=gst_number_context_config_id,
            data=gst_data.gst_number,
            created_by_id=created_by_id,
        )
        text_obj.save()


def create_snapshot_gst_data(order_id: int, gst_number_context_config_id: int):
    snapshot_instances = OrderSnapshot.objects.filter(order_id=order_id).available()
    snapshot_text_data = []
    for snapshot in snapshot_instances:
        if snapshot.gst_number:
            snapshot_text_data.append(
                SnapshotTextFieldData(
                    snapshot_id=snapshot.id,
                    context_config_id=gst_number_context_config_id,
                    data=snapshot.gst_number,
                    created_by_id=snapshot.created_by_id,
                )
            )
    if snapshot_text_data:
        SnapshotTextFieldData.objects.bulk_create(snapshot_text_data)


# Script for Proposal GST Data migration
class ProposalDynamicFieldConfig:
    @staticmethod
    def proposal_gst_field_config_create():
        field_config = OrganizationDocumentFieldConfig.objects.filter(name=FieldNames.GST_NUMBER).first()
        OrganizationDocumentFieldContextConfig(
            context=MicroContextChoices.PROPOSAL, country_id=INDIA, field_config_id=field_config.pk, name="GST Number"
        ).save()

    @staticmethod
    def migrate_proposal_gst_data(prev_proposal_id: int = 0):
        proposal_ids = Proposal.objects.filter(id__gt=prev_proposal_id).order_by("id").values_list("id", flat=True)

        gst_number_context_config = OrganizationDocumentFieldContextConfig.objects.filter(
            context=MicroContextChoices.PROPOSAL, field_config__name=FieldNames.GST_NUMBER
        ).first()

        for proposal_id in proposal_ids:
            ProposalDynamicFieldConfig.create_proposal_gst_field_data(
                proposal_id=proposal_id,
                gst_number_context_config_id=gst_number_context_config.pk,
            )

    @staticmethod
    def fetch_gst_number_of_vendor(vendor_id: int) -> str:
        text_object = OrganizationDocumentTextFieldData.objects.filter(
            field_config__name=FieldNames.GST_NUMBER, document__organization_id=vendor_id
        ).first()
        return text_object.data if text_object else None

    @staticmethod
    def fetch_gst_number_from_order(order_id: int) -> str:
        order_text_obj = OrderTextFieldData.objects.filter(
            order_id=order_id, context_config__field_config__name=FieldNames.GST_NUMBER
        ).first()
        return order_text_obj.data if order_text_obj else None

    @staticmethod
    @transaction.atomic
    def create_proposal_gst_field_data(proposal_id: int, gst_number_context_config_id: int):
        proposal = Proposal.objects.filter(id=proposal_id).first()
        if proposal:
            if proposal.order:
                gst_number = ProposalDynamicFieldConfig.fetch_gst_number_from_order(order_id=proposal.order_id)
            else:
                gst_number = ProposalDynamicFieldConfig.fetch_gst_number_of_vendor(vendor_id=proposal.proposal_from_id)

            if gst_number:
                text_obj = ProposalTextFieldData(
                    proposal_id=proposal_id,
                    context_config_id=gst_number_context_config_id,
                    data=gst_number,
                    created_by_id=proposal.created_by_id,
                )
                text_obj.save()
                print("Proposal GST data created for -> ", proposal_id)


def process_proposal_gst_data():
    ProposalDynamicFieldConfig.proposal_gst_field_config_create()
    ProposalDynamicFieldConfig.migrate_proposal_gst_data()


def main():
    # Creating Country config for org and project
    create_config()
    print("Country config created successfully.")

    # Filling Country in city
    fill_country_in_city()

    # Migrating org address
    fill_organization_address()

    # Migrating store address
    fill_store_address()

    # Migrating Company address
    fill_company_address()

    # Migrating Lead address
    fill_lead_project_data()

    # Creating Org data config
    create_organization_data_config()
    # Migrating Org data
    migrate_organizations_data()

    # Migrating Project fields data
    process_project_fields()

    # Migrating Lead fields data
    process_lead_fields()

    # Create Order Config
    order_gst_field_config_create()

    # Migrate Order GST data
    migrate_order_gst_data()

    # Migrate Proposal GST data
    process_proposal_gst_data()

    # Configure New Country
    # ConfigureNewCountry().configure()


# create project config for project with given revived job_ids
@transaction.atomic
def assign_project_config(job_ids: list[str]):
    timezone, _ = Timezone.objects.get_or_create(tz=pytz.timezone("Asia/Kolkata"), locale="en-IN")
    currency, _ = Currency.objects.get_or_create(symbol="₹", code="INR", name="Indian National Rupee", locale="en-IN")
    tax_type, _ = TaxType.objects.get_or_create(name="GST")

    project_config_objs = []
    success_job_ids = []
    projects = Project.objects.filter(job_id__in=job_ids)
    for project in projects:
        project_config_objs.append(
            ProjectConfig(
                project_id=project.pk, timezone_id=timezone.pk, currency_id=currency.pk, tax_type_id=tax_type.pk
            )
        )
        success_job_ids.append(project.job_id)
    if project_config_objs:
        ProjectConfig.objects.bulk_create(project_config_objs)
    print(f"Project Config assigned to job ids: {success_job_ids}")
