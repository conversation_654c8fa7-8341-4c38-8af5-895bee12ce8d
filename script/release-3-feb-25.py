from authorization.domain.constants import Permissions
from controlroom.data.models import OrganizationUserRole, ProjectUserRole
from core.models import RolePermission
from django.db import transaction
from recce.data.models import RecceTemplate


@transaction.atomic
def create_vendor_invoive_permission():
    org_role_ids = OrganizationUserRole.objects.filter(permission=Permissions.CAN_ACCESS_MANAGE_VENDOR).values_list(
        "role_id", flat=True
    )
    new_permissions = [
        RolePermission(role_id=role_id, permission=Permissions.CAN_ACCESS_VENDOR_INVOICES) for role_id in org_role_ids
    ]
    project_role_ids = ProjectUserRole.objects.filter(permission=Permissions.CAN_ACCESS_OUTGOING_ORDER).values_list(
        "role_id", flat=True
    )
    new_permissions.extend(
        [
            RolePermission(role_id=role_id, permission=Permissions.CAN_ACCESS_VENDOR_INVOICES)
            for role_id in project_role_ids
        ]
    )
    RolePermission.objects.bulk_create(new_permissions)


@transaction.atomic
def fill_position():
    org_ids = RecceTemplate.objects.all().distinct().values_list("organization_id", flat=True)
    recce_templates = []

    for org_id in org_ids:
        templates = RecceTemplate.objects.all().filter(organization_id=org_id).values_list("id", flat=True)
        for i, template in enumerate(templates):
            recce_templates.append(RecceTemplate(id=template, organization_id=org_id, position=i + 1))

    RecceTemplate.objects.bulk_update(recce_templates, ["position"])
