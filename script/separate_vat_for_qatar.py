from django.db import transaction

from core.models import Country, Organization, OrganizationConfig, TaxType
from project.data.models import ProjectConfig, Project


def update_organization_config(organization_ids: list[int], old_tax_type_id: int, new_tax_type_id: int):
    # All organization_ids updated which are created with old_tax_type
    org_config_to_update = OrganizationConfig.objects.filter(
        organization_id__in=organization_ids, tax_type_id=old_tax_type_id
    )
    for org_config in org_config_to_update:
        org_config.tax_type_id = new_tax_type_id
        org_config.save(update_fields=["tax_type_id"])

    print("Number of updated Organization Config = ", org_config_to_update.count())


def update_project_config(organization_ids: list[int], old_tax_type_id: int, new_tax_type_id: int):
    # All project_ids with old_tax_type are updated which are created by given orgs
    project_ids = list(Project.objects.filter(organization_id__in=organization_ids).values_list("id", flat=True))
    project_config_to_update = ProjectConfig.objects.filter(project_id__in=project_ids, tax_type_id=old_tax_type_id)

    for project_config in project_config_to_update:
        project_config.tax_type_id = new_tax_type_id
        project_config.save(update_fields=["tax_type_id"])

    print("Number of updated project Config = ", project_config_to_update.count())


@transaction.atomic
def separating_tax_for_country(
    country_name: str, old_tax_name: str, old_tax_remark: str, new_tax_name: str, new_tax_remark: str
):
    country_id = Country.objects.get(name=country_name).id

    old_tax_type_id = TaxType.objects.get(name=old_tax_name, remark=old_tax_remark).id
    new_tax_type_id = TaxType.objects.get(name=new_tax_name, remark=new_tax_remark).id

    # orgs with old tax type
    organization_ids = list(Organization.objects.filter(country_id=country_id).values_list("id", flat=True))

    # update organizations' config
    update_organization_config(
        organization_ids=organization_ids, old_tax_type_id=old_tax_type_id, new_tax_type_id=new_tax_type_id
    )

    # update projects' config
    update_project_config(
        organization_ids=organization_ids, old_tax_type_id=old_tax_type_id, new_tax_type_id=new_tax_type_id
    )

    print(f"Separate {new_tax_name} for {country_name} implemented successfully.")
