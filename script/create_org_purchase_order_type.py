from common.constants import SYSTEM_USER_ID
from order.data.models import OrganizationPurchaseOrderType, Organization
from django.core.paginator import Paginator
from django.db import transaction
from order.data.models import VendorOrder
from order.config.domain.constants import OrgPurchaseOrderTypeEnum


@transaction.atomic
def create_org_purchase_order_type_for_all_organizations():
    organization_ids = Organization.objects.values_list("id", flat=True)
    page_size = 500
    paginator = Paginator(organization_ids, page_size)
    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        objs_for_create = []
        for organization_id in list(page.object_list):
            objs_for_create.append(
                OrganizationPurchaseOrderType(
                    organization_id=organization_id,
                    name=OrgPurchaseOrderTypeEnum.PURCHASE_ORDER.value,
                    is_default=False,
                    is_active=True,
                    created_by_id=SYSTEM_USER_ID,
                    is_editable=False,
                )
            )
            objs_for_create.append(
                OrganizationPurchaseOrderType(
                    organization_id=organization_id,
                    name=OrgPurchaseOrderTypeEnum.REGULAR.value,
                    is_default=False,
                    is_active=True,
                    created_by_id=SYSTEM_USER_ID,
                )
            )
            objs_for_create.append(
                OrganizationPurchaseOrderType(
                    organization_id=organization_id,
                    name=OrgPurchaseOrderTypeEnum.INSTA_ORDER.value,
                    is_default=False,
                    is_active=True,
                    created_by_id=SYSTEM_USER_ID,
                )
            )
        if objs_for_create:
            print(f"Creating purchase order types for page {page_number} with {len(objs_for_create)} organizations")
            OrganizationPurchaseOrderType.objects.bulk_create(objs_for_create, batch_size=500)

@transaction.atomic
def backfill_orders_to_regular_insta_order_type():
    orders = VendorOrder.objects.filter(purchase_order_type__isnull=True).available()
    page_size = 500
    paginator = Paginator(orders, page_size)
    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        orders = list(page.object_list)
        order_origin_org_ids = [order.org_from_id for order in orders]
        print(order_origin_org_ids)
        org_purchase_order_types_regular_qs = OrganizationPurchaseOrderType.objects.filter(organization_id__in=order_origin_org_ids, name__in=["Regular"])
        org_purchase_order_types_insta_order_qs = OrganizationPurchaseOrderType.objects.filter(organization_id__in=order_origin_org_ids, name__in=["Insta Order"])
        org_purchase_order_types_regular = {org_purchase_order_type.organization_id: org_purchase_order_type for org_purchase_order_type in org_purchase_order_types_regular_qs}
        org_purchase_order_types_insta_order = {org_purchase_order_type.organization_id: org_purchase_order_type for org_purchase_order_type in org_purchase_order_types_insta_order_qs}
        print(org_purchase_order_types_regular)
        for order in orders:
            if order.order_type == "insta_order":
                order.purchase_order_type_id = org_purchase_order_types_insta_order[order.origin_org_id].id
            elif order.order_type == "regular":
                order.purchase_order_type_id = org_purchase_order_types_regular[order.origin_org_id].id
        VendorOrder.objects.bulk_update(orders, fields=["purchase_order_type_id"], batch_size=500)
        print(f"Updated purchase order types for page {page_number} with {len(orders)} orders")

