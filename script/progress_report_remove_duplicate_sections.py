from collections import defaultdict

from django.db import transaction

from progressreport.models import ProgressReportSection, ProgressReportItem, ProgressReportAttachment

def map_duplicate_sections():
    sections = ProgressReportSection.objects.all().order_by("id")
    section_map = {}
    duplicates = defaultdict(list)

    for section in sections:
        key = (section.progress_report_id, section.name, section.progress_percentage)

        if key in section_map:
            duplicates[section_map[key]].append(section.id)
        else:
            section_map[key] = section.id
            duplicates[section.id] = []

    print("dict(duplicates)\n")
    print(dict(duplicates))
    print("\n\n")
    print("section_map\n")
    print(section_map)

    return dict(duplicates)

duplicates = map_duplicate_sections()

def print_duplicate_sections_in_progress_report_item_and_attachment():
    for original_id, duplicate_ids in duplicates.items():
        items = ProgressReportItem.objects.filter(section_id__in=duplicate_ids)
        attachments = ProgressReportAttachment.objects.filter(section_id__in=duplicate_ids)

        if items.exists():
            print("  ProgressReportItem IDs:", list(items.values_list("id", flat=True)))

        if attachments.exists():
            print("  ProgressReportAttachment IDs:", list(attachments.values_list("id", flat=True)))

    print("\nDuplicate section mappings printed successfully.")

@transaction.atomic()
def replace_duplicate_sections_in_progress_report_item():
    for original_id, duplicate_ids in duplicates.items():
        ProgressReportItem.objects.filter(section_id__in=duplicate_ids).update(section_id=original_id)

@transaction.atomic()
def replace_duplicate_sections_in_progress_report_attachment():
    for original_id, duplicate_ids in duplicates.items():
        ProgressReportAttachment.objects.filter(section_id__in=duplicate_ids).update(section_id=original_id)

def delete_duplicate_sections():
    ProgressReportSection.objects.filter(id__in=[id for ids in duplicates.values() for id in ids]).delete()


def remove_duplicate_sections():
    try:
        replace_duplicate_sections_in_progress_report_item()
    except Exception as e:
        print(f"Error in replace_duplicate_sections_in_progress_report_item: {e}")
        return

    try:
        replace_duplicate_sections_in_progress_report_attachment()
    except Exception as e:
        print(f"Error in replace_duplicate_sections_in_progress_report_attachment: {e}")
        return

    delete_duplicate_sections()

if __name__ == "__main__":
    remove_duplicate_sections()





