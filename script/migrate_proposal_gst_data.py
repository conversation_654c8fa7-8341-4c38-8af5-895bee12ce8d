from core.organization.data.models import (
    OrganizationDocumentFieldConfig,
    OrganizationDocumentFieldContextConfig,
    OrganizationDocumentTextFieldData,
)
from microcontext.choices import MicroContextChoices
from order.data.models import OrderTextFieldData
from proposal.data.models import Proposal, ProposalTextFieldData
from script.feature_middle_east import INDIA, FieldNames
from django.db import transaction


class ProposalDynamicFieldConfig:
    @staticmethod
    def proposal_gst_field_config_create():
        field_config = OrganizationDocumentFieldConfig.objects.filter(name=FieldNames.GST_NUMBER).first()
        OrganizationDocumentFieldContextConfig(
            context=MicroContextChoices.PROPOSAL, country_id=INDIA, field_config_id=field_config.pk, name="GST Number"
        ).save()

    @staticmethod
    def migrate_proposal_gst_data(prev_proposal_id: int = 0):
        proposal_ids = Proposal.objects.filter(id__gt=prev_proposal_id).order_by("id").values_list("id", flat=True)

        gst_number_context_config = OrganizationDocumentFieldContextConfig.objects.filter(
            context=MicroContextChoices.PROPOSAL, field_config__name=FieldNames.GST_NUMBER
        ).first()

        for proposal_id in proposal_ids:
            ProposalDynamicFieldConfig.create_proposal_gst_field_data(
                proposal_id=proposal_id,
                gst_number_context_config_id=gst_number_context_config.pk,
            )

    @staticmethod
    def fetch_gst_number_of_vendor(vendor_id: int) -> str:
        text_object = OrganizationDocumentTextFieldData.objects.filter(
            field_config__name=FieldNames.GST_NUMBER, document__organization_id=vendor_id
        ).first()
        return text_object.data if text_object else None

    @staticmethod
    def fetch_gst_number_from_order(order_id: int) -> str:
        order_text_obj = OrderTextFieldData.objects.filter(
            order_id=order_id, context_config__field_config__name=FieldNames.GST_NUMBER
        ).first()
        return order_text_obj.data if order_text_obj else None

    @staticmethod
    @transaction.atomic
    def create_proposal_gst_field_data(proposal_id: int, gst_number_context_config_id: int):
        proposal = Proposal.objects.filter(id=proposal_id).first()
        if proposal:
            if proposal.order:
                gst_number = ProposalDynamicFieldConfig.fetch_gst_number_from_order(order_id=proposal.order_id)
            else:
                gst_number = ProposalDynamicFieldConfig.fetch_gst_number_of_vendor(vendor_id=proposal.proposal_from_id)

            if gst_number:
                text_obj = ProposalTextFieldData(
                    proposal_id=proposal_id,
                    context_config_id=gst_number_context_config_id,
                    data=gst_number,
                    created_by_id=proposal.created_by_id,
                )
                text_obj.save()
                print("Proposal GST data created for -> ", proposal_id)


# Run this method
def process_proposal_gst_data():
    ProposalDynamicFieldConfig.proposal_gst_field_config_create()
    ProposalDynamicFieldConfig.migrate_proposal_gst_data()
