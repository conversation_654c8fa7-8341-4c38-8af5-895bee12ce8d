import datetime
from django.db.models import Prefetch
from boq.data.models import BoqElement, BoqElementActionHistory
from boq.data.choices import BoqElementAction
from django.core.paginator import Paginator
from django.db import transaction


@transaction.atomic
def backfill_boq_sync_wp():
    start_date_time = datetime.datetime(2025, 5, 8)

    histories = BoqElementActionHistory.objects.filter(
        created_at__gt=start_date_time,
        action__in=[
            BoqElementAction.ORDER_APPROVED,
            BoqElementAction.CHANGE_APPROVED,
            BoqElementAction.CHANGES_DONE,
            BoqElementAction.CANCELLED,
            BoqElementAction.CANCELLATION_APPROVED,
            BoqElementAction.REQUEST_REJECTED,
        ],
    ).order_by("-created_at")

    element_ids = histories.values_list("boq_element_id", flat=True)

    prefetch_history = Prefetch("action_histories", queryset=histories, to_attr="histories")

    boq_elements = BoqElement.objects.all().filter(id__in=element_ids).prefetch_related(prefetch_history).order_by("id")

    client_id_dump = {}

    paginator = Paginator(boq_elements, 1000)

    print(f"Total BOQ Elements: {boq_elements.count()}")

    for page_number in paginator.page_range:

        page = paginator.page(page_number)

        updates = []

        for boq_element in page.object_list:
            boq_element: BoqElement

            if not hasattr(boq_element, "histories") or not boq_element.histories:
                continue

            action_history = boq_element.histories[0]
            client_id_dump[boq_element.pk] = {
                "from": boq_element.client_rate,
                "to": action_history.client_rate,
            }

            boq_element.client_rate = action_history.client_rate

            updates.append(boq_element)

        BoqElement.objects.bulk_update(updates, ["client_rate"])

        print(f"Page {page_number} processed. BOQ Elements updated: {len(updates)}")

    print("Client ID Dump:", client_id_dump)
    print("Backfill completed.")
