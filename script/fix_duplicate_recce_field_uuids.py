import uuid
from itertools import islice
from recce.data.models import <PERSON>cc<PERSON><PERSON><PERSON>, Recce
from django.db import transaction
from django.db.models import OuterRef, Subquery, Count


def batch_iterator(iterable, batch_size=500):
    """Helper to yield items in batches"""
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch


def fix_duplicate_recce_field_uuids():
    recce_section_unique_fields_uuids_count_subquery = (
        RecceField.objects
        .filter(section_id=OuterRef('section_id'), uuid=OuterRef("uuid"), deleted_at__isnull=True)
        .values('section_id', 'uuid')
        .annotate(uuid_count=Count('id'))
        .values("uuid_count")[:1]
    )

    recce_ids = RecceField.objects.annotate(uuid_count=Subquery(recce_section_unique_fields_uuids_count_subquery)).filter(uuid_count__gt=1).values_list('section__recce_id', flat=True).distinct() # noqa

    recces = Recce.objects.filter(id__in=recce_ids).prefetch_related("sections", "sections__fields").order_by("id").iterator() # noqa
    for recce_batch in batch_iterator(recces, batch_size=500):
        for recce in recce_batch:
            print(f"\n--- Processing Recce ID {recce.id} ---")
            with transaction.atomic():  # atomic at per-recce level
                field_objs_for_update = []
                for section in recce.sections.all():
                    seen_uuids = set()
                    for field in section.fields.all():
                        uuid_str = str(field.uuid)
                        if uuid_str in seen_uuids:
                            new_uuid = str(uuid.uuid4())
                            field_objs_for_update.append(RecceField(id=field.id, uuid=new_uuid))
                        else:
                            seen_uuids.add(uuid_str)
                if field_objs_for_update:
                    print(f"\n--- Updating duplicate UUIDs in Recce ID {recce.id} ---")
                    print(field_objs_for_update)
                    RecceField.objects.bulk_update(field_objs_for_update, ["uuid"])
