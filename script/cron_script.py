import json
from django_celery_beat.models import CrontabSchedule, PeriodicTask
import zoneinfo
from django.conf import settings


def schedule_todays_due_task():
    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=0,
        hour=10,
        day_of_month="*",
        month_of_year="*",
        day_of_week="*",
        timezone=zoneinfo.ZoneInfo(settings.TIME_ZONE),
    )
    PeriodicTask.objects.create(
        crontab=schedule,
        name="Today's due task notifications",
        task="schedular.tasks.todays_due_task_schedular",
        queue="schedular_worker_queue",
    )


def project_schedule_due_and_overdue_task():
    # For India
    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=0,
        hour=10,
        day_of_month="*",
        month_of_year="*",
        day_of_week="*",
        timezone=zoneinfo.ZoneInfo("Asia/Kolkata"),
    )
    PeriodicTask.objects.create(
        crontab=schedule,
        name="Project Schedule Delay and Overdue Notification For India",
        task="schedular.tasks.project_schedule_delay_and_overdue_task_schedular",
        queue="schedular_worker_queue",
        kwargs=json.dumps({"country_id": 1}),
    )
    # For UAE
    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=0,
        hour=10,
        timezone=zoneinfo.ZoneInfo("Asia/Dubai"),
    )
    PeriodicTask.objects.create(
        crontab=schedule,
        name="Project Schedule Delay and Overdue Notification For UAE",
        task="schedular.tasks.project_schedule_delay_and_overdue_task_schedular",
        queue="schedular_worker_queue",
        kwargs=json.dumps({"country_id": 7}),
    )
    # For Kuwait
    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=0,
        hour=10,
        timezone=zoneinfo.ZoneInfo("Asia/Riyadh"),
    )
    PeriodicTask.objects.create(
        crontab=schedule,
        name="Project Schedule Delay and Overdue Notification For Kuwait",
        task="schedular.tasks.project_schedule_delay_and_overdue_task_schedular",
        queue="schedular_worker_queue",
        kwargs=json.dumps({"country_id": 8}),
    )
    # For Bahrain
    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=0,
        hour=10,
        timezone=zoneinfo.ZoneInfo("Asia/Bahrain"),
    )
    PeriodicTask.objects.create(
        crontab=schedule,
        name="Project Schedule Delay and Overdue Notification For Bahrain",
        task="schedular.tasks.project_schedule_delay_and_overdue_task_schedular",
        queue="schedular_worker_queue",
        kwargs=json.dumps({"country_id": 5}),
    )
