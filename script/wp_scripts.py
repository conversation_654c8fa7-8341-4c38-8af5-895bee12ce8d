from collections import defaultdict
import copy
import decimal

from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Sum
from django.utils import timezone

from authorization.domain.constants import Permissions
from authorization.domain.services import permission_get_all
from contextlib import contextmanager
from boq.data.models import BoqElement
from core.caches import ReleaseQueue<PERSON>ache
from common.choices import OrganizationType, PermissionScope
from common.choices import Section<PERSON>ey
from common.constants import SYSTEM_USER_ID
from core.models import Role, RolePermission
from core.role.constants import PermissionSet
from core.role.data.services import PermissionModuleBaseService
from element.data.choices import ItemTypeUpdateMethodChoices
from element.data.models import ElementItemType
from progressreport.models import ProgressReport
from project.domain.entities import ItemTypeConfigCacheEntity
from project.data.models import Project, ProjectOrganization
from work_progress_v2.data.models.config import ProjectReportConfig, ProjectReportConfigHistory
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.milestone import ItemType<PERSON>ileStone, ItemTypeMileStoneConfig
from work_progress_v2.data.models.temp import WPReleaseQueue
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.constants import (
    EXPORT_MULTI_DAY_REPORT_CONFIG,
    EXPORT_SINGLE_DAY_REPORT_CONFIG,
)
from work_progress_v2.domain.entities import (
    BulkUpdateTimelineActionHistoryEntity,
    BulkUpdateTimelineElementEntity,
    ReportConfigEntity,
)
from work_progress_v2.domain.enums import WorkProgressConfigReportTypeEnum, WorkProgressReportConfigIdEnum
from work_progress_v2.interface.factory_helper import WorkProgressTimelineSync

DEFAULT_GENERATE_REPORT_CONFIG_FOR_OLD_PROJECTS: list[ReportConfigEntity] = [
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCOPE_UPDATE,
        name="Scope Update",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCOPE_SUMMARY,
                name="Scope Summary",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ITEM_WISE_UPDATE,
                name="Item wise Update",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SCHEDULE_UPDATE,
        name="Schedule Update",
        mandatory=False,
        checked=False,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.SCHEDULE_SUMMARY,
                name="Schedule Summary",
                mandatory=False,
                checked=False,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE,
                name="Activity Update",
                mandatory=False,
                checked=False,
                children=[
                    ReportConfigEntity(
                        id=WorkProgressReportConfigIdEnum.ACTIVITY_UPDATE_WITH_STATUS,
                        name="With Status",
                        mandatory=False,
                        checked=False,
                        children=[],
                    )
                ],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.DAILY_LOG,
        name="Daily Log",
        mandatory=True,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TODAY_UPDATE,
                name="Today's Update",
                mandatory=True,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.BLOCKER,
                name="Blocker",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.TOMORROW_PLAN,
                name="Tomorrow's Plan",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.SITE_VIEW_POINT,
        name="Site View Points",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MATERIAL_UPDATE,
        name="Material Update",
        mandatory=False,
        checked=False,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.MANPOWER,
        name="Manpower",
        mandatory=False,
        checked=True,
        children=[],
    ),
    ReportConfigEntity(
        id=WorkProgressReportConfigIdEnum.REPORTED_PROJECT_PROGRESS,
        name="Reported Project Progress",
        mandatory=False,
        checked=True,
        children=[
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.REPORTED_PROGRESS,
                name="Reported Progress",
                mandatory=False,
                checked=True,
                children=[],
            ),
            ReportConfigEntity(
                id=WorkProgressReportConfigIdEnum.PROJECTED_END_DATE,
                name="Projected End Date",
                mandatory=False,
                checked=True,
                children=[],
            ),
        ],
    ),
]


"""
Milestone Data Script ----------
"""


@transaction.atomic
def milestone_data():
    print("Starting milestone data backfill")

    item_types = ElementItemType.objects.all()

    print(f"Total item types: {item_types.count()}")

    for item_type in item_types:
        instance_config = ItemTypeMileStoneConfig(item_type=item_type, created_by_id=SYSTEM_USER_ID)
        instance_config.save()

        if item_type.slider_type == "slider_type_1":
            continue

        configs = item_type.progress_config
        milestones = []
        for milestone in configs:
            instance = ItemTypeMileStone()

            instance.name = milestone["title"]
            instance.percentage = decimal.Decimal(milestone["percentage"])
            instance.milestone_config = instance_config
            instance.created_by_id = SYSTEM_USER_ID
            instance.is_visible = milestone["is_visible"]

            milestones.append(instance)

        ItemTypeMileStone.objects.bulk_create(milestones)

        print(f"Item type {item_type.name} data backfilled successfully")

    print("Milestone data backfilled successfully\n")


"""
Work Progress Element Script ----------
"""


def create_milestone_mapping():
    item_type_milestone_configs = ItemTypeMileStoneConfig.objects.all().prefetch_related("milestones")

    milestone_mapping: dict[int, dict[int, int]] = {}

    for item_type_milestone_config in item_type_milestone_configs:
        item_type_milestone_config: ItemTypeMileStoneConfig

        for milestone in item_type_milestone_config.milestones.all():
            milestone: ItemTypeMileStone

            if item_type_milestone_config.item_type_id not in milestone_mapping:
                milestone_mapping[item_type_milestone_config.item_type_id] = {}

            milestone_mapping[item_type_milestone_config.item_type_id][int(milestone.percentage)] = milestone.pk

    return milestone_mapping


def take_closest_value_from_config(percentage: decimal.Decimal, item_type_milestone_mapping: dict):
    min_value = float("inf")
    result_value = percentage
    for val in item_type_milestone_mapping.keys():
        diff = abs(float(val) - float(percentage))
        if diff < min_value:
            min_value = diff
            result_value = val

    return float(result_value)


PROJECT_ITEM_TYPE_MAPPING: dict[int, ItemTypeConfigCacheEntity] = {}


def get_project_item_type_config(org_id: int, project_id: int) -> ItemTypeConfigCacheEntity:
    if PROJECT_ITEM_TYPE_MAPPING.get(project_id) is None:
        PROJECT_ITEM_TYPE_MAPPING[project_id] = WorkProgressCache(
            org_id=org_id,
            project_id=project_id,
            user_id=SYSTEM_USER_ID,
        ).get_item_type_config()
    return PROJECT_ITEM_TYPE_MAPPING[project_id]


def get_element_update_method(
    element: BoqElement,
    item_type_map: dict[int, str],
):
    if element.update_method is not None:
        return element.update_method

    update_method = None

    if element.organization_id:
        item_type_config = get_project_item_type_config(
            org_id=element.organization_id,
            project_id=element.boq_id,
        )

        if element.item_type_id:
            update_method = item_type_config.item_types.get(element.item_type_id).default_update_method
        else:
            update_method = item_type_config.default_config.default_update_method

    elif element.item_type_id:
        update_method = item_type_map.get(element.item_type_id)
    else:
        update_method = ItemTypeUpdateMethodChoices.PERCENTAGE

    assert (
        update_method is not None
    ), f"Update method not found for element {element.pk} item_type {element.item_type_id}"

    return update_method


def get_element_milestone_id_and_percentage(element: BoqElement, milestone_mapping: dict[int, dict[int, int]]):
    milestone_id = milestone_mapping[element.item_type_id].get(int(element.progress_percentage))
    milestone_percentage = int(element.progress_percentage)

    if not milestone_id:
        nearest_percent_value = take_closest_value_from_config(
            item_type_milestone_mapping=milestone_mapping[element.item_type_id],
            percentage=element.progress_percentage,
        )
        milestone_id = milestone_mapping[element.item_type_id].get(nearest_percent_value)
        milestone_percentage = nearest_percent_value

    assert (
        milestone_id is not None
    ), f"Milestone not found for element {element.pk} {element.item_type_id} {milestone_mapping[element.item_type_id]} {int(element.progress_percentage)}"
    return milestone_id, decimal.Decimal(milestone_percentage)


@transaction.atomic
def page_update(page, milestone_mapping: dict[int, dict[int, int]], item_type_map: dict[int, str]):
    updates = []

    for element in page.object_list:
        element: BoqElement

        wp_element = WorkProgressElement()

        update_method = get_element_update_method(element=element, item_type_map=item_type_map)

        input_milestone_id = None
        input_progress_percentage = None
        input_progress_quantity = None
        element_progress_percentage = element.progress_percentage

        if update_method == ItemTypeUpdateMethodChoices.MILESTONE:
            input_milestone_id, element_progress_percentage = get_element_milestone_id_and_percentage(
                element=element, milestone_mapping=milestone_mapping
            )

        elif update_method == ItemTypeUpdateMethodChoices.PERCENTAGE:
            input_progress_percentage = (
                element.progress_percentage_input
                if element.progress_percentage_input is not None
                else element.progress_percentage
            )

        elif update_method == ItemTypeUpdateMethodChoices.QUANTITY:
            input_progress_quantity = element.progress_quantity_input

        wp_element = WorkProgressElement()
        wp_element.boq_element_id = element.pk
        wp_element.progress_percentage = element_progress_percentage
        wp_element.progress_quantity_input = input_progress_quantity
        wp_element.progress_percentage_input = input_progress_percentage
        wp_element.milestone_input_id = input_milestone_id
        wp_element.update_method = element.update_method
        wp_element.input_progress_updated_at = element.status_updated_at
        wp_element.unlocked_at = None  # TODO: backfill this
        wp_element.progress_updated_at = None  # TODO: backfill this

        updates.append(wp_element)

    WorkProgressElement.objects.bulk_create(updates)
    return len(updates)


def page_start_from(start_from_page: int, paginator: Paginator, milestone_mapping, item_type_map: dict[int, str]):
    for page_number in paginator.page_range:
        if page_number < start_from_page:
            continue

        page = paginator.page(page_number)

        total_updated = page_update(page, milestone_mapping, item_type_map=item_type_map)

        print(f"Updated work progress element for page {page_number} -> {total_updated}")


def create_wp_element_data():
    print("Starting work progress element data backfill")
    boq_elements = BoqElement.objects.all().order_by("id")

    print(f"Total elements: {boq_elements.count()}")

    milestone_mapping = create_milestone_mapping()
    item_types = ElementItemType.objects.all().values("id", "default_update_method")
    item_type_map = {item_type["id"]: item_type["default_update_method"] for item_type in item_types}

    paginator = Paginator(boq_elements, 10000)

    page_start_from(1, paginator, milestone_mapping, item_type_map=item_type_map)

    print("Work progress element data backfilled successfully.\n")


"""
Total Manpower Script ----------
"""


@transaction.atomic
def backfill_total_manpower():
    print("Starting total manpower backfill")

    progress_reports = (
        ProgressReport.objects.prefetch_related("pr_manpower")
        .annotate(manpower_count=Sum("pr_manpower__count"))
        .order_by("id")
    )

    print(f"Total progress reports: {progress_reports.count()}")

    paginator = Paginator(progress_reports, 10000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []

        for progress_report in page.object_list:
            progress_report.total_manpower_count = (
                progress_report.manpower_count if progress_report.manpower_count else 0
            )

            updates.append(progress_report)

        ProgressReport.objects.bulk_update(updates, ["total_manpower_count"])
        print(f"Updated total manpower for page {page_number} -> {len(updates)}")

    print("Total manpower backfilled successfully.\n")


"""
Config Script ----------
"""


def create_project_report_config(
    org_id: int,
    project_id: int,
    config: list[dict],
    config_type: WorkProgressConfigReportTypeEnum,
):
    project_report_config = ProjectReportConfig()

    project_report_config.organization_id = org_id
    project_report_config.project_id = project_id
    project_report_config.config = config
    project_report_config.config_type = config_type.value
    project_report_config.created_by_id = SYSTEM_USER_ID

    project_report_config_history = ProjectReportConfigHistory()

    project_report_config_history.organization_id = org_id
    project_report_config_history.project_id = project_id
    project_report_config_history.config = config
    project_report_config_history.config_type = config_type.value
    project_report_config_history.created_by_id = SYSTEM_USER_ID

    return project_report_config, project_report_config_history


@transaction.atomic
def backfill_project_configs():
    print("Starting project report config backfill")
    projects = Project.objects.all().order_by("id").prefetch_related("project_organizations")

    print(f"Total projects: {projects.count()}")
    generate_report_config = copy.deepcopy(DEFAULT_GENERATE_REPORT_CONFIG_FOR_OLD_PROJECTS)
    export_single_day_config = copy.deepcopy(EXPORT_SINGLE_DAY_REPORT_CONFIG)
    export_multi_day_config = copy.deepcopy(EXPORT_MULTI_DAY_REPORT_CONFIG)

    generate_report_config_dump = [config.model_dump() for config in generate_report_config]
    export_single_day_config_dump = [config.model_dump() for config in export_single_day_config]
    export_multi_day_config_dump = [config.model_dump() for config in export_multi_day_config]

    paginator = Paginator(projects, 1000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        generate_reports = []
        export_single_day_reports = []
        export_multi_day_reports = []
        generate_report_history = []
        export_single_day_history = []
        export_multi_day_history = []

        for project in page.object_list:
            project: Project

            for project_organization in project.project_organizations.all():
                project_organization: ProjectOrganization

                config, history = create_project_report_config(
                    org_id=project_organization.organization_id,
                    project_id=project.pk,
                    config=generate_report_config_dump,
                    config_type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT,
                )

                generate_reports.append(config)
                generate_report_history.append(history)

                config, history = create_project_report_config(
                    org_id=project_organization.organization_id,
                    project_id=project.pk,
                    config=export_single_day_config_dump,
                    config_type=WorkProgressConfigReportTypeEnum.EXPORT_SINGLE_DAY_REPORT,
                )

                export_single_day_reports.append(config)
                export_single_day_history.append(history)

                config, history = create_project_report_config(
                    org_id=project_organization.organization_id,
                    project_id=project.pk,
                    config=export_multi_day_config_dump,
                    config_type=WorkProgressConfigReportTypeEnum.EXPORT_MULTI_DAY_REPORT,
                )

                export_multi_day_reports.append(config)
                export_multi_day_history.append(history)

        ProjectReportConfig.objects.bulk_create(generate_reports + export_single_day_reports + export_multi_day_reports)
        ProjectReportConfigHistory.objects.bulk_create(
            generate_report_history + export_single_day_history + export_multi_day_history
        )
        print(f"Created project report configs for page {page_number} -> {len(generate_reports)}")

    print("Project report configs backfilled successfully.\n")


"""
Config attachment Script ----------
"""


@transaction.atomic
def backfill_report_config():
    print("Starting report config backfill")
    reports = ProgressReport.objects.all().order_by("id").select_related("ppr", "ppr__project")
    config_histories = (
        ProjectReportConfigHistory.objects.all()
        .filter(config_type=WorkProgressConfigReportTypeEnum.GENERATE_REPORT.value)
        .order_by("id")
        .values("id", "project_id", "organization_id")
    )

    config_history_mapping = {}

    for config_history in config_histories:
        config_history_mapping[(config_history["organization_id"], config_history["project_id"])] = config_history["id"]

    print(f"Total reports: {reports.count()}")

    paginator = Paginator(reports, 10000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []

        for report in page.object_list:
            report: ProgressReport

            config_id = config_history_mapping.get((report.organization_id, report.ppr.project_id))

            if not config_id:
                config_id = config_history_mapping.get((report.ppr.project.organization_id, report.ppr.project_id))

            report.config_id = config_id

            updates.append(report)

        ProgressReport.objects.bulk_update(updates, ["config"])
        print(f"Updated report config for page {page_number} -> {len(updates)}")

    print("Report config backfilled successfully.\n")


def create_role_permission(role_id: int, existing_permissions: list[str], permissions: list[Permissions]):
    instances = []
    for per in permissions:
        if per in existing_permissions:
            continue

        instance = RolePermission()

        instance.role_id = role_id
        instance.permission = per

        instances.append(instance)

    return instances


def backfill_permissions():
    print("Starting permission backfill")
    roles = Role.objects.all().order_by("id")

    org_id_to_permissions = {}

    paginator = Paginator(roles, 1000)

    print(f"Total roles: {roles.count()}")

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []

        for role in page.object_list:
            role: Role

            role_id = role.pk
            org_id = role.organization.pk

            if org_id in org_id_to_permissions:
                org_permissions = org_id_to_permissions[org_id]
            else:
                org_permissions = permission_get_all(org_id=org_id)
                org_id_to_permissions[org_id] = org_permissions

            permissions = list(
                RolePermission.objects.filter(role_id=role_id)
                .values_list("permission", flat=True)
                .order_by("permission")
            )

            permission = (
                PermissionModuleBaseService(module=SectionKey.PROGRESS_UPDATE)
                .get_base_data(permissions=permissions)
                .values
            )

            can_manage_dpr_subscription = Permissions.CAN_MANAGE_DPR_SUBSCRIPTION.value in org_permissions
            can_upload_from_gallery = Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR.value in org_permissions

            if PermissionSet.PROGRESS_UPDATE_LEVEL_3 in permission:
                updates.extend(
                    create_role_permission(
                        role_id,
                        existing_permissions=permissions,
                        permissions=(
                            [
                                Permissions.CAN_ACCESS_WORK_PROGRESS,
                                Permissions.CAN_UPDATE_WORK_PROGRESS,
                                Permissions.CAN_CAPTURE_FILE_IN_DPR,
                                Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD,
                                Permissions.CAN_GENERATE_PROGRESS_REPORT,
                                Permissions.CAN_EXPORT_PROGRESS_REPORT,
                            ]
                            + ([Permissions.CAN_MANAGE_DPR_SUBSCRIPTION] if can_manage_dpr_subscription else [])
                            + ([Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR] if can_upload_from_gallery else [])
                        ),
                    )
                )

            elif PermissionSet.PROGRESS_UPDATE_LEVEL_2 in permission:
                updates.extend(
                    create_role_permission(
                        role_id,
                        existing_permissions=permissions,
                        permissions=(
                            [
                                Permissions.CAN_ACCESS_WORK_PROGRESS,
                                Permissions.CAN_UPDATE_WORK_PROGRESS,
                                Permissions.CAN_CAPTURE_FILE_IN_DPR,
                                Permissions.CAN_GENERATE_PROGRESS_REPORT,
                                Permissions.CAN_EXPORT_PROGRESS_REPORT,
                            ]
                            + ([Permissions.CAN_MANAGE_DPR_SUBSCRIPTION] if can_manage_dpr_subscription else [])
                            + ([Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR] if can_upload_from_gallery else [])
                        ),
                    )
                )

            elif PermissionSet.PROGRESS_UPDATE_LEVEL_1 in permission:
                updates.extend(
                    create_role_permission(
                        role_id,
                        permissions,
                        [
                            Permissions.CAN_ACCESS_WORK_PROGRESS,
                        ],
                    )
                )

        RolePermission.objects.bulk_create(updates)
        print(f"Updated role permissions for page {page_number} -> {len(updates)}")

    print("Role permissions backfilled successfully.\n")


def backfill_my_report_permissions():
    print("Starting my report permission backfill")
    roles = Role.objects.filter(scope=PermissionScope.CORE).order_by("id").all()

    paginator = Paginator(roles, 1000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []

        for role in page.object_list:
            role: Role

            role_id = role.pk

            permissions = list(RolePermission.objects.filter(role_id=role_id).values_list("permission", flat=True))

            if Permissions.CAN_ACCESS_MY_REPORTS.value in permissions:
                continue

            instance = RolePermission()

            instance.role_id = role_id
            instance.permission = Permissions.CAN_ACCESS_MY_REPORTS.value

            updates.append(instance)

        RolePermission.objects.bulk_create(updates)
        print(f"Updated role permissions for page {page_number} -> {len(updates)}")

    print("Role permissions backfilled successfully.\n")


@contextmanager
def fixed_datetime(dt):
    """Context manager for testing with a fixed datetime."""
    original_now = timezone.now
    timezone.now = lambda: dt
    try:
        yield
    finally:
        timezone.now = original_now


def run_release_queue():
    queue = WPReleaseQueue.objects.all().order_by("created_at")

    paginator = Paginator(queue, 1000)

    print(f"Total release queue: {queue.count()}")

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []
        with transaction.atomic():
            for release_queue in page.object_list:
                with fixed_datetime(release_queue.created_at):
                    release_queue: WPReleaseQueue

                    action_histories = release_queue.action_histories
                    element_to_updated_fields_map = release_queue.element_to_updated_fields_map

                    action_histories = [BulkUpdateTimelineActionHistoryEntity(**data) for data in action_histories]
                    element_to_updated_fields_map = {
                        BulkUpdateTimelineElementEntity(**data.get("element")): data.get("updated_fields")
                        for pk, data in element_to_updated_fields_map.items()
                    }

                    timeline_sync = WorkProgressTimelineSync()

                    timeline_sync.run_release_queue(
                        user_id=release_queue.created_by_id,
                        project_id=release_queue.project_id,
                        org_id=release_queue.organization_id,
                        element_to_updated_fields_map=element_to_updated_fields_map,
                        action_histories=action_histories,
                    )
                    print(f"Release queue processed for {release_queue.pk}")

                release_queue.status = True
                updates.append(release_queue)

            WPReleaseQueue.objects.bulk_update(updates, ["status"])

        print(f"Release queue processed for page {page_number} -> {len(page.object_list)}")

    ReleaseQueueCache.set(data=False)

    print("Release queue processed successfully.\n")


def main():
    milestone_data()
    create_wp_element_data()
    backfill_total_manpower()
    backfill_project_configs()
    backfill_report_config()
    backfill_permissions()
    backfill_my_report_permissions()


if __name__ == "__main__":
    main()
