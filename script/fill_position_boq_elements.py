from boq.data.models import Boq, BoqElementV2
from django.db import transaction
from django.db.models import F
from itertools import islice


def chunks(iterable, size):
    iterator = iter(iterable)
    for first in iterator:
        yield [first] + list(islice(iterator, size - 1))

@transaction.atomic
def fill_position_in_boq_elements():
    boq_element_objs = []
    boq_ids = Boq.objects.all().order_by("project_id").values_list("project_id", flat=True)
    # boq_ids = Boq.objects.filter(project_id__in=[55665]).values_list("project_id", flat=True)
    for boq_id_chunk in chunks(boq_ids, 100):
        print("Processing chunk of {} boqs".format(len(boq_id_chunk)))
        for boq_id in boq_id_chunk:
            print("Processing boq {}".format(boq_id))
            boq_elements = BoqElementV2.objects.filter(boq_id=boq_id).order_by(F("section_id").asc(nulls_first=True), "position_ts").available() # noqa
            position = 1
            prev_section_id = -1
            for boq_element in boq_elements:
                if boq_element.section_id != prev_section_id:
                    position = 1
                    prev_section_id = boq_element.section_id
                boq_element.position = position
                boq_element_objs.append(boq_element)
                position += 1
        print("Updating positions for {} boq elements".format(len(boq_element_objs)))
        BoqElementV2.objects.bulk_update(boq_element_objs, ["position"], batch_size=1000)
        boq_element_objs = []  # Clear the list after each bulk update