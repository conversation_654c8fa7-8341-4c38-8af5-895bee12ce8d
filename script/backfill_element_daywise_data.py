from boq.data.models import BoqElementActionHistory
from django.db import transaction

from work_progress_v2.data.models.daywise_data import WorkProgressElementDayWiseData
from core.caches import UnitOfMeasurementCache
from work_progress_v2.data.models.timeline import MileStoneHistory


@transaction.atomic
def backfill_element_daywise_data():
    """
    Backfill element daywise data for the last 30 days.
    """
    wp_element_ids = WorkProgressElementDayWiseData.objects.values_list("element_id", flat=True).distinct()

    objects_to_update: list[WorkProgressElementDayWiseData] = []
    uom_cache = UnitOfMeasurementCache.get()

    not_found_action_history = []

    daywise_data = WorkProgressElementDayWiseData.objects.filter(element_id__in=wp_element_ids).order_by("date")
    print(f"Total records to process: {daywise_data.count()}")
    for data in list(daywise_data):
        data: WorkProgressElementDayWiseData

        day_end_action_history = (
            BoqElementActionHistory.objects.filter(
                boq_element_id=data.element_id,
                created_at__date__lte=data.date,
            )
            .order_by("-created_at")
            .first()
        )
        if not day_end_action_history:
            not_found_action_history.append({"element_id": data.element_id, "date": data.date})
            print(f"Day end action history not found for element ID: {data.element_id} on date: {data.date}")
            continue

        # day end milestone history
        milestone_history = (
            MileStoneHistory.objects.filter(
                timeline__element__boq_element_id=data.element_id,
                created_at__date=data.date,
            )
            .order_by("-created_at")
            .first()
        )

        data.day_end_uom = int(day_end_action_history.uom) if day_end_action_history else None
        data.day_end_uom_name = uom_cache.get(day_end_action_history.uom) if data.day_end_uom else None
        data.day_end_milestone_name = milestone_history.name if milestone_history else None

        objects_to_update.append(data)

        # print(f"Backfilled data for element ID: {data.element_id} on date: {data.date}")

    # Update the objects in bulk
    if objects_to_update:
        WorkProgressElementDayWiseData.objects.bulk_update(
            objects_to_update, ["day_end_uom", "day_end_uom_name", "day_end_milestone_name"], batch_size=1000
        )
        print(f"Updated {len(objects_to_update)} records in bulk.")
    print("Not found action history count:", len(not_found_action_history))
    print("Script completed successfully.")
