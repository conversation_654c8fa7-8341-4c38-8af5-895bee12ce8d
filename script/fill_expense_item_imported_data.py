from common.element_base.services import ElementCodeService
from expense.data.models import ExpenseItem, ExpenseItemImportedData


def fill_expense_item_imported_data():
    expense_item_imported_data_objs = []
    expense_items = ExpenseItem.objects.filter(boq_element__isnull=False).select_related("boq_element")
    for expense_item in expense_items:
        if not hasattr(expense_item, "imported_data") and expense_item.boq_element:
            print(expense_item.pk)
            expense_item_imported_data_objs.append(
                ExpenseItemImportedData(
                    expense_item_id=expense_item.pk,
                    code=ElementCodeService.get_code(
                        serial_number=expense_item.boq_element.serial_number, custom_type=expense_item.boq_element.custom_type, code=expense_item.boq_element.code, version=expense_item.boq_element.version # noqa
                    ),
                    item_type_id=expense_item.boq_element.item_type_id,
                    category_id=expense_item.boq_element.category_id,
                    preview_file=expense_item.boq_element.main_preview_file.file if expense_item.boq_element.main_preview_file else None, # noqa
                )
            )
    print(len(expense_item_imported_data_objs))
    ExpenseItemImportedData.objects.bulk_create(expense_item_imported_data_objs)
    return expense_item_imported_data_objs
