from order.data.models import VendorPurchaseOrder, VendorOrder
from django.db import transaction

@transaction.atomic
def fill_po_data_in_orders():
    vendor_orders_objs = []
    vendor_purchase_orders = VendorPurchaseOrder.objects.filter(vendor_order__outgoing_status__in=["approved", "sent"], vendor_order__po_status__in=["pending"], vendor_order__po_attached_at__isnull=True)
    for vendor_purchase_order in vendor_purchase_orders:
        print(vendor_purchase_order.vendor_order_id)
        vendor_orders_objs.append(
            VendorOrder(
                id=vendor_purchase_order.vendor_order_id,
                po_status="attached",
                po_attached_at=vendor_purchase_order.uploaded_at
            )
        )
    VendorOrder.objects.bulk_update(vendor_orders_objs, fields=["po_status", "po_attached_at"])