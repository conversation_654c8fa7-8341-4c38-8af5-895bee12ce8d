from django.core.paginator import Paginator

from crm.data.models import BoardUser
from crm.board.data.choices import BoardUserEditSettingChoices, BoardUserDeleteSettingChoices


def backfill_board_user_permission():
    board_users = BoardUser.objects.all().order_by("id")

    print(f"Total Board Users: {board_users.count()}")

    paginator = Paginator(board_users, 10000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        for board_user in page.object_list:
            board_user: BoardUser

            edit_setting = BoardUserEditSettingChoices(board_user.edit_setting) if board_user.edit_setting else False

            if edit_setting:
                if edit_setting == BoardUserEditSettingChoices.ONLY_CREATED_LEADS:
                    board_user.delete_setting = BoardUserDeleteSettingChoices.ONLY_CREATED_LEADS
                elif edit_setting == BoardUserEditSettingChoices.ASSIGNED_AND_CREATED_LEADS:
                    board_user.delete_setting = BoardUserDeleteSettingChoices.ASSIGNED_AND_CREATED_LEADS
                elif edit_setting == BoardUserEditSettingChoices.EDIT_ALL_LEADS:
                    board_user.delete_setting = BoardUserDeleteSettingChoices.DELETE_ALL_LEADS

                updates.append(board_user)

        BoardUser.objects.bulk_update(updates, ["delete_setting"])
        print(f"Processed page {page_number} of {paginator.num_pages} with {len(updates)} updates.")

    print("Backfill completed.")
