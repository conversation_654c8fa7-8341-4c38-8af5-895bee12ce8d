from django.core.paginator import Paginator
from django.db import transaction

from core.entities import ProjectUserEntity
from project.data.models import ProjectOrgData, ProjectOrganization
from work_progress_v2.data.repositories import WorkProgressRepo
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService


@transaction.atomic
def project_scope_percent_fix_for_org(org_id: int):
    print("project_scope_percent_fix started")

    all_project_ids = list(
        ProjectOrganization.objects.filter(organization_id=org_id).values_list("project_id", flat=True)
    )

    paginator = Paginator(all_project_ids, 1000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        updates = []
        project_ids = list(page.object_list)
        project_org_data_objs = ProjectOrgData.objects.filter(organization_id=org_id, project_id__in=project_ids)
        project_org_data_obj_map = {obj.project_id: obj for obj in project_org_data_objs}

        for project_id in project_ids:
            print("Updating project id : ", project_id)

            scope_data = WorkProgressScopeDataService(
                repo=WorkProgressRepo(
                    user_id=1,
                    project_id=project_id,
                    org_id=org_id,
                ),
                user_entity=ProjectUserEntity(user_id=1, org_id=org_id, project_id=project_id),
            ).get_scope_data()

            project_org_data = project_org_data_obj_map.get(project_id)
            project_org_data.progress_percentage = scope_data.total_progress

            updates.append(project_org_data)

        if updates:
            ProjectOrgData.objects.bulk_update(updates, ["progress_percentage"])
        print(f"Updated {len(updates)} projects in page {page_number}.")


# main function to process organizations
def process_orgs(org_ids: list[int]):
    for org_id in org_ids:
        print(f"Processing organization id: {org_id}")
        project_scope_percent_fix_for_org(org_id)

    print("All organizations processed successfully.")
