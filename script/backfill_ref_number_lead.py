from core.models import Organization
from crm.data.models import Lead
from django.db import transaction


@transaction.atomic
def fill_ref_number():
    organizations = Organization.objects.all()

    for organization in organizations:
        print(f"Processing organization: {organization.pk}")
        leads = (
            Lead.objects.select_related("board", "board__organization")
            .filter(board__organization_id=organization.pk)
            .order_by("created_at")
        )
        lead_objs_to_update = []
        starting = 1
        for lead in leads:
            lead_objs_to_update.append(Lead(id=lead.pk, serial_number=starting))
            starting += 1

        Lead.objects.bulk_update(lead_objs_to_update, fields=["serial_number"])
