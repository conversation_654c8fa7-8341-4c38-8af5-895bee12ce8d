from boq.data.models import BoqElement, BoqElementPreviewFile
from django.db.models import Prefetch
from django.core.paginator import Paginator


def main_preview_file():
    elements = (
        BoqElement.objects.prefetch_related(
            Prefetch(
                "preview_files",
                queryset=BoqElementPreviewFile.objects.filter(is_main=True).order_by("uploaded_at").available(),
            ),
        )
        .all()
        .order_by("id")
    )

    print(f"Total elements: {elements.count()}")

    paginator = Paginator(elements, 10000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        for element in page.object_list:
            if element.preview_files.exists():
                element.main_preview_file_id = element.preview_files.first().pk
                updates.append(element)

        BoqElement.objects.bulk_update(updates, ["main_preview_file_id"])
        print(f"Updated {len(updates)} elements")
