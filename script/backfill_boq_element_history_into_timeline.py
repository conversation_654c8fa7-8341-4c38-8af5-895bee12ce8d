from collections import defaultdict
from py import process
from pydantic import ConfigDict
import datetime
import decimal
from django.db import transaction
from django.db.models import Subquery, OuterRef, Q, F, Min, Max
from django.db.models.functions import JSONObject
from boq.data.models import BoqElement, BoqElementHistory
from core.caches import OrganizationUOMCache, UnitOfMeasurementCache
from common.pydantic.base_model import BaseModelV2, OrgIdProjectIdEntity
from project.domain.caches import ItemTypeConfigCache
from element.data.choices import ItemTypeUpdateMethodChoices
from work_progress_v2.data.choices import WorkProgressElementActionChoices
from work_progress_v2.data.models import element, timeline
from work_progress_v2.data.models.timeline import *
import structlog
from django.core.paginator import Paginator
from common.utils import get_local_time
from project.data.models import Project, ProjectConfig
import pytz
from django.utils import timezone
from dateutil import parser
import multiprocessing
from functools import partial

from work_progress_v2.domain.services import status

logger = structlog.get_logger(__name__)


uom_cache = UnitOfMeasurementCache.get()


class TimelineObjectCreationHelper:
    @classmethod
    def _create_input_quantity_history_object(
        cls,
        timeline_obj,
        quantity: decimal.Decimal,
        uom: int | None,
        uom_name: str,
        created_at: datetime.datetime,
        user_id: int,
    ) -> InputProgressQuantityHistory:
        element_uom = None
        element_uom_name = None
        if uom:
            element_uom = uom
            element_uom_name = uom_name

        return InputProgressQuantityHistory(
            timeline=timeline_obj,
            quantity=quantity,
            uom=element_uom,
            uom_name=element_uom_name,
            created_at=created_at,
            created_by_id=user_id,
        )

    @classmethod
    def _create_input_progress_percentage_history_object(
        cls, timeline_obj, percentage: decimal.Decimal, created_at: datetime.datetime, user_id: int
    ) -> InputProgressPercentageHistory:
        return InputProgressPercentageHistory(
            timeline=timeline_obj,
            percentage=percentage,
            created_at=created_at,
            created_by_id=user_id,
        )

    @classmethod
    def _create_milestone_history_object(
        cls, timeline_obj, milestone_name: str, created_at: datetime.datetime, user_id: int
    ) -> MileStoneHistory:
        # milestone = ItemTypeMileStone.objects.available().get(id=milestone_id)
        return MileStoneHistory(
            timeline=timeline_obj,
            milestone_id=None,  # None not allowed
            name=milestone_name,
            created_at=created_at,
            created_by_id=user_id,
        )

    @classmethod
    def _create_update_method_history_object(
        cls, timeline_obj, update_method: str, created_at: datetime.datetime, user_id: int
    ) -> UpdateMethodHistory:
        return UpdateMethodHistory(
            timeline=timeline_obj,
            update_method=update_method,
            created_at=created_at,
            created_by_id=user_id,
        )

    @classmethod
    def _create_timeline_and_action_history_mapping_object(
        cls,
        timeline_obj,
        boq_element_action_history_id: int,
        element_history_id: int,
        updated_fields: list[str],
        created_at: datetime.datetime,
        user_id: int,
    ) -> WorkProgressTimelineAndBoqElementActionHistoryMapping:
        return WorkProgressTimelineAndBoqElementActionHistoryMapping(
            timeline=timeline_obj,
            boq_element_action_history_id=boq_element_action_history_id,
            boq_element_history_id=element_history_id,
            updated_fields=updated_fields,
            created_at=created_at,
            created_by_id=user_id,
        )

    @classmethod
    def _create_timeline_percentage_history(
        cls,
        element_id: int,
        timeline_obj,
        item_type_id: int | None,
        percentage: decimal.Decimal,
        created_at: datetime.datetime,
        user_id: int,
    ) -> PercentageHistory:
        percentage_history_obj = PercentageHistory(
            timeline=timeline_obj,
            percentage=percentage,
            item_type_id=item_type_id,
            previous_day_percentage_id=None,
            created_at=created_at,
            created_by_id=user_id,
        )
        # previous_history_obj = (
        #     PercentageHistory.objects.filter(timeline__element_id=element_id).order_by("-created_at").first()
        # )
        # if previous_history_obj:
        #     if previous_history_obj.created_at.date() == created_at.date():
        #         percentage_history_obj.previous_day_percentage_id = previous_history_obj.previous_day_percentage_id
        #     else:
        #         percentage_history_obj.previous_day_percentage_id = previous_history_obj.pk

        return percentage_history_obj

    @classmethod
    def _create_timeline_object(
        cls, element_id: int, action: WorkProgressElementActionChoices, created_at: datetime.datetime, user_id: int
    ) -> WorkProgressElementTimeline:
        return WorkProgressElementTimeline(
            element_id=element_id,
            action=action,
            created_at=created_at,
            created_by_id=user_id,
        )


class TimelineHistoryData(BaseModelV2):
    history_id: int
    percentage_history: decimal.Decimal
    input_progress_quantity: decimal.Decimal | None
    input_progress_percentage: decimal.Decimal | None  # convert from int to decimal while storing
    input_progress_milestone: str | None
    item_type_id: int | None
    update_method: ItemTypeUpdateMethodChoices | None
    uom: int | None
    uom_name: str
    action: WorkProgressElementActionChoices
    boq_updated_fields: list[str] | None = None
    is_input_quantity_changed_by_boq_element_update: bool = False
    created_at: datetime.datetime
    created_by_id: int
    previous_history_obj_id: int | None
    action_history_id: int | None


class BulkCreateObjects(BaseModelV2):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    timeline_objects: list[WorkProgressElementTimeline] = []
    percentage_history_objects: list[PercentageHistory] = []
    input_quantity_history_objects: list[InputProgressQuantityHistory] = []
    input_progress_percentage_history_objects: list[InputProgressPercentageHistory] = []
    milestone_history_objects: list[MileStoneHistory] = []
    update_method_history_objects: list[UpdateMethodHistory] = []
    timeline_and_action_history_objects: list[WorkProgressTimelineAndBoqElementActionHistoryMapping] = []


WP_ELEMENT_TO_PROGRESS_UPDATED_AT_MAP: dict[int, datetime.datetime] = {}


def fetch_element_histories(element_id: int):
    # Get all history records for the specified elements
    previous_history_subquery = Subquery(
        BoqElementHistory.objects.filter(element_id=OuterRef("element_id"), created_at__lt=OuterRef("created_at"))
        .order_by("-created_at")
        .annotate(
            previous_progress_history_data=JSONObject(
                progress_percentage="progress_percentage",
                status_name="status_name",
                item_type_id="item_type_id",
                quantity="quantity",
                progress_quantity_input="progress_quantity_input",
                progress_percentage_input="progress_percentage_input",
                update_method="update_method",
                uom="uom",
            )
        )
        .values("previous_progress_history_data")[:1]
    )

    return list(
        BoqElementHistory.objects.select_related("element", "item_type")
        .filter(element_id=element_id)
        .annotate(previous_progress_history=previous_history_subquery)
        .order_by("created_at")
    )


def fetch_element_action_histories(element_id: int):
    # Get all action history records for the specified elements

    previous_history_subquery = Subquery(
        BoqElementActionHistory.objects.filter(
            boq_element_id=OuterRef("boq_element_id"), created_at__lt=OuterRef("created_at")
        )
        .order_by("-created_at")
        .annotate(
            previous_action_history_data=JSONObject(
                quantity="quantity",
                uom="uom",
                item_type_id="item_type_id",
            )
        )
        .values("previous_action_history_data")[:1]
    )

    return list(
        BoqElementActionHistory.objects.select_related("boq_element", "item_type")
        .filter(boq_element_id=element_id)
        .annotate(previous_action_history=previous_history_subquery)
        .order_by("created_at")
    )


def prepare_element_history_to_action_history_mapping(
    element_histories: list[BoqElementHistory], action_histories: list[BoqElementActionHistory]
):
    mapping: dict[BoqElementHistory, list[BoqElementActionHistory]] = defaultdict(list)
    # visited_action_history_ids = []

    duplicate_action_history_data: dict[int, list[int]] = defaultdict(list)

    for history in element_histories:
        lower_time = history.created_at - datetime.timedelta(milliseconds=250)
        upper_limit = history.created_at + datetime.timedelta(milliseconds=250)
        for action_history in action_histories:
            if action_history.created_at >= lower_time and action_history.created_at <= upper_limit:
                if hasattr(action_history, "previous_action_history") and action_history.previous_action_history:
                    if (
                        (
                            action_history.quantity != action_history.previous_action_history.get("quantity")
                            and action_history.quantity == history.quantity
                        )
                        or (
                            action_history.uom != action_history.previous_action_history.get("uom")
                            and action_history.uom == history.uom
                        )
                        or (
                            action_history.item_type_id != action_history.previous_action_history.get("item_type_id")
                            and action_history.item_type_id == history.item_type_id
                        )
                    ):
                        mapping[history].append(action_history)
                        duplicate_action_history_data[action_history.pk].append(history.pk)

                elif (
                    (action_history.quantity == history.quantity)
                    and (int(action_history.uom) == history.uom)
                    and (action_history.item_type_id == history.item_type_id)
                ):

                    mapping[history].append(action_history)
                    duplicate_action_history_data[action_history.pk].append(history.pk)

    # Check if action history is linked to more than one element history
    # for action_history_id, history_ids in duplicate_action_history_data.items():
    #     if len(history_ids) > 1:
    #         print(f"Action history {action_history_id} linked to More than 1 Element histories {history_ids}.")

    return mapping


def compare_action_history_data(
    element_history_to_action_histories_mapping: dict[BoqElementHistory, list[BoqElementActionHistory]],
):
    mapping: dict[int, int | None] = defaultdict()

    # compare action history data
    for history, action_histories in element_history_to_action_histories_mapping.items():
        action_id = None
        prev_quantity = None
        prev_uom = None
        prev_item_type_id = None
        for idx, action_history in enumerate(action_histories):
            if idx == 0:
                # TODO: how to check if (quantity, uom, item_type_id is updated)
                if (
                    (action_history.quantity == history.quantity)
                    and (int(action_history.uom) == history.uom)
                    and (action_history.item_type_id == history.item_type_id)
                ):
                    action_id = action_history.pk
            else:
                if (
                    (action_history.quantity != prev_quantity and action_history.quantity == history.quantity)
                    or (action_history.uom != prev_uom and int(action_history.uom) == history.uom)
                    or (
                        action_history.item_type_id != prev_item_type_id
                        and action_history.item_type_id == history.item_type_id
                    )
                ):
                    action_id = action_history.pk

            prev_quantity = action_history.quantity
            prev_uom = action_history.uom
            prev_item_type_id = action_history.item_type_id

        mapping[history.pk] = action_id

    # if mapping:
    #     print("History to action history mapping: \n", mapping)
    return mapping


def create_timeline(element_id: int, timeline_data_list: list[TimelineHistoryData]):
    timeline_objects: list[WorkProgressElementTimeline] = []
    percentage_history_objects: list[PercentageHistory] = []
    input_quantity_history_objects: list[InputProgressQuantityHistory] = []
    input_progress_percentage_history_objects: list[InputProgressPercentageHistory] = []
    milestone_history_objects: list[MileStoneHistory] = []
    update_method_history_objects: list[UpdateMethodHistory] = []
    timeline_and_action_history_objects: list[WorkProgressTimelineAndBoqElementActionHistoryMapping] = []

    progress_updated_at = None

    for history in timeline_data_list:
        action = history.action
        created_at = history.created_at
        created_by_id = history.created_by_id

        # print("timeline_data", history)

        progress_updated_at = created_at
        timeline_obj: WorkProgressElementTimeline = TimelineObjectCreationHelper._create_timeline_object(
            element_id=element_id, action=action, created_at=created_at, user_id=created_by_id
        )
        if action == WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED:
            assert history.input_progress_quantity is not None

            input_quantity_history_objects.append(
                TimelineObjectCreationHelper._create_input_quantity_history_object(
                    timeline_obj=timeline_obj,
                    quantity=history.input_progress_quantity,
                    uom=history.uom,
                    uom_name=history.uom_name,
                    created_at=created_at,
                    user_id=created_by_id,
                )
            )
        elif action == WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED:
            assert history.input_progress_percentage is not None or history.percentage_history is not None
            input_progress_percentage_history_objects.append(
                TimelineObjectCreationHelper._create_input_progress_percentage_history_object(
                    timeline_obj=timeline_obj,
                    percentage=(
                        history.input_progress_percentage
                        if history.input_progress_percentage
                        else history.percentage_history
                    ),
                    created_at=created_at,
                    user_id=created_by_id,
                )
            )
        elif action == WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED:
            assert history.input_progress_milestone is not None
            milestone_history_objects.append(
                TimelineObjectCreationHelper._create_milestone_history_object(
                    timeline_obj=timeline_obj,
                    milestone_name=history.input_progress_milestone,
                    created_at=created_at,
                    user_id=created_by_id,
                )
            )
        elif action == WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED:
            assert history.update_method is not None
            update_method_history_objects.append(
                TimelineObjectCreationHelper._create_update_method_history_object(
                    timeline_obj=timeline_obj,
                    update_method=history.update_method,
                    created_at=created_at,
                    user_id=created_by_id,
                )
            )
        elif action == WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED:
            assert history.percentage_history is not None
            # assert updated_data.boq_element_action_history_id is not None
            assert history.boq_updated_fields is not None
            assert history.history_id is not None

            # assert history.action_history_id is not None

            timeline_and_action_history_objects.append(
                TimelineObjectCreationHelper._create_timeline_and_action_history_mapping_object(
                    timeline_obj=timeline_obj,
                    boq_element_action_history_id=history.action_history_id,
                    element_history_id=history.history_id,
                    updated_fields=history.boq_updated_fields,
                    created_at=created_at,
                    user_id=created_by_id,
                )
            )

            # to create timeline for progress_input_quantity change by update in boq_element
            if history.is_input_quantity_changed_by_boq_element_update:
                # assert (
                #     history.input_progress_quantity is not None
                # ), "Input progress quantity is required when boq element quantity is updated."
                input_quantity_history_objects.append(
                    TimelineObjectCreationHelper._create_input_quantity_history_object(
                        timeline_obj=timeline_obj,
                        quantity=history.input_progress_quantity,
                        uom=history.uom,
                        uom_name=history.uom_name,
                        created_at=created_at,
                        user_id=created_by_id,
                    )
                )

        assert history.percentage_history is not None, "Progress_percentage is required for all actions"

        timeline_objects.append(timeline_obj)

        percentage_history_objects.append(
            TimelineObjectCreationHelper._create_timeline_percentage_history(
                element_id=element_id,
                timeline_obj=timeline_obj,
                item_type_id=history.item_type_id,
                percentage=history.percentage_history,
                created_at=created_at,
                user_id=created_by_id,
            )
        )

    WP_ELEMENT_TO_PROGRESS_UPDATED_AT_MAP[element_id] = progress_updated_at

    return BulkCreateObjects(
        timeline_objects=timeline_objects,
        input_quantity_history_objects=input_quantity_history_objects,
        input_progress_percentage_history_objects=input_progress_percentage_history_objects,
        milestone_history_objects=milestone_history_objects,
        update_method_history_objects=update_method_history_objects,
        timeline_and_action_history_objects=timeline_and_action_history_objects,
        percentage_history_objects=percentage_history_objects,
    )


def get_first_history_action(history: BoqElementHistory):
    status_name = history.status_name
    action = None

    check_condition_1 = (
        status_name is None and history.progress_quantity_input is None and history.progress_percentage_input is None
    )

    check_condition_2 = (
        status_name is None
        and history.progress_quantity_input is None
        and (
            history.progress_percentage_input is not None
            and decimal.Decimal(history.progress_percentage_input) == decimal.Decimal(0)
        )
    )

    check_condition_3 = (
        status_name is None
        and (
            history.progress_quantity_input is not None
            and decimal.Decimal(history.progress_quantity_input) == decimal.Decimal(0)
        )
        and history.progress_percentage_input is None
    )

    # status_name is None and input_progress_quantity is 0 and input_progress_percent is 0
    if check_condition_1 or check_condition_2 or check_condition_3:
        # No progress, Skip this history
        return None

    # Check if any type of progress is done
    if status_name is not None:
        if bool(set(status_name).intersection("0123456789")):
            # Either I/P Percentage Or I/P Quantity changed
            if status_name.isdigit():
                return WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED
            elif "%" in status_name:
                return WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED
            elif status_name.split(" ")[0].isdigit():
                return WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED
        else:
            # Milestone updated
            return WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED

    if history.progress_quantity_input is not None:
        return WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED
    elif history.progress_percentage_input is not None:
        return WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED

    return action


def get_history_action_by_comparing_previous_history_data(history: BoqElementHistory):
    previous_history_data = history.previous_progress_history
    action = None
    status_name = history.status_name

    check_condition_1 = (
        status_name is None and history.progress_quantity_input is None and history.progress_percentage_input is None
    )

    check_condition_2 = (
        status_name is None
        and history.progress_quantity_input is None
        and (
            history.progress_percentage_input is not None
            and decimal.Decimal(history.progress_percentage_input) == decimal.Decimal(0)
        )
    )

    check_condition_3 = (
        status_name is None
        and (
            history.progress_quantity_input is not None
            and decimal.Decimal(history.progress_quantity_input) == decimal.Decimal(0)
        )
        and history.progress_percentage_input is None
    )

    # status_name is None and input_progress_quantity is 0 and input_progress_percent is 0
    if check_condition_1 or check_condition_2 or check_condition_3:
        # No progress, Skip this history
        return None

    if (
        status_name != previous_history_data.get("status_name")
        or history.progress_quantity_input != previous_history_data.get("progress_quantity_input")
        or history.progress_percentage_input != previous_history_data.get("progress_percentage_input")
    ):
        if bool(set(status_name).intersection("0123456789")):
            # Either I/P Percentage Or I/P Quantity changed
            if status_name.isdigit():
                return WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED
            elif "%" in status_name:
                return WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED
            elif status_name.split(" ")[0].isdigit():
                return WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED
        else:
            # Milestone updated
            return WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED

    return action


def process_element(element_id: int):
    element_histories = fetch_element_histories(element_id=element_id)
    action_histories = fetch_element_action_histories(element_id=element_id)

    first_history = element_histories[0]
    element_org_id = first_history.element.organization_id
    if element_org_id is None:
        element_org_id = first_history.created_by.org_id

    # for element default item type
    item_type_configs = ItemTypeConfigCache.get(
        key=OrgIdProjectIdEntity(org_id=element_org_id, project_id=first_history.element.boq_id)
    )
    item_type_config = item_type_configs.item_types.get(first_history.item_type_id)
    element_default_update_method = item_type_config.default_update_method if item_type_config else None

    element_history_to_action_histories_mapping = prepare_element_history_to_action_history_mapping(
        element_histories=list(element_histories), action_histories=list(action_histories)
    )

    # to create a mapping of history id to action history id (will be used only in case of Boq Element Updated)
    history_to_action_history_map = compare_action_history_data(
        element_history_to_action_histories_mapping=element_history_to_action_histories_mapping
    )
    unknown_action_history_ids = []
    # element_id_to_timeline_data_map: dict[int, list[TimelineHistoryData]] = defaultdict(list)
    timeline_data_list: list[TimelineHistoryData] = []

    # traverse from earliest to latest history
    for index, history in enumerate(element_histories):
        # print("History id: ", history.__dict__)

        if index == 0:
            # first history record, set the previous values to None
            element_action = None
            action_id = history_to_action_history_map.get(history.pk, None)
            boq_updated_fields = None

            if action_id is not None:
                element_action = WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED
                boq_updated_fields = ["item_type_id", "uom", "quantity"]  # check
            elif history.update_method and history.update_method != element_default_update_method:
                element_action = WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED
            else:
                element_action = get_first_history_action(history)

            # Skipping First History if it is not linked to any action
            if element_action is None:
                # print(f"Unknown action for element_id {element_id} and history_id {history.pk}")
                unknown_action_history_ids.append(history.pk)
                continue

            timeline_data_list.append(
                TimelineHistoryData(
                    history_id=history.pk,
                    percentage_history=history.progress_percentage,
                    input_progress_quantity=(
                        history.progress_quantity_input if history.progress_quantity_input is not None else None
                    ),
                    input_progress_percentage=(
                        decimal.Decimal(history.progress_percentage_input)
                        if history.progress_percentage_input is not None
                        else None
                    ),
                    input_progress_milestone=history.status_name,
                    item_type_id=history.item_type_id,
                    update_method=(
                        ItemTypeUpdateMethodChoices(history.update_method) if history.update_method else None
                    ),
                    uom=history.uom if history.uom else None,
                    uom_name=uom_cache.get(history.uom, "") if history.uom else "",
                    action=element_action,
                    boq_updated_fields=boq_updated_fields,
                    previous_history_obj_id=None,  # can be filled with another separate script
                    created_at=history.created_at,
                    created_by_id=history.created_by_id,
                    action_history_id=action_id,
                )
            )

        else:
            element_action = None
            boq_updated_fields = []
            is_input_quantity_changed_by_boq_element_update = False

            if (
                history.item_type_id != history.previous_progress_history.get("item_type_id")
                or history.uom != history.previous_progress_history.get("uom")
                or history.quantity != history.previous_progress_history.get("quantity")
            ):
                element_action = WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED

                # check for other relevant boq updated fields
                if history.item_type_id != history.previous_progress_history.get("item_type_id"):
                    boq_updated_fields.append("item_type_id")
                if history.uom != history.previous_progress_history.get("uom"):
                    boq_updated_fields.append("uom")
                if history.quantity != history.previous_progress_history.get("quantity"):
                    boq_updated_fields.append("quantity")

                # check if input_progress_quantity changed by boq element update
                if history.progress_quantity_input != history.previous_progress_history.get("progress_quantity_input"):
                    is_input_quantity_changed_by_boq_element_update = True

            elif history.update_method != history.previous_progress_history.get("update_method"):
                element_action = WorkProgressElementActionChoices.UPDATE_METHOD_UPDATED

            else:
                element_action = get_history_action_by_comparing_previous_history_data(history)

            # assert element_action is not None
            if element_action is None:
                # print(f"Unknown action for element_id {history.element_id} and history_id {history.pk}")
                # print("ele_name", history.element.name, "project_id", history.element.boq_id, "org_id", history.element.organization_id)
                unknown_action_history_ids.append(history.pk)
                continue

            # Skip the BoqElement Update history if it is not linked to any action history
            # if (
            #     element_action == WorkProgressElementActionChoices.BOQ_ELEMENT_UPDATED
            #     and history_to_action_history_map.get(history.pk) is None
            # ):
            #     continue

            timeline_data_list.append(
                TimelineHistoryData(
                    history_id=history.pk,
                    percentage_history=history.progress_percentage,
                    input_progress_quantity=(
                        history.progress_quantity_input if history.progress_quantity_input is not None else None
                    ),
                    input_progress_percentage=(
                        decimal.Decimal(history.progress_percentage_input)
                        if history.progress_percentage_input is not None
                        else None
                    ),
                    input_progress_milestone=history.status_name,
                    item_type_id=history.item_type_id,
                    update_method=ItemTypeUpdateMethodChoices(history.update_method) if history.update_method else None,
                    uom=history.uom if history.uom else None,
                    uom_name=uom_cache.get(history.uom, "") if history.uom else "",
                    action=element_action,
                    boq_updated_fields=boq_updated_fields if len(boq_updated_fields) else None,  # check
                    is_input_quantity_changed_by_boq_element_update=is_input_quantity_changed_by_boq_element_update,
                    previous_history_obj_id=None,  # storing None for now, can be updated by separate script
                    created_at=history.created_at,
                    created_by_id=history.created_by_id,
                    action_history_id=history_to_action_history_map.get(history.pk, None),
                )
            )

    # create timeline objects
    objects = create_timeline(element_id=element_id, timeline_data_list=timeline_data_list)
    return objects
    # print(f"Unknown action histories for {element_id} - {unknown_action_history_ids}")
    # print("Timeline objects created for element_id: ", element_id)


@transaction.atomic
def process_page(page, skipped_element_ids: list[int]):
    timeline_objects: list[WorkProgressElementTimeline] = []
    percentage_history_objects: list[PercentageHistory] = []
    input_quantity_history_objects: list[InputProgressQuantityHistory] = []
    input_progress_percentage_history_objects: list[InputProgressPercentageHistory] = []
    milestone_history_objects: list[MileStoneHistory] = []
    update_method_history_objects: list[UpdateMethodHistory] = []
    timeline_and_action_history_objects: list[WorkProgressTimelineAndBoqElementActionHistoryMapping] = []
    wp_elements_to_update: list[WorkProgressElement] = []

    WP_ELEMENT_TO_PROGRESS_UPDATED_AT_MAP.clear()

    for element_id in page.object_list:
        print("Element id: ", element_id)
        if element_id in skipped_element_ids:
            continue
        objects = process_element(element_id=element_id)
        # print("\n --------------------------------- \n")
        timeline_objects.extend(objects.timeline_objects)
        percentage_history_objects.extend(objects.percentage_history_objects)
        input_quantity_history_objects.extend(objects.input_quantity_history_objects)
        input_progress_percentage_history_objects.extend(objects.input_progress_percentage_history_objects)
        milestone_history_objects.extend(objects.milestone_history_objects)
        update_method_history_objects.extend(objects.update_method_history_objects)
        timeline_and_action_history_objects.extend(objects.timeline_and_action_history_objects)

        obj = WorkProgressElement()
        obj.boq_element_id = element_id
        obj.progress_updated_at = WP_ELEMENT_TO_PROGRESS_UPDATED_AT_MAP[element_id]
        wp_elements_to_update.append(obj)

    # bulk create objects
    WorkProgressElementTimeline.objects.bulk_create(timeline_objects)
    InputProgressQuantityHistory.objects.bulk_create(input_quantity_history_objects)
    InputProgressPercentageHistory.objects.bulk_create(input_progress_percentage_history_objects)
    MileStoneHistory.objects.bulk_create(milestone_history_objects)
    UpdateMethodHistory.objects.bulk_create(update_method_history_objects)
    WorkProgressTimelineAndBoqElementActionHistoryMapping.objects.bulk_create(timeline_and_action_history_objects)
    PercentageHistory.objects.bulk_create(percentage_history_objects)

    # update progress_update_updated_at
    WorkProgressElement.objects.bulk_update(wp_elements_to_update, fields=["progress_updated_at"])
    # print("Timeline objects created.")


def process_paginator(start_page: int, paginator: Paginator, skipped_element_ids: list[int]):
    for page_number in paginator.page_range:
        if page_number < start_page:
            continue

        page = paginator.page(page_number)
        process_page(page=page, skipped_element_ids=skipped_element_ids)
        print(f"Processed {page_number} pages with {len(page.object_list)} elements.")


def process_timeline(start_page: int = 1, skipped_element_ids: list[int] = []):
    boq_element_ids = (
        BoqElement.objects.filter(boq_element_history__isnull=False)
        .order_by("id")
        .distinct()
        .values_list("id", flat=True)
    )

    # boq_element min-max ids
    boq_element_min_max_ids = (
        BoqElement.objects.filter(boq_element_history__isnull=False)
        .order_by("id")
        .distinct()
        .aggregate(min_id=Min("id"), max_id=Max("id"))
    )
    timeline_boq_element_id_data["min_id"] = boq_element_min_max_ids.get("min_id")
    timeline_boq_element_id_data["max_id"] = boq_element_min_max_ids.get("max_id")

    # timeline min-max ids
    timeline_min_max_ids = (
        WorkProgressElementTimeline.objects.all()
        .order_by("id")
        .distinct()
        .aggregate(min_id=Min("id"), max_id=Max("id"))
    )
    timeline_min_max_id_data["min_id"] = timeline_min_max_ids.get("min_id")
    timeline_min_max_id_data["max_id"] = timeline_min_max_ids.get("max_id")
    #######

    print("Total boq_elements with history: ", boq_element_ids.count())
    paginator = Paginator(boq_element_ids, 1000)
    process_paginator(start_page=start_page, paginator=paginator, skipped_element_ids=skipped_element_ids)
    # raise Exception("Script is OK.")
    print("Backfill completed for all elements.")
    print("BoqElement Min-Max ids: ", timeline_boq_element_id_data)
    print("Timeline Min-Max ids: ", timeline_min_max_id_data)


def process_timeline_parallel(
    start_pk: int,
    end_pk: int,
    start_page: int = 1,
    skipped_element_ids: list[int] = [],
):
    print("Backfill started for boq_element id range: ", start_pk, " to ", end_pk)
    boq_element_ids = BoqElement.objects.filter(boq_element_history__isnull=False).order_by("id")

    boq_element_ids = boq_element_ids.filter(id__gte=start_pk, id__lte=end_pk)

    boq_element_ids = boq_element_ids.distinct().values_list("id", flat=True)

    count = boq_element_ids.count()

    print("Total boq_elements with history: ", count)
    paginator = Paginator(boq_element_ids, 1000)
    process_paginator(start_page=start_page, paginator=paginator, skipped_element_ids=skipped_element_ids)
    # raise Exception("Script is OK.")
    print("Backfill completed for all elements.")
    return count


def initialize_django():
    """Initialize Django for worker processes"""
    import django

    django.setup()

    # After setup, import any modules that depend on Django
    # This will prevent circular import issues
    from django.db import connections

    # Close any existing connections to avoid leak warnings
    connections.close_all()


def process_worker_with_django_init(*args):
    initialize_django()
    return process_timeline_parallel(*args)


def process_timeline_multiprocessing(core: int = 4, start_page: int = 1, skipped_element_ids: list[int] = []):
    import time

    start_time = time.time()
    """Process timeline in parallel using multiple processes"""
    boq_element_ids = (
        BoqElement.objects.filter(boq_element_history__isnull=False)
        .order_by("id")
        .distinct()
        .values_list("id", flat=True)
    )

    # Convert to list for slicing
    element_ids = list(boq_element_ids)
    total_elements = len(element_ids)
    print(f"Total boq_elements with history: {total_elements}")

    batch_size = (total_elements + core - 1) // core

    # Create batch parameters with start_pk and end_pk for each batch
    batch_params = []
    for i in range(0, total_elements, batch_size):
        batch = element_ids[i : i + batch_size]
        if batch:  # Make sure the batch is not empty
            batch_params.append((batch[0], batch[-1]))  # (start_pk, end_pk)

    print(f"Created {len(batch_params)} batches with ~{batch_size} elements each")

    pool_args = []
    for start_pk, end_pk in batch_params:
        pool_args.append((start_pk, end_pk, start_page, skipped_element_ids))

    mp_context = multiprocessing.get_context("fork")
    # Set up multiprocessing pool
    with mp_context.Pool(processes=core) as pool:
        # Map batches to worker processes
        results = pool.starmap(process_worker_with_django_init, pool_args)

    # Sum of all processed elements
    total_processed = sum(r for r in results if r)

    end_time = time.time()
    execution_time = end_time - start_time

    print(f"Total execution time: {execution_time:.2f} seconds ({execution_time/60:.2f} minutes)")
    print(f"Backfill completed. Processed {total_processed} elements.")


#################### timeline scripts ######################


def process_project_elements(project: dict):
    project_timezone = project.get("config__timezone__tz")
    if project_timezone:
        tz = pytz.timezone(str(project_timezone))
    else:
        tz = pytz.timezone("Asia/Kolkata")
    timezone.activate(tz)

    element_ids = BoqElement.objects.filter(boq_id=project["id"]).values_list("id", flat=True)

    objects_to_update: list[PercentageHistory] = []

    for element_id in element_ids:
        percentage_histories = PercentageHistory.objects.filter(timeline__element__boq_element_id=element_id).order_by(
            "created_at"
        )

        previous_history = None

        for history in percentage_histories:
            history: PercentageHistory

            if previous_history is None:
                previous_history = history
                continue

            current_history_created_at = history.created_at
            previous_history_created_at = previous_history.created_at

            if get_local_time(current_history_created_at).date() == get_local_time(previous_history_created_at).date():
                history.previous_day_percentage_id = previous_history.previous_day_percentage_id
            else:
                history.previous_day_percentage_id = previous_history.pk

            objects_to_update.append(history)

            previous_history = history

    PercentageHistory.objects.bulk_update(objects_to_update, fields=["previous_day_percentage_id"], batch_size=1000)

    # deactivate timezone
    timezone.deactivate()


@transaction.atomic
def process_percentage_history_page(page):
    for project in page.object_list:
        print("Project id: ", project["id"])
        process_project_elements(project=project)


def backfill_previous_percentage_history(start_page: int = 1):
    print("Backfill previous percentage history started.")

    projects = (
        Project.objects.all()
        .select_related("config", "config__timezone")
        .order_by("id")
        .values("id", "config__timezone__tz")
    )

    # percentage history min-max ids
    percentage_history_min_max_ids = (
        PercentageHistory.objects.all().order_by("id").distinct().aggregate(min_id=Min("id"), max_id=Max("id"))
    )
    previous_percentage_min_max_id_data["min_id"] = percentage_history_min_max_ids.get("min_id")
    previous_percentage_min_max_id_data["max_id"] = percentage_history_min_max_ids.get("max_id")
    #######

    paginator = Paginator(projects, 100)
    for page_number in paginator.page_range:
        if page_number < start_page:
            continue

        page = paginator.page(page_number)
        process_percentage_history_page(page=page)
        print(f"Processed {page_number} pages with {len(page.object_list)} elements.")

    print("Backfill previous percentage history completed.")
    print("Previous Percentage History Min-Max ids: ", previous_percentage_min_max_id_data)


######### backfill unlocked_at field in wp_element table ######
@transaction.atomic
def backfill_unlocked_at():
    print("Backfill unlocked_at field in wp_element table started.")

    wp_elements = WorkProgressElement.objects.filter(unlocked_at__isnull=True).order_by("boq_element_id")

    # unlocked_at min-max ids
    unlocked_at_min_max_ids = (
        WorkProgressElement.objects.all()
        .order_by("boq_element_id")
        .distinct()
        .aggregate(min_id=Min("boq_element_id"), max_id=Max("boq_element_id"))
    )
    unlocked_at_min_max_id_data["min_id"] = unlocked_at_min_max_ids.get("min_id")
    unlocked_at_min_max_id_data["max_id"] = unlocked_at_min_max_ids.get("max_id")
    #######

    paginator = Paginator(wp_elements, 1000)
    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        objects_to_update: list[WorkProgressElement] = []
        for wp_element in page.object_list:
            wp_element: WorkProgressElement

            if (
                wp_element.progress_percentage
                and wp_element.progress_percentage > decimal.Decimal(0)
                and wp_element.progress_updated_at is not None
            ):
                wp_element.unlocked_at = wp_element.progress_updated_at - datetime.timedelta(seconds=1)
                objects_to_update.append(wp_element)
        WorkProgressElement.objects.bulk_update(objects_to_update, fields=["unlocked_at"], batch_size=1000)
        print(f"Processed {page_number} pages with {len(page.object_list)} elements.")

    print("Backfill unlocked_at field in wp_element table completed.")
    print("Unlocked At Min-Max ids: ", unlocked_at_min_max_id_data)


"""
MAIN FUNCTION
"""

timeline_boq_element_id_data = {}
timeline_min_max_id_data = {}

previous_percentage_min_max_id_data = {}

unlocked_at_min_max_id_data = {}


def timeline_scripts():
    # process_timeline()
    backfill_previous_percentage_history()
    backfill_unlocked_at()


if __name__ == "__main__":
    import django

    django.setup()

    process_timeline_multiprocessing()
