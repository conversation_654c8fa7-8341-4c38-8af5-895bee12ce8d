
from project_schedule.data.models import ProjectScheduleActivity
from project_schedule.data.choices import ProjectScheduleActivityTypeChoices


def fill_type():
    activities = ProjectScheduleActivity.objects.all().only("id", "is_parent")
    updated_activities = []
    for activity in activities:
        if activity.is_parent:
            activity.type = ProjectScheduleActivityTypeChoices.PROJECT.value
            updated_activities.append(activity)

    ProjectScheduleActivity.objects.bulk_update(updated_activities, ["type"], batch_size=1000)
    print(f"Updated {len(updated_activities)} activities")