from collections import defaultdict
from project.data.models import Project

from django.core.paginator import Paginator
from collections import Counter
from project_schedule.data.models import ProjectScheduleActivityProgressAttachment
from common.constants import SYSTEM_USER_ID
from django.utils import timezone
from django.db import transaction


def get_activities():
    projects = (
        Project.objects.all()
        .order_by("id")
        .prefetch_related("schedule__activities__progress_attachments")
        .filter(schedule__isnull=False)
    )

    print(f"Total projects: {projects.count()}")

    result = defaultdict(dict)

    paginator = Paginator(projects, 1000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)

        for project in page.object_list:
            project: Project
            project_activities_with_dupes = {}

            for activity in project.schedule.activities.all():
                # Get all UUIDs in one query
                uuids = list(activity.progress_attachments.values_list("uuid", flat=True))

                # Find duplicates in a single pass
                duplicate_uuids = [uuid for uuid, count in Counter(uuids).items() if count > 1]

                if duplicate_uuids:
                    project_activities_with_dupes[activity.pk] = {
                        "id": activity.pk,
                        "name": activity.name,
                        "duplicate_uuids": duplicate_uuids,
                    }

            if project_activities_with_dupes:
                result[project.pk] = project_activities_with_dupes

        print(f"Finished processing page {page_number} of {paginator.num_pages}")

    print(f"Found {len(result)} projects with duplicate attachment UUIDs")
    for project_id, activities in result.items():
        print(f"Project {project_id}: {activities} affected activities")

    return result


def remove_duplicate_attachments():
    result = get_activities()

    with transaction.atomic():
        for project_id, activities in result.items():
            print(f"Project {project_id}")

            updates = []

            for activity_id, activity in activities.items():
                print(f"Activity {activity_id}: {activity['name']}")

                # Get all attachments for the activity
                attachments = ProjectScheduleActivityProgressAttachment.objects.filter(
                    activity_id=activity_id,
                    uuid__in=activity["duplicate_uuids"],
                ).order_by("id")

                # Create a set to track unique UUIDs
                seen_uuids = set()

                for attachment in attachments:
                    if attachment.uuid in seen_uuids:
                        attachment.deleted_at = timezone.now()
                        attachment.deleted_by_id = SYSTEM_USER_ID
                        updates.append(attachment)
                    else:
                        seen_uuids.add(attachment.uuid)

            # Bulk update the attachments
            if updates:
                ProjectScheduleActivityProgressAttachment.objects.bulk_update(
                    updates,
                    ["deleted_at", "deleted_by_id"],
                    batch_size=10000,
                )

            print(f"Updated {len(updates)} attachments for project {project_id}")

    print("Result:", result)
    print("Finished removing duplicate attachments.")
