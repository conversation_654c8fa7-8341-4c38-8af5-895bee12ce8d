from django.contrib.postgres.search import Search<PERSON><PERSON>y, SearchVector
from django.db.models import <PERSON>, CharField, F, Q, QuerySet, Value, When

from common.choices import StateChoices, VendorStatusChoices
from vendor.data.models import Vendor
from vendorv2.data.models import ClientVendorMapping


def vendor_search(*, search_query: str):
    search = SearchQuery(search_query, search_type="websearch")
    state_choice = [When(Q(state=key), then=Value(value)) for key, value in StateChoices.choices]

    vendors = (
        Vendor.objects.annotate(
            states=Case(
                *state_choice,
                output_field=CharField(),
            ),
            search=SearchVector("organization__name", "code", "city", "states", "category__name"),
        )
        .filter(search=search)
        .all()
    )
    return vendors


def vendor_get_using_vendor_code(*, code: int):
    return Vendor.objects.get(code=code)


def vendor_get_using_pan_number(*, pan_number: str):
    return Vendor.objects.get(pan_number=pan_number)


# def outgoing_org_list_fetch(*, org_from_id: int) -> QuerySet:
#     org_mappings_queryset = FromToOrgMapping.objects.filter(org_from_id=org_from_id).
#     values_list("org_to_id", flat=True)
#     return (
#         Vendor.objects.filter(organization_id__in=org_mappings_queryset, is_active=True)
#         .all()
#         .select_related("organization")
#         .prefetch_related("category")
#     )


def outgoing_org_list_fetch(*, org_from_id: int) -> QuerySet:
    org_mappings_queryset = (
        ClientVendorMapping.objects.filter(
            org_from_id=org_from_id,
            vendor_status=VendorStatusChoices.ACTIVE.value,
            deleted_at__isnull=True,
        )
        .exclude(org_from_id=F("org_to_id"))
        .values_list("org_to_id", flat=True)
    )
    return (
        Vendor.objects.filter(organization_id__in=org_mappings_queryset, code__isnull=False)
        .all()
        .select_related("organization")
        .prefetch_related("category")
    )
