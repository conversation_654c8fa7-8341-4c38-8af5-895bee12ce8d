from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import IntegrityError, models
from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from common.choices import StateChoices
from common.models import BaseModel, CreateUpdateModel
from common.querysets import AvailableQuerySetMixin
from core.models import Organization


class VendorCategory(BaseModel):
    name = models.CharField(max_length=50, unique=True)
    color_code = models.CharField(null=True, max_length=15)

    class Meta:
        db_table = "vendor_categories"


class Vendor(CreateUpdateModel):
    class VendorQueryset(QuerySet, AvailableQuerySetMixin):
        pass

    organization = models.OneToOneField(
        Organization, verbose_name=_("organization"), primary_key=True, on_delete=models.CASCADE, related_name="vendor"
    )
    code = models.CharField(_("code"), max_length=10, unique=True)
    serial_number = models.IntegerField(unique=True, null=True)
    city = models.CharField(null=True, blank=True, max_length=50)
    state = models.PositiveSmallIntegerField(_("state"), default=StateChoices.NOT_SET, choices=StateChoices.choices)
    gst_number = models.CharField(null=True, blank=True, max_length=50)
    category = models.ManyToManyField(VendorCategory, through="VendorCategoryMapping", related_name="vendor")
    referal_organization = models.ForeignKey(
        Organization, blank=True, null=True, default=None, on_delete=models.RESTRICT, related_name="+"
    )
    vendor_pk_id = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    msme_id = models.CharField(max_length=20, null=True, blank=True)
    pan_number = models.CharField(max_length=100, unique=True, null=True, blank=True)
    aadhar_number = models.CharField(null=True, blank=True, max_length=12, default=None)
    is_kyc_filled = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.RESTRICT, related_name="%(app_label)s_%(class)s_created", default=1
    )

    objects = VendorQueryset.as_manager()

    def save(self, *args, **kwargs):
        try:
            self.pan_number = self.pan_number.upper() if self.pan_number else None
            Organization.objects.filter(id=self.organization_id).update(pan_number=self.pan_number)
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "pan_number" in str(e):
                raise IntegrityError("Pan number already exists") from e
            raise e

    @property
    def state_name(self):
        return dict(StateChoices.choices)[self.state]

    @property
    def vendor_id(self):
        return self.code

    @property
    def location(self):
        if self.city and self.state:
            if self.state == StateChoices.NOT_SET.value:
                return self.city
            return f"{self.city}, {self.state_name}"
        elif self.city and not self.state:
            return self.city
        elif self.state and not self.city:
            if self.state == StateChoices.NOT_SET.value:
                return None
            return self.state_name


class VendorCategoryMapping(models.Model):
    vendor = models.ForeignKey(Vendor, on_delete=models.RESTRICT)
    vendor_category = models.ForeignKey(VendorCategory, on_delete=models.RESTRICT)

    class Meta:
        db_table = "vendor_category_mappings"


class VendorData(models.Model):
    id = models.IntegerField(null=False, blank=False, unique=True, primary_key=True)
    vendor_id = models.CharField(null=False, blank=False, unique=True, max_length=12)
    business_name = models.CharField(null=True, blank=True, max_length=50)
    trade_name = models.CharField(null=True, blank=True, max_length=50)
    gst_registered = models.BooleanField(null=True, blank=True)
    vendor_type = models.JSONField(null=True, blank=True)
    payment_term = models.JSONField(null=True, blank=True)
    policy = models.JSONField(null=True, blank=True)
    rcm_enabled = models.BooleanField(null=True, blank=True)
    pan_number = models.CharField(null=False, blank=False, unique=True, max_length=10)
    msme_registered = models.BooleanField(null=True, blank=True)
    msme_registration_number = models.CharField(null=True, blank=True, max_length=10)
    tds_rates = ArrayField(models.IntegerField(), null=True, blank=True)
    locations = ArrayField(models.JSONField(), null=True, blank=True)
    bank_accounts = ArrayField(models.JSONField(), null=False, blank=False)
    created_at = models.DateTimeField(
        null=False,
        blank=False,
    )
    updated_at = models.DateTimeField(
        null=False,
        blank=False,
    )
    submitted_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    created_by = models.JSONField(null=True, blank=True)
    vendor_country = models.CharField(null=True, blank=True, max_length=10)
    address = models.CharField(null=True, blank=True, max_length=150)
    tax_rate = models.JSONField(null=True, blank=True)
    attachments = models.JSONField(null=True, blank=True)
    form_custom_fields = ArrayField(models.JSONField(), null=True, blank=True)
    vendor_mapper = models.ForeignKey(
        Vendor,
        on_delete=models.RESTRICT,
        related_name="vendors",
    )

    class Meta:
        db_table = "vendor_data"
