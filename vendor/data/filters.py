from django.db.models import Q
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, FilterSet


class VendorListFilter(FilterSet):
    search_text = CharFilter(method="filter_search_text")

    def filter_search_text(self, queryset, name, value):
        queryset = queryset.filter(
            Q(organization__name__icontains=value) | Q(code__icontains=value) | Q(category__name__icontains=value)
        ).distinct()
        return queryset
