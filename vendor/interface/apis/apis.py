from typing import Optional

import structlog
from django.core.exceptions import (
    MultipleObjectsReturned,
    ObjectDoesNotExist,
    ValidationError,
)
from django.db import transaction
from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.mixins import ListModelMixin
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from common.apis import BaseApi, BaseOpenApi
from common.choices import OrganizationType, VendorStatusChoices
from common.exceptions import BaseValidationError
from common.serializers import BaseSerializer, SearchTextFilterSerializer
from core.exceptions import UserNotRegistered
from core.models import Organization, OrganizationConfig, OrganizationGSTNumber, PmcVendorMapping
from core.organization.entities import OnboardUserEntity
from core.selectors import get_user
from core.services import user_get_using_phone_number
from order.config.domain.services import IncomingOrderNotificationReceiverService
from rollingbanners.settings import VMS_TOKEN
from vendor.data.filters import VendorListFilter
from vendor.data.models import Vendor, VendorCategory
from vendor.data.selectors import (
    outgoing_org_list_fetch,
    vendor_get_using_pan_number,
    vendor_get_using_vendor_code,
    vendor_search,
)
from vendor.interface.exceptions import VendorException
from vendor.interface.serializers import VendorCreateSerializer, VendorModelSerializer
from vendorv2.data.models import ClientVendorMapping
from vendorv2.domain.entities import VendorPOCEntity
from vendorv2.domain.services.services import create_poc_user
from vendorv2.domain.services.user import VendorUserService

logger = structlog.get_logger(__name__)


class VendorSearchApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        search_query = serializers.CharField()

        class Meta:
            pass

    filter_serializer_class = FilterSerializer
    parser_classes = (MultiPartParser,)

    class OutputSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            ref_name = "VendorSearchOutput"
            fields = ("id", "name", "vendor_id", "categories", "location")
            output_hash_id_fields = ("id",)

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()}, query_serializer=FilterSerializer(), operation_id="vendor_search"
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        vendors = vendor_search(search_query=data.get("search_query"))
        return Response(self.OutputSerializer(vendors, many=True).data, status=HTTP_200_OK)


class VendorCreateApi(BaseOpenApi):
    input_serializer_class = VendorCreateSerializer
    permission_classes = []

    def check_if_vendor(self, vendor_type=None):
        if not vendor_type:
            is_vendor = True
        elif vendor_type != "Employee":
            is_vendor = True
        else:
            is_vendor = False
        return is_vendor

    @staticmethod
    def update_poc_mobile(poc_mobile, user):
        is_user_exists_with_phone_number = False
        try:
            _user = user_get_using_phone_number(phone_number=poc_mobile, create=False)
            is_user_exists_with_phone_number = True if _user is not None else False
        except ObjectDoesNotExist:
            is_user_exists_with_phone_number = False
            logger.info("User does not exist with phone number", poc_mobile=poc_mobile, user=user)
        except UserNotRegistered:
            is_user_exists_with_phone_number = False
            logger.info("User not registered with phone number", poc_mobile=poc_mobile, user=user)
        except MultipleObjectsReturned:
            is_user_exists_with_phone_number = True
            logger.info("User exist with phone number", poc_mobile=poc_mobile, user=user)

        if poc_mobile:
            if not user.phone_number and is_user_exists_with_phone_number is False:
                logger.info("mobile number not found in db, updating")
                user.phone_number = poc_mobile
                user.save()
            else:
                logger.info("mobile number already in db, mobile not updated")

    def add_vendor_category(self, vendor_type, vendor_obj):
        if vendor_type:
            vendor_category_obj, category_created = VendorCategory.objects.get_or_create(name=vendor_type)
            vendor_obj.category.add(vendor_category_obj)
            if not category_created:
                logger.info("category not created", vendor_type=vendor_type, vendor_obj=vendor_obj)

    def insert_vendor_object_to_db(
        self,
        poc,
        pan_number: str,
        pmc,
        vendor_id,
        trade_name,
        gstin: Optional[str] = None,
        vendor_type: Optional[str] = None,
    ):
        org_obj, _ = Organization.objects.get_or_create(
            name=trade_name,
            defaults={"is_client": False, "is_vendor": True, "type": OrganizationType.VENDOR, "referral_by_id": 1},
        )
        poc_user = None
        if poc and poc.get("email"):
            poc_entity = VendorPOCEntity(
                name=poc.get("first_name"), phone_number=poc.get("mobile"), email=poc.get("email")
            )

            try:
                get_user(email=poc_entity.email, phone_number=poc_entity.phone_number)
            except MultipleObjectsReturned:
                pass
            except ObjectDoesNotExist:
                poc_user = create_poc_user(
                    poc_data=poc_entity, created_by_id=1, vendor_org_id=org_obj.id, is_new_org=False
                )
                IncomingOrderNotificationReceiverService(org_id=org_obj.id).add(receiver_id=poc_user.id, user_id=1)
            org_config, _ = OrganizationConfig.objects.get_or_create(
                organization=org_obj, defaults={"poc_id": poc_user.id if poc_user else None}
            )

            org_config.order_receiver_emails.append(poc.get("email"))

            org_config.save()
            logger.info(
                "POC mapped to vendor organization",
                poc=poc,
                pmc=pmc,
                vendor_id=vendor_id,
                vendor_type=vendor_type,
                trade_name=trade_name,
                gstin=gstin,
            )
        try:
            vendor_obj, vendor_created = Vendor.objects.update_or_create(
                organization=org_obj,
                defaults={"gst_number": gstin, "pan_number": pan_number, "code": vendor_id},
            )
            if gstin:
                OrganizationGSTNumber.objects.get_or_create(
                    organization=org_obj, gst_number=gstin, defaults={"created_by_id": 1}
                )
        except Exception as e:
            logger.error(
                "Unable to mark active as vendor details mismatched",
                poc=poc,
                pmc=pmc,
                vendor_id=vendor_id,
                vendor_type=vendor_type,
                trade_name=trade_name,
                gstin=gstin,
                message=str(e),
            )
            raise ValidationError("Unable to mark active as vendor details mismatched")
        if not vendor_created:
            logger.info(
                "Vendor fetched with organization",
                vendor_obj=vendor_obj,
                org=org_obj,
                poc=poc,
                pmc=pmc,
                vendor_id=vendor_id,
                trade_name=trade_name,
                gstin=gstin,
                vendor_type=vendor_type,
            )
        if pmc is not None:
            try:
                pmc_obj = Organization.objects.get(name="91Squarefeet", id__in=[421, 498])
                vendor_obj.referal_organization = pmc_obj
                vendor_obj.save(update_fields=["referal_organization"])
                PmcVendorMapping.objects.get_or_create(pmc=pmc_obj, vendor=org_obj)

                config = OrganizationConfig.objects.filter(organization_id=pmc_obj.id).first()

                mapping, is_created = ClientVendorMapping.objects.get_or_create(
                    org_from_id=pmc_obj.id,
                    org_to_id=org_obj.id,
                    defaults={
                        "invited_by_org_id": pmc_obj.id,
                        "vendor_poc_id": config.poc_id if config and config.poc_id else None,
                        "created_by_id": 1,
                    },
                )
                if poc:
                    try:
                        VendorUserService().add(
                            mapping_id=mapping.id,
                            created_by_id=1,
                            user=OnboardUserEntity(
                                name=poc.get("name"),
                                phone_number=poc.get("mobile"),
                                email=poc.get("email"),
                                is_invited=False,
                            ),
                        )
                    except BaseValidationError:
                        pass
            except Organization.DoesNotExist:
                logger.error(
                    "Organization.DoesNotExist: Unable to create PMC Vendor mapping as PMC does not exist",
                    poc=poc,
                    vendor_id=vendor_id,
                    trade_name=trade_name,
                    gstin=gstin,
                    vendor_type=vendor_type,
                )

        logger.info(
            "Vendor Object Created/Fetched",
            org=org_obj,
            poc=poc,
            pmc=pmc,
            vendor_id=vendor_id,
            trade_name=trade_name,
            gstin=gstin,
            vendor_type=vendor_type,
        )

        self.add_vendor_category(vendor_type=vendor_type, vendor_obj=vendor_obj)

        return vendor_obj

    @swagger_auto_schema(request_body=VendorCreateSerializer, operation_summary="Create Vendor")
    @transaction.atomic
    def post(self, request):
        data = self.validate_input_data()
        if data.get("auth_token") != VMS_TOKEN:
            return Response({"message": "Please provide correct token"}, status=HTTP_401_UNAUTHORIZED)

        vendor_id = data.get("vendor_id")
        trade_name = data.get("trade_name")
        pan_number = data.get("pan_number")
        gstin = data.get("gstin")
        vendor_type = data.get("vendor_type")
        poc = data.get("poc")
        pmc = data.get("pmc_name")

        if not self.check_if_vendor(vendor_type=vendor_type):
            logger.info(
                "Provided Vendor is not of valid type",
                poc=poc,
                pmc=pmc,
                vendor_id=vendor_id,
                vendor_type=vendor_type,
                trade_name=trade_name,
                gstin=gstin,
            )
            return Response(
                {
                    "error_code": 400,
                    "message": _("Invalid Vendor provided"),
                    "description": "Vendor type is other than Contractors/Material Suppliers/Factories/Other/Turnkey",
                    "error_file": None,
                },
                status=HTTP_400_BAD_REQUEST,
            )

        org_obj = Organization.objects.filter(name=trade_name).first()
        if org_obj:
            logger.info(
                "Vendor with same company name already exists",
                poc=poc,
                pmc=pmc,
                vendor_id=vendor_id,
                vendor_type=vendor_type,
                trade_name=trade_name,
                gstin=gstin,
            )
            return Response(
                {
                    "error_code": 400,
                    "message": _("Vendor with same company name already exists"),
                    "description": None,
                    "error_file": None,
                },
                status=HTTP_400_BAD_REQUEST,
            )

        vendor = self.insert_vendor_object_to_db(
            poc=poc,
            pan_number=pan_number,
            pmc=pmc,
            vendor_id=vendor_id,
            trade_name=trade_name,
            gstin=gstin,
            vendor_type=vendor_type,
        )
        logger.info(
            "Vendor has been inserted successfully",
            vendor=vendor,
            poc=poc,
            pmc=pmc,
            vendor_id=vendor_id,
            trade_name=trade_name,
            gstin=gstin,
        )
        return Response(
            {
                "message": _("Vendor has been created successfully"),
            },
            status=HTTP_201_CREATED,
        )


class ChangeVendorStatus(BaseOpenApi):
    class InputSerializer(BaseSerializer):
        vendor_id = serializers.CharField()
        company_name = serializers.CharField()
        gst_number = serializers.CharField(allow_null=True)
        vendor_status = serializers.ChoiceField(choices=VendorStatusChoices.choices)
        auth_token = serializers.CharField()
        pan_number = serializers.CharField()

        class Meta:
            ref_name = "change_vendor_status"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "message"},
        operation_id="change_vendor_status",
        operation_summary="change the vendor as active or inactive",
    )
    def post(self, request, *args, **kwargs):
        validated_data = self.validate_input_data()
        vendor_code = validated_data.get("vendor_id")
        company_name = validated_data.get("company_name")
        gst_number = validated_data.get("gst_number")
        vendor_status = validated_data.get("vendor_status")
        is_active = True if vendor_status == VendorStatusChoices.ACTIVE else False
        pan_number = validated_data.get("pan_number")
        if validated_data.get("auth_token") != VMS_TOKEN:
            return Response({"message": "Please provide correct token"}, status=HTTP_401_UNAUTHORIZED)

        try:
            try:
                vendor = vendor_get_using_vendor_code(code=vendor_code)
            except ObjectDoesNotExist:
                vendor = vendor_get_using_pan_number(pan_number=pan_number)
                vendor.code = vendor_code

            if company_name != vendor.organization.name:
                vendor.organization.name = company_name
                vendor.organization.save()
            if gst_number != vendor.gst_number:
                OrganizationGSTNumber.objects.update_or_create(
                    organization=vendor.organization,
                    gst_number=vendor.gst_number,
                    defaults={"gst_number": gst_number, "created_by_id": 1},
                )
                vendor.gst_number = gst_number
            if pan_number != vendor.pan_number:
                vendor.pan_number = pan_number
            vendor.is_active = is_active
            vendor.save()
            # TODO: Need to collaborate
            pmc_obj = Organization.objects.get(name="91Squarefeet", id__in=[421, 498])
            client_vendor_mapping = ClientVendorMapping.objects.filter(
                org_from_id=pmc_obj.id, org_to_id=vendor.organization_id
            ).first()
            if not client_vendor_mapping:
                ClientVendorMapping.objects.create(
                    org_from_id=pmc_obj.id,
                    org_to_id=vendor.organization_id,
                    created_by_id=1,
                    vendor_poc_id=None,
                    invited_by_org_id=pmc_obj.id,
                    is_active=is_active,
                    vendor_status=vendor_status,
                )
            else:
                client_vendor_mapping.is_active = is_active
                client_vendor_mapping.vendor_status = vendor_status
                client_vendor_mapping.save()

        except Organization.DoesNotExist:
            logger.error(
                "Organization.DoesNotExist: Unable to create PMC Vendor mapping as PMC does not exist",
                vendor_code=vendor_code,
                company_name=company_name,
                is_active=is_active,
            )

        except ObjectDoesNotExist:
            logger.error(
                "vendor does not exist",
                vendor_code=vendor_code,
                company_name=company_name,
                is_active=is_active,
            )
            raise VendorException("vendor does not exist")
        except Exception as e:
            logger.error(
                "vendor exception in update api",
                vendor_code=vendor_code,
                company_name=company_name,
                is_active=is_active,
                error=str(e),
            )
            return Response({"message": "Unable to toggle vendor status"}, status=HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({"message": "status updated"}, status=HTTP_200_OK)


class VendorListV2Api(BaseApi, ListModelMixin):
    class OutputSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            ref_name = "VendorListV2ApiOutput"
            fields = ("id", "name", "code", "categories", "location")
            output_hash_id_fields = ("id",)

    serializer_class = OutputSerializer
    filterset_class = VendorListFilter
    filter_serializer_class = SearchTextFilterSerializer

    def get_queryset(self):
        org_from_id = self.get_organization_id()
        outgoing_org_list: QuerySet = outgoing_org_list_fetch(org_from_id=org_from_id)
        return outgoing_org_list

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_list_2",
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)
