from phonenumber_field import serializerfields
from rest_framework import serializers

from common.serializers import BaseModelSerializer
from vendor.data.models import Vendor, VendorCategory


class VendorCategorySerializer(BaseModelSerializer):
    class Meta:
        fields = ("id", "name", "color_code")
        model = VendorCategory


class VendorModelSerializer(BaseModelSerializer):
    name = serializers.CharField(source="organization.name")
    categories = VendorCategorySerializer(many=True, source="category")

    class Meta:
        model = Vendor
        fields = "__all__"


class VendorPOCSerializer(serializers.Serializer):
    first_name = serializers.CharField(min_length=1, max_length=50, required=False, allow_null=True, allow_blank=True)
    last_name = serializers.CharField(min_length=1, max_length=50, required=False, allow_null=True, allow_blank=True)
    email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)
    # example mobile = '+919721628195'
    mobile = serializerfields.PhoneNumberField(required=False, allow_null=True, allow_blank=True)


class VendorCreateSerializer(serializers.Serializer):
    vendor_id = serializers.CharField(min_length=1, max_length=20, allow_null=False, required=True)
    trade_name = serializers.CharField(min_length=1, max_length=250, allow_null=False, required=True)
    gstin = serializers.CharField(min_length=1, max_length=20, required=False, allow_null=True)
    vendor_type = serializers.CharField(min_length=1, max_length=30, required=False)
    poc = VendorPOCSerializer(allow_null=True, many=False)
    pmc_name = serializers.CharField(min_length=1, max_length=250, allow_null=True, required=False)
    auth_token = serializers.CharField()
    pan_number = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VMSVendorCreateSerializer"
