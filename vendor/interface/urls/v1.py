from django.urls import include, path
from rest_framework.routers import Default<PERSON>outer

# TODO: dependency break. Remove this once vendor list for order api is available
from order.interface.apis.order_apis import VendorListApi
from vendor.interface import views
from vendor.interface.apis import ChangeVendorStatus, VendorCreateApi, VendorSearchApi

router = DefaultRouter()
router.register("", views.VendorView)


urlpatterns = [
    path("", include(router.urls)),
    path("search/", VendorSearchApi.as_view(), name="vendor-search"),
    path("add-new-vendor/", VendorCreateApi.as_view(), name="vendor-create"),
    path("update-vendor-status/", ChangeVendorStatus.as_view()),
    path("list/", VendorListApi.as_view(), name="vendor-list"),
    path("config/", include("vendorv2.interface.urls.config_urls"), name="vendor-config"),
]
