from rest_framework import mixins, viewsets
from rest_framework.filters import <PERSON>Filter

from core.models import Organization
from core.serializers import OrganizationSerializer


# TODO: add permission
class VendorView(mixins.ListModelMixin, viewsets.GenericViewSet):
    permission_classes = []
    pagination_class = None
    # TODO: can we create a proxy model or manager for is_vendor=True
    queryset = Organization.objects.filter(is_vendor=True).order_by("name")
    serializer_class = OrganizationSerializer
    filter_backends = [SearchFilter]
    search_fields = ["name"]
