FROM python:3.10-slim-buster


ARG INSTALL_DEV=true
ENV PYTHONPATH=/code


RUN apt-get -y update \
	&& apt-get -y install curl wget libxrender1 libxext6 fontconfig libjpeg62-turbo xfonts-base xfonts-75dpi ffmpeg build-essential \
	&& wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.5/wkhtmltox_0.12.5-1.buster_amd64.deb \
	&& dpkg --install wkhtmltox_0.12.5-1.buster_amd64.deb \
	&& apt-get -y install gdal-bin \
	&& curl -sSL https://install.python-poetry.org | POETRY_HOME=/opt/poetry python \
	&& cd /usr/local/bin \
	&& ln -s /opt/poetry/bin/poetry \
	&& poetry config virtualenvs.create false


WORKDIR /code

# Copy poetry.lock* in case it doesn't exist in the repo
COPY pyproject.toml poetry.lock ./

# Allow installing dev dependencies to run tests
ARG INSTALL_DEV=true

# RUN poetry install -E psycopg2 -E gdal --no-dev
RUN bash -c "if [ $INSTALL_DEV == 'true' ] ; then poetry install --no-root ; else poetry install --no-root --no-dev ; fi"

COPY rollingbanners/cython_hashids /code/temp

RUN bash -c "cd temp && python setup.py build_ext --inplace"

COPY . .

RUN bash -c "rm -rf rollingbanners/cython_hashids && mv /code/temp rollingbanners/cython_hashids"


CMD ["gunicorn", "rollingbanners.wsgi:application", "--config", "rollingbanners/gunicorn.py"]
