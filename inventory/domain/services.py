import datetime
import decimal
from typing import List, Optional

import structlog
from pydantic import validate_call

from authorization.domain.constants import Permissions
from common.choices import ElementTypeContext
from common.entities import ObjectStatus
from common.exceptions import BaseValidationError
from common.utils import should_handle_backward_compatibility
from core.models import User
from core.utils import get_relative_path
from inventory.data.models import InventoryBatch
from inventory.data.selectors import (
    get_batch_items_by_ids,
    inventory_batch_selector,
    is_a_grn_batch,
)
from inventory.domain.abstract_repositories import (
    InventoryBatchAbstractRepository,
    InventoryConsumptionBatchAbstractRepository,
    InventoryStockTransactionAbstractRepository,
    InventoryTransferBatchAbstractRepository,
    InventoryWorkProgressAbstractRepository,
)
from inventory.domain.entities import (
    BatchItemQuantityUpdateEntity,
    ConsumptionBatchItemBaseEntity,
    InventoryAdhocBatchCreateEntity,
    InventoryBatchItemCreateEntity,
    InventoryBatchUpdateEntity,
    InventoryConsumptionBatchCreateEntity,
    InventoryExpenseBatchCreateEntity,
    InventoryOrderBatchCreateEntity,
    InventoryStockBaseItemEntity,
    InventoryStockItemEntity,
    InventoryStockItemTransactionBaseEntity,
    InventoryStockItemTransactionEntity,
    InventoryStockItemUpdateEntity,
    InventoryTransferBaseEntity,
    InventoryTransferBatchCreateEntity,
    InventoryTransferBatchEntity,
    StockItemTransactionEntity,
    TransferInventoryBatchCreateEntity,
)
from inventory.domain.enums import (
    InventoryActionEnum,
    InventoryTransactionType,
    StockBatchType,
    TransferBatchActions,
    TransferStatus,
)
from inventory.domain.exceptions import InventoryTransferBatchException
from inventory.domain.helpers import get_read_only_fields
from inventory.domain.triggers import (
    trigger_stock_transfer_batch_action,
    trigger_stock_transfer_batch_created,
)
from project.domain.helpers import ProjectPermissionHelper

logger = structlog.get_logger(__name__)


class InventoryStockService:
    class StockItemInvalidNewQuantityException(BaseValidationError):
        pass

    class InvalidCreditStockTransactionException(BaseValidationError):
        pass

    class InvalidDebitStockTransactionException(BaseValidationError):
        pass

    class StockItemTransactionCreateError(BaseValidationError):
        pass

    class StockItemCreateError(BaseValidationError):
        pass

    def __init__(
        self,
        repo: InventoryStockTransactionAbstractRepository,
        user_id: int,  # current user
        project_id: int,
        organization_id: int,
    ):
        self.user_id = user_id
        self.repo = repo
        self.project_id = project_id
        self.organization_id = organization_id

    def get_stock_items_using_ids(self, id_list: list[int]) -> dict[int, InventoryStockItemEntity]:
        return self.repo.get_stock_items_in_bulk(id_list=id_list)

    @validate_call
    def create_credit_transaction(self, data: StockItemTransactionEntity) -> InventoryStockItemTransactionBaseEntity:
        if data.transaction_type not in [InventoryTransactionType.INVENTORY, InventoryTransactionType.STOCK_TRANSFER]:
            logger.info("Invalid credit stock transaction_type", transaction_type=data.transaction_type)
            raise self.InvalidCreditStockTransactionException("Invalid credit stock transaction_type")
        inventory_batch_id = None
        stock_transfer_batch_id = None
        if data.transaction_type == InventoryTransactionType.STOCK_TRANSFER:
            stock_transfer_batch_id = data.batch_id
        else:
            inventory_batch_id = data.batch_id

        if data.quantity < data.old_quantity or data.quantity < decimal.Decimal(0):
            logger.info(
                "Invalid new credit quantity",
                transaction_type=data.transaction_type,
                batch_id=data.batch_id,
                quantity=data.quantity,
                old_quantity=data.old_quantity,
                reason=data.reason,
            )
            raise self.StockItemInvalidNewQuantityException("Invalid new credit quantity")

        entity = InventoryStockItemTransactionBaseEntity(
            item_id=data.item_id,
            balance_quantity=data.quantity,
            credit_quantity=data.quantity - data.old_quantity,
            debit_quantity=decimal.Decimal(0),
            reason=data.reason,
            consumption_batch_id=None,
            transfer_batch_id=stock_transfer_batch_id,
            inventory_batch_id=inventory_batch_id,
            created_by_id=self.user_id,
        )
        logger.info("credit transaction entity created")
        return entity

    @validate_call
    def create_debit_transaction(self, data: StockItemTransactionEntity) -> InventoryStockItemTransactionBaseEntity:
        consumption_batch_id = None
        stock_transfer_batch_id = None
        inventory_batch_id = None
        if data.transaction_type == InventoryTransactionType.CONSUMPTION:
            consumption_batch_id = data.batch_id
        elif data.transaction_type == InventoryTransactionType.STOCK_TRANSFER:
            stock_transfer_batch_id = data.batch_id
        elif data.transaction_type == InventoryTransactionType.INVENTORY:
            inventory_batch_id = data.batch_id
        elif data.transaction_type == InventoryTransactionType.GRN_DELETE:
            inventory_batch_id = data.batch_id
        if data.quantity > data.old_quantity or data.quantity < decimal.Decimal(0):
            logger.info(
                "Invalid new debit quantity",
                transaction_type=data.transaction_type,
                quantity=data.quantity,
                old_quantity=data.old_quantity,
                item_id=data.item_id,
            )
            raise self.StockItemInvalidNewQuantityException("Invalid new debit quantity")

        entity = InventoryStockItemTransactionBaseEntity(
            item_id=data.item_id,
            balance_quantity=data.quantity,
            credit_quantity=decimal.Decimal(0),
            debit_quantity=data.old_quantity - data.quantity,
            reason=data.reason,
            consumption_batch_id=consumption_batch_id,
            transfer_batch_id=stock_transfer_batch_id,
            inventory_batch_id=inventory_batch_id,
            created_by_id=self.user_id,
        )
        logger.info("debit transaction entity created")
        return entity

    @validate_call
    def create_stock_transaction_data(
        self, data: StockItemTransactionEntity
    ) -> InventoryStockItemTransactionBaseEntity:
        if data.quantity < data.old_quantity:
            transaction_entity = self.create_debit_transaction(data)
            return transaction_entity

        elif data.quantity > data.old_quantity:
            transaction_entity = self.create_credit_transaction(data)
            return transaction_entity

        logger.info("new and old quantity are same")
        raise self.StockItemInvalidNewQuantityException("new and old quantity are same")

    @validate_call
    def prepare_stock_transaction_entity(
        self,
        old_quantity: decimal.Decimal,
        stock_item_id: int,
        batch_id: int,
        transaction_type: InventoryTransactionType,
        quantity: decimal.Decimal,
        reason: str,
    ):
        return StockItemTransactionEntity(
            old_quantity=old_quantity,
            item_id=stock_item_id,
            batch_id=batch_id,
            transaction_type=transaction_type,
            quantity=quantity,
            reason=reason,
        )

    @validate_call
    def prepare_stock_item_entity(self, data: InventoryBatchItemCreateEntity):
        return InventoryStockBaseItemEntity(
            name=data.name,
            rate=data.rate,
            category_id=data.category_id,
            item_type_id=data.item_type_id,
            uom=data.uom,
            tax_percent=data.tax_percent,
            total_quantity=data.quantity,
            available_quantity=data.quantity,
            preview_file=data.preview_file,
            code=data.code,
        )

    @validate_call
    def create_transaction_for_new_stock_item(
        self,
        batch_id,
        transaction_type: InventoryTransactionType,
        reason: str,
        quantity: decimal.Decimal,
        stock_item: InventoryStockItemEntity,
    ):
        logger.info("transaction create entity for new stock item")
        transaction_entity = self.prepare_stock_transaction_entity(
            old_quantity=decimal.Decimal(0),
            stock_item_id=stock_item.id,
            batch_id=batch_id,
            transaction_type=transaction_type,
            reason=reason,
            quantity=quantity,
        )
        item_transaction = self.create_stock_transaction_data(data=transaction_entity)
        return item_transaction

    @validate_call
    def get_items_already_in_stock_with_codes(self, items: list[InventoryBatchItemCreateEntity]):
        if not items:
            return []
        return self.repo.get_items_already_in_stock_with_codes(
            project_id=self.project_id, organization_id=self.organization_id, items=items
        )

    @validate_call
    def get_items_already_in_stock_items_without_codes(self, items: list[InventoryBatchItemCreateEntity]):
        if not items:
            return []
        return self.repo.get_items_already_in_stock_without_codes(
            items=items,
            project_id=self.project_id,
            organization_id=self.organization_id,
        )

    @validate_call
    def segregate_present_items(
        self,
        batch_item_entities: list[InventoryBatchItemCreateEntity],
    ):
        present_data = []  # list of tuple - (batch_item, stock_item_id)
        not_present_data = []  # list of batch items to create fresh stock
        for batch_item in batch_item_entities:
            if batch_item.code and "/" in batch_item.code:
                batch_item.code = batch_item.code.split("/")[0]

        batch_item_with_codes = [item for item in batch_item_entities if item.code]
        stock_items_with_code = self.get_items_already_in_stock_with_codes(items=batch_item_with_codes)
        batch_custom_items = [item for item in batch_item_entities if not item.code]
        items_without_code = self.get_items_already_in_stock_items_without_codes(items=batch_custom_items)

        existing_item_with_codes = {(item.code, item.rate): item for item in stock_items_with_code if item.code}

        existing_item_without_codes = {
            (
                item.name,
                item.rate,
                item.category_id,
                item.item_type_id,
                item.uom,
                item.tax_percent,
            ): item
            for item in items_without_code
            if not item.code
        }

        for batch_item in batch_item_with_codes:
            if (batch_item.code, batch_item.rate) in existing_item_with_codes.keys():
                present_data.append((batch_item, existing_item_with_codes[(batch_item.code, batch_item.rate)]))
            else:
                not_present_data.append(batch_item)
        for batch_item in batch_custom_items:
            if (
                batch_item.name,
                batch_item.rate,
                batch_item.category_id,
                batch_item.item_type_id,
                batch_item.uom,
                batch_item.tax_percent,
            ) in existing_item_without_codes.keys():
                present_data.append(
                    (
                        batch_item,
                        existing_item_without_codes[
                            (
                                batch_item.name,
                                batch_item.rate,
                                batch_item.category_id,
                                batch_item.item_type_id,
                                batch_item.uom,
                                batch_item.tax_percent,
                            )
                        ],
                    )
                )
            else:
                not_present_data.append(batch_item)
        return present_data, not_present_data

    @validate_call
    def update_stock_quantity(
        self,
        stock_update_entity: InventoryStockItemUpdateEntity,
        batch_id: int,
        transaction_type: InventoryTransactionType,
        stock_item: InventoryStockItemEntity,
    ) -> (InventoryStockItemEntity, InventoryStockItemTransactionBaseEntity):
        old_quantity = stock_item.total_quantity
        stock_item.total_quantity = (
            stock_item.total_quantity + stock_update_entity.quantity_change
        )  # deriving final quantity from quantity change
        stock_item.available_quantity = stock_item.available_quantity + stock_update_entity.quantity_change
        logger.info(
            "updating stock item quantity",
            stock_item_id=stock_item.id,
            quantity=stock_item.total_quantity,
            old_quantity=old_quantity,
        )
        transaction_entity = self.prepare_stock_transaction_entity(
            old_quantity=old_quantity,
            stock_item_id=stock_item.id,
            batch_id=batch_id,
            transaction_type=transaction_type,
            reason=stock_update_entity.reason,
            quantity=old_quantity + stock_update_entity.quantity_change,
        )
        item_transaction = self.create_stock_transaction_data(data=transaction_entity)
        return stock_item, item_transaction

    @validate_call
    def save_transactions_data(
        self, data_list: list[InventoryStockItemTransactionBaseEntity]
    ) -> list[InventoryStockItemTransactionEntity]:
        try:
            logger.info("saving stock item transactions")
            return self.repo.save_transaction_data_in_bulk(data_list=data_list, user_id=self.user_id)
        except self.repo.InventoryStockItemTransactionModelValidationException as e:
            logger.error("Unable to create stock item transactions", str(e))
            raise self.StockItemTransactionCreateError(e)

    @validate_call
    def save_stock_items(
        self, data_list: List[InventoryStockItemEntity], transaction_type: InventoryTransactionType
    ) -> None:
        logger.info("saving stock items")
        return self.repo.save_stock_items(data_list=data_list, transaction_type=transaction_type)

    @validate_call
    def update_stock_items(
        self,
        data_items: list[InventoryStockItemUpdateEntity],
        batch_id: int,
        transaction_type: InventoryTransactionType,
    ) -> dict[int, int]:
        logger.info("updating stock items")
        stock_items = self.repo.get_stock_items_in_bulk(id_list=[item.id for item in data_items])
        stock_items_entities = []
        stock_transactions_entities = []
        for update_entity in data_items:
            if update_entity.quantity_change != decimal.Decimal(0):
                stock_item, item_transaction = self.update_stock_quantity(
                    stock_update_entity=update_entity,
                    batch_id=batch_id,
                    transaction_type=transaction_type,
                    stock_item=stock_items[update_entity.id],
                )
                # update meta data here also
                if transaction_type == InventoryTransactionType.INVENTORY:
                    self.update_stock_item_metadata(stock_item=stock_item, data=update_entity)
                stock_items_entities.append(stock_item)
                stock_transactions_entities.append(item_transaction)
            else:
                # possible only in case of transaction_type InventoryTransactionType.INVENTORY
                # add update here to update other fields except qty of stock item call new method
                if transaction_type == InventoryTransactionType.INVENTORY:
                    stock_item = stock_items[update_entity.id]
                    self.update_stock_item_metadata(stock_item=stock_item, data=update_entity)
                    stock_items_entities.append(stock_item)
        transactions_data = self.save_transactions_data(data_list=stock_transactions_entities)
        self.save_stock_items(data_list=stock_items_entities, transaction_type=transaction_type)
        logger.info("updated stock items")
        return {data.item_id: data.id for data in transactions_data}

    def update_stock_item_metadata(self, stock_item, data: InventoryStockItemUpdateEntity):
        stock_item.name = data.name
        stock_item.category_id = data.category_id
        stock_item.item_type_id = data.item_type_id
        stock_item.uom = data.uom
        stock_item.rate = data.rate
        stock_item.tax_percent = data.tax_percent

    @validate_call
    def process_stock_items_creation(
        self,
        batch_id,
        transaction_type: InventoryTransactionType,
        reason: str,
        entities: list[InventoryBatchItemCreateEntity],
    ):
        stock_items_entities = []
        transaction_entities = []
        items_already_present, items_not_present = self.segregate_present_items(batch_item_entities=entities)
        batch_items_stock_id_list = []
        if items_already_present:
            for batch_item, stock_item in items_already_present:
                item = InventoryStockItemUpdateEntity(
                    id=stock_item.id, quantity_change=batch_item.quantity, reason=reason
                )
                stock_item, item_transaction = self.update_stock_quantity(
                    stock_update_entity=item,
                    batch_id=batch_id,
                    transaction_type=transaction_type,
                    stock_item=stock_item,
                )
                stock_items_entities.append(stock_item)
                batch_items_stock_id_list.append((batch_item, stock_item.id))
                transaction_entities.append(item_transaction)
        if items_not_present:
            stock_item_entities = [self.prepare_stock_item_entity(item) for item in items_not_present]
            try:
                logger.info("creating stock items")
                items = self.repo.create_stock_items_in_bulk(
                    items=stock_item_entities,
                    project_id=self.project_id,
                    organization_id=self.organization_id,
                    created_by_id=self.user_id,
                )
            except self.repo.InventoryStockItemModelValidationError as e:
                raise self.StockItemCreateError(e) from e
            except self.repo.InventoryStockIntegrityError as e:
                raise self.StockItemCreateError(e) from e

            for item in items:
                item_transaction = self.create_transaction_for_new_stock_item(
                    batch_id=batch_id,
                    transaction_type=transaction_type,
                    reason=reason,
                    stock_item=item,
                    quantity=item.total_quantity,
                )
                transaction_entities.append(item_transaction)
            for stock_item, batch_item in zip(items, items_not_present):
                batch_items_stock_id_list.append((batch_item, stock_item.id))
        self.save_transactions_data(data_list=transaction_entities)
        self.save_stock_items(data_list=stock_items_entities, transaction_type=transaction_type)
        return batch_items_stock_id_list

    @validate_call
    def update_available_quantity(self, stock_items: list[InventoryStockItemEntity]):
        logger.info("updating available quantity of stock items")
        return self.repo.update_available_quantity_of_items(stock_items=stock_items)


class InventoryBatchService:
    class NoBatchItemsToCreateException(BaseValidationError):
        pass

    class InvalidOrderBatchItemException(BaseValidationError):
        pass

    class InvalidOrderBatchQuantity(BaseValidationError):
        pass

    class BatchUpdateNotAllowed(BaseValidationError):
        pass

    class BatchDeleteNotAllowed(BaseValidationError):
        pass

    class InventoryBatchItemCreateError(BaseValidationError):
        pass

    class InventoryBatchItemTransactionCreateError(BaseValidationError):
        pass

    class InventoryBatchNotFoundException(BaseValidationError):
        pass

    def __init__(
        self,
        inventory_batch_repository: InventoryBatchAbstractRepository,
        stock_transaction_service: InventoryStockService,
        project_id: int,
        organization_id: int,
        user_id: int,
    ):
        self.inventory_batch_repository = inventory_batch_repository
        self.stock_transaction_service = stock_transaction_service
        self.project_id = project_id
        self.organization_id = organization_id
        self.user_id = user_id

    @validate_call
    def create_batch_items(
        self, batch_id: int, batch_items_and_stock_ids: list[tuple[InventoryBatchItemCreateEntity, int]]
    ):
        try:
            logger.info("creating batch items")
            items = self.inventory_batch_repository.create_items_in_bulk(
                batch_id=batch_id, created_by_id=self.user_id, items=batch_items_and_stock_ids
            )
        except self.inventory_batch_repository.InventoryBatchModelValidationError as e:
            raise self.InventoryBatchItemCreateError(e)
        logger.info("created batch items")
        return items

    def process_items_create(
        self,
        transaction_type: InventoryTransactionType,
        batch_id: int,
        reason: str,
        data: list[InventoryBatchItemCreateEntity],
    ):
        if not data:
            raise self.NoBatchItemsToCreateException("No batch items to create")
        try:
            batch_items_and_stock_ids = self.stock_transaction_service.process_stock_items_creation(
                batch_id=batch_id,
                reason=reason,
                entities=data,
                transaction_type=transaction_type,
            )
        except self.stock_transaction_service.StockItemCreateError as e:
            logger.info("error in creating stock item", message=str(e))
            raise self.InventoryBatchItemCreateError(e)

        except self.stock_transaction_service.StockItemTransactionCreateError as e:
            logger.info("error in creating stock item transaction", str(e))
            raise self.InventoryBatchItemTransactionCreateError(e)

        self.create_batch_items(batch_id=batch_id, batch_items_and_stock_ids=batch_items_and_stock_ids)
        return True

    def prepare_stock_item_update_entity(
        self,
        stock_item_id: int,
        quantity_change: decimal.Decimal,
        reason: str,
        name: str,
        category_id: str,
        item_type_id: Optional[str],
        uom: str,
        rate: decimal.Decimal,
        tax_percent: decimal.Decimal,
        quantity: decimal.Decimal,
    ) -> InventoryStockItemUpdateEntity:
        return InventoryStockItemUpdateEntity(
            id=stock_item_id,
            quantity_change=quantity_change,
            reason=reason,
            name=name,
            category_id=category_id,
            item_type_id=item_type_id,
            uom=uom,
            rate=rate,
            tax_percent=tax_percent,
            quantity=quantity,
        )

    def process_batch_items_update(
        self,
        batch_id: int,
        items: list[BatchItemQuantityUpdateEntity],
        reason: str,
        transaction_type: InventoryTransactionType,
    ):
        batch_items = self.inventory_batch_repository.get_batch_items_in_bulk(
            batch_id=batch_id, item_ids=[item.id for item in items]
        )
        stock_item_update_entities = []
        for item in items:
            quantity_change = item.quantity - batch_items.get(item.id).quantity
            stock_item_update_entity = self.prepare_stock_item_update_entity(
                stock_item_id=batch_items.get(item.id).stock_item_id,
                quantity_change=quantity_change,
                reason=reason,
                name=item.name,
                category_id=item.category_id,
                item_type_id=item.item_type_id,
                uom=item.uom,
                rate=item.rate,
                tax_percent=item.tax_percent,
                quantity=item.quantity,
            )
            stock_item_update_entities.append(stock_item_update_entity)

        self.stock_transaction_service.update_stock_items(
            data_items=stock_item_update_entities,
            batch_id=batch_id,
            transaction_type=transaction_type,
        )
        self.inventory_batch_repository.update_batch_items(data=items, transaction_type=transaction_type)

    @validate_call
    def validate_available_quantity(
        self,
        items: list[InventoryBatchItemCreateEntity],
        item_receivable_quantity_dict: dict[int, decimal.Decimal],
    ):
        logger.info("validating available quantity of order batch items")
        for item in items:
            if item.quantity > item_receivable_quantity_dict[item.context_id]:
                raise self.InvalidOrderBatchQuantity("Invalid batch item quantity")

    @validate_call
    def validate_order_batch_items(self, items: list[InventoryBatchItemCreateEntity], order_id: int):
        for item in items:
            if item.context != ElementTypeContext.ORDER_ELEMENT:
                raise self.InvalidOrderBatchItemException("Invalid order batch item")

        order_item_receivable_quantity_dict = self.inventory_batch_repository.get_order_elements_receivable_quantity(
            items=items, order_id=order_id
        )

        if len(items) != len(list(order_item_receivable_quantity_dict.keys())):
            raise self.InvalidOrderBatchItemException("Invalid order batch item")

        self.validate_available_quantity(items=items, item_receivable_quantity_dict=order_item_receivable_quantity_dict)

    # TODO: Logic To be updated in upcoming Task
    # @validate_call
    # def validate_order_batch_item_for_update(
    #     self, batch_id: int, items: list[BatchItemQuantityUpdateEntity], order_id: int
    # ):
    #     items = self.inventory_batch_repository.get_batch_items_in_bulk(
    #         batch_id=batch_id, item_ids=[item.id for item in items]
    #     )
    #     order_item_receivable_quantity_dict = self.inventory_batch_repository.get_order_elements_receivable_quantity(
    #         items=list(items.values()), order_id=order_id
    #     )
    #     self.validate_available_quantity(items=items,
    #     item_receivable_quantity_dict=order_item_receivable_quantity_dict)

    @validate_call
    def validate_expense_batch_items(self, items: list[InventoryBatchItemCreateEntity], expense_id: int):
        for item in items:
            if item.context != ElementTypeContext.EXPENSE_ITEM:
                raise self.InvalidOrderBatchItemException("Invalid expense batch item")

        expense_item_receivable_quantity_dict = self.inventory_batch_repository.get_expense_items_receivable_quantity(
            items=items, expense_id=expense_id
        )

        if len(items) != len(list(expense_item_receivable_quantity_dict.keys())):
            raise self.InvalidOrderBatchItemException("Invalid expense batch item")

        self.validate_available_quantity(
            items=items, item_receivable_quantity_dict=expense_item_receivable_quantity_dict
        )

    # TODO: Logic To be updated in upcoming Task
    # @validate_call
    # def validate_expense_batch_item_for_update(
    #     self, batch_id: int, items: list[BatchItemQuantityUpdateEntity], expense_id: int
    # ):
    #     items = self.inventory_batch_repository.get_batch_items_in_bulk(
    #         batch_id=batch_id, item_ids=[item.id for item in items]
    #     )
    #     expense_item_receivable_quantity_dict = self.inventory_batch_repository.get_expense_items_receivable_quantity(
    #         items=list(items.values()), expense_id=expense_id
    #     )
    #     self.validate_available_quantity(
    #         items=items, item_receivable_quantity_dict=expense_item_receivable_quantity_dict
    #     )

    @validate_call
    def create_adhoc_batch(self, data: InventoryAdhocBatchCreateEntity):
        logger.info("adhoc batch creation started")
        batch = self.inventory_batch_repository.create_adhoc_batch(
            data,
            project_id=self.project_id,
            organization_id=self.organization_id,
            created_by_id=self.user_id,
            received_by_id=self.user_id,
            source=StockBatchType.ADHOC.value,
        )
        self.process_items_create(
            batch_id=batch.id,
            reason=StockBatchType.ADHOC.value,
            data=data.items,
            transaction_type=InventoryTransactionType.INVENTORY.value,
        )
        logger.info("adhoc batch created")
        return batch

    @validate_call
    def create_transfer_inventory_batch(
        self, inventory_created_by_id: int, received_by_id: int, data: TransferInventoryBatchCreateEntity
    ):
        logger.info("stock transfer batch creation started")
        batch = self.inventory_batch_repository.create_adhoc_batch(
            data=data,
            project_id=self.project_id,
            organization_id=self.organization_id,
            received_by_id=received_by_id,
            created_by_id=inventory_created_by_id,
            source=StockBatchType.STOCK_TRANSFER.value,
        )

        self.process_items_create(
            batch_id=batch.id,
            reason="Stock Transfer",
            data=data.items,
            transaction_type=InventoryTransactionType.INVENTORY.value,
        )
        logger.info("stock transfer batch created")
        return batch

    def create_order_batch(self, data: InventoryOrderBatchCreateEntity):
        logger.info("order batch creation started")
        self.validate_order_batch_items(items=data.items, order_id=data.order_id)
        batch = self.inventory_batch_repository.create_order_batch(
            data,
            project_id=self.project_id,
            organization_id=self.organization_id,
            received_by_id=self.user_id,
            created_by_id=self.user_id,
        )
        self.process_items_create(
            batch_id=batch.id,
            reason=StockBatchType.ORDER.value,
            data=data.items,
            transaction_type=InventoryTransactionType.INVENTORY.value,
        )
        logger.info("order batch created")
        return batch

    def create_expense_batch(self, data: InventoryExpenseBatchCreateEntity):
        logger.info("expense batch creation started")
        self.validate_expense_batch_items(items=data.items, expense_id=data.expense_id)
        batch = self.inventory_batch_repository.create_expense_batch(
            data=data,
            project_id=self.project_id,
            organization_id=self.organization_id,
            created_by_id=self.user_id,
            received_by_id=self.user_id,
        )
        self.process_items_create(
            batch_id=batch.id,
            reason=StockBatchType.OTHER_EXPENSE.value,
            data=data.items,
            transaction_type=InventoryTransactionType.INVENTORY.value,
        )
        logger.info("expense batch created")
        return batch

    def validate_updated_quantities(self, updated_items: list[BatchItemQuantityUpdateEntity]):
        if not updated_items:
            return
        item_id_map = {item.id: item for item in updated_items if item.id is not None}
        db_items = get_batch_items_by_ids(item_ids=item_id_map.keys())

        for db_item in db_items:
            payload_item = item_id_map.get(db_item.id)
            new_quantity = payload_item.quantity

            if new_quantity < db_item.used_quantity:
                raise self.InvalidOrderBatchQuantity(
                    "Entered value exceeds the editable limit as some stock has already been consumed."
                )

    def _validate_batch_source(self, batch: InventoryBatch):
        allowed_sources = {
            StockBatchType.ADHOC.value,
            StockBatchType.ORDER.value,
            StockBatchType.OTHER_EXPENSE.value,
        }
        if batch.source not in allowed_sources:
            raise self.BatchUpdateNotAllowed("Batch update not allowed")

    def _validate_batch_action_permissions(self, batch: InventoryBatch):
        permitted_actions = self.get_inventory_batch_actions(user_id=self.user_id, batch=batch)
        if not any(
            action in permitted_actions for action in [InventoryActionEnum.EDIT.value, InventoryActionEnum.DELETE.value]
        ):
            raise self.BatchUpdateNotAllowed("Batch update not allowed")

    def _validate_read_only_item_fields(
        self, batch: InventoryBatch, updated_items: list[BatchItemQuantityUpdateEntity]
    ):
        updated_items = [item for item in updated_items if item.object_status == ObjectStatus.UPDATE.value]

        item_id_map = {item.id: item for item in updated_items if item.id is not None}
        if not item_id_map:
            return

        for db_item in batch.items.all():
            payload_item = item_id_map.get(db_item.id)
            if not payload_item:
                continue

        self._raise_if_read_only_field_updated(db_item, payload_item)

    def _raise_if_read_only_field_updated(self, db_item, payload_item):
        read_only_fields = set(db_item.read_only_fields)
        # TODO add uom here
        check_fields = ["name", "category_id", "item_type_id", "rate", "tax_percent", "quantity"]

        for field in check_fields:
            if field in read_only_fields:
                updated_value = getattr(payload_item, field, None)
                original_value = getattr(db_item, field, None)

                if updated_value != original_value:
                    raise self.BatchUpdateNotAllowed(
                        f"Field '{field}' is read-only and cannot be updated for item ID {db_item.id}. "
                        f"Updated value: {updated_value}, original: {original_value}."
                    )

    def validate_if_batch_update_allowed(
        self, batch: InventoryBatch, updated_items: list[BatchItemQuantityUpdateEntity]
    ):
        if batch.source not in [
            StockBatchType.ADHOC.value,
            StockBatchType.ORDER.value,
            StockBatchType.OTHER_EXPENSE.value,
        ]:
            raise self.BatchUpdateNotAllowed("Batch update not allowed")

        permitted_actions = self.get_inventory_batch_actions(user_id=self.user_id, batch=batch)
        if not any(
            action in permitted_actions for action in [InventoryActionEnum.EDIT.value, InventoryActionEnum.DELETE.value]
        ):
            raise self.BatchUpdateNotAllowed("Batch update not allowed")

        # check checks here id 'i.e' no read only fields of any item are updated it has read_only_fields in each item
        item_id_map = {item.id: item for item in updated_items if item.id is not None}
        if not item_id_map:
            return  # Nothing to validate

        for db_item in batch.items.all():  # or batch.items if it's a prefetched list
            payload_item = item_id_map.get(db_item.id)
            if not payload_item:
                continue  # Skip items that are not part of the update payload

            read_only_fields = set(db_item.read_only_fields)

            # TODO add uom here
            for field in ["name", "category_id", "item_type_id", "rate", "tax_percent", "quantity"]:
                updated_value = getattr(payload_item, field, None)
                original_value = getattr(db_item, field, None)

                if field in read_only_fields and updated_value != original_value:
                    raise self.BatchUpdateNotAllowed(
                        f"Field '{field}' is read-only and cannot be updated for item ID "
                        f"{db_item.id} updated val {updated_value}, "
                        f"original val {original_value}."
                    )

    def get_reason_for_batch_update(self, batch_source: str):
        return "GRN Revised"

    def update_batch(
        self, user_id: int, batch_id: int, data: InventoryBatchUpdateEntity, org_id: int, source: str, app_version: str
    ):
        batch = self.get_inventory_batch_details(user_id, batch_id, org_id)
        # handle backward compatibility for app remove it after force update
        # as app is not sending complete items data in old version
        logger.info("checking for backward compatibility", app_version=app_version, source=source)
        if should_handle_backward_compatibility(source, app_version):
            logger.info("prefilling missing data for backward compatibility")
            self.prefill_missing_data(data=data, batch=batch)

        self._validate_batch_source(batch=batch)
        self._validate_batch_action_permissions(batch=batch)
        self._validate_read_only_item_fields(batch=batch, updated_items=data.items)
        self.validate_updated_quantities(data.items)

        self.inventory_batch_repository.update_batch_data(batch=batch, data=data, updated_by_id=self.user_id)
        reason = self.get_reason_for_batch_update(batch_source=batch.source)

        updated_items = [item for item in data.items if item.object_status == ObjectStatus.UPDATE.value]

        self.process_batch_items_update(
            batch_id=batch_id,
            items=updated_items,
            transaction_type=InventoryTransactionType.INVENTORY,
            reason=reason,
        )
        return True

    def prefill_missing_data(self, data: InventoryBatchUpdateEntity, batch: InventoryBatch):
        existing_items = batch.items.all()  # Prefetch related items if not already done
        for item in data.items:
            existing_item = existing_items.get(id=item.id)
            # existing_item = existing_items.get(item.id)
            if not existing_item:
                continue

            if item.category_id is None:
                item.category_id = existing_item.category_id

            if item.item_type_id is None:
                item.item_type_id = existing_item.item_type_id

            if item.rate is None:
                item.rate = existing_item.rate

            if item.tax_percent is None:
                item.tax_percent = existing_item.tax_percent

            if item.uom is None:
                item.uom = existing_item.uom.id

            if item.name is None:
                item.name = existing_item.name

            if item.object_status is None:
                item.object_status = ObjectStatus.UPDATE.value

    def update_used_quantity_for_items(
        self, stock_item_quantity_used_dict: {int, int}, transaction_data: dict[int, int], reason: str
    ):
        logger.info("updating used quantity for stock items")
        stock_items_batch_dict = self.inventory_batch_repository.get_available_batch_items_using_stock_ids(
            stock_ids=list(stock_item_quantity_used_dict.keys())
        )
        utilisation_history_data = []
        update_item_list = []
        for stock_id, batch_items in stock_items_batch_dict.items():
            quantity_used = stock_item_quantity_used_dict[stock_id]
            quantity_to_consume = quantity_used
            for item in batch_items:
                if quantity_to_consume == decimal.Decimal(0):
                    break
                elif item.quantity - item.used_quantity >= quantity_to_consume:
                    item.used_quantity += quantity_to_consume
                    quantity_to_consume = 0
                else:
                    quantity_to_consume -= item.quantity - item.used_quantity
                    item.used_quantity = item.quantity

                update_item_list.append(item)
                utilisation_history_data.append((transaction_data[stock_id], item.id, item.used_quantity))

        self.inventory_batch_repository.update_used_quantity(data=update_item_list)
        self.inventory_batch_repository.save_utilization_data(
            data=utilisation_history_data, reason=reason, created_by_id=self.user_id
        )
        logger.info("updated used quantity for stock items, with utilisation history data")

    def delete_grn(self, batch_id: int, user: User):
        if not ProjectPermissionHelper.has_permission(
            user=user,
            project_id=self.project_id,
            permission=Permissions.CAN_ACCESS_MATERIALS,
        ):
            raise self.BatchDeleteNotAllowed("You don't have permission to delete GRN.")

        # check if it's a grn batch
        is_grn = is_a_grn_batch(batch_id=batch_id)
        if not is_grn:
            raise self.BatchDeleteNotAllowed("Batch delete not allowed, not a grn batch.")

        # check if deletion is allowed
        qs = inventory_batch_selector()
        batch: InventoryBatch = qs.filter(id=batch_id).first()
        actions = self.get_inventory_batch_actions(user_id=self.user_id, batch=batch)

        if InventoryActionEnum.DELETE.value not in actions:
            raise self.BatchDeleteNotAllowed("Batch delete not allowed.")
        self.consume_all_items_of_inventory_batch(batch=batch)

        for item in batch.items.all():
            item.soft_delete(user_id=self.user_id, save=True)
        # soft delete grn batch
        batch.soft_delete(user_id=self.user_id, save=True)

    def consume_all_items_of_inventory_batch(self, batch: InventoryBatch):
        stock_item_update_entities = []
        stock_item_id_and_quantity_dict = {}
        for item in batch.items.all():
            quantity_change = decimal.Decimal(0) - item.quantity
            stock_item_update_entity = InventoryStockItemUpdateEntity(
                id=item.stock_item_id, quantity_change=quantity_change, reason=InventoryTransactionType.GRN_DELETE
            )
            stock_item_update_entities.append(stock_item_update_entity)
            stock_item_id_and_quantity_dict[item.stock_item_id] = item.quantity

        try:
            transaction_data = self.stock_transaction_service.update_stock_items(
                data_items=stock_item_update_entities,
                batch_id=batch.id,
                transaction_type=InventoryTransactionType.GRN_DELETE,
            )
        except self.stock_transaction_service.StockItemTransactionCreateError as e:
            logger.info("Error in creating stock transaction data", str(e))
            raise self.BatchDeleteNotAllowed(e)
        except self.stock_transaction_service.StockItemInvalidNewQuantityException as e:
            logger.info("Invalid quantity in stock transaction", str(e))
            raise self.BatchDeleteNotAllowed(e)
        except self.stock_transaction_service.InvalidDebitStockTransactionException as e:
            logger.info("Invalid quantity change in stock transaction", str(e))
            raise self.BatchDeleteNotAllowed(e)

        self.update_used_quantity_for_items(
            stock_item_quantity_used_dict=stock_item_id_and_quantity_dict,
            transaction_data=transaction_data,
            reason=InventoryTransactionType.GRN_DELETE.value,
        )

    def get_inventory_batch_details(self, user_id: int, batch_id: int, org_id: int) -> InventoryBatch:
        qs = inventory_batch_selector()

        if org_id is not None:
            qs = qs.annotate_comment_count(org_id=org_id)

        batch = qs.filter(id=batch_id).first()

        # check if batch has been soft delete then raise 404
        if batch is None:
            raise self.InventoryBatchNotFoundException("Inventory batch not found")
        batch.action = self.get_inventory_batch_actions(user_id=user_id, batch=batch)

        if not batch:
            return None

        self._attach_read_only_fields(batch)
        return batch

    def _attach_read_only_fields(self, batch: InventoryBatch):
        for item in batch.items.all():  # All annotated fields already available
            item.read_only_fields = get_read_only_fields(item)

    def get_inventory_batch_actions(self, user_id: int, batch: InventoryBatch) -> list[str]:
        actions = []
        all_fields_set = {"name", "category", "item_type", "uom", "rate", "tax_percent", "quantity"}

        is_stock_consumed_in_same_batch = any(item.consumed_quantity > 0 for item in batch.items.all())

        for item in batch.items.all():
            # Check if read_only_fields is already attached to item as an attribute
            read_only_fields = getattr(item, "read_only_fields", set())

            if set(read_only_fields) != all_fields_set:
                actions.append(InventoryActionEnum.EDIT.value)
                break

        # only user who created the grn can delete
        if user_id == getattr(batch.received_by, "id", None) and not is_stock_consumed_in_same_batch:
            actions.append(InventoryActionEnum.DELETE.value)

        return actions


class InventoryTransferBatchService:
    class TransferQuantityGreaterThanInStockQuantity(InventoryTransferBatchException):
        pass

    class NoTransferItemsToCreateException(BaseValidationError):
        pass

    class InventoryTransferReceiverProjectException(BaseValidationError):
        pass

    class InventoryTransferBatchItemsCreateError(BaseValidationError):
        pass

    def __init__(
        self,
        inventory_transfer_batch_repository: InventoryTransferBatchAbstractRepository,
        stock_transaction_service: InventoryStockService,
        user_id: int,
        organization_id: int,
        project_id: int,
    ):
        self.inventory_transfer_batch_repository = inventory_transfer_batch_repository
        self.stock_transaction_service = stock_transaction_service
        self.user_id = user_id
        self.organization_id = organization_id
        self.project_id = project_id

    def _trigger_stock_transfer_batch_created(self, batch_id: int, project_id: int):
        trigger_stock_transfer_batch_created(batch_id=batch_id, project_id=project_id)

    def update_available_quantity(
        self, stock_items: dict[int, InventoryStockItemEntity], items: list[InventoryTransferBaseEntity]
    ):
        logger.info("updating available quantity of stock items")
        items_to_update = []
        for item in items:
            stock_item = stock_items[item.item_id]
            stock_item.available_quantity = stock_item.available_quantity - item.quantity
            items_to_update.append(stock_item)

        self.stock_transaction_service.update_available_quantity(stock_items=items_to_update)
        logger.info("updated available quantity of stock items")

    @validate_call
    def validate_transfer_batch_items(
        self, stock_items: dict[int, InventoryStockItemEntity], items: list[InventoryTransferBaseEntity]
    ):
        logger.info("validating transfer batch items")
        for transfer_item in items:
            stock_item = stock_items[transfer_item.item_id]
            if stock_item.available_quantity < transfer_item.quantity:
                raise self.TransferQuantityGreaterThanInStockQuantity(
                    "Transfer quantity is greater than in stock quantity"
                )

        logger.info("batch item validation done")

    @validate_call
    def create_transfer_batch(self, data: InventoryTransferBatchCreateEntity):
        if not data.items:
            raise self.NoTransferItemsToCreateException("No transfer items to create")
        items = self.stock_transaction_service.get_stock_items_using_ids(id_list=[item.item_id for item in data.items])
        self.validate_transfer_batch_items(stock_items=items, items=data.items)
        try:
            logger.info("creating transfer batch")
            batch_id = self.inventory_transfer_batch_repository.create_transfer_batch(
                data=data, project_id=self.project_id, organization_id=self.organization_id, user_id=self.user_id
            )
        except self.inventory_transfer_batch_repository.InventoryTransferReceiverProjectException as e:
            logger.info("Error in creating transfer batch", str(e))
            raise self.InventoryTransferReceiverProjectException(e)
        self.update_available_quantity(stock_items=items, items=data.items)
        try:
            self.inventory_transfer_batch_repository.create_transfer_batch_items(
                batch_id=batch_id, data=data.items, created_by_id=self.user_id
            )
        except self.inventory_transfer_batch_repository.InventoryTransferItemCreateError as e:
            logger.info("Error in creating transfer batch items", str(e))
            raise self.InventoryTransferBatchItemsCreateError(e)
        self._trigger_stock_transfer_batch_created(batch_id=batch_id, project_id=self.project_id)
        return batch_id


class InventoryStockConsumptionService:
    class InventoryConsumptionItemsCreateError(BaseValidationError):
        pass

    class InventoryStockItemQuantityException(BaseValidationError):
        pass

    class InventoryStockTransactionException(BaseValidationError):
        pass

    class NoConsumptionItemToCreateException(BaseValidationError):
        pass

    def __init__(
        self,
        consumption_batch_repository: InventoryConsumptionBatchAbstractRepository,
        stock_transaction_service: InventoryStockService,
        inventory_batch_service: InventoryBatchService,
        project_id: int,
        organization_id: int,
        user_id: int,
    ):
        self.consumption_batch_repository = consumption_batch_repository
        self.stock_transaction_service = stock_transaction_service
        self.inventory_batch_service = inventory_batch_service
        self.project_id = project_id
        self.organization_id = organization_id
        self.user_id = user_id

    @validate_call
    def validate_batch_and_stock_quantity(self, stock_items, items: list[ConsumptionBatchItemBaseEntity]):
        for item in items:
            if item.quantity <= 0 or item.quantity > stock_items[item.item_id].available_quantity:
                raise self.stock_transaction_service.StockItemInvalidNewQuantityException("Invalid new debit quantity")

    @validate_call
    def process_stock_transaction(self, batch_id: int, items: list[ConsumptionBatchItemBaseEntity]):
        stock_item_update_entities = []
        stock_item_id_and_quantity_dict = {}
        for item in items:
            quantity_change = decimal.Decimal(0) - item.quantity
            if quantity_change == decimal.Decimal(0):
                continue
            stock_item_update_entity = InventoryStockItemUpdateEntity(
                id=item.item_id, quantity_change=quantity_change, reason=item.reason
            )

            stock_item_update_entities.append(stock_item_update_entity)
            stock_item_id_and_quantity_dict[item.item_id] = item.quantity

        try:
            transaction_data = self.stock_transaction_service.update_stock_items(
                data_items=stock_item_update_entities,
                batch_id=batch_id,
                transaction_type=InventoryTransactionType.CONSUMPTION,
            )
        except self.stock_transaction_service.StockItemTransactionCreateError as e:
            logger.info("Error in creating stock transaction data", str(e))
            raise self.InventoryStockTransactionException(e)
        except self.stock_transaction_service.StockItemInvalidNewQuantityException as e:
            logger.info("Invalid quantity in stock transaction", str(e))
            raise self.InventoryStockTransactionException(e)
        except self.stock_transaction_service.InvalidDebitStockTransactionException as e:
            logger.info("Invalid quantity change in stock transaction", str(e))
            raise self.InventoryStockTransactionException(e)

        self.inventory_batch_service.update_used_quantity_for_items(
            stock_item_quantity_used_dict=stock_item_id_and_quantity_dict,
            transaction_data=transaction_data,
            reason=InventoryTransactionType.CONSUMPTION.value,
        )

    def process_consumption(self, data: InventoryConsumptionBatchCreateEntity):
        if not data.items:
            raise self.NoConsumptionItemToCreateException("No consumption items to create")
        stock_items = self.stock_transaction_service.get_stock_items_using_ids(
            id_list=[item.item_id for item in data.items]
        )
        self.validate_batch_and_stock_quantity(stock_items=stock_items, items=data.items)
        batch_id = self.consumption_batch_repository.create_consumption_batch(
            data, project_id=self.project_id, organization_id=self.organization_id, user_id=self.user_id
        )
        self.process_stock_transaction(batch_id=batch_id, items=data.items)

        try:
            self.consumption_batch_repository.create_consumption_items(
                batch_id=batch_id, items=data.items, user_id=self.user_id
            )
        except self.consumption_batch_repository.InventoryConsumptionBatchModelValidationError as e:
            raise self.InventoryConsumptionItemsCreateError(e)

        return batch_id


class InventoryStockTransferActionService:
    class TransferBatchActionNotAllowed(BaseValidationError):
        pass

    class TransferStockTransactionException(BaseValidationError):
        pass

    class TransferInventoryBatchException(BaseValidationError):
        pass

    def __init__(
        self,
        transfer_batch_service: InventoryTransferBatchService,
        stock_transaction_service: InventoryStockService,
        inventory_batch_service: InventoryBatchService,
        user_id: int,
        project_id: int,
        organization_id: int,
    ):
        self.transfer_batch_service = transfer_batch_service
        self.stock_transaction_service = stock_transaction_service
        self.inventory_batch_service = inventory_batch_service
        self.user_id = user_id
        self.project_id = project_id
        self.org_id = organization_id

    def _trigger_stock_transfer_batch_action(
        self, batch_id: int, project_id: int, action: TransferBatchActions, transfer_id: int
    ):
        trigger_stock_transfer_batch_action(
            batch_id=batch_id, project_id=project_id, action=action, transfer_id=transfer_id
        )

    def process_stock_transaction(self, batch_id: int, items: list[InventoryTransferBaseEntity]):
        stock_item_update_entities = []
        stock_item_id_and_quantity_dict = {}
        for item in items:
            quantity_change = decimal.Decimal(0) - item.quantity
            if quantity_change == decimal.Decimal(0):
                continue
            stock_item_update_entity = InventoryStockItemUpdateEntity(
                id=item.item_id, quantity_change=quantity_change, reason="Stock Transfer"
            )

            stock_item_update_entities.append(stock_item_update_entity)
            stock_item_id_and_quantity_dict[item.item_id] = item.quantity

        try:
            transaction_data = self.stock_transaction_service.update_stock_items(
                data_items=stock_item_update_entities,
                batch_id=batch_id,
                transaction_type=InventoryTransactionType.STOCK_TRANSFER,
            )
        except self.stock_transaction_service.StockItemTransactionCreateError as e:
            logger.info("Error in creating stock transaction data", str(e))
            raise self.TransferStockTransactionException(e)
        except self.stock_transaction_service.StockItemInvalidNewQuantityException as e:
            logger.info("Error in creating stock transaction data", str(e))
            raise self.TransferStockTransactionException(e)
        except self.stock_transaction_service.InvalidDebitStockTransactionException as e:
            logger.info("Error in creating stock transaction data", str(e))
            raise self.TransferStockTransactionException(e)

        self.inventory_batch_service.update_used_quantity_for_items(
            stock_item_quantity_used_dict=stock_item_id_and_quantity_dict,
            transaction_data=transaction_data,
            reason=InventoryTransactionType.CONSUMPTION.value,
        )

    @validate_call
    def is_cancellation_allowed(self, batch_entity: InventoryTransferBatchEntity):
        if batch_entity.status != TransferStatus.PENDING:
            raise self.TransferBatchActionNotAllowed("Cancellation not allowed")
        if batch_entity.project_id != self.project_id:
            raise self.TransferBatchActionNotAllowed("Cancellation not allowed")

    @validate_call
    def is_rejection_allowed(self, batch_entity: InventoryTransferBatchEntity):
        if batch_entity.status != TransferStatus.PENDING:
            raise self.TransferBatchActionNotAllowed("Rejection not allowed")
        if batch_entity.receiver_project_id != self.project_id:
            raise self.TransferBatchActionNotAllowed("Rejection not allowed")

    @validate_call
    def is_approval_allowed(self, batch_entity: InventoryTransferBatchEntity):
        if batch_entity.status != TransferStatus.PENDING:
            raise self.TransferBatchActionNotAllowed("Approval not allowed")
        if batch_entity.receiver_project_id != self.project_id:
            raise self.TransferBatchActionNotAllowed("Approval not allowed")

    def update_available_quantity(
        self, stock_items: dict[int, InventoryStockItemEntity], items: list[InventoryTransferBaseEntity]
    ):
        items_to_update = []
        for item in items:
            stock_item = stock_items[item.item_id]
            stock_item.available_quantity = stock_item.available_quantity + item.quantity
            items_to_update.append(stock_item)

        self.stock_transaction_service.update_available_quantity(stock_items=items_to_update)

    def prepare_inventory_batch_data(self, batch_data: InventoryTransferBatchEntity, stock_items, transfer_batch_items):
        batch_items = []
        for item in transfer_batch_items:
            preview_file = (
                get_relative_path(stock_items[item.item_id].preview_file)
                if stock_items[item.item_id].preview_file
                else None
            )
            batch_items.append(
                InventoryBatchItemCreateEntity(
                    name=stock_items[item.item_id].name,
                    rate=stock_items[item.item_id].rate,
                    category_id=stock_items[item.item_id].category_id,
                    item_type_id=stock_items[item.item_id].item_type_id,
                    uom=stock_items[item.item_id].uom,
                    tax_percent=stock_items[item.item_id].tax_percent,
                    quantity=item.quantity,
                    batch_id=batch_data.id,
                    context=None,
                    context_id=None,
                    preview_file=preview_file,
                    code=stock_items[item.item_id].code,
                )
            )
        attachments = self.transfer_batch_service.inventory_transfer_batch_repository.get_batch_attachments(
            batch_id=batch_data.id
        )

        return TransferInventoryBatchCreateEntity(
            source=StockBatchType.STOCK_TRANSFER.value,
            remark=batch_data.remark,
            items=batch_items,
            attachments=attachments,
        )

    @validate_call
    def approve_transfer(self, batch_entity: InventoryTransferBatchEntity):
        self.is_approval_allowed(batch_entity=batch_entity)
        transfer_batch_items, stock_items = self.transfer_action_wrapper(
            batch_entity=batch_entity, status=TransferStatus.APPROVED.value
        )
        self.process_stock_transaction(batch_id=batch_entity.id, items=transfer_batch_items)
        inventory_data = self.prepare_inventory_batch_data(
            batch_data=batch_entity, stock_items=stock_items, transfer_batch_items=transfer_batch_items
        )
        try:
            inventory_batch = self.inventory_batch_service.create_transfer_inventory_batch(
                inventory_created_by_id=batch_entity.created_by_id, received_by_id=self.user_id, data=inventory_data
            )
        except self.inventory_batch_service.InventoryBatchItemTransactionCreateError as e:
            logger.info("Error in creating inventory batch items", str(e))
            raise self.TransferInventoryBatchException(e)

        except self.inventory_batch_service.InventoryBatchItemCreateError as e:
            logger.info("Error in creating inventory batch items", str(e))
            raise self.TransferInventoryBatchException(e)

        self.transfer_batch_service.inventory_transfer_batch_repository.latch_inventory_batch_to_transfer_batch(
            inventory_batch_id=inventory_batch.id, transfer_batch_id=batch_entity.id
        )
        return inventory_batch.id

    def transfer_action_wrapper(self, batch_entity: InventoryTransferBatchEntity, status: TransferStatus):
        transfer_batch_items = self.transfer_batch_service.inventory_transfer_batch_repository.get_transfer_batch_items(
            batch_id=batch_entity.id
        )
        stock_items = self.stock_transaction_service.get_stock_items_using_ids(
            id_list=[item.item_id for item in transfer_batch_items]
        )
        self.update_available_quantity(stock_items=stock_items, items=transfer_batch_items)
        self.transfer_batch_service.inventory_transfer_batch_repository.update_transfer_status(
            transfer_batch_id=batch_entity.id, status=status, created_by_id=self.user_id
        )
        return transfer_batch_items, stock_items

    @validate_call
    def reject_transfer(self, batch_entity: InventoryTransferBatchEntity):
        self.is_rejection_allowed(batch_entity=batch_entity)
        self.transfer_action_wrapper(batch_entity=batch_entity, status=TransferStatus.REJECTED.value)

    @validate_call
    def cancel_transfer(self, batch_entity: InventoryTransferBatchEntity):
        self.is_cancellation_allowed(batch_entity=batch_entity)
        self.transfer_action_wrapper(batch_entity=batch_entity, status=TransferStatus.CANCELLED.value)

    def process_action(self, batch_id: int, action: TransferBatchActions) -> Optional[int]:
        batch_entity = self.transfer_batch_service.inventory_transfer_batch_repository.get_transfer_batch(batch_id)
        self._trigger_stock_transfer_batch_action(
            batch_id=batch_id, project_id=self.project_id, action=action, transfer_id=batch_entity.id
        )
        if action == TransferBatchActions.APPROVE.value:
            return self.approve_transfer(batch_entity=batch_entity)
        elif action == TransferBatchActions.REJECT.value:
            return self.reject_transfer(batch_entity=batch_entity)
        elif action == TransferBatchActions.CANCEL.value:
            return self.cancel_transfer(batch_entity=batch_entity)
        else:
            raise self.TransferBatchActionNotAllowed("Invalid action")


class InventoryWorkProgressService:
    def __init__(self, repo: InventoryWorkProgressAbstractRepository):
        self.repo = repo

    def get_today_material_update_data(self):
        return self.repo.get_today_material_update_data()

    def get_material_update_data_in_date_range(self, start_date: datetime.date, end_date: datetime.date):
        return self.repo.get_material_update_data_in_date_range(start_date=start_date, end_date=end_date)
