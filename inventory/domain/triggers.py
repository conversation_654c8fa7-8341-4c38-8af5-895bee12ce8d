from functools import partial

from django.db.transaction import on_commit

from common.events.constants import Events
from common.events.materials import InventoryTransferBatchApproveEventData, InventoryTransferBatchCreateEventData
from common.events.services import trigger_event
from inventory.domain.enums import TransferBatchActions


def trigger_stock_transfer_batch_created(batch_id: int, project_id: int, receiver_project_id: int):
    on_commit(
        partial(
            trigger_event,
            event=Events.STOCK_TRANSFER_BATCH_CREATED,
            event_data=InventoryTransferBatchCreateEventData(
                batch_id=batch_id, project_id=project_id, receiver_project_id=receiver_project_id
            ),
        )
    )


def trigger_stock_transfer_batch_action(
    batch_id: int, project_id: int, action: TransferBatchActions, transfer_id: int, batch_creator_project_id: int
):
    on_commit(
        partial(
            trigger_event,
            event=Events.STOCK_TRANSFER_BATCH_ACTION,
            event_data=InventoryTransferBatchApproveEventData(
                batch_id=batch_id,
                project_id=project_id,
                action=action,
                transfer_id=transfer_id,
                batch_creator_project_id=batch_creator_project_id,
            ),
        )
    )
    if action == TransferBatchActions.APPROVE.value:
        on_commit(
            partial(
                trigger_event,
                event=Events.STOCK_TRANSFER_BATCH_APPROVED,
                event_data=InventoryTransferBatchApproveEventData(
                    batch_id=batch_id,
                    project_id=project_id,
                    action=action,
                    transfer_id=transfer_id,
                    batch_creator_project_id=batch_creator_project_id,
                ),
            )
        )
