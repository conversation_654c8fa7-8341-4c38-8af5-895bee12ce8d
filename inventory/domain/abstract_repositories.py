import abc
import datetime
from typing import List

from common.exceptions import BaseValidationError
from inventory.data.entities import (
    DayWiseInventoryWorkProgressUpdatedDataEntity,
    InventoryWorkProgressTodayUpdatedDataEntity,
)
from inventory.data.models import InventoryBatch
from inventory.domain.entities import (
    AttachmentBaseEntity,
    BatchItemQuantityUpdateEntity,
    ConsumptionBatchItemBaseEntity,
    InventoryAdhocBatchCreateEntity,
    InventoryBatchEntity,
    InventoryBatchItemCreateEntity,
    InventoryBatchItemEntity,
    InventoryBatchUpdateEntity,
    InventoryConsumptionBatchCreateEntity,
    InventoryExpenseBatchCreateEntity,
    InventoryOrderBatchCreateEntity,
    InventoryStockBaseItemEntity,
    InventoryStockItemEntity,
    InventoryStockItemTransactionBaseEntity,
    InventoryTransferBaseEntity,
    InventoryTransferBatchCreateEntity,
    InventoryTransferBatchEntity,
)
from inventory.domain.enums import InventoryTransactionType, TransferStatus
from inventory.domain.exceptions import InventoryException


class InventoryBatchAbstractRepository:
    class UpdateNotAllowedException(InventoryException):
        # if consumption or transfer batch is already created
        pass

    class InventoryBatchModelValidationError(InventoryException):
        pass

    @abc.abstractmethod
    def get_order_elements_receivable_quantity(
        self, items: list[InventoryBatchItemCreateEntity], order_id: int
    ) -> {int: int}:
        pass

    @abc.abstractmethod
    def get_expense_items_receivable_quantity(
        self, items: list[InventoryBatchItemCreateEntity], expense_id: int
    ) -> {int: int}:
        pass

    @abc.abstractmethod
    def get_inventory_batch_details(self, batch_id: int) -> InventoryBatch:
        pass

    @abc.abstractmethod
    def create_adhoc_batch(
        self,
        data: InventoryAdhocBatchCreateEntity,
        project_id: int,
        organization_id: int,
        received_by_id: int,
        created_by_id: int,
        source: str,
    ) -> InventoryBatchEntity:
        pass

    @abc.abstractmethod
    def create_order_batch(
        self,
        data: InventoryOrderBatchCreateEntity,
        project_id: int,
        organization_id: int,
        received_by_id: int,
        created_by_id: int,
    ) -> InventoryBatchEntity:
        pass

    @abc.abstractmethod
    def create_expense_batch(
        self,
        data: InventoryExpenseBatchCreateEntity,
        project_id: int,
        organization_id: int,
        received_by_id: int,
        created_by_id: int,
    ) -> InventoryBatchEntity:
        pass

    @abc.abstractmethod
    def check_if_items_consumed_or_transferred(self, batch_id) -> bool:
        pass

    def get_batch_data(self, batch_id: int) -> InventoryBatchEntity:
        pass

    @abc.abstractmethod
    def create_items_in_bulk(
        self, batch_id: int, created_by_id: int, items: list[(InventoryBatchItemCreateEntity, int)]
    ) -> List[InventoryBatchItemEntity]:
        pass

    @abc.abstractmethod
    def update_batch_data(self, batch: InventoryBatchEntity, data: InventoryBatchUpdateEntity, updated_by_id: int):
        pass

    @abc.abstractmethod
    def get_batch_items_in_bulk(self, batch_id: int, item_ids: list[int]) -> {int, InventoryBatchItemEntity}:
        pass

    @abc.abstractmethod
    def get_available_batch_items_using_stock_ids(self, stock_ids: list[int]) -> {int, list[InventoryBatchItemEntity]}:
        pass

    @abc.abstractmethod
    def update_batch_items(self, data: list[BatchItemQuantityUpdateEntity], transaction_type: InventoryTransactionType):
        pass

    @abc.abstractmethod
    def update_used_quantity(self, data: list[InventoryBatchItemEntity]) -> None:
        pass

    @abc.abstractmethod
    def save_utilization_data(self, data: list[tuple], reason: str, created_by_id: int):
        pass


class InventoryStockTransactionAbstractRepository:
    class StockItemInvalidNewQuantity(BaseValidationError):
        pass

    class InventoryStockItemModelValidationError(BaseValidationError):
        pass

    class InventoryStockIntegrityError(BaseValidationError):
        pass

    class InventoryStockItemTransactionModelValidationException(BaseValidationError):
        pass

    @abc.abstractmethod
    def update_available_quantity_of_items(self, stock_items: list[InventoryStockItemEntity]) -> None:
        pass

    @abc.abstractmethod
    def get_stock_items_in_bulk(self, id_list: list[int]) -> {int, InventoryStockItemEntity}:
        pass

    @abc.abstractmethod
    def create_stock_items_in_bulk(
        self, project_id: int, organization_id: int, created_by_id: int, items: List[InventoryStockBaseItemEntity]
    ) -> List[InventoryStockItemEntity]:
        pass

    def get_items_already_in_stock_with_codes(
        self, project_id: int, organization_id: int, items: list[InventoryBatchItemCreateEntity]
    ) -> list[InventoryStockItemEntity]:
        pass

    @abc.abstractmethod
    def save_transaction_data_in_bulk(
        self,
        user_id: int,
        data_list: List[InventoryStockItemTransactionBaseEntity],
    ):
        pass

    @abc.abstractmethod
    def save_stock_items(self, data_list: List[InventoryStockItemEntity], transaction_type: InventoryTransactionType):
        pass

    @abc.abstractmethod
    def get_items_already_in_stock_without_codes(
        self, project_id: int, organization_id: int, items: List[InventoryBatchItemCreateEntity]
    ) -> List[InventoryStockItemEntity]:
        pass


class InventoryTransferBatchAbstractRepository:
    class InventoryTransferReceiverProjectException(BaseValidationError):
        pass

    class InventoryTransferItemCreateError(BaseValidationError):
        pass

    @abc.abstractmethod
    def create_transfer_batch(
        self,
        data: InventoryTransferBatchCreateEntity,
        project_id: int,
        organization_id: int,
        user_id: int,
    ) -> int:
        pass

    @abc.abstractmethod
    def update_transfer_status(self, transfer_batch_id: int, status: TransferStatus, created_by_id: int):
        pass

    @abc.abstractmethod
    def create_transfer_batch_items(self, batch_id: int, data: list[InventoryTransferBaseEntity], created_by_id: int):
        pass

    @abc.abstractmethod
    def get_transfer_batch_items(self, batch_id: int) -> List[InventoryTransferBaseEntity]:
        pass

    @abc.abstractmethod
    def get_transfer_batch(self, batch_id: int) -> InventoryTransferBatchEntity:
        pass

    @abc.abstractmethod
    def get_batch_attachments(self, batch_id: int) -> List[AttachmentBaseEntity]:
        pass

    @abc.abstractmethod
    def latch_inventory_batch_to_transfer_batch(self, transfer_batch_id: int, inventory_batch_id: int):
        pass


class InventoryConsumptionBatchAbstractRepository:
    class InventoryConsumptionBatchModelValidationError(BaseValidationError):
        pass

    @abc.abstractmethod
    def create_consumption_batch(
        self,
        data: InventoryConsumptionBatchCreateEntity,
        project_id: int,
        organization_id: int,
        user_id: int,
    ):
        pass

    @abc.abstractmethod
    def create_attachments(self, batch_id: int, data: list[AttachmentBaseEntity], created_by_id: int):
        pass

    @abc.abstractmethod
    def create_consumption_items(self, batch_id: int, items: list[ConsumptionBatchItemBaseEntity], user_id: int):
        pass


class InventoryWorkProgressAbstractRepository:
    @abc.abstractmethod
    def get_today_material_update_data(self) -> InventoryWorkProgressTodayUpdatedDataEntity:
        pass

    @abc.abstractmethod
    def get_material_update_data_in_date_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[DayWiseInventoryWorkProgressUpdatedDataEntity]: ...
