import decimal
from collections import defaultdict

import structlog
from django.core.validators import Min<PERSON><PERSON>ueValidator
from django.db import models
from django.db.models import Q
from psycopg2 import IntegrityError

from boq.data.models import BoqElement
from common.constants import (
    FILE_FIELD_MAX_LENGTH,
    ElementNameConfig,
    FileNameFieldConfig,
    QuantityDecimalConfig,
    RateDecimalConfig,
    TaxPercentageFieldConfig,
)
from common.helpers import get_upload_path
from common.models import CreateModel, CreateUpdateDeleteModel, UploadDeleteModel, UploadModel
from common.validators import tax_percent_validator
from core.models import Organization, UnitOfMeasurement, User
from element.data.models import Element, ElementCategory, ElementItemType
from expense.data.models import Expense, ExpenseItem
from inventory.data.choices import StockBatchTypeChoices, TransferStatusChoices
from inventory.data.managers import InventoryBatchItemManager
from inventory.data.querysets import (
    InventoryBatchItemQuerySet,
    InventoryBatchQuerySet,
    InventoryStockItemQuerySet,
    InventoryStockItemTransactionQueryset,
    InventoryTransferBatchQuerySet,
)
from order.data.models import VendorOrder, VendorOrderElement
from project.data.models import Project

logger = structlog.get_logger(__name__)


class InventoryConsumptionBatch(CreateModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    remark = models.TextField(default="", null=True, blank=True)

    class Meta:
        db_table = "inventory_consumption_batches"
        verbose_name = "Inventory Consumption Batch"
        verbose_name_plural = "Inventory Consumption Batches"
        app_label = "inventory"


class ConsumptionBatchAttachment(UploadModel):
    file_name = models.CharField(max_length=FileNameFieldConfig.MAX_LENGTH)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    batch = models.ForeignKey(InventoryConsumptionBatch, on_delete=models.RESTRICT, related_name="attachments")

    class Meta:
        db_table = "inventory_consumption_batch_attachments"
        verbose_name = "Consumption Batch Attachment"
        verbose_name_plural = "Consumption Batch Attachments"
        app_label = "inventory"


class InventoryStockItem(CreateModel):
    # exceptions declarations
    class InventoryStockItemModelValidation(CreateModel.ModelFieldValidationException):
        pass

    class InventoryStockItemIntegrityError(CreateModel.ModelDataIntegrityException):
        pass

    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    name = models.CharField(max_length=ElementNameConfig.MAX_LENGTH)
    rate = models.DecimalField(
        max_digits=RateDecimalConfig.MAX_DIGITS,
        decimal_places=RateDecimalConfig.DECIMAL_PLACES,
    )
    uom = models.ForeignKey(UnitOfMeasurement, on_delete=models.RESTRICT, related_name="+")
    category = models.ForeignKey(ElementCategory, on_delete=models.RESTRICT)
    code = models.CharField(max_length=20, null=True, blank=True)
    tax_percent = models.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        validators=[tax_percent_validator],
    )
    available_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )  # total quantity of items available excluding locked items for transfer
    total_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )  # total quantity of items available
    preview_file = models.FileField(null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    item_type = models.ForeignKey(ElementItemType, on_delete=models.RESTRICT, null=True, blank=True, related_name="+")

    objects = InventoryStockItemQuerySet.as_manager()

    @property
    def stock_value(self):
        return (self.rate * self.available_quantity) + ((self.rate * self.available_quantity) * self.tax_percent / 100)

    def clean(self):
        error_list = defaultdict(list)

        if self.available_quantity > self.total_quantity:
            error_list["available_quantity"].append(
                f"available quantity cannot be greater than total quantity, "
                f"available here is {self.available_quantity}"
            )
            error_list["total_quantity"].append(
                f"total quantity cannot be less than available quantity, total here is {self.total_quantity}"
            )

        if error_list:
            raise self.InventoryStockItemModelValidation(error_list)

    def save(self, *args, **kwargs):
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "inventory_stock_item_element_unique_constraint" in str(e):
                logger.info("Stock item with same code and rate already exists")
                logger.info(str(e))
                raise self.InventoryStockItemIntegrityError("Stock item with same code and rate already exists")
            if "inventory_stock_item_custom_item_unique_constraint" in str(e):
                logger.info("Stock item with same details already exists")
                logger.info(str(e))
                raise self.InventoryStockItemIntegrityError("Stock item with same details already exists")
            logger.info(str(e))
            raise e

    class Meta:
        db_table = "inventory_stock_items"
        verbose_name = "Inventory Stock Item"
        verbose_name_plural = "Inventory Stock Items"
        app_label = "inventory"
        constraints = [
            models.UniqueConstraint(
                name="inventory_stock_item_element_unique_constraint",
                fields=["project_id", "organization_id", "code", "rate"],
                condition=Q(code__isnull=False),
            ),
            models.UniqueConstraint(
                name="inventory_stock_item_custom_item_unique_constraint",
                fields=[
                    "project_id",
                    "organization_id",
                    "name",
                    "rate",
                    "uom",
                    "category",
                    "tax_percent",
                    "item_type",
                ],
                condition=Q(code__isnull=True),
            ),
        ]


class InventoryConsumptionBatchItem(CreateModel):
    class InventoryConsumptionIntegrityError(CreateModel.ModelDataIntegrityException):
        pass

    class InventoryConsumptionModelFieldValidationException(CreateModel.ModelFieldValidationException):
        pass

    batch = models.ForeignKey(InventoryConsumptionBatch, on_delete=models.RESTRICT, related_name="items")
    item = models.ForeignKey(InventoryStockItem, on_delete=models.RESTRICT, related_name="consumption_items")
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS, decimal_places=QuantityDecimalConfig.DECIMAL_PLACES
    )
    reason = models.CharField(max_length=100)

    def clean(self):
        if self.quantity < decimal.Decimal(0) or self.quantity == decimal.Decimal(0):
            raise self.InventoryConsumptionModelFieldValidationException("Quantity cannot be zero or negative")

    def save(self, *args, **kwargs):
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "inventory_consumption_batch_item_unique_constraint" in str(e):
                logger.error("Item already exists in consumption batch")
                raise self.InventoryConsumptionIntegrityError("Item already exists in consumption batch")
            raise e

    class Meta:
        unique_together = ("batch_id", "item_id")
        db_table = "inventory_consumption_batch_items"
        verbose_name = "Inventory Consumption Batch Item"
        verbose_name_plural = "Inventory Consumption Batch Items"
        app_label = "inventory"


class InventoryBatch(CreateUpdateDeleteModel):
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    grn_number = models.IntegerField()
    source = models.CharField(max_length=20, choices=StockBatchTypeChoices.choices)
    order = models.ForeignKey(VendorOrder, on_delete=models.RESTRICT, null=True, blank=True)
    expense = models.ForeignKey(Expense, on_delete=models.RESTRICT, null=True, blank=True)
    received_by = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="+")
    remark = models.TextField(default="", null=True, blank=True)

    objects = InventoryBatchQuerySet.as_manager()

    class Meta:
        db_table = "inventory_batches"
        verbose_name = "Inventory Batch"
        verbose_name_plural = "Inventory Batches"
        app_label = "inventory"
        unique_together = ("organization_id", "grn_number")


class InventoryBatchAttachment(UploadDeleteModel):
    file_name = models.CharField(max_length=FileNameFieldConfig.MAX_LENGTH)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    batch = models.ForeignKey(InventoryBatch, on_delete=models.RESTRICT, related_name="attachments")

    class Meta:
        db_table = "inventory_batch_attachments"
        verbose_name = "Inventory Batch Attachment"
        verbose_name_plural = "Inventory Batch Attachments"
        app_label = "inventory"


class InventoryBatchItem(CreateUpdateDeleteModel):
    class InventoryBatchItemModelValidationException(CreateModel.ModelFieldValidationException):
        pass

    class InventoryBatchItemIntegrityError(CreateModel.ModelDataIntegrityException):
        pass

    batch = models.ForeignKey(InventoryBatch, on_delete=models.RESTRICT, related_name="items")
    order_element = models.ForeignKey(
        VendorOrderElement,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    library_element = models.ForeignKey(
        Element,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    boq_element = models.ForeignKey(
        BoqElement,
        on_delete=models.RESTRICT,
        related_name="+",
        null=True,
        blank=True,
    )
    expense_item = models.ForeignKey(ExpenseItem, on_delete=models.RESTRICT, related_name="+", null=True, blank=True)
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS, decimal_places=QuantityDecimalConfig.DECIMAL_PLACES
    )  # total quantity of items added in batch
    used_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )  # quantity of items used from batch
    name = models.CharField(max_length=300)
    rate = models.DecimalField(max_digits=RateDecimalConfig.MAX_DIGITS, decimal_places=RateDecimalConfig.DECIMAL_PLACES)
    uom = models.ForeignKey(UnitOfMeasurement, on_delete=models.RESTRICT, related_name="+")
    category = models.ForeignKey(ElementCategory, on_delete=models.RESTRICT)
    code = models.CharField(max_length=20, null=True, blank=True)
    tax_percent = models.DecimalField(
        max_digits=TaxPercentageFieldConfig.MAX_DIGITS,
        decimal_places=TaxPercentageFieldConfig.DECIMAL_PLACES,
        validators=[tax_percent_validator],
    )
    preview_file = models.FileField(
        default=None, null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH
    )
    item_type = models.ForeignKey(ElementItemType, null=True, blank=True, on_delete=models.RESTRICT, related_name="+")
    stock_item = models.ForeignKey(InventoryStockItem, on_delete=models.RESTRICT, related_name="batch_items")
    objects = InventoryBatchItemManager()  # returns only non-deleted records
    all_objects = InventoryBatchItemQuerySet.as_manager()

    def clean(self):
        error_list = defaultdict(list)

        if self.batch.source == StockBatchTypeChoices.ORDER:
            if self.order_element_id is None:
                error_list["order_element_id"].append("Order element is required for order source batch item")

        elif self.batch.source == StockBatchTypeChoices.EXPENSE:
            if self.expense_item_id is None:
                error_list["expense_item_id"].append("Expense item is required for expense source batch item")

        elif self.batch.source == StockBatchTypeChoices.ADHOC:
            if not self.library_element and not self.boq_element and not self.order_element:
                pass
            elif not (bool(self.library_element_id) ^ bool(self.boq_element_id) ^ bool(self.order_element_id)):
                error_list["element"].append("Only one of library element, boq element or order element is required")

        if self.used_quantity > self.quantity:
            error_list["used_quantity"].append(
                f"Used quantity ({self.used_quantity}) cannot be greater than quantity ({self.quantity})"
            )

        if error_list:
            raise self.InventoryBatchItemModelValidationException(error_list)

    class Meta:
        db_table = "inventory_batch_items"
        verbose_name = "Inventory Batch Item"
        verbose_name_plural = "Inventory Batch Items"
        app_label = "inventory"


class InventoryTransferBatch(CreateModel):
    class InventoryTransferBatchModelValidationException(CreateModel.ModelFieldValidationException):
        pass

    class InventoryTransferBatchIntegrityError(CreateModel.ModelDataIntegrityException):
        pass

    transfer_number = models.IntegerField()
    project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    receiver_project = models.ForeignKey(Project, on_delete=models.RESTRICT, related_name="+")
    batch = models.OneToOneField(
        InventoryBatch, on_delete=models.RESTRICT, related_name="transfer_batch", null=True, blank=True
    )
    status = models.CharField(max_length=20, choices=TransferStatusChoices.choices)
    status_updated_by = models.ForeignKey(User, on_delete=models.RESTRICT, related_name="+")
    remark = models.TextField(default="", null=True, blank=True)

    objects = InventoryTransferBatchQuerySet.as_manager()

    def clean(self):
        if self.project_id == self.receiver_project_id:
            raise self.InventoryTransferBatchModelValidationException("Project and receiver project cannot be same")

    class Meta:
        db_table = "inventory_transfer_batches"
        verbose_name = "Inventory Transfer Batch"
        verbose_name_plural = "Inventory Transfer Batches"
        app_label = "inventory"
        unique_together = ("organization_id", "transfer_number")


class InventoryTransferBatchAttachment(UploadModel):
    file_name = models.CharField(max_length=FileNameFieldConfig.MAX_LENGTH)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    batch = models.ForeignKey(InventoryTransferBatch, on_delete=models.RESTRICT, related_name="attachments")

    class Meta:
        db_table = "inventory_transfer_batch_attachments"
        verbose_name = "Inventory Transfer Batch Attachment"
        verbose_name_plural = "Inventory Transfer Batch Attachments"
        app_label = "inventory"


class InventoryTransferBatchItem(CreateModel):
    class InventoryTransferBatchModelValidationException(CreateModel.ModelFieldValidationException):
        pass

    batch = models.ForeignKey(InventoryTransferBatch, on_delete=models.RESTRICT, related_name="items")
    item = models.ForeignKey(InventoryStockItem, on_delete=models.RESTRICT, related_name="transfer_items")
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS, decimal_places=QuantityDecimalConfig.DECIMAL_PLACES
    )

    def clean(self):
        if self.quantity < decimal.Decimal(0) or self.quantity == decimal.Decimal(0):
            raise self.InventoryTransferBatchModelValidationException("Quantity cannot be zero or negative")

    class Meta:
        unique_together = ("batch_id", "item_id")
        db_table = "inventory_transfer_batch_items"
        verbose_name = "Inventory Transfer Batch Item"
        verbose_name_plural = "Inventory Transfer Batch Items"
        app_label = "inventory"


class InventoryTransferBatchStatusHistory(CreateModel):
    batch = models.ForeignKey(InventoryTransferBatch, on_delete=models.RESTRICT, related_name="status_histories")
    status = models.CharField(max_length=20, choices=TransferStatusChoices.choices)

    class Meta:
        db_table = "inventory_transfer_batch_status_histories"
        verbose_name = "Inventory Transfer Batch Status History"
        verbose_name_plural = "Inventory Transfer Batch Status Histories"
        app_label = "inventory"


class InventoryStockItemTransaction(CreateModel):
    item_id: int
    consumption_batch_id: int | None
    transfer_batch_id: int | None
    inventory_batch_id: int | None

    class InventoryStockItemTransactionModelValidationException(CreateModel.ModelFieldValidationException):
        pass

    item = models.ForeignKey(InventoryStockItem, on_delete=models.RESTRICT, related_name="transactions")
    balance_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        validators=[MinValueValidator(0)],
    )  # final stock quantity after credit or debit of stock item
    debit_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        null=True,
        blank=True,
    )
    credit_quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
        null=True,
        blank=True,
    )
    reason = models.TextField()
    consumption_batch = models.ForeignKey(InventoryConsumptionBatch, on_delete=models.RESTRICT, null=True, blank=True)
    transfer_batch = models.ForeignKey(InventoryTransferBatch, on_delete=models.RESTRICT, null=True, blank=True)
    inventory_batch = models.ForeignKey(InventoryBatch, on_delete=models.RESTRICT, null=True, blank=True)

    objects = InventoryStockItemTransactionQueryset.as_manager()

    def clean(self):
        if not (bool(self.credit_quantity) ^ bool(self.debit_quantity)):
            raise self.InventoryStockItemTransactionModelValidationException(
                "Either credit quantity or debit quantity is required"
            )

        if self.balance_quantity < 0:
            raise self.InventoryStockItemTransactionModelValidationException("Balance quantity cannot be negative")

        if (self.credit_quantity < decimal.Decimal(0)) or (self.debit_quantity < decimal.Decimal(0)):
            raise self.InventoryStockItemTransactionModelValidationException(
                "Credit or Debit quantity cannot be zero or negative"
            )

        if not (bool(self.consumption_batch_id) ^ bool(self.transfer_batch_id) ^ bool(self.inventory_batch_id)):
            raise self.InventoryStockItemTransactionModelValidationException(
                "Either consumption batch or transfer batch or inventory batch is required"
            )

    class Meta:
        db_table = "inventory_stock_item_transactions"
        verbose_name = "Inventory Stock Item Transaction"
        verbose_name_plural = "Inventory Stock Item Transactions"
        app_label = "inventory"


class ConsumptionReason(CreateModel):
    reason = models.CharField(max_length=300)

    class Meta:
        db_table = "inventory_consumption_reasons"
        verbose_name = "Inventory Consumption Reason"
        verbose_name_plural = "Inventory Consumption Reasons"
        app_label = "inventory"


class InventoryBatchItemUtilisationHistory(CreateModel):
    batch_item = models.ForeignKey(InventoryBatchItem, on_delete=models.RESTRICT, related_name="utilisation_histories")
    quantity = models.DecimalField(
        max_digits=QuantityDecimalConfig.MAX_DIGITS,
        decimal_places=QuantityDecimalConfig.DECIMAL_PLACES,
    )  # quantity of items used from batch
    reason = models.TextField()
    transaction = models.ForeignKey(
        InventoryStockItemTransaction, on_delete=models.RESTRICT, related_name="utilisation_histories"
    )

    class Meta:
        db_table = "inventory_batch_item_utilisation_histories"
        verbose_name = "Inventory Batch Item Utilisation History"
        verbose_name_plural = "Inventory Batch Item Utilisation Histories"
        app_label = "inventory"
