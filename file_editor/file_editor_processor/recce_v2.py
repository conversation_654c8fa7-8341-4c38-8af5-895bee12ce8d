from file_editor.data.selectors import recce_v2_file, recce_v2_file_list
from file_editor.file_editor_processor.base import BaseFileEditorProcessor
from microcontext.choices import MicroContextChoices as ContextChoices
from project.domain.status import Module
from reccev2.interface.serializers import RecceFileFileEditorSerializer


class RecceV2ModuleFileEditorDataProcessor(BaseFileEditorProcessor):
    detail_meta_fields = (
        "source",
        "name",
        "section_name",
        "version",
        "detail",
        "uploaded_by",
        "uploaded_at",
        # "elements",
    )
    detail_meta_serializer = RecceFileFileEditorSerializer
    selectors = {"detail_selector": recce_v2_file, "list_selector": recce_v2_file_list}

    def prepare_breadcrumb(self, meta_info):
        return f"""{Module.RECCE.label} {meta_info["version"]} > {meta_info["section_name"]} > {meta_info["name"]}"""


@BaseFileEditorProcessor.register_context(context=ContextChoices.RECCE_FILE)
class RecceFileDataSerializer(RecceV2ModuleFileEditorDataProcessor):
    """"""
