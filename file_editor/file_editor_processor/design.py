from design.interface.serializers import DesignFileVersionSerializer
from file_editor.data.selectors import design_file, design_file_list
from file_editor.file_editor_processor.base import BaseFileEditorProcessor
from microcontext.choices import MicroContextChoices as ContextChoices
from project.domain.status import Module


class DesignModuleFileEditorDataProcessor(BaseFileEditorProcessor):
    detail_meta_fields = (
        "name",
        "type",
        "section",
        "tags",
        "version",
        "status",
        "uploaded_at",
        "uploaded_by",
        "updated_by",
        "updated_at",
    )
    detail_meta_serializer = DesignFileVersionSerializer
    selectors = {"detail_selector": design_file, "list_selector": design_file_list}

    def prepare_breadcrumb(self, meta_info):
        return f"""{Module.DESIGN.label} > {meta_info["section"]["name"]} > {meta_info["name"]}"""


@BaseFileEditorProcessor.register_context(context=ContextChoices.DESIGN_FILE)
class DesignFileModuleEditorData(DesignModuleFileEditorDataProcessor):
    """"""
