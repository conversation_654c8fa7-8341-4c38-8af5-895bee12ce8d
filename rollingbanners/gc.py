import gc
import os

import structlog
from gunicorn.workers.sync import SyncWorker

tune_gc = bool(os.environ.get("TUNE_GC", False))
logger = structlog.getLogger(__name__)

GEN_2_THRESH = 50
GEN_1_THRESH = 10

gc_count = 0


def run_gc():
    """
    Run and time garbage collection.
    """
    global gc_count

    if gc_count % GEN_2_THRESH == 0:
        gen = 2
    elif gc_count % GEN_1_THRESH == 0:
        gen = 1
    else:
        gen = 0

    gc.collect(gen)
    gc_count += 1

    logger.info(f"[GC] Collected {gen} generation garbage.")


class DeferredGCSyncWorker(SyncWorker):
    """
    SyncWorker that always runs GC after completing a request
    (if set in env).

    Deferred garbage collection

    The SyncWorker parent class performs the full wsgi
    request-response lifecycle in the `handle` function. We add a
    call to garbage collection here once that function is fully
    completely to add behavior that occurs entirely outside the wsgi
    request lifecycle
    """

    def handle(self, listener, client, addr):
        try:
            return super().handle(listener, client, addr)
        finally:
            if tune_gc:
                run_gc()
