from django.urls import include, path, register_converter

from rollingbanners.hash_id_converter import HashIdConverter

register_converter(HashIdConverter, "hash_id")

url_patterns = [
    path("v2/projects", include("project.interface.urls.external.v2"), name="external-project-urls-v2"),
    path(
        "v2/projects/<hash_id:project_id>/vendor-payment-requests",
        include("payment_request.interface.urls.external.project.vendor.v2"),
        name="external-vendor-payment-request-urls-v2",
    ),
    path(
        "v2/projects/<hash_id:project_id>/client-payment-requests",
        include("payment_request.interface.urls.external.project.client.v2"),
        name="external-client-payment-request-urls-v2",
    ),
    path(
        "v2/projects/<hash_id:project_id>/vendor-orders",
        include("order.interface.urls.external.project.vendor.v2"),
        name="external-vendor-order-urls-v2",
    ),
    path(
        "v2/projects/<hash_id:project_id>/vendor-invoices",
        include("order.invoice.interface.urls.external.project.vendor.v2"),
        name="external-vendor-invoice-urls-v2",
    ),
    path(
        "v2/projects/<hash_id:project_id>/client-orders",
        include("order.interface.urls.external.project.client.v2"),
        name="external-client-order-urls-v2",
    ),
    path(
        "v2/projects/<hash_id:project_id>/client-invoices",
        include("order.invoice.interface.urls.external.project.client.v2"),
        name="external-client-invoice-urls-v2",
    ),
    path(
        "v2/invoices",
        include("order.invoice.interface.urls.external.v2"),
        name="external-invoice-urls-v2",
    ),
]
