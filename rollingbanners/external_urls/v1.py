from django.urls import include, path, register_converter

from rollingbanners.hash_id_converter import HashIdConverter

register_converter(HashIdConverter, "hash_id")

url_patterns = [
    path("api/v1/ext/projects/", include("project.interface.urls.external.urls"), name="project-external-urls"),
    path("api/v1/ext/projects/<str:job_id>/boq/", include("boq.external.urls"), name="boq-external-urls"),
    path(
        "api/v1/ext/projects/<str:job_id>/orders/",
        include("order.interface.urls.external.v1"),
        name="order-external-urls",
    ),
    path("api/v1/ext/element/", include("element.interface.urls.external.urls"), name="vendor-external-urls"),
    path("api/v1/ext/vendor/", include("vendorv2.external.urls"), name="vendor-external-urls"),
    path("api/v1/ext/client/", include("client.external.urls"), name="vendor-external-urls"),
]
