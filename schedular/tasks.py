from datetime import timedelta

import requests
import structlog
from celery import shared_task
from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from django.utils.module_loading import import_string
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

from common.events.constants import Events
from common.events.project_schedule import ProjectScheduleDelayEventData
from common.events.services import trigger_event
from common.events.task import ScheduledJobEventData, TodaysDueTaskCountEventData
from execution_tracker.data.choices import DLQStatusType
from execution_tracker.data.models import DeadLetterQueueTask
from project_schedule.data.models import ProjectSchedule
from schedular.domain.abstract_repos import JobAbstractRepo
from schedular.domain.constants import JobStatus
from schedular.domain.entities import SchedularWorkerJobData
from schedular.external_schedular.base import WorkerToSchedularAbstractService
from task.data.selectors import fetch_assignee_ids_by_due_date

logger = structlog.get_logger()

WorkerToSchedularService: WorkerToSchedularAbstractService = import_string(settings.WORKER_TO_SCHEDULAR_SERVICE)()
job_repo: JobAbstractRepo = import_string(settings.JOB_REPO)()


class JobProcessor:
    def __init__(self, data: SchedularWorkerJobData):
        self.data = data

    def _process_active_job(self):
        logger.info("Job is active, processing job !!!")
        WorkerToSchedularService.on_active_job_execution(job_id=self.data.job_id)
        logger.info("On Active callback executed successfully !!!")
        job_repo.update_job_status(self.data.job_id, status=JobStatus.DISPATCHED.value)
        trigger_event(
            event=self.data.event_type,
            event_data=ScheduledJobEventData(
                context=self.data.context,
                context_id=self.data.context_id,
                user_id=self.data.user_id,
            ),
        )
        logger.info("Task Reminder Event Triggered Successfully !!!", task_id=self.data.context_id)

    def _process_inactive_job(self):
        logger.info("Job is inactive, processing job !!!")
        WorkerToSchedularService.on_inactive_job_exection(job_id=self.data.job_id)
        logger.info("Inactive callback executed successfully !!!")
        job_repo.update_job_status(self.data.job_id, status=JobStatus.CANCELLED.value)

    def process(self):
        if self.data.is_active:
            self._process_active_job()
        else:
            self._process_inactive_job()


@shared_task
def rd_schedular_task(job_id: int):
    job_data = job_repo.get_job_data(job_id=job_id)
    logger.info("Job Received for Processing !!!", job_id=job_id, job_data=job_data.__dict__)
    try:
        JobProcessor(data=job_data).process()
        logger.info("Job Processed Successfully !!!", job_id=job_id)
    except Exception:
        job_repo.update_job_status(job_id=job_id, status=JobStatus.FAILED.value)
        logger.error("Job Processing Failed !!!", job_id=job_id)


@shared_task
def todays_due_task_schedular():
    logger.info("Running Todays Due Task Schedular !!!")
    if settings.IS_NON_PROD_ENV:
        logger.info("Skipping Todays Due Task Schedular for Non Prod Environment !!!")
        return
    assignee_ids = set(fetch_assignee_ids_by_due_date(date=timezone.now().date()))
    for assignee_id in assignee_ids:
        trigger_event(
            event=Events.TODAYS_DUE_TASK_COUNT,
            event_data=TodaysDueTaskCountEventData(
                user_id=assignee_id,
            ),
        )
    logger.info("Todays Due Task Schedular Completed !!!", assignee_ids=assignee_ids)


@shared_task
def project_schedule_delay_and_overdue_task_schedular(*args, **kwargs):
    logger.info("Running Project Schedule Delay and Overdue Task Schedular !!!")
    if settings.IS_NON_PROD_ENV:
        logger.info("Skipping Project Schedule Delay and Overdue Task Schedular for Non Prod Environment !!!")
        return
    country_id = 1
    schedules = (
        ProjectSchedule.objects.filter(project__store__country_id=country_id)
        .incomplete_schedules()
        .annotate_delay_and_overdue()
    )
    for schedule in schedules:
        if schedule.is_delay:
            trigger_event(
                event=Events.PROJECT_SCHEDULE_DELAY,
                event_data=ProjectScheduleDelayEventData(
                    schedule_id=schedule.id,
                    project_id=schedule.project_id,
                ),
            )
        # Ticket :- SB-285
        # elif schedule.is_overdue:
        #     trigger_event(
        #         event=Events.PROJECT_SCHEDULE_OVERDUE,
        #         event_data=ProjectScheduleOverdueEventData(
        #             schedule_id=schedule.id,
        #             project_id=schedule.project_id,
        #         ),
        #     )
    logger.info("Project Schedule Delay and Overdue Task Schedular Completed !!!")


@shared_task
def hard_delete_execution_tracker_history():
    logger.info("Running Hard Delete Execution Tracker History !!!")
    if settings.IS_NON_PROD_ENV:
        logger.info("Skipping Hard Delete Execution Tracker History for Non Prod Environment !!!")
        return
    one_day_ago = timezone.now() - timedelta(days=1)
    three_days_ago = timezone.now() - timedelta(days=3)
    seven_days_ago = timezone.now() - timedelta(days=7)
    tasks_to_delete = DeadLetterQueueTask.objects.filter(
        Q(status=DLQStatusType.DONE, updated_at__lt=one_day_ago)
        | Q(status=DLQStatusType.DROPPED, updated_at__lt=three_days_ago)
        | Q(status=DLQStatusType.QUEUED, updated_at__lt=seven_days_ago)
    )
    deleted_count = tasks_to_delete.delete()[0]
    logger.info(
        "Hard Delete Execution Tracker History Completed !!!",
        deleted_count=deleted_count,
        deleted_tasks=list[tasks_to_delete.values_list("task_id", flat=True)],
    )


@shared_task
def send_twilio_low_balance_alerts():
    """
    Checks Twilio account balance and sends a Slack alert if it's below a threshold.
    Fault-tolerant: Catches all exceptions and logs errors instead of throwing.
    """
    logger.info("Starting Twilio Low Balance Alerts Schedular.")

    is_non_prod = getattr(settings, "IS_NON_PROD_ENV", False)
    threshold = int(getattr(settings, "TWILIO_ACCOUNT_MINIMUM_BALANCE_THRESHOLD", 21))
    slack_webhook_url = getattr(
        settings,
        "SLACK_SERVICE_ALERT_WEBHOOK_URL",
        None,
    )

    if is_non_prod:
        logger.info("Skipping Twilio Low Balance Alerts Schedular for Non-Prod environment.")
        return

    try:
        client = Client()
        account_object = client.api.v2010.account.fetch()
        logger.info("Twilio Account Object Fetched.")
        account_balance_object = account_object.balance.fetch()
        logger.info("Twilio Account Balance Object Fetched.")
        account_balance_str = account_balance_object.balance
        account_balance = float(account_balance_str)
        account_balance_dollars = int(account_balance)
        logger.info("twilio_account_balance", balance=account_balance_dollars)
        if not slack_webhook_url:
            logger.warning("Slack webhook URL is not configured. Cannot send low balance alert via Slack.")
            return
        if account_balance_dollars < threshold:
            logger.warning(
                f"Twilio Account Balance ({account_balance_dollars} USD) is below threshold ({threshold} USD). Attempting to send Slack alert."  # noqa
            )
            slack_payload = {
                "text": f":alert: Twilio Account Balance is Low: {account_balance:.2f} USD :alert:",
                "username": "Twilio Low Balance Alert",
                "icon_emoji": ":warning:",
            }
            try:
                response = requests.post(slack_webhook_url, json=slack_payload, timeout=30)
                response.raise_for_status()
                logger.info(f"Slack alert sent successfully. Status Code: {response.status_code}")
            except requests.exceptions.RequestException as req_e:
                logger.error(f"Failed to send Slack alert due to network or HTTP error: {req_e}", exc_info=True)
            except Exception as slack_e:
                logger.error(f"An unexpected error occurred while sending Slack alert: {slack_e}", exc_info=True)

        else:
            logger.info(
                f"Twilio Account Balance ({account_balance_dollars} USD) is sufficient ({account_balance_dollars} >= {threshold})."  # noqa
            )
    except TwilioRestException as twilio_e:
        logger.error(f"Twilio API error occurred during balance check: {twilio_e}", exc_info=True)
    except requests.exceptions.RequestException as req_e:
        logger.error(f"Network/Request error occurred during Twilio operation: {req_e}", exc_info=True)
    except ValueError as ve:
        logger.error(f"Error converting Twilio balance to number: {ve}", exc_info=True)
    except Exception as e:
        logger.error(f"An unexpected error occurred during Twilio low balance check: {e}", exc_info=True)
    logger.info("Finished Twilio Low Balance Alerts Schedular.")
