import requests
import structlog
from requests.exceptions import RequestException

logger = structlog.get_logger()


class WatiApiClient:
    """A client for interacting with the WATI API."""

    WATI_URL = "https://live-mt-server.wati.io/{tenant_id}/api/v1"

    def __init__(self, tenant_id: str, email: str, token: str):
        self.tenant_id = tenant_id
        self.email = email
        self._session = requests.Session()
        self._session.headers.update(
            {
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Authorization": f"Bearer {token}",
            }
        )
        self.base_url = self.WATI_URL.format(tenant_id=self.tenant_id)

    def get_credit(self) -> int | None:
        """
        Fetches the current account credit.

        Returns:
            The current credit as an integer, or None if an error occurs.
        """
        json_payload = {
            "paymentType": None,
            "firstName": "Tech",
            "lastName": "Admin",
            "email": self.email,
            "phone": "",
            "isTrial": False,
            "domain": f"https://live.wati.io/{self.tenant_id}",
        }
        try:
            response = self._session.post(
                f"{self.base_url}/payment/getCredit",
                json=json_payload,
                timeout=30,
            )
            response.raise_for_status()
            data = response.json()
            credit_value = data.get("creditCustomer", {}).get("credit")
            if credit_value is not None:
                return int(credit_value)
            logger.warning("Credit information not found in API response for tenant: %s", self.tenant_id)
            return None

        except RequestException as e:
            logger.error("API request failed for tenant: %s. Error: %s", self.tenant_id, e, exc_info=True)
            return None
        except (ValueError, TypeError) as e:
            logger.error(
                "Could not parse credit from WATI response for tenant: %s. Error: %s", self.tenant_id, e, exc_info=True
            )
            return None
