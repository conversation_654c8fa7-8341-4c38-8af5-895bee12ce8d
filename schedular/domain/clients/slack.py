import requests
import structlog

logger = structlog.get_logger(__name__)


class SlackClient:
    """Client for sending alerts to <PERSON>lack."""

    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url

    def send_alert(self, message: str, username: str = "<PERSON><PERSON>", icon_emoji: str = ":warning:"):
        """Sends an alert to <PERSON>lack with the given message, title, and color."""

        slack_payload = {
            "text": message,
            "username": username,
            "icon_emoji": icon_emoji,
        }
        try:
            response = requests.post(self.webhook_url, json=slack_payload, timeout=30)
            response.raise_for_status()
            logger.info(f"Slack alert sent successfully. Status Code: {response.status_code}")
        except requests.RequestException as e:
            logger.error(f"Failed to send Slack alert: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"An unexpected error occurred while sending Slack alert: {e}", exc_info=True)
