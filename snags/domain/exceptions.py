from django.utils.translation import gettext_lazy as _
from rest_framework.settings import api_settings

from common.exceptions import BaseValidationError, RollingBannersException


class ExceptionConstant:
    SNAG_NOT_FOUND = "Snag Not Found"
    SNAG_ASSIGNMENT_ALREADY_EXISTS_FOR_THIS_ORGANIZATION = "This snag is already assigned to this organization"
    AT_LEAST_ONE_FIELD_NEEDS_TO_BE_FILLED = "Either of the fields needs to be filled"
    SNAG_ASSIGNMENT_NOT_FOUND = "Snag Assignment Not Found"
    SNAG_CANNOT_BE_UNASSIGNED_BY_YOUR_ORG = "Snag Cannot be unassigned by your organization"
    SNAG_CANNOT_BE_UNASSIGNED_ANYMORE = "Snag Cannot be unassigned anymore"
    SNAG_ALREADY_UNRESOLVED = "This snag is already unresolved"
    SNAG_ALREADY_RESOLVED = "This snag is already resolved"
    SNAG_CANNOT_BE_CLOSED_BY_YOUR_ORG = "Snag Cannot be closed by your organization"
    SNAG_CANNOT_BE_CLOSED_BY_YOU = "Snag Cannot be closed by you"
    SNAG_ALREADY_CLOSED = "This snag is already closed"
    NOT_FOUND = "Not Found"
    ALREADY_LATCHED = "Already Latched"
    NAME_NOT_MATCHED = "name not matched"
    UNABLE_TO_UPLOAD_FILE = "Unable to upload file"
    UNABLE_TO_SAVE_FILE = "Unable to save file"
    CANNOT_ASSIGN_ALREADY_RESOLVED = "Cannot Un-assign, as snag already resolved"
    APP_SEARCH = "if app_search field specified- do not use title,code"
    NOT_A_VALID_CHOICE = "not a valid choice"
    SNAG_CANNOT_BE_DELETED_BY_YOUR_ORG = "Snag Cannot be deleted by your organization"
    SNAG_CANNOT_BE_DELETED_ANYMORE = "Snag Cannot be deleted anymore"
    SNAG_CANNOT_BE_EDITED_BY_YOUR_ORG = "Snag Cannot be edited by your organization"
    SNAG_CANNOT_BE_DELETED_AS_ALREADY_ALLOTTED_OR_COMMITTED = (
        "Snag Cannot be deleted as it is already allotted and committed"
    )
    ASSIGNMENT_TIMELINE_NOT_FOUND = "AssignmentTimeline Not Found against SnagAssignment"
    SNAG_CANNOT_BE_DELETED_AS_ALREADY_ASSIGNED = "Snag cannot be deleted as it is already assigned"
    NO_SNAG_ASSIGNED_BY_YOUR_ORG = "No snag assigned by your organization"
    NO_SNAG_ASSIGNED_TO_YOUR_ORG = "No snag assigned to your organization"
    ASSIGNMENTS_OF_SOME_SELECTED_SNAGS_ALREADY_CLOSED = "Assignments of some selected snags already closed"
    SOME_SELECTED_SNAGS_ARE_NO_MORE_PENDING = "Some selected snags are no more pending"
    SNAG_REPORTER_CONFIG_DATA_COULD_NOT_BE_UPDATED = "Snag reporter could not be updated"


class SnagException(BaseValidationError):
    def __init__(
        self,
        message: str,
        key=api_settings.NON_FIELD_ERRORS_KEY,
    ):
        super().__init__({key: _(message)})


class SnagFileError(RollingBannersException):
    pass


class SnagFileUploadValidationError(SnagException):
    pass


class SnagBulkException(BaseValidationError):
    def __init__(
        self,
        message: str,
        key=api_settings.NON_FIELD_ERRORS_KEY,
    ):
        super().__init__({key: _(message)})


class SnagConfigurationException(BaseValidationError):
    pass


class InvalidSnagConfigurationException(SnagConfigurationException):
    pass


class NoSnagConfiguration(SnagConfigurationException):
    pass


class MultipleDefaultSnagReporterException(InvalidSnagConfigurationException):
    pass


class NoDefaultSnagReporterException(InvalidSnagConfigurationException):
    pass


class NoSnagReporterCheckedException(InvalidSnagConfigurationException):
    pass
