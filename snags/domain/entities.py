from dataclasses import dataclass
from typing import List, Optional

from snags.data.choices import SnagRoleChoices


@dataclass
class SnagReporterData:
    key: SnagRoleChoices
    name: Optional[str]
    is_checked: bool
    is_default: bool
    can_link_snag_items: bool = True


@dataclass(frozen=True)
class OrganizationSnagReporterConfigData:
    reporter_options: List[SnagReporterData]


@dataclass
class SnagData:
    location: str
    description: str
    snag_source: str
    snag_title: str
