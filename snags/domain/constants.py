from common.constants import BaseEnum
from microcontext.choices import MicroContextActions
from snags.data.choices import SnagRoleChoices

SnagRoleOrganizationMapping = {
    # NOTE: Need to change this if new snag creator role is added
    SnagRoleChoices.SNAG_FOR_CLIENT: "Client Organization",
    SnagRoleChoices.SNAG_FOR_ME: "My Organization",
}


class SnagExcelImportColumns(BaseEnum):
    SNAG_SOURCE = "snag_source"
    SNAG_TITLE = "snag_title"
    DESCRIPTION = "description"
    LOCATION = "location"


class SnagExcelFileOutputColumns(BaseEnum):
    SNAG_SOURCE = "Snag Source"
    SNAG_TITLE = "Snag Title"
    DESCRIPTION = "Description"
    LOCATION = "Location"


SNAG_EXCEL_IMPORT_COLUMNS = {
    SnagExcelFileOutputColumns.SNAG_SOURCE.value: SnagExcelImportColumns.SNAG_SOURCE.value,
    SnagExcelFileOutputColumns.SNAG_TITLE.value: SnagExcelImportColumns.SNAG_TITLE.value,
    SnagExcelFileOutputColumns.DESCRIPTION.value: SnagExcelImportColumns.DESCRIPTION.value,
    SnagExcelFileOutputColumns.LOCATION.value: SnagExcelImportColumns.LOCATION.value,
}

SNAG_SOURCE_MAPPING = {value: key for key, value in SnagRoleOrganizationMapping.items()}


SnagActions = [
    MicroContextActions.ASSIGN,
    MicroContextActions.ALLOT_USER_AND_COMMIT_TIMELINE,
    MicroContextActions.MARKED_UNRESOLVED,
    MicroContextActions.BULK_ASSIGN,
    MicroContextActions.BULK_ALLOT_USER_AND_COMMIT_TIMELINE,
]
