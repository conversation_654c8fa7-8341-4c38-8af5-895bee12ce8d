import csv
import json
from collections import defaultdict
from dataclasses import asdict
from datetime import datetime
from functools import partial
from io import BytesIO, <PERSON><PERSON>
from typing import Dict, List, Optional

import openpyxl
import structlog
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Q
from django.db.models.functions import Lower
from django.db.transaction import on_commit
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from openpyxl import Workbook
from openpyxl.utils import quote_sheetname
from openpyxl.writer.excel import save_virtual_workbook

from authorization.domain.constants import Permissions
from common.events.constants import Events
from common.events.services import trigger_event
from common.events.snags import (
    SnagAllotAndTimelineCommitEventData,
    SnagAssignedEventData,
    SnagBulkAllotAndTimelineCommitEventData,
    SnagBulkAssignedEventData,
    SnagPOCAllotedEventData,
    SnagUnresolvedEventData,
)
from common.excel_file_generation import (
    create_excel_file,
    excel_data_file_with_no_record,
    get_excel_data_validation,
)
from common.exceptions import ExcelError, MultipleTrueValueError, NoTrueValueError
from common.services import model_update
from common.utils import check_if_only_one_truth_value_in_list
from core.helpers import OrgPermissionHelper
from core.models import Organization, User
from project.data.models import Project
from project.domain.helpers import ProjectPermissionHelper
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.choices import (
    ClosureStatusChoices,
    ResolutionStatusChoices,
    SnagRoleChoices,
    StatusFilterChoices,
)
from snags.data.models import (
    AssignmentElementMapping,
    AssignmentTimeline,
    AssignmentTimelinePreviewFile,
    OrganizationSnagReporter,
    Snag,
    SnagAssignment,
    SnagPreviewFile,
    SnagSupportingDocument,
    SnagUpdateHistory,
)
from snags.data.selectors import (
    get_open_snag_assignments_from_org,
    get_snag_list_from_ids,
    get_snag_project_client,
    get_snag_serial_number,
    snag_reporter_options_fetch,
    snags_category_mappings_get,
    snags_fetch_all_with_annotations,
)
from snags.domain.constants import (
    SNAG_SOURCE_MAPPING,
    SnagExcelImportColumns,
    SnagRoleOrganizationMapping,
)
from snags.domain.entities import OrganizationSnagReporterConfigData, SnagData, SnagReporterData
from snags.domain.exceptions import (
    ExceptionConstant,
    MultipleDefaultSnagReporterException,
    NoDefaultSnagReporterException,
    NoSnagConfiguration,
    NoSnagReporterCheckedException,
    SnagException,
)
from snags.interface.serializers import SnagExcelSerializer
from snags.remark.data.models import Remark

logger = structlog.getLogger(__name__)


def snag_category_mappings():
    snags_category_mappings_list = snags_category_mappings_get()

    category_to_sub_category_dict = defaultdict(list)
    subcategory_to_category_dict = defaultdict(list)

    for category in snags_category_mappings_list:
        category_to_sub_category_dict[HashIdConverter.encode(category.snag_category_id)].append(
            HashIdConverter.encode(category.snag_subcategory_id)
        )
        subcategory_to_category_dict[HashIdConverter.encode(category.snag_subcategory_id)].append(
            HashIdConverter.encode(category.snag_category_id)
        )

    return category_to_sub_category_dict, subcategory_to_category_dict


def filter_snag_object_on_type_basis(queryset, filter_type: StatusFilterChoices):
    objs_id_list = []
    opened = set()
    closed = set()
    resolved = set()
    unresolved = set()

    for obj in queryset:
        if obj.resolution_status == ResolutionStatusChoices.UNRESOLVED:
            unresolved.add(obj.id)
        else:
            resolved.add(obj.id)
        if obj.closure_status == ClosureStatusChoices.OPEN:
            opened.add(obj.id)
        else:
            closed.add(obj.id)

    open_resolved = opened.intersection(resolved)
    open_unresolved = opened.intersection(unresolved)
    closed_resolved = closed.intersection(resolved)
    closed_unresolved = closed.intersection(unresolved)
    if StatusFilterChoices.OPEN_UNRESOLVED in filter_type:
        objs_id_list.extend(list(open_unresolved))
    if StatusFilterChoices.OPEN_RESOLVED in filter_type:
        objs_id_list.extend(list(open_resolved))
    if StatusFilterChoices.CLOSED in filter_type:
        objs_id_list.extend(list(closed))
    if StatusFilterChoices.CLOSED_UNRESOLVED in filter_type:
        objs_id_list.extend(list(closed_unresolved))
    if StatusFilterChoices.CLOSED_RESOLVED in filter_type:
        objs_id_list.extend(list(closed_resolved))

    return objs_id_list


def snag_history_create(snag_id: int, user_id: int, save: bool):
    snag_update_history = SnagUpdateHistory(snag_id=snag_id, created_by_id=user_id, created_at=timezone.now())
    if save:
        snag_update_history.full_clean()
        snag_update_history.save()
    return snag_update_history


def snag_create(
    project_id: int,
    title: str,
    description: str,
    location: str,
    user_id: int,
    user_org_id: int,
    snag_role: bool,
    snag_category_id: int = None,
    snag_subcategory_id: int = None,
):
    created_for_org_id = None
    if snag_role == SnagRoleChoices.SNAG_FOR_ME:
        created_for_org_id = user_org_id
    elif snag_role == SnagRoleChoices.SNAG_FOR_CLIENT:
        created_for_org_id = get_snag_project_client(project_id=project_id, organization_id=user_org_id).id
    serial_number = get_snag_serial_number(project_id=project_id)
    created_snag = Snag(
        project_id=project_id,
        serial_number=serial_number,
        title=title,
        description=description,
        location=location,
        created_at=timezone.now(),
        created_by_id=user_id,
        snag_subcategory_id=snag_subcategory_id,
        snag_category_id=snag_category_id,
        created_by_org_id=user_org_id,
        created_for_org_id=created_for_org_id,
    )
    created_snag.full_clean()
    created_snag.save()
    snag_history_create(snag_id=created_snag.id, user_id=user_id, save=True)
    return created_snag


def snag_bulk_create(
    project_id: int,
    snag_data: List[SnagData],
    user_id: int,
    org_id: int,
):
    snag_objects: List[Snag] = []

    current_serial_number = get_snag_serial_number(project_id=project_id)
    for i, data in enumerate(snag_data, start=current_serial_number):
        snag_role = data.snag_source
        if snag_role in SNAG_SOURCE_MAPPING:
            snag_role = SNAG_SOURCE_MAPPING[snag_role]

        created_for_org_id = None
        if snag_role == SnagRoleChoices.SNAG_FOR_ME:
            created_for_org_id = org_id
        elif snag_role == SnagRoleChoices.SNAG_FOR_CLIENT:
            created_for_org_id = get_snag_project_client(project_id=project_id, organization_id=org_id).id
        created_snag = Snag(
            project_id=project_id,
            serial_number=i,
            title=data.snag_title,
            description=data.description,
            location=data.location,
            created_at=timezone.now(),
            created_by_id=user_id,
            created_by_org_id=org_id,
            created_for_org_id=created_for_org_id,
        )
        snag_objects.append(created_snag)

    Snag.objects.bulk_create(snag_objects)
    snag_bulk_create_histories(snag_objects, user_id)

    return snag_objects


def snag_bulk_create_histories(snag_objects: List[Snag], user_id: int):
    snag_histories = []
    for created_snag in snag_objects:
        snag_history = snag_history_create(snag_id=created_snag.id, user_id=user_id, save=False)
        snag_histories.append(snag_history)
    SnagUpdateHistory.objects.bulk_create(snag_histories)


def snag_update(
    snag: Snag,
    title: str,
    description: str,
    location: str,
    user_id: int,
    snag_category_id: int = None,
    snag_subcategory_id: int = None,
):
    allowed_fields = [
        "title",
        "description",
        "location",
        "snag_category_id",
        "snag_subcategory_id",
    ]
    instance, _, _ = model_update(
        instance=snag,
        fields=allowed_fields,
        data={
            "title": title,
            "description": description,
            "location": location,
            "snag_category_id": snag_category_id,
            "snag_subcategory_id": snag_subcategory_id,
        },
        updated_by_id=user_id,
    )

    return instance


def snag_preview_files_delete(preview_file_ids: list, user_id: int):
    return SnagPreviewFile.objects.filter(id__in=preview_file_ids).update(
        deleted_at=timezone.now(), deleted_by_id=user_id
    )


def snag_supporting_docs_delete(snag_docs_ids: list, user_id: int):
    return SnagSupportingDocument.objects.filter(id__in=snag_docs_ids).update(
        deleted_at=timezone.now(), deleted_by_id=user_id
    )


def snag_preview_files_create(created_snag: Snag, preview_files: list, user_id: int):
    snag_preview_files = []
    for preview_file in preview_files:
        snag_preview_files.append(
            SnagPreviewFile(
                snag=created_snag,
                name=preview_file["name"],
                type=preview_file["type"],
                uploaded_by_id=user_id,
            )
        )
    created_preview_files = SnagPreviewFile.objects.bulk_create(snag_preview_files)
    return created_preview_files


def snag_supporting_documents_create(created_snag: Snag, supporting_documents: list, user_id: int):
    snag_supporting_documents = []
    for supporting_document in supporting_documents:
        snag_supporting_documents.append(
            SnagSupportingDocument(
                snag=created_snag,
                name=supporting_document["name"],
                type=supporting_document["type"],
                uploaded_by_id=user_id,
            )
        )
    created_supporting_documents = SnagSupportingDocument.objects.bulk_create(snag_supporting_documents)
    return created_supporting_documents


def get_snag_update_history(snag_id: int):
    snag_update_history = SnagUpdateHistory.objects.filter(snag_id=snag_id)
    return snag_update_history


def snag_delete(snag_id: int, user_id: int, organization_id: int):
    Snag.objects.filter(
        Q(Q(created_for_org_id=organization_id) | Q(created_by_org_id=organization_id)), id=snag_id
    ).available().update(deleted_at=timezone.now(), deleted_by_id=user_id)


def snag_assign(
    snag_id: int,
    assign_to_id: int,
    user_id: int,
    boq_element_list: list,
    organization_id: int,
    date_of_closure: datetime.date,
):
    assigned_snag = SnagAssignment(
        snag_id=snag_id,
        date_of_closure=date_of_closure,
        org_from_id=organization_id,
        org_to_id=assign_to_id,
        created_by_id=user_id,
    )
    assigned_snag.full_clean()
    assigned_snag.save()

    saved_elements = set(
        AssignmentElementMapping.objects.filter(snag_assignment_id=assigned_snag.id)
        .available()
        .values_list("boq_element_id", flat=True)
    )

    if boq_element_list:
        boq_elements = []
        for boq_element in boq_element_list:
            if boq_element not in saved_elements:
                boq_elements.append(
                    AssignmentElementMapping(
                        boq_element_id=boq_element, snag_assignment_id=assigned_snag.pk, created_by_id=user_id
                    )
                )
        AssignmentElementMapping.objects.bulk_create(boq_elements)
    return assigned_snag


def snag_assignment_update(
    snag_assignment: SnagAssignment,
    user_id: int,
    boq_element_list: list,
    date_of_closure: datetime.date,
):
    allowed_fields = ["date_of_closure"]
    instance, _, _ = model_update(
        instance=snag_assignment,
        fields=allowed_fields,
        data={"date_of_closure": date_of_closure},
        updated_by_id=user_id,
    )

    saved_elements = set(
        AssignmentElementMapping.objects.filter(snag_assignment_id=snag_assignment.pk)
        .available()
        .values_list("boq_element_id", flat=True)
    )

    if boq_element_list:
        boq_elements = []
        for boq_element in boq_element_list:
            if boq_element not in saved_elements:
                boq_elements.append(
                    AssignmentElementMapping(
                        boq_element_id=boq_element, snag_assignment_id=instance.id, created_by_id=user_id
                    )
                )
        AssignmentElementMapping.objects.bulk_create(boq_elements)
    return instance


def create_assignment_timeline(
    created_by_id: int,
    snag_assignment_id: int,
    allotted_poc_id: int = None,
    committed_at: datetime = None,
    resolution_status: str = None,
    closure_status: str = None,
    resolution_remark: str = None,
    timeline_remark_id: int = None,
):
    timeline_obj = AssignmentTimeline(
        allotted_poc_id=allotted_poc_id,
        committed_at=committed_at,
        resolution_status=resolution_status,
        closure_status=closure_status,
        created_by_id=created_by_id,
        snag_assignment_id=snag_assignment_id,
        resolution_remark=resolution_remark,
        timeline_remark_id=timeline_remark_id,
    )
    timeline_obj.full_clean(exclude=["timeline_remark"])
    timeline_obj.save()
    return timeline_obj


def timeline_preview_file_create(timeline_id, name, file_url=None, save=True, clean=False):
    preview_file_obj = AssignmentTimelinePreviewFile()
    preview_file_obj.snag_assignment_timeline_id = timeline_id
    preview_file_obj.file = file_url
    preview_file_obj.name = name
    if clean:
        preview_file_obj.full_clean()
    if save:
        preview_file_obj.save()
    return preview_file_obj


def timeline_preview_file_bulk_create(preview_files_list: list, timeline_id: int):
    objs = []
    for preview_file in preview_files_list:
        objs.append(
            timeline_preview_file_create(
                timeline_id=timeline_id,
                name=preview_file.get("name"),
                file_url=preview_file.get("file_url"),
                save=False,
                clean=False,
            )
        )
    return AssignmentTimelinePreviewFile.objects.bulk_create(objs=objs)


def get_filter_config(queryset, organization_id: int, project_id: int):
    created_by_distinct = queryset.distinct("created_by")
    snags_category_distinct = queryset.distinct("snag_category")
    snags_subcategory_distinct = queryset.distinct("snag_subcategory")
    snag_id_list = queryset.values_list("id", flat=True)
    snags_location_distinct = (
        queryset.annotate(lower_location=Lower("location"))
        .distinct("lower_location")
        .values_list("lower_location", flat=True)
    )

    org_to_ids = (
        SnagAssignment.objects.filter(
            Q(org_from_id=organization_id) | Q(org_to_id=organization_id),
            snag_id__in=snag_id_list,
        )
        .distinct("org_to_id")
        .values_list("org_to_id", flat=True)
    )

    organizations = Organization.objects.filter(Q(id__in=org_to_ids))
    alloted_poc_distinct = (
        SnagAssignment.objects.filter(snag_id__in=snag_id_list, org_to_id=organization_id, allotted_poc__isnull=False)
        .available()
        .distinct("allotted_poc")
        .select_related("allotted_poc")
        .only("allotted_poc")
    )

    return (
        created_by_distinct,
        snags_category_distinct,
        snags_subcategory_distinct,
        organizations,
        alloted_poc_distinct,
        snags_location_distinct,
    )


class SnagButtonFlag:
    def __init__(
        self, assignment_id, card_assigned_to, card_assigned_from, card_resolution_status, card_closure_status
    ):
        self.card_assignment_id = assignment_id
        self.card_assigned_to = card_assigned_to
        self.card_assigned_from = card_assigned_from
        self.resolution_status = card_resolution_status
        self.closure_status = card_closure_status

    def mark_resolved_button_flag(
        self,
        organization_id: int,
    ):
        return bool(
            self.resolution_status
            in [
                ResolutionStatusChoices.UNRESOLVED,
                ResolutionStatusChoices.NOT_APPLICABLE,
                ResolutionStatusChoices.NOT_RESOLVABLE,
            ]
            and self.closure_status == ClosureStatusChoices.OPEN
            and self.card_assigned_to == organization_id
        )

    def mark_not_applicable_button_flag(self, organization_id: int):
        return bool(
            self.resolution_status
            in [
                ResolutionStatusChoices.UNRESOLVED,
                ResolutionStatusChoices.RESOLVED,
                ResolutionStatusChoices.NOT_RESOLVABLE,
            ]
            and self.closure_status == ClosureStatusChoices.OPEN
            and self.card_assigned_to == organization_id
        )

    def mark_not_resolvable_button_flag(self, organization_id: int):
        return bool(
            self.resolution_status
            in [
                ResolutionStatusChoices.UNRESOLVED,
                ResolutionStatusChoices.RESOLVED,
                ResolutionStatusChoices.NOT_APPLICABLE,
            ]
            and self.closure_status == ClosureStatusChoices.OPEN
            and self.card_assigned_to == organization_id
        )

    def mark_unresolved_button_flag(self, organization_id: int):
        if self.resolution_status == ResolutionStatusChoices.RESOLVED:
            return bool(self.closure_status == ClosureStatusChoices.OPEN)

        return bool(
            self.resolution_status
            in [
                ResolutionStatusChoices.NOT_APPLICABLE,
                ResolutionStatusChoices.NOT_RESOLVABLE,
            ]
            and self.closure_status == ClosureStatusChoices.OPEN
            and self.card_assigned_from == organization_id
        )

    def pass_to_client_button_flag(self, parent_assignment, child_id_list: list):
        return bool(
            parent_assignment
            and self.card_assignment_id in child_id_list
            and self.resolution_status
            in [
                ResolutionStatusChoices.RESOLVED,
                ResolutionStatusChoices.NOT_RESOLVABLE,
                ResolutionStatusChoices.NOT_APPLICABLE,
            ]
            and parent_assignment.get("resolution_status") == ResolutionStatusChoices.UNRESOLVED
            and self.closure_status == ClosureStatusChoices.OPEN
            and parent_assignment.get("closure_status") == ClosureStatusChoices.OPEN
        )

    def mark_un_assign_button_flag(self, organization_id: int, committed_at, allotted_poc_id: int):
        return bool(
            self.card_assigned_from == organization_id
            and self.resolution_status == ResolutionStatusChoices.UNRESOLVED
            and self.closure_status == ClosureStatusChoices.OPEN
            and not committed_at
            and not allotted_poc_id
        )

    def mark_close_button_flag(self, organization_id: int):
        return bool(self.card_assigned_from == organization_id and self.closure_status == ClosureStatusChoices.OPEN)

    def snag_allot_poc_and_commit_timeline(self, organization_id: int):
        return bool(
            self.card_assigned_to == organization_id
            and self.resolution_status == ResolutionStatusChoices.UNRESOLVED
            and self.closure_status == ClosureStatusChoices.OPEN
        )

    def edit_assignment_button_flag(self, organization_id: int):
        return bool(self.card_assigned_from == organization_id) and bool(
            self.closure_status != ClosureStatusChoices.CLOSED
        )


def snag_assignment_button_service(snag_id: int, organization_id: int, project_id: int, user: User):
    snag_assignments = (
        SnagAssignment.objects.filter(snag_id=snag_id)
        .available()
        .filter(Q(org_to_id=organization_id) | Q(org_from_id=organization_id))
        .values(
            "org_to_id",
            "org_from_id",
            "id",
            "resolution_status",
            "closure_status",
            "committed_at",
            "allotted_poc_id",
        )
    )

    parent_assignment = None
    child_id_list = []

    data_list = []
    for assignment in snag_assignments:
        if assignment.get("org_to_id") == organization_id:
            parent_assignment = assignment
        else:
            child_id_list.append(assignment.get("id"))

    for assignment in snag_assignments:
        assignment_id = assignment.get("id")
        card_assigned_to = assignment.get("org_to_id")
        card_assigned_from = assignment.get("org_from_id")
        card_resolution_status = assignment.get("resolution_status")
        card_closure_status = assignment.get("closure_status")
        snag_button_visibility = SnagButtonFlag(
            assignment_id=assignment_id,
            card_assigned_from=card_assigned_from,
            card_assigned_to=card_assigned_to,
            card_resolution_status=card_resolution_status,
            card_closure_status=card_closure_status,
        )
        data_dict = {
            "id": assignment_id,
            "mark_resolved": snag_button_visibility.mark_resolved_button_flag(
                organization_id=organization_id,
            ),
            "mark_unresolved": snag_button_visibility.mark_unresolved_button_flag(organization_id=organization_id),
            "not_applicable": snag_button_visibility.mark_not_applicable_button_flag(organization_id=organization_id),
            "not_resolvable": snag_button_visibility.mark_not_resolvable_button_flag(organization_id=organization_id),
            "pass_to_client": snag_button_visibility.pass_to_client_button_flag(
                child_id_list=child_id_list,
                parent_assignment=parent_assignment,
            ),
            "mark_close": (
                snag_button_visibility.mark_close_button_flag(organization_id=organization_id)
                if ProjectPermissionHelper.has_permission(
                    user=user, project_id=project_id, permission=Permissions.CAN_CLOSE_SNAG
                )
                else False
            ),
            "mark_un_assign": snag_button_visibility.mark_un_assign_button_flag(
                organization_id=organization_id,
                committed_at=assignment.get("committed_at"),
                allotted_poc_id=assignment.get("allotted_poc_id"),
            ),
            "allot_poc": snag_button_visibility.snag_allot_poc_and_commit_timeline(organization_id=organization_id),
            "commit_timeline": snag_button_visibility.snag_allot_poc_and_commit_timeline(
                organization_id=organization_id
            ),
            "parent_assignment_id": parent_assignment.get("id") if parent_assignment else None,
            "can_edit_assignment": snag_button_visibility.edit_assignment_button_flag(organization_id=organization_id),
        }
        data_list.append(data_dict)

    return data_list


def pass_to_client_prefilled_data(
    snag_id: int, snag_assignment_id: int, organization_id: int, project_id: int, user: User
) -> dict:
    data = dict()
    assignment_flags = snag_assignment_button_service(
        snag_id=snag_id, organization_id=organization_id, project_id=project_id, user=user
    )
    assignment_dict = {assignment["id"]: assignment for assignment in assignment_flags}
    if assignment_dict and assignment_dict[snag_assignment_id]["pass_to_client"]:
        data["parent_assignment_id"] = assignment_dict[snag_assignment_id]["parent_assignment_id"]
    else:
        raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

    assignment_timeline = (
        AssignmentTimeline.objects.filter(snag_assignment_id=snag_assignment_id)
        .prefetch_related("assignment_timeline_preview_files")
        .filter(
            resolution_status__in=[
                ResolutionStatusChoices.RESOLVED,
                ResolutionStatusChoices.NOT_RESOLVABLE,
                ResolutionStatusChoices.NOT_APPLICABLE,
            ]
        )
    ).last()

    if not assignment_timeline:
        raise SnagException(ExceptionConstant.ASSIGNMENT_TIMELINE_NOT_FOUND)

    preview_files = assignment_timeline.assignment_timeline_preview_files.all()

    data["preview_files"] = preview_files if preview_files else None
    data["remark"] = assignment_timeline.resolution_remark
    data["resolution_status"] = assignment_timeline.resolution_status
    return data


def update_assignment_card(
    snag_assignment_id: int,
    updated_boq_elements: list,
    deleted_boq_elements: list,
    date_of_closure: datetime.date,
    allotted_poc_id: Optional[int],
    committed_at: Optional[datetime],
    user_id: int,
    project_id: int,
):
    if deleted_boq_elements:
        AssignmentElementMapping.objects.filter(
            snag_assignment_id=snag_assignment_id, boq_element__in=deleted_boq_elements
        ).soft_delete(user_id=user_id)
    if updated_boq_elements:
        boq_element_list = []
        for updated_boq_element in updated_boq_elements:
            boq_element_list.append(
                AssignmentElementMapping(
                    boq_element_id=updated_boq_element, snag_assignment_id=snag_assignment_id, created_by_id=user_id
                )
            )
        AssignmentElementMapping.objects.bulk_create(objs=boq_element_list)
    snag_assignment = SnagAssignment.objects.filter(id=snag_assignment_id).first()
    updated_assignment, _, _ = model_update(
        instance=snag_assignment,
        fields=["date_of_closure"],
        data={"date_of_closure": date_of_closure},
        updated_by_id=user_id,
    )
    snag_allotment_and_timeline_create(
        snag_assignment=snag_assignment,
        snag_id=snag_assignment.snag_id,
        user_id=user_id,
        allotted_poc_id=allotted_poc_id,
        committed_at=committed_at,
        project_id=project_id,
    )
    return updated_assignment


def snag_excel_file(project_id: int, organization_id: int, user: User, client_org_id: int, creator_org_id: int):
    can_link_snag_item_permission = ProjectPermissionHelper.has_permission(
        user=user, project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
    )
    queryset = snags_fetch_all_with_annotations(
        project_id=project_id,
        organization_id=organization_id,
        can_link_snag_item_permission=can_link_snag_item_permission,
        client_org_id=client_org_id,
        creator_org_id=creator_org_id,
    ).order_by("-id")
    project = Project.objects.filter(id=project_id).first()

    filename = f"{project.job_id}_{project.name}_Snag_List"
    excel_data = [
        {
            "sheet_title": "Snags",
            "sheet_data": (
                SnagExcelSerializer(
                    queryset,
                    many=True,
                ).data
                if queryset
                else excel_data_file_with_no_record(sheet_name="Snags")
            ),
            "with_serial_number": bool(queryset),
            "image-fields": [
                "preview_file",
                "resolved_preview_file",
            ],
        }
    ]

    return create_excel_file(data=excel_data), filename


def create_bulk_remark_service(*, assignments: List[SnagAssignment], user_id: int, remark_data: Dict):
    remark_objs = []
    for assignment in assignments:
        remark = Remark()
        remark.blocks = remark_data
        remark_objs.append(remark)

    remarks = Remark.objects.bulk_create(remark_objs)

    timeline_objs = []
    for assignment, remark in zip(assignments, remarks):
        timeline_obj = AssignmentTimeline(
            allotted_poc_id=None,
            committed_at=None,
            resolution_status=None,
            closure_status=None,
            created_by_id=user_id,
            snag_assignment_id=assignment.pk,
            resolution_remark=None,
            timeline_remark_id=remark.pk,
        )
        timeline_objs.append(timeline_obj)
    AssignmentTimeline.objects.bulk_create(timeline_objs)


def bulk_assign_snag(
    snag_id_list: list,
    assign_to_id: int,
    user_id: int,
    organization_id: int,
    boq_element_list: list[int] = None,
    allotted_poc_id: Optional[int] = None,
    date_of_closure: Optional[datetime.date] = None,
    committed_at: Optional[datetime] = None,
    remark_blocks: Optional[list] = None,
):
    saved_assignments: List[SnagAssignment] = (
        SnagAssignment.objects.filter(snag_id__in=snag_id_list, org_to_id=assign_to_id).available().all()
    )
    to_create_assignments = []

    saved_snag_id_set = {
        (int(saved_assignment.snag_id), int(saved_assignment.org_to_id), int(saved_assignment.org_from_id))
        for saved_assignment in saved_assignments
    }

    logger.info("Already Saved Snag", saved_snag_id_set=saved_snag_id_set)

    for snag_id in snag_id_list:
        if (int(snag_id), int(assign_to_id), int(organization_id)) not in saved_snag_id_set:
            to_create_assignments.append(
                SnagAssignment(
                    snag_id=snag_id,
                    org_from_id=organization_id,
                    org_to_id=assign_to_id,
                    created_by_id=user_id,
                    allotted_poc_id=None,
                    date_of_closure=date_of_closure,
                )
            )
    logger.info("To create Assignments", to_create_assignments=to_create_assignments)
    assigned_snag_list = []
    if to_create_assignments:
        assigned_snag_list = SnagAssignment.objects.bulk_create(objs=to_create_assignments)
    all_assignments = list(saved_assignments) + list(assigned_snag_list)
    if allotted_poc_id or committed_at:
        bulk_allot_or_commit_snags(
            allotted_poc_id=allotted_poc_id,
            snag_assignment_list=all_assignments,
            user_id=user_id,
            committed_at=committed_at,
        )
    if date_of_closure:
        SnagAssignment.objects.filter(id__in=[saved_assignment.id for saved_assignment in saved_assignments]).update(
            date_of_closure=date_of_closure, updated_by_id=user_id
        )
    if remark_blocks:
        create_bulk_remark_service(assignments=all_assignments, user_id=user_id, remark_data=remark_blocks)

    if boq_element_list:
        saved_assigned_elements = (
            AssignmentElementMapping.objects.filter(
                snag_assignment_id__in=[saved_assignment.id for saved_assignment in saved_assignments]
            )
            .available()
            .all()
        )
        saved_assigned_element_set: Dict[int, AssignmentElementMapping] = {
            saved_assigned_element.boq_element_id: saved_assigned_element
            for saved_assigned_element in saved_assigned_elements
        }
        logger.info("Saved Assigned Elements", saved_assigned_element_set=saved_assigned_element_set)
        to_create_elements = []
        for snag_assignment in all_assignments:
            for element_id in boq_element_list:
                if element_id not in saved_assigned_element_set:
                    for boq_element in boq_element_list:
                        to_create_elements.append(
                            AssignmentElementMapping(
                                boq_element_id=boq_element, snag_assignment_id=snag_assignment.pk, created_by_id=user_id
                            )
                        )
        logger.info("To create Elements", to_create_elements=to_create_elements)
        if to_create_elements:
            AssignmentElementMapping.objects.bulk_create(objs=to_create_elements)

    return assigned_snag_list


def assignment_timeline_bulk_create(
    created_by_id: int,
    snag_assignment_list: list,
    allotted_poc_id: int = None,
    committed_at: datetime = None,
    resolution_status: str = None,
    closure_status: str = None,
    is_snag_applicable: bool = None,
    resolution_remark: str = None,
    timeline_remark_id: int = None,
):
    timeline_obj_list = []
    for snag_assignment in snag_assignment_list:
        timeline_obj = AssignmentTimeline(
            allotted_poc_id=allotted_poc_id,
            committed_at=committed_at,
            resolution_status=resolution_status,
            closure_status=closure_status,
            created_by_id=created_by_id,
            snag_assignment_id=snag_assignment.id,
            is_snag_applicable=is_snag_applicable,
            resolution_remark=resolution_remark,
            timeline_remark_id=timeline_remark_id,
        )
        timeline_obj_list.append(timeline_obj)
    created_timeline_obj_list = AssignmentTimeline.objects.bulk_create(objs=timeline_obj_list)

    return created_timeline_obj_list


def check_if_snag_ids_exist(snag_id_list: list) -> bool:
    return len(get_snag_list_from_ids(snag_id_list=snag_id_list)) == len(snag_id_list)


def organization_id_list_fetch(snag_id_list: list[int]):
    return (
        SnagAssignment.objects.filter(snag_id__in=snag_id_list)
        .values_list("org_to_id", flat=True)
        .distinct("org_to_id")
    )


def close_snags_assigned_by_org(snag_id_list: list, organization_id: int, user_id: int):
    snag_assignment_list = get_open_snag_assignments_from_org(
        snag_id_list=snag_id_list, organization_id=organization_id
    )
    assignment_timeline_bulk_create(
        created_by_id=user_id,
        snag_assignment_list=snag_assignment_list,
        closure_status=ClosureStatusChoices.CLOSED,
    )
    snag_assignment_list.update(
        closure_status=ClosureStatusChoices.CLOSED, closed_at=timezone.now(), updated_by_id=user_id
    )


def bulk_allot_or_commit_snags(allotted_poc_id: int, committed_at: datetime, snag_assignment_list: list, user_id: int):
    snag_assignment_for_update_allotted_poc = []
    snag_assignment_for_update_committed_at = []
    snag_assignments_for_timeline_create = set()
    timeline_obj_list = []
    for snag_assignment in snag_assignment_list:
        if (allotted_poc_id and snag_assignment.allotted_poc_id != allotted_poc_id) or (
            committed_at and snag_assignment.committed_at != committed_at
        ):
            timeline_obj = AssignmentTimeline()
            timeline_obj.created_by_id = user_id
            timeline_obj.snag_assignment_id = snag_assignment.id
            if allotted_poc_id and snag_assignment.allotted_poc_id != allotted_poc_id:
                snag_assignment.allotted_poc_id = allotted_poc_id
                snag_assignment_for_update_allotted_poc.append(snag_assignment)
                snag_assignments_for_timeline_create.add(snag_assignment)
                timeline_obj.allotted_poc_id = allotted_poc_id

            if committed_at and snag_assignment.committed_at != committed_at:
                snag_assignment.committed_at = committed_at
                snag_assignment_for_update_committed_at.append(snag_assignment)
                snag_assignments_for_timeline_create.add(snag_assignment)
                timeline_obj.committed_at = committed_at

            timeline_obj_list.append(timeline_obj)

    SnagAssignment.objects.bulk_update(objs=snag_assignment_for_update_allotted_poc, fields=["allotted_poc_id"])
    SnagAssignment.objects.bulk_update(objs=snag_assignment_for_update_committed_at, fields=["committed_at"])

    AssignmentTimeline.objects.bulk_create(objs=timeline_obj_list)
    if timeline_obj_list:
        on_commit(
            partial(
                snag_bulk_allot_and_timeline_commit,
                project_id=snag_assignment_list[0].snag.project_id,
                snag_ids=[snag_assignment.snag_id for snag_assignment in snag_assignment_list],
                assigned_to_org_id=snag_assignment_list[0].org_to_id,
                allotted_poc_id=allotted_poc_id,
                updated_by_id=user_id,
                committed_at=committed_at,
            )
        )


def snag_bulk_allot_and_timeline_commit(
    project_id: int,
    snag_ids: List[int],
    assigned_to_org_id: int,
    allotted_poc_id: int,
    committed_at: datetime,
    updated_by_id: int,
):
    event_data = SnagBulkAllotAndTimelineCommitEventData(
        project_id=project_id,
        organization_id=assigned_to_org_id,
        snag_ids=snag_ids,
        allotted_to_id=allotted_poc_id,
        committed_at=committed_at.strftime("%d-%m-%Y") if committed_at else None,
        updated_by_id=updated_by_id,
    )
    trigger_event(
        event=Events.SNAG_BULK_ALLOTTED_AND_COMMITTED_TIMELINE,
        event_data=event_data,
    )


def snag_poc_alloted_trigger_event(project_id: int, snag_id: int, poc_user_id: int):
    event_data = SnagPOCAllotedEventData(project_id=project_id, snag_id=snag_id, poc_user_id=poc_user_id)
    trigger_event(event=Events.SNAG_POC_ALLOTED, event_data=event_data)


def snag_allot_and_commit_timeline_trigger_event(
    project_id: int,
    snag_id: int,
    allotted_to_id: int,
    timeline_id: int,
    assignment_id: int,
):
    event_data = SnagAllotAndTimelineCommitEventData(
        project_id=project_id,
        snag_id=snag_id,
        allotted_to_id=allotted_to_id,
        timeline_id=timeline_id,
        snag_assignment_id=assignment_id,
    )
    trigger_event(event=Events.SNAG_ALLOTTED_POC_AND_COMMITTED_TIMELINE, event_data=event_data)


def snag_marked_unresolved_trigger_event(project_id: int, snag_id: int, timeline_id: int, snag_assignment_id: int):
    event_data = SnagUnresolvedEventData(
        project_id=project_id,
        snag_id=snag_id,
        timeline_id=timeline_id,
        snag_assignment_id=snag_assignment_id,
    )
    trigger_event(event=Events.SNAG_UNRESOLVED, event_data=event_data)


def snag_assigned_trigger_event(project_id: int, snag_ids: List[int], assigned_to_org_id: int, assigned_by_org_id: int):
    event_data = SnagAssignedEventData(
        project_id=project_id,
        snag_ids=snag_ids,
        assigned_to_org_id=assigned_to_org_id,
        assigned_by_org_id=assigned_by_org_id,
    )
    trigger_event(event=Events.SNAG_ASSIGNED, event_data=event_data)


def snag_bulk_assign_trigger_event(
    project_id: int, snag_ids: List[int], assigned_to_org_id: int, assigned_by_org_id: int
):
    event_data = SnagBulkAssignedEventData(
        project_id=project_id,
        snag_ids=snag_ids,
        assigned_to_org_id=assigned_to_org_id,
        assigned_by_org_id=assigned_by_org_id,
    )
    trigger_event(event=Events.SNAG_BULK_ASSIGNED, event_data=event_data)


def validate_configuration_conditions(attrs):
    if not attrs.get("reporter_options"):
        raise NoSnagConfiguration("You must provide snag reporter configuration.")
    try:
        check_if_only_one_truth_value_in_list([reporter["is_default"] for reporter in attrs.get("reporter_options")])
    except MultipleTrueValueError:
        raise MultipleDefaultSnagReporterException("Only one configuration can be selected as default.")
    except NoTrueValueError:
        raise NoDefaultSnagReporterException("Atleast one configuration should be selected as default.")

    try:
        check_if_only_one_truth_value_in_list([reporter["is_checked"] for reporter in attrs.get("reporter_options")])
    except MultipleTrueValueError:
        pass
    except NoTrueValueError:
        raise NoSnagReporterCheckedException("You must check at least one snag reporter.")


def organization_snag_reporter_options_fetch(org_id: int, is_pure_client: bool):
    if is_pure_client:
        return OrganizationSnagReporterConfigData(reporter_options=pure_client_snag_reporter_options_default_fetch())
    reporter_options = snag_reporter_options_fetch(org_id=org_id)
    if reporter_options:
        reporter_options = [
            SnagReporterData(
                key=reporter_option.get("key"),
                name=reporter_option.get("name"),
                is_checked=reporter_option.get("is_checked"),
                is_default=reporter_option.get("is_default"),
            )
            for reporter_option in reporter_options.get("reporter_options")
        ]
    return (
        OrganizationSnagReporterConfigData(reporter_options=reporter_options)
        if reporter_options
        else OrganizationSnagReporterConfigData(reporter_options=snag_reporter_options_default_fetch())
    )


def get_snag_reporter_options(org_id: int, is_pure_client: bool, user: User, creator_org_id: int):
    can_link_snag_item = OrgPermissionHelper.has_permission(
        user=user,
        permission=Permissions.CAN_LINK_SNAG_ITEMS,
    )
    snag_reporter_options = organization_snag_reporter_options_fetch(org_id=org_id, is_pure_client=is_pure_client)
    for reporter_option in snag_reporter_options.reporter_options:
        if not can_link_snag_item:
            if creator_org_id == org_id:
                if reporter_option.key == SnagRoleChoices.SNAG_FOR_ME.value:
                    reporter_option.can_link_snag_items = True
                elif reporter_option.key == SnagRoleChoices.SNAG_FOR_CLIENT.value:
                    reporter_option.can_link_snag_items = False
            else:
                reporter_option.can_link_snag_items = False
    return snag_reporter_options


def snag_reporter_options_update(org_id: int, config_data: OrganizationSnagReporterConfigData, user_id: int):
    if not OrganizationSnagReporter.objects.filter(organization_id=org_id).exists():
        OrganizationSnagReporter.objects.create(organization_id=org_id, reporter_options=config_data)
    else:
        OrganizationSnagReporter.objects.filter(organization_id=org_id).update(
            reporter_options=config_data, updated_by_id=user_id
        )
    return config_data


def snag_reporter_options_default_fetch():
    return [
        SnagReporterData(
            key=SnagRoleChoices.SNAG_FOR_CLIENT.value,
            name=SnagRoleOrganizationMapping[SnagRoleChoices.SNAG_FOR_CLIENT],
            is_checked=True,
            is_default=True,
        ),
        SnagReporterData(
            key=SnagRoleChoices.SNAG_FOR_ME.value,
            name=SnagRoleOrganizationMapping[SnagRoleChoices.SNAG_FOR_ME],
            is_checked=True,
            is_default=False,
        ),
    ]


def pure_client_snag_reporter_options_default_fetch():
    return [
        SnagReporterData(
            key=SnagRoleChoices.SNAG_FOR_ME.value,
            name=SnagRoleOrganizationMapping[SnagRoleChoices.SNAG_FOR_ME],
            is_checked=True,
            is_default=True,
        ),
    ]


def bulk_snag_excel_export(
    project_id: int, organization_id: int, snag_id_list: List[int], user: User, creator_org_id: int, client_org_id: int
):
    if not check_if_snag_ids_exist(snag_id_list=snag_id_list):
        raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)
    can_link_snag_item_permission = OrgPermissionHelper.has_permission(
        user=user,
        permission=Permissions.CAN_LINK_SNAG_ITEMS,
    ) and ProjectPermissionHelper.has_permission(
        user=user, project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
    )
    selected_snags = (
        snags_fetch_all_with_annotations(
            project_id=project_id,
            organization_id=organization_id,
            can_link_snag_item_permission=can_link_snag_item_permission,
            creator_org_id=creator_org_id,
            client_org_id=client_org_id,
        )
        .filter(id__in=snag_id_list)
        .order_by("-id")
        .select_related("project")
    )

    filename = f"{selected_snags.first().project.job_id}_{selected_snags.first().project.name.replace(' ','_').lower()}_selected_snag_list"  # noqa

    excel_data = [
        {
            "sheet_title": "Snags",
            "sheet_data": (
                SnagExcelSerializer(
                    selected_snags,
                    many=True,
                ).data
                if selected_snags
                else excel_data_file_with_no_record(sheet_name="Snags")
            ),
            "with_serial_number": bool(selected_snags),
            "image-fields": [
                "preview_file",
                "resolved_preview_file",
            ],
        }
    ]

    return create_excel_file(data=excel_data), filename


def snag_excel_template_generate(org_id: int, is_pure_client: bool):
    excel_columns = {
        "A1": "Snag Source",
        "B1": "Snag Title",
        "C1": "Description",
        "D1": "Location",
    }
    snag_config = organization_snag_reporter_options_fetch(org_id=org_id, is_pure_client=is_pure_client)
    snag_source_list = [config.name for config in snag_config.reporter_options if config.is_checked]
    workbook = Workbook()
    dropdowns = workbook.create_sheet("Dropdown Options")
    for index, snag_source in enumerate(snag_source_list, start=1):
        dropdowns[f"A{index}"] = snag_source

    worksheet = workbook.active
    worksheet.title = "Snags Upload Sheet"

    for key, value in excel_columns.items():
        worksheet[key] = value

    for col in worksheet.columns:
        column = col[0].column_letter
        worksheet.column_dimensions[column].width = 30

    snag_source_validation = get_excel_data_validation(
        formula=f"{quote_sheetname('Dropdown Options')}!$A$1:$A${len(snag_source_list)}",
        error_title="Invalid Snag Source",
        error_message="Please select a valid Snag Source from the dropdown list.",
        prompt_title="Snag Source",
        prompt_message="Please select a Snag Source from the dropdown list.",
    )
    snag_source_validation.add("A2:A1048576")
    worksheet.add_data_validation(snag_source_validation)
    workbook.properties.creator = "rdash_snag_v1"
    workbook.properties.title = "snags"
    workbook.properties.version = "v1"
    return save_virtual_workbook(workbook)


def snag_excel_error_csv_generate(*, excel_export_columns: Dict, errors: list):
    errors = json.loads(json.dumps(errors))
    error_data = StringIO()
    rows = [["Row Number", "Error"]]
    row_counter = 1
    total_errors = 0
    for error in errors:
        row_counter += 1
        if error:
            total_errors += 1
            for field in error:
                for message in error.get(field):
                    rows.append([row_counter, f"{excel_export_columns[field]} : {message}"])
    csv.writer(error_data, errors)
    error_data.seek(0)
    byte_buffer = BytesIO()
    byte_buffer.write(error_data.getvalue().encode())
    byte_buffer.seek(0)
    byte_buffer.name = "excel_errors.csv"
    total_success = row_counter - total_errors
    return byte_buffer, total_errors, total_success


def snag_excel_validate_using_openpyxl(
    *, excel_import_columns: dict, file: InMemoryUploadedFile, checked_snag_config: dict
):
    wb_obj = openpyxl.load_workbook(file)
    sheet_obj = wb_obj.active

    file_column_names = []

    for i in range(1, len(excel_import_columns.keys()) + 1):
        cell_obj = sheet_obj.cell(row=1, column=i)
        file_column_names.append(cell_obj.value)

    if tuple(excel_import_columns.keys()) != tuple(file_column_names):
        raise ExcelError(_("Invalid file format."))

    snag_list = []
    skip_position = []

    for i in range(2, sheet_obj.max_row + 1):
        if sheet_obj.cell(row=i, column=1).value or sheet_obj.cell(row=i, column=2).value:
            row = dict()
            row_snag_source = (
                str(sheet_obj.cell(row=i, column=1).value).strip() if sheet_obj.cell(row=i, column=1).value else ""
            )
            if len(checked_snag_config) > 1:
                checked_config_list = list(checked_snag_config.values())
                if row_snag_source not in checked_config_list:
                    raise ExcelError(_("Snag source does not match any existing source"))
            else:
                checked_config = list(checked_snag_config.values())[0]
                if row_snag_source != checked_config:
                    raise ExcelError(
                        _("{snag_source} is not a reporting snag type").format(
                            snag_source=list(checked_snag_config.values())[0]
                        )
                    )
            row[SnagExcelImportColumns.SNAG_SOURCE.value] = row_snag_source
            row[SnagExcelImportColumns.SNAG_TITLE.value] = (
                str(sheet_obj.cell(row=i, column=2).value).strip() if sheet_obj.cell(row=i, column=2).value else ""
            )
            row[SnagExcelImportColumns.DESCRIPTION.value] = (
                str(sheet_obj.cell(row=i, column=3).value).strip() if sheet_obj.cell(row=i, column=3).value else ""
            )
            row[SnagExcelImportColumns.LOCATION.value] = (
                str(sheet_obj.cell(row=i, column=4).value).strip() if sheet_obj.cell(row=i, column=4).value else ""
            )
            row["row_num"] = i
            snag_list.append(row)
        else:
            skip_position.append(i)

    if not len(snag_list):
        raise ExcelError(_("No data found in file."))
    return snag_list, skip_position


def fetch_checked_snag_config(is_pure_client: bool, org_id: int) -> Dict[str, str]:
    # if is_pure_client:
    snag_config = asdict(organization_snag_reporter_options_fetch(org_id=org_id, is_pure_client=is_pure_client))
    # else:
    #     snag_config = organization_snag_reporter_options_fetch(org_id=org_id, is_pure_client=is_pure_client)

    checked_snag_config = {
        config.get("key"): config.get("name") for config in snag_config["reporter_options"] if config["is_checked"]
    }
    return checked_snag_config


def snag_allotment_and_timeline_create(
    *,
    snag_assignment: SnagAssignment,
    snag_id: int,
    user_id: int,
    allotted_poc_id: Optional[int],
    committed_at: Optional[datetime],
    project_id: int,
) -> Optional[AssignmentTimeline]:
    if not snag_assignment:
        raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

    if snag_assignment.resolution_status == ResolutionStatusChoices.RESOLVED:
        raise SnagException(ExceptionConstant.SNAG_ALREADY_RESOLVED)

    if snag_assignment.closure_status == ClosureStatusChoices.CLOSED:
        raise SnagException(ExceptionConstant.SNAG_ALREADY_CLOSED)

    update_fields = []
    send_callback = False
    if allotted_poc_id and snag_assignment.allotted_poc_id != allotted_poc_id:
        snag_assignment.allotted_poc_id = allotted_poc_id
        update_fields.append("allotted_poc_id")
        send_callback = True

    if committed_at and snag_assignment.committed_at != committed_at:
        snag_assignment.committed_at = committed_at
        update_fields.append("committed_at")

    timeline = None
    if update_fields:
        snag_assignment.save(update_fields=update_fields)
        timeline = create_assignment_timeline(
            created_by_id=user_id,
            snag_assignment_id=snag_assignment.pk,
            allotted_poc_id=allotted_poc_id,
            committed_at=committed_at,
        )
        on_commit(
            partial(
                snag_allot_and_commit_timeline_trigger_event,
                project_id=project_id,
                snag_id=snag_id,
                allotted_to_id=allotted_poc_id,
                timeline_id=timeline.id,
                assignment_id=snag_assignment.id,
            )
        )
        if send_callback:
            on_commit(
                partial(
                    snag_poc_alloted_trigger_event,
                    project_id=snag_assignment.snag.project_id,
                    snag_id=snag_id,
                    poc_user_id=allotted_poc_id,
                )
            )
    return timeline
