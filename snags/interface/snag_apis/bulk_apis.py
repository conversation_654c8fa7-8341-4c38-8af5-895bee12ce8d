from functools import partial

from django.db import transaction
from django.db.transaction import on_commit
from django.http import HttpResponse
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from authorization.domain.constants import Permissions
from common.serializers import BaseSerial<PERSON>, CustomDate<PERSON>ield, HashIdField, HashIdListField
from project.domain.helpers import ProjectPermissionHelper
from project.interface.apis.internal.apis import ProjectBaseApi
from snags.data.selectors import (
    get_snag_assignments_from_org,
    get_snag_assignments_to_org,
    if_exists_resolved_or_closed_snag_assignments_to_org,
)
from snags.domain.exceptions import ExceptionConstant, SnagBulkException, SnagException
from snags.domain.services import (
    bulk_allot_or_commit_snags,
    bulk_assign_snag,
    bulk_snag_excel_export,
    check_if_snag_ids_exist,
    close_snags_assigned_by_org,
    snag_assigned_trigger_event,
    snag_bulk_assign_trigger_event,
)


class SnagBulkAssignApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_id_list = HashIdListField()
        assign_to_id = HashIdField()
        date_of_closure = serializers.DateField(allow_null=True)
        boq_element_list = HashIdListField(allow_null=True)
        allotted_poc_id = HashIdField(allow_null=True, required=False)
        committed_at = CustomDateField(allow_null=True, required=False)
        remark_blocks = serializers.ListField(child=serializers.JSONField(), allow_null=True)

        class Meta:
            ref_name = "SnagAssignmentInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_201_CREATED: ""},
        operation_id="bulk_snags_assignment",
        operation_summary="Bulk Snags Assignment",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        if not check_if_snag_ids_exist(snag_id_list=data.get("snag_id_list")):
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)
        bulk_assign_snag(
            snag_id_list=data.get("snag_id_list"),
            user_id=request.user.pk,
            assign_to_id=data.get("assign_to_id"),
            organization_id=self.get_organization_id(),
            allotted_poc_id=data.get("allotted_poc_id"),
            committed_at=data.get("committed_at"),
            boq_element_list=data.get("boq_element_list"),
            date_of_closure=data.get("date_of_closure"),
            remark_blocks=data.get("remark_blocks"),
        )
        if len(data.get("snag_id_list")) == 1:
            on_commit(
                partial(
                    snag_assigned_trigger_event,
                    project_id=project_id,
                    snag_ids=data.get("snag_id_list"),
                    assigned_to_org_id=data.get("assign_to_id"),
                    assigned_by_org_id=self.get_organization_id(),
                )
            )
        elif len(data.get("snag_id_list")) > 1:
            on_commit(
                partial(
                    snag_bulk_assign_trigger_event,
                    project_id=project_id,
                    snag_ids=data.get("snag_id_list"),
                    assigned_to_org_id=data.get("assign_to_id"),
                    assigned_by_org_id=self.get_organization_id(),
                )
            )
        return Response(
            {"message": "Selected snags have been assigned"},
            status=HTTP_200_OK,
        )


class SnagBulkAllotPOCAndCommitTimelineApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_id_list = HashIdListField()
        allotted_poc_id = HashIdField(allow_null=True)
        committed_at = CustomDateField(allow_null=True)

        def validate(self, attrs):
            if not (attrs["allotted_poc_id"] or attrs["committed_at"]):
                raise SnagException(ExceptionConstant.AT_LEAST_ONE_FIELD_NEEDS_TO_BE_FILLED)
            return attrs

        class Meta:
            ref_name = "SnagBulkAllottedPOCAndCommitTimelineInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: "Snags allotted with POC and committed time"},
        operation_id="snag_bulk_poc_and_commit_timeline",
        operation_summary="Snag Bulk POC and Commit Timelines",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        organization_id = self.get_organization_id()

        if not check_if_snag_ids_exist(snag_id_list=data.get("snag_id_list")):
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)

        snag_assignment_list = get_snag_assignments_to_org(
            snag_id_list=data.get("snag_id_list"), organization_id=organization_id
        )
        if not snag_assignment_list:
            raise SnagBulkException(ExceptionConstant.NO_SNAG_ASSIGNED_TO_YOUR_ORG)

        if if_exists_resolved_or_closed_snag_assignments_to_org(
            snag_id_list=data.get("snag_id_list"), organization_id=organization_id
        ):
            raise SnagBulkException(ExceptionConstant.SOME_SELECTED_SNAGS_ARE_NO_MORE_PENDING)
        bulk_allot_or_commit_snags(
            allotted_poc_id=data.get("allotted_poc_id"),
            committed_at=data.get("committed_at"),
            snag_assignment_list=snag_assignment_list,
            user_id=request.user.id,
        )

        return Response(
            {"message": "Selected snags have been allotted with POC and committed time"},
            status=HTTP_200_OK,
        )


class SnagBulkAssignmentTimelineCloseAPI(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_id_list = HashIdListField()

        class Meta:
            ref_name = "SnagIdListInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="snag_assignment_bulk_close_api",
        operation_summary="Mark Snag as Closed in Bulk",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        organization_id = self.get_organization_id()

        if not check_if_snag_ids_exist(snag_id_list=data.get("snag_id_list")):
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)

        if not ProjectPermissionHelper.has_permission(
            user=request.user, project_id=project_id, permission=Permissions.CAN_CLOSE_SNAG
        ):
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_CLOSED_BY_YOU)

        snag_assignment_list = get_snag_assignments_from_org(
            snag_id_list=data.get("snag_id_list"), organization_id=organization_id
        )
        if not snag_assignment_list:
            raise SnagBulkException(ExceptionConstant.NO_SNAG_ASSIGNED_BY_YOUR_ORG)

        close_snags_assigned_by_org(
            snag_id_list=data.get("snag_id_list"), organization_id=organization_id, user_id=request.user.pk
        )

        return Response(
            {"message": "Selected snags have been marked closed!"},
            status=HTTP_200_OK,
        )


class SnagBulkExportExcel(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_id_list = HashIdListField()

        class Meta:
            ref_name = "SnagIdExcelInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: ""},
        operation_id="snag_bulk_export_excel",
        operation_summary="Bulk Export Snags in Excel",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()

        excel_file, filename = bulk_snag_excel_export(
            project_id=project_id,
            organization_id=self.get_organization_id(),
            snag_id_list=data.get("snag_id_list"),
            user=request.user,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )

        response = HttpResponse(content=excel_file, content_type="application/ms-excel")
        response["Content-Disposition"] = f"attachment; filename={filename}.xlsx"
        return response
