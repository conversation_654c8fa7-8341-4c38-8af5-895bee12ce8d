from django.db.models import Q, Subquery
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.serializers import (
    BaseSerializer,
    SplitCharHashId<PERSON>ist<PERSON>ield,
    SplitCharListField,
)
from common.utils import order_queryset_by_null_last
from core.helpers import OrgPermissionHelper
from core.models import Organization
from core.serializers import OrganizationModelSerializer
from project.domain.helpers import ProjectPermissionHelper
from project.interface.apis.internal.apis import ProjectBaseApi
from snags.data.choices import StatusFilterChoices
from snags.data.models import SnagAssignment
from snags.data.selectors import snags_fetch_all_with_annotations
from snags.domain.exceptions import ExceptionConstant, SnagException
from snags.interface.serializers import SnagListSerializer


class SnagListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterSerializer(BaseSerializer):
        filter_type = SplitCharListField(required=False)
        title = serializers.CharField(required=False)
        code = serializers.CharField(required=False)
        locations = SplitCharListField(required=False)  # Multiple locations are send by app through filters
        location = serializers.CharField(required=False)  # In web, user can apply filter on location by searching
        created_by = SplitCharHashIdListField(required=False)
        expected_closure_date = serializers.DateField(required=False, format="%Y-%m-%d")
        assigned_to = SplitCharHashIdListField(required=False)
        alloted_poc = SplitCharHashIdListField(required=False)
        snag_category_list = SplitCharHashIdListField(required=False)
        snag_subcategory_list = SplitCharHashIdListField(required=False)
        ordering = serializers.CharField(required=False)
        app_search = serializers.CharField(required=False)
        snag_ids = SplitCharHashIdListField(required=False)

        def validate(self, attrs):
            if attrs.get("app_search") and (attrs.get("title") or attrs.get("code")):
                raise SnagException(key="app_search", message=ExceptionConstant.APP_SEARCH)

            filter_type = attrs.get("filter_type")

            if filter_type:
                if len(filter_type) == 1 and filter_type[0] == "":
                    return attrs

                for a in filter_type:
                    if a not in StatusFilterChoices.values:
                        raise SnagException(key="filter_type", message=f"{a} is {ExceptionConstant.NOT_A_VALID_CHOICE}")

            return attrs

        class Meta:
            ref_name = "SnagFilterSerializer"

    ordering_fields = {
        "title": "title",
        "code": "serial_number",
        "snag_category": "snag_category__name",
        "snag_subcategory": "snag_subcategory__name",
        "location": "location",
        "created_by": "created_by__name",
        "updated_by": "updated_by__name",
        "updated_at": "updated_at",
        "created_at": "created_at",
        "resolution_date": "resolution_date",
        "closure_date": "closure_date",
        "committed_at": "committed_at",
        "expected_date_of_closure": "expected_date_of_closure",
    }

    def get_queryset(self):
        user = self.get_user()
        can_link_snag_item_permission = OrgPermissionHelper.has_permission(
            user=user,
            permission=Permissions.CAN_LINK_SNAG_ITEMS,
        ) and ProjectPermissionHelper.has_permission(
            user=user, project_id=self.kwargs.get("project_id"), permission=Permissions.CAN_LINK_SNAG_ITEMS
        )
        queryset = snags_fetch_all_with_annotations(
            project_id=self.kwargs.get("project_id"),
            organization_id=self.get_organization_id(),
            can_link_snag_item_permission=can_link_snag_item_permission,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )

        return queryset

    def filter_queryset(self, queryset):
        data = self.validate_filter_data()
        snag_ids = data.get("snag_ids")

        if snag_ids:
            queryset = queryset.filter(id__in=snag_ids)

        if data.get("snag_category_list"):
            queryset = queryset.filter(snag_category_id__in=data.get("snag_category_list"))

        if data.get("locations"):
            queryset = queryset.filter(location__in=data.get("locations"))

        if data.get("snag_subcategory_list"):
            queryset = queryset.filter(snag_subcategory_id__in=data.get("snag_subcategory_list"))

        if data.get("alloted_poc"):
            subquery_for_me = (
                SnagAssignment.objects.select_related("allotted_poc")
                .filter(allotted_poc__id__in=data.get("alloted_poc"), org_to_id=self.get_organization_id())
                .available()
                .values_list("snag_id")
            )
            queryset = queryset.filter(id__in=subquery_for_me)

        if not data.get("app_search"):
            if data.get("title"):
                queryset = queryset.filter(
                    Q(title__icontains=data.get("title")) | Q(description__icontains=data.get("title"))
                )

            if data.get("code"):
                queryset = queryset.filter(code__icontains=data.get("code"))
            if data.get("location"):
                queryset = queryset.filter(location__icontains=data.get("location"))

        else:
            search_text = data.get("app_search")

            queryset = queryset.filter(Q(title__icontains=search_text) | Q(code__icontains=search_text))

        if data.get("created_by"):
            queryset = queryset.filter(created_by_id__in=data.get("created_by"))

        if data.get("assigned_to"):
            subquery = (
                SnagAssignment.objects.select_related("snag")
                .filter(snag__project_id=self.kwargs.get("project_id"), org_to_id__in=data.get("assigned_to"))
                .values("snag_id")
            )
            queryset = queryset.filter(id__in=Subquery(subquery))

        status_filter = data.get("filter_type")

        if status_filter and status_filter[0] == "":
            status_filter[0] = StatusFilterChoices.ALL.value

        if status_filter and status_filter[0] != StatusFilterChoices.ALL.value:
            if StatusFilterChoices.CLOSED in status_filter:
                status_filter.extend(
                    [
                        StatusFilterChoices.CLOSED_RESOLVED,
                        StatusFilterChoices.CLOSED_UNRESOLVED,
                        StatusFilterChoices.CLOSED_NOT_APPLICABLE,
                        StatusFilterChoices.CLOSED_NOT_RESOLVABLE,
                    ]
                )
            if StatusFilterChoices.OPEN_UNRESOLVED in status_filter:
                status_filter.extend([StatusFilterChoices.OPEN_NOT_APPLICABLE, StatusFilterChoices.OPEN_NOT_RESOLVABLE])
            queryset = queryset.filter(filter_type__in=status_filter)

        if data.get("ordering"):
            ordering = data.get("ordering")
            return order_queryset_by_null_last(
                queryset=queryset, ordering=ordering, ordering_fields=self.ordering_fields
            )

        return queryset.order_by("-id")

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: SnagListSerializer()},
        operation_id="snag_list_api",
        operation_summary="snag_list_api",
    )
    def get(self, request, project_id, *args, **kwargs):
        organization_id = self.get_organization_id()
        known_organization_ids = self.get_known_organization_ids()
        known_organization_ids.append(organization_id)
        queryset = self.filter_queryset(queryset=self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = SnagListSerializer(
                page,
                many=True,
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": organization_id,
                },
            )
            return self.get_paginated_response(serializer.data)

        return Response(
            SnagListSerializer(
                self.get_queryset(),
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": organization_id,
                },
                many=True,
            ).data,
            status=HTTP_200_OK,
        )


class SnagAssignmentOrganizationListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationModelSerializer):
        class Meta(OrganizationModelSerializer.Meta):
            ref_name = "SnagAssignmentOrganizationSerializer"
            fields = (
                "id",
                "name",
            )
            output_hash_id_fields = ("id",)

    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="snag_assignment_organization_list_api",
    )
    def get(self, request, project_id):
        vendor_id_list = self.get_vendor_organization_ids()
        vendor_id_list.append(self.get_organization_id())
        vendors: list[Organization] = Organization.objects.filter(id__in=vendor_id_list).order_by("name")

        return Response(self.OutputSerializer(vendors, many=True).data, status=HTTP_200_OK)
