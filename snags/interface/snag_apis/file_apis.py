import structlog
from django.db import transaction
from django.db.models import TextChoices
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from authorization.domain.constants import Permissions
from common.apis import BaseApi
from common.serializers import BaseSerializer
from common.services import upload_file
from core.constants import ExternalWebhookType
from core.organization.domain.mixins import ExternalWebhookFileMixin
from project.data.models import Project
from rollingbanners.hash_id_converter import HashIdConverter
from snags.data.models import SnagPreviewFile, SnagSupportingDocument
from snags.domain.exceptions import (
    ExceptionConstant,
    SnagFileError,
    SnagFileUploadValidationError,
)
from snags.interface.serializers import (
    SnagPreviewFileSerializer,
    SnagSupportingDocumentSerializer,
)

logger = structlog.getLogger(__name__)


class SnagFileTypeChoices(TextChoices):
    PREVIEW_FILE = "snag-preview-file"
    SUPPORTING_DOCUMENT = "snag-supporting-document"


class SnagFileUploadApi(BaseApi):
    class InputSerializer(BaseSerializer):
        file_type = serializers.ChoiceField(choices=SnagFileTypeChoices.choices)
        file = serializers.FileField()
        id = serializers.CharField()

        class Meta:
            ref_name = "SnagPreviewFileUploadApiInputSerializer"

    input_serializer_class = InputSerializer
    parser_classes = (MultiPartParser,)

    @swagger_auto_schema(
        request_body=InputSerializer(),
        operation_id="snag_file_upload_api",
        operation_summary="Snag Preview File and Supporting Document Upload Api",
        responses={HTTP_200_OK: SnagPreviewFileSerializer()},
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, *args, **kwargs):
        data = self.validate_input_data(copy=False)
        file = data.get("file")
        file_id = data.get("id")
        file_type = data.get("file_type")
        filename = data.get("file").name
        obj = None
        path = f"snags/{project_id}/{snag_id}/"

        if file_type == SnagFileTypeChoices.SUPPORTING_DOCUMENT:
            obj = SnagSupportingDocument.objects.filter(id=file_id).first()
            path += SnagFileTypeChoices.SUPPORTING_DOCUMENT.value

        elif file_type == SnagFileTypeChoices.PREVIEW_FILE:
            obj = SnagPreviewFile.objects.filter(id=file_id).first()
            path += SnagFileTypeChoices.PREVIEW_FILE.value

        if not obj:
            raise SnagFileUploadValidationError(f"{file_type} {ExceptionConstant.NOT_FOUND}")

        if obj.file.name:
            raise SnagFileUploadValidationError(f"{file_type} {ExceptionConstant.ALREADY_LATCHED}")

        if obj.name != filename:
            raise SnagFileUploadValidationError(f"{file_type} {ExceptionConstant.NAME_NOT_MATCHED}")
        try:
            file_url = upload_file(
                file=file,
                destination_blob_name=path,
                filename=filename,
            )
        except SnagFileError:
            message = f"upload failed for {file_type} having file_name:{filename} for file_id:{file_id}"
            logger.error(msg=message, file_type=file_type, filename=filename, file_id=file_id)
            raise SnagFileUploadValidationError(ExceptionConstant.UNABLE_TO_UPLOAD_FILE)

        relative_path = "/".join(file_url.split("/")[-5:])
        try:
            obj.file = relative_path
            obj.save(update_fields=["file"])
        except SnagFileError:
            message = f"save failed for {file_type} having file_name:{filename} for file_id:{file_id}"
            logger.error(msg=message, file_type=file_type, filename=filename, file_id=file_id)
            raise SnagFileUploadValidationError(ExceptionConstant.UNABLE_TO_SAVE_FILE)
        response = None
        if file_type == SnagFileTypeChoices.PREVIEW_FILE:
            response = SnagPreviewFileSerializer(obj).data
        elif file_type == SnagFileTypeChoices.SUPPORTING_DOCUMENT:
            response = SnagSupportingDocumentSerializer(obj).data
        return Response(response, status=HTTP_200_OK)


class SnagExportPdfApi(ExternalWebhookFileMixin):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_EXPORT_SNAG_PDF]

    class InputSerializer(BaseSerializer):
        snag_ids = serializers.ListField(child=serializers.CharField(), required=False)
        all_snags = serializers.BooleanField(required=False)

        class Meta:
            ref_name = "SnagExportPdfApiInputSerializer"

    def get_webhook_data(self):
        data = self.validate_input_data()

        return {
            "snag_ids": data.get("snag_ids", []),
            "all_snags": data.get("all_snags", False),
            "project_id": HashIdConverter.encode(self.kwargs["project_id"]),
            "user_id": HashIdConverter.encode(self.request.user.pk),
            "org_id": HashIdConverter.encode(self.get_organization_id()),
        }

    def get_filename(self):
        return f"{Project.objects.get(pk=self.kwargs['project_id']).job_id}_SNAG_PDF.pdf"

    @swagger_auto_schema(
        responses={HTTP_200_OK: "snag-export-pdf"},
        operation_id="snag_export_pdf",
        operation_summary="Export SNAG PDF",
    )
    def post(self, request, *args, **kwargs):
        response = super().post(webhook_type=ExternalWebhookType.SNAG_ACTION_EXPORT_PDF)
        return Response(data=response, status=HTTP_200_OK)
