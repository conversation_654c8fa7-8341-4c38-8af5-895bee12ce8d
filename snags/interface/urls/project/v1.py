from django.urls import include, path

from snags.interface.apis import (
    AllotPOCAndCommitTimelineApi,
    AllotUserListApi,
    AssignedOrganizationsListApi,
    AssignmentTimelineCloseAPI,
    AssignmentTimelineResolutionStatusUpdateAPI,
    AssignmentTimelineUnresolvedAPI,
    ProjectSnagReporterOptionsFetchApi,
    SnagAssignApi,
    SnagAssignmentButtonVisibleApi,
    SnagAssignmentTimelineListApi,
    SnagAssignmentUpdateApi,
    SnagBulkCreateViaExcelApi,
    SnagBulkDetailsApi,
    SnagCounterApi,
    SnagCreateApi,
    SnagDeleteApi,
    SnagDetailsApi,
    SnagExcelExportApi,
    SnagExcelTemplateDownloadApi,
    SnagFilterConfigApi,
    SnagPassToClientPrefillDataApi,
    SnagPreviewFileListApi,
    SnagUnAssignApi,
    SnagUpdate<PERSON><PERSON>,
    SnagUpdateHistoryApi,
)
from snags.interface.snag_apis import (
    SnagAssignmentOrganizationListApi,
    SnagBulkAllotPOCAndCommitTimelineApi,
    SnagBulkAssignApi,
    SnagBulkAssignmentTimelineCloseAPI,
    SnagBulkExportExcel,
    SnagExportPdfApi,
    SnagFileUploadApi,
    SnagListApi,
)

app_name = "snags"


urlpatterns = [
    path(
        "<hash_id:snag_id>/assignments/<hash_id:assignment_id>/remark/",
        include("snags.remark.interface.urls"),
        name="snags-remark",
    ),
    path("create/", SnagCreateApi.as_view(), name="snag-create-api"),
    path("<hash_id:snag_id>/update/", SnagUpdateApi.as_view(), name="snag-update-api"),
    path("<hash_id:snag_id>/update-history/", SnagUpdateHistoryApi.as_view(), name="snag-update-history-api"),
    path("<hash_id:snag_id>/delete/", SnagDeleteApi.as_view(), name="snag-delete-api"),
    path("<hash_id:snag_id>/details/", SnagDetailsApi.as_view(), name="snag-details"),
    path("bulk-details/", SnagBulkDetailsApi.as_view(), name="snag-bulk-details"),
    path("<hash_id:snag_id>/preview-file-list/", SnagPreviewFileListApi.as_view(), name="snag-preview-file-list"),
    path(
        "<hash_id:snag_id>/buttons/",
        SnagAssignmentButtonVisibleApi.as_view(),
        name="snag-assignment-button-visible-api",
    ),
    path(
        "assigned-organizations/",
        AssignedOrganizationsListApi.as_view(),
        name="assigned-organizations-list",
    ),
    path("list/", SnagListApi.as_view(), name="snag-list-api"),
    path(
        "organization/list/", SnagAssignmentOrganizationListApi.as_view(), name="snag-assignment-organization-list-api"
    ),
    path("export-excel/", SnagExcelExportApi.as_view(), name="snag-excel-export-api"),
    path("filter-config/", SnagFilterConfigApi.as_view(), name="filter-config-api"),
    path("counter/", SnagCounterApi.as_view(), name="counter-api"),
    path("<hash_id:snag_id>/assign/", SnagAssignApi.as_view(), name="snag-assign-api"),
    path("bulk-assign/", SnagBulkAssignApi.as_view(), name="snag-bulk-assign-api"),
    path(
        "<hash_id:snag_id>/file-upload/",
        SnagFileUploadApi.as_view(),
        name="snags-preview-file-upload",
    ),
    path(
        "export-pdf/",
        SnagExportPdfApi.as_view(),
        name="snag-pdf-download",
    ),
    path(
        "<hash_id:snag_id>/assignment-list/", SnagAssignmentTimelineListApi.as_view(), name="snag-assignment-list-api"
    ),
    path("allotment-users/", AllotUserListApi.as_view(), name="allotment-users"),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/allot/",
        AllotPOCAndCommitTimelineApi.as_view(),
        name="allot-user-and-commit-timeline",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/resolution-status/update/",
        AssignmentTimelineResolutionStatusUpdateAPI.as_view(),
        name="assignment-timeline-resolution-status-change",
    ),
    path(
        "bulk-allot-commit/",
        SnagBulkAllotPOCAndCommitTimelineApi.as_view(),
        name="bulk-allot-user-and-commit-timeline",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/close/",
        AssignmentTimelineCloseAPI.as_view(),
        name="snag-assignment-mark-close",
    ),
    path(
        "bulk-close/",
        SnagBulkAssignmentTimelineCloseAPI.as_view(),
        name="snag-assignment-mark-bulk-close",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/un-resolve/",
        AssignmentTimelineUnresolvedAPI.as_view(),
        name="snag-assignment-mark-un-resolve",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/un-assign/",
        SnagUnAssignApi.as_view(),
        name="snag-un-assign-api",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/prefilled-data/",
        SnagPassToClientPrefillDataApi.as_view(),
        name="snag-assignment-prefilled-api",
    ),
    path(
        "<hash_id:snag_id>/assignments/<hash_id:snag_assignment_id>/update/",
        SnagAssignmentUpdateApi.as_view(),
        name="snag-assignment-update-api",
    ),
    path("snag-reporter-options/", ProjectSnagReporterOptionsFetchApi.as_view(), name="project-snag-reporter"),
    path("bulk-excel-export/", SnagBulkExportExcel.as_view(), name="bulk-export-excel-snags"),
    path("snags-excel-import/", SnagBulkCreateViaExcelApi.as_view(), name="bulk-create-snags-import-excel"),
    path("excel-template/", SnagExcelTemplateDownloadApi.as_view(), name="snags-excel-template"),
]
