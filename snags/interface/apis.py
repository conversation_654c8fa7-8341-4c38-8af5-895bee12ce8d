from functools import partial

from django.conf import settings
from django.db import transaction
from django.db.models import Count, Q
from django.db.transaction import on_commit
from django.http import HttpResponse
from django.utils import timezone
from django.utils.module_loading import import_string
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.apis import BaseApi
from common.events import Events
from common.events.snags import SnagUnresolvedEventData
from common.exceptions import ExcelError
from common.serializers import BaseSerializer, CustomDateField, CustomFileField, HashIdField, HashIdListField
from core.apis import OrgBase<PERSON><PERSON>
from core.caches import UnitOfMeasurementCache
from core.helpers import OrgPermissionHelper
from core.models import User
from core.serializers import OrganizationSerializer, UserModelSerializer
from microcontext.choices import MicroContextChoices
from project.domain.helpers import ProjectPermissionHelper
from project.interface.apis.internal.apis import ProjectBaseApi
from rollingbanners.comment_base_service import CommentBaseService
from snags.data.choices import (
    ClosureStatusChoices,
    ResolutionStatusChoices,
    SnagRoleChoices,
    StatusFilterChoices,
)
from snags.data.models import SnagCategory, SnagsCategoryMapping, SnagSubcategory
from snags.data.selectors import (
    assignment_timeline_fetch,
    get_alloted_or_committed_snag_assignments,
    get_child_object_for_snag_assignment,
    get_snag,
    get_snag_assignment_for_snag,
    get_snag_bulk_details,
    get_snag_update_history,
    snag_assignment_get,
    snag_assignment_list_with_timeline,
    snag_details_get,
    snag_fetch,
    snag_preview_file_list_get,
    snags_fetch,
)
from snags.domain.constants import SNAG_EXCEL_IMPORT_COLUMNS
from snags.domain.entities import OrganizationSnagReporterConfigData
from snags.domain.exceptions import (
    ExceptionConstant,
    SnagConfigurationException,
    SnagException,
)
from snags.domain.services import (
    create_assignment_timeline,
    fetch_checked_snag_config,
    get_filter_config,
    get_snag_reporter_options,
    organization_id_list_fetch,
    organization_snag_reporter_options_fetch,
    pass_to_client_prefilled_data,
    snag_allotment_and_timeline_create,
    snag_assign,
    snag_assigned_trigger_event,
    snag_assignment_button_service,
    snag_assignment_update,
    snag_bulk_create,
    snag_category_mappings,
    snag_create,
    snag_delete,
    snag_excel_error_csv_generate,
    snag_excel_file,
    snag_excel_template_generate,
    snag_excel_validate_using_openpyxl,
    snag_history_create,
    snag_preview_files_create,
    snag_preview_files_delete,
    snag_reporter_options_update,
    snag_supporting_docs_delete,
    snag_supporting_documents_create,
    snag_update,
    timeline_preview_file_bulk_create,
    update_assignment_card,
    validate_configuration_conditions,
)
from snags.interface.serializers import (
    AssignmentTimelineBaseSerializer,
    AssignmentTimelinePreviewFileSerializer,
    AssignmentTimelineSerializer,
    BoqElementSerializer,
    CategorySerializer,
    OrganizationSnagConfigInputSerializer,
    OrganizationSnagReporterConfigSerializer,
    SnagAssignmentModelSerializer,
    SnagAssignmentSerializer,
    SnagBulkDetailSerializer,
    SnagDetailSerializer,
    SnagPreviewFileInputSerializer,
    SnagPreviewFileSerializer,
    SnagsExcelRowSerializer,
    SnagSupportingDocumentInputSerializer,
    SnagUpdateHistoryModelSerializer,
    SubCategorySerializer,
    UserOrgBaseSerializer,
    UserOrgSerializer,
)
from snags.remark.domain.entities import RemarkData
from snags.remark.domain.services.services import create_remark_service

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class SnagsCategoryMappingsGetApi(BaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="snags_categories_mappings",
        operation_summary="snags_categories_mappings",
    )
    def get(self, request, *args, **kwargs):
        # snags category list
        mappings = SnagsCategoryMapping.objects.all()
        category_id_list = mappings.values_list("snag_category_id")
        subcategory_id_list = mappings.values_list("snag_subcategory_id")

        all_categories = SnagCategory.objects.filter(id__in=category_id_list).order_by("name")

        # snags sub category List
        all_sub_categories = SnagSubcategory.objects.filter(id__in=subcategory_id_list).order_by("name")
        category_to_subcategory_dict, subcategory_to_category_dict = snag_category_mappings()
        return Response(
            {
                "snags_categories": CategorySerializer(all_categories, many=True).data,
                "snags_sub_categories": SubCategorySerializer(all_sub_categories, many=True).data,
                "category_to_subcategory_mappings": category_to_subcategory_dict,
                "subcategory_to_category_mappings": subcategory_to_category_dict,
            },
            status=HTTP_200_OK,
        )


class SnagCreateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        title = serializers.CharField(max_length=100)
        description = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
        location = serializers.CharField(max_length=100, allow_null=True)
        snag_category_id = HashIdField(allow_null=True)
        snag_subcategory_id = HashIdField(allow_null=True)
        snag_role = serializers.ChoiceField(choices=SnagRoleChoices.choices)
        preview_files = serializers.ListField(child=SnagPreviewFileInputSerializer(), allow_null=True)
        supporting_documents = serializers.ListField(child=SnagSupportingDocumentInputSerializer(), allow_null=True)

        class Meta:
            ref_name = "SnagCreationInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_201_CREATED: SnagDetailSerializer()},
        operation_id="snags_create",
        operation_summary="snags_create",
    )
    @transaction.atomic
    def post(self, request, project_id):
        data = self.validate_input_data()
        user_org_id = self.get_organization_id()
        known_organization_ids = self.get_known_organization_ids()
        known_organization_ids.append(user_org_id)
        created_snag = snag_create(
            project_id=project_id,
            title=data.get("title"),
            description=data.get("description"),
            location=data.get("location"),
            snag_category_id=data.get("snag_category_id"),
            snag_subcategory_id=data.get("snag_subcategory_id"),
            snag_role=data.get("snag_role"),
            user_org_id=user_org_id,
            user_id=request.user.pk,
        )
        if data.get("preview_files"):
            snag_preview_files_create(
                created_snag=created_snag, preview_files=data.get("preview_files"), user_id=request.user.pk
            )
        if data.get("supporting_documents"):
            snag_supporting_documents_create(
                created_snag=created_snag,
                supporting_documents=data.get("supporting_documents"),
                user_id=request.user.pk,
            )
        can_link_snag_item_permission = OrgPermissionHelper.has_permission(
            user=self.get_user(),
            permission=Permissions.CAN_LINK_SNAG_ITEMS,
        ) and ProjectPermissionHelper.has_permission(
            user=self.get_user(), project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
        )
        snag_details = snag_details_get(
            snag_id=created_snag.id,
            organization_id=user_org_id,
            can_link_snag_item=can_link_snag_item_permission,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )
        return Response(
            SnagDetailSerializer(
                snag_details,
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": self.get_organization_id(),
                },
            ).data,
            status=HTTP_201_CREATED,
        )


class SnagUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        title = serializers.CharField(max_length=100)
        description = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
        location = serializers.CharField(max_length=100, allow_null=True)
        snag_category_id = HashIdField(allow_null=True)
        snag_subcategory_id = HashIdField(allow_null=True)
        preview_files = serializers.ListField(child=SnagPreviewFileInputSerializer(), allow_null=True)
        supporting_documents = serializers.ListField(child=SnagSupportingDocumentInputSerializer(), allow_null=True)
        delete_preview_file_ids = serializers.ListField(child=HashIdField(), allow_empty=True)
        delete_supporting_document_ids = serializers.ListField(child=HashIdField(), allow_empty=True)

        class Meta:
            ref_name = "SnagUpdateInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_201_CREATED: SnagDetailSerializer()},
        operation_id="snags_update_api",
        operation_summary="Snag Update API",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, *args, **kwargs):
        data = self.validate_input_data()
        user_org_id = self.get_organization_id()
        known_organization_ids = self.get_known_organization_ids()
        known_organization_ids.append(user_org_id)

        snag = snag_fetch(snag_id=snag_id)
        if not snag:
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)
        if user_org_id not in [snag.created_by_org_id, snag.created_for_org_id]:
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_EDITED_BY_YOUR_ORG)
        snag = snag_update(
            snag=snag,
            title=data.get("title"),
            description=data.get("description"),
            location=data.get("location"),
            user_id=request.user.pk,
            snag_category_id=data.get("snag_category_id"),
            snag_subcategory_id=data.get("snag_subcategory_id"),
        )

        snag_history_create(snag_id=snag.id, user_id=request.user.pk, save=True)

        if data.get("preview_files"):
            snag_preview_files_create(
                created_snag=snag, preview_files=data.get("preview_files"), user_id=request.user.pk
            )
        if data.get("supporting_documents"):
            snag_supporting_documents_create(
                created_snag=snag,
                supporting_documents=data.get("supporting_documents"),
                user_id=request.user.pk,
            )

        if data.get("delete_preview_file_ids"):
            snag_preview_files_delete(preview_file_ids=data.get("delete_preview_file_ids"), user_id=request.user.pk)

        if data.get("delete_supporting_document_ids"):
            snag_supporting_docs_delete(
                snag_docs_ids=data.get("delete_supporting_document_ids"), user_id=request.user.pk
            )
        can_link_snag_item_permission = OrgPermissionHelper.has_permission(
            user=self.get_user(),
            permission=Permissions.CAN_LINK_SNAG_ITEMS,
        ) and ProjectPermissionHelper.has_permission(
            user=self.get_user(), project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
        )
        snag_details = snag_details_get(
            snag_id=snag.id,
            organization_id=user_org_id,
            can_link_snag_item=can_link_snag_item_permission,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )
        return Response(
            SnagDetailSerializer(
                snag_details,
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": self.get_organization_id(),
                },
            ).data,
            status=HTTP_201_CREATED,
        )


class SnagDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: SnagDetailSerializer()},
        operation_id="snag_details_api",
        operation_summary="snag_details_api",
    )
    def get(self, request, project_id, snag_id, *args, **kwargs):
        user = self.get_user()
        known_organization_ids = self.get_known_organization_ids()
        organization_id = self.get_organization_id()
        known_organization_ids.append(organization_id)

        can_link_snag_item_permission = OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_LINK_SNAG_ITEMS
        ) and ProjectPermissionHelper.has_permission(
            user=user, project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
        )

        snag_details = snag_details_get(
            snag_id=snag_id,
            organization_id=organization_id,
            can_link_snag_item=can_link_snag_item_permission,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )

        return Response(
            SnagDetailSerializer(
                snag_details,
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": self.get_organization_id(),
                },
            ).data,
            status=HTTP_200_OK,
        )


class AssignedOrganizationsListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_id_list = HashIdListField()

        class Meta:
            ref_name = "AssignedOrganizationsListInputSerializer"

    class OutputSerializer(BaseSerializer):
        organization_id_list = HashIdListField()

        class Meta:
            ref_name = "AssignedOrganizationsListOutputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="assigned_organizations_list",
        operation_summary="Get all the Organization of snag id mentioned",
    )
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()

        organization_id_list = organization_id_list_fetch(snag_id_list=data.get("snag_id_list"))
        return Response(
            self.OutputSerializer({"organization_id_list": organization_id_list}).data,
            status=HTTP_200_OK,
        )


class SnagExcelExportApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: "excel-file"},
        operation_id="snag_excel_export_api",
        operation_summary="export excel for snag list",
    )
    @transaction.atomic
    def get(self, request, project_id, *args, **kwargs):
        self.set_project_timezone()
        excel_file, filename = snag_excel_file(
            project_id=project_id,
            organization_id=self.get_organization_id(),
            user=request.user,
            client_org_id=self.get_project_client_id(),
            creator_org_id=self.get_creator_org_id(),
        )
        response = HttpResponse(content=excel_file, content_type="application/ms-excel")
        response["Content-Disposition"] = f"attachment; filename={filename}.xlsx"
        return response


class SnagFilterConfigApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class UserSerializer(UserOrgSerializer):
        class Meta(UserOrgSerializer.Meta):
            fields = (
                "id",
                "name",
            )

    class AllotedPocSerializer(UserModelSerializer):
        id = serializers.CharField(source="allotted_poc_id")
        name = serializers.SerializerMethodField()

        def get_name(self, obj):
            return obj.allotted_poc.name

        class Meta(UserModelSerializer.Meta):
            fields = (
                "id",
                "name",
            )

    def get_queryset(self):
        queryset = snags_fetch(project_id=self.kwargs.get("project_id"), organization_id=self.get_organization_id())
        return queryset

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="snag_filter_config_api",
        operation_summary="Snag Filter Config API",
    )
    def get(self, request, project_id, *args, **kwargs):
        queryset = self.get_queryset()
        (
            created_by_distinct,
            snags_category_distinct,
            snags_subcategory_distinct,
            organizations,
            alloted_poc_distinct,
            snags_location_distinct,
        ) = get_filter_config(project_id=project_id, queryset=queryset, organization_id=self.get_organization_id())
        data = {
            "created_by_list": self.UserSerializer([obj.created_by for obj in created_by_distinct], many=True).data,
            "assigned_to_list": OrganizationSerializer(
                organizations,
                many=True,
            ).data,
            "snags_categories": CategorySerializer(
                [obj.snag_category for obj in snags_category_distinct if obj.snag_category], many=True
            ).data,
            "snags_subcategories": SubCategorySerializer(
                [obj.snag_subcategory for obj in snags_subcategory_distinct if obj.snag_subcategory], many=True
            ).data,
            "alloted_poc": self.AllotedPocSerializer(alloted_poc_distinct, many=True).data,
            "location": [{"id": location, "name": location} for location in snags_location_distinct if location],
        }

        return Response(
            data,
            status=HTTP_200_OK,
        )


class SnagCounterApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    def get_queryset(self):
        queryset = snags_fetch(project_id=self.kwargs.get("project_id"), organization_id=self.get_organization_id())
        return queryset

    class OutputSerializer(BaseSerializer):
        open_unresolved = serializers.IntegerField()
        open_resolved = serializers.IntegerField()
        closed_unresolved = serializers.IntegerField()
        closed_resolved = serializers.IntegerField()
        all = serializers.IntegerField()

        class Meta:
            ref_name = "SnagCounterOutputSerializer"

    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="snag_counter_api",
        operation_summary="Snag Counter API",
    )
    def get(self, request, project_id, *args, **kwargs):
        organization_id = self.get_organization_id()
        queryset = (
            self.get_queryset()
            .annotate_org_to_id(organization_id=organization_id)
            .annotate_org_from_id(organization_id=organization_id)
            .annotate_assignment_count(organization_id=organization_id)
            .annotate_all_assignment_count(organization_id=organization_id)
            .annotate_closure_status(organization_id=organization_id)
            .annotate_resolution_status(organization_id=organization_id)
            .annotate_status_concatenation()
        )
        aggregate_counter = queryset.aggregate(
            open_resolved=Count("id", filter=Q(filter_type=StatusFilterChoices.OPEN_RESOLVED)),
            open_unresolved=Count(
                "id",
                filter=Q(
                    filter_type__in=[
                        StatusFilterChoices.OPEN_UNRESOLVED,
                        StatusFilterChoices.OPEN_NOT_APPLICABLE,
                        StatusFilterChoices.OPEN_NOT_RESOLVABLE,
                    ]
                ),
            ),
            closed_unresolved=Count("id", filter=Q(filter_type=StatusFilterChoices.CLOSED_UNRESOLVED)),
            closed_resolved=Count(
                "id",
                filter=Q(
                    filter_type__in=[
                        StatusFilterChoices.CLOSED_RESOLVED,
                        StatusFilterChoices.CLOSED_NOT_RESOLVABLE,
                        StatusFilterChoices.CLOSED_NOT_APPLICABLE,
                    ]
                ),
            ),
            all=Count("id"),
        )

        return Response(
            self.OutputSerializer(aggregate_counter).data,
            status=HTTP_200_OK,
        )


class AllotUserListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class UserSerializer(UserOrgSerializer):
        class Meta(UserOrgSerializer.Meta):
            fields = (
                "id",
                "name",
            )

    def get_queryset(self):
        queryset = User.objects.filter(org_id=self.get_organization_id(), is_active=True, deleted_at__isnull=True)

        return queryset

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="snags_user_allot_list",
        operation_summary="User List for Snag Allotment",
    )
    def get(self, request, project_id, *args, **kwargs):
        queryset = self.get_queryset()

        return Response(
            self.UserSerializer(queryset, many=True).data,
            status=HTTP_200_OK,
        )


class SnagUpdateHistoryApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: SnagUpdateHistoryModelSerializer()},
        operation_id="snag_update_history_api",
        operation_summary="snag_update_history_api",
    )
    def get(self, request, project_id, snag_id, *args, **kwargs):
        snag_update_history = get_snag_update_history(snag_id=snag_id)
        organization_id = self.get_organization_id()
        known_organization_ids = self.get_known_organization_ids()
        known_organization_ids.append(organization_id)
        return Response(
            SnagUpdateHistoryModelSerializer(
                snag_update_history,
                many=True,
                context={
                    "known_organization_ids": known_organization_ids,
                },
            ).data,
            status=HTTP_200_OK,
        )


class SnagDeleteApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="snag_delete_api",
        operation_summary="snag_delete_api",
    )
    @transaction.atomic
    def delete(self, request, project_id, snag_id, *args, **kwargs):
        organization_id = self.get_organization_id()
        snag = snag_fetch(snag_id=snag_id)
        if not snag:
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)

        if organization_id not in [snag.created_for_org_id, snag.created_by_org_id]:
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_DELETED_BY_YOUR_ORG)
        if get_alloted_or_committed_snag_assignments(snag_id=snag_id):
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_DELETED_AS_ALREADY_ALLOTTED_OR_COMMITTED)
        snag_delete(snag_id=snag_id, user_id=request.user.pk, organization_id=organization_id)
        data_dict = dict()
        data_dict[MicroContextChoices.SNAG.value] = [snag_id]
        CommentHelperService.archive_many(data=data_dict, user_id=request.user.pk)
        return Response({"message": "Snag has been deleted!"}, status=HTTP_200_OK)


class SnagAssignApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        assign_to_id = HashIdField()
        date_of_closure = serializers.DateField(allow_null=True)
        boq_element_list = HashIdListField(allow_null=True)
        allotted_poc_id = HashIdField(allow_null=True, required=False)
        committed_at = CustomDateField(allow_null=True, required=False)
        remark_blocks = serializers.ListField(child=serializers.JSONField(), allow_null=True)

        class Meta:
            ref_name = "SnagAssignmentInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_201_CREATED: SnagAssignmentModelSerializer()},
        operation_id="snags_assignment",
        operation_summary="snags_assignment",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, *args, **kwargs):
        data = self.validate_input_data()
        remark_blocks = data.get("remark_blocks")
        snag = get_snag(snag_id=snag_id)
        if not snag:
            raise SnagException(ExceptionConstant.SNAG_NOT_FOUND)
        org_id = None
        if get_snag_assignment_for_snag(snag_id=snag_id).exists():
            org_id = self.get_organization_id()
        else:
            org_id = snag.created_for_org_id
        snag_assignment = get_child_object_for_snag_assignment(snag_id=snag_id, org_to_id=data.get("assign_to_id"))
        if snag_assignment:
            snag_assignment_card = snag_assignment_update(
                snag_assignment=snag_assignment,
                user_id=request.user.pk,
                date_of_closure=data.get("date_of_closure"),
                boq_element_list=data.get("boq_element_list"),
            )
        else:
            snag_assignment_card = snag_assign(
                snag_id=snag_id,
                user_id=request.user.pk,
                assign_to_id=data.get("assign_to_id"),
                date_of_closure=data.get("date_of_closure"),
                boq_element_list=data.get("boq_element_list"),
                organization_id=org_id,
            )
        if snag_assignment_card:
            snag_allotment_and_timeline_create(
                snag_assignment=snag_assignment_card,
                snag_id=snag_id,
                user_id=request.user.pk,
                allotted_poc_id=data.get("allotted_poc_id"),
                committed_at=data.get("committed_at"),
                project_id=project_id,
            )
        if remark_blocks:
            remark_data = RemarkData(blocks=remark_blocks)
            create_remark_service(
                assignment_id=snag_assignment_card.id, user_id=request.user.pk, remark_data=remark_data
            )
        on_commit(
            partial(
                snag_assigned_trigger_event,
                project_id=project_id,
                snag_ids=[snag_id],
                assigned_to_org_id=data.get("assign_to_id"),
                assigned_by_org_id=org_id,
            )
        )
        return Response(SnagAssignmentModelSerializer(snag_assignment_card).data, status=HTTP_201_CREATED)


class SnagAssignmentTimelineListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(BaseSerializer):
        class OrganizationSerializer(BaseSerializer):
            id = serializers.IntegerField()
            name = serializers.CharField()

            class Meta:
                ref_name = "OrganizationBaseSerializer"

        id = serializers.IntegerField()
        org_to = OrganizationSerializer()
        org_from = OrganizationSerializer()
        boq_elements = serializers.SerializerMethodField()
        date_of_closure = serializers.DateField()
        created_at = serializers.DateTimeField()
        created_by = UserOrgBaseSerializer()
        updated_at = serializers.DateTimeField()
        updated_by = UserOrgBaseSerializer()
        unassigned_at = serializers.DateTimeField(source="deleted_at")
        unassigned_by = UserOrgBaseSerializer(source="deleted_by")
        assignment_timelines = AssignmentTimelineBaseSerializer(many=True)
        hidden_fields = serializers.ListField(child=serializers.CharField(), required=False, default=[])

        def get_boq_elements(self, instance):
            if hasattr(instance, "snag_element_mappings") and instance.snag_element_mappings:
                return BoqElementSerializer(
                    [snag_element_mapping.boq_element for snag_element_mapping in instance.snag_element_mappings.all()],
                    many=True,
                    context={"uom_choices": self.context.get("uom_choices")},
                ).data
            return None

        class Meta:
            ref_name = "SnagAssignmentWithTimelineSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer(many=True)},
        operation_id="snags_assignment_timeline_list",
        operation_summary="snags_assignment_timeline_list",
    )
    def get(self, request, project_id, snag_id, *args, **kwargs):
        assignment_timeline_list = snag_assignment_list_with_timeline(
            snag_id=snag_id, organization_id=self.get_organization_id()
        )
        return Response(
            self.OutputSerializer(
                assignment_timeline_list,
                context={"uom_choices": UnitOfMeasurementCache.get()},
                many=True,
            ).data,
            status=HTTP_200_OK,
        )


class AllotPOCAndCommitTimelineApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        allotted_poc_id = HashIdField(allow_null=True)
        committed_at = CustomDateField(allow_null=True)

        def validate(self, attrs):
            if not (attrs["allotted_poc_id"] or attrs["committed_at"]):
                raise SnagException(ExceptionConstant.AT_LEAST_ONE_FIELD_NEEDS_TO_BE_FILLED)
            return attrs

        class Meta:
            ref_name = "AllottedPOCAndCommitTimelineInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer,
        responses={HTTP_200_OK: AssignmentTimelineSerializer()},
        operation_id="snag_poc_and_commit_timeline",
        operation_summary="Snag POC and Commit Timelines",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, snag_assignment_id, *args, **kwargs):
        data = self.validate_input_data()
        snag_assignment = snag_assignment_get(snag_assignment_id=snag_assignment_id, snag_id=snag_id)
        timeline = snag_allotment_and_timeline_create(
            snag_assignment=snag_assignment,
            snag_id=snag_id,
            user_id=request.user.pk,
            allotted_poc_id=data.get("allotted_poc_id"),
            committed_at=data.get("committed_at"),
            project_id=project_id,
        )
        obj = None
        if timeline:
            obj = assignment_timeline_fetch(assignment_timeline_id=timeline.pk)

        return Response(
            AssignmentTimelineSerializer(obj).data if obj else None,
            status=HTTP_200_OK,
        )


class AssignmentTimelineResolutionStatusUpdateAPI(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        class PreviewFileSerializer(BaseSerializer):
            name = serializers.CharField(max_length=100)
            file_url = CustomFileField()

            class Meta:
                ref_name = "AssignmentTimelinePreviewFileSerializer"

        preview_files = PreviewFileSerializer(many=True)
        resolution_remark = serializers.CharField(
            max_length=1000,
            required=False,
            allow_null=True,
        )
        resolution_status = serializers.ChoiceField(choices=ResolutionStatusChoices.choices)

        class Meta:
            ref_name = "AssignmentTimelineResolveInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: AssignmentTimelineSerializer()},
        operation_id="snag_assignment_resolve_api",
        operation_summary="Mark Snag as resolved",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, snag_assignment_id, *args, **kwargs):
        snag_assignment = snag_assignment_get(snag_assignment_id=snag_assignment_id, snag_id=snag_id)
        if not snag_assignment:
            raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

        data = self.validate_input_data()

        if snag_assignment.resolution_status == data.get("resolution_status"):
            self.set_response_message(
                "Snag is already marked as {resolution_status}".format(resolution_status=data.get("resolution_status"))
            )
            return Response(status=HTTP_400_BAD_REQUEST)

        timeline = create_assignment_timeline(
            created_by_id=request.user.pk,
            snag_assignment_id=snag_assignment_id,
            resolution_remark=data.get("resolution_remark"),
            resolution_status=data.get("resolution_status"),
        )

        snag_assignment.resolution_status = data.get("resolution_status")
        snag_assignment.save(update_fields=["resolution_status"])

        timeline_preview_file_bulk_create(preview_files_list=data.get("preview_files"), timeline_id=timeline.id)

        obj = assignment_timeline_fetch(assignment_timeline_id=timeline.id)

        return Response(
            AssignmentTimelineSerializer(obj).data,
            status=HTTP_200_OK,
        )


class AssignmentTimelineCloseAPI(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: AssignmentTimelineSerializer()},
        operation_id="snag_assignment_close_api",
        operation_summary="Mark Snag as Closed",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, snag_assignment_id, *args, **kwargs):
        snag_assignment = snag_assignment_get(snag_assignment_id=snag_assignment_id, snag_id=snag_id)
        if not snag_assignment:
            raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

        if snag_assignment.closure_status == ClosureStatusChoices.CLOSED:
            raise SnagException(ExceptionConstant.SNAG_ALREADY_CLOSED)

        if snag_assignment.org_from_id != self.get_organization_id():
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_CLOSED_BY_YOUR_ORG)

        if not ProjectPermissionHelper.has_permission(
            user=request.user, project_id=project_id, permission=Permissions.CAN_CLOSE_SNAG
        ):
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_CLOSED_BY_YOU)

        timeline = create_assignment_timeline(
            created_by_id=request.user.pk,
            snag_assignment_id=snag_assignment_id,
            closure_status=ClosureStatusChoices.CLOSED,
        )

        snag_assignment.closure_status = ClosureStatusChoices.CLOSED
        snag_assignment.closed_at = timezone.now()
        snag_assignment.save(update_fields=["closure_status", "closed_at"])

        obj = assignment_timeline_fetch(assignment_timeline_id=timeline.id)

        return Response(
            AssignmentTimelineSerializer(obj).data,
            status=HTTP_200_OK,
        )


class AssignmentTimelineUnresolvedAPI(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: AssignmentTimelineSerializer()},
        operation_id="snag_assignment_mark_unresolved_api",
        operation_summary="Mark Snag as Unresolved API",
    )
    @transaction.atomic
    def put(self, request, project_id, snag_id, snag_assignment_id, *args, **kwargs):
        snag_assignment = snag_assignment_get(snag_assignment_id=snag_assignment_id, snag_id=snag_id)
        if not snag_assignment:
            raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

        if snag_assignment.resolution_status == ResolutionStatusChoices.UNRESOLVED:
            raise SnagException(ExceptionConstant.SNAG_ALREADY_UNRESOLVED)

        timeline = create_assignment_timeline(
            created_by_id=request.user.pk,
            snag_assignment_id=snag_assignment_id,
            resolution_status=ResolutionStatusChoices.UNRESOLVED,
        )

        snag_assignment.resolution_status = ResolutionStatusChoices.UNRESOLVED

        snag_assignment.save(update_fields=["resolution_status"])

        event_data = SnagUnresolvedEventData(
            project_id=project_id,
            snag_id=snag_id,
            timeline_id=timeline.id,
            snag_assignment_id=snag_assignment_id,
        )
        self.trigger_event(event=Events.SNAG_UNRESOLVED, event_data=event_data)

        obj = assignment_timeline_fetch(assignment_timeline_id=timeline.id)

        return Response(
            AssignmentTimelineSerializer(obj).data,
            status=HTTP_200_OK,
        )


class SnagUnAssignApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: AssignmentTimelineSerializer()},
        operation_id="snag_assignment_mark_un_assign_api",
        operation_summary="Un-assign Organization from Snag ",
    )
    @transaction.atomic
    def put(self, request, project_id, snag_id, snag_assignment_id, *args, **kwargs):
        snag_assignment = snag_assignment_get(snag_assignment_id=snag_assignment_id, snag_id=snag_id)
        if not snag_assignment:
            raise SnagException(ExceptionConstant.SNAG_ASSIGNMENT_NOT_FOUND)

        if snag_assignment.org_from_id != self.get_organization_id():
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_UNASSIGNED_BY_YOUR_ORG)

        if snag_assignment.allotted_poc_id is not None or snag_assignment.committed_at is not None:
            raise SnagException(ExceptionConstant.SNAG_CANNOT_BE_UNASSIGNED_ANYMORE)

        if snag_assignment.resolution_status == ResolutionStatusChoices.RESOLVED:
            raise SnagException(ExceptionConstant.CANNOT_ASSIGN_ALREADY_RESOLVED)

        snag_assignment.soft_delete(user_id=request.user.pk)

        return Response(status=HTTP_200_OK)


class SnagPreviewFileListApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: SnagPreviewFileSerializer(many=True)},
        operation_id="snag_preview_file_list_api",
        operation_summary="snags_preview_file_list",
    )
    def get(self, request, project_id, snag_id):
        snag_preview_files = snag_preview_file_list_get(snag_id=snag_id)
        return Response(SnagPreviewFileSerializer(snag_preview_files, many=True).data, status=HTTP_200_OK)


class SnagAssignmentButtonVisibleApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class OutputSerializer(BaseSerializer):
        id = serializers.CharField()
        mark_resolved = serializers.BooleanField()
        not_applicable = serializers.BooleanField()
        not_resolvable = serializers.BooleanField()
        mark_unresolved = serializers.BooleanField()
        pass_to_client = serializers.BooleanField()
        mark_close = serializers.BooleanField()
        mark_un_assign = serializers.BooleanField()
        allot_poc = serializers.BooleanField()
        commit_timeline = serializers.BooleanField()
        parent_assignment_id = HashIdField(allow_null=True)
        can_edit_assignment = serializers.BooleanField()

        class Meta:
            ref_name = "SnagAssignmentButtonVisibleApiOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="snag_assignment_button_visible_api",
        operation_summary="Buttons that needs to be visible",
    )
    def get(self, request, project_id, snag_id):
        data_list = snag_assignment_button_service(
            snag_id=snag_id, organization_id=self.get_organization_id(), project_id=project_id, user=request.user
        )
        data = self.OutputSerializer(data_list, many=True).data

        return Response(data, status=HTTP_200_OK)


class SnagPassToClientPrefillDataApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    class OutputSerializer(BaseSerializer):
        parent_assignment_id = HashIdField(allow_null=True)
        preview_files = AssignmentTimelinePreviewFileSerializer(many=True)
        remark = serializers.CharField()
        resolution_status = serializers.ChoiceField(choices=ResolutionStatusChoices.choices)

        class Meta:
            ref_name = "SnagPassToClientPrefillDataAPIOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="snag_assignment_prefilled_api",
        operation_summary="Snag Assignment Prefilled API",
    )
    def get(self, request, project_id, snag_id, snag_assignment_id):
        data = pass_to_client_prefilled_data(
            organization_id=self.get_organization_id(),
            snag_assignment_id=snag_assignment_id,
            snag_id=snag_id,
            project_id=project_id,
            user=request.user,
        )
        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class SnagAssignmentUpdateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        updated_boq_elements = HashIdListField(allow_null=True)
        deleted_boq_elements = HashIdListField(allow_null=True)
        date_of_closure = serializers.DateField(allow_null=True)
        allotted_poc_id = HashIdField(allow_null=True, required=False)
        committed_at = CustomDateField(allow_null=True, required=False)

        class Meta:
            ref_name = "SnagAssignmentUpdateApi"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: SnagAssignmentSerializer()},
        operation_id="snag_assignment_update_api",
        operation_summary="Snag Assignment Update API",
    )
    @transaction.atomic
    def put(self, request, project_id, snag_id, snag_assignment_id):
        data = self.validate_input_data()
        updated_assignment = update_assignment_card(
            snag_assignment_id=snag_assignment_id,
            updated_boq_elements=data.get("updated_boq_elements"),
            deleted_boq_elements=data.get("deleted_boq_elements"),
            date_of_closure=data.get("date_of_closure"),
            allotted_poc_id=data.get("allotted_poc_id"),
            committed_at=data.get("committed_at"),
            user_id=request.user.pk,
            project_id=project_id,
        )
        return Response(SnagAssignmentSerializer(updated_assignment).data, status=HTTP_200_OK)


class SnagReporterOptionsFetchApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    serializer_class = OrganizationSnagReporterConfigSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationSnagReporterConfigSerializer()},
        operation_id="org_snag_reporter_options_api",
        operation_summary="Organization Snag Reporter Options Api",
    )
    def get(self, request, *args, **kwargs):
        snag_config = organization_snag_reporter_options_fetch(org_id=self.get_organization_id(), is_pure_client=False)
        self.set_response_message("Snag reporter options fetched succesfully.")
        return Response(self.serializer_class(snag_config).data, status=HTTP_200_OK)


class SnagReporterOptionsUpdateApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    input_serializer_class = OrganizationSnagConfigInputSerializer
    serializer_class = OrganizationSnagReporterConfigSerializer

    @swagger_auto_schema(
        request_body=OrganizationSnagConfigInputSerializer,
        responses={HTTP_200_OK: OrganizationSnagReporterConfigSerializer()},
        operation_id="org_ snag_reporter_config_update_api",
        operation_summary="Organization Snag Reporter Config Update Api",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data: OrganizationSnagReporterConfigData = self.validate_input_data()
        try:
            validate_configuration_conditions(attrs=data)
        except SnagConfigurationException as e:
            self.set_response_message(e.message)
            raise e
        snag_reporters = snag_reporter_options_update(
            org_id=self.get_organization_id(), config_data=data, user_id=request.user.pk
        )
        self.set_response_message("Snag reporter config updated successfully.")
        return Response(self.serializer_class(snag_reporters).data, status=HTTP_200_OK)


class ProjectSnagReporterOptionsFetchApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    serializer_class = OrganizationSnagReporterConfigSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationSnagReporterConfigSerializer()},
        operation_id="project_snag_reporter_options_api",
        operation_summary="Project Snag Reporter Options Api",
    )
    def get(self, request, project_id, *args, **kwargs):
        snag_config = get_snag_reporter_options(
            org_id=self.get_organization_id(),
            is_pure_client=self.is_pure_client(),
            user=request.user,
            creator_org_id=self.get_creator_org_id(),
        )
        self.set_response_message("Snag reporter data fetched successfully.")
        return Response(
            self.serializer_class(
                snag_config,
            ).data,
            status=HTTP_200_OK,
        )


class SnagExcelTemplateDownloadApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    @swagger_auto_schema(
        responses={HTTP_200_OK: "snags.xlsx"},
        operation_id="snag_excel_template_download_api",
        operation_summary="Snag Excel Template Download Api",
    )
    def get(self, request, *args, **kwargs):
        excel_file = snag_excel_template_generate(
            org_id=self.get_organization_id(), is_pure_client=self.is_pure_client()
        )
        response = HttpResponse(content=excel_file, content_type="application/ms-excel")
        response["Content-Disposition"] = "attachment; filename=snags_upload.xlsx"
        return response


class SnagBulkCreateViaExcelApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        file = serializers.FileField()

        class Meta:
            ref_name = "SnagBulkCreateViaExcelApiInputSerialize"

    parser_classes = (MultiPartParser,)
    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: SnagDetailSerializer(many=True)},
        operation_id="snag_bulk_create_via_excel_api",
        operation_summary="Snag Bulk Create Via Excel Api",
    )
    @transaction.atomic
    def post(self, request, project_id, *args, **kwargs):
        data = self.validate_input_data()
        checked_snag_config = fetch_checked_snag_config(
            is_pure_client=self.is_pure_client(), org_id=self.get_organization_id()
        )
        try:
            snag_list, skip_position = snag_excel_validate_using_openpyxl(
                file=data.get("file"),
                excel_import_columns=SNAG_EXCEL_IMPORT_COLUMNS,
                checked_snag_config=checked_snag_config,
            )
        except ExcelError as e:
            return Response(
                {
                    "error_code": 4001,
                    "message": _("File Error"),
                    "description": str(e),
                    "error_file": None,
                },
                status=HTTP_400_BAD_REQUEST,
            )
        serializer = SnagsExcelRowSerializer(
            data=snag_list,
            many=True,
        )
        if not serializer.is_valid(raise_exception=False):
            error_list = serializer.errors
            for i in skip_position:
                error_list.insert(i - 2, {})
            error_file, total_errors, total_success = snag_excel_error_csv_generate(
                errors=error_list, excel_export_columns={value: key for key, value in SNAG_EXCEL_IMPORT_COLUMNS.items()}
            )
            return Response(
                {
                    "error_code": 4002,
                    "message": _("Errors found in {} rows").format(total_errors),
                    "description": _("{} snags successfully passed for upload").format(
                        total_success - len(skip_position)
                    ),
                    "error_file": error_file,
                },
                status=HTTP_400_BAD_REQUEST,
            )
        snag_bulk_create(
            project_id=project_id,
            snag_data=serializer.validated_data,
            user_id=request.user.pk,
            org_id=self.get_organization_id(),
        )

        return Response(status=HTTP_201_CREATED)


class SnagBulkDetailsApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []

    class InputSerializer(BaseSerializer):
        snag_ids = HashIdListField(required=False)
        all_snags = serializers.BooleanField(required=False)

        class Meta:
            ref_name = "SnagBulkDetailInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: SnagBulkDetailSerializer()},
        operation_id="snag_bulk_details_api",
        operation_summary="snag_bulk_details_api",
    )
    def post(self, request, project_id, *args, **kwargs):
        user = self.get_user()
        known_organization_ids = self.get_known_organization_ids()
        organization_id = self.get_organization_id()
        known_organization_ids.append(organization_id)
        data = self.validate_input_data()

        can_link_snag_item_permission = OrgPermissionHelper.has_permission(
            user=user, permission=Permissions.CAN_LINK_SNAG_ITEMS
        ) and ProjectPermissionHelper.has_permission(
            user=user, project_id=project_id, permission=Permissions.CAN_LINK_SNAG_ITEMS
        )

        snag_details = get_snag_bulk_details(
            snag_ids=data.get("snag_ids", []),
            project_id=project_id,
            all_snags=data.get("all_snags", False),
            organization_id=organization_id,
            can_link_snag_item=can_link_snag_item_permission,
            creator_org_id=self.get_creator_org_id(),
            client_org_id=self.get_project_client_id(),
        )

        return Response(
            SnagBulkDetailSerializer(
                snag_details,
                context={
                    "known_organization_ids": known_organization_ids,
                    "organization_id": self.get_organization_id(),
                },
                many=True,
            ).data,
            status=HTTP_200_OK,
        )
