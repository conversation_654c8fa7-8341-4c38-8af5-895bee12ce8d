from common.serializers import BaseDataclassSerializer
from snags.domain.entities import OrganizationSnagReporterConfigData, SnagReporterData


class SnagReporterDataclassSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = SnagReporterData


class OrganizationSnagReporterConfigSerializer(BaseDataclassSerializer):
    reporter_options = SnagReporterDataclassSerializer(many=True)

    class Meta:
        dataclass = OrganizationSnagReporterConfigData
