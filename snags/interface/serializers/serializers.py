from rest_framework import serializers

from boq.interface.serializers import BoqElementModelSerializer
from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import TypeOutputSerializer
from common.serializers import BaseDataclassSerializer, BaseModelSerializer, BaseSerializer
from common.utils import get_local_time
from core.serializers import OrganizationSerializer, UserModelSerializer, UserSerializer
from element.domain.services import BaseElementQuantityService
from rollingbanners.storage_backends import PublicMediaFileStorage
from snags.data.choices import SnagRoleChoices
from snags.data.models import (
    AssignmentTimeline,
    AssignmentTimelinePreviewFile,
    OrganizationSnagReporter,
    Snag,
    SnagAssignment,
    SnagCategory,
    SnagPreviewFile,
    SnagsCategoryMapping,
    SnagSubcategory,
    SnagSupportingDocument,
    SnagUpdateHistory,
)
from snags.domain.constants import SnagRoleOrganizationMapping
from snags.domain.entities import SnagData


class CategoryModelSerializer(BaseModelSerializer):
    class Meta:
        model = SnagCategory
        fields = "__all__"


class SubCategoryModelSerializer(BaseModelSerializer):
    class Meta:
        model = SnagSubcategory
        fields = "__all__"


class SubCategorySerializer(SubCategoryModelSerializer):
    class Meta(SubCategoryModelSerializer.Meta):
        ref_name = "SnagSubcategorySerializer"
        fields = ("id", "name")


class CategorySerializer(CategoryModelSerializer):
    class Meta(CategoryModelSerializer.Meta):
        ref_name = "SnagCategorySerializer"
        fields = ("id", "name")


class SnagsCategoryModelSerializer(BaseModelSerializer):
    class Meta:
        model = SnagsCategoryMapping
        fields = "__all__"


class SnagPreviewFileInputSerializer(BaseSerializer):
    type = serializers.ChoiceField(choices=SnagPreviewFile.TYPE_CHOICES)
    name = serializers.CharField(max_length=250)

    class Meta:
        ref_name = "SnagPreviewFileInput"


class SnagSupportingDocumentInputSerializer(BaseSerializer):
    type = serializers.CharField(max_length=10)
    name = serializers.CharField(max_length=250)

    class Meta:
        ref_name = "SnagSupportingDocumentInput"


class SnagPreviewFileModelSerializer(BaseModelSerializer):
    class Meta:
        model = SnagPreviewFile
        fields = "__all__"


class SnagPreviewFileSerializer(SnagPreviewFileModelSerializer):
    class Meta(SnagPreviewFileModelSerializer.Meta):
        fields = ("id", "type", "name", "uploaded_at", "file")
        ref_name = "SnagPreviewFile"


class SnagSupportingDocumentModelSerializer(BaseModelSerializer):
    class Meta:
        ref_name = "SnagSupportingDocument"
        model = SnagSupportingDocument
        fields = "__all__"


class SnagSupportingDocumentSerializer(SnagSupportingDocumentModelSerializer):
    class Meta(SnagSupportingDocumentModelSerializer.Meta):
        fields = ("id", "type", "name", "file", "uploaded_at")


class UserOrgSerializer(UserModelSerializer):
    organization_name = serializers.CharField(source="org.name")

    class Meta(UserModelSerializer.Meta):
        ref_name = "ProjectStatusHistoryUserOutput"
        fields = ("id", "name", "photo", "organization_name")


class UserOutputBaseSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    photo = serializers.CharField()

    class Meta:
        fields = ("id", "name", "photo")
        ref_name = "SnagUserOutput"


class AssignedToOrganizationSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()

    class Meta:
        ref_name = "AssignedToOrganizationSerializer"


class SnagModelSerializer(BaseModelSerializer):
    snag_category = CategorySerializer()
    snag_subcategory = SubCategorySerializer()
    code = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    expected_date_of_closure = serializers.SerializerMethodField()
    closed_at = serializers.SerializerMethodField()
    resolution_status = serializers.SerializerMethodField()
    closure_status = serializers.SerializerMethodField()
    assigned_to = serializers.SerializerMethodField()
    assign_count = serializers.SerializerMethodField()
    committed_at = serializers.SerializerMethodField()
    allotted_poc = serializers.SerializerMethodField()
    preview_file = serializers.SerializerMethodField()
    can_edit_snag = serializers.SerializerMethodField()
    can_delete_snag = serializers.SerializerMethodField()
    can_assign_organization = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()

    def get_comment_count(self, obj):
        if hasattr(obj, "comment_count"):
            return obj.comment_count if obj.comment_count else 0
        return None

    def get_can_assign_organization(self, obj):
        return True

    def get_can_edit_snag(self, obj):
        if hasattr(obj, "can_edit_snag"):
            return obj.can_edit_snag
        return None

    def get_can_delete_snag(self, obj):
        if hasattr(obj, "can_delete_snag"):
            return obj.can_delete_snag
        return None

    def get_preview_file(self, obj):
        if hasattr(obj, "preview_file") and obj.preview_file:
            return (
                {"file": PublicMediaFileStorage.url(obj.preview_file), "type": SnagPreviewFile.IMAGE}
                if obj.preview_file
                else None
            )
        return SnagPreviewFileSerializer(obj.preview_files.all()[0]).data if obj.preview_files.count() else None

    def get_committed_at(self, obj):
        if hasattr(obj, "committed_at"):
            return obj.committed_at
        return None

    def get_allotted_poc(self, obj):
        if hasattr(obj, "allotted_poc") and obj.allotted_poc:
            return (
                UserOutputBaseSerializer(
                    {
                        "id": obj.allotted_poc["id"],
                        "name": obj.allotted_poc["name"],
                        "photo": (
                            PublicMediaFileStorage.url(obj.allotted_poc["photo"]) if obj.allotted_poc["photo"] else None
                        ),
                    }
                ).data
                if obj.allotted_poc
                else None
            )
        return None

    def get_assign_count(self, obj):
        if hasattr(obj, "assignment_count") and hasattr(obj, "assigned_to_flag"):
            return obj.assignment_count - 1 if obj.assigned_to_flag else obj.assignment_count
        return 0

    def get_assigned_to(self, obj):
        if hasattr(obj, "assigned_to") and obj.assigned_to:
            return AssignedToOrganizationSerializer(obj.assigned_to).data
        return None

    def get_resolution_status(self, obj):
        if hasattr(obj, "resolution_status"):
            return " ".join(obj.resolution_status.split("_"))
        return None

    def get_closure_status(self, obj):
        if hasattr(obj, "closure_status"):
            return obj.closure_status
        return None

    def get_closed_at(self, obj):
        if hasattr(obj, "closed_at"):
            return obj.closed_at
        return None

    def get_expected_date_of_closure(self, obj):
        if hasattr(obj, "expected_date_of_closure"):
            return obj.expected_date_of_closure
        return None

    def get_updated_by(self, obj):
        return (
            UserOrgSerializer(obj.updated_by).data
            if obj.updated_by and obj.updated_by.org_id in self.context.get("known_organization_ids", [])
            else None
        )

    def get_created_by(self, obj):
        return (
            UserOrgSerializer(obj.created_by).data
            if obj.created_by.org_id in self.context.get("known_organization_ids", [])
            else None
        )

    def get_code(self, obj):
        if hasattr(obj, "code"):
            return obj.code
        return None

    class Meta:
        model = Snag
        fields = "__all__"


class SnagUpdateHistoryModelSerializer(BaseModelSerializer):
    created_by = serializers.SerializerMethodField()

    def get_created_by(self, obj):
        return (
            UserOrgSerializer(obj.created_by).data
            if obj.created_by.org_id in self.context.get("known_organization_ids", [])
            else None
        )

    class Meta:
        model = SnagUpdateHistory
        ref_name = "SnagUpdateHistory"
        fields = "__all__"


class SnagDetailSerializer(SnagModelSerializer):
    preview_files = SnagPreviewFileSerializer(many=True)
    supporting_documents = SnagSupportingDocumentSerializer(many=True)
    created_by_org = serializers.CharField()
    child_org = serializers.SerializerMethodField()
    can_link_snag_items = serializers.SerializerMethodField()
    hidden_fields = serializers.ListField(child=serializers.CharField(), required=False, default=[])
    expected_date_of_closure = serializers.SerializerMethodField()

    def get_expected_date_of_closure(self, obj):
        if hasattr(obj, "expected_date_of_closure"):
            return obj.expected_date_of_closure
        else:
            return None

    def get_can_link_snag_items(self, obj):
        if obj.snag_by_me:
            return obj.can_link_snag_items_for_me
        else:
            return obj.can_link_snag_items_for_client

    def get_child_org(self, obj):
        if (hasattr(obj, "all_assignment_count") and obj.all_assignment_count == 0) and (
            obj.created_by_org != obj.created_for_org
            and (self.context.get("organization_id") in [obj.created_by_org_id, obj.created_for_org_id])
        ):
            return OrganizationSerializer(obj.created_by_org).data
        return None

    class Meta(SnagModelSerializer.Meta):
        ref_name = "SnagDetailsSerializer"
        fields = (
            "id",
            "title",
            "code",
            "description",
            "location",
            "snag_category",
            "snag_subcategory",
            "resolution_status",
            "closure_status",
            "supporting_documents",
            "preview_files",
            "snag_category",
            "snag_subcategory",
            "created_at",
            "created_by",
            "updated_by",
            "updated_at",
            "created_by_org",
            "child_org",
            "can_edit_snag",
            "can_delete_snag",
            "can_assign_organization",
            "comment_count",
            "can_link_snag_items",
            "hidden_fields",
            "expected_date_of_closure",
        )


class SnagExcelSerializer(SnagModelSerializer):
    Category = serializers.SerializerMethodField()
    Sub_Category = serializers.SerializerMethodField()
    created_at = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    expected_date_of_closure = serializers.DateField(format="%Y-%m-%d")
    assigned_to = serializers.SerializerMethodField()
    committed_date = serializers.SerializerMethodField()
    resolved_preview_file = serializers.SerializerMethodField()
    preview_file = serializers.SerializerMethodField()
    resolution_status_timestamp = serializers.SerializerMethodField()
    resolution_status_by = serializers.SerializerMethodField()
    closure_status_by = serializers.SerializerMethodField()
    closure_status_timestamp = serializers.SerializerMethodField()

    def get_created_at(self, obj):
        return get_local_time(obj.created_at).strftime("%d %B %Y %I:%M %p %Z")

    def get_resolution_status_by(self, obj):
        return obj.resolution_by_name

    def get_resolution_status_timestamp(self, obj):
        return get_local_time(obj.resolution_date).strftime("%d %B %Y %I:%M %p %Z") if obj.resolution_date else ""

    def get_closure_status_by(self, obj):
        if hasattr(obj, "closed_by"):
            return obj.closed_by

    def get_closure_status_timestamp(self, obj):
        if hasattr(obj, "snag_closure_date") and obj.snag_closure_date is not None:
            return get_local_time(obj.snag_closure_date).strftime("%d %B %Y %I:%M %p %Z")

    def get_committed_date(self, obj):
        return get_local_time(obj.committed_at).strftime("%d %B %Y %I:%M %p %Z") if obj.committed_at else ""

    def get_preview_file(self, obj):
        if hasattr(obj, "preview_file") and obj.preview_file:
            return PublicMediaFileStorage.url(obj.preview_file)
        return None

    def get_resolved_preview_file(self, obj):
        if hasattr(obj, "resolved_preview_file") and obj.resolved_preview_file:
            return PublicMediaFileStorage.url(obj.resolved_preview_file)
        return None

    def get_Category(self, obj):
        if obj.snag_category:
            return obj.snag_category.name
        return None

    def get_Sub_Category(self, obj):
        if obj.snag_subcategory:
            return obj.snag_subcategory.name
        return None

    def get_created_by(self, obj):
        if obj.created_by:
            return obj.created_by.name
        return None

    def get_updated_by(self, obj):
        if obj.updated_by:
            return obj.updated_by.name
        return None

    def get_assigned_to(self, obj):
        if hasattr(obj, "assigned_to") and obj.assigned_to:
            return AssignedToOrganizationSerializer(obj.assigned_to).data["name"]
        return None

    class Meta(SnagModelSerializer.Meta):
        ref_name = "SnagListSerializer"
        fields = (
            "code",
            "title",
            "description",
            "Category",
            "Sub_Category",
            "assigned_to",
            "assign_count",
            "location",
            "created_at",
            "created_by",
            "expected_date_of_closure",
            "committed_date",
            "updated_at",
            "updated_by",
            "resolution_status",
            "resolution_status_by",
            "resolution_status_timestamp",
            "closure_status",
            "closure_status_by",
            "closure_status_timestamp",
            "preview_file",
            "resolved_preview_file",
        )


class AssignmentTimelinePreviewFileModelSerializer(BaseModelSerializer):
    class Meta:
        fields = "__all__"
        model = AssignmentTimelinePreviewFile


class AssignmentTimelinePreviewFileSerializer(AssignmentTimelinePreviewFileModelSerializer):
    class Meta(AssignmentTimelinePreviewFileModelSerializer.Meta):
        fields = ("id", "file", "name")
        model = AssignmentTimelinePreviewFile


class AssignmentTimelineModelSerializer(BaseModelSerializer):
    allotted_poc = UserOrgSerializer()
    status_timeline = serializers.SerializerMethodField()
    expected_date_of_closure = serializers.DateField(source="date_of_closure")
    preview_files = AssignmentTimelinePreviewFileSerializer(source="assignment_timeline_preview_files", many=True)
    created_by = UserOrgSerializer()

    class Meta:
        fields = "__all__"
        model = AssignmentTimeline


class SnagBulkDetailAssignmentSerializer(BaseSerializer):
    # There can be cases when there is some resolution_status and also deleted_by is not null
    # This means first resolution status of that assignment is changed and then it is unassigned
    org_from = OrganizationSerializer()
    org_to = OrganizationSerializer()
    closure_status = serializers.CharField()
    created_by = UserSerializer()
    created_at = serializers.DateTimeField()
    resolution_status = serializers.SerializerMethodField()
    resolution_remark = serializers.SerializerMethodField()
    resolution_status_updated_by = serializers.SerializerMethodField()
    resolution_status_updated_at = serializers.SerializerMethodField()
    resolution_attachments = serializers.SerializerMethodField()
    # deleted_at and unassigned_at are same
    deleted_at = serializers.DateTimeField()
    # deleted_by and unassigned_by are same
    deleted_by = UserSerializer()

    def get_resolution_attachments(self, obj):
        latest_timeline = obj.assignment_timelines.filter(resolution_status__isnull=False).first()

        if latest_timeline:
            return AssignmentTimelinePreviewFileSerializer(
                latest_timeline.assignment_timeline_preview_files.all(), many=True
            ).data
        return []

    def get_org_from(self, obj):
        return obj.org_from.name

    def get_org_to(self, obj):
        return obj.org_to.name

    def get_created_by(self, obj):
        return obj.created_by.name

    def get_closure_status(self, obj):
        latest_timeline = obj.assignment_timelines.first()
        return latest_timeline.closure_status if latest_timeline else None

    def get_resolution_status(self, obj):
        latest_timeline = obj.assignment_timelines.filter(resolution_status__isnull=False).first()
        return latest_timeline.resolution_status if latest_timeline else None

    def get_resolution_remark(self, obj):
        latest_timeline = obj.assignment_timelines.filter(resolution_status__isnull=False).first()
        if latest_timeline is None:
            return None
        if latest_timeline.resolution_remark is not None:
            return latest_timeline.resolution_remark
        elif latest_timeline.timeline_remark is not None:
            return latest_timeline.timeline_remark
        else:
            return None

    def get_resolution_status_updated_by(self, obj):
        latest_timeline = obj.assignment_timelines.filter(resolution_status__isnull=False).first()
        return UserSerializer(latest_timeline.created_by).data if latest_timeline else None

    def get_resolution_status_updated_at(self, obj):
        latest_timeline = obj.assignment_timelines.filter(resolution_status__isnull=False).first()
        return serializers.DateTimeField().to_representation(latest_timeline.created_at) if latest_timeline else None

    class Meta:
        fields = (
            "org_from",
            "org_to",
            "closure_status",
            "created_by",
            "created_at",
            "resolution_status",
            "resolution_remark",
            "resolution_status_updated_by",
            "resolution_status_updated_at",
            "resolution_attachments" "deleted_at",
            "deleted_by",
        )


class SnagBulkDetailSerializer(SnagDetailSerializer):
    snag_assignments = SnagBulkDetailAssignmentSerializer(many=True)
    expected_date_of_closure = serializers.SerializerMethodField()

    def get_expected_date_of_closure(self, obj):
        if hasattr(obj, "expected_date_of_closure"):
            return obj.expected_date_of_closure
        else:
            return None

    class Meta(SnagModelSerializer.Meta):
        fields = (
            "id",
            "title",
            "code",
            "description",
            "location",
            "snag_category",
            "snag_subcategory",
            "resolution_status",
            "closure_status",
            "supporting_documents",
            "preview_files",
            "snag_category",
            "snag_subcategory",
            "created_at",
            "created_by",
            "updated_by",
            "updated_at",
            "created_by_org",
            "child_org",
            "can_edit_snag",
            "can_delete_snag",
            "can_assign_organization",
            "comment_count",
            "expected_date_of_closure",
            "can_link_snag_items",
            "hidden_fields",
            "expected_date_of_closure",
            "snag_assignments",
        )


class AssignmentTimelineSerializer(AssignmentTimelineModelSerializer):
    class Meta(AssignmentTimelineModelSerializer.Meta):
        fields = (
            "id",
            "created_at",
            "created_by",
            "committed_at",
            "allotted_poc",
            "status_timeline",
            "is_snag_applicable",
            "resolution_remark",
            "preview_files",
        )

    def get_status_timeline(self, obj):
        if hasattr(obj, "resolution_status") and obj.resolution_status:
            return obj.resolution_status
        if hasattr(obj, "closure_status") and obj.closure_status:
            return obj.closure_status
        return None


class SnagAssignmentModelSerializer(BaseModelSerializer):
    org_to = OrganizationSerializer()
    org_from = OrganizationSerializer()
    boq_elements = serializers.SerializerMethodField()
    created_by = UserOrgSerializer()
    updated_by = UserOrgSerializer()
    assignment_timelines = AssignmentTimelineSerializer(many=True)
    unassigned_by = UserOrgSerializer(source="deleted_by")
    unassigned_at = serializers.DateTimeField(source="deleted_at")

    def get_boq_elements(self, obj):
        if hasattr(obj, "snag_element_mappings") and obj.snag_element_mappings:
            return SnagBoqElementSerializer(
                [snag_element_mapping.boq_element for snag_element_mapping in obj.snag_element_mappings.all()],
                many=True,
            ).data
        return None

    class Meta:
        model = SnagAssignment
        fields = "__all__"
        ref_name = "SnagAssignmentModelSerializer"


class SnagBoqElementSerializer(BoqElementModelSerializer):
    class Meta(BoqElementModelSerializer.Meta):
        fields = ("id", "preview_file", "category_name", "item_type", "uom", "rate", "code", "name", "quantity")
        ref_name = "SnagBoqElementSerializer"


class SnagAssignmentSerializer(SnagAssignmentModelSerializer):
    class Meta(SnagAssignmentModelSerializer.Meta):
        fields = (
            "id",
            "org_to",
            "org_from",
            "boq_elements",
            "date_of_closure",
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
            "unassigned_by",
            "unassigned_at",
        )


class UserOrgBaseSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField(max_length=100)
    organization_name = serializers.CharField(source="org.name", max_length=100)
    photo = serializers.SerializerMethodField()

    @staticmethod
    def get_photo(instance):
        return PublicMediaFileStorage.url(instance.photo.url) if instance.photo != "" else None

    class Meta:
        pass


class AssignToSerializer(BaseSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()

    class Meta:
        ref_name = "AssignToBaseSerializer"


class SnagListSerializer(BaseSerializer):
    class CategorySerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()

        class Meta:
            ref_name = "CategoryBaseSerializer"

    id = serializers.IntegerField()
    title = serializers.CharField()
    code = serializers.CharField()
    description = serializers.CharField()
    snag_category = CategorySerializer()
    snag_subcategory = CategorySerializer()
    location = serializers.CharField()
    created_at = serializers.DateTimeField()
    created_by = UserOrgBaseSerializer()
    updated_at = serializers.DateTimeField()
    updated_by = UserOrgBaseSerializer()
    expected_date_of_closure = serializers.DateField()
    resolution_status = serializers.SerializerMethodField()
    closure_status = serializers.CharField()
    assigned_to = serializers.SerializerMethodField()
    allotted_poc = serializers.SerializerMethodField()
    preview_file = serializers.SerializerMethodField()
    assign_count = serializers.SerializerMethodField()
    committed_at = serializers.DateTimeField()
    can_delete_snag = serializers.BooleanField()
    can_edit_snag = serializers.BooleanField()
    can_assign_organization = serializers.SerializerMethodField()
    comment_count = serializers.SerializerMethodField()
    can_bulk_allot_and_commit_snag = serializers.BooleanField()
    can_bulk_assign_snag = serializers.BooleanField()
    can_bulk_close_snag = serializers.BooleanField()
    can_link_snag_items = serializers.SerializerMethodField()

    def get_can_link_snag_items(self, instance):
        if instance.snag_by_me:
            return instance.can_link_snag_items_for_me
        else:
            return instance.can_link_snag_items_for_client

    @staticmethod
    def get_resolution_status(instance):
        return " ".join(instance.resolution_status.split("_"))

    @staticmethod
    def get_assign_count(instance):
        return instance.assignment_count - 1 if instance.assignment_count > 0 else 0

    @staticmethod
    def get_comment_count(instance):
        return instance.comment_count if instance.comment_count else 0

    @staticmethod
    def get_can_assign_organization(instance):
        return True

    @staticmethod
    def get_preview_file(instance):
        if instance.preview_file:
            return {"file": PublicMediaFileStorage.url(instance.preview_file), "type": SnagPreviewFile.IMAGE}
        return None

    @staticmethod
    def get_assigned_to(instance):
        return AssignToSerializer(instance.assigned_to).data if instance.assigned_to else None

    @staticmethod
    def get_allotted_poc(instance):
        if instance.allotted_poc:
            return (
                UserOutputBaseSerializer(
                    {
                        "id": instance.allotted_poc["id"],
                        "name": instance.allotted_poc["name"],
                        "photo": (
                            PublicMediaFileStorage.url(instance.allotted_poc["photo"])
                            if instance.allotted_poc["photo"]
                            else None
                        ),
                    }
                ).data
                if instance.allotted_poc
                else None
            )
        return None

    class Meta:
        ref_name = "SnagListSerializer"


class AssignmentTimelineBaseSerializer(BaseSerializer):
    class AssignmentTimelinePreviewFile(BaseSerializer):
        id = serializers.IntegerField()
        file = serializers.SerializerMethodField()
        name = serializers.CharField(max_length=100)

        @staticmethod
        def get_file(instance):
            return PublicMediaFileStorage.url(instance.file.url) if instance.file != "" else None

        class Meta:
            ref_name = "AssignmentTimelineBasePreviewFile"

    class RemarkOutputBaseSerializer(BaseSerializer):
        id = serializers.IntegerField()
        blocks = serializers.ListField(
            child=TypeOutputSerializer(
                context={
                    "allowed_type_choices": {
                        const.TEXT: TEXT_TYPE_LIST,
                        const.ATTACHMENT: None,
                        const.LINE_BREAK: None,
                    }
                }
            )
        )

        class Meta:
            ref_name = "RemarkOutputSerializer"

    id = serializers.IntegerField()
    created_at = serializers.DateTimeField()
    created_by = UserOrgBaseSerializer()
    committed_at = serializers.DateTimeField()
    allotted_poc = UserOrgBaseSerializer()
    status_timeline = serializers.SerializerMethodField()
    resolution_remark = serializers.CharField()
    preview_files = AssignmentTimelinePreviewFile(source="assignment_timeline_preview_files", many=True)
    timeline_remark = RemarkOutputBaseSerializer()

    @staticmethod
    def get_status_timeline(instance):
        if hasattr(instance, "resolution_status") and instance.resolution_status:
            return " ".join(instance.resolution_status.split("_"))
        if hasattr(instance, "closure_status") and instance.closure_status:
            return instance.closure_status
        return None

    class Meta:
        pass


class BoqElementSerializer(BaseSerializer):
    class BoqElementUomSerializer(BaseSerializer):
        name = serializers.CharField()
        value = serializers.IntegerField()

        class Meta:
            pass

    class BoqElementItemTypeSerializer(BaseSerializer):
        id = serializers.IntegerField()
        name = serializers.CharField()
        color_code = serializers.CharField()
        slider_type = serializers.CharField()

        class Meta:
            pass

    class BoqElementPreviewFile(BaseSerializer):
        id = serializers.IntegerField()
        type = serializers.CharField()
        is_main = serializers.BooleanField()
        file = serializers.CharField()
        name = serializers.CharField()

        class Meta:
            pass

    id = serializers.IntegerField()
    preview_file = serializers.SerializerMethodField()
    category_name = serializers.CharField(source="category.name")
    item_type = BoqElementItemTypeSerializer()
    uom = serializers.SerializerMethodField()
    code = serializers.CharField()
    name = serializers.CharField()
    quantity = serializers.DecimalField(max_digits=15, default=0, decimal_places=4)
    deleted_at = serializers.DateTimeField()
    quantity_dimensions = serializers.SerializerMethodField()

    def get_quantity_dimensions(self, instance):
        return BaseElementQuantityService().get_quantity_dimensions(instance.quantity_dimensions)

    @staticmethod
    def get_preview_file(obj):
        if hasattr(obj, "preview_file") and obj.preview_file:
            return {
                "file": PublicMediaFileStorage.url(obj.preview_file["file"]),
                "type": obj.preview_file["type"],
            }

    def get_uom(self, instance):
        return {"value": instance.uom, "name": self.context.get("uom_choices").get(str(instance.uom))}

    class Meta:
        ref_name = "BoqElementBaseSerializer"


class OrganizationSnagReporterModelSerializer(BaseModelSerializer):
    class Meta:
        model = OrganizationSnagReporter
        fields = "__all__"


class SnagReporterDataSerializer(BaseSerializer):
    key = serializers.ChoiceField(choices=SnagRoleChoices.choices)
    name = serializers.CharField(max_length=100, allow_null=True, required=False)
    is_checked = serializers.BooleanField()
    is_default = serializers.BooleanField()

    class Meta:
        ref_nam = "SnagReporterDataSerializer"


class OrganizationSnagConfigInputSerializer(BaseSerializer):
    reporter_options = SnagReporterDataSerializer(many=True)

    class Meta:
        ref_name = "OrganizationSnagConfigInputSerializer"


class SnagsExcelRowSerializer(BaseDataclassSerializer):
    snag_source = serializers.ChoiceField(choices=[(value, value) for value in SnagRoleOrganizationMapping.values()])
    snag_title = serializers.CharField(max_length=300, min_length=1)
    description = serializers.CharField(allow_null=True, allow_blank=True)
    location = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        dataclass = SnagData
