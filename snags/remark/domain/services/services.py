from snags.domain.services import create_assignment_timeline
from snags.remark.data.models import Remark
from snags.remark.domain.entities import RemarkData


def create_remark(remark_data: RemarkData):
    remark = Remark()
    remark.blocks = remark_data.blocks
    remark.save()
    return remark


def create_remark_service(assignment_id: int, user_id: int, remark_data: RemarkData):
    remark = create_remark(
        remark_data=remark_data,
    )
    create_assignment_timeline(created_by_id=user_id, snag_assignment_id=assignment_id, timeline_remark_id=remark.id)
    return remark
