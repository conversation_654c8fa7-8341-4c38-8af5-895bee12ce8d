from rest_framework import serializers

from common.json_parser.constants import TEXT_TYPE_LIST, const
from common.json_parser.serializers import (
    BaseRemarkBlockSerializer,
    TypeOutputSerializer,
)
from common.serializers import BaseModelSerializer
from snags.remark.data.models import Remark
from snags.remark.domain.entities import RemarkData


class RemarkInputSerializer(BaseRemarkBlockSerializer):
    class Meta:
        dataclass = RemarkData
        ref_name = "RemarkInputSerializer"


class RemarkBaseModelSerializer(BaseModelSerializer):
    blocks = serializers.ListField(
        child=TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.LINE_BREAK: None,
                    const.IMAGE: None,
                }
            }
        )
    )

    class Meta:
        fields = "__all__"
        model = Remark


class RemarkOutputSerializer(BaseModelSerializer):
    blocks = serializers.ListField(
        child=TypeOutputSerializer(
            context={
                "allowed_type_choices": {
                    const.TEXT: TEXT_TYPE_LIST,
                    const.ATTACHMENT: None,
                    const.LINE_BREAK: None,
                    const.IMAGE: None,
                }
            }
        )
    )

    class Meta:
        ref_name = "RemarkOutputSerializer"
        fields = ("id", "blocks")
        model = Remark
