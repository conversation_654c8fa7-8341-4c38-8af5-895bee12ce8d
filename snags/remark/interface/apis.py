from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED

from project.interface.apis.internal.apis import Project<PERSON><PERSON><PERSON><PERSON>
from snags.remark.domain.entities import RemarkData
from snags.remark.domain.services import create_remark_service
from snags.remark.interface.serializers import RemarkInputSerializer, RemarkOutputSerializer


class RemarkCreateApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = []
    input_serializer_class = RemarkInputSerializer

    @swagger_auto_schema(
        request_body=RemarkInputSerializer(),
        responses={HTTP_201_CREATED: RemarkOutputSerializer()},
        operation_id="snags_remark_create_api",
        operation_summary="Create Snags Assignment Remark API",
    )
    @transaction.atomic
    def post(self, request, project_id, snag_id, assignment_id, *args, **kwargs):
        remark_data: RemarkData = self.validate_input_data()
        remark = create_remark_service(assignment_id=assignment_id, user_id=request.user.pk, remark_data=remark_data)
        return Response(RemarkOutputSerializer(remark).data, status=HTTP_201_CREATED)
