from django.conf import settings
from django.db import models
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>teger<PERSON><PERSON>,
    OuterRef,
    Q,
    QuerySet,
    Subquery,
    Value,
    When,
)
from django.db.models.functions import Cast, Concat, JSONObject, LPad
from django.utils.module_loading import import_string

from boq.data.models import BoqElement
from common.constants import FILE_FIELD_MAX_LENGTH
from common.helpers import get_upload_path
from common.models import (
    CreateDeleteModel,
    CreateModel,
    CreateUpdateDeleteModel,
    UpdateModel,
    UploadDeleteModel,
)
from common.querysets import AvailableQuerySetMixin
from core.models import Organization, User
from project.data.models import Project
from rollingbanners.comment_base_service import CommentBaseService
from snags.data.choices import ClosureStatusChoices, ResolutionStatusChoices
from snags.remark.data.models import Remark

CommentHelperService: CommentBaseService = import_string(settings.COMMENT_HELPER_SERVICES)


class SnagCategory(CreateModel):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return str(self.name)

    class Meta:
        db_table = "snag_categories"


class SnagSubcategory(CreateModel):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return str(self.name)

    class Meta:
        db_table = "snag_subcategories"


class SnagsCategoryMapping(CreateModel):
    snag_category = models.ForeignKey(SnagCategory, on_delete=models.RESTRICT)
    snag_subcategory = models.ForeignKey(SnagSubcategory, on_delete=models.RESTRICT)

    def __str__(self):
        return f"{self.snag_category.name} {self.snag_subcategory.name}"

    class Meta:
        unique_together = ["snag_category", "snag_subcategory"]
        db_table = "snag_category_subcategory_mappings"


class Snag(CreateUpdateDeleteModel):
    class SnagQueryset(QuerySet, AvailableQuerySetMixin):
        def annotate_comment_count(self, organization_id: int):
            return self.annotate(
                comment_count=CommentHelperService.get_count(
                    context_group=CommentHelperService.GROUPED_CONTEXT.SNAG.name,
                    org_id=organization_id,
                )
            )

        def annotate_assignment_count(self, organization_id):
            return self.annotate(
                assignment_count=Count(
                    "snag_assignments",
                    filter=Q(
                        snag_assignments__org_from_id=organization_id,
                        snag_assignments__deleted_at__isnull=True,
                    ),
                )
            )

        def annotate_all_assignment_count(self, organization_id):
            return self.annotate(
                all_assignment_count=Count(
                    "snag_assignments",
                    filter=Q(
                        Q(snag_assignments__org_from_id=organization_id)
                        | Q(snag_assignments__org_to_id=organization_id),
                        snag_assignments__deleted_at__isnull=True,
                    ),
                )
            )

        def annotate_assigned_to_me_flag(self, organization_id):
            return self.annotate(
                assign_to_me_flag=Count(
                    "snag_assignments",
                    filter=Q(snag_assignments__org_to_id=organization_id, snag_assignments__deleted_at__isnull=True),
                ),
            )

        def annotate_org_to_id(self, organization_id: int):
            subquery = SnagAssignment.objects.available().filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
            return self.annotate(org_to_id=Subquery(subquery.values("org_to_id")[:1]))

        # org_to id means someone has assigned me
        # org_from id means I have assigned someone

        def annotate_org_from_id(self, organization_id: int):
            subquery = SnagAssignment.objects.available().filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
            return self.annotate(org_from_id=Subquery(subquery.values("org_from_id")[:1]))

        def annotate_preview_file(self):
            preview_file_subquery = (
                SnagPreviewFile.objects.filter(snag_id=OuterRef("pk"), type=SnagPreviewFile.IMAGE)
                .exclude(file="")
                .available()
                .order_by("uploaded_at")
            )
            return self.annotate(preview_file=Subquery(preview_file_subquery.values("file")[:1]))

        def annotate_resolved_preview_file(self, organization_id: int):
            latest_timeline_entry_subquery = AssignmentTimeline.objects.filter(
                snag_assignment__snag_id=OuterRef("pk"),
                snag_assignment__org_to_id=organization_id,
                resolution_status=ResolutionStatusChoices.RESOLVED,
            ).order_by("-id")
            self = self.annotate(latest_timeline_entry=Subquery(latest_timeline_entry_subquery.values("id")[:1]))
            resolved_preview_file_subquery = (
                AssignmentTimelinePreviewFile.objects.filter(
                    snag_assignment_timeline__snag_assignment__snag_id=OuterRef("pk"),
                    snag_assignment_timeline__snag_assignment__resolution_status=ResolutionStatusChoices.RESOLVED,
                    snag_assignment_timeline__snag_assignment__org_to_id=organization_id,
                    snag_assignment_timeline_id=OuterRef("latest_timeline_entry"),
                )
                .exclude(Q(file="") | Q(file__icontains=".mp4"))
                .order_by("-snag_assignment_timeline__created_at")
            )
            return self.annotate(resolved_preview_file=Subquery(resolved_preview_file_subquery.values("file")[:1]))

        def annotate_snag_code(self):
            return self.annotate(
                code=Concat(
                    Value("SNG"), LPad(Cast("serial_number", CharField()), 3, Value("0")), output_field=CharField()
                )
            )

        def annotate_resolution_status(self, organization_id: int):
            subquery_for_me = SnagAssignment.objects.filter(
                snag_id=OuterRef("pk"), org_to_id=organization_id
            ).available()
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .annotate(custom_order=Case(When(closure_status=ResolutionStatusChoices.RESOLVED, then=1), default=0))
                .order_by("custom_order")
            )

            return self.annotate(
                resolution_status=Case(
                    When(all_assignment_count=0, then=Value(ResolutionStatusChoices.UNRESOLVED)),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_me.values("resolution_status")[:1]),
                    ),
                    When(
                        Q(org_from_id=organization_id, all_assignment_count__gt=0),
                        then=Subquery(subquery_for_other.values("resolution_status")[:1]),
                    ),
                    default=Value(ResolutionStatusChoices.UNRESOLVED.value),
                )
            )

        def annotate_closure_status(self, organization_id: int):
            subquery_for_me = SnagAssignment.objects.filter(
                snag_id=OuterRef("pk"), org_to_id=organization_id
            ).available()
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .annotate(custom_order=Case(When(closure_status=ClosureStatusChoices.CLOSED, then=1), default=0))
                .order_by("custom_order")
            )
            return self.annotate(
                closure_status=Case(
                    When(all_assignment_count=0, then=Value(ClosureStatusChoices.OPEN)),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_me.values("closure_status")[:1]),
                    ),
                    When(
                        Q(org_from_id=organization_id, all_assignment_count__gt=0),
                        then=Subquery(subquery_for_other.order_by("closure_status").values("closure_status")[:1]),
                    ),
                    default=Value(ClosureStatusChoices.OPEN.value),
                )
            )

        def annotate_status_concatenation(self):
            return self.annotate(
                filter_type=Concat(F("closure_status"), Value("_"), F("resolution_status"), output_field=CharField())
            )

        def annotate_assigned_to_org(self, organization_id: int):
            subquery_for_me = (
                SnagAssignment.objects.select_related("org_to_id")
                .select_related("org_to")
                .available()
                .filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
                .annotate(org_data=JSONObject(id="org_to_id", name="org_to__name"))
            )
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .select_related("org_to")
                .available()
                .annotate(org_data=JSONObject(id="org_to_id", name="org_to_id__name"))
            )
            return self.annotate(
                assigned_to=Case(
                    When(all_assignment_count=0, then=JSONObject()),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_me.values("org_data")[:1]),
                    ),
                    When(
                        Q(org_from_id=organization_id, all_assignment_count__gt=0),
                        then=Subquery(subquery_for_other.values("org_data")[:1]),
                    ),
                    default=JSONObject(),
                )
            )

        def annotate_count_use_flag(self, organization_id: int):
            return self.annotate(
                assigned_to_flag=(
                    Case(
                        When(all_assignment_count=0, then=False),
                        When(
                            Q(all_assignment_count__gt=0, org_to_id=organization_id),
                            then=False,
                        ),
                        When(
                            Q(org_from_id=organization_id, all_assignment_count__gt=0),
                            then=True,
                        ),
                        default=False,
                    )
                )
            )

        def annotate_expected_date_of_closure(self, organization_id: int):
            subquery_for_me = (
                SnagAssignment.objects.select_related("org_to")
                .filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
                .available()
            )
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .order_by("-date_of_closure")
            )
            return self.annotate(
                expected_date_of_closure=(
                    Case(
                        When(all_assignment_count=0, then=None),
                        When(
                            Q(all_assignment_count__gt=0, org_to_id=organization_id),
                            then=Subquery(subquery_for_me.values("date_of_closure")[:1]),
                        ),
                        When(
                            Q(org_from_id=organization_id, all_assignment_count__gt=0),
                            then=Subquery(subquery_for_other.values("date_of_closure")[:1]),
                        ),
                        output_field=DateField(),
                        default=None,
                    )
                )
            )

        def annotate_committed_at(self, organization_id: int):
            subquery_for_me = SnagAssignment.objects.filter(
                snag_id=OuterRef("pk"), org_to_id=organization_id
            ).available()

            return self.annotate(committed_at=Subquery(subquery_for_me.values("committed_at")[:1]))

        def annotate_snag_resolution_date_and_resolution_status_by(self, organization_id: int):
            subquery_for_me = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
                .available()
                .annotate_resolution_date_and_resolution_by()
            )
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .annotate_resolution_date_and_resolution_by()
                .order_by("-resolution_date")
            )
            return self.annotate(
                resolution_date=(
                    Case(
                        When(all_assignment_count=0, then=None),
                        When(
                            Q(all_assignment_count__gt=0, org_to_id=organization_id),
                            then=Subquery(subquery_for_me.values("resolution_date")[:1]),
                        ),
                        When(
                            Q(org_from_id=organization_id, all_assignment_count__gt=0),
                            then=Subquery(subquery_for_other.values("resolution_date")[:1]),
                        ),
                        output_field=DateField(),
                        default=None,
                    )
                ),
                resolution_by_name=(
                    Case(
                        When(all_assignment_count=0, then=None),
                        When(
                            Q(all_assignment_count__gt=0, org_to_id=organization_id),
                            then=Subquery(subquery_for_me.values("resolution_by_name")[:1]),
                        ),
                        When(
                            Q(org_from_id=organization_id, all_assignment_count__gt=0),
                            then=Subquery(subquery_for_other.values("resolution_by_name")[:1]),
                        ),
                        default=None,
                    )
                ),
            )

        def annotate_snag_closure_date(self, organization_id: int):
            subquery_for_me = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
                .available()
                .annotate_closure_date()
            )
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .annotate_closure_date()
                .order_by("-closure_date")
            )
            return self.annotate(
                closure_date=(
                    Case(
                        When(all_assignment_count=0, then=None),
                        When(
                            Q(all_assignment_count__gt=0, org_to_id=organization_id),
                            then=Subquery(subquery_for_me.values("closure_date")[:1]),
                        ),
                        When(
                            Q(org_from_id=organization_id, all_assignment_count__gt=0),
                            then=Subquery(subquery_for_other.values("closure_date")[:1]),
                        ),
                        output_field=DateField(),
                    )
                )
            )

        def annotate_snag_closure_date_v2(self, organization_id: int):
            subquery_for_me = SnagAssignment.objects.filter(
                snag_id=OuterRef("pk"), org_to_id=organization_id
            ).available()
            subquery_for_other = (
                SnagAssignment.objects.filter(snag_id=OuterRef("pk"), org_from_id=organization_id)
                .available()
                .annotate(custom_order=Case(When(closure_status=ClosureStatusChoices.CLOSED, then=1), default=0))
                .order_by("custom_order")
            )

            return self.annotate(
                snag_closure_date=Case(
                    When(all_assignment_count=0, then=None),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_me.values("closed_at")[:1]),
                    ),
                    When(
                        Q(org_from_id=organization_id, all_assignment_count__gt=0),
                        then=Subquery(subquery_for_other.order_by("closure_status").values("closed_at")[:1]),
                    ),
                    default=None,
                ),
            )

        def annotate_snag_closed_by(self, organization_id: int):
            base_subquery = (
                AssignmentTimeline.objects.select_related("snag_assignment", "created_by")
                .filter(
                    snag_assignment__snag_id=OuterRef("pk"),
                    closure_status=ClosureStatusChoices.CLOSED,
                )
                .order_by("-created_at")
                .annotate(name=Concat("created_by__first_name", Value(""), "created_by__last_name"))
            )
            subquery_for_org_to = base_subquery.filter(snag_assignment__org_to_id=organization_id).values("name")[:1]
            subquery_for_org_from = base_subquery.filter(snag_assignment__org_from_id=organization_id).values("name")[
                :1
            ]

            return self.annotate(
                closed_by=Case(
                    When(all_assignment_count=0, then=None),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_org_to),
                    ),
                    When(
                        Q(org_from_id=organization_id, all_assignment_count__gt=0),
                        then=Subquery(subquery_for_org_from),
                    ),
                    default=None,
                )
            )

        def annotate_allotted_poc(self, organization_id: int):
            subquery_for_me = (
                SnagAssignment.objects.select_related("allotted_poc")
                .filter(snag_id=OuterRef("pk"), org_to_id=organization_id)
                .available()
                .annotate(
                    poc_data=JSONObject(
                        id="allotted_poc_id",
                        name=Concat("allotted_poc__first_name", Value(" "), "allotted_poc__last_name"),
                        photo="allotted_poc__photo",
                    )
                )
            )

            return self.annotate(
                allotted_poc=Case(
                    When(all_assignment_count=0, then=JSONObject()),
                    When(
                        Q(all_assignment_count__gt=0, org_to_id=organization_id),
                        then=Subquery(subquery_for_me.values("poc_data")[:1]),
                    ),
                    default=JSONObject(),
                )
            )

        def annotate_allotted_or_committed_count(self, organization_id: int):
            return self.annotate(
                allotted_or_committed_count=Count(
                    "id",
                    filter=(
                        Q(
                            Q(
                                Q(snag_assignments__allotted_poc__isnull=False)
                                | Q(snag_assignments__committed_at__isnull=False)
                            ),
                            snag_assignments__deleted_at__isnull=True,
                            snag_assignments__org_from_id=organization_id,
                        )
                    ),
                )
            )

        def annotate_can_delete_snag(self, organization_id: int):
            return self.annotate(
                can_delete_snag=Case(
                    When(
                        Q(created_for_org_id=organization_id) | Q(created_by_org_id=organization_id),
                        all_assignment_count=0,
                        then=True,
                    ),
                    When(
                        Q(created_for_org_id=organization_id) | Q(created_by_org_id=organization_id),
                        all_assignment_count__gt=0,
                        allotted_or_committed_count=0,
                        then=True,
                    ),
                    default=False,
                )
            )

        def annotate_can_bulk_assign_snag(self):
            return self.annotate(can_bulk_assign_snag=Value(True))

        def annotate_can_bulk_commit_and_allot_snag(self):
            return self.annotate(can_bulk_allot_and_commit_snag=Value(True))

        def annotate_can_bulk_close_snag(self, organization_id: int):
            return self.annotate(
                open_assignment_count=Count(
                    "snag_assignments",
                    filter=Q(
                        snag_assignments__org_from_id=organization_id,
                        snag_assignments__deleted_at__isnull=True,
                        snag_assignments__closure_status=ClosureStatusChoices.OPEN,
                    ),
                )
            ).annotate(
                can_bulk_close_snag=Case(
                    When(
                        open_assignment_count__gte=1,
                        then=True,
                    ),
                    default=False,
                )
            )

        def annotate_can_edit_snag(self, organization_id: int):
            return self.annotate(
                can_edit_snag=Case(
                    When(Q(created_for_org_id=organization_id) | Q(created_by_org_id=organization_id), then=True),
                    default=False,
                )
            )

        def annotate_snag_by_me(self, organization_id: int):
            return self.annotate(
                snag_by_me=Case(
                    When(
                        Q(created_by_org_id=organization_id) & Q(created_for_org_id=organization_id),
                        then=Value(True),
                    ),
                    default=Value(False),
                )
            )

        def annotate_can_link_snag_items(
            self,
            organization_id: int,
            can_link_snag_item_permission_for_current_org: bool,
            creator_org_id: int,
            client_org_id: int,
        ):
            return (
                self.annotate_snag_by_me(organization_id=organization_id)
                .annotate(
                    can_link_snag_item_permission_for_current_org=Cast(
                        Value(can_link_snag_item_permission_for_current_org), output_field=BooleanField()
                    )
                )
                .annotate(creator_org_id=Cast(Value(creator_org_id), output_field=IntegerField()))
                .annotate(client_org_id=Cast(Value(client_org_id), output_field=IntegerField()))
                .annotate(
                    can_link_snag_items_for_me=Case(
                        When(
                            Q(can_link_snag_item_permission_for_current_org=True),
                            then=Value(True),
                        ),
                        When(
                            Q(creator_org_id=organization_id),
                            then=Value(True),
                        ),
                        When(
                            Q(client_org_id=organization_id),
                            then=Value(False),
                        ),
                        default=Value(True),
                    )
                )
                .annotate(
                    can_link_snag_items_for_client=Case(
                        When(
                            Q(can_link_snag_item_permission_for_current_org=True),
                            then=Value(True),
                        ),
                        When(
                            Q(creator_org_id=organization_id),
                            then=Value(False),
                        ),
                        When(
                            Q(client_org_id=organization_id),
                            then=Value(False),
                        ),
                        default=Value(True),
                    )
                )
            )

    project = models.ForeignKey(Project, on_delete=models.RESTRICT)
    serial_number = models.IntegerField(default=1)
    title = models.CharField(max_length=100)
    description = models.TextField(default="", blank=True, null=True)
    location = models.CharField(max_length=100, null=True, blank=True)
    snag_category = models.ForeignKey(SnagCategory, on_delete=models.RESTRICT, null=True, blank=True)
    snag_subcategory = models.ForeignKey(SnagSubcategory, on_delete=models.RESTRICT, null=True, blank=True)
    created_for_org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    created_by_org = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")

    objects = SnagQueryset.as_manager()

    def __str__(self):
        return str(self.title)

    class Meta:
        db_table = "snags"
        indexes = [
            models.Index(fields=["created_at"]),
            models.Index(fields=["deleted_at"]),
        ]


class SnagUpdateHistory(CreateModel):
    snag = models.ForeignKey(Snag, related_name="snag_update_history", on_delete=models.RESTRICT)

    class Meta:
        db_table = "snag_update_histories"
        indexes = [
            models.Index(fields=["created_at"]),
        ]


class SnagPreviewFile(UploadDeleteModel):
    class SnagPreviewFileQueryset(QuerySet, AvailableQuerySetMixin):
        pass

    IMAGE = "IMAGE"
    VIDEO = "VIDEO"

    TYPE_CHOICES = [(IMAGE, "Image"), (VIDEO, "Video")]
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    name = models.CharField(max_length=100)
    file = models.FileField(null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    snag = models.ForeignKey(Snag, on_delete=models.RESTRICT, related_name="preview_files")
    objects = SnagPreviewFileQueryset.as_manager()

    class Meta:
        db_table = "snag_preview_files"


class SnagSupportingDocument(UploadDeleteModel):
    class SnagSupportingDocumentQueryset(QuerySet, AvailableQuerySetMixin):
        pass

    snag = models.ForeignKey(Snag, on_delete=models.RESTRICT, related_name="supporting_documents")
    file = models.FileField(null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=250)
    type = models.CharField(max_length=10)
    objects = SnagSupportingDocumentQueryset.as_manager()

    class Meta:
        db_table = "snag_supporting_documents"


class SnagAssignment(CreateUpdateDeleteModel):
    class SnagAssignmentQueryset(QuerySet, AvailableQuerySetMixin):
        def annotate_closure_date(self):
            subquery = AssignmentTimeline.objects.filter(
                snag_assignment_id=OuterRef("pk"), closure_status=ClosureStatusChoices.CLOSED
            ).order_by("-id")
            return self.annotate(
                closure_date=Case(
                    When(closure_status=ClosureStatusChoices.CLOSED, then=Subquery(subquery.values("created_at")[:1]))
                )
            )

        def annotate_resolution_date_and_resolution_by(self):
            subquery = AssignmentTimeline.objects.filter(snag_assignment_id=OuterRef("pk")).order_by("-id")
            return self.annotate(
                resolution_date=Subquery(subquery.values("created_at")[:1]),
                resolution_by_name=Subquery(
                    subquery.annotate(
                        full_name=Concat(
                            F("created_by__first_name"),
                            Value(" "),
                            F("created_by__last_name"),
                            output_field=CharField(),
                        )
                    ).values("full_name")[:1]
                ),
            )

        def annotate_snag_code(self):
            return self.annotate(
                code=Concat(
                    Value("SNG"),
                    LPad(Cast("snag__serial_number", CharField()), 3, Value("0")),
                    output_field=CharField(),
                )
            )

    snag = models.ForeignKey(Snag, related_name="snag_assignments", on_delete=models.RESTRICT)
    boq_elements = models.ManyToManyField(BoqElement, through="AssignmentElementMapping")
    date_of_closure = models.DateField(null=True, blank=True)  # expected date of closure
    org_from = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    org_to = models.ForeignKey(Organization, on_delete=models.RESTRICT, related_name="+")
    resolution_status = models.CharField(
        max_length=20, choices=ResolutionStatusChoices.choices, default=ResolutionStatusChoices.UNRESOLVED
    )
    closure_status = models.CharField(
        max_length=10, choices=ClosureStatusChoices.choices, default=ClosureStatusChoices.OPEN
    )
    objects = SnagAssignmentQueryset.as_manager()
    closed_at = models.DateTimeField(null=True, blank=True)
    committed_at = models.DateTimeField(null=True, blank=True)  # committed date
    allotted_poc = models.ForeignKey(User, related_name="+", on_delete=models.RESTRICT, null=True, blank=True)

    class Meta:
        unique_together = ["snag", "org_to", "org_from"]
        db_table = "snag_assignments"
        indexes = [
            models.Index(fields=["closure_status"]),
            models.Index(fields=["resolution_status"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["deleted_at"]),
        ]


class AssignmentElementMapping(CreateDeleteModel):
    class AssignmentElementMappingQueryset(QuerySet, AvailableQuerySetMixin):
        pass

    boq_element = models.ForeignKey(BoqElement, on_delete=models.RESTRICT)
    snag_assignment = models.ForeignKey(SnagAssignment, on_delete=models.RESTRICT, related_name="snag_element_mappings")
    objects = AssignmentElementMappingQueryset.as_manager()

    class Meta:
        db_table = "snag_assignment_element_mappings"


class AssignmentTimeline(CreateModel):
    class AssignmentTimelineQueryset(QuerySet, AvailableQuerySetMixin):
        def annotate_snag_code(self):
            return self.annotate(
                code=Concat(
                    Value("SNG"),
                    LPad(Cast("snag_assignment__snag__serial_number", CharField()), 3, Value("0")),
                    output_field=CharField(),
                )
            )

        def previous_committed_timeline(self):
            subquery = (
                AssignmentTimeline.objects.filter(
                    snag_assignment_id=OuterRef("snag_assignment_id"),
                    committed_at__isnull=False,
                )
                .exclude(id=OuterRef("id"))
                .order_by("-created_at")
            )

            return self.annotate(previous_committed_timeline=Subquery(subquery.values("committed_at")[:1]))

        def annotate_allotter_user_id(self):
            subquery = AssignmentTimeline.objects.filter(
                snag_assignment_id=OuterRef("snag_assignment_id"),
                allotted_poc_id=OuterRef("snag_assignment__allotted_poc_id"),
            ).order_by("-created_at")
            return self.annotate(
                allotter_user_id=Case(
                    When(
                        snag_assignment__allotted_poc_id__isnull=False,
                        then=Subquery(subquery.values("created_by_id")[:1]),
                    ),
                    default=None,
                )
            )

    snag_assignment = models.ForeignKey(SnagAssignment, related_name="assignment_timelines", on_delete=models.RESTRICT)
    allotted_poc = models.ForeignKey(User, related_name="+", on_delete=models.RESTRICT, null=True, blank=True)
    committed_at = models.DateTimeField(null=True, blank=True)
    resolution_status = models.CharField(max_length=20, choices=ResolutionStatusChoices.choices, null=True, blank=True)
    closure_status = models.CharField(max_length=10, choices=ClosureStatusChoices.choices, null=True, blank=True)
    is_snag_applicable = models.BooleanField(blank=True, null=True)
    resolution_remark = models.TextField(max_length=500, null=True, blank=True)
    timeline_remark = models.OneToOneField(
        Remark, on_delete=models.RESTRICT, null=True, related_name="assignment_timeline"
    )
    objects = AssignmentTimelineQueryset.as_manager()

    class Meta:
        db_table = "snag_assignment_timelines"
        indexes = [
            models.Index(fields=["resolution_status"]),
            models.Index(fields=["closure_status"]),
            models.Index(fields=["committed_at"]),
        ]


class AssignmentTimelinePreviewFile(models.Model):
    snag_assignment_timeline = models.ForeignKey(
        AssignmentTimeline,
        related_name="assignment_timeline_preview_files",
        on_delete=models.RESTRICT,
    )
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = "snag_assignment_timeline_preview_files"


class OrganizationSnagReporter(UpdateModel):
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, unique=True, related_name="+")
    reporter_options = models.JSONField(null=True, blank=True)

    class Meta:
        db_table = "snags_organization_default_snag_reporters"
