from django_filters import FilterSet, filters

from common.filters import Custom<PERSON>ilterList
from common.utils import get_id_list_from_string
from snags.data.models import Snag


class SnagFilter(FilterSet):
    title = filters.CharFilter(field_name="title", lookup_expr="icontains")
    code = filters.CharFilter(method="filter_code")
    location = filters.CharFilter(field_name="location", lookup_expr="icontains")
    created_by = CustomFilterList(field_name="created_by", lookup_expr="in")
    expected_closure_date = filters.DateFilter(method="filter_expected_closure_date")
    assigned_to = CustomFilterList(method="filter_assigned_to")

    def filter_code(self, queryset, name, value):
        value = value.lstrip("SNG0")
        queryset = queryset.filter(serial_number=value)
        return queryset

    def filter_expected_closure_date(self, queryset, name, value):
        return queryset.filter(snag_assignments__date_of_closure=value)

    def filter_assigned_to(self, queryset, name, value):
        return queryset.filter(snag_assignments__org_to_id=get_id_list_from_string(value=value))

    class Meta:
        model = Snag
        fields = (
            "title",
            "location",
            "created_by",
        )
