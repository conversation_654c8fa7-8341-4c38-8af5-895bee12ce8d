from django.db import models
from django.utils.translation import gettext_lazy as _


class ResolutionStatusChoices(models.TextChoices):
    RESOLVED = "resolved", _("Resolved")
    UNRESOLVED = "unresolved", _("Un-Resolved")
    NOT_APPLICABLE = "not_applicable", _("Not-Applicable")
    NOT_RESOLVABLE = "not_resolvable", _("Not-Resolvable")


class ClosureStatusChoices(models.TextChoices):
    OPEN = "open", _("Open")
    CLOSED = "closed", _("Closed")


class StatusFilterChoices(models.TextChoices):
    ALL = "all", _("all")
    OPEN_UNRESOLVED = "open_unresolved", _("Open Unresolved")
    OPEN_RESOLVED = "open_resolved", _("Open Resolved")
    CLOSED = "closed", _("Closed")
    CLOSED_RESOLVED = "closed_resolved", _("Closed Resolved")
    CLOSED_UNRESOLVED = "closed_unresolved", _("Closed Unresolved")
    OPEN_NOT_APPLICABLE = "open_not_applicable", _("Open Not Applicable")
    OPEN_NOT_RESOLVABLE = "open_not_resolvable", _("Open Not Resolvable")
    CLOSED_NOT_APPLICABLE = "closed_not_applicable", _("Closed Not Applicable")
    CLOSED_NOT_RESOLVABLE = "closed_not_resolvable", _("Closed Not Resolvable")


class SnagRoleChoices(models.TextChoices):
    """
    This denotes snag creator i.e., same as 'snag_by_me' and 'snag_by_client' on front-end
    """

    SNAG_FOR_ME = "snag_for_me", _("Snag for Me")  # request user is creating snag
    SNAG_FOR_CLIENT = "snag_for_client", _("Snag for Client")  # request user is creating snag on client's behalf
