from django.db.models import Prefetch, Q

from boq.data.models import BoqElement
from project.data.models import Project, ProjectOrganization
from snags.data.choices import ClosureStatusChoices, ResolutionStatusChoices
from snags.data.models import (
    AssignmentElementMapping,
    AssignmentTimeline,
    OrganizationSnagReporter,
    Snag,
    SnagAssignment,
    SnagPreviewFile,
    SnagsCategoryMapping,
    SnagSupportingDocument,
    SnagUpdateHistory,
)


def get_snag(snag_id: int):
    return Snag.objects.get(id=snag_id)


def snags_category_mappings_get():
    return SnagsCategoryMapping.objects.select_related("snag_category", "snag_subcategory")


def check_project_organization_snags_exists(project_id: int, organization_id: int):
    snag_assignment_id_list = (
        SnagAssignment.objects.select_related("snag")
        .available()
        .filter(snag__project_id=project_id, org_to_id=organization_id)
        .values_list("snag_id", flat=True)
    )

    if_exists = (
        Snag.objects.filter(project_id=project_id)
        .filter(
            Q(Q(created_by_org_id=organization_id) | Q(created_for_org_id=organization_id))
            | Q(id__in=snag_assignment_id_list)
        )
        .available()
    ).exists()
    return if_exists


def snags_fetch(project_id: int, organization_id: int):
    snag_assignment_id_list = (
        SnagAssignment.objects.select_related("snag")
        .available()
        .filter(snag__project_id=project_id, org_to_id=organization_id)
        .values_list("snag_id", flat=True)
    )
    snag_queryset = (
        Snag.objects.select_related(
            "snag_category",
            "snag_subcategory",
            "created_for_org",
            "created_by_org",
        )
        .filter(project_id=project_id)
        .filter(
            Q(Q(created_by_org_id=organization_id) | Q(created_for_org_id=organization_id))
            | Q(id__in=snag_assignment_id_list)
        )
        .available()
    )
    return snag_queryset


def snags_fetch_all_with_annotations(
    project_id: int, organization_id: int, can_link_snag_item_permission: bool, creator_org_id: int, client_org_id: int
):
    queryset = (
        snags_fetch(project_id=project_id, organization_id=organization_id)
        .select_related(
            "created_by",
            "created_by__org",
            "updated_by",
            "updated_by__org",
        )
        .annotate_snag_code()
        .annotate_preview_file()
        .annotate_resolved_preview_file(organization_id=organization_id)
        .prefetch_related("snag_assignments")
        .annotate_org_to_id(organization_id=organization_id)
        .annotate_org_from_id(organization_id=organization_id)
        .annotate_assignment_count(organization_id=organization_id)
        .annotate_all_assignment_count(organization_id=organization_id)
        .annotate_assigned_to_org(organization_id=organization_id)
        .annotate_closure_status(organization_id=organization_id)
        .annotate_resolution_status(organization_id=organization_id)
        .annotate_status_concatenation()
        .annotate_committed_at(organization_id=organization_id)
        .annotate_expected_date_of_closure(organization_id=organization_id)
        .annotate_count_use_flag(organization_id=organization_id)
        .annotate_snag_resolution_date_and_resolution_status_by(organization_id=organization_id)
        .annotate_snag_closure_date(organization_id=organization_id)
        .annotate_snag_closure_date_v2(organization_id=organization_id)
        .annotate_snag_closed_by(organization_id)
        .annotate_allotted_or_committed_count(organization_id=organization_id)
        .annotate_allotted_poc(organization_id=organization_id)
        .annotate_can_delete_snag(organization_id=organization_id)
        .annotate_can_edit_snag(organization_id=organization_id)
        .annotate_comment_count(organization_id=organization_id)
        .annotate_assigned_to_me_flag(organization_id=organization_id)
        .annotate_can_bulk_assign_snag()
        .annotate_can_bulk_commit_and_allot_snag()
        .annotate_can_bulk_close_snag(organization_id=organization_id)
        .annotate_can_link_snag_items(
            organization_id=organization_id,
            can_link_snag_item_permission_for_current_org=can_link_snag_item_permission,
            creator_org_id=creator_org_id,
            client_org_id=client_org_id,
        )
    )

    return queryset


def snag_details_get(
    snag_id: int, organization_id: int, can_link_snag_item: bool, creator_org_id: int, client_org_id: int
):
    snag_details = (
        Snag.objects.filter(id=snag_id)
        .available()
        .select_related(
            "created_by_org",
            "snag_category",
            "snag_subcategory",
            "created_by",
            "created_by__org",
            "updated_by",
            "updated_by__org",
        )
        .prefetch_related(
            Prefetch("preview_files", SnagPreviewFile.objects.available()),
            Prefetch("supporting_documents", SnagSupportingDocument.objects.available()),
        )
        .annotate_snag_code()
        .annotate_org_to_id(organization_id=organization_id)
        .annotate_org_from_id(organization_id=organization_id)
        .annotate_assignment_count(organization_id=organization_id)
        .annotate_all_assignment_count(organization_id=organization_id)
        .annotate_resolution_status(organization_id=organization_id)
        .annotate_closure_status(organization_id=organization_id)
        .annotate_allotted_or_committed_count(organization_id=organization_id)
        .annotate_can_delete_snag(organization_id=organization_id)
        .annotate_can_edit_snag(organization_id=organization_id)
        .annotate_comment_count(organization_id=organization_id)
        .annotate_can_link_snag_items(
            organization_id=organization_id,
            can_link_snag_item_permission_for_current_org=can_link_snag_item,
            creator_org_id=creator_org_id,
            client_org_id=client_org_id,
        )
        .first()
    )
    return snag_details


def get_snag_bulk_details(
    all_snags: bool,
    snag_ids: list,
    organization_id: int,
    can_link_snag_item: bool,
    creator_org_id: int,
    client_org_id: int,
    project_id: int,
):
    if all_snags:
        queryset = snags_fetch(project_id=project_id, organization_id=organization_id)
    else:
        queryset = Snag.objects.filter(id__in=snag_ids)

    snag_details = (
        queryset.available()
        .select_related(
            "created_by_org",
            "snag_category",
            "snag_subcategory",
            "created_by",
            "created_by__org",
            "updated_by",
            "updated_by__org",
        )
        .prefetch_related(
            Prefetch("preview_files", SnagPreviewFile.objects.available()),
            Prefetch("supporting_documents", SnagSupportingDocument.objects.available()),
            Prefetch(
                "snag_assignments",
                SnagAssignment.objects.select_related("org_from", "org_to")
                .all()
                .prefetch_related(
                    Prefetch(
                        "assignment_timelines",
                        AssignmentTimeline.objects.filter(
                            Q(resolution_status__isnull=False) | Q(closure_status__isnull=False)
                        )
                        .select_related("timeline_remark")
                        .order_by("-created_at")
                        .prefetch_related(Prefetch("assignment_timeline_preview_files")),
                    )
                ),
            ),
        )
        .annotate_snag_code()
        .annotate_org_to_id(organization_id=organization_id)
        .annotate_org_from_id(organization_id=organization_id)
        .annotate_assignment_count(organization_id=organization_id)
        .annotate_all_assignment_count(organization_id=organization_id)
        .annotate_resolution_status(organization_id=organization_id)
        .annotate_closure_status(organization_id=organization_id)
        .annotate_allotted_or_committed_count(organization_id=organization_id)
        .annotate_can_delete_snag(organization_id=organization_id)
        .annotate_can_edit_snag(organization_id=organization_id)
        .annotate_comment_count(organization_id=organization_id)
        .annotate_expected_date_of_closure(organization_id=organization_id)
        .annotate_can_link_snag_items(
            organization_id=organization_id,
            can_link_snag_item_permission_for_current_org=can_link_snag_item,
            creator_org_id=creator_org_id,
            client_org_id=client_org_id,
        )
        .annotate_expected_date_of_closure(organization_id=organization_id)
    )
    return snag_details


def snag_assignment_list_with_timeline(snag_id: int, organization_id: int):
    assignment_timeline = (
        SnagAssignment.objects.filter(Q(Q(org_to_id=organization_id) | Q(org_from_id=organization_id)), snag_id=snag_id)
        .select_related(
            "org_from",
            "org_to",
            "deleted_by",
            "created_by",
            "updated_by",
            "created_by__org",
            "updated_by__org",
            "deleted_by__org",
        )
        .prefetch_related(
            Prefetch(
                "snag_element_mappings",
                queryset=AssignmentElementMapping.objects.filter(deleted_at__isnull=True).prefetch_related(
                    Prefetch(
                        "boq_element",
                        queryset=BoqElement.objects.select_related("item_type", "category").annotate_preview_file(),
                    )
                ),
            )
        )
        .prefetch_related(
            Prefetch(
                "assignment_timelines",
                queryset=AssignmentTimeline.objects.select_related(
                    "created_by", "created_by__org", "allotted_poc", "allotted_poc__org", "timeline_remark"
                )
                .prefetch_related("assignment_timeline_preview_files")
                .order_by("created_at"),
            ),
        )
        .order_by("created_at")
    )
    return assignment_timeline


def snag_fetch(snag_id: int):
    return Snag.objects.filter(id=snag_id).available().first()


def assignment_timeline_fetch(assignment_timeline_id: int):
    return (
        AssignmentTimeline.objects.filter(id=assignment_timeline_id)
        .prefetch_related("assignment_timeline_preview_files")
        .select_related("allotted_poc", "created_by", "created_by__org")
        .first()
    )


def snag_assignment_get(snag_assignment_id: int, snag_id: int) -> SnagAssignment:
    snag_assignment = SnagAssignment.objects.filter(id=snag_assignment_id, snag_id=snag_id).available().first()
    return snag_assignment


def snag_preview_file_list_get(snag_id: int):
    snag_preview_files_list = SnagPreviewFile.objects.filter(snag_id=snag_id).available().all()
    return snag_preview_files_list


def get_alloted_or_committed_snag_assignments(snag_id: int):
    alloted_or_committed_snag_assignments = SnagAssignment.objects.filter(
        Q(Q(committed_at__isnull=False) | Q(allotted_poc__isnull=False)), snag_id=snag_id
    ).available()
    return alloted_or_committed_snag_assignments


def get_child_object_for_snag_assignment(snag_id: int, org_to_id: int):
    snag_assignment = SnagAssignment.objects.filter(snag_id=snag_id, org_to_id=org_to_id).available().first()
    return snag_assignment


def get_snag_project_client(project_id: int, organization_id: int):
    client = ProjectOrganization.objects.filter(organization=organization_id, project_id=project_id).first().assigned_by
    if client.id == organization_id:
        client = Project.objects.filter(id=project_id).select_related("client").first().client
    return client


def get_snag_serial_number(project_id: int):
    snag_count = Snag.objects.filter(project=project_id).count()
    return snag_count + 1


def get_snag_update_history(snag_id: int):
    snag_update_history = SnagUpdateHistory.objects.filter(snag_id=snag_id)
    return snag_update_history


def get_snag_assignment_for_snag(snag_id: int):
    return SnagAssignment.objects.filter(snag_id=snag_id)


def get_snag_assignments_from_snag_id_list(snag_id_list: list):
    return SnagAssignment.objects.filter(snag_id__in=snag_id_list)


def get_snag_assignments_from_org(snag_id_list: list, organization_id: int):
    return (
        get_snag_assignments_from_snag_id_list(snag_id_list=snag_id_list)
        .filter(org_from_id=organization_id)
        .available()
    )


def check_if_snags_already_assigned_to_org(snag_id_list: list, org_to_id: int):
    snag_assignment = (
        get_snag_assignments_from_snag_id_list(snag_id_list=snag_id_list)
        .filter(org_to_id=org_to_id)
        .available()
        .exists()
    )
    return snag_assignment


def get_snag_assignments_to_org(snag_id_list: list, organization_id: int):
    return (
        get_snag_assignments_from_snag_id_list(snag_id_list=snag_id_list).filter(org_to_id=organization_id).available()
    )


def get_assignment_timeline_latest_order(snag_assignment_id: int):
    return AssignmentTimeline.objects.filter(snag_assignment_id=snag_assignment_id).order_by("-created_at")


def get_snag_list_from_ids(snag_id_list: list):
    return Snag.objects.filter(id__in=snag_id_list)


def if_exists_resolved_or_closed_snag_assignments_to_org(snag_id_list: list, organization_id: int):
    return (
        get_snag_assignments_to_org(snag_id_list=snag_id_list, organization_id=organization_id)
        .filter(
            Q(Q(resolution_status=ResolutionStatusChoices.RESOLVED) | Q(closure_status=ClosureStatusChoices.CLOSED)),
        )
        .exists()
    )


def get_open_snag_assignments_from_org(snag_id_list: list, organization_id: int):
    return SnagAssignment.objects.filter(
        snag_id__in=snag_id_list, org_from_id=organization_id, closure_status=ClosureStatusChoices.OPEN
    )


def snag_reporter_options_fetch(org_id: int):
    snag_reporter = OrganizationSnagReporter.objects.filter(organization_id=org_id).first()
    return snag_reporter.reporter_options if snag_reporter else None
