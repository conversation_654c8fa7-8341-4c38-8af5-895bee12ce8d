# Generated by Django 3.2.15 on 2025-02-27 12:11

import common.helpers
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('snags', '0013_auto_20240909_1434'),
    ]

    operations = [
        migrations.AlterField(
            model_name='assignmenttimelinepreviewfile',
            name='file',
            field=models.FileField(max_length=250, upload_to=common.helpers.get_upload_path),
        ),
        migrations.AlterField(
            model_name='snagpreviewfile',
            name='file',
            field=models.FileField(blank=True, max_length=250, null=True, upload_to=common.helpers.get_upload_path),
        ),
        migrations.AlterField(
            model_name='snagsupportingdocument',
            name='file',
            field=models.FileField(blank=True, max_length=250, null=True, upload_to=common.helpers.get_upload_path),
        ),
    ]
