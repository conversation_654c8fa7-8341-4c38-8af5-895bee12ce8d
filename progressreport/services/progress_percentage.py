import decimal
import math
from collections import defaultdict
from datetime import date, datetime, timedelta
from typing import List, Optional, Tuple

import structlog
from django.core.exceptions import ValidationError
from django.db.models import F, QuerySet, Sum
from django.utils import timezone

from boq.data.choices import BoqElementStatus
from boq.data.models import BoqElement, BoqElementHistory, BoqSection
from boq.data.selectors import boq_elements_fetch
from boq.services.work_report import boq_element_progress_percentage_update, element_progress_history_create
from common.constants import TWO_DECIMAL_FACTOR
from common.entities import ProjectOrganizationEntity
from common.utils import date_range, get_current_local_time, get_local_time
from core.caches import UnitOfMeasurementCache
from element.data.choices import ItemTypeUpdateMethodChoices
from progressreport.caches import ProgressReportItemTypeCache
from progressreport.constants import Constants
from progressreport.entity import (
    ProgressReportElementUpdateMethodChangeData,
    ProgressReportProgressEntity,
    ProgressReportTodayAndTotalProgressForConfigEntity,
    ProgressReportTodayAndTotalProgressForHeaderEntity,
)
from progressreport.querysets import WorkProgressElementQueryset
from progressreport.selectors import (
    boq_element_history_list_of_a_date,
    get_boq_elements_and_histories_between_range,
    latest_boq_element_history_fetch,
)
from project.domain.caches import ItemTypeProgressUpdateMethodCache

logger = structlog.get_logger(__name__)


class ProjectProgressHelper:
    @staticmethod
    def get_today_progress(elements: List[ProgressReportProgressEntity], total_amount: int) -> (float, float):
        total_completion_amount = 0
        for element in elements:
            previous_day_progress = 0 if element.previous_day_progress is None else element.previous_day_progress
            total_completion_amount += float(
                element.amount * (float((float(element.progress_percentage) - float(previous_day_progress)) / 100))
            )
        if total_amount != 0:
            return (
                math.floor(float((total_completion_amount / total_amount) * 100) * TWO_DECIMAL_FACTOR)
                / TWO_DECIMAL_FACTOR,
                total_completion_amount,
            )
        return 0, 0

    @staticmethod
    def get_total_progress(elements: List[ProgressReportProgressEntity]) -> (float, float, float):
        if not elements:
            return 0, 0, 0
        total_amount = 0
        total_completion_amount = 0

        for element in elements:
            total_completion_amount += float(element.amount * (float(element.progress_percentage / 100)))
            total_amount += element.amount
        if total_amount != 0:
            return (
                math.floor(float((total_completion_amount / total_amount) * 100) * TWO_DECIMAL_FACTOR)
                / TWO_DECIMAL_FACTOR,
                total_amount,
                total_completion_amount,
            )
        return 0, total_amount, total_completion_amount

    @staticmethod
    def get_today_progress_for_section(
        elements: List[ProgressReportProgressEntity], total_amount: int
    ) -> (float, float):
        total_completion_amount = 0
        for element in elements:
            previous_day_progress = 0 if element.previous_day_progress is None else element.previous_day_progress
            total_completion_amount += float(
                element.amount * (float((float(element.progress_percentage) - float(previous_day_progress)) / 100))
            )
        logger.info("Today progress", total_completion_amount=total_completion_amount, total_amount=total_amount)
        if total_amount != 0:
            return (
                decimal.Decimal((total_completion_amount / total_amount) * 100),
                total_completion_amount,
            )
        return 0, 0

    @staticmethod
    def get_total_progress_for_section(elements: List[ProgressReportProgressEntity]) -> (float, float, float):
        if not elements:
            return 0, 0, 0
        total_amount = 0
        total_completion_amount = 0

        for element in elements:
            total_completion_amount += float(element.amount / 100 * (float(element.progress_percentage)))
            total_amount += element.amount
        logger.info("Total progress", total_completion_amount=total_completion_amount, total_amount=total_amount)
        if total_amount != 0:
            logger.info("Total progress return ", ret=float((total_completion_amount * 100) / total_amount))
            return (
                decimal.Decimal((total_completion_amount / total_amount) * 100),
                total_amount,
                total_completion_amount,
            )
        return 0, total_amount, total_completion_amount

    @classmethod
    def process(
        cls, elements: list[ProgressReportProgressEntity], today_updated_elements: list[ProgressReportProgressEntity]
    ) -> Tuple[decimal.Decimal, decimal.Decimal, float, float]:
        today_progress = 0
        total_progress = 0
        total_completion_amount = 0
        today_completion_amount = 0
        total_amount = 0

        if elements:
            total_progress, total_amount, total_completion_amount = cls.get_total_progress(elements=elements)

        if today_updated_elements and elements:
            today_progress, today_completion_amount = cls.get_today_progress(
                elements=today_updated_elements, total_amount=total_amount
            )

        return today_progress, total_progress, total_completion_amount, today_completion_amount

    @classmethod
    def process_section(
        cls, elements: list[ProgressReportProgressEntity], today_updated_elements: list[ProgressReportProgressEntity]
    ) -> Tuple[decimal.Decimal, decimal.Decimal, float, float]:
        today_progress = 0
        total_progress = 0
        total_completion_amount = 0
        today_completion_amount = 0
        total_amount = 0

        if elements:
            total_progress, total_amount, total_completion_amount = cls.get_total_progress_for_section(
                elements=elements
            )

        if today_updated_elements and elements:
            today_progress, today_completion_amount = cls.get_today_progress_for_section(
                elements=today_updated_elements, total_amount=total_amount
            )

        return today_progress, total_progress, total_completion_amount, today_completion_amount


def initial_date_element_progress(*, boq_elements: list[BoqElement], start_date: datetime) -> dict:
    element_progress_dict = {
        boq_element.id: {
            "current_progress_percentage": 0,
            "previous_day_progress_percentage": 0,
            "boq_rate": boq_element.boq_rate,
            "element_status": boq_element.element_status,
            "item_type_id": boq_element.item_type_id,
        }
        for boq_element in boq_elements
    }

    boq_element_histories: list[BoqElementHistory] = boq_element_history_list_of_a_date(
        boq_elements=boq_elements, start_date=start_date
    )
    for boq_element_history in boq_element_histories:
        element_progress_dict[boq_element_history.element_id][
            "current_progress_percentage"
        ] = boq_element_history.progress_percentage

        if boq_element_history.created_at.date() == start_date:
            element_progress_dict[boq_element_history.element_id][
                "previous_day_progress_percentage"
            ] = boq_element_history.previous_day_progress_percentage
        else:
            element_progress_dict[boq_element_history.element_id][
                "previous_day_progress_percentage"
            ] = boq_element_history.progress_percentage

    return element_progress_dict


def add_progress_entity(
    *,
    current_progress_percentage: int,
    item_type_id: int,
    boq_rate: float,
    previous_day_progress_percentage: int,
    item_type_configs: dict,
) -> ProgressReportProgressEntity:
    progress_percentage = ProgressReportItemTypeCache.get_latest_progress_percentage_from_config(
        incoming_progress_percentage=current_progress_percentage,
        item_type_id=item_type_id,
        item_type_configs=item_type_configs,
    )

    data = ProgressReportProgressEntity(
        progress_percentage=int(progress_percentage),
        previous_day_progress=previous_day_progress_percentage,
        amount=boq_rate,
    )
    return data


def get_today_and_total_progress_with_range(
    *, boq_elements: list[BoqElement], start_date: datetime, end_date: Optional[datetime]
):
    date_wise_progress_percentage = defaultdict(dict)
    boq_elements = boq_elements.annotate_final_amount().annotate(boq_rate=Sum(F("final_element_amount")))

    boq_elements_and_histories = get_boq_elements_and_histories_between_range(
        boq_elements=boq_elements, start_date=start_date, end_date=end_date
    )

    boq_element_data: dict[int, BoqElement] = {}
    boq_element_ppr: dict[int, dict[datetime, ProgressReportProgressEntity]] = dict()
    for element in boq_elements_and_histories:
        history_dict = {}
        for history in element.boq_element_history.all():
            history_dict[history.created_at.date()] = history
        setattr(element, "dates", history_dict)
        boq_element_ppr[element.pk] = {}
        boq_element_data[element.id] = element

    last_date = None
    item_type_configs = {}
    check_status = [
        BoqElementStatus.DRAFT.value,
        BoqElementStatus.APPROVED.value,
        BoqElementStatus.REQUESTED.value,
        BoqElementStatus.CHANGE_REQUESTED.value,
        BoqElementStatus.CANCELLATION_REQUESTED.value,
    ]

    for single_date in date_range(start_date - timedelta(days=1), end_date):
        if single_date > date.today():
            date_wise_progress_percentage[single_date]["today_progress"] = None
            date_wise_progress_percentage[single_date]["total_progress"] = None
            continue

        todays_updated_ppr_entities = []
        all_ppr_entities = []
        is_99_percent = False

        for element_id, element in boq_element_data.items():
            # If there is no progress percentage for the element on that day,
            # then simply copy the progress percentage of the previous day.
            if last_date is not None and element.dates.get(single_date, None) is None:
                ppr_entity = boq_element_ppr[element_id].get(last_date)
                if ppr_entity:
                    boq_element_ppr[element_id][single_date] = ppr_entity
                    all_ppr_entities.append(ppr_entity)
                    continue

            if (
                not last_date
                and len(element.dates) == 0
                and element.status_updated_at is not None
                and element.progress_percentage != 0
                and element.status_updated_at.date() > start_date
            ):
                element.dates[element.status_updated_at.date()] = BoqElementHistory(
                    progress_percentage=element.progress_percentage
                )
            if element.element_status not in check_status:
                continue

            if element.progress_percentage < 100 and float(element.boq_rate) == 0:
                is_99_percent = True
            if not last_date:
                previous = (
                    element.progress_percentage
                    if not len(element.dates) and not element.initial_progress_percentage
                    else (element.initial_progress_percentage if element.initial_progress_percentage else 0)
                )
            else:
                previous = boq_element_ppr[element_id].get(last_date).progress_percentage

            current = (
                element.dates.get(single_date).progress_percentage if element.dates.get(single_date, None) else previous
            )
            ppr_entity = add_progress_entity(
                current_progress_percentage=current,
                item_type_id=element.item_type_id,
                boq_rate=float(element.boq_rate),
                previous_day_progress_percentage=previous,
                item_type_configs=item_type_configs,
            )
            boq_element_ppr[element_id][single_date] = ppr_entity
            if ppr_entity.previous_day_progress != ppr_entity.progress_percentage:
                todays_updated_ppr_entities.append(ppr_entity)
            all_ppr_entities.append(ppr_entity)
        (
            today_progress,
            total_progress,
            total_completion_amount,
            today_completion_amount,
        ) = ProjectProgressHelper.process(elements=all_ppr_entities, today_updated_elements=todays_updated_ppr_entities)
        logger.info(
            "Progress Data",
            total_progress=total_progress,
            today_progress=today_progress,
            total_completion_amount=total_completion_amount,
            today_completion_amount=today_completion_amount,
            is_99_percent=is_99_percent,
        )
        if today_progress == 100 and is_99_percent:
            today_progress = 99.99
        if total_progress == 100 and is_99_percent:
            total_progress = 99.99
        if last_date is not None:
            date_wise_progress_percentage[single_date]["today_progress"] = today_progress
            date_wise_progress_percentage[single_date]["total_progress"] = total_progress
        last_date = single_date
    return date_wise_progress_percentage


def get_today_and_total_progress(
    *, boq_elements: QuerySet[BoqElement], item_type_configs: dict = {}, is_section_data: bool = False
) -> Tuple[float, float, dict, list, float, float]:
    if not boq_elements:
        return 0, 0, {}, [], 0, 0

    boq_elements = (
        boq_elements.annotate_final_amount()
        .annotate(total_amount=Sum(F("final_element_amount")))
        .select_related("section")
    )

    is_not_100_percent = boq_elements.filter(
        progress_percentage__lt=100,
        element_status__in=[
            BoqElementStatus.DRAFT.value,
            BoqElementStatus.APPROVED.value,
            BoqElementStatus.REQUESTED.value,
            BoqElementStatus.CHANGE_REQUESTED.value,
            BoqElementStatus.CANCELLATION_REQUESTED.value,
        ],
    ).exists()

    elements = boq_elements.annotate_previous_day_progress().values(
        "progress_percentage",
        "status_updated_at",
        "item_type_id",
        "total_amount",
        "element_status",
        "previous_day_progress",
        "section__name",
        "section_id",
        "client_rate",
        "update_method",
    )

    element_data = []
    today_updated_elements = []
    section_element_data = defaultdict(list)
    section_names = []
    is_unsection_element = False
    section_ids = []
    section_is_99_percent = defaultdict(bool)
    for element in elements:
        if element.get("element_status") not in (
            BoqElementStatus.DRAFT.value,
            BoqElementStatus.APPROVED.value,
            BoqElementStatus.REQUESTED.value,
            BoqElementStatus.CHANGE_REQUESTED.value,
            BoqElementStatus.CANCELLATION_REQUESTED.value,
        ):
            continue
        if (
            not element.get("update_method")
            or element.get("update_method") == ItemTypeUpdateMethodChoices.QUANTITY.value
        ):
            progress_percentage = element.get("progress_percentage")
        else:
            progress_percentage = ProgressReportItemTypeCache.get_latest_progress_percentage_if_config_changed(
                incoming_progress_percentage=element.get("progress_percentage"),
                item_type_id=element.get("item_type_id"),
                item_type_configs=item_type_configs,
            )

        data = ProgressReportProgressEntity(
            progress_percentage=progress_percentage,
            status_updated_at=element.get("status_updated_at"),
            previous_day_progress=element.get("previous_day_progress"),
            amount=float(element.get("total_amount", 0)),
        )

        if data.status_updated_at and get_local_time(data.status_updated_at).date() == get_current_local_time().date():
            today_updated_elements.append(data)
            if element.get("section_id") is None:
                is_unsection_element = True
            else:
                if element.get("section_id") not in section_ids:
                    section_names.append({"name": element.get("section__name"), "id": element.get("section_id")})
                    section_ids.append(element.get("section_id"))
        element_data.append(data)
        if (
            section_is_99_percent.get(element.get("section_id"), None) is None
            and progress_percentage < 100
            and float(element.get("client_rate")) == 0
        ):
            section_is_99_percent[element.get("section_id") if element.get("section_id") else 0] = True
        section_element_data[element.get("section_id") if element.get("section_id") else 0].append(data)

    section_names.sort(key=lambda x: x["name"])
    if is_unsection_element:
        section_names = [{"id": 0, "name": BoqSection.DEFAULT_SECTION}] + section_names

    if is_section_data:
        (
            today_progress,
            total_progress,
            total_completion_amount,
            today_completion_amount,
        ) = ProjectProgressHelper.process_section(elements=element_data, today_updated_elements=today_updated_elements)
    else:
        (
            today_progress,
            total_progress,
            total_completion_amount,
            today_completion_amount,
        ) = ProjectProgressHelper.process(elements=element_data, today_updated_elements=today_updated_elements)
    section_total_progress = defaultdict(int)
    for section_id, data in section_element_data.items():
        section_total_progress[section_id] = ProjectProgressHelper.get_total_progress_for_section(elements=data)[0]
        if section_total_progress[section_id] == 100 and section_is_99_percent.get(section_id, False):
            section_total_progress[section_id] = 99.99
    logger.info(
        "Progess Data",
        is_not_100_percent=is_not_100_percent,
        today_progress=today_progress,
        total_progress=total_progress,
        section_total_progress=section_total_progress,
        section_names=section_names,
    )
    if not is_not_100_percent:
        return (
            today_progress,
            100,
            dict(section_total_progress),
            section_names,
            today_completion_amount,
            total_completion_amount,
        )
    elif total_progress == 100 and is_not_100_percent:
        return (
            99.99 if today_progress == 100 else today_progress,
            99.99,
            dict(section_total_progress),
            section_names,
            today_completion_amount,
            total_completion_amount,
        )

    return (
        today_progress,
        total_progress,
        dict(section_total_progress),
        section_names,
        today_completion_amount,
        total_completion_amount,
    )


"""
this selector receives elements annotated with trigger updated element_final_amount_with_tax
"""


def get_today_and_total_progress_v2(
    boq_elements: WorkProgressElementQueryset,
    is_not_100_percent: bool,
    item_type_configs: dict = {},
    is_section_data: bool = False,
) -> ProgressReportTodayAndTotalProgressForConfigEntity:
    boq_elements = boq_elements.annotate(total_amount=Sum(F("element_final_amount_with_tax"))).select_related("section")

    elements = boq_elements.annotate_previous_day_progress().values(
        "progress_percentage",
        "status_updated_at",
        "item_type_id",
        "total_amount",
        "element_status",
        "previous_day_progress",
        "section__name",
        "section_id",
        "client_rate",
        "update_method",
    )

    if not elements:
        return ProgressReportTodayAndTotalProgressForConfigEntity(
            today_progress=decimal.Decimal(0),
            total_progress=decimal.Decimal(0),
            section_total_progress={},
            section_names=[],
            today_progress_amount=decimal.Decimal(0),
            total_progress_amount=decimal.Decimal(0),
        )

    element_data = []
    today_updated_elements = []
    section_element_data = defaultdict(list)
    section_names = []
    is_unsection_element = False
    section_ids = []
    section_is_99_percent = defaultdict(bool)
    for element in elements:
        if element.get("element_status") not in (
            BoqElementStatus.DRAFT.value,
            BoqElementStatus.APPROVED.value,
            BoqElementStatus.REQUESTED.value,
            BoqElementStatus.CHANGE_REQUESTED.value,
            BoqElementStatus.CANCELLATION_REQUESTED.value,
        ):
            continue
        if (
            not element.get("update_method")
            or element.get("update_method") == ItemTypeUpdateMethodChoices.QUANTITY.value
        ):
            progress_percentage = element.get("progress_percentage")
        else:
            progress_percentage = ProgressReportItemTypeCache.get_latest_progress_percentage_if_config_changed(
                incoming_progress_percentage=element.get("progress_percentage"),
                item_type_id=element.get("item_type_id"),
                item_type_configs=item_type_configs,
            )

        data = ProgressReportProgressEntity(
            progress_percentage=progress_percentage,
            status_updated_at=element.get("status_updated_at"),
            previous_day_progress=element.get("previous_day_progress"),
            amount=float(element.get("total_amount", 0)),
        )

        if data.status_updated_at and get_local_time(data.status_updated_at).date() == get_current_local_time().date():
            today_updated_elements.append(data)
            if element.get("section_id") is None:
                is_unsection_element = True
            else:
                if element.get("section_id") not in section_ids:
                    section_names.append({"name": element.get("section__name"), "id": element.get("section_id")})
                    section_ids.append(element.get("section_id"))
        element_data.append(data)
        if (
            section_is_99_percent.get(element.get("section_id"), None) is None
            and progress_percentage < 100
            and float(element.get("client_rate")) == 0
        ):
            section_is_99_percent[element.get("section_id") if element.get("section_id") else 0] = True
        section_element_data[element.get("section_id") if element.get("section_id") else 0].append(data)

    section_names.sort(key=lambda x: x["name"])
    if is_unsection_element:
        section_names = [{"id": 0, "name": BoqSection.DEFAULT_SECTION}] + section_names

    if is_section_data:
        (
            today_progress,
            total_progress,
            total_completion_amount,
            today_completion_amount,
        ) = ProjectProgressHelper.process_section(elements=element_data, today_updated_elements=today_updated_elements)
    else:
        (
            today_progress,
            total_progress,
            total_completion_amount,
            today_completion_amount,
        ) = ProjectProgressHelper.process(elements=element_data, today_updated_elements=today_updated_elements)
    section_total_progress = defaultdict(int)
    for section_id, data in section_element_data.items():
        section_total_progress[section_id] = ProjectProgressHelper.get_total_progress_for_section(elements=data)[0]
        if section_total_progress[section_id] == 100 and section_is_99_percent.get(section_id, False):
            section_total_progress[section_id] = 99.99
    logger.info(
        "Progess Data",
        is_not_100_percent=is_not_100_percent,
        today_progress=today_progress,
        total_progress=total_progress,
        section_total_progress=section_total_progress,
        section_names=section_names,
    )
    if not is_not_100_percent:
        return ProgressReportTodayAndTotalProgressForConfigEntity(
            today_progress=today_progress,
            total_progress=decimal.Decimal(100),
            section_total_progress=dict(section_total_progress),
            section_names=section_names,
            today_progress_amount=decimal.Decimal(today_completion_amount),
            total_progress_amount=decimal.Decimal(total_completion_amount),
        )
    elif total_progress == 100 and is_not_100_percent:
        return ProgressReportTodayAndTotalProgressForConfigEntity(
            today_progress=decimal.Decimal(99.99 if today_progress == 100 else today_progress),
            total_progress=decimal.Decimal(99.99),
            section_total_progress=dict(section_total_progress),
            section_names=section_names,
            today_progress_amount=decimal.Decimal(today_completion_amount),
            total_progress_amount=decimal.Decimal(total_completion_amount),
        )

    return ProgressReportTodayAndTotalProgressForConfigEntity(
        today_progress=today_progress,
        total_progress=total_progress,
        section_total_progress=dict(section_total_progress),
        section_names=section_names,
        today_progress_amount=decimal.Decimal(today_completion_amount),
        total_progress_amount=decimal.Decimal(total_completion_amount),
    )


def compute_previous_day_progress(
    *, previous_day_progress_percentage: int, current_day_progress_percentage: int
) -> int:
    if previous_day_progress_percentage is None:
        previous_day_progress_percentage = 0
    elif previous_day_progress_percentage > current_day_progress_percentage:
        previous_day_progress_percentage = current_day_progress_percentage
    return previous_day_progress_percentage


def default_item_type_configuration_get() -> dict:
    return {
        "progress_config": ProgressReportItemTypeCache.get_front_end_mapping_from_item_type(item_type_id=None),
        "interval_step": Constants.DEFAULT_ITEM_INTERVAL,
        "update_method_options": [
            ItemTypeUpdateMethodChoices.PERCENTAGE.value,
            ItemTypeUpdateMethodChoices.QUANTITY.value,
        ],
    }


def elements_progress_update_method_change(
    data: list[ProgressReportElementUpdateMethodChangeData], user_id: int, project_id: int, org_id: int
):
    element_ids = [element_data.element_id for element_data in data]
    elements_dict: dict[int, BoqElement] = BoqElement.objects.filter(id__in=element_ids).in_bulk()
    config = ItemTypeProgressUpdateMethodCache.get(
        project_org_data=ProjectOrganizationEntity(
            project_id=project_id,
            organization_id=org_id,
        )
    )
    updated_elements = []
    errors = []
    for element_data in data:
        element = elements_dict.get(element_data.element_id)
        update_method = element.update_method if element.update_method else config.get(str(element.item_type_id))
        if update_method is None:
            update_method = ItemTypeUpdateMethodChoices.PERCENTAGE.value
        if (
            update_method in [ItemTypeUpdateMethodChoices.PERCENTAGE.value, ItemTypeUpdateMethodChoices.QUANTITY.value]
            and element_data.update_method == ItemTypeUpdateMethodChoices.MILESTONE
        ):
            errors.append("Element have update method percentage and quantity, cannot be changed to milestone")
        if (
            update_method
            not in [ItemTypeUpdateMethodChoices.PERCENTAGE.value, ItemTypeUpdateMethodChoices.QUANTITY.value]
            and element_data.update_method != ItemTypeUpdateMethodChoices.MILESTONE
        ):
            errors.append("Element have update method milestone cannot be changed to percentage and quantity")

        if element_data.update_method == ItemTypeUpdateMethodChoices.PERCENTAGE:
            element.progress_percentage_input = element.progress_percentage
        elif element_data.update_method == ItemTypeUpdateMethodChoices.QUANTITY:
            element.progress_quantity_input = (element.quantity * element.progress_percentage) / 100
        element.auto_entry = True
        element.update_method = element_data.update_method
        element.updated_by_id = user_id
        element.updated_at = timezone.now()
        updated_elements.append(element)
    if errors:
        raise ValidationError(errors)
    BoqElement.objects.bulk_update(
        updated_elements,
        fields=[
            "update_method",
            "updated_by_id",
            "updated_at",
            "progress_quantity_input",
            "progress_percentage_input",
            "auto_entry",
        ],
    )


def update_element_progress_quantity(
    project_id: int,
    element_id: int,
    org_id: int,
    progress_quantity: float,
    section_total_amount: float,
    quantity: float,
    rate: float,
    user_id: int,
):
    element = boq_elements_fetch(project_id=project_id).filter(id=element_id).first()
    update_method = (
        element.update_method
        if element.update_method
        else ItemTypeProgressUpdateMethodCache.get(
            project_org_data=ProjectOrganizationEntity(project_id=project_id, organization_id=org_id)
        ).get(str(element.item_type_id))
    )
    if update_method != ItemTypeUpdateMethodChoices.QUANTITY.value:
        raise ValidationError("Update method is not quantity")
    if element.element_status == BoqElementStatus.CANCELLED.value:
        raise ValidationError("Cancelled element cannot be updated")
    if quantity < progress_quantity:
        raise ValidationError("Progress quantity cannot be greater than quantity")
    if element.progress_quantity_input == progress_quantity:
        return 0
    last_updated_state = latest_boq_element_history_fetch(element_id=element.pk, item_type=element.item_type_id)
    progress_percentage = math.floor((progress_quantity / quantity) * 100 * TWO_DECIMAL_FACTOR) / TWO_DECIMAL_FACTOR
    is_locked = False
    if (
        last_updated_state
        and element.quantity == last_updated_state.quantity
        and element.update_method == last_updated_state.update_method
        and element.auto_entry is False
    ):
        is_locked = True
    if (
        last_updated_state
        and element.progress_quantity_input is not None
        and last_updated_state.progress_quantity_input
        and last_updated_state.progress_quantity_input > decimal.Decimal(str(progress_quantity))
        and is_locked
    ):
        raise ValidationError("Current progress quantity must be greater than previous progress quantity")
    if quantity == 0:
        return 0

    progress_name = f"{progress_quantity} {UnitOfMeasurementCache.get().get(str(element.uom))}"
    amount = quantity * rate
    old_amount = float(element.progress_percentage) * amount / 100
    new_amount = progress_percentage * amount / 100
    change_amount = new_amount - old_amount
    is_amount_negative = True if change_amount < 0 else False
    try:
        percent_change = (abs(change_amount) / section_total_amount) * 100
    except ZeroDivisionError:
        percent_change = 0
    element_progress_history_create(
        progress_percentage=progress_percentage,
        progress_name=progress_name,
        element_id=element.pk,
        item_type_id=element.item_type_id,
        created_by_id=user_id,
        quantity=quantity,
        create=True,
        progress_quantity_input=progress_quantity,
        progress_percentage_input=element.progress_percentage_input,
        update_method=element.update_method,
        uom=element.uom,
        client_rate=rate,
    )
    update_data = {
        "progress_percentage": progress_percentage,
        "status_updated_at": timezone.now(),
        "progress_quantity_input": progress_quantity,
        "progress_percentage_input": element.progress_percentage_input,
        "auto_entry": False,
    }
    boq_element_progress_percentage_update(boq_element=element, data=update_data, updated_by_id=user_id, org_id=org_id)
    return percent_change * (-1) if is_amount_negative else percent_change


def get_today_and_total_progress_for_header(
    wp_elements: WorkProgressElementQueryset,
    is_not_100_percent: bool,
    item_type_configs: dict = {},
) -> ProgressReportTodayAndTotalProgressForHeaderEntity:
    today_progress = decimal.Decimal(0)
    total_progress = decimal.Decimal(0)
    today_progress_amount = decimal.Decimal(0)
    total_progress_amount = decimal.Decimal(0)
    total_amount = decimal.Decimal(0)

    wp_elements = (
        wp_elements.select_related("section")
        .annotate_previous_day_progress()
        .annotate_saved_final_amount_with_tax()
        .annotate(total_amount=F("element_final_amount_with_tax"))
    )

    elements = wp_elements.values(
        "progress_percentage",
        "status_updated_at",
        "item_type_id",
        "total_amount",
        "element_status",
        "previous_day_progress",
        "section__name",
        "section_id",
        "client_rate",
        "update_method",
    )

    if not elements:
        return ProgressReportTodayAndTotalProgressForHeaderEntity(
            today_progress,
            total_progress,
            today_progress_amount,
            total_progress_amount,
            total_amount,
        )

    element_data = []
    today_updated_elements = []
    for element in elements:
        if (
            not element.get("update_method")
            or element.get("update_method") == ItemTypeUpdateMethodChoices.QUANTITY.value
        ):
            progress_percentage = element.get("progress_percentage")
        else:
            progress_percentage = ProgressReportItemTypeCache.get_latest_progress_percentage_if_config_changed(
                incoming_progress_percentage=element.get("progress_percentage"),
                item_type_id=element.get("item_type_id"),
                item_type_configs=item_type_configs,
            )

        data = ProgressReportProgressEntity(
            progress_percentage=progress_percentage,
            status_updated_at=element.get("status_updated_at"),
            previous_day_progress=element.get("previous_day_progress"),
            amount=float(element.get("total_amount", 0)),
        )

        if data.status_updated_at and get_local_time(data.status_updated_at).date() == get_current_local_time().date():
            today_updated_elements.append(data)

        element_data.append(data)
        total_amount += element.get("total_amount", 0)

    (
        today_progress,
        total_progress,
        total_completion_amount,
        today_completion_amount,
    ) = ProjectProgressHelper.process(elements=element_data, today_updated_elements=today_updated_elements)

    logger.info(
        "Process Data",
        is_not_100_percent=is_not_100_percent,
        today_progress=today_progress,
        total_progress=total_progress,
    )

    if not is_not_100_percent:
        return ProgressReportTodayAndTotalProgressForHeaderEntity(
            today_progress=today_progress,
            total_progress=100,
            today_progress_amount=today_completion_amount,
            total_progress_amount=total_completion_amount,
            total_amount=total_amount,
        )
    elif total_progress == 100 and is_not_100_percent:
        return ProgressReportTodayAndTotalProgressForHeaderEntity(
            today_progress=99.99 if today_progress == 100 else today_progress,
            total_progress=99.99,
            today_progress_amount=today_completion_amount,
            total_progress_amount=total_completion_amount,
            total_amount=total_amount,
        )

    return ProgressReportTodayAndTotalProgressForHeaderEntity(
        today_progress=today_progress,
        total_progress=total_progress,
        today_progress_amount=today_completion_amount,
        total_progress_amount=total_completion_amount,
        total_amount=total_amount,
    )
