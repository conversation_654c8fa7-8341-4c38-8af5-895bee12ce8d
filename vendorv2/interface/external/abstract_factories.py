import abc

from core.organization.domain.entities import OrganizationSectionData


class OrderToVendorAbstractFactory(abc.ABC):
    @abc.abstractmethod
    def get_service(self) -> "OrderToVendorAbstractService":
        pass


class OrderToVendorAbstractService(abc.ABC):
    @abc.abstractmethod
    def get_kyc_details(self) -> OrganizationSectionData:
        pass

    @abc.abstractmethod
    def get_bank_details(self) -> OrganizationSectionData:
        pass
