from core.entities import OrgUserEntity
from vendorv2.domain.factory import VendorFactory
from vendorv2.domain.services.vendor import VendorLinkedOrganizationService
from vendorv2.interface.external.abstract_factories import (
    OrderToVendorAbstractFactory,
    OrderToVendorAbstractService,
)


class OrderToVendorFactory(OrderToVendorAbstractFactory):
    def __init__(self, user_entity: OrgUserEntity, vendor_id: int, billing_entity_id: int | None = None):
        self.user_entity = user_entity
        self.vendor_id = vendor_id
        self.billing_entity_id = billing_entity_id

    def get_service(self):
        factory = VendorFactory(user_entity=self.user_entity)

        service = factory.get_linked_service(
            vendor_id=self.vendor_id,
            billing_entity_id=self.billing_entity_id,
        )

        return OrderToVendorService(linked_service=service)


class OrderToVendorService(OrderToVendorAbstractService):
    def __init__(self, linked_service: VendorLinkedOrganizationService):
        self.linked_service = linked_service

    def get_kyc_details(self):
        return self.linked_service.get_kyc_details()

    def get_bank_details(self):
        return self.linked_service.get_bank_details()
