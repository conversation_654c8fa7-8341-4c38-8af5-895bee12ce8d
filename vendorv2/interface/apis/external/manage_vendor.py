from django.core.exceptions import ObjectDoesNotExist
from drf_yasg.utils import swagger_auto_schema
from rest_framework.mixins import ListModelMixin
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND, HTTP_500_INTERNAL_SERVER_ERROR

from authorization.domain.constants import Permissions
from common.enums import RequestMethod
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.apis import ExternalOrgBaseV2Api
from core.helpers import OrgPermissionHelper
from core.organization.domain.entities import OrganizationSectionData
from vendorv2.data.selectors.selectors import (
    get_vendor_basic_details_v2,
    get_vendor_list,
    get_vendor_other_detail,
    mapping_get,
)
from vendorv2.data.selectors.vendor_selectors import vendor_users_get
from vendorv2.domain.factory import VendorFactory
from vendorv2.domain.services.vendor import VendorOnboardServiceV2
from vendorv2.interface.exceptions import VendorV2Exception
from vendorv2.interface.serializers import (
    VendorBasicDetailsV2Serializer,
    VendorListOutputV2Serializer,
    VendorOtherDetailSerializer,
    VendorUserSerializer,
)


class VendorV2Api(ExternalOrgBaseV2Api, ListModelMixin):
    serializer_class = VendorListOutputV2Serializer
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    def get_queryset(self):
        return get_vendor_list(organization_id=self.get_organization_id())

    @swagger_auto_schema(
        operation_summary="Vendors List",
        operation_id="external-vendors-list-v2",
        responses={HTTP_200_OK: VendorListOutputV2Serializer(many=True)},
    )
    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            if not queryset:
                self.set_response_message("Vendors not found.")
                return Response(status=HTTP_404_NOT_FOUND)
            self.set_response_message("Vendors fetched successfully.")
            return self.list(request, *args, **kwargs)
        except (ObjectDoesNotExist, VendorV2Exception):
            self.set_response_message("Vendors not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendors.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)


class VendorBasicDetailsV2Api(ExternalOrgBaseV2Api):
    serializer_class = VendorBasicDetailsV2Serializer
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorBasicDetailsV2Serializer()},
        operation_id="external-vendor-basic-details-v2",
        operation_summary="Get Vendor Basic Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        try:
            org_to_id = int(vendor_id)
            basic_details = get_vendor_basic_details_v2(organization_id=self.get_organization_id(), vendor_id=org_to_id)

            if not basic_details:
                self.set_response_message("Vendor not found.")
                return Response(status=HTTP_404_NOT_FOUND)

            serializer = self.serializer_class(basic_details)
        except (ObjectDoesNotExist, VendorV2Exception, VendorOnboardServiceV2.VendorNotFoundException):
            self.set_response_message("Vendor not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendor basic details.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)

        self.set_response_message("Vendor basic details fetched successfully.")
        return Response(serializer.data, status=HTTP_200_OK)


class VendorKycDetailsV2Api(ExternalOrgBaseV2Api):
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationSectionData)},
        operation_id="external-vendor-kyc-details-v2",
        operation_summary="Get Vendor KYC Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id)

        try:
            vendor_kyc_details = service.get_kyc_details()
        except (ObjectDoesNotExist, VendorV2Exception, VendorOnboardServiceV2.VendorNotFoundException):
            self.set_response_message("Vendor not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendor KYC details.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)
        if not vendor_kyc_details:
            self.set_response_message("Vendor KYC details not found.")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Vendor KYC details fetched successfully.")
        return Response(pydantic_dump(vendor_kyc_details), status=HTTP_200_OK)


class VendorBankDetailsV2Api(ExternalOrgBaseV2Api):
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationSectionData)},
        operation_id="external-vendor-bank-details-v2",
        operation_summary="Get Vendor Bank Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id)
        try:
            bank_details = service.get_bank_details()
        except (ObjectDoesNotExist, VendorV2Exception, VendorOnboardServiceV2.VendorNotFoundException):
            self.set_response_message("Vendor not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendor bank details.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)
        if not bank_details:
            self.set_response_message("Vendor bank details not found.")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Vendor Bank details fetched successfully.")
        return Response(pydantic_dump(bank_details), status=HTTP_200_OK)


class VendorOtherDetailsV2Api(ExternalOrgBaseV2Api):
    serializer_class = VendorOtherDetailSerializer
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorOtherDetailSerializer()},
        operation_id="external-vendor-other-details-v2",
        operation_summary="Get Vendor Other Details/Attachments",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        try:
            mapping = mapping_get(client_id=self.get_organization_id(), vendor_id=vendor_id)
            data = get_vendor_other_detail(mapping=mapping)
            if not data:
                self.set_response_message("Vendor other details not found.")
                return Response(status=HTTP_404_NOT_FOUND)
            is_editable = OrgPermissionHelper.has_permission(
                user=request.user, permission=Permissions.CAN_EDIT_MANAGE_VENDOR
            )
            setattr(data, "is_editable", is_editable)
            serializer = self.serializer_class(data)
        except (ObjectDoesNotExist, VendorV2Exception, VendorOnboardServiceV2.VendorNotFoundException):
            self.set_response_message("Vendor not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendor other details.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)

        self.set_response_message("Vendor other details fetched successfully.")
        return Response(serializer.data, status=HTTP_200_OK)


class VendorUserListV2Api(ExternalOrgBaseV2Api, ListModelMixin):
    serializer_class = VendorUserSerializer
    REQUIRED_METHOD_PERMISSIONS = {RequestMethod.GET.value: [Permissions.CAN_ACCESS_MANAGE_VENDOR]}

    def get_queryset(self):
        mapping = mapping_get(client_id=self.get_organization_id(), vendor_id=self.kwargs["vendor_id"])
        return vendor_users_get(mapping.id)

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorUserSerializer(many=True)},
        operation_id="external-vendor-user-list-v2",
        operation_summary="Get Vendor Users",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        try:
            queryset = self.get_queryset()
        except (ObjectDoesNotExist, VendorV2Exception, VendorOnboardServiceV2.VendorNotFoundException):
            self.set_response_message("Vendor not found.")
            return Response(status=HTTP_404_NOT_FOUND)
        except Exception:
            self.set_response_message("An unexpected error occurred while fetching vendor users.")
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)

        if not queryset:
            self.set_response_message("Vendor users not found.")
            return Response(status=HTTP_404_NOT_FOUND)

        self.set_response_message("Vendor users fetched successfully.")
        return self.list(request, *args, **kwargs)
