from typing import List

from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import IntegrityError, transaction
from django.db.models import Q
from django.http import Http404
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.mixins import ListModelMixin
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_202_ACCEPTED, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.apis import Base<PERSON><PERSON>
from common.choices import StateChoices, VendorStatusChoices
from common.regex import RegularExpression as Regex
from common.serializers import BaseSerializer, HashIdField
from core.apis import Org<PERSON>ase<PERSON>pi
from core.exceptions import OrganizationException, OrgGSTNumberAndStateUniqueException
from core.helpers import OrgPermissionHelper
from core.models import Organization
from core.serializers import OrganizationGstModelSerializer, OtherDocumentDataSerializer
from vendorv2.data.filters import (
    CustomOrderFilter,
    VendorDetailSearchFilter,
    VendorFilters,
)
from vendorv2.data.selectors.selectors import (
    bank_detail_get_all,
    fetch_all_vendor_mappings,
    fetch_vendor_mapping,
    get_vendor_from_org_id,
    get_vendor_list,
    get_vendor_onboard_organization_config,
    get_vendor_org_tags,
    get_vendor_other_detail,
    get_vendor_using_pan,
)
from vendorv2.data.selectors.vendor_selectors import fetch_vendor_gst_details
from vendorv2.domain.entities import VendorBankListUpdateData, VendorOnboardInBulkData
from vendorv2.domain.factory import VendorFactory
from vendorv2.domain.services.services import (
    ManageVendorOrgService,
    ManageVendorOtherDocumentService,
    VendorServiceV2,
    check_if_vendor_already_exists,
    get_vendor_kyc_filled,
    mark_kyc_filled,
    submit_kyc_details,
    vendor_basic_and_kyc_detail_with_org_id,
    vendor_basic_detail_and_kyc_detail,
    vendor_search_config_data,
)
from vendorv2.excel.parse import VendorBulkCreateExcelParser
from vendorv2.interface.base_api import ManageVendorBaseApi
from vendorv2.interface.serializers import (
    BankDetailsInputSerializer,
    FileSerializer,
    OrganizationAddressInputSerializer,
    OrganizationDataUpdateSerializer,
    VendorBankListUpdateDataSerializer,
    VendorBankSerializer,
    VendorBasicDetailUpdateDataSerializer,
    VendorCreateDataSerializer,
    VendorDataViaPanSerializer,
    VendorGSTSerializer,
    VendorKycDetailAddSerializer,
    VendorKYCUpdateDataSerializer,
    VendorModelSerializer,
    VendorMSMESerializer,
    VendorOnboardConfigDataSerializer,
    VendorOrgTagModelSerializer,
    VendorOtherDetailDocumentOutputSerialzer,
    VendorOtherDetailSerializer,
    VendorPANSerializer,
    VendorSerializer,
    VendorSerializerV2,
    VendorTagListUpdateDataSerializer,
)


class VendorListApi(BaseApi):
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorSerializer()},
        operation_id="vendor_v2_vendor_list",
        operation_summary="Vendor V2 Vendor List API with POC details",
    )
    def get(self, request, *args, **kwargs):
        data = fetch_all_vendor_mappings(organization_id=self.get_organization_id())
        return Response(VendorSerializer(data, many=True).data, status=HTTP_200_OK)


class VendorListSearchConfigApi(ManageVendorBaseApi):
    pagination_class = None

    class OutputSerializer(BaseSerializer):
        class VendorOrgTagSerializer(VendorOrgTagModelSerializer):
            class Meta(VendorOrgTagModelSerializer.Meta):
                fields = ("id", "name")
                ref_name = "VendorListSearchConfigApiOutputVendorOrgTagSerializer"

        vendor_tag_list = VendorOrgTagSerializer(many=True)
        invite_status_list = serializers.ListField()
        vendor_status_list = serializers.ListField()

        class Meta:
            ref_name = "VendorListSearchConfigApiOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_list_search_config",
        operation_summary="Vendor Search Config API",
    )
    def get(self, request, *args, **kwargs):
        data = vendor_search_config_data(organization_id=self.get_organization_id())
        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class VendorListV2Api(ManageVendorBaseApi, ListModelMixin):
    ordering_fields = ["name", "pan_number", "poc_name", "code"]
    serializer_class = VendorSerializerV2
    filter_backends = [
        CustomOrderFilter,
        DjangoFilterBackend,
        VendorDetailSearchFilter,
    ]
    filterset_class = VendorFilters

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorSerializerV2()},
        operation_id="vendor_v3_vendor_list",
        operation_summary="Vendor V2 Vendor List API with POC details",
    )
    def get_queryset(self):
        return get_vendor_list(organization_id=self.get_organization_id())

    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class VendorDetailsApi(ManageVendorBaseApi):
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="vendor_v2_vendor_detail",
        operation_summary="Vendor V2 Vendor Detail API",
        deprecated=True,
    )
    def get(self, request, vendor_id, *args, **kwargs):
        data = fetch_vendor_mapping(mapping_id=self.get_mapping().id)
        return Response(VendorSerializer(data).data, status=HTTP_200_OK)


class VendorBasicDetailApi(ManageVendorBaseApi):
    pagination_class = None

    class OutputSerializer(VendorDataViaPanSerializer):
        class Meta(VendorDataViaPanSerializer.Meta):
            ref_name = "VendorBasicDetailOutputSerializer"
            fields = (
                "organization_id",
                "company_name",
                "company_addresses",
                "company_business_card",
                "pan",
                "aadhar",
                "msme",
                "gst",
                "is_editable",
            )

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_basic_detail",
        operation_summary="Vendor Basic Detail",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        data = vendor_basic_and_kyc_detail_with_org_id(
            mapping=self.get_mapping(), org_id=self.get_organization_id(), user=request.user
        )
        return Response(self.OutputSerializer(data).data, status=HTTP_200_OK)


class VendorOrganizationGSTDetails(OrgBaseApi):
    REQUIRED_PERMISSIONS = []

    class OutputSerializer(OrganizationGstModelSerializer):
        state = serializers.SerializerMethodField()
        number = serializers.CharField(source="gst_number")

        def get_state(self, obj):
            return (
                {"id": obj.gst_state, "name": dict(StateChoices.choices).get(int(obj.gst_state))}
                if obj.gst_state
                else None
            )

        class Meta(OrganizationGstModelSerializer.Meta):
            fields = ("id", "number", "state")
            ref_name = "VendorOrganizationGSTDetailsOutputSerializer"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_organization_gst_details",
        operation_summary="Vendor Organization GST Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        data = fetch_vendor_gst_details(vendor_id=vendor_id)
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class VendorOtherDetailApi(ManageVendorBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorOtherDetailSerializer()},
        operation_id="vendor_other_detail",
        operation_summary="Vendor Other Detail",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        data = get_vendor_other_detail(mapping=self.get_mapping())
        is_editable = OrgPermissionHelper.has_permission(
            user=request.user, permission=Permissions.CAN_EDIT_MANAGE_VENDOR
        )
        setattr(data, "is_editable", is_editable)
        return Response(VendorOtherDetailSerializer(data).data, status=HTTP_200_OK)


class VendorBankDetailListApi(BaseApi):
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorBankSerializer()},
        operation_id="vendor_bank_detail_list_api",
        operation_summary="Vendor bank detail list api",
    )
    def get(self, request, *args, **kwargs):
        data = bank_detail_get_all(organization_id=self.get_organization_id())
        return Response(VendorBankSerializer(data, many=True).data, status=HTTP_200_OK)


class VendorTagListApi(ManageVendorBaseApi):
    class OutputSerializer(VendorOrgTagModelSerializer):
        class Meta(VendorOrgTagModelSerializer.Meta):
            ref_name = "VendorTagListOutputSerializer"
            fields = ["name", "id"]

    @swagger_auto_schema(
        responses={HTTP_200_OK: ""},
        operation_id="vendor_tag_list",
        operation_summary="Vendor Tag List",
    )
    def get(self, request, *args, **kwargs):
        data = get_vendor_org_tags(organization_id=self.get_organization_id())
        return Response(self.OutputSerializer(data, many=True).data, status=HTTP_200_OK)


class VendorDetailFetchFromPANApi(ManageVendorBaseApi):
    class FilterSerializer(BaseSerializer):
        pan = serializers.RegexField(regex=Regex.PAN_NUMBER, required=True)

        class Meta:
            ref_name = "VendorDetailFetchFromPANFilterSerializer"

    filter_serializer_class = FilterSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorDataViaPanSerializer()},
        query_serializer=FilterSerializer(),
        operation_id="vendor_detail_fetch_from_pan",
        operation_summary="Vendor Detail Fetch From PAN",
    )
    def get(self, request, *args, **kwargs):
        pan_number = self.validate_filter_data().get("pan")
        try:
            vendor_data = vendor_basic_detail_and_kyc_detail(
                filter=Q(pan_number=pan_number),
                org_id=self.get_organization_id(),
            )
        except ObjectDoesNotExist:
            vendor_data = None
        if vendor_data:
            if vendor_data.id == self.get_organization_id():
                raise ValidationError("Vendor's organization is same as the organization you are logged in")

            check_if_vendor_already_exists(organization_id=self.get_organization_id(), vendor_id=vendor_data.pk)
        else:
            data = {
                "organization_id": None,
                "company_name": "",
                "company_addresses": [],
                "company_business_card": {"id": "", "file_name": ""},
                "pan": {"id": "", "number": "", "file_name": ""},
                "aadhar": {"files": [], "number": ""},
                "msme": {"id": "", "number": "", "file_name": ""},
                "gst": [],
                "is_editable": True,
            }
            return Response(data, status=HTTP_200_OK)
        return Response(VendorDataViaPanSerializer(vendor_data).data, status=HTTP_200_OK)


class VendorCreateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    class OutputSerializer(BaseSerializer):
        id = serializers.CharField()

        class Meta:
            ref_name = "VendorCreateOutputSerializer"

    input_serializer_class = VendorCreateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorCreateDataSerializer()},
        request_body=VendorCreateDataSerializer(),
        operation_id="vendor_create",
        operation_summary="Vendor create",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        vendor_data = self.validate_input_data()
        data = VendorServiceV2(user=request.user, organization_id=self.get_organization_id()).onboard_vendor(
            vendor_data=vendor_data, user=request.user
        )
        return Response(self.OutputSerializer(data).data, status=HTTP_201_CREATED)


class VendorBulkCreateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    class InputSerializer(BaseSerializer):
        file = serializers.FileField()
        country_id = serializers.IntegerField()

        class Meta:
            ref_name = "VendorBulkCreateInputSerializer"

    class OutputSerializer(BaseSerializer):
        organization_name = serializers.CharField()
        status = serializers.CharField()

        class Meta:
            ref_name = "VendorBulkCreateApiOutputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorCreateDataSerializer()},
        request_body=InputSerializer(),
        operation_id="vendor_bulk_create",
        operation_summary="Vendor bulk create",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = self.validate_input_data()
        file: InMemoryUploadedFile = data.get("file")
        country_id = data.get("country_id")
        user_entity = self.get_org_user_entity()

        excel_parser = VendorBulkCreateExcelParser(file=file, country_id=country_id)

        try:
            excel_data = excel_parser.parse()
        except VendorBulkCreateExcelParser.InvalidFileFormatException:
            return Response(
                {
                    "error_code": 4001,
                    "message": "Invalid file format supplied, please download correct file format",
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except VendorBulkCreateExcelParser.InvalidDataException:
            error_file, total_errors, total_success = excel_parser.get_error_file()
            return Response(
                {
                    "error_code": 4002,
                    "message": f"Errors found in {total_errors} rows.",
                    "description": f"{total_success} Vendor successfully passed for upload.",
                    "error_file": error_file,
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except VendorBulkCreateExcelParser.ExcelParserException as e:
            return Response(
                {"error_code": 4001, "message": e.message},
                status=HTTP_400_BAD_REQUEST,
            )

        vendors_data: List[VendorOnboardInBulkData] = [
            VendorOnboardInBulkData(
                uid=data.get("uid"),
                organization_name=data.get("organization_name", ""),
                tax_number=data.get("tax_number"),
                user_name=data.get("user_name"),
                phone_number=data.get("phone_number"),
                email=data.get("email"),
                primary_account_holder_name=data.get("primary_account_holder_name"),
                primary_account_number=data.get("primary_account_number"),
                primary_bank_name=data.get("primary_bank_name"),
                primary_bank_code=data.get("primary_bank_code"),
                primary_iban_number=data.get("primary_iban_number"),
                primary_bank_branch_name=data.get("primary_bank_branch_name"),
                secondary_account_holder_name=data.get("secondary_account_holder_name"),
                secondary_bank_name=data.get("secondary_bank_name"),
                secondary_bank_code=data.get("secondary_bank_code"),
                secondary_account_number=data.get("secondary_account_number"),
                vendor_tags=data.get("vendor_tags"),
                base_address_line_1=data.get("base_address_line_1"),
                base_address_line_2=data.get("base_address_line_2"),
                base_country_name=data.get("base_country_name"),
                base_state_name=data.get("base_state_name"),
                base_city_name=data.get("base_city_name"),
                base_zip_code=data.get("base_zip_code"),
                additional_address_line_1=data.get("additional_address_line_1"),
                additional_address_line_2=data.get("additional_address_line_2"),
                additional_country_name=data.get("additional_country_name"),
                additional_state_name=data.get("additional_state_name"),
                additional_city_name=data.get("additional_city_name"),
                additional_zip_code=data.get("additional_zip_code"),
                aadhar_number=data.get("aadhar_number"),
                msme_id=data.get("msme_id"),
            )
            for data in excel_data
        ]

        factory = VendorFactory(user_entity=user_entity)
        onboard_service = factory.get_onboard_service()

        status_list = onboard_service.onboard_vendor_in_bulk(
            vendors_data=vendors_data,
            country_id=country_id,
        )

        return Response(
            self.OutputSerializer(
                [
                    {
                        "organization_name": status[0],
                        "status": status[1],
                    }
                    for status in status_list
                ],
                many=True,
            ).data,
            status=HTTP_201_CREATED,
        )


class VendorKycDetailAddApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorKycDetailAddSerializer

    @swagger_auto_schema(
        responses={HTTP_202_ACCEPTED: ""},
        request_body=VendorKycDetailAddSerializer(),
        operation_id="vendor_create",
        operation_summary="Vendor create",
    )
    @transaction.atomic
    def post(self, request, vendor_id, *args, **kwargs):
        vendor_data = self.validate_input_data()
        try:
            VendorServiceV2.kyc_detail_add(
                created_by_id=self.get_user_id(),
                data=vendor_data,
                vendor_id=vendor_id,
                client_id=self.get_organization_id(),
            )
        except OrgGSTNumberAndStateUniqueException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_202_ACCEPTED)


class VendorDetailUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorBasicDetailUpdateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=VendorBasicDetailUpdateDataSerializer(),
        operation_id="vendor_v2_vendor_detail_update_api",
        operation_summary="Vendor V2 Vendor Detail Update API",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        ManageVendorOrgService.basic_detail_update(
            organization=self.get_mapping().org_to, user_id=request.user.pk, data=self.validate_input_data()
        )
        return Response(status=HTTP_201_CREATED)


class VendorKYCUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorKYCUpdateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=VendorKYCUpdateDataSerializer(),
        operation_id="vendor_v2_vendor_kyc_update_api",
        operation_summary="Vendor V2 Vendor KYC Update API",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        data = self.validate_input_data()
        self.get_mapping()
        try:
            ManageVendorOrgService.kyc_detail_update(org_id=vendor_id, user_id=request.user.pk, data=data)
        except OrgGSTNumberAndStateUniqueException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except OrganizationException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        return Response(status=HTTP_201_CREATED)


class VendorTagUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorTagListUpdateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=VendorTagListUpdateDataSerializer(),
        operation_id="vendor_v2_vendor_tag_update_api",
        operation_summary="Vendor V2 Vendor Tag Update API",
    )
    def put(self, request, vendor_id, *args, **kwargs):
        self.get_mapping()
        ManageVendorOrgService.tags_update(
            cv_mapping_id=self.mapping.id, user_id=request.user.pk, data=self.validate_input_data()
        )
        return Response(status=HTTP_201_CREATED)


class VendorBankDetailUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorBankListUpdateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=VendorBankListUpdateDataSerializer(),
        operation_id="vendor_v2_vendor_bank_update_api",
        operation_summary="Vendor V2 Vendor Bank Update API",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        mapping = self.get_mapping()
        ManageVendorOrgService.bank_detail_create_and_update(
            client_id=mapping.org_from_id,
            vendor_id=mapping.org_to_id,
            user_id=request.user.pk,
            data=self.validate_input_data(),
        )
        return Response(status=HTTP_201_CREATED)


class OrganizationDetailUpdateApi(BaseApi):
    input_serializer_class = OrganizationDataUpdateSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=OrganizationDataUpdateSerializer(),
        operation_id="org_detail_update_api",
        operation_summary="Organization detail update API",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        data = self.validate_input_data()
        organization = Organization.objects.get(id=self.get_organization_id())
        try:
            ManageVendorOrgService.basic_detail_update(organization=organization, user_id=request.user.pk, data=data)
            ManageVendorOrgService.kyc_detail_update(
                org_id=self.get_organization_id(), user_id=request.user.pk, data=data
            )
            ManageVendorOrgService.bank_detail_create_and_update(
                client_id=None,
                vendor_id=self.get_organization_id(),
                user_id=request.user.pk,
                data=VendorBankListUpdateData(data=data.bank_details),
            )
        except ValidationError as e:
            self.set_response_message("".join(e.messages))
            raise ValidationError(e)
        return Response(status=HTTP_201_CREATED)


class VendorOtherDetailDocumentCreateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = OtherDocumentDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorOtherDetailDocumentOutputSerialzer()},
        request_body=OtherDocumentDataSerializer(),
        operation_id="vendor_v2_vendor_other_document_create_api",
        operation_summary="Vendor V2 Vendor Other Document Create API",
    )
    @transaction.atomic
    def post(self, request, vendor_id, *args, **kwargs):
        self.get_mapping()
        other_doc = ManageVendorOtherDocumentService.other_doc_create(
            data=self.validate_input_data(), user_id=request.user.pk, cv_mapping_id=self.mapping.id
        )
        return Response(VendorOtherDetailDocumentOutputSerialzer(other_doc).data, status=HTTP_201_CREATED)


class VendorOtherDetailDocumentUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = OtherDocumentDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: VendorOtherDetailDocumentOutputSerialzer()},
        request_body=OtherDocumentDataSerializer(),
        operation_id="vendor_v2_vendor_other_document_update_api",
        operation_summary="Vendor V2 Vendor Other Document Update API",
    )
    @transaction.atomic
    def put(self, request, vendor_id, doc_id, *args, **kwargs):
        self.get_mapping()
        other_doc = ManageVendorOtherDocumentService.other_doc_update(
            data=self.validate_input_data(), user_id=request.user.pk, doc_id=doc_id
        )
        return Response(VendorOtherDetailDocumentOutputSerialzer(other_doc).data, status=HTTP_201_CREATED)


class VendorOtherDetailDocumentDeleteApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_EDIT_MANAGE_VENDOR]

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        operation_id="vendor_v2_vendor_other_document_delete_api",
        operation_summary="Vendor V2 Vendor Other Document Update API",
    )
    @transaction.atomic
    def delete(self, request, vendor_id, doc_id, *args, **kwargs):
        self.get_mapping()
        ManageVendorOtherDocumentService.other_doc_delete(doc_id=doc_id)
        return Response(status=HTTP_201_CREATED)


class VendorStatusUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = ManageVendorBaseApi.REQUIRED_PERMISSIONS + [Permissions.CAN_CHANGE_VENDOR_STATUS]

    class InputSerializer(BaseSerializer):
        status = serializers.ChoiceField(choices=VendorStatusChoices.choices)
        blacklist_reason = serializers.CharField(required=False)

        def validate(self, attrs):
            if attrs.get("status") == VendorStatusChoices.BLACKLISTED.value and not attrs.get("blacklist_reason"):
                raise ValidationError("Blacklist reason is required")
            elif attrs.get("status") == VendorStatusChoices.ONBOARDED.value:
                raise ValidationError("Invalid status")

            return attrs

        class Meta:
            ref_name = "VendorMarkActiveInactiveInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=InputSerializer(),
        operation_id="vendor_v2_vendor_status_update_api",
        operation_summary="Vendor V2 Vendor status update API",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        data = self.validate_input_data()
        self.get_mapping()
        ManageVendorOrgService.vendor_status_change(
            mapping=self.mapping,
            user_id=request.user.pk,
            status=data.get("status"),
            blacklist_reason=data.get("blacklist_reason"),
        )
        vendor_data = get_vendor_list(organization_id=self.get_organization_id()).get(id=self.mapping.id)

        return Response(data=VendorSerializerV2(vendor_data).data, status=HTTP_201_CREATED)


class VendorOrganizationGetUsingPANApi(BaseApi):
    class FilterSerializer(BaseSerializer):
        pan_number = serializers.RegexField(regex=Regex.PAN_NUMBER, error_messages={"invalid": "PAN Number Invalid"})

        class Meta:
            ref_name = "VendorOrganizationGetUsingPANFilterSerializer"

    class OutputSerializer(VendorModelSerializer):
        class Meta(VendorModelSerializer.Meta):
            fields = ("id", "organization", "pan_number")
            ref_name = "VendorOrganizationGetUsingPANOutputSerializer"

    pagination_class = None

    @swagger_auto_schema(
        query_serializer=FilterSerializer(),
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_v2_vendor_fetch_using_pan_api",
        operation_summary="Vendor V2 Get Vendor Detail Using PAN",
    )
    def get(self, request, *args, **kwargs):
        data = self.validate_filter_data()
        try:
            vendor = get_vendor_using_pan(pan_number=data.get("pan_number"))
            check_if_vendor_already_exists(organization_id=self.get_organization_id(), vendor_id=vendor.pk)
        except Http404:
            vendor = None

        return Response(self.OutputSerializer(vendor).data, status=HTTP_200_OK)


class VendorKycCreateApi(BaseApi):
    class InputSerializer(BaseSerializer):
        organization_name = serializers.CharField(max_length=100)
        business_card = FileSerializer()
        organization_address = OrganizationAddressInputSerializer(many=True)
        pan = VendorPANSerializer()
        gst = VendorGSTSerializer(many=True)
        msme = VendorMSMESerializer()
        bank_details = BankDetailsInputSerializer(many=True)

        class Meta:
            ref_name = "VendorKycInput"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_201_CREATED: "KYC Submitted"},
        operation_id="vendor_kyc_input",
        operation_summary="vendor_kyc_input",
    )
    @transaction.atomic
    def post(self, request):
        organization_id = self.get_organization_id()
        data = self.validate_input_data()
        vendor = None
        try:
            vendor = get_vendor_from_org_id(organization_id=organization_id)
        except ObjectDoesNotExist:
            raise ValidationError("Vendor does not exist")
        try:
            submit_kyc_details(
                data,
                user_id=request.user.pk,
                organization_id=organization_id,
                vendor_id=vendor.pk,
            )
            mark_kyc_filled(organization_id=organization_id, vendor=vendor, user_id=request.user.pk)
        except IntegrityError as e:
            if "msme" in e.message:
                raise ValidationError("MSME ID already exists")
            if "gst" in e.message:
                raise ValidationError("GST NUMBER already exists")
        except OrgGSTNumberAndStateUniqueException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        return Response({"message": "KYC Submitted"}, status=HTTP_201_CREATED)


class VendorIsKycFilled(BaseApi):
    class OutputSerializer(BaseSerializer):
        is_kyc_filled = serializers.BooleanField(default=False)
        is_vendor = serializers.BooleanField(default=False)

        class Meta:
            ref_name = "vendor_is_kyc_filled"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_is_kyc_filled",
        operation_summary="vendor_is_kyc_filled",
    )
    def get(self, request):
        return Response(
            self.OutputSerializer(get_vendor_kyc_filled(organization_id=self.get_organization_id())).data,
            status=HTTP_200_OK,
        )


class VendorRemoveApi(BaseApi):
    class InputSerializer(BaseSerializer):
        organization_id = HashIdField()

        class Meta:
            ref_name = "vendor_delete"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: "Vendor deleted"},
        operation_id="vendor_delete",
        operation_summary="vendor_delete",
    )
    @transaction.atomic
    def delete(self, request):
        raise ValidationError("Not Allowed")


class VendorOnboardConfigFetchApi(BaseApi):
    class OutputSerializer(VendorOnboardConfigDataSerializer):
        class Meta(VendorOnboardConfigDataSerializer.Meta):
            fields = ("is_pan_required", "is_bank_detail_required", "uid_name", "bank_section_name")
            ref_name = "org_vendor_onboard_config_fetch"

    @swagger_auto_schema(
        responses={HTTP_200_OK: OutputSerializer()},
        operation_id="vendor_onboard_config_fetch",
        operation_summary="vendor_onboard_config_fetch",
    )
    def get(self, request):
        return Response(
            self.OutputSerializer(get_vendor_onboard_organization_config(org_id=self.get_organization_id())).data,
            status=HTTP_200_OK,
        )


class VendorOnboardConfigUpdateApi(BaseApi):
    class InputSerializer(VendorOnboardConfigDataSerializer):
        class Meta(VendorOnboardConfigDataSerializer.Meta):
            ref_name = "org_vendor_onboard_config_update"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: "Config Updated"},
        operation_id="update_vendor_onboard_config",
        operation_summary="update_vendor_onboard_config",
    )
    @transaction.atomic
    def put(self, request):
        data = self.validate_input_data()
        ManageVendorOrgService.update_vendor_onboard_config(
            org_id=self.get_organization_id(),
            user_id=self.get_user_id(),
            data=data,
        )
        return Response(status=HTTP_200_OK)
