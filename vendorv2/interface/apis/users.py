from django.db import transaction
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_202_ACCEPTED

from common.serializers import BaseSerializer, HashIdField, PhoneNumberSerializer
from core.organization.enums import UserStatus
from core.organization.exceptions import PocUserException
from core.organization.serializers import UserAddInputSerializer
from vendorv2.data.selectors.vendor_selectors import vendor_users_get
from vendorv2.domain.services.user import VendorUserService
from vendorv2.interface.base_api import ManageVendorBase<PERSON><PERSON>
from vendorv2.interface.serializers import VendorUserSerializer


class VendorUserListApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = VendorUserSerializer

    class FilterSerializer(BaseSerializer):
        email_available = serializers.BooleanField(default=False, allow_null=True, required=False)

        class Meta:
            ref_name = "VendorUserListApiFilterSerializer"

    def get_queryset(self):
        data = self.validate_filter_data()
        condition = Q()
        if data.get("email_available") and data.get("email_available") is not None:
            condition = Q(
                email__isnull=not data.get("email_available"),
                has_left_org=False,
            )
        users = vendor_users_get(mapping_id=self.get_mapping().id).filter(condition).order_by("created_at")
        return users

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorUserSerializer(many=True)},
        operation_id="vendor_onboard_user_list",
        operation_summary="List all User POC's of a vendor.",
    )
    @transaction.atomic
    def get(self, request, vendor_id, *args, **kwargs):
        return Response(self.serializer_class(self.get_queryset(), many=True).data, status=HTTP_200_OK)


class VendorUserAddAndInviteApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = VendorUserSerializer
    input_serializer_class = UserAddInputSerializer

    class FilterSerializer(BaseSerializer):
        create_user_only = serializers.BooleanField(default=False, allow_null=True, required=False)

        class Meta:
            ref_name = "VendorUserAddAndInviteApiFilterSerializer"

    @swagger_auto_schema(
        request_body=UserAddInputSerializer(),
        responses={HTTP_200_OK: VendorUserSerializer()},
        operation_id="vendor_onboard_add_and_invite_api",
        operation_summary="Add and Invite Vendor User Api",
    )
    @transaction.atomic
    def post(self, request, vendor_id, *args, **kwargs):
        data = self.validate_input_data()
        filter_data = self.validate_filter_data()
        self.get_mapping()
        try:
            user = VendorUserService().add_and_invite(
                created_by_id=self.get_user_id(),
                mapping=self.mapping,
                user=data,
                organization_id=vendor_id,
                create_user_only=filter_data.get("create_user_only", False),
            )
            self.set_response_message("User added Successfully")
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User added Successfully")
        user = vendor_users_get(mapping_id=self.mapping.id).filter(id=user.id).first()
        return Response(self.serializer_class(user).data, status=HTTP_201_CREATED)


class VendorUserUpdateAndInviteApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = VendorUserSerializer
    input_serializer_class = UserAddInputSerializer

    class FilterSerializer(BaseSerializer):
        create_user_only = serializers.BooleanField(default=False, allow_null=True, required=False)

        class Meta:
            ref_name = "VendorUserAddAndInviteApiFilterSerializer"

    @swagger_auto_schema(
        request_body=UserAddInputSerializer(),
        responses={HTTP_200_OK: VendorUserSerializer()},
        operation_id="vendor_onboard_update_and_invite_api",
        operation_summary="Vendor user update and invite ",
    )
    @transaction.atomic
    def put(self, request, vendor_id, poc_id, *args, **kwargs):
        data = self.validate_input_data()
        filter_data = self.validate_filter_data()
        self.get_mapping()
        try:
            user = VendorUserService().update_and_invite(
                poc_user_id=poc_id,
                updated_by_id=self.get_user_id(),
                mapping=self.mapping,
                poc_user=data,
                organization_id=vendor_id,
                create_user_only=filter_data.get("create_user_only", False),
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User updated successfully")
        user = vendor_users_get(mapping_id=self.mapping.id).filter(id=user.id).first()
        return Response(self.serializer_class(user).data, status=HTTP_202_ACCEPTED)


class VendorUserInviteAgainApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        operation_id="vendor_onboard_invite_again_api",
        operation_summary="User onboard invite again api",
    )
    @transaction.atomic
    def put(self, request, vendor_id, poc_id, *args, **kwargs):
        self.get_mapping()
        poc_user = vendor_users_get(mapping_id=self.mapping).filter(id=poc_id).first()
        if poc_user.active_status == UserStatus.LEFT.value:
            self.set_response_message("Cannot Invite")
            raise PocUserException("Now Allowed")
        try:
            VendorUserService().invite_again(
                poc_user_id=poc_id,
                invited_by_id=self.get_user_id(),
                mapping=self.mapping,
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message("User updated successfully")
        return Response(status=HTTP_200_OK)


class VendorUserRemoveApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None
    serializer_class = VendorUserSerializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorUserSerializer(many=True)},
        operation_id="vendor_onboard_vendor_user_remove_api",
        operation_summary="Vendor User Remove api",
    )
    @transaction.atomic
    def delete(self, request, vendor_id, poc_id, *args, **kwargs):
        self.get_mapping()
        try:
            VendorUserService().remove(
                poc_user_id=poc_id,
                deleted_by_id=self.get_user_id(),
                mapping_id=self.mapping.id,
            )
        except PocUserException as e:
            self.set_response_message(e.message)
            raise e
        self.set_response_message(message="User removed successfully.")
        return Response(status=HTTP_200_OK)


class VendorUserSetPrimaryApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []
    pagination_class = None

    @swagger_auto_schema(
        responses={HTTP_200_OK: VendorUserSerializer(many=True)},
        operation_id="vendor_onboard_vendor_user_set_primary_api",
        operation_summary="Vendor User Make Primary api",
    )
    @transaction.atomic
    def put(self, request, vendor_id, poc_id, *args, **kwargs):
        self.get_mapping()
        poc_user = vendor_users_get(mapping_id=self.mapping).filter(id=poc_id).first()
        if poc_user.active_status == UserStatus.LEFT.value:
            self.set_response_message("Cannot set primary")
            raise PocUserException("Now Allowed")

        VendorUserService().set_primary(
            poc_user_id=poc_id,
            mapping_id=self.mapping.id,
        )
        self.set_response_message("User marked as primary.")
        return Response(status=HTTP_200_OK)


class VendorPOCVerifyApi(ManageVendorBaseApi):
    class OutputSerializer(BaseSerializer):
        name = serializers.CharField(required=True, allow_blank=True)
        phone_number = PhoneNumberSerializer()

        class Meta:
            ref_name = "VendorPOCVerifyOutputSerializer"

    class InputSerializer(BaseSerializer):
        email = serializers.EmailField(required=True)
        poc_id = HashIdField(required=False)

        class Meta:
            ref_name = "VendorPOCVerifyEmailSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=InputSerializer(),
        operation_id="vendor_poc_verify",
        operation_summary="Vendor POC Verify",
    )
    def post(self, request, vendor_id, *args, **kwargs):
        try:
            data = self.validate_input_data()
        except ValidationError as e:
            if "email" in e.detail:
                self.set_response_message(message=" ".join(e.detail["email"]))
            raise e
        try:
            data = VendorUserService().verify_email(
                email=data.get("email"),
                organization_id=vendor_id,
                mapping_id=self.get_mapping().id,
                poc_id=data.get("poc_id"),
            )
        except VendorUserService.PocUserServiceException as e:
            self.set_response_message(e.message)
            raise e

        if data:
            response_data = self.OutputSerializer(data).data
        else:
            response_data = {"name": None, "phone_number": None}
        return Response(response_data, status=HTTP_201_CREATED)


class VendorRequestDocumentFromPocApi(ManageVendorBaseApi):
    class InputSerializer(BaseSerializer):
        poc_id = HashIdField()

        class Meta:
            ref_name = "VendorRequestDocumentInputSerializer"

    input_serializer_class = InputSerializer

    @swagger_auto_schema(
        request_body=InputSerializer(),
        responses={HTTP_200_OK: "Vendor document request sent"},
        operation_id="vendor_request_document_api",
        operation_summary="Vendor Request Document API",
    )
    @transaction.atomic
    def put(self, request, *args, **kwargs):
        self.get_mapping()
        data = self.validate_input_data()
        try:
            VendorUserService().request_document(
                poc_id=data.get("poc_id"),
                mapping=self.mapping,
                requested_by_id=self.get_user_id(),
            )
        except VendorUserService.PocUserServiceException as e:
            self.set_response_message(e.message)
            raise e

        self.set_response_message("Vendor document request sent")
        return Response(status=HTTP_200_OK)
