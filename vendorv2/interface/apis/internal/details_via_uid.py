from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.base_model import PydanticFilterBaseModel
from common.pydantic.custom_fields import HashIdInt
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBasicDetailsData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi
from vendorv2.interface.exceptions import VendorV2Exception


class VendorDetailsViaUidApi(ManageVendorBaseApi):
    class FilterModel(PydanticFilterBaseModel):
        uid: str
        country_id: HashIdInt

    filter_pydantic_class = FilterModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBasicDetailsData)},
        operation_id="vendor_details_via_uid",
        operation_summary="Get Vendor Details Via UID",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_filter_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_onboard_service()
        org_service = factory.get_org_service()

        try:
            vendor_id = service.get_vendor_id_via_uid(uid=data.uid, country_id=data.country_id)
            vendor_data = org_service.get_prefill_basic_details(org_id=vendor_id)
        except service.VendorNotFoundException:
            self.set_response_message("Please create vendor")
            return Response(status=HTTP_200_OK)
        except service.ClientExistsWithGivenUid as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except service.VendorAlreadyExistsWithGivenUid as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)
        except VendorV2Exception as e:
            self.set_response_message(
                e.message_dict.get("root")[0] if e.message_dict.get("root") else "Validation Error."
            )
            return Response(status=HTTP_400_BAD_REQUEST)
        if vendor_data:
            if vendor_data.id == user_entity.org_id:
                self.set_response_message("Vendor's organization is same as the organization you are logged in")
                return Response(status=HTTP_400_BAD_REQUEST)
        self.set_response_message("Vendor details fetched successfully.")
        return Response(pydantic_dump(vendor_data), status=HTTP_200_OK)
