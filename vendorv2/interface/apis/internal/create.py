from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.serializers import BaseSerializer, HashIdField
from vendorv2.domain.entities import VendorBasicDetailData, VendorOnboardInputData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorCreateApiV2(ManageVendorBaseApi):
    class OutputSerializer(BaseSerializer):
        vendor_id = HashIdField()

        class Meta:
            ref_name = "VendorCreateApiV2Output"

    input_serializer_class = VendorOnboardInputData.drf_serializer

    @swagger_auto_schema(
        request_body=VendorOnboardInputData.drf_serializer,
        responses={HTTP_200_OK: VendorBasicDetailData.drf_serializer},
        operation_id="vendor_create_api_v2",
        operation_summary="Create Vendor Api V2",
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_input_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_onboard_service()

        vendor_id, _ = service.onboard_vendor(data=data)

        return Response(self.OutputSerializer({"vendor_id": vendor_id}).data, status=HTTP_200_OK)
