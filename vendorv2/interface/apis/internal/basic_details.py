from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBasicDetail, OrganizationBasicDetailsUpdateData
from vendorv2.domain.entities import VendorBillingEntityFilter
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorBasicDetailsApi(ManageVendorBaseApi):
    filter_pydantic_class = VendorBillingEntityFilter

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBasicDetail)},
        operation_id="vendor_basic_details",
        operation_summary="Get Vendor Basic Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data: VendorBillingEntityFilter = self.validate_pydantic_filter_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            vendor_id=vendor_id,
            billing_entity_id=filter_data.billing_entity_id,
        )

        try:
            data = service.get_basic_details()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Vendor details fetched successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class VendorBasicDetailsUpdateApi(ManageVendorBaseApi):
    input_pydantic_class = OrganizationBasicDetailsUpdateData

    @swagger_auto_schema(
        request_body=pydantic_schema(OrganizationBasicDetailsUpdateData),
        responses={HTTP_200_OK: {}},
        operation_id="vendor_basic_details_update",
        operation_summary="Update Vendor Basic Details",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id, billing_entity_id=data.billing_entity_id)

        try:
            service.update_basic_details(data=data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Vendor details updated successfully.")
        return Response(status=HTTP_200_OK)
