from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from core.organization.domain.entities import OrganizationSectionUpdateData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorOrganizationSectionsUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = OrganizationSectionUpdateData.drf_serializer

    @swagger_auto_schema(
        responses={HTTP_200_OK: OrganizationSectionUpdateData.drf_serializer},
        operation_id="vendor_section_update_api",
        operation_summary="Update Vendor Section Api",
    )
    @transaction.atomic
    def put(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data: OrganizationSectionUpdateData = self.validate_input_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            vendor_id=vendor_id,
            billing_entity_id=data.billing_entity_id,
        )

        try:
            service.update_sections_data(data=data)
        except service.SectionUpdateException as e:
            return Response(e.message_dict, status=HTTP_400_BAD_REQUEST)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Client sections updated successfully.")
        return Response(status=HTTP_200_OK)
