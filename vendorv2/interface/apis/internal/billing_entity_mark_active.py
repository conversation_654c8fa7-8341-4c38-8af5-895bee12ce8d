from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.base_model import PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import LinkedOrganizationBillingEntityData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorBillingEntityMarkActiveApi(ManageVendorBaseApi):
    class InputModel(PydanticInputBaseModel):
        is_active: bool

    input_pydantic_class = InputModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(LinkedOrganizationBillingEntityData)},
        operation_id="vendor_billing_entity_mark_active",
        operation_summary="Mark Vendor Billing Entity Active",
    )
    def post(self, request, vendor_id, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            vendor_id=vendor_id,
            billing_entity_id=billing_entity_id,
        )

        try:
            service.mark_billing_entity_active(is_active=data.is_active)
            billing_entity = service.get_billing_entities(billing_entity_id=billing_entity_id)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message(f"Billing entity marked as {'active' if data.is_active else 'inactive'} successfully")
        return Response(pydantic_dump(billing_entity[0]), status=HTTP_200_OK)
