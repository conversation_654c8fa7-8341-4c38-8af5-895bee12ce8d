from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBillingEntityCreateData, OrganizationBillingEntityCreateEntity
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorBillingEntityCreateApi(ManageVendorBaseApi):
    input_pydantic_class = OrganizationBillingEntityCreateData

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityCreateEntity)},
        operation_id="vendor_billing_entity_create",
        operation_summary="Create Vendor Billing Entity",
    )
    def post(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        data = self.validate_pydantic_input_data()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id)

        try:
            billing_entity = service.create_billing_entity(data=data)
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Billing entity created successfully")
        return Response(pydantic_dump(billing_entity), status=HTTP_200_OK)
