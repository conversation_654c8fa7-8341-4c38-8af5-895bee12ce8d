from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.base_model import HashIdInt, PydanticInputBaseModel
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationCountryConfigData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorOnboardConfigApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = []

    class FilterModel(PydanticInputBaseModel):
        country_id: HashIdInt

    filter_pydantic_class = FilterModel

    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationCountryConfigData)},
        operation_id="vendor_onboard_config",
        operation_summary="Get Vendor Onboard Config",
    )
    def get(self, request, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        filter_data = self.validate_pydantic_filter_data()
        is_app = self.is_source_app()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_onboard_service()

        config = service.get_onboard_config(country_id=filter_data.country_id, is_app=is_app)

        return Response(pydantic_dump(config), status=HTTP_200_OK)
