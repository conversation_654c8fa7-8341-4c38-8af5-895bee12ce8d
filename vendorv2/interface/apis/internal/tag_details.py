from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from authorization.domain.constants import Permissions
from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from vendorv2.domain.entities.vendor_entities import VendorTag
from vendorv2.domain.factory import VendorFactory
from vendorv2.domain.services.services import ManageVendorOrgService
from vendorv2.interface.base_api import ManageVendorBaseApi
from vendorv2.interface.serializers import VendorTagListUpdateDataSerializer


class VendorTagDetailsApi(ManageVendorBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(VendorTag)},
        operation_id="vendor_tag_details",
        operation_summary="Get Vendor Tag Details",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id)

        data = service.get_tag_details()

        self.set_response_message("Vendor Tag details fetched successfully.")
        return Response(pydantic_dump(data), status=HTTP_200_OK)


class VendorTagUpdateApi(ManageVendorBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_EDIT_MANAGE_VENDOR]

    input_serializer_class = VendorTagListUpdateDataSerializer

    @swagger_auto_schema(
        responses={HTTP_201_CREATED: ""},
        request_body=VendorTagListUpdateDataSerializer(),
        operation_id="vendor_v2_vendor_tag_update_api",
        operation_summary="Vendor V2 Vendor Tag Update API",
    )
    def put(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()
        self.get_mapping()
        data = self.validate_input_data()

        ManageVendorOrgService.tags_update(cv_mapping_id=self.mapping.id, user_id=user_entity.user_id, data=data)

        self.set_response_message("Vendor Tag updated successfully.")
        return Response(status=HTTP_201_CREATED)
