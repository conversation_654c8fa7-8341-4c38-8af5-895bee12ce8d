from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorBillingEntityDeleteApi(ManageVendorBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: {}},
        operation_id="vendor_billing_entity_delete",
        operation_summary="Delete Vendor Billing Entity",
    )
    def delete(self, request, vendor_id, billing_entity_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(
            vendor_id=vendor_id,
            billing_entity_id=billing_entity_id,
        )

        try:
            service.delete_billing_entity()
        except service.BaseException as e:
            self.set_response_message(e.message)
            return Response(status=HTTP_400_BAD_REQUEST)

        self.set_response_message("Billing entity deleted successfully")
        return Response(status=HTTP_200_OK)
