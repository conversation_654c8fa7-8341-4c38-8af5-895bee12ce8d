from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from common.pydantic.model_dump import pydantic_dump
from common.pydantic.schema import pydantic_schema
from core.organization.domain.entities import OrganizationBillingEntityData
from vendorv2.domain.factory import VendorFactory
from vendorv2.interface.base_api import ManageVendorBaseApi


class VendorBillingEntityListApi(ManageVendorBaseApi):
    @swagger_auto_schema(
        responses={HTTP_200_OK: pydantic_schema(OrganizationBillingEntityData)},
        operation_id="vendor_billing_entity_list",
        operation_summary="Get Vendor Billing Entities",
    )
    def get(self, request, vendor_id, *args, **kwargs):
        user_entity = self.get_org_user_entity()

        factory = VendorFactory(user_entity=user_entity)
        service = factory.get_linked_service(vendor_id=vendor_id)

        data = service.get_billing_entities()

        self.set_response_message("Billing entities fetched successfully")
        return Response(pydantic_dump(data), status=HTTP_200_OK)
