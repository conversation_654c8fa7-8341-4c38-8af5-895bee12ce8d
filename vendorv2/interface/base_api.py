from authorization.domain.constants import Permissions
from core.apis import OrgBaseApi
from vendorv2.data.selectors.selectors import mapping_get


class ManageVendorBaseApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_MANAGE_VENDOR]
    mapping = None

    def get_mapping(self):
        if self.kwargs.get("vendor_id") and self.mapping is None:
            self.mapping = mapping_get(client_id=self.get_organization_id(), vendor_id=self.kwargs.get("vendor_id"))
        return self.mapping
