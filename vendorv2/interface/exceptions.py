from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from rest_framework.settings import api_settings

from common.exceptions import BaseValidationError


class VendorV2ExceptionConstants:
    ALREADY_EXISTS = "Vendor with this UID already exists"
    MULTIPLE_USERS_EXISTS = "Multiple Users Exists"
    USER_EXISTS_OTHER_ORG = "User Exists in Other Organization"


class VendorV2Exception(BaseValidationError):
    def __init__(
        self,
        message: str,
        key=api_settings.NON_FIELD_ERRORS_KEY,
    ):
        super().__init__({key: _(message)})


class VendorPOCException(ValidationError):
    pass
