from django.urls import include, path

from vendorv2.interface.apis.apis import Vendor<PERSON>ist<PERSON>2<PERSON>pi
from vendorv2.interface.apis.internal.bank_details import VendorBankDetailsApi
from vendorv2.interface.apis.internal.basic_details import VendorBasicDetails<PERSON>pi, VendorBasicDetailsUpdate<PERSON>pi
from vendorv2.interface.apis.internal.billing_entity_create import VendorBillingEntityCreateApi
from vendorv2.interface.apis.internal.billing_entity_delete import VendorBillingEntityDeleteApi
from vendorv2.interface.apis.internal.billing_entity_list import VendorBillingEntityListApi
from vendorv2.interface.apis.internal.billing_entity_mark_active import VendorBillingEntityMarkActiveApi
from vendorv2.interface.apis.internal.billing_entity_mark_default import VendorBillingEntityMarkDefaultApi
from vendorv2.interface.apis.internal.create import VendorCreateApiV2
from vendorv2.interface.apis.internal.details_via_uid import VendorDetailsViaUidApi
from vendorv2.interface.apis.internal.document_details import VendorDocumentDetailsApi
from vendorv2.interface.apis.internal.kyc_details import VendorKycDetailsApi
from vendorv2.interface.apis.internal.logo_upload import VendorUploadLogoApi
from vendorv2.interface.apis.internal.onboard_config import VendorOnboardConfigApi
from vendorv2.interface.apis.internal.other_details import VendorOtherDetailsApi
from vendorv2.interface.apis.internal.section_update import VendorOrganizationSectionsUpdateApi
from vendorv2.interface.apis.internal.tag_details import VendorTagDetailsApi, VendorTagUpdateApi

BILLING_ENTITY = [
    path(
        "list/",
        VendorBillingEntityListApi.as_view(),
        name="vendor-billing-entity-list",
    ),
    path(
        "create/",
        VendorBillingEntityCreateApi.as_view(),
        name="vendor-billing-entity-create",
    ),
    path(
        "<hash_id:billing_entity_id>/mark-default/",
        VendorBillingEntityMarkDefaultApi.as_view(),
        name="vendor-billing-entity-mark-default",
    ),
    path(
        "<hash_id:billing_entity_id>/mark-active/",
        VendorBillingEntityMarkActiveApi.as_view(),
        name="vendor-billing-entity-mark-active",
    ),
    path(
        "<hash_id:billing_entity_id>/delete/",
        VendorBillingEntityDeleteApi.as_view(),
        name="vendor-billing-entity-delete",
    ),
]

VENDOR = [
    path("basic-details/", VendorBasicDetailsApi.as_view(), name="vendor-basic-details"),
    path(
        "basic-details/update/",
        VendorBasicDetailsUpdateApi.as_view(),
        name="vendor-basic-details-update",
    ),
    path("kyc-details/", VendorKycDetailsApi.as_view(), name="vendor-kyc-details"),
    path("bank-details/", VendorBankDetailsApi.as_view(), name="vendor-bank-details"),
    path("other-details/", VendorOtherDetailsApi.as_view(), name="vendor-other-details"),
    path("document-details/", VendorDocumentDetailsApi.as_view(), name="vendor-document-details"),
    path("tag-details/", VendorTagDetailsApi.as_view(), name="vendor-tag-details"),
    path("update-tag/", VendorTagUpdateApi.as_view(), name="vendor-update-tag-api"),
    path(
        "sections/update/",
        VendorOrganizationSectionsUpdateApi.as_view(),
        name="vendor-sections-update",
    ),
    path("logo-upload/", VendorUploadLogoApi.as_view(), name="vendor-logo-upload"),
    path("billing-entity/", include(BILLING_ENTITY)),
]

urlpatterns = [
    path("list/", VendorListV2Api.as_view(), name="vendor-list-api-v2"),
    path("config/", VendorOnboardConfigApi.as_view(), name="vendor-onboard-config"),
    path("details-via-uid/", VendorDetailsViaUidApi.as_view(), name="vendor-details-via-uid"),
    path("create/", VendorCreateApiV2.as_view(), name="vendor-create-v2"),
    path("<hash_id:vendor_id>/", include(VENDOR)),
]
