from django.urls import path

from vendorv2.interface.apis.external.manage_vendor import (
    VendorBankDetailsV2Api,
    VendorBasicDetailsV2Api,
    VendorKycDetailsV2Api,
    VendorOtherDetailsV2Api,
    VendorUserListV2Api,
    VendorV2Api,
)

urlpatterns = [
    path("", VendorV2Api.as_view(), name="external-vendors-v2"),
    path(
        "/<hash_id:vendor_id>/basic-details",
        VendorBasicDetailsV2Api.as_view(),
        name="external-vendor-basic-details-v2",
    ),
    path("/<hash_id:vendor_id>/kyc-details", VendorKycDetailsV2Api.as_view(), name="external-vendor-kyc-details-v2"),
    path("/<hash_id:vendor_id>/bank-details", VendorBankDetailsV2Api.as_view(), name="external-vendor-bank-details-v2"),
    path(
        "/<hash_id:vendor_id>/other-details",
        VendorOtherDetailsV2Api.as_view(),
        name="external-vendor-other-details-v2",
    ),
    path("/<hash_id:vendor_id>/users", VendorUserListV2Api.as_view(), name="external-vendor-user-list-v2"),
]
