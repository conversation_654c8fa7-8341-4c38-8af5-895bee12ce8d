from django.urls import include, path

from vendorv2.interface.apis.apis import (
    OrganizationDetailUpdateApi,
    VendorBankDetailListApi,
    VendorBankDetailUpdateApi,
    VendorBasicDetailApi,
    VendorBulkCreateApi,
    VendorCreateApi,
    VendorDetailFetchFromPANApi,
    VendorDetailsApi,
    VendorDetailUpdateApi,
    VendorIsKycFilled,
    VendorKycCreateApi,
    VendorKycDetailAddApi,
    VendorKYCUpdateApi,
    VendorListApi,
    VendorListSearchConfigApi,
    VendorListV2Api,
    VendorOrganizationGetUsingPANApi,
    VendorOrganizationGSTDetails,
    VendorOtherDetailApi,
    VendorOtherDetailDocumentCreateApi,
    VendorOtherDetailDocumentDeleteApi,
    VendorOtherDetailDocumentUpdateApi,
    VendorRemoveApi,
    VendorStatusUpdate<PERSON><PERSON>,
    <PERSON>endor<PERSON>ag<PERSON>ist<PERSON><PERSON>,
    VendorTagUpdate<PERSON>pi,
)

urlpatterns = [
    path("", include("vendorv2.interface.urls.users"), name="vendor-onboard-urls"),
    path("list/", VendorListApi.as_view(), name="vendor-list-api"),
    path("list-v2/", VendorListV2Api.as_view(), name="vendor-list-api"),
    path("search-config/", VendorListSearchConfigApi.as_view(), name="vendor-search-config-api"),
    path("create/", VendorCreateApi.as_view(), name="vendor-add-api"),
    path("bulk-create-via-excel/", VendorBulkCreateApi.as_view(), name="vendor-bulk-add-api"),
    path("<hash_id:vendor_id>/kyc-detail/add/", VendorKycDetailAddApi.as_view(), name="vendor-add-api"),
    path(
        "<hash_id:vendor_id>/status-update/",
        VendorStatusUpdateApi.as_view(),
        name="vendor-mark-active-api",
    ),
    path("<hash_id:vendor_id>/update-detail/", VendorDetailUpdateApi.as_view(), name="vendor-update-detail-api"),
    path("<hash_id:vendor_id>/update-kyc/", VendorKYCUpdateApi.as_view(), name="vendor-update-kyc-api"),
    path("<hash_id:vendor_id>/update-tag/", VendorTagUpdateApi.as_view(), name="vendor-update-tag-api"),
    path("<hash_id:vendor_id>/update-bank/", VendorBankDetailUpdateApi.as_view(), name="vendor-update-tag-api"),
    path(
        "<hash_id:vendor_id>/other-detail-document/create/",
        VendorOtherDetailDocumentCreateApi.as_view(),
        name="vendor-create-doc-api",
    ),
    path(
        "<hash_id:vendor_id>/other-detail-document/<hash_id:doc_id>/update/",
        VendorOtherDetailDocumentUpdateApi.as_view(),
        name="vendor-update-doc-api",
    ),
    path(
        "<hash_id:vendor_id>/other-detail-document/<hash_id:doc_id>/delete/",
        VendorOtherDetailDocumentDeleteApi.as_view(),
        name="vendor-delete-doc-api",
    ),
    path("<hash_id:vendor_id>/detail/", VendorDetailsApi.as_view(), name="vendor-details-api"),
    path("<hash_id:vendor_id>/basic-detail/", VendorBasicDetailApi.as_view(), name="vendor-basic-details-api"),
    path("<hash_id:vendor_id>/gst-details/", VendorOrganizationGSTDetails.as_view(), name="vendor-gst-details-api"),
    path("<hash_id:vendor_id>/other-detail/", VendorOtherDetailApi.as_view(), name="vendor-other-details-api"),
    path("bank-details/", VendorBankDetailListApi.as_view(), name="vendor-other-details-api"),
    path("org-details/update/", OrganizationDetailUpdateApi.as_view(), name="org-details-update-api"),
    path("tags/", VendorTagListApi.as_view(), name="vendor-tag-list-api"),
    path("detail-via-pan/", VendorDetailFetchFromPANApi.as_view(), name="vendor-basic-details-via-pan-pi"),
    path("check-pan-exists/", VendorOrganizationGetUsingPANApi.as_view(), name="vendor-pan-exists-api"),
    path("vendor-kyc/", VendorKycCreateApi.as_view(), name="vendor-onboarding-kyc"),
    path("is-kyc-filled/", VendorIsKycFilled.as_view(), name="vendor-is-kyc-filled"),
    path("remove-vendor/", VendorRemoveApi.as_view(), name="vendor-remove"),
]
