from drf_yasg.utils import swagger_serializer_method
from phonenumber_field import serializerfields
from phonenumber_field.phonenumber import PhoneNumber
from phonenumbers import NumberParseException
from rest_framework import serializers

from common.choices import CountryChoices, StateChoices, VendorStatusChoices, VendorStatusColorCode
from common.regex import RegularExpression as Regex
from common.serializers import (
    BaseDataclassSerializer,
    BaseModelSerializer,
    BaseSerializer,
    CustomFileField,
    HashIdField,
    PhoneNumberSerializer,
)
from core.models import VendorOrgTag
from core.serializers import (
    OrganizationAddressSerializer,
    OrganizationGstModelSerializer,
    OrganizationModelSerializer,
    UserModelSerializer,
)
from rollingbanners.storage_backends import PublicMediaFileStorage
from vendor.data.models import Vendor
from vendorv2.data.models import (
    BlacklistReasonHistory,
    ClientVendorMapping,
    VendorBankDetail,
    VendorOtherDetailDocument,
)
from vendorv2.domain.entities import VendorBulkEntity, VendorOnboardConfigData


class FileSerializer(BaseSerializer):
    name = serializers.CharField()
    url = CustomFileField()

    class Meta:
        ref_name = "file_serializer"


class FileOutputSerializer(BaseSerializer):
    name = serializers.CharField()
    url = serializers.SerializerMethodField()

    @staticmethod
    def get_url(obj):
        if obj.get("url"):
            return PublicMediaFileStorage.url(str(obj["url"]))

    class Meta:
        ref_name = "file_output_serializer"


class OrganizationAddressOutputSerializer(BaseSerializer):
    address_line_one = serializers.CharField(max_length=100)
    address_line_two = serializers.CharField(max_length=100, allow_null=True)
    city = serializers.CharField(max_length=20)
    state = serializers.SerializerMethodField()
    country = serializers.CharField(default=CountryChoices.INDIA)
    pincode = serializers.CharField(max_length=6)

    def get_state(self, obj):
        if hasattr(obj, "state") and obj.state is not None:
            return str(dict(StateChoices.choices)[obj.state])
        return None

    class Meta:
        ref_name = "organization_address_output"


class OrganizationAddressInputSerializer(BaseSerializer):
    address_line_one = serializers.CharField(max_length=100)
    address_line_two = serializers.CharField(max_length=100, allow_null=True)
    city = serializers.CharField(max_length=20)
    state = serializers.IntegerField()
    country = serializers.CharField(default=CountryChoices.INDIA)
    pincode = serializers.CharField(max_length=6)

    class Meta:
        ref_name = "organization_address_input"


class ChequeFileSerializer(FileOutputSerializer):
    url = serializers.SerializerMethodField()
    name = serializers.CharField(source="cheque_file_name")

    @staticmethod
    def get_url(obj):
        if hasattr(obj, "cheque_file") and obj.cheque_file:
            return PublicMediaFileStorage.url(str(obj.cheque_file))
        return None


class BankDetailsInputSerializer(BaseSerializer):
    account_holder_name = serializers.CharField(allow_blank=False)
    bank_name = serializers.CharField(allow_blank=False)
    number = serializers.CharField(min_length=9, max_length=18)
    ifsc = serializers.RegexField(regex=Regex.IFSC_CODE)
    cheque = FileSerializer()

    class Meta:
        ref_name = "vendor_bank_details_input"


class BankDetailsOutputSerializer(BaseSerializer):
    account_holder_name = serializers.CharField(allow_blank=False)
    bank_name = serializers.CharField(allow_blank=False)
    number = serializers.CharField(source="account_number", min_length=9, max_length=18)
    ifsc = serializers.RegexField(source="ifsc_code", regex=Regex.IFSC_CODE)
    cheque = serializers.SerializerMethodField()

    def get_cheque(self, obj):
        return ChequeFileSerializer(obj).data

    class Meta:
        ref_name = "vendor_bank_details_output"


class VendorPANSerializer(FileSerializer):
    number = serializers.RegexField(regex=Regex.PAN_NUMBER)

    class Meta:
        ref_name = "pan_serializer"


class VendorMSMESerializer(BaseSerializer):
    name = serializers.CharField(allow_null=True, allow_blank=True)
    url = CustomFileField(allow_null=True, allow_blank=True)
    number = serializers.RegexField(regex=Regex.MSME_ID, allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "msme_id_serializer"


class VendorGSTSerializer(BaseSerializer):
    name = serializers.CharField(allow_null=True, allow_blank=True)
    url = CustomFileField(allow_null=True, allow_blank=True)
    number = serializers.RegexField(regex=Regex.GST_NUMBER, allow_blank=True, allow_null=True)

    class Meta:
        ref_name = "gst_serializer"


class VendorPANOutputSerializer(FileOutputSerializer):
    number = serializers.RegexField(regex=Regex.PAN_NUMBER)

    class Meta:
        ref_name = "pan_serializer"


class VendorMSMEOutputSerializer(FileOutputSerializer):
    number = serializers.RegexField(regex=Regex.MSME_ID)

    class Meta:
        ref_name = "msme_id_serializer"


class VendorGSTOutputSerializer(FileOutputSerializer):
    number = serializers.RegexField(regex=Regex.GST_NUMBER)

    class Meta:
        ref_name = "gst_serializer"


class VendorOrgSerializer(OrganizationModelSerializer):
    id = serializers.IntegerField(allow_null=True)

    class Meta(OrganizationModelSerializer.Meta):
        ref_name = "VendorOrgSerializer"
        fields = ("id", "name")


class VendorModelSerializer(BaseModelSerializer):
    organization = VendorOrgSerializer()

    class Meta:
        model = Vendor
        fields = "__all__"


class UserSerializer(UserModelSerializer):
    class Meta(UserModelSerializer.Meta):
        ref_name = "VendorPOCUserSerializer"
        fields = ("id", "name", "email", "phone_number", "photo")


class ClientVendorMappingSerializer(BaseModelSerializer):
    class Meta:
        model = ClientVendorMapping
        fields = "__all__"


class VendorSerializer(ClientVendorMappingSerializer):
    organization = VendorOrgSerializer(source="org_to")
    poc = UserSerializer(source="vendor_poc")
    is_kyc_filled = serializers.BooleanField(source="org_to.vendor.is_kyc_filled")
    pan_number = serializers.CharField(source="org_to.pan_number")
    is_poc_editable = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField())
    def get_is_poc_editable(self, obj):
        if (
            not obj.vendor_poc
            or (obj.vendor_poc.is_verified and hasattr(obj, "user_last_login") and not obj.user_last_login)
            or (not obj.vendor_poc.is_verified)
        ):
            return True
        return False

    class Meta(ClientVendorMappingSerializer.Meta):
        ref_name = "VendorSerializerV2"
        fields = (
            "id",
            "organization",
            "poc",
            "is_invited",
            "is_active",
            "is_kyc_filled",
            "pan_number",
            "is_poc_editable",
        )


class VendorOrgTagModelSerializer(BaseModelSerializer):
    class Meta:
        model = VendorOrgTag
        fields = "__all__"
        output_hash_id_fields = ("id",)


class VendorBlacklistedHistoryModelSerializer(BaseModelSerializer):
    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "VendorPOCUserSerializer"
            fields = ("id", "name", "photo")

    created_by = UserSerializer()

    class Meta:
        model = BlacklistReasonHistory
        fields = "__all__"


class VendorSerializerV2(ClientVendorMappingSerializer):
    class BlacklistHistorySerializer(VendorBlacklistedHistoryModelSerializer):
        class Meta(VendorBlacklistedHistoryModelSerializer.Meta):
            ref_name = "VendorBlacklistHistorySerializer"
            fields = ("id", "blacklist_reason", "created_at", "created_by")

    class VendorTagSerializer(VendorOrgTagModelSerializer):
        class Meta(VendorOrgTagModelSerializer.Meta):
            fields = ("id", "name")
            output_hash_id_fields = ("id",)
            ref_name = "VendorTagSerializer"

    name = serializers.CharField(source="org_to.name")
    code = serializers.CharField(source="org_to.vendor.code")
    poc_name = serializers.SerializerMethodField()
    poc_email = serializers.SerializerMethodField()
    poc_phone_number = serializers.SerializerMethodField()
    tags = VendorTagSerializer(many=True, source="vendor_tag")
    pan_number = serializers.CharField(source="uid_value")
    uid_name = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    blacklist_reason = serializers.SerializerMethodField()
    invite_status = serializers.CharField()
    mark_active = serializers.BooleanField()
    vendor_id = HashIdField()

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_uid_name(self, obj):
        return (
            obj.org_to.country.uid_field.name
            if obj.org_to.country and obj.org_to.country.uid_field and obj.org_to.country.uid_field.name
            else None
        )

    @swagger_serializer_method(serializer_or_field=serializers.CharField())
    def get_blacklist_reason(self, obj):
        if obj.vendor_status == VendorStatusChoices.BLACKLISTED.value and obj.vendor_blacklist_reason_histories.all():
            return self.BlacklistHistorySerializer(obj.vendor_blacklist_reason_histories.all()[0]).data
        return None

    @swagger_serializer_method(serializer_or_field=serializers.DictField())
    def get_status(self, obj):
        status = {
            "key": obj.status,
            "value": dict(VendorStatusChoices.choices).get(obj.status),
            "color_code": getattr(VendorStatusColorCode, obj.status.upper()).value,
        }
        return status

    def get_poc_name(self, obj):
        return obj.primary_user.get("name") if obj.primary_user else None

    def get_poc_email(self, obj):
        return obj.primary_user.get("email") if obj.primary_user else None

    def get_poc_phone_number(self, obj):
        if obj.primary_user:
            phone_number = obj.primary_user.get("phone_number")
            if phone_number:
                try:
                    phone_number = PhoneNumber.from_string(obj.primary_user.get("phone_number"))
                    return PhoneNumberSerializer(phone_number).data
                except NumberParseException:
                    return

    class Meta(ClientVendorMappingSerializer.Meta):
        ref_name = "VendorSerializerV2"
        fields = (
            "id",  # client_vendor_mapping_id
            "name",
            "poc_name",
            "poc_email",
            "poc_phone_number",
            "tags",
            "pan_number",
            "status",
            "invite_status",
            "mark_active",
            "code",
            "uid_name",
            "vendor_id",
            "blacklist_reason",
        )
        output_hash_id_fields = ("id",)


class VendorDataViaPanSerializer(OrganizationModelSerializer):
    class VendorPanDataSerializer(BaseSerializer):
        id = serializers.CharField()
        file = serializers.SerializerMethodField()
        number = serializers.RegexField(regex=Regex.PAN_NUMBER)
        file_name = serializers.CharField()

        def get_file(self, obj):
            return PublicMediaFileStorage.url(obj["file"]) if obj["file"] else None

        class Meta:
            ref_name = "VendorPanDataSerializer"

    class VendorAdharDataSerializer(BaseSerializer):
        class AadharFileSerializer(BaseSerializer):
            id = serializers.CharField()
            file = serializers.FileField()
            file_name = serializers.CharField()

            class Meta:
                ref_name = "AadharFileSerializer"

        files = AadharFileSerializer(many=True)
        number = serializers.RegexField(regex=Regex.AADHAR_NUMBER)

        class Meta:
            ref_name = "VendorAdharDataSerializer"

    class VendorMsmeDataSerializer(BaseSerializer):
        id = serializers.CharField()
        file = serializers.SerializerMethodField()
        number = serializers.RegexField(regex=Regex.MSME_ID)
        file_name = serializers.CharField()

        def get_file(self, obj):
            return PublicMediaFileStorage.url(obj["file"]) if obj["file"] else None

        class Meta:
            ref_name = "VendorMsmeDataSerializer"

    class VendorCompanyBusinessDataSerializer(BaseSerializer):
        id = serializers.CharField()
        file = serializers.SerializerMethodField()
        file_name = serializers.CharField()

        def get_file(self, obj):
            return PublicMediaFileStorage.url(obj["file"]) if obj["file"] else None

        class Meta:
            ref_name = "VendorCompanyBusinessDataSerializer"

    class VendorGSTDataSerializer(OrganizationGstModelSerializer):
        id = serializers.CharField()
        number = serializers.RegexField(regex=Regex.GST_NUMBER, source="gst_number")
        file = serializers.FileField()
        state = serializers.SerializerMethodField()
        file_name = serializers.CharField(source="name")

        @swagger_serializer_method(serializer_or_field=serializers.DictField())
        def get_state(self, obj):
            return (
                {"id": obj.gst_state, "name": dict(StateChoices.choices).get(int(obj.gst_state))}
                if obj.gst_state
                else None
            )

        class Meta(OrganizationGstModelSerializer.Meta):
            fields = ("id", "number", "file", "state", "file_name")

    organization_id = HashIdField(source="id")
    company_name = serializers.CharField(source="name")
    company_business_card = VendorCompanyBusinessDataSerializer()
    pan = VendorPanDataSerializer()
    aadhar = VendorAdharDataSerializer()
    msme = VendorMsmeDataSerializer()
    company_addresses = OrganizationAddressSerializer(many=True, source="addresses")
    gst = VendorGSTDataSerializer(many=True, source="organization_gst")
    is_editable = serializers.BooleanField()

    class Meta(OrganizationModelSerializer.Meta):
        ref_name = "VendorDataViaPanSerializer"
        fields = (
            "organization_id",
            "company_name",
            "company_addresses",
            "company_business_card",
            "pan",
            "aadhar",
            "msme",
            "gst",
            "is_editable",
        )


class VendorOtherDetailDocumentModelSerializer(BaseModelSerializer):
    class UserSerializer(UserModelSerializer):
        class Meta(UserModelSerializer.Meta):
            ref_name = "VendorPOCUserSerializer"
            fields = ("id", "name", "photo")

    created_by = UserSerializer()
    updated_by = UserSerializer()

    class Meta:
        model = VendorOtherDetailDocument
        fields = "__all__"


class ClientVendorModelSerializer(BaseModelSerializer):
    class Meta:
        model = ClientVendorMapping
        fields = "__all__"


class VendorBankDetailModelSerializer(BaseModelSerializer):
    cheque = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=serializers.DictField())
    def get_cheque(self, obj):
        return ChequeFileSerializer(obj).data

    class Meta:
        model = VendorBankDetail
        fields = "__all__"


class VendorBankSerializer(VendorBankDetailModelSerializer):
    class Meta(VendorBankDetailModelSerializer.Meta):
        ref_name = "VendorBankSerializer"
        fields = ("id", "account_holder_name", "bank_name", "account_number", "ifsc_code", "cheque")


class VendorOtherDetailSerializer(ClientVendorModelSerializer):
    class VendorPOCSerializer(BaseSerializer):
        name = serializers.CharField()
        email = serializers.EmailField()
        phone_number = serializers.CharField()

        class Meta:
            ref_name = "VendorPOCSerializer"

    class VendorTagSerializer(VendorOrgTagModelSerializer):
        class Meta(VendorOrgTagModelSerializer.Meta):
            ref_name = "VendorTagSerializer"
            fields = ("id", "name")

    class VendorOtherDetailDocumentSerializer(VendorOtherDetailDocumentModelSerializer):
        uploaded_by_name = serializers.CharField(source="created_by.name")
        uploaded_by_photo = serializers.FileField(source="created_by.photo")
        uploaded_at = serializers.DateTimeField(source="created_at")

        class Meta(VendorOtherDetailDocumentModelSerializer.Meta):
            ref_name = "VendorOtherDetailDocumentSerializer"
            fields = ("id", "name", "tags", "file", "uploaded_by_name", "uploaded_by_photo", "uploaded_at")

    class BankDetailSerializer(VendorBankSerializer):
        class Meta(VendorBankSerializer.Meta):
            ref_name = "VendorBankDetailSerializer"
            fields = ("id", "account_holder_name", "bank_name", "account_number", "ifsc_code", "cheque", "is_editable")

    poc = serializers.SerializerMethodField()
    tags = VendorTagSerializer(many=True, source="vendor_tag")
    bank_details = BankDetailSerializer(many=True)
    other_documents = VendorOtherDetailDocumentSerializer(many=True, source="other_detail_docs")
    is_editable = serializers.BooleanField(default=True)
    company_name = serializers.CharField(source="org_to.name")

    @swagger_serializer_method(serializer_or_field=VendorPOCSerializer())
    def get_poc(self, obj):
        return {
            "name": obj.vendor_poc_name,
            "email": obj.vendor_poc_email,
            "phone_number": (
                {
                    "country_code": str(obj.vendor_poc_phone_number)[:-10],
                    "number": str(obj.vendor_poc_phone_number)[-10:],
                }
                if obj.vendor_poc_phone_number
                else None
            ),
        }

    class Meta(ClientVendorModelSerializer.Meta):
        ref_name = "VendorOtherDetailSerializer"
        fields = ("id", "poc", "tags", "bank_details", "other_documents", "is_editable", "company_name")


class VendorOtherDetailDocumentOutputSerialzer(VendorOtherDetailDocumentModelSerializer):
    class Meta(VendorOtherDetailDocumentModelSerializer.Meta):
        ref_name = "VendorOtherDetailDocumentCreateOutputSerializer"
        fields = ["id", "name", "file", "created_by", "tags", "created_at", "updated_at", "created_by", "updated_by"]


class VendorExcelImportSerializer(BaseDataclassSerializer):
    pan_number = serializers.RegexField(
        regex=Regex.PAN_NUMBER,
        allow_null=True,
        allow_blank=True,
        error_messages={"invalid": "PAN Number invalid"},
    )
    gst_number = serializers.RegexField(
        regex=Regex.GST_NUMBER,
        allow_null=True,
        allow_blank=True,
        error_messages={"invalid": "Tax Number invalid"},
    )
    organization_name = serializers.CharField()
    user_name = serializers.CharField(allow_null=True, allow_blank=True)
    phone_number = serializerfields.PhoneNumberField(allow_null=True, allow_blank=True)
    email = serializers.EmailField(allow_null=True, allow_blank=True)

    class Meta:
        dataclass = VendorBulkEntity


class VendorOnboardConfigDataSerializer(BaseDataclassSerializer):
    class Meta:
        dataclass = VendorOnboardConfigData
