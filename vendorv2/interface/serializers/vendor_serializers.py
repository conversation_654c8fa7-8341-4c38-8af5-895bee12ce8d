from rest_framework import serializers

from common.serializers import BaseModelSerializer, HashIdField, PhoneNumberSerializer
from vendorv2.data.models import VendorPoc


class VendorPOCModelSerializer(BaseModelSerializer):
    class Meta:
        model = VendorPoc
        fields = "__all__"


class VendorUserSerializer(VendorPOCModelSerializer):
    active_status = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()
    user_id = HashIdField(allow_null=True)

    def get_phone_number(self, obj):
        return PhoneNumberSerializer(obj.phone_number).data if obj.phone_number else None

    def get_active_status(self, obj):
        return obj.active_status

    class Meta:
        model = VendorPoc
        ref_name = "VendorUserSerializer"
        fields = (
            "id",
            "name",
            "phone_number",
            "email",
            "is_primary",
            "is_invited",
            "active_status",
            "created_at",
            "user_id",
        )
