from phonenumber_field import serializerfields
from rest_framework import serializers

from common.entities import ObjectStatus
from common.regex import RegularExpression as Regex
from common.serializers import BaseDataclassSerializer, CustomFileField, HashIdField
from common.validators import alphanumeric_validator
from core.entities import (
    OrganizationAadharUpdateData,
    OrganizationAddressUpdateData,
    OrganizationBasicDetailUpdateData,
    OrganizationBusinessCardUpdateData,
    OrganizationGSTUpdateData,
    OrganizationKYCUpdateData,
    OrganizationMSMEUpdateData,
)
from core.serializers import (
    OrganizationDocumentUpdateDataSerializer,
    OrganizationPANUpdateDataSerializer,
)
from vendorv2.domain.entities import (
    OrgDataUpdate,
    VendorAadharData,
    VendorAadharFileData,
    VendorAddressData,
    VendorBankData,
    VendorBankListUpdateData,
    VendorBankUpdateData,
    VendorBusinessCardData,
    VendorCreateData,
    VendorGSTData,
    VendorKycDetailAddEntity,
    VendorMSMEData,
    VendorOnboardConfigData,
    VendorOrganizationEntity,
    VendorPanData,
    VendorPOCData,
    VendorPOCEntity,
    VendorPocInviteData,
    VendorTagData,
    VendorTagListUpdateData,
)


class VendorOrgSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    name = serializers.CharField(max_length=50)

    class Meta:
        ref_name = "VendorOrgDataClassSerializer"
        dataclass = VendorOrganizationEntity


class VendorPOCSerializer(BaseDataclassSerializer):
    name = serializers.CharField(max_length=50)
    phone_number = serializerfields.PhoneNumberField(allow_blank=True, allow_null=True, default=None)
    email = serializers.EmailField()

    class Meta:
        ref_name = "VendorPOCDataClassSerializer"
        dataclass = VendorPOCEntity


class VendorAddressDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    country = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    state = serializers.IntegerField(allow_null=True)
    city = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    pincode = serializers.CharField(max_length=6, allow_null=True, allow_blank=True)
    address_line_1 = serializers.CharField(max_length=100, allow_null=True, allow_blank=True)
    address_line_2 = serializers.CharField(max_length=100, allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VendorAddressDataClassSerializer"
        dataclass = VendorAddressData


class VendorGSTDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    number = serializers.RegexField(
        regex=Regex.GST_NUMBER, error_messages={"invalid": "GST Number Invalid"}, allow_null=True
    )
    file = CustomFileField(allow_null=True)
    state = serializers.IntegerField(allow_null=True)
    file_name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VendorGSTDataClassSerializer"
        dataclass = VendorGSTData


class VendorMSMEDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    number = serializers.RegexField(
        regex=Regex.MSME_ID, error_messages={"invalid": "MSME Number Invalid"}, allow_null=True
    )
    file = CustomFileField(allow_null=True)
    file_name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VendorMSMEDataClassSerializer"
        dataclass = VendorMSMEData


class VendorAadharFileDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    file = CustomFileField()

    class Meta:
        dataclass = VendorAadharFileData


class VendorAadharDataSerializer(BaseDataclassSerializer):
    number = serializers.RegexField(
        regex=Regex.AADHAR_NUMBER, error_messages={"invalid": "Aadhar Number Invalid"}, allow_null=True
    )
    files = VendorAadharFileDataSerializer(many=True, required=False, default=[])

    def validate(self, attrs):
        data: VendorAadharData = super().validate(attrs)
        if len(data.files) > 0 and not data.number:
            raise serializers.ValidationError("Aadhar number is required")
        return data

    class Meta:
        ref_name = "VendorAadharDataClassSerializer"
        dataclass = VendorAadharData


class VendorPOCDataSerializer(BaseDataclassSerializer):
    name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    phone_number = serializerfields.PhoneNumberField(allow_blank=True, allow_null=True, default=None)
    email = serializers.EmailField(allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VendorPOCDataClassSerializer"
        dataclass = VendorPOCData


class VendorBankDataSerializer(BaseDataclassSerializer):
    cancelled_cheque = CustomFileField(allow_null=True)
    holder_name = serializers.CharField(max_length=250, allow_null=True)
    bank_name = serializers.CharField(max_length=250, allow_null=True)
    account_number = serializers.CharField(max_length=18, allow_null=True, validators=[alphanumeric_validator])
    ifsc_code = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    cancelled_cheque_name = serializers.CharField(max_length=250, allow_null=True)

    class Meta:
        ref_name = "VendorBankDataClassSerializer"
        dataclass = VendorBankData


class VendorTagDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    name = serializers.CharField(max_length=250)

    class Meta:
        ref_name = "VendorTagDataClassSerializer"
        dataclass = VendorTagData


class VendorPanDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    file = CustomFileField(allow_null=True)
    file_name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)

    class Meta:
        ref_name = "VendorPanDataClassSerializer"
        dataclass = VendorPanData


class VendorBusinessCardDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    file = CustomFileField()

    class Meta:
        dataclass = VendorBusinessCardData
        ref_name = "VendorBusinessCardDataClassSerializer"


class VendorCreateDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    addresses = VendorAddressDataSerializer(many=True, default=[], allow_null=True)
    business_card = VendorBusinessCardDataSerializer(allow_null=True)
    pan_number = serializers.RegexField(
        regex=Regex.PAN_NUMBER, error_messages={"invalid": "PAN Number Invalid"}, allow_null=True
    )

    class Meta:
        dataclass = VendorCreateData
        ref_name = "VendorCreateDataClassSerializer"


class VendorKycDetailAddSerializer(BaseDataclassSerializer):
    pan = VendorPanDataSerializer(allow_null=True)
    gst = VendorGSTDataSerializer(many=True, default=[], allow_null=True)
    msme = VendorMSMEDataSerializer(allow_null=True, required=False, default=None)
    aadhar = VendorAadharDataSerializer(allow_null=True, required=False, default=None)
    bank = VendorBankDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = VendorKycDetailAddEntity
        ref_name = "VendorCreateDataClassSerializer"


class VendorPocInviteDataSerializer(BaseDataclassSerializer):
    phone_number = serializerfields.PhoneNumberField()

    class Meta:
        dataclass = VendorPocInviteData


class VendorAddressUpdateDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    address_line_one = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    address_line_two = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    city = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    state = serializers.IntegerField(allow_null=True)
    country = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    pincode = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)

    class Meta:
        dataclass = OrganizationAddressUpdateData


class VendorBusinessCardUpdateDataSerializer(BaseDataclassSerializer):
    id = HashIdField(allow_null=True)
    file = CustomFileField(allow_null=True)
    file_name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)

    def validate(self, attrs):
        data: OrganizationBusinessCardUpdateData = super().validate(attrs)
        if data.object_status not in [ObjectStatus.ADD, ObjectStatus.DELETE]:
            raise serializers.ValidationError("Invalid object status")
        if data.object_status == ObjectStatus.DELETE and not data.id:
            raise serializers.ValidationError("ID is required to delete the file")
        if data.object_status == ObjectStatus.ADD and not data.file:
            raise serializers.ValidationError("File is required to add the file")
        return data

    class Meta:
        dataclass = OrganizationBusinessCardUpdateData


class VendorBasicDetailUpdateDataSerializer(BaseDataclassSerializer):
    addresses = VendorAddressUpdateDataSerializer(many=True, default=[], allow_null=True)
    business_cards = VendorBusinessCardUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = OrganizationBasicDetailUpdateData


class VendorAadharUpdateDataSerializer(BaseDataclassSerializer):
    number = serializers.RegexField(
        regex=Regex.AADHAR_NUMBER, error_messages={"invalid": "Aadhar Number Invalid"}, allow_null=True
    )
    files = OrganizationDocumentUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = OrganizationAadharUpdateData


class VendorMSMEUpdateDataSerializer(BaseDataclassSerializer):
    number = serializers.RegexField(
        regex=Regex.MSME_ID, error_messages={"invalid": "MSME Number Invalid"}, allow_null=True
    )
    files = OrganizationDocumentUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = OrganizationMSMEUpdateData


class VendorGSTUpdateDataSerializer(BaseDataclassSerializer):
    number = serializers.RegexField(
        regex=Regex.GST_NUMBER, error_messages={"invalid": "GST Number Invalid"}, allow_null=True
    )
    file = CustomFileField(allow_null=True)
    file_name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    id = HashIdField(allow_null=True)
    state = serializers.IntegerField(allow_null=True)

    class Meta:
        dataclass = OrganizationGSTUpdateData


class VendorKYCUpdateDataSerializer(BaseDataclassSerializer):
    pan = OrganizationPANUpdateDataSerializer()
    aadhar = VendorAadharUpdateDataSerializer()
    msme = VendorMSMEUpdateDataSerializer()
    gst = VendorGSTUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = OrganizationKYCUpdateData


class VendorBankUpdateDataSerializer(VendorBankDataSerializer):
    id = HashIdField(allow_null=True)

    class Meta:
        dataclass = VendorBankUpdateData


class VendorBankListUpdateDataSerializer(BaseDataclassSerializer):
    data = VendorBankUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = VendorBankListUpdateData


class OrganizationDataUpdateSerializer(
    VendorBasicDetailUpdateDataSerializer,
    VendorKYCUpdateDataSerializer,
):
    bank_details = VendorBankUpdateDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = OrgDataUpdate
        ref_name = "OrganizationUpdateDataInputSerializer"


class VendorPocUpdateDataSerializer(VendorPocInviteDataSerializer):
    phone_number = serializerfields.PhoneNumberField(allow_blank=True, allow_null=True, default=None)
    email = serializers.EmailField(allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)


class VendorTagListUpdateDataSerializer(BaseDataclassSerializer):
    data = VendorTagDataSerializer(many=True, default=[], allow_null=True)

    class Meta:
        dataclass = VendorTagListUpdateData


class VendorOnboardConfigDataSerializer(BaseDataclassSerializer):
    is_pan_required = serializers.BooleanField()
    is_bank_detail_required = serializers.BooleanField()

    class Meta:
        dataclass = VendorOnboardConfigData
