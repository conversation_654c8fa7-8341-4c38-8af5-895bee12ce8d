from django.core.files.uploadedfile import InMemoryUploadedFile

from common.excel.entities import ColumnNameKeyMapping, ExcelColumnMappingData
from common.excel.enum import ExcelColumnTypeEnum
from common.excel.parser import BaseExcelParser
from core.organization.domain.constants import INDIA, UAE
from vendorv2.domain.entities import VendorOnboardInBulkData


class VendorBulkCreateExcelParser(BaseExcelParser):
    INDIA_COLUMNS_NAME_KEY_MAPPING: ColumnNameKeyMapping = {
        "uid": ExcelColumnMappingData(
            column_name="PAN Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "organization_name": ExcelColumnMappingData(
            column_name="Company Registration Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "tax_number": ExcelColumnMappingData(
            column_name="GST Number",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "user_name": ExcelColumnMappingData(
            column_name="User Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "phone_number": ExcelColumnMappingData(
            column_name="Mobile Number",
            type=ExcelColumnTypeEnum.PHONE_NUMBER,
        ),
        "email": ExcelColumnMappingData(
            column_name="Email Address",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_account_holder_name": ExcelColumnMappingData(
            column_name="Primary Account Holder Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_bank_name": ExcelColumnMappingData(
            column_name="Primary Bank Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_account_number": ExcelColumnMappingData(
            column_name="Primary Account Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "primary_bank_code": ExcelColumnMappingData(
            column_name="Primary IFSC Code",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "secondary_account_holder_name": ExcelColumnMappingData(
            column_name="Secondary Account Holder Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "secondary_bank_name": ExcelColumnMappingData(
            column_name="Secondary Bank Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "secondary_account_number": ExcelColumnMappingData(
            column_name="Secondary Account Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "secondary_bank_code": ExcelColumnMappingData(
            column_name="Secondary IFSC Code",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "vendor_tags": ExcelColumnMappingData(
            column_name="Vendor Tags",
            type=ExcelColumnTypeEnum.LIST,
        ),
        "base_address_line_1": ExcelColumnMappingData(
            column_name="Base Address Line 1",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_address_line_2": ExcelColumnMappingData(
            column_name="Base Address Line 2",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_country_name": ExcelColumnMappingData(
            column_name="Base Country",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_state_name": ExcelColumnMappingData(
            column_name="Base State",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_city_name": ExcelColumnMappingData(
            column_name="Base City",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_zip_code": ExcelColumnMappingData(
            column_name="Base Postal/Zip Code",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "additional_address_line_1": ExcelColumnMappingData(
            column_name="Additional Address Line 1",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_address_line_2": ExcelColumnMappingData(
            column_name="Additional Address Line 2",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_country_name": ExcelColumnMappingData(
            column_name="Additional Country",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_state_name": ExcelColumnMappingData(
            column_name="Additional State",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_city_name": ExcelColumnMappingData(
            column_name="Additional City",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_zip_code": ExcelColumnMappingData(
            column_name="Additional Postal/Zip Code",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "aadhar_number": ExcelColumnMappingData(
            column_name="Aadhar Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "msme_id": ExcelColumnMappingData(
            column_name="MSME ID",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
    }

    UAE_COLUMNS_NAME_KEY_MAPPING = {
        "uid": ExcelColumnMappingData(
            column_name="Trade License Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "organization_name": ExcelColumnMappingData(
            column_name="Company Registration Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "tax_number": ExcelColumnMappingData(
            column_name="VAT Registration Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "user_name": ExcelColumnMappingData(
            column_name="User Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "phone_number": ExcelColumnMappingData(
            column_name="Mobile Number",
            type=ExcelColumnTypeEnum.PHONE_NUMBER,
        ),
        "email": ExcelColumnMappingData(
            column_name="Email Address",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_account_holder_name": ExcelColumnMappingData(
            column_name="Primary Account Holder Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_iban_number": ExcelColumnMappingData(
            column_name="Primary IBAN (International Bank Account Number)",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "primary_bank_branch_name": ExcelColumnMappingData(
            column_name="Primary Branch Name",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_bank_code": ExcelColumnMappingData(
            column_name="Primary SWIFT/BIC Code",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "primary_account_number": ExcelColumnMappingData(
            column_name="Primary Account Number",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "vendor_tags": ExcelColumnMappingData(
            column_name="Vendor Tags",
            type=ExcelColumnTypeEnum.LIST,
        ),
        "base_address_line_1": ExcelColumnMappingData(
            column_name="Base Address Line 1",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_address_line_2": ExcelColumnMappingData(
            column_name="Base Address Line 2",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_country_name": ExcelColumnMappingData(
            column_name="Base Country",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_state_name": ExcelColumnMappingData(
            column_name="Base State",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_city_name": ExcelColumnMappingData(
            column_name="Base City",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "base_zip_code": ExcelColumnMappingData(
            column_name="Base Postal/Zip Code",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "additional_address_line_1": ExcelColumnMappingData(
            column_name="Additional Address Line 1",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_address_line_2": ExcelColumnMappingData(
            column_name="Additional Address Line 2",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_country_name": ExcelColumnMappingData(
            column_name="Additional Country",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_state_name": ExcelColumnMappingData(
            column_name="Additional State",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_city_name": ExcelColumnMappingData(
            column_name="Additional City",
            type=ExcelColumnTypeEnum.STRING,
        ),
        "additional_zip_code": ExcelColumnMappingData(
            column_name="Additional Postal/Zip Code",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
        "aadhar_number": ExcelColumnMappingData(
            # Emirates id treated as aadhar number in object of VendorOnboardInBulkData
            column_name="Emirates ID",
            type=ExcelColumnTypeEnum.ALPHANUMERIC,
        ),
    }

    serializer_class = VendorOnboardInBulkData.drf_serializer

    def __init__(self, file: InMemoryUploadedFile, country_id: int):
        if country_id is INDIA:
            self.COLUMNS_NAME_KEY_MAPPING = self.INDIA_COLUMNS_NAME_KEY_MAPPING
        elif country_id is UAE:
            self.COLUMNS_NAME_KEY_MAPPING = self.UAE_COLUMNS_NAME_KEY_MAPPING
        else:
            raise NotImplementedError("Country not supported")
        super().__init__(file)
