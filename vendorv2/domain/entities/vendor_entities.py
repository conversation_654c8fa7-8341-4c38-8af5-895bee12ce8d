import datetime
from typing import Optional

from pydantic import EmailStr

from common.pydantic.base_model import BaseModel, BaseModelV2, PydanticInputBaseModel
from common.pydantic.custom_fields import CustomFileUrlStr, HashIdInt, PhoneNumberStr
from core.entities import CountryData
from core.organization.domain.entities import (
    OrganizationAddressData,
    OrganizationBusinessCardData,
    OrganizationOnboardInputData,
)


class VendorAddressData(OrganizationAddressData):
    pass


class VendorBusinessCardData(OrganizationBusinessCardData):
    pass


class VendorBasicDetailData(BaseModel):
    id: HashIdInt | None = None
    name: str
    logo: Optional[str]
    business_card: Optional[VendorBusinessCardData]
    addresses: list[VendorAddressData]
    country: CountryData


class VendorOnboardInputData(OrganizationOnboardInputData):
    uid: Optional[str]


class VendorOnboardInBulkData(BaseModel):
    uid: Optional[str] = None
    organization_name: str
    tax_number: Optional[str] = None
    user_name: Optional[str] = None
    phone_number: Optional[PhoneNumberStr] = None
    email: Optional[EmailStr] = None
    primary_account_holder_name: Optional[str] = None
    primary_bank_name: Optional[str] = None
    primary_bank_code: Optional[str] = None
    primary_account_number: Optional[str] = None
    primary_iban_number: Optional[str] = None  # only for uae
    primary_bank_branch_name: Optional[str] = None
    secondary_account_holder_name: Optional[str] = None
    secondary_bank_name: Optional[str] = None
    secondary_bank_code: Optional[str] = None
    secondary_account_number: Optional[str] = None
    vendor_tags: Optional[list[str]] = None
    base_address_line_1: Optional[str] = None
    base_address_line_2: Optional[str] = None
    base_country_name: Optional[str] = None
    base_state_name: Optional[str] = None
    base_city_name: Optional[str] = None
    base_zip_code: Optional[str] = None
    additional_address_line_1: Optional[str] = None
    additional_address_line_2: Optional[str] = None
    additional_country_name: Optional[str] = None
    additional_state_name: Optional[str] = None
    additional_city_name: Optional[str] = None
    additional_zip_code: Optional[str] = None
    aadhar_number: Optional[str] = None
    msme_id: Optional[str] = None


class VendorDocumentDetailUploadedBy(BaseModelV2):
    name: str
    photo: CustomFileUrlStr | None


class VendorDocumentDetail(BaseModelV2):
    id: HashIdInt
    file: CustomFileUrlStr
    name: str
    tags: list[str] | None = []
    uploaded_by: VendorDocumentDetailUploadedBy
    uploaded_at: datetime.datetime


class VendorTag(BaseModelV2):
    id: HashIdInt
    name: str


class VendorBasicDetails(BaseModelV2):
    id: HashIdInt
    name: str
    logo: CustomFileUrlStr | None
    business_card: OrganizationBusinessCardData | None
    addresses: list[VendorAddressData]


class VendorBillingEntityFilter(PydanticInputBaseModel):
    billing_entity_id: HashIdInt | None = None
