from dataclasses import dataclass
from typing import List, Optional

from common.entities import BaseNestedObject
from core.entities import OrganizationBasicDetailUpdateData, OrganizationKYCUpdateData, OrganizationPocUpdateData
from vendorv2.domain.entities.vendor_entities import (
    VendorAddressData,
)


@dataclass
class VendorPOCEntity:
    name: str
    phone_number: str
    email: str


@dataclass
class VendorOrganizationEntity:
    id: int
    name: str


@dataclass
class UserUpdateEntity:
    first_name: str
    last_name: str
    email: str
    phone_number: str


# @dataclass
# class VendorAddressData:
#     id: Optional[int]
#     country: Optional[str]
#     state: Optional[str]
#     city: Optional[str]
#     pincode: Optional[str]
#     address_line_1: Optional[str]
#     address_line_2: Optional[str]


@dataclass
class VendorGSTData:
    id: Optional[int]
    number: str
    file: str
    state: Optional[str]
    file_name: str


@dataclass
class VendorMSMEData:
    id: Optional[int]
    number: str
    file: str
    file_name: str


@dataclass
class VendorAadharFileData:
    file: str
    file_name: str
    id: Optional[int]


@dataclass
class VendorAadharData:
    number: str
    files: List[VendorAadharFileData]


@dataclass
class VendorPOCData:
    name: Optional[str]
    phone_number: Optional[str]
    email: Optional[str]


@dataclass
class VendorBankData:
    holder_name: Optional[str]
    bank_name: Optional[str]
    account_number: Optional[str]
    ifsc_code: Optional[str]
    cancelled_cheque: Optional[str]
    cancelled_cheque_name: Optional[str]


@dataclass
class VendorTagData:
    id: Optional[int]
    name: str


@dataclass
class VendorPanData:
    id: Optional[int]
    file: str
    file_name: str


@dataclass
class VendorBusinessCardData:
    id: Optional[int]
    file: str
    file_name: str


@dataclass
class VendorOrgCreateData:
    id: Optional[int]
    pan_number: Optional[str]
    name: Optional[str]


@dataclass
class VendorCreateData(VendorOrgCreateData):
    business_card: Optional[VendorBusinessCardData]
    addresses: Optional[List[VendorAddressData]]


@dataclass
class VendorKycDetailAddEntity:
    gst: Optional[List[VendorGSTData]]
    msme: Optional[VendorMSMEData]
    aadhar: Optional[VendorAadharData]
    bank: Optional[List[VendorBankData]]
    pan: Optional[VendorPanData]


@dataclass
class VendorPocInviteData(OrganizationPocUpdateData):
    pass


@dataclass
class VendorBankUpdateData(BaseNestedObject, VendorBankData):
    pass


@dataclass
class VendorBankListUpdateData:
    data: List[VendorBankUpdateData]


@dataclass
class VendorTagListUpdateData:
    data: List[VendorTagData]


@dataclass
class VendorBulkEntity:
    pan_number: Optional[str]
    organization_name: str
    gst_number: Optional[str]
    user_name: Optional[str]
    phone_number: Optional[str]
    email: Optional[str]


@dataclass
class OrgDataUpdate(OrganizationBasicDetailUpdateData, OrganizationKYCUpdateData):
    bank_details: VendorBankListUpdateData


@dataclass
class VendorOnboardConfigData:
    is_pan_required: bool
    is_bank_detail_required: bool
    uid_name: Optional[str] = None
    bank_section_name: Optional[str] = None
