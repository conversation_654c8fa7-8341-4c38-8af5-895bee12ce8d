import json
from typing import Dict, List, Union

from django.conf import settings

from smtp_email.domain.services import DjangoEmailService
from vendorv2.data.models import VendorEmail


def create_vendor_email(
    vendor_id: int,
    subject: str,
    receivers: list,
    sent_by_id: int,
    context: json,
):
    email = VendorEmail()
    # email.cc_receiver =
    email.subject = subject
    email.vendor_id = vendor_id
    email.to_receiver = receivers
    email.sent_by_id = sent_by_id
    email.context = context
    # email.files = [attachment.get("url") for attachment in attachments]
    email.full_clean()
    email.save()
    return email


class VendorEmailService(DjangoEmailService):
    def process_email(
        self,
        subject: str,
        vendor_id: int,
        to: List,
        body: str = None,
        cc: List = list,
        bcc: List = list,
        context: Union[Dict, None] = dict,
        attachments: Union[List, None] = list,
        html_email_template_name: str = None,
        sent_by_id: int = None,
    ):
        create_vendor_email(
            vendor_id=vendor_id, subject=subject, receivers=to, sent_by_id=sent_by_id, context=json.dumps(context)
        )
        return self.send_email(
            subject=subject,
            body=body,
            to=list(to),
            cc=cc,
            bcc=bcc,
            from_email=settings.EMAIL_HOST_USER,
            attachments=attachments,
            context=context,
            html_email_template_name=html_email_template_name,
            created_by_id=sent_by_id,
        )
