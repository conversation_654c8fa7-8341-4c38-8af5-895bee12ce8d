import re
from collections import defaultdict
from typing import Op<PERSON>, <PERSON><PERSON>, Union

import structlog
from django.db import transaction
from phonenumber_field.phonenumber import PhoneNumber

from common.choices import OrganizationType, VendorStatusChoices
from common.exceptions import BaseValidationError
from core.caches import CountryListCache
from core.choices import OrganizationDocumentChoices
from core.entities import CountryData
from core.models import City, Organization, OrganizationConfig, State
from core.models import OrganizationDocument as OrganizationBusinessCard
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.selectors import (
    get_linked_organization_documents_by_section,
    get_mapping_for_vendor,
    get_organization_sections_config,
    get_uid_field_by_country,
)
from core.organization.domain.entities import (
    CityData,
    DocumentDateFieldValueData,
    DocumentFieldValueBaseData,
    DocumentFileFieldValueData,
    DocumentStateFieldValueData,
    DocumentTextFieldValueData,
    FileData,
    OrganizationAddressUpdateData,
    OrganizationBasicDetailsUpdateData,
    OrganizationCountryConfigData,
    OrganizationDocumentData,
    OrganizationFieldData,
    OrganizationSectionData,
    OrganizationSectionUpdateData,
    StateData,
)
from core.organization.domain.services import (
    OrganizationBaseService,
    OrganizationOnboardBaseService,
    get_section_config_data,
)
from core.organization.entities import OnboardUserEntity
from core.organization.models import (
    LinkedOrganizationBusinessCard,
    LinkedOrganizationDocument,
    LinkedOrganizationDocumentTextFieldData,
    LinkedOrganizations,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
)
from core.selectors import get_country_with_timezone_tax_currency
from core.services import organization_create_by_referral_org
from vendor.interface.apis import ClientVendorMapping
from vendorv2.data.models import VendorOnboardConfig
from vendorv2.data.selectors.selectors import get_vendor_onboard_organization_config, get_vendor_org_tags
from vendorv2.data.selectors.vendor_selectors import get_vendor_basic_details, get_vendor_onboard_sections_config
from vendorv2.domain.constants import get_vendor_onboard_data
from vendorv2.domain.entities import (
    VendorAddressData,
    VendorBasicDetailData,
    VendorBusinessCardData,
    VendorOnboardInBulkData,
    VendorOnboardInputData,
    VendorTagData,
    VendorTagListUpdateData,
)
from vendorv2.domain.entities.vendor_entities import VendorBusinessCardData as VendorBusinessCardDataEntity
from vendorv2.domain.services.services import ManageVendorOrgService, check_if_vendor_already_exists
from vendorv2.domain.services.user import VendorUserService
from vendorv2.interface.exceptions import VendorV2Exception, VendorV2ExceptionConstants

logger = structlog.get_logger(__name__)


def get_vendor_onboard_config(country_id: int, org_id: int) -> OrganizationCountryConfigData:
    vendor_onboard_config = VendorOnboardConfig.objects.filter(org_id=org_id).first()
    sections_config = get_vendor_onboard_sections_config(country_id=country_id)
    uid_field = get_uid_field_by_country(country_id=country_id)
    sections = []
    for section_config in sections_config:
        sections.append(
            get_section_config_data(
                section_config=section_config,
                uid_field_id=uid_field.id if uid_field else None,
                uid_required=vendor_onboard_config.is_pan_required if vendor_onboard_config else False,
                bank_details_required=vendor_onboard_config.is_bank_detail_required if vendor_onboard_config else False,
            )
        )
    return OrganizationCountryConfigData(
        sections=sections,
        uid_field=(
            OrganizationFieldData(
                id=uid_field.id,
                name=uid_field.name,
                type=uid_field.type,
                position=uid_field.position,
                is_required=vendor_onboard_config.is_pan_required if vendor_onboard_config else uid_field.is_required,
                regex=uid_field.regex,
                is_visible_on_app=uid_field.is_visible_on_app,
                is_capitalized=uid_field.is_capitalized,
            )
            if uid_field
            else None
        ),
        uid_document_id=uid_field.document_config.pk if uid_field else None,
    )


class VendorOnboardServiceV2(OrganizationOnboardBaseService):
    class VendorOnboardServiceV2Exception(BaseValidationError):
        pass

    class VendorNotFoundException(VendorOnboardServiceV2Exception):
        pass

    class InvalidUidException(VendorOnboardServiceV2Exception):
        pass

    class ClientExistsWithGivenUid(VendorOnboardServiceV2Exception):
        pass

    class VendorAlreadyExistsWithGivenUid(VendorOnboardServiceV2Exception):
        pass

    def create_linked_organizations(self, vendor_id: int, org_id: int, user_id: int) -> int:
        logger.info("Creating linked organization mappings", vendor_id=vendor_id, org_id=org_id, user_id=user_id)
        mapping = LinkedOrganizations(
            client_id=org_id,
            vendor_id=vendor_id,
            org_id=org_id,
            created_by_id=user_id,
        )
        mapping_r = LinkedOrganizations(
            client_id=org_id,
            vendor_id=vendor_id,
            org_id=vendor_id,
            created_by_id=user_id,
        )
        mapping, mapping_r = LinkedOrganizations.objects.bulk_create([mapping, mapping_r])
        logger.info("linked organization mappings created.", mapping_id=mapping.pk, mapping_r_id=mapping_r.pk)
        return mapping.pk

    def get_vendor_id_via_uid(self, uid: str, country_id: int, org_id: int) -> int:
        uid_field_config = get_uid_field_by_country(country_id=country_id)
        if uid_field_config is None:
            logger.info("uid_field not found for country", country_id=country_id)
            raise self.VendorOnboardServiceV2Exception("uid_field is not found for country")
        if uid_field_config.regex and not re.match(uid_field_config.regex, uid):
            logger.info("uid validation failed", uid=uid, regex=uid_field_config.regex)
            raise self.InvalidUidException("Please enter correct uid value")
        vendor_mapping_ids = LinkedOrganizations.objects.filter(
            client_id=org_id, org_id=org_id, deleted_at__isnull=True
        ).values_list("id", flat=True)
        if LinkedOrganizationDocumentTextFieldData.objects.filter(
            data=uid, document__mapping_id__in=vendor_mapping_ids, document__deleted_at__isnull=True
        ).exists():
            logger.info(f"Vendor already exists with given {uid_field_config.name}", uid=uid)
            raise self.VendorAlreadyExistsWithGivenUid(
                VendorV2ExceptionConstants.ALREADY_EXISTS.replace("UID", uid_field_config.name)
            )
        client_mapping_ids = LinkedOrganizations.objects.filter(
            vendor_id=org_id, org_id=org_id, deleted_at__isnull=True
        ).values_list("id", flat=True)
        if LinkedOrganizationDocumentTextFieldData.objects.filter(
            data=uid, document__mapping_id__in=client_mapping_ids, document__deleted_at__isnull=True
        ).exists():
            logger.info(
                f"Client already exists with given {uid_field_config.name}, so can't onboard as vendor", uid=uid
            )
            raise self.ClientExistsWithGivenUid(
                f"Client already exists with given {uid_field_config.name}, so can't onboard as vendor"
            )
        vendor_id = (
            OrganizationDocumentTextFieldData.objects.filter(field_config_id=uid_field_config.pk, data=uid)
            .select_related("document__organization")
            .values_list("document__organization_id", flat=True)
            .available()
            .first()
        )
        if not vendor_id:
            raise self.VendorNotFoundException(f"Vendor not found for given {uid_field_config.name}")
        try:
            check_if_vendor_already_exists(organization_id=org_id, vendor_id=vendor_id, uid_name=uid_field_config.name)
        except VendorV2Exception as e:
            raise self.VendorAlreadyExistsWithGivenUid(
                VendorV2ExceptionConstants.ALREADY_EXISTS.replace("UID", uid_field_config.name)
            ) from e
        return vendor_id

    def fill_uid_field_data(self, uid_field_data: Optional[str], country_id: int, vendor_id: int, user_id: int):
        if not uid_field_data:
            return
        field_config = get_uid_field_by_country(
            country_id=country_id,
        )
        document = OrganizationDocumentV2(
            organization_id=vendor_id,
            document_config_id=field_config.document_config_id,
            created_by_id=user_id,
        )
        document.save()
        field_data = OrganizationDocumentTextFieldData(
            field_config_id=field_config.pk,
            document_id=document.pk,
            data=uid_field_data,
            created_by_id=user_id,
        )
        field_data.save()

    def create_linked_business_card(self, business_card_data: VendorBusinessCardData, mapping_id: int, user_id: int):
        if business_card_data:
            business_card = LinkedOrganizationBusinessCard(
                mapping_id=mapping_id,
                file=business_card_data.file,
                name=business_card_data.file_name,
                created_by_id=user_id,
            )
            business_card.save()

    def create_business_card(self, business_card_data: VendorBusinessCardData, organization_id: int, user_id: int):
        if business_card_data:
            business_card = OrganizationBusinessCard(
                organization_id=organization_id,
                file=business_card_data.file,
                name=business_card_data.file_name,
                type=OrganizationDocumentChoices.BUSINESS_CARD,
                uploaded_by_id=user_id,
            )
            business_card.save()

    def onboard_vendor(self, data: VendorOnboardInputData, user_id: int, org_id: int) -> Tuple[int, int]:
        is_new_vendor = False
        if data.id:
            if not data.uid:
                raise self.VendorOnboardServiceV2Exception("Uid should be provided, because vendor is already exists")
            vendor_org_id = data.id
        else:
            vendor_org = organization_create_by_referral_org(
                name=data.name,
                org_type=OrganizationType.VENDOR,
                referral_org_id=org_id,
                referred_by_id=user_id,
                country_id=data.country_id,
                pan_number=data.uid,
            )
            vendor_org_id = vendor_org.pk
            is_new_vendor = True

        country = get_country_with_timezone_tax_currency(
            country_id=data.country_id,
        )
        currency = country.country_currency_mapping.all().first().currency
        tax_type_mapping = country.country_tax_mapping.all().first()
        tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
        timezone = country.country_timezone_mapping.all().first().timezone
        OrganizationConfig.objects.get_or_create(
            organization_id=vendor_org_id,
            defaults={
                "timezone": timezone,
                "tax_type": tax_type,
                "currency": currency,
            },
        )

        mapping_id = self.create_from_to_org_mapping(
            client_id=org_id, vendor_id=vendor_org_id, user_id=user_id, vendor_status=VendorStatusChoices.ONBOARDED
        )
        linked_mapping_id = self.create_linked_organizations(vendor_id=vendor_org_id, org_id=org_id, user_id=user_id)
        if data.addresses:
            self.create_linked_addresses(addresses_data=data.addresses, mapping_id=linked_mapping_id, user_id=user_id)
        if data.business_card:
            self.create_linked_business_card(
                business_card_data=data.business_card, mapping_id=linked_mapping_id, user_id=user_id
            )

        if is_new_vendor:
            self.create_addresses(addresses_data=data.addresses[:1], org_id=vendor_org_id, user_id=user_id)
            self.fill_uid_field_data(
                uid_field_data=data.uid, vendor_id=vendor_org_id, user_id=user_id, country_id=data.country_id
            )
            self.create_business_card(
                business_card_data=data.business_card, organization_id=vendor_org_id, user_id=user_id
            )
        return vendor_org_id, mapping_id

    def onboard_vendor_in_bulk(
        self, vendors_data: list[VendorOnboardInBulkData], org_id: int, country_id: int, user_id: int
    ) -> list[tuple]:
        status_list = []
        tags = get_vendor_org_tags(organization_id=org_id)
        current_tags = {tag.name: tag for tag in tags}
        current_tag_names = list(tags.values_list("name", flat=True))
        new_tag_created: list[str] = []
        country_mapping = {country.get("name"): country.get("id") for country in CountryListCache.get()}
        states_mapping = {
            state.name: state.pk
            for state in State.objects.filter(country_id=country_id, is_active=True).all().order_by("name")
        }
        city_mapping = {
            city.name: city.pk for city in City.objects.filter(is_active=True, is_verified=True).order_by("name")
        }
        for vendor_data in vendors_data:
            sid = transaction.savepoint()
            vendor_id = None
            try:
                if not vendor_data.uid:
                    raise self.VendorNotFoundException("Vendor uid not found")
                vendor_id = self.get_vendor_id_via_uid(uid=vendor_data.uid, country_id=country_id, org_id=org_id)
                if not ClientVendorMapping.objects.filter(org_from_id=org_id, org_to_id=vendor_id).available().exists():
                    logger.info("From to org mapping not found", org_id=org_id, vendor_id=vendor_id)
                    mapping_id = self.create_from_to_org_mapping(
                        client_id=org_id,
                        vendor_id=vendor_id,
                        user_id=user_id,
                        vendor_status=VendorStatusChoices.ONBOARDED,
                    )
                    self.create_linked_organizations(vendor_id=vendor_id, org_id=org_id, user_id=user_id)
            except self.InvalidUidException:
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Invalid Uid value",
                    )
                )
                transaction.savepoint_rollback(sid)
                continue
            except self.ClientExistsWithGivenUid:
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Client already exists with given UID value, So can't onboard as vendor",
                    )
                )
                transaction.savepoint_rollback(sid)
                continue
            except self.VendorAlreadyExistsWithGivenUid:
                transaction.savepoint_rollback(sid)
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Vendor already exists with given UID value",
                    )
                )
                continue
            except self.VendorNotFoundException:
                logger.info(
                    "Vendor not found with given UID", uid=vendor_data.uid, country_id=country_id, org_id=org_id
                )
                try:
                    addresses = []
                    if any(
                        [
                            vendor_data.base_address_line_1,
                            vendor_data.base_address_line_2,
                            vendor_data.base_city_name,
                            vendor_data.base_state_name,
                            vendor_data.base_country_name,
                            vendor_data.base_zip_code,
                        ]
                    ):
                        addresses.append(
                            OrganizationAddressUpdateData(
                                id=None,
                                address_line_1=vendor_data.base_address_line_1,
                                address_line_2=vendor_data.base_address_line_2,
                                city_id=city_mapping.get(vendor_data.base_city_name),
                                state_id=states_mapping.get(vendor_data.base_state_name),
                                country_id=country_mapping.get(vendor_data.base_country_name),
                                zip_code=vendor_data.base_zip_code,
                            ),
                        )
                    if any(
                        [
                            vendor_data.additional_address_line_1,
                            vendor_data.additional_address_line_2,
                            vendor_data.additional_city_name,
                            vendor_data.additional_state_name,
                            vendor_data.additional_country_name,
                            vendor_data.additional_zip_code,
                        ]
                    ):
                        addresses.append(
                            OrganizationAddressUpdateData(
                                id=None,
                                address_line_1=vendor_data.additional_address_line_1,
                                address_line_2=vendor_data.additional_address_line_2,
                                city_id=city_mapping.get(vendor_data.additional_city_name),
                                state_id=states_mapping.get(vendor_data.additional_state_name),
                                country_id=country_mapping.get(vendor_data.additional_country_name),
                                zip_code=vendor_data.additional_zip_code,
                            )
                        )
                    vendor_id, mapping_id = self.onboard_vendor(
                        data=VendorOnboardInputData(
                            id=vendor_id,
                            name=vendor_data.organization_name,
                            addresses=addresses,
                            country_id=country_id,
                            business_card=None,
                            uid=vendor_data.uid,
                        ),
                        user_id=user_id,
                        org_id=org_id,
                    )
                except self.VendorOnboardServiceV2Exception:
                    status_list.append(
                        (
                            vendor_data.organization_name,
                            "UID not provided for existing vendor",
                        )
                    )
                    transaction.savepoint_rollback(sid)
                    continue
            data = get_vendor_onboard_data(
                country_id=country_id,
                vendor_data=vendor_data,
            )
            try:
                VendorOrganizationService(org_id=org_id, vendor_id=vendor_id).update_sections_data(
                    data=data, user_id=user_id
                )
            except VendorOrganizationService.OrganizationSectionUpdateException as e:
                errors = []
                for error in e.error_dict.values():
                    errors.append(error[0].message)
                status_list.append((vendor_data.organization_name, errors))
                transaction.savepoint_rollback(sid)
                continue

            tag_data: list[VendorTagData] = []

            for tag in vendor_data.vendor_tags or []:
                if tag in current_tag_names:
                    tag_data.append(VendorTagData(id=current_tags.get(tag).pk, name=tag))
                else:  # Create new tag
                    tag_data.append(VendorTagData(id=None, name=tag))
                    new_tag_created.append(tag)

            updated_tags = ManageVendorOrgService.tags_update(
                cv_mapping_id=mapping_id,
                user_id=user_id,
                data=VendorTagListUpdateData(data=tag_data),
            )

            for tag in updated_tags:
                if tag.name not in current_tag_names:
                    current_tags[tag.name] = tag
                    current_tag_names.append(tag.name)

            if vendor_data.user_name or vendor_data.phone_number or vendor_data.email:
                try:
                    VendorUserService().add(
                        mapping_id=mapping_id,
                        created_by_id=user_id,
                        user=OnboardUserEntity(
                            name=vendor_data.user_name,
                            phone_number=(
                                PhoneNumber.from_string(vendor_data.phone_number) if vendor_data.phone_number else None
                            ),
                            email=vendor_data.email,
                            is_invited=False,
                        ),
                    )
                except VendorUserService.PocUserServiceException as e:
                    status_list.append((vendor_data.organization_name, e.message))
                    transaction.savepoint_rollback(sid)
                    continue
            transaction.savepoint_commit(sid)
            status_list.append((vendor_data.organization_name, "Vendor Added"))
        logger.info("Error status list.", status_list=status_list)
        return status_list


class VendorOrganizationService(OrganizationBaseService):
    def __init__(self, vendor_id: int, org_id: int):
        self.vendor_id = vendor_id
        self.org_id = org_id

    def get_basic_details(self):
        vendor_org = get_vendor_basic_details(vendor_id=self.vendor_id, onboarder_org_id=self.org_id)
        if not vendor_org:
            raise VendorOnboardServiceV2.VendorNotFoundException("Vendor not found")
        business_card = vendor_org.business_cards.all().first()
        addresses: list[VendorAddressData] = []
        for address in vendor_org.vendor_addresses.all():
            addresses.append(
                VendorAddressData(
                    id=address.pk,
                    address_line_1=address.address_line_1,
                    address_line_2=address.address_line_2,
                    city=(CityData(id=address.city.pk, name=address.city.name) if address.city else None),
                    state=(StateData(id=address.state.pk, name=address.state.name) if address.state else None),
                    country=(
                        CountryData(id=address.country.pk, name=address.country.name) if address.country else None
                    ),
                    zip_code=address.zip_code,
                    value=address.full_address,
                )
            )
        return VendorBasicDetailData(
            id=vendor_org.pk,
            name=vendor_org.name,
            logo=vendor_org.logo.url if vendor_org.logo.name else None,
            business_card=(
                VendorBusinessCardDataEntity(
                    id=business_card.pk,
                    file=business_card.file.url if business_card.file.name else None,
                    file_name=business_card.name,
                )
                if business_card
                else None
            ),
            addresses=addresses,
            country=CountryData(id=vendor_org.country.pk, name=vendor_org.country.name),
        )

    def update_basic_details(self, data: OrganizationBasicDetailsUpdateData, user_id: int):
        mapping = get_mapping_for_vendor(org_id=self.org_id, vendor_id=self.vendor_id)
        return super()._update_basic_details(data=data, user_id=user_id, mapping=mapping)

    def _get_section_details(self, section_type: OrganizationSectionTypeChoices) -> OrganizationSectionData:
        vendor_onboard_config = get_vendor_onboard_organization_config(org_id=self.org_id)
        uid_field_config_id = (
            Organization.objects.filter(id=self.org_id).values_list("country__uid_field_id", flat=True).first()
        )
        section_config = get_organization_sections_config(org_id=self.vendor_id).filter(type=section_type).first()
        mapping_id = get_mapping_for_vendor(org_id=self.org_id, vendor_id=self.vendor_id)
        org_docs = get_linked_organization_documents_by_section(mapping_id=mapping_id, section_type=section_type)
        doc_config_org_doc_mapping: dict[int, list[LinkedOrganizationDocument]] = defaultdict(list)
        for org_doc in org_docs:
            doc_config_org_doc_mapping[org_doc.document_config_id].append(org_doc)

        org_doc_id_field_config_id_value_mapping: dict[Tuple[int, int], DocumentFieldValueBaseData] = {}
        for org_doc in org_docs:
            for field_data in org_doc.text_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentTextFieldValueData(id=field_data.pk, data=field_data.data)
                )

            for field_data in org_doc.file_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentFileFieldValueData(
                        id=field_data.pk,
                        data=(
                            FileData(file_name=field_data.file_name, file=field_data.file.url)
                            if field_data.file_name and field_data.file.name
                            else None
                        ),
                    )
                )

            for field_data in org_doc.state_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentStateFieldValueData(
                        id=field_data.pk, data=StateData(id=field_data.state.pk, name=field_data.state.name)
                    )
                )

            for field_data in org_doc.date_field_data.all():
                org_doc_id_field_config_id_value_mapping[(org_doc.pk, field_data.field_config_id)] = (
                    DocumentDateFieldValueData(id=field_data.pk, data=field_data.data)
                )

        documents: list[OrganizationDocumentData] = []
        for doc_config in section_config.documents_config.all():
            fields: list[OrganizationFieldData] = []
            fields_data_list: list[dict[int, DocumentFieldValueBaseData]] = []

            for field_config in doc_config.fields_config.all():
                field = OrganizationFieldData(
                    id=field_config.pk,
                    name=field_config.name,
                    type=field_config.type,
                    position=field_config.position,
                    is_required=(
                        field_config.is_required
                        if uid_field_config_id != field_config.pk
                        else vendor_onboard_config.is_pan_required
                    ),
                    regex=field_config.regex,
                    is_visible_on_app=field_config.is_visible_on_app,
                    is_capitalized=field_config.is_capitalized,
                )
                fields.append(field)

            for org_doc in doc_config_org_doc_mapping[doc_config.pk]:
                field_data_dict: dict[Union[str, int], Union[int, DocumentFieldValueBaseData]] = defaultdict()
                for field_config in doc_config.fields_config.all():
                    if (org_doc.pk, field_config.pk) in org_doc_id_field_config_id_value_mapping:
                        field_data_dict[field_config.pk] = org_doc_id_field_config_id_value_mapping[
                            (org_doc.pk, field_config.pk)
                        ]
                field_data_dict["id"] = org_doc.pk
                fields_data_list.append(field_data_dict)

            document = OrganizationDocumentData(
                id=doc_config.pk,
                name=doc_config.name,
                multiple_allowed=doc_config.multiple_allowed,
                position=doc_config.position,
                is_required=doc_config.is_required,
                fields=fields,
                fields_data=fields_data_list,
                is_visible_on_app=doc_config.is_visible_on_app,
            )
            documents.append(document)

        section = OrganizationSectionData(
            id=section_config.pk,
            name=section_config.name,
            position=section_config.position,
            type=section_config.type,
            documents=documents,
        )
        return section

    def get_kyc_details(self) -> OrganizationSectionData:
        return self._get_section_details(section_type=OrganizationSectionTypeChoices.KYC_DETAILS)

    def get_bank_details(self) -> OrganizationSectionData:
        return self._get_section_details(section_type=OrganizationSectionTypeChoices.BANK_DETAILS)

    def update_sections_data(self, data: OrganizationSectionUpdateData, user_id: int):
        logger.info(
            "Updating vendor organization section data", org_id=self.org_id, data=data, vendor_id=self.vendor_id
        )
        mapping = get_mapping_for_vendor(org_id=self.org_id, vendor_id=self.vendor_id)
        self.update_linked_organization_section_data(data=data, mapping=mapping, user_id=user_id, org_id=self.org_id)
        logger.info("Vendor sections updated successfully.")
