from typing import Optional, <PERSON><PERSON>

import structlog
from django.db import transaction
from phonenumber_field.phonenumber import PhoneNumber

from common.choices import OrganizationType, VendorStatusChoices
from common.exceptions import BaseValidationError
from core.caches import CountryListCache
from core.entities import OrgUserEntity
from core.models import City, Organization, OrganizationConfig, State
from core.organization.data.choices import OrganizationSectionTypeChoices
from core.organization.data.models import (
    LinkedOrganizations,
    OrganizationDocumentTextFieldData,
    OrganizationDocumentV2,
)
from core.organization.data.selectors import get_uid_field_by_country
from core.organization.domain.entities import (
    OrganizationAddressUpdateData,
    OrganizationBusinessCardUpdateData,
    OrganizationCountryConfigData,
    OrganizationFieldData,
    OrganizationSectionData,
)
from core.organization.domain.services.linked_organization import LinkedOrganizationService
from core.organization.domain.services.onboard import OrganizationOnboardBaseService
from core.organization.domain.services.organization import get_section_config_data
from core.organization.entities import OnboardUserEntity
from core.selectors import get_country_with_timezone_tax_currency
from core.services import organization_create_by_referral_org
from vendor.interface.apis import ClientVendorMapping
from vendorv2.data.models import VendorOnboardConfig
from vendorv2.data.repositories import VendorLinkedOrganizationRepo
from vendorv2.data.selectors.selectors import get_vendor_org_tags
from vendorv2.data.selectors.vendor_selectors import get_vendor_onboard_sections_config
from vendorv2.domain.abstract_repos import VendorLinkedOrganizationAbstractRepo
from vendorv2.domain.constants import get_vendor_onboard_data
from vendorv2.domain.entities import (
    VendorOnboardInBulkData,
    VendorOnboardInputData,
    VendorTagData,
    VendorTagListUpdateData,
)
from vendorv2.domain.services.services import ManageVendorOrgService, check_if_vendor_already_exists
from vendorv2.domain.services.user import VendorUserService
from vendorv2.interface.exceptions import VendorV2Exception, VendorV2ExceptionConstants

logger = structlog.get_logger(__name__)


def get_vendor_onboard_config(country_id: int, org_id: int) -> OrganizationCountryConfigData:
    vendor_onboard_config = VendorOnboardConfig.objects.filter(org_id=org_id).first()
    sections_config = get_vendor_onboard_sections_config(country_id=country_id)
    uid_field = get_uid_field_by_country(country_id=country_id)
    sections = []
    for section_config in sections_config:
        sections.append(
            get_section_config_data(
                section_config=section_config,
                uid_field_id=uid_field.id if uid_field else None,
                uid_required=vendor_onboard_config.is_pan_required if vendor_onboard_config else False,
                bank_details_required=vendor_onboard_config.is_bank_detail_required if vendor_onboard_config else False,
            )
        )
    return OrganizationCountryConfigData(
        sections=sections,
        uid_field=(
            OrganizationFieldData(
                id=uid_field.id,
                name=uid_field.name,
                type=uid_field.type,
                position=uid_field.position,
                is_required=vendor_onboard_config.is_pan_required if vendor_onboard_config else uid_field.is_required,
                regex=uid_field.regex,
                is_visible_on_app=uid_field.is_visible_on_app,
                is_capitalized=uid_field.is_capitalized,
            )
            if uid_field
            else None
        ),
        uid_document_id=uid_field.document_config.pk if uid_field else None,
    )


class VendorOnboardServiceV2(OrganizationOnboardBaseService):
    class VendorOnboardServiceV2Exception(BaseValidationError):
        pass

    class VendorNotFoundException(VendorOnboardServiceV2Exception):
        pass

    class ClientExistsWithGivenUid(VendorOnboardServiceV2Exception):
        pass

    class VendorAlreadyExistsWithGivenUid(VendorOnboardServiceV2Exception):
        pass

    def __init__(self, user_entity: OrgUserEntity):
        self.user_id = user_entity.user_id
        self.org_id = user_entity.org_id

    def create_linked_organizations(self, vendor_id: int):
        logger.info(
            "Creating linked organization mappings", vendor_id=vendor_id, org_id=self.org_id, user_id=self.user_id
        )
        mapping = LinkedOrganizations(
            client_id=self.org_id,
            vendor_id=vendor_id,
            org_id=self.org_id,
            created_by_id=self.user_id,
        )
        mapping_r = LinkedOrganizations(
            client_id=self.org_id,
            vendor_id=vendor_id,
            org_id=vendor_id,
            created_by_id=self.user_id,
        )
        mapping, mapping_r = LinkedOrganizations.objects.bulk_create([mapping, mapping_r])
        logger.info("linked organization mappings created.", mapping_id=mapping.pk, mapping_r_id=mapping_r.pk)
        return mapping, mapping_r

    def get_vendor_id_via_uid(self, uid: str, country_id: int) -> int:
        uid_field_config = self.get_uid_field(uid=uid, country_id=country_id)

        # TODO: if we need to check UID in linked vendor and client, we can uncomment the following lines
        # try:
        #     self.check_uid_in_linked_vendor(uid=uid, org_id=self.org_id)
        # except self.UIDAlreadyExists as e:
        #     logger.info(f"Vendor already exists with given {uid_field_config.name}", uid=uid)
        #     raise self.VendorAlreadyExistsWithGivenUid(
        #         VendorV2ExceptionConstants.ALREADY_EXISTS.replace("UID", uid_field_config.name)
        #     ) from e

        # try:
        #     self.check_uid_in_linked_client(uid=uid, org_id=self.org_id)
        # except self.UIDAlreadyExists as e:
        #     logger.info(f"Client already exists with given {uid_field_config.name}", uid=uid)
        #     raise self.ClientExistsWithGivenUid(
        #         f"Client already exists with given {uid_field_config.name}, so can't onboard as vendor"
        #     ) from e

        vendor_id = self.check_uid_in_organization(uid=uid)

        if not vendor_id:
            raise self.VendorNotFoundException(f"Vendor not found for given {uid_field_config.name}")
        try:
            check_if_vendor_already_exists(
                organization_id=self.org_id, vendor_id=vendor_id, uid_name=uid_field_config.name
            )
        except VendorV2Exception as e:
            raise self.VendorAlreadyExistsWithGivenUid(
                VendorV2ExceptionConstants.ALREADY_EXISTS.replace("UID", uid_field_config.name)
            ) from e

        return vendor_id

    def fill_uid_field_data(
        self,
        uid_field_data: Optional[str],
        country_id: int,
        vendor_id: int,
        billing_entity_id: int,
    ):
        if not uid_field_data:
            return
        field_config = get_uid_field_by_country(
            country_id=country_id,
        )
        document = OrganizationDocumentV2(
            organization_id=vendor_id,
            document_config_id=field_config.document_config_id,
            created_by_id=self.user_id,
            billing_entity_id=billing_entity_id,
        )
        document.save()
        field_data = OrganizationDocumentTextFieldData(
            field_config_id=field_config.pk,
            document_id=document.pk,
            data=uid_field_data,
            created_by_id=self.user_id,
        )
        field_data.save()

    def onboard_vendor(self, data: VendorOnboardInputData) -> Tuple[int, int]:
        is_new_vendor = False
        if data.id:
            if not data.uid:
                raise self.VendorOnboardServiceV2Exception("Uid should be provided, because vendor is already exists")
            vendor_org_id = data.id
        else:
            vendor_org = organization_create_by_referral_org(
                name=data.name,
                org_type=OrganizationType.VENDOR,
                referral_org_id=self.org_id,
                referred_by_id=self.user_id,
                country_id=data.country_id,
                pan_number=data.uid,
                uid=data.uid,
            )
            vendor_org_id = vendor_org.pk
            is_new_vendor = True

        country = get_country_with_timezone_tax_currency(
            country_id=data.country_id,
        )
        currency = country.country_currency_mapping.all().first().currency
        tax_type_mapping = country.country_tax_mapping.all().first()
        tax_type = tax_type_mapping.tax_type if tax_type_mapping else None
        timezone = country.country_timezone_mapping.all().first().timezone
        OrganizationConfig.objects.get_or_create(
            organization_id=vendor_org_id,
            defaults={
                "timezone": timezone,
                "tax_type": tax_type,
                "currency": currency,
            },
        )

        mapping_id = self.create_from_to_org_mapping(
            client_id=self.org_id,
            vendor_id=vendor_org_id,
            user_id=self.user_id,
            vendor_status=VendorStatusChoices.ONBOARDED,
        )
        linked_mapping, reverse_linked_mapping = self.create_linked_organizations(vendor_id=vendor_org_id)
        # Create linked billing entity for the vendor
        linked_mapping_id = linked_mapping.pk
        linked_billing_entity_id = self.create_linked_billing_entity(
            mapping_id=linked_mapping_id,
            name=data.name,
            user_id=self.user_id,
            country_id=country.pk,
        )
        linked_mapping.primary_billing_entity_id = linked_billing_entity_id
        linked_mapping.save()

        # create reverse linked billing entity for the client
        current_org = Organization.objects.get(id=self.org_id)
        reverse_linked_billing_entity_id = self.create_linked_billing_entity(
            mapping_id=reverse_linked_mapping.pk,
            name=current_org.name,
            logo=current_org.logo,
            user_id=self.user_id,
            country_id=country.pk,
        )
        reverse_linked_mapping.primary_billing_entity_id = reverse_linked_billing_entity_id
        reverse_linked_mapping.save()

        if data.addresses:
            self.create_linked_addresses(
                addresses_data=data.addresses,
                mapping_id=linked_mapping_id,
                user_id=self.user_id,
                billing_entity_id=linked_billing_entity_id,
            )
        if data.business_card:
            self.create_linked_business_card(
                data=OrganizationBusinessCardUpdateData(
                    id=data.business_card.id,
                    file=data.business_card.file,
                    name=data.business_card.file_name,
                ),
                mapping_id=linked_mapping_id,
                user_id=self.user_id,
                billing_entity_id=linked_billing_entity_id,
            )

        if is_new_vendor:
            billing_entity_id = self.create_billing_entity(
                org_id=vendor_org_id,
                name=data.name,
                country_id=data.country_id,
                user_id=self.user_id,
            )

            self.create_addresses(
                addresses_data=data.addresses[:1],
                org_id=vendor_org_id,
                user_id=self.user_id,
                billing_entity_id=billing_entity_id,
            )
            self.fill_uid_field_data(
                uid_field_data=data.uid,
                vendor_id=vendor_org_id,
                country_id=data.country_id,
                billing_entity_id=billing_entity_id,
            )

            if data.business_card:
                self.create_business_card(
                    data=OrganizationBusinessCardUpdateData(
                        id=data.business_card.id,
                        file=data.business_card.file,
                        name=data.business_card.file_name,
                    ),
                    org_id=vendor_org_id,
                    user_id=self.user_id,
                    billing_entity_id=billing_entity_id,
                )
        return vendor_org_id, mapping_id

    def onboard_vendor_in_bulk(
        self,
        vendors_data: list[VendorOnboardInBulkData],
        country_id: int,
    ) -> list[tuple]:
        status_list = []
        tags = get_vendor_org_tags(organization_id=self.org_id)
        current_tags = {tag.name: tag for tag in tags}
        current_tag_names = list(tags.values_list("name", flat=True))
        new_tag_created: list[str] = []
        country_mapping = {country.get("name"): country.get("id") for country in CountryListCache.get()}
        states_mapping = {
            state.name: state.pk
            for state in State.objects.filter(country_id=country_id, is_active=True).all().order_by("name")
        }
        city_mapping = {
            city.name: city.pk for city in City.objects.filter(is_active=True, is_verified=True).order_by("name")
        }

        user_entity = OrgUserEntity(user_id=self.user_id, org_id=self.org_id)

        for vendor_data in vendors_data:
            sid = transaction.savepoint()
            vendor_id = None
            try:
                if not vendor_data.uid:
                    raise self.VendorNotFoundException("Vendor uid not found")
                vendor_id = self.get_vendor_id_via_uid(uid=vendor_data.uid, country_id=country_id)
                if (
                    not ClientVendorMapping.objects.filter(org_from_id=self.org_id, org_to_id=vendor_id)
                    .available()
                    .exists()
                ):
                    logger.info("From to org mapping not found", org_id=self.org_id, vendor_id=vendor_id)
                    mapping_id = self.create_from_to_org_mapping(
                        client_id=self.org_id,
                        vendor_id=vendor_id,
                        user_id=self.user_id,
                        vendor_status=VendorStatusChoices.ONBOARDED,
                    )
                    self.create_linked_organizations(vendor_id=vendor_id)
            except self.InvalidUidException:
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Invalid Uid value",
                    )
                )
                transaction.savepoint_rollback(sid)
                continue
            except self.ClientExistsWithGivenUid:
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Client already exists with given UID value, So can't onboard as vendor",
                    )
                )
                transaction.savepoint_rollback(sid)
                continue
            except self.VendorAlreadyExistsWithGivenUid:
                transaction.savepoint_rollback(sid)
                status_list.append(
                    (
                        vendor_data.organization_name,
                        "Vendor already exists with given UID value",
                    )
                )
                continue
            except self.VendorNotFoundException:
                logger.info(
                    "Vendor not found with given UID", uid=vendor_data.uid, country_id=country_id, org_id=self.org_id
                )
                try:
                    addresses = []
                    if any(
                        [
                            vendor_data.base_address_line_1,
                            vendor_data.base_address_line_2,
                            vendor_data.base_city_name,
                            vendor_data.base_state_name,
                            vendor_data.base_country_name,
                            vendor_data.base_zip_code,
                        ]
                    ):
                        addresses.append(
                            OrganizationAddressUpdateData(
                                id=None,
                                address_line_1=vendor_data.base_address_line_1,
                                address_line_2=vendor_data.base_address_line_2,
                                city_id=city_mapping.get(vendor_data.base_city_name),
                                state_id=states_mapping.get(vendor_data.base_state_name),
                                country_id=country_mapping.get(vendor_data.base_country_name),
                                zip_code=vendor_data.base_zip_code,
                            ),
                        )
                    if any(
                        [
                            vendor_data.additional_address_line_1,
                            vendor_data.additional_address_line_2,
                            vendor_data.additional_city_name,
                            vendor_data.additional_state_name,
                            vendor_data.additional_country_name,
                            vendor_data.additional_zip_code,
                        ]
                    ):
                        addresses.append(
                            OrganizationAddressUpdateData(
                                id=None,
                                address_line_1=vendor_data.additional_address_line_1,
                                address_line_2=vendor_data.additional_address_line_2,
                                city_id=city_mapping.get(vendor_data.additional_city_name),
                                state_id=states_mapping.get(vendor_data.additional_state_name),
                                country_id=country_mapping.get(vendor_data.additional_country_name),
                                zip_code=vendor_data.additional_zip_code,
                            )
                        )
                    vendor_id, mapping_id = self.onboard_vendor(
                        data=VendorOnboardInputData(
                            id=vendor_id,
                            name=vendor_data.organization_name,
                            addresses=addresses,
                            country_id=country_id,
                            business_card=None,
                            uid=vendor_data.uid,
                        ),
                    )
                except self.VendorOnboardServiceV2Exception:
                    status_list.append(
                        (
                            vendor_data.organization_name,
                            "UID not provided for existing vendor",
                        )
                    )
                    transaction.savepoint_rollback(sid)
                    continue
            data = get_vendor_onboard_data(
                country_id=country_id,
                vendor_data=vendor_data,
            )
            try:
                VendorLinkedOrganizationService(
                    user_entity=user_entity,
                    vendor_id=vendor_id,
                    repo=VendorLinkedOrganizationRepo(user_entity=user_entity, vendor_id=vendor_id),
                ).update_sections_data(data=data)
            except VendorLinkedOrganizationService.SectionUpdateException as e:
                errors = []
                for error in e.error_dict.values():
                    errors.append(error[0].message)
                status_list.append((vendor_data.organization_name, errors))
                transaction.savepoint_rollback(sid)
                continue

            tag_data: list[VendorTagData] = []

            for tag in vendor_data.vendor_tags or []:
                if tag in current_tag_names:
                    tag_data.append(VendorTagData(id=current_tags.get(tag).pk, name=tag))
                else:  # Create new tag
                    tag_data.append(VendorTagData(id=None, name=tag))
                    new_tag_created.append(tag)

            updated_tags = ManageVendorOrgService.tags_update(
                cv_mapping_id=mapping_id,
                user_id=self.user_id,
                data=VendorTagListUpdateData(data=tag_data),
            )

            for tag in updated_tags:
                if tag.name not in current_tag_names:
                    current_tags[tag.name] = tag
                    current_tag_names.append(tag.name)

            if vendor_data.user_name or vendor_data.phone_number or vendor_data.email:
                try:
                    VendorUserService().add(
                        mapping_id=mapping_id,
                        created_by_id=self.user_id,
                        user=OnboardUserEntity(
                            name=vendor_data.user_name,
                            phone_number=(
                                PhoneNumber.from_string(vendor_data.phone_number) if vendor_data.phone_number else None
                            ),
                            email=vendor_data.email,
                            is_invited=False,
                        ),
                    )
                except VendorUserService.PocUserServiceException as e:
                    status_list.append((vendor_data.organization_name, e.message))
                    transaction.savepoint_rollback(sid)
                    continue
            transaction.savepoint_commit(sid)
            status_list.append((vendor_data.organization_name, "Vendor Added"))
        logger.info("Error status list.", status_list=status_list)
        return status_list

    def get_onboard_config(self, country_id: int, is_app: bool) -> OrganizationCountryConfigData:
        vendor_onboard_config = VendorOnboardConfig.objects.filter(org_id=self.org_id).first()
        sections_config = self.get_org_sections_config(country_id=country_id)

        if is_app:
            sections_config = sections_config.filter(
                type__in=[
                    OrganizationSectionTypeChoices.KYC_DETAILS,
                    OrganizationSectionTypeChoices.BANK_DETAILS,
                ]
            )

        uid_field = get_uid_field_by_country(country_id=country_id)
        sections = []
        for section_config in sections_config:
            sections.append(
                get_section_config_data(
                    section_config=section_config,
                    uid_field_id=uid_field.id if uid_field else None,
                    uid_required=vendor_onboard_config.is_pan_required if vendor_onboard_config else False,
                    bank_details_required=vendor_onboard_config.is_bank_detail_required
                    if vendor_onboard_config
                    else False,
                )
            )
        return OrganizationCountryConfigData(
            sections=sections,
            uid_field=(
                OrganizationFieldData(
                    id=uid_field.id,
                    name=uid_field.name,
                    type=uid_field.type,
                    position=uid_field.position,
                    is_required=vendor_onboard_config.is_pan_required
                    if vendor_onboard_config
                    else uid_field.is_required,
                    regex=uid_field.regex,
                    is_visible_on_app=uid_field.is_visible_on_app,
                    is_capitalized=uid_field.is_capitalized,
                )
                if uid_field
                else None
            ),
            uid_document_id=uid_field.document_config.pk if uid_field else None,
        )


class VendorLinkedOrganizationService(LinkedOrganizationService):
    def __init__(
        self,
        user_entity: OrgUserEntity,
        vendor_id: int,
        repo: VendorLinkedOrganizationAbstractRepo,
        billing_entity_id: int | None = None,
    ):
        super().__init__(user_entity=user_entity, repo=repo, billing_entity_id=billing_entity_id)
        self.vendor_id = vendor_id

    def _is_pan_required(self, data: OrganizationSectionData) -> OrganizationSectionData:
        vendor_onboard_config = self.repo.get_onboard_organization_config()
        uid_field_config_id = self.repo.get_uid_field_config_id()

        for section in data.documents:
            for field in section.fields:
                if uid_field_config_id == field.id:
                    field.is_required = vendor_onboard_config.is_pan_required

        return data

    def get_bank_details(self) -> OrganizationSectionData:
        data = super().get_bank_details()
        return self._is_pan_required(data)

    def get_kyc_details(self) -> OrganizationSectionData:
        data = super().get_kyc_details()
        return self._is_pan_required(data)

    def get_other_details(self) -> OrganizationSectionData:
        data = super().get_other_details()
        return self._is_pan_required(data)

    def get_document_details(self):
        return self.repo.get_document_details()

    def get_tag_details(self):
        return self.repo.get_tag_details()
