from typing import Dict, List, Optional, Tuple

import structlog
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.http import Http404
from django.utils import timezone

from authorization.domain.constants import Permissions
from common.choices import OrganizationType, VendorStatusChoices, VendorStatusColorCode
from common.services import (
    get_first_name_last_name,
    model_update,
    nested_object_segregation,
)
from common.utils import padding_for_serial_number
from core.choices import OrganizationDocumentChoices
from core.entities import (
    OrganizationAadharUpdateData,
    OrganizationBasicDetailUpdateData,
    OrganizationKYCUpdateData,
    OrganizationMSMEUpdateData,
)
from core.exceptions import OrgGSTNumberAndStateUniqueException
from core.models import (
    Organization,
    OrganizationAddress,
    OrganizationDocument,
    OrganizationGSTNumber,
    User,
    VendorOrgTag,
)
from core.organization.entities import OnboardUserEntity
from core.organization.manage_cv_services import (
    ManageOrgService,
    OtherDocumentService,
    is_organization_editable,
    organization_addresses_update_and_create,
    organization_business_card_add_and_delete,
    organization_name_update,
)
from core.services import (
    OrganizationPanService,
    add_organization_legal_name,
    organization_create_by_referral_org,
)
from core.user.entities import UserCreateUpdateData
from core.user.services import UserCreateService
from vendor.data.models import Vendor
from vendorv2.data.models import (
    BlacklistReasonHistory,
    ClientVendorMapping,
    VendorBankDetail,
    VendorOnboardConfig,
    VendorOtherDetailDocument,
)
from vendorv2.data.selectors.selectors import (
    get_vendor_basic_and_kyc_detail,
    get_vendor_from_org_id,
    get_vendor_mapping_using_client_and_vendor_id,
    get_vendor_or_404,
    get_vendor_org_tags,
    get_vendor_using_pan,
)
from vendorv2.domain.constants import InviteStatus
from vendorv2.domain.entities import (
    VendorAddressData,
    VendorBankListUpdateData,
    VendorBankUpdateData,
    VendorBulkEntity,
    VendorCreateData,
    VendorGSTData,
    VendorKycDetailAddEntity,
    VendorOnboardConfigData,
    VendorOrgCreateData,
    VendorPOCEntity,
    VendorTagData,
    VendorTagListUpdateData,
)
from vendorv2.domain.services.user import VendorUserService
from vendorv2.interface.exceptions import VendorV2Exception, VendorV2ExceptionConstants

logger = structlog.get_logger(__name__)


def create_vendor_organization(
    organization_id: int,
    serial_number: int,
    code_prefix: str,
    pan_number: Optional[str],
    created_by_id: int,
    aadhar_number: Optional[str] = None,
    msme_id: Optional[str] = None,
    referal_organization_id: Optional[int] = None,
):
    vendor_org = Vendor(
        organization_id=organization_id,
        code=f"{code_prefix}{padding_for_serial_number(serial_number, 4)}",
        serial_number=serial_number,
        pan_number=pan_number,
        aadhar_number=aadhar_number,
        created_by_id=created_by_id,
        msme_id=msme_id,
        referal_organization_id=referal_organization_id,
    )
    # vendor_org.full_clean(exclude=["msme_id", "pan_number"])
    vendor_org.save()
    return vendor_org


def create_client_vendor_mapping(
    created_by_id: int,
    user_organization_id: int,
    vendor_org_id: int,
    poc_user_id: int,
    is_invited: bool,
):
    client_vendor_mapping = ClientVendorMapping(
        created_by_id=created_by_id,
        client_id=user_organization_id,
        vendor_id=vendor_org_id,
        invited_by_org_id=user_organization_id,
        vendor_poc_id=poc_user_id,
        is_invited=is_invited,
    )
    client_vendor_mapping.full_clean()
    client_vendor_mapping.save()
    return client_vendor_mapping


def create_client_vendor_mapping_v2(
    *,
    created_by_id: int,
    client_id: int,
    vendor_id: int,
    is_invited: bool,
    invited_by_org_id: int,
):
    client_vendor_mapping = ClientVendorMapping(
        created_by_id=created_by_id,
        client_id=client_id,
        vendor_id=vendor_id,
        invited_by_org_id=invited_by_org_id,
        is_invited=is_invited,
        vendor_status=VendorStatusChoices.ONBOARDED.value,
    )
    client_vendor_mapping.full_clean()
    client_vendor_mapping.save()
    return client_vendor_mapping


def create_poc_user(
    poc_data: VendorPOCEntity,
    created_by_id: int,
    vendor_org_id: int,
    is_new_org: bool,
):
    first_name, last_name = get_first_name_last_name(full_name=poc_data.name)
    user_data = UserCreateUpdateData(
        first_name=first_name,
        last_name=last_name,
        phone_number=poc_data.phone_number,
        email=poc_data.email,
        role_id=None,
        default_project_role_id=None,
        photo=None,
        is_active=True,
    )

    user, _ = UserCreateService().create(
        data=user_data,
        joined_by_id=created_by_id,
        org_id=vendor_org_id,
        is_admin=True if is_new_org else False,
        is_verified=True,
    )

    return user


def check_if_vendor_already_exists(organization_id: int, vendor_id: int, uid_name: Optional[str] = None):
    if get_vendor_mapping_using_client_and_vendor_id(organization_id=organization_id, vendor_id=vendor_id).exists():
        raise VendorV2Exception(
            VendorV2ExceptionConstants.ALREADY_EXISTS.replace("UID", uid_name if uid_name else "UID")
        )
    return True


def add_bank_details(bank_details, vendor_id: int, user_id: int):
    vendor_bank_detail_list = []
    for bank_detail in bank_details:
        vendor_bank_detail = VendorBankDetail(
            vendor_id=vendor_id,
            account_holder_name=bank_detail["account_holder_name"],
            bank_name=bank_detail["bank_name"],
            account_number=bank_detail["number"],
            ifsc_code=bank_detail["ifsc"],
            cheque_file=bank_detail["cheque"]["url"],
            cheque_file_name=bank_detail["cheque"]["name"],
            created_by_id=user_id,
        )
        vendor_bank_detail_list.append(vendor_bank_detail)
    return VendorBankDetail.objects.bulk_create(objs=vendor_bank_detail_list)


def create_bank_detail_copy_for_vendor_org(objs: List[VendorBankDetail]):
    bank_detail_list = []
    for bank_detail in objs:
        if bank_detail.client_id is not None:
            bank_detail_obj = VendorBankDetail(
                vendor_id=bank_detail.vendor_id,
                client_id=None,
                account_holder_name=bank_detail.account_holder_name,
                bank_name=bank_detail.bank_name,
                account_number=bank_detail.account_number,
                ifsc_code=bank_detail.ifsc_code,
                cheque_file=bank_detail.cheque_file,
                cheque_file_name=bank_detail.cheque_file_name,
                created_by_id=bank_detail.created_by_id,
            )
            bank_detail_list.append(bank_detail_obj)
    return VendorBankDetail.objects.bulk_create(objs=bank_detail_list)


def create_bank_detail_copy_for_referral_org(objs: List[VendorBankDetail], referral_org_id: Optional[int]):
    if not referral_org_id:
        return []
    bank_detail_list = []
    for bank_detail in objs:
        if bank_detail.client_id is None:
            bank_detail_obj = VendorBankDetail(
                vendor_id=bank_detail.vendor_id,
                client_id=referral_org_id,
                account_holder_name=bank_detail.account_holder_name,
                bank_name=bank_detail.bank_name,
                account_number=bank_detail.account_number,
                ifsc_code=bank_detail.ifsc_code,
                cheque_file=bank_detail.cheque_file,
                cheque_file_name=bank_detail.cheque_file_name,
                created_by_id=bank_detail.created_by_id,
            )
            bank_detail_list.append(bank_detail_obj)
    return VendorBankDetail.objects.bulk_create(objs=bank_detail_list)


def add_business_card(business_card, vendor_id: int, user_id: int):
    vendor_business_card = OrganizationDocument(
        organization_id=vendor_id,
        file=business_card["url"],
        name=business_card["name"],
        type=OrganizationDocumentChoices.BUSINESS_CARD,
        uploaded_by_id=user_id,
    )
    vendor_business_card.full_clean()
    vendor_business_card.save()
    return vendor_business_card


def add_pan_msme_number(pan_number: str, msme_number: str, vendor_id: int, user_id: int):
    vendor = get_vendor_from_org_id(organization_id=vendor_id)
    if vendor.pan_number is None or vendor.pan_number == pan_number:
        data = dict()
        data["pan_number"] = pan_number
        fields = ["pan_number"]
        if msme_number:
            data["msme_id"] = msme_number
            fields.append("msme_id")

        updated_vendor, _, _ = model_update(instance=vendor, fields=fields, data=data, updated_by_id=user_id)
        return updated_vendor
    raise ValidationError("Please contact your client for mismatching PAN number")


def add_kyc_documents(pan, msme, gst, vendor_id: int, user_id: int):
    add_pan_msme_number(pan_number=pan["number"], msme_number=msme["number"], vendor_id=vendor_id, user_id=user_id)
    vendor_docs = []
    vendor_gst_number_list = []
    vendor_docs.append(
        OrganizationDocument(
            organization_id=vendor_id,
            file=pan["url"],
            name=pan["name"],
            type=OrganizationDocumentChoices.PAN,
            uploaded_by_id=user_id,
        )
    )
    if msme["number"] and msme["url"] and msme["name"]:
        vendor_docs.append(
            OrganizationDocument(
                organization_id=vendor_id,
                file=msme["url"],
                name=msme["name"],
                type=OrganizationDocumentChoices.MSME_DOC,
                uploaded_by_id=user_id,
            )
        )
    OrganizationDocument.objects.bulk_create(objs=vendor_docs)
    for gst_object in gst:
        if gst_object["number"] and gst_object["url"] and gst_object["name"]:
            vendor_gst_number_list.append(
                OrganizationGSTNumber(
                    vendor_id=vendor_id,
                    gst_number=gst_object["number"],
                    file=gst_object["url"],
                    name=gst_object["name"],
                    created_by_id=user_id,
                )
            )
    OrganizationGSTNumber.objects.bulk_create(objs=vendor_gst_number_list)


def add_org_address(organization_address_list, organization_id: int, user_id: int):
    organization_address_obj_list = []
    for organization_address in organization_address_list:
        organization_address = OrganizationAddress(
            organization_id=organization_id,
            address_line_one=organization_address["address_line_one"],
            address_line_two=organization_address["address_line_two"],
            city=organization_address["city"],
            state=organization_address["state"],
            country=organization_address["country"],
            pincode=organization_address["pincode"],
        )
        organization_address_obj_list.append(organization_address)
    return OrganizationAddress.objects.bulk_create(objs=organization_address_obj_list)


def submit_kyc_details(data: dict, user_id: int, organization_id: int, vendor_id: int):
    add_bank_details(bank_details=data.get("bank_details"), vendor_id=vendor_id, user_id=user_id)
    add_business_card(business_card=data.get("business_card"), vendor_id=vendor_id, user_id=user_id)
    add_kyc_documents(
        pan=data.get("pan"), gst=data.get("gst"), msme=data.get("msme"), vendor_id=vendor_id, user_id=user_id
    )
    add_org_address(
        organization_address_list=data.get("organization_address"), organization_id=organization_id, user_id=user_id
    )
    add_organization_legal_name(company_name=data.get("organization_name"), organization_id=organization_id)


def mark_kyc_filled(organization_id: int, vendor: Vendor, user_id: int):
    model_update(instance=vendor, fields=["is_kyc_filled"], data={"is_kyc_filled": True}, updated_by_id=user_id)
    return True


def delete_vendor(vendor_id: int, user_id: int, organization_id: int):
    get_vendor_mapping_using_client_and_vendor_id(vendor_id=vendor_id, organization_id=organization_id).soft_delete(
        user_id
    )


def get_vendor_kyc_filled(organization_id: int):
    try:
        vendor = get_vendor_or_404(organization_id=organization_id)
        setattr(vendor, "is_vendor", False)
        return vendor
    except Http404:
        return False


def vendor_search_config_data(*, organization_id: int) -> Dict:
    return {
        "vendor_tag_list": get_vendor_org_tags(organization_id=organization_id),
        "invite_status_list": [status.value for status in InviteStatus],
        "vendor_status_list": [
            {
                "key": choice[0],
                "value": choice[1],
                "color_code": getattr(VendorStatusColorCode, choice[0].upper()).value,
            }
            for choice in VendorStatusChoices.choices
        ],
    }


def vendor_basic_detail_and_kyc_detail(*, filter: Q, org_id: int) -> Organization:
    vendor_detail = get_vendor_basic_and_kyc_detail(filter=filter)
    if not vendor_detail:
        return
    if not vendor_detail.msme and hasattr(vendor_detail, "vendor") and getattr(vendor_detail, "vendor"):
        setattr(
            vendor_detail,
            "msme",
            {"number": vendor_detail.vendor.msme_id, "file": None, "file_name": None, "id": None},
        )

    if not vendor_detail.pan and hasattr(vendor_detail, "vendor") and getattr(vendor_detail, "vendor"):
        setattr(
            vendor_detail,
            "pan",
            {"number": vendor_detail.pan_number, "file": None, "file_name": None, "id": None},
        )

    aadhar_data = {
        "number": vendor_detail.vendor.aadhar_number if hasattr(vendor_detail, "vendor") else "",
        "files": [],
    }
    for data in vendor_detail.aadhar_data_prefetch:
        aadhar_data["files"].append({"id": data.id, "file": data.file, "file_name": data.name})
    setattr(vendor_detail, "aadhar", aadhar_data)
    if hasattr(vendor_detail, "vendor") and getattr(vendor_detail, "vendor"):
        if vendor_detail.id == org_id:
            setattr(vendor_detail, "is_editable", True)
        else:
            setattr(vendor_detail, "is_editable", False)
    return vendor_detail


def vendor_basic_and_kyc_detail_with_org_id(*, mapping: ClientVendorMapping, org_id: int, user: User) -> Organization:
    if not mapping:
        raise VendorV2Exception("Client Vendor Mapping does not exist")
    vendor_detail = vendor_basic_detail_and_kyc_detail(filter=Q(id=mapping.vendor_id), org_id=org_id)
    is_editable = is_organization_editable(
        organization=mapping.org_to, user=user, permission=Permissions.CAN_EDIT_MANAGE_VENDOR
    )
    setattr(vendor_detail, "is_editable", is_editable)
    return vendor_detail


class VendorServiceV2:
    code_prefix = "MVVDR"

    def __init__(self, user: User, organization_id: int) -> None:
        self.organization_id = organization_id
        self.user = user

    @classmethod
    def fetch_next_serial_number(cls):
        return Vendor.objects.count() + 1

    @classmethod
    def set_vendor_tags(cls, tags: List[VendorTagData], cv_mapping: ClientVendorMapping, user_id: int):
        to_created_vendor_org_tags = []
        already_created_tags = []
        for tag in tags:
            if not tag.id:
                tag_obj = VendorOrgTag()
                tag_obj.organization_id = cv_mapping.client_id
                tag_obj.name = tag.name
                tag_obj.created_by_id = user_id
                to_created_vendor_org_tags.append(tag_obj)
            else:
                already_created_tags.append(tag)

        created_tags = VendorOrgTag.objects.bulk_create(objs=to_created_vendor_org_tags)
        all_tags = list(created_tags) + list(already_created_tags)
        cv_mapping.vendor_tag.set([tag.id for tag in all_tags])
        return all_tags

    @classmethod
    def _add_bank_details(cls, vendor_data, vendor_id: int, user_id: int, client_id: Optional[int] = None):
        vendor_bank_detail_list = []
        if hasattr(vendor_data, "bank"):
            for bank_detail in vendor_data.bank:
                vendor_bank_detail = VendorBankDetail(
                    client_id=client_id,
                    vendor_id=vendor_id,
                    account_holder_name=bank_detail.holder_name,
                    bank_name=bank_detail.bank_name,
                    account_number=bank_detail.account_number,
                    ifsc_code=bank_detail.ifsc_code,
                    cheque_file=bank_detail.cancelled_cheque,
                    cheque_file_name=bank_detail.cancelled_cheque_name,
                    created_by_id=user_id,
                )
                vendor_bank_detail_list.append(vendor_bank_detail)

        if vendor_bank_detail_list:
            create_bank_detail_copy_for_vendor_org(objs=vendor_bank_detail_list)
        return VendorBankDetail.objects.bulk_create(objs=vendor_bank_detail_list)

    @classmethod
    def _add_gst_data(cls, vendor_id: int, gst_data: List[VendorGSTData], user_id: int):
        vendor_gst_number_list = []
        for gst_object in gst_data:
            vendor_gst_number_list.append(
                OrganizationGSTNumber(
                    organization_id=vendor_id,
                    gst_number=gst_object.number,
                    file=gst_object.file,
                    name=gst_object.file,
                    created_by_id=user_id,
                    gst_state=gst_object.state,
                )
            )
        if vendor_gst_number_list:
            try:
                OrganizationGSTNumber.objects.bulk_create(objs=vendor_gst_number_list)
            except OrgGSTNumberAndStateUniqueException as e:
                raise ValidationError("Tax number already exists for this state") from e

    @classmethod
    def _update_gst_data(cls, gst_data: List[VendorGSTData], user_id: int):
        gst_instances: List[OrganizationGSTNumber] = (
            OrganizationGSTNumber.objects.filter(id__in=[gst.id for gst in gst_data]).order_by("id").all()
        )
        vendor_gst_number_list = []
        for gst, gst_instance in zip(gst_data, gst_instances):
            updated_gst, is_updated, _ = model_update(
                instance=gst_instance,
                data=gst,
                fields=["gst_number", "gst_state"],
                updated_by_id=user_id,
                save=False,
                clean=False,
            )
            if is_updated:
                vendor_gst_number_list.append(updated_gst)
        if vendor_gst_number_list:
            OrganizationGSTNumber.objects.bulk_update(objs=vendor_gst_number_list, fields=["gst_number", "gst_state"])

    @classmethod
    def _add_and_update_gst_data(cls, vendor_data, vendor_id: int, user_id: int):
        if hasattr(vendor_data, "gst"):
            to_create_gst, to_update_gst = cls.segegrate_add_and_update_objects(objects=vendor_data.gst)
            if to_create_gst:
                cls._add_gst_data(vendor_id=vendor_id, gst_data=to_create_gst, user_id=user_id)
            if to_update_gst:
                cls._update_gst_data(gst_data=to_update_gst, user_id=user_id)

    @classmethod
    def _add_kyc_documents(cls, vendor_data, vendor: Vendor, user_id: int):
        vendor_docs = []
        if hasattr(vendor_data, "pan") and vendor_data.pan and vendor_data.pan.file and not vendor_data.pan.id:
            vendor_docs.append(
                OrganizationDocument(
                    organization_id=vendor.pk,
                    file=vendor_data.pan.file,
                    name=vendor_data.pan.file_name,
                    type=OrganizationDocumentChoices.PAN,
                    uploaded_by_id=user_id,
                )
            )
        if hasattr(vendor_data, "msme") and vendor_data.msme and vendor_data.msme.file and not vendor_data.msme.id:
            vendor.msme_id = vendor_data.msme.number
            vendor_docs.append(
                OrganizationDocument(
                    organization_id=vendor.pk,
                    file=vendor_data.msme.file,
                    name=vendor_data.msme.file_name,
                    type=OrganizationDocumentChoices.MSME_DOC,
                    uploaded_by_id=user_id,
                )
            )
        if (
            hasattr(vendor_data, "business_card")
            and vendor_data.business_card
            and vendor_data.business_card.file
            and not vendor_data.business_card.id
        ):
            vendor_docs.append(
                OrganizationDocument(
                    organization_id=vendor.pk,
                    file=vendor_data.business_card.file,
                    name=vendor_data.business_card.file_name,
                    type=OrganizationDocumentChoices.BUSINESS_CARD,
                    uploaded_by_id=user_id,
                )
            )
        if hasattr(vendor_data, "aadhar") and vendor_data.aadhar:
            vendor.aadhar_number = vendor_data.aadhar.number
            for file_data in vendor_data.aadhar.files:
                if not file_data.id:
                    vendor_docs.append(
                        OrganizationDocument(
                            organization_id=vendor.pk,
                            file=file_data.file,
                            name=file_data.file_name,
                            type=OrganizationDocumentChoices.AADHAR,
                            uploaded_by_id=user_id,
                        )
                    )
        vendor.save(update_fields=["msme_id", "aadhar_number"])
        OrganizationDocument.objects.bulk_create(objs=vendor_docs)

    @classmethod
    def segegrate_add_and_update_objects(cls, objects: List) -> Tuple[List, List]:
        to_create_objects = []
        to_update_objects = []
        for obj in objects:
            if not obj.id:
                to_create_objects.append(obj)
            else:
                to_update_objects.append(obj)

        return to_create_objects, to_update_objects.sort(key=lambda x: x.id)

    @classmethod
    def update_org_addresses(cls, addresses: List[VendorAddressData], user_id: int):
        to_update_addresses = []
        address_instances: List[OrganizationAddress] = (
            OrganizationAddress.objects.filter(id__in=[address.id for address in addresses]).order_by("id").all()
        )
        for address, address_instance in zip(addresses, address_instances):
            updated_address, is_updated, _ = model_update(
                instance=address_instance,
                data=address,
                fields=["address_line_one", "address_line_two", "city", "state", "country", "pincode"],
                updated_by_id=user_id,
                save=False,
                clean=False,
            )
            if is_updated:
                to_update_addresses.append(updated_address)

        if to_update_addresses:
            OrganizationAddress.objects.bulk_update(
                objs=to_update_addresses,
                fields=["address_line_one", "address_line_two", "city", "state", "country", "pincode"],
            )

    @classmethod
    def _add_and_update_org_address(cls, vendor_data, vendor_id: int, user_id: int):
        if hasattr(vendor_data, "addresses"):
            to_create_addresses, to_update_addresses = cls.segegrate_add_and_update_objects(
                objects=vendor_data.addresses
            )
            if to_create_addresses:
                cls.add_org_address(vendor_id=vendor_id, addresses=to_create_addresses)
            if to_update_addresses:
                cls.update_org_addresses(addresses=to_update_addresses, user_id=user_id)

    @classmethod
    def add_org_address(cls, vendor_id: int, addresses: List[VendorAddressData]) -> List[OrganizationAddress]:
        to_create_address_obj_list = (
            OrganizationAddress(
                organization_id=vendor_id,
                address_line_one=address.address_line_1,
                address_line_two=address.address_line_2,
                city=address.city,
                state=address.state,
                country=address.country,
                pincode=address.pincode,
            )
            for address in addresses
        )
        OrganizationAddress.objects.bulk_create(objs=to_create_address_obj_list)

    def create_vendor(
        self,
        vendor_data: VendorOrgCreateData,
    ):
        if not vendor_data.id:
            organization = organization_create_by_referral_org(
                name=vendor_data.name,
                org_type=OrganizationType.VENDOR,
                referral_org_id=self.organization_id,
                referred_by_id=self.user.pk,
                pan_number=vendor_data.pan_number if vendor_data.pan_number else None,
            )
            organization = Organization.objects.select_related("vendor").get(id=organization.id)
            vendor_org = organization.vendor
        else:
            if not vendor_data.pan_number:
                raise ValidationError("Pan number should be provided")
            organization = Organization.objects.select_related("vendor").get(
                id=vendor_data.id, pan_number=vendor_data.pan_number
            )
            vendor_org = organization.vendor

        mapping = create_client_vendor_mapping_v2(
            created_by_id=self.user.pk,
            client_id=self.organization_id,
            vendor_id=vendor_org.pk,
            invited_by_org_id=self.organization_id,
            is_invited=False,
        )
        return vendor_org, mapping

    def onboard_vendor(self, vendor_data: VendorCreateData, user: User) -> Dict:
        vendor_org, mapping = self.create_vendor(vendor_data=vendor_data)
        self._add_and_update_org_address(vendor_id=vendor_org.pk, user_id=user.pk, vendor_data=vendor_data)

        return {
            "id": vendor_org.pk,
        }

    @classmethod
    def kyc_detail_add(cls, vendor_id: int, data: VendorKycDetailAddEntity, created_by_id: int, client_id: int):
        org_obj = Vendor.objects.get(organization_id=vendor_id)
        cls._add_kyc_documents(vendor=org_obj, user_id=created_by_id, vendor_data=data)
        cls._add_and_update_gst_data(vendor_id=vendor_id, user_id=created_by_id, vendor_data=data)
        cls._add_bank_details(vendor_id=vendor_id, user_id=created_by_id, vendor_data=data, client_id=client_id)


class VendorBulkService(VendorServiceV2):
    def __init__(self, organization_id, user: User):
        super().__init__(organization_id=organization_id, user=user)

    def create_vendors(self, vendor_list: List[VendorBulkEntity]):
        status_list = []
        for vendor in vendor_list:
            vendor_data = VendorOrgCreateData(pan_number=vendor.pan_number, name=vendor.organization_name, id=None)
            if vendor.pan_number:
                try:
                    vendor_org = get_vendor_using_pan(pan_number=vendor.pan_number)
                    mapping = get_vendor_mapping_using_client_and_vendor_id(
                        organization_id=self.organization_id, vendor_id=vendor_org.pk
                    ).first()
                    if mapping:
                        status_list.append((vendor.organization_name, "Existing Vendor"))
                        if vendor.user_name or vendor.phone_number or vendor.email:
                            try:
                                VendorUserService().add(
                                    mapping_id=mapping.id,
                                    created_by_id=self.user.pk,
                                    user=OnboardUserEntity(
                                        name=vendor.user_name,
                                        phone_number=vendor.phone_number,
                                        email=vendor.email,
                                        is_invited=False,
                                    ),
                                )
                            except VendorUserService.PocUserServiceException:
                                pass
                        continue
                    vendor_data.id = vendor_org.pk
                except Http404:
                    pass
            status_list.append((vendor.organization_name, "Vendor Added"))
            vendor_obj, mapping = self.create_vendor(vendor_data=vendor_data)
            if vendor.gst_number:
                gst_data = VendorGSTData(
                    file=None,
                    file_name=None,
                    number=vendor.gst_number,
                    id=None,
                    state=None,
                )
                self._add_gst_data(vendor_id=vendor_obj.pk, gst_data=[gst_data], user_id=self.user.pk)
            if vendor.user_name or vendor.phone_number or vendor.email:
                VendorUserService().add(
                    mapping_id=mapping.id,
                    created_by_id=self.user.pk,
                    user=OnboardUserEntity(
                        name=vendor.user_name,
                        phone_number=vendor.phone_number,
                        email=vendor.email,
                        is_invited=False,
                    ),
                )
        return status_list


class ManageVendorOrgService(ManageOrgService):
    _gst_number_model = OrganizationGSTNumber

    @classmethod
    def basic_detail_update(cls, organization: Organization, data: OrganizationBasicDetailUpdateData, user_id: int):
        organization_name_update(organization=organization, data=data, user_id=user_id)
        organization_addresses_update_and_create(addresses=data.addresses, org_id=organization.pk, user_id=user_id)
        organization_business_card_add_and_delete(
            document_model=cls._document_model,
            org_id=organization.pk,
            business_cards=data.business_cards,
            user_id=user_id,
        )

    @classmethod
    def _bank_detail_create(cls, client_id: int, vendor_id: int, user_id: int, to_create: List[VendorBankUpdateData]):
        to_create_objs = [
            VendorBankDetail(
                client_id=client_id,
                vendor_id=vendor_id,
                account_number=doc.account_number,
                ifsc_code=doc.ifsc_code,
                account_holder_name=doc.holder_name,
                bank_name=doc.bank_name,
                cheque_file=doc.cancelled_cheque,
                cheque_file_name=doc.cancelled_cheque_name,
                created_by_id=user_id,
            )
            for doc in to_create
        ]
        VendorBankDetail.objects.bulk_create(objs=to_create_objs)
        if client_id is not None:
            create_bank_detail_copy_for_vendor_org(objs=to_create_objs)
        else:
            create_bank_detail_copy_for_referral_org(
                objs=to_create_objs, referral_org_id=Organization.objects.get(id=vendor_id).referral_org_id
            )

    @classmethod
    def _bank_detail_delete(cls, document_ids: List[int], user_id: int):
        VendorBankDetail.objects.filter(id__in=document_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())

    @classmethod
    def _bank_detail_update(cls, to_update: List[VendorBankUpdateData], user_id: int, vendor_id: int):
        to_update: List[VendorBankUpdateData] = sorted(to_update, key=lambda x: x.id)
        bank_detail_instances = (
            VendorBankDetail.objects.filter(id__in=[doc.id for doc in to_update]).order_by().order_by("id").all()
        )
        fields_to_update = [
            "account_number",
            "ifsc_code",
            "account_holder_name",
            "bank_name",
            "cheque_file_name",
        ]

        to_update_objs = []
        # TODO : this logic can be moved to model_update
        # this logic converts blank fields to null field values
        for doc, bank_detail_instance in zip(to_update, bank_detail_instances):
            for field in fields_to_update:
                if hasattr(bank_detail_instance, field) and getattr(bank_detail_instance, field) == "":
                    setattr(bank_detail_instance, field, None)

            updated_obj, is_updated, updated_fields = model_update(
                instance=bank_detail_instance,
                fields=fields_to_update,
                data={
                    "account_number": doc.account_number,
                    "ifsc_code": doc.ifsc_code,
                    "account_holder_name": doc.holder_name,
                    "bank_name": doc.bank_name,
                    "cheque_file_name": doc.cancelled_cheque_name,
                },
                updated_by_id=user_id,
                save=False,
                clean=False,
            )
            # to move this logic to model_update specially for file fields
            if doc.cancelled_cheque is None:
                doc.cancelled_cheque = ""
            if bank_detail_instance.cheque_file.name != doc.cancelled_cheque:
                updated_obj.cheque_file = doc.cancelled_cheque
                if not is_updated:
                    is_updated = True
            if is_updated:
                to_update_objs.append(updated_obj)

        VendorBankDetail.objects.bulk_update(
            objs=to_update_objs,
            fields=[
                "account_number",
                "ifsc_code",
                "account_holder_name",
                "bank_name",
                "cheque_file",
                "cheque_file_name",
                "updated_by_id",
                "updated_at",
            ],
        )
        create_bank_detail_copy_for_vendor_org(objs=to_update_objs)
        create_bank_detail_copy_for_referral_org(
            objs=to_update_objs, referral_org_id=Organization.objects.get(id=vendor_id).referral_org_id
        )

    @classmethod
    def aadhar_data_update(cls, data: OrganizationAadharUpdateData, vendor: Vendor, user_id: int):
        aadhar_number = data.number
        updated_fields = []
        if aadhar_number and aadhar_number != vendor.aadhar_number:
            vendor_org = (
                Vendor.objects.exclude(organization_id=vendor.pk, aadhar_number__isnull=False)
                .filter(aadhar_number=aadhar_number)
                .first()
            )
            if vendor_org:
                raise VendorV2Exception("Aadhar Number Exist for Other Organization, Kindly Contact Support")
        vendor, _, updated_fields = model_update(
            instance=vendor,
            fields=["aadhar_number"],
            data={"aadhar_number": aadhar_number},
            updated_by_id=user_id,
            save=False,
            clean=False,
        )
        if data.files:
            to_create, _, to_delete = nested_object_segregation(docs_list=data.files)
            if to_create:
                cls.create_bulk_documents(
                    files=to_create, organization_id=vendor.pk, user_id=user_id, type=OrganizationDocumentChoices.AADHAR
                )
            if to_delete:
                cls.delete_many_documents(document_ids=[doc.id for doc in to_delete], user_id=user_id)
        return vendor, updated_fields

    @classmethod
    def msme_data_update(cls, data: OrganizationMSMEUpdateData, vendor: Vendor, user_id: int) -> Tuple[Vendor, List]:
        msme_id = data.number
        updated_fields = []
        if msme_id and msme_id != vendor.msme_id:
            vendor_org = (
                Vendor.objects.exclude(organization_id=vendor.pk, msme_id__isnull=False).filter(msme_id=msme_id).first()
            )
            if vendor_org:
                raise VendorV2Exception("MSME Number Exist for Other Organization, Kindly Contact Support")
        vendor, _, updated_fields = model_update(
            instance=vendor,
            fields=["msme_id"],
            data={"msme_id": msme_id},
            updated_by_id=user_id,
            save=False,
            clean=False,
        )
        if data.files:
            to_create, _, to_delete = nested_object_segregation(docs_list=data.files)
            if to_create:
                cls.create_bulk_documents(
                    files=to_create,
                    organization_id=vendor.pk,
                    user_id=user_id,
                    type=OrganizationDocumentChoices.MSME_DOC,
                )
            if to_delete:
                cls.delete_many_documents(document_ids=[doc.id for doc in to_delete], user_id=user_id)
        return vendor, updated_fields

    @classmethod
    def kyc_detail_update(cls, org_id: int, data: OrganizationKYCUpdateData, user_id: int):
        vendor: Vendor = Vendor.objects.filter(organization_id=org_id).first()
        updated_fields = []
        if not vendor:
            raise ValidationError("Vendor doesn't exist")
        if data.pan:
            OrganizationPanService.pan_data_update(data=data.pan, organization=vendor.organization, user_id=user_id)

        if data.aadhar:
            vendor, aadhar_updated_fields = cls.aadhar_data_update(data=data.aadhar, vendor=vendor, user_id=user_id)
            if aadhar_updated_fields:
                updated_fields.extend(aadhar_updated_fields)
        if data.msme:
            vendor, msme_updated_fields = cls.msme_data_update(data=data.msme, vendor=vendor, user_id=user_id)
            if msme_updated_fields:
                updated_fields.extend(msme_updated_fields)

        if data.gst:
            cls.gst_data_update(data=data.gst, organization_id=vendor.pk, user_id=user_id)

        if updated_fields:
            vendor.save(update_fields=updated_fields)

    @classmethod
    def bank_detail_create_and_update(
        cls, data: VendorBankListUpdateData, client_id: Optional[int], vendor_id: int, user_id: int
    ):
        to_create, to_update, to_delete = nested_object_segregation(docs_list=data.data)
        if to_create:
            cls._bank_detail_create(client_id=client_id, vendor_id=vendor_id, user_id=user_id, to_create=to_create)

        if to_update:
            cls._bank_detail_update(to_update=to_update, user_id=user_id, vendor_id=vendor_id)

        if to_delete:
            cls._bank_detail_delete(document_ids=[doc.id for doc in to_delete], user_id=user_id)

    @classmethod
    def tags_update(cls, data: VendorTagListUpdateData, cv_mapping_id: int, user_id: int):
        cv_mapping = ClientVendorMapping.objects.filter(id=cv_mapping_id).first()
        return VendorServiceV2.set_vendor_tags(tags=data.data, cv_mapping=cv_mapping, user_id=user_id)

    @classmethod
    def vendor_status_change(
        cls, status: str, mapping: ClientVendorMapping, user_id: int, blacklist_reason: Optional[str] = None
    ):
        if mapping and mapping.vendor_status != status:
            mapping.vendor_status = status
            mapping.updated_at = timezone.now()
            mapping.updated_by_id = user_id
            mapping.save(update_fields=["vendor_status", "updated_at", "updated_by_id"])
            if status == VendorStatusChoices.BLACKLISTED and blacklist_reason:
                BlacklistReasonHistory.objects.create(
                    cv_mapping=mapping,
                    created_by_id=user_id,
                    blacklist_reason=blacklist_reason,
                )

    @classmethod
    def update_vendor_onboard_config(cls, org_id: int, user_id: int, data: VendorOnboardConfigData):
        return VendorOnboardConfig.objects.update_or_create(
            org_id=org_id,
            defaults={
                "is_pan_required": data.is_pan_required,
                "is_bank_detail_required": data.is_bank_detail_required,
                "updated_at": timezone.now(),
                "updated_by_id": user_id,
            },
        )


class ManageVendorOtherDocumentService(OtherDocumentService):
    _other_document_model = VendorOtherDetailDocument
