from core.entities import OrgUserEntity
from core.organization.data.repositories import OrganizationRepo
from core.organization.domain.services.organization import OrganizationService
from vendorv2.data.repositories import VendorLinkedOrganizationRepo
from vendorv2.domain.services.vendor import VendorLinkedOrganizationService, VendorOnboardServiceV2


class VendorFactory:
    def __init__(self, user_entity: OrgUserEntity):
        self.user_entity = user_entity

    def get_linked_repo(self, vendor_id: int):
        return VendorLinkedOrganizationRepo(
            user_entity=self.user_entity,
            vendor_id=vendor_id,
        )

    def get_org_repo(self):
        return OrganizationRepo(
            user_entity=self.user_entity,
        )

    def get_linked_service(self, vendor_id: int, billing_entity_id: int | None = None):
        return VendorLinkedOrganizationService(
            user_entity=self.user_entity,
            vendor_id=vendor_id,
            billing_entity_id=billing_entity_id,
            repo=self.get_linked_repo(vendor_id=vendor_id),
        )

    def get_onboard_service(self):
        return VendorOnboardServiceV2(user_entity=self.user_entity)

    def get_org_service(self, billing_entity_id: int | None = None):
        return OrganizationService(
            user_entity=self.user_entity,
            repo=self.get_org_repo(),
            billing_entity_id=billing_entity_id,
        )
