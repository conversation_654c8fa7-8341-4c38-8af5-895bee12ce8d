import abc

from common.exceptions import BaseValidationError
from core.organization.domain.abstract_repos import LinkedOrganizationAbstractRepo
from vendorv2.domain.entities import VendorOnboardConfigData
from vendorv2.domain.entities.vendor_entities import VendorBasicDetails, VendorDocumentDetail, VendorTag


class VendorAbstractRepo(abc.ABC):
    class Exception(BaseValidationError):
        pass

    class ClientVendorMappingNotFound(Exception):
        pass

    class LegalEntityNotFound(Exception):
        pass

    @abc.abstractmethod
    def get_basic_details(self) -> VendorBasicDetails: ...


class VendorLinkedOrganizationAbstractRepo(LinkedOrganizationAbstractRepo):
    @abc.abstractmethod
    def get_document_details(self) -> list[VendorDocumentDetail]:
        pass

    @abc.abstractmethod
    def get_tag_details(self) -> list[VendorTag]:
        pass

    @abc.abstractmethod
    def get_onboard_organization_config(self) -> VendorOnboardConfigData:
        pass
