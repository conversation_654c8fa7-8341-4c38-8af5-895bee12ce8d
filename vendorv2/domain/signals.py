from django.db.models.signals import post_save
from django.dispatch import receiver

from core.models import Organization
from vendor.data.models import Vendor
from vendorv2.domain.services.services import VendorServiceV2, create_vendor_organization


@receiver(post_save, sender=Organization, dispatch_uid="organization_post_save_signal_for_vendor")
def organization_post_save(sender, instance, created, **kwargs):
    if not created:
        return

    if not Vendor.objects.filter(organization_id=instance.id).exists():
        create_vendor_organization(
            organization_id=instance.id,
            serial_number=VendorServiceV2.fetch_next_serial_number(),
            code_prefix=VendorServiceV2.code_prefix,
            pan_number=instance.pan_number,
            created_by_id=instance.referral_by_id,
            aadhar_number=None,
            msme_id=None,
            referal_organization_id=instance.referral_org_id,
        )
