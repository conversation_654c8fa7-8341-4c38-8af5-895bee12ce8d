from django.db.models import F

from core.entities import OrgUserEntity
from core.models import Country, Organization, VendorTagMapping
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.data.models import (
    LinkedOrganizations,
    OrganizationSectionConfig,
)
from core.organization.data.repositories import LinkedOrganizationRepo
from vendorv2.data.models import (
    ClientVendorMapping,
    VendorOnboardConfig,
    VendorOtherDetailDocument,
)
from vendorv2.domain.abstract_repos import VendorLinkedOrganizationAbstractRepo
from vendorv2.domain.entities import VendorOnboardConfigData
from vendorv2.domain.entities.vendor_entities import (
    VendorDocumentDetail,
    VendorDocumentDetailUploadedBy,
    VendorTag,
)


class VendorLinkedOrganizationRepo(VendorLinkedOrganizationAbstractRepo, LinkedOrganizationRepo):
    def __init__(self, user_entity: OrgUserEntity, vendor_id: int):
        self.vendor_id = vendor_id
        super().__init__(user_entity)

    def get_mapping(self) -> LinkedOrganizations:
        mapping = (
            LinkedOrganizations.objects.filter(client_id=self.org_id, vendor_id=self.vendor_id, org_id=self.org_id)
            .select_related("client__country__uid_field")
            .annotate(
                uid_field_id=F("client__country__uid_field_id"),
                uid_field_name=F("client__country__uid_field__name"),
            )
            .first()
        )
        if not mapping:
            raise self.MappingNotFound("Linked organization mapping not found.")
        return mapping

    def get_from_to_org_mapping_id(self) -> int:
        mapping = (
            ClientVendorMapping.objects.filter(
                org_from_id=self.org_id,
                org_to_id=self.vendor_id,
            )
            .available()
            .values("id")
            .first()
        )

        if not mapping:
            raise self.MappingNotFound("Client vendor mapping not found")

        return mapping["id"]

    def get_linked_org_mapping(self) -> LinkedOrganizations:
        mapping = (
            LinkedOrganizations.objects.filter(client_id=self.org_id, vendor_id=self.vendor_id, org_id=self.org_id)
            .select_related("vendor__country__uid_field")
            .annotate(
                uid_field_id=F("vendor__country__uid_field_id"),
                uid_field_name=F("vendor__country__uid_field__name"),
            )
            .first()
        )
        if not mapping:
            raise self.MappingNotFound("Linked organization mapping not found.")
        return mapping

    def get_document_details(self) -> list[VendorDocumentDetail]:
        from_to_org_mapping_id = self.get_from_to_org_mapping_id()

        documents = VendorOtherDetailDocument.objects.filter(cv_mapping=from_to_org_mapping_id, deleted_at__isnull=True)

        vendor_documents = []

        for doc in documents:
            vendor_documents.append(
                VendorDocumentDetail(
                    id=doc.pk,
                    name=doc.name,
                    file=doc.file.url,
                    tags=doc.tags,
                    uploaded_at=doc.created_at,
                    uploaded_by=VendorDocumentDetailUploadedBy(
                        name=doc.created_by.name,
                        photo=doc.created_by.photo.url if doc.created_by.photo else None,
                    ),
                )
            )
        return vendor_documents

    def get_tag_details(self) -> list[VendorTag]:
        from_to_org_mapping_id = self.get_from_to_org_mapping_id()

        tags = (
            VendorTagMapping.objects.filter(cv_mapping_id=from_to_org_mapping_id)
            .select_related("vendor_org_tag")
            .order_by("vendor_org_tag__created_at")
        )

        return [VendorTag(id=tag.vendor_org_tag.pk, name=tag.vendor_org_tag.name) for tag in tags] if tags else []

    def get_onboard_organization_config(self) -> VendorOnboardConfigData:
        vendor_onboard_config = VendorOnboardConfig.objects.filter(org_id=self.org_id).first()
        country_ids = Organization.objects.filter(id=self.org_id).values_list("country_id", flat=True)
        uid_name = (
            Country.objects.filter(id__in=country_ids)
            .select_related("uid_field")
            .values_list("uid_field__name", flat=True)
            .first()
        )
        bank_section_name = (
            OrganizationSectionConfig.objects.filter(
                country_id__in=country_ids, type=OrganizationSectionTypeEnum.BANK_DETAILS.value
            )
            .values_list("name", flat=True)
            .first()
        )
        if not vendor_onboard_config:
            return VendorOnboardConfigData(
                is_pan_required=True,
                is_bank_detail_required=False,
                uid_name=uid_name,
                bank_section_name=bank_section_name,
            )
        return VendorOnboardConfigData(
            is_pan_required=vendor_onboard_config.is_pan_required,
            is_bank_detail_required=vendor_onboard_config.is_bank_detail_required,
            uid_name=uid_name,
            bank_section_name=bank_section_name,
        )
