from django.db import models

from common.models import DocumentAndAttachmentsModel
from core.models import FromToOrgMapping


class VendorOtherDetailDocument(DocumentAndAttachmentsModel):
    cv_mapping_id: int

    cv_mapping = models.ForeignKey(FromToOrgMapping, related_name="other_detail_docs", on_delete=models.RESTRICT)

    class Meta:
        db_table = "vendor_v2_other_detail_documents"
        verbose_name = "Vendor Other Detail Document"
        verbose_name_plural = "Vendor Other Detail Documents"
