from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError, models
from django.db.models import Q, QuerySet

from common.choices import StateChoices
from common.constants import FILE_FIELD_MAX_LENGTH
from common.helpers import get_upload_path
from common.models import (
    CreateModel,
    CreateUpdateDeleteModel,
    UpdateModel,
    UploadDeleteModel,
)
from common.querysets import AvailableQuerySetMixin
from common.validators import alphanumeric_validator
from core.models import BasePOCModel, FromToOrgMapping, Organization
from smtp_email.data.models import EmailBase
from vendorv2.data.managers import (
    VendorBankDetailManager,
    VendorDocumentManager,
    VendorGSTNUmberManager,
)
from vendorv2.data.querysets import VendorPocQueryset
from vendorv2.domain.choices import VendorDocumentChoices


class ClientVendorMapping(FromToOrgMapping):
    class ClientVendorMappingQueryset(QuerySet, AvailableQuerySetMixin):
        pass

    objects = ClientVendorMappingQueryset.as_manager()

    @property
    def client(self):
        return self.org_from

    @client.setter
    def client(self, client):
        self.org_from = client

    @property
    def vendor(self):
        return self.org_to

    @vendor.setter
    def vendor(self, vendor):
        self.org_to = vendor

    @property
    def client_id(self):
        return self.org_from_id

    @client_id.setter
    def client_id(self, client_id):
        self.org_from_id = client_id

    @property
    def vendor_id(self):
        return self.org_to_id

    @vendor_id.setter
    def vendor_id(self, vendor_id):
        self.org_to_id = vendor_id

    class Meta:
        proxy = True
        # constraints = [
        #     models.UniqueConstraint(
        #         fields=["client", "vendor"],
        #         condition=Q(deleted_at__isnull=True),
        #         name="client_vendor_unique_together_if_deleted_at_isnull",
        #     ),
        # ]
        # db_table = "vendor_v2_client_vendor_mapping"

    def clean(self):
        if self.client_id == self.vendor_id:
            raise ValidationError("Client and Vendor cannot be same")


class VendorDocument(UploadDeleteModel):
    vendor = models.ForeignKey(Organization, related_name="vendor_organization_document", on_delete=models.RESTRICT)
    file = models.FileField(upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=15, choices=VendorDocumentChoices.choices)

    objects = VendorDocumentManager()

    @property
    def org_foreign_key(self):
        return "vendor_id"

    class Meta:
        db_table = "vendor_v2_vendor_organization_document"


class VendorGSTNumber(CreateUpdateDeleteModel):
    vendor = models.ForeignKey(Organization, related_name="vendor_gst", on_delete=models.RESTRICT)
    gst_number = models.CharField(max_length=20, null=True, blank=True, default=None)
    gst_state = models.CharField(
        max_length=20, choices=StateChoices.choices, null=True, blank=True, default=None
    )  # fill state field for old data and mark null=False
    file = models.FileField(
        null=True, default=None, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH
    )
    name = models.CharField(max_length=100, default=None, null=True, blank=True)

    objects = VendorGSTNUmberManager()

    def save(self, *args, **kwargs) -> None:
        try:
            return super().save(*args, **kwargs)
        except IntegrityError as e:
            if "unique_gst_number_state_wise" in str(e):
                raise IntegrityError("Tax number already exists for this state") from e
            raise e

    class Meta:
        db_table = "vendor_v2_vendor_gst_number"
        constraints = [
            models.UniqueConstraint(
                fields=["gst_number", "gst_state"],
                condition=Q(deleted_at__isnull=True),
                name="unique_gst_number_state_wise",
            ),
        ]


class VendorBankDetail(CreateUpdateDeleteModel):
    client = models.ForeignKey(
        Organization, related_name="client_bank_detail", on_delete=models.RESTRICT, null=True, blank=True, default=None
    )
    vendor = models.ForeignKey(Organization, related_name="vendor_bank_detail", on_delete=models.RESTRICT)
    account_holder_name = models.CharField(max_length=250, default=None, null=True, blank=True)
    bank_name = models.CharField(max_length=250, default=None, null=True, blank=True)
    account_number = models.CharField(
        max_length=18, default=None, null=True, blank=True, validators=[alphanumeric_validator]
    )
    ifsc_code = models.CharField(max_length=15, default=None, null=True, blank=True)
    cheque_file = models.FileField(
        default=None, null=True, blank=True, upload_to=get_upload_path, max_length=FILE_FIELD_MAX_LENGTH
    )
    cheque_file_name = models.CharField(default=None, max_length=150, null=True, blank=True)

    objects = VendorBankDetailManager()

    @property
    def is_editable(self):
        return False if self.client is None else True

    class Meta:
        db_table = "vendor_v2_vendor_organization_bank_detail"


class VendorEmail(EmailBase):
    sent_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name="%(app_label)s_%(class)s_created",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    vendor = models.ForeignKey(Organization, on_delete=models.RESTRICT)
    context = models.JSONField(default=dict)

    class Meta:
        db_table = "vendor_v2_vendor_email"


class InvitedVendor(CreateModel):
    cv_mapping = models.ForeignKey(FromToOrgMapping, related_name="+", on_delete=models.RESTRICT)
    poc_data = models.JSONField(default=dict)

    class Meta:
        db_table = "vendor_v2_poc_invitation"


class VendorPoc(BasePOCModel):
    objects = VendorPocQueryset.as_manager()

    class Meta:
        db_table = "vendor_poc"
        verbose_name = "Vendor POC"
        verbose_name_plural = "Vendor POC's"
        constraints = [
            models.UniqueConstraint(
                name="vendor_poc_mapping_user_unique_together",
                fields=["mapping_id", "user_id"],
                condition=Q(user__isnull=False, deleted_at__isnull=True),
            )
        ]


class BlacklistReasonHistory(CreateModel):
    cv_mapping = models.ForeignKey(
        FromToOrgMapping,
        on_delete=models.RESTRICT,
        related_name="vendor_blacklist_reason_histories",
    )
    blacklist_reason = models.TextField()

    class Meta:
        db_table = "vendor_v2_blacklist_reason_histories"


class VendorOnboardConfig(UpdateModel):
    org = models.OneToOneField(Organization, on_delete=models.RESTRICT, related_name="vendor_onboard_config")
    is_pan_required = models.BooleanField(default=True)
    is_bank_detail_required = models.BooleanField(default=False)

    class Meta:
        db_table = "vendor_v2_vendor_onboard_config"
