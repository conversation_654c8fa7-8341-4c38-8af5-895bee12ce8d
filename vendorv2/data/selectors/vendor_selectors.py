from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

from django.db.models import Case, F, OuterRef, Q, QuerySet, Subquery, Value, When
from django.utils import timezone

from core.models import Organization, OrganizationGSTNumber, UserLoginHistory
from core.organization.data.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationBusinessCard,
    LinkedOrganizations,
    OrganizationSectionConfig,
)
from core.organization.data.selectors import get_organization_sections_config
from core.organization.enums import UserStatus
from vendorv2.data.models import VendorPoc


def vendor_users_get(mapping_id: int):
    subquery = UserLoginHistory.objects.filter(user_id=OuterRef("user_id")).order_by("-id").values("login_at")[:1]
    return (
        VendorPoc.objects.available()
        .filter(mapping_id=mapping_id)
        .annotate(
            last_login_at=Subquery(subquery),
            active_status=Case(
                When(is_invited=False, then=None),
                When(
                    ~Q(user__org_id=F("mapping__org_to_id")),
                    is_invited=True,
                    user_id__isnull=False,
                    then=Value(UserStatus.LEFT.value),
                ),
                When(
                    user__org_id=F("mapping__org_to_id"),
                    is_invited=True,
                    user_id__isnull=False,
                    user__deleted_at__isnull=False,
                    then=Value(UserStatus.LEFT.value),
                ),
                When(
                    is_invited=True,
                    user_id__isnull=False,
                    last_login_at__isnull=False,
                    user__org_id=F("mapping__org_to_id"),
                    last_login_at__gt=timezone.now() - timedelta(days=90),
                    then=Value(UserStatus.ACTIVE.value),
                ),
                default=Value(UserStatus.INACTIVE.value),
            ),
        )
        .annotate(has_left_org=Case(When(active_status=UserStatus.LEFT.value, then=True), default=False))
    )


def fetch_vendor_gst_details(vendor_id: int):
    return OrganizationGSTNumber.objects.filter(
        organization_id=vendor_id, gst_number__isnull=False, deleted_at__isnull=True
    )


def get_vendor_onboard_sections_config(country_id: int) -> QuerySet[OrganizationSectionConfig]:
    return get_organization_sections_config(country_id=country_id)


def get_vendor_basic_details(vendor_id: int, onboarder_org_id: int) -> Optional[Organization]:
    mapping_id = Subquery(
        LinkedOrganizations.objects.filter(
            vendor_id=vendor_id, client_id=onboarder_org_id, org_id=onboarder_org_id
        ).values_list("id", flat=True)
    )
    addresses = (
        LinkedOrganizationAddress.objects.filter(mapping_id__in=mapping_id)
        .select_related("country", "state", "city")
        .available()
        .order_by("id")
    )
    business_cards = LinkedOrganizationBusinessCard.objects.filter(mapping_id__in=mapping_id).available()
    org = Organization.objects.filter(id=vendor_id).first()
    if org:
        setattr(org, "vendor_addresses", addresses)
        setattr(org, "business_cards", business_cards)
        return org
