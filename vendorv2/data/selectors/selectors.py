from django.db.models import (
    <PERSON>,
    Exists,
    F,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Value,
    When,
)
from django.db.models.functions import JSONObject
from django.shortcuts import get_object_or_404

from common.choices import VendorStatusChoices
from core.choices import OrganizationDocumentChoices
from core.models import Country, Organization, OrganizationDocument, UserLoginHistory, VendorOrgTag, VendorTagMapping
from core.organization.data.constants import OrganizationSectionTypeEnum
from core.organization.data.models import (
    LinkedOrganizationAddress,
    LinkedOrganizationBillingEntity,
    LinkedOrganizations,
    OrganizationSectionConfig,
)
from vendor.data.models import Vendor
from vendorv2.data.models import (
    BlacklistReasonHistory,
    ClientVendorMapping,
    VendorBankDetail,
    VendorOnboardConfig,
    VendorOtherDetailDocument,
    VendorPoc,
)
from vendorv2.domain.constants import InviteStatus
from vendorv2.domain.entities import VendorOnboardConfigData
from vendorv2.interface.exceptions import VendorV2Exception


def get_vendor_from_org_id(organization_id: int) -> Vendor:
    return Vendor.objects.get(organization_id=organization_id)


def mapping_get(client_id: int, vendor_id: int):
    mapping = (
        ClientVendorMapping.objects.filter(
            org_from_id=client_id,
            org_to_id=vendor_id,
        )
        .available()
        .first()
    )
    if not mapping:
        raise VendorV2Exception("mapping not found")
    return mapping


def fetch_all_vendor_mappings(organization_id: int):
    return (
        ClientVendorMapping.objects.filter(org_from_id=organization_id)
        .select_related("vendor_poc", "org_to")
        .prefetch_related("org_to__vendor")
        .available()
        .annotate(
            user_last_login=Subquery(
                UserLoginHistory.objects.filter(user_id=OuterRef("vendor_poc_id"))
                .order_by("-id")
                .values("login_at")[:1]
            )
        )
    )


def fetch_vendor_mapping(mapping_id: int):
    return (
        ClientVendorMapping.objects.filter(id=mapping_id)
        .select_related("vendor_poc", "org_to")
        .prefetch_related("org_to__vendor")
        .available()
        .annotate(
            user_last_login=Subquery(
                UserLoginHistory.objects.filter(user_id=OuterRef("vendor_poc_id"))
                .order_by("-id")
                .values("login_at")[:1]
            )
        )
        .first()
    )


def get_vendor_using_pan(pan_number: str):
    return get_object_or_404(Organization, pan_number=pan_number.upper())


def get_vendor_mapping_using_client_and_vendor_id(organization_id: int, vendor_id: int):
    return ClientVendorMapping.objects.filter(org_from_id=organization_id, org_to_id=vendor_id).available()


def get_client_vendor_mapping(*, id):
    return ClientVendorMapping.objects.filter(id=id).first()


def get_vendor_or_404(organization_id: int):
    return get_object_or_404(Vendor, pk=organization_id)


def get_vendor_org_tags(*, organization_id: int) -> QuerySet:
    return VendorOrgTag.objects.filter(Q(organization_id=organization_id) | Q(organization_id=None)).all()


def get_vendor_list(*, organization_id: int) -> QuerySet:
    vendor_poc_subquery = (
        VendorPoc.objects.filter(mapping_id=OuterRef("id"), is_primary=True)
        .available()
        .order_by("created_at")
        .annotate(primary_user=JSONObject(name="name", phone_number="phone_number", email="email"))
        .values("primary_user")[:1]
    )

    linked_org_subquery = Subquery(
        LinkedOrganizations.objects.filter(
            client_id=OuterRef("org_from_id"),
            vendor_id=OuterRef("org_to_id"),
            org_id=OuterRef("org_from_id"),
        )
        .annotate(
            billing_entity_name=Subquery(
                LinkedOrganizationBillingEntity.objects.filter(mapping_id=OuterRef("id"), is_primary=True).values(
                    "name"
                )[:1]
            )
        )
        .values("billing_entity_name")[:1]
    )

    return (
        ClientVendorMapping.objects.filter(org_from_id=organization_id)
        .exclude(org_from_id=F("org_to_id"))
        .select_related("org_to", "org_to__vendor", "org_to__country__uid_field")
        .prefetch_related(
            "vendor_tag",
            Prefetch(
                "vendor_blacklist_reason_histories",
                queryset=BlacklistReasonHistory.objects.all().order_by("-created_at"),
            ),
        )
        .available()
        .annotate(primary_user=Subquery(vendor_poc_subquery))
        .annotate(
            is_users_invited=Exists(
                Subquery(
                    VendorPoc.objects.filter(mapping_id=OuterRef("id"), is_invited=True)
                    .available()
                    .order_by("-id")
                    .values("id")[:1]
                )
            ),
            invite_status=Case(
                When(is_users_invited=True, then=Value(InviteStatus.INVITED.value)),
                default=Value(InviteStatus.NOTINVITED.value),
            ),
            status=F("vendor_status"),
            mark_active=Case(  # Todo need to check if this is used
                When(vendor_status=VendorStatusChoices.ACTIVE.value, then=Value(True)),
                When(vendor_status=VendorStatusChoices.INACTIVE.value, then=Value(False)),
                default=Value(False),
            ),
            status_priority=Case(
                When(status__in=[VendorStatusChoices.ONBOARDED.value, VendorStatusChoices.ACTIVE.value], then=Value(0)),
                When(status=VendorStatusChoices.INACTIVE.value, then=Value(1)),
                When(status=VendorStatusChoices.BLACKLISTED.value, then=Value(2)),
                default=Value(3),
            ),
        )
        .annotate(uid_value=F("org_to__uid"))
        .annotate(billing_entity_name=linked_org_subquery)
        .order_by(
            "status_priority",
            # "org_to__name",
            "org_to__vendor__pan_number",
            "vendor_poc_name",
            "billing_entity_name",
        )
    )


def get_vendor_basic_details_v2(*, organization_id: int, vendor_id: int):
    linked_org_mapping_subquery = LinkedOrganizations.objects.filter(
        client_id=organization_id, org_id=organization_id, vendor_id=OuterRef("org_to_id")
    ).values("id")[:1]

    # Get the specific mapping
    mapping = (
        ClientVendorMapping.objects.filter(org_from_id=organization_id, org_to_id=vendor_id)
        .exclude(org_from_id=F("org_to_id"))
        .select_related("org_to", "org_to__vendor", "org_to__country__uid_field")
        .prefetch_related(
            Prefetch(
                "vendor_blacklist_reason_histories",
                queryset=BlacklistReasonHistory.objects.all().order_by("-created_at"),
            ),
        )
        .available()
        .annotate(
            is_users_invited=Exists(
                VendorPoc.objects.filter(mapping_id=OuterRef("id"), is_invited=True)
                .available()
                .order_by("-id")
                .values("id")[:1]
            ),
            invite_status=Case(
                When(is_users_invited=True, then=Value(InviteStatus.INVITED.value)),
                default=Value(InviteStatus.NOTINVITED.value),
            ),
            status=F("vendor_status"),
        )
        .annotate(
            linked_mapping_id=Subquery(linked_org_mapping_subquery),
        )
        .first()
    )

    if not mapping:
        return None

    # Fetch addresses only for this specific mapping
    linked_mapping_id = getattr(mapping, "linked_mapping_id", None)
    addresses = []
    if linked_mapping_id:
        addresses = list(
            LinkedOrganizationAddress.objects.filter(mapping_id=linked_mapping_id).select_related(
                "country", "state", "city"
            )
        )

    setattr(mapping, "prefetched_addresses", addresses)
    return mapping


def get_vendor_basic_and_kyc_detail(*, filter: Q):
    pan_subquery = (
        OrganizationDocument.objects.filter(organization_id=OuterRef("id"), type=OrganizationDocumentChoices.PAN)
        .annotate(pan_data=JSONObject(id="id", number="organization__pan_number", file="file", file_name="name"))
        .available()
    )

    msme = (
        OrganizationDocument.objects.filter(organization_id=OuterRef("id"), type=OrganizationDocumentChoices.MSME_DOC)
        .available()
        .annotate(msme_data=JSONObject(id="id", number="organization__vendor__msme_id", file="file", file_name="name"))
    )

    company_business_card = (
        OrganizationDocument.objects.filter(
            organization_id=OuterRef("id"), type=OrganizationDocumentChoices.BUSINESS_CARD
        )
        .available()
        .annotate(business_card_data=JSONObject(id="id", file="file", file_name="name"))
    )

    aadhar_data_prefetch = Prefetch(
        "organization_document",
        queryset=OrganizationDocument.objects.filter(type=OrganizationDocumentChoices.AADHAR).all().available(),
        to_attr="aadhar_data_prefetch",
    )
    return (
        Organization.objects.filter()
        .select_related("vendor")
        .annotate(
            pan=Subquery(pan_subquery.values("pan_data")[:1]),
            msme=Subquery(msme.values("msme_data")[:1]),
            company_business_card=Subquery(company_business_card.values("business_card_data")[:1]),
        )
        .prefetch_related("organization_gst", "addresses", aadhar_data_prefetch)
        .get(filter)
    )


def get_vendor_bank_details(*, mapping: ClientVendorMapping) -> QuerySet:
    return VendorBankDetail.objects.filter(client_id=mapping.org_from_id, vendor_id=mapping.org_to_id).all()


def get_vendor_other_detail(*, mapping: ClientVendorMapping) -> QuerySet:
    bank_details = VendorBankDetail.objects.filter(client_id=mapping.org_from_id, vendor_id=mapping.org_to_id).all()
    other_detail_data = (
        ClientVendorMapping.objects.filter(id=mapping.id)
        .select_related("org_to", "org_to__vendor")
        .prefetch_related("vendor_tag", "other_detail_docs")
        .available()
        .first()
    )
    if not other_detail_data:
        raise VendorV2Exception("Mapping not found")
    setattr(other_detail_data, "bank_details", bank_details)
    return other_detail_data


def get_vendor_documents(mapping_id: int) -> QuerySet[VendorOtherDetailDocument]:
    return VendorOtherDetailDocument.objects.filter(cv_mapping_id=mapping_id, deleted_at__isnull=True).order_by(
        "created_at"
    )


def get_vendor_tags(mapping_id: int):
    return VendorTagMapping.objects.filter(cv_mapping_id=mapping_id).order_by("vendor_org_tag__created_at")


def bank_detail_get_all(organization_id: int):
    return VendorBankDetail.objects.filter(client_id=None, vendor_id=organization_id).order_by("created_at")


def is_primary_vendor(primary_user_name: str) -> bool:
    return VendorPoc.objects.filter(name=primary_user_name, is_primary=True, deleted_at__isnull=True).exists()


def get_vendor_onboard_organization_config(org_id: int):
    vendor_onboard_config = VendorOnboardConfig.objects.filter(org_id=org_id).first()
    country_ids = Organization.objects.filter(id=org_id).values_list("country_id", flat=True)
    uid_name = (
        Country.objects.filter(id__in=country_ids)
        .select_related("uid_field")
        .values_list("uid_field__name", flat=True)
        .first()
    )
    bank_section_name = (
        OrganizationSectionConfig.objects.filter(
            country_id__in=country_ids, type=OrganizationSectionTypeEnum.BANK_DETAILS.value
        )
        .values_list("name", flat=True)
        .first()
    )
    if not vendor_onboard_config:
        return VendorOnboardConfigData(
            is_pan_required=True,
            is_bank_detail_required=False,
            uid_name=uid_name,
            bank_section_name=bank_section_name,
        )
    return VendorOnboardConfigData(
        is_pan_required=vendor_onboard_config.is_pan_required,
        is_bank_detail_required=vendor_onboard_config.is_bank_detail_required,
        uid_name=uid_name,
        bank_section_name=bank_section_name,
    )
