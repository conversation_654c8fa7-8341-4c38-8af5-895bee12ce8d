from django_filters import FilterSet, filters
from rest_framework.filters import OrderingFilter, SearchFilter

from vendorv2.data.models import ClientVendorMapping
from vendorv2.domain.constants import InviteStatus


class CustomOrderFilter(OrderingFilter):
    fields_related = {
        "name": "org_to__name",  # ForeignKey Field lookup for ordering
        "pan_number": "org_to__pan_number",
        "poc_name": "vendor_poc_name",
    }

    def filter_queryset(self, request, queryset, view):
        order_fields = []
        ordering = self.get_ordering(request, queryset, view)
        if ordering:
            for field in ordering:
                symbol = "-" if "-" in field else ""
                order_fields.append(symbol + self.fields_related[field.lstrip("-")])
        if order_fields:
            return queryset.order_by(*order_fields)

        return queryset


class VendorDetailSearchFilter(SearchFilter):
    search_param = "vendor"

    def get_search_fields(self, view, request):
        return ["org_to__name", "org_to__vendor__code"]


class VendorFilters(FilterSet):
    name = filters.CharFilter(field_name="org_to__name", lookup_expr="icontains")
    pan_number = filters.CharFilter(field_name="org_to__pan_number", lookup_expr="icontains")
    status = filters.CharFilter(field_name="status", method="filter_status")
    invite_status = filters.CharFilter(field_name="invite_status", method="filter_invite_status")
    tags = filters.CharFilter(field_name="vendor_tag", method="filter_tags")
    code = filters.CharFilter(field_name="org_to__vendor__code", lookup_expr="icontains")
    poc = filters.CharFilter(method="filter_poc")

    def filter_poc(self, queryset, name, value):
        return queryset.filter(primary_user__icontains=value)

    def filter_invite_status(self, queryset, name, value):
        if value == InviteStatus.INVITED.value:
            return queryset.filter(is_users_invited=True)
        if value == InviteStatus.NOTINVITED.value:
            return queryset.filter(is_users_invited=False)
        return queryset

    def filter_status(self, queryset, name, value):
        return queryset.filter(vendor_status=value)

    def filter_tags(self, queryset, name, value):
        tag_names = value.split(",")  # Assuming tag names are comma-separated
        return queryset.filter(vendor_tag__name__in=tag_names)

    class Meta:
        model = ClientVendorMapping
        fields = ["name", "pan_number", "status", "invite_status", "tags", "code"]
